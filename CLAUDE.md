# CLAUDE.md - giki.ai Development Guide

@.claude/memory/code_index.md

## Project Overview

**giki.ai** - MIS-First Management Information System Platform  
**Tech Stack**: React + FastAPI + PostgreSQL + Vertex AI (pnpm monorepo)  
**Architecture**: Unified MIS architecture with progressive enhancement capabilities

## CORE ANTI-FRAGMENTATION PRINCIPLE

**NEVER CREATE MULTIPLE VERSIONS - ALWAYS MODIFY EXISTING FILES**  
Enhance existing files rather than creating simplified/alternate/fixed versions. This prevents workspace fragmentation and maintains single sources of truth across ALL file types: code, tests, scripts, configs, documentation.

## NEVER GIVE UP DEVELOPMENT PHILOSOPHY

**ABSOLUTE RULE**: Never abandon, skip, or leave incomplete any development task, bug, issue, or improvement.

### Mandatory Todo-Driven Development
- **EVERY ISSUE BECOMES A TODO**: The moment any problem, bug, improvement, or task is identified, it MUST be added to TodoWrite immediately
- **NO EXCEPTIONS**: Whether it's a small styling issue, major architecture problem, test failure, or performance concern - all become tracked todos
- **SYSTEMATIC COMPLETION**: Continue development systematically until ALL todos are resolved to completion
- **NO DEFERRING**: Never use phrases like "we'll fix this later" or "this is good enough for now" - fix it now or make it a todo

### Implementation Requirements
```typescript
// MANDATORY: Every session must follow this pattern
const developmentWorkflow = {
  onIssueFound: "Immediately create todo with TodoWrite tool",
  onBlocker: "Add todo, continue with next item, return to resolve",
  onCompletion: "Only when todo list is empty or all items completed",
  onSessionEnd: "Update docs/todos-persistent.yaml for recovery"
};
```

### Success Criteria
- **Todo List Empty**: No pending todos of any priority level
- **All Features Working**: End-to-end functionality verified
- **No Known Issues**: Every identified problem has been resolved
- **Quality Standards Met**: All lint, test, and build processes passing

### Violation Consequences
- **Incomplete Work**: If any todo remains unresolved, the session is considered incomplete
- **Issue Discovery**: New issues found during testing immediately become new todos
- **Quality Failures**: Failed tests, linting errors, or broken functionality immediately become high-priority todos

---

## CRITICAL DEVELOPMENT RULES

### Platform Detection & Branch Strategy
**CRITICAL**: Always detect platform first to determine correct development branch
- **master** - macOS/Darwin development (primary)
- **poppy** - Pop!_OS/Linux development 
- **windy** - Windows development (future)

```bash
uname -a                    # Darwin = master, Linux = poppy, Windows = windy
git branch --show-current   # Verify current branch matches platform
```

### pnpm-First Principle
**NEVER USE NX COMMANDS** - All development operations use direct pnpm scripts from workspace root
- ALL commands must use `pnpm` and `pnpm exec` - NO npm, npx, or yarn commands

### Zero Tolerance for Fake Data
**ABSOLUTE PROHIBITION**: NEVER create mock data, placeholder values, simulated responses, fake metrics, or "theater" demonstrations. ALWAYS implement real API calls, real data fetching, and real functionality.

### Smart Development Workflow
**BACKGROUND-FIRST APPROACH**: Use background execution scripts to avoid Claude Code timeouts.

#### Efficient Development Pattern
```bash
# Start with background execution to prevent timeouts
pnpm lint:api:bg           # Never use direct lint commands that timeout
pnpm test:visual           # Auto-starts all needed services (PostgreSQL, Redis, API, Frontend)
pnpm test:e2e              # Full integration testing with smart service management

# Monitor with real-time logs
pnpm logs:test             # Follow testing progress
pnpm logs                  # Check overall system status
```

#### Smart Service Management
- **AUTO-START SERVICES**: All test commands automatically start PostgreSQL, Redis, servers as needed
- **PORT DETECTION**: Scripts intelligently detect running services to avoid conflicts
- **LOG OVERWRITE**: Background scripts overwrite logs (>) to keep files manageable for Claude
- **NO TIMEOUTS**: Background execution eliminates Claude Code timeout issues

## CLAUDE CODE MEMORY SYSTEM

### Memory Import Architecture
**Memory Storage Rule**: Store only timeless development patterns, NOT current state or session data

### Strategic Memory Imports

#### Core Development Patterns
@.claude/memory/consolidated-development-patterns.md

#### Essential Documentation  
docs/01-CURRENT-STATUS.md
docs/02-SYSTEM-ARCHITECTURE.md  
docs/05-DESIGN-SYSTEM.md
docs/06-PAGE-SPECIFICATIONS.md
docs/08-TESTING-STRATEGY.md

#### Session Recovery
docs/todos-persistent.yaml

### Memory Maintenance Rules
- **Update memory files**: When development patterns change, new tools added, workflow patterns evolve
- **Update docs files**: When project status changes, architecture changes, new specifications added
- **NEVER create new memory files** - always enhance existing ones (anti-fragmentation)

### Timeless Development Patterns

#### MIS-First Architecture
- **UNIFIED PRODUCT**: Every customer gets a complete MIS with progressive enhancement opportunities
- **CATEGORIZATION**: Hierarchical categorization with AI-powered accuracy improvements
- **ACCURACY FOCUS**: Business value through accuracy improvements, not technical complexity

#### Workspace Organization
- **WORKSPACE ROOT RULE**: NO code, tests, scripts, or docs in workspace root
- **TEST DATA**: ALL test data centralized in `libs/test-data/` - NO exceptions
- **FILE STRUCTURE**: apps/ for code, scripts/ for tools, docs/ for documentation

#### Command Execution Patterns
- **BACKGROUND-FIRST**: Use :bg scripts for all slow operations to prevent timeouts
- **AUTO-SERVICE**: Scripts automatically start PostgreSQL, Redis, API/Frontend as needed
- **LOG OVERWRITE**: Background scripts use (>) not (>>) to keep logs manageable
- **NO MANUAL SETUP**: Scripts detect and start required services intelligently

#### Testing Philosophy
- **MIS COMPLETENESS**: MIS completeness and accuracy metrics drive all testing
- **REAL DATA**: Use centralized test data from `libs/test-data/` 
- **END-TO-END**: Test complete customer journeys: upload → categorize → review → export
- **NEVER IGNORE TESTS**: Always update/remove when outdated or incorrect

#### Development Efficiency
- **AUTH HELPER**: Use scripts/auth-helper.py for automated authentication in testing
- **PARALLEL EXECUTION**: Run independent tools simultaneously for 40-60% time savings
- **SMART MONITORING**: Use pnpm logs commands for real-time monitoring
- **TODO-DRIVEN**: Every issue immediately becomes a tracked todo

## ESSENTIAL CONTEXT

### MIS-First Product Architecture
Every customer gets a complete MIS with hierarchical categorization and progressive enhancement opportunities.

### Key Information Sources
- **Test Accounts**: See docs/01-CURRENT-STATUS.md for role-based test users
- **Design Standards**: See docs/05-DESIGN-SYSTEM.md for brand colors, icons, and layout  
- **Technical Architecture**: See docs/02-SYSTEM-ARCHITECTURE.md for MIS-first architecture
- **Testing Strategy**: See docs/08-TESTING-STRATEGY.md for MIS-focused validation

### Optimized Development Principles
- **Background-First**: Always use :bg scripts to prevent Claude Code timeouts
- **Service Auto-Start**: Let scripts handle PostgreSQL, Redis, server startup automatically
- **Log Monitoring**: Use pnpm logs commands for real-time monitoring
- **Zero Manual Setup**: Scripts detect and start what's needed intelligently

### Environment Configuration Strategy
- **Environment Files**: Keep development and production env files in `infrastructure/environments /`
- **Service Account Paths**: Store GCP service account file paths in env files (later replaced with secrets)
- **Auto-Loading**: Config system automatically loads from infrastructure directory based on ENVIRONMENT variable
- **Security**: Production secrets will be managed through Google Cloud Secret Manager

### Todo Management Rules
**CRITICAL**: When you decide to defer/postpone any task, fix, or issue during development:
- **ALWAYS CREATE A TODO** immediately using TodoWrite tool
- **NEVER just note/mention** issues without adding them to the todo list
- **INCLUDE SPECIFIC DETAILS** about what needs to be done and why
- **SET APPROPRIATE PRIORITY** based on impact (high for breaking issues, medium for warnings/deprecations)

### Efficient Command Patterns
**CRITICAL**: Use only proven timeout-resistant commands:
```bash
# Quality (fast/background only)
pnpm lint:api              # Fast Python linting
pnpm lint:api:bg           # Background Python linting
pnpm lint:app:bg           # Background frontend linting (never direct)

# Testing (smart auto-start)
pnpm test:visual           # Auto-starts all services, background execution
pnpm test:e2e              # Auto-starts all services, background execution
pnpm test:api:bg unit      # Background API unit tests
pnpm test:app:bg unit      # Background frontend unit tests

# Development (auto-service management)
pnpm serve:api             # Auto-starts PostgreSQL, Redis, API
pnpm db                    # Test database (works after serve:api)
```

### Claude Code Optimization Rules
- **NO DIRECT COMMANDS**: Never use lint:app, test:api, test:app, build:app (timeout)
- **BACKGROUND EXECUTION**: All slow operations run in background with log overwrite
- **AUTO-SERVICE STARTUP**: Scripts intelligently start required services
- **LOG OVERWRITE**: Prevents massive log files Claude can't read

### Screenshot Analysis Patterns
- **UNIFIED TOOL**: Use `mcp__screenshot__analyze_screenshots` for all visual analysis
- **PROBLEM-DISCOVERY FOCUS**: Tool configured to FIND PROBLEMS, not validate success
- **DOCUMENTATION CONTEXT**: Always include relevant docs (05-DESIGN-SYSTEM.md, etc.)
- **MANDATORY WORKFLOW**: Capture → Read images → Analyze with context

### Eliminated Inefficient Practices
#### Timeout-Prone Commands (NEVER USE)
- ❌ `pnpm lint:app` → ✅ `pnpm lint:app:bg`
- ❌ `pnpm test:api` → ✅ `pnpm test:api:bg [unit|integration|slow|all]`
- ❌ `pnpm test:app` → ✅ `pnpm test:app:bg [unit|coverage|visual|integration|e2e]`
- ❌ `pnpm build:app` → ✅ `pnpm build:app:bg`

#### Manual Service Management (ELIMINATED)
- ❌ Manual PostgreSQL/Redis startup → ✅ Auto-started by scripts
- ❌ Manual API/Frontend startup → ✅ Auto-started when needed
- ❌ Log rotation → ✅ Log overwrite strategy
- ❌ Manual process cleanup → ✅ Automatic management

---

**Updated**: 2025-07-12 | **Method**: Enhanced Claude Code memory system with timeless patterns and efficiency optimizations - NS

