---
description: 'Production deployment with comprehensive pre-deployment testing and intelligent quality gates'
allowed-tools: ['Bash', 'TodoWrite', 'mcp__playwright__browser_navigate', 'mcp__playwright__browser_console_messages', 'mcp__playwright__browser_network_requests', 'mcp__playwright__browser_take_screenshot', 'mcp__postgres-prod__query', 'mcp__postgres-prod__describe_table', 'mcp__postgres-prod__list_tables']
memory-context: ['@docs/01-CURRENT-STATUS.md', '@docs/07-DEPLOYMENT-GUIDE.md', '@docs/08-TESTING-STRATEGY.md', '@docs/02-SYSTEM-ARCHITECTURE.md']
arguments: 'Optional: validate|production|staging|rollback|monitoring|quick'
---

# Production Deployment with Comprehensive Quality Gates

**Purpose**: Production deployment orchestration with comprehensive pre-deployment validation and intelligent quality assurance  
**Focus**: Zero-downtime deployment with complete quality gate validation and automated rollback capabilities  
**Intelligence**: Pipeline orchestration, performance monitoring, and customer impact assessment

## 🚀 INTELLIGENT DEPLOYMENT PIPELINE ORCHESTRATION

### **Complete Quality Gate Validation (MANDATORY BEFORE DEPLOYMENT)**
```
🔺 PHASE 1: Backend Foundation Validation (BLOCKING)
   ├── Database connectivity and schema validation
   ├── API endpoint health and authentication
   ├── External service integration and dependencies
   └── Performance benchmarks and optimization

🔺 PHASE 2: Integration & Service Communication (BLOCKING)
   ├── Frontend-backend API communication validation
   ├── Cross-service data flow integrity
   ├── External service integration (Vertex AI, Cloud SQL)
   └── Real-time processing workflow validation

🔺 PHASE 3: Customer Experience Validation (BLOCKING)
   ├── Complete MIS setup workflow (5-minute target)
   ├── Real customer scenario testing and validation
   ├── Cross-platform compatibility and accessibility
   └── Business value measurement and success metrics

🔺 PHASE 4: Production Environment Preparation (BLOCKING)
   ├── Visual brand compliance and professional appearance
   ├── Security audit and vulnerability assessment
   ├── Performance optimization and resource allocation
   └── Monitoring and alerting system preparation
```

## 🔍 INTELLIGENT DEPLOYMENT CONTEXT ANALYSIS

### Current Production Environment Assessment
- **Service Status**: !`pgrep -f "uvicorn\\|vite" || echo "⚠️ Local services not running - start for final validation"`
- **Git Status**: !`git status --porcelain | head -3`
- **Production Status**: !`curl -s https://giki-ai-api-273348121056.us-central1.run.app/health || echo "⚠️ Production API not responding"`
- **Database Status**: !`pnpm db || echo "⚠️ Database connection issues"`

### Dynamic Deployment Path Selection
Based on `$ARGUMENTS` and production readiness state:

#### **Complete Production Deployment** (`/deploy` or `/deploy production`)
Full production deployment with comprehensive validation pipeline:
1. **Complete Quality Gate Validation** - All four phases with blocking quality gates
2. **Production Environment Preparation** - Infrastructure validation and optimization
3. **Zero-Downtime Deployment** - Backend → Frontend with health monitoring
4. **Post-Deployment Validation** - Customer workflow testing in production
5. **Production Monitoring Setup** - Real-time monitoring and alerting activation

#### **Pre-Deployment Validation** (`/deploy validate`)
Comprehensive validation without actual deployment:
1. **Quality Gate Simulation** - All validation phases with detailed reporting
2. **Production Readiness Assessment** - Infrastructure and performance evaluation
3. **Risk Assessment** - Potential deployment issues and mitigation strategies
4. **Deployment Recommendation** - Go/No-Go decision with detailed reasoning

#### **Staging Deployment** (`/deploy staging`)
Deploy to staging environment for final validation:
1. **Staging Environment Deployment** - Deploy to staging with production configuration
2. **Staging Validation** - Complete customer workflow testing in staging
3. **Performance Benchmarking** - Production-like load testing and optimization
4. **Staging Sign-Off** - Final approval before production deployment

#### **Emergency Rollback** (`/deploy rollback`)
Emergency rollback to previous stable version:
1. **Rollback Trigger Detection** - Identify critical issues requiring rollback
2. **Automated Rollback Execution** - Revert to last known good deployment
3. **Health Validation** - Confirm rollback success and system stability
4. **Incident Documentation** - Document rollback reason and resolution plan

#### **Production Monitoring** (`/deploy monitoring`)
Production health monitoring and performance tracking:
1. **Real-Time Monitoring** - Customer workflow success rates and performance
2. **Performance Analytics** - Response times, error rates, and resource usage
3. **Customer Success Metrics** - MIS accuracy, setup times, business value
4. **Alert Management** - Proactive issue detection and notification

#### **Quick Production Check** (`/deploy quick`)
Essential production health validation:
1. **Critical Path Testing** - Core customer workflows and API health
2. **Performance Spot Check** - Key performance indicators and response times
3. **Customer Impact Assessment** - User experience and business continuity
4. **Alert Review** - Current system alerts and performance warnings

## 🎯 COMPREHENSIVE QUALITY GATE VALIDATION

### **Phase 1: Backend Foundation Validation (MANDATORY FIRST)**
```bash
# Comprehensive backend foundation validation before deployment
execute_backend_foundation_validation() {
  echo "🔧 Phase 1: Backend Foundation Validation..."
  
  # 1. Database schema and connectivity validation
  echo "📊 Validating database foundation..."
  validate_production_database_readiness
  
  # 2. API endpoint health and authentication
  echo "🌐 Validating API foundation..."
  validate_api_endpoint_health_comprehensive
  
  # 3. External service integration validation
  echo "🔗 Validating external service integration..."
  validate_external_service_readiness
  
  # 4. Performance benchmark validation
  echo "⚡ Validating performance benchmarks..."
  validate_performance_readiness_for_production
  
  # 5. Security audit and vulnerability check
  echo "🔒 Validating security readiness..."
  validate_security_and_vulnerability_status
}
```

### **Production Database Readiness Validation**
```bash
# Use MCP PostgreSQL tools for production database validation
validate_production_database_readiness() {
  echo "📊 Production database readiness validation..."
  
  # Production database schema validation
  mcp__postgres-prod__describe_table public users
  mcp__postgres-prod__describe_table public transactions
  mcp__postgres-prod__describe_table public categories
  
  # Production data integrity checks
  mcp__postgres-prod__query "SELECT COUNT(*) as total_users FROM users WHERE is_active = true"
  mcp__postgres-prod__query "SELECT COUNT(*) as total_transactions FROM transactions"
  mcp__postgres-prod__query "SELECT COUNT(*) as total_categories FROM categories"
  
  # Production performance validation
  mcp__postgres-prod__explain_query "SELECT * FROM transactions WHERE tenant_id = 3 ORDER BY date DESC LIMIT 100"
  
  # Production constraint validation
  mcp__postgres-prod__query "SELECT COUNT(*) as categorized_transactions FROM transactions WHERE category_id IS NOT NULL"
}
```

### **API Health and Authentication Validation**
```bash
# Comprehensive API health validation for production readiness
validate_api_endpoint_health_comprehensive() {
  echo "🌐 Comprehensive API health validation..."
  
  # Production API health check
  production_health=$(curl -s https://giki-ai-api-273348121056.us-central1.run.app/health || echo "PRODUCTION_API_DOWN")
  if [[ "$production_health" == "PRODUCTION_API_DOWN" ]]; then
    echo "❌ BLOCKING: Production API not responding"
    exit 1
  fi
  
  # Production authentication validation
  auth_response=$(curl -X POST "https://giki-ai-api-273348121056.us-central1.run.app/api/v1/auth/token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=<EMAIL>&password=GikiTest2025Secure" -s)
  
  # Extract and validate token
  token=$(echo "$auth_response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
  if [[ -z "$token" ]]; then
    echo "❌ BLOCKING: Production authentication failed"
    exit 1
  fi
  
  # Test critical API endpoints
  test_critical_api_endpoints_with_token "$token"
}
```

## 🔗 PHASE 2: INTEGRATION & SERVICE COMMUNICATION VALIDATION

### **Cross-Service Communication Validation**
```bash
# Comprehensive integration validation for production deployment
execute_integration_validation() {
  echo "🔗 Phase 2: Integration & Service Communication Validation..."
  
  # 1. Frontend-backend communication validation
  echo "💻 Validating frontend-backend integration..."
  validate_frontend_backend_production_communication
  
  # 2. External service integration validation
  echo "🌐 Validating external service integration..."
  validate_vertex_ai_production_integration
  validate_cloud_sql_production_integration
  
  # 3. Data flow integrity validation
  echo "🔄 Validating data flow integrity..."
  validate_production_data_flow_integrity
  
  # 4. Real-time processing validation
  echo "⚡ Validating real-time processing..."
  validate_production_real_time_processing
}
```

### **Frontend-Backend Production Communication**
```bash
# Test frontend-backend communication in production environment
validate_frontend_backend_production_communication() {
  echo "💻 Frontend-backend production communication validation..."
  
  # Use MCP Playwright for production environment testing
  mcp__playwright__browser_navigate "https://app-giki-ai.web.app"
  
  # Test production authentication flow
  test_production_authentication_workflow
  
  # Test production API communication
  test_production_api_communication
  
  # Monitor network requests for errors
  mcp__playwright__browser_network_requests
  
  # Check console for production errors
  mcp__playwright__browser_console_messages
}
```

## 🎭 PHASE 3: CUSTOMER EXPERIENCE VALIDATION

### **Production Customer Workflow Testing**
```bash
# Complete customer experience validation in production environment
execute_customer_experience_validation() {
  echo "🎭 Phase 3: Customer Experience Validation..."
  
  # 1. MIS setup workflow in production
  echo "🏗️ Testing MIS setup in production..."
  test_production_mis_setup_workflow
  
  # 2. Real customer scenario validation
  echo "👥 Testing real customer scenarios..."
  test_production_customer_scenarios
  
  # 3. Cross-platform compatibility validation
  echo "📱 Testing cross-platform compatibility..."
  test_production_cross_platform_compatibility
  
  # 4. Business value measurement
  echo "💰 Measuring business value in production..."
  measure_production_business_value_metrics
}
```

### **Production MIS Workflow Validation**
```bash
# Test complete MIS workflow in production environment
test_production_mis_setup_workflow() {
  echo "🏗️ Production MIS setup workflow validation..."
  
  # Navigate to production application
  mcp__playwright__browser_navigate "https://app-giki-ai.web.app"
  
  # Test production authentication
  test_production_authentication_complete
  
  # Test production file upload workflow
  test_production_file_upload_workflow
  
  # Test production categorization accuracy
  validate_production_categorization_accuracy
  
  # Test production export functionality
  test_production_export_functionality
  
  # Take production screenshots for validation
  mcp__playwright__browser_take_screenshot "production-mis-workflow.png"
}
```

## 🚀 PHASE 4: PRODUCTION ENVIRONMENT PREPARATION

### **Pre-Deployment Production Environment Validation**
```bash
# Final production environment preparation and validation
execute_production_environment_preparation() {
  echo "🚀 Phase 4: Production Environment Preparation..."
  
  # 1. Visual brand compliance validation
  echo "🎨 Validating visual brand compliance..."
  validate_production_visual_compliance
  
  # 2. Security audit and vulnerability assessment
  echo "🔒 Executing security audit..."
  execute_production_security_audit
  
  # 3. Performance optimization validation
  echo "⚡ Validating performance optimization..."
  validate_production_performance_optimization
  
  # 4. Monitoring and alerting preparation
  echo "📊 Preparing monitoring and alerting..."
  prepare_production_monitoring_and_alerting
}
```

### **Production Security Audit**
```bash
# Comprehensive security audit before production deployment
execute_production_security_audit() {
  echo "🔒 Production security audit..."
  
  # Authentication security validation
  validate_production_authentication_security
  
  # API security validation
  validate_production_api_security
  
  # Database security validation
  validate_production_database_security
  
  # Infrastructure security validation
  validate_production_infrastructure_security
}
```

## 🎯 INTELLIGENT QUALITY GATES ENFORCEMENT

### **Blocking Quality Gates (DEPLOYMENT PREVENTED ON FAILURE)**
```bash
# Enforce blocking quality gates that prevent deployment
enforce_blocking_quality_gates() {
  echo "🎯 Enforcing blocking quality gates..."
  
  # Backend foundation must be 100% functional
  if ! backend_foundation_validated; then
    echo "❌ BLOCKING: Backend foundation validation failed"
    create_backend_failure_deployment_todos
    exit 1
  fi
  
  # Integration tests must pass completely
  if ! integration_validation_passed; then
    echo "❌ BLOCKING: Integration validation failed"
    create_integration_failure_deployment_todos
    exit 1
  fi
  
  # Customer experience must be functional
  if ! customer_experience_validated; then
    echo "❌ BLOCKING: Customer experience validation failed"
    create_customer_experience_failure_todos
    exit 1
  fi
  
  # Production environment must be ready
  if ! production_environment_ready; then
    echo "❌ BLOCKING: Production environment not ready"
    create_production_readiness_todos
    exit 1
  fi
  
  echo "✅ All blocking quality gates passed - deployment authorized"
}
```

### **Warning Quality Gates (NON-BLOCKING WITH MONITORING)**
```bash
# Check warning quality gates that don't block deployment but require monitoring
check_warning_quality_gates() {
  echo "⚠️ Checking warning quality gates..."
  
  # Performance warnings
  check_production_performance_warnings
  
  # Security recommendations
  check_production_security_recommendations
  
  # Monitoring coverage
  check_production_monitoring_coverage
  
  # Business impact assessment
  assess_production_business_impact_risks
}
```

## 🚀 ZERO-DOWNTIME DEPLOYMENT EXECUTION

### **Production Deployment Pipeline**
```bash
# Execute production deployment with zero downtime
execute_production_deployment() {
  echo "🚀 Executing production deployment..."
  
  # 1. Pre-deployment backup and tagging
  echo "💾 Creating deployment backup..."
  create_deployment_backup_and_tags
  
  # 2. Backend deployment with health monitoring
  echo "🔧 Deploying backend with health monitoring..."
  deploy_backend_with_health_monitoring
  
  # 3. Frontend deployment with rollback capability
  echo "💻 Deploying frontend with rollback capability..."
  deploy_frontend_with_rollback_capability
  
  # 4. Post-deployment validation
  echo "✅ Executing post-deployment validation..."
  execute_post_deployment_validation
  
  # 5. Production monitoring activation
  echo "📊 Activating production monitoring..."
  activate_production_monitoring_and_alerting
}
```

### **Backend Deployment with Health Monitoring**
```bash
# Deploy backend with continuous health monitoring
deploy_backend_with_health_monitoring() {
  echo "🔧 Backend deployment with health monitoring..."
  
  # Deploy backend
  pnpm deploy:api
  
  # Wait for stabilization with health checks
  wait_for_backend_stabilization_with_health_checks
  
  # Validate backend deployment success
  validate_backend_deployment_success
  
  # Enable backend monitoring
  enable_backend_production_monitoring
}
```

### **Frontend Deployment with Rollback Capability**
```bash
# Deploy frontend with automatic rollback capability
deploy_frontend_with_rollback_capability() {
  echo "💻 Frontend deployment with rollback capability..."
  
  # Deploy frontend
  pnpm deploy:app
  
  # Validate frontend deployment success
  validate_frontend_deployment_success
  
  # Test frontend-backend communication in production
  test_production_frontend_backend_communication
  
  # Enable frontend monitoring
  enable_frontend_production_monitoring
}
```

## 📊 POST-DEPLOYMENT VALIDATION & MONITORING

### **Immediate Production Health Validation**
```bash
# Comprehensive post-deployment validation
execute_post_deployment_validation() {
  echo "📊 Post-deployment validation..."
  
  # 1. Production API health validation
  echo "🌐 Validating production API health..."
  validate_production_api_health_post_deployment
  
  # 2. Production customer workflow validation
  echo "🎭 Validating production customer workflows..."
  validate_production_customer_workflows_post_deployment
  
  # 3. Production performance validation
  echo "⚡ Validating production performance..."
  validate_production_performance_post_deployment
  
  # 4. Production monitoring validation
  echo "📈 Validating production monitoring..."
  validate_production_monitoring_operational
}
```

### **Production Customer Workflow Validation**
```bash
# Test complete customer workflows in production after deployment
validate_production_customer_workflows_post_deployment() {
  echo "🎭 Production customer workflow validation..."
  
  # Test production MIS setup workflow
  test_production_mis_setup_complete
  
  # Test production file processing workflow
  test_production_file_processing_complete
  
  # Test production export workflow
  test_production_export_workflow_complete
  
  # Validate production accuracy metrics
  validate_production_mis_accuracy_metrics
  
  # Test production customer success metrics
  measure_production_customer_success_metrics
}
```

## 🚨 EMERGENCY ROLLBACK & INCIDENT RESPONSE

### **Automated Rollback Triggers**
```bash
# Automated rollback triggers for critical production issues
monitor_rollback_triggers() {
  echo "🚨 Monitoring rollback triggers..."
  
  # Critical error rate threshold
  if critical_error_rate_exceeded; then
    trigger_emergency_rollback "Critical error rate exceeded"
  fi
  
  # Customer workflow failure threshold
  if customer_workflow_failure_rate_exceeded; then
    trigger_emergency_rollback "Customer workflow failure rate exceeded"
  fi
  
  # Performance degradation threshold
  if performance_degradation_critical; then
    trigger_emergency_rollback "Critical performance degradation"
  fi
  
  # Authentication failure threshold
  if authentication_failure_rate_critical; then
    trigger_emergency_rollback "Critical authentication failure rate"
  fi
}
```

### **Emergency Rollback Execution**
```bash
# Execute emergency rollback to last known good deployment
execute_emergency_rollback() {
  echo "🚨 Executing emergency rollback..."
  
  # 1. Identify last good deployment
  last_good_tag=$(git tag | grep "pre-deploy" | tail -1)
  echo "📋 Rolling back to: $last_good_tag"
  
  # 2. Execute rollback
  git checkout "$last_good_tag"
  pnpm deploy:api
  pnpm deploy:app
  
  # 3. Validate rollback success
  validate_rollback_success
  
  # 4. Document incident
  document_rollback_incident "$last_good_tag"
}
```

## 🎛️ DEPLOYMENT CROSS-COMMAND ORCHESTRATION

### **Complete Deployment Pipeline Coordination**
Orchestrate all commands for comprehensive deployment readiness:

**Prerequisites from `/test`**: Backend foundation and integration validation must pass
**Prerequisites from `/visual-consistency`**: Brand compliance and professional appearance validated
**Prerequisites from `/uat`**: Customer workflows and business value confirmed
**Coordination with monitoring**: Real-time production health and customer success tracking

### **Deployment Context Sharing**
```typescript
interface DeploymentContextSharing {
  preDeploymentValidation: {
    backendFoundationReady: boolean;
    integrationValidated: boolean;
    customerExperienceValidated: boolean;
    productionEnvironmentReady: boolean;
  };
  
  deploymentExecution: {
    backendDeploymentStatus: "SUCCESS" | "FAILED" | "IN_PROGRESS";
    frontendDeploymentStatus: "SUCCESS" | "FAILED" | "IN_PROGRESS";
    overallDeploymentHealth: "HEALTHY" | "WARNING" | "CRITICAL";
  };
  
  postDeploymentValidation: {
    productionHealthStatus: "OPERATIONAL" | "DEGRADED" | "CRITICAL";
    customerWorkflowStatus: "FUNCTIONAL" | "IMPAIRED" | "BROKEN";
    performanceStatus: "OPTIMAL" | "ACCEPTABLE" | "POOR";
    monitoringStatus: "ACTIVE" | "PARTIAL" | "INACTIVE";
  };
  
  productionMetrics: {
    customerSuccessRate: number;
    averageResponseTime: number;
    errorRate: number;
    misAccuracyRate: number;
  };
}
```

## 📊 DEPLOYMENT SUCCESS METRICS

### **Production Deployment Success Criteria**
- [ ] **All Quality Gates Passed**: Backend, integration, customer experience, and production environment validation
- [ ] **Zero-Downtime Deployment**: No service interruption during deployment process
- [ ] **Production Health Validated**: All APIs, databases, and services operational
- [ ] **Customer Workflows Functional**: Complete MIS setup, file processing, and export workflows working
- [ ] **Performance Targets Met**: Response times, accuracy metrics, and business value maintained

### **Production Monitoring Success Criteria**
- [ ] **Real-Time Monitoring Active**: Customer success, performance, and error rate monitoring operational
- [ ] **Alerting System Functional**: Proactive issue detection and notification system working
- [ ] **Customer Success Tracking**: MIS accuracy, setup times, and business value metrics tracked
- [ ] **Performance Baseline Established**: Production performance benchmarks documented
- [ ] **Incident Response Ready**: Rollback procedures and emergency response protocols validated

### **Business Continuity Success Criteria**
- [ ] **Customer Impact Minimized**: No disruption to customer workflows or business operations
- [ ] **Business Value Maintained**: MIS accuracy and customer success metrics preserved
- [ ] **Production Stability**: System reliability and performance maintained post-deployment
- [ ] **Monitoring Coverage**: Complete visibility into production health and customer experience
- [ ] **Future Deployment Readiness**: Deployment pipeline and processes optimized for future releases

## 📚 DEPLOYMENT DOCUMENTATION EVOLUTION

### **Real-Time Deployment Documentation Updates**
**MANDATORY**: Update documentation with deployment execution results:

1. **Update Current Status** (`docs/01-CURRENT-STATUS.md`):
   - Record production deployment completion with timestamp and success metrics
   - Update production environment status with actual performance measurements
   - Document production deployment validation results and customer impact
   - Track production system health baseline and monitoring coverage

2. **Update Deployment Guide** (`docs/07-DEPLOYMENT-GUIDE.md`):
   - Document deployment process improvements and lessons learned
   - Update production environment specifications with actual configuration
   - Record deployment validation procedures and quality gate effectiveness
   - Track deployment automation maturity and process optimization

3. **Update Todo System** (`docs/todos-persistent.yaml`):
   - Mark deployment pipeline tasks as completed with detailed success metrics
   - Create todos for any production optimization opportunities discovered
   - Update project completion status to reflect production deployment success
   - Record deployment efficiency metrics and process improvement opportunities

### **Production Success Documentation Protocol**

When production deployment succeeds:

1. **Production Baseline Documentation**: Record actual production performance and customer success metrics
2. **Process Optimization**: Document deployment process improvements for future releases
3. **Monitoring Enhancement**: Update monitoring and alerting based on production experience
4. **Customer Success Validation**: Confirm customer workflows and business value delivery
5. **Future Deployment Preparation**: Optimize deployment pipeline for continuous delivery

---

**PRODUCTION DEPLOYMENT COMMAND**: Orchestrates comprehensive pre-deployment validation, executes zero-downtime production deployment, and establishes production monitoring with automated rollback capabilities. Ensures customer experience continuity while delivering enhanced MIS platform capabilities to production environment.