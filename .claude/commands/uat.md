---
description: 'Real customer scenario testing with MIS accuracy validation and business value measurement'
allowed-tools: ['mcp__playwright__browser_navigate', 'mcp__playwright__browser_snapshot', 'mcp__playwright__browser_console_messages', 'mcp__playwright__browser_click', 'mcp__playwright__browser_type', 'mcp__playwright__browser_network_requests', 'mcp__playwright__browser_file_upload', 'mcp__playwright__browser_wait_for', 'TodoWrite']
memory-context: ['@docs/01-CURRENT-STATUS.md', '@docs/04-CUSTOMER-JOURNEYS.md', '@docs/08-TESTING-STRATEGY.md', '@docs/06-PAGE-SPECIFICATIONS.md']
arguments: 'Optional: production|quick|accuracy|workflows|performance|business-value'
---

# Real Customer Scenario Testing (UAT)

**Purpose**: Validate real customer scenarios with MIS accuracy measurement and business value quantification  
**Focus**: 87% baseline accuracy → 95% enhanced accuracy with genuine business workflows  
**Intelligence**: Performance measurement, cross-platform validation, and customer success metrics

## 🎯 CUSTOMER-FOCUSED TESTING PHILOSOPHY

### **Real Customer Scenarios (Not Artificial Tests)**
```
🎭 Real Business Workflows (PRIMARY FOCUS)
   ├── Actual company financial data processing
   ├── Real time constraints and business pressures
   ├── Genuine accuracy requirements and expectations
   └── Authentic user behavior patterns and workflows

📊 MIS Accuracy Validation (CORE MEASUREMENT)
   ├── 87% baseline accuracy achievement verification
   ├── 95%+ enhanced accuracy with progressive improvements
   ├── Business-appropriate categorization validation
   └── Export compatibility with real accounting software

⚡ Performance Under Real Conditions
   ├── Realistic data volumes (1000+ transactions)
   ├── Actual network conditions and device constraints
   ├── Real user interaction patterns and timing
   └── Business workflow completion time measurement
```

## 🔍 INTELLIGENT CUSTOMER CONTEXT ANALYSIS

### Current Customer Environment Assessment
- **Environment Detection**: !`pwd && uname -a`
- **Service Readiness**: !`pgrep -f "uvicorn\|vite" || echo "⚠️ Services not running - start with pnpm serve"`
- **Database Status**: !`pnpm db || echo "⚠️ Database connection issues"`
- **Git Status**: !`git status --porcelain | head -3`

### Dynamic Customer Testing Path Selection
Based on `$ARGUMENTS` and real customer scenarios:

#### **Complete Customer Journey** (`/uat` or `/uat full`)
End-to-end customer validation with all business scenarios:
1. **Real MIS Setup Journey** - Authentic business setup with time pressure simulation
2. **Realistic File Processing** - Large volume data processing with business constraints
3. **Genuine Transaction Review** - Real decision-making patterns and accuracy requirements
4. **Authentic Export Scenarios** - Actual accounting software integration workflows
5. **Business Value Measurement** - Time savings, accuracy improvements, workflow efficiency

#### **Production Customer Testing** (`/uat production`)
Live production environment customer scenario validation:
1. **Production Workflow Testing** - Real customer scenarios in live environment
2. **Production Performance Testing** - Customer experience under real load conditions
3. **Production Accuracy Validation** - MIS accuracy measurement in production
4. **Production Integration Testing** - Live accounting software integration validation

#### **Quick Customer Validation** (`/uat quick`)
Essential customer scenarios for rapid feedback:
1. **Critical Path Testing** - Core customer workflows only
2. **Essential Accuracy Testing** - Key categorization accuracy validation
3. **Basic Performance Testing** - Critical response time validation
4. **Core Integration Testing** - Essential export functionality validation

#### **Accuracy-Focused Testing** (`/uat accuracy`)
MIS accuracy measurement and validation focus:
1. **Baseline Accuracy Testing** - Verify 87% baseline achievement
2. **Enhancement Accuracy Testing** - Validate 95%+ enhanced accuracy
3. **Business Appropriateness Testing** - Real-world categorization validation
4. **Cross-Industry Accuracy Testing** - Multiple business type validation

#### **Customer Workflow Testing** (`/uat workflows`)
Deep customer workflow validation and optimization:
1. **Complete Workflow Testing** - End-to-end customer journey validation
2. **Workflow Performance Testing** - Customer task completion time measurement
3. **Workflow Usability Testing** - Real user interaction pattern validation
4. **Workflow Optimization Testing** - Efficiency and friction point identification

#### **Business Value Testing** (`/uat business-value`)
Customer success and business impact measurement:
1. **Time Savings Measurement** - Actual time saved vs manual processes
2. **Accuracy Improvement Quantification** - Real accuracy gains and business impact
3. **Workflow Efficiency Measurement** - Productivity improvements and bottleneck removal
4. **Customer Satisfaction Assessment** - User experience quality and business value delivery

#### **Performance Under Load** (`/uat performance`)
Real-world performance validation with customer data volumes:
1. **High Volume Testing** - 1000+ transaction processing performance
2. **Concurrent User Testing** - Multiple user workflow performance
3. **Network Condition Testing** - Slow connection and mobile performance
4. **Device Performance Testing** - Cross-device customer experience validation

## 🏗️ REAL MIS SETUP CUSTOMER JOURNEY (5-MINUTE TARGET)

### **Authentic Business MIS Setup Testing**
```bash
# Test realistic MIS setup with genuine business pressure
test_real_mis_setup_journey() {
  echo "🏗️ Testing Real MIS Setup Customer Journey..."
  
  # Start timing for 5-minute target
  start_time=$(date +%s)
  
  # 1. Authentic customer authentication
  echo "🔐 Testing real customer authentication..."
  test_customer_authentication_flow
  
  # 2. Business context gathering
  echo "🏢 Testing business context setup..."
  test_business_context_collection
  
  # 3. Industry-specific MIS configuration
  echo "⚙️ Testing industry MIS configuration..."
  test_industry_mis_configuration
  
  # 4. Real data file upload and processing
  echo "📁 Testing real file upload..."
  test_real_file_upload_processing
  
  # 5. MIS accuracy validation
  echo "📊 Testing MIS accuracy achievement..."
  validate_87_percent_baseline_accuracy
  
  # 6. Export readiness verification
  echo "📋 Testing export readiness..."
  test_export_readiness_validation
  
  # Calculate total setup time
  setup_time=$(($(date +%s) - start_time))
  echo "⏱️ MIS Setup Completed in: ${setup_time}s (Target: <300s)"
  
  # Business impact assessment
  assess_mis_setup_business_impact "$setup_time"
}
```

### **Real Customer Authentication with Business Context**
```bash
# Authenticate as real customer with business context
test_customer_authentication_flow() {
  echo "🔐 Testing customer authentication with business context..."
  
  # Use MCP Playwright for realistic customer interaction
  mcp__playwright__browser_navigate "http://localhost:4200"
  
  # Test realistic login workflow
  mcp__playwright__browser_type "<EMAIL>" "input[type=email]"
  mcp__playwright__browser_type "GikiTest2025Secure" "input[type=password]"
  mcp__playwright__browser_click "Submit" "button[type=submit]"
  
  # Wait for dashboard load
  mcp__playwright__browser_wait_for "text=Dashboard"
  
  # Validate authentication success
  mcp__playwright__browser_console_messages
  
  echo "✅ Customer authentication flow validated"
}
```

### **Industry-Specific MIS Configuration Testing**
```bash
# Test MIS configuration for specific industry with real constraints
test_industry_mis_configuration() {
  echo "⚙️ Testing industry-specific MIS configuration..."
  
  # Test with real business context (e.g., Manufacturing, Retail, Services)
  test_manufacturing_mis_setup
  test_retail_mis_setup
  test_services_mis_setup
  
  # Validate industry-appropriate categorization
  validate_industry_categorization_appropriateness
  
  # Test GL code assignment accuracy
  validate_gl_code_assignment_accuracy
  
  echo "✅ Industry MIS configuration validated"
}
```

## 📁 REALISTIC FILE PROCESSING WORKFLOW

### **High-Volume Real Data Processing**
```bash
# Test file processing with realistic data volumes and constraints
test_realistic_file_processing_workflow() {
  echo "📁 Testing Realistic File Processing Workflow..."
  
  # 1. Large file upload testing
  echo "📤 Testing large file upload..."
  test_large_file_upload_performance
  
  # 2. Real-time schema detection
  echo "🔍 Testing real-time schema detection..."
  test_realistic_schema_detection
  
  # 3. High-volume categorization
  echo "🤖 Testing high-volume AI categorization..."
  test_high_volume_categorization_accuracy
  
  # 4. Progress tracking validation
  echo "📈 Testing progress tracking..."
  test_realistic_progress_tracking
  
  # 5. Processing completion validation
  echo "✅ Testing processing completion..."
  validate_processing_completion_accuracy
}
```

### **Real Data Volume Testing**
```bash
# Test with realistic business data volumes
test_large_file_upload_performance() {
  echo "📤 Testing realistic data volume processing..."
  
  # Test with actual business data volumes
  large_file="libs/test-data/synthetic/indian-banks/HDFC.xlsx" # 1000+ transactions
  
  # Upload large file and measure performance
  start_time=$(date +%s)
  
  mcp__playwright__browser_navigate "http://localhost:4200/upload"
  mcp__playwright__browser_file_upload "$large_file"
  
  # Wait for processing to complete
  mcp__playwright__browser_wait_for "text=Processing Complete"
  
  processing_time=$(($(date +%s) - start_time))
  echo "📊 Large file processing time: ${processing_time}s"
  
  # Validate performance meets business requirements
  if [ "$processing_time" -lt 120 ]; then
    echo "✅ Processing performance meets business requirements"
  else
    echo "⚠️ Processing performance slower than business requirements"
  fi
}
```

### **AI Categorization Accuracy Measurement**
```bash
# Measure actual AI categorization accuracy with real data
test_high_volume_categorization_accuracy() {
  echo "🤖 Testing AI categorization accuracy..."
  
  # Process real transaction data
  process_real_transaction_data "libs/test-data/synthetic/indian-banks/HDFC.xlsx"
  
  # Measure categorization accuracy
  accuracy=$(calculate_categorization_accuracy)
  
  echo "📊 AI Categorization Accuracy: ${accuracy}%"
  
  # Validate accuracy meets business requirements
  validate_accuracy_business_requirements "$accuracy"
  
  # Test business appropriateness of categorizations
  validate_business_appropriateness_of_categories
}
```

## 💰 AUTHENTIC TRANSACTION MANAGEMENT WORKFLOW

### **Real Business Transaction Review**
```bash
# Test transaction review with real business decision patterns
test_authentic_transaction_management() {
  echo "💰 Testing Authentic Transaction Management..."
  
  # 1. Real transaction review patterns
  echo "👀 Testing real transaction review..."
  test_realistic_transaction_review_patterns
  
  # 2. Business decision-making simulation
  echo "🤔 Testing business decision patterns..."
  test_business_decision_patterns
  
  # 3. Bulk operations with business constraints
  echo "📦 Testing bulk operations..."
  test_business_bulk_operations
  
  # 4. Category assignment with business logic
  echo "🏷️ Testing category assignment..."
  test_business_category_assignment
  
  # 5. Workflow efficiency measurement
  echo "⚡ Measuring workflow efficiency..."
  measure_transaction_workflow_efficiency
}
```

### **Business Decision Pattern Simulation**
```bash
# Simulate real business decision-making patterns
test_business_decision_patterns() {
  echo "🤔 Testing business decision patterns..."
  
  # Test accountant workflow patterns
  test_accountant_decision_patterns
  
  # Test bookkeeper workflow patterns
  test_bookkeeper_decision_patterns
  
  # Test business owner workflow patterns
  test_owner_decision_patterns
  
  # Measure decision time and accuracy
  measure_decision_efficiency_metrics
}
```

## 📊 REAL EXPORT AND ACCOUNTING INTEGRATION

### **Authentic Accounting Software Integration**
```bash
# Test export integration with real accounting software requirements
test_authentic_export_integration() {
  echo "📊 Testing Authentic Export Integration..."
  
  # 1. Real accounting software format testing
  echo "💼 Testing accounting software formats..."
  test_real_accounting_software_formats
  
  # 2. Export file validation with real data
  echo "📋 Testing export file validation..."
  test_export_file_accuracy_validation
  
  # 3. Import compatibility testing
  echo "📥 Testing import compatibility..."
  test_accounting_software_import_compatibility
  
  # 4. Business workflow integration
  echo "🔄 Testing business workflow integration..."
  test_business_export_workflow_integration
}
```

### **Real Accounting Software Compatibility**
```bash
# Test compatibility with actual accounting software
test_real_accounting_software_formats() {
  echo "💼 Testing real accounting software compatibility..."
  
  # Test QuickBooks Desktop compatibility
  test_quickbooks_desktop_compatibility
  
  # Test QuickBooks Online compatibility
  test_quickbooks_online_compatibility
  
  # Test Xero compatibility
  test_xero_compatibility
  
  # Test Tally Prime compatibility
  test_tally_prime_compatibility
  
  # Test Sage compatibility
  test_sage_compatibility
  
  # Validate file format accuracy
  validate_export_file_format_accuracy
}
```

## 📈 MIS ACCURACY VALIDATION AND BUSINESS VALUE

### **87% Baseline → 95% Enhanced Accuracy Measurement**
```bash
# Comprehensive MIS accuracy validation with business impact
validate_mis_accuracy_and_business_impact() {
  echo "📈 Validating MIS Accuracy and Business Impact..."
  
  # 1. Baseline accuracy measurement
  echo "📊 Measuring baseline accuracy..."
  baseline_accuracy=$(measure_baseline_mis_accuracy)
  
  # 2. Progressive enhancement accuracy
  echo "🚀 Measuring enhanced accuracy..."
  enhanced_accuracy=$(measure_enhanced_mis_accuracy)
  
  # 3. Business appropriateness validation
  echo "🏢 Validating business appropriateness..."
  business_appropriateness=$(validate_business_categorization_appropriateness)
  
  # 4. Customer success metrics
  echo "🎯 Measuring customer success..."
  customer_success_metrics=$(measure_customer_success_metrics)
  
  # Report comprehensive results
  report_mis_accuracy_and_business_impact "$baseline_accuracy" "$enhanced_accuracy" "$business_appropriateness" "$customer_success_metrics"
}
```

### **Progressive Enhancement Accuracy Testing**
```bash
# Test progressive enhancement accuracy improvements
test_progressive_enhancement_accuracy() {
  echo "🚀 Testing Progressive Enhancement Accuracy..."
  
  # Test historical enhancement
  echo "📜 Testing historical enhancement..."
  historical_improvement=$(test_historical_enhancement_accuracy)
  
  # Test schema enhancement
  echo "🏗️ Testing schema enhancement..."
  schema_improvement=$(test_schema_enhancement_accuracy)
  
  # Test vendor mapping enhancement
  echo "🏪 Testing vendor mapping enhancement..."
  vendor_improvement=$(test_vendor_mapping_accuracy)
  
  # Test combined enhancements
  echo "🎯 Testing combined enhancements..."
  combined_accuracy=$(test_combined_enhancement_accuracy)
  
  # Validate business impact of enhancements
  validate_enhancement_business_impact "$historical_improvement" "$schema_improvement" "$vendor_improvement" "$combined_accuracy"
}
```

## ⚡ PERFORMANCE UNDER REAL CONDITIONS

### **Real-World Performance Validation**
```bash
# Test performance with real customer conditions
test_real_world_performance_conditions() {
  echo "⚡ Testing Real-World Performance Conditions..."
  
  # 1. High-volume data processing
  echo "📊 Testing high-volume performance..."
  test_high_volume_performance
  
  # 2. Concurrent user scenarios
  echo "👥 Testing concurrent user performance..."
  test_concurrent_user_performance
  
  # 3. Network condition simulation
  echo "🌐 Testing network condition performance..."
  test_network_condition_performance
  
  # 4. Device constraint testing
  echo "📱 Testing device performance..."
  test_device_constraint_performance
  
  # 5. Business workflow timing
  echo "⏱️ Measuring business workflow timing..."
  measure_business_workflow_performance
}
```

### **Customer Success Metrics Measurement**
```bash
# Measure actual customer success and business value
measure_customer_success_metrics() {
  echo "🎯 Measuring Customer Success Metrics..."
  
  # Time savings measurement
  time_saved=$(calculate_time_savings_vs_manual)
  
  # Accuracy improvement measurement
  accuracy_gain=$(calculate_accuracy_improvement)
  
  # Workflow efficiency measurement
  efficiency_gain=$(calculate_workflow_efficiency_improvement)
  
  # Error reduction measurement
  error_reduction=$(calculate_error_reduction)
  
  # Business value quantification
  business_value=$(quantify_business_value "$time_saved" "$accuracy_gain" "$efficiency_gain" "$error_reduction")
  
  echo "📊 Customer Success Results:"
  echo "  Time Saved: ${time_saved} hours per month"
  echo "  Accuracy Improvement: ${accuracy_gain}%"
  echo "  Workflow Efficiency: ${efficiency_gain}% faster"
  echo "  Error Reduction: ${error_reduction}% fewer errors"
  echo "  Business Value: $${business_value} per month"
}
```

## 🎭 CROSS-PLATFORM CUSTOMER VALIDATION

### **Multi-Device Customer Experience Testing**
```bash
# Test customer experience across different devices and platforms
test_cross_platform_customer_experience() {
  echo "🎭 Testing Cross-Platform Customer Experience..."
  
  # Desktop customer experience
  echo "💻 Testing desktop customer experience..."
  test_desktop_customer_workflows
  
  # Tablet customer experience
  echo "📱 Testing tablet customer experience..."
  test_tablet_customer_workflows
  
  # Mobile customer experience
  echo "📱 Testing mobile customer experience..."
  test_mobile_customer_workflows
  
  # Browser compatibility
  echo "🌐 Testing browser compatibility..."
  test_browser_compatibility_workflows
  
  # Accessibility compliance
  echo "♿ Testing accessibility compliance..."
  test_accessibility_customer_workflows
}
```

### **Real Network Condition Testing**
```bash
# Test customer workflows under real network conditions
test_real_network_conditions() {
  echo "🌐 Testing Real Network Conditions..."
  
  # Slow connection simulation
  test_slow_connection_customer_workflows
  
  # Intermittent connectivity simulation
  test_intermittent_connectivity_workflows
  
  # Mobile network simulation
  test_mobile_network_workflows
  
  # Offline capability testing
  test_offline_capability_workflows
}
```

## 🎯 INTELLIGENT QUALITY GATES FOR CUSTOMER SUCCESS

### **Customer Success Quality Gates**
```bash
# Enforce customer success quality gates
enforce_customer_success_quality_gates() {
  echo "🎯 Enforcing Customer Success Quality Gates..."
  
  # Critical customer success gates (BLOCKING)
  echo "🚫 Critical customer success gates..."
  
  # MIS setup must complete in <5 minutes
  if ! mis_setup_time_acceptable; then
    echo "❌ BLOCKING: MIS setup time exceeds 5-minute target"
    create_mis_setup_performance_todos
    exit 1
  fi
  
  # Baseline accuracy must be 87%+
  if ! baseline_accuracy_acceptable; then
    echo "❌ BLOCKING: Baseline accuracy below 87% requirement"
    create_accuracy_improvement_todos
    exit 1
  fi
  
  # Customer workflows must be functional
  if ! customer_workflows_functional; then
    echo "❌ BLOCKING: Customer workflows not functional"
    create_workflow_failure_todos
    exit 1
  fi
  
  # Warning customer success gates (NON-BLOCKING)
  echo "⚠️ Warning customer success gates..."
  check_performance_warnings
  check_usability_warnings
  check_business_value_warnings
}
```

### **Customer Issue Todo Creation**
```bash
# Create todos for customer success issues
create_customer_success_todos() {
  echo "📝 Creating customer success todos..."
  
  # Use TodoWrite to track customer issues
  if [[ "$customer_workflow_issues_found" == "true" ]]; then
    create_customer_workflow_todos
  fi
  
  if [[ "$accuracy_issues_found" == "true" ]]; then
    create_accuracy_improvement_todos
  fi
  
  if [[ "$performance_issues_found" == "true" ]]; then
    create_performance_optimization_todos
  fi
  
  if [[ "$usability_issues_found" == "true" ]]; then
    create_usability_improvement_todos
  fi
}
```

## 🎛️ CUSTOMER-FOCUSED CROSS-COMMAND ORCHESTRATION

### **Customer Success Pipeline Coordination**
Ensure all commands work together for optimal customer experience:

**Preparation for `/deploy`**: Validate all customer workflows are production-ready
**Coordination with `/test`**: Ensure backend foundation supports customer workflows
**Integration with `/visual-consistency`**: Validate professional customer experience
**Alignment with `/develop`**: Prioritize customer workflow fixes in development

### **Customer Success Context Sharing**
```typescript
interface CustomerSuccessContext {
  customerWorkflowHealth: {
    misSetupWorkflow: "EXCELLENT" | "GOOD" | "POOR";
    fileProcessingWorkflow: "EXCELLENT" | "GOOD" | "POOR";
    transactionManagementWorkflow: "EXCELLENT" | "GOOD" | "POOR";
    exportWorkflow: "EXCELLENT" | "GOOD" | "POOR";
  };
  
  businessValueMetrics: {
    timeSavingsPerMonth: number;
    accuracyImprovement: number;
    workflowEfficiencyGain: number;
    errorReductionPercentage: number;
    monthlyBusinessValue: number;
  };
  
  customerSatisfactionMetrics: {
    misSetupSatisfaction: number;
    usabilityRating: number;
    performanceSatisfaction: number;
    overallCustomerSatisfaction: number;
  };
  
  performanceMetrics: {
    misSetupTime: number;
    fileProcessingTime: number;
    transactionReviewTime: number;
    exportGenerationTime: number;
  };
}
```

## 📊 CUSTOMER SUCCESS METRICS

### **Customer Workflow Success Criteria**
- [ ] **MIS Setup Time**: <5 minutes average setup time achievement
- [ ] **Baseline Accuracy**: 87%+ AI categorization accuracy without training
- [ ] **Enhanced Accuracy**: 95%+ accuracy with progressive enhancements
- [ ] **Business Appropriateness**: 100% business-appropriate categorizations
- [ ] **Workflow Efficiency**: Measurable time savings vs manual processes

### **Business Value Success Criteria**
- [ ] **Time Savings**: Minimum 10 hours saved per month per customer
- [ ] **Accuracy Improvement**: Minimum 20% improvement over manual categorization
- [ ] **Error Reduction**: Minimum 50% reduction in categorization errors
- [ ] **Workflow Speed**: Minimum 3x faster than manual processes
- [ ] **Customer Satisfaction**: Minimum 90% customer satisfaction rating

### **Performance Success Criteria**
- [ ] **MIS Setup Performance**: 100% of setups complete in <5 minutes
- [ ] **File Processing Performance**: 1000+ transactions processed in <2 minutes
- [ ] **Export Performance**: All formats generated in <30 seconds
- [ ] **Cross-Platform Performance**: Consistent experience across all devices
- [ ] **Network Performance**: Functional under slow connection conditions

## 📚 CUSTOMER SUCCESS DOCUMENTATION EVOLUTION

### **Real-Time Customer Journey Updates**
**MANDATORY**: Update documentation with real customer testing results:

1. **Update Customer Journeys** (`docs/04-CUSTOMER-JOURNEYS.md`):
   - Record actual customer workflow completion times and success rates
   - Document real customer feedback and experience insights
   - Update customer success metrics with measured business value
   - Track customer workflow optimization opportunities

2. **Update Current Status** (`docs/01-CURRENT-STATUS.md`):
   - Update customer workflow validation completion status
   - Record customer success metrics and business value achievements
   - Document customer experience improvements implemented
   - Track customer satisfaction benchmarks and trends

3. **Update Todo System** (`docs/todos-persistent.yaml`):
   - Mark customer workflow validation tasks as completed
   - Create todos for any customer experience issues discovered
   - Update customer success completion percentages
   - Record customer testing timestamps and efficiency metrics

### **Customer Success Issue Resolution Protocol**

When customer testing discovers issues affecting customer success:

1. **Critical Customer Fixes**: Fix customer workflow blocking issues immediately
2. **Customer Experience Enhancement**: Implement improvements that enhance customer satisfaction
3. **Performance Optimization**: Address performance issues affecting customer workflows
4. **Business Value Optimization**: Enhance features that increase customer business value
5. **Documentation Evolution**: Update customer journeys with real testing insights

---

**REAL CUSTOMER SCENARIO TESTING COMMAND**: Validates authentic customer scenarios with MIS accuracy measurement, business value quantification, and cross-platform performance validation. Focuses on 87% baseline → 95% enhanced accuracy achievement while measuring real time savings, workflow efficiency, and customer satisfaction to ensure genuine business value delivery.