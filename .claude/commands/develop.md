---
description: 'Unified backend development and integration validation with context-aware execution'
allowed-tools: ['Bash', 'Read', 'Edit', 'Write', 'MultiEdit', 'Glob', 'Grep', 'TodoWrite', 'mcp__postgres-dev__query', 'mcp__postgres-dev__describe_table', 'mcp__postgres-dev__list_tables', 'mcp__playwright__browser_navigate', 'mcp__playwright__browser_network_requests', 'mcp__playwright__browser_console_messages']
memory-context: ['@docs/01-CURRENT-STATUS.md', '@docs/02-SYSTEM-ARCHITECTURE.md', '@docs/todos-persistent.yaml']
arguments: 'Optional: resume|fix|backend|integration|api|production|quick'
---

# Unified Development - Backend Implementation + Integration Validation

**Purpose**: Complete backend development with integrated validation and context-aware execution  
**Scope**: Service implementation, API completion, integration testing, and progress tracking  
**Intelligence**: Session recovery, todo-driven development, and adaptive execution paths

## 🔍 INTELLIGENT STATE DETECTION

### Current Development Context
- **Service Status**: !`pgrep -f "uvicorn" || echo "API not running"`
- **Database Status**: !`pnpm db || echo "Database connection issues"`
- **Git Status**: !`git status --porcelain | head -5`
- **Environment**: !`pwd && uname -a`

### Session Context Analysis
Use TodoWrite to analyze previous session state and determine execution path:
1. **Check existing todos** for incomplete development work
2. **Analyze conversation context** to understand recent development decisions
3. **Detect critical issues** requiring immediate attention
4. **Prioritize implementation tasks** based on customer impact and dependencies

## 📋 CONTEXT-AWARE EXECUTION PATHS

### Execution Path Selection
Based on `$ARGUMENTS` and detected state:

#### **Default/Fresh Start** (`/develop` or `/develop fresh`)
Complete development cycle with intelligent prioritization:
1. Create comprehensive development todos
2. Execute backend implementation with integration validation
3. Document progress and update architecture status

#### **Resume Previous Work** (`/develop resume`)
Continue from existing todos and session context:
1. Load existing todos and analyze incomplete work
2. Resume exactly where previous session ended
3. Validate previous work still functions correctly
4. Continue development with learned context

#### **Crisis Response** (`/develop fix`)
Emergency mode for critical system failures:
1. Detect critical failures automatically
2. Prioritize only blocking issues
3. Execute emergency fixes with minimal testing
4. Quick validation and progress documentation

#### **Backend Focus** (`/develop backend`)
Pure backend implementation without integration testing:
1. Complete missing API implementations
2. Fix service communication issues  
3. Resolve database integration problems
4. Update authentication and security systems

#### **Integration Focus** (`/develop integration`)
Pure integration validation without new implementation:
1. Test service-to-service communication
2. Validate database operations and data integrity
3. Test customer workflow reliability
4. Verify external service integrations

#### **API Development** (`/develop api`)
Specific API endpoint development and testing:
1. Complete missing API endpoints from architecture docs
2. Fix broken API responses and error handling
3. Validate API authentication and authorization
4. Test API performance and optimization

#### **Production Readiness** (`/develop production`)
Production deployment preparation and validation:
1. Assess production readiness across all services
2. Fix deployment blocking issues
3. Validate production environment configuration
4. Test production workflow reliability

#### **Quick Validation** (`/develop quick`)
Essential validation for rapid feedback:
1. Quick service health checks
2. Essential API endpoint validation
3. Critical workflow testing
4. Brief progress documentation

## 🎯 UNIFIED DEVELOPMENT WORKFLOW

### Phase 1: Context Analysis & Todo Management

#### State Analysis & Todo Creation
```bash
# Intelligent state detection and todo management
analyze_development_context() {
  echo "🔍 Analyzing development context..."
  
  # Service health assessment
  api_status=$(pgrep -f "uvicorn" && echo "Running" || echo "Stopped")
  db_status=$(pnpm db >/dev/null 2>&1 && echo "Connected" || echo "Disconnected")
  
  # Git analysis for uncommitted changes
  git_changes=$(git status --porcelain | wc -l)
  
  # Create contextual todos based on detected issues
  create_development_todos_based_on_state
}
```

#### Todo-Driven Development Planning
Use TodoWrite to create comprehensive development plan:
- **Critical Issues**: Service failures, database problems, authentication breaks
- **Implementation Gaps**: Missing APIs, incomplete functionality, broken workflows
- **Integration Tasks**: Service communication, data validation, workflow testing
- **Documentation Updates**: Architecture status, progress tracking, completion metrics

### Phase 2: Backend Implementation with Integration Validation

#### Service Implementation & API Development
Complete backend development per @ imported architecture specifications:

**Export System Implementation**:
- Complete all 10 accounting format exports (QuickBooks, Xero, Sage, Tally, etc.)
- Real export file generation with format validation
- Export readiness APIs with business rule checking
- Integration testing with accounting software compatibility

**MIS Categorization System**:
- Complete MIS hierarchy service with industry templates
- AI categorization with 87% baseline accuracy (enhanced to 95%+)
- Progressive enhancement detection and application
- Business context integration and continuous learning

**File Processing Pipeline**:
- Enhanced file upload with schema detection
- Multi-format support (Excel, CSV, OFX, QBO, PDF)
- Real-time processing with WebSocket updates
- Error handling and recovery workflows

**Transaction Management**:
- Complete CRUD operations with bulk processing
- Advanced filtering, search, and pagination
- Review queue with AI suggestions
- Audit trail and transaction history

**Authentication & Security**:
- JWT token management with refresh strategies
- Role-based access control (Owner, Accountant, Bookkeeper, Viewer)
- Tenant isolation with row-level security
- Security audit logging and monitoring

#### Integration Validation During Development
Validate each implementation with integrated testing:

**Service Communication Testing**:
- API endpoint validation with real data
- Database integration with transaction testing
- External service communication (Vertex AI, Cloud SQL)
- Performance testing and optimization

**Customer Workflow Validation**:
- Complete MIS setup workflow (5-minute target)
- File upload and processing workflow reliability
- Transaction review and categorization workflow
- Export and accounting software integration

**Data Integrity Testing**:
- Database consistency and constraint validation
- Tenant isolation and security testing
- Transaction accuracy and categorization validation
- Export file integrity and compatibility testing

### Phase 3: Comprehensive Validation & Documentation

#### Multi-Layer System Validation
Execute comprehensive testing across all integration points:

**Database Integration Testing**:
```bash
# Use MCP PostgreSQL tools for validation
validate_database_integration() {
  # Schema validation
  mcp__postgres-dev__describe_table public transactions
  mcp__postgres-dev__describe_table public categories
  
  # Data integrity checks
  mcp__postgres-dev__query "SELECT COUNT(*) FROM transactions WHERE tenant_id = 3"
  mcp__postgres-dev__query "SELECT COUNT(*) FROM categories WHERE tenant_id = 3"
  
  # Performance validation
  mcp__postgres-dev__explain_query "SELECT * FROM transactions WHERE tenant_id = 3 LIMIT 100"
}
```

**API Integration Testing**:
```bash
# Comprehensive API validation
validate_api_integration() {
  # Authentication testing
  test_auth_endpoints
  
  # Core API testing
  test_dashboard_apis
  test_upload_apis
  test_transaction_apis
  test_export_apis
  
  # Performance testing
  test_api_response_times
}
```

**Customer Workflow Testing**:
```bash
# Use MCP Playwright for workflow validation
validate_customer_workflows() {
  # MIS setup workflow
  mcp__playwright__browser_navigate "http://localhost:4200"
  test_complete_mis_setup_workflow
  
  # File processing workflow
  test_upload_processing_workflow
  
  # Transaction review workflow
  test_transaction_review_workflow
  
  # Export workflow
  test_export_workflow
}
```

#### Progress Documentation & Architecture Updates
Update documentation with implementation results:

**System Architecture Evolution**:
- Mark all backend services as IMPLEMENTED in docs/02-SYSTEM-ARCHITECTURE.md
- Update API endpoint specifications with real performance data
- Document integration patterns and service communication
- Record security implementation and compliance status

**Current Status Updates**:
- Update backend completion percentage to reflect actual implementation
- Record API performance metrics and benchmarks achieved
- Document export system completion and format support
- Track customer workflow success rates and reliability metrics

**Todo System Management**:
- Mark completed development tasks as finished with timestamps
- Create new todos for any issues discovered during implementation
- Update development completion tracking with quality metrics
- Record implementation learnings and optimization opportunities

## 🚀 INTELLIGENT IMPLEMENTATION PRIORITIES

### Critical Path Detection (Auto-Prioritized)
Automatically detect and prioritize critical issues:

1. **Service Failures** - APIs not responding, database disconnections, authentication breaks
2. **Customer Blockers** - Issues preventing core user workflows and business operations
3. **Integration Breaks** - Service communication failures, data corruption, workflow interruptions
4. **Data Integrity Issues** - Database consistency problems, transaction accuracy, tenant isolation
5. **Security Vulnerabilities** - Authentication bypasses, unauthorized access, data exposure

### Implementation Focus Matrix
```typescript
interface DevelopmentPriorities {
  // P0: System Breaking (Fix Immediately)
  critical: [
    "API services not responding",
    "Database connection failures", 
    "Authentication system broken",
    "Customer workflow completely blocked"
  ];
  
  // P1: Customer Impact (Fix Same Session)
  high: [
    "Export functionality broken",
    "File upload processing failures",
    "Transaction categorization not working",
    "MIS setup incomplete"
  ];
  
  // P2: Feature Gaps (Complete During Development)
  medium: [
    "Missing API endpoints",
    "Incomplete integration testing",
    "Performance optimization needed",
    "Documentation updates required"
  ];
  
  // P3: Polish & Enhancement (Address If Time Permits)
  low: [
    "Code quality improvements",
    "Additional test coverage",
    "Performance fine-tuning",
    "Documentation enhancements"
  ];
}
```

## 📊 SESSION RECOVERY & CONTEXT MANAGEMENT

### Todo-Driven Development Pattern
```typescript
// Automatic session management and recovery
interface SessionContext {
  previousTodos: "Load incomplete work from previous session";
  conversationContext: "Understand recent development decisions and progress";
  gitState: "Analyze recent commits and uncommitted changes";
  serviceState: "Detect running services and configurations";
  
  recoveryStrategy: {
    completeResume: "Continue exactly where previous session ended";
    contextualResume: "Adapt based on changes since last session";
    freshStart: "Begin new development cycle with learned context";
    crisisMode: "Handle critical failures discovered during recovery";
  };
}
```

### Context Integration & Memory
**Conversation Integration**: Reference recent development work, decisions, and discoveries
**Git History Awareness**: Understand recent commits, branches, and development patterns
**Service Continuity**: Maintain awareness of running services, configurations, and states
**Progress Persistence**: Use TodoWrite for cross-session development continuity

## 🔧 ADAPTIVE EXECUTION STRATEGIES

### Dynamic Execution Based on Detected State

#### Normal Development Mode (Services Running, No Critical Issues)
```bash
execute_normal_development() {
  echo "🚀 Executing normal development workflow..."
  
  # 1. Complete backend implementation
  implement_missing_backend_functionality
  
  # 2. Validate with integration testing
  execute_integration_validation
  
  # 3. Document progress and update architecture
  update_documentation_with_results
  
  # 4. Update todos with completion status
  mark_completed_todos_and_create_new_ones
}
```

#### Crisis Response Mode (Critical Failures Detected)
```bash
execute_crisis_response() {
  echo "🚨 Crisis mode: Critical issues detected..."
  
  # 1. Emergency assessment and triage
  detect_and_prioritize_critical_issues
  
  # 2. Fix only blocking problems
  execute_emergency_fixes
  
  # 3. Quick validation
  minimal_validation_testing
  
  # 4. Document emergency actions
  update_todos_with_crisis_resolutions
}
```

#### Resume Mode (Session Recovery)
```bash
execute_session_resume() {
  echo "🔄 Resuming from previous session..."
  
  # 1. Load and analyze previous todos
  analyze_incomplete_work_from_todos
  
  # 2. Validate previous work still functions
  validate_previous_implementation_integrity
  
  # 3. Continue development where left off
  continue_development_from_context
  
  # 4. Update progress with resumed work
  document_resumed_development_progress
}
```

## 📈 SUCCESS METRICS & COMPLETION TRACKING

### Development Completion Indicators
- [ ] **All Critical APIs Responding**: Authentication, upload, categorization, export endpoints operational
- [ ] **Database Operations Reliable**: Connections stable, queries optimized, data integrity maintained
- [ ] **Service Integrations Validated**: Backend-frontend communication, external service connectivity
- [ ] **Customer Workflows Functional**: MIS setup, file processing, transaction review, export workflows
- [ ] **Export System Operational**: All 10 accounting formats generating valid files
- [ ] **Security Properly Implemented**: JWT authentication, role-based access, tenant isolation

### Real-Time Progress Tracking
```typescript
interface DevelopmentProgress {
  backendImplementation: {
    exportSystem: "COMPLETE" | "IN_PROGRESS" | "NOT_STARTED";
    misCategorizationService: "COMPLETE" | "IN_PROGRESS" | "NOT_STARTED";
    fileProcessingPipeline: "COMPLETE" | "IN_PROGRESS" | "NOT_STARTED";
    transactionManagement: "COMPLETE" | "IN_PROGRESS" | "NOT_STARTED";
    authenticationSecurity: "COMPLETE" | "IN_PROGRESS" | "NOT_STARTED";
  };
  
  integrationValidation: {
    serviceCommunication: "VALIDATED" | "TESTING" | "FAILED";
    databaseIntegration: "VALIDATED" | "TESTING" | "FAILED";
    customerWorkflows: "VALIDATED" | "TESTING" | "FAILED";
    externalServices: "VALIDATED" | "TESTING" | "FAILED";
  };
  
  qualityAssurance: {
    codeQuality: "PASSING" | "ISSUES_FOUND" | "NOT_CHECKED";
    performanceOptimization: "OPTIMIZED" | "ACCEPTABLE" | "NEEDS_WORK";
    securityValidation: "SECURE" | "VULNERABILITIES_FOUND" | "NOT_TESTED";
    documentationCompleteness: "COMPLETE" | "PARTIAL" | "MISSING";
  };
}
```

## 🎛️ INTELLIGENT COMMAND ORCHESTRATION

### Cross-Command Intelligence & Preparation
**Preparation for `/test`**: Ensure all development work complete and stable before comprehensive testing
**Preparation for `/uat`**: Validate customer workflows are functional and ready for user acceptance testing  
**Preparation for `/visual-consistency`**: Confirm UI changes don't break backend functionality
**Preparation for `/deploy`**: Verify production readiness and deployment prerequisites

### Documentation Evolution During Development
**System Architecture Updates**: Real-time implementation status updates in docs/02-SYSTEM-ARCHITECTURE.md
**Current Status Tracking**: Development progress and completion metrics in docs/01-CURRENT-STATUS.md  
**Todo Persistence**: Cross-session development continuity through docs/todos-persistent.yaml
**Pattern Learning**: Capture effective development patterns in .claude/memory/consolidated-development-patterns.md

## 🔄 DEVELOPMENT ISSUE RESOLUTION PROTOCOL

### When Development Discovers Issues, Gaps, or Improvement Opportunities

#### Issue Discovery & Analysis
1. **Analyze Root Cause**: Understand if issue is implementation gap, integration failure, or design flaw
2. **Assess Impact Scope**: Determine if issue affects single service, system layer, or entire platform
3. **Evaluate Fix Strategy**: Consider immediate patch vs systematic improvement vs architectural change
4. **Research Best Practices**: Investigate current development methodologies for similar challenges

#### Intelligent Resolution Strategy
1. **Critical System Fixes**: Address blocking issues that prevent core functionality immediately
2. **Strategic Implementation**: Build solutions that benefit multiple areas of the system
3. **Future-Proof Architecture**: Choose implementations that scale well and align with system evolution
4. **Integration-First Approach**: Ensure all fixes are validated through integration testing

#### Documentation & Learning Evolution
1. **Capture Implementation Decisions**: Document why specific solutions were chosen over alternatives
2. **Update Architecture Docs**: Evolve system architecture based on real implementation learnings
3. **Enhance Development Patterns**: Record successful implementation approaches for future development
4. **Create Reusable Solutions**: Extract successful patterns into reusable development frameworks

### Continuous Improvement Integration
1. **Pattern Recognition**: Identify recurring development challenges and create systematic solutions
2. **Performance Optimization**: Continuously improve system performance based on implementation insights
3. **Cross-Service Consistency**: Ensure development solutions work well across all system components
4. **Scalability Planning**: Consider how development solutions will scale with system growth

---

**UNIFIED DEVELOPMENT COMMAND**: Intelligently combines backend implementation and integration validation with context-aware execution, todo-driven development, and adaptive strategies. Provides seamless session recovery, comprehensive progress tracking, and systematic issue resolution while evolving documentation and architecture based on real implementation learnings.