# Codebase Index

This index shows the key source files and their main exports/definitions.

## Root Files
- `.gitignore`
- `CLAUDE.md`
- `README.md`
- `SECURITY-SETUP.md`
- `eslint.base.config.mjs`
- `package.json`
- `pyproject.toml`

## API (FastAPI Backend)
- `docs/api/`
- `pyproject.toml`
- `requirements.txt`
- `scripts/`
  - `add_file_schemas_table.py`: create_file_schemas_table, verify_schema_persistence_setup, main
  - `add_onboarding_fields.py`: add_onboarding_fields, main
  - `add_onboarding_tables.py`: get_table_name, add_onboarding_tables
  - `add_transaction_columns_migration.py`: add_transaction_columns, verify_migration, main
  - `cleanup_auth.py`: cleanup_auth, main
  - `create_current_tables.py`: create_missing_tables
  - `create_performance_indexes.py`: create_indexes, main
  - `create_sample_downloads.py`: create_sample_files
  - `create_tenant_table.py`: create_tenant_table, main
  - `fix_jwt_algorithm.py`
  - `generate_openapi_docs.py`: generate_openapi_schema, save_schema_files, generate_documentation_summary, main
  - `optimize_database_performance.py`: DatabaseOptimizer, connect, disconnect, analyze_current_indexes, create_performance_indexes
  - `performance_index_optimization.py`: DatabaseIndexOptimizer, initialize, create_performance_indexes, analyze_query_performance, generate_performance_report
  - `performance_migration.py`: run_performance_migration
  - `performance_optimization_implementation.py`: UltraPerformanceOptimizer, initialize, apply_ultra_performance_optimizations, validate_performance_improvements, generate_performance_report
  - `production_user_setup.py`: create_production_users
  - `query_transactions.py`: query_transactions
  - `setup_demo_data.py`: setup_demo_data
  - `setup_role_based_accounts.py`: setup_role_based_accounts, migrate_old_accounts
  - `time_series_accuracy_measurement.py`: main
  - `validate_performance_improvements.py`: PerformanceValidator, validate_all_endpoints, main
  - `verify_auth_performance.py`: test_auth_performance
- `src/`
  - `__init__.py`
  - `giki_ai_api/`
    - `__init__.py`
    - `api/v1/`
      - `endpoints/`
        - `accuracy.py`: CreateAccuracyTestRequest, AccuracyTestResponse, create_accuracy_test, run_accuracy_test, list_accuracy_tests
    - `cli/`
      - `__init__.py`
      - `onboarding_data_cli.py`: DBSession, CLIFileProcessingError, generate_secure_onboarding_password, cli_main_async_logic, main_cli_runner_sync_wrapper
    - `core/`
      - `__init__.py`
      - `cloud_run_main.py`
      - `config.py`: Settings, SensitiveInfoFilter, database_host, database_port, database_user
      - `database.py`: Database, PoolMonitor, get_database_url, parse_database_url, retry_db_operation
      - `database_health.py`: CircuitState, DatabaseCircuitBreaker, DatabaseHealthMonitor, call, record_failure
      - `database_monitoring.py`: QueryMetrics, ConnectionPoolMetrics, DatabaseMonitor, start_monitoring, stop_monitoring
      - `database_warmup.py`: DatabaseWarmup, warmup_connections, health_check, close, startup_database_warmup
      - `dependencies.py`: get_tenant_by_id, get_current_user_with_tenant, get_current_tenant_id, get_categorization_service, get_vertex_ai_client_from_app_state
      - `jwt_keys.py`: generate_rsa_key_pair, ensure_jwt_keys, get_private_key, get_public_key, rotate_keys
      - `main.py`: CustomerAccountNode, CustomerHierarchy, StructuredFormatter, AgentCommandRequest, AgentCommandResponse
      - `secure_config.py`: SecureSettings, validate_database_url_security, is_production, get_cors_origins, get_security_headers
      - `secure_dependencies.py`: get_current_user_with_tenant, get_current_tenant_id
      - `security_middleware.py`: security_headers_middleware, request_id_middleware
      - `status_check.py`: check_environment, main
    - `data/`
      - `__init__.py`
    - `domains/`
      - `__init__.py`
      - `accuracy/`
        - `__init__.py`
        - `ai_judge_agent.py`: JudgmentConfidence, AIJudgeAgent, judge_categorization, evaluate_improvement_over_original, batch_judge_categorizations
        - `business_appropriateness_agent.py`: BusinessAppropriatenessConfig, BusinessAppropriatenessResult, BusinessAppropriatenessAgent, evaluate_appropriateness, evaluate_batch
        - `judge_agent.py`: AIJudgeAgent, initialize_vertex_ai, evaluate_categorization, batch_evaluate_categorizations, get_evaluation_summary
        - `m2_validation_pipeline.py`: M2ValidationPipeline, validate_historical_data_availability, run_progressive_validation, generate_m2_report, validate_m2_readiness
        - `models.py`: AccuracyTestScenario, AccuracyTestStatus, AIJudgmentResult, AccuracyTest, AccuracyMetric
        - `prompts/`
          - `ai_judge.yaml`
        - `repository.py`: AccuracyRepositoryError, AccuracyRepository, create_accuracy_test, get_accuracy_test, list_accuracy_tests
        - `router.py`: create_accuracy_test, list_accuracy_tests, get_accuracy_test, run_accuracy_test, get_accuracy_metrics
        - `schema_import_service.py`: SchemaImportError, CategorySchemaImportService, import_schema_from_content
        - `schemas.py`: AccuracyTestCreate, AccuracyTestUpdate, AccuracyTestFilter, CategorySchemaCreate, CategorySchemaImport
        - `service.py`: AccuracyMeasurementError, AccuracyMeasurementService, create_accuracy_test, get_accuracy_test, list_accuracy_tests
        - `temporal_accuracy_service.py`: TemporalAccuracyService, load_historical_data, create_temporal_data_split, run_progressive_monthly_validation, get_temporal_validation_status
        - `temporal_accuracy_service_simple.py`: SimpleTemporalAccuracyService, get_temporal_validation_status, run_progressive_monthly_validation, create_temporal_data_split, load_historical_data
        - `tools.py`: create_accuracy_test_tool, run_accuracy_test_tool, get_accuracy_test_tool, list_accuracy_tests_tool, get_test_results_tool
        - `transaction_loader.py`: TransactionLoader, load_from_file, validate_transactions
      - `admin/`
        - `__init__.py`
        - `performance_router.py`: PerformanceMetrics, QueryPerformanceResult, get_performance_health, get_performance_metrics, get_slow_queries
        - `router.py`: CreateTenantRequest, TenantResponse, TenantsListResponse, UpdateTenantRequest, CreateUserRequest
        - `schemas.py`
        - `service.py`: AdminService, create_tenant, get_tenants, update_tenant, create_user
      - `agents/`
        - `conversational_agent.py`: ConversationalAgent, emit_agent_event, emit_agent_typing, emit_agent_response, process_command
        - `router.py`: ChatMessage, ChatResponse, chat_with_agent, get_available_commands, get_agent_status
      - `ai/`
        - `unified_router.py`: AIRequest, AIResponse, ReportParseRequest, ReportRequest, CategorizationRequest
      - `auth/`
        - `__init__.py`
        - `dependencies.py`
        - `models.py`: TenantDB, UserDB, User, UserCreate, UserUpdate
        - `schemas.py`: UserBase, UserCreate, UserUpdate, UserResponse, OptimizedUserResponse
        - `secure_auth.py`: verify_password, verify_password_fast, get_password_hash, create_access_token, create_refresh_token
        - `secure_router.py`: SimpleUserResponse, get_auth_cache_stats, clear_auth_caches, login, read_users_me
        - `tools.py`: authenticate_user_tool, create_user_tool, get_user_by_id_tool, get_user_by_email_tool, validate_access_token_tool
      - `categories/`
        - `__init__.py`
        - `baseline_accuracy_service.py`: BaselineAccuracyService, measure_accuracy, get_training_statistics, store_accuracy_results, get_accuracy_trend
        - `batch_categorization_tool.py`: BatchCategorizationRequest, BatchCategorizationResult, BatchCategorizationService, get_or_create_agent, categorize_batch
        - `bulk_approval_service.py`: BulkActionType, BulkActionRequest, BulkActionResult, BulkApprovalService, process_bulk_action
        - `business_category_mapper.py`: CategoryMatch, BusinessCategoryMapper, categorize_transaction, resolve_category_paths_to_ids, categorize_transaction_with_id
        - `categorization_agent.py`: CircuitBreaker, AgentConfig, CategorySuggestion, CategorizationError, CategorizationAgent
        - `categorization_metrics.py`: CategorizationMetrics, CategorizationMetricsService, categorization_rate, user_engagement_rate, confidence_distribution
        - `category_path_resolver.py`: CategoryResolution, CategoryPathResolver, resolve_path_to_id, resolve_multiple_paths, get_category_suggestions
        - `confidence_based_categorization.py`: CategorizationConfidence, ConfidenceThresholds, TransactionGroup, CategorizationResult, ConfidenceBasedCategorizationService
        - `confidence_scorer.py`: ConfidenceMetrics, ConfidenceScorer, calculate_confidence, update_performance_metrics, get_confidence_explanation
        - `crud_service.py`: CategorizationError, ValidationError, HierarchyTree, CategoryAnalytics, CategoryCrudService
        - `enhancement_detection_service.py`: EnhancementOpportunity, EnhancementAnalysis, EnhancementDetectionService, analyze_upload_for_enhancements
        - `gl_code_router.py`: GLCodeMappingRequest, BulkGLCodeRequest, GLCodeSuggestionRequest, GLCodeValidationResponse, GLCodeHierarchyResponse
        - `gl_industry_templates.py`: GLIndustryTemplates, get_industry_template, get_gl_account_type_for_range, get_suggested_gl_range, get_industry_context_for_ai
        - `hierarchy_setup_service.py`: HierarchySetupService, setup_standard_mis_hierarchy, fix_hierarchy_integrity, convert_flat_to_hierarchical
        - `improvement_router.py`: AccuracyMetrics, AccuracyImprovement, EnhancementOption, EnhancementRequest, CompanyProfileData
        - `mis_categorization_agent.py`: MISCategoryResult, HierarchicalMISResult, MISCategorizationAgent, tenant_id, get_tenant_context
        - `mis_categorization_service.py`: MISCategorizationResult, BusinessContext, MISCategorizationService, categorize_transaction, categorize_batch
        - `mis_template_selection_agent.py`: TemplateSelectionResult, MISTemplateSelectionAgent, analyze_business_context, validate_template_fit
        - `mis_templates.py`: MISTemplates, get_industry_template, get_all_industries, create_categories_from_template
        - `mis_validation_service.py`: ValidationResult, ComprehensiveValidationReport, MISValidationService, validate_comprehensive_mis_structure, quick_validation_check
        - `models.py`: Category, CategoryAccuracy, get_full_path, is_leaf, is_root
        - `pattern_recognition.py`: PatternType, PatternMatch, RecognitionResult, TransactionPatternRecognizer, recognize_patterns
        - `progressive_setup_service.py`: ProgressiveSetupResult, ProgressiveMISSetupService, apply_vendor_mappings, add_gl_codes_to_categories, process_historical_enhancement
        - `prompt_manager.py`: PromptTemplate, PromptManager, load_prompt, format_prompt, get_zero_onboarding_prompt
        - `prompts/`
        - `review_router.py`: ReviewQueueSummary, TransactionSuggestion, TransactionGroup, ReviewQueueResponse, BulkReviewAction
        - `router.py`: GLCodeUpdate, BulkGLUpdate, GLMappingExport, GLCodeValidationRequest, GLCodeSuggestionRequest
        - `schemas.py`: CategoryBase, CategoryCreate, CategoryUpdate, Category, CategoryTree
        - `schemas_vendor.py`: VendorMappingCreate, VendorMappingUpdate, VendorMapping, VendorGrouping, BulkVendorMappingRequest
        - `service.py`: CategorizationError, ValidationError, HierarchyTree, CategorySuggestion, MappingResult
        - `tools.py`: lookup_transaction_category_tool, get_category_taxonomy_tool, create_hierarchical_category_tool, get_categorization_tools
        - `transaction_grouping_service.py`: TransactionPattern, TransactionGroup, TransactionGroupingService, group_transactions_for_review
        - `user_vendor_mapping_service.py`: UserVendorMappingService, get_vendor_groupings, create_vendor_mapping, create_bulk_vendor_mappings, get_user_vendor_mappings
        - `vendor_detection_service.py`: VendorPattern, VendorDetectionResult, VendorDetectionService, detect_vendors, create_vendor_mapping
        - `vendor_lookup_service.py`: VendorSearchContext, VendorLookupResult, VendorLookupService, lookup_vendor_with_context, batch_lookup_vendors
        - `vertex_rag.py`: get_vector_rag_context
      - `dashboard/`
        - `__init__.py`
        - `router.py`: get_dashboard_metrics, get_recent_transactions, get_category_breakdown
        - `service.py`: DashboardError, DashboardService, get_dashboard_metrics, get_recent_transactions, get_category_breakdown
        - `tools.py`: get_dashboard_metrics_tool, get_recent_transactions_tool, get_category_breakdown_tool, get_monthly_trends_tool, get_entity_insights_tool
      - `exports/`
        - `__init__.py`
        - `export_formats.py`: ExportFormat, DateFormat, AmountFormat, ColumnMapping, ExportFormatSpec
        - `router.py`: ExportFormatInfo, ExportRequest, Config, ExportReadinessResponse, get_export_formats
        - `service.py`: ExportService, export_transactions, get_available_formats, validate_export_readiness
      - `files/`
        - `__init__.py`
        - `ai_interpretation_service.py`: AIInterpretationService, agent, interpret_file, interpret_with_storage, convert_to_file_column_mapping
        - `bank_formats.py`: BankRegion, DateFormat, AmountFormat, BankFormatSpec, BankFormatRegistry
        - `batch_upload_processing_tool.py`: UploadProcessingRequest, UploadProcessingResult, BatchUploadProcessingService, process_upload, batch_process_uploads_tool
        - `debit_credit_agent.py`: DebitCreditAgentConfig, DebitCreditAgent, infer_transaction_direction_tool_function, analyze_banking_patterns_tool_function, handle_complex_transactions_tool_function
        - `enhanced_processing_service.py`: EnhancedProcessingService, start_enhanced_processing, get_processing_status, cleanup_processing_state, create_enhanced_processing_service
        - `enhanced_router.py`: ConnectionManager, connect, disconnect, send_progress_update, websocket_progress
        - `models.py`: InterpretationResultStorage, InterpretationColumnMappingStorage, InterpretationCategorizationColumnStorage, FileProcessingLog, InterpretationTemplate
        - `router.py`: IntelligentDataInterpretationService, ColumnsResponse, normalize_headers, process_confirmed_interpretation, get_intelligent_data_interpretation_service
        - `schema_interpretation_agent.py`: SchemaInterpretationAgentConfig, SchemaInterpretationAgent, safe_schema_ai_call, suggest_schema_mapping_tool_function, detect_category_hierarchy_tool_function
        - `schemas.py`: UploadResponse, MultipleUploadResponse, ColumnListResponse, ColumnMapping, SchemaInterpretationResponse
        - `service.py`: FileService, process_file, validate_column_mapping, extract_transactions, is_nan_value
        - `tools.py`: process_file_upload_tool, extract_transactions_tool, validate_column_mapping_tool, get_supported_file_formats_tool, analyze_file_structure_tool
        - `upload_deduplication.py`: FileUploadPolicy, FileDeduplicationResult, UploadDeduplicationService, check_file_upload_duplicates, store_file_hash
      - `integrations/`
        - `__init__.py`
        - `excel_data_service.py`: ExcelDataService, process_nuvie_file, extract_transactions, test_nuvie_processing
      - `intelligence/`
        - `__init__.py`
        - `adk_router.py`: ADKAgent, AgentSession, StartSessionRequest, AgentTransferRequest, PreloadMemoryRequest
        - `conversational_agent.py`: ConversationalAgent, process_command, handle_conversation, handle_upload_command, handle_categorize_command
        - `coordinator_agent.py`: CoordinatorAgentConfig, CoordinatorAgent, adk_agent_transfer_tool_function, unified_workflow_coordination_tool_function, unified_response_handling_tool_function
        - `customer_agent.py`: CustomerFacingAgentConfig, CustomerFacingAgent, query_customer_transactions_tool_function, get_accuracy_metrics_tool_function, search_customer_data_tool_function
        - `router.py`: AccountingSystemDetectionRequest, AccountingSystemDetectionResponse, EntityExtractionRequest, EntityExtractionResponse, AmountProcessingRequest
        - `schemas.py`: AccountingSystemDetectionRequest, AccountingSystemDetectionResponse, EntityExtractionRequest, EntityExtractionResponse, AmountProcessingRequest
        - `service.py`: IntelligenceService, detect_accounting_system, extract_entities, process_confirmed_interpretation, process_amount_fields
        - `tools.py`: detect_accounting_system_tool, extract_entities_tool, normalize_description_tool, process_amount_fields_tool, analyze_transaction_batch_tool
        - `ui_equivalence_tools.py`: upload_files_via_chat_tool_function, navigate_user_to_page_tool_function, generate_downloadable_reports_tool_function, display_charts_in_chat_tool_function, display_tables_in_chat_tool_function
        - `unified_tools.py`: unified_data_access_tool_function, unified_reporting_tool_function, unified_ui_operations_tool_function
        - `websocket_router.py`: websocket_agent_endpoint, websocket_files_endpoint
      - `monitoring/`
        - `__init__.py`
        - `router.py`: PerformanceMetricsResponse, get_performance_metrics, clear_performance_metrics, get_performance_health
      - `onboarding/`
        - `__init__.py`
        - `agent.py`: OnboardingAgentConfig, OnboardingAgent, process_historical_files_tool_function, validate_database_storage_tool_function, create_rag_corpus_tool_function
        - `models.py`: TemporalValidation, Config, RAGCorpus, OnboardingStatus
        - `processing_models.py`: FileProcessingReport, RowProcessingDetail, ColumnStatistic, convert_values_to_string, create_file_processing_report
        - `processing_schemas.py`: ProcessingStatus, RowStatus, DataType, MappingMethod, ValidationError
        - `router.py`: CompanyInfo, MISSetupRequest, MISSetupResponse, Config, Enhancement
        - `schemas.py`: FileColumnMapping, OnboardingFileUpload, MonthlyAccuracyResult, TemporalValidationRequest, TemporalValidationResult
        - `service.py`: OnboardingService, get_onboarding_status, initialize_onboarding_record, store_business_context, apply_mis_template_for_tenant
        - `tools.py`: get_onboarding_status_tool, initialize_onboarding_tool, process_uploaded_file_tool, build_rag_corpus_tool, run_temporal_validation_tool
      - `reports/`
        - `__init__.py`
        - `agent.py`: ReportsAgentConfig, ReportsAgent, generate_spending_reports_tool_function, create_income_statements_tool_function, generate_trend_analysis_tool_function
        - `csv_export_service.py`: ProfessionalCSVExportService, generate_transactions_csv, generate_category_summary_csv, generate_monthly_summary_csv
        - `custom_report_service.py`: CustomReportService, generate_custom_report, save_custom_report, list_custom_reports, get_custom_report
        - `excel_export_service.py`: ProfessionalExcelExportService, ExcelExportError, export_transactions
        - `m1_export_service.py`: M1ExportService, export_m1_verification_workbook, export_m1_csv, export_m1_summary_pdf, get_m1_performance_summary
        - `models.py`: CustomReport, Config
        - `pdf_export_service.py`: ProfessionalPDFExportService, generate_financial_report_pdf
        - `router.py`: get_spending_by_category, get_spending_by_entity, get_income_expense_summary, get_financial_summary, get_monthly_trends
        - `schemas.py`: ReportDateRangeQueryFilters, SpendingByCategoryItem, SpendingByCategoryResponse, EntitySpendingItem, EntitySpendingResponse
        - `service.py`: ReportGenerator, generate_report, get_available_report_types, generate_comprehensive_report
        - `tools.py`: generate_spending_by_category_report_tool, generate_spending_by_entity_report_tool, generate_income_expense_summary_tool, generate_monthly_trend_report_tool
      - `system/`
        - `__init__.py`
        - `performance_router.py`: PerformanceSummaryResponse, SystemStatusResponse, get_performance_summary, get_system_status, get_health_check
      - `transactions/`
        - `__init__.py`
        - `agent.py`: AccountingSystemDetector, AmountFieldProcessor, DescriptionProcessor, LLMEntityExtractor, TransactionIntelligenceAgent
        - `debit_credit_agent.py`: DebitCreditAgentConfig, DebitCreditAgent, infer_debit_credit_tool_function, batch_infer_debit_credit_tool_function, handle_regional_banking_terms_tool_function
        - `duplicate_detection_service.py`: DuplicateMatch, DeduplicationResult, DuplicateDetectionService, detect_duplicates_batch, log_duplicate_decision
        - `models.py`: Upload, Transaction, TransactionCreate, TransactionUpdate, TransactionFilter
        - `router.py`: TransactionResponse, PaginatedTransactionResponse, ReviewQueueItem, ReviewQueueResponse, ConfidenceDistribution
        - `schemas.py`: BaseTransactionSchema, CreateTransactionSchema, TransactionUpdate, TransactionResponse, TransactionListResponse
        - `service.py`: BatchResult, AnalysisResult, ProcessingOptions, TransactionService, create_transaction
        - `tools.py`: get_transaction_by_id_tool, search_transactions_tool, categorize_transaction_tool, extract_entity_from_transaction_tool, get_transaction_analytics_tool
    - `internal_utils/`
      - `__init__.py`
      - `css_utils.py`: cn
    - `main.py`
    - `scripts/`
      - `__init__.py`
      - `run_data_processing.py`
    - `services/`
      - `__init__.py`
      - `core/`
        - `__init__.py`
        - `file_processing.py`: FileProcessingService, process_file_with_mappings
    - `shared/`
      - `__init__.py`
      - `ai/`
        - `__init__.py`
        - `adk_integration.py`: EnhancedFinancialAgent, process_financial_documents_with_artifacts, integrate_external_apis_with_openapi, search_financial_knowledge_with_vertex, coordinate_with_multiple_agents
        - `adk_integration_demo.py`: AdvancedFinancialWorkflowAgent, process_financial_documents_tool_function, integrate_external_financial_apis_tool_function, connect_google_analytics_services_tool_function, demonstrate_comprehensive_workflow
        - `agent_patterns.py`: AgentDiscoveryCard, FinancialLoopAgent, FinancialParallelAgent, FinancialSequentialAgent, AgentOrchestrator
        - `prompt_registry.py`: PromptCategory, PromptVersion, PromptRegistry, format, to_dict
        - `prompts/`
          - `__init__.py`
          - `accounting_prompts.py`
          - `agent_prompts.py`
          - `ai_judge_prompts.py`
          - `analysis_prompts.py`
          - `categorization_prompts.py`
          - `schema_interpretation_prompts.py`
        - `standard_giki_agent.py`: AgentCapabilities, AgentCard, StandardGikiAgent, create_vertex_ai_search_tool, create_enhanced_function_tool
        - `unified_ai.py`: AIOperationType, AIServiceConfig, AIRequest, AIResponse, UnifiedAIService
        - `vertex_ai_lock.py`: my_vertex_ai_function, get_global_vertex_ai_lock, get_global_vertex_ai_model, safe_vertex_ai_call, reset_global_vertex_ai_model
        - `vertex_client.py`: VertexAIClient, SimpleVertexClient, setup_clients, is_ready, get_generative_model
        - `vertex_patterns.py`: AdvancedToolConfig, AdvancedVertexAIClient, create_function_declaration, create_structured_tool, configure_tools
      - `error_handlers.py`: validation_exception_handler, pydantic_validation_exception_handler, serialize_error_item, sqlalchemy_exception_handler, service_not_initialized_exception_handler
      - `exceptions.py`: GikiAIError, ConfigurationError, VertexAIClientError, VertexAIInitializationError, VertexAIRAGOperationError
      - `middleware/`
        - `__init__.py`
        - `caching.py`: CacheEntry, ResponseCacheMiddleware, dispatch, get_cache_stats, clear_cache
        - `error_handling.py`: ErrorHandlingMiddleware, dispatch, setup_error_handling, add_correlation_id
        - `performance.py`: PerformanceMonitoringMiddleware, DatabasePerformanceTracker, PerformanceMetrics, PerformanceMonitor, PerformanceUtils
        - `rate_limiting.py`: RateLimitBucket, RateLimitConfig, RateLimitMiddleware, RateLimiter, consume
        - `timeout_middleware.py`: TimeoutMiddleware, get_timeout_for_endpoint, dispatch
      - `monitoring/`
        - `__init__.py`
        - `performance.py`: FileUploadTracker, track_performance, decorator, async_wrapper, sync_wrapper
      - `routers/`
        - `__init__.py`
        - `progress.py`: progress_event_generator, stream_progress, get_task_status
      - `services/`
        - `__init__.py`
        - `async_service.py`: AsyncTaskResult, EnhancedAsyncService, start_background_workers, stop_background_workers, read_file_async
        - `google_cloud_logging.py`: GoogleCloudLoggingSetup, log_performance_metric, log_security_event, log_business_event, log_ai_event
        - `google_cloud_secrets.py`: GoogleCloudSecretsManager, get_secret, get_database_url, get_auth_secret_key, get_secret_key
        - `monitoring.py`: AlertSeverity, MetricType, Metric, Alert, HealthStatus
        - `progress_tracker.py`: ProgressUpdate, ProcessingStatus, ProgressTracker, start, stop
        - `system/`
          - `__init__.py`
          - `performance_monitor.py`: MetricData, EndpointStats, PerformanceMonitor, OperationTracker, avg_time_ms
        - `websocket_service.py`: WebSocketManager, WebSocketService, connect, disconnect, send_personal_message
      - `utils/`
        - `__init__.py`
        - `error_recovery.py`: CircuitBreakerState, CircuitBreaker, RetryStrategy, call, execute_with_retry
        - `rate_limiter.py`: RateLimiter, wait_if_needed, execute_with_limit
    - `templates/`
      - `monitoring_dashboard.html`
  - `pyproject.toml`
  - `shared/monitoring/`
    - `performance.py`: PerformanceMetric, PerformanceMonitor, to_dict, track_operation, record_metric
- `start_server.py`
- `tests/load/`

## App (React Frontend)
- `generate_secure_secrets.py`: generate_secure_secret, generate_base64_secret, main
- `index.d.ts`: ReactComponent
- `package.json`
- `playwright.config.ts`
- `scripts/`
  - `generate_transaction_data.py`: generate_random_date, generate_description, generate_transactions, main
- `src/`
  - `core/`
    - `app/`
      - `App.tsx`: LoginPage, RegisterPage, GetStartedPage, FirstLoginPage, TransactionReviewPage
    - `config/`
      - `env.ts`: env, isDevelopment, isStaging, isProduction
      - `index.ts`: APP
    - `providers/`
      - `PanelStateContext.tsx`: STORAGE, PanelStateContext, PanelStateProvider, usePanelState
      - `ThemeProvider.tsx`: ThemeProviderContext, ThemeProvider, useTheme
      - `index.ts`
    - `router/`
      - `PrivateRoute.tsx`: PrivateRoute
  - `environments/`
    - `environment.prod.ts`: environment
    - `environment.ts`: environment
  - `features/`
    - `accuracy/`
      - `components/`
        - `AccuracyDashboard.tsx`: AccuracyDashboard
        - `AccuracyMetricsChart.tsx`: AccuracyMetricsChart
        - `AccuracyTestCard.tsx`: AccuracyTestCard, StatusIcon
        - `AccuracyTrendsPanel.tsx`: AccuracyTrendsPanel
        - `HistoricalAccuracyValidator.tsx`: HistoricalAccuracyValidator
        - `HistoricalValidationDashboard.tsx`: HistoricalValidationDashboard
        - `TemporalAccuracyChart.tsx`: TemporalAccuracyChart
        - `TemporalAccuracyValidationPanel.tsx`: TemporalAccuracyValidationPanel
        - `TransactionResultsTable.tsx`: TransactionResultsTable
      - `index.ts`
      - `pages/`
        - `AccuracyTestsPage.tsx`: AccuracyTestsPage
        - `CreateAccuracyTestPage.tsx`: CreateAccuracyTestPage, Icon
        - `HistoricalAccuracyDashboardPage.tsx`: HistoricalAccuracyDashboardPage
        - `HistoricalAccuracyWorkflowPage.tsx`: DataLearningWorkflowPage
        - `SchemaGuidedWorkflowPage.tsx`: StructuredSetupWorkflowPage
        - `TestResultsPage.tsx`: TestResultsPage, StatusIcon
      - `services/`
        - `accuracyService.ts`: AccuracyService, accuracyService
        - `temporalAccuracyService.ts`: TemporalAccuracyService, temporalAccuracyService, useTemporalAccuracy
      - `types/`
        - `accuracy.ts`
    - `admin/`
      - `index.ts`
      - `pages/`
        - `AccountingSyncPage.tsx`: AccountingSyncPage
        - `AuditTrailPage.tsx`: AuditTrailPage
        - `BankConnectionsPage.tsx`: BankConnectionsPage
        - `HelpHubPage.tsx`: HelpHubPage
        - `SettingsPage.tsx`: SettingsPage
        - `UserManagementPage.tsx`: UserManagementPage
    - `agent/`
      - `components/`
        - `EnhancedAgentPanel.tsx`: EnhancedAgentPanel
      - `hooks/`
        - `useAgentCommunication.ts`: useAgentCommunication
      - `index.ts`
      - `pages/`
        - `AgentTestPage.tsx`: AgentTestPage
    - `auth/`
      - `components/`
        - `LoginForm.tsx`: LoginForm
        - `RegistrationForm.tsx`: RegistrationForm
      - `hooks/`
        - `useAuth.ts`: useAuth
        - `useSessionRecovery.ts`: useSessionRecovery, useGlobalSessionRecovery
      - `index.ts`
      - `pages/`
        - `FirstLoginPage.tsx`: FirstLoginPage
        - `GetStartedPage.tsx`: GetStartedPage
        - `LoginPage.tsx`: LoginPage
        - `RegisterPage.tsx`: RegisterPage
      - `providers/`
        - `SessionRecoveryProvider.tsx`: SessionRecoveryProvider
      - `services/`
        - `auth.ts`: ACCESS, REFRESH, getAccessToken, setAccessToken, getRefreshToken
        - `authService.ts`: AUTH, login, refreshToken, register, logout
        - `contactService.ts`: ContactService, contactService
        - `emailVerificationService.ts`: sendVerificationEmail, verifyEmail, resendVerificationEmail
      - `types/`
        - `auth.ts`
    - `categories/`
      - `components/`
        - `AIBatchCategorizationModal.tsx`: AIBatchCategorizationModal
        - `BulkGLCodeManager.tsx`: BulkGLCodeManager
        - `CategoryBatchTool.tsx`: CategoryBatchTool
        - `CategoryManagementView.tsx`: CategoryManagementView
        - `CategorySelector.tsx`: CategorySelector
        - `CategoryTreeView.tsx`: CategoryNode, CategoryTreeView
        - `ConfidenceIndicator.tsx`: ConfidenceIndicator
        - `ConfidenceScoringDisplay.tsx`: ConfidenceScoringDisplay
        - `GLCodeAnalyticsDashboard.tsx`: GLCodeAnalyticsDashboard
        - `GLCodeBulkManager.tsx`: GLCodeBulkManager
        - `GLCodeHierarchyEditor.tsx`: GLCodeHierarchyEditor, TreeNodeComponent, GLCodeDetailsPanel
        - `GLCodeManagementSystem.tsx`: TreeView, TreeViewItem, GLCodeManagementSystem
        - `GLCodeManager.tsx`: GLCodeManagerComponent, GLCodeManager
        - `GLCodeSuggestionItem.tsx`: GLCodeSuggestionItem
        - `MISValidationStatus.tsx`: MISValidationStatus
      - `hooks/`
        - `useCategories.ts`: useCategories
      - `index.ts`
      - `pages/`
        - `CategoriesPage.tsx`: CategoriesPage
        - `CategoryManagementPage.tsx`: CategoryManagementPage
      - `services/`
        - `categoryService.ts`: addCategory, fetchCategories, updateCategory, deleteCategory, categoryService
        - `confidenceService.ts`: ConfidenceService, confidenceService
        - `glCodeService.ts`: GLCodeService, glCodeService
      - `types/`
        - `categorization.ts`
        - `category.ts`
    - `compliance/pages/`
      - `GLCodeCompliancePage.tsx`: GLCodeCompliancePage
    - `dashboard/`
      - `components/`
        - `ActionsGrid.tsx`: ActionsGrid, IconComponent
        - `CategoryBreakdownChart.tsx`: CategoryBreakdownChart
        - `DashboardMetricsGrid.tsx`: DashboardMetricsGrid
        - `DashboardMetricsSkeleton.tsx`: DashboardMetricsSkeleton
        - `EmptyDashboard.tsx`: EmptyDashboard
        - `ExcelDashboard.tsx`: ExcelDashboard
        - `ExportHistoryWidget.tsx`: ExportHistoryWidget
        - `ExportReadinessWidget.tsx`: ExportReadinessWidget
        - `InteractiveCategoryChart.tsx`: COLORS, InteractiveCategoryChart, CustomTooltip
        - `MobileDashboard.tsx`: MobileDashboard
        - `PerformanceDashboard.tsx`: PerformanceDashboard
        - `ProcessingStatusCard.tsx`: ProcessingStatusCard
        - `ProfessionalMetricsGrid.tsx`: DEFAULT, ProfessionalMetricsGrid
        - `RecentTransactionsList.tsx`: RecentTransactionsList
        - `RecentTransactionsSkeleton.tsx`: RecentTransactionsSkeleton
        - `SpendingTrendChart.tsx`: SpendingTrendChart, CustomTooltip
        - `index.ts`
      - `hooks/`
        - `useDashboard.ts`: DEBOUNCE, useDashboard
        - `useExportHistory.ts`: useExportHistory, downloadExport
        - `useExportReadiness.ts`: useExportReadiness
      - `index.ts`
      - `pages/`
        - `DashboardPage.tsx`: DashboardPage
      - `services/`
        - `dashboardService.ts`: DashboardService, dashboardService, getDashboardData, refreshDashboardData, clearDashboardCache
      - `types/`
        - `dashboard.ts`
    - `files/`
      - `components/`
        - `ActivityItem.tsx`: ActivityItem
        - `ColumnMappingModal.tsx`: ESSENTIAL, AMOUNT, FIELD, ColumnMappingModal, NONE
        - `DataUpload.tsx`: DataUpload, MAX
        - `EnhancedFileUpload.tsx`: EnhancedFileUploadComponent, EnhancedFileUpload
        - `FileItem.tsx`: FileItem
        - `FileUploadWithCurrency.tsx`: FileUploadWithCurrency
        - `MultiFileUploadWithCurrency.tsx`: MultiFileUploadWithCurrency
        - `ProcessingExperience.tsx`: ProcessingExperienceComponent, ProcessingExperience
        - `ResultsCelebration.tsx`: ResultsCelebration
        - `index.ts`
        - `onboarding/`
          - `AccuracySimulationChart.tsx`: AccuracySimulationChart
          - `BulkFileUpload.tsx`: BulkFileUpload
          - `FirstTimeUserGuide.tsx`: FirstTimeUserGuide, TooltipProvider
          - `InteractiveCategoryPreview.tsx`: InteractiveCategoryPreview
          - `OnboardingCompletionFlow.tsx`: OnboardingCompletionFlow, CelebrationView, NextStepsView, FeatureTourView
          - `OnboardingOrchestrator.tsx`: OnboardingOrchestrator
          - `ProgressiveUploadWizard.tsx`: WelcomeStep, FilePreparationStep, UploadStep, ProcessingStep, CompletionStep
          - `RealTimeProcessingStatus.tsx`: RealTimeProcessingStatus, Icon
          - `WelcomeTutorialModal.tsx`: WelcomeTutorialModal
        - `production/`
          - `ProductionUploadFlow.tsx`: ProductionUploadFlow
        - `temporal/`
          - `BatchUploadDialog.tsx`: BatchUploadDialog
          - `HistoricalDataUpload.tsx`: HistoricalDataUpload
          - `ProgressiveRAGProgress.tsx`: ProgressiveRAGProgress
          - `RAGCorpusProgress.tsx`: RAGCorpusProgress
          - `TemporalValidationDashboard.tsx`: TemporalValidationDashboard
          - `TemporalValidationFlow.tsx`: TemporalValidationFlow
          - `index.ts`
      - `hooks/`
        - `useFileProcessing.ts`: useFileProcessing
        - `useFileUpload.ts`: useFileUpload
      - `index.ts`
      - `pages/`
        - `EnhancedUploadPage.tsx`: EnhancedUploadPage
        - `OnboardingPage.tsx`: OnboardingPage
        - `ProcessingPage.tsx`: ProcessingPage
        - `ResultsPage.tsx`: ResultsPage
        - `TemporalValidationTestPage.tsx`: TemporalValidationTestPage
        - `UploadPage.tsx`: UploadPage
        - `WelcomePage.tsx`: WelcomePage
      - `services/`
        - `enhancedUploadService.ts`: EnhancedUploadOrchestrator, uploadOrchestrator
        - `fileService.ts`: formatFileSize, uploadFile, uploadMultipleFiles, uploadProductionFiles, uploadProductionFile
        - `filesApi.ts`
        - `processingService.ts`: RealProcessingService, createProcessingService, processFileWithRealAI
        - `schemaInterpretationService.ts`
        - `temporalValidationService.ts`: uploadHistoricalData, runTemporalValidation, startTemporalValidation, getTemporalValidationStatus, getTemporalValidationStatusLegacy
        - `uploadService.ts`: uploadFiles, uploadFile, getFileColumns, getSchemaInterpretation, submitColumnMapping
        - `workPageService.ts`
      - `types/`
        - `upload.ts`
    - `intelligence/`
      - `components/`
        - `EntityDetailSheet.tsx`: EntityDetailSheet
        - `EntityGrid.tsx`: EntityGrid
        - `EntityTable.tsx`: EntityTable
        - `RagCorpusManagement.tsx`: RagCorpusManagement
        - `index.ts`
      - `hooks/`
        - `useAgent.ts`: useAgent
      - `index.ts`
      - `pages/`
        - `AnomalyDetectionPage.tsx`: AnomalyDetectionPage
        - `BudgetVariancePage.tsx`: BudgetVariancePage
        - `CashflowDashboardPage.tsx`: CashflowDashboardPage
        - `FinancialForecastingPage.tsx`: FinancialForecastingPage
        - `KnowledgeHubPage.tsx`: COLORS, KnowledgeHubPage
        - `PatternRecognitionPage.tsx`: PatternRecognitionPage
        - `ProfitLossPage.tsx`: ProfitLossPage
        - `RAGCorpusManagementPage.tsx`: RAGCorpusManagementPage
        - `ROIAnalysisPage.tsx`: ROIAnalysisPage
        - `TrendsAnalysisPage.tsx`: TrendsAnalysisPage
        - `VendorSpendPage.tsx`: VendorSpendPage
      - `services/`
        - `adkAgentService.ts`: ADKAgentService, adkAgentService
        - `ragCorpusService.ts`: createRAGCorpus, listRAGCorpora, deleteRAGCorpus, categorizeWithRAG, getRAGCorpus
      - `types/`
        - `knowledgehub.ts`
    - `onboarding/`
      - `components/`
        - `BusinessContextForm.tsx`: INDUSTRY, COMPANY, BUSINESS, REVENUE, REPORTING
        - `HistoricalDataUpload.tsx`: HistoricalDataUpload
        - `HistoricalFileUpload.tsx`: HistoricalFileUpload
        - `OnboardingWizard.tsx`: OnboardingWizard
        - `SchemaGuidedFlow.tsx`: SchemaGuidedFlow
        - `SchemaUploadInterface.tsx`: SchemaUploadInterface
        - `TemporalAccuracyFlow.tsx`: TemporalAccuracyFlow
        - `ZeroOnboardingFlow.tsx`: ZeroOnboardingFlow, Icon
      - `hooks/`
        - `useMISSetup.ts`: useMISSetup
      - `pages/`
        - `AITrainingPage.tsx`: AITrainingPage
        - `AccuracyValidationPage.tsx`: AccuracyValidationPage
        - `ColumnMappingPage.tsx`: ColumnMappingPage
        - `GoLivePage.tsx`: GoLivePage
        - `MISActivation.tsx`: MISActivation
        - `MISCompanySetup.tsx`: MISCompanySetup
        - `MISDataUpload.tsx`: MISDataUpload
        - `MISEnhancementReview.tsx`: MISEnhancementReview
        - `MISSetupIntroduction.tsx`: MISSetupIntroduction
        - `OnboardingPathSelector.tsx`: OnboardingPathSelector
        - `OnboardingWizardPage.tsx`: OnboardingWizardPage
      - `services/`
        - `misSetupService.ts`
        - `onboardingService.ts`: startOnboarding, uploadOnboardingSchema, uploadHistoricalData, getOnboardingProgress, completeOnboarding
    - `reports/`
      - `components/`
        - `ColumnMappingTable.tsx`: ColumnMappingTable
        - `CustomReportBuilder.tsx`: CustomReportBuilder
        - `ExportFormatSelector.tsx`: ExportFormatSelector
        - `ExportValidationSystem.tsx`: ExportValidationSystem
        - `FileProcessingReportCard.tsx`: FileProcessingReportCard
        - `IncomeVsExpenseReport.tsx`: IncomeVsExpenseReport
        - `PivotTable.tsx`: SimpleField, SimpleDropZone, PivotTable
        - `RowProcessingDetails.tsx`: RowProcessingDetails
        - `SpendingAnalytics.tsx`: SpendingAnalytics, CustomTooltip
        - `SpendingByCategoryReport.tsx`: SpendingByCategoryReport
        - `TransactionListTable.tsx`: TransactionListTable
      - `hooks/`
        - `useReports.ts`: useReports
      - `index.ts`
      - `pages/`
        - `CustomReportBuilderPage.tsx`: CustomReportBuilderPage
        - `ExecutiveSummaryPage.tsx`: ExecutiveSummaryPage
        - `ExpenseTrendsPage.tsx`: ExpenseTrendsPage, transactions
        - `ExportPage.tsx`: ExportPage
        - `FileProcessingReportPage.tsx`: FileProcessingReportPage
        - `FileProcessingReportsList.tsx`: FileProcessingReportsList
        - `GLAnalysisPage.tsx`: GLAnalysisPage
        - `MonthlySummaryPage.tsx`: MonthlySummaryPage, transactions
        - `ReportsIndexPage.tsx`: ReportsPage
        - `ReportsPage.tsx`: ReportsPage, transactions
        - `TaxDashboardPage.tsx`: TaxDashboardPage
      - `services/`
        - `customReportService.ts`: AVAILABLE, STORAGE, generateCustomReport, saveCustomReport, getSavedCustomReports
        - `exportApiService.ts`: ExportApiService
        - `reportService.ts`: getSpendingByCategoryReport, getSpendingByEntityReport, getIncomeVsExpenseReport, getIncomeVsExpenseReportLegacy, exportReport
        - `taxService.ts`: TaxService, taxService
      - `types/`
        - `reports.ts`
    - `transactions/`
      - `components/`
        - `AccuracyProgressTracker.tsx`: AccuracyProgressTracker
        - `BulkActionPanel.tsx`: BulkActionPanel, BulkActionSummary, Icon
        - `ConfidenceIndicator.tsx`: ConfidenceIndicator, Icon, ConfidenceBadge, ConfidenceProgress, ConfidenceSummary
        - `EnhancementOptionCards.tsx`: EnhancementOptionCards
        - `GroupedTransactionReview.tsx`: GroupedTransactionReview
        - `IndividualTransactionReview.tsx`: IndividualTransactionReview
        - `ReviewProgressStats.tsx`: ReviewProgressStats, Icon, CompactReviewStats
        - `ReviewTransactions.tsx`: ReviewTransactionsComponent, ReviewTransactions
        - `TransactionCard.tsx`: TransactionCard
        - `TransactionRow.tsx`: TransactionRow
        - `TransactionStatusBadge.tsx`: TransactionStatusBadge, Icon, StatusDot, WorkflowStatus, CombinedStatus
        - `TransactionTable.tsx`: TransactionTable
        - `index.ts`
      - `hooks/`
        - `useTransactions.ts`: useTransactions
      - `index.ts`
      - `pages/`
        - `DuplicateDetectionPage.tsx`: DuplicateDetectionPage
        - `RecurringRulesPage.tsx`: RecurringRulesPage
        - `ReviewAndImprovementsPage.tsx`: ReviewAndImprovementsPage
        - `ReviewPage.tsx`: ReviewPage
        - `SearchFilterPage.tsx`: SearchFilterPage
        - `SmartReviewPage.tsx`: SmartReviewPage
        - `TransactionAnalysisPage.tsx`: TransactionAnalysisPage
        - `TransactionPipeline.tsx`: TransactionPipeline
        - `TransactionReviewPage.tsx`: TransactionReviewPage
      - `services/`
        - `aiSuggestionService.ts`: getConfidenceExplanation
        - `exportService.ts`: getAvailableFormats
        - `transactionService.ts`: fetchTransactions, fetchTransaction, updateTransactionCategory, updateBatchCategories, getReviewQueue
      - `types/`
        - `transaction.ts`
    - `uploads/components/`
      - `UploadNewTransactions.tsx`: UploadNewTransactions
  - `index.css`
  - `main.tsx`
  - `shared/`
    - `components/`
      - `agent/`
        - `ADKAgentClient.ts`: ADKAgentClient
        - `ADKAgentClientComponent.tsx`: ADKAgentClient
        - `AgentPanel.tsx`: AgentPanel
        - `AgentPanelHandle.tsx`: AgentPanelHandle
        - `AudioInput.tsx`: AudioInput
        - `ChatInterface.tsx`: ChatInterface
        - `ChatInterfaceToggle.tsx`: ChatInterfaceToggle
        - `PanelHeader.tsx`: PanelHeader
        - `ProfessionalAgentPanel.tsx`: ProfessionalAgentPanel
        - `QueryHistory.tsx`: QueryHistory
        - `QuickActions.tsx`: QuickActions
        - `UnifiedAgentPanel.tsx`: UnifiedAgentPanel
      - `datagrid/`
        - `FormulaBar.tsx`: FormulaBar
      - `debug/`
        - `DebugPanel.tsx`: DebugPanel, DebugToggle
      - `error/`
        - `ErrorBoundary.tsx`: ErrorBoundary
        - `FeatureErrorBoundary.tsx`: FileUploadFallback, DashboardFallback, TransactionFallback, AgentPanelFallback, FeatureErrorBoundary
        - `NotFoundPage.tsx`: NotFoundPage
      - `index.ts`
      - `layout/`
        - `AuthLayout.tsx`: AuthLayoutComponent, AuthLayout
        - `EnhancedAppLayout.tsx`: LAYOUT, PANEL, EnhancedAppLayout
        - `GlobalFooter.tsx`: FOOTER, SYSTEM, GlobalFooterComponent, GlobalFooter
        - `GlobalHeader.tsx`: PAGE, GlobalHeaderComponent, GlobalHeader, useConnectionStatus
        - `MainContent.tsx`: MainContentComponent, MainContent
      - `performance/`
        - `PerformanceMonitor.tsx`: PerformanceMonitor
      - `routing/`
        - `IntelligentRoute.tsx`: IntelligentRouteComponent, IntelligentRoute
        - `UserJourneyDebug.tsx`: UserJourneyDebug
      - `ui/`
        - `Modal.tsx`: Modal, ConfirmationModal, DestructiveModal, SuccessModal, ErrorModal
        - `ProgressBar.tsx`: ProgressBar
        - `ScrollContainer.tsx`: ScrollContainer, TableScrollContainer, ChatScrollContainer
        - `TextWithEllipsis.tsx`: TextWithEllipsis
        - `ThemeToggle.tsx`: ThemeToggle
        - `accordion.tsx`: Accordion, AccordionItem, AccordionTrigger, AccordionContent
        - `accuracy-tooltip.tsx`: AccuracyTooltip, IconComponent, CategorizationAccuracyTooltip, ConfidenceScoreTooltip, ProcessingAccuracyTooltip
        - `alert.tsx`: Alert, AlertTitle, AlertDescription
        - `avatar.tsx`: Avatar, AvatarImage, AvatarFallback
        - `badge.tsx`: Badge
        - `button.tsx`: Button, Comp, LoadingSpinner
        - `calendar.tsx`: Calendar
        - `card.tsx`: Card, CardHeader, CardTitle, CardDescription, CardAction
        - `checkbox.tsx`: Checkbox
        - `collapsible.tsx`: Collapsible, CollapsibleTrigger, CollapsibleContent
        - `command.tsx`: Command, CommandDialog, CommandInput, CommandList, CommandEmpty
        - `date-range-picker.tsx`: DateRangePicker
        - `dialog.tsx`: Dialog, DialogTrigger, DialogPortal, DialogClose, DialogOverlay
        - `dropdown-menu.tsx`: DropdownMenu, DropdownMenuTrigger, DropdownMenuGroup, DropdownMenuPortal, DropdownMenuSub
        - `empty-states.tsx`: EmptyState, EmptyTransactions, EmptyCategories, EmptyReports, EmptySearchResults
        - `error-boundary.tsx`: ErrorBoundary, AsyncErrorBoundary, useErrorHandler
        - `error-display.tsx`: ErrorDisplay
        - `error-state.tsx`: ErrorState, APIErrorState, NotFoundErrorState, PermissionErrorState, LoadingErrorState
        - `excel-layout.tsx`: ExcelToolbar, ExcelFormulaBar, ExcelLayout
        - `excel-table.tsx`: ExcelTable
        - `financial-badge.tsx`: FinancialBadge, TrendIcon, CurrencyBadge, PercentageBadge, VarianceBadge
        - `form.tsx`: Form, FormFieldContext, FormField, FormItemContext, FormItem
        - `giki-loading.tsx`: LoadingSpinner, LoadingSkeleton, ProgressBar, LoadingOverlay, LoadingPage
        - `giki-table.tsx`: GikiTable
        - `index.ts`
        - `input.tsx`: Input
        - `label.tsx`: Label
        - `loading.tsx`: Loading, LoadingSkeleton, LoadingText, LoadingCard, LoadingTable
        - `logo.tsx`: Logo
        - `pagination.tsx`: Pagination
        - `performance-monitor.tsx`: PerformanceToast, PerformanceMonitor, StatusIcon, usePerformanceTracking
        - `popover.tsx`: Popover, PopoverTrigger, PopoverContent
        - `progress.tsx`: Progress
        - `radio-group.tsx`: RadioGroup, RadioGroupItem
        - `resizable.tsx`: ResizablePanelGroup, ResizablePanel, ResizableHandle
        - `scroll-area.tsx`: ScrollArea, ScrollBar
        - `select.tsx`: Select, SelectGroup, SelectValue, SelectTrigger, SelectContent
        - `separator.tsx`: Separator
        - `sheet.tsx`: Sheet, SheetTrigger, SheetClose, SheetPortal, SheetOverlay
        - `skeleton.tsx`: Skeleton, TableSkeleton, CardSkeleton, DashboardCardSkeleton, ChartSkeleton
        - `standardized-loading.tsx`: LoadingSpinner, LoadingState, InlineLoading, ButtonLoading, StandardLoadingStates
        - `switch.tsx`: Switch
        - `table.tsx`: Table, TableHeader, TableBody, TableFooter, TableRow
        - `tabs.tsx`: Tabs, TabsList, TabsTrigger, TabsContent
        - `textarea.tsx`: Textarea
        - `toast.tsx`: ToastProvider, ToastViewport, Toast, ToastAction, ToastClose
        - `toaster.tsx`: Toaster
        - `tooltip-button.tsx`: TooltipButton
        - `tooltip.tsx`: TooltipProvider, Tooltip, TooltipTrigger, TooltipContent
        - `unified-error.tsx`: UnifiedError, APIError, NotFoundError, PermissionError, LoadingError
        - `use-toast.ts`: TOAST, reducer, useToast
        - `user-feedback-system.tsx`: PERFORMANCE, CONTEXTUAL, UserFeedbackSystem, SmartLoading, useUserFeedback
        - `workflow-progress.tsx`: WorkflowProgress, UploadWorkflowProgress
    - `constants/`
      - `brand.ts`: BRAND, API, LAYOUT, SPACING, ANIMATIONS
    - `hooks/`
      - `index.ts`
      - `useAccuracyMetrics.ts`: useAccuracyMetrics
      - `useAgentIntegration.ts`: PAGE, useAgentIntegration
      - `useAgentWebSocket.ts`: useAgentWebSocket
      - `useApi.ts`: useApiGet, useApiPost, useApiPut, useApiPatch, useApiDelete
      - `useAuth.ts`
      - `useInterval.ts`: useInterval
      - `useLoadingState.ts`: useLoadingState, useConcurrentLoadingStates
      - `useOnboardingWorkflow.ts`: useOnboardingWorkflow
      - `useProgressTracking.ts`: FileUploadComponent, useProgressTracking
      - `useScrollPosition.ts`: useScrollPosition, useScrollDirection, useInViewport
      - `useTransactionData.ts`: useTransactionData
      - `useUserJourneyStatus.ts`: useUserJourneyStatus
    - `lib/`
      - `logger.ts`: Logger, logger, logPerformance, logAsyncPerformance, trackUserAction
    - `services/`
      - `agentsApi.ts`: AgentsApiService, agentsApi
      - `ai/`
        - `UnifiedAIService.ts`: UnifiedAIService, AI, unifiedAIService
      - `api/`
        - `apiClient.ts`: ApiClient, apiClient
        - `apiOptimization.ts`: ApiOptimizationService, CHUNK, apiOptimization
      - `auth/`
        - `authStore.ts`: useAuthStore
      - `index.ts`
      - `realtime/`
        - `realtimeSync.ts`: RealtimeSyncService, realtimeSync, useRealtimeSync, useRealtimeEmit
      - `websocket/`
        - `AgentWebSocketService.ts`: AgentWebSocketService, agentWebSocketService, useAgentWebSocket
        - `WebSocketService.ts`: EventEmitter, WebSocketService, webSocketService, useWebSocket, useWebSocketStatus
    - `styles/`
      - `accessibility.css`
    - `types/`
      - `api.ts`
      - `categorization.ts`
      - `category.ts`
      - `errors.ts`: PASSWORD, USER, ERROR, validatePassword, createApiError
      - `knowledgehub.ts`
      - `transaction.ts`
    - `utils/`
      - `accessibility.ts`: announce, useFocusTrap, useId, formatCurrencyForScreenReader, skipToMain
      - `api.ts`: getApiBaseUrl, getApiUrl, getWebSocketUrl, getApiHeaders, buildQueryString
      - `brandGradients.ts`: brandGradients, applyGradientHover, gradientButtonStyle, gradientButtonHoverStyle
      - `dataQuality.tsx`: SafeDataDisplay, safeNumber, safePercentage, safeNumberFormat, safeMetricDisplay
      - `errorHandling.ts`: Logger, RetryHandler, CircuitBreaker, logger, createApiError
      - `financial-formatting.ts`: CURRENCY, getUserCurrency, formatCurrency, formatCurrencyForTable, formatPercentage
      - `formatCurrency.ts`: FINANCIAL, formatCurrency, formatCompactCurrency, formatCurrencyForTable, formatCurrencyForMetrics
      - `performance-monitoring.ts`: PerformanceMonitor, MonitoredComponent, performanceMonitor, withPerformanceMonitoring, usePerformanceMonitoring
      - `performance.ts`: PerformanceMonitor, performanceMonitor
      - `safeDisplay.ts`: safePercentage, safeNumber, safeDivide, safePercentageCalc
      - `spacing.ts`: spacing, spacingClasses, getSpacing, getSpacingStyle, layoutSpacing
      - `timeFormatting.ts`: formatProcessingTime, formatRelativeTime, formatDuration, getEstimatedCompletion
      - `utils.ts`: cn, formatDateTime, formatDate, formatNumber, formatPercentage
  - `styles/`
  - `test/`
    - `authMocks.ts`: mockTokenStorage, mockRefreshToken, setupAuthenticatedTokens, clearAuthenticatedTokens, simulateTokenExpiration
    - `mockDataBuilders.ts`: buildUser, buildTransaction, buildCategory, buildFileData, buildDashboardMetrics
    - `serviceMocks.ts`: authServiceMock, dashboardServiceMock, transactionServiceMock, categoryServiceMock, fileServiceMock
    - `setup.ts`
    - `utils.tsx`: MockWebSocketService, AllTheProviders, mockAuthStore, authStoreMock, mockApiClient
    - `visual/`
      - `setup-visual-testing.ts`: SCREENSHOTS, BASELINE, CURRENT, DIFF, DEFAULT
- `tailwind.config.ts`
- `tests/e2e/`
- `tsconfig.json`
- `vite.config.ts`

## Scripts
- `archive/`
  - `add-missing-standard-columns.py`: add_missing_standard_columns
  - `add-string-id-column.py`: add_string_id_column
  - `add-vendor-name-column.py`: add_vendor_name_column
  - `analyze_any_excel.py`: UniversalExcelAnalyzer, analyze_file, main
  - `analyze_nuvie_excel.py`
  - `check-all-production-tables.py`: check_all_tables
  - `check-production-schema.py`: check_production_schema
  - `check-users-table.py`: check_users_table
  - `data-creation/`
    - `create-test-transactions-tenant3.py`: create_database_connection, get_categories_for_tenant, create_sample_transactions, update_dashboard_metrics, main
    - `create-test-transactions.py`: create_test_transactions
    - `create-test-users-production.py`: create_test_users
    - `setup-production-test-data.py`: setup_production_test_data
    - `setup-test-users.py`: setup_test_users
  - `debug-transactions-structure.py`: debug_transactions_structure
  - `debug_test_categories.py`: debug_comprehensive_test_cases
  - `demos/`
    - `demo-vendor-lookup.py`: demo_vendor_lookup
  - `fix-categories-path-column.py`: fix_categories_path_column
  - `fix-categories-table.py`: fix_categories_table
  - `fix-tenant-id-type.py`: check_id_types
  - `fix-transactions-table.py`: fix_transactions_table
  - `fix-uploads-table.py`: fix_uploads_table
  - `migrate-production-database.py`: migrate_production_database
  - `one-time-fixes/`
    - `credential_cleanup.py`: CredentialCleanup, run_cleanup, main
    - `fix-production-endpoints.py`: test_and_fix_endpoints
    - `fix-reports-tests-async.py`: fix_async_mocks
    - `fix-reports-tests.py`: fix_reports_tests
    - `get-transaction-ids.py`: get_transaction_ids
    - `migrate-card-components.js`: MIGRATION, SEARCH, IGNORE
  - `production-setup/`
    - `setup-secure-environment.py`: generate_secure_secret, generate_secure_admin_key, create_env_file, main
  - `safe-production-migration.py`: safe_production_migration
  - `security_performance_assessment.py`: SecurityAssessment, run_full_assessment, generate_report, main
- `auth-helper.py`: AuthHelper, get_cached_token, cache_token, login, verify_token
- `build-app-bg.sh`
- `create_code_index.py`: should_include_file, get_file_type, get_summary, get_key_definitions, create_code_index
- `create_realistic_test_data.py`: generate_realistic_narration, generate_synthetic_data
- `design-system-compliance-check.js`: CONFIG, DesignSystemChecker
- `generate_categorization_test_data.py`: create_categorization_test_data
- `generate_mis_test_data.py`: MISTestDataGenerator, generate_quick_setup_data, generate_historical_enhancement_data, generate_schema_enhancement_data, generate_vendor_mapping_data
- `generate_synthetic_test_data.py`: SyntheticDataGenerator, load_analysis_results, generate_reference_number, generate_date_sequence, generate_amount_by_category
- `lint-app-bg.sh`
- `run_categorization_tests.py`: run_comprehensive_categorization_tests
- `serve-app.sh`

## Docs
- `api/`
- `architecture/`
  - `COMPREHENSIVE-IMPLEMENTATION-PLAN.md`
- `archive/`
  - `user-vendor-mapping-guide.md`
- `audits/`
  - `implementation-todo-framework.md`
- `design-system/`
  - `mockups/components/`
    - `agent/`
  - `specifications/`
    - `mockup-improvements.md`
- `specifications/`
  - `claude-code-index.md`

## Infrastructure

