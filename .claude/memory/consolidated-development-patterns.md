# Consolidated Development Patterns - giki.ai MIS Platform

## ESSENTIAL DOCUMENTATION IMPORTS
docs/02-SYSTEM-ARCHITECTURE.md
docs/05-DESIGN-SYSTEM.md
docs/06-PAGE-SPECIFICATIONS.md
docs/08-TESTING-STRATEGY.md

## WORKSPACE ROOT RULE - ABSOLUTE

**NO CODE, TESTS, SCRIPTS, OR DOCS IN WORKSPACE ROOT**
- Only allowed files in root: CLAUDE.md, README.md, configuration files
- All code → apps/
- All tests → apps/*/tests/
- All scripts → scripts/ or apps/*/scripts/
- All docs → docs/
- **Zero tolerance policy - no exceptions**

## TODO PERSISTENCE SYSTEM
**File**: `docs/todos-persistent.yaml`
**Purpose**: Prevent todo loss when Claude Code sessions get corrupted
**Usage**: Check/update this file at session start and after major todo changes

## PLATFORM STRATEGY REFERENCE

**See CLAUDE.md for platform detection commands and three-branch strategy (master/poppy/windy)**

## MIS-FIRST TESTING PHILOSOPHY (2025-07-05)

**UNIFIED PRODUCT**: Every customer gets a complete MIS with progressive enhancement
**PRINCIPLE**: MIS completeness and accuracy metrics drive all testing
**COMMANDS**: `/project:uat` and `/project:test` validate MIS setup and enhancements
**TEST DATA**: Centralized in `libs/test-data/` with MIS-focused organization
**CRITICAL RULE**: NEVER ignore tests - always update/remove when outdated or incorrect

**See docs/02-SYSTEM-ARCHITECTURE.md for the complete MIS-first architecture and MISCategorizationService**
**See docs/08-TESTING-STRATEGY.md for MIS-focused testing philosophy, centralized test data management, and accuracy validation**
**See docs/05-DESIGN-SYSTEM.md for professional brand compliance, three-panel layout system, and component specifications**
**See docs/06-PAGE-SPECIFICATIONS.md for complete page states, API integrations, user flows, and functional requirements**

## CENTRALIZED TEST DATA SYSTEM (2025-07-09)

### **SINGLE SOURCE OF TRUTH: `libs/test-data/`**

**ABSOLUTE RULE**: ALL test data must be in `libs/test-data/` - NO exceptions, NO scattered files

#### **Test Data Organization**
- **Synthetic**: Generated financial data (12 files, Indian/US banks, credit cards)
- **MIS Testing**: Quick setup, historical/schema enhancement, vendor mapping
- **Usage**: Unit tests (quick-setup), Integration (synthetic banks), E2E (mixed-formats)

**MANDATORY CLEANUP RULE**: Remove ALL test data from workspace root and random directories

## PRIMARY INTERFACE: PNPM SCRIPTS (NX REMOVED)

### CRITICAL: Command Execution Context
```bash
# ALWAYS execute from workspace root - verify before EVERY command sequence:
pwd  # Must show: /Users/<USER>/giki-ai-workspace/

# For ALL commands, use direct pnpm scripts from workspace root:
pnpm lint:api         # Runs ruff in api directory, returns to root
pnpm serve:api        # Starts uvicorn server, returns to root
pnpm build:app        # Runs vite build, returns to root
```

### PNPM Scripts Quick Reference (NX REMOVED)
```bash
# DEVELOPMENT - Direct execution (terminal output)
pnpm serve                 # Both API:8000 + frontend:4200 with concurrently
pnpm serve:api             # uvicorn FastAPI server
pnpm serve:app             # Vite development server

# QUALITY - Background execution (prevents timeouts)
pnpm lint:api:bg           # Python ruff linting in background
pnpm lint:app:bg           # Frontend ESLint linting in background

# TESTING - Background execution (prevents timeouts, auto-starts services)
pnpm test:api:bg [unit|integration|slow|all]  # API pytest in background
pnpm test:app:bg [unit|coverage|visual|integration|e2e]  # Frontend testing in background
pnpm test:visual                             # Visual regression tests (auto-starts servers)
pnpm test:e2e                               # E2E tests (auto-starts servers)
pnpm test:visual:ui                         # Visual tests with UI (manual)
pnpm test:e2e:ui                           # E2E tests with UI (manual)

# BUILD - Background execution (prevents timeouts)
pnpm build:api             # FastAPI ready message (no build needed)
pnpm build:app:bg          # Vite production build in background

# LOG VIEWING - Real-time monitoring
pnpm logs                  # Status overview
pnpm logs:lint             # Follow linting logs
pnpm logs:test             # Follow testing logs
pnpm logs:build            # Follow build logs
pnpm logs:serve            # Follow server logs

# UTILITIES
pnpm db                    # Test PostgreSQL connection (giki_ai_dev database)
```

### CRITICAL RULES
1. **ALWAYS use pnpm scripts** - Never bypass with direct commands
2. **BACKGROUND EXECUTION** - Use background scripts (lint:api:bg, test:app:bg, etc.) to avoid timeout issues
3. **LOG OVERWRITE** - All background scripts overwrite logs (>) not append (>>) to prevent massive files
4. **AUTO-START SERVICES** - Scripts automatically start PostgreSQL, Redis, API/Frontend servers when needed
5. **NEVER ignore errors** - Fix immediately, create todos for issues
6. **Workspace root execution** - All scripts run from workspace root
7. **IMPROVE pnpm commands** - When scripts fail, fix the scripts rather than bypassing them
8. **SUBSHELL PATTERN** - All pnpm scripts use `(cd dir && command)` to maintain workspace root

## ANTI-FRAGMENTATION REFERENCE

**See CLAUDE.md for core anti-fragmentation principle and universal rules**

## CRITICAL: NX HAS BEEN REMOVED
**Due to NX corruption issues, all NX functionality has been completely removed from this workspace. Use direct pnpm scripts only.**

## PARALLEL EXECUTION & TASK TOOL GUIDELINES

### Core Principles
- **Parallel Execution**: Run independent tools simultaneously (40-60% time savings)
- **Task Tool**: Use for complex searches; avoid for known paths or simple operations
- **Thinking Depth**: ULTRATHINK (architecture), think harder (analysis), think (simple checks)

### MIS-First UAT Commands Integration (Updated 2025-07-05)
```typescript
interface MISFirstTestingIntegration {
  // MIS-Centric UAT Command
  uatCommand: {
    purpose: "Test complete MIS setup and progressive enhancement scenarios",
    scenarios: ["MIS Quick Setup (5-min)", "MIS Historical Enhancement (+15-20%)", "MIS Schema Enhancement (+20%)", "Combined enhancements"],
    testData: "libs/test-data/ centralized organization",
    thinkingDepth: "ULTRATHINK for comprehensive MIS workflow analysis",
    executionPattern: "Local development → Production validation"
  };
}
```

## SCREENSHOT MCP SERVER USAGE PATTERNS (2025-07-11)

### **Unified Analysis Tool: `mcp__screenshot__analyze_screenshots`**

**Single Tool Architecture**: Replaced dual tools with unified `analyze_screenshots` tool for all visual analysis needs.

#### **Tool Parameters**
```typescript
interface AnalyzeScreenshotsArgs {
  image_paths: string[];           // Array of screenshot file paths (required)
  markdown_paths?: string[];       // Optional documentation context files  
  query: string;                   // Specific analysis question (required)
}
```

#### **Problem-Discovery Focus**
**Critical Principle**: Tool is configured to FIND PROBLEMS, not validate success.

```typescript
// Example Usage Patterns
const SCREENSHOT_ANALYSIS_PATTERNS = {
  // Mockup vs Implementation Comparison
  designConsistency: {
    image_paths: [
      "/path/to/screenshots/development/page-current.png",
      "/path/to/screenshots/mockups/page-mockup.png"
    ],
    markdown_paths: [
      "/path/to/docs/05-DESIGN-SYSTEM.md",
      "/path/to/docs/06-PAGE-SPECIFICATIONS.md"
    ],
    query: "Compare implementation with mockup. Find problems, inconsistencies, and violations. Be ruthlessly critical about deviations, quality issues, or missing elements."
  },
  
  // Brand Compliance Validation  
  brandCompliance: {
    image_paths: ["/path/to/screenshots/development/page-current.png"],
    markdown_paths: ["/path/to/docs/05-DESIGN-SYSTEM.md"],
    query: "Audit for brand compliance violations: check for prohibited emoji icons (🎯🚀💡📊✨🔄⚡✓), non-#295343 colors, spacing violations, typography inconsistencies."
  },
  
  // Cross-Screen Consistency
  internalConsistency: {
    image_paths: [
      "/path/to/screenshots/development/page1.png",
      "/path/to/screenshots/development/page2.png", 
      "/path/to/screenshots/development/page3.png"
    ],
    markdown_paths: ["/path/to/docs/05-DESIGN-SYSTEM.md"],
    query: "Check internal consistency across screens: design patterns, spacing, typography, component usage. Identify any inconsistencies between different pages."
  }
};
```

#### **Integration with Custom Slash Commands**
```markdown
# Visual Consistency Integration Pattern
- /develop command: Use for implementation vs mockup validation
- /test command: Use for visual regression testing  
- /uat command: Use for customer journey visual validation
- /visual-consistency: DEPRECATED - functionality merged into other commands

# Screenshot Workflow Protocol (MANDATORY)
1. ALWAYS capture screenshots for visual analysis
2. Navigate to mockup and capture: mcp__playwright__browser_take_screenshot
3. Navigate to development version and capture: mcp__playwright__browser_take_screenshot  
4. READ both image files to actually see them: Read tool
5. ONLY THEN use mcp__screenshot__analyze_screenshots with appropriate documentation context
```

#### **Critical Analysis Focus Areas**
The tool is configured to prioritize:
1. **Design System Violations**: Color deviations, icon violations, typography inconsistencies
2. **Implementation vs Mockup Gaps**: Missing/added elements, layout differences, quality degradation
3. **Usability & Accessibility Problems**: Poor contrast, touch targets, navigation issues
4. **Professional Appearance Failures**: Unprofessional elements, inconsistent quality

#### **Documentation Integration Strategy**
**Always include relevant documentation context**:
- `docs/05-DESIGN-SYSTEM.md`: For brand compliance and component standards
- `docs/06-PAGE-SPECIFICATIONS.md`: For functional requirements and page states
- `docs/04-CUSTOMER-JOURNEYS.md`: For user experience context
- `docs/02-SYSTEM-ARCHITECTURE.md`: For technical implementation context

## OPTIMIZED CLAUDE CODE WORKFLOW

### SMART BACKGROUND EXECUTION
**ALL SLOW OPERATIONS RUN IN BACKGROUND WITH LOG OVERWRITE**

```typescript
interface ClaudeCodeOptimizedWorkflow {
  // Background scripts prevent timeouts
  backgroundCommands: {
    "pnpm lint:app:bg": "Background frontend linting (never direct)",
    "pnpm test:api:bg unit": "Background API unit tests",
    "pnpm test:app:bg unit": "Background frontend unit tests",
    "pnpm test:visual": "Auto-starts all services, background execution",
    "pnpm test:e2e": "Auto-starts all services, background execution"
  };
  
  // Fast commands work directly
  fastCommands: {
    "pnpm lint:api": "Python linting is fast enough",
    "pnpm db": "Database test after auto-start",
    "pnpm logs": "Status monitoring"
  };
  
  // Auto-service startup
  smartServices: {
    "PostgreSQL": "Auto-started by serve:api and test scripts",
    "Redis": "Auto-started with graceful fallback if unavailable",
    "API/Frontend": "Auto-started by test scripts when needed"
  };
}
```

## ESSENTIAL EXECUTION RULES

### Claude Code Optimized Standards
1. **BACKGROUND-FIRST EXECUTION** - Use :bg scripts for all slow operations to prevent timeouts
2. **AUTO-SERVICE MANAGEMENT** - Let scripts handle PostgreSQL, Redis, API, Frontend startup automatically
3. **LOG OVERWRITE STRATEGY** - Background scripts use (>) not (>>) to keep logs manageable for Claude
4. **NEVER USE TIMEOUT-PRONE COMMANDS** - Never use lint:app, test:api, test:app, build:app directly
5. **SMART MONITORING** - Use pnpm logs commands for real-time monitoring instead of tailing
6. **ZERO MANUAL SETUP** - Scripts detect and start required services intelligently

## CLAUDE CODE EFFICIENCY PATTERNS

### ELIMINATED INEFFICIENT PRACTICES
**These patterns have been removed for Claude Code optimization:**

#### Removed Timeout-Prone Commands
- ❌ `pnpm lint:app` - Direct frontend linting (timeout)
- ❌ `pnpm test:api` - Direct API testing (timeout)  
- ❌ `pnpm test:app` - Direct frontend testing (timeout)
- ❌ `pnpm build:app` - Direct frontend build (timeout)
- ❌ `pnpm test:visual` - Direct visual testing (timeout)
- ❌ `pnpm test:e2e` - Direct E2E testing (timeout)

#### Replaced With Smart Background Execution
- ✅ `pnpm lint:app:bg` - Background frontend linting
- ✅ `pnpm test:api:bg [unit|integration|slow|all]` - Background API testing
- ✅ `pnpm test:app:bg [unit|coverage|visual|integration|e2e]` - Background frontend testing
- ✅ `pnpm test:visual` - Smart visual testing with auto-service startup
- ✅ `pnpm test:e2e` - Smart E2E testing with auto-service startup
- ✅ `pnpm build:app:bg` - Background frontend build

#### Eliminated Manual Service Management
- ❌ Manual PostgreSQL startup/checking
- ❌ Manual Redis startup/checking  
- ❌ Manual API/Frontend server startup
- ❌ Log rotation (replaced with overwrite)
- ❌ Manual process cleanup

---

**UNIFIED PRINCIPLE**: giki.ai is a complete MIS platform where every customer gets hierarchical categorization with progressive enhancement opportunities, focused on business value through accuracy improvements rather than technical complexity.