/**
 * Agent Activation Badge Component
 * 
 * Positioned at the right edge of the screen with the giki logo.
 * Clicking expands the agent panel for AI assistance.
 * Only appears on authenticated pages after first upload completion.
 */
import React from 'react';

interface AgentActivationBadgeProps {
  onClick?: () => void;
  isVisible?: boolean;
  className?: string;
}

export const AgentActivationBadge: React.FC<AgentActivationBadgeProps> = ({
  onClick,
  isVisible = true,
  className = '',
}) => {
  if (!isVisible) return null;

  return (
    <div
      className={`
        fixed top-1/2 right-0 transform -translate-y-1/2
        w-12 h-12 bg-[#295343] hover:bg-[#1D372E]
        rounded-l-xl flex items-center justify-center
        cursor-pointer transition-all duration-300 ease-in-out
        shadow-lg hover:shadow-xl
        hover:translate-x-[-4px]
        z-[100]
        ${className}
      `}
      onClick={onClick}
      title="Open AI Assistant (Cmd+K)"
    >
      {/* Giki Logo */}
      <img 
        src="/images/giki-logo.svg" 
        alt="AI Assistant" 
        className="w-6 h-6 filter brightness-0 invert"
      />
      
      {/* Pulse Animation Dot */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div className="w-2 h-2 bg-white rounded-full animate-pulse-dot" />
      </div>
      
      {/* Custom pulse animation */}
      <style jsx>{`
        @keyframes pulse-dot {
          0%, 100% { 
            opacity: 1; 
            transform: translate(-50%, -50%) scale(1); 
          }
          50% { 
            opacity: 0.5; 
            transform: translate(-50%, -50%) scale(1.2); 
          }
        }
        .animate-pulse-dot {
          animation: pulse-dot 2s infinite;
        }
      `}</style>
    </div>
  );
};

export default AgentActivationBadge;