# Cloud Build configuration for giki.ai API
# Uses Docker layer caching and Artifact Registry for faster builds

steps:
  # Configure Docker to use Artifact Registry
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args: ['auth', 'configure-docker', 'us-central1-docker.pkg.dev', '--quiet']

  # Pull previous image for layer caching (ignore if fails)
  - name: 'gcr.io/cloud-builders/docker'
    entrypoint: 'bash'
    args: 
      - '-c'
      - |
        docker pull us-central1-docker.pkg.dev/$PROJECT_ID/giki-ai/api:latest || echo "No previous image found for caching"

  # Build the Docker image with cache
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/giki-ai/api:latest'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/giki-ai/api:$BUILD_ID'
      - '--cache-from'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/giki-ai/api:latest'
      - './apps/giki-ai-api'

  # Push the image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/giki-ai/api:latest'

  # Push the build-specific tag
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/giki-ai/api:$BUILD_ID'

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/giki-ai/api:latest'
      - '--region'
      - '${_DEPLOY_REGION}'
      - '--service-account'
      - 'dev-giki-ai-service-account@${PROJECT_ID}.iam.gserviceaccount.com'
      - '--add-cloudsql-instances'
      - 'rezolve-poc:us-central1:giki-ai-postgres-prod'
      - '--set-env-vars'
      - 'ENVIRONMENT=production,DEBUG=false,API_HOST=0.0.0.0,SECRET_KEY=prod_secret_key_replace_with_secure_key,AUTH_SECRET_KEY=auth_secret_key_replace_with_secure_key,ALGORITHM=HS256,ACCESS_TOKEN_EXPIRE_MINUTES=30,CORS_ALLOWED_ORIGINS=https://app-giki-ai.web.app;https://giki-ai.web.app,DATABASE_URL=postgresql+asyncpg://giki_ai_user:GikiAI2025SecureProdPwd@************:5432/giki_ai_db?sslmode=require,VERTEX_PROJECT_ID=rezolve-poc,VERTEX_LOCATION=us-central1'
      - '--allow-unauthenticated'
      - '--timeout'
      - '300'
      - '--quiet'

# Images to store in Artifact Registry
images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/giki-ai/api:latest'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/giki-ai/api:$BUILD_ID'

# Build options for performance
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'  # High CPU for faster builds
  diskSizeGb: 100

# Build timeout (default is 10 minutes, we set 20 for safety)
timeout: 1200s

# Substitutions for flexible deployments
substitutions:
  _DEPLOY_REGION: 'us-central1'
  _SERVICE_NAME: 'giki-ai-api'