#!/bin/bash

# Pre-commit hook to enforce real AI tests
# This hook prevents commits that introduce AI mocking or bypass real AI requirements

set -e

echo "🔒 Pre-commit: Real AI Tests Enforcement"
echo "======================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Get list of staged files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM)

# Check if any test files are being committed
TEST_FILES=$(echo "$STAGED_FILES" | grep -E '\.(test|spec)\.(js|ts|jsx|tsx|py)$' || true)

if [ -n "$TEST_FILES" ]; then
    echo "🧪 Checking staged test files for AI mocking..."
    
    # AI mocking patterns to block
    BLOCKED_PATTERNS=(
        "vi\.mock.*ai"
        "jest\.mock.*ai"
        "mockImplementation.*ai"
        "mockResolvedValue.*ai"
        "mock.*categorization"
        "mock.*vertex"
        "mock.*gemini"
        "fake.*ai"
        "stub.*ai"
    )
    
    VIOLATIONS_FOUND=false
    
    for file in $TEST_FILES; do
        if [ -f "$file" ]; then
            echo "  Checking: $file"
            
            for pattern in "${BLOCKED_PATTERNS[@]}"; do
                if grep -q "$pattern" "$file"; then
                    echo -e "${RED}❌ BLOCKED: AI mocking pattern found in $file: $pattern${NC}"
                    VIOLATIONS_FOUND=true
                fi
            done
        fi
    done
    
    if [ "$VIOLATIONS_FOUND" = true ]; then
        echo -e "${RED}"
        echo "🚫 COMMIT BLOCKED: AI mocking detected in test files"
        echo "Real AI tests are mandatory. Please remove AI mocking patterns."
        echo "Use real AI services for testing instead of mocks."
        echo -e "${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ No AI mocking patterns found in test files${NC}"
fi

# Check if package.json is being modified
if echo "$STAGED_FILES" | grep -q "package\.json"; then
    echo "📦 Checking package.json for problematic mock dependencies..."
    
    MOCK_DEPS=(
        "jest-mock"
        "sinon"
        "mock-fs"
        "nock.*mock"
        "msw.*mock"
    )
    
    DEPS_VIOLATIONS=false
    
    for dep in "${MOCK_DEPS[@]}"; do
        if grep -q "$dep" package.json; then
            echo -e "${RED}❌ BLOCKED: Problematic mock dependency found: $dep${NC}"
            DEPS_VIOLATIONS=true
        fi
    done
    
    if [ "$DEPS_VIOLATIONS" = true ]; then
        echo -e "${RED}"
        echo "🚫 COMMIT BLOCKED: Problematic mock dependencies detected"
        echo "These dependencies could be used to bypass real AI tests."
        echo -e "${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ No problematic mock dependencies found${NC}"
fi

# Check AI service files for mock implementations
AI_SERVICE_FILES=$(echo "$STAGED_FILES" | grep -E "(ai_service|categorization_service|vertex)" || true)

if [ -n "$AI_SERVICE_FILES" ]; then
    echo "🧠 Checking AI service files for mock implementations..."
    
    for file in $AI_SERVICE_FILES; do
        if [ -f "$file" ]; then
            echo "  Checking: $file"
            
            if grep -q "mock\|fake\|stub" "$file"; then
                echo -e "${RED}❌ BLOCKED: Mock implementation found in AI service: $file${NC}"
                echo -e "${RED}AI services must use real implementations only${NC}"
                exit 1
            fi
        fi
    done
    
    echo -e "${GREEN}✅ No mock implementations found in AI services${NC}"
fi

# Run quick validation if validation script exists
if [ -f "scripts/validate-real-ai-tests.sh" ]; then
    echo "🔍 Running quick real AI validation..."
    
    # Run a subset of validation checks
    if ! scripts/validate-real-ai-tests.sh --quick 2>/dev/null; then
        echo -e "${YELLOW}⚠️  Quick validation failed, but allowing commit${NC}"
        echo -e "${YELLOW}Full validation will run in CI/CD pipeline${NC}"
    else
        echo -e "${GREEN}✅ Quick validation passed${NC}"
    fi
fi

echo -e "${GREEN}✅ Pre-commit checks passed - Real AI requirements maintained${NC}"
exit 0
