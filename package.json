{"name": "giki-ai-workspace", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "libs/*"], "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "exceljs": "^4.4.0", "form-data": "^4.0.3", "framer-motion": "^12.19.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.510.0", "node-fetch": "^2.7.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "react-router-dom": "^6.28.0", "recharts": "^2.15.3", "serve": "^14.2.4", "tailwind-merge": "^3.3.0", "tslib": "^2.8.1", "uuid": "^11.1.0", "zod": "^3.25.1", "zustand": "^5.0.4"}, "devDependencies": {"@axe-core/cli": "^4.10.1", "@eslint/js": "^9.27.0", "@playwright/test": "^1.53.2", "@tanstack/react-query": "^5.82.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "3.2.4", "axe-core": "^4.10.3", "concurrently": "^9.1.2", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "playwright": "^1.53.2", "prettier": "^3.5.3", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.33.0", "vite": "^5.4.19", "vitest": "^3.2.4"}, "scripts": {"preinstall": "node -e \"if (process.env.npm_execpath && !process.env.npm_execpath.includes('pnpm')) { console.error('\\n\\nERROR: This project requires pnpm. Please install it with: npm install -g pnpm\\n\\n'); process.exit(1); }\"", "lint:api": "(cd apps/giki-ai-api && uv run ruff check src/)", "lint:api:bg": "./scripts/lint-api-bg.sh", "lint:app:bg": "./scripts/lint-app-bg.sh", "design-check": "node scripts/design-system-compliance-check.js", "test:api:bg": "./scripts/test-api-bg.sh", "test:app:bg": "./scripts/test-app-bg.sh", "test:visual": "./scripts/test-visual-bg.sh", "test:visual:ui": "playwright test --config=tests/visual-regression/playwright.config.ts --ui", "test:e2e": "./scripts/test-e2e-bg.sh", "test:e2e:ui": "playwright test --config=apps/giki-ai-app/playwright.config.ts --ui", "build:api": "(cd apps/giki-ai-api && echo 'FastAPI API ready - no build step needed')", "build:app:bg": "./scripts/build-app-bg.sh", "serve:api": "./scripts/serve-api.sh", "serve:app": "./scripts/serve-app.sh", "serve:all": "concurrently \"pnpm serve:api\" \"pnpm serve:app\"", "logs": "./scripts/logs.sh", "logs:serve": "./scripts/logs.sh serve", "logs:lint": "./scripts/logs.sh lint", "logs:test": "./scripts/logs.sh test", "logs:build": "./scripts/logs.sh build", "logs:deploy": "./scripts/logs.sh deploy", "db": "(cd apps/giki-ai-api && uv run python ../../scripts/test-db.py)", "deploy:api": "./scripts/deploy-api.sh", "deploy:api:dry": "./scripts/deploy-api.sh --dry-run", "deploy:app": "./scripts/deploy-app.sh", "deploy:app:dry": "./scripts/deploy-app.sh --dry-run", "deploy:all": "./scripts/deploy.sh all", "deploy:all:dry": "./scripts/deploy.sh all --dry-run"}}