-- Migration: Add schema discovery fields for dynamic categorization
-- Date: 2025-06-16
-- Purpose: Support dynamic schema discovery from customer files per critical user feedback

-- Add schema discovery fields to categories table
ALTER TABLE categories 
ADD COLUMN IF NOT EXISTS original_labels JSON,
ADD COLUMN IF NOT EXISTS is_unified_category BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS unified_category_id INTEGER REFERENCES categories(id),
ADD COLUMN IF NOT EXISTS schema_discovery_session_id VARCHAR(100),
ADD COLUMN IF NOT EXISTS source_files JSON;

-- Add index for unified category lookups
CREATE INDEX IF NOT EXISTS idx_categories_unified_category_id ON categories(unified_category_id);
CREATE INDEX IF NOT EXISTS idx_categories_schema_discovery_session ON categories(schema_discovery_session_id);

-- Add schema discovery fields to rag_corpus table
ALTER TABLE rag_corpus
ADD COLUMN IF NOT EXISTS customer_category_schemas JSON,
ADD COLUMN IF NOT EXISTS unified_category_mapping JSON,
ADD COLUMN IF NOT EXISTS reverse_category_mapping JSON,
ADD COLUMN IF NOT EXISTS source_file_schemas JSON,
ADD COLUMN IF NOT EXISTS schema_discovery_session_id VARCHAR(100);

-- Add index for schema discovery session lookups
CREATE INDEX IF NOT EXISTS idx_rag_corpus_schema_discovery_session ON rag_corpus(schema_discovery_session_id);

-- Add comment explaining the schema discovery architecture
COMMENT ON COLUMN categories.original_labels IS 'Original category labels from customer files: {"file_1": "Payroll", "file_2": "Staff Costs"}';
COMMENT ON COLUMN categories.is_unified_category IS 'True if this is a unified category created by schema discovery, False if original customer category';
COMMENT ON COLUMN categories.unified_category_id IS 'For original categories, points to the unified category it maps to';
COMMENT ON COLUMN categories.schema_discovery_session_id IS 'Links to the schema discovery session that created/updated this category';

COMMENT ON COLUMN rag_corpus.customer_category_schemas IS 'Complete discovered category schemas from all customer files';
COMMENT ON COLUMN rag_corpus.unified_category_mapping IS 'Mapping from original customer categories to unified categories';
COMMENT ON COLUMN rag_corpus.reverse_category_mapping IS 'Mapping from unified categories back to original customer categories';
COMMENT ON COLUMN rag_corpus.source_file_schemas IS 'Per-file schema information including category columns and patterns';

-- Verification query to check migration success
SELECT 
    'categories' as table_name,
    COUNT(*) as new_columns_count
FROM information_schema.columns 
WHERE table_name = 'categories' 
AND column_name IN ('original_labels', 'is_unified_category', 'unified_category_id', 'schema_discovery_session_id', 'source_files')
UNION ALL
SELECT 
    'rag_corpus' as table_name,
    COUNT(*) as new_columns_count
FROM information_schema.columns 
WHERE table_name = 'rag_corpus' 
AND column_name IN ('customer_category_schemas', 'unified_category_mapping', 'reverse_category_mapping', 'source_file_schemas', 'schema_discovery_session_id');