-- Migration: Hybrid Architecture Tables (Fixed)
-- Date: 2025-01-02
-- Purpose: Support runtime service pattern and fast lookups

-- 1. Transaction Categories (if not exists)
CREATE TABLE IF NOT EXISTS transaction_categories (
    id SERIAL PRIMARY KEY,
    transaction_id VARCHAR(36) NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
    category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
    category_name VARCHAR(255),
    confidence FLOAT NOT NULL DEFAULT 0.0,
    is_approved BOOLEAN DEFAULT FALSE,
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(transaction_id)
);

CREATE INDEX IF NOT EXISTS idx_transaction_categories_transaction 
ON transaction_categories(transaction_id);

CREATE INDEX IF NOT EXISTS idx_transaction_categories_approved 
ON transaction_categories(is_approved) WHERE is_approved = TRUE;

-- Apply trigger to transaction_categories
DROP TRIGGER IF EXISTS update_transaction_categories_updated_at ON transaction_categories;
CREATE TRIGGER update_transaction_categories_updated_at 
BEFORE UPDATE ON transaction_categories 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL ON transaction_categories TO giki_ai_user;
GRANT ALL ON transaction_categories_id_seq TO giki_ai_user;