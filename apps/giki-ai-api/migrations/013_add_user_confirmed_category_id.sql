-- Migration: Add user_confirmed_category_id column to transactions table
-- Purpose: Enable tracking of user-confirmed categories for accuracy calculations
-- Date: 2025-07-07

-- Add user_confirmed_category_id column to transactions table
ALTER TABLE transactions
ADD COLUMN IF NOT EXISTS user_confirmed_category_id INTEGER;

-- Add foreign key constraint to categories table
ALTER TABLE transactions
ADD CONSTRAINT fk_transactions_user_confirmed_category
FOREIGN KEY (user_confirmed_category_id) 
REFERENCES categories(id)
ON DELETE SET NULL;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_transactions_user_confirmed_category
ON transactions(user_confirmed_category_id);

-- Add comment for documentation
COMMENT ON COLUMN transactions.user_confirmed_category_id IS 
'User-confirmed category ID for accuracy tracking. NULL means not yet confirmed by user.';

-- Update existing transactions where category_id exists and is_reviewed is true
-- This assumes reviewed transactions have user-confirmed categories
UPDATE transactions 
SET user_confirmed_category_id = category_id 
WHERE is_reviewed = true 
  AND user_confirmed_category_id IS NULL
  AND category_id IS NOT NULL;

-- Create a tracking column for when the user confirmation happened
ALTER TABLE transactions
ADD COLUMN IF NOT EXISTS category_confirmed_at TIMESTAMP WITHOUT TIME ZONE;

-- Update confirmation timestamp for already reviewed transactions
UPDATE transactions
SET category_confirmed_at = reviewed_at
WHERE is_reviewed = true 
  AND user_confirmed_category_id IS NOT NULL
  AND category_confirmed_at IS NULL;

-- Add index for confirmation tracking
CREATE INDEX IF NOT EXISTS idx_transactions_category_confirmed_at
ON transactions(category_confirmed_at);

COMMENT ON COLUMN transactions.category_confirmed_at IS 
'Timestamp when user confirmed the category assignment';