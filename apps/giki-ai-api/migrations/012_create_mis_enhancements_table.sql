-- Migration: Create mis_enhancements table for MIS enhancement tracking
-- Date: 2025-07-07
-- Purpose: Track MIS enhancement applications and accuracy improvements

-- Create mis_enhancements table
CREATE TABLE IF NOT EXISTS mis_enhancements (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    setup_id INTEGER NOT NULL,
    enhancement_type VARCHAR(50) NOT NULL,
    accuracy_gain DECIMAL(5,4) DEFAULT 0.0,
    status VARCHAR(20) DEFAULT 'completed',
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_mis_enhancements_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT chk_mis_enhancements_accuracy_gain CHECK (accuracy_gain >= 0 AND accuracy_gain <= 1),
    CONSTRAINT chk_mis_enhancements_type CHECK (enhancement_type IN ('historical', 'schema', 'vendor')),
    CONSTRAINT chk_mis_enhancements_status CHECK (status IN ('pending', 'in_progress', 'completed', 'failed'))
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_mis_enhancements_tenant ON mis_enhancements(tenant_id);
CREATE INDEX IF NOT EXISTS idx_mis_enhancements_setup ON mis_enhancements(setup_id);
CREATE INDEX IF NOT EXISTS idx_mis_enhancements_type ON mis_enhancements(enhancement_type);
CREATE INDEX IF NOT EXISTS idx_mis_enhancements_applied ON mis_enhancements(applied_at DESC);

-- Add comments for documentation
COMMENT ON TABLE mis_enhancements IS 'Tracks MIS enhancement applications and their accuracy improvements';
COMMENT ON COLUMN mis_enhancements.setup_id IS 'Reference to the MIS setup this enhancement was applied to';
COMMENT ON COLUMN mis_enhancements.enhancement_type IS 'Type of enhancement: historical, schema, or vendor';
COMMENT ON COLUMN mis_enhancements.accuracy_gain IS 'Accuracy improvement gained from this enhancement (0.0 to 1.0)';
COMMENT ON COLUMN mis_enhancements.status IS 'Enhancement application status';