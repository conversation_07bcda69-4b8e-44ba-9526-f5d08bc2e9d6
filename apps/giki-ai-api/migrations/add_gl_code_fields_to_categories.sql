-- Add GL Code mapping fields to categories table for multilevel GL category system
-- This migration supports the fundamental business requirement that categories are 
-- always multilevel and map to General Ledger codes for accounting integration
-- Updated to use standard table names (removed dev_ prefix system)

-- Add GL Code mapping columns
ALTER TABLE category ADD COLUMN IF NOT EXISTS gl_code VARCHAR(50);
ALTER TABLE category ADD COLUMN IF NOT EXISTS gl_account_name VARCHAR(200);
ALTER TABLE category ADD COLUMN IF NOT EXISTS gl_account_type VARCHAR(50);

-- Add learning metadata columns for tenant-specific category inference
ALTER TABLE category ADD COLUMN IF NOT EXISTS level INTEGER DEFAULT 0;
ALTER TABLE category ADD COLUMN IF NOT EXISTS learned_from_onboarding BOOLEAN DEFAULT TRUE;
ALTER TABLE category ADD COLUMN IF NOT EXISTS frequency_score DECIMAL(5,2);
ALTER TABLE category ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(5,2);

-- Add path column if it doesn't exist, or extend it if it does
ALTER TABLE category ADD COLUMN IF NOT EXISTS path VARCHAR(500);

-- Add comments for documentation
COMMENT ON COLUMN category.gl_code IS 'General Ledger code for accounting integration (e.g., "4001", "1200.1")';
COMMENT ON COLUMN category.gl_account_name IS 'Human-readable GL account name (e.g., "Office Supplies Expense")';
COMMENT ON COLUMN category.gl_account_type IS 'Account classification: Asset, Liability, Revenue, Expense, Equity';
COMMENT ON COLUMN category.level IS 'Depth in category hierarchy (0=root, 1=child, etc.)';
COMMENT ON COLUMN category.learned_from_onboarding IS 'Whether this category was learned from tenant onboarding data';
COMMENT ON COLUMN category.frequency_score IS 'How often this category appears in transactions (0.00-99.99)';
COMMENT ON COLUMN category.confidence_score IS 'AI confidence in this categorization (0.00-1.00)';
COMMENT ON COLUMN category.path IS 'Full hierarchical path for efficient queries (e.g., "Business Expenses > Office > Software")';

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_category_gl_code ON category(gl_code) WHERE gl_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_category_gl_account_type ON category(gl_account_type) WHERE gl_account_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_category_level ON category(level);
CREATE INDEX IF NOT EXISTS idx_category_tenant_level ON category(tenant_id, level);
CREATE INDEX IF NOT EXISTS idx_category_path_gin ON category USING gin(to_tsvector('english', path)) WHERE path IS NOT NULL;

-- Update existing categories to have proper hierarchy levels (if any exist)
-- This handles migration of existing flat categories to the multilevel system
UPDATE category 
SET level = CASE 
    WHEN parent_id IS NULL THEN 0
    ELSE (
        WITH RECURSIVE category_hierarchy AS (
            SELECT id, parent_id, 0 as depth
            FROM category 
            WHERE parent_id IS NULL
            
            UNION ALL
            
            SELECT c.id, c.parent_id, ch.depth + 1
            FROM category c
            JOIN category_hierarchy ch ON c.parent_id = ch.id
        )
        SELECT depth FROM category_hierarchy WHERE id = category.id
    )
END
WHERE level = 0 AND id IN (SELECT id FROM category);

-- Ensure path is properly set for all categories
UPDATE category 
SET path = CASE 
    WHEN parent_id IS NULL THEN name
    ELSE (
        WITH RECURSIVE category_path AS (
            SELECT id, name, parent_id, name::VARCHAR(500) as full_path
            FROM category 
            WHERE parent_id IS NULL
            
            UNION ALL
            
            SELECT c.id, c.name, c.parent_id, (cp.full_path || ' > ' || c.name)::VARCHAR(500)
            FROM category c
            JOIN category_path cp ON c.parent_id = cp.id
        )
        SELECT full_path FROM category_path WHERE id = category.id
    )
END
WHERE path IS NULL OR path = '';