-- Migration: Add file processing report tables
-- Description: Add tables for detailed file processing reports, row-by-row tracking, and column statistics
-- Date: 2025-06-16

-- Create file_processing_reports table
CREATE TABLE IF NOT EXISTS file_processing_reports (
    id UUID PRIMARY KEY,
    upload_id VARCHAR(36) NOT NULL REFERENCES uploads(id) ON DELETE CASCADE,
    tenant_id INTEGER NOT NULL REFERENCES tenant(id) ON DELETE CASCADE,
    
    -- File metadata
    file_name VARCHAR(255) NOT NULL,
    file_size_bytes INTEGER,
    total_rows INTEGER NOT NULL,
    total_columns INTEGER NOT NULL,
    
    -- Processing results
    successful_rows INTEGER NOT NULL DEFAULT 0,
    failed_rows INTEGER NOT NULL DEFAULT 0,
    skipped_rows INTEGER NOT NULL DEFAULT 0,
    
    -- Processing metadata
    processing_started_at TIMESTAMP NOT NULL,
    processing_completed_at TIMESTAMP,
    processing_duration_seconds FLOAT,
    date_format_detected VARCHAR(50),
    
    -- Column mapping results (JSON)
    column_mapping_confidence JSONB,
    mapped_columns JSONB,
    unmapped_columns JSONB,
    
    -- Data quality metrics
    data_quality_score FLOAT,
    validation_errors JSONB,
    warnings JSONB,
    
    -- Schema discovery results
    categories_discovered JSONB,
    category_count INTEGER DEFAULT 0,
    schema_confidence FLOAT,
    
    -- Status
    status VARCHAR(50) NOT NULL CHECK (status IN ('processing', 'completed', 'failed')),
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for file_processing_reports
CREATE INDEX idx_file_processing_reports_upload_id ON file_processing_reports(upload_id);
CREATE INDEX idx_file_processing_reports_tenant_id ON file_processing_reports(tenant_id);
CREATE INDEX idx_file_processing_reports_status ON file_processing_reports(status);
CREATE INDEX idx_file_processing_reports_created_at ON file_processing_reports(created_at DESC);

-- Create row_processing_details table
CREATE TABLE IF NOT EXISTS row_processing_details (
    id UUID PRIMARY KEY,
    report_id UUID NOT NULL REFERENCES file_processing_reports(id) ON DELETE CASCADE,
    
    -- Row identification
    row_number INTEGER NOT NULL,
    raw_data JSONB,
    
    -- Processing status
    status VARCHAR(50) NOT NULL CHECK (status IN ('success', 'failed', 'skipped')),
    
    -- Parsed data
    parsed_date DATE,
    parsed_amount NUMERIC(15, 2),
    parsed_description TEXT,
    parsed_category VARCHAR(255),
    parsed_vendor VARCHAR(255),
    
    -- Validation results
    validation_errors JSONB,
    data_quality_issues JSONB,
    
    -- Processing metadata
    date_parsing_attempted BOOLEAN DEFAULT FALSE,
    date_parsing_format VARCHAR(50),
    amount_parsing_attempted BOOLEAN DEFAULT FALSE,
    category_mapping_confidence FLOAT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for row_processing_details
CREATE INDEX idx_row_processing_details_report_id ON row_processing_details(report_id);
CREATE INDEX idx_row_processing_details_row_number ON row_processing_details(row_number);
CREATE INDEX idx_row_processing_details_status ON row_processing_details(status);

-- Create column_statistics table
CREATE TABLE IF NOT EXISTS column_statistics (
    id UUID PRIMARY KEY,
    report_id UUID NOT NULL REFERENCES file_processing_reports(id) ON DELETE CASCADE,
    
    -- Column identification
    column_name VARCHAR(255) NOT NULL,
    column_index INTEGER NOT NULL,
    mapped_field VARCHAR(100),
    
    -- Data statistics
    total_values INTEGER NOT NULL,
    non_null_values INTEGER NOT NULL,
    null_values INTEGER NOT NULL,
    unique_values INTEGER NOT NULL,
    
    -- Data type analysis
    detected_type VARCHAR(50),
    type_consistency FLOAT,
    
    -- Value distribution
    min_value VARCHAR(255),
    max_value VARCHAR(255),
    average_value FLOAT,
    most_common_values JSONB,
    
    -- Data quality
    empty_string_count INTEGER DEFAULT 0,
    invalid_format_count INTEGER DEFAULT 0,
    outlier_count INTEGER DEFAULT 0,
    
    -- Mapping quality
    mapping_confidence FLOAT,
    mapping_method VARCHAR(50),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for column_statistics
CREATE INDEX idx_column_statistics_report_id ON column_statistics(report_id);
CREATE INDEX idx_column_statistics_column_index ON column_statistics(column_index);

-- Add relationship from uploads table to processing reports (already handled by foreign key)
-- No need to modify uploads table as the relationship is one-to-one via upload_id

-- Add comment to tables
COMMENT ON TABLE file_processing_reports IS 'Detailed reports for file upload processing including success rates and data quality metrics';
COMMENT ON TABLE row_processing_details IS 'Row-by-row processing details for debugging and quality analysis';
COMMENT ON TABLE column_statistics IS 'Statistical analysis of columns in uploaded files';

-- Grant permissions (adjust based on your user/role setup)
-- GRANT ALL ON file_processing_reports TO your_app_user;
-- GRANT ALL ON row_processing_details TO your_app_user;
-- GRANT ALL ON column_statistics TO your_app_user;