-- Migration: Hybrid Architecture Tables
-- Date: 2025-01-02
-- Purpose: Support runtime service pattern and fast lookups

-- 1. Transaction Categories (if not exists)
CREATE TABLE IF NOT EXISTS transaction_categories (
    id SERIAL PRIMARY KEY,
    transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
    category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
    category_name VARCHAR(255),
    confidence FLOAT NOT NULL DEFAULT 0.0,
    is_approved BOOLEAN DEFAULT FALSE,
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(transaction_id)
);

CREATE INDEX IF NOT EXISTS idx_transaction_categories_transaction 
ON transaction_categories(transaction_id);

CREATE INDEX IF NOT EXISTS idx_transaction_categories_approved 
ON transaction_categories(is_approved) WHERE is_approved = TRUE;

-- 2. Categorization Patterns for fast lookup
CREATE TABLE IF NOT EXISTS categorization_patterns (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    pattern_text VARCHAR(100) NOT NULL,
    vendor_pattern VARCHAR(100),
    category_name VARCHAR(255) NOT NULL,
    confidence FLOAT NOT NULL DEFAULT 0.8,
    usage_count INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, pattern_text, vendor_pattern)
);

CREATE INDEX IF NOT EXISTS idx_categorization_patterns_tenant_pattern 
ON categorization_patterns(tenant_id, pattern_text);

CREATE INDEX IF NOT EXISTS idx_categorization_patterns_usage 
ON categorization_patterns(usage_count DESC);

-- 3. Agent Session Memory
CREATE TABLE IF NOT EXISTS agent_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) NOT NULL UNIQUE,
    user_id INTEGER NOT NULL REFERENCES users(id),
    tenant_id INTEGER NOT NULL,
    agent_type VARCHAR(50) NOT NULL DEFAULT 'conversational',
    session_data JSONB NOT NULL DEFAULT '{}',
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours')
);

CREATE INDEX IF NOT EXISTS idx_agent_sessions_user 
ON agent_sessions(user_id);

CREATE INDEX IF NOT EXISTS idx_agent_sessions_activity 
ON agent_sessions(last_activity DESC);

-- 4. Agent Command History
CREATE TABLE IF NOT EXISTS agent_commands (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id INTEGER NOT NULL REFERENCES users(id),
    tenant_id INTEGER NOT NULL,
    command VARCHAR(1000) NOT NULL,
    command_type VARCHAR(50),
    parameters JSONB,
    response JSONB,
    execution_time_ms INTEGER,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_agent_commands_session 
ON agent_commands(session_id);

CREATE INDEX IF NOT EXISTS idx_agent_commands_user 
ON agent_commands(user_id, created_at DESC);

-- 5. Add vendor column to transactions if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'transactions' 
                   AND column_name = 'vendor') THEN
        ALTER TABLE transactions ADD COLUMN vendor VARCHAR(255);
    END IF;
END $$;

-- 6. Update trigger for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to new tables
DROP TRIGGER IF EXISTS update_transaction_categories_updated_at ON transaction_categories;
CREATE TRIGGER update_transaction_categories_updated_at 
BEFORE UPDATE ON transaction_categories 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_categorization_patterns_updated_at ON categorization_patterns;
CREATE TRIGGER update_categorization_patterns_updated_at 
BEFORE UPDATE ON categorization_patterns 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON transaction_categories TO giki_ai_user;
GRANT ALL ON categorization_patterns TO giki_ai_user;
GRANT ALL ON agent_sessions TO giki_ai_user;
GRANT ALL ON agent_commands TO giki_ai_user;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO giki_ai_user;