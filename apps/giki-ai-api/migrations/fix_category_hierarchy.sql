-- Migration: Fix Category Hierarchy Levels and Paths
-- Purpose: Correct the category hierarchy data integrity issues
-- Date: 2025-01-08

-- Step 1: Fix level values by calculating actual depth in hierarchy
WITH RECURSIVE category_tree AS (
  -- Base case: root categories (Income and Expenses)
  SELECT id, parent_id, name, 0 as calculated_level
  FROM categories
  WHERE parent_id IS NULL
  
  UNION ALL
  
  -- Recursive case: all child categories
  SELECT c.id, c.parent_id, c.name, ct.calculated_level + 1
  FROM categories c
  INNER JOIN category_tree ct ON c.parent_id = ct.id
)
UPDATE categories c
SET level = ct.calculated_level,
    updated_at = NOW()
FROM category_tree ct
WHERE c.id = ct.id
  AND c.level != ct.calculated_level;  -- Only update if different

-- Step 2: Rebuild paths to ensure they reflect actual hierarchy
WITH RECURSIVE category_paths AS (
  -- Base case: root categories
  SELECT id, name, parent_id, name::varchar as full_path
  FROM categories
  WHERE parent_id IS NULL
  
  UNION ALL
  
  -- Recursive case: build full path
  SELECT c.id, c.name, c.parent_id, 
         (cp.full_path || ' > ' || c.name)::varchar as full_path
  FROM categories c
  INNER JOIN category_paths cp ON c.parent_id = cp.id
)
UPDATE categories c
SET path = cp.full_path,
    updated_at = NOW()
FROM category_paths cp
WHERE c.id = cp.id
  AND (c.path IS NULL OR c.path != cp.full_path);  -- Only update if needed

-- Step 3: Add function to maintain hierarchy on insert/update
CREATE OR REPLACE FUNCTION maintain_category_hierarchy()
RETURNS TRIGGER AS $$
DECLARE
  parent_level INTEGER;
  parent_path TEXT;
BEGIN
  -- If updating parent_id or inserting new category
  IF NEW.parent_id IS NOT NULL THEN
    -- Get parent's level and path
    SELECT level, path INTO parent_level, parent_path
    FROM categories WHERE id = NEW.parent_id;
    
    -- Set level as parent's level + 1
    NEW.level := COALESCE(parent_level, 0) + 1;
    
    -- Build path
    IF parent_path IS NOT NULL THEN
      NEW.path := parent_path || ' > ' || NEW.name;
    ELSE
      NEW.path := NEW.name;
    END IF;
  ELSE
    -- Root category
    NEW.level := 0;
    NEW.path := NEW.name;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create trigger for automatic hierarchy maintenance
DROP TRIGGER IF EXISTS maintain_category_hierarchy_trigger ON categories;
CREATE TRIGGER maintain_category_hierarchy_trigger
BEFORE INSERT OR UPDATE OF parent_id, name ON categories
FOR EACH ROW
EXECUTE FUNCTION maintain_category_hierarchy();

-- Step 5: Add function to update child paths when parent changes
CREATE OR REPLACE FUNCTION cascade_category_path_updates()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if path actually changed
  IF OLD.path IS DISTINCT FROM NEW.path THEN
    -- Update all children's paths
    WITH RECURSIVE child_categories AS (
      -- Direct children
      SELECT id, parent_id, name
      FROM categories
      WHERE parent_id = NEW.id
      
      UNION ALL
      
      -- Recursive children
      SELECT c.id, c.parent_id, c.name
      FROM categories c
      INNER JOIN child_categories cc ON c.parent_id = cc.id
    )
    UPDATE categories c
    SET path = NEW.path || ' > ' || 
               substring(c.path from length(OLD.path) + 4),  -- +4 for ' > '
        updated_at = NOW()
    FROM child_categories cc
    WHERE c.id = cc.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create trigger for cascading path updates
DROP TRIGGER IF EXISTS cascade_category_path_trigger ON categories;
CREATE TRIGGER cascade_category_path_trigger
AFTER UPDATE OF path ON categories
FOR EACH ROW
EXECUTE FUNCTION cascade_category_path_updates();

-- Step 7: Add check constraint for level consistency
ALTER TABLE categories DROP CONSTRAINT IF EXISTS check_level_consistency;
ALTER TABLE categories ADD CONSTRAINT check_level_consistency
CHECK (
  (parent_id IS NULL AND level = 0) OR
  (parent_id IS NOT NULL AND level > 0)
);

-- Step 8: Create index for efficient hierarchy queries if not exists
CREATE INDEX IF NOT EXISTS idx_categories_path ON categories(path);
CREATE INDEX IF NOT EXISTS idx_categories_parent_level ON categories(parent_id, level);

-- Step 9: Log migration results
DO $$
DECLARE
  categories_fixed INTEGER;
  paths_updated INTEGER;
BEGIN
  SELECT COUNT(*) INTO categories_fixed
  FROM categories
  WHERE updated_at >= NOW() - INTERVAL '1 minute';
  
  SELECT COUNT(*) INTO paths_updated
  FROM categories
  WHERE path IS NOT NULL;
  
  RAISE NOTICE 'Category hierarchy migration completed:';
  RAISE NOTICE '- Categories with corrected levels: %', categories_fixed;
  RAISE NOTICE '- Categories with paths: %', paths_updated;
  RAISE NOTICE '- Triggers installed for automatic maintenance';
END $$;

-- Step 10: Verify hierarchy integrity
WITH hierarchy_check AS (
  SELECT 
    c.id,
    c.name,
    c.level,
    c.parent_id,
    p.level as parent_level,
    CASE 
      WHEN c.parent_id IS NULL AND c.level != 0 THEN 'Root with wrong level'
      WHEN c.parent_id IS NOT NULL AND p.level IS NULL THEN 'Orphaned category'
      WHEN c.parent_id IS NOT NULL AND c.level != p.level + 1 THEN 'Incorrect level'
      ELSE 'OK'
    END as status
  FROM categories c
  LEFT JOIN categories p ON c.parent_id = p.id
)
SELECT status, COUNT(*) as count
FROM hierarchy_check
GROUP BY status;