-- Add credit/debit awareness and user control to vendor mappings
-- Version 2: Works with existing table structure that uses category_name instead of category_id

BEGIN;

-- First, add category_id column to link to categories table
ALTER TABLE vendor_category_mappings
ADD COLUMN IF NOT EXISTS category_id INTEGER REFERENCES categories(id);

-- Add normalized_name for better matching
ALTER TABLE vendor_category_mappings
ADD COLUMN IF NOT EXISTS normalized_name VARCHAR(255);

-- Add new columns for enhanced functionality
ALTER TABLE vendor_category_mappings
ADD COLUMN IF NOT EXISTS applies_to_debits BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS applies_to_credits BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS description_pattern TEXT,
ADD COLUMN IF NOT EXISTS min_amount DECIMAL(12, 2),
ADD COLUMN IF NOT EXISTS max_amount DECIMAL(12, 2),
ADD COLUMN IF NOT EXISTS business_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS confidence_threshold NUMERIC(5,4) DEFAULT 0.9,
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS times_applied INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_applied TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS created_by INTEGER REFERENCES users(id),
ADD COLUMN IF NOT EXISTS updated_by INTEGER REFERENCES users(id);

-- Update normalized_name for existing records
UPDATE vendor_category_mappings
SET normalized_name = UPPER(REGEXP_REPLACE(vendor_name, '[^A-Za-z0-9\s]', ' ', 'g'))
WHERE normalized_name IS NULL;

-- Try to populate category_id from category_name for existing records
UPDATE vendor_category_mappings vcm
SET category_id = c.id
FROM categories c
WHERE vcm.tenant_id = c.tenant_id
AND (
    LOWER(c.name) = LOWER(vcm.category_name)
    OR c.path LIKE '%' || vcm.category_name
)
AND vcm.category_id IS NULL;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_vendor_mappings_active 
ON vendor_category_mappings(tenant_id, is_active) 
WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_vendor_mappings_direction 
ON vendor_category_mappings(tenant_id, applies_to_debits, applies_to_credits);

CREATE INDEX IF NOT EXISTS idx_vendor_mappings_created_by 
ON vendor_category_mappings(tenant_id, created_by);

CREATE INDEX IF NOT EXISTS idx_vendor_mappings_category_id
ON vendor_category_mappings(tenant_id, category_id);

-- Update existing mappings to have sensible defaults
UPDATE vendor_category_mappings
SET 
    applies_to_debits = true,
    applies_to_credits = false,
    is_active = true,
    confidence_threshold = COALESCE(confidence, 0.9)
WHERE applies_to_debits IS NULL;

-- Create a view for active vendor mappings with category info
CREATE OR REPLACE VIEW active_vendor_mappings AS
SELECT 
    vcm.id,
    vcm.vendor_name,
    vcm.normalized_name,
    vcm.category_id,
    COALESCE(c.path, vcm.category_name) as category_path,
    COALESCE(c.name, vcm.category_name) as category_name,
    vcm.tenant_id,
    vcm.applies_to_debits,
    vcm.applies_to_credits,
    vcm.description_pattern,
    vcm.min_amount,
    vcm.max_amount,
    vcm.confidence_threshold,
    vcm.business_type,
    vcm.notes,
    vcm.times_applied,
    vcm.last_applied,
    vcm.created_at,
    vcm.created_by,
    u.email as created_by_email,
    vcm.updated_at
FROM vendor_category_mappings vcm
LEFT JOIN categories c ON vcm.category_id = c.id
LEFT JOIN users u ON vcm.created_by = u.id
WHERE vcm.is_active = true;

-- Function to apply vendor mappings with direction awareness
CREATE OR REPLACE FUNCTION apply_vendor_mappings_with_direction(
    p_tenant_id INTEGER,
    p_upload_id TEXT DEFAULT NULL
)
RETURNS TABLE (
    transactions_updated INTEGER,
    debits_updated INTEGER,
    credits_updated INTEGER
) AS $$
DECLARE
    v_total INTEGER := 0;
    v_debits INTEGER := 0;
    v_credits INTEGER := 0;
    v_mapping RECORD;
    v_batch_total INTEGER;
    v_batch_debits INTEGER;
    v_batch_credits INTEGER;
BEGIN
    -- Initialize counters
    v_total := 0;
    v_debits := 0;
    v_credits := 0;

    -- Apply each active vendor mapping
    FOR v_mapping IN 
        SELECT * FROM active_vendor_mappings 
        WHERE tenant_id = p_tenant_id
        AND category_id IS NOT NULL  -- Only process mappings with valid category_id
    LOOP
        -- Update transactions based on mapping rules
        WITH updated AS (
            UPDATE transactions t
            SET 
                category_id = v_mapping.category_id,
                ai_confidence_score = v_mapping.confidence_threshold,
                updated_at = CURRENT_TIMESTAMP
            WHERE 
                t.tenant_id = p_tenant_id
                AND (p_upload_id IS NULL OR t.upload_id = p_upload_id)
                AND LOWER(t.description) LIKE '%' || LOWER(v_mapping.vendor_name) || '%'
                AND (t.category_id IS NULL OR t.ai_confidence_score < v_mapping.confidence_threshold)
                -- Direction filter
                AND (
                    (v_mapping.applies_to_debits AND t.amount < 0) OR
                    (v_mapping.applies_to_credits AND t.amount > 0)
                )
                -- Amount range filter
                AND (v_mapping.min_amount IS NULL OR ABS(t.amount) >= v_mapping.min_amount)
                AND (v_mapping.max_amount IS NULL OR ABS(t.amount) <= v_mapping.max_amount)
                -- Pattern filter
                AND (v_mapping.description_pattern IS NULL OR 
                     t.description ~ v_mapping.description_pattern)
            RETURNING 
                CASE WHEN amount < 0 THEN 1 ELSE 0 END as is_debit,
                CASE WHEN amount > 0 THEN 1 ELSE 0 END as is_credit
        )
        SELECT 
            COUNT(*),
            SUM(is_debit),
            SUM(is_credit)
        INTO v_batch_total, v_batch_debits, v_batch_credits
        FROM updated;
        
        -- Update running totals
        v_total := v_total + COALESCE(v_batch_total, 0);
        v_debits := v_debits + COALESCE(v_batch_debits, 0);
        v_credits := v_credits + COALESCE(v_batch_credits, 0);
        
        -- Update usage statistics
        IF v_batch_total > 0 THEN
            UPDATE vendor_category_mappings
            SET 
                times_applied = times_applied + v_batch_total,
                last_applied = CURRENT_TIMESTAMP
            WHERE id = v_mapping.id;
        END IF;
    END LOOP;
    
    RETURN QUERY SELECT v_total, v_debits, v_credits;
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the enhancement
COMMENT ON TABLE vendor_category_mappings IS 
'User-controlled vendor to category mappings with credit/debit awareness. 
Supports transaction direction filtering, amount ranges, and pattern matching.
Legacy category_name column maintained for backward compatibility.';

COMMIT;