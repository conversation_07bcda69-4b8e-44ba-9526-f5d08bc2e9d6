-- Create table to track MIS enhancement applications
CREATE TABLE IF NOT EXISTS mis_enhancements (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenant(id) ON DELETE CASCADE,
    setup_id INTEGER REFERENCES onboarding_status(id),
    enhancement_type VARCHAR(50) NOT NULL,
    accuracy_gain DECIMAL(5, 3) DEFAULT 0.0,
    status VARCHAR(50) DEFAULT 'pending',
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT mis_enhancements_type_check CHECK (enhancement_type IN ('historical', 'schema', 'vendor', 'combined')),
    CONSTRAINT mis_enhancements_status_check CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    CONSTRAINT mis_enhancements_accuracy_gain_check CHECK (accuracy_gain >= 0 AND accuracy_gain <= 1)
);

-- Create indexes for performance
CREATE INDEX idx_mis_enhancements_tenant_id ON mis_enhancements(tenant_id);
CREATE INDEX idx_mis_enhancements_setup_id ON mis_enhancements(setup_id);
CREATE INDEX idx_mis_enhancements_type ON mis_enhancements(enhancement_type);
CREATE INDEX idx_mis_enhancements_status ON mis_enhancements(status);

-- Add unique constraint to prevent duplicate enhancements
CREATE UNIQUE INDEX idx_mis_enhancements_unique_tenant_type 
ON mis_enhancements(tenant_id, enhancement_type) 
WHERE status = 'completed';

-- Add comment
COMMENT ON TABLE mis_enhancements IS 'Tracks MIS enhancement applications and their accuracy improvements';