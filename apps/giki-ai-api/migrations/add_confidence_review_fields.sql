-- Add fields for confidence-based categorization and review workflow
-- This migration adds support for:
-- 1. Tracking how transactions were categorized (AI vs user)
-- 2. Marking transactions for review
-- 3. Grouping similar low-confidence transactions
-- 4. Audit trail for categorization

BEGIN;

-- Add review and categorization tracking fields
ALTER TABLE transactions
ADD COLUMN IF NOT EXISTS auto_categorized BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS user_categorized BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS needs_review BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS ai_grouping_id VARCHAR(100),
ADD COLUMN IF NOT EXISTS categorized_by INTEGER REFERENCES users(id),
ADD COLUMN IF NOT EXISTS review_notes TEXT;

-- Add indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_transactions_needs_review 
ON transactions(tenant_id, upload_id, needs_review) 
WHERE needs_review = true;

CREATE INDEX IF NOT EXISTS idx_transactions_auto_categorized 
ON transactions(tenant_id, upload_id, auto_categorized) 
WHERE auto_categorized = true;

CREATE INDEX IF NOT EXISTS idx_transactions_grouping 
ON transactions(tenant_id, ai_grouping_id) 
WHERE ai_grouping_id IS NOT NULL;

-- Create a view for the review queue
CREATE OR REPLACE VIEW review_queue AS
WITH categorization_stats AS (
    SELECT 
        upload_id,
        tenant_id,
        COUNT(*) FILTER (WHERE auto_categorized = true) as auto_count,
        COUNT(*) FILTER (WHERE needs_review = true AND ai_suggested_category IS NOT NULL) as suggestion_count,
        COUNT(*) FILTER (WHERE ai_grouping_id IS NOT NULL) as grouped_count,
        COUNT(*) FILTER (WHERE category_id IS NULL AND ai_suggested_category IS NULL) as uncategorized_count
    FROM transactions
    GROUP BY upload_id, tenant_id
)
SELECT 
    t.id,
    t.upload_id,
    t.tenant_id,
    t.date,
    t.description,
    t.amount,
    t.category_id,
    t.ai_suggested_category,
    t.ai_confidence_score,
    t.needs_review,
    t.ai_grouping_id,
    c.path as current_category_path,
    sc.path as suggested_category_path,
    cs.auto_count,
    cs.suggestion_count,
    cs.grouped_count,
    cs.uncategorized_count
FROM transactions t
LEFT JOIN categories c ON t.category_id = c.id
LEFT JOIN categories sc ON t.ai_suggested_category = sc.id
JOIN categorization_stats cs ON t.upload_id = cs.upload_id AND t.tenant_id = cs.tenant_id
WHERE t.needs_review = true OR (t.category_id IS NULL AND t.auto_categorized = false);

-- Create a view for transaction groups
CREATE OR REPLACE VIEW transaction_groups AS
SELECT 
    ai_grouping_id,
    tenant_id,
    upload_id,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(ai_confidence_score) as avg_confidence,
    ARRAY_AGG(DISTINCT LEFT(description, 50)) as sample_descriptions,
    MODE() WITHIN GROUP (ORDER BY ai_suggested_category) as suggested_category_id
FROM transactions
WHERE ai_grouping_id IS NOT NULL
AND category_id IS NULL
GROUP BY ai_grouping_id, tenant_id, upload_id;

-- Function to get review queue summary
CREATE OR REPLACE FUNCTION get_review_queue_summary(
    p_tenant_id INTEGER,
    p_upload_id TEXT
)
RETURNS TABLE (
    status VARCHAR(50),
    count INTEGER,
    percentage DECIMAL(5,2)
) AS $$
DECLARE
    v_total INTEGER;
BEGIN
    -- Get total transaction count
    SELECT COUNT(*) INTO v_total
    FROM transactions
    WHERE tenant_id = p_tenant_id 
    AND upload_id = p_upload_id;
    
    -- Return summary
    RETURN QUERY
    SELECT 
        'auto_categorized'::VARCHAR(50) as status,
        COUNT(*)::INTEGER as count,
        CASE WHEN v_total > 0 
            THEN ROUND((COUNT(*) * 100.0 / v_total)::DECIMAL, 2)
            ELSE 0 
        END as percentage
    FROM transactions
    WHERE tenant_id = p_tenant_id 
    AND upload_id = p_upload_id
    AND auto_categorized = true
    
    UNION ALL
    
    SELECT 
        'needs_review'::VARCHAR(50),
        COUNT(*)::INTEGER,
        CASE WHEN v_total > 0 
            THEN ROUND((COUNT(*) * 100.0 / v_total)::DECIMAL, 2)
            ELSE 0 
        END
    FROM transactions
    WHERE tenant_id = p_tenant_id 
    AND upload_id = p_upload_id
    AND needs_review = true
    
    UNION ALL
    
    SELECT 
        'grouped'::VARCHAR(50),
        COUNT(*)::INTEGER,
        CASE WHEN v_total > 0 
            THEN ROUND((COUNT(*) * 100.0 / v_total)::DECIMAL, 2)
            ELSE 0 
        END
    FROM transactions
    WHERE tenant_id = p_tenant_id 
    AND upload_id = p_upload_id
    AND ai_grouping_id IS NOT NULL
    AND category_id IS NULL
    
    UNION ALL
    
    SELECT 
        'uncategorized'::VARCHAR(50),
        COUNT(*)::INTEGER,
        CASE WHEN v_total > 0 
            THEN ROUND((COUNT(*) * 100.0 / v_total)::DECIMAL, 2)
            ELSE 0 
        END
    FROM transactions
    WHERE tenant_id = p_tenant_id 
    AND upload_id = p_upload_id
    AND category_id IS NULL
    AND ai_suggested_category IS NULL
    AND NOT auto_categorized;
END;
$$ LANGUAGE plpgsql;

-- Add comments
COMMENT ON COLUMN transactions.auto_categorized IS 
'True if transaction was automatically categorized with high confidence (>90%)';

COMMENT ON COLUMN transactions.needs_review IS 
'True if transaction has medium confidence suggestion (70-90%) needing review';

COMMENT ON COLUMN transactions.ai_grouping_id IS 
'Group ID for similar low-confidence transactions that can be bulk categorized';

COMMENT ON VIEW review_queue IS 
'Transactions needing human review, including suggestions and uncategorized items';

COMMENT ON VIEW transaction_groups IS 
'Groups of similar transactions for efficient bulk categorization';

COMMIT;