-- Migration: Fix foreign key constraint in categories table to point to correct tenants table
-- Date: 2025-07-07  
-- Purpose: Fix constraint that points to 'tenant' (singular) when it should point to 'tenants' (plural)

-- Drop the incorrect foreign key constraint
ALTER TABLE categories DROP CONSTRAINT IF EXISTS categories_tenant_id_fkey;

-- Add the correct foreign key constraint pointing to 'tenants' table
ALTER TABLE categories ADD CONSTRAINT fk_categories_tenant 
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;

-- Add comment for documentation
COMMENT ON CONSTRAINT fk_categories_tenant ON categories IS 'Fixed foreign key constraint to reference correct tenants table (plural)';