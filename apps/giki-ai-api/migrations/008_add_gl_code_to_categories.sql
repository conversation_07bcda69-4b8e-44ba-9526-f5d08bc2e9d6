-- Migration: Add gl_code column to categories table for MIS functionality
-- Date: 2025-07-07
-- Purpose: Support GL code integration for MIS categorization service

-- Add gl_code column to categories table
ALTER TABLE categories ADD COLUMN IF NOT EXISTS gl_code VARCHAR(20);

-- Create index for gl_code lookups
CREATE INDEX IF NOT EXISTS idx_categories_gl_code ON categories(gl_code) WHERE gl_code IS NOT NULL;

-- Create composite index for tenant_id and gl_code
CREATE INDEX IF NOT EXISTS idx_categories_tenant_gl_code ON categories(tenant_id, gl_code) WHERE gl_code IS NOT NULL;

-- Add comment for documentation
COMMENT ON COLUMN categories.gl_code IS 'General Ledger code for accounting integration and enhanced MIS categorization accuracy';