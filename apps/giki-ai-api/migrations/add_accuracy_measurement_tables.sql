-- Migration: Add accuracy measurement tables for production accuracy testing
-- Purpose: Support comprehensive accuracy measurement across three categorization scenarios
-- Created: 2025-06-26
-- Author: <PERSON> (AI Assistant)

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ==================== Accuracy Tests Table ====================

CREATE TABLE IF NOT EXISTS accuracy_tests (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    
    -- Test configuration
    name VARCHAR(200) NOT NULL,
    description TEXT,
    scenario VARCHAR(50) NOT NULL CHECK (scenario IN ('historical_data', 'schema_only', 'zero_onboarding')),
    test_data_source VARCHAR(500) NOT NULL,
    category_schema_id INTEGER, -- Foreign key to category_schemas table
    sample_size INTEGER NOT NULL DEFAULT 100 CHECK (sample_size >= 1 AND sample_size <= 10000),
    
    -- Execution tracking
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Results summary
    total_transactions INTEGER DEFAULT 0,
    successful_categorizations INTEGER DEFAULT 0,
    ai_judge_correct INTEGER DEFAULT 0,
    ai_judge_incorrect INTEGER DEFAULT 0,
    ai_judge_partially_correct INTEGER DEFAULT 0,
    
    -- Calculated metrics
    precision DECIMAL(5,4) CHECK (precision >= 0.0 AND precision <= 1.0),
    recall DECIMAL(5,4) CHECK (recall >= 0.0 AND recall <= 1.0),
    f1_score DECIMAL(5,4) CHECK (f1_score >= 0.0 AND f1_score <= 1.0),
    accuracy_percentage DECIMAL(5,2) CHECK (accuracy_percentage >= 0.0 AND accuracy_percentage <= 100.0),
    
    -- Error tracking
    error_message TEXT,
    error_details JSONB,
    
    -- Metadata
    created_by VARCHAR(100),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_accuracy_tests_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT chk_accuracy_tests_dates CHECK (
        started_at IS NULL OR started_at >= created_at
    ),
    CONSTRAINT chk_accuracy_tests_completion CHECK (
        completed_at IS NULL OR (started_at IS NOT NULL AND completed_at >= started_at)
    )
);

-- Indexes for accuracy_tests
CREATE INDEX idx_accuracy_tests_tenant_id ON accuracy_tests(tenant_id);
CREATE INDEX idx_accuracy_tests_scenario ON accuracy_tests(scenario);
CREATE INDEX idx_accuracy_tests_status ON accuracy_tests(status);
CREATE INDEX idx_accuracy_tests_created_at ON accuracy_tests(created_at DESC);
CREATE INDEX idx_accuracy_tests_tenant_scenario ON accuracy_tests(tenant_id, scenario);

-- ==================== Category Schemas Table ====================

CREATE TABLE IF NOT EXISTS category_schemas (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    
    -- Schema identification
    name VARCHAR(200) NOT NULL,
    description TEXT,
    schema_format VARCHAR(50) NOT NULL CHECK (schema_format IN ('excel', 'csv', 'json', 'yaml')),
    schema_data JSONB NOT NULL,
    
    -- Schema metadata
    category_count INTEGER NOT NULL DEFAULT 0 CHECK (category_count >= 0),
    max_hierarchy_depth INTEGER NOT NULL DEFAULT 1 CHECK (max_hierarchy_depth >= 1),
    has_gl_codes BOOLEAN NOT NULL DEFAULT FALSE,
    gl_code_mapping JSONB,
    
    -- Import metadata
    imported_from VARCHAR(500) NOT NULL,
    imported_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    imported_by VARCHAR(100),
    
    -- Usage tracking
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER NOT NULL DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_category_schemas_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT uq_category_schemas_tenant_name UNIQUE (tenant_id, name)
);

-- Indexes for category_schemas
CREATE INDEX idx_category_schemas_tenant_id ON category_schemas(tenant_id);
CREATE INDEX idx_category_schemas_active ON category_schemas(is_active);
CREATE INDEX idx_category_schemas_format ON category_schemas(schema_format);
CREATE INDEX idx_category_schemas_imported_at ON category_schemas(imported_at DESC);

-- Add foreign key constraint to accuracy_tests after category_schemas is created
ALTER TABLE accuracy_tests 
ADD CONSTRAINT fk_accuracy_tests_schema 
FOREIGN KEY (category_schema_id) REFERENCES category_schemas(id) ON DELETE SET NULL;

-- ==================== AI Judgments Table ====================

CREATE TABLE IF NOT EXISTS ai_judgments (
    id SERIAL PRIMARY KEY,
    test_id INTEGER NOT NULL,
    
    -- Transaction reference
    transaction_id VARCHAR(100) NOT NULL,
    
    -- Categorization comparison
    original_category VARCHAR(500),
    ai_category VARCHAR(500) NOT NULL,
    ai_full_hierarchy VARCHAR(1000),
    ai_confidence DECIMAL(5,4) NOT NULL CHECK (ai_confidence >= 0.0 AND ai_confidence <= 1.0),
    
    -- AI judge evaluation
    judgment_result VARCHAR(20) NOT NULL CHECK (judgment_result IN ('correct', 'incorrect', 'partially_correct', 'indeterminate')),
    judgment_confidence DECIMAL(5,4) NOT NULL CHECK (judgment_confidence >= 0.0 AND judgment_confidence <= 1.0),
    judgment_reasoning TEXT,
    
    -- Detailed comparison analysis
    category_exact_match BOOLEAN NOT NULL DEFAULT FALSE,
    category_semantic_match BOOLEAN NOT NULL DEFAULT FALSE,
    hierarchy_level_matches JSONB, -- JSON object with level-by-level comparison
    
    -- Transaction context
    transaction_description VARCHAR(1000) NOT NULL,
    transaction_amount DECIMAL(12,2) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('debit', 'credit')),
    
    -- Evaluation metadata
    judged_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    judge_model VARCHAR(100) NOT NULL DEFAULT 'gemini-2.0-flash-001',
    judge_version VARCHAR(20) NOT NULL DEFAULT '1.0',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_ai_judgments_test FOREIGN KEY (test_id) REFERENCES accuracy_tests(id) ON DELETE CASCADE
);

-- Indexes for ai_judgments
CREATE INDEX idx_ai_judgments_test_id ON ai_judgments(test_id);
CREATE INDEX idx_ai_judgments_result ON ai_judgments(judgment_result);
CREATE INDEX idx_ai_judgments_transaction_id ON ai_judgments(transaction_id);
CREATE INDEX idx_ai_judgments_judged_at ON ai_judgments(judged_at DESC);
CREATE INDEX idx_ai_judgments_test_result ON ai_judgments(test_id, judgment_result);

-- ==================== Accuracy Metrics Table ====================

CREATE TABLE IF NOT EXISTS accuracy_metrics (
    id SERIAL PRIMARY KEY,
    test_id INTEGER NOT NULL,
    
    -- Metric identification
    metric_name VARCHAR(100) NOT NULL, -- "overall", "category_X", "level_1", etc.
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('precision', 'recall', 'f1_score', 'accuracy')),
    
    -- Metric values
    value DECIMAL(8,6) NOT NULL CHECK (value >= 0.0 AND value <= 1.0),
    sample_count INTEGER NOT NULL CHECK (sample_count >= 0),
    
    -- Confusion matrix breakdown
    true_positives INTEGER NOT NULL DEFAULT 0,
    false_positives INTEGER NOT NULL DEFAULT 0,
    true_negatives INTEGER NOT NULL DEFAULT 0,
    false_negatives INTEGER NOT NULL DEFAULT 0,
    
    -- Categorization context filters
    category_filter VARCHAR(500), -- Specific category for filtered metrics
    confidence_range VARCHAR(20), -- "0.0-0.5", "0.5-0.8", "0.8-1.0"
    hierarchy_level INTEGER CHECK (hierarchy_level >= 1), -- Hierarchy level for level-specific metrics
    
    -- Calculation metadata
    calculated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    calculation_method VARCHAR(100) NOT NULL, -- Description of calculation method
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_accuracy_metrics_test FOREIGN KEY (test_id) REFERENCES accuracy_tests(id) ON DELETE CASCADE,
    CONSTRAINT uq_accuracy_metrics_unique UNIQUE (test_id, metric_name, metric_type, category_filter, confidence_range, hierarchy_level)
);

-- Indexes for accuracy_metrics
CREATE INDEX idx_accuracy_metrics_test_id ON accuracy_metrics(test_id);
CREATE INDEX idx_accuracy_metrics_type ON accuracy_metrics(metric_type);
CREATE INDEX idx_accuracy_metrics_name ON accuracy_metrics(metric_name);
CREATE INDEX idx_accuracy_metrics_test_type ON accuracy_metrics(test_id, metric_type);
CREATE INDEX idx_accuracy_metrics_calculated_at ON accuracy_metrics(calculated_at DESC);

-- ==================== Views for Reporting ====================

-- View for accuracy test summaries
CREATE OR REPLACE VIEW accuracy_test_summaries AS
SELECT 
    t.id,
    t.tenant_id,
    t.name,
    t.scenario,
    t.status,
    t.created_at,
    t.completed_at,
    t.total_transactions,
    t.successful_categorizations,
    t.precision,
    t.recall,
    t.f1_score,
    t.accuracy_percentage,
    
    -- Calculate derived metrics
    CASE 
        WHEN t.total_transactions > 0 THEN 
            ROUND((t.successful_categorizations::DECIMAL / t.total_transactions * 100), 2)
        ELSE 0.0 
    END AS success_rate,
    
    CASE 
        WHEN (t.ai_judge_correct + t.ai_judge_incorrect) > 0 THEN 
            ROUND((t.ai_judge_correct::DECIMAL / (t.ai_judge_correct + t.ai_judge_incorrect) * 100), 2)
        ELSE NULL 
    END AS ai_judge_accuracy,
    
    -- Execution duration
    CASE 
        WHEN t.started_at IS NOT NULL AND t.completed_at IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (t.completed_at - t.started_at))
        ELSE NULL 
    END AS execution_duration_seconds,
    
    -- Category schema info
    cs.name AS category_schema_name,
    cs.category_count AS schema_category_count
    
FROM accuracy_tests t
LEFT JOIN category_schemas cs ON t.category_schema_id = cs.id;

-- View for AI judgment statistics
CREATE OR REPLACE VIEW ai_judgment_statistics AS
SELECT 
    test_id,
    COUNT(*) as total_judgments,
    COUNT(*) FILTER (WHERE judgment_result = 'correct') as correct_count,
    COUNT(*) FILTER (WHERE judgment_result = 'incorrect') as incorrect_count,
    COUNT(*) FILTER (WHERE judgment_result = 'partially_correct') as partially_correct_count,
    COUNT(*) FILTER (WHERE judgment_result = 'indeterminate') as indeterminate_count,
    
    -- Percentages
    ROUND(COUNT(*) FILTER (WHERE judgment_result = 'correct')::DECIMAL / COUNT(*) * 100, 2) as correct_percentage,
    ROUND(COUNT(*) FILTER (WHERE category_exact_match = TRUE)::DECIMAL / COUNT(*) * 100, 2) as exact_match_percentage,
    ROUND(COUNT(*) FILTER (WHERE category_semantic_match = TRUE)::DECIMAL / COUNT(*) * 100, 2) as semantic_match_percentage,
    
    -- Average confidence scores
    ROUND(AVG(ai_confidence), 4) as avg_ai_confidence,
    ROUND(AVG(judgment_confidence), 4) as avg_judgment_confidence,
    
    -- Confidence breakdown
    COUNT(*) FILTER (WHERE judgment_confidence >= 0.8) as high_confidence_judgments,
    COUNT(*) FILTER (WHERE judgment_confidence BETWEEN 0.5 AND 0.8) as medium_confidence_judgments,
    COUNT(*) FILTER (WHERE judgment_confidence < 0.5) as low_confidence_judgments

FROM ai_judgments
GROUP BY test_id;

-- ==================== Functions for Maintenance ====================

-- Function to clean up old accuracy test data (optional - for data retention)
CREATE OR REPLACE FUNCTION cleanup_old_accuracy_tests(retention_days INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- Delete tests older than retention period
    DELETE FROM accuracy_tests 
    WHERE created_at < NOW() - INTERVAL '1 day' * retention_days
    AND status IN ('completed', 'failed', 'cancelled');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update accuracy test statistics
CREATE OR REPLACE FUNCTION update_accuracy_test_stats(test_id_param INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE accuracy_tests SET
        ai_judge_correct = (
            SELECT COUNT(*) FROM ai_judgments 
            WHERE test_id = test_id_param AND judgment_result = 'correct'
        ),
        ai_judge_incorrect = (
            SELECT COUNT(*) FROM ai_judgments 
            WHERE test_id = test_id_param AND judgment_result = 'incorrect'
        ),
        ai_judge_partially_correct = (
            SELECT COUNT(*) FROM ai_judgments 
            WHERE test_id = test_id_param AND judgment_result = 'partially_correct'
        ),
        updated_at = NOW()
    WHERE id = test_id_param;
END;
$$ LANGUAGE plpgsql;

-- ==================== Triggers ====================

-- Trigger to update accuracy test stats when judgments are added
CREATE OR REPLACE FUNCTION trigger_update_accuracy_stats()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM update_accuracy_test_stats(NEW.test_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_ai_judgments_update_stats
    AFTER INSERT OR UPDATE OR DELETE ON ai_judgments
    FOR EACH ROW EXECUTE FUNCTION trigger_update_accuracy_stats();

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION trigger_update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_accuracy_tests_updated_at
    BEFORE UPDATE ON accuracy_tests
    FOR EACH ROW EXECUTE FUNCTION trigger_update_timestamp();

CREATE TRIGGER trigger_category_schemas_updated_at
    BEFORE UPDATE ON category_schemas
    FOR EACH ROW EXECUTE FUNCTION trigger_update_timestamp();

-- ==================== Grants and Permissions ====================

-- Grant permissions to application user (adjust username as needed)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON accuracy_tests TO giki_ai_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ai_judgments TO giki_ai_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON accuracy_metrics TO giki_ai_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON category_schemas TO giki_ai_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO giki_ai_user;

-- ==================== Sample Data (Optional - for testing) ====================

-- Insert a sample category schema for testing
INSERT INTO category_schemas (
    tenant_id, name, description, schema_format, schema_data, 
    category_count, max_hierarchy_depth, imported_from
) VALUES (
    1, 'Standard Business Categories', 'Standard business expense categories for testing',
    'json', 
    '{"categories": [
        {"name": "Operating Expenses > Office Supplies", "level": 2, "gl_code": "6100"},
        {"name": "Operating Expenses > Travel & Entertainment", "level": 2, "gl_code": "6200"},
        {"name": "Marketing > Digital Marketing", "level": 2, "gl_code": "6300"},
        {"name": "Professional Services > Legal", "level": 2, "gl_code": "6400"}
    ]}',
    4, 2, 'sample_schema.json'
) ON CONFLICT DO NOTHING;

-- Migration completion message
SELECT 'Accuracy measurement tables created successfully!' AS migration_status;