-- Performance Optimization Migration: Database Indexes for <500ms API Response Times
-- Date: 2025-01-03
-- Purpose: Add critical database indexes to optimize API performance for production readiness

-- 1. Critical Transaction Query Optimizations
-- Optimize the most common transaction list queries with tenant + date filtering

-- Drop existing slow indexes if they exist
DROP INDEX IF EXISTS idx_transactions_tenant_date;

-- Create composite index for tenant_id + date DESC with included columns for covering index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_date_desc_covering 
ON transactions(tenant_id, date DESC) 
INCLUDE (id, description, amount, account, transaction_type, category_id, ai_category, ai_confidence, vendor_name, entity_id, upload_id);

-- Optimize status filtering queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_categorization_status 
ON transactions(tenant_id, (CASE 
    WHEN category_id IS NOT NULL THEN 'user_modified'
    WHEN ai_category IS NOT NULL THEN 'ai_suggested' 
    WHEN original_category IS NOT NULL THEN 'categorized'
    ELSE 'uncategorized'
END));

-- Optimize amount range queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_amount_range 
ON transactions(tenant_id, amount) WHERE amount IS NOT NULL;

-- Optimize search queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_description_gin 
ON transactions USING gin(to_tsvector('english', description));

-- 2. Dashboard Query Optimizations
-- Optimize monthly trends and category breakdown queries

-- Monthly aggregation optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_month_amount 
ON transactions(tenant_id, date_trunc('month', date), amount);

-- Category breakdown optimization  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_category_amount 
ON transactions(tenant_id, COALESCE(original_category, ai_category), amount) 
WHERE (original_category IS NOT NULL OR ai_category IS NOT NULL);

-- 3. Upload and File Processing Optimizations

-- Upload status queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_uploads_tenant_status_date 
ON uploads(tenant_id, status, uploaded_at DESC);

-- Transaction upload relationship
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_upload_tenant 
ON transactions(upload_id, tenant_id) WHERE upload_id IS NOT NULL;

-- 4. Review Queue and AI Confidence Optimizations

-- AI confidence filtering for review queue
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_confidence_review 
ON transactions(tenant_id, ai_confidence) 
WHERE ai_confidence IS NOT NULL AND ai_confidence < 0.85;

-- Uncategorized transactions for review
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_uncategorized 
ON transactions(tenant_id, amount DESC) 
WHERE category_id IS NULL AND original_category IS NULL;

-- 5. Entity and Vendor Analysis Optimizations

-- Entity transaction analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_entity_date_amount 
ON transactions(entity_id, date, amount) WHERE entity_id IS NOT NULL;

-- Vendor pattern analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_vendor_tenant 
ON transactions(vendor_name, tenant_id) WHERE vendor_name IS NOT NULL;

-- 6. Category Management Optimizations

-- GL Code hierarchy lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_tenant_gl_hierarchy 
ON categories(tenant_id, gl_code, parent_id) WHERE gl_code IS NOT NULL;

-- Category path queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_tenant_path 
ON categories(tenant_id, name, parent_id);

-- 7. Agent and Session Performance

-- Agent session cleanup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_sessions_expires_cleanup 
ON agent_sessions(expires_at) WHERE expires_at < CURRENT_TIMESTAMP;

-- Agent command performance tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_commands_performance 
ON agent_commands(execution_time_ms DESC, created_at DESC) 
WHERE execution_time_ms IS NOT NULL;

-- 8. Reporting and Analytics Optimizations

-- Financial summary calculations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_type_amount_date 
ON transactions(tenant_id, transaction_type, amount, date) 
WHERE amount IS NOT NULL;

-- Time-based reporting
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_tenant_created_reporting 
ON transactions(tenant_id, created_at, amount) 
WHERE created_at IS NOT NULL;

-- 9. Add missing tenant_id indexes for multi-tenant performance

-- Ensure all tables have tenant_id indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_tenant_performance 
ON categories(tenant_id, id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_uploads_tenant_performance 
ON uploads(tenant_id, id);

-- 10. Partial indexes for common filtered queries

-- Active uploads only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_uploads_active_processing 
ON uploads(tenant_id, status, uploaded_at DESC) 
WHERE status IN ('processing', 'completed', 'pending');

-- Recent transactions (last 30 days) - commonly queried
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_recent_activity 
ON transactions(tenant_id, date DESC, amount) 
WHERE date >= CURRENT_DATE - INTERVAL '30 days';

-- High-value transactions for alerts
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_high_value_alerts 
ON transactions(tenant_id, amount, date DESC) 
WHERE ABS(amount) > 1000;

-- 11. Performance monitoring and statistics

-- Create function to update table statistics more frequently
CREATE OR REPLACE FUNCTION update_transaction_statistics()
RETURNS void AS $$
BEGIN
    -- Update statistics for critical tables
    ANALYZE transactions;
    ANALYZE categories;
    ANALYZE uploads;
    ANALYZE users;
END;
$$ LANGUAGE plpgsql;

-- Create view for performance monitoring
CREATE OR REPLACE VIEW api_performance_monitor AS
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    ROUND((idx_scan::numeric / GREATEST(seq_scan + idx_scan, 1)) * 100, 2) as index_usage_pct
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY seq_tup_read DESC;

-- Grant permissions
GRANT SELECT ON api_performance_monitor TO giki_ai_user;
GRANT EXECUTE ON FUNCTION update_transaction_statistics() TO giki_ai_user;

-- Schedule statistics updates (requires pg_cron extension in production)
-- SELECT cron.schedule('update-stats', '*/30 * * * *', 'SELECT update_transaction_statistics();');

-- Performance optimization notes:
-- 1. Use CONCURRENTLY to avoid blocking during index creation
-- 2. Covering indexes include frequently accessed columns to avoid table lookups
-- 3. Partial indexes reduce size and improve performance for filtered queries
-- 4. GIN indexes for full-text search on descriptions
-- 5. Expression indexes for computed status fields
-- 6. Time-based partitioning consideration for future scaling

COMMIT;