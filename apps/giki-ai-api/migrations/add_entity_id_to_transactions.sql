-- Migration: Add entity_id column to transactions table for proper entity-transaction relationships
-- This fixes the entity spending amounts showing as $0.00 in Knowledge Hub
-- Updated to use standard table names (removed dev_ prefix system)

-- Add entity_id column to transactions table
ALTER TABLE transaction 
ADD COLUMN entity_id VARCHAR(36) REFERENCES entity(id);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_transaction_entity_id ON transaction(entity_id);

-- Update existing transactions to link them with entities based on description matching
-- This is a one-time data migration to populate the new entity_id field

UPDATE transaction 
SET entity_id = (
    SELECT e.id 
    FROM entity e 
    WHERE e.tenant_id = transaction.tenant_id 
    AND transaction.description ILIKE CONCAT('%', e.name, '%')
    LIMIT 1
)
WHERE entity_id IS NULL;

-- Verify the migration
SELECT 
    COUNT(*) as total_transactions,
    COUNT(entity_id) as transactions_with_entities,
    ROUND(COUNT(entity_id) * 100.0 / COUNT(*), 2) as percentage_linked
FROM transaction;
