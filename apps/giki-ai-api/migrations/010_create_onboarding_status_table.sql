-- Migration: Create onboarding_status table for MIS setup workflow
-- Date: 2025-07-07
-- Purpose: Support MIS onboarding workflow tracking and status management

-- Create onboarding_status table
CREATE TABLE IF NOT EXISTS onboarding_status (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    onboarding_type VARCHAR(50) DEFAULT 'historical_data',
    stage VARCHAR(50) DEFAULT 'not_started',
    approved_for_production BOOLEAN DEFAULT FALSE,
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by_user_id INTEGER,
    approval_notes TEXT,
    last_validation_id VARCHAR(255),
    last_validation_accuracy DECIMAL(5,4),
    total_transactions INTEGER DEFAULT 0,
    transactions_with_labels INTEGER DEFAULT 0,
    date_range_start TIMESTAMP WITH TIME ZONE,
    date_range_end TIMESTAMP WITH TIME ZONE,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_onboarding_status_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_onboarding_status_approved_by FOREIGN KEY (approved_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT uq_onboarding_status_tenant UNIQUE (tenant_id),
    CONSTRAINT chk_onboarding_status_accuracy CHECK (last_validation_accuracy IS NULL OR (last_validation_accuracy >= 0 AND last_validation_accuracy <= 1)),
    CONSTRAINT chk_onboarding_status_transactions CHECK (total_transactions >= 0 AND transactions_with_labels >= 0 AND transactions_with_labels <= total_transactions)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_onboarding_status_tenant ON onboarding_status(tenant_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_status_stage ON onboarding_status(stage);
CREATE INDEX IF NOT EXISTS idx_onboarding_status_approved ON onboarding_status(approved_for_production);
CREATE INDEX IF NOT EXISTS idx_onboarding_status_type ON onboarding_status(onboarding_type);
CREATE INDEX IF NOT EXISTS idx_onboarding_status_activity ON onboarding_status(last_activity DESC);

-- Add comments for documentation
COMMENT ON TABLE onboarding_status IS 'Tracks MIS onboarding workflow status and production approval for each tenant';
COMMENT ON COLUMN onboarding_status.onboarding_type IS 'Types: zero_onboarding (M1), historical_data (M2), schema_only (M3)';
COMMENT ON COLUMN onboarding_status.stage IS 'Stages: not_started, data_uploaded, corpus_building, validating, completed, zero_ready, schema_imported, schema_ready';
COMMENT ON COLUMN onboarding_status.last_validation_accuracy IS 'Latest validation accuracy score (0.0 to 1.0)';
COMMENT ON COLUMN onboarding_status.total_transactions IS 'Total transaction count for this tenant';
COMMENT ON COLUMN onboarding_status.transactions_with_labels IS 'Number of transactions with manual category labels';