-- Migration: Fix Onboarding Table Relationships
-- Purpose: Fix foreign key references from 'tenant' to 'tenants' table for onboarding domain
-- Issue: Onboarding tables reference old 'tenant' table instead of new 'tenants' table
-- Impact: Resolves "Onboarding relationships not configured" warning in API logs

-- ==================== Fix Onboarding Relationships ====================

-- 1. Fix onboarding_status table foreign key reference
DO $$
BEGIN
    -- Drop old foreign key constraint that references 'tenant' table
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'onboarding_status_tenant_id_fkey'
    ) THEN
        ALTER TABLE onboarding_status DROP CONSTRAINT onboarding_status_tenant_id_fkey;
        RAISE NOTICE 'Dropped old onboarding_status foreign key constraint';
    END IF;
    
    -- Add new foreign key constraint that references 'tenants' table
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_onboarding_status_tenants'
    ) THEN
        ALTER TABLE onboarding_status 
        ADD CONSTRAINT fk_onboarding_status_tenants 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added new onboarding_status foreign key constraint to tenants table';
    END IF;
END $$;

-- 2. Fix rag_corpus table foreign key reference
DO $$
BEGIN
    -- Drop old foreign key constraint that references 'tenant' table
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'rag_corpus_tenant_id_fkey'
    ) THEN
        ALTER TABLE rag_corpus DROP CONSTRAINT rag_corpus_tenant_id_fkey;
        RAISE NOTICE 'Dropped old rag_corpus foreign key constraint';
    END IF;
    
    -- Add new foreign key constraint that references 'tenants' table
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_rag_corpus_tenants'
    ) THEN
        ALTER TABLE rag_corpus 
        ADD CONSTRAINT fk_rag_corpus_tenants 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added new rag_corpus foreign key constraint to tenants table';
    END IF;
END $$;

-- 3. Fix temporal_validations table foreign key reference
DO $$
BEGIN
    -- Drop old foreign key constraint that references 'tenant' table
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'temporal_validations_tenant_id_fkey'
    ) THEN
        ALTER TABLE temporal_validations DROP CONSTRAINT temporal_validations_tenant_id_fkey;
        RAISE NOTICE 'Dropped old temporal_validations foreign key constraint';
    END IF;
    
    -- Add new foreign key constraint that references 'tenants' table
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_temporal_validations_tenants'
    ) THEN
        ALTER TABLE temporal_validations 
        ADD CONSTRAINT fk_temporal_validations_tenants 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added new temporal_validations foreign key constraint to tenants table';
    END IF;
END $$;

-- 4. Fix any other onboarding-related tables that might reference 'tenant'

-- Fix onboarding_uploads table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'onboarding_uploads') THEN
        -- Drop old constraint if it exists
        ALTER TABLE onboarding_uploads DROP CONSTRAINT IF EXISTS onboarding_uploads_tenant_id_fkey;
        
        -- Add new constraint to tenants table
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_onboarding_uploads_tenants'
        ) THEN
            ALTER TABLE onboarding_uploads 
            ADD CONSTRAINT fk_onboarding_uploads_tenants 
            FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;
            RAISE NOTICE 'Fixed onboarding_uploads foreign key constraint';
        END IF;
    END IF;
END $$;

-- Fix onboarding_sessions table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'onboarding_sessions') THEN
        -- Drop old constraint if it exists
        ALTER TABLE onboarding_sessions DROP CONSTRAINT IF EXISTS onboarding_sessions_tenant_id_fkey;
        
        -- Add new constraint to tenants table
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_onboarding_sessions_tenants'
        ) THEN
            ALTER TABLE onboarding_sessions 
            ADD CONSTRAINT fk_onboarding_sessions_tenants 
            FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;
            RAISE NOTICE 'Fixed onboarding_sessions foreign key constraint';
        END IF;
    END IF;
END $$;

-- ==================== Data Migration ====================

-- 5. Migrate data from 'tenant' to 'tenants' table if needed
-- Only migrate records that exist in 'tenant' but not in 'tenants'
INSERT INTO tenants (id, name, domain, settings, subscription_status, subscription_plan, created_at, updated_at, is_active)
SELECT t.id, t.name, t.domain, t.settings, t.subscription_status, t.subscription_plan, t.created_at, t.updated_at, true
FROM tenant t
WHERE NOT EXISTS (SELECT 1 FROM tenants tn WHERE tn.id = t.id)
ON CONFLICT (id) DO NOTHING;

-- ==================== Validation ====================

-- 6. Validate that all onboarding relationships are properly configured
DO $$
DECLARE
    orphaned_onboarding INTEGER := 0;
    orphaned_rag_corpus INTEGER := 0;
    orphaned_temporal_validations INTEGER := 0;
BEGIN
    -- Check for orphaned onboarding_status records
    SELECT COUNT(*) INTO orphaned_onboarding
    FROM onboarding_status os
    WHERE NOT EXISTS (SELECT 1 FROM tenants t WHERE t.id = os.tenant_id);
    
    -- Check for orphaned rag_corpus records
    SELECT COUNT(*) INTO orphaned_rag_corpus
    FROM rag_corpus rc
    WHERE NOT EXISTS (SELECT 1 FROM tenants t WHERE t.id = rc.tenant_id);
    
    -- Check for orphaned temporal_validations records
    SELECT COUNT(*) INTO orphaned_temporal_validations
    FROM temporal_validations tv
    WHERE NOT EXISTS (SELECT 1 FROM tenants t WHERE t.id = tv.tenant_id);
    
    IF orphaned_onboarding > 0 THEN
        RAISE WARNING 'Found % orphaned onboarding_status records with invalid tenant_id', orphaned_onboarding;
    END IF;
    
    IF orphaned_rag_corpus > 0 THEN
        RAISE WARNING 'Found % orphaned rag_corpus records with invalid tenant_id', orphaned_rag_corpus;
    END IF;
    
    IF orphaned_temporal_validations > 0 THEN
        RAISE WARNING 'Found % orphaned temporal_validations records with invalid tenant_id', orphaned_temporal_validations;
    END IF;
    
    RAISE NOTICE 'Onboarding relationships validation completed';
END $$;

-- ==================== Remove Warning Message ====================

-- 7. Create a view to confirm onboarding relationships are properly configured
CREATE OR REPLACE VIEW onboarding_relationships_status AS
SELECT 
    'onboarding_status' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN EXISTS (SELECT 1 FROM tenants t WHERE t.id = os.tenant_id) THEN 1 END) as valid_relationships,
    COUNT(CASE WHEN NOT EXISTS (SELECT 1 FROM tenants t WHERE t.id = os.tenant_id) THEN 1 END) as invalid_relationships
FROM onboarding_status os
UNION ALL
SELECT 
    'rag_corpus' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN EXISTS (SELECT 1 FROM tenants t WHERE t.id = rc.tenant_id) THEN 1 END) as valid_relationships,
    COUNT(CASE WHEN NOT EXISTS (SELECT 1 FROM tenants t WHERE t.id = rc.tenant_id) THEN 1 END) as invalid_relationships
FROM rag_corpus rc
UNION ALL
SELECT 
    'temporal_validations' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN EXISTS (SELECT 1 FROM tenants t WHERE t.id = tv.tenant_id) THEN 1 END) as valid_relationships,
    COUNT(CASE WHEN NOT EXISTS (SELECT 1 FROM tenants t WHERE t.id = tv.tenant_id) THEN 1 END) as invalid_relationships
FROM temporal_validations tv;

-- Final validation message
SELECT 
    'Onboarding relationships fix completed successfully!' as status,
    (SELECT COUNT(*) FROM onboarding_status) as onboarding_status_records,
    (SELECT COUNT(*) FROM rag_corpus) as rag_corpus_records,
    (SELECT COUNT(*) FROM temporal_validations) as temporal_validations_records,
    'All onboarding tables now properly reference tenants table' as message;