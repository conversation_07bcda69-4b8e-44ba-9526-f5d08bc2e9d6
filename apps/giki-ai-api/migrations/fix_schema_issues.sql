-- Migration: Fix Critical Database Schema Issues
-- Purpose: Resolve foreign key references, table naming, and missing relationships
-- Created: 2025-06-29
-- Author: <PERSON> (Backend Development)

-- ==================== Schema Fixes ====================

-- 1. Create missing entities table (referenced by transactions)
CREATE TABLE IF NOT EXISTS entities (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) DEFAULT 'vendor',
    description TEXT,
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_entities_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT uq_entities_tenant_name UNIQUE (tenant_id, name)
);

-- Create indexes for entities table
CREATE INDEX IF NOT EXISTS idx_entities_tenant_id ON entities(tenant_id);
CREATE INDEX IF NOT EXISTS idx_entities_name ON entities(name);
CREATE INDEX IF NOT EXISTS idx_entities_type ON entities(type);
CREATE INDEX IF NOT EXISTS idx_entities_active ON entities(is_active);

-- 2. Fix foreign key references in transactions table
-- First, ensure transactions.entity_id exists and is properly constrained
DO $$
BEGIN
    -- Check if entity_id column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'entity_id'
    ) THEN
        ALTER TABLE transactions ADD COLUMN entity_id INTEGER;
    END IF;
    
    -- Add foreign key constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_transactions_entity'
    ) THEN
        ALTER TABLE transactions 
        ADD CONSTRAINT fk_transactions_entity 
        FOREIGN KEY (entity_id) REFERENCES entities(id) ON DELETE SET NULL;
    END IF;
END $$;

-- 3. Fix uploads table references (standardize on 'uploads')
-- Ensure all tables reference 'uploads' not 'upload'

-- Update file_schemas table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'file_schemas') THEN
        -- Drop old constraint if it exists
        ALTER TABLE file_schemas DROP CONSTRAINT IF EXISTS fk_file_schemas_upload;
        
        -- Add correct constraint to uploads table
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_file_schemas_uploads'
        ) THEN
            ALTER TABLE file_schemas 
            ADD CONSTRAINT fk_file_schemas_uploads 
            FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE;
        END IF;
    END IF;
END $$;

-- Update onboarding_uploads table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'onboarding_uploads') THEN
        -- Drop old constraint if it exists
        ALTER TABLE onboarding_uploads DROP CONSTRAINT IF EXISTS fk_onboarding_uploads_upload;
        
        -- Add correct constraint to uploads table
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_onboarding_uploads_uploads'
        ) THEN
            ALTER TABLE onboarding_uploads 
            ADD CONSTRAINT fk_onboarding_uploads_uploads 
            FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE;
        END IF;
    END IF;
END $$;

-- 4. Fix schema discovery fields in categories table
-- Add missing columns from schema discovery migration
DO $$
BEGIN
    -- Add original_labels column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'original_labels'
    ) THEN
        ALTER TABLE categories ADD COLUMN original_labels TEXT[];
    END IF;
    
    -- Add is_unified_category column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'is_unified_category'
    ) THEN
        ALTER TABLE categories ADD COLUMN is_unified_category BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add unified_category_id column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'unified_category_id'
    ) THEN
        ALTER TABLE categories ADD COLUMN unified_category_id INTEGER;
    END IF;
    
    -- Add schema_discovery_session_id column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'schema_discovery_session_id'
    ) THEN
        ALTER TABLE categories ADD COLUMN schema_discovery_session_id VARCHAR(255);
    END IF;
    
    -- Add source_files column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'source_files'
    ) THEN
        ALTER TABLE categories ADD COLUMN source_files TEXT[];
    END IF;
    
    -- Add foreign key constraint for unified_category_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_categories_unified_category'
    ) THEN
        ALTER TABLE categories 
        ADD CONSTRAINT fk_categories_unified_category 
        FOREIGN KEY (unified_category_id) REFERENCES categories(id) ON DELETE SET NULL;
    END IF;
END $$;

-- 5. Fix custom_reports table structure to match actual usage
-- Update the table structure to match what the application expects
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'custom_reports') THEN
        -- Add missing columns if they don't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'custom_reports' AND column_name = 'query_config'
        ) THEN
            ALTER TABLE custom_reports ADD COLUMN query_config JSONB DEFAULT '{}';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'custom_reports' AND column_name = 'chart_config'
        ) THEN
            ALTER TABLE custom_reports ADD COLUMN chart_config JSONB DEFAULT '{}';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'custom_reports' AND column_name = 'is_public'
        ) THEN
            ALTER TABLE custom_reports ADD COLUMN is_public BOOLEAN DEFAULT FALSE;
        END IF;
        
        -- Fix foreign key reference to users table
        IF EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_custom_reports_user'
        ) THEN
            ALTER TABLE custom_reports DROP CONSTRAINT fk_custom_reports_user;
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_custom_reports_users'
        ) THEN
            -- Ensure created_by column references users table properly
            IF EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'custom_reports' AND column_name = 'created_by'
            ) THEN
                -- If created_by is a string, we'll assume it's a username reference
                -- For now, just ensure it exists without FK constraint
                NULL;
            END IF;
        END IF;
    END IF;
END $$;

-- 6. Ensure all tenant references are consistent (use 'tenants' table)
-- Fix any remaining references to singular 'tenant' table

-- Update file_processing_reports table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'file_processing_reports') THEN
        -- Drop old constraint if it exists
        ALTER TABLE file_processing_reports DROP CONSTRAINT IF EXISTS fk_file_processing_reports_tenant;
        
        -- Add correct constraint to tenants table
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_file_processing_reports_tenants'
        ) THEN
            ALTER TABLE file_processing_reports 
            ADD CONSTRAINT fk_file_processing_reports_tenants 
            FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;
        END IF;
    END IF;
END $$;

-- 7. Create updated_at trigger for entities table
CREATE OR REPLACE FUNCTION trigger_update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at trigger to entities table
DROP TRIGGER IF EXISTS trigger_entities_updated_at ON entities;
CREATE TRIGGER trigger_entities_updated_at
    BEFORE UPDATE ON entities
    FOR EACH ROW EXECUTE FUNCTION trigger_update_timestamp();

-- 8. Add missing indexes for performance
CREATE INDEX IF NOT EXISTS idx_transactions_entity_id ON transactions(entity_id);
CREATE INDEX IF NOT EXISTS idx_transactions_upload_id ON transactions(upload_id);
CREATE INDEX IF NOT EXISTS idx_transactions_category_id ON transactions(category_id);
CREATE INDEX IF NOT EXISTS idx_transactions_tenant_date ON transactions(tenant_id, date);

CREATE INDEX IF NOT EXISTS idx_categories_unified_category_id ON categories(unified_category_id);
CREATE INDEX IF NOT EXISTS idx_categories_schema_session ON categories(schema_discovery_session_id);
CREATE INDEX IF NOT EXISTS idx_categories_tenant_parent ON categories(tenant_id, parent_id);

-- 9. Data integrity fixes
-- Populate entity_id in transactions where possible
INSERT INTO entities (tenant_id, name, type)
SELECT DISTINCT 
    t.tenant_id,
    TRIM(REGEXP_REPLACE(t.description, '[^A-Za-z0-9\s\-\.]', '', 'g')) as name,
    'vendor' as type
FROM transactions t
WHERE t.entity_id IS NULL 
    AND t.description IS NOT NULL 
    AND LENGTH(TRIM(t.description)) > 2
    AND NOT EXISTS (
        SELECT 1 FROM entities e 
        WHERE e.tenant_id = t.tenant_id 
        AND e.name = TRIM(REGEXP_REPLACE(t.description, '[^A-Za-z0-9\s\-\.]', '', 'g'))
    )
ON CONFLICT (tenant_id, name) DO NOTHING;

-- Update transactions with entity_id based on description matching
UPDATE transactions SET entity_id = (
    SELECT e.id 
    FROM entities e 
    WHERE e.tenant_id = transactions.tenant_id 
    AND e.name = TRIM(REGEXP_REPLACE(transactions.description, '[^A-Za-z0-9\s\-\.]', '', 'g'))
    LIMIT 1
)
WHERE entity_id IS NULL 
    AND description IS NOT NULL 
    AND LENGTH(TRIM(description)) > 2;

-- 10. Validate schema integrity
DO $$
DECLARE
    invalid_fks INTEGER := 0;
    orphaned_records INTEGER := 0;
BEGIN
    -- Check for orphaned transactions (invalid tenant_id)
    SELECT COUNT(*) INTO orphaned_records
    FROM transactions t
    WHERE NOT EXISTS (SELECT 1 FROM tenants tn WHERE tn.id = t.tenant_id);
    
    IF orphaned_records > 0 THEN
        RAISE WARNING 'Found % orphaned transactions with invalid tenant_id', orphaned_records;
    END IF;
    
    -- Check for orphaned categories (invalid tenant_id)
    SELECT COUNT(*) INTO orphaned_records
    FROM categories c
    WHERE NOT EXISTS (SELECT 1 FROM tenants tn WHERE tn.id = c.tenant_id);
    
    IF orphaned_records > 0 THEN
        RAISE WARNING 'Found % orphaned categories with invalid tenant_id', orphaned_records;
    END IF;
    
    RAISE NOTICE 'Schema integrity validation completed successfully';
END $$;

-- ==================== Cleanup ====================

-- Drop any duplicate or unused tables (be careful with this in production)
-- DROP TABLE IF EXISTS tenant CASCADE;  -- Uncomment if confirmed unused
-- DROP TABLE IF EXISTS upload CASCADE;  -- Uncomment if confirmed unused

-- ==================== Views for Validation ====================

-- Create view to monitor entity-transaction relationships
CREATE OR REPLACE VIEW entity_transaction_summary AS
SELECT 
    e.tenant_id,
    e.name as entity_name,
    e.type as entity_type,
    COUNT(t.id) as transaction_count,
    SUM(t.amount) as total_amount,
    MIN(t.date) as first_transaction,
    MAX(t.date) as last_transaction
FROM entities e
LEFT JOIN transactions t ON e.id = t.entity_id
GROUP BY e.tenant_id, e.id, e.name, e.type
ORDER BY transaction_count DESC;

-- Create view to monitor schema discovery progress
CREATE OR REPLACE VIEW schema_discovery_summary AS
SELECT 
    tenant_id,
    schema_discovery_session_id,
    COUNT(*) as categories_count,
    COUNT(*) FILTER (WHERE is_unified_category = TRUE) as unified_categories,
    COUNT(*) FILTER (WHERE original_labels IS NOT NULL) as categories_with_labels,
    COUNT(*) FILTER (WHERE source_files IS NOT NULL) as categories_with_sources
FROM categories
WHERE schema_discovery_session_id IS NOT NULL
GROUP BY tenant_id, schema_discovery_session_id
ORDER BY tenant_id, schema_discovery_session_id;

-- ==================== Final Validation ====================

-- Migration completion message
SELECT 
    'Database schema fixes applied successfully!' as status,
    (SELECT COUNT(*) FROM entities) as entities_created,
    (SELECT COUNT(*) FROM transactions WHERE entity_id IS NOT NULL) as transactions_with_entities,
    (SELECT COUNT(*) FROM categories WHERE original_labels IS NOT NULL) as categories_with_schema_discovery
;