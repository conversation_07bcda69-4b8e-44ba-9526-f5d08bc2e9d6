-- Migration: Create vendor_category_mappings table for MIS vendor enhancement
-- Date: 2025-07-07
-- Purpose: Support vendor-specific category mapping for enhanced MIS accuracy

-- Create vendor_category_mappings table
CREATE TABLE IF NOT EXISTS vendor_category_mappings (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    vendor_name VARCHAR(255) NOT NULL,
    category_name VARCHAR(255) NOT NULL,
    gl_code VARCHAR(20),
    confidence DECIMAL(5,4) DEFAULT 0.9,
    source VARCHAR(50) DEFAULT 'vendor_enhancement',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_vendor_mappings_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT uq_vendor_mappings_tenant_vendor UNIQUE (tenant_id, vendor_name),
    CONSTRAINT chk_vendor_mappings_confidence CHECK (confidence >= 0 AND confidence <= 1),
    CONSTRAINT chk_vendor_mappings_source CHECK (source IN ('vendor_enhancement', 'historical_enhancement', 'manual_mapping', 'ai_generated'))
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_vendor_mappings_tenant ON vendor_category_mappings(tenant_id);
CREATE INDEX IF NOT EXISTS idx_vendor_mappings_vendor ON vendor_category_mappings(vendor_name);
CREATE INDEX IF NOT EXISTS idx_vendor_mappings_category ON vendor_category_mappings(category_name);
CREATE INDEX IF NOT EXISTS idx_vendor_mappings_confidence ON vendor_category_mappings(confidence DESC);
CREATE INDEX IF NOT EXISTS idx_vendor_mappings_source ON vendor_category_mappings(source);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_vendor_mappings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER vendor_mappings_update_timestamp
    BEFORE UPDATE ON vendor_category_mappings
    FOR EACH ROW
    EXECUTE FUNCTION update_vendor_mappings_updated_at();

-- Add comments for documentation
COMMENT ON TABLE vendor_category_mappings IS 'Stores vendor-specific category mappings for enhanced MIS accuracy';
COMMENT ON COLUMN vendor_category_mappings.vendor_name IS 'Vendor/merchant name extracted from transaction descriptions';
COMMENT ON COLUMN vendor_category_mappings.category_name IS 'Mapped category name for this vendor';
COMMENT ON COLUMN vendor_category_mappings.gl_code IS 'General Ledger code for accounting integration';
COMMENT ON COLUMN vendor_category_mappings.confidence IS 'Confidence score for this mapping (0.0 to 1.0)';
COMMENT ON COLUMN vendor_category_mappings.source IS 'Source of this mapping (vendor_enhancement, historical_enhancement, manual_mapping, ai_generated)';