-- Create table for vendor to category mappings
CREATE TABLE IF NOT EXISTS vendor_category_mappings (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenant(id) ON DELETE CASCADE,
    vendor_name VARCHAR(255) NOT NULL,
    category_name VA<PERSON>HAR(255) NOT NULL,
    gl_code VARCHAR(50),
    confidence DECIMAL(3, 2) DEFAULT 0.95,
    source VARCHAR(50) DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT vendor_category_mappings_confidence_check CHECK (confidence >= 0 AND confidence <= 1),
    CONSTRAINT vendor_category_mappings_source_check CHECK (source IN ('manual', 'historical_enhancement', 'vendor_enhancement', 'ai_learning'))
);

-- Create indexes for performance
CREATE INDEX idx_vendor_category_mappings_tenant_id ON vendor_category_mappings(tenant_id);
CREATE INDEX idx_vendor_category_mappings_vendor_name ON vendor_category_mappings(vendor_name);
CREATE INDEX idx_vendor_category_mappings_category_name ON vendor_category_mappings(category_name);

-- Add unique constraint to prevent duplicate vendor mappings per tenant
CREATE UNIQUE INDEX idx_vendor_category_mappings_unique_tenant_vendor 
ON vendor_category_mappings(tenant_id, vendor_name);

-- Add comment
COMMENT ON TABLE vendor_category_mappings IS 'Maps vendor names to categories for improved categorization accuracy';