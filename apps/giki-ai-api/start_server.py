#!/usr/bin/env python3
"""
Simple script to start the FastAPI server for development.
"""

import uvicorn

from giki_ai_api.main import app

if __name__ == "__main__":
    print("[INFO] Starting giki.ai API server...")
    print("[INFO] Server will be available at: http://localhost:8000")
    print("[INFO] API docs will be available at: http://localhost:8000/docs")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,  # Disable reload for stability
        log_level="info",
    )
