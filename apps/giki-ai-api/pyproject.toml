[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "giki-ai-api"
version = "0.1.0"
description = "Backend API for the giki-ai application"
authors = [
    { name = "Giki AI", email = "<EMAIL>" },
]
license = { text = "Proprietary" }
readme = "README.md"
requires-python = ">=3.12,<3.14"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    # Core FastAPI dependencies (latest stable versions from UV environment)
    "fastapi[standard]>=0.115.14",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "uvicorn[standard]>=0.34.3",
    # Authentication and security
    "python-jose[cryptography]>=3.5.0",
    "passlib[bcrypt]>=1.7.4",
    "argon2-cffi>=25.1.0",
    # Configuration and environment
    "python-dotenv>=1.1.1",
    "pyyaml>=6.0.2",
    # Database and async
    "asyncpg>=0.30.0",
    "psutil>=7.0.0",
    # HTTP client and files
    "httpx>=0.28.1",
    "aiofiles>=24.1.0",
    # Email validation and event loop
    "email-validator>=2.2.0",
    "uvloop>=0.21.0",
    # Google Cloud dependencies (latest from Context7 + UV environment)
    "google-cloud-aiplatform>=1.100.0",
    "google-cloud-storage>=2.19.0",
    "google-auth>=2.40.3",
    "google-auth-oauthlib>=1.2.2",
    "google-auth-httplib2>=0.2.0",
    "google-api-core>=2.25.1",
    "proto-plus>=1.26.1",
    "protobuf>=6.31.1",
    "vertexai>=1.43.0",
    # Google Gen AI SDK - Recommended approach (replaces deprecated vertexai.generative_models)
    "google-genai>=1.25.0",
    # AI/ML dependencies (latest working versions)
    "google-adk>=1.5.0",
    "a2a-sdk>=0.2.8",
    # Data processing (compatible versions)
    "pandas>=2.3.0",
    "numpy>=2.3.1",
    "openpyxl>=3.1.5",
    # CLI and utilities
    "typer>=0.16.0",
    "reportlab>=4.4.2",
    # Redis dependencies
    "redis>=6.2.0",
    "aioredis>=2.0.1",
    "psycopg2-binary>=2.9.10",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "pytest>=8.2.2",
    "pytest-asyncio>=0.23.7",
    "pytest-json-report",
    "pytest-cov",
    "pytest-mock",
    "httpx", # Already in main dependencies, but often also in dev
    # Add other dev dependencies if needed
]

[tool.hatch.build.targets.wheel]
packages = ["src/giki_ai_api"]

[tool.ruff]
# Same as Black's default
line-length = 88

# Target Python 3.12
target-version = "py312"

# Exclude files
exclude = [
    ".git",
    "__pycache__",
    "dist",
    "build",
    "migrations",
    ".venv",
    "htmlcov",
    "test-results",
    "coverage_html_report"
]

[tool.ruff.lint]
# Enable pycodestyle (E), Pyflakes (F), flake8-bugbear (B), and imports (I)
select = ["E", "F", "B", "I"]
ignore = [
    "E501",  # Line too long - let ruff format handle this
    "E402",  # Module level import not at top - already fixed
    "B008",  # Do not perform function call in argument defaults - legitimate for FastAPI Depends()
    "B904",  # Within an except clause - sometimes chaining isn't needed
]

[tool.ruff.format]
# Use Black-compatible formatting
quote-style = "double"
indent-style = "space"
line-ending = "auto"

[dependency-groups]
dev = [
    "openpyxl>=3.1.5",
    "pytest-asyncio>=0.23.7",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.14.0",
    "ruff>=0.11.9",
    "websockets>=15.0.1",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests",
]
filterwarnings = [
    # Ignore external library deprecation warnings we cannot control
    "ignore:'crypt' is deprecated:DeprecationWarning:passlib.*",
    "ignore:This feature is deprecated as of June 24, 2025:UserWarning:vertexai.*",
    "ignore:Support for class-based.*config.*is deprecated:DeprecationWarning:pydantic.*",
    # Keep our own deprecation warnings visible
    "default::DeprecationWarning:giki_ai_api.*",
]

[tool.ruff.lint.isort]
# Import sorting compatible with Black
known-first-party = ["giki_ai_api"]
force-single-line = false
combine-as-imports = true
