# General Python
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
.venv/

# Nx
node_modules/
dist/
tmp/

# Test artifacts
.pytest_cache/
coverage_html_report/
coverage.xml
test-results/

# IDE / OS specific
.vscode/
.idea/
*.swp
.DS_Store

# Logs
*.log

# Database files
*.db
*.sqlite
*.sqlite3
data/

# Cloud SQL proxy binaries 
cloud-sql-proxy
cloud-sql-proxy.exe

# Local wheels - DO NOT IGNORE THESE FOR SOURCE DEPLOYMENT
# Ensure this directory and its contents are included
# !local_wheels/
# !local_wheels/*.whl

# Other specific files to exclude if any
# e.g. local_config.json

# Ensure pyproject.toml and requirements.txt are included
!pyproject.toml
!requirements.txt
!Procfile
!main.py
!src/
!local_wheels/
!local_wheels/*.whl