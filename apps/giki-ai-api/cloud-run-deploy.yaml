# Cloud Run deployment configuration
# Use this with: gcloud run services replace cloud-run-deploy.yaml

apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: giki-ai-api
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cpu-throttling: "false"
        # Increase startup timeout for initial deployment
        run.googleapis.com/timeout: "300s"
        # Keep minimum instances warm to prevent cold starts
        run.googleapis.com/min-instances: "1"
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      containers:
      - name: giki-ai-api
        image: gcr.io/YOUR-PROJECT/giki-ai-api:latest
        ports:
        - containerPort: 8080
        env:
        - name: PORT
          value: "8080"
        - name: ENVIRONMENT
          value: "production"
        - name: DISABLE_DATABASE_WARMUP
          value: "true"
        - name: VERTEX_TIMEOUT_SECONDS
          value: "10"
        - name: POOL_PRE_PING
          value: "false"
        - name: STARTUP_TIMEOUT
          value: "10"
        - name: DATABASE_URL
          value: "postgresql://YOUR_DATABASE_URL"
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/app/dev-service-account.json"
        - name: VERTEX_PROJECT_ID
          value: "rezolve-poc"
        - name: VERTEX_LOCATION
          value: "us-central1"
        # CORS Configuration - Add your frontend URLs here
        - name: CORS_ALLOWED_ORIGINS
          value: "http://localhost:4200,http://localhost:3000,https://giki-ai-frontend-************.us-central1.run.app"
        resources:
          limits:
            cpu: 2000m
            memory: 4Gi
          requests:
            cpu: 1000m
            memory: 2Gi
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 3
          timeoutSeconds: 5
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5