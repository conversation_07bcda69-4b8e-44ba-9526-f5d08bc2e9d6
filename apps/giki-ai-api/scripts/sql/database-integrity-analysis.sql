-- DATABASE INTEGRITY ANALYSIS AND SCHEMA CONSOLIDATION
-- Generated: 2025-06-29
-- Purpose: Resolve duplicate tenant tables and ensure production readiness

-- =============================================================================
-- CRITICAL ISSUE: DUPLICATE TENANT TABLES CONSOLIDATION
-- =============================================================================

-- Current State Analysis:
-- 1. 'tenant' table (PRIMARY - used by foreign keys): 4 records (ids: 1,2,3,999)
-- 2. 'tenants' table (DUPLICATE - not used): 2 records (ids: 1,2)
-- 3. All foreign keys reference 'tenant' table correctly
-- 4. All 912 transactions belong to tenant_id 3 (Nuvie)

-- RECOMMENDED CONSOLIDATION STRATEGY:
-- Keep 'tenant' table as primary, migrate any missing data from 'tenants', drop 'tenants'

BEGIN;

-- Step 1: Backup current tenant data for safety
CREATE TABLE tenant_backup AS SELECT * FROM tenant;
CREATE TABLE tenants_backup AS SELECT * FROM tenants;

-- Step 2: Check for any data in 'tenants' that doesn't exist in 'tenant'
SELECT 'Data comparison between tenant tables:' as analysis;
SELECT 
    'tenants_only' as source,
    t.id, t.name, t.domain, t.settings
FROM tenants t 
LEFT JOIN tenant tn ON t.id = tn.id 
WHERE tn.id IS NULL;

-- Step 3: Insert any missing records from 'tenants' to 'tenant' if needed
-- (Based on analysis, this appears unnecessary as tenant table is more complete)

-- Step 4: Verify all foreign key relationships are intact
SELECT 'Foreign key integrity check:' as analysis;
SELECT 
    'transactions_referential_integrity' as check_type,
    COUNT(*) as total_transactions,
    COUNT(tn.id) as valid_references,
    COUNT(*) - COUNT(tn.id) as orphaned_records
FROM transactions t 
LEFT JOIN tenant tn ON t.tenant_id = tn.id;

SELECT 
    'users_referential_integrity' as check_type,
    COUNT(*) as total_users,
    COUNT(tn.id) as valid_references,
    COUNT(*) - COUNT(tn.id) as orphaned_records
FROM users u 
LEFT JOIN tenant tn ON u.tenant_id = tn.id;

-- Step 5: Drop the duplicate 'tenants' table (EXECUTE ONLY AFTER VERIFICATION)
-- DROP TABLE IF EXISTS tenants CASCADE;

COMMIT;

-- =============================================================================
-- PRODUCTION READINESS VALIDATION QUERIES
-- =============================================================================

-- Tenant isolation validation
SELECT 'TENANT ISOLATION ANALYSIS' as report_section;
SELECT 
    tenant_id,
    COUNT(*) as transaction_count,
    MIN(date) as earliest_transaction,
    MAX(date) as latest_transaction,
    SUM(amount) as total_amount,
    COUNT(DISTINCT ai_category) as distinct_categories
FROM transactions 
GROUP BY tenant_id 
ORDER BY tenant_id;

-- AI categorization completeness
SELECT 'AI CATEGORIZATION ANALYSIS' as report_section;
SELECT 
    tenant_id,
    COUNT(*) as total_transactions,
    COUNT(ai_category) as categorized_transactions,
    ROUND(COUNT(ai_category) * 100.0 / COUNT(*), 2) as categorization_percentage,
    AVG(ai_confidence) as avg_confidence
FROM transactions 
GROUP BY tenant_id;

-- Data quality validation
SELECT 'DATA QUALITY ANALYSIS' as report_section;
SELECT 
    'null_descriptions' as metric,
    COUNT(*) as count
FROM transactions 
WHERE description IS NULL OR description = '';

SELECT 
    'null_amounts' as metric,
    COUNT(*) as count
FROM transactions 
WHERE amount IS NULL OR amount = 0;

SELECT 
    'invalid_dates' as metric,
    COUNT(*) as count
FROM transactions 
WHERE date < '2020-01-01' OR date > CURRENT_DATE + INTERVAL '1 year';

-- Performance index analysis
SELECT 'INDEX PERFORMANCE ANALYSIS' as report_section;
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('transactions', 'tenant', 'users', 'categories')
ORDER BY idx_tup_read DESC;

-- Storage utilization
SELECT 'STORAGE ANALYSIS' as report_section;
SELECT 
    table_name,
    pg_size_pretty(pg_total_relation_size(quote_ident(table_name))) as size,
    pg_total_relation_size(quote_ident(table_name)) as size_bytes
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
ORDER BY pg_total_relation_size(quote_ident(table_name)) DESC
LIMIT 10;