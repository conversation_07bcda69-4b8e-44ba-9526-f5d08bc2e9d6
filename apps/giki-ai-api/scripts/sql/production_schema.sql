-- Export database schema for production
-- Generated from development database

-- Table: accuracy_metrics
CREATE TABLE IF NOT EXISTS accuracy_metrics (  id integer NOT NULL DEFAULT nextval('accuracy_metrics_id_seq'::regclass),
  test_id integer NOT NULL,
  metric_name character varying NOT NULL,
  metric_type character varying NOT NULL,
  value numeric NOT NULL,
  sample_count integer NOT NULL,
  true_positives integer NOT NULL DEFAULT 0,
  false_positives integer NOT NULL DEFAULT 0,
  true_negatives integer NOT NULL DEFAULT 0,
  false_negatives integer NOT NULL DEFAULT 0,
  category_filter character varying,
  confidence_range character varying,
  hierarchy_level integer,
  calculated_at timestamp with time zone NOT NULL DEFAULT now(),
  calculation_method character varying NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Table: accuracy_test_summaries
CREATE TABLE IF NOT EXISTS accuracy_test_summaries (  id integer,
  tenant_id integer,
  name character varying,
  scenario character varying,
  status character varying,
  created_at timestamp with time zone,
  completed_at timestamp with time zone,
  total_transactions integer,
  successful_categorizations integer,
  precision numeric,
  recall numeric,
  f1_score numeric,
  accuracy_percentage numeric,
  success_rate numeric,
  ai_judge_accuracy numeric,
  execution_duration_seconds numeric,
  category_schema_name character varying,
  schema_category_count integer
);

-- Table: accuracy_tests
CREATE TABLE IF NOT EXISTS accuracy_tests (  id integer NOT NULL DEFAULT nextval('accuracy_tests_id_seq'::regclass),
  tenant_id integer NOT NULL,
  name character varying NOT NULL,
  description text,
  scenario character varying NOT NULL,
  test_data_source character varying NOT NULL,
  category_schema_id integer,
  sample_size integer NOT NULL DEFAULT 100,
  status character varying NOT NULL DEFAULT 'pending'::character varying,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  total_transactions integer DEFAULT 0,
  successful_categorizations integer DEFAULT 0,
  ai_judge_correct integer DEFAULT 0,
  ai_judge_incorrect integer DEFAULT 0,
  ai_judge_partially_correct integer DEFAULT 0,
  precision numeric,
  recall numeric,
  f1_score numeric,
  accuracy_percentage numeric,
  error_message text,
  error_details jsonb,
  created_by character varying,
  updated_at timestamp with time zone DEFAULT now()
);

-- Table: ai_agent_responses
CREATE TABLE IF NOT EXISTS ai_agent_responses (  id integer NOT NULL DEFAULT nextval('ai_agent_responses_id_seq'::regclass),
  tenant_id integer NOT NULL,
  user_id integer NOT NULL,
  transaction_id character varying,
  agent_type character varying NOT NULL,
  request_type character varying NOT NULL,
  request_data jsonb,
  response_data jsonb,
  confidence_score numeric,
  processing_time_ms integer,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: ai_judgment_statistics
CREATE TABLE IF NOT EXISTS ai_judgment_statistics (  test_id integer,
  total_judgments bigint,
  correct_count bigint,
  incorrect_count bigint,
  partially_correct_count bigint,
  indeterminate_count bigint,
  correct_percentage numeric,
  exact_match_percentage numeric,
  semantic_match_percentage numeric,
  avg_ai_confidence numeric,
  avg_judgment_confidence numeric,
  high_confidence_judgments bigint,
  medium_confidence_judgments bigint,
  low_confidence_judgments bigint
);

-- Table: ai_judgments
CREATE TABLE IF NOT EXISTS ai_judgments (  id integer NOT NULL DEFAULT nextval('ai_judgments_id_seq'::regclass),
  test_id integer NOT NULL,
  transaction_id character varying NOT NULL,
  original_category character varying,
  ai_category character varying NOT NULL,
  ai_full_hierarchy character varying,
  ai_confidence numeric NOT NULL,
  judgment_result character varying NOT NULL,
  judgment_confidence numeric NOT NULL,
  judgment_reasoning text,
  category_exact_match boolean NOT NULL DEFAULT false,
  category_semantic_match boolean NOT NULL DEFAULT false,
  hierarchy_level_matches jsonb,
  transaction_description character varying NOT NULL,
  transaction_amount numeric NOT NULL,
  transaction_type character varying NOT NULL,
  judged_at timestamp with time zone NOT NULL DEFAULT now(),
  judge_model character varying NOT NULL DEFAULT 'gemini-2.0-flash-001'::character varying,
  judge_version character varying NOT NULL DEFAULT '1.0'::character varying,
  created_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Table: categories
CREATE TABLE IF NOT EXISTS categories (  id integer NOT NULL DEFAULT nextval('categories_id_seq'::regclass),
  name character varying NOT NULL,
  tenant_id integer,
  parent_id integer,
  description text,
  color character varying,
  icon character varying,
  is_active boolean DEFAULT true,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  gl_code character varying,
  gl_account_name character varying,
  gl_account_type character varying,
  level integer DEFAULT 0,
  learned_from_onboarding boolean DEFAULT true,
  frequency_score numeric,
  confidence_score numeric,
  path character varying
);

-- Table: category_schemas
CREATE TABLE IF NOT EXISTS category_schemas (  id integer NOT NULL DEFAULT nextval('category_schemas_id_seq'::regclass),
  tenant_id integer NOT NULL,
  name character varying NOT NULL,
  description text,
  schema_format character varying NOT NULL,
  schema_data jsonb NOT NULL,
  category_count integer NOT NULL DEFAULT 0,
  max_hierarchy_depth integer NOT NULL DEFAULT 1,
  has_gl_codes boolean NOT NULL DEFAULT false,
  gl_code_mapping jsonb,
  imported_from character varying NOT NULL,
  imported_at timestamp with time zone NOT NULL DEFAULT now(),
  imported_by character varying,
  is_active boolean NOT NULL DEFAULT true,
  last_used_at timestamp with time zone,
  usage_count integer NOT NULL DEFAULT 0,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Table: column_mappings
CREATE TABLE IF NOT EXISTS column_mappings (  id integer NOT NULL DEFAULT nextval('column_mappings_id_seq'::regclass),
  tenant_id integer NOT NULL,
  upload_id character varying NOT NULL,
  source_column character varying NOT NULL,
  target_column character varying NOT NULL,
  mapping_type character varying,
  confidence_score numeric,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: column_statistics
CREATE TABLE IF NOT EXISTS column_statistics (  id character varying NOT NULL,
  report_id character varying NOT NULL,
  column_name character varying NOT NULL,
  column_index integer NOT NULL,
  mapped_field character varying,
  total_values integer NOT NULL,
  non_null_values integer NOT NULL,
  null_values integer NOT NULL,
  unique_values integer NOT NULL,
  detected_type character varying,
  type_consistency double precision,
  min_value character varying,
  max_value character varying,
  average_value double precision,
  most_common_values jsonb,
  empty_string_count integer DEFAULT 0,
  invalid_format_count integer DEFAULT 0,
  outlier_count integer DEFAULT 0,
  mapping_confidence double precision,
  mapping_method character varying,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: custom_reports
CREATE TABLE IF NOT EXISTS custom_reports (  id integer NOT NULL DEFAULT nextval('custom_reports_id_seq'::regclass),
  tenant_id integer,
  name character varying NOT NULL,
  description text,
  query_config jsonb,
  chart_config jsonb,
  created_by integer,
  is_public boolean DEFAULT false,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: file_processing_reports
CREATE TABLE IF NOT EXISTS file_processing_reports (  id character varying NOT NULL,
  upload_id character varying,
  tenant_id integer,
  file_path character varying,
  status character varying DEFAULT 'pending'::character varying,
  total_rows integer,
  processed_rows integer,
  valid_rows integer,
  invalid_rows integer,
  processing_start_time timestamp without time zone,
  processing_end_time timestamp without time zone,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  error_message text,
  processing_completed_at timestamp with time zone,
  processing_duration_seconds numeric,
  file_name character varying,
  processing_started_at timestamp with time zone,
  total_columns integer,
  successful_rows integer,
  failed_rows integer,
  skipped_rows integer,
  file_size_bytes bigint,
  column_mapping_confidence jsonb,
  mapped_columns jsonb,
  unmapped_columns jsonb,
  data_quality_score numeric,
  validation_errors jsonb,
  warnings jsonb,
  categories_discovered jsonb,
  category_count integer DEFAULT 0,
  schema_confidence numeric,
  date_format_detected character varying
);

-- Table: file_schemas
CREATE TABLE IF NOT EXISTS file_schemas (  id integer NOT NULL DEFAULT nextval('file_schemas_id_seq'::regclass),
  upload_id character varying,
  schema_data jsonb,
  column_mappings jsonb,
  validation_errors jsonb,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: income_expense_summary
CREATE TABLE IF NOT EXISTS income_expense_summary (  tenant_id integer,
  month timestamp with time zone,
  income numeric,
  expenses numeric,
  transaction_count bigint
);

-- Table: onboarding_sessions
CREATE TABLE IF NOT EXISTS onboarding_sessions (  id integer NOT NULL DEFAULT nextval('onboarding_sessions_id_seq'::regclass),
  tenant_id integer,
  status character varying DEFAULT 'in_progress'::character varying,
  step integer DEFAULT 1,
  completed_at timestamp without time zone,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: onboarding_status
CREATE TABLE IF NOT EXISTS onboarding_status (  id integer NOT NULL DEFAULT nextval('onboarding_status_id_seq'::regclass),
  tenant_id integer,
  status character varying DEFAULT 'not_started'::character varying,
  files_uploaded integer DEFAULT 0,
  categories_discovered integer DEFAULT 0,
  temporal_validation_passed boolean DEFAULT false,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  stage character varying DEFAULT 'not_started'::character varying,
  approved_for_production boolean DEFAULT false,
  transactions_with_labels integer NOT NULL DEFAULT 0,
  date_range_start timestamp with time zone,
  date_range_end timestamp with time zone,
  last_validation_accuracy double precision,
  approved_by_user_id integer,
  approval_notes text,
  total_transactions integer NOT NULL DEFAULT 0,
  last_validation_id character varying,
  approved_at timestamp with time zone,
  last_activity timestamp with time zone NOT NULL DEFAULT now(),
  onboarding_type character varying NOT NULL DEFAULT 'historical_data'::character varying
);

-- Table: onboarding_uploads
CREATE TABLE IF NOT EXISTS onboarding_uploads (  id integer NOT NULL DEFAULT nextval('onboarding_uploads_id_seq'::regclass),
  session_id integer,
  upload_id character varying,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: rag_corpus
CREATE TABLE IF NOT EXISTS rag_corpus (  id integer NOT NULL DEFAULT nextval('rag_corpus_id_seq'::regclass),
  tenant_id integer,
  corpus_resource_name character varying,
  corpus_display_name character varying,
  total_patterns integer DEFAULT 0,
  unique_categories integer DEFAULT 0,
  gcs_uri character varying,
  is_active boolean DEFAULT true,
  last_rebuild_at timestamp without time zone,
  customer_category_schemas jsonb,
  unified_category_mapping jsonb,
  reverse_category_mapping jsonb,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  source_file_schemas jsonb,
  schema_discovery_session_id character varying
);

-- Table: row_processing_details
CREATE TABLE IF NOT EXISTS row_processing_details (  id character varying NOT NULL,
  report_id character varying NOT NULL,
  row_number integer NOT NULL,
  original_data jsonb,
  status character varying NOT NULL,
  parsed_date date,
  parsed_amount numeric,
  parsed_description text,
  parsed_category character varying,
  parsed_vendor character varying,
  validation_errors jsonb,
  data_quality_issues jsonb,
  date_parsing_attempted boolean DEFAULT false,
  date_parsing_format character varying,
  amount_parsing_attempted boolean DEFAULT false,
  category_mapping_confidence double precision,
  processing_time_ms integer DEFAULT 0,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: schema_interpretations
CREATE TABLE IF NOT EXISTS schema_interpretations (  id character varying NOT NULL,
  upload_id character varying NOT NULL,
  tenant_id integer NOT NULL,
  filename character varying NOT NULL,
  column_mappings jsonb NOT NULL,
  overall_confidence double precision NOT NULL,
  required_fields_mapped jsonb NOT NULL,
  interpretation_summary text,
  debit_credit_inference jsonb DEFAULT '{}'::jsonb,
  regional_variations jsonb DEFAULT '[]'::jsonb,
  created_at timestamp without time zone NOT NULL,
  updated_at timestamp without time zone NOT NULL
);

-- Table: temporal_validations
CREATE TABLE IF NOT EXISTS temporal_validations (  id uuid NOT NULL DEFAULT gen_random_uuid(),
  validation_id character varying NOT NULL,
  tenant_id integer,
  start_month character varying,
  end_month character varying,
  accuracy_threshold double precision DEFAULT 0.85,
  status character varying DEFAULT 'pending'::character varying,
  started_at timestamp without time zone,
  completed_at timestamp without time zone,
  average_accuracy double precision,
  meets_threshold boolean,
  monthly_results jsonb,
  error_message text,
  total_transactions_tested integer,
  total_training_transactions integer,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: tenant
CREATE TABLE IF NOT EXISTS tenant (  id integer NOT NULL DEFAULT nextval('tenant_id_seq'::regclass),
  name character varying NOT NULL,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  domain character varying,
  settings jsonb DEFAULT '{}'::jsonb,
  subscription_status character varying DEFAULT 'active'::character varying,
  subscription_plan character varying DEFAULT 'basic'::character varying,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

-- Table: tenants
CREATE TABLE IF NOT EXISTS tenants (  id integer NOT NULL DEFAULT nextval('tenants_id_seq'::regclass),
  name character varying NOT NULL,
  domain character varying,
  settings jsonb,
  subscription_status character varying DEFAULT 'active'::character varying,
  subscription_plan character varying DEFAULT 'free'::character varying,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  is_active boolean DEFAULT true
);

-- Table: transactions
CREATE TABLE IF NOT EXISTS transactions (  id character varying NOT NULL,
  tenant_id integer NOT NULL,
  date date NOT NULL,
  description text NOT NULL,
  amount numeric NOT NULL,
  category_id integer,
  original_category text,
  ai_suggested_category text,
  vendor_name text,
  account text,
  transaction_type text,
  upload_id character varying,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  ai_category character varying,
  ai_confidence numeric,
  notes text,
  tags ARRAY,
  is_reviewed boolean DEFAULT false,
  reviewed_at timestamp without time zone,
  reviewed_by integer,
  entity_id integer,
  entity_name character varying,
  original_category_label character varying
);

-- Table: upload
CREATE TABLE IF NOT EXISTS upload (  id character varying NOT NULL,
  filename character varying NOT NULL,
  tenant_id integer,
  status character varying DEFAULT 'pending'::character varying,
  uploaded_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  completed_at timestamp without time zone,
  error_message text,
  file_size bigint,
  row_count integer,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  size bigint
);

-- Table: uploads
CREATE TABLE IF NOT EXISTS uploads (  id character varying NOT NULL,
  filename character varying NOT NULL,
  tenant_id integer,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  content_type character varying,
  status character varying NOT NULL DEFAULT 'uploaded'::character varying,
  size bigint,
  transaction_count integer,
  file_path character varying,
  upload_type character varying,
  processing_status character varying NOT NULL DEFAULT 'pending'::character varying,
  column_mapping jsonb,
  headers jsonb,
  file_size integer,
  file_type character varying,
  processed_count integer DEFAULT 0,
  error_count integer DEFAULT 0,
  error_message text,
  has_category_labels boolean,
  baseline_accuracy numeric,
  processed_transactions_count integer DEFAULT 0,
  upload_status character varying DEFAULT 'pending'::character varying,
  row_count integer DEFAULT 0,
  user_id integer,
  metadata jsonb
);

-- Table: users
CREATE TABLE IF NOT EXISTS users (  id integer NOT NULL DEFAULT nextval('users_id_seq'::regclass),
  email character varying NOT NULL,
  tenant_id integer,
  is_admin boolean DEFAULT false,
  created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  hashed_password character varying NOT NULL DEFAULT ''::character varying,
  is_active boolean DEFAULT true,
  is_verified boolean DEFAULT false,
  is_superuser boolean DEFAULT false,
  updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  last_login timestamp without time zone,
  login_count integer DEFAULT 0,
  username character varying
);

