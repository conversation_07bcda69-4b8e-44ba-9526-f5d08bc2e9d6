-- Add missing columns to tenant table

-- Add client_type column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tenant' AND column_name = 'client_type') THEN
        ALTER TABLE tenant ADD COLUMN client_type VARCHAR(100);
        RAISE NOTICE 'Added client_type column';
    ELSE
        RAISE NOTICE 'client_type column already exists';
    END IF;
END $$;

-- Add timezone column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tenant' AND column_name = 'timezone') THEN
        ALTER TABLE tenant ADD COLUMN timezone VARCHAR(50) DEFAULT 'UTC';
        RAISE NOTICE 'Added timezone column';
    ELSE
        RAISE NOTICE 'timezone column already exists';
    END IF;
END $$;

-- Add date_format column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tenant' AND column_name = 'date_format') THEN
        ALTER TABLE tenant ADD COLUMN date_format VARCHAR(20) DEFAULT 'YYYY-MM-DD';
        RAISE NOTICE 'Added date_format column';
    ELSE
        RAISE NOTICE 'date_format column already exists';
    END IF;
END $$;

-- Add currency column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tenant' AND column_name = 'currency') THEN
        ALTER TABLE tenant ADD COLUMN currency VARCHAR(10) DEFAULT 'USD';
        RAISE NOTICE 'Added currency column';
    ELSE
        RAISE NOTICE 'currency column already exists';
    END IF;
END $$;

-- Show final schema
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'tenant'
ORDER BY ordinal_position;
