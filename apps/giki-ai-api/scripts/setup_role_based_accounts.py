"""
Setup role-based test accounts for MIS platform.

This script creates role-based test accounts (owner, accountant, bookkeeper, viewer)
to replace the old milestone-based accounts (<EMAIL>, <EMAIL>, <EMAIL>).
"""

import asyncio
import os
import sys
from datetime import datetime, timezone
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from src.giki_ai_api.domains.auth.models import User
from src.giki_ai_api.domains.auth.secure_auth import get_password_hash


async def setup_role_based_accounts():
    """Create role-based test accounts for MIS platform."""
    # Get database URL from environment
    database_url = os.environ.get("DATABASE_URL")
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set")
        sys.exit(1)

    # Create engine
    engine = create_async_engine(database_url, echo=True)

    # Define role-based test accounts
    role_based_accounts = [
        {
            "email": "<EMAIL>",
            "password": "GikiTest2025Secure",
            "is_admin": False,
            "tenant_id": 1,
            "role": "owner",
            "description": "Business owner - Full MIS management and configuration"
        },
        {
            "email": "<EMAIL>",
            "password": "GikiTest2025Secure",
            "is_admin": False,
            "tenant_id": 1,
            "role": "accountant",
            "description": "Accountant - Category management and review"
        },
        {
            "email": "<EMAIL>",
            "password": "GikiTest2025Secure",
            "is_admin": False,
            "tenant_id": 1,
            "role": "bookkeeper",
            "description": "Bookkeeper - Transaction entry and categorization"
        },
        {
            "email": "<EMAIL>",
            "password": "GikiTest2025Secure",
            "is_admin": False,
            "tenant_id": 1,
            "role": "viewer",
            "description": "Viewer - Read-only MIS reports and dashboards"
        },
        {
            "email": "<EMAIL>",
            "password": "GikiTest2025Secure",
            "is_admin": True,
            "tenant_id": 1,
            "role": "admin",
            "description": "System administrator - Full system access"
        }
    ]

    async with AsyncSession(engine) as session:
        # First, ensure tenant exists
        tenant_check = await session.execute(
            text("SELECT id FROM tenants WHERE id = 1")
        )
        if not tenant_check.scalar():
            print("Creating test tenant...")
            await session.execute(
                text("""
                    INSERT INTO tenants (id, name, company_name, company_website, created_at, updated_at)
                    VALUES (1, 'Test Company', 'Test Company Inc.', 'https://testcompany.com', :now, :now)
                """),
                {"now": datetime.now(timezone.utc)}
            )
            await session.commit()

        # Create role-based accounts
        for account in role_based_accounts:
            # Check if user exists
            result = await session.execute(
                select(User).where(User.email == account["email"])
            )
            existing_user = result.scalar_one_or_none()

            if existing_user:
                print(f"✓ User already exists: {account['email']} - {account['description']}")
            else:
                # Create new user
                print(f"+ Creating user: {account['email']} - {account['description']}")
                new_user = User(
                    email=account["email"],
                    hashed_password=get_password_hash(account["password"]),
                    is_active=True,
                    is_admin=account["is_admin"],
                    tenant_id=account["tenant_id"],
                )
                session.add(new_user)

        await session.commit()
        print("\n✅ Role-based accounts setup completed!")

        # List all users
        print("\n📋 Current users in database:")
        result = await session.execute(
            select(User).order_by(User.email)
        )
        all_users = result.scalars().all()
        
        for user in all_users:
            role = "admin" if user.is_admin else "user"
            status = "active" if user.is_active else "inactive"
            print(f"  - {user.email:<30} (role: {role:<6}, status: {status})")

    await engine.dispose()


async def migrate_old_accounts():
    """Optional: Migrate data from old milestone-based accounts to new role-based accounts."""
    database_url = os.environ.get("DATABASE_URL")
    if not database_url:
        return

    engine = create_async_engine(database_url, echo=False)
    
    # Mapping of old accounts to new accounts
    account_mapping = {
        "<EMAIL>": "<EMAIL>",
        "<EMAIL>": "<EMAIL>",
        "<EMAIL>": "<EMAIL>",
        "<EMAIL>": "<EMAIL>"
    }

    async with AsyncSession(engine) as session:
        print("\n🔄 Checking for data migration needs...")
        
        for old_email, _new_email in account_mapping.items():
            # Check if old account exists and has data
            old_user = await session.execute(
                select(User).where(User.email == old_email)
            )
            old_user = old_user.scalar_one_or_none()
            
            if old_user:
                print(f"  Found old account: {old_email}")
                # Here you could add logic to migrate user-specific data
                # For now, we'll just note that the old account exists
                
    await engine.dispose()


if __name__ == "__main__":
    print("Role-Based Test Accounts Setup Script")
    print("=" * 50)
    print("This script will create role-based test accounts for the MIS platform.")
    print("\nAccounts to be created:")
    print("  - <EMAIL>      (Business owner)")
    print("  - <EMAIL> (Accountant)")
    print("  - <EMAIL> (Bookkeeper)")
    print("  - <EMAIL>     (Viewer)")
    print("  - <EMAIL>      (Administrator)")
    print()
    
    # Check if running in production
    if "PRODUCTION" in os.environ.get("DATABASE_URL", ""):
        print("⚠️  WARNING: You are about to modify the PRODUCTION database!")
        confirm = input("Are you sure you want to continue? (yes/no): ")
        if confirm.lower() != "yes":
            print("Aborted.")
            sys.exit(0)
    
    asyncio.run(setup_role_based_accounts())
    asyncio.run(migrate_old_accounts())