#!/usr/bin/env python3
"""Fix JWT algorithm configuration issue."""

import os
import sys

# Ensure RS256 is used
os.environ['ALGORITHM'] = 'RS256'
os.environ['AUTH_ALGORITHM'] = 'RS256'

# Import after setting env vars
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from giki_ai_api.core.config import settings

print(f"Current ALGORITHM: {settings.ALGORITHM}")
print(f"Current AUTH_ALGORITHM: {settings.AUTH_ALGORITHM}")

# Force update
settings.ALGORITHM = "RS256"
settings.AUTH_ALGORITHM = "RS256"

print(f"Updated ALGORITHM: {settings.ALGORITHM}")
print(f"Updated AUTH_ALGORITHM: {settings.AUTH_ALGORITHM}")