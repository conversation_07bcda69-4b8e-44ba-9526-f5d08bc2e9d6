#!/usr/bin/env python3
"""
Clean up authentication - <NAME_EMAIL> exists
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from sqlalchemy import text

from giki_ai_api.core.database import get_database_config
from giki_ai_api.domains.auth.auth import get_password_hash

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def cleanup_auth():
    """Clean up auth - only <EMAIL> should exist"""
    logger.info("Starting auth cleanup...")

    try:
        # Get database configuration
        AsyncSessionLocal = get_database_config()

        async with AsyncSessionLocal() as db:
            logger.info("Connected to database successfully")

            # Delete all <NAME_EMAIL>
            result = await db.execute(
                text("DELETE FROM dev_user WHERE email != :email"),
                {"email": "<EMAIL>"},
            )
            deleted_count = result.rowcount
            logger.info(f"Deleted {deleted_count} users (<NAME_EMAIL>)")

            # Hash password for nikhil (from environment variable for security)
            dev_password = os.getenv("DEV_USER_PASSWORD", "secure_default_change_me")
            if dev_password == "secure_default_change_me":
                logger.warning("DEV_USER_PASSWORD not set - using secure default")
            hashed_password = get_password_hash(dev_password)

            # Ensure <EMAIL> exists with correct password
            await db.execute(
                text("""
                    INSERT INTO dev_user (email, hashed_password, full_name, is_active, tenant_id, created_at, updated_at)
                    VALUES (:email, :password, :name, true, 1, NOW(), NOW())
                    ON CONFLICT (email) DO UPDATE SET
                        hashed_password = EXCLUDED.hashed_password,
                        is_active = true,
                        updated_at = NOW()
                """),
                {
                    "email": "<EMAIL>",
                    "password": hashed_password,
                    "name": "Nikhil Singh",
                },
            )

            await db.commit()
            logger.info("Updated <EMAIL> with correct password")

            # Verify final state
            result = await db.execute(
                text("SELECT email, full_name, is_active FROM dev_user ORDER BY email")
            )
            users = result.fetchall()

            logger.info("Final users in database:")
            for user in users:
                logger.info(f"  - {user[0]} ({user[1]}) - Active: {user[2]}")

            if len(users) == 1 and users[0][0] == "<EMAIL>":
                logger.info("✅ Auth cleanup successful - only <EMAIL> exists")
            else:
                logger.error(f"❌ Auth cleanup failed - found {len(users)} users")

    except Exception as e:
        logger.error(f"Error during auth cleanup: {e}")
        raise


async def main():
    await cleanup_auth()


if __name__ == "__main__":
    asyncio.run(main())
