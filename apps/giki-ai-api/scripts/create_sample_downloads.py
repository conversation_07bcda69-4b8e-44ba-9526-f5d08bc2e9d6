#!/usr/bin/env python
"""
Create Sample Download Files for Customer Guidance - giki.ai
===========================================================

Creates small sample files that customers can download to understand
the expected format for uploading their own transaction data.

Usage:
    uv run python scripts/create_sample_downloads.py
"""

from datetime import datetime
from pathlib import Path

import pandas as pd


def create_sample_files():
    """Create small sample files for customer downloads."""
    
    # Create sample downloads directory
    samples_dir = Path("apps/giki-ai-app/public/samples")
    samples_dir.mkdir(parents=True, exist_ok=True)
    
    print("📄 Creating Sample Download Files")
    print("=" * 35)
    
    # Basic transaction format sample
    basic_data = [
        ["2024-01-15", "Office supplies from Staples", "Staples", 156.73, "Office Supplies"],
        ["2024-01-16", "Monthly software subscription", "Adobe Creative Cloud", 52.99, "Software"],
        ["2024-01-17", "Client lunch meeting", "The Local Bistro", 87.45, "Meals & Entertainment"],
        ["2024-01-18", "Gas for company vehicle", "Shell Station", 65.20, "Vehicle Expenses"],
        ["2024-01-19", "Legal consultation", "Smith & Associates Law", 450.00, "Professional Services"],
        ["2024-01-20", "Marketing materials", "Print Shop Pro", 234.15, "Marketing"],
        ["2024-01-21", "Internet service", "Comcast Business", 129.99, "Utilities"],
        ["2024-01-22", "Equipment maintenance", "TechFix Solutions", 320.00, "Maintenance"],
        ["2024-01-23", "Travel expenses", "United Airlines", 542.80, "Travel"],
        ["2024-01-24", "Office rent", "Property Management Co", 2500.00, "Rent"],
    ]
    
    basic_df = pd.DataFrame(basic_data, columns=["Date", "Description", "Vendor", "Amount", "Category"])
    
    # Save basic sample
    basic_file = samples_dir / "sample_transactions_basic.xlsx"
    basic_df.to_excel(basic_file, index=False)
    print(f"   ✅ Basic Format: {basic_file}")
    
    # Bank statement format sample
    bank_data = [
        ["2024-01-15", "STAPLES #123 PURCHASE", 156.73, "", 9843.27],
        ["2024-01-16", "ADOBE CREATIVE CLOUD", 52.99, "", 9790.28],
        ["2024-01-17", "THE LOCAL BISTRO", 87.45, "", 9702.83],
        ["2024-01-18", "SHELL #456", 65.20, "", 9637.63],
        ["2024-01-19", "SMITH & ASSOCIATES LAW", 450.00, "", 9187.63],
        ["2024-01-20", "ACH DEPOSIT CUSTOMER PAYMENT", "", 1250.00, 10437.63],
        ["2024-01-21", "COMCAST BUSINESS", 129.99, "", 10307.64],
        ["2024-01-22", "TECHFIX SOLUTIONS", 320.00, "", 9987.64],
        ["2024-01-23", "UNITED AIRLINES", 542.80, "", 9444.84],
        ["2024-01-24", "PROPERTY MGMT CO RENT", 2500.00, "", 6944.84],
    ]
    
    bank_df = pd.DataFrame(bank_data, columns=["Date", "Description", "Debit", "Credit", "Balance"])
    
    # Save bank statement sample
    bank_file = samples_dir / "sample_bank_statement.xlsx"
    bank_df.to_excel(bank_file, index=False)
    print(f"   ✅ Bank Statement: {bank_file}")
    
    # GL Code format sample (for M3 Giki customers)
    gl_data = [
        ["2024-01-15", "Office supplies from Staples", "Staples", 156.73, "Office Supplies", "6100"],
        ["2024-01-16", "Monthly software subscription", "Adobe Creative Cloud", 52.99, "Software", "6200"],
        ["2024-01-17", "Client lunch meeting", "The Local Bistro", 87.45, "Meals & Entertainment", "6300"],
        ["2024-01-18", "Gas for company vehicle", "Shell Station", 65.20, "Vehicle Expenses", "6400"],
        ["2024-01-19", "Legal consultation", "Smith & Associates Law", 450.00, "Professional Services", "6500"],
        ["2024-01-20", "Product sales", "Customer ABC Corp", "", 1250.00, "Product Revenue", "4100"],
        ["2024-01-21", "Internet service", "Comcast Business", 129.99, "Utilities", "6600"],
        ["2024-01-22", "Equipment maintenance", "TechFix Solutions", 320.00, "Maintenance", "6700"],
        ["2024-01-23", "Travel expenses", "United Airlines", 542.80, "Travel", "6800"],
        ["2024-01-24", "Office rent", "Property Management Co", 2500.00, "Rent", "6900"],
    ]
    
    gl_df = pd.DataFrame(gl_data, columns=["Date", "Description", "Vendor", "Debit", "Credit", "Category", "GL Code"])
    
    # Save GL code sample
    gl_file = samples_dir / "sample_transactions_with_gl_codes.xlsx"
    gl_df.to_excel(gl_file, index=False)
    print(f"   ✅ GL Code Format: {gl_file}")
    
    # Create CSV versions for broader compatibility
    basic_csv = samples_dir / "sample_transactions_basic.csv"
    basic_df.to_csv(basic_csv, index=False)
    print(f"   ✅ Basic CSV: {basic_csv}")
    
    bank_csv = samples_dir / "sample_bank_statement.csv"
    bank_df.to_csv(bank_csv, index=False)
    print(f"   ✅ Bank CSV: {bank_csv}")
    
    gl_csv = samples_dir / "sample_transactions_with_gl_codes.csv"
    gl_df.to_csv(gl_csv, index=False)
    print(f"   ✅ GL Code CSV: {gl_csv}")
    
    # Create format guide
    guide_content = """# Transaction File Format Guide

## Supported Formats
- Excel (.xlsx)
- CSV (.csv)

## Required Columns

### Basic Format
- **Date**: Transaction date (YYYY-MM-DD or MM/DD/YYYY)
- **Description**: Transaction description
- **Amount**: Transaction amount (positive numbers)
- **Vendor** (optional): Vendor/payee name
- **Category** (optional): Expense category

### Bank Statement Format  
- **Date**: Transaction date
- **Description**: Transaction description
- **Debit**: Expense amounts (positive numbers)
- **Credit**: Income amounts (positive numbers)
- **Balance** (optional): Running balance

### GL Code Format (Enterprise)
- **Date**: Transaction date
- **Description**: Transaction description
- **Vendor**: Vendor/payee name
- **Debit**: Expense amounts
- **Credit**: Income amounts
- **Category**: Account category
- **GL Code**: General ledger account code

## Tips for Best Results
1. Include vendor names when available
2. Use descriptive transaction descriptions
3. Keep date formats consistent
4. For uncategorized data, leave Category column empty
5. AI will automatically categorize based on description and vendor

## File Size Limits
- Maximum: 10MB per file
- Recommended: Under 5,000 transactions per upload

Generated: {timestamp}
"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    guide_file = samples_dir / "FORMAT_GUIDE.md"
    guide_file.write_text(guide_content.format(timestamp=timestamp))
    print(f"   ✅ Format Guide: {guide_file}")
    
    print("\n🎉 Sample files created successfully!")
    print(f"   📁 Available at: {samples_dir}")
    print("   🌐 Web accessible at: /samples/")
    
    # Show file sizes
    print("\n📊 File Summary:")
    for file_path in samples_dir.glob("sample_*"):
        if file_path.suffix in ['.xlsx', '.csv']:
            size_kb = file_path.stat().st_size / 1024
            print(f"   {file_path.name}: {size_kb:.1f} KB")

if __name__ == "__main__":
    create_sample_files()