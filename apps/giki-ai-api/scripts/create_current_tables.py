#!/usr/bin/env python3
"""
<PERSON><PERSON>t to create missing database tables using current domain models.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))


async def create_missing_tables():
    """Create the missing database tables using current models."""
    try:
        # Import database configuration and engine
        from giki_ai_api.core.database import engine, get_database_config

        # Import all models to register them with Base
        from giki_ai_api.shared.models import Base, fix_model_table_names

        print("[INFO] Creating missing database tables...")

        # Initialize database configuration to get the session factory
        session_factory = get_database_config()
        print(f"[DEBUG] Session factory: {session_factory}")
        print(f"[DEBUG] Global engine: {engine}")

        # Get the engine by creating a temporary session
        async with session_factory() as temp_session:
            db_engine = temp_session.bind
            print(f"[DEBUG] Engine from session: {db_engine}")

        if db_engine is None:
            raise RuntimeError("Database engine not initialized")

        # Fix table names before creating tables
        print("[INFO] Fixing table names with environment prefix...")
        fix_model_table_names()

        # Create all tables defined in the models
        async with db_engine.begin() as conn:
            print("[INFO] Creating tables from Base.metadata...")
            await conn.run_sync(
                lambda sync_conn: Base.metadata.create_all(sync_conn, checkfirst=True)
            )
            print("[SUCCESS] Successfully created all missing tables!")

        print("[INFO] Tables created:")
        for table_name in Base.metadata.tables.keys():
            print(f"  - {table_name}")

    except Exception as e:
        print(f"[ERROR] Error creating tables: {e}")
        import traceback

        traceback.print_exc()
        raise


if __name__ == "__main__":
    print("[INFO] Starting database table creation with current models...")
    asyncio.run(create_missing_tables())
    print("[SUCCESS] Database table creation completed!")
