#!/usr/bin/env python3
"""
Verify authentication performance after hash fix.
"""

import asyncio
import time

import httpx


async def test_auth_performance():
    """Test authentication performance with the updated hash."""
    
    base_url = "http://localhost:8000"
    email = "<EMAIL>"
    password = "GikiTest2024"
    
    print(f"🔑 Testing authentication performance for {email}")
    print("-" * 50)
    
    async with httpx.AsyncClient() as client:
        # Warm up
        print("Warming up...")
        for _ in range(3):
            try:
                response = await client.post(
                    f"{base_url}/api/v1/auth/login",
                    data={
                        "username": email,
                        "password": password,
                        "grant_type": "password"
                    },
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                    timeout=10.0
                )
            except Exception as e:
                print(f"⚠️  Warmup failed: {e}")
        
        # Performance test
        print("\nRunning performance test (10 authentications)...")
        times = []
        
        for i in range(10):
            start = time.time()
            try:
                response = await client.post(
                    f"{base_url}/api/v1/auth/login",
                    data={
                        "username": email,
                        "password": password,
                        "grant_type": "password"
                    },
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                    timeout=10.0
                )
                
                elapsed = time.time() - start
                times.append(elapsed)
                
                if response.status_code == 200:
                    print(f"  ✅ Auth #{i+1}: {elapsed*1000:.1f}ms")
                else:
                    print(f"  ❌ Auth #{i+1}: {response.status_code} - {elapsed*1000:.1f}ms")
                    
            except Exception as e:
                print(f"  ❌ Auth #{i+1}: Error - {e}")
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            print("\n📊 Performance Results:")
            print(f"  Average: {avg_time*1000:.1f}ms")
            print(f"  Minimum: {min_time*1000:.1f}ms")
            print(f"  Maximum: {max_time*1000:.1f}ms")
            
            if avg_time < 0.2:  # 200ms target
                print("  ✅ Performance target met (<200ms)")
            else:
                print("  ⚠️  Performance below target (>200ms)")
        else:
            print("\n❌ No successful authentications")


if __name__ == "__main__":
    print("Note: Make sure the API server is running (pnpm nx serve:api)")
    asyncio.run(test_auth_performance())