#!/usr/bin/env python3
"""Test JWT configuration to debug RS256 setup."""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from giki_ai_api.core.config import settings
from giki_ai_api.core.jwt_keys import get_private_key, get_public_key
from jose import jwt

print(f"Algorithm: {settings.ALGORITHM}")
print(f"SECRET_KEY exists: {bool(settings.SECRET_KEY)}")
print(f"AUTH_ALGORITHM: {settings.AUTH_ALGORITHM}")

# Test key generation
try:
    private_key = get_private_key()
    public_key = get_public_key()
    print(f"Private key loaded: {len(private_key)} bytes")
    print(f"Public key loaded: {len(public_key)} bytes")
    
    # Test encoding
    test_payload = {"sub": "1:1", "exp": 9999999999}
    token = jwt.encode(test_payload, private_key, algorithm="RS256")
    print(f"Token created: {token[:50]}...")
    
    # Test decoding
    decoded = jwt.decode(token, public_key, algorithms=["RS256"])
    print(f"Token decoded successfully: {decoded}")
    
except Exception as e:
    print(f"Error: {type(e).__name__}: {e}")