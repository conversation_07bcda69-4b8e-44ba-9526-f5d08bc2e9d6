#!/usr/bin/env python3
"""
Database Migration: Add missing transaction table columns for categorization system
=================================================================================

This script adds the missing columns to the transactions table that are defined
in the Transaction model but don't exist in the actual database. These columns
are essential for the AI categorization system to work properly.

Missing columns:
- original_category_label: Customer's original category label for temporal validation
- original_category: Original category from historical data (onboarding only)
- ai_suggested_category: AI suggested category name (string)
- user_corrected_category: User's corrected category for continuous learning
- needs_review: Low confidence categorization needs review
- corrected_at: When user corrected the category
- vendor_name: Vendor or merchant name extracted from description
- vendor: Alias for vendor_name

Usage:
    python scripts/add_transaction_columns_migration.py
"""

import asyncio
import os
import sys
from pathlib import Path

import asyncpg
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

# Load environment variables
env = os.getenv("ENVIRONMENT", "development")
env_file = project_root / f".env.{env}"
if env_file.exists():
    load_dotenv(env_file)
    print(f"[INFO] Loaded environment from {env_file}")
else:
    load_dotenv()
    print("[INFO] Loaded default environment")


async def add_transaction_columns():
    """Add the missing columns to the transactions table."""

    # Get database connection details from environment
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("[ERROR] DATABASE_URL environment variable not set")
        return False

    # Convert SQLAlchemy URL format to asyncpg format
    if database_url.startswith("postgresql+asyncpg://"):
        database_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
    elif database_url.startswith("postgres+asyncpg://"):
        database_url = database_url.replace("postgres+asyncpg://", "postgresql://")

    try:
        # Connect to the database
        conn = await asyncpg.connect(database_url)
        print("[SUCCESS] Connected to database")

        # Check which columns already exist
        existing_columns = await conn.fetch(
            """
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'transactions' AND table_schema = 'public'
            """
        )

        existing_column_names = {col["column_name"] for col in existing_columns}
        print(
            f"[INFO] Found {len(existing_column_names)} existing columns in transactions table"
        )

        # Define the columns we need to add
        columns_to_add = [
            {
                "name": "original_category_label",
                "definition": "VARCHAR(255) NULL",
                "comment": "Customer's original category label for temporal validation",
            },
            {
                "name": "original_category",
                "definition": "VARCHAR(255) NULL",
                "comment": "Original category from historical data (onboarding only)",
            },
            {
                "name": "ai_suggested_category",
                "definition": "VARCHAR(255) NULL",
                "comment": "AI suggested category name (string)",
            },
            {
                "name": "user_corrected_category",
                "definition": "VARCHAR(255) NULL",
                "comment": "User's corrected category for continuous learning",
            },
            {
                "name": "needs_review",
                "definition": "BOOLEAN NOT NULL DEFAULT FALSE",
                "comment": "Low confidence categorization needs review",
            },
            {
                "name": "corrected_at",
                "definition": "TIMESTAMP WITH TIME ZONE NULL",
                "comment": "When user corrected the category",
            },
            {
                "name": "vendor_name",
                "definition": "VARCHAR(255) NULL",
                "comment": "Vendor or merchant name extracted from description",
            },
            {
                "name": "vendor",
                "definition": "VARCHAR(255) NULL",
                "comment": "Alias for vendor_name",
            },
        ]

        # Add each missing column
        columns_added = 0
        for column in columns_to_add:
            if column["name"] not in existing_column_names:
                try:
                    await conn.execute(f"""
                        ALTER TABLE transactions 
                        ADD COLUMN {column["name"]} {column["definition"]}
                    """)

                    # Add comment if specified
                    if column["comment"]:
                        await conn.execute(f"""
                            COMMENT ON COLUMN transactions.{column["name"]} IS '{column["comment"]}'
                        """)

                    print(f"[SUCCESS] Added column: {column['name']}")
                    columns_added += 1
                except Exception as e:
                    print(f"[ERROR] Failed to add column {column['name']}: {e}")
                    return False
            else:
                print(f"[SKIP] Column {column['name']} already exists")

        # Create indexes for performance
        indexes_to_create = [
            {
                "name": "idx_transactions_vendor_name",
                "definition": "CREATE INDEX IF NOT EXISTS idx_transactions_vendor_name ON transactions(vendor_name)",
                "description": "Index on vendor_name for merchant analysis",
            },
            {
                "name": "idx_transactions_vendor",
                "definition": "CREATE INDEX IF NOT EXISTS idx_transactions_vendor ON transactions(vendor)",
                "description": "Index on vendor for merchant analysis",
            },
            {
                "name": "idx_transactions_needs_review",
                "definition": "CREATE INDEX IF NOT EXISTS idx_transactions_needs_review ON transactions(needs_review)",
                "description": "Index on needs_review for finding transactions requiring attention",
            },
            {
                "name": "idx_transactions_corrected_at",
                "definition": "CREATE INDEX IF NOT EXISTS idx_transactions_corrected_at ON transactions(corrected_at)",
                "description": "Index on corrected_at for tracking user corrections",
            },
        ]

        print("\n[INFO] Creating performance indexes...")
        for index in indexes_to_create:
            try:
                await conn.execute(index["definition"])
                print(f"[SUCCESS] Created index: {index['name']}")
            except Exception as e:
                print(f"[WARNING] Failed to create index {index['name']}: {e}")

        # Verify the migration was successful
        updated_columns = await conn.fetch(
            """
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'transactions' AND table_schema = 'public'
            ORDER BY ordinal_position
            """
        )

        print(f"\n[SUCCESS] Migration completed! Added {columns_added} new columns")
        print(f"[INFO] Transactions table now has {len(updated_columns)} columns")

        # Show the newly added columns
        new_column_names = {col["name"] for col in columns_to_add}
        print("\n[INFO] Newly added columns:")
        for col in updated_columns:
            if col["column_name"] in new_column_names:
                nullable = "NULL" if col["is_nullable"] == "YES" else "NOT NULL"
                default = (
                    f" DEFAULT {col['column_default']}" if col["column_default"] else ""
                )
                print(
                    f"  - {col['column_name']}: {col['data_type']} {nullable}{default}"
                )

        return True

    except Exception as e:
        print(f"[ERROR] Error adding transaction columns: {e}")
        import traceback

        print(f"[DEBUG] Traceback: {traceback.format_exc()}")
        return False
    finally:
        if "conn" in locals():
            await conn.close()
            print("[SUCCESS] Database connection closed")


async def verify_migration():
    """Verify that the migration was successful by testing a simple query."""

    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("[ERROR] DATABASE_URL environment variable not set")
        return False

    # Convert SQLAlchemy URL format to asyncpg format
    if database_url.startswith("postgresql+asyncpg://"):
        database_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
    elif database_url.startswith("postgres+asyncpg://"):
        database_url = database_url.replace("postgres+asyncpg://", "postgresql://")

    try:
        conn = await asyncpg.connect(database_url)

        # Test a query that would have failed before the migration
        result = await conn.fetchrow("""
            SELECT id, original_category, ai_suggested_category, vendor_name, needs_review
            FROM transactions 
            LIMIT 1
        """)

        print("[SUCCESS] Verification query executed successfully")
        print("[SUCCESS] Database schema is now compatible with Transaction model")

        # Count total transactions
        count_result = await conn.fetchrow("SELECT COUNT(*) as count FROM transactions")
        print(f"[INFO] Total transactions in database: {count_result['count']}")

        return True

    except Exception as e:
        print(f"[ERROR] Verification failed: {e}")
        return False
    finally:
        if "conn" in locals():
            await conn.close()


async def main():
    """Main function to run the migration."""
    print("[INFO] Starting Transaction Columns Migration")
    print("=" * 60)
    print("[INFO] This will add missing columns required for AI categorization")
    print("")

    # Add the missing columns
    success = await add_transaction_columns()

    if success:
        print("\n" + "=" * 60)
        print("[INFO] Verifying migration...")
        verification_success = await verify_migration()

        if verification_success:
            print("\n" + "=" * 60)
            print("[SUCCESS] Transaction Columns Migration Complete!")
            print("\nColumns Added:")
            print("  - original_category_label: For customer historical data")
            print("  - original_category: For onboarding categorization")
            print("  - ai_suggested_category: For AI category suggestions")
            print("  - user_corrected_category: For continuous learning")
            print("  - needs_review: For low confidence flagging")
            print("  - corrected_at: For tracking user corrections")
            print("  - vendor_name: For merchant/vendor identification")
            print("  - vendor: For merchant/vendor alias")
            print("\nNext Steps:")
            print("  1. Restart the API server to pick up schema changes")
            print("  2. Test AI categorization functionality")
            print("  3. Verify data pipeline completion")
        else:
            print("[ERROR] Migration completed but verification failed")
            sys.exit(1)
    else:
        print("\n[ERROR] Migration failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
