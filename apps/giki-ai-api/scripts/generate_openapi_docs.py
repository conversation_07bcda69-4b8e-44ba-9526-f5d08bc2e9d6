#!/usr/bin/env python3
"""
Generate OpenAPI documentation for giki.ai API.

This script generates comprehensive OpenAPI documentation including:
- Complete API schema
- Endpoint documentation
- Model schemas
- Authentication details
"""

import json
import os
import sys
from pathlib import Path

# Add src to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))

try:
    from giki_ai_api.core.main import app
    
    def generate_openapi_schema():
        """Generate the OpenAPI schema."""
        print("Generating OpenAPI schema...")
        
        # Get the OpenAPI schema
        openapi_schema = app.openapi()
        
        # Add additional metadata
        openapi_schema["info"]["description"] = """
giki.ai API - AI-Powered Financial Transaction Management Platform

## Overview
giki.ai provides intelligent financial data processing with automated categorization,
pattern recognition, and comprehensive reporting capabilities.

## Key Features
- **AI-Powered Categorization**: 85%+ accuracy with confidence scoring
- **Intelligent File Processing**: Support for multiple formats (CSV, Excel, PDF)
- **Real-time Processing**: Batch processing with progress tracking
- **Conversational Agent**: Natural language interface for data operations
- **Comprehensive Reporting**: Export to multiple formats (Excel, CSV, QBO, IIF)
- **Tenant Isolation**: Multi-tenant architecture with secure data isolation

## Authentication
This API uses JWT (RS256) bearer token authentication. Include the token in the 
Authorization header: `Authorization: Bearer <token>`

## Rate Limiting
API requests are rate-limited to prevent abuse. Check response headers for rate limit status.

## Error Handling
The API returns structured error responses with detailed error codes and messages.
All errors follow RFC 7807 Problem Details for HTTP APIs standard.
        """
        
        # Enhance server information
        openapi_schema["servers"] = [
            {
                "url": "http://localhost:8000",
                "description": "Development server"
            },
            {
                "url": "https://api.giki.ai",
                "description": "Production server"
            }
        ]
        
        # Add security schemes
        openapi_schema["components"]["securitySchemes"] = {
            "JWTBearer": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
                "description": "JWT token obtained from /auth/token endpoint"
            }
        }
        
        # Add global security requirement
        openapi_schema["security"] = [{"JWTBearer": []}]
        
        return openapi_schema
    
    def save_schema_files(schema):
        """Save schema to different formats."""
        output_dir = current_dir.parent / "docs" / "api"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save as JSON
        json_file = output_dir / "openapi.json"
        with open(json_file, "w") as f:
            json.dump(schema, f, indent=2)
        print(f"✓ JSON schema saved to: {json_file}")
        
        # Save as YAML
        try:
            import yaml
            yaml_file = output_dir / "openapi.yaml"
            with open(yaml_file, "w") as f:
                yaml.dump(schema, f, default_flow_style=False, sort_keys=False)
            print(f"✓ YAML schema saved to: {yaml_file}")
        except ImportError:
            print("⚠ YAML output skipped (pyyaml not installed)")
        
        return json_file, output_dir
    
    def generate_documentation_summary(schema):
        """Generate a summary of the API documentation."""
        total_endpoints = len(schema.get("paths", {}))
        
        # Count endpoints by tag/category
        endpoint_categories = {}
        for path, methods in schema.get("paths", {}).items():
            for method, details in methods.items():
                if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                    tags = details.get("tags", ["Untagged"])
                    for tag in tags:
                        endpoint_categories[tag] = endpoint_categories.get(tag, 0) + 1
        
        # Count models
        total_models = len(schema.get("components", {}).get("schemas", {}))
        
        summary = f"""
# giki.ai API Documentation Summary

## Statistics
- **Total Endpoints**: {total_endpoints}
- **Total Models**: {total_models}
- **API Version**: {schema['info']['version']}

## Endpoint Categories
"""
        for category, count in sorted(endpoint_categories.items()):
            summary += f"- **{category}**: {count} endpoints\n"
        
        summary += f"""
## Authentication
- **Type**: JWT Bearer Token (RS256)
- **Security**: All endpoints require authentication except public registration

## Key Endpoints
- **Authentication**: `/api/v1/auth/token` - Login and token generation
- **File Upload**: `/api/v1/files/upload` - Transaction file processing
- **Transactions**: `/api/v1/transactions/` - Transaction management
- **Categories**: `/api/v1/categories/` - Category management
- **Reports**: `/api/v1/reports/` - Report generation
- **Agent**: `/api/v1/agent/command` - Conversational AI interface

## Data Models
The API includes comprehensive data models for:
- User management and authentication
- Transaction processing and categorization
- File upload and processing
- Reporting and analytics
- System health and monitoring

## Error Handling
All endpoints return structured error responses with:
- HTTP status codes
- Error message details
- Validation error specifics
- Request correlation IDs
"""
        
        return summary
    
    def main():
        """Main function to generate documentation."""
        print("🚀 Starting OpenAPI documentation generation...")
        
        try:
            # Generate schema
            schema = generate_openapi_schema()
            
            # Save files
            json_file, output_dir = save_schema_files(schema)
            
            # Generate summary
            summary = generate_documentation_summary(schema)
            summary_file = output_dir / "API_SUMMARY.md"
            with open(summary_file, "w") as f:
                f.write(summary)
            print(f"✓ Documentation summary saved to: {summary_file}")
            
            # Generate endpoint list
            endpoint_list = []
            for path, methods in schema.get("paths", {}).items():
                for method, details in methods.items():
                    if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                        endpoint_list.append({
                            "path": path,
                            "method": method.upper(),
                            "summary": details.get("summary", ""),
                            "tags": details.get("tags", []),
                        })
            
            endpoints_file = output_dir / "endpoints.json"
            with open(endpoints_file, "w") as f:
                json.dump(endpoint_list, f, indent=2)
            print(f"✓ Endpoint list saved to: {endpoints_file}")
            
            print(f"""
🎉 OpenAPI documentation generation complete!

📁 Files generated:
   - {json_file}
   - {summary_file}
   - {endpoints_file}

📊 Summary:
   - {len(schema.get('paths', {}))} API endpoints documented
   - {len(schema.get('components', {}).get('schemas', {}))} data models
   - Authentication and security details included
   
🔗 View documentation:
   - Use tools like Swagger UI, Redoc, or Postman
   - Import the OpenAPI JSON/YAML file for full API documentation
            """)
            
            return True
            
        except Exception as e:
            print(f"❌ Error generating documentation: {e}")
            import traceback
            traceback.print_exc()
            return False

    if __name__ == "__main__":
        success = main()
        sys.exit(0 if success else 1)

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the API directory with the virtual environment activated.")
    sys.exit(1)