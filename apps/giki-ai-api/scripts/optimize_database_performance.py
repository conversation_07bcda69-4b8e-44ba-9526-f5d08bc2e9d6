#!/usr/bin/env python3
"""
Database Performance Optimization Script for giki.ai

This script implements comprehensive database optimizations to reduce timeouts
and improve overall system performance, especially for AI categorization operations.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

import asyncpg
from asyncpg import Connection

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.giki_ai_api.core.config import settings

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseOptimizer:
    """Comprehensive database optimization for giki.ai performance."""
    
    def __init__(self, db_url: str):
        self.db_url = db_url
        self.conn: Optional[Connection] = None
        
    async def connect(self):
        """Establish database connection."""
        self.conn = await asyncpg.connect(self.db_url)
        logger.info("Connected to database")
        
    async def disconnect(self):
        """Close database connection."""
        if self.conn:
            await self.conn.close()
            logger.info("Disconnected from database")
            
    async def analyze_current_indexes(self) -> Dict[str, List[Dict]]:
        """Analyze existing indexes and their usage."""
        logger.info("Analyzing current indexes...")
        
        # Get all indexes with their sizes
        index_query = """
        SELECT
            schemaname,
            tablename,
            indexname,
            indexdef,
            pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
            idx_scan as times_used,
            idx_tup_read as tuples_read,
            idx_tup_fetch as tuples_fetched
        FROM
            pg_stat_user_indexes
        WHERE
            schemaname = 'public'
        ORDER BY
            tablename, indexname;
        """
        
        indexes = await self.conn.fetch(index_query)
        
        # Get missing index suggestions based on slow queries
        missing_query = """
        SELECT
            schemaname,
            tablename,
            attname as column_name,
            n_distinct,
            correlation
        FROM
            pg_stats
        WHERE
            schemaname = 'public'
            AND n_distinct > 100
            AND tablename IN ('transactions', 'categories', 'tenants', 'users')
        ORDER BY
            n_distinct DESC;
        """
        
        missing_candidates = await self.conn.fetch(missing_query)
        
        return {
            'existing_indexes': [dict(row) for row in indexes],
            'index_candidates': [dict(row) for row in missing_candidates]
        }
        
    async def create_performance_indexes(self):
        """Create indexes to improve query performance."""
        logger.info("Creating performance-optimized indexes...")
        
        indexes_to_create = [
            # Critical for authentication queries
            ("idx_users_email", "users", "email", "WHERE is_active = true"),
            ("idx_users_tenant_active", "users", "tenant_id, is_active", None),
            
            # Critical for transaction queries
            ("idx_transactions_tenant_status", "transactions", "tenant_id, status", None),
            ("idx_transactions_tenant_date", "transactions", "tenant_id, date DESC", None),
            ("idx_transactions_ai_confidence", "transactions", "tenant_id, ai_confidence", 
             "WHERE ai_confidence IS NOT NULL"),
            ("idx_transactions_needs_review", "transactions", "tenant_id, ai_confidence", 
             "WHERE status = 'needs_review'"),
            
            # Critical for category lookups
            ("idx_categories_tenant_name", "categories", "tenant_id, name", None),
            ("idx_categories_tenant_level", "categories", "tenant_id, level, parent_id", None),
            ("idx_categories_gl_code", "categories", "tenant_id, gl_code", 
             "WHERE gl_code IS NOT NULL"),
            
            # Critical for MIS operations
            ("idx_tenants_settings", "tenants", "(settings->'business_context')", 
             "WHERE settings IS NOT NULL"),
            
            # Performance for file operations
            ("idx_uploaded_files_tenant_status", "uploaded_files", "tenant_id, status", None),
            ("idx_uploaded_files_processing", "uploaded_files", "tenant_id, created_at DESC", 
             "WHERE status IN ('processing', 'pending')"),
            
            # AI categorization performance
            ("idx_business_context", "tenants", "id, (settings->'business_context'->'industry')", 
             "WHERE settings->'business_context' IS NOT NULL"),
        ]
        
        for index_name, table, columns, where_clause in indexes_to_create:
            try:
                # Check if index already exists
                check_query = """
                SELECT 1 FROM pg_indexes 
                WHERE schemaname = 'public' 
                AND indexname = $1
                """
                exists = await self.conn.fetchval(check_query, index_name)
                
                if not exists:
                    # Create index concurrently to avoid locking
                    index_sql = f"""
                    CREATE INDEX CONCURRENTLY IF NOT EXISTS {index_name}
                    ON {table} ({columns})
                    {where_clause if where_clause else ''}
                    """
                    
                    logger.info(f"Creating index: {index_name}")
                    await self.conn.execute(index_sql)
                    logger.info(f"Created index: {index_name}")
                else:
                    logger.info(f"Index already exists: {index_name}")
                    
            except Exception as e:
                logger.error(f"Failed to create index {index_name}: {e}")
                
    async def optimize_database_settings(self):
        """Optimize database configuration for better performance."""
        logger.info("Optimizing database settings...")
        
        # Note: Some settings require superuser privileges
        # These are recommendations that can be applied at the database level
        optimization_queries = [
            # Increase work memory for complex queries
            "ALTER DATABASE giki_ai_db SET work_mem = '16MB'",
            
            # Optimize for SSD storage
            "ALTER DATABASE giki_ai_db SET random_page_cost = 1.1",
            
            # Increase maintenance work memory for index creation
            "ALTER DATABASE giki_ai_db SET maintenance_work_mem = '128MB'",
            
            # Enable parallel queries
            "ALTER DATABASE giki_ai_db SET max_parallel_workers_per_gather = 2",
            
            # Optimize checkpoint settings
            "ALTER DATABASE giki_ai_db SET checkpoint_completion_target = 0.9",
            
            # Enable query planning optimizations
            "ALTER DATABASE giki_ai_db SET enable_partitionwise_join = on",
            "ALTER DATABASE giki_ai_db SET enable_partitionwise_aggregate = on",
        ]
        
        for query in optimization_queries:
            try:
                await self.conn.execute(query)
                logger.info(f"Applied: {query}")
            except Exception as e:
                logger.warning(f"Could not apply setting (may require superuser): {query} - {e}")
                
    async def analyze_and_vacuum_tables(self):
        """Run ANALYZE and VACUUM on critical tables."""
        logger.info("Running ANALYZE and VACUUM on critical tables...")
        
        critical_tables = [
            'transactions',
            'categories', 
            'users',
            'tenants',
            'uploaded_files',
        ]
        
        for table in critical_tables:
            try:
                # Analyze table to update statistics
                logger.info(f"Analyzing table: {table}")
                await self.conn.execute(f"ANALYZE {table}")
                
                # Vacuum to reclaim space and update visibility map
                logger.info(f"Vacuuming table: {table}")
                await self.conn.execute(f"VACUUM (ANALYZE) {table}")
                
            except Exception as e:
                logger.error(f"Failed to analyze/vacuum {table}: {e}")
                
    async def create_materialized_views(self):
        """Create materialized views for expensive queries."""
        logger.info("Creating materialized views for performance...")
        
        materialized_views = [
            # Category hierarchy view for faster lookups
            {
                'name': 'mv_category_hierarchy',
                'query': """
                CREATE MATERIALIZED VIEW IF NOT EXISTS mv_category_hierarchy AS
                SELECT 
                    c.id,
                    c.tenant_id,
                    c.name,
                    c.gl_code,
                    c.level,
                    c.parent_id,
                    p.name as parent_name,
                    CASE 
                        WHEN c.level = 0 THEN c.name
                        WHEN c.level = 1 THEN p.name || ' > ' || c.name
                        ELSE p.name || ' > ' || c.name
                    END as full_path
                FROM categories c
                LEFT JOIN categories p ON c.parent_id = p.id
                WITH DATA
                """,
                'indexes': [
                    "CREATE INDEX idx_mv_cat_hierarchy_tenant ON mv_category_hierarchy (tenant_id)",
                    "CREATE INDEX idx_mv_cat_hierarchy_name ON mv_category_hierarchy (tenant_id, name)",
                    "CREATE INDEX idx_mv_cat_hierarchy_gl ON mv_category_hierarchy (tenant_id, gl_code)",
                ]
            },
            
            # Transaction statistics view
            {
                'name': 'mv_transaction_stats',
                'query': """
                CREATE MATERIALIZED VIEW IF NOT EXISTS mv_transaction_stats AS
                SELECT 
                    tenant_id,
                    DATE_TRUNC('day', date) as date,
                    status,
                    COUNT(*) as transaction_count,
                    SUM(amount) as total_amount,
                    AVG(ai_confidence) as avg_confidence,
                    COUNT(CASE WHEN ai_confidence < 0.85 THEN 1 END) as needs_review_count
                FROM transactions
                GROUP BY tenant_id, DATE_TRUNC('day', date), status
                WITH DATA
                """,
                'indexes': [
                    "CREATE INDEX idx_mv_trans_stats_tenant_date ON mv_transaction_stats (tenant_id, date DESC)",
                ]
            }
        ]
        
        for mv in materialized_views:
            try:
                # Create materialized view
                await self.conn.execute(mv['query'])
                logger.info(f"Created materialized view: {mv['name']}")
                
                # Create indexes on materialized view
                for index_sql in mv['indexes']:
                    await self.conn.execute(index_sql)
                    
            except Exception as e:
                if 'already exists' in str(e):
                    logger.info(f"Materialized view already exists: {mv['name']}")
                else:
                    logger.error(f"Failed to create materialized view {mv['name']}: {e}")
                    
    async def setup_connection_pooling_config(self):
        """Display recommended connection pool settings."""
        logger.info("\n" + "="*60)
        logger.info("RECOMMENDED CONNECTION POOL SETTINGS")
        logger.info("="*60)
        
        recommendations = """
Update the following in database.py:

async def create_pool(
    min_size: int = 10,        # Increased from 5
    max_size: int = 30,        # Increased from 15
    max_queries: int = 500000,  # Increased from 100000
    max_inactive_connection_lifetime: float = 300.0,  # Increased from 180
    command_timeout: float = 10.0,  # Increased from 3.0 for complex queries
    statement_cache_size=5000,  # Increased from 2000
    ...
)

Also update these server settings:
- statement_timeout: "10s"  # Increased from 3s
- work_mem: "16MB"         # For complex queries
- shared_buffers: "256MB"  # If you have DB admin access
"""
        logger.info(recommendations)
        
    async def setup_redis_cache(self):
        """Display Redis setup instructions."""
        logger.info("\n" + "="*60)
        logger.info("REDIS CACHE SETUP INSTRUCTIONS")
        logger.info("="*60)
        
        redis_setup = """
To enable Redis cache and reduce database load:

1. Install Redis locally:
   - macOS: brew install redis && brew services start redis
   - Linux: sudo apt-get install redis-server && sudo systemctl start redis
   
2. Verify Redis is running:
   redis-cli ping
   (should return PONG)
   
3. The application will automatically use Redis when available.

4. For production, consider:
   - Redis Cloud or Google Memorystore
   - Connection pooling
   - Persistence configuration
   - Monitoring and alerting
"""
        logger.info(redis_setup)
        
    async def check_slow_queries(self):
        """Identify slow queries that need optimization."""
        logger.info("\nAnalyzing slow queries...")
        
        # Note: pg_stat_statements extension required
        slow_query_check = """
        SELECT 
            query,
            calls,
            total_exec_time,
            mean_exec_time,
            max_exec_time,
            rows
        FROM pg_stat_statements
        WHERE mean_exec_time > 100  -- queries averaging over 100ms
        ORDER BY mean_exec_time DESC
        LIMIT 10;
        """
        
        try:
            slow_queries = await self.conn.fetch(slow_query_check)
            if slow_queries:
                logger.info("\nTop slow queries found:")
                for query in slow_queries:
                    logger.info(f"- Mean time: {query['mean_exec_time']:.2f}ms")
                    logger.info(f"  Query: {query['query'][:100]}...")
        except Exception:
            logger.info("pg_stat_statements extension not available - skipping slow query analysis")
            
    async def optimize(self):
        """Run all optimizations."""
        try:
            await self.connect()
            
            # Analyze current state
            analysis = await self.analyze_current_indexes()
            logger.info(f"Found {len(analysis['existing_indexes'])} existing indexes")
            
            # Create performance indexes
            await self.create_performance_indexes()
            
            # Optimize database settings
            await self.optimize_database_settings()
            
            # Analyze and vacuum tables
            await self.analyze_and_vacuum_tables()
            
            # Create materialized views
            await self.create_materialized_views()
            
            # Check for slow queries
            await self.check_slow_queries()
            
            # Display recommendations
            await self.setup_connection_pooling_config()
            await self.setup_redis_cache()
            
            logger.info("\n✅ Database optimization complete!")
            
        finally:
            await self.disconnect()


async def main():
    """Main execution function."""
    # Get database URL from environment or settings
    db_url = os.environ.get("DATABASE_URL") or settings.DATABASE_URL
    
    if not db_url:
        logger.error("DATABASE_URL not set. Please configure your environment.")
        sys.exit(1)
    
    # Convert SQLAlchemy-style URLs to asyncpg format
    if "postgresql+asyncpg://" in db_url:
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
        
    optimizer = DatabaseOptimizer(db_url)
    await optimizer.optimize()


if __name__ == "__main__":
    asyncio.run(main())