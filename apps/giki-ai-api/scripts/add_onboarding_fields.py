#!/usr/bin/env python3
"""
Add onboarding-related fields to the transactions table.

Adds:
- original_category_label: Customer's original category for temporal validation
- vendor_name: Extracted vendor/merchant name
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from sqlalchemy import text

from giki_ai_api.core.database import get_database_config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def add_onboarding_fields():
    """Add onboarding fields to transactions table."""

    # Get database session
    SessionLocal = get_database_config()

    async with SessionLocal() as session:
        try:
            # Check if columns already exist
            check_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'transactions' 
                AND column_name IN ('original_category_label', 'vendor_name')
            """)

            result = await session.execute(check_query)
            existing_columns = [row[0] for row in result]

            # Add original_category_label if it doesn't exist
            if "original_category_label" not in existing_columns:
                logger.info("Adding original_category_label column...")
                await session.execute(
                    text("""
                    ALTER TABLE transactions 
                    ADD COLUMN original_category_label VARCHAR(255) NULL
                """)
                )

                # Add comment separately for PostgreSQL
                await session.execute(
                    text("""
                    COMMENT ON COLUMN transactions.original_category_label 
                    IS 'Customer''s original category label for temporal validation'
                """)
                )
                logger.info("✅ Added original_category_label column")
            else:
                logger.info("original_category_label column already exists")

            # Add vendor_name if it doesn't exist
            if "vendor_name" not in existing_columns:
                logger.info("Adding vendor_name column...")
                await session.execute(
                    text("""
                    ALTER TABLE transactions 
                    ADD COLUMN vendor_name VARCHAR(255) NULL
                """)
                )

                # Add index for vendor_name
                await session.execute(
                    text("""
                    CREATE INDEX idx_transactions_vendor_name 
                    ON transactions(vendor_name)
                """)
                )
                logger.info("✅ Added vendor_name column with index")
            else:
                logger.info("vendor_name column already exists")

            # Commit changes
            await session.commit()
            logger.info("✅ Database schema updated successfully")

            # Verify the changes
            verify_query = text("""
                SELECT 
                    c.column_name, 
                    c.data_type, 
                    c.is_nullable,
                    pgd.description as column_comment
                FROM information_schema.columns c
                LEFT JOIN pg_catalog.pg_statio_all_tables st ON c.table_name = st.relname
                LEFT JOIN pg_catalog.pg_description pgd ON pgd.objoid = st.relid AND pgd.objsubid = c.ordinal_position
                WHERE c.table_name = 'transactions'
                AND c.column_name IN ('original_category_label', 'vendor_name')
                ORDER BY c.column_name
            """)

            result = await session.execute(verify_query)
            logger.info("\nColumn verification:")
            for row in result:
                logger.info(
                    f"  - {row[0]}: {row[1]}, nullable={row[2]}, comment='{row[3] or 'N/A'}'"
                )

        except Exception as e:
            logger.error(f"Failed to add onboarding fields: {e}")
            await session.rollback()
            raise


async def main():
    """Main function."""
    logger.info("Adding onboarding fields to transactions table...")

    try:
        await add_onboarding_fields()
        logger.info("✅ Onboarding fields added successfully")

    except Exception as e:
        logger.error(f"❌ Failed to add onboarding fields: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
