#!/usr/bin/env python
"""
Setup Demo Data for Customer Testing - giki.ai
===============================================

Creates standardized demo data files for each customer persona that can be used
for testing, demos, and customer onboarding validation.

Usage:
    uv run python scripts/setup_demo_data.py
"""

import os
import shutil
from datetime import datetime
from pathlib import Path


def setup_demo_data():
    """Setup standardized demo data files."""
    
    # Create demo data directory structure
    # Use centralized test data library
    base_dir = Path(__file__).parent.parent.parent.parent / "libs/test-data"
    demo_dir = base_dir / "common"
    
    # MIS directory paths
    mis_dirs = {
        "quick_setup": base_dir / "mis-quick-setup",
        "historical": base_dir / "mis-historical-enhancement",
        "schema": base_dir / "mis-schema-enhancement"
    }
    
    # Ensure directories exist
    demo_dir.mkdir(parents=True, exist_ok=True)
    for mis_dir in mis_dirs.values():
        mis_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate fresh demo data
    print("🎯 Setting up Customer Demo Data")
    print("=" * 40)
    
    # Generate M1 Nuvie - small retail (500 transactions)
    print("\n📊 Generating M1 Nuvie (Small Retail) Demo Data...")
    os.system(f"uv run python scripts/generate_performance_test_data.py --persona m1_nuvie --months 1 --output-dir {demo_dir}")
    
    # Generate M2 Rezolve - consultancy (1500 transactions)  
    print("\n📊 Generating M2 Rezolve (Consultancy) Demo Data...")
    os.system(f"uv run python scripts/generate_performance_test_data.py --persona m2_rezolve --months 1 --output-dir {demo_dir}")
    
    # Generate M3 Giki - manufacturing (5000 transactions)
    print("\n📊 Generating M3 Giki (Manufacturing) Demo Data...")
    os.system(f"uv run python scripts/generate_performance_test_data.py --persona m3_giki --months 1 --output-dir {demo_dir}")
    
    # Copy to milestones directory with standard names
    print("\n📁 Organizing Demo Files...")
    
    # Find the generated files and copy with standard names
    demo_files = list(demo_dir.glob("*_business_*"))
    
    for file_path in demo_files:
        filename = file_path.name
        
        if "m1_nuvie" in filename:
            standard_name = "MIS_Quick_Setup_Retail_Demo.xlsx"
            target_dir = mis_dirs["quick_setup"]
            shutil.copy2(file_path, target_dir / standard_name)
            print(f"   ✅ MIS Quick Setup → {target_dir / standard_name}")
            
        elif "m2_rezolve" in filename:
            standard_name = "MIS_Historical_Consultancy_Demo.xlsx"
            target_dir = mis_dirs["historical"]
            shutil.copy2(file_path, target_dir / standard_name)
            print(f"   ✅ MIS Historical → {target_dir / standard_name}")
            
        elif "m3_giki" in filename:
            standard_name = "MIS_Schema_Manufacturing_Demo.xlsx"
            target_dir = mis_dirs["schema"]
            shutil.copy2(file_path, target_dir / standard_name)
            print(f"   ✅ MIS Schema → {target_dir / standard_name}")
    
    # Create README for demo data
    readme_content = """# Customer Demo Data - MIS Implementation Strategy

This directory contains realistic transaction data for each MIS implementation path:

## MIS Quick Setup Path (5 minutes)
- **File**: MIS_Quick_Setup_Retail_Demo.xlsx
- **Description**: Small retail business (200-800 transactions/month)
- **Characteristics**: 90% uncategorized, simple transaction types
- **Use Case**: Test zero-onboarding AI categorization accuracy (87% target)

## MIS Historical Enhancement Path (30-45 minutes)
- **File**: MIS_Historical_Consultancy_Demo.xlsx
- **Description**: Growing consultancy (800-3000 transactions/month)
- **Characteristics**: 70% uncategorized, moderate complexity
- **Use Case**: Test historical pattern learning (95% accuracy target)

## MIS Schema Enhancement Path (30 minutes)
- **File**: MIS_Schema_Manufacturing_Demo.xlsx
- **Description**: Enterprise manufacturing (3000-15000 transactions/month)
- **Characteristics**: Full GL code integration, high complexity
- **Use Case**: Test GL code schema compliance (95% accuracy target)

## Usage

Upload these files through the giki.ai interface to test:
1. **Categorization accuracy** for each persona
2. **Processing performance** with realistic data volumes
3. **Export functionality** with proper MIS formatting
4. **Customer journey validation** end-to-end

Generated: {timestamp}
"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    readme_path = base_dir / "DEMO_DATA_README.md"
    readme_path.write_text(readme_content.format(timestamp=timestamp))
    
    print(f"\n📄 Created README at {readme_path}")
    print("\n🎉 Demo data setup complete!")
    print(f"   📁 Test data library: {base_dir}")
    print(f"   📁 Common files in: {demo_dir}")
    
    # Show summary
    print("\n📊 Demo Data Summary:")
    for mis_name, mis_dir in mis_dirs.items():
        if mis_dir.is_dir():
            files = list(mis_dir.glob("*.xlsx"))
            if files:
                file_path = files[0]
                size_mb = file_path.stat().st_size / (1024 * 1024)
                print(f"   {mis_name}: {file_path.name} ({size_mb:.1f} MB)")

if __name__ == "__main__":
    setup_demo_data()