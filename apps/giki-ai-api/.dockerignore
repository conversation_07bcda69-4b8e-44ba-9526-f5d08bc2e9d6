# Python cache and bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
src/.venv/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/
htmlcov/
test-results/
coverage_html_report/

# Documentation
docs/_build/
.readthedocs.yml

# Logs
*.log
logs/

# Development files
start.sh
Procfile.minimal
minimal_main.py

# Temporary files
*.tmp
*.temp
*.bak

# Database files
*.db
*.sqlite3

# Environment files
.env.*
!.env.example

# Node modules (if any)
node_modules/

# Nx cache
.nx/

# Playwright
.playwright-mcp/