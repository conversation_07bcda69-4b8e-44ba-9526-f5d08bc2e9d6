{"name": "giki-ai-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/giki-ai-api/src", "tags": ["type:app", "lang:python", "scope:giki-ai"], "targets": {"serve": {"executor": "nx:run-commands", "options": {"commands": ["./scripts/nx/ensure-postgres.sh", "./scripts/nx/ensure-redis.sh", "./scripts/nx/clear-ports.sh clear-api", "cd apps/giki-ai-api && nohup env GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/giki-ai-workspace/infrastructure/service-accounts/development/dev-service-account.json uv run uvicorn giki_ai_api.core.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir src > ../../logs/api.log 2>&1 &"], "parallel": false, "cwd": "{workspaceRoot}"}}, "serve:stable": {"executor": "nx:run-commands", "options": {"commands": ["./scripts/nx/clear-ports.sh clear-api", "cd apps/giki-ai-api && uv run gunicorn -k uvicorn.workers.UvicornWorker giki_ai_api.core.main:app --bind 0.0.0.0:8000 --workers 1 --access-logfile - --error-logfile -"], "parallel": false, "cwd": "{workspaceRoot}"}}, "db": {"executor": "nx:run-commands", "options": {"command": "./scripts/nx/ensure-postgres.sh", "cwd": "{workspaceRoot}"}}, "redis": {"executor": "nx:run-commands", "options": {"command": "./scripts/nx/ensure-redis.sh", "cwd": "{workspaceRoot}"}}, "start-services": {"executor": "nx:run-commands", "options": {"commands": ["./scripts/nx/ensure-postgres.sh", "./scripts/nx/ensure-redis.sh"], "parallel": true, "cwd": "{workspaceRoot}"}}, "start-all": {"executor": "nx:run-commands", "options": {"command": "./scripts/nx/start-all.sh", "cwd": "{workspaceRoot}"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-api && uv run ruff check src --fix && uv run ruff format src", "cwd": "{workspaceRoot}"}}, "test": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-api && source .env.test && export ENVIRONMENT=test && export TEST_DATABASE_URL=\"$TEST_DATABASE_URL\" && export DATABASE_URL=\"$TEST_DATABASE_URL\" && export GOOGLE_APPLICATION_CREDENTIALS=\"$TEST_GOOGLE_APPLICATION_CREDENTIALS\" && export VERTEX_PROJECT_ID=\"$TEST_VERTEX_AI_PROJECT_ID\" && uv run pytest tests/", "cwd": "{workspaceRoot}"}}, "test:unit": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-api && source .env.test && export ENVIRONMENT=test && export TEST_DATABASE_URL=\"$TEST_DATABASE_URL\" && export DATABASE_URL=\"$TEST_DATABASE_URL\" && export GOOGLE_APPLICATION_CREDENTIALS=\"$TEST_GOOGLE_APPLICATION_CREDENTIALS\" && export VERTEX_PROJECT_ID=\"$TEST_VERTEX_AI_PROJECT_ID\" && uv run pytest tests/unit/ -v", "cwd": "{workspaceRoot}"}}, "test:integration": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-api && source .env.test && export ENVIRONMENT=test && export TEST_DATABASE_URL=\"$TEST_DATABASE_URL\" && export DATABASE_URL=\"$TEST_DATABASE_URL\" && export GOOGLE_APPLICATION_CREDENTIALS=\"$TEST_GOOGLE_APPLICATION_CREDENTIALS\" && export VERTEX_PROJECT_ID=\"$TEST_VERTEX_AI_PROJECT_ID\" && uv run pytest tests/integration/ -v", "cwd": "{workspaceRoot}"}}, "test:mis": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-api && export TEST_DATABASE_URL='postgresql://postgres:postgres@localhost:5432/giki_ai_test' && uv run pytest tests/integration/mis/ -v", "cwd": "{workspaceRoot}"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "./scripts/nx/build-and-deploy.sh", "cwd": "{workspaceRoot}"}}}}