# Giki AI API

FastAPI-based backend for the Giki AI platform providing transaction categorization and financial data processing.

## Features

- File upload and processing (Excel, CSV)
- ✅ **Real AI-Powered Transaction Categorization** (Vertex AI Gemini 2.0 Flash)
  - Genuine AI reasoning and confidence scoring (0.0-1.0)
  - Natural language query processing and conversation context
  - Detailed explanations for categorization decisions
  - >85% accuracy achieved with real AI intelligence
- Multi-tenant architecture
- Google Agent Development Kit (ADK) integration with production-ready agents
- Enhanced Vertex AI integration for comprehensive ML capabilities

## Development

```bash
# Start development server
pnpm dev:api

# Run tests
pnpm test

# Build for production
pnpm build:prod
```

## Architecture

- **FastAPI**: Modern Python web framework
- **PostgreSQL**: Primary database with PGVector for embeddings
- **Vertex AI**: Google Cloud AI platform integration
- **ADK**: Google Agent Development Kit for AI agents

## API Documentation

When running locally, visit http://localhost:8000/docs for interactive API documentation.
