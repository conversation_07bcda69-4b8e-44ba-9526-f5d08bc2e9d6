# Multi-stage build for optimal production deployment with uv + hatchling
FROM python:3.12-slim AS builder

# Install uv - the fast Python package manager
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Set working directory
WORKDIR /app

# Copy dependency files for optimal layer caching
COPY pyproject.toml README.md ./

# Create virtual environment and install dependencies from pyproject.toml
RUN uv venv && \
    uv pip install -e .

# Copy application source code
COPY src/ ./src/

# Production runtime stage - minimal image
FROM python:3.12-slim AS runtime

# Set production environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PATH="/app/.venv/bin:$PATH"

# Set working directory
WORKDIR /app

# Copy virtual environment and application from builder
COPY --from=builder /app/.venv /app/.venv
COPY --from=builder /app/src /app/src
COPY --from=builder /app/pyproject.toml /app/
COPY --from=builder /app/README.md /app/

# Expose Cloud Run standard port
EXPOSE 8080

# Use uvicorn directly for better control over CORS
# --proxy-headers for Cloud Run load balancer compatibility
# Use PORT environment variable (Cloud Run standard)
CMD ["sh", "-c", "cd /app && python -m uvicorn src.giki_ai_api.core.main:app --host 0.0.0.0 --port ${PORT:-8080} --proxy-headers"]