# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
a2a-sdk==0.2.8
    # via giki-ai-api (pyproject.toml)
aiofiles==24.1.0
    # via giki-ai-api (pyproject.toml)
aioredis==2.0.1
    # via giki-ai-api (pyproject.toml)
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   google-adk
    #   google-genai
    #   httpx
    #   mcp
    #   sse-starlette
    #   starlette
    #   watchfiles
argon2-cffi==25.1.0
    # via giki-ai-api (pyproject.toml)
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
async-timeout==5.0.1
    # via aioredis
asyncpg==0.30.0
    # via giki-ai-api (pyproject.toml)
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
authlib==1.6.0
    # via google-adk
bcrypt==4.3.0
    # via passlib
cachetools==5.5.2
    # via google-auth
certifi==2025.6.15
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via
    #   argon2-cffi-bindings
    #   cryptography
charset-normalizer==3.4.2
    # via
    #   reportlab
    #   requests
click==8.2.1
    # via
    #   google-adk
    #   rich-toolkit
    #   typer
    #   uvicorn
cloudpickle==3.1.1
    # via google-cloud-aiplatform
cryptography==45.0.4
    # via
    #   authlib
    #   python-jose
dnspython==2.7.0
    # via email-validator
docstring-parser==0.16
    # via google-cloud-aiplatform
ecdsa==0.19.1
    # via python-jose
email-validator==2.2.0
    # via
    #   giki-ai-api (pyproject.toml)
    #   fastapi
et-xmlfile==2.0.0
    # via openpyxl
fastapi==0.115.14
    # via
    #   giki-ai-api (pyproject.toml)
    #   a2a-sdk
    #   google-adk
fastapi-cli==0.0.7
    # via fastapi
google-adk==1.5.0
    # via giki-ai-api (pyproject.toml)
google-api-core==2.25.1
    # via
    #   giki-ai-api (pyproject.toml)
    #   a2a-sdk
    #   google-api-python-client
    #   google-cloud-aiplatform
    #   google-cloud-appengine-logging
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-logging
    #   google-cloud-resource-manager
    #   google-cloud-secret-manager
    #   google-cloud-speech
    #   google-cloud-storage
    #   google-cloud-trace
google-api-python-client==2.174.0
    # via google-adk
google-auth==2.40.3
    # via
    #   giki-ai-api (pyproject.toml)
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-auth-oauthlib
    #   google-cloud-aiplatform
    #   google-cloud-appengine-logging
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-logging
    #   google-cloud-resource-manager
    #   google-cloud-secret-manager
    #   google-cloud-speech
    #   google-cloud-storage
    #   google-cloud-trace
    #   google-genai
google-auth-httplib2==0.2.0
    # via
    #   giki-ai-api (pyproject.toml)
    #   google-api-python-client
google-auth-oauthlib==1.2.2
    # via giki-ai-api (pyproject.toml)
google-cloud-aiplatform==1.100.0
    # via
    #   giki-ai-api (pyproject.toml)
    #   google-adk
    #   vertexai
google-cloud-appengine-logging==1.6.2
    # via google-cloud-logging
google-cloud-audit-log==0.3.2
    # via google-cloud-logging
google-cloud-bigquery==3.34.0
    # via google-cloud-aiplatform
google-cloud-core==2.4.3
    # via
    #   google-cloud-bigquery
    #   google-cloud-logging
    #   google-cloud-storage
google-cloud-logging==3.12.1
    # via google-cloud-aiplatform
google-cloud-resource-manager==1.14.2
    # via google-cloud-aiplatform
google-cloud-secret-manager==2.24.0
    # via google-adk
google-cloud-speech==2.33.0
    # via google-adk
google-cloud-storage==2.19.0
    # via
    #   giki-ai-api (pyproject.toml)
    #   google-adk
    #   google-cloud-aiplatform
google-cloud-trace==1.16.2
    # via
    #   google-cloud-aiplatform
    #   opentelemetry-exporter-gcp-trace
google-crc32c==1.7.1
    # via
    #   google-cloud-storage
    #   google-resumable-media
google-genai==1.23.0
    # via
    #   google-adk
    #   google-cloud-aiplatform
google-resumable-media==2.7.2
    # via
    #   google-cloud-bigquery
    #   google-cloud-storage
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   google-cloud-audit-log
    #   grpc-google-iam-v1
    #   grpcio-status
graphviz==0.21
    # via google-adk
grpc-google-iam-v1==0.14.2
    # via
    #   google-cloud-logging
    #   google-cloud-resource-manager
    #   google-cloud-secret-manager
grpcio==1.73.1
    # via
    #   a2a-sdk
    #   google-api-core
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-reflection
    #   grpcio-status
    #   grpcio-tools
grpcio-reflection==1.73.1
    # via a2a-sdk
grpcio-status==1.73.1
    # via google-api-core
grpcio-tools==1.73.1
    # via a2a-sdk
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   giki-ai-api (pyproject.toml)
    #   a2a-sdk
    #   fastapi
    #   google-genai
    #   mcp
httpx-sse==0.4.1
    # via
    #   a2a-sdk
    #   mcp
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
importlib-metadata==8.7.0
    # via opentelemetry-api
jinja2==3.1.6
    # via fastapi
jsonschema==4.24.0
    # via mcp
jsonschema-specifications==2025.4.1
    # via jsonschema
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mcp==1.10.1
    # via google-adk
mdurl==0.1.2
    # via markdown-it-py
numpy==2.3.1
    # via
    #   giki-ai-api (pyproject.toml)
    #   pandas
    #   shapely
oauthlib==3.3.1
    # via requests-oauthlib
openpyxl==3.1.5
    # via giki-ai-api (pyproject.toml)
opentelemetry-api==1.34.1
    # via
    #   a2a-sdk
    #   google-adk
    #   google-cloud-logging
    #   opentelemetry-exporter-gcp-trace
    #   opentelemetry-resourcedetector-gcp
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-exporter-gcp-trace==1.9.0
    # via
    #   google-adk
    #   google-cloud-aiplatform
opentelemetry-resourcedetector-gcp==1.9.0a0
    # via opentelemetry-exporter-gcp-trace
opentelemetry-sdk==1.34.1
    # via
    #   a2a-sdk
    #   google-adk
    #   google-cloud-aiplatform
    #   opentelemetry-exporter-gcp-trace
    #   opentelemetry-resourcedetector-gcp
opentelemetry-semantic-conventions==0.55b1
    # via opentelemetry-sdk
packaging==25.0
    # via
    #   google-cloud-aiplatform
    #   google-cloud-bigquery
pandas==2.3.0
    # via giki-ai-api (pyproject.toml)
passlib==1.7.4
    # via giki-ai-api (pyproject.toml)
pillow==11.2.1
    # via reportlab
proto-plus==1.26.1
    # via
    #   giki-ai-api (pyproject.toml)
    #   google-api-core
    #   google-cloud-aiplatform
    #   google-cloud-appengine-logging
    #   google-cloud-logging
    #   google-cloud-resource-manager
    #   google-cloud-secret-manager
    #   google-cloud-speech
    #   google-cloud-trace
protobuf==6.31.1
    # via
    #   giki-ai-api (pyproject.toml)
    #   a2a-sdk
    #   google-api-core
    #   google-cloud-aiplatform
    #   google-cloud-appengine-logging
    #   google-cloud-audit-log
    #   google-cloud-logging
    #   google-cloud-resource-manager
    #   google-cloud-secret-manager
    #   google-cloud-speech
    #   google-cloud-trace
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-reflection
    #   grpcio-status
    #   grpcio-tools
    #   proto-plus
psutil==7.0.0
    # via giki-ai-api (pyproject.toml)
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   python-jose
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   giki-ai-api (pyproject.toml)
    #   a2a-sdk
    #   fastapi
    #   google-adk
    #   google-cloud-aiplatform
    #   google-genai
    #   mcp
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via
    #   giki-ai-api (pyproject.toml)
    #   mcp
pygments==2.19.2
    # via rich
pyparsing==3.2.3
    # via httplib2
python-dateutil==2.9.0.post0
    # via
    #   google-adk
    #   google-cloud-bigquery
    #   pandas
python-dotenv==1.1.1
    # via
    #   giki-ai-api (pyproject.toml)
    #   google-adk
    #   pydantic-settings
    #   uvicorn
python-jose==3.5.0
    # via giki-ai-api (pyproject.toml)
python-multipart==0.0.20
    # via
    #   fastapi
    #   mcp
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   giki-ai-api (pyproject.toml)
    #   google-adk
    #   uvicorn
redis==6.2.0
    # via giki-ai-api (pyproject.toml)
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
reportlab==4.4.2
    # via giki-ai-api (pyproject.toml)
requests==2.32.4
    # via
    #   google-adk
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-storage
    #   google-genai
    #   opentelemetry-resourcedetector-gcp
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via google-auth-oauthlib
rich==14.0.0
    # via
    #   rich-toolkit
    #   typer
rich-toolkit==0.14.7
    # via fastapi-cli
rpds-py==0.25.1
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via
    #   google-auth
    #   python-jose
setuptools==80.9.0
    # via grpcio-tools
shapely==2.1.1
    # via google-cloud-aiplatform
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   ecdsa
    #   python-dateutil
sniffio==1.3.1
    # via anyio
sqlalchemy==2.0.41
    # via google-adk
sse-starlette==2.3.6
    # via
    #   a2a-sdk
    #   mcp
starlette==0.46.2
    # via
    #   a2a-sdk
    #   fastapi
    #   google-adk
    #   mcp
tenacity==8.5.0
    # via google-genai
typer==0.16.0
    # via
    #   giki-ai-api (pyproject.toml)
    #   fastapi-cli
typing-extensions==4.14.0
    # via
    #   aioredis
    #   anyio
    #   fastapi
    #   google-adk
    #   google-cloud-aiplatform
    #   google-genai
    #   opentelemetry-api
    #   opentelemetry-resourcedetector-gcp
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
    #   pydantic
    #   pydantic-core
    #   referencing
    #   rich-toolkit
    #   sqlalchemy
    #   typer
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
tzlocal==5.3.1
    # via google-adk
uritemplate==4.2.0
    # via google-api-python-client
urllib3==2.5.0
    # via requests
uvicorn==0.35.0
    # via
    #   giki-ai-api (pyproject.toml)
    #   fastapi
    #   fastapi-cli
    #   google-adk
    #   mcp
uvloop==0.21.0
    # via
    #   giki-ai-api (pyproject.toml)
    #   uvicorn
vertexai==1.43.0
    # via giki-ai-api (pyproject.toml)
watchfiles==1.1.0
    # via uvicorn
websockets==15.0.1
    # via
    #   google-adk
    #   google-genai
    #   uvicorn
zipp==3.23.0
    # via importlib-metadata
