"""
CSS utility functions for the giki-ai-api application.

This module contains utility functions for handling CSS class names,
migrated from the shared utils library.
"""


def cn(*inputs):
    """
    Combines CSS class names, similar to the clsx and tailwind-merge functionality in JavaScript.

    This is a Python implementation of the original TypeScript utility:

    ```typescript
    import { type ClassValue, clsx } from "clsx"
    import { twMerge } from "tailwind-merge"

    export function cn(...inputs: ClassValue[]) {
      return twMerge(clsx(inputs))
    }
    ```

    Args:
        *inputs: Variable number of class name inputs (strings, lists, dicts, etc.)

    Returns:
        str: A string of combined class names with duplicates and conflicts resolved
    """
    # Simple implementation that joins all string inputs with spaces
    # This is a placeholder - in a real implementation, we would need to:
    # 1. Handle conditional classes (similar to clsx)
    # 2. Resolve Tailwind CSS conflicts (similar to tailwind-merge)
    result = []

    for item in inputs:
        if isinstance(item, str) and item:
            result.append(item)
        elif isinstance(item, list | tuple):
            result.append(cn(*item))
        elif isinstance(item, dict):
            for key, value in item.items():
                if value:
                    result.append(key)

    return " ".join(result)
