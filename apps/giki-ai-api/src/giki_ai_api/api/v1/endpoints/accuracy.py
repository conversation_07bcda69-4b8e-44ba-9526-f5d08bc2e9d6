"""
Accuracy measurement API endpoints.

Provides endpoints for running accuracy tests across three scenarios:
1. Historical data (RAG-based)
2. Schema only
3. Zero onboarding
"""

import logging
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field

from ....core.dependencies import get_db_session
from ....domains.accuracy.models import (
    AccuracyTestScenario,
    AccuracyTestStatus,
    AccuracyTestSummary,
)
from ....domains.accuracy.service import AccuracyMeasurementService

logger = logging.getLogger(__name__)

router = APIRouter()


class CreateAccuracyTestRequest(BaseModel):
    """Request to create accuracy test."""

    name: str = Field(..., max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    scenario: AccuracyTestScenario
    test_data_source: str = Field(..., max_length=500)
    category_schema_id: Optional[int] = None
    sample_size: int = Field(default=100, ge=1, le=1000)


class AccuracyTestResponse(BaseModel):
    """Response for accuracy test creation."""

    test_id: int
    status: str
    message: str


@router.post("/tests", response_model=AccuracyTestResponse)
async def create_accuracy_test(
    request: CreateAccuracyTestRequest, db=Depends(get_db_session)
) -> AccuracyTestResponse:
    """Create a new accuracy test."""
    try:
        service = AccuracyMeasurementService(db)

        test_id = await service.create_accuracy_test(
            tenant_id=1,  # Default tenant for testing
            name=request.name,
            scenario=request.scenario,
            test_data_source=request.test_data_source,
            description=request.description,
            category_schema_id=request.category_schema_id,
            sample_size=request.sample_size,
        )

        return AccuracyTestResponse(
            test_id=test_id,
            status="created",
            message=f"Accuracy test {test_id} created successfully",
        )

    except Exception as e:
        logger.error(f"Failed to create accuracy test: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create accuracy test: {str(e)}",
        )


@router.post("/tests/{test_id}/run")
async def run_accuracy_test(test_id: int, db=Depends(get_db_session)) -> Dict:
    """Execute an accuracy test."""
    try:
        service = AccuracyMeasurementService(db)

        result = await service.run_accuracy_test(test_id, tenant_id=1)

        return {"test_id": test_id, "status": "completed", "results": result}

    except Exception as e:
        logger.error(f"Failed to run accuracy test {test_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to run accuracy test: {str(e)}",
        )


@router.get("/tests", response_model=List[AccuracyTestSummary])
async def list_accuracy_tests(
    scenario: Optional[AccuracyTestScenario] = None,
    status_filter: Optional[AccuracyTestStatus] = None,
    limit: int = 50,
    offset: int = 0,
    db=Depends(get_db_session),
) -> List[AccuracyTestSummary]:
    """List accuracy tests with optional filtering."""
    try:
        service = AccuracyMeasurementService(db)

        tests = await service.list_accuracy_tests(
            tenant_id=1,
            limit=limit,
            offset=offset,
            scenario=scenario,
            status=status_filter,
        )

        return tests

    except Exception as e:
        logger.error(f"Failed to list accuracy tests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list accuracy tests: {str(e)}",
        )


@router.get("/tests/{test_id}")
async def get_accuracy_test(test_id: int, db=Depends(get_db_session)) -> Dict:
    """Get accuracy test details."""
    try:
        service = AccuracyMeasurementService(db)

        test = await service.get_accuracy_test(test_id, tenant_id=1)

        if not test:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Accuracy test {test_id} not found",
            )

        return test.dict()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get accuracy test {test_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get accuracy test: {str(e)}",
        )


@router.post("/quick-test")
async def run_quick_accuracy_test(db=Depends(get_db_session)) -> Dict:
    """Run quick accuracy test on Nuvie data with all three scenarios."""
    try:
        service = AccuracyMeasurementService(db)

        # Test data path
        test_data_path = "/Users/<USER>/giki-ai-workspace/test_data/Nuvie_Copy of Expense Ledger_v1.xlsx"

        results = {}

        # Scenario 1: Historical Data
        test_id_1 = await service.create_accuracy_test(
            tenant_id=1,
            name="Nuvie Historical Data Test",
            scenario=AccuracyTestScenario.HISTORICAL_DATA,
            test_data_source=test_data_path,
            description="Historical data RAG-based test on Nuvie dataset",
            sample_size=50,  # Smaller sample for quick test
        )
        results["historical_data"] = await service.run_accuracy_test(
            test_id_1, tenant_id=1
        )

        # Scenario 2: Schema Only
        test_id_2 = await service.create_accuracy_test(
            tenant_id=1,
            name="Nuvie Schema Only Test",
            scenario=AccuracyTestScenario.SCHEMA_ONLY,
            test_data_source=test_data_path,
            description="Schema-only test on Nuvie dataset",
            sample_size=50,
        )
        results["schema_only"] = await service.run_accuracy_test(test_id_2, tenant_id=1)

        # Scenario 3: Zero Onboarding
        test_id_3 = await service.create_accuracy_test(
            tenant_id=1,
            name="Nuvie Zero Onboarding Test",
            scenario=AccuracyTestScenario.ZERO_ONBOARDING,
            test_data_source=test_data_path,
            description="Zero onboarding pure AI test on Nuvie dataset",
            sample_size=50,
        )
        results["zero_onboarding"] = await service.run_accuracy_test(
            test_id_3, tenant_id=1
        )

        return {
            "status": "completed",
            "message": "All three scenarios tested successfully",
            "results": results,
        }

    except Exception as e:
        logger.error(f"Failed to run quick accuracy test: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to run quick accuracy test: {str(e)}",
        )
