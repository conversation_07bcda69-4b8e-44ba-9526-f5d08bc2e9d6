"""
Pydantic schemas for file processing reports.

These schemas define the API request/response models for detailed
file processing reports during onboarding.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class ProcessingStatus(str, Enum):
    """Processing status values."""

    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class RowStatus(str, Enum):
    """Row processing status values."""

    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


class DataType(str, Enum):
    """Detected data type values."""

    STRING = "string"
    NUMBER = "number"
    DATE = "date"
    BOOLEAN = "boolean"
    MIXED = "mixed"
    UNKNOWN = "unknown"


class MappingMethod(str, Enum):
    """Column mapping method values."""

    AI = "ai"
    PATTERN = "pattern"
    EXACT = "exact"
    MANUAL = "manual"


class ValidationError(BaseModel):
    """Validation error details."""

    field: str = Field(..., description="Field that failed validation")
    error: str = Field(..., description="Error description")
    severity: str = Field("error", description="Error severity: error, warning, info")
    count: Optional[int] = Field(None, description="Number of occurrences")


class DataQualityIssue(BaseModel):
    """Data quality issue details."""

    issue_type: str = Field(..., description="Type of quality issue")
    description: str = Field(..., description="Issue description")
    affected_rows: Optional[List[int]] = Field(None, description="Affected row numbers")
    severity: str = Field("warning", description="Issue severity")


class ColumnMapping(BaseModel):
    """Column mapping details."""

    original_name: str = Field(..., description="Original column name in file")
    mapped_field: str = Field(..., description="Mapped field name")
    confidence: float = Field(..., description="Mapping confidence (0.0 to 1.0)")
    method: MappingMethod = Field(..., description="Mapping method used")


class ValueDistribution(BaseModel):
    """Value distribution for a column."""

    value: str = Field(..., description="The value")
    count: int = Field(..., description="Number of occurrences")
    percentage: float = Field(..., description="Percentage of total")


class ColumnStatisticResponse(BaseModel):
    """Column statistics response."""

    column_name: str = Field(..., description="Column name")
    column_index: int = Field(..., description="Column index in file")
    mapped_field: Optional[str] = Field(None, description="Mapped field name")

    # Statistics
    total_values: int = Field(..., description="Total number of values")
    non_null_values: int = Field(..., description="Number of non-null values")
    null_percentage: float = Field(..., description="Percentage of null values")
    unique_values: int = Field(..., description="Number of unique values")

    # Data type
    detected_type: DataType = Field(..., description="Detected data type")
    type_consistency: float = Field(
        ..., description="Type consistency score (0.0 to 1.0)"
    )

    # Value info
    min_value: Optional[str] = Field(None, description="Minimum value")
    max_value: Optional[str] = Field(None, description="Maximum value")
    average_value: Optional[float] = Field(
        None, description="Average for numeric columns"
    )
    most_common_values: List[ValueDistribution] = Field(
        default_factory=list, description="Most common values"
    )

    # Data quality
    empty_string_count: int = Field(0, description="Count of empty strings")
    invalid_format_count: int = Field(0, description="Count of invalid formats")
    data_quality_score: float = Field(
        ..., description="Data quality score (0.0 to 1.0)"
    )

    # Mapping
    mapping_confidence: Optional[float] = Field(None, description="Mapping confidence")
    mapping_method: Optional[MappingMethod] = Field(None, description="Mapping method")


class RowProcessingDetailResponse(BaseModel):
    """Row processing detail response."""

    row_number: int = Field(..., description="Row number in file")
    status: RowStatus = Field(..., description="Processing status")

    # Parsed data
    parsed_date: Optional[datetime] = Field(None, description="Parsed date")
    parsed_amount: Optional[float] = Field(None, description="Parsed amount")
    parsed_description: Optional[str] = Field(None, description="Parsed description")
    parsed_category: Optional[str] = Field(None, description="Parsed category")
    parsed_vendor: Optional[str] = Field(None, description="Parsed vendor")

    # Validation
    validation_errors: List[ValidationError] = Field(
        default_factory=list, description="Validation errors"
    )
    data_quality_issues: List[str] = Field(
        default_factory=list, description="Data quality issues"
    )

    # Raw data (optional, for debugging)
    raw_data: Optional[Dict[str, Any]] = Field(None, description="Original row data")


class FileProcessingReportResponse(BaseModel):
    """Complete file processing report response."""

    upload_id: UUID = Field(..., description="Upload ID")
    file_name: str = Field(..., description="Original file name")
    status: ProcessingStatus = Field(..., description="Overall processing status")

    # Summary statistics
    total_rows: int = Field(..., description="Total rows in file")
    successful_rows: int = Field(..., description="Successfully processed rows")
    failed_rows: int = Field(..., description="Failed rows")
    skipped_rows: int = Field(..., description="Skipped rows (e.g., headers)")
    success_rate: float = Field(..., description="Success rate percentage")

    # Processing metadata
    processing_started_at: datetime = Field(..., description="Processing start time")
    processing_completed_at: Optional[datetime] = Field(
        None, description="Processing end time"
    )
    processing_duration_seconds: Optional[float] = Field(
        None, description="Processing duration"
    )
    date_format_detected: Optional[str] = Field(
        None, description="Detected date format"
    )

    # Column mapping
    total_columns: int = Field(..., description="Total columns in file")
    mapped_columns: List[ColumnMapping] = Field(
        default_factory=list, description="Successfully mapped columns"
    )
    unmapped_columns: List[str] = Field(
        default_factory=list, description="Unmapped column names"
    )

    # Data quality
    data_quality_score: float = Field(
        ..., description="Overall data quality score (0.0 to 1.0)"
    )
    validation_errors: List[ValidationError] = Field(
        default_factory=list, description="Validation error summary"
    )
    warnings: List[str] = Field(default_factory=list, description="Processing warnings")

    # Schema discovery
    categories_discovered: List[str] = Field(
        default_factory=list, description="Unique categories found"
    )
    category_count: int = Field(0, description="Number of unique categories")
    schema_confidence: Optional[float] = Field(
        None, description="Schema interpretation confidence"
    )

    # Error info
    error_message: Optional[str] = Field(None, description="Error message if failed")

    # Timestamps
    created_at: datetime = Field(..., description="Report creation time")
    updated_at: datetime = Field(..., description="Last update time")


class ProcessingReportSummary(BaseModel):
    """Summary of processing report for listing."""

    upload_id: UUID = Field(..., description="Upload ID")
    file_name: str = Field(..., description="File name")
    status: ProcessingStatus = Field(..., description="Processing status")
    total_rows: int = Field(..., description="Total rows")
    success_rate: float = Field(..., description="Success rate percentage")
    data_quality_score: float = Field(..., description="Data quality score")
    processing_duration_seconds: Optional[float] = Field(
        None, description="Processing duration"
    )
    created_at: datetime = Field(..., description="Creation time")


class RowDetailsRequest(BaseModel):
    """Request for row details with pagination."""

    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(100, ge=1, le=1000, description="Items per page")
    status_filter: Optional[RowStatus] = Field(None, description="Filter by status")
    has_errors: Optional[bool] = Field(None, description="Filter rows with errors")


class RowDetailsResponse(BaseModel):
    """Paginated row details response."""

    rows: List[RowProcessingDetailResponse] = Field(..., description="Row details")
    total_rows: int = Field(..., description="Total matching rows")
    page: int = Field(..., description="Current page")
    page_size: int = Field(..., description="Items per page")
    total_pages: int = Field(..., description="Total pages")


class SchemaMappingReportResponse(BaseModel):
    """Schema mapping report for a tenant."""

    tenant_id: int = Field(..., description="Tenant ID")
    has_schema_discovery: bool = Field(
        ..., description="Whether schema discovery was performed"
    )
    session_id: Optional[str] = Field(None, description="Schema discovery session ID")

    # Discovery results
    schemas_discovered: int = Field(0, description="Number of schemas discovered")
    total_categories: int = Field(
        0, description="Total unique categories across all files"
    )
    unified_categories: int = Field(0, description="Number of unified categories")

    # File-level details
    source_files: List[str] = Field(
        default_factory=list, description="Source file names"
    )
    file_schemas: List[Dict[str, Any]] = Field(
        default_factory=list, description="Schema details per file"
    )

    # Mapping details
    category_mappings: Dict[str, str] = Field(
        default_factory=dict, description="Original to unified mappings"
    )
    reverse_mappings: Dict[str, List[str]] = Field(
        default_factory=dict, description="Unified to original mappings"
    )

    # Quality metrics
    mapping_confidence: float = Field(0.0, description="Overall mapping confidence")
    unmapped_categories: List[str] = Field(
        default_factory=list, description="Categories without mappings"
    )

    # Timestamps
    created_at: Optional[datetime] = Field(None, description="Schema discovery time")
    last_updated: Optional[datetime] = Field(None, description="Last update time")
