"""
Onboarding service for temporal accuracy validation workflow.

Handles the complete customer onboarding process including:
- Historical data ingestion with labels
- RAG corpus creation from customer patterns
- Temporal accuracy validation
- Production approval workflow
"""

import asyncio
import json
import logging
import uuid
from datetime import date, datetime, time, timedelta, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd
from asyncpg import Connection

# Database connections managed by dependency injection
from ...shared.ai.vertex_client import VertexAIClient
from ...shared.exceptions import ServiceError, ValidationError
from ..categories.mis_categorization_service import MISCategorizationService
from ..categories.mis_template_selection_agent import MISTemplateSelectionAgent
from ..files.schema_interpretation_agent import suggest_schema_mapping_tool_function
from ..transactions.models import Transaction, Upload
from .models import (
    OnboardingStatus as OnboardingStatusModel,
    RAGCorpus,
    TemporalValidation,
)
from .processing_models import (
    ColumnStatistic,
    FileProcessingReport,
    RowProcessingDetail,
)
from .schemas import (
    FileColumnMapping,
    MonthlyAccuracyResult,
    OnboardingStatus,
    RAGCorpusStatus,
    TemporalValidationRequest,
    TemporalValidationResult,
)

logger = logging.getLogger(__name__)


class OnboardingService:
    """Service for managing customer onboarding and temporal validation."""

    def __init__(
        self, conn: Connection, vertex_client: Optional[VertexAIClient] = None
    ):
        self.conn = conn
        self.vertex_client = vertex_client
        self._rag_corpus_cache: Dict[int, str] = {}  # tenant_id -> corpus_name

    async def get_onboarding_status(self, tenant_id: int) -> OnboardingStatus:
        """Get current onboarding status for a tenant."""

        # Check if we have an existing onboarding status record
        status_query = """
            SELECT id, tenant_id, onboarding_type, stage, approved_for_production, approved_at, 
                   approved_by_user_id, approval_notes, last_validation_id, last_validation_accuracy,
                   total_transactions, transactions_with_labels, date_range_start, date_range_end,
                   last_activity, created_at, updated_at
            FROM onboarding_status WHERE tenant_id = $1
        """
        status_row = await self.conn.fetchrow(status_query, tenant_id)
        status_record = (
            OnboardingStatusModel(**dict(status_row)) if status_row else None
        )

        # Get transaction counts
        total_count = (
            await self.conn.fetchval(
                "SELECT COUNT(*) FROM transactions WHERE tenant_id = $1", tenant_id
            )
            or 0
        )
        total_transactions = total_count

        # Get transactions with labels
        labeled_count = (
            await self.conn.fetchval(
                "SELECT COUNT(*) FROM transactions WHERE tenant_id = $1 AND original_category IS NOT NULL",
                tenant_id,
            )
            or 0
        )
        transactions_with_labels = labeled_count

        # Get date range
        date_range_row = await self.conn.fetchrow(
            "SELECT MIN(date) as min_date, MAX(date) as max_date FROM transactions WHERE tenant_id = $1",
            tenant_id,
        )
        date_range = (
            (date_range_row["min_date"], date_range_row["max_date"])
            if date_range_row
            else (None, None)
        )

        # Get category count
        (
            await self.conn.fetchval(
                "SELECT COUNT(*) FROM categories WHERE tenant_id = $1", tenant_id
            )
            or 0
        )

        # Check RAG corpus status
        rag_corpus_status = await self._get_rag_corpus_status(tenant_id)

        # Determine onboarding stage
        if total_transactions == 0:
            stage = "not_started"
        elif transactions_with_labels == 0:
            stage = "data_uploaded"
        elif not rag_corpus_status.corpus_exists:
            stage = "corpus_building"
        else:
            stage = "validating"

        # Update or create onboarding status record
        # Use timezone-naive datetime for asyncpg compatibility
        current_time = datetime.utcnow()
        if status_record:
            # Update existing record
            update_query = """
                UPDATE onboarding_status 
                SET stage = $2, total_transactions = $3, transactions_with_labels = $4,
                    date_range_start = $5, date_range_end = $6, last_activity = $7,
                    updated_at = $8
                WHERE tenant_id = $1
                RETURNING *
            """

            # Check validation results first
            if (
                status_record.last_validation_accuracy
                and status_record.last_validation_accuracy >= 85
            ):
                stage = "completed"

            # Convert dates to timezone-naive datetimes for asyncpg compatibility
            date_range_start = None
            date_range_end = None
            if date_range and date_range[0]:
                if isinstance(date_range[0], date) and not isinstance(
                    date_range[0], datetime
                ):
                    date_range_start = datetime.combine(date_range[0], time.min)
                elif isinstance(date_range[0], datetime):
                    date_range_start = (
                        date_range[0].replace(tzinfo=None)
                        if date_range[0].tzinfo
                        else date_range[0]
                    )
                else:
                    date_range_start = date_range[0]
            if date_range and date_range[1]:
                if isinstance(date_range[1], date) and not isinstance(
                    date_range[1], datetime
                ):
                    date_range_end = datetime.combine(date_range[1], time.max)
                elif isinstance(date_range[1], datetime):
                    date_range_end = (
                        date_range[1].replace(tzinfo=None)
                        if date_range[1].tzinfo
                        else date_range[1]
                    )
                else:
                    date_range_end = date_range[1]

            status_row = await self.conn.fetchrow(
                update_query,
                tenant_id,
                stage,
                total_transactions,
                transactions_with_labels,
                date_range_start,
                date_range_end,
                current_time,
                current_time,
            )
            status_record = OnboardingStatusModel(**dict(status_row))
        else:
            # Create new record - preserve onboarding_type if it exists
            # Check if there's an existing record with onboarding_type set but without other fields
            existing_type_query = """
                SELECT onboarding_type FROM onboarding_status WHERE tenant_id = $1
            """
            existing_type_row = await self.conn.fetchrow(existing_type_query, tenant_id)
            existing_onboarding_type = (
                existing_type_row["onboarding_type"]
                if existing_type_row
                else "historical_data"  # Default to historical_data, not zero_onboarding
            )

            insert_query = """
                INSERT INTO onboarding_status (
                    tenant_id, onboarding_type, stage, total_transactions, transactions_with_labels,
                    date_range_start, date_range_end, last_activity, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING *
            """

            # Convert dates to timezone-naive datetimes if they exist
            date_range_start = None
            date_range_end = None
            if date_range and date_range[0]:
                if isinstance(date_range[0], date) and not isinstance(
                    date_range[0], datetime
                ):
                    date_range_start = datetime.combine(date_range[0], time.min)
                elif isinstance(date_range[0], datetime):
                    date_range_start = (
                        date_range[0].replace(tzinfo=None)
                        if date_range[0].tzinfo
                        else date_range[0]
                    )
                else:
                    date_range_start = date_range[0]
            if date_range and date_range[1]:
                if isinstance(date_range[1], date) and not isinstance(
                    date_range[1], datetime
                ):
                    date_range_end = datetime.combine(date_range[1], time.max)
                elif isinstance(date_range[1], datetime):
                    date_range_end = (
                        date_range[1].replace(tzinfo=None)
                        if date_range[1].tzinfo
                        else date_range[1]
                    )
                else:
                    date_range_end = date_range[1]

            status_row = await self.conn.fetchrow(
                insert_query,
                tenant_id,
                existing_onboarding_type,
                stage,
                total_transactions,
                transactions_with_labels,
                date_range_start,
                date_range_end,
                current_time,
                current_time,
                current_time,
            )
            status_record = OnboardingStatusModel(**dict(status_row))

        # Check if approved for production
        approved_for_production = status_record.approved_for_production

        return OnboardingStatus(
            tenant_id=tenant_id,
            onboarding_type=getattr(
                status_record, "onboarding_type", "historical_data"
            ),
            onboarding_stage=stage,
            is_onboarding_complete=approved_for_production,
            last_activity=status_record.last_activity,
            total_transactions=total_transactions,
            transactions_with_labels=transactions_with_labels,
            date_range_start=date_range[0] if date_range and date_range[0] else None,
            date_range_end=date_range[1] if date_range and date_range[1] else None,
            rag_corpus_status=rag_corpus_status,
            approved_for_production=approved_for_production,
        )

    async def initialize_onboarding_record(
        self, tenant_id: int, onboarding_type: str = "historical_data"
    ) -> None:
        """Initialize onboarding record for a new tenant with specified type."""
        try:
            # Check if record already exists
            existing_record = await self.conn.fetchrow(
                "SELECT id, onboarding_type FROM onboarding_status WHERE tenant_id = $1",
                tenant_id,
            )

            if existing_record:
                # Update existing record to the new onboarding type
                logger.info(
                    f"Updating existing onboarding record for tenant {tenant_id} from {existing_record['onboarding_type']} to {onboarding_type}"
                )

                # Determine initial stage based on onboarding type
                initial_stage = "not_started"
                if onboarding_type == "zero_onboarding":
                    initial_stage = "zero_ready"  # M1: Ready for immediate production
                elif onboarding_type == "schema_only":
                    initial_stage = "schema_imported"  # M3: Need schema import first

                current_time_tz = datetime.now(
                    timezone.utc
                )  # For timezone-aware columns
                current_time_naive = datetime.utcnow()  # For timezone-naive columns

                await self.conn.execute(
                    """
                    UPDATE onboarding_status 
                    SET onboarding_type = $1, 
                        stage = $2,
                        approved_for_production = $3,
                        last_activity = $4,
                        updated_at = $5
                    WHERE tenant_id = $6
                    """,
                    onboarding_type,
                    initial_stage,
                    onboarding_type
                    == "zero_onboarding",  # M1 auto-approved for production
                    current_time_tz,  # last_activity is 'timestamp with time zone'
                    current_time_naive,  # updated_at is 'timestamp without time zone'
                    tenant_id,
                )
                return

            # Determine initial stage based on onboarding type
            initial_stage = "not_started"
            if onboarding_type == "zero_onboarding":
                initial_stage = "zero_ready"  # M1: Ready for immediate production
            elif onboarding_type == "schema_only":
                initial_stage = "schema_imported"  # M3: Need schema import first

            # Create new onboarding record
            current_time_tz = datetime.now(timezone.utc)  # For timezone-aware columns
            current_time_naive = datetime.utcnow()  # For timezone-naive columns
            await self.conn.execute(
                """
                INSERT INTO onboarding_status (
                    tenant_id, onboarding_type, stage, approved_for_production, 
                    total_transactions, transactions_with_labels,
                    last_activity, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """,
                tenant_id,
                onboarding_type,
                initial_stage,
                onboarding_type == "zero_onboarding",  # M1 auto-approved for production
                0,
                0,
                current_time_tz,  # last_activity is 'timestamp with time zone'
                current_time_naive,  # created_at is 'timestamp without time zone'
                current_time_naive,  # updated_at is 'timestamp without time zone'
            )

            logger.info(
                f"Initialized {onboarding_type} onboarding record for tenant {tenant_id} with stage {initial_stage}"
            )

        except Exception as e:
            logger.error(
                f"Failed to initialize onboarding record for tenant {tenant_id}: {e}"
            )
            raise

    async def store_business_context(
        self,
        tenant_id: int,
        industry: Optional[str] = None,
        company_size: Optional[str] = None,
        company_website: Optional[str] = None,
    ) -> None:
        """
        Store business context in tenants.settings JSONB field for AI categorization.

        Args:
            tenant_id: Tenant ID
            industry: Business industry
            company_size: Company size (startup, small, medium, enterprise)
            company_website: Company website URL
        """
        try:
            # Build business context JSON
            business_context = {}
            if industry:
                business_context["industry"] = industry
            if company_size:
                business_context["size"] = (
                    company_size  # Use 'size' to match categorization agent
                )
            if company_website:
                business_context["companyWebsite"] = company_website

            if business_context:
                # Find the corresponding tenants table ID (tenant.id + 3 mapping pattern)
                tenants_id = tenant_id + 3

                # Update or insert business context in tenants.settings
                await self.conn.execute(
                    """
                    INSERT INTO tenants (id, settings) 
                    VALUES ($1, jsonb_build_object('business_context', $2::jsonb))
                    ON CONFLICT (id) 
                    DO UPDATE SET settings = COALESCE(tenants.settings, '{}'::jsonb) || jsonb_build_object('business_context', $2::jsonb)
                    """,
                    tenants_id,
                    json.dumps(business_context),
                )

                logger.info(
                    f"Stored business context for tenant {tenant_id}: {business_context}"
                )

        except Exception as e:
            logger.error(
                f"Failed to store business context for tenant {tenant_id}: {e}"
            )
            # Don't raise - business context storage failure shouldn't block onboarding

    async def apply_mis_template_for_tenant(
        self,
        tenant_id: int,
        company_name: str,
        company_website: Optional[str] = None,
        business_description: Optional[str] = None,
        company_size: Optional[str] = None,
        force_industry: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Apply MIS category template during onboarding based on business context.

        This method:
        1. Uses AI to analyze business context and select appropriate industry
        2. Applies the selected MIS template to create Income/Expense structure
        3. Validates the template application

        Args:
            tenant_id: Tenant ID
            company_name: Company name for analysis
            company_website: Company website for industry detection
            business_description: User-provided business description
            company_size: Company size category
            force_industry: Force specific industry template (bypasses AI selection)

        Returns:
            Dict with template application results
        """
        try:
            if force_industry:
                # Skip AI selection if industry is forced
                selected_industry = force_industry
                confidence = 1.0
                reasoning = f"Industry template forced by user: {force_industry}"
            else:
                # Use AI to select appropriate industry template
                selection_agent = MISTemplateSelectionAgent(
                    tenant_id=tenant_id, vertex_client=self.vertex_client
                )

                # Get sample transactions if available for better analysis
                sample_txns_query = """
                    SELECT description, amount, date, vendor
                    FROM transactions
                    WHERE tenant_id = $1
                    LIMIT 20
                """
                sample_txns = await self.conn.fetch(sample_txns_query, tenant_id)
                sample_transactions = (
                    [dict(row) for row in sample_txns] if sample_txns else None
                )

                # Analyze business context
                selection_result = await selection_agent.analyze_business_context(
                    company_name=company_name,
                    company_website=company_website,
                    business_description=business_description,
                    company_size=company_size,
                    sample_transactions=sample_transactions,
                )

                selected_industry = selection_result.industry
                confidence = selection_result.confidence
                reasoning = selection_result.reasoning

                logger.info(
                    f"AI selected {selected_industry} template for {company_name} "
                    f"with {confidence:.1%} confidence. Reason: {reasoning}"
                )

            # Apply the selected template using MISCategorizationService
            mis_service = MISCategorizationService(self.conn)
            template_result = await mis_service.apply_industry_template(
                tenant_id=tenant_id, industry=selected_industry, customize=True
            )

            # Store template selection in tenant settings
            await self.conn.execute(
                """
                UPDATE tenants
                SET settings = COALESCE(settings, '{}'::jsonb) || 
                    jsonb_build_object(
                        'mis_template', $2::jsonb,
                        'template_applied_at', $3
                    )
                WHERE id = $1
                """,
                tenant_id,  # Fixed: use tenant_id directly
                json.dumps(
                    {
                        "industry": selected_industry,
                        "confidence": confidence,
                        "reasoning": reasoning,
                        "applied_at": datetime.utcnow().isoformat(),
                    }
                ),
                datetime.utcnow().isoformat(),
            )

            logger.info(
                f"MIS template '{selected_industry}' applied successfully for tenant {tenant_id}. "
                f"Created {template_result['categories_created']} categories."
            )

            return {
                "success": True,
                "industry": selected_industry,
                "confidence": confidence,
                "reasoning": reasoning,
                "template_result": template_result,
            }

        except Exception as e:
            logger.error(f"Failed to apply MIS template for tenant {tenant_id}: {e}")
            # Return partial success - don't block onboarding
            return {
                "success": False,
                "error": str(e),
                "industry": "General Business",
                "confidence": 0.0,
                "reasoning": "Failed to apply template, using default",
            }

    async def initialize_zero_onboarding(self, tenant_id: int) -> None:
        """Initialize M1 zero-onboarding process - immediate production ready."""
        try:
            # Check if record already exists
            existing_record = await self.conn.fetchrow(
                "SELECT id FROM onboarding_status WHERE tenant_id = $1", tenant_id
            )

            current_time_tz = datetime.now(timezone.utc)  # For timezone-aware columns
            current_time_naive = datetime.utcnow()  # For timezone-naive columns

            if existing_record:
                # Update existing record to zero-onboarding mode
                await self.conn.execute(
                    """
                    UPDATE onboarding_status 
                    SET onboarding_type = $1, 
                        stage = $2, 
                        approved_for_production = $3,
                        last_activity = $4,
                        updated_at = $5
                    WHERE tenant_id = $6
                    """,
                    "zero_onboarding",  # onboarding_type
                    "zero_ready",  # stage - ready for immediate production
                    True,  # approved_for_production
                    current_time_tz,  # last_activity is 'timestamp with time zone'
                    current_time_naive,  # updated_at is 'timestamp without time zone'
                    tenant_id,
                )
                logger.info(
                    f"Updated existing onboarding record to M1 zero-onboarding for tenant {tenant_id}"
                )
            else:
                # Create new onboarding record
                await self.conn.execute(
                    """
                    INSERT INTO onboarding_status (
                        tenant_id, onboarding_type, stage, approved_for_production, 
                        total_transactions, transactions_with_labels,
                        last_activity, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    """,
                    tenant_id,
                    "zero_onboarding",  # onboarding_type
                    "zero_ready",  # stage - ready for immediate production
                    True,  # approved_for_production - M1 auto-approved
                    0,  # total_transactions
                    0,  # transactions_with_labels
                    current_time_tz,  # last_activity is 'timestamp with time zone'
                    current_time_naive,  # created_at is 'timestamp without time zone'
                    current_time_naive,  # updated_at is 'timestamp without time zone'
                )
                logger.info(
                    f"Created new M1 zero-onboarding record for tenant {tenant_id}"
                )

        except Exception as e:
            logger.error(
                f"Failed to initialize zero-onboarding for tenant {tenant_id}: {e}"
            )
            raise

        logger.info(f"M1 zero-onboarding initialized for tenant {tenant_id}")

    async def initialize_schema_only_onboarding(self, tenant_id: int) -> None:
        """Initialize M3 schema-only onboarding process."""
        await self.initialize_onboarding_record(tenant_id, "schema_only")
        logger.info(f"M3 schema-only onboarding initialized for tenant {tenant_id}")

    async def import_category_schema(
        self, tenant_id: int, schema_file_path: str
    ) -> None:
        """Import category hierarchy for M3 schema-only onboarding."""
        try:
            # This would implement category hierarchy import
            # For now, just update the stage to schema_ready
            current_time_tz = datetime.now(timezone.utc)  # For timezone-aware columns
            current_time_naive = datetime.utcnow()  # For timezone-naive columns
            await self.conn.execute(
                """
                UPDATE onboarding_status 
                SET stage = 'schema_ready', 
                    approved_for_production = true,
                    last_activity = $1,
                    updated_at = $2
                WHERE tenant_id = $3
                """,
                current_time_tz,  # last_activity is 'timestamp with time zone'
                current_time_naive,  # updated_at is 'timestamp without time zone'
                tenant_id,
            )
            logger.info(f"Category schema imported for tenant {tenant_id}")
        except Exception as e:
            logger.error(
                f"Failed to import category schema for tenant {tenant_id}: {e}"
            )
            raise

    async def interpret_file_columns(
        self, file_path: str, sample_rows: int = 5
    ) -> FileColumnMapping:
        """Use AI to interpret column mappings from uploaded file."""

        try:
            # Use the unified AI interpretation service
            from ..files.ai_interpretation_service import get_ai_interpretation_service

            logger.info(
                f"Using unified AI interpretation service for file: {file_path}"
            )
            ai_service = get_ai_interpretation_service()

            # Get AI interpretation
            interpretation = await ai_service.interpret_file(
                file_path=file_path,
                upload_id=None,  # No upload ID yet in onboarding flow
                tenant_id=None,  # Will be set later
            )

            # Convert to FileColumnMapping for backward compatibility
            file_column_mapping = ai_service.convert_to_file_column_mapping(
                interpretation
            )

            logger.info(
                f"AI interpretation completed with {interpretation.overall_confidence:.1%} confidence"
            )

            # Log any issues or warnings
            validation = await ai_service.validate_interpretation(interpretation)
            if validation["warnings"]:
                logger.warning(f"AI interpretation warnings: {validation['warnings']}")
            if not validation["valid"]:
                logger.error(f"AI interpretation issues: {validation['issues']}")
                raise ServiceError(
                    f"AI interpretation incomplete: {', '.join(validation['issues'])}"
                )

            return file_column_mapping

        except Exception as e:
            logger.error(f"Column interpretation failed: {e}")
            raise ServiceError(
                f"AI column interpretation failed: {e}. Please check your file format and try again."
            )

    async def process_uploaded_file_with_reporting(
        self,
        tenant_id: int,
        file_path: str,
        column_mapping: FileColumnMapping,
        has_category_labels: bool = True,
    ) -> tuple[int, str, str]:
        """
        Enhanced file processing with detailed row-by-row and column reporting.

        This method provides comprehensive tracking of file processing including:
        - Row-by-row processing details
        - Column statistics and data quality metrics
        - Schema discovery integration
        - Detailed error and warning tracking
        - Multi-sheet processing for Excel files

        Returns:
            Tuple of (transaction_count, processing_report_id, upload_id)
        """
        processing_start = datetime.utcnow()
        report_id = str(uuid.uuid4())

        # Initialize processing report (without upload_id for now)
        report = FileProcessingReport(
            id=report_id,
            tenant_id=tenant_id,
            file_name=file_path.split("/")[-1],
            status="processing",
            processing_started_at=processing_start,
            total_rows=0,
            total_columns=0,
            successful_rows=0,
            failed_rows=0,
            skipped_rows=0,
        )

        try:
            # Read the file - handle multiple sheets for Excel files
            all_dfs = []
            sheet_names = []

            if file_path.endswith(".csv"):
                df = pd.read_csv(file_path)
                # ENHANCED: Use robust column name cleaning to handle all edge cases
                df.columns = [
                    self._clean_column_name(col, idx)
                    for idx, col in enumerate(df.columns)
                ]
                all_dfs.append(df)
                sheet_names.append("CSV")
            else:
                # Read all sheets from Excel file
                excel_file = pd.ExcelFile(file_path)
                for sheet_name in excel_file.sheet_names:
                    # First, try to detect the header row by reading a small sample
                    sample_df = pd.read_excel(
                        excel_file, sheet_name=sheet_name, nrows=10
                    )

                    # Detect header row based on data type patterns
                    header_row = 0
                    for i in range(min(5, len(sample_df))):
                        row_values = sample_df.iloc[i].values
                        # Check if this row looks like headers (mostly strings, no numbers)
                        non_null_values = [v for v in row_values if pd.notna(v)]
                        if non_null_values:
                            string_count = sum(
                                1
                                for v in non_null_values
                                if isinstance(v, str)
                                and not str(v)
                                .replace(".", "")
                                .replace("-", "")
                                .isdigit()
                            )
                            if (
                                string_count >= len(non_null_values) * 0.7
                            ):  # 70% or more are non-numeric strings
                                header_row = i
                                break

                    # Now read the full dataframe with the detected header row
                    df = pd.read_excel(
                        excel_file, sheet_name=sheet_name, header=header_row
                    )

                    if not df.empty:  # Skip empty sheets
                        # ENHANCED: Use robust column name cleaning for numeric headers and edge cases
                        # This handles float column names, None values, and other problematic types
                        df.columns = [
                            self._clean_column_name(col, idx)
                            for idx, col in enumerate(df.columns)
                        ]
                        all_dfs.append(df)
                        sheet_names.append(sheet_name)
                        logger.info(
                            f"Processing sheet '{sheet_name}' with {len(df)} rows, columns: {list(df.columns)}"
                        )

            # Process all sheets
            total_transaction_count = 0
            all_categories_discovered = set()

            # Calculate total rows across all sheets
            report.total_rows = sum(len(df) for df in all_dfs)
            report.total_columns = (
                max(len(df.columns) for df in all_dfs) if all_dfs else 0
            )
            report.file_size_bytes = Path(file_path).stat().st_size

            # Create upload record with total size
            upload_id = str(uuid.uuid4())
            current_time = datetime.utcnow()

            upload_query = """
                INSERT INTO uploads (
                    id, filename, content_type, file_path, size, status, 
                    processing_status, tenant_id, column_mapping, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                RETURNING *
            """

            upload_row = await self.conn.fetchrow(
                upload_query,
                upload_id,
                report.file_name,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                file_path,
                report.total_rows,  # Total rows from all sheets
                "processing",
                "pending",  # processing_status
                tenant_id,
                json.dumps(column_mapping.model_dump()) if column_mapping else None,
                current_time,
                current_time,
            )
            upload = Upload(**dict(upload_row))

            # Create processing report
            report.upload_id = upload.id
            report_query = """
                INSERT INTO file_processing_reports (
                    id, tenant_id, upload_id, file_name, status, processing_started_at,
                    total_rows, total_columns, successful_rows, failed_rows, skipped_rows,
                    file_size_bytes, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            """

            await self.conn.execute(
                report_query,
                report.id,
                report.tenant_id,
                report.upload_id,
                report.file_name,
                report.status,
                report.processing_started_at,
                report.total_rows,
                report.total_columns,
                report.successful_rows,
                report.failed_rows,
                report.skipped_rows,
                report.file_size_bytes,
                current_time,
                current_time,
            )

            # Process each sheet
            all_column_stats = []
            all_validation_errors = []
            sheet_transaction_counts = []

            for sheet_idx, (df, sheet_name) in enumerate(
                zip(all_dfs, sheet_names, strict=False)
            ):
                logger.info(
                    f"Processing sheet {sheet_idx + 1}/{len(all_dfs)}: '{sheet_name}' with {len(df)} rows"
                )

                # Analyze columns for this sheet
                column_stats = await self._analyze_columns(
                    df, report_id, column_mapping
                )
                all_column_stats.extend(column_stats)

                # Parse dates with smart format detection
                if column_mapping.date_column in df.columns:
                    date_series = df[column_mapping.date_column]
                    (
                        parsed_dates,
                        date_format,
                    ) = await self._parse_dates_with_format_detection(date_series)
                    df[column_mapping.date_column] = parsed_dates
                    if sheet_idx == 0:  # Store format from first sheet
                        report.date_format_detected = date_format

                # Process amount column
                if "Credit" in df.columns and "Debit" in df.columns:
                    df["amount"] = df["Credit"].fillna(0) - df["Debit"].fillna(0)
                elif column_mapping.amount_column in df.columns:
                    df["amount"] = pd.to_numeric(
                        df[column_mapping.amount_column], errors="coerce"
                    )

                # Process rows with detailed tracking using parallel processing
                sheet_transaction_count = 0
                categories_discovered = set()
                row_details = []

                # Calculate row offset for this sheet
                row_offset = sum(sheet_transaction_counts)

                # Process rows in parallel batches for improved performance
                PARALLEL_BATCH_SIZE = 50  # Process 50 rows in parallel
                rows_to_process = []

                for idx, row in df.iterrows():
                    # Global row number across all sheets
                    global_row_num = row_offset + idx + 1
                    rows_to_process.append((row, global_row_num))

                # Process in parallel batches
                for i in range(0, len(rows_to_process), PARALLEL_BATCH_SIZE):
                    batch = rows_to_process[i : i + PARALLEL_BATCH_SIZE]

                    # Create tasks for parallel processing
                    tasks = [
                        self._process_single_row(
                            row,
                            global_row_num,
                            column_mapping,
                            tenant_id,
                            upload.id,
                            has_category_labels,
                            report_id,
                        )
                        for row, global_row_num in batch
                    ]

                    # Execute batch in parallel
                    batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                    # Process results
                    for row_detail in batch_results:
                        if isinstance(row_detail, Exception):
                            logger.error(f"Row processing error: {row_detail}")
                            continue

                        if row_detail.status == "success":
                            report.successful_rows += 1
                            sheet_transaction_count += 1

                            # Track discovered categories
                            if row_detail.parsed_category:
                                categories_discovered.add(row_detail.parsed_category)
                                all_categories_discovered.add(
                                    row_detail.parsed_category
                                )
                        elif row_detail.status == "failed":
                            report.failed_rows += 1
                        else:
                            report.skipped_rows += 1

                        row_details.append(row_detail)

                        # Batch save row details every 100 rows
                        if len(row_details) >= 100:
                            await self._batch_insert_row_details(row_details)
                            row_details = []

                # Save remaining row details for this sheet
                if row_details:
                    await self._batch_insert_row_details(row_details)

                sheet_transaction_counts.append(sheet_transaction_count)
                total_transaction_count += sheet_transaction_count

                logger.info(
                    f"Completed sheet '{sheet_name}': {sheet_transaction_count} transactions, {len(categories_discovered)} categories"
                )

            # Save all column statistics
            if all_column_stats:
                await self._batch_insert_column_stats(all_column_stats)

            # Calculate data quality score
            report.data_quality_score = self._calculate_data_quality_score(
                report, all_column_stats
            )

            # Update report with final statistics
            report.categories_discovered = list(all_categories_discovered)
            report.category_count = len(all_categories_discovered)
            report.validation_errors = all_validation_errors
            report.status = "completed"
            report.processing_completed_at = datetime.utcnow()
            report.processing_duration_seconds = (
                report.processing_completed_at - report.processing_started_at
            ).total_seconds()

            # Update upload status and final report
            await self.conn.execute(
                "UPDATE uploads SET status = $1, updated_at = $2 WHERE id = $3",
                "processed",
                datetime.utcnow(),
                upload.id,
            )

            # Update final report
            await self.conn.execute(
                """
                UPDATE file_processing_reports 
                SET categories_discovered = $1, category_count = $2, validation_errors = $3,
                    status = $4, processing_completed_at = $5, processing_duration_seconds = $6,
                    data_quality_score = $7, updated_at = $8
                WHERE id = $9
                """,
                json.dumps(report.categories_discovered),
                report.category_count,
                json.dumps(report.validation_errors),
                report.status,
                report.processing_completed_at,
                report.processing_duration_seconds,
                report.data_quality_score,
                datetime.utcnow(),
                report.id,
            )

            # Trigger schema discovery if categories were found
            # Use combined data from all sheets for schema discovery
            if all_categories_discovered and has_category_labels and all_dfs:
                # Concatenate all dataframes for schema discovery
                combined_df = pd.concat(all_dfs, ignore_index=True)
                await self._trigger_schema_discovery_for_file(
                    tenant_id, file_path, combined_df, column_mapping, report_id
                )

            logger.info(
                f"Processed {total_transaction_count} transactions across {len(all_dfs)} sheets with detailed reporting for tenant {tenant_id}"
            )
            return total_transaction_count, report_id, upload_id

        except Exception as e:
            logger.error(f"File processing with reporting failed: {e}")
            # Update failed report
            report.status = "failed"
            report.error_message = str(e)
            report.processing_completed_at = datetime.utcnow()
            report.processing_duration_seconds = (
                report.processing_completed_at - report.processing_started_at
            ).total_seconds()

            await self.conn.execute(
                """
                UPDATE file_processing_reports 
                SET status = $1, error_message = $2, processing_completed_at = $3,
                    processing_duration_seconds = $4, updated_at = $5
                WHERE id = $6
                """,
                report.status,
                report.error_message,
                report.processing_completed_at,
                report.processing_duration_seconds,
                datetime.utcnow(),
                report.id,
            )

            raise ServiceError(
                f"Failed to process file with reporting: {str(e)}",
                service_name="OnboardingService",
                operation="process_uploaded_file_with_reporting",
            )

    async def _analyze_columns(
        self, df: pd.DataFrame, report_id: str, column_mapping: FileColumnMapping
    ) -> List[ColumnStatistic]:
        """Analyze columns and create statistics."""
        column_stats = []

        for col_idx, col_name in enumerate(df.columns):
            # ROBUST: Enhanced column name cleaning to handle all edge cases
            # Handle float column names, None values, and other problematic types
            try:
                clean_col_name = self._clean_column_name(col_name, col_idx)

                # Calculate statistics with proper NaN handling
                series = df[col_name]
                total_values = len(series)
                non_null_count = int(series.notna().sum()) if total_values > 0 else 0
                null_count = int(series.isna().sum()) if total_values > 0 else 0
                unique_count = int(series.nunique()) if total_values > 0 else 0

                stat = ColumnStatistic(
                    id=str(uuid.uuid4()),
                    report_id=report_id,
                    column_name=clean_col_name,
                    column_index=col_idx,
                    total_values=total_values,
                    non_null_values=non_null_count,
                    null_values=null_count,
                    unique_values=unique_count,
                )
            except Exception as e:
                logger.error(
                    f"Failed to create ColumnStatistic for column {col_name} (type: {type(col_name)}): {e}"
                )
                # Create a fallback with safe column name and zero statistics
                clean_col_name = self._clean_column_name(
                    col_name, col_idx, fallback=True
                )
                stat = ColumnStatistic(
                    id=str(uuid.uuid4()),
                    report_id=report_id,
                    column_name=clean_col_name,
                    column_index=col_idx,
                    total_values=len(df),
                    non_null_values=0,
                    null_values=len(df),
                    unique_values=0,
                )

            # Determine mapped field
            if col_name == column_mapping.date_column:
                stat.mapped_field = "date"
                stat.mapping_confidence = 0.95
            elif col_name == column_mapping.description_column:
                stat.mapped_field = "description"
                stat.mapping_confidence = 0.95
            elif col_name == column_mapping.amount_column:
                stat.mapped_field = "amount"
                stat.mapping_confidence = 0.95
            elif col_name == column_mapping.category_column:
                stat.mapped_field = "category"
                stat.mapping_confidence = 0.90
            elif col_name == column_mapping.vendor_column:
                stat.mapped_field = "vendor"
                stat.mapping_confidence = 0.85

            # Detect data type
            stat.detected_type = self._detect_column_type(df[col_name])
            stat.type_consistency = self._calculate_type_consistency(df[col_name])

            # Calculate statistics based on type with robust NaN handling
            if stat.detected_type == "number":
                numeric_series = pd.to_numeric(df[col_name], errors="coerce")
                # Enhanced NaN handling for all statistical calculations
                if (
                    numeric_series.notna().sum() > 0
                ):  # Only calculate if we have valid numeric data
                    min_val = numeric_series.min()
                    max_val = numeric_series.max()
                    mean_val = numeric_series.mean()

                    # Robust string conversion with NaN handling
                    stat.min_value = None if pd.isna(min_val) else str(min_val)
                    stat.max_value = None if pd.isna(max_val) else str(max_val)
                    stat.average_value = None if pd.isna(mean_val) else float(mean_val)
                else:
                    # No valid numeric data
                    stat.min_value = None
                    stat.max_value = None
                    stat.average_value = None

            elif stat.detected_type == "date":
                # Enhanced date statistics with proper NaN handling
                date_series = pd.to_datetime(df[col_name], errors="coerce")
                if date_series.notna().sum() > 0:
                    min_date = date_series.min()
                    max_date = date_series.max()
                    stat.min_value = (
                        None if pd.isna(min_date) else min_date.strftime("%Y-%m-%d")
                    )
                    stat.max_value = (
                        None if pd.isna(max_date) else max_date.strftime("%Y-%m-%d")
                    )
                else:
                    stat.min_value = None
                    stat.max_value = None

            elif stat.detected_type == "string":
                # String statistics with NaN-safe operations - prevent NaN->nan conversion
                string_series = df[col_name].fillna("").astype(str)
                non_null_strings = string_series[
                    string_series != ""
                ]  # Filter out empty strings (converted from NaN)
                if len(non_null_strings) > 0:
                    # Calculate string lengths safely
                    lengths = non_null_strings.str.len()
                    stat.min_value = str(lengths.min()) if not lengths.empty else None
                    stat.max_value = str(lengths.max()) if not lengths.empty else None
                else:
                    stat.min_value = None
                    stat.max_value = None

            column_stats.append(stat)

        return column_stats

    async def _parse_dates_with_format_detection(
        self, date_series: pd.Series
    ) -> tuple[pd.Series, str]:
        """
        Parse dates with smart format detection and AI assistance.

        First tries common formats, then uses AI to parse unique dates if needed.
        This ensures we handle ANY date format humans might use.
        """
        # First attempt: Try common date formats
        date_formats = [
            "%m/%d/%Y",  # MM/DD/YYYY (US format, 4-digit year)
            "%d/%m/%Y",  # DD/MM/YYYY (European/Indian format)
            "%m/%d/%y",  # MM/DD/YY (US format, 2-digit year)
            "%d/%m/%y",  # DD/MM/YY (European format, 2-digit year)
            "%Y-%m-%d",  # ISO format
            "%d-%b-%Y",  # DD-MMM-YYYY
            "%d-%b-%y",  # DD-MMM-YY
            "%Y/%m/%d",  # YYYY/MM/DD
            "%d.%m.%Y",  # DD.MM.YYYY (German format)
            "%d.%m.%y",  # DD.MM.YY
        ]

        best_success_rate = 0.0

        # Try each format looking for 100% success
        for fmt in date_formats:
            try:
                parsed = pd.to_datetime(date_series, format=fmt, errors="coerce")
                success_rate = (
                    parsed.notna().sum() / len(date_series)
                    if len(date_series) > 0
                    else 0
                )
                if success_rate > best_success_rate:
                    best_success_rate = success_rate
                # We need 100% success - even one failure means we use AI
                if success_rate == 1.0:
                    logger.info(f"Successfully parsed 100% of dates with format: {fmt}")
                    return parsed, fmt
            except Exception:
                continue

        # If we didn't achieve 100% success, use AI
        # Even a single unparsed date means we need AI assistance
        logger.warning(
            f"Standard parsing achieved only {best_success_rate * 100:.1f}% success. Using AI for date parsing since we need 100% accuracy."
        )

        # Extract unique date values (as strings) for AI parsing
        unique_dates = date_series.dropna().astype(str).unique()

        if len(unique_dates) == 0:
            # No dates to parse
            return pd.Series(
                [pd.NaT] * len(date_series), index=date_series.index
            ), "no_dates"

        # Use AI to parse unique dates
        ai_parsed_mapping = await self._parse_dates_with_ai(list(unique_dates))

        # Apply the AI parsing results to the entire series
        parsed_series = date_series.copy()
        for i, val in enumerate(date_series):
            if pd.notna(val):
                str_val = str(val)
                if str_val in ai_parsed_mapping:
                    parsed_series.iloc[i] = ai_parsed_mapping[str_val]
                else:
                    parsed_series.iloc[i] = pd.NaT
            else:
                parsed_series.iloc[i] = pd.NaT

        # Convert to datetime series
        parsed_series = pd.to_datetime(parsed_series, errors="coerce")

        # Calculate AI success rate
        ai_success_rate = (
            parsed_series.notna().sum() / len(date_series)
            if len(date_series) > 0
            else 0
        )
        logger.info(
            f"AI date parsing achieved {ai_success_rate * 100:.1f}% success rate"
        )

        return parsed_series, "ai_parsed"

    async def _parse_dates_with_ai(
        self, unique_dates: List[str]
    ) -> Dict[str, datetime]:
        """
        Use AI to intelligently parse a list of unique date strings.

        This preserves privacy since only dates are sent, with no context about
        what the dates represent.

        Args:
            unique_dates: List of unique date strings to parse

        Returns:
            Dictionary mapping original date strings to parsed datetime objects
        """
        if not self.vertex_client:
            logger.warning("Vertex AI client not available for date parsing")
            return {}

        # Limit the number of dates to avoid token limits
        if len(unique_dates) > 100:
            logger.info(
                f"Limiting AI date parsing to first 100 unique dates (out of {len(unique_dates)})"
            )
            unique_dates = unique_dates[:100]

        try:
            import json

            from vertexai.generative_models import GenerativeModel

            model = GenerativeModel("gemini-2.0-flash-001")

            # Create a prompt that asks the AI to parse all date formats
            prompt = f"""You are an expert at parsing dates in ANY format that humans might use.

Analyze these date strings and convert each to a standardized format (YYYY-MM-DD).
Handle any format including but not limited to:
- MM/DD/YY or MM/DD/YYYY
- DD/MM/YY or DD/MM/YYYY  
- Month names (Jan, January, etc.)
- Various separators (/, -, ., space)
- Ambiguous dates (use best judgment based on patterns)
- 2-digit years (assume 00-29 is 2000-2029, 30-99 is 1930-1999)

Date strings to parse:
{json.dumps(unique_dates, indent=2)}

Return a JSON object where keys are the original date strings and values are the parsed dates in YYYY-MM-DD format.
If a date cannot be parsed, set its value to null.

Example response format:
{{
  "12/31/24": "2024-12-31",
  "01-Jan-2023": "2023-01-01",
  "invalid date": null
}}

JSON response:"""

            response = await model.generate_content_async(
                prompt,
                generation_config={
                    "temperature": 0.1,  # Low temperature for consistency
                    "max_output_tokens": 4000,
                },
            )

            # Parse the AI response
            try:
                ai_result = json.loads(response.text.strip())
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Failed to parse AI response as JSON: {e}. Response: {response.text.strip()}")
                ai_result = {}

            # Handle case where AI returns null or invalid response
            if not ai_result or not isinstance(ai_result, dict):
                logger.warning(f"AI returned invalid or null response: {ai_result}")
                ai_result = {}

            # Convert parsed dates to datetime objects
            parsed_mapping = {}
            for original, parsed in ai_result.items():
                if parsed:
                    try:
                        # Parse the standardized date
                        dt = pd.to_datetime(parsed, format="%Y-%m-%d")
                        parsed_mapping[original] = dt
                    except Exception as e:
                        logger.warning(
                            f"Failed to parse AI result '{parsed}' for date '{original}': {e}"
                        )
                        parsed_mapping[original] = pd.NaT
                else:
                    parsed_mapping[original] = pd.NaT

            # Log results
            success_count = sum(1 for v in parsed_mapping.values() if pd.notna(v))
            logger.info(
                f"AI successfully parsed {success_count}/{len(unique_dates)} unique dates"
            )

            return parsed_mapping

        except Exception as e:
            logger.error(f"AI date parsing failed: {e}")
            # Return empty mapping on failure
            return {}

    async def _process_single_row(
        self,
        row: pd.Series,
        row_number: int,
        column_mapping: FileColumnMapping,
        tenant_id: int,
        upload_id: str,
        has_category_labels: bool,
        report_id: str,
    ) -> RowProcessingDetail:
        """Process a single row and create detailed tracking."""
        # Convert row to dict with proper JSON serialization
        raw_data_dict = {}
        for key, value in row.items():
            if pd.isna(value):
                raw_data_dict[key] = None
            elif hasattr(value, "isoformat"):  # Handle datetime/timestamp objects
                raw_data_dict[key] = value.isoformat()
            else:
                raw_data_dict[key] = value

        row_detail = RowProcessingDetail(
            id=str(uuid.uuid4()),
            report_id=report_id,
            row_number=row_number,
            original_data=raw_data_dict,
            status="processing",
            validation_errors=[],
            data_quality_issues=[],
        )

        try:
            # Parse date
            row_detail.date_parsing_attempted = True
            date_val = None
            if column_mapping.date_column and column_mapping.date_column in row:
                date_val = row[column_mapping.date_column]
                if pd.notna(date_val):
                    row_detail.parsed_date = (
                        date_val.date() if hasattr(date_val, "date") else date_val
                    )
                else:
                    row_detail.validation_errors.append(
                        {
                            "field": "date",
                            "error": "Missing date value",
                            "severity": "error",
                        }
                    )
            else:
                row_detail.validation_errors.append(
                    {
                        "field": "date",
                        "error": "Date column not found",
                        "severity": "error",
                    }
                )

            # Parse amount
            row_detail.amount_parsing_attempted = True
            amount_val = None

            # First check if there's an "amount" column (created from Credit/Debit)
            if "amount" in row:
                amount_val = row["amount"]
            # Then check if amount_column is specified and exists
            elif column_mapping.amount_column and column_mapping.amount_column in row:
                amount_val = row[column_mapping.amount_column]
            # Finally check for Credit/Debit columns directly
            elif "Credit" in row and "Debit" in row:
                credit_val = row["Credit"] if pd.notna(row["Credit"]) else 0
                debit_val = row["Debit"] if pd.notna(row["Debit"]) else 0
                amount_val = float(credit_val) - float(debit_val)

            if pd.notna(amount_val):
                row_detail.parsed_amount = float(amount_val)
            else:
                row_detail.validation_errors.append(
                    {
                        "field": "amount",
                        "error": "Missing amount value - no amount, credit, or debit column found",
                        "severity": "error",
                    }
                )

            # Parse description
            desc_val = None
            if (
                column_mapping.description_column
                and column_mapping.description_column in row
            ):
                desc_val = row[column_mapping.description_column]
                if pd.notna(desc_val):
                    desc_str = str(desc_val).strip()
                    # Skip if description is "nan" or empty
                    if desc_str.lower() not in ["nan", "null", "none", ""]:
                        row_detail.parsed_description = desc_str
                    else:
                        row_detail.data_quality_issues.append("Invalid description")
                else:
                    row_detail.data_quality_issues.append("Missing description")
            else:
                row_detail.validation_errors.append(
                    {
                        "field": "description",
                        "error": "Description column not found",
                        "severity": "error",
                    }
                )

            # Parse category if available
            if has_category_labels and column_mapping.category_column:
                cat_val = row.get(column_mapping.category_column)
                if pd.notna(cat_val):
                    cat_str = str(cat_val).strip()
                    # Skip if category is "nan" or empty
                    if cat_str.lower() not in ["nan", "null", "none", ""]:
                        row_detail.parsed_category = cat_str
                        row_detail.category_mapping_confidence = 1.0

            # Parse vendor if available
            if column_mapping.vendor_column:
                vendor_val = row.get(column_mapping.vendor_column)
                if pd.notna(vendor_val):
                    vendor_str = str(vendor_val).strip()
                    # Skip if vendor is "nan" or empty
                    if vendor_str.lower() not in ["nan", "null", "none", ""]:
                        row_detail.parsed_vendor = vendor_str

            # Determine final status
            if row_detail.validation_errors:
                row_detail.status = "failed"
            elif not row_detail.parsed_description:
                # Skip rows without valid descriptions
                row_detail.status = "skipped"
                row_detail.data_quality_issues.append(
                    "No valid description - skipping row"
                )
            else:
                row_detail.status = "success"

                # Create transaction record
                # Create transaction record with asyncpg
                transaction_id = str(uuid.uuid4())
                current_time = datetime.utcnow()

                await self.conn.execute(
                    """
                    INSERT INTO transactions (
                        id, tenant_id, upload_id, date, description, amount, 
                        vendor_name, original_category, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                """,
                    transaction_id,
                    tenant_id,
                    upload_id,
                    row_detail.parsed_date,
                    row_detail.parsed_description,
                    row_detail.parsed_amount,
                    row_detail.parsed_vendor,
                    row_detail.parsed_category,
                    current_time,
                    current_time,
                )

        except Exception as e:
            row_detail.status = "failed"
            row_detail.validation_errors.append(
                {"field": "general", "error": str(e), "severity": "error"}
            )

        return row_detail

    def _clean_column_name(
        self, col_name: Any, col_idx: int, fallback: bool = False
    ) -> str:
        """
        Robust column name cleaning to handle all edge cases that cause Pydantic validation errors.

        Args:
            col_name: Original column name (any type)
            col_idx: Column index for fallback naming
            fallback: If True, use safe fallback name

        Returns:
            Clean string column name that's safe for Pydantic validation
        """
        if fallback:
            return f"Column_{col_idx}"

        try:
            # Handle None or NaN values
            if col_name is None or (isinstance(col_name, float) and pd.isna(col_name)):
                return f"Unnamed_{col_idx}"

            # Convert to string and strip whitespace
            clean_name = str(col_name).strip()

            # Handle empty strings
            if not clean_name:
                return f"Empty_{col_idx}"

            # Handle numeric column names (common in Excel files)
            if isinstance(col_name, (int, float)):
                if isinstance(col_name, float):
                    # Remove floating point precision errors
                    if col_name.is_integer():
                        clean_name = str(int(col_name))
                    else:
                        clean_name = f"{col_name:.10g}"  # Remove trailing zeros
                else:
                    clean_name = str(col_name)

            # Handle string representations of numbers with decimals
            elif (
                "." in clean_name
                and clean_name.replace(".", "").replace("-", "").isdigit()
            ):
                try:
                    float_val = float(clean_name)
                    if float_val.is_integer():
                        clean_name = str(int(float_val))
                    else:
                        clean_name = f"{float_val:.10g}"
                except (ValueError, OverflowError):
                    pass  # Keep original string if conversion fails

            # Replace any remaining problematic characters
            clean_name = (
                clean_name.replace("\n", " ").replace("\r", " ").replace("\t", " ")
            )

            # Ensure the name is not too long (Pydantic field length limits)
            if len(clean_name) > 100:
                clean_name = clean_name[:97] + "..."

            return clean_name

        except Exception as e:
            logger.warning(
                f"Failed to clean column name {col_name}: {e}. Using fallback."
            )
            return f"Column_{col_idx}"

    def _detect_column_type(self, series: pd.Series) -> str:
        """Detect the predominant data type of a column."""
        # Remove null values for analysis
        non_null = series.dropna()

        if len(non_null) == 0:
            return "unknown"

        # Try numeric
        numeric_count = pd.to_numeric(non_null, errors="coerce").notna().sum()
        if numeric_count / len(non_null) > 0.8:
            return "number"

        # Try date
        date_count = pd.to_datetime(non_null, errors="coerce").notna().sum()
        if date_count / len(non_null) > 0.8:
            return "date"

        # Try boolean
        bool_vals = {"true", "false", "yes", "no", "1", "0", "t", "f", "y", "n"}
        bool_count = non_null.astype(str).str.lower().isin(bool_vals).sum()
        if bool_count / len(non_null) > 0.8:
            return "boolean"

        return "string"

    def _calculate_type_consistency(self, series: pd.Series) -> float:
        """Calculate how consistent the data type is in a column."""
        detected_type = self._detect_column_type(series)
        non_null = series.dropna()

        if len(non_null) == 0:
            return 0.0

        if detected_type == "number":
            consistent = pd.to_numeric(non_null, errors="coerce").notna().sum()
        elif detected_type == "date":
            consistent = pd.to_datetime(non_null, errors="coerce").notna().sum()
        elif detected_type == "boolean":
            bool_vals = {"true", "false", "yes", "no", "1", "0", "t", "f", "y", "n"}
            consistent = non_null.astype(str).str.lower().isin(bool_vals).sum()
        else:
            consistent = len(non_null)

        return consistent / len(non_null)

    def _calculate_data_quality_score(
        self, report: FileProcessingReport, column_stats: List[ColumnStatistic]
    ) -> float:
        """Calculate overall data quality score."""
        scores = []

        # Success rate score
        if report.total_rows > 0:
            success_rate = report.successful_rows / report.total_rows
            scores.append(success_rate)

        # Column completeness score
        for stat in column_stats:
            if stat.total_values > 0:
                completeness = stat.non_null_values / stat.total_values
                scores.append(completeness * (stat.mapping_confidence or 0.5))

        # Type consistency score
        for stat in column_stats:
            if stat.type_consistency:
                scores.append(stat.type_consistency)

        return sum(scores) / len(scores) if scores else 0.0

    async def _trigger_schema_discovery_for_file(
        self,
        tenant_id: int,
        file_path: str,
        df: pd.DataFrame,
        column_mapping: FileColumnMapping,
        report_id: str,
    ) -> None:
        """Trigger schema discovery for the processed file."""
        try:
            # Prepare file data for schema discovery
            # Convert DataFrame to JSON-serializable format
            sample_df = df.head(20)
            sample_data = []
            for _, row in sample_df.iterrows():
                row_dict = {}
                for col in df.columns:
                    val = row[col]
                    if pd.isna(val):
                        row_dict[col] = None
                    elif hasattr(val, "isoformat"):  # Handle datetime/timestamp objects
                        row_dict[col] = val.isoformat()
                    else:
                        row_dict[col] = val
                sample_data.append(row_dict)

            file_data = {
                "file_name": file_path.split("/")[-1],
                "headers": list(df.columns),
                "sample_data": sample_data,
            }

            # Discover schema for this file
            schema = await self._discover_single_file_schema(tenant_id, file_data)

            if schema:
                # Update processing report with schema discovery results
                # Get report using asyncpg
                report_row = await self.conn.fetchrow(
                    "SELECT * FROM file_processing_reports WHERE id = $1", report_id
                )
                report = (
                    FileProcessingReport.model_validate(dict(report_row))
                    if report_row
                    else None
                )
                if report:
                    # Update schema confidence using asyncpg
                    await self.conn.execute(
                        "UPDATE file_processing_reports SET schema_confidence = $1 WHERE id = $2",
                        schema.get("confidence_score", 0.0),
                        report_id,
                    )

                logger.info(
                    f"Schema discovery completed for file {file_data.get('file_name', file_data.get('filename', 'unknown_file'))}"
                )

        except Exception as e:
            logger.error(f"Schema discovery failed for file: {e}")
            # Don't fail the overall processing if schema discovery fails

    async def process_uploaded_file(
        self,
        tenant_id: int,
        file_path: str,
        column_mapping: FileColumnMapping,
        has_category_labels: bool = True,
    ) -> int:
        """Process uploaded file and save transactions to database."""
        try:
            # Read the file
            if file_path.endswith(".csv"):
                df = pd.read_csv(file_path)
                # ENHANCED: Use robust column name cleaning to handle all edge cases
                df.columns = [
                    self._clean_column_name(col, idx)
                    for idx, col in enumerate(df.columns)
                ]
            else:
                # First, try to detect the header row by reading a small sample
                excel_file = pd.ExcelFile(file_path)
                sheet_name = excel_file.sheet_names[0]  # Use first sheet
                sample_df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=10)

                # Detect header row based on data type patterns
                header_row = 0
                for i in range(min(5, len(sample_df))):
                    row_values = sample_df.iloc[i].values
                    # Check if this row looks like headers (mostly strings, no numbers)
                    non_null_values = [v for v in row_values if pd.notna(v)]
                    if non_null_values:
                        string_count = sum(
                            1
                            for v in non_null_values
                            if isinstance(v, str)
                            and not str(v).replace(".", "").replace("-", "").isdigit()
                        )
                        if (
                            string_count >= len(non_null_values) * 0.7
                        ):  # 70% or more are non-numeric strings
                            header_row = i
                            break

                # Now read the full dataframe with the detected header row
                df = pd.read_excel(file_path, header=header_row)
                # ENHANCED: Use robust column name cleaning for numeric headers and edge cases
                # This handles float column names, None values, and other problematic types
                df.columns = [
                    self._clean_column_name(col, idx)
                    for idx, col in enumerate(df.columns)
                ]

            # Parse dates with smart format detection
            date_series = df[column_mapping.date_column]

            # Try multiple date formats
            date_formats = [
                "%m/%d/%Y",  # MM/DD/YYYY (US format, 4-digit year)
                "%d/%m/%Y",  # DD/MM/YYYY (European/Indian format)
                "%m/%d/%y",  # MM/DD/YY (US format, 2-digit year)
                "%d/%m/%y",  # DD/MM/YY (European format, 2-digit year)
                "%Y-%m-%d",  # ISO format
                "%d-%b-%Y",  # DD-MMM-YYYY
                "%d-%b-%y",  # DD-MMM-YY
            ]

            parsed_dates = None
            successful_format = None

            # Try each format
            for fmt in date_formats:
                try:
                    parsed = pd.to_datetime(date_series, format=fmt, errors="coerce")
                    # Check if at least 80% of dates were parsed successfully
                    if parsed.notna().sum() / len(date_series) > 0.8:
                        parsed_dates = parsed
                        successful_format = fmt
                        break
                except Exception:
                    continue

            # If no format worked well, use pandas' intelligent parsing
            if parsed_dates is None:
                parsed_dates = pd.to_datetime(
                    date_series, infer_datetime_format=True, errors="coerce"
                )
                successful_format = "inferred"

            df[column_mapping.date_column] = parsed_dates
            logger.info(f"Parsed dates using format: {successful_format}")

            # Check for any unparsed dates
            null_dates = df[column_mapping.date_column].isna().sum()
            if null_dates > 0:
                logger.warning(
                    f"Failed to parse {null_dates} dates in {column_mapping.date_column}"
                )

            # Process amount column (handle credit/debit)
            if "Credit" in df.columns and "Debit" in df.columns:
                # Separate credit/debit columns
                df["amount"] = df["Credit"].fillna(0) - df["Debit"].fillna(0)
            else:
                df["amount"] = pd.to_numeric(
                    df[column_mapping.amount_column], errors="coerce"
                )

            # Create an upload record for onboarding
            upload = Upload(
                id=str(uuid.uuid4()),
                filename=file_path.split("/")[-1],
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                file_path=file_path,
                size=len(df),
                status="processed",
                tenant_id=tenant_id,
                column_mapping=column_mapping.model_dump() if column_mapping else None,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            # Insert upload using asyncpg
            await self.conn.execute(
                """
                INSERT INTO uploads (
                    id, filename, content_type, file_path, size, status, tenant_id, 
                    column_mapping, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            """,
                upload.id,
                upload.filename,
                upload.content_type,
                upload.file_path,
                upload.size,
                upload.status,
                upload.tenant_id,
                json.dumps(upload.column_mapping) if upload.column_mapping else None,
                upload.created_at,
                upload.updated_at,
            )

            # Save transactions
            transaction_count = 0
            for _, row in df.iterrows():
                # Check for invalid date values to prevent database integrity errors
                date_value = row[column_mapping.date_column]
                if pd.isna(date_value):
                    logger.warning(f"Skipping row with invalid date: {row.to_dict()}")
                    continue

                # Safely extract date value
                try:
                    if hasattr(date_value, "date"):
                        transaction_date = date_value.date()
                    else:
                        transaction_date = date_value
                except (AttributeError, ValueError) as e:
                    logger.warning(
                        f"Skipping row with invalid date format: {date_value}, error: {e}"
                    )
                    continue

                # Create transaction record with asyncpg
                transaction_id = str(uuid.uuid4())
                current_time = datetime.utcnow()

                await self.conn.execute(
                    """
                    INSERT INTO transactions (
                        id, tenant_id, upload_id, date, description, amount, 
                        vendor_name, original_category, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                """,
                    transaction_id,
                    tenant_id,
                    upload.id,
                    transaction_date,
                    str(row[column_mapping.description_column]),
                    float(row["amount"]),
                    str(
                        row.get(
                            column_mapping.vendor_column,
                            row[column_mapping.description_column],
                        )
                    ),
                    str(row[column_mapping.category_column])
                    if has_category_labels and column_mapping.category_column
                    else None,
                    current_time,
                    current_time,
                )
                transaction_count += 1

            # No need for commit with asyncpg - auto-commits
            logger.info(
                f"Processed {transaction_count} transactions for tenant {tenant_id}"
            )
            return transaction_count

        except Exception as e:
            logger.error(f"File processing failed: {e}")
            # No rollback needed with asyncpg auto-commit
            raise ServiceError(
                f"Failed to process file: {str(e)}",
                service_name="OnboardingService",
                operation="process_uploaded_file",
            )

    async def build_rag_corpus(self, tenant_id: int) -> RAGCorpusStatus:
        """Build RAG corpus from tenant's labeled transaction data using Vertex AI."""

        logger.info(f"Building RAG corpus for tenant {tenant_id}")

        # Get labeled transactions count first using asyncpg
        total_count = (
            await self.conn.fetchval(
                "SELECT COUNT(id) FROM transactions WHERE tenant_id = $1 AND original_category IS NOT NULL",
                tenant_id,
            )
            or 0
        )
        logger.info(f"Found {total_count} labeled transactions for corpus building")

        # Get labeled transactions using asyncpg
        sql = """
            SELECT * FROM transactions 
            WHERE tenant_id = $1 AND original_category IS NOT NULL
            ORDER BY date
        """
        rows = await self.conn.fetch(sql, tenant_id)
        transactions = [Transaction.model_validate(dict(row)) for row in rows]

        if len(transactions) < 10:
            raise ValidationError(
                f"Insufficient labeled transactions: {len(transactions)} (need at least 10)"
            )

        # Group by category
        category_patterns: Dict[str, List[Dict]] = {}
        for txn in transactions:
            category = txn.original_category
            if category not in category_patterns:
                category_patterns[category] = []

            category_patterns[category].append(
                {
                    "description": txn.description,
                    "amount": float(txn.amount),
                    "vendor": txn.vendor_name,
                    "date": txn.date.isoformat() if txn.date else None,
                }
            )

        # Create corpus entries
        corpus_entries = []

        for category, patterns in category_patterns.items():
            # Create category summary
            examples = [p["description"] for p in patterns[:10]]

            category_entry = {
                "type": "category_definition",
                "category": category,
                "example_count": len(patterns),
                "examples": examples,
                "amount_range": {
                    "min": min(p["amount"] for p in patterns),
                    "max": max(p["amount"] for p in patterns),
                    "avg": sum(p["amount"] for p in patterns) / len(patterns),
                },
            }
            corpus_entries.append(json.dumps(category_entry))

            # Add individual patterns (limit to prevent corpus explosion)
            for pattern in patterns[:20]:
                pattern_entry = {
                    "type": "transaction_pattern",
                    "category": category,
                    "description": pattern["description"],
                    "amount": pattern["amount"],
                    "vendor": pattern["vendor"],
                }
                corpus_entries.append(json.dumps(pattern_entry))

        # Create or update corpus in Vertex AI
        if self.vertex_client:
            try:
                # Ensure vertex client is initialized
                if not self.vertex_client.is_ready():
                    await self.vertex_client.setup_clients()

                corpus_display_name = f"tenant-{tenant_id}-corpus-{datetime.utcnow().strftime('%Y%m%d-%H%M%S')}"

                # Create RAG corpus in Vertex AI
                logger.info(f"Creating Vertex AI RAG corpus: {corpus_display_name}")
                corpus_resource_name = await self.vertex_client.create_rag_corpus(
                    display_name=corpus_display_name
                )
                logger.info(f"Created RAG corpus: {corpus_resource_name}")

                # Prepare JSONL content for GCS upload
                jsonl_content = "\n".join(corpus_entries)

                # Upload to GCS if bucket is configured
                from ...core.config import settings

                if settings.GCS_BUCKET_NAME_RAG:
                    # Create GCS URI for the corpus file
                    gcs_filename = f"tenant-{tenant_id}/corpus-{datetime.now(timezone.utc).strftime('%Y%m%d-%H%M%S')}.jsonl"
                    gcs_uri = f"gs://{settings.GCS_BUCKET_NAME_RAG}/{gcs_filename}"

                    logger.info(f"Uploading corpus to GCS: {gcs_uri}")
                    await self.vertex_client.upload_to_gcs(
                        local_content_str=jsonl_content,
                        gcs_uri=gcs_uri,
                        content_type="application/jsonl",
                    )

                    # Import files into RAG corpus
                    logger.info("Importing files into RAG corpus")
                    import_result = await self.vertex_client.import_rag_files(
                        corpus_name=corpus_resource_name,
                        gcs_uris=[gcs_uri],
                        chunk_size=settings.RAG_CHUNK_SIZE,
                        chunk_overlap=settings.RAG_CHUNK_OVERLAP,
                    )

                    logger.info(f"Import result: {import_result}")

                    # Cache the corpus resource name for future retrieval
                    self._rag_corpus_cache[tenant_id] = corpus_resource_name

                    # Save corpus information to database
                    # Check if corpus already exists for tenant using asyncpg
                    sql = "SELECT * FROM rag_corpus WHERE tenant_id = $1"
                    row = await self.conn.fetchrow(sql, tenant_id)
                    corpus_record = RAGCorpus.model_validate(dict(row)) if row else None

                    if corpus_record:
                        # Update existing record using asyncpg
                        update_sql = """
                            UPDATE rag_corpus 
                            SET corpus_resource_name = $1, corpus_display_name = $2, 
                                total_patterns = $3, unique_categories = $4, 
                                gcs_uri = $5, is_active = $6, last_rebuild_at = $7
                            WHERE tenant_id = $8
                        """
                        await self.conn.execute(
                            update_sql,
                            corpus_resource_name,
                            corpus_display_name,
                            len(corpus_entries),
                            len(category_patterns),
                            gcs_uri,
                            True,
                            datetime.utcnow(),
                            tenant_id,
                        )
                    else:
                        # Create new record using asyncpg
                        import uuid

                        corpus_id = str(uuid.uuid4())
                        insert_sql = """
                            INSERT INTO rag_corpus 
                            (id, tenant_id, corpus_resource_name, corpus_display_name, 
                             total_patterns, unique_categories, gcs_uri, is_active, created_at)
                            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                        """
                        await self.conn.execute(
                            insert_sql,
                            corpus_id,
                            tenant_id,
                            corpus_resource_name,
                            corpus_display_name,
                            len(corpus_entries),
                            len(category_patterns),
                            gcs_uri,
                            True,
                            datetime.utcnow(),
                        )

                    # Also save to file as backup
                    corpus_metadata_file = (
                        Path("data/rag_corpus") / f"tenant-{tenant_id}-metadata.json"
                    )
                    corpus_metadata_file.parent.mkdir(parents=True, exist_ok=True)
                    with open(corpus_metadata_file, "w") as f:
                        json.dump(
                            {
                                "tenant_id": tenant_id,
                                "corpus_resource_name": corpus_resource_name,
                                "corpus_display_name": corpus_display_name,
                                "created_at": datetime.now(timezone.utc).isoformat(),
                                "gcs_uri": gcs_uri,
                            },
                            f,
                        )

                    return RAGCorpusStatus(
                        tenant_id=tenant_id,
                        corpus_exists=True,
                        corpus_name=corpus_resource_name,
                        created_at=datetime.now(timezone.utc).replace(tzinfo=None),
                        last_updated=datetime.now(timezone.utc).replace(tzinfo=None),
                        total_patterns=len(corpus_entries),
                        unique_categories=len(category_patterns),
                        source_files=[gcs_uri],
                    )
                else:
                    # Fallback: Save locally when GCS bucket not configured
                    logger.warning(
                        "GCS_BUCKET_NAME_RAG not configured - saving corpus locally"
                    )
                    corpus_dir = Path("data/rag_corpus")
                    corpus_dir.mkdir(parents=True, exist_ok=True)

                    corpus_file = corpus_dir / f"{corpus_display_name}.jsonl"
                    with open(corpus_file, "w") as f:
                        f.write(jsonl_content)

                    logger.info(f"Created local corpus file: {corpus_file}")
                    self._rag_corpus_cache[tenant_id] = corpus_display_name

                    return RAGCorpusStatus(
                        tenant_id=tenant_id,
                        corpus_exists=True,
                        corpus_name=corpus_display_name,
                        created_at=datetime.now(timezone.utc).replace(tzinfo=None),
                        last_updated=datetime.now(timezone.utc).replace(tzinfo=None),
                        total_patterns=len(corpus_entries),
                        unique_categories=len(category_patterns),
                        source_files=[],
                    )

            except Exception as e:
                logger.error(f"Failed to create Vertex AI RAG corpus: {e}")
                raise ServiceError(
                    f"RAG corpus creation failed: {str(e)}",
                    service_name="OnboardingService",
                    operation="build_rag_corpus",
                )
        else:
            raise ServiceError(
                "Vertex AI client not available for RAG corpus creation",
                service_name="OnboardingService",
                operation="build_rag_corpus",
            )

    async def run_temporal_validation_background(
        self, request: TemporalValidationRequest, validation_id: str
    ) -> None:
        """Run temporal validation in background with connection management."""
        # Import necessary dependencies for fresh sessions

        # Create initial validation record using asyncpg
        insert_sql = """
            INSERT INTO temporal_validations (
                id, validation_id, tenant_id, start_month, end_month,
                accuracy_threshold, status, started_at, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        """
        current_time = datetime.now(timezone.utc).replace(tzinfo=None)
        await self.conn.execute(
            insert_sql,
            str(uuid.uuid4()),
            validation_id,
            request.tenant_id,
            request.start_month,
            request.end_month,
            request.accuracy_threshold,
            "running",
            current_time,
            current_time,
            current_time,
        )
        logger.info(f"Created validation record {validation_id}")

        try:
            # Run validation with a fresh session that we'll refresh periodically
            result = await self.run_temporal_validation_with_refresh(
                request, validation_id
            )

            # Update validation record with results using asyncpg
            sql = "SELECT * FROM temporal_validations WHERE validation_id = $1"
            row = await self.conn.fetchrow(sql, validation_id)
            validation = TemporalValidation.model_validate(dict(row)) if row else None

            if validation:
                # Update validation record with results using asyncpg
                update_sql = """
                    UPDATE temporal_validations 
                    SET status = $1, completed_at = $2, average_accuracy = $3, 
                        meets_threshold = $4, monthly_results = $5
                    WHERE validation_id = $6
                """
                monthly_results_json = (
                    [r.model_dump() for r in result.monthly_results]
                    if result.monthly_results
                    else []
                )
                total_transactions_tested = (
                    sum(r.transactions_tested for r in result.monthly_results)
                    if result.monthly_results
                    else 0
                )

                await self.conn.execute(
                    update_sql,
                    "completed",
                    datetime.utcnow(),
                    result.average_accuracy,
                    result.meets_threshold,
                    json.dumps(monthly_results_json),
                    validation_id,
                )

                # Update total_transactions_tested separately
                await self.conn.execute(
                    "UPDATE temporal_validations SET total_transactions_tested = $1 WHERE validation_id = $2",
                    total_transactions_tested,
                    validation_id,
                )

                # Update onboarding status with validation results using asyncpg
                sql = "SELECT * FROM onboarding_status WHERE tenant_id = $1"
                row = await self.conn.fetchrow(sql, request.tenant_id)
                status_record = (
                    OnboardingStatusModel.model_validate(dict(row)) if row else None
                )

                if status_record:
                    # Update status record using asyncpg
                    stage = (
                        "completed" if result.meets_threshold else status_record.stage
                    )
                    update_sql = """
                        UPDATE onboarding_status 
                        SET last_validation_id = $1, last_validation_accuracy = $2, stage = $3
                        WHERE tenant_id = $4
                    """
                    await self.conn.execute(
                        update_sql,
                        validation_id,
                        result.average_accuracy,
                        stage,
                        request.tenant_id,
                    )

                logger.info(f"Validation {validation_id} completed successfully")

        except Exception as e:
            logger.error(f"Validation {validation_id} failed: {e}")
            # Update validation record with error using asyncpg
            sql = "SELECT * FROM temporal_validations WHERE validation_id = $1"
            row = await self.conn.fetchrow(sql, validation_id)
            validation = TemporalValidation.model_validate(dict(row)) if row else None

            if validation:
                # Update validation record to failed status
                update_sql = """
                    UPDATE temporal_validations 
                    SET status = $1, completed_at = $2, error_message = $3
                    WHERE validation_id = $4
                """
                await self.conn.execute(
                    update_sql,
                    "failed",
                    datetime.utcnow(),
                    str(e),
                    validation_id,
                )

    async def run_temporal_validation_with_refresh(
        self, request: TemporalValidationRequest, validation_id: str
    ) -> TemporalValidationResult:
        """Run temporal validation with periodic database connection refresh."""

        if not validation_id:
            validation_id = str(uuid.uuid4())
        logger.info(
            f"Starting temporal validation {validation_id} for tenant {request.tenant_id}"
        )

        result = TemporalValidationResult(
            tenant_id=request.tenant_id,
            validation_id=validation_id,
            started_at=datetime.now(timezone.utc).replace(tzinfo=None),
            status="running",
            accuracy_threshold=request.accuracy_threshold,
        )

        try:
            # Parse date range
            start_date = datetime.strptime(request.start_month, "%Y-%m").date()
            end_date = datetime.strptime(request.end_month, "%Y-%m").date()

            # Get first day of month after end_month for upper bound
            end_parts = request.end_month.split("-")
            end_year = int(end_parts[0])
            end_month = int(end_parts[1])
            if end_month == 12:
                end_date_upper = date(end_year + 1, 1, 1)
            else:
                end_date_upper = date(end_year, end_month + 1, 1)

            # Fetch all transactions using asyncpg
            sql = """
                SELECT * FROM transactions 
                WHERE tenant_id = $1 
                  AND original_category IS NOT NULL 
                  AND date < $2
                ORDER BY date
            """
            rows = await self.conn.fetch(sql, request.tenant_id, end_date_upper)
            all_transactions = [Transaction.model_validate(dict(row)) for row in rows]

            # Convert to list of dicts immediately
            txn_list = []
            for txn in all_transactions:
                # Ensure date is a date object, not datetime
                txn_date = txn.date
                if hasattr(txn_date, "date"):
                    # It's a datetime, convert to date
                    txn_date = txn_date.date()
                elif not isinstance(txn_date, date):
                    # Skip invalid dates
                    continue

                txn_list.append(
                    {
                        "id": txn.id,
                        "date": txn_date,
                        "description": txn.description,
                        "amount": float(txn.amount),
                        "category_label": txn.original_category,
                        "vendor": txn.vendor_name,
                        "tenant_id": txn.tenant_id,
                    }
                )

            if not txn_list:
                raise ValidationError("No labeled transactions found for validation")

            logger.info(f"Loaded {len(txn_list)} transactions for validation")

            # Process each month
            current_month = start_date
            monthly_results = []

            while current_month <= end_date:
                month_str = current_month.strftime("%Y-%m")

                # Get training and test data for this month
                training_data = []
                test_data = []

                if current_month.month == 12:
                    next_month = date(current_month.year + 1, 1, 1)
                else:
                    next_month = date(current_month.year, current_month.month + 1, 1)

                for txn in txn_list:
                    if txn["date"]:
                        txn_date = txn["date"]
                        if txn_date < current_month:
                            training_data.append(txn)
                        elif current_month <= txn_date < next_month:
                            test_data.append(txn)

                if len(training_data) >= 10 and len(test_data) >= 5:
                    # Process this month with a fresh AI service instance
                    logger.info(
                        f"Processing {month_str} with {len(training_data)} training, {len(test_data)} test"
                    )

                    # Process this month with current service instance
                    month_result = await self._test_month_accuracy(
                        month_str, training_data, test_data
                    )
                    monthly_results.append(month_result)
                else:
                    logger.warning(
                        f"Insufficient data for {month_str}: "
                        f"{len(training_data)} training, {len(test_data)} test"
                    )

                # Move to next month
                if current_month.month == 12:
                    current_month = date(current_month.year + 1, 1, 1)
                else:
                    current_month = date(current_month.year, current_month.month + 1, 1)

            # Calculate overall results
            if monthly_results:
                accuracies = [r.accuracy for r in monthly_results]
                avg_accuracy = sum(accuracies) / len(accuracies)

                result.monthly_results = monthly_results
                result.average_accuracy = avg_accuracy
                result.meets_threshold = (
                    avg_accuracy >= request.accuracy_threshold * 100
                )
                result.status = "completed"
                result.total_training_transactions = len(txn_list)
                result.total_test_transactions = sum(
                    r.transactions_tested for r in monthly_results
                )
            else:
                result.status = "failed"
                result.error_message = "No months had sufficient data for validation"

            result.completed_at = datetime.now(timezone.utc).replace(tzinfo=None)

        except Exception as e:
            logger.error(f"Temporal validation failed: {e}")
            result.status = "failed"
            result.error_message = str(e)
            result.completed_at = datetime.now(timezone.utc).replace(tzinfo=None)

        return result

    async def run_temporal_validation(
        self, request: TemporalValidationRequest, validation_id: str = None
    ) -> TemporalValidationResult:
        """Run temporal accuracy validation for a tenant."""

        if not validation_id:
            validation_id = str(uuid.uuid4())
        logger.info(
            f"Starting temporal validation {validation_id} for tenant {request.tenant_id}"
        )

        result = TemporalValidationResult(
            tenant_id=request.tenant_id,
            validation_id=validation_id,
            started_at=datetime.now(timezone.utc).replace(tzinfo=None),
            status="running",
            accuracy_threshold=request.accuracy_threshold,
        )

        try:
            # Parse date range from request
            if hasattr(request, "start_month") and hasattr(request, "end_month"):
                # Monthly validation format
                start_date = datetime.strptime(request.start_month, "%Y-%m").date()
                # Get first day of month after end_month for upper bound
                end_parts = request.end_month.split("-")
                end_year = int(end_parts[0])
                end_month = int(end_parts[1])
                if end_month == 12:
                    end_date_upper = date(end_year + 1, 1, 1)
                else:
                    end_date_upper = date(end_year, end_month + 1, 1)
            else:
                # Date range format
                start_date = datetime.strptime(request.start_date, "%Y-%m-%d").date()
                end_date_upper = datetime.strptime(
                    request.end_date, "%Y-%m-%d"
                ).date() + timedelta(days=1)

            # Only fetch transactions we need (from beginning up to end of validation period) using asyncpg
            sql = """
                SELECT * FROM transactions 
                WHERE tenant_id = $1 
                  AND original_category IS NOT NULL 
                  AND date < $2
                ORDER BY date
            """
            rows = await self.conn.fetch(sql, request.tenant_id, end_date_upper)
            all_transactions = [Transaction.model_validate(dict(row)) for row in rows]

            if not all_transactions:
                raise ValidationError("No labeled transactions found for validation")

            # Convert to list of dicts for easier processing
            txn_list = [
                {
                    "id": txn.id,
                    "date": txn.date
                    if isinstance(txn.date, date)
                    else txn.date.date(),  # Ensure it's a date object
                    "description": txn.description,
                    "amount": float(txn.amount),
                    "category_label": txn.original_category_label,
                    "vendor": txn.vendor_name,
                    "tenant_id": request.tenant_id,  # Include tenant_id for RAG corpus selection
                }
                for txn in all_transactions
            ]

            # Parse month range
            start_date = datetime.strptime(request.start_month, "%Y-%m").date()
            end_date = datetime.strptime(request.end_month, "%Y-%m").date()

            # Run validation for each month
            current_month = start_date
            monthly_results = []

            while current_month <= end_date:
                month_str = current_month.strftime("%Y-%m")

                # Get training data (all transactions before this month)
                training_data = []
                for txn in txn_list:
                    if txn["date"]:
                        # Ensure date comparison works
                        txn_date = txn["date"]
                        if hasattr(txn_date, "date"):
                            txn_date = txn_date.date()
                        if txn_date < current_month:
                            training_data.append(txn)

                # Get test data (transactions in this month)
                if current_month.month == 12:
                    next_month = date(current_month.year + 1, 1, 1)
                else:
                    next_month = date(current_month.year, current_month.month + 1, 1)

                test_data = []
                for txn in txn_list:
                    if txn["date"]:
                        # Ensure date comparison works
                        txn_date = txn["date"]
                        if hasattr(txn_date, "date"):
                            txn_date = txn_date.date()
                        if current_month <= txn_date < next_month:
                            test_data.append(txn)

                if len(training_data) >= 10 and len(test_data) >= 5:
                    # Run accuracy test for this month
                    month_result = await self._test_month_accuracy(
                        month_str, training_data, test_data
                    )
                    monthly_results.append(month_result)
                else:
                    logger.warning(
                        f"Insufficient data for {month_str}: "
                        f"{len(training_data)} training, {len(test_data)} test"
                    )

                # Move to next month
                if current_month.month == 12:
                    current_month = date(current_month.year + 1, 1, 1)
                else:
                    current_month = date(current_month.year, current_month.month + 1, 1)

            # Calculate overall results
            if monthly_results:
                accuracies = [r.accuracy for r in monthly_results]
                avg_accuracy = sum(accuracies) / len(accuracies)

                result.monthly_results = monthly_results
                result.average_accuracy = avg_accuracy
                result.meets_threshold = (
                    avg_accuracy >= request.accuracy_threshold * 100
                )
                result.status = "completed"
            else:
                result.status = "failed"
                result.error_message = "No months had sufficient data for validation"

            result.completed_at = datetime.now(timezone.utc).replace(tzinfo=None)

        except Exception as e:
            logger.error(f"Temporal validation failed: {e}")
            result.status = "failed"
            result.error_message = str(e)
            result.completed_at = datetime.now(timezone.utc).replace(tzinfo=None)

        finally:
            # Clean up temporal corpora and reactivate regular corpus
            try:
                logger.info("Cleaning up temporal validation corpora")

                # Delete all temporal corpora from database

                # Delete temporal validation corpus using asyncpg
                await self.conn.execute(
                    "DELETE FROM rag_corpus WHERE tenant_id = $1 AND gcs_uri LIKE 'gs://temporal-validation/%'",
                    request.tenant_id,
                )

                # Reactivate the regular corpus if it exists using asyncpg
                sql = """
                    SELECT * FROM rag_corpus 
                    WHERE tenant_id = $1 
                      AND gcs_uri NOT LIKE 'gs://temporal-validation/%' 
                      AND is_active = false
                    LIMIT 1
                """
                row = await self.conn.fetchrow(sql, request.tenant_id)
                regular_corpus = RAGCorpus.model_validate(dict(row)) if row else None

                if regular_corpus:
                    # Reactivate the regular corpus using asyncpg
                    await self.conn.execute(
                        "UPDATE rag_corpus SET is_active = $1 WHERE id = $2",
                        True,
                        regular_corpus.id,
                    )
                    logger.info(
                        f"Reactivated regular corpus: {regular_corpus.corpus_display_name}"
                    )
                logger.info("✅ Temporal validation cleanup complete")

            except Exception as cleanup_error:
                logger.error(f"Failed to clean up temporal corpora: {cleanup_error}")
                # Don't fail the validation due to cleanup errors

        return result

    async def _test_month_accuracy(
        self, month: str, training_data: List[Dict], test_data: List[Dict]
    ) -> MonthlyAccuracyResult:
        """Test categorization accuracy for a specific month."""

        logger.info(
            f"Testing accuracy for {month} with {len(training_data)} training, {len(test_data)} test"
        )

        # CRITICAL FIX: Build RAG corpus for this month's training data
        tenant_id = training_data[0]["tenant_id"] if training_data else 1
        corpus_name = f"temporal_validation_{tenant_id}_{month}"

        logger.info(
            f"🔨 Building progressive RAG corpus '{corpus_name}' with {len(training_data)} transactions"
        )

        # Build temporary RAG corpus from training data
        corpus_resource_name = None
        try:
            # Deactivate any existing temporal validation corpora for this tenant
            await self._deactivate_temporal_corpora(tenant_id)

            corpus_resource_name = await self._build_temporal_rag_corpus(
                tenant_id=tenant_id,
                corpus_name=corpus_name,
                training_data=training_data,
            )
            logger.info(
                f"✅ RAG corpus '{corpus_name}' built successfully: {corpus_resource_name}"
            )
        except Exception as e:
            logger.error(f"❌ Failed to build RAG corpus for {month}: {e}")
            # Continue without RAG if build fails
            corpus_resource_name = None

        # Build category patterns from training data
        category_patterns = {}
        for txn in training_data:
            category = txn["category_label"]
            if category not in category_patterns:
                category_patterns[category] = []
            category_patterns[category].append(txn)

        # Test transactions in batches for better performance
        predictions = []
        actuals = []

        # Process in smaller batches with conservative limits for rate limiting
        from ...core.config import settings

        batch_size = settings.TEMPORAL_VALIDATION_BATCH_SIZE
        test_subset = test_data[
            : settings.TEMPORAL_VALIDATION_MAX_TRANSACTIONS_PER_MONTH
        ]

        for i in range(0, len(test_subset), batch_size):
            batch = test_subset[i : i + batch_size]
            batch_predictions = await self._predict_categories_batch(
                batch, category_patterns, month
            )
            predictions.extend(batch_predictions)
            actuals.extend([txn["category_label"] for txn in batch])

        # CRITICAL FIX: Calculate metrics using schema discovery mappings
        # This is the key fix for measuring accuracy against customer's original schema
        correct = 0
        # Get tenant_id from test data (all transactions should have the same tenant_id)
        tenant_id = test_data[0].get("tenant_id") if test_data else None

        # If tenant_id is not in test_data, try to extract from training_data
        if not tenant_id and training_data:
            tenant_id = training_data[0].get("tenant_id")

        logger.info(
            f"Using tenant_id {tenant_id} for accuracy measurement with {len(predictions)} predictions"
        )

        # Track matches for debugging
        matches = 0
        mismatches = []

        # CRITICAL DEBUG: Log first 5 predictions vs actuals to understand the issue
        logger.info(f"🔍 DEBUG: First 5 predictions vs actuals for {month}:")
        for i in range(min(5, len(predictions))):
            logger.info(
                f"  [{i}] Predicted: '{predictions[i]}' | Actual: '{actuals[i]}'"
            )

        for i, (p, a) in enumerate(zip(predictions, actuals, strict=False)):
            is_match = await self._categories_match_with_schema_mapping(p, a, tenant_id)
            if is_match:
                matches += 1
            else:
                mismatches.append({"predicted": p, "actual": a, "index": i})

        correct = matches
        accuracy = (correct / len(predictions)) * 100 if predictions else 0

        logger.info(
            f"Accuracy calculation: {correct}/{len(predictions)} = {accuracy:.2f}%"
        )
        if (
            mismatches and len(mismatches) <= 10
        ):  # Log first 10 mismatches for debugging
            logger.info(f"🔴 Sample mismatches: {mismatches[:10]}")

        # Calculate per-category metrics using schema mapping
        category_metrics = {}
        for category in set(actuals + predictions):
            # Use async schema mapping for calculating metrics
            tp = 0
            fp = 0
            fn = 0

            for p, a in zip(predictions, actuals, strict=False):
                # True Positive: predicted this category and it matches actual
                if p == category and await self._categories_match_with_schema_mapping(
                    p, a, tenant_id
                ):
                    tp += 1
                # False Positive: predicted this category but actual is different
                elif (
                    p == category
                    and not await self._categories_match_with_schema_mapping(
                        p, a, tenant_id
                    )
                ):
                    fp += 1
                # False Negative: actual is this category but prediction is different
                elif (
                    a == category
                    and not await self._categories_match_with_schema_mapping(
                        p, a, tenant_id
                    )
                ):
                    fn += 1

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = (
                2 * (precision * recall) / (precision + recall)
                if (precision + recall) > 0
                else 0
            )

            category_metrics[category] = {
                "precision": precision,
                "recall": recall,
                "f1_score": f1,
                "support": sum(1 for a in actuals if a == category),
            }

        # Calculate overall metrics
        total_support = len(actuals)
        precision = sum(
            m["precision"] * m["support"] / total_support
            for m in category_metrics.values()
        )
        recall = sum(
            m["recall"] * m["support"] / total_support
            for m in category_metrics.values()
        )
        f1_score = (
            2 * (precision * recall) / (precision + recall)
            if (precision + recall) > 0
            else 0
        )

        # Get training months
        training_months = sorted(
            list(
                set(
                    txn["date"].strftime("%Y-%m")
                    for txn in training_data
                    if txn["date"]
                )
            )
        )

        # Create result before cleanup
        result = MonthlyAccuracyResult(
            month=month,
            training_months=training_months[-6:],  # Last 6 months
            transactions_tested=len(test_data),
            correct_predictions=correct,
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1_score,
            category_breakdown=category_metrics,
        )

        # Cleanup: Deactivate this month's corpus after testing
        if "corpus_resource_name" in locals():
            try:
                await self._deactivate_temporal_corpora(tenant_id)
                logger.info(f"✅ Deactivated temporal corpus for {month}")
            except Exception as e:
                logger.error(f"Failed to deactivate corpus after {month} test: {e}")

        return result

    async def _predict_categories_batch(
        self,
        transactions: List[Dict],
        category_patterns: Dict[str, List[Dict]],
        month: str = None,
    ) -> List[str]:
        """Predict categories for a batch of transactions using individual predictions with RAG."""

        if not self.vertex_client:
            raise ServiceError(
                "Vertex AI client required for categorization",
                service_name="OnboardingService",
                operation="_predict_categories_batch",
            )

        # IMPORTANT: Always use individual predictions to enable RAG lookup
        # The batch processing bypasses the CategorizationAgent and thus RAG
        logger.info(
            f"Processing {len(transactions)} transactions individually to enable RAG lookup"
        )

        predictions = []
        for i, txn in enumerate(transactions):
            try:
                pred = await self._predict_category(txn, category_patterns, month)
                predictions.append(pred)
                if (i + 1) % 5 == 0:
                    logger.info(f"Processed {i + 1}/{len(transactions)} transactions")
                    # Add small delay between batches to help with rate limiting
                    await asyncio.sleep(1)
            except Exception as e:
                error_msg = str(e)
                if "429" in error_msg or "rate limit" in error_msg.lower():
                    from ...core.config import settings

                    logger.warning(
                        f"Rate limit hit at transaction {i}, waiting {settings.TEMPORAL_VALIDATION_RATE_LIMIT_DELAY} seconds..."
                    )
                    await asyncio.sleep(settings.TEMPORAL_VALIDATION_RATE_LIMIT_DELAY)
                    try:
                        pred = await self._predict_category(
                            txn, category_patterns, month
                        )
                        predictions.append(pred)
                    except Exception as retry_e:
                        logger.error(f"Retry failed for transaction {i}: {retry_e}")
                        # Use most common category as fallback
                        if category_patterns:
                            pred = max(
                                category_patterns.items(), key=lambda x: len(x[1])
                            )[0]
                        else:
                            pred = "Unknown"
                        predictions.append(pred)
                else:
                    logger.error(f"Error predicting category for transaction {i}: {e}")
                    # Use most common category as fallback
                    if category_patterns:
                        pred = max(category_patterns.items(), key=lambda x: len(x[1]))[
                            0
                        ]
                    else:
                        pred = "Unknown"
                    predictions.append(pred)

        return predictions

    async def _predict_category(
        self,
        transaction: Dict,
        category_patterns: Dict[str, List[Dict]],
        month: str = None,
    ) -> str:
        """Predict category for a transaction using learned patterns and RAG."""

        if not self.vertex_client:
            raise ServiceError(
                "Vertex AI client required for categorization",
                service_name="OnboardingService",
                operation="_predict_category",
            )

        # Use the CategorizationAgent for consistency with production categorization
        # Initialize categorization agent with RAG enabled
        # Use the configured model from settings instead of hardcoded experimental model
        from ...core.config import settings
        from ..categories.categorization_agent import AgentConfig, CategorizationAgent

        # Get the corpus name for temporal validation
        corpus_name = (
            f"temporal_validation_{transaction.get('tenant_id', 1)}_{month}"
            if month
            else None
        )

        agent_config = AgentConfig(
            model_name=settings.CATEGORIZATION_MODEL_ID,
            rag_enabled=True,  # Enable RAG for temporal validation
            rag_corpus_name=corpus_name,  # Use specific temporal validation corpus
            project=self.vertex_client.project_id,
            location=self.vertex_client.location,
        )

        categorization_agent = CategorizationAgent(config=agent_config)

        # Create transaction details for categorization
        transaction_details = {
            "description": transaction["description"],
            "amount": transaction["amount"],
            "vendor": transaction.get("vendor", ""),
            "tenant_id": transaction.get("tenant_id", 1),  # Use tenant_id if available
            "transaction_type": "debit" if transaction["amount"] < 0 else "credit",
        }

        try:
            # Use the agent to categorize with RAG context
            result = await categorization_agent.categorize_transaction_with_details(
                transaction_details
            )

            predicted = result.get("category", "Unknown")
            confidence = result.get("confidence", 0.0)
            rag_used = result.get("rag_used", False)

            logger.info(
                f"🎯 AI Predicted: '{predicted}' (confidence: {confidence:.2f}, RAG: {rag_used}) for transaction: {transaction['description'][:50]}..."
            )

            # Find closest match in known categories from training data
            for category in category_patterns.keys():
                if (
                    category.lower() in predicted.lower()
                    or predicted.lower() in category.lower()
                ):
                    return category

            # If no exact match, return the AI prediction
            return predicted

        except Exception as e:
            logger.error(f"AI prediction with RAG failed: {e}")
            # As a last resort, return the most common category
            if category_patterns:
                # Return category with most patterns
                return max(category_patterns.items(), key=lambda x: len(x[1]))[0]
            return "Unknown"

    async def _build_temporal_rag_corpus(
        self, tenant_id: int, corpus_name: str, training_data: List[Dict]
    ) -> str:
        """Build a temporary RAG corpus for temporal validation.

        This creates a RAG corpus specific to a test month containing only
        the training data available up to that point in time.
        """
        if not self.vertex_client:
            raise ServiceError(
                "Vertex AI client required for RAG corpus creation",
                service_name="OnboardingService",
                operation="_build_temporal_rag_corpus",
            )

        logger.info(
            f"Building temporal RAG corpus '{corpus_name}' with {len(training_data)} transactions"
        )

        try:
            # Create corpus entries from training data
            corpus_entries = []
            categories_seen = set()

            for txn in training_data:
                category = txn["category_label"]
                categories_seen.add(category)

                # Create a RAG entry for this transaction pattern
                entry = {
                    "category": category,
                    "transaction_pattern": txn["description"],
                    "amount": txn["amount"],
                    "vendor": txn.get("vendor", ""),
                    "transaction_type": "debit" if txn["amount"] < 0 else "credit",
                    "metadata": {
                        "date": str(txn["date"]),
                        "tenant_id": tenant_id,
                        "corpus_type": "temporal_validation",
                    },
                }
                corpus_entries.append(entry)

            logger.info(
                f"Created {len(corpus_entries)} RAG entries covering {len(categories_seen)} categories"
            )

            # Create the corpus in Vertex AI
            corpus_display_name = f"Temporal-{corpus_name}"
            corpus_resource_name = await self.vertex_client.create_rag_corpus(
                display_name=corpus_display_name
            )

            # Convert entries to JSONL format
            jsonl_content = "\n".join(json.dumps(entry) for entry in corpus_entries)

            # Upload to GCS
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            gcs_filename = (
                f"tenant-{tenant_id}/temporal/{corpus_name}-{timestamp}.jsonl"
            )

            try:
                # Create full GCS URI
                from ...core.config import settings

                if not settings.GCS_BUCKET_NAME_RAG:
                    raise ServiceError("GCS bucket for RAG not configured")

                gcs_uri = f"gs://{settings.GCS_BUCKET_NAME_RAG}/{gcs_filename}"

                await self.vertex_client.upload_to_gcs(
                    local_content_str=jsonl_content,
                    gcs_uri=gcs_uri,
                    content_type="application/jsonl",
                )
                logger.info(f"Uploaded temporal corpus to GCS: {gcs_uri}")

                # Import data into RAG corpus

                await self.vertex_client.import_rag_files(
                    corpus_name=corpus_resource_name,
                    gcs_uris=[gcs_uri],
                    chunk_size=settings.RAG_CHUNK_SIZE,
                    chunk_overlap=settings.RAG_CHUNK_OVERLAP,
                )
                logger.info(
                    f"Imported {len(corpus_entries)} entries into temporal RAG corpus"
                )

            except Exception as e:
                logger.error(f"Failed to upload corpus data to GCS: {e}")
                # Delete the empty corpus
                await self.vertex_client.delete_rag_corpus(corpus_resource_name)
                raise

            # Store temporal corpus in database temporarily so CategorizationAgent can find it
            # We'll mark it with a special GCS URI pattern to identify temporal corpora
            temporal_corpus = RAGCorpus(
                tenant_id=tenant_id,
                corpus_resource_name=corpus_resource_name,
                corpus_display_name=corpus_display_name,
                gcs_uri=f"gs://temporal-validation/{gcs_filename}",  # Special pattern for temporal
                is_active=True,
                total_chunks=len(corpus_entries),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                status="ready",
                training_job_id=f"temporal_{corpus_name}",
                corpus_metadata={
                    "type": "temporal_validation",
                    "month": corpus_name.split("_")[-1]
                    if "_" in corpus_name
                    else "unknown",
                    "entries": len(corpus_entries),
                    "categories": len(categories_seen),
                },
            )
            # Insert temporal corpus using asyncpg
            await self.conn.execute(
                """
                INSERT INTO rag_corpus (
                    id, tenant_id, corpus_resource_name, corpus_display_name, 
                    gcs_uri, is_active, total_chunks, created_at, updated_at, 
                    status, training_job_id, corpus_metadata
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            """,
                str(uuid.uuid4()),
                temporal_corpus.tenant_id,
                temporal_corpus.corpus_resource_name,
                temporal_corpus.corpus_display_name,
                temporal_corpus.gcs_uri,
                temporal_corpus.is_active,
                temporal_corpus.total_chunks,
                temporal_corpus.created_at,
                temporal_corpus.updated_at,
                temporal_corpus.status,
                temporal_corpus.training_job_id,
                json.dumps(temporal_corpus.corpus_metadata)
                if temporal_corpus.corpus_metadata
                else None,
            )

            logger.info(
                f"✅ Temporal RAG corpus created and activated: {corpus_resource_name}"
            )
            return corpus_resource_name

        except Exception as e:
            logger.error(f"Failed to build temporal RAG corpus: {e}")
            raise ServiceError(
                f"Temporal RAG corpus creation failed: {str(e)}",
                service_name="OnboardingService",
                operation="_build_temporal_rag_corpus",
            )

    async def _deactivate_temporal_corpora(self, tenant_id: int) -> None:
        """Deactivate all temporal validation corpora for a tenant.

        This ensures only one corpus is active at a time during validation.
        """
        try:
            # First deactivate any regular corpus for this tenant using asyncpg
            sql = """
                SELECT * FROM rag_corpus 
                WHERE tenant_id = $1 
                  AND is_active = true 
                  AND gcs_uri NOT LIKE 'gs://temporal-validation/%'
            """
            rows = await self.conn.fetch(sql, tenant_id)
            regular_corpora = [RAGCorpus.model_validate(dict(row)) for row in rows]

            for corpus in regular_corpora:
                # Deactivate using asyncpg
                await self.conn.execute(
                    "UPDATE rag_corpus SET is_active = false WHERE id = $1", corpus.id
                )
                logger.info(
                    f"Temporarily deactivated regular corpus: {corpus.corpus_display_name}"
                )

            # Then deactivate any existing temporal validation corpora using asyncpg
            sql = """
                SELECT * FROM rag_corpus 
                WHERE tenant_id = $1 
                  AND gcs_uri LIKE 'gs://temporal-validation/%' 
                  AND is_active = true
            """
            rows = await self.conn.fetch(sql, tenant_id)
            temporal_corpora = [RAGCorpus.model_validate(dict(row)) for row in rows]

            # Deactivate them using asyncpg
            for corpus in temporal_corpora:
                await self.conn.execute(
                    "UPDATE rag_corpus SET is_active = false WHERE id = $1", corpus.id
                )
                logger.info(
                    f"Deactivated temporal corpus: {corpus.corpus_display_name}"
                )

        except Exception as e:
            logger.error(f"Failed to deactivate temporal corpora: {e}")
            # Continue even if deactivation fails

    async def _cleanup_temporal_corpora(self, tenant_id: int) -> None:
        """Clean up all temporal validation corpora for a tenant.

        This removes temporary corpora created during validation.
        """
        try:
            # Find all temporal validation corpora for this tenant
            sql = """
                SELECT * FROM rag_corpus 
                WHERE tenant_id = $1 AND gcs_uri LIKE $2
            """
            rows = await self.conn.fetch(sql, tenant_id, "gs://temporal-validation/%")
            temporal_corpora = [RAGCorpus.model_validate(dict(row)) for row in rows]

            # Delete them from Vertex AI and database
            for corpus in temporal_corpora:
                try:
                    # Delete from Vertex AI
                    if self.vertex_client:
                        await self.vertex_client.delete_rag_corpus(
                            corpus.corpus_resource_name
                        )

                    # Delete from database
                    # Delete from database using asyncpg
                    await self.conn.execute(
                        "DELETE FROM rag_corpus WHERE id = $1", corpus.id
                    )
                    logger.info(
                        f"Deleted temporal corpus: {corpus.corpus_display_name}"
                    )

                except Exception as e:
                    logger.error(
                        f"Failed to delete corpus {corpus.corpus_display_name}: {e}"
                    )

            # No commit needed with asyncpg auto-commit
            logger.info(f"✅ Cleaned up {len(temporal_corpora)} temporal corpora")

        except Exception as e:
            logger.error(f"Failed to cleanup temporal corpora: {e}")

    async def _categories_match_with_schema_mapping(
        self,
        predicted_category: str,
        actual_category: str,
        tenant_id: Optional[int] = None,
    ) -> bool:
        """
        Check if predicted unified category matches actual original category using schema mappings.

        This is the CRITICAL function that fixes accuracy measurement to use customer's original schema.
        Instead of direct string comparison, it uses the bidirectional mappings from schema discovery.

        Args:
            predicted_category: AI predicted category (unified schema)
            actual_category: Customer's original category label
            tenant_id: Tenant ID for schema lookup

        Returns:
            True if categories match according to schema mapping, False otherwise
        """
        # Sanitize input categories
        if not predicted_category or not actual_category:
            logger.debug(
                f"Empty category detected: predicted='{predicted_category}', actual='{actual_category}'"
            )
            return False

        predicted_clean = str(predicted_category).strip()
        actual_clean = str(actual_category).strip()

        # If categories are identical strings, they match
        if predicted_clean == actual_clean:
            logger.debug(f"Exact match: '{predicted_clean}' == '{actual_clean}'")
            return True

        # If no tenant_id provided, fall back to enhanced semantic comparison
        if not tenant_id:
            logger.debug(
                f"No tenant_id provided, using semantic comparison for '{predicted_clean}' vs '{actual_clean}'"
            )
            return self._semantic_category_match(predicted_clean, actual_clean)

        try:
            # Get the RAG corpus with schema discovery mappings for this tenant
            sql = "SELECT * FROM rag_corpus WHERE tenant_id = $1 LIMIT 1"
            row = await self.conn.fetchrow(sql, tenant_id)
            rag_corpus = RAGCorpus.model_validate(dict(row)) if row else None

            if not rag_corpus:
                logger.debug(
                    f"No RAG corpus found for tenant {tenant_id}, using semantic comparison"
                )
                return self._semantic_category_match(predicted_clean, actual_clean)

            # Check if schema mappings are available
            unified_mapping = rag_corpus.unified_category_mapping
            reverse_mapping = rag_corpus.reverse_category_mapping

            if not unified_mapping and not reverse_mapping:
                logger.debug(
                    f"No schema mappings found for tenant {tenant_id}, using semantic comparison"
                )
                return self._semantic_category_match(predicted_clean, actual_clean)

            # Initialize mappings as empty dicts if None
            unified_mapping = unified_mapping or {}
            reverse_mapping = reverse_mapping or {}

            logger.info(
                f"🔍 Schema mapping check for tenant {tenant_id}: '{predicted_clean}' vs '{actual_clean}'"
            )
            logger.info(
                f"📊 Available unified mappings: {len(unified_mapping)} entries"
            )
            logger.info(
                f"📊 Available reverse mappings: {len(reverse_mapping)} entries"
            )

            # DEBUG: Log some sample mappings to understand the data
            if len(reverse_mapping) > 0:
                sample_reverse = list(reverse_mapping.items())[:3]
                logger.info(f"🔍 Sample reverse mappings: {sample_reverse}")
            if len(unified_mapping) > 0:
                sample_unified = list(unified_mapping.items())[:3]
                logger.info(f"🔍 Sample unified mappings: {sample_unified}")

            # Method 1: Check if predicted unified category maps back to actual original category
            if predicted_clean in reverse_mapping:
                mapped_originals = reverse_mapping[predicted_clean]
                logger.debug(
                    f"Found reverse mapping for '{predicted_clean}': {mapped_originals}"
                )

                if isinstance(mapped_originals, list):
                    match_found = actual_clean in mapped_originals
                    logger.debug(
                        f"List mapping result: {actual_clean} in {mapped_originals} = {match_found}"
                    )
                    if match_found:
                        return True
                else:
                    match_found = actual_clean == mapped_originals
                    logger.debug(
                        f"Direct mapping result: {actual_clean} == {mapped_originals} = {match_found}"
                    )
                    if match_found:
                        return True

            # Method 2: Check if actual original category maps to predicted unified category
            if actual_clean in unified_mapping:
                mapped_unified = unified_mapping[actual_clean]
                match_found = predicted_clean == mapped_unified
                logger.debug(
                    f"Unified mapping check: '{actual_clean}' -> '{mapped_unified}' == '{predicted_clean}' = {match_found}"
                )
                if match_found:
                    return True

            # Method 3: Enhanced semantic comparison for unmapped categories
            semantic_match = self._semantic_category_match(
                predicted_clean, actual_clean
            )
            logger.debug(
                f"Semantic comparison result for '{predicted_clean}' vs '{actual_clean}': {semantic_match}"
            )
            return semantic_match

        except Exception as e:
            logger.error(
                f"Schema mapping lookup failed for tenant {tenant_id}: {e}",
                exc_info=True,
            )
            # Fall back to enhanced semantic comparison
            return self._semantic_category_match(predicted_clean, actual_clean)

    def _semantic_category_match(self, category1: str, category2: str) -> bool:
        """
        Perform enhanced semantic matching between categories when no explicit mapping exists.

        This handles cases like "Employee Costs" vs "Payroll" that are semantically equivalent.
        """
        if not category1 or not category2:
            return False

        # Normalize categories
        c1 = str(category1).lower().strip()
        c2 = str(category2).lower().strip()

        # Direct match
        if c1 == c2:
            return True

        # Enhanced common semantic equivalences in financial categories
        semantic_groups = [
            # Employee-related
            {
                "employee costs",
                "payroll",
                "salaries",
                "wages",
                "staff costs",
                "personnel",
                "hr costs",
                "employee benefits",
                "compensation",
            },
            # Office supplies
            {
                "office supplies",
                "supplies",
                "office expenses",
                "stationery",
                "office materials",
                "consumables",
            },
            # Travel-related
            {
                "travel",
                "travel expenses",
                "business travel",
                "transportation",
                "travel costs",
                "commuting",
                "mileage",
                "airfare",
                "lodging",
            },
            # Professional services
            {
                "professional services",
                "consulting",
                "professional fees",
                "legal fees",
                "advisory services",
                "contractor fees",
                "consulting fees",
            },
            # Marketing
            {
                "marketing",
                "advertising",
                "marketing expenses",
                "promotion",
                "promotional costs",
                "ad spend",
                "branding",
            },
            # Utilities
            {
                "utilities",
                "utility bills",
                "electricity",
                "gas",
                "water",
                "phone",
                "internet",
                "telecommunications",
                "power",
            },
            # Rent and lease
            {
                "rent",
                "rental",
                "office rent",
                "lease",
                "lease payments",
                "facility costs",
                "space rental",
            },
            # Insurance
            {
                "insurance",
                "insurance premiums",
                "business insurance",
                "liability insurance",
                "property insurance",
            },
            # Meals and entertainment
            {
                "meals",
                "food",
                "dining",
                "entertainment",
                "meals and entertainment",
                "client meals",
                "catering",
                "business meals",
            },
            # Technology
            {
                "software",
                "subscriptions",
                "saas",
                "technology",
                "it expenses",
                "licenses",
                "software licenses",
                "cloud services",
            },
            # Maintenance and repairs
            {
                "maintenance",
                "repairs",
                "upkeep",
                "maintenance and repairs",
                "facility maintenance",
                "equipment maintenance",
            },
            # Equipment
            {
                "equipment",
                "machinery",
                "tools",
                "hardware",
                "computer equipment",
                "office equipment",
            },
            # Banking and finance
            {
                "bank fees",
                "banking",
                "financial services",
                "loan payments",
                "interest",
                "finance charges",
            },
            # Communications
            {
                "phone",
                "telecommunications",
                "internet",
                "communications",
                "mobile",
                "landline",
            },
        ]

        # Check if both categories belong to the same semantic group
        for group in semantic_groups:
            if c1 in group and c2 in group:
                logger.debug(
                    f"Semantic group match found: '{c1}' and '{c2}' both in {group}"
                )
                return True

        # Enhanced substring matching for compound categories
        # Check for partial matches with common words
        common_words = ["costs", "expenses", "fees", "payments", "bills", "charges"]

        # Remove common words for comparison
        c1_clean = c1
        c2_clean = c2
        for word in common_words:
            c1_clean = c1_clean.replace(word, "").strip()
            c2_clean = c2_clean.replace(word, "").strip()

        # Check if cleaned versions match
        if c1_clean and c2_clean and c1_clean == c2_clean:
            logger.debug(
                f"Match after removing common words: '{c1}' -> '{c1_clean}' == '{c2}' -> '{c2_clean}'"
            )
            return True

        # Substring matching (one category contains the other)
        if len(c1) > 3 and len(c2) > 3:  # Avoid matching very short strings
            if c1 in c2 or c2 in c1:
                logger.debug(
                    f"Substring match: '{c1}' contains or is contained in '{c2}'"
                )
                return True

        # Check for partial word matches (split on spaces and check for overlap)
        words1 = set(c1.split())
        words2 = set(c2.split())

        # Remove very common words that shouldn't count as matches
        stop_words = {
            "and",
            "or",
            "the",
            "of",
            "for",
            "to",
            "in",
            "on",
            "at",
            "by",
            "with",
        }
        words1 = words1 - stop_words
        words2 = words2 - stop_words

        # Check for significant word overlap (at least 50% of words match)
        if words1 and words2:
            overlap = len(words1.intersection(words2))
            min_words = min(len(words1), len(words2))
            if min_words > 0 and overlap / min_words >= 0.5:
                logger.debug(
                    f"Word overlap match: {overlap}/{min_words} words match between '{c1}' and '{c2}'"
                )
                return True

        return False

    async def diagnose_schema_mappings(self, tenant_id: int) -> Dict[str, Any]:
        """
        Diagnostic function to check the state of schema mappings for a tenant.

        This helps debug accuracy measurement issues by providing visibility into
        the schema discovery data.
        """
        try:
            # Get RAG corpus for tenant
            sql = "SELECT * FROM rag_corpus WHERE tenant_id = $1 LIMIT 1"
            row = await self.conn.fetchrow(sql, tenant_id)
            rag_corpus = RAGCorpus.model_validate(dict(row)) if row else None

            if not rag_corpus:
                return {
                    "tenant_id": tenant_id,
                    "corpus_exists": False,
                    "error": "No RAG corpus found for tenant",
                }

            # Analyze schema mappings
            unified_mapping = rag_corpus.unified_category_mapping or {}
            reverse_mapping = rag_corpus.reverse_category_mapping or {}
            customer_schemas = rag_corpus.customer_category_schemas or []

            # Count mappings
            total_unified_mappings = len(unified_mapping)
            total_reverse_mappings = len(reverse_mapping)
            total_customer_categories = sum(
                len(schema.get("discovered_categories", []))
                for schema in customer_schemas
            )

            # Sample some mappings for inspection
            sample_unified = (
                dict(list(unified_mapping.items())[:5]) if unified_mapping else {}
            )
            sample_reverse = (
                dict(list(reverse_mapping.items())[:5]) if reverse_mapping else {}
            )

            return {
                "tenant_id": tenant_id,
                "corpus_exists": True,
                "corpus_resource_name": rag_corpus.corpus_resource_name,
                "corpus_display_name": rag_corpus.corpus_display_name,
                "schema_discovery_session_id": rag_corpus.schema_discovery_session_id,
                "total_unified_mappings": total_unified_mappings,
                "total_reverse_mappings": total_reverse_mappings,
                "total_customer_categories": total_customer_categories,
                "schemas_discovered": len(customer_schemas),
                "sample_unified_mappings": sample_unified,
                "sample_reverse_mappings": sample_reverse,
                "customer_schema_files": [
                    schema.get("file_name", "unknown") for schema in customer_schemas
                ],
                "last_rebuild": rag_corpus.last_rebuild_at,
                "is_active": rag_corpus.is_active,
            }

        except Exception as e:
            logger.error(
                f"Failed to diagnose schema mappings for tenant {tenant_id}: {e}"
            )
            return {
                "tenant_id": tenant_id,
                "error": str(e),
                "corpus_exists": False,
            }

    async def _get_rag_corpus_status(self, tenant_id: int) -> RAGCorpusStatus:
        """Get RAG corpus status for a tenant."""

        # Check database first
        sql = (
            "SELECT * FROM rag_corpus WHERE tenant_id = $1 AND is_active = true LIMIT 1"
        )
        row = await self.conn.fetchrow(sql, tenant_id)
        corpus_record = RAGCorpus.model_validate(dict(row)) if row else None

        if corpus_record:
            # Update cache
            self._rag_corpus_cache[tenant_id] = corpus_record.corpus_resource_name

            return RAGCorpusStatus(
                tenant_id=tenant_id,
                corpus_exists=True,
                corpus_name=corpus_record.corpus_resource_name,
                created_at=corpus_record.created_at,
                last_updated=corpus_record.updated_at,
                total_patterns=corpus_record.total_patterns,
                unique_categories=corpus_record.unique_categories,
                source_files=[corpus_record.gcs_uri] if corpus_record.gcs_uri else [],
            )

        # Fallback: Check cache
        corpus_resource_name = self._rag_corpus_cache.get(tenant_id)

        # Fallback: Try to load from metadata file
        corpus_metadata_file = (
            Path("data/rag_corpus") / f"tenant-{tenant_id}-metadata.json"
        )
        if corpus_metadata_file.exists() and not corpus_resource_name:
            try:
                with open(corpus_metadata_file, "r") as f:
                    metadata = json.load(f)
                    corpus_resource_name = metadata.get("corpus_resource_name")
                    if corpus_resource_name:
                        # Update cache
                        self._rag_corpus_cache[tenant_id] = corpus_resource_name

                        # Get corpus info from file system
                        corpus_dir = Path("data/rag_corpus")
                        tenant_files = list(
                            corpus_dir.glob(f"tenant-{tenant_id}-corpus-*.jsonl")
                        )
                        if tenant_files:
                            latest_file = max(
                                tenant_files, key=lambda f: f.stat().st_mtime
                            )

                            # Count patterns
                            with open(latest_file, "r") as f:
                                lines = f.readlines()

                            categories = set()
                            for line in lines:
                                try:
                                    entry = json.loads(line)
                                    if "category" in entry:
                                        categories.add(entry["category"])
                                except Exception:
                                    pass

                            return RAGCorpusStatus(
                                tenant_id=tenant_id,
                                corpus_exists=True,
                                corpus_name=corpus_resource_name,  # Use resource name
                                created_at=datetime.fromisoformat(
                                    metadata.get(
                                        "created_at",
                                        datetime.now(timezone.utc).isoformat(),
                                    )
                                ),
                                last_updated=datetime.fromtimestamp(
                                    latest_file.stat().st_mtime
                                ),
                                total_patterns=len(lines),
                                unique_categories=len(categories),
                                source_files=metadata.get(
                                    "source_files", [metadata.get("gcs_uri")]
                                ),
                            )
            except Exception as e:
                logger.warning(f"Failed to load corpus metadata: {e}")

        # Fallback: Check file system for legacy format
        corpus_dir = Path("data/rag_corpus")
        if corpus_dir.exists():
            tenant_files = list(corpus_dir.glob(f"tenant-{tenant_id}-corpus-*.jsonl"))
            if tenant_files:
                latest_file = max(tenant_files, key=lambda f: f.stat().st_mtime)
                corpus_display_name = latest_file.stem

                # Count patterns
                with open(latest_file, "r") as f:
                    lines = f.readlines()

                categories = set()
                for line in lines:
                    try:
                        entry = json.loads(line)
                        if "category" in entry:
                            categories.add(entry["category"])
                    except Exception:
                        pass

                # For legacy files, we don't have the resource name, only display name
                logger.warning(
                    f"Found legacy corpus file without resource name for tenant {tenant_id}"
                )

                return RAGCorpusStatus(
                    tenant_id=tenant_id,
                    corpus_exists=True,
                    corpus_name=corpus_display_name,  # This is display name, not resource name
                    created_at=datetime.fromtimestamp(latest_file.stat().st_ctime),
                    last_updated=datetime.fromtimestamp(latest_file.stat().st_mtime),
                    total_patterns=len(lines),
                    unique_categories=len(categories),
                )

        return RAGCorpusStatus(tenant_id=tenant_id, corpus_exists=False)

    async def approve_for_production(
        self, tenant_id: int, validation_id: str, approval_notes: Optional[str] = None
    ) -> OnboardingStatus:
        """Approve tenant for production usage after successful validation."""

        # Verify validation results exist
        validation_result = await self.conn.fetchrow(
            "SELECT average_accuracy, meets_threshold FROM temporal_validations WHERE validation_id = $1",
            validation_id,
        )

        if not validation_result:
            raise ValueError(f"Validation {validation_id} not found")

        if not validation_result["meets_threshold"]:
            raise ValueError(
                f"Validation {validation_id} did not meet accuracy threshold"
            )

        # Update tenant status in database
        await self.conn.execute(
            """
            INSERT INTO onboarding_status (tenant_id, stage, approved_for_production, approved_at, approval_notes, last_validation_id, last_validation_accuracy)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            ON CONFLICT (tenant_id) 
            DO UPDATE SET 
                stage = $2,
                approved_for_production = $3,
                approved_at = $4,
                approval_notes = $5,
                last_validation_id = $6,
                last_validation_accuracy = $7,
                updated_at = NOW()
            """,
            tenant_id,
            "completed",
            True,
            datetime.now(timezone.utc).replace(tzinfo=None),
            approval_notes,
            validation_id,
            validation_result["average_accuracy"],
        )

        status = await self.get_onboarding_status(tenant_id)
        status.approved_for_production = True
        status.approved_at = datetime.now(timezone.utc).replace(tzinfo=None)
        status.approval_notes = approval_notes
        status.onboarding_stage = "completed"
        status.is_onboarding_complete = True

        logger.info(
            f"Tenant {tenant_id} approved for production usage with validation {validation_id} (accuracy: {validation_result['average_accuracy']:.3f})"
        )

        return status

    # ===============================
    # SCHEMA DISCOVERY METHODS (NEW)
    # ===============================

    async def discover_customer_schemas_from_files(
        self, tenant_id: int, file_data_list: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Discover customer categorization schemas from ALL uploaded files simultaneously.

        This is the key method that implements the critical architecture fix:
        - Process ALL customer files together to discover complete schema
        - Build tenant-specific unified schema from customer's actual categorization patterns
        - Maintain bidirectional mappings between original and unified schemas

        Args:
            tenant_id: Tenant identifier
            file_data_list: List of dicts with keys: file_name, headers, sample_data

        Returns:
            Dict with schema discovery results
        """
        session_id = f"schema_discovery_{tenant_id}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"

        try:
            logger.info(
                f"Starting schema discovery for tenant {tenant_id} with {len(file_data_list)} files"
            )

            # Step 1: Discover category schemas from each file using existing AI tools
            discovered_schemas = []
            for file_data in file_data_list:
                try:
                    schema = await self._discover_single_file_schema(
                        tenant_id, file_data
                    )
                    if schema:
                        discovered_schemas.append(schema)
                except Exception as e:
                    logger.error(
                        f"Failed to discover schema from {file_data.get('file_name', file_data.get('filename', 'unknown_file'))}: {e}"
                    )
                    continue

            if not discovered_schemas:
                logger.warning(f"No category schemas discovered for tenant {tenant_id}")
                return {
                    "session_id": session_id,
                    "success": False,
                    "error": "No category schemas found in uploaded files",
                    "schemas_discovered": 0,
                }

            # Step 2: Create unified schema from all discovered schemas
            unified_mapping = await self._create_unified_category_mapping(
                discovered_schemas
            )

            # Step 3: Update RAG corpus with schema discovery metadata
            await self._update_rag_corpus_with_schema_data(
                tenant_id, session_id, discovered_schemas, unified_mapping
            )

            logger.info(
                f"Schema discovery completed for tenant {tenant_id}: {len(discovered_schemas)} schemas"
            )

            return {
                "session_id": session_id,
                "success": True,
                "schemas_discovered": len(discovered_schemas),
                "total_categories": sum(
                    len(s["discovered_categories"]) for s in discovered_schemas
                ),
                "unified_categories": len(unified_mapping["unified_categories"]),
                "discovered_schemas": discovered_schemas,
                "unified_mapping": unified_mapping,
            }

        except Exception as e:
            logger.error(f"Schema discovery failed for tenant {tenant_id}: {e}")
            raise ServiceError(
                f"Schema discovery failed: {e}",
                service_name="onboarding",
                operation="schema_discovery",
            )

    async def _discover_single_file_schema(
        self, tenant_id: int, file_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Discover categorization schema from a single file using existing AI tools.
        """
        try:
            # Use existing schema interpretation agent (REUSE existing code)
            mapping_result = await suggest_schema_mapping_tool_function(
                file_name=file_data["file_name"],
                file_headers=file_data["headers"],
                sample_data=file_data["sample_data"],
            )

            # Extract category columns from mapping
            category_columns = []
            for mapping in mapping_result.get("column_mappings", []):
                field_name = mapping.get("mapped_field", "")
                if "category" in field_name.lower():
                    category_columns.append(
                        {
                            "original_name": mapping["original_name"],
                            "mapped_field": field_name,
                            "confidence": mapping.get("confidence", 0.0),
                        }
                    )

            if not category_columns:
                logger.info(
                    f"No category columns found in {file_data.get('file_name', file_data.get('filename', 'unknown_file'))}"
                )
                return None

            # Extract unique categories from sample data
            discovered_categories = set()
            category_frequency = {}

            for row in file_data["sample_data"]:
                for cat_col in category_columns:
                    try:
                        col_index = file_data["headers"].index(cat_col["original_name"])
                        if col_index < len(row) and row[col_index]:
                            category_value = str(row[col_index]).strip()
                            if category_value and category_value.lower() not in [
                                "",
                                "nan",
                                "null",
                                "none",
                            ]:
                                discovered_categories.add(category_value)
                                category_frequency[category_value] = (
                                    category_frequency.get(category_value, 0) + 1
                                )
                    except (ValueError, IndexError):
                        continue

            if not discovered_categories:
                logger.info(
                    f"No category values found in {file_data.get('file_name', file_data.get('filename', 'unknown_file'))}"
                )
                return None

            # Detect hierarchy pattern using existing AI (REUSE existing code)
            hierarchy_info = await self._detect_category_hierarchy_ai(
                list(discovered_categories)
            )

            logger.info(
                f"Discovered {len(discovered_categories)} categories from {file_data.get('file_name', file_data.get('filename', 'unknown_file'))}"
            )

            return {
                "file_name": file_data.get(
                    "file_name", file_data.get("filename", "unknown_file")
                ),
                "discovered_categories": list(discovered_categories),
                "category_frequency": category_frequency,
                "category_columns": category_columns,
                "hierarchy_info": hierarchy_info,
                "total_categories": len(discovered_categories),
                "confidence_score": mapping_result.get("overall_confidence", 0.0),
            }

        except Exception as e:
            logger.error(
                f"Failed to discover schema from {file_data.get('file_name', file_data.get('filename', 'unknown_file'))}: {e}"
            )
            return None

    async def _detect_category_hierarchy_ai(
        self, categories: List[str]
    ) -> Dict[str, Any]:
        """
        Detect hierarchical patterns in categories using AI.
        REUSES existing hierarchy detection logic.
        """
        try:
            # Use existing AI hierarchy detection from schema interpretation agent
            from ..files.schema_interpretation_agent import _detect_hierarchy_with_ai

            hierarchy_result = await _detect_hierarchy_with_ai(categories)
            return hierarchy_result

        except Exception as e:
            logger.warning(f"Hierarchy detection failed: {e}")
            return {
                "is_hierarchical": False,
                "separator": None,
                "pattern_type": "flat",
                "confidence": 0.0,
                "reasoning": f"Detection failed: {e}",
            }

    async def _create_unified_category_mapping(
        self, discovered_schemas: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Create unified category mapping by merging all discovered schemas.
        """
        try:
            # Collect all categories from all schemas
            all_categories = []
            for schema in discovered_schemas:
                all_categories.extend(schema["discovered_categories"])

            # Use AI to create semantic groupings (REUSE existing AI patterns)
            unified_categories = await self._create_semantic_category_groupings(
                all_categories
            )

            # Create category mappings (original -> unified)
            category_mappings = {}
            reverse_mappings = {}

            for original_cat in all_categories:
                unified_cat = self._find_best_unified_category(
                    original_cat, unified_categories
                )
                category_mappings[original_cat] = unified_cat

                if unified_cat not in reverse_mappings:
                    reverse_mappings[unified_cat] = []
                if original_cat not in reverse_mappings[unified_cat]:
                    reverse_mappings[unified_cat].append(original_cat)

            logger.info(
                f"Created unified mapping: {len(all_categories)} original -> {len(unified_categories)} unified"
            )

            return {
                "unified_categories": unified_categories,
                "category_mappings": category_mappings,
                "reverse_mappings": reverse_mappings,
                "total_original_categories": len(set(all_categories)),
                "total_unified_categories": len(unified_categories),
            }

        except Exception as e:
            logger.error(f"Failed to create unified mapping: {e}")
            raise ServiceError(f"Unified mapping creation failed: {e}")

    async def _create_semantic_category_groupings(
        self, all_categories: List[str]
    ) -> List[str]:
        """
        Use AI to create semantic groupings of similar categories.
        REUSES existing AI patterns from the codebase.
        """
        try:
            from vertexai.generative_models import GenerativeModel

            # Remove duplicates while preserving order
            unique_categories = list(dict.fromkeys(all_categories))

            if len(unique_categories) <= 10:
                # Small number - return as is
                return unique_categories

            # Use AI to group semantically similar categories (REUSE existing AI patterns)
            model = GenerativeModel("gemini-2.0-flash-001")

            prompt = f"""
            You are an expert at analyzing financial categorization patterns.
            
            Categories to analyze: {unique_categories}
            
            Group these categories into semantic clusters where categories mean the same thing.
            Choose the best representative name for each cluster that preserves customer intent.
            
            Return JSON format:
            {{
                "unified_categories": [
                    "Representative Category 1",
                    "Representative Category 2"
                ],
                "reasoning": "Brief explanation of grouping decisions"
            }}
            
            JSON:"""

            response = await model.generate_content_async(
                prompt,
                generation_config={
                    "temperature": 0.1,
                    "max_output_tokens": 2000,
                },
            )

            import json

            result = json.loads(response.text.strip())
            unified_categories = result.get("unified_categories", unique_categories)

            logger.info(
                f"AI grouped {len(unique_categories)} categories into {len(unified_categories)} unified categories"
            )
            return unified_categories

        except Exception as e:
            logger.warning(f"Semantic grouping failed, using original categories: {e}")
            return list(dict.fromkeys(all_categories))

    def _find_best_unified_category(
        self, original_category: str, unified_categories: List[str]
    ) -> str:
        """
        Find the best unified category for an original category.
        """
        # Simple approach: find exact match first, then semantic similarity
        if original_category in unified_categories:
            return original_category

        # Use simple text similarity for now
        original_lower = original_category.lower()

        for unified_cat in unified_categories:
            if (
                original_lower in unified_cat.lower()
                or unified_cat.lower() in original_lower
            ):
                return unified_cat

        # If no good match, return the original category (preserves customer intent)
        return original_category

    async def _update_rag_corpus_with_schema_data(
        self,
        tenant_id: int,
        session_id: str,
        discovered_schemas: List[Dict[str, Any]],
        unified_mapping: Dict[str, Any],
    ) -> None:
        """
        Update existing RAG corpus with schema discovery metadata.
        EXTENDS existing RAG corpus instead of creating new tables.
        """
        try:
            # Get existing RAG corpus
            sql = "SELECT * FROM rag_corpus WHERE tenant_id = $1 LIMIT 1"
            row = await self.conn.fetchrow(sql, tenant_id)
            corpus = RAGCorpus.model_validate(dict(row)) if row else None

            if corpus:
                # Update existing corpus with schema data
                corpus.customer_category_schemas = discovered_schemas
                corpus.unified_category_mapping = unified_mapping["category_mappings"]
                corpus.reverse_category_mapping = unified_mapping["reverse_mappings"]
                corpus.source_file_schemas = [
                    {
                        "file_name": s["file_name"],
                        "categories": s["discovered_categories"],
                        "confidence": s["confidence_score"],
                    }
                    for s in discovered_schemas
                ]
                corpus.schema_discovery_session_id = session_id

                # No commit needed with asyncpg auto-commit
                logger.info(
                    f"Updated RAG corpus with schema discovery data for tenant {tenant_id}"
                )
            else:
                logger.warning(
                    f"No RAG corpus found for tenant {tenant_id} - schema data not stored"
                )

        except Exception as e:
            logger.error(f"Failed to update RAG corpus with schema data: {e}")
            # Don't raise error here - schema discovery can succeed without corpus update

    # ===============================
    # REPORTING SERVICE METHODS (NEW)
    # ===============================

    async def get_file_processing_report(
        self, upload_id: str
    ) -> Optional[FileProcessingReport]:
        """Get detailed processing report for a specific upload."""
        sql = "SELECT * FROM file_processing_report WHERE upload_id = $1 LIMIT 1"
        row = await self.conn.fetchrow(sql, upload_id)
        return FileProcessingReport.model_validate(dict(row)) if row else None

    async def get_column_statistics(self, upload_id: str) -> List[ColumnStatistic]:
        """Get column statistics for a specific upload."""
        # First get the report
        report = await self.get_file_processing_report(upload_id)
        if not report:
            return []

        sql = """
            SELECT * FROM column_statistic 
            WHERE report_id = $1 
            ORDER BY column_index
        """
        rows = await self.conn.fetch(sql, report.id)
        return [ColumnStatistic.model_validate(dict(row)) for row in rows]

    async def get_row_processing_details(
        self,
        upload_id: str,
        page: int = 1,
        page_size: int = 100,
        status_filter: Optional[str] = None,
        has_errors: Optional[bool] = None,
    ) -> Dict[str, Any]:
        """Get paginated row processing details."""
        # First get the report
        report = await self.get_file_processing_report(upload_id)
        if not report:
            return {
                "rows": [],
                "total_rows": 0,
                "page": page,
                "page_size": page_size,
                "total_pages": 0,
            }

        # Build dynamic query conditions
        where_conditions = ["report_id = $1"]
        params = [report.id]
        param_count = 1

        if status_filter:
            param_count += 1
            where_conditions.append(f"status = ${param_count}")
            params.append(status_filter)

        if has_errors is not None:
            if has_errors:
                where_conditions.append("validation_errors != '[]'::jsonb")
            else:
                where_conditions.append("validation_errors = '[]'::jsonb")

        where_clause = " AND ".join(where_conditions)

        # Count total rows
        count_sql = f"SELECT COUNT(*) FROM row_processing_detail WHERE {where_clause}"
        total_rows = await self.conn.fetchval(count_sql, *params) or 0

        # Apply pagination
        offset = (page - 1) * page_size

        # Get paginated rows
        param_count += 1
        limit_param = param_count
        param_count += 1
        offset_param = param_count

        data_sql = f"""
            SELECT * FROM row_processing_detail 
            WHERE {where_clause} 
            ORDER BY row_number 
            LIMIT ${limit_param} OFFSET ${offset_param}
        """
        rows_data = await self.conn.fetch(data_sql, *params, page_size, offset)
        rows = [RowProcessingDetail.model_validate(dict(row)) for row in rows_data]

        total_pages = (total_rows + page_size - 1) // page_size

        return {
            "rows": rows,
            "total_rows": total_rows,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
        }

    async def get_schema_mapping_report(self, tenant_id: int) -> Dict[str, Any]:
        """Get comprehensive schema mapping report for a tenant."""
        # Get RAG corpus with schema data
        sql = "SELECT * FROM rag_corpus WHERE tenant_id = $1 LIMIT 1"
        row = await self.conn.fetchrow(sql, tenant_id)
        corpus = RAGCorpus.model_validate(dict(row)) if row else None

        if not corpus:
            return {
                "tenant_id": tenant_id,
                "has_schema_discovery": False,
                "schemas_discovered": 0,
                "total_categories": 0,
                "unified_categories": 0,
                "source_files": [],
                "file_schemas": [],
                "category_mappings": {},
                "reverse_mappings": {},
                "mapping_confidence": 0.0,
                "unmapped_categories": [],
            }

        # Extract schema information
        customer_schemas = corpus.customer_category_schemas or []
        unified_mapping = corpus.unified_category_mapping or {}
        reverse_mapping = corpus.reverse_category_mapping or {}

        # Calculate metrics
        total_categories = sum(
            len(schema.get("discovered_categories", [])) for schema in customer_schemas
        )
        unified_categories = len(set(unified_mapping.values()))

        # Find unmapped categories
        all_original_categories = set()
        for schema in customer_schemas:
            all_original_categories.update(schema.get("discovered_categories", []))

        mapped_categories = set(unified_mapping.keys())
        unmapped_categories = list(all_original_categories - mapped_categories)

        # Calculate mapping confidence
        mapping_confidence = (
            len(mapped_categories) / len(all_original_categories)
            if all_original_categories
            else 0.0
        )

        return {
            "tenant_id": tenant_id,
            "has_schema_discovery": True,
            "session_id": corpus.schema_discovery_session_id,
            "schemas_discovered": len(customer_schemas),
            "total_categories": total_categories,
            "unified_categories": unified_categories,
            "source_files": [s.get("file_name", "unknown") for s in customer_schemas],
            "file_schemas": customer_schemas,
            "category_mappings": unified_mapping,
            "reverse_mappings": reverse_mapping,
            "mapping_confidence": mapping_confidence,
            "unmapped_categories": unmapped_categories,
            "created_at": corpus.created_at,
            "last_updated": corpus.updated_at,
        }

    async def get_recent_processing_reports(
        self, tenant_id: int, limit: int = 10
    ) -> List[FileProcessingReport]:
        """Get recent processing reports for a tenant."""
        import json

        sql = """
            SELECT * FROM file_processing_reports 
            WHERE tenant_id = $1 
            ORDER BY created_at DESC 
            LIMIT $2
        """
        rows = await self.conn.fetch(sql, tenant_id, limit)

        # Parse JSONB columns that come back as strings from asyncpg
        reports = []
        for row in rows:
            row_dict = dict(row)

            # Parse JSONB string columns to proper JSON objects
            jsonb_columns = [
                "validation_errors",
                "categories_discovered",
                "warnings",
                "column_mapping_confidence",
                "mapped_columns",
                "unmapped_columns",
            ]

            for col in jsonb_columns:
                if col in row_dict and row_dict[col] is not None:
                    if isinstance(row_dict[col], str):
                        try:
                            row_dict[col] = json.loads(row_dict[col])
                        except json.JSONDecodeError:
                            # If parsing fails, default to empty list for list fields
                            if col in [
                                "validation_errors",
                                "categories_discovered",
                                "warnings",
                                "mapped_columns",
                                "unmapped_columns",
                            ]:
                                row_dict[col] = []
                            else:
                                row_dict[col] = {}

            reports.append(FileProcessingReport.model_validate(row_dict))

        return reports

    async def translate_category_to_customer_original(
        self, tenant_id: int, unified_category: str
    ) -> List[str]:
        """
        Translate unified category back to customer's original categorization.

        This is CRITICAL for measuring accuracy against customer's original schema.
        """
        try:
            sql = "SELECT * FROM rag_corpus WHERE tenant_id = $1 LIMIT 1"
            row = await self.conn.fetchrow(sql, tenant_id)
            corpus = RAGCorpus.model_validate(dict(row)) if row else None

            if not corpus or not corpus.reverse_category_mapping:
                return [unified_category]

            # Get original categories that map to this unified category
            reverse_mapping = corpus.reverse_category_mapping
            original_categories = reverse_mapping.get(
                unified_category, [unified_category]
            )

            logger.debug(f"Translated '{unified_category}' -> {original_categories}")
            return original_categories

        except Exception as e:
            logger.error(f"Category translation failed: {e}")
            return [unified_category]

    async def get_customer_schema_summary(self, tenant_id: int) -> Dict[str, Any]:
        """
        Get summary of discovered customer schemas for a tenant.
        """
        try:
            sql = "SELECT * FROM rag_corpus WHERE tenant_id = $1 LIMIT 1"
            row = await self.conn.fetchrow(sql, tenant_id)
            corpus = RAGCorpus.model_validate(dict(row)) if row else None

            if not corpus or not corpus.customer_category_schemas:
                return {
                    "has_schema_discovery": False,
                    "schemas_discovered": 0,
                    "total_categories": 0,
                }

            schemas = corpus.customer_category_schemas
            total_categories = sum(
                len(s.get("discovered_categories", [])) for s in schemas
            )

            return {
                "has_schema_discovery": True,
                "session_id": corpus.schema_discovery_session_id,
                "schemas_discovered": len(schemas),
                "total_categories": total_categories,
                "source_files": [s.get("file_name", "unknown") for s in schemas],
                "unified_mapping_available": bool(corpus.unified_category_mapping),
            }

        except Exception as e:
            logger.error(f"Failed to get schema summary: {e}")
            return {"has_schema_discovery": False, "error": str(e)}

    async def _batch_insert_row_details(self, row_details: List) -> None:
        """Batch insert row processing details using asyncpg."""
        if not row_details:
            return

        insert_query = """
            INSERT INTO row_processing_details (
                id, report_id, row_number, status, original_data, parsed_description,
                parsed_amount, parsed_date, parsed_category, validation_errors,
                processing_time_ms, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        """

        current_time = datetime.now(timezone.utc).replace(tzinfo=None)

        # Prepare batch data
        batch_data = []
        for detail in row_details:
            batch_data.append(
                [
                    detail.id,
                    detail.report_id,
                    detail.row_number,
                    detail.status,
                    json.dumps(detail.original_data) if detail.original_data else None,
                    detail.parsed_description,
                    detail.parsed_amount,
                    detail.parsed_date,
                    detail.parsed_category,
                    json.dumps(detail.validation_errors)
                    if detail.validation_errors
                    else None,
                    detail.processing_time_ms,
                    current_time,
                    current_time,
                ]
            )

        # Execute batch insert
        await self.conn.executemany(insert_query, batch_data)

    async def _batch_insert_column_stats(self, column_stats: List) -> None:
        """Batch insert column statistics using asyncpg."""
        if not column_stats:
            return

        insert_query = """
            INSERT INTO column_statistics (
                id, report_id, column_name, column_index, mapped_field, mapping_confidence,
                detected_type, type_consistency, total_values, non_null_values, null_values,
                unique_values, min_value, max_value, average_value, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        """

        current_time = datetime.now(timezone.utc).replace(tzinfo=None)

        # Prepare batch data
        batch_data = []
        for stat in column_stats:
            batch_data.append(
                [
                    stat.id,
                    stat.report_id,
                    stat.column_name,
                    stat.column_index,
                    stat.mapped_field,
                    stat.mapping_confidence,
                    stat.detected_type,
                    stat.type_consistency,
                    stat.total_values,
                    stat.non_null_values,
                    stat.null_values,
                    stat.unique_values,
                    stat.min_value,
                    stat.max_value,
                    stat.average_value,
                    current_time,
                    current_time,
                ]
            )

        # Execute batch insert
        await self.conn.executemany(insert_query, batch_data)

    async def save_business_context(
        self, tenant_id: int, business_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Save comprehensive business context for MIS categorization enhancement.

        This enhanced business context is used by:
        - MIS Template Selection Agent for industry-specific templates
        - AI categorization for context-aware accuracy
        - GL code recommendations based on business model
        - Financial reporting customization
        """
        try:
            # Validate and prepare business context data
            prepared_context = {
                # Basic Information
                "industry": business_context.get("industry", ""),
                "company_size": business_context.get("company_size", ""),
                "company_website": business_context.get("company_website", ""),
                "business_description": business_context.get(
                    "business_description", ""
                ),
                # Financial Context
                "fiscal_year_end": business_context.get("fiscal_year_end", "december"),
                "primary_currency": business_context.get("primary_currency", "USD"),
                "accounting_method": business_context.get(
                    "accounting_method", "accrual"
                ),
                "existing_accounting_system": business_context.get(
                    "existing_accounting_system", ""
                ),
                # Geographic Context
                "primary_country": business_context.get("primary_country", "US"),
                "tax_jurisdiction": business_context.get("tax_jurisdiction", "US"),
                # Business Model Context
                "business_model": business_context.get("business_model", ""),
                "revenue_streams": business_context.get("revenue_streams", []),
                # MIS Specific Requirements
                "reporting_requirements": business_context.get(
                    "reporting_requirements", []
                ),
                "gl_code_preference": business_context.get(
                    "gl_code_preference", "4_digit"
                ),
                "department_structure": business_context.get(
                    "department_structure", ""
                ),
                # Metadata
                "context_collected_at": datetime.now(timezone.utc).isoformat(),
                "context_version": "1.0",
            }

            # Update tenant settings with business context
            update_query = """
                UPDATE tenants 
                SET settings = COALESCE(settings, '{}'::jsonb) || $2::jsonb,
                    updated_at = NOW()
                WHERE id = $1
                RETURNING settings
            """

            # Wrap business context in settings structure
            settings_update = {"business_context": prepared_context}

            result = await self.conn.fetchrow(
                update_query, tenant_id, json.dumps(settings_update)
            )

            if result:
                logger.info(f"Business context saved for tenant {tenant_id}")

                # Trigger MIS template application if we have industry information
                if prepared_context.get("industry"):
                    await self._apply_industry_template(tenant_id, prepared_context)

                return {
                    "success": True,
                    "business_context": prepared_context,
                    "message": "Business context saved successfully",
                }
            else:
                raise ServiceError(
                    f"Failed to update business context for tenant {tenant_id}"
                )

        except Exception as e:
            logger.error(f"Failed to save business context for tenant {tenant_id}: {e}")
            raise ServiceError(f"Failed to save business context: {e}")

    async def get_business_context(self, tenant_id: int) -> Dict[str, Any]:
        """
        Retrieve business context for a tenant.
        """
        try:
            query = """
                SELECT settings->'business_context' as business_context
                FROM tenants 
                WHERE id = $1
            """

            result = await self.conn.fetchrow(query, tenant_id)

            if result and result["business_context"]:
                return dict(result["business_context"])
            else:
                # Return default business context structure
                return {
                    "industry": "",
                    "company_size": "",
                    "company_website": "",
                    "business_description": "",
                    "fiscal_year_end": "december",
                    "primary_currency": "USD",
                    "accounting_method": "accrual",
                    "existing_accounting_system": "",
                    "primary_country": "US",
                    "tax_jurisdiction": "US",
                    "business_model": "",
                    "revenue_streams": [],
                    "reporting_requirements": [],
                    "gl_code_preference": "4_digit",
                    "department_structure": "",
                }

        except Exception as e:
            logger.error(f"Failed to get business context for tenant {tenant_id}: {e}")
            return {}

    async def _apply_industry_template(
        self, tenant_id: int, business_context: Dict[str, Any]
    ) -> None:
        """
        Apply industry-specific MIS template based on business context.
        """
        try:
            from ..categories.mis_template_selection_agent import (
                MISTemplateSelectionAgent,
            )

            # Initialize services
            template_agent = MISTemplateSelectionAgent(self.conn)

            # Get industry-specific template
            industry = business_context.get("industry", "")
            if industry:
                logger.info(
                    f"Applying industry template for {industry} to tenant {tenant_id}"
                )

                # Let MIS Template Selection Agent choose and apply the best template
                template_result = await template_agent.select_and_apply_template(
                    tenant_id=tenant_id, business_context=business_context
                )

                if template_result.get("success"):
                    logger.info(
                        f"Successfully applied industry template for tenant {tenant_id}"
                    )
                else:
                    logger.warning(
                        f"Failed to apply industry template for tenant {tenant_id}: {template_result}"
                    )

        except Exception as e:
            logger.error(
                f"Failed to apply industry template for tenant {tenant_id}: {e}"
            )
            # Don't raise - this is a best-effort enhancement

    async def update_onboarding_with_business_context(
        self, tenant_id: int, business_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update onboarding status with business context and trigger appropriate setup.
        """
        try:
            # Save business context
            context_result = await self.save_business_context(
                tenant_id, business_context
            )

            # Update onboarding status to reflect business context collection
            update_query = """
                UPDATE onboarding_status 
                SET last_activity = NOW(),
                    updated_at = NOW()
                WHERE tenant_id = $1
            """
            await self.conn.execute(update_query, tenant_id)

            # Get updated onboarding status
            onboarding_status = await self.get_onboarding_status(tenant_id)

            return {
                "success": True,
                "business_context_saved": context_result["success"],
                "onboarding_status": onboarding_status,
                "message": "Business context integrated with onboarding successfully",
            }

        except Exception as e:
            logger.error(
                f"Failed to update onboarding with business context for tenant {tenant_id}: {e}"
            )
            raise ServiceError(
                f"Failed to update onboarding with business context: {e}"
            )
