"""
Onboarding domain module for temporal accuracy validation workflow.

This module handles the critical customer onboarding process:
1. Bulk historical data upload with existing labels
2. AI column interpretation
3. RAG corpus building from customer labeled data
4. Temporal accuracy validation (July-December 2024)
5. >85% accuracy approval for production usage
"""

from .models import (
    OnboardingStatus as OnboardingStatusModel,
    RAGCorpus,
    TemporalValidation,
)
from .router import router
from .schemas import (
    AccuracyMetrics,
    OnboardingStatus,
    RAGCorpusStatus,
    TemporalValidationRequest,
    TemporalValidationResult,
)
from .service import OnboardingService

__all__ = [
    "router",
    "OnboardingStatus",
    "TemporalValidationRequest",
    "TemporalValidationResult",
    "AccuracyMetrics",
    "RAGCorpusStatus",
    "OnboardingService",
    "TemporalValidation",
    "RAGCorpus",
    "OnboardingStatusModel",
]
