"""
Schemas for the onboarding domain.

Defines data structures for temporal accuracy validation workflow.
"""

from datetime import date, datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class FileColumnMapping(BaseModel):
    """Mapping of file columns to standard transaction fields."""

    date_column: str = Field(..., description="Column containing transaction date")
    description_column: str = Field(
        ..., description="Column containing transaction description"
    )
    amount_column: Optional[str] = Field(
        None, description="Column containing transaction amount (single column)"
    )
    debit_column: Optional[str] = Field(
        None, description="Column containing debit amounts (for dual-column systems)"
    )
    credit_column: Optional[str] = Field(
        None, description="Column containing credit amounts (for dual-column systems)"
    )
    category_column: Optional[str] = Field(
        None, description="Column containing existing category labels"
    )
    vendor_column: Optional[str] = Field(
        None, description="Column containing vendor/merchant names"
    )

    model_config = ConfigDict(from_attributes=True)


class OnboardingFileUpload(BaseModel):
    """Request to upload historical transaction data for onboarding."""

    filename: str = Field(..., description="Name of the uploaded file")
    year: int = Field(..., description="Year of transaction data (e.g., 2024)")
    has_category_labels: bool = Field(
        ..., description="Whether file contains existing category labels"
    )
    column_mapping: Optional[FileColumnMapping] = Field(
        None, description="Manual column mapping if needed"
    )

    model_config = ConfigDict(from_attributes=True)


class MonthlyAccuracyResult(BaseModel):
    """Accuracy results for a specific month."""

    month: str = Field(..., description="Month in YYYY-MM format")
    training_months: List[str] = Field(..., description="Months used for training")
    transactions_tested: int = Field(..., description="Number of transactions tested")
    correct_predictions: int = Field(..., description="Number of correct predictions")
    accuracy: float = Field(..., description="Accuracy percentage (0-100)")
    precision: float = Field(..., description="Precision score")
    recall: float = Field(..., description="Recall score")
    f1_score: float = Field(..., description="F1 score")
    category_breakdown: Dict[str, Dict[str, float]] = Field(
        default_factory=dict, description="Per-category accuracy metrics"
    )

    model_config = ConfigDict(from_attributes=True)


class TemporalValidationRequest(BaseModel):
    """Request to run temporal accuracy validation."""

    tenant_id: int = Field(..., description="Tenant ID for isolation")
    start_month: str = Field(
        default="2024-07", description="Start month for validation (YYYY-MM)"
    )
    end_month: str = Field(
        default="2024-12", description="End month for validation (YYYY-MM)"
    )
    accuracy_threshold: float = Field(
        default=0.85, description="Minimum accuracy threshold"
    )

    model_config = ConfigDict(from_attributes=True)


class TemporalValidationResult(BaseModel):
    """Results of temporal accuracy validation."""

    tenant_id: int
    validation_id: str = Field(..., description="Unique validation run ID")
    started_at: datetime
    completed_at: Optional[datetime] = None
    status: str = Field(..., description="running, completed, failed")
    monthly_results: List[MonthlyAccuracyResult] = Field(default_factory=list)
    average_accuracy: float = Field(
        0.0, description="Average accuracy across all months"
    )
    meets_threshold: bool = Field(False, description="Whether accuracy meets threshold")
    accuracy_threshold: float
    total_training_transactions: Optional[int] = Field(
        None, description="Total transactions used for training"
    )
    total_test_transactions: Optional[int] = Field(
        None, description="Total transactions tested"
    )
    error_message: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class RAGCorpusStatus(BaseModel):
    """Status of RAG corpus for a tenant."""

    tenant_id: int
    corpus_exists: bool = Field(
        False, description="Whether RAG corpus has been created"
    )
    corpus_name: Optional[str] = Field(
        None, description="Name of the corpus in Vertex AI"
    )
    created_at: Optional[datetime] = None
    last_updated: Optional[datetime] = None
    total_patterns: int = Field(
        0, description="Number of categorization patterns in corpus"
    )
    unique_categories: int = Field(0, description="Number of unique categories")
    source_files: List[str] = Field(
        default_factory=list, description="Files used to build corpus"
    )

    model_config = ConfigDict(from_attributes=True)


class TemporalCorpusProgress(BaseModel):
    """Progress tracking for temporal RAG corpus building."""

    month: str = Field(..., description="Month being processed (YYYY-MM)")
    corpus_name: str = Field(..., description="Temporal corpus name")
    training_data_size: int = Field(..., description="Number of training transactions")
    status: str = Field(..., description="building, completed, failed")
    progress_percentage: float = Field(0.0, description="Progress percentage (0-100)")
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class TemporalValidationProgress(BaseModel):
    """Overall progress for temporal validation with progressive RAG corpus building."""

    validation_id: str
    current_phase: str = Field(..., description="corpus_building, testing, completed")
    months_total: int = Field(..., description="Total months to process")
    months_completed: int = Field(0, description="Months completed")
    current_month: Optional[str] = None
    corpus_progress: List[TemporalCorpusProgress] = Field(default_factory=list)
    overall_progress_percentage: float = Field(
        0.0, description="Overall progress (0-100)"
    )
    estimated_completion_time: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class AccuracyMetrics(BaseModel):
    """Detailed accuracy metrics for validation."""

    overall_accuracy: float = Field(..., description="Overall accuracy percentage")
    precision: float = Field(..., description="Weighted average precision")
    recall: float = Field(..., description="Weighted average recall")
    f1_score: float = Field(..., description="Weighted average F1 score")
    confusion_matrix: Dict[str, Dict[str, int]] = Field(
        default_factory=dict, description="Confusion matrix of predictions"
    )
    per_category_metrics: Dict[str, Dict[str, float]] = Field(
        default_factory=dict, description="Metrics broken down by category"
    )

    model_config = ConfigDict(from_attributes=True)


class OnboardingStatus(BaseModel):
    """Complete onboarding status for a tenant."""

    tenant_id: int
    onboarding_type: str = Field(
        "historical_data",
        description="Types: zero_onboarding (M1), historical_data (M2), schema_only (M3)",
    )
    onboarding_stage: str = Field(
        "not_started",
        description="Current stage: not_started, data_uploaded, corpus_building, validating, completed, failed, zero_ready, schema_imported, schema_ready",
    )
    is_onboarding_complete: bool = Field(False)
    last_activity: Optional[datetime] = None

    # Data upload status
    uploaded_files: List[str] = Field(default_factory=list)
    total_transactions: int = Field(0)
    transactions_with_labels: int = Field(0)
    date_range_start: Optional[date] = None
    date_range_end: Optional[date] = None

    # RAG corpus status
    rag_corpus_status: Optional[RAGCorpusStatus] = None

    # Validation status
    validation_runs: List[str] = Field(
        default_factory=list, description="IDs of validation runs"
    )
    latest_validation: Optional[TemporalValidationResult] = None
    meets_accuracy_threshold: bool = Field(False)

    # Production readiness
    approved_for_production: bool = Field(False)
    approved_at: Optional[datetime] = None
    approval_notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class OnboardingStartRequest(BaseModel):
    """Request to start the onboarding process."""

    tenant_id: int
    tenant_name: str
    contact_email: str
    expected_transaction_volume: int = Field(
        ..., description="Expected monthly transaction volume"
    )
    # Business context for AI categorization enhancement
    industry: Optional[str] = Field(
        None, description="Business industry for context-aware categorization"
    )
    company_size: Optional[str] = Field(
        None, description="Company size (startup, small, medium, enterprise)"
    )
    company_website: Optional[str] = Field(
        None, description="Company website URL for business context analysis"
    )
    onboarding_type: Optional[str] = Field(
        "historical_data",
        description="Type of onboarding: zero_onboarding, historical_data, or schema_only",
    )

    model_config = ConfigDict(from_attributes=True)


class OnboardingApprovalRequest(BaseModel):
    """Request to approve tenant for production usage."""

    tenant_id: int
    validation_id: str = Field(..., description="ID of successful validation run")
    approval_notes: Optional[str] = Field(None, description="Notes about the approval")

    model_config = ConfigDict(from_attributes=True)


class OnboardingProgressResponse(BaseModel):
    """Progress response matching frontend expectations."""

    onboarding_id: str
    type: str = Field(
        ..., description="zero_onboarding, schema_guided, or historical_data"
    )
    status: str = Field(
        ..., description="initialized, ai_training, accuracy_validation, or completed"
    )
    progress: float = Field(..., description="Progress percentage (0-100)")
    steps_completed: List[str] = Field(default_factory=list)
    current_step: str
    next_step: str
    ai_training_results: Optional[Dict[str, Any]] = Field(
        None, description="Training results if available"
    )

    model_config = ConfigDict(from_attributes=True)


class ZeroOnboardingStartRequest(BaseModel):
    """Request to start M1 zero-onboarding process."""

    tenant_id: int
    tenant_name: str
    contact_email: str
    business_type: Optional[str] = Field(
        None, description="Type of business (optional)"
    )
    # Business context for AI categorization enhancement
    industry: Optional[str] = Field(
        None, description="Business industry for context-aware categorization"
    )
    company_size: Optional[str] = Field(
        None, description="Company size (startup, small, medium, enterprise)"
    )
    company_website: Optional[str] = Field(
        None, description="Company website URL for business context analysis"
    )

    model_config = ConfigDict(from_attributes=True)


class SchemaOnlyStartRequest(BaseModel):
    """Request to start M3 schema-only onboarding process."""

    tenant_id: int
    tenant_name: str
    contact_email: str
    category_hierarchy_file: Optional[str] = Field(
        None, description="Category hierarchy file path"
    )
    # Business context for AI categorization enhancement
    industry: Optional[str] = Field(
        None, description="Business industry for context-aware categorization"
    )
    company_size: Optional[str] = Field(
        None, description="Company size (startup, small, medium, enterprise)"
    )
    company_website: Optional[str] = Field(
        None, description="Company website URL for business context analysis"
    )

    model_config = ConfigDict(from_attributes=True)
