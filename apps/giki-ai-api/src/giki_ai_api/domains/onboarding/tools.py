"""
Onboarding domain ADK tools.

This module provides ADK tool functions for guided onboarding agent workflow operations that can be used
by agents to manage customer onboarding, process files, and validate temporal accuracy.
"""

import logging
from typing import Any, Dict, Optional

import asyncpg

from .schemas import TemporalValidationRequest
from .service import OnboardingService

logger = logging.getLogger(__name__)


async def get_onboarding_status_tool(
    db: asyncpg.Connection,
    tenant_id: int,
) -> Dict[str, Any]:
    """
    ADK tool function to get current onboarding status for a tenant.

    Args:
        db: Database connection
        tenant_id: Tenant ID

    Returns:
        Comprehensive onboarding status with progress and metrics
    """
    try:
        service = OnboardingService(db)
        status = await service.get_onboarding_status(tenant_id)

        return {
            "success": True,
            "tenant_id": tenant_id,
            "onboarding_status": {
                "onboarding_type": status.onboarding_type,
                "stage": status.stage,
                "approved_for_production": status.approved_for_production,
                "approved_at": status.approved_at.isoformat()
                if status.approved_at
                else None,
                "last_validation_accuracy": float(status.last_validation_accuracy)
                if status.last_validation_accuracy
                else None,
            },
            "data_metrics": {
                "total_transactions": status.total_transactions,
                "transactions_with_labels": status.transactions_with_labels,
                "date_range_start": status.date_range_start.isoformat()
                if status.date_range_start
                else None,
                "date_range_end": status.date_range_end.isoformat()
                if status.date_range_end
                else None,
                "has_sufficient_data": status.total_transactions >= 100,
                "labeling_percentage": (
                    status.transactions_with_labels / max(status.total_transactions, 1)
                )
                * 100,
            },
            "next_steps": status.next_steps,
            "recommendations": status.recommendations,
        }

    except Exception as e:
        logger.error(f"Failed to get onboarding status for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
        }


async def initialize_onboarding_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    onboarding_type: str,
) -> Dict[str, Any]:
    """
    ADK tool function to initialize onboarding for a specific type.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        onboarding_type: Type of onboarding (zero_onboarding/schema_only/historical_data)

    Returns:
        Initialization result with setup status
    """
    try:
        service = OnboardingService(db)

        if onboarding_type == "zero_onboarding":
            await service.initialize_zero_onboarding(tenant_id)
        elif onboarding_type == "schema_only":
            await service.initialize_schema_only_onboarding(tenant_id)
        else:
            # For historical_data, initialize record without specific setup
            await service.initialize_onboarding_record(
                tenant_id=tenant_id,
                onboarding_type=onboarding_type,
                stage="file_upload",
            )

        return {
            "success": True,
            "tenant_id": tenant_id,
            "onboarding_type": onboarding_type,
            "message": f"Successfully initialized {onboarding_type} onboarding",
            "next_steps": [
                "Upload historical transaction files"
                if onboarding_type == "historical_data"
                else "Import category schema"
                if onboarding_type == "schema_only"
                else "Begin using the system with AI-driven categorization"
            ],
        }

    except Exception as e:
        logger.error(
            f"Failed to initialize {onboarding_type} onboarding for tenant {tenant_id}: {e}"
        )
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
            "onboarding_type": onboarding_type,
        }


async def process_uploaded_file_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    upload_id: str,
    file_path: str,
    column_mapping: Optional[Dict[str, str]] = None,
    schema_hints: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to process uploaded file with schema interpretation.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        upload_id: Upload identifier
        file_path: Path to the uploaded file
        column_mapping: Optional column mapping configuration
        schema_hints: Optional schema interpretation hints

    Returns:
        File processing results with transaction creation status
    """
    try:
        service = OnboardingService(db)

        result = await service.process_uploaded_file(
            tenant_id=tenant_id,
            upload_id=upload_id,
            file_path=file_path,
            column_mapping=column_mapping or {},
        )

        return {
            "success": True,
            "upload_id": upload_id,
            "processing_result": {
                "total_rows": result.get("total_rows", 0),
                "successful_transactions": result.get("successful_transactions", 0),
                "failed_transactions": result.get("failed_transactions", 0),
                "success_rate": result.get("success_rate", 0.0),
                "processing_time_ms": result.get("processing_time_ms", 0),
            },
            "schema_analysis": {
                "detected_columns": result.get("detected_columns", []),
                "suggested_mapping": result.get("suggested_mapping", {}),
                "confidence_score": result.get("confidence_score", 0.0),
            },
            "validation_status": result.get("validation_status", "pending"),
            "errors": result.get("errors", []),
            "warnings": result.get("warnings", []),
        }

    except Exception as e:
        logger.error(f"Failed to process uploaded file {upload_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "upload_id": upload_id,
        }


async def build_rag_corpus_tool(
    db: asyncpg.Connection,
    tenant_id: int,
) -> Dict[str, Any]:
    """
    ADK tool function to build RAG corpus from customer transaction patterns.

    Args:
        db: Database connection
        tenant_id: Tenant ID

    Returns:
        RAG corpus building status and metrics
    """
    try:
        service = OnboardingService(db)

        corpus_status = await service.build_rag_corpus(tenant_id)

        return {
            "success": True,
            "tenant_id": tenant_id,
            "corpus_status": {
                "status": corpus_status.status,
                "corpus_name": corpus_status.corpus_name,
                "documents_indexed": corpus_status.documents_indexed,
                "categories_learned": corpus_status.categories_learned,
                "patterns_identified": corpus_status.patterns_identified,
                "last_updated": corpus_status.last_updated.isoformat()
                if corpus_status.last_updated
                else None,
            },
            "performance_metrics": {
                "indexing_time_ms": corpus_status.indexing_time_ms,
                "corpus_size_mb": corpus_status.corpus_size_mb,
                "vector_dimensions": corpus_status.vector_dimensions,
            },
            "validation_required": corpus_status.status == "ready"
            and corpus_status.documents_indexed > 0,
            "next_steps": [
                "Run temporal validation to test accuracy",
                "Review categorization patterns",
                "Validate against historical data",
            ]
            if corpus_status.status == "ready"
            else [
                "Wait for corpus building to complete",
                "Check transaction data quality",
            ],
        }

    except Exception as e:
        logger.error(f"Failed to build RAG corpus for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
        }


async def run_temporal_validation_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    validation_months: int = 6,
    accuracy_threshold: float = 0.95,
) -> Dict[str, Any]:
    """
    ADK tool function to run temporal validation for accuracy testing.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        validation_months: Number of months to validate
        accuracy_threshold: Required accuracy threshold

    Returns:
        Temporal validation results with monthly accuracy breakdown
    """
    try:
        service = OnboardingService(db)

        # Create validation request
        validation_request = TemporalValidationRequest(
            tenant_id=tenant_id,
            validation_months=validation_months,
            accuracy_threshold=accuracy_threshold,
        )

        result = await service.run_temporal_validation(validation_request)

        return {
            "success": True,
            "tenant_id": tenant_id,
            "validation_id": result.validation_id,
            "overall_results": {
                "overall_accuracy": result.overall_accuracy,
                "meets_threshold": result.overall_accuracy >= accuracy_threshold,
                "total_transactions_tested": result.total_transactions_tested,
                "months_validated": len(result.monthly_results),
            },
            "monthly_breakdown": [
                {
                    "month": monthly.month,
                    "accuracy": monthly.accuracy,
                    "transactions_tested": monthly.transactions_tested,
                    "correct_predictions": monthly.correct_predictions,
                    "meets_threshold": monthly.accuracy >= accuracy_threshold,
                }
                for monthly in result.monthly_results
            ],
            "validation_summary": {
                "passed_validation": result.overall_accuracy >= accuracy_threshold,
                "ready_for_production": result.overall_accuracy >= accuracy_threshold,
                "improvement_needed": result.overall_accuracy < accuracy_threshold,
            },
            "recommendations": result.recommendations,
            "next_steps": [
                "Approve for production deployment"
                if result.overall_accuracy >= accuracy_threshold
                else "Review failed predictions and retrain model",
                "Analyze categorization patterns for improvement"
                if result.overall_accuracy < accuracy_threshold
                else "Begin production onboarding workflow",
            ],
        }

    except Exception as e:
        logger.error(f"Failed to run temporal validation for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
        }


async def approve_for_production_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    user_id: int,
    approval_notes: Optional[str] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to approve tenant for production after validation.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        user_id: User ID performing the approval
        approval_notes: Optional approval notes

    Returns:
        Production approval status and next steps
    """
    try:
        service = OnboardingService(db)

        approval_result = await service.approve_for_production(
            tenant_id=tenant_id,
            user_id=user_id,
            approval_notes=approval_notes,
        )

        return {
            "success": True,
            "tenant_id": tenant_id,
            "approval_result": {
                "approved": approval_result.get("approved", False),
                "approval_timestamp": approval_result.get("approval_timestamp"),
                "approved_by": user_id,
                "validation_accuracy": approval_result.get("validation_accuracy"),
            },
            "production_status": {
                "ready_for_production": approval_result.get("approved", False),
                "can_process_transactions": approval_result.get("approved", False),
                "rag_corpus_active": approval_result.get("rag_corpus_active", False),
            },
            "next_steps": [
                "Begin processing production transactions",
                "Monitor categorization accuracy",
                "Set up regular accuracy monitoring",
            ]
            if approval_result.get("approved")
            else [
                "Address validation issues",
                "Re-run temporal validation",
                "Improve categorization accuracy",
            ],
            "message": "Successfully approved for production use"
            if approval_result.get("approved")
            else "Approval failed - check validation requirements",
        }

    except Exception as e:
        logger.error(f"Failed to approve tenant {tenant_id} for production: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
        }


async def interpret_file_columns_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    file_path: str,
    sample_size: int = 10,
) -> Dict[str, Any]:
    """
    ADK tool function to interpret file columns using AI schema detection.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        file_path: Path to file for column interpretation
        sample_size: Number of sample rows to analyze

    Returns:
        Column interpretation results with suggested mappings
    """
    try:
        service = OnboardingService(db)

        interpretation = await service.interpret_file_columns(
            tenant_id=tenant_id,
            file_path=file_path,
            sample_size=sample_size,
        )

        return {
            "success": True,
            "file_path": file_path,
            "interpretation_results": {
                "detected_columns": interpretation.get("detected_columns", []),
                "suggested_mapping": interpretation.get("suggested_mapping", {}),
                "confidence_scores": interpretation.get("confidence_scores", {}),
                "file_type": interpretation.get("file_type", "unknown"),
            },
            "column_analysis": {
                "total_columns": len(interpretation.get("detected_columns", [])),
                "mapped_columns": len(
                    [
                        k
                        for k, v in interpretation.get("suggested_mapping", {}).items()
                        if v
                    ]
                ),
                "unmapped_columns": len(
                    [
                        k
                        for k, v in interpretation.get("suggested_mapping", {}).items()
                        if not v
                    ]
                ),
                "mapping_confidence": interpretation.get("overall_confidence", 0.0),
            },
            "validation_status": {
                "has_date_column": "date"
                in interpretation.get("suggested_mapping", {}).values(),
                "has_amount_column": "amount"
                in interpretation.get("suggested_mapping", {}).values(),
                "has_description_column": "description"
                in interpretation.get("suggested_mapping", {}).values(),
                "ready_for_processing": interpretation.get(
                    "ready_for_processing", False
                ),
            },
            "sample_data": interpretation.get("sample_data", []),
            "recommendations": interpretation.get("recommendations", []),
        }

    except Exception as e:
        logger.error(f"Failed to interpret file columns for {file_path}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "file_path": file_path,
        }


async def import_category_schema_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    schema_file_path: str,
    schema_format: str = "excel",
) -> Dict[str, Any]:
    """
    ADK tool function to import customer category schema for schema-only onboarding.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        schema_file_path: Path to category schema file
        schema_format: Format of schema file (excel/csv/json)

    Returns:
        Schema import results with category hierarchy
    """
    try:
        service = OnboardingService(db)

        import_result = await service.import_category_schema(
            tenant_id=tenant_id,
            schema_file_path=schema_file_path,
            schema_format=schema_format,
        )

        return {
            "success": True,
            "tenant_id": tenant_id,
            "schema_import": {
                "categories_imported": import_result.get("categories_imported", 0),
                "hierarchy_levels": import_result.get("hierarchy_levels", 0),
                "root_categories": import_result.get("root_categories", []),
                "total_leaf_categories": import_result.get("total_leaf_categories", 0),
            },
            "import_validation": {
                "schema_valid": import_result.get("schema_valid", False),
                "hierarchical_structure": import_result.get(
                    "hierarchical_structure", False
                ),
                "unique_categories": import_result.get("unique_categories", 0),
                "duplicate_categories": import_result.get("duplicate_categories", 0),
            },
            "schema_analysis": {
                "business_categories": import_result.get("business_categories", []),
                "expense_categories": import_result.get("expense_categories", []),
                "income_categories": import_result.get("income_categories", []),
                "asset_categories": import_result.get("asset_categories", []),
            },
            "next_steps": [
                "Validate category mappings",
                "Begin transaction categorization using schema",
                "Monitor categorization accuracy",
            ]
            if import_result.get("schema_valid")
            else [
                "Fix schema validation errors",
                "Ensure unique category names",
                "Review hierarchy structure",
            ],
            "warnings": import_result.get("warnings", []),
            "errors": import_result.get("errors", []),
        }

    except Exception as e:
        logger.error(f"Failed to import category schema for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
            "schema_file_path": schema_file_path,
        }
