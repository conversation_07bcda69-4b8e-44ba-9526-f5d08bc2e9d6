"""
API router for onboarding domain.

Provides endpoints for temporal accuracy validation workflow.
"""

import json
import logging
import os
import tempfile
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd
from asyncpg import Connection
from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    File,
    Form,
    HTTPException,
    Request,
    UploadFile,
    status,
)
from pydantic import BaseModel

from ...core.config import settings
from ...core.database import get_db_session
from ...core.dependencies import (
    get_current_tenant_id,
    get_current_user_with_tenant,
    get_vertex_ai_client,
)
from ...shared.ai.vertex_client import VertexAIClient
from .processing_schemas import (
    ColumnStatisticResponse,
    FileProcessingReportResponse,
    ProcessingReportSummary,
    RowDetailsRequest,
    RowDetailsResponse,
    SchemaMappingReportResponse,
)
from .schemas import (
    FileColumnMapping,
    OnboardingApprovalRequest,
    OnboardingProgressResponse,
    OnboardingStartRequest,
    OnboardingStatus,
    SchemaOnlyStartRequest,
    TemporalValidationProgress,
    TemporalValidationRequest,
    TemporalValidationResult,
    ZeroOnboardingStartRequest,
)
from .service import OnboardingService

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Onboarding"])


# MIS Setup Models
class CompanyInfo(BaseModel):
    """Company information for MIS setup."""

    name: str
    industry: str
    size: str
    fiscal_year_end: str
    default_currency: str


class MISSetupRequest(BaseModel):
    """Request for unified MIS setup."""

    company_info: CompanyInfo
    uploaded_files: Optional[List[str]] = None


def to_camel_case(field_name: str) -> str:
    """Convert snake_case to camelCase."""
    return "".join(
        word.capitalize() if i else word for i, word in enumerate(field_name.split("_"))
    )


class MISSetupResponse(BaseModel):
    """Response from MIS setup."""

    setup_id: str
    status: str
    enhancement_opportunities: List[dict]
    baseline_accuracy: float

    class Config:
        populate_by_name = True
        alias_generator = to_camel_case


class Enhancement(BaseModel):
    """Enhancement opportunity detected."""

    type: str  # 'historical', 'schema', 'vendor'
    title: str
    description: str
    accuracy_gain: str
    time_estimate: str
    priority: str  # 'recommended', 'highly recommended', 'optional'
    confidence: float
    record_count: Optional[int] = None


@router.get("/status", response_model=OnboardingStatus)
async def get_onboarding_status(
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """Get current onboarding status for the tenant."""

    service = OnboardingService(conn, vertex_client)
    return await service.get_onboarding_status(tenant_id)


@router.get("/{onboarding_id}/progress", response_model=OnboardingProgressResponse)
async def get_onboarding_progress(
    onboarding_id: str,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingProgressResponse:
    """Get onboarding progress by onboarding ID."""

    service = OnboardingService(conn, vertex_client)
    onboarding_status = await service.get_onboarding_status(tenant_id)

    # Map onboarding_status to progress response format
    # Extract onboarding type from onboarding_id (e.g., "1-zero_onboarding" -> "zero_onboarding")
    onboarding_type = (
        onboarding_id.split("-", 1)[1]
        if "-" in onboarding_id
        else onboarding_status.onboarding_type
    )

    # Map backend status to frontend status
    status_mapping = {
        "not_started": "initialized",
        "data_uploaded": "ai_training",
        "corpus_building": "ai_training",
        "validating": "accuracy_validation",
        "completed": "completed",
        "failed": "initialized",
        "zero_ready": "completed",
        "schema_imported": "ai_training",
        "schema_ready": "completed",
    }

    frontend_status = status_mapping.get(
        onboarding_status.onboarding_stage, "initialized"
    )

    # Calculate progress percentage
    progress_mapping = {
        "initialized": 10.0,
        "ai_training": 50.0,
        "accuracy_validation": 80.0,
        "completed": 100.0,
    }
    progress = progress_mapping.get(frontend_status, 0.0)

    # Determine steps and current/next step
    steps_completed = []
    current_step = "Getting started"
    next_step = "Upload data"

    if frontend_status in ["ai_training", "accuracy_validation", "completed"]:
        steps_completed.append("data_uploaded")
        current_step = "Training AI model"
        next_step = "Validate accuracy"

    if frontend_status in ["accuracy_validation", "completed"]:
        steps_completed.append("ai_training")
        current_step = "Validating accuracy"
        next_step = "Complete setup"

    if frontend_status == "completed":
        steps_completed.extend(["ai_training", "accuracy_validation"])
        current_step = "Setup complete"
        next_step = "Start using system"

    # Build training results if available
    ai_training_results = None
    if onboarding_status.latest_validation:
        ai_training_results = {
            "categories_configured": onboarding_status.total_transactions,
            "gl_codes_mapped": len(onboarding_status.uploaded_files),
            "validation_accuracy": onboarding_status.latest_validation.average_accuracy,
        }

    return OnboardingProgressResponse(
        onboarding_id=onboarding_id,
        type=onboarding_type,
        status=frontend_status,
        progress=progress,
        steps_completed=steps_completed,
        current_step=current_step,
        next_step=next_step,
        ai_training_results=ai_training_results,
    )


@router.post("/start", response_model=OnboardingStatus)
async def start_onboarding(
    request: OnboardingStartRequest,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """Start the onboarding process for a new tenant."""

    if request.tenant_id != tenant_id:
        raise HTTPException(
            status_code=403, detail="Cannot start onboarding for another tenant"
        )

    service = OnboardingService(conn, vertex_client)

    # Store business context for AI categorization enhancement
    await service.store_business_context(
        tenant_id=tenant_id,
        industry=request.industry,
        company_size=request.company_size,
        company_website=request.company_website,
    )

    # Initialize onboarding record in database
    await service.initialize_onboarding_record(
        tenant_id, onboarding_type=request.onboarding_type or "historical_data"
    )

    return await service.get_onboarding_status(tenant_id)


async def _process_file_background(
    tenant_id: int,
    file_path: str,
    column_mapping: FileColumnMapping,
    has_category_labels: bool,
    upload_id: str,
    report_id: str,
):
    """Background task to process uploaded file."""
    # Import here to avoid circular imports
    from ...core.database import get_pool

    # Get a new database connection for the background task
    pool = await get_pool()
    async with pool.acquire() as conn:
        try:
            # Initialize vertex client for background task
            vertex_client = None
            try:
                from ...core.dependencies import get_vertex_client

                vertex_client = await get_vertex_client()
            except Exception as e:
                logger.warning(
                    f"Could not initialize Vertex AI client in background: {e}"
                )

            service = OnboardingService(conn, vertex_client)

            # Update upload status to processing
            await conn.execute(
                "UPDATE uploads SET status = $1, processing_status = $2, updated_at = $3 WHERE id = $4",
                "processing",
                "in_progress",
                datetime.utcnow(),
                upload_id,
            )

            # Process the file
            (
                transaction_count,
                _,
                _,
            ) = await service.process_uploaded_file_with_reporting(
                tenant_id=tenant_id,
                file_path=file_path,
                column_mapping=column_mapping,
                has_category_labels=has_category_labels,
            )

            # Update upload status to completed
            await conn.execute(
                "UPDATE uploads SET status = $1, processing_status = $2, size = $3, updated_at = $4 WHERE id = $5",
                "completed",
                "completed",
                transaction_count,
                datetime.utcnow(),
                upload_id,
            )

            logger.info(
                f"Background processing completed for upload {upload_id}: {transaction_count} transactions"
            )

        except Exception as e:
            logger.error(f"Background processing failed for upload {upload_id}: {e}")
            # Update upload status to failed
            await conn.execute(
                "UPDATE uploads SET status = $1, processing_status = $2, error_message = $3, updated_at = $4 WHERE id = $5",
                "failed",
                "failed",
                str(e),
                datetime.utcnow(),
                upload_id,
            )
            # Update report status to failed
            await conn.execute(
                "UPDATE file_processing_reports SET status = $1, error_message = $2, updated_at = $3 WHERE id = $4",
                "failed",
                str(e),
                datetime.utcnow(),
                report_id,
            )


@router.post("/upload-historical-data")
async def upload_historical_data(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    year: int = Form(...),
    has_category_labels: bool = Form(...),
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
):
    """
    Upload historical transaction data with existing category labels.

    This is the critical first step in onboarding where customers upload
    their full year of labeled transaction data.

    The file is uploaded and processing starts in the background to prevent
    timeouts for large files.
    """

    if not file.filename.endswith((".csv", ".xlsx", ".xls")):
        raise HTTPException(
            status_code=400,
            detail="Invalid file format. Please upload CSV or Excel file.",
        )

    # Save file temporarily
    current_user, tenant = user_tenant
    upload_dir = settings.get_upload_directory(tenant.id)

    file_path = upload_dir / f"{year}_{file.filename}"

    try:
        contents = await file.read()
        with open(file_path, "wb") as f:
            f.write(contents)

        service = OnboardingService(conn, vertex_client)

        # Interpret columns first (quick operation)
        column_mapping = await service.interpret_file_columns(str(file_path))

        # Create initial upload and report records
        upload_id = str(uuid.uuid4())
        report_id = str(uuid.uuid4())
        current_time = datetime.utcnow()

        # Create upload record with pending status
        await conn.execute(
            """
            INSERT INTO uploads (
                id, filename, content_type, file_path, size, status, 
                processing_status, tenant_id, column_mapping, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            """,
            upload_id,
            file.filename,
            file.content_type or "application/octet-stream",
            str(file_path),
            0,  # Will be updated after processing
            "pending",
            "pending",
            tenant.id,
            json.dumps(column_mapping.model_dump()) if column_mapping else None,
            current_time,
            current_time,
        )

        # Create initial report record
        await conn.execute(
            """
            INSERT INTO file_processing_reports (
                id, tenant_id, upload_id, file_name, status, processing_started_at,
                total_rows, total_columns, successful_rows, failed_rows, skipped_rows,
                file_size_bytes, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            """,
            report_id,
            tenant.id,
            upload_id,
            file.filename,
            "pending",
            current_time,
            0,
            0,
            0,
            0,
            0,  # Will be updated during processing
            Path(file_path).stat().st_size,
            current_time,
            current_time,
        )

        # Add background task to process the file
        background_tasks.add_task(
            _process_file_background,
            tenant.id,
            str(file_path),
            column_mapping,
            has_category_labels,
            upload_id,
            report_id,
        )

        return {
            "status": "processing",
            "message": "File uploaded successfully. Processing has started in the background.",
            "file_path": str(file_path),
            "column_mapping": column_mapping.model_dump(),
            "has_category_labels": has_category_labels,
            "processing_report_id": report_id,
            "upload_id": upload_id,
            "transactions_imported": 0,  # Will be populated after processing
            "check_status_url": f"/api/v1/onboarding/upload-status/{upload_id}",
        }

    except Exception as e:
        logger.error(f"File upload failed: {e}")
        raise HTTPException(status_code=500, detail=f"File processing failed: {str(e)}")


@router.get("/upload-status/{upload_id}")
async def get_upload_status(
    upload_id: str,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
) -> dict:
    """
    Check the status of a file upload and processing operation.
    """
    current_user, tenant = user_tenant

    # Get upload record
    upload_row = await conn.fetchrow(
        """
        SELECT id, filename, status, processing_status, size, error_message, 
               created_at, updated_at
        FROM uploads 
        WHERE id = $1 AND tenant_id = $2
        """,
        upload_id,
        tenant.id,
    )

    if not upload_row:
        raise HTTPException(status_code=404, detail=f"Upload {upload_id} not found")

    # Get processing report if available
    report_row = await conn.fetchrow(
        """
        SELECT id, status, total_rows, successful_rows, failed_rows, 
               skipped_rows, processing_started_at, processing_completed_at,
               processing_duration_seconds, error_message
        FROM file_processing_reports
        WHERE upload_id = $1
        """,
        upload_id,
    )

    response = {
        "upload_id": upload_id,
        "filename": upload_row["filename"],
        "status": upload_row["status"],
        "processing_status": upload_row["processing_status"],
        "transactions_imported": upload_row["size"] or 0,
        "error_message": upload_row["error_message"],
        "created_at": upload_row["created_at"].isoformat()
        if upload_row["created_at"]
        else None,
        "updated_at": upload_row["updated_at"].isoformat()
        if upload_row["updated_at"]
        else None,
    }

    if report_row:
        response["processing_report"] = {
            "report_id": report_row["id"],
            "status": report_row["status"],
            "total_rows": report_row["total_rows"],
            "successful_rows": report_row["successful_rows"],
            "failed_rows": report_row["failed_rows"],
            "skipped_rows": report_row["skipped_rows"],
            "processing_duration_seconds": report_row["processing_duration_seconds"],
            "error_message": report_row["error_message"],
        }

    return response


@router.post("/batch-upload-files")
async def batch_upload_files(
    files: List[UploadFile] = File(..., description="Financial data files to upload"),
    year: str = Form(..., description="Year of the data (e.g., 2024)"),
    has_category_labels: bool = Form(
        True, description="Whether files contain category labels"
    ),
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> dict:
    """
    Batch upload multiple financial data files with enhanced reporting.

    This endpoint accepts multiple file uploads and processes each one:
    - Supports Excel (.xlsx) and CSV files
    - Intelligent column mapping and categorization
    - Stores transactions with original category labels
    - Provides detailed processing reports for each file

    Returns summary of upload results, transaction counts, and report IDs
    for retrieving detailed processing information including row-by-row
    results, column statistics, and data quality metrics.
    """
    current_user, tenant = user_tenant

    logger.info(f"Starting batch upload for tenant {tenant.id} with {len(files)} files")

    # Results tracking
    upload_results = []
    total_transactions = 0
    successful_uploads = 0

    service = OnboardingService(conn, vertex_client)

    for file in files:
        if not file.filename:
            upload_results.append(
                {
                    "filename": "unknown",
                    "status": "error",
                    "error": "No filename provided",
                    "transactions_imported": 0,
                    "report_id": None,
                }
            )
            continue

        try:
            logger.info(f"Processing {file.filename}...")

            # Save uploaded file to tenant directory
            upload_dir = Path("uploads") / str(tenant.id)
            upload_dir.mkdir(parents=True, exist_ok=True)
            tenant_file_path = upload_dir / f"{year}_{file.filename}"

            # Save uploaded file content
            content = await file.read()
            with open(tenant_file_path, "wb") as f:
                f.write(content)

            # Interpret columns with AI
            column_mapping = await service.interpret_file_columns(str(tenant_file_path))

            # Process file with enhanced reporting
            (
                transaction_count,
                report_id,
                upload_id,
            ) = await service.process_uploaded_file_with_reporting(
                tenant_id=tenant.id,
                file_path=str(tenant_file_path),
                column_mapping=column_mapping,
                has_category_labels=has_category_labels,
            )

            upload_results.append(
                {
                    "filename": file.filename,
                    "status": "success",
                    "transactions_imported": transaction_count,
                    "report_id": str(report_id),
                    "upload_id": str(upload_id),
                    "column_mapping": column_mapping.model_dump(),
                    "file_path": str(tenant_file_path),
                }
            )

            total_transactions += transaction_count
            successful_uploads += 1

            logger.info(
                f"✅ {file.filename}: {transaction_count} transactions imported"
            )

        except Exception as e:
            logger.error(f"Failed to process {file.filename}: {e}")
            upload_results.append(
                {
                    "filename": file.filename,
                    "status": "failed",
                    "error": str(e),
                    "transactions_imported": 0,
                    "report_id": None,
                }
            )

    # Optionally build RAG corpus from all uploaded data
    rag_corpus_built = False
    if successful_uploads > 0:
        try:
            logger.info("Building RAG corpus from uploaded data...")
            corpus_result = await service.build_rag_corpus(tenant.id)
            rag_corpus_built = True
            logger.info(
                f"✅ RAG corpus built with {corpus_result.total_patterns} patterns"
            )
        except Exception as e:
            logger.error(f"Failed to build RAG corpus: {e}")

    return {
        "status": "completed",
        "summary": {
            "total_files_processed": len(files),
            "successful_uploads": successful_uploads,
            "failed_uploads": len(files) - successful_uploads,
            "total_transactions_imported": total_transactions,
            "rag_corpus_built": rag_corpus_built,
        },
        "upload_results": upload_results,
        "message": f"Batch upload completed. {successful_uploads}/{len(files)} files processed successfully. Use report_id from upload_results to fetch detailed processing reports via /file-processing-report/{{report_id}}.",
    }


@router.post("/interpret-columns", response_model=FileColumnMapping)
async def interpret_file_columns(
    file_path: str,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> FileColumnMapping:
    """Use AI to interpret column mappings from an uploaded file."""

    # Validate file path is within tenant's upload directory
    current_user, tenant = user_tenant
    expected_dir = settings.upload_base_path / str(tenant.id)
    file_path_obj = Path(file_path)

    if not file_path_obj.is_relative_to(expected_dir):
        raise HTTPException(status_code=403, detail="Access denied to file")

    if not file_path_obj.exists():
        raise HTTPException(status_code=404, detail="File not found")

    service = OnboardingService(conn, vertex_client)
    return await service.interpret_file_columns(str(file_path_obj))


@router.post("/build-rag-corpus")
async def build_rag_corpus(
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> dict:
    """
    Build RAG corpus from the tenant's labeled transaction data.

    This creates a knowledge base from the customer's historical categorization
    patterns that will be used for AI predictions.

    Now runs synchronously to provide immediate feedback to users.
    """

    current_user, tenant = user_tenant

    try:
        # Initialize service
        service = OnboardingService(conn, vertex_client)

        # Build corpus synchronously
        logger.info(f"Starting synchronous RAG corpus build for tenant {tenant.id}")
        corpus_result = await service.build_rag_corpus(tenant.id)

        # Update onboarding status
        query = """
            UPDATE onboarding_status 
            SET stage = $1, last_activity = $2
            WHERE tenant_id = $3
        """
        await conn.execute(
            query, "corpus_created", datetime.now(timezone.utc), tenant.id
        )

        logger.info(f"✅ Successfully built RAG corpus for tenant {tenant.id}")

        return {
            "corpus_id": corpus_result.corpus_name,
            "status": "completed",
            "message": "RAG corpus successfully built",
            "tenant_id": tenant.id,
            "total_patterns": corpus_result.total_patterns,
            "categories_found": len(corpus_result.categories_found),
            "processing_time_seconds": corpus_result.processing_time_seconds,
        }

    except Exception as e:
        logger.error(f"Failed to build RAG corpus for tenant {tenant.id}: {e}")

        # Update status to show failure
        try:
            query = """
                UPDATE onboarding_status 
                SET stage = $1, last_activity = $2
                WHERE tenant_id = $3
            """
            await conn.execute(
                query, "corpus_failed", datetime.now(timezone.utc), tenant.id
            )
        except Exception:
            pass

        raise HTTPException(
            status_code=500, detail=f"Failed to build RAG corpus: {str(e)}"
        )


@router.post("/validate-temporal-accuracy", response_model=TemporalValidationResult)
async def validate_temporal_accuracy(
    request: TemporalValidationRequest,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> TemporalValidationResult:
    """
    Run temporal accuracy validation for July-December 2024.

    This simulates month-by-month accuracy testing where:
    - Each month uses all previous months as training data
    - Tests accuracy against the customer's original labels
    - Must achieve >85% accuracy for production approval

    Now runs synchronously to provide immediate results.
    """

    current_user, tenant = user_tenant
    if request.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Cannot validate another tenant")

    service = OnboardingService(conn, vertex_client)

    # Generate a validation ID for tracking
    validation_id = str(uuid.uuid4())

    try:
        # Run validation synchronously
        logger.info(f"Starting synchronous temporal validation for tenant {tenant.id}")
        validation_result = await service.run_temporal_validation(
            request, validation_id
        )

        # Persist the validation result
        import json

        query = """
            INSERT INTO temporal_validations (
                validation_id, tenant_id, started_at, completed_at, 
                status, accuracy_threshold, average_accuracy, meets_threshold,
                monthly_results, total_training_transactions, total_transactions_tested, 
                error_message
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        """

        await conn.execute(
            query,
            validation_id,
            tenant.id,
            validation_result.started_at,
            validation_result.completed_at,
            validation_result.status,
            validation_result.accuracy_threshold,
            validation_result.average_accuracy,
            validation_result.meets_threshold,
            json.dumps([r.model_dump() for r in validation_result.monthly_results]),
            validation_result.total_training_transactions,
            validation_result.total_test_transactions,
            validation_result.error_message,
        )

        logger.info(
            f"✅ Temporal validation completed for tenant {tenant.id} - Average accuracy: {validation_result.average_accuracy:.1f}%"
        )

        return validation_result

    except Exception as e:
        logger.error(f"Failed temporal validation for tenant {tenant.id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to run temporal validation: {str(e)}"
        )


@router.get(
    "/validation-results/{validation_id}", response_model=TemporalValidationResult
)
async def get_validation_results(
    validation_id: str,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
) -> TemporalValidationResult:
    """Get results of a specific validation run."""

    current_user, tenant = user_tenant

    # Fetch validation from database
    query = """
        SELECT validation_id, tenant_id, started_at, completed_at, status, 
               validation_results, error_message, months_tested, overall_accuracy,
               corpus_build_time, testing_time, total_transactions_tested
        FROM temporal_validation
        WHERE validation_id = $1 AND tenant_id = $2
    """
    row = await conn.fetchrow(query, validation_id, tenant.id)

    if not row:
        validation = None
    else:
        from .models import TemporalValidation

        validation = TemporalValidation(**dict(row))

    if not validation:
        raise HTTPException(status_code=404, detail="Validation not found")

    # Convert to response model
    from .schemas import MonthlyAccuracyResult

    return TemporalValidationResult(
        tenant_id=validation.tenant_id,
        validation_id=validation.validation_id,
        started_at=validation.started_at,
        completed_at=validation.completed_at,
        status=validation.status,
        accuracy_threshold=validation.accuracy_threshold,
        average_accuracy=validation.average_accuracy or 0.0,
        meets_threshold=validation.meets_threshold or False,
        monthly_results=[
            MonthlyAccuracyResult(**mr) for mr in (validation.monthly_results or [])
        ]
        if validation.monthly_results
        else [],
        total_training_transactions=validation.total_training_transactions,
        total_test_transactions=validation.total_transactions_tested,
        error_message=validation.error_message,
    )


@router.get(
    "/validation-progress/{validation_id}", response_model=TemporalValidationProgress
)
async def get_validation_progress(
    validation_id: str,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
) -> TemporalValidationProgress:
    """
    Get real-time progress for temporal validation with progressive RAG corpus building.

    Returns detailed progress including:
    - Current phase (corpus_building, testing, completed)
    - Per-month corpus building progress
    - Overall validation progress
    - Estimated completion time
    """
    # Get validation record
    query = """
        SELECT validation_id, tenant_id, started_at, completed_at, status,
               validation_results, error_message, months_tested, overall_accuracy,
               corpus_build_time, testing_time, total_transactions_tested,
               monthly_results
        FROM temporal_validation
        WHERE validation_id = $1 AND tenant_id = $2
    """
    row = await conn.fetchrow(query, validation_id, tenant_id)

    if row:
        from .models import TemporalValidation

        validation = TemporalValidation(**dict(row))
    else:
        validation = None

    if not validation:
        raise HTTPException(status_code=404, detail="Validation not found")

    # Calculate progress based on status and monthly results
    months_total = (
        len(validation.monthly_results or []) if validation.monthly_results else 4
    )  # Default to 4 months
    months_completed = len(
        [m for m in (validation.monthly_results or []) if m.get("accuracy", 0) > 0]
    )

    # Determine current phase
    if validation.status == "pending":
        current_phase = "corpus_building"
    elif validation.status == "running":
        current_phase = "testing"
    elif validation.status in ["completed", "failed"]:
        current_phase = "completed"
    else:
        current_phase = "corpus_building"

    # Calculate overall progress
    if validation.status == "completed":
        overall_progress = 100.0
    elif validation.status == "failed":
        overall_progress = 0.0
    else:
        overall_progress = (
            (months_completed / months_total) * 100.0 if months_total > 0 else 0.0
        )

    # Get current month being processed
    current_month = None
    if validation.monthly_results and months_completed < months_total:
        # Next month to process
        all_months = [
            "2024-03",
            "2024-04",
            "2024-05",
            "2024-06",
        ]  # Adjust based on date range
        if months_completed < len(all_months):
            current_month = all_months[months_completed]

    # Build corpus progress (simplified - in real implementation, this would track actual corpus building)
    corpus_progress = []
    if validation.monthly_results:
        for i, monthly_result in enumerate(validation.monthly_results):
            month = monthly_result.get("month", f"2024-{3 + i:02d}")
            corpus_progress.append(
                {
                    "month": month,
                    "corpus_name": f"temporal_validation_{tenant_id}_{month}",
                    "training_data_size": monthly_result.get(
                        "training_transactions", 0
                    ),
                    "status": "completed"
                    if monthly_result.get("accuracy", 0) > 0
                    else "building",
                    "progress_percentage": 100.0
                    if monthly_result.get("accuracy", 0) > 0
                    else 0.0,
                    "started_at": validation.started_at,
                    "completed_at": validation.completed_at
                    if monthly_result.get("accuracy", 0) > 0
                    else None,
                }
            )

    return TemporalValidationProgress(
        validation_id=validation_id,
        current_phase=current_phase,
        months_total=months_total,
        months_completed=months_completed,
        current_month=current_month,
        corpus_progress=corpus_progress,
        overall_progress_percentage=overall_progress,
        estimated_completion_time=validation.completed_at,
    )


@router.post("/approve-for-production", response_model=OnboardingStatus)
async def approve_for_production(
    request: OnboardingApprovalRequest,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """
    Approve tenant for production usage after successful validation.

    This is the final step that enables the tenant to start using
    the AI categorization on new transactions.
    """

    current_user, tenant = user_tenant
    if request.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Cannot approve another tenant")

    service = OnboardingService(conn, vertex_client)

    try:
        status = await service.approve_for_production(
            request.tenant_id, request.validation_id, request.approval_notes
        )

        if not status.approved_for_production:
            raise HTTPException(status_code=400, detail="Approval criteria not met")

        return status

    except Exception as e:
        logger.error(f"Production approval failed: {e}")
        raise HTTPException(status_code=500, detail=f"Approval failed: {str(e)}")


@router.get("/sample-data")
async def get_sample_data(user_tenant: tuple = Depends(get_current_user_with_tenant)):
    """Get sample data files for testing the onboarding process."""
    current_user, tenant = user_tenant

    sample_files = ["Capital One.xlsx", "Credit Card.xlsx", "ICICI.xlsx", "SVB.xlsx"]

    # Use absolute path to the test-files directory (the ONLY authorized sample data location)
    data_dir = Path(__file__).parent.parent.parent.parent.parent / "test-files"
    available_files = []

    for filename in sample_files:
        file_path = data_dir / filename
        if file_path.exists():
            available_files.append(
                {
                    "filename": filename,
                    "path": str(file_path),
                    "size_kb": file_path.stat().st_size / 1024,
                    "description": f"Sample {filename.replace('.xlsx', '')} bank transactions with category labels",
                }
            )

    return {
        "sample_files": available_files,
        "instructions": "Use these files to test the temporal accuracy validation workflow",
    }


@router.get("/diagnose-schema/{tenant_id}")
async def diagnose_schema_mappings(
    tenant_id: int,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
):
    """
    Diagnose schema mapping issues for accuracy debugging.

    This endpoint helps debug why accuracy measurements may be failing
    by providing detailed information about schema mappings and category comparisons.
    """
    current_user, tenant = user_tenant

    # Only allow access to own tenant data
    if tenant_id != tenant.id:
        raise HTTPException(
            status_code=403, detail="Cannot diagnose schema mappings for another tenant"
        )

    service = OnboardingService(conn, vertex_client)

    try:
        diagnostic_info = await service.diagnose_schema_mappings(tenant_id)
        return diagnostic_info
    except Exception as e:
        logger.error(f"Schema diagnosis failed for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Schema diagnosis failed: {str(e)}"
        )


# ===============================
# FILE PROCESSING REPORT ENDPOINTS
# ===============================


@router.post("/upload-with-reporting")
async def upload_file_with_detailed_reporting(
    file: UploadFile = File(...),
    year: int = Form(...),
    has_category_labels: bool = Form(...),
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
):
    """
    Upload a file with enhanced processing that provides detailed reporting.

    This endpoint uses the new enhanced processing method that tracks:
    - Row-by-row processing results
    - Column statistics and data quality
    - Schema discovery integration
    - Detailed error and warning messages

    Returns the processing report ID that can be used to retrieve detailed reports.
    """
    if not file.filename.endswith((".csv", ".xlsx", ".xls")):
        raise HTTPException(
            status_code=400,
            detail="Invalid file format. Please upload CSV or Excel file.",
        )

    current_user, tenant = user_tenant
    upload_dir = settings.get_upload_directory(tenant.id)

    file_path = upload_dir / f"{year}_{file.filename}"

    try:
        contents = await file.read()
        with open(file_path, "wb") as f:
            f.write(contents)

        service = OnboardingService(conn, vertex_client)

        # Interpret columns
        column_mapping = await service.interpret_file_columns(str(file_path))

        # Process file with enhanced reporting
        (
            transaction_count,
            report_id,
            upload_id,
        ) = await service.process_uploaded_file_with_reporting(
            tenant_id=tenant.id,
            file_path=str(file_path),
            column_mapping=column_mapping,
            has_category_labels=has_category_labels,
        )

        return {
            "status": "success",
            "message": f"File processed with detailed reporting. {transaction_count} transactions imported.",
            "report_id": report_id,
            "upload_id": upload_id,
            "transactions_imported": transaction_count,
            "file_path": str(file_path),
            "column_mapping": column_mapping.model_dump(),
        }

    except FileNotFoundError as e:
        logger.error(f"Upload directory creation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to create upload directory. This may be a permissions issue in the deployment environment.",
        )
    except PermissionError as e:
        logger.error(f"File write permission denied: {e}")
        raise HTTPException(
            status_code=500,
            detail="Permission denied when writing uploaded file. Check deployment file system permissions.",
        )
    except ValueError as e:
        logger.error(f"File processing validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"File upload with reporting failed: {e}", exc_info=True)
        # Log environment details for debugging
        import os

        logger.error(
            f"Environment: K_SERVICE={os.environ.get('K_SERVICE')}, PWD={os.getcwd()}"
        )
        logger.error(f"Upload path attempted: {upload_dir}")
        raise HTTPException(
            status_code=500,
            detail=f"File processing failed: {str(e)}. Check server logs for details.",
        )


@router.get(
    "/file-processing-report/{upload_id}", response_model=FileProcessingReportResponse
)
async def get_file_processing_report(
    upload_id: str,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> FileProcessingReportResponse:
    """
    Get detailed processing report for a specific file upload.

    This provides comprehensive information about how the file was processed,
    including success rates, data quality scores, and discovered categories.
    """
    current_user, tenant = user_tenant
    service = OnboardingService(conn, vertex_client)

    report = await service.get_file_processing_report(upload_id)
    if not report:
        raise HTTPException(status_code=404, detail="Processing report not found")

    # Verify tenant access
    if report.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Access denied to this report")

    # Get column statistics for mapping info
    column_stats = await service.get_column_statistics(upload_id)

    # Build response
    mapped_columns = []
    unmapped_columns = []

    for stat in column_stats:
        if stat.mapped_field:
            mapped_columns.append(
                {
                    "original_name": stat.column_name,
                    "mapped_field": stat.mapped_field,
                    "confidence": stat.mapping_confidence or 0.0,
                    "method": stat.mapping_method or "ai",
                }
            )
        else:
            unmapped_columns.append(stat.column_name)

    success_rate = (
        (report.successful_rows / report.total_rows * 100)
        if report.total_rows > 0
        else 0.0
    )

    return FileProcessingReportResponse(
        upload_id=report.upload_id,
        file_name=report.file_name,
        status=report.status,
        total_rows=report.total_rows,
        successful_rows=report.successful_rows,
        failed_rows=report.failed_rows,
        skipped_rows=report.skipped_rows,
        success_rate=success_rate,
        processing_started_at=report.processing_started_at,
        processing_completed_at=report.processing_completed_at,
        processing_duration_seconds=report.processing_duration_seconds,
        date_format_detected=report.date_format_detected,
        total_columns=report.total_columns,
        mapped_columns=mapped_columns,
        unmapped_columns=unmapped_columns,
        data_quality_score=report.data_quality_score or 0.0,
        validation_errors=report.validation_errors or [],
        warnings=report.warnings or [],
        categories_discovered=report.categories_discovered or [],
        category_count=report.category_count or 0,
        schema_confidence=report.schema_confidence,
        error_message=report.error_message,
        created_at=report.created_at,
        updated_at=report.updated_at,
    )


@router.get(
    "/column-statistics/{upload_id}", response_model=List[ColumnStatisticResponse]
)
async def get_column_statistics(
    upload_id: str,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> List[ColumnStatisticResponse]:
    """
    Get detailed column statistics for a processed file.

    This provides information about each column including:
    - Data type detection and consistency
    - Null value percentages
    - Value distributions
    - Mapping confidence scores
    """
    current_user, tenant = user_tenant
    service = OnboardingService(conn, vertex_client)

    # Verify access
    report = await service.get_file_processing_report(upload_id)
    if not report or report.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Access denied")

    column_stats = await service.get_column_statistics(upload_id)

    responses = []
    for stat in column_stats:
        null_percentage = (
            (stat.null_values / stat.total_values * 100)
            if stat.total_values > 0
            else 0.0
        )

        # Calculate data quality score for this column
        quality_score = 1.0
        if stat.null_values > 0:
            quality_score *= 1 - null_percentage / 100
        if stat.type_consistency:
            quality_score *= stat.type_consistency

        responses.append(
            ColumnStatisticResponse(
                column_name=stat.column_name,
                column_index=stat.column_index,
                mapped_field=stat.mapped_field,
                total_values=stat.total_values,
                non_null_values=stat.non_null_values,
                null_percentage=null_percentage,
                unique_values=stat.unique_values,
                detected_type=stat.detected_type or "unknown",
                type_consistency=stat.type_consistency or 0.0,
                min_value=stat.min_value,
                max_value=stat.max_value,
                average_value=stat.average_value,
                most_common_values=stat.most_common_values or [],
                empty_string_count=stat.empty_string_count or 0,
                invalid_format_count=stat.invalid_format_count or 0,
                data_quality_score=quality_score,
                mapping_confidence=stat.mapping_confidence,
                mapping_method=stat.mapping_method,
            )
        )

    return responses


@router.post("/row-details/{upload_id}", response_model=RowDetailsResponse)
async def get_row_processing_details(
    upload_id: str,
    request: RowDetailsRequest,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> RowDetailsResponse:
    """
    Get paginated row-by-row processing details.

    This allows drilling down into individual row processing results,
    including parsing errors, validation issues, and data quality problems.
    """
    current_user, tenant = user_tenant
    service = OnboardingService(conn, vertex_client)

    # Verify access
    report = await service.get_file_processing_report(upload_id)
    if not report or report.tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Access denied")

    result = await service.get_row_processing_details(
        upload_id=upload_id,
        page=request.page,
        page_size=request.page_size,
        status_filter=request.status_filter,
        has_errors=request.has_errors,
    )

    # Convert row details to response format
    row_responses = []
    for row in result["rows"]:
        row_responses.append(
            {
                "row_number": row.row_number,
                "status": row.status,
                "parsed_date": row.parsed_date,
                "parsed_amount": row.parsed_amount,
                "parsed_description": row.parsed_description,
                "parsed_category": row.parsed_category,
                "parsed_vendor": row.parsed_vendor,
                "validation_errors": row.validation_errors or [],
                "data_quality_issues": row.data_quality_issues or [],
                "raw_data": row.raw_data,
            }
        )

    return RowDetailsResponse(
        rows=row_responses,
        total_rows=result["total_rows"],
        page=result["page"],
        page_size=result["page_size"],
        total_pages=result["total_pages"],
    )


@router.get(
    "/schema-mapping-report/{tenant_id}", response_model=SchemaMappingReportResponse
)
async def get_schema_mapping_report(
    tenant_id: int,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> SchemaMappingReportResponse:
    """
    Get comprehensive schema mapping report showing discovered categories and mappings.

    This report shows:
    - All categories discovered from uploaded files
    - Unified category mappings
    - Bidirectional mapping relationships
    - Mapping confidence and unmapped categories
    """
    current_user, tenant = user_tenant

    # Verify access
    if tenant_id != tenant.id:
        raise HTTPException(status_code=403, detail="Access denied")

    service = OnboardingService(conn, vertex_client)
    report_data = await service.get_schema_mapping_report(tenant_id)

    return SchemaMappingReportResponse(**report_data)


@router.get("/recent-processing-reports", response_model=List[ProcessingReportSummary])
async def get_recent_processing_reports(
    limit: int = 10,
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> List[ProcessingReportSummary]:
    """
    Get recent file processing reports for the current tenant.

    Returns a summary of recent file uploads with key metrics.
    """
    current_user, tenant = user_tenant
    service = OnboardingService(conn, vertex_client)

    reports = await service.get_recent_processing_reports(tenant.id, limit)

    summaries = []
    for report in reports:
        success_rate = (
            (report.successful_rows / report.total_rows * 100)
            if report.total_rows > 0
            else 0.0
        )

        summaries.append(
            ProcessingReportSummary(
                upload_id=report.upload_id,
                file_name=report.file_name,
                status=report.status,
                total_rows=report.total_rows,
                success_rate=success_rate,
                data_quality_score=report.data_quality_score or 0.0,
                processing_duration_seconds=report.processing_duration_seconds,
                created_at=report.created_at,
            )
        )

    return summaries


@router.post("/start-zero", response_model=OnboardingStatus)
async def start_zero_onboarding(
    request: ZeroOnboardingStartRequest,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """Start M1 zero-onboarding process - immediate production ready."""

    if request.tenant_id != tenant_id:
        raise HTTPException(
            status_code=403, detail="Cannot start onboarding for another tenant"
        )

    service = OnboardingService(conn, vertex_client)

    # Store business context for AI categorization enhancement
    await service.store_business_context(
        tenant_id=tenant_id,
        industry=request.industry,
        company_size=request.company_size,
        company_website=request.company_website,
    )

    # Initialize M1 zero-onboarding record
    await service.initialize_zero_onboarding(tenant_id)

    # Apply MIS template based on business context
    logger.info(f"Applying MIS template for tenant {tenant_id} during zero-onboarding")
    template_result = await service.apply_mis_template_for_tenant(
        tenant_id=tenant_id,
        company_name=request.tenant_name,
        company_website=request.company_website,
        business_description=request.business_type,
        company_size=request.company_size,
        force_industry=request.industry,  # Use provided industry if available
    )

    if template_result["success"]:
        logger.info(
            f"MIS template '{template_result['industry']}' applied successfully "
            f"for tenant {tenant_id} with {template_result['confidence']:.1%} confidence"
        )
    else:
        logger.warning(
            f"MIS template application failed for tenant {tenant_id}: {template_result.get('error')}"
        )

    return await service.get_onboarding_status(tenant_id)


@router.post("/start-schema-only", response_model=OnboardingStatus)
async def start_schema_only_onboarding(
    request: SchemaOnlyStartRequest,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> OnboardingStatus:
    """Start M3 schema-only onboarding process."""

    if request.tenant_id != tenant_id:
        raise HTTPException(
            status_code=403, detail="Cannot start onboarding for another tenant"
        )

    service = OnboardingService(conn, vertex_client)

    # Store business context for AI categorization enhancement
    await service.store_business_context(
        tenant_id=tenant_id,
        industry=request.industry,
        company_size=request.company_size,
        company_website=request.company_website,
    )

    # Initialize M3 schema-only onboarding record
    await service.initialize_schema_only_onboarding(tenant_id)

    # Apply MIS template based on business context
    logger.info(
        f"Applying MIS template for tenant {tenant_id} during schema-only onboarding"
    )
    template_result = await service.apply_mis_template_for_tenant(
        tenant_id=tenant_id,
        company_name=request.tenant_name,
        company_website=request.company_website,
        business_description=None,  # Schema-only doesn't have business_type
        company_size=request.company_size,
        force_industry=request.industry,  # Use provided industry if available
    )

    if template_result["success"]:
        logger.info(
            f"MIS template '{template_result['industry']}' applied successfully "
            f"for tenant {tenant_id} with {template_result['confidence']:.1%} confidence"
        )
    else:
        logger.warning(
            f"MIS template application failed for tenant {tenant_id}: {template_result.get('error')}"
        )

    return await service.get_onboarding_status(tenant_id)


@router.post("/import-schema")
async def import_category_schema(
    file: UploadFile = File(...),
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
):
    """Import category hierarchy for M3 schema-only onboarding."""

    # Validate file format
    if not file.filename or not file.filename.endswith((".xlsx", ".csv", ".json")):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Schema file must be Excel (.xlsx), CSV (.csv), or JSON (.json)",
        )

    service = OnboardingService(conn, vertex_client)

    try:
        # Read and parse the uploaded file content
        file_content = await file.read()
        
        # Create a temporary file to work with
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name
        
        try:
            # Parse the schema based on file type
            parsed_schema = await _parse_category_schema_file(temp_file_path, file.filename)
            
            # Store the parsed schema in the database
            schema_id = await _store_category_schema(
                conn, tenant_id, file.filename, parsed_schema
            )
            
            # Create the actual categories from the parsed schema
            categories_created = await _create_categories_from_schema(
                conn, tenant_id, parsed_schema
            )
            
            # Update onboarding status
            await service.import_category_schema(tenant_id, file.filename)
            
            logger.info(f"Schema imported: {categories_created} categories created for tenant {tenant_id}")
            
            return {
                "message": f"Category schema imported from {file.filename}",
                "schema_id": schema_id,
                "categories_created": categories_created,
                "details": {
                    "total_categories": len(parsed_schema.get("categories", [])),
                    "hierarchy_levels": parsed_schema.get("metadata", {}).get("max_hierarchy_depth", 0),
                    "has_gl_codes": parsed_schema.get("metadata", {}).get("has_gl_codes", False),
                    "source_format": parsed_schema.get("metadata", {}).get("source_format", "unknown")
                }
            }
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except Exception as e:
        logger.error(f"Failed to import category schema for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse and import schema: {str(e)}"
        )


@router.post("/business-context")
async def save_business_context(
    business_context: dict,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
):
    """
    Save comprehensive business context for MIS categorization enhancement.

    This endpoint collects detailed business information that enhances:
    - Industry-specific MIS template selection
    - AI categorization accuracy
    - GL code recommendations
    - Financial reporting customization
    """
    logger.info(f"Saving business context for tenant {tenant_id}")

    try:
        service = OnboardingService(conn, vertex_client)
        result = await service.update_onboarding_with_business_context(
            tenant_id, business_context
        )

        return {
            "success": True,
            "message": "Business context saved successfully",
            "business_context_saved": result["business_context_saved"],
            "onboarding_status": result["onboarding_status"],
        }

    except Exception as e:
        logger.error(f"Error saving business context for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save business context: {str(e)}",
        )


@router.get("/business-context")
async def get_business_context(
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
):
    """
    Retrieve business context for a tenant.
    """
    logger.info(f"Getting business context for tenant {tenant_id}")

    try:
        service = OnboardingService(conn, vertex_client)
        business_context = await service.get_business_context(tenant_id)

        return {
            "success": True,
            "business_context": business_context,
        }

    except Exception as e:
        logger.error(f"Error getting business context for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get business context: {str(e)}",
        )


# Unified MIS Setup Endpoints


@router.post("/mis/setup", response_model=MISSetupResponse)
async def unified_mis_setup(
    request: MISSetupRequest,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> MISSetupResponse:
    """
    Unified MIS setup endpoint.

    Creates a complete Management Information System for any business
    with progressive enhancement based on available data.
    """
    try:
        # Initialize MIS categorization service
        from ..categories.mis_categorization_service import MISCategorizationService

        mis_service = MISCategorizationService(conn)

        # 1. Apply industry template to create base MIS structure
        await mis_service.apply_industry_template(
            tenant_id=tenant_id, industry=request.company_info.industry, customize=True
        )

        # 2. Update business context
        await mis_service.update_business_context(
            tenant_id=tenant_id,
            context_updates={
                "company_name": request.company_info.name,
                "company_size": request.company_info.size,
                "fiscal_year_end": request.company_info.fiscal_year_end,
                "default_currency": request.company_info.default_currency,
                "industry": request.company_info.industry,
            },
        )

        # 3. Detect enhancement opportunities if files were uploaded
        enhancement_opportunities = []
        if request.uploaded_files:
            # This would analyze uploaded files for enhancement opportunities
            # For now, returning mock data
            pass

        # 4. Create or update setup record
        result = await conn.fetchrow(
            """
            INSERT INTO onboarding_status (
                tenant_id, onboarding_type, stage, 
                created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (tenant_id) DO UPDATE SET
                onboarding_type = EXCLUDED.onboarding_type,
                stage = EXCLUDED.stage,
                updated_at = EXCLUDED.updated_at
            RETURNING id
            """,
            tenant_id,
            "zero_onboarding",
            "completed",
            datetime.utcnow(),
            datetime.utcnow(),
        )
        setup_id = str(result["id"])

        # Calculate real baseline accuracy
        from ..categories.mis_categorization_service import MISCategorizationService
        mis_service = MISCategorizationService(conn)
        accuracy_metrics = await mis_service.calculate_mis_accuracy(tenant_id)
        
        return MISSetupResponse(
            setup_id=setup_id,
            status="active",
            enhancement_opportunities=enhancement_opportunities,
            baseline_accuracy=accuracy_metrics["baseline_accuracy"],
        )

    except Exception as e:
        logger.error(f"MIS setup failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initialize MIS setup: {str(e)}",
        )


@router.post("/mis/detect-enhancements")
async def detect_enhancements(
    file: UploadFile = File(...),
    user_tenant: tuple = Depends(get_current_user_with_tenant),
    conn: Connection = Depends(get_db_session),
) -> dict:
    """Detect enhancement opportunities from uploaded file."""
    try:
        current_user, tenant = user_tenant
        tenant_id = tenant.id

        logger.info(
            f"Starting enhancement detection for file: {file.filename}, tenant_id: {tenant_id}"
        )

        # Save file temporarily
        upload_dir = settings.get_upload_directory(tenant_id)
        file_path = upload_dir / f"temp_{file.filename}"

        contents = await file.read()
        with open(file_path, "wb") as f:
            f.write(contents)

        # Analyze file for enhancement opportunities
        # Quick analysis to detect file type
        enhancements = []

        # Read file header - try different header rows
        import pandas as pd

        try:
            columns = []
            # Try header=0 first, then header=1 if columns are unnamed
            for header_row in [0, 1]:
                df = pd.read_excel(file_path, header=header_row, nrows=10)
                test_columns = list(df.columns)

                # Check if we have meaningful column names (not mostly Unnamed)
                unnamed_count = sum(
                    1 for col in test_columns if str(col).startswith("Unnamed")
                )
                if unnamed_count < len(test_columns) * 0.5:  # Less than 50% unnamed
                    columns = test_columns
                    break

            if not columns:  # Fallback to header=0 if nothing better found
                df = pd.read_excel(file_path, header=0, nrows=10)
                columns = list(df.columns)

            # Detect historical transactions
            if any(
                str(col).lower() in ["category", "gl code", "account"]
                for col in columns
            ):
                if any(
                    str(col).lower() in ["date", "amount", "description"]
                    for col in columns
                ):
                    enhancements.append(
                        {
                            "type": "historical",
                            "title": "Learn from your transaction history",
                            "description": "We detected categorized transactions that can improve accuracy",
                            "accuracy_gain": "+15-20%",
                            "time_estimate": "10 minutes",
                            "priority": "recommended",
                            "confidence": 0.9,
                            "record_count": len(df),
                        }
                    )

            # Detect GL structure
            if any(
                str(col).lower() in ["gl code", "account code", "account number"]
                for col in columns
            ):
                if any(
                    str(col).lower() in ["account name", "description"]
                    for col in columns
                ):
                    enhancements.append(
                        {
                            "type": "schema",
                            "title": "Map to your GL structure",
                            "description": "We detected a chart of accounts that ensures compliance",
                            "accuracy_gain": "+20%",
                            "time_estimate": "5 minutes",
                            "priority": "highly recommended",
                            "confidence": 0.95,
                        }
                    )

        except Exception as e:
            logger.warning(f"Could not analyze file: {e}")

        # Clean up temp file
        file_path.unlink(missing_ok=True)

        return {"enhancements": enhancements}

    except Exception as e:
        logger.error(f"Enhancement detection failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to detect enhancements: {str(e)}",
        )


@router.post("/mis/setup/{setup_id}/complete")
async def complete_mis_setup(
    setup_id: str,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
) -> dict:
    """
    Complete MIS setup and activate the system.

    This finalizes the MIS setup process and marks the tenant as ready
    to start processing transactions.
    """
    try:
        # Verify the setup belongs to this tenant
        result = await conn.fetchrow(
            """
            SELECT id, tenant_id, stage 
            FROM onboarding_status 
            WHERE id = $1 AND tenant_id = $2
            """,
            int(setup_id),
            tenant_id,
        )

        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Setup not found"
            )

        # Update the status to completed
        await conn.execute(
            """
            UPDATE onboarding_status 
            SET stage = 'completed', updated_at = $1
            WHERE id = $2 AND tenant_id = $3
            """,
            datetime.utcnow(),
            int(setup_id),
            tenant_id,
        )

        logger.info(f"MIS setup completed for tenant {tenant_id}, setup_id {setup_id}")

        return {
            "status": "completed",
            "message": "MIS setup completed successfully",
            "setup_id": setup_id,
            "tenant_id": tenant_id,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to complete MIS setup: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to complete MIS setup: {str(e)}",
        )


@router.get("/mis/setup/{setup_id}")
async def get_mis_setup_status(
    setup_id: str,
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
) -> dict:
    """
    Get the status of an MIS setup.
    
    Returns the current status, progress, and available enhancement opportunities.
    """
    try:
        # Get setup record
        result = await conn.fetchrow(
            """
            SELECT id, tenant_id, stage, onboarding_type,
                   created_at, updated_at
            FROM onboarding_status 
            WHERE id = $1 AND tenant_id = $2
            """,
            int(setup_id),
            tenant_id,
        )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"MIS setup {setup_id} not found"
            )
        
        # Get category count to assess MIS completeness
        category_count = await conn.fetchval(
            """
            SELECT COUNT(*) FROM categories 
            WHERE tenant_id = $1
            """,
            tenant_id,
        )
        
        # Determine status based on stage
        setup_status = "active" if result["stage"] == "completed" else "in_progress"
        
        # Calculate real baseline accuracy
        from ..categories.mis_categorization_service import MISCategorizationService
        mis_service = MISCategorizationService(conn)
        accuracy_metrics = await mis_service.calculate_mis_accuracy(tenant_id)
        
        return {
            "setup_id": setup_id,
            "status": setup_status,
            "stage": result["stage"],
            "category_count": category_count,
            "baseline_accuracy": accuracy_metrics["baseline_accuracy"],
            "current_accuracy": accuracy_metrics["current_accuracy"],
            "accuracy_gain": accuracy_metrics["accuracy_gain"],
            "enhancements_applied": accuracy_metrics["enhancements_applied"],
            "created_at": result["created_at"].isoformat() if result["created_at"] else None,
            "updated_at": result["updated_at"].isoformat() if result["updated_at"] else None,
            "enhancement_opportunities": []  # Would be populated based on detected files
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get MIS setup status: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get MIS setup status: {str(e)}",
        )


@router.post("/mis/enhance/{setup_id}")
async def apply_mis_enhancement(
    setup_id: str,
    type: str = Form(...),  # "historical", "schema", "vendor", etc.
    applyRetrospectively: str = Form("false"),
    files: List[UploadFile] = File(...),
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    vertex_client: Optional[VertexAIClient] = Depends(get_vertex_ai_client),
) -> dict:
    """
    Apply an enhancement to an existing MIS setup.
    
    Enhancements can include:
    - Historical data learning (+15-20% accuracy)
    - Schema/GL code mapping (+20% accuracy)
    - Vendor list integration (+5-10% accuracy)
    """
    try:
        # Verify the setup belongs to this tenant
        result = await conn.fetchrow(
            """
            SELECT id, tenant_id, stage 
            FROM onboarding_status 
            WHERE id = $1 AND tenant_id = $2
            """,
            int(setup_id),
            tenant_id,
        )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"MIS setup {setup_id} not found"
            )
        
        # Process the enhancement based on type
        apply_retrospectively = applyRetrospectively.lower() == "true"
        
        # Initialize MIS categorization service
        from ..categories.mis_categorization_service import MISCategorizationService
        mis_service = MISCategorizationService(conn)
        
        # Get accuracy before enhancement
        accuracy_before = await mis_service.calculate_mis_accuracy(tenant_id)
        
        # Process files based on enhancement type
        for file in files:
            # Save file temporarily
            upload_dir = settings.get_upload_directory(tenant_id)
            file_path = upload_dir / f"enhancement_{type}_{file.filename}"
            
            contents = await file.read()
            with open(file_path, "wb") as f:
                f.write(contents)
            
            # Process the file based on type
            if type == "historical":
                # Process historical data for pattern learning
                logger.info(f"Processing historical data enhancement for tenant {tenant_id}")
                result = await mis_service.process_historical_enhancement(
                    tenant_id, file_path, apply_retrospectively
                )
                if not result.get("success"):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Historical enhancement failed: {result.get('error')}"
                    )
                accuracy_gain = result["accuracy_improvement"]
                
            elif type == "schema":
                # Process GL schema/chart of accounts
                logger.info(f"Processing schema enhancement for tenant {tenant_id}")
                result = await mis_service.process_schema_enhancement(
                    tenant_id, file_path, apply_retrospectively
                )
                if not result.get("success"):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Schema enhancement failed: {result.get('error')}"
                    )
                accuracy_gain = result["accuracy_improvement"]
                
            elif type == "vendor":
                # Process vendor list
                logger.info(f"Processing vendor list enhancement for tenant {tenant_id}")
                result = await mis_service.process_vendor_enhancement(
                    tenant_id, file_path, apply_retrospectively
                )
                if not result.get("success"):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Vendor enhancement failed: {result.get('error')}"
                    )
                accuracy_gain = result["accuracy_improvement"]
                
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unknown enhancement type: {type}"
                )
            
            # Clean up temp file
            file_path.unlink(missing_ok=True)
        
        # Record enhancement in database
        await conn.execute(
            """
            INSERT INTO mis_enhancements (
                tenant_id, setup_id, enhancement_type, 
                accuracy_gain, status, applied_at
            ) VALUES ($1, $2, $3, $4, $5, NOW())
            """,
            tenant_id,
            int(setup_id),
            type,
            accuracy_gain,
            "completed"
        )
        
        # Get accuracy after enhancement
        accuracy_after = await mis_service.calculate_mis_accuracy(tenant_id)
        
        return {
            "setup_id": setup_id,
            "enhancement_type": type,
            "status": "completed",
            "accuracy_before": accuracy_before["current_accuracy"],
            "accuracy_after": accuracy_after["current_accuracy"],
            "accuracy_gain": round(accuracy_after["current_accuracy"] - accuracy_before["current_accuracy"], 3),
            "apply_retrospectively": apply_retrospectively,
            "message": f"{type.title()} enhancement applied successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to apply MIS enhancement: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to apply enhancement: {str(e)}",
        )


@router.get("/mis/enhancement-recommendations")
async def get_enhancement_recommendations(
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
) -> dict:
    """
    Get recommended enhancements for the current MIS setup.
    
    Analyzes the current state and suggests improvements.
    """
    try:
        # Get current accuracy metrics
        category_count = await conn.fetchval(
            """
            SELECT COUNT(*) FROM categories 
            WHERE tenant_id = $1
            """,
            tenant_id,
        )
        
        recommendations = []
        
        # Base recommendations on category count and current setup
        logger.info(f"Found {category_count} categories for enhancement recommendations")
        
        # Always recommend historical data if not applied
        recommendations.append({
            "type": "historical",
            "title": "Learn from your past data",
            "description": "Upload categorized transactions for AI learning",
            "accuracy_gain": "+15-20%",
            "time_estimate": "8-12 minutes",
            "priority": "recommended",
            "requirements": ["Excel file with categorized transactions"]
        })
        
        # Recommend schema enhancement for accounting compliance
        recommendations.append({
            "type": "schema", 
            "title": "Map to your GL structure",
            "description": "Upload chart of accounts for compliance",
            "accuracy_gain": "+20%",
            "time_estimate": "10 minutes",
            "priority": "highly recommended",
            "requirements": ["Chart of accounts file"]
        })
        
        # Recommend vendor list for better recognition
        recommendations.append({
            "type": "vendor",
            "title": "Import vendor list",
            "description": "Improve vendor transaction recognition",
            "accuracy_gain": "+5-10%", 
            "time_estimate": "5 minutes",
            "priority": "optional",
            "requirements": ["Vendor master list"]
        })
        
        return {
            "current_accuracy": 0.87,  # Base accuracy
            "potential_accuracy": 0.99,  # With all enhancements
            "recommendations": recommendations
        }
        
    except Exception as e:
        logger.error(f"Failed to get enhancement recommendations: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendations: {str(e)}",
        )


@router.post("/mis/detect-enhancements-debug")
async def detect_enhancements_debug(
    request: Request,
) -> dict:
    """Debug endpoint to check request format."""
    try:
        # Log request headers
        logger.info(f"Request headers: {dict(request.headers)}")

        # Try to get form data
        form_data = await request.form()
        logger.info(f"Form data keys: {list(form_data.keys())}")

        # Check if file is present
        if "file" in form_data:
            file = form_data["file"]
            logger.info(
                f"File info: filename={file.filename}, content_type={file.content_type}"
            )
            return {"status": "file_found", "filename": file.filename}
        else:
            return {"status": "no_file", "form_keys": list(form_data.keys())}

    except Exception as e:
        logger.error(f"Debug endpoint error: {e}", exc_info=True)
        return {"status": "error", "error": str(e)}


# Helper functions for schema parsing and storage

async def _parse_category_schema_file(file_path: str, filename: str) -> Dict[str, Any]:
    """Parse category schema from uploaded file (Excel, CSV, or JSON)."""
    try:
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ""
        
        if file_ext == "json":
            # Parse JSON file
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                # Convert list of categories to standard format
                categories = []
                for item in data:
                    if isinstance(item, dict):
                        categories.append({
                            "name": item.get("name", item.get("category", "")),
                            "level": item.get("level", 0),
                            "gl_code": item.get("gl_code", item.get("account_code", "")),
                            "parent": item.get("parent", ""),
                            "account_type": item.get("account_type", "Expense")
                        })
                
                return {
                    "categories": categories,
                    "metadata": {
                        "source_format": "json",
                        "total_categories": len(categories),
                        "max_hierarchy_depth": max((cat.get("level", 0) for cat in categories), default=0),
                        "has_gl_codes": any(cat.get("gl_code") for cat in categories)
                    }
                }
            elif isinstance(data, dict) and "categories" in data:
                # Already in correct format
                return data
            else:
                raise ValueError("Invalid JSON format: expected list of categories or object with 'categories' field")
        
        elif file_ext in ["xlsx", "csv"]:
            # Parse Excel or CSV file
            if file_ext == "xlsx":
                df = pd.read_excel(file_path)
            else:
                df = pd.read_csv(file_path)
            
            # Detect column names for categories
            category_columns = ["category", "name", "category_name", "title", "label"]
            category_col = None
            for col in df.columns:
                if col.lower() in category_columns:
                    category_col = col
                    break
            
            if not category_col:
                # Use first column as category
                category_col = df.columns[0]
            
            # Detect other columns
            gl_code_col = None
            parent_col = None
            level_col = None
            account_type_col = None
            
            for col in df.columns:
                col_lower = col.lower()
                if "gl" in col_lower or "account" in col_lower and "code" in col_lower:
                    gl_code_col = col
                elif "parent" in col_lower:
                    parent_col = col
                elif "level" in col_lower:
                    level_col = col
                elif "type" in col_lower:
                    account_type_col = col
            
            categories = []
            for _, row in df.iterrows():
                category_name = str(row[category_col]).strip()
                if not category_name or category_name.lower() in ["nan", "none", ""]:
                    continue
                
                # Detect hierarchy from category name patterns
                level = 0
                parent = ""
                hierarchy_parts = []
                
                if " > " in category_name:
                    # Hierarchical format: "Parent > Child > Grandchild"
                    hierarchy_parts = [part.strip() for part in category_name.split(" > ")]
                    level = len(hierarchy_parts) - 1
                    if level > 0:
                        parent = " > ".join(hierarchy_parts[:-1])
                elif "/" in category_name:
                    # Path format: "Parent/Child/Grandchild"
                    hierarchy_parts = [part.strip() for part in category_name.split("/")]
                    level = len(hierarchy_parts) - 1
                    if level > 0:
                        parent = "/".join(hierarchy_parts[:-1])
                else:
                    hierarchy_parts = [category_name]
                
                # Override with explicit columns if available
                if level_col and not pd.isna(row[level_col]):
                    level = int(row[level_col])
                if parent_col and not pd.isna(row[parent_col]):
                    parent = str(row[parent_col]).strip()
                
                # Get GL code and account type
                gl_code = ""
                if gl_code_col and not pd.isna(row[gl_code_col]):
                    gl_code = str(row[gl_code_col]).strip()
                
                account_type = "Expense"  # Default
                if account_type_col and not pd.isna(row[account_type_col]):
                    account_type = str(row[account_type_col]).strip()
                elif gl_code:
                    # Infer account type from GL code
                    if gl_code.startswith(("4", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49")):
                        account_type = "Revenue"
                    elif gl_code.startswith(("1", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19")):
                        account_type = "Asset"
                    elif gl_code.startswith(("2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29")):
                        account_type = "Liability"
                    elif gl_code.startswith(("3", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39")):
                        account_type = "Equity"
                
                categories.append({
                    "name": category_name,
                    "level": level,
                    "gl_code": gl_code,
                    "parent": parent,
                    "account_type": account_type,
                    "hierarchy_parts": hierarchy_parts
                })
            
            return {
                "categories": categories,
                "metadata": {
                    "source_format": file_ext,
                    "total_categories": len(categories),
                    "max_hierarchy_depth": max((cat.get("level", 0) for cat in categories), default=0),
                    "has_gl_codes": any(cat.get("gl_code") for cat in categories),
                    "columns_detected": {
                        "category_column": category_col,
                        "gl_code_column": gl_code_col,
                        "parent_column": parent_col,
                        "level_column": level_col,
                        "account_type_column": account_type_col
                    }
                }
            }
        
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
    
    except Exception as e:
        logger.error(f"Failed to parse category schema file {filename}: {e}")
        raise


async def _store_category_schema(
    conn: Connection, tenant_id: int, filename: str, parsed_schema: Dict[str, Any]
) -> str:
    """Store the parsed category schema in the database."""
    try:
        schema_id = str(uuid.uuid4())
        metadata = parsed_schema.get("metadata", {})
        
        # Insert into category_schemas table
        insert_query = """
            INSERT INTO category_schemas (
                id, tenant_id, name, schema_format, schema_data,
                category_count, max_hierarchy_depth, has_gl_codes,
                imported_from, imported_by, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
            )
        """
        
        await conn.execute(
            insert_query,
            schema_id,
            tenant_id,
            f"Schema from {filename}",
            metadata.get("source_format", "unknown"),
            json.dumps(parsed_schema),
            metadata.get("total_categories", 0),
            metadata.get("max_hierarchy_depth", 0),
            metadata.get("has_gl_codes", False),
            filename,
            "onboarding_import",  # Could be enhanced to use actual user
            datetime.utcnow(),
            datetime.utcnow()
        )
        
        logger.info(f"Stored category schema {schema_id} for tenant {tenant_id}")
        return schema_id
        
    except Exception as e:
        logger.error(f"Failed to store category schema for tenant {tenant_id}: {e}")
        raise


async def _create_categories_from_schema(
    conn: Connection, tenant_id: int, parsed_schema: Dict[str, Any]
) -> int:
    """Create category records from parsed schema data."""
    try:
        categories = parsed_schema.get("categories", [])
        created_count = 0
        
        # Sort categories by level to ensure parents are created before children
        categories_sorted = sorted(categories, key=lambda x: x.get("level", 0))
        
        for category in categories_sorted:
            name = category.get("name", "").strip()
            if not name:
                continue
            
            # Check if category already exists for this tenant
            existing = await conn.fetchrow(
                "SELECT id FROM categories WHERE tenant_id = $1 AND name = $2",
                tenant_id, name
            )
            
            if existing:
                logger.debug(f"Category '{name}' already exists for tenant {tenant_id}")
                continue
            
            # Find parent ID if parent is specified
            parent_id = None
            parent_name = category.get("parent", "").strip()
            if parent_name:
                parent_record = await conn.fetchrow(
                    "SELECT id FROM categories WHERE tenant_id = $1 AND name = $2",
                    tenant_id, parent_name
                )
                if parent_record:
                    parent_id = parent_record["id"]
            
            # Generate GL code if not provided
            gl_code = category.get("gl_code", "").strip()
            if not gl_code:
                # Auto-generate GL code based on account type and level
                account_type = category.get("account_type", "Expense")
                if account_type == "Revenue":
                    gl_code = f"4{created_count + 100:03d}"
                elif account_type == "Asset":
                    gl_code = f"1{created_count + 100:03d}"
                elif account_type == "Liability":
                    gl_code = f"2{created_count + 100:03d}"
                elif account_type == "Equity":
                    gl_code = f"3{created_count + 100:03d}"
                else:  # Expense
                    gl_code = f"6{created_count + 100:03d}"
            
            # Create the category
            insert_query = """
                INSERT INTO categories (
                    name, tenant_id, parent_id, gl_code, gl_account_name, gl_account_type,
                    level, path, learned_from_onboarding, confidence_score, created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
                )
            """
            
            # Build hierarchical path
            if parent_name:
                path = f"{parent_name} > {name}"
            else:
                path = name
            
            await conn.execute(
                insert_query,
                name,
                tenant_id,
                parent_id,
                gl_code,
                name,  # Use category name as GL account name
                category.get("account_type", "Expense"),
                category.get("level", 0),
                path,
                True,  # learned_from_onboarding
                0.95,  # High confidence for imported schemas
                datetime.utcnow(),
                datetime.utcnow()
            )
            
            created_count += 1
            logger.debug(f"Created category '{name}' for tenant {tenant_id}")
        
        logger.info(f"Created {created_count} categories from schema for tenant {tenant_id}")
        return created_count
        
    except Exception as e:
        logger.error(f"Failed to create categories from schema for tenant {tenant_id}: {e}")
        raise
