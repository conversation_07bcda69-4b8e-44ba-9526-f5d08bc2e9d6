# apps/giki-ai-api/src/giki_ai_api/routers/transactions.py
"""
Transaction Router - Customer-Facing Transaction Management

Provides real transaction endpoints for customers to view, filter, and manage
their financial data using direct SQL queries for optimal performance.
"""

import logging
import time
from datetime import datetime
from typing import List, Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, ConfigDict

from ...core.database import get_db_session
from ...core.dependencies import get_current_tenant_id
from ...shared.cache.performance_cache import cache_response, invalidate_cache_on_change
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user

logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["Transactions"],
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Not found"},
        status.HTTP_401_UNAUTHORIZED: {"description": "Not authenticated"},
        status.HTTP_403_FORBIDDEN: {"description": "Not authorized"},
    },
)


class TransactionResponse(BaseModel):
    """Transaction response schema."""

    model_config = ConfigDict(from_attributes=True)

    id: str
    date: str
    description: str
    amount: float
    account: Optional[str] = None
    transaction_type: Optional[str] = None
    category_id: Optional[int] = None
    category_path: Optional[str] = None
    ai_suggested_category: Optional[int] = None
    ai_suggested_category_path: Optional[str] = None
    ai_category_confidence: Optional[float] = None
    is_categorized: bool = False
    is_user_modified: bool = False
    user_corrected: bool = False
    entity_id: Optional[int] = None
    upload_id: Optional[str] = None
    status: str = "uncategorized"
    vendor_name: Optional[str] = None
    original_category: Optional[str] = None
    ai_category: Optional[str] = None


class PaginatedTransactionResponse(BaseModel):
    """Paginated transaction response."""

    items: List[TransactionResponse]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    next_cursor: Optional[str] = None
    has_more: bool = False


def derive_transaction_status(row: dict) -> str:
    """Derive transaction status from row data."""
    if row.get("is_user_modified"):
        return "user_modified"
    elif (row.get("ai_category") and row.get("ai_category") != "") or (row.get("ai_suggested_category") and row.get("ai_suggested_category") != ""):
        # Only consider AI suggested if there's actually a non-empty category
        return "ai_suggested"
    elif row.get("category_id") or row.get("original_category"):
        return "categorized"
    return "uncategorized"


def row_to_transaction_response(row: dict) -> TransactionResponse:
    """Convert database row to TransactionResponse."""
    # Determine transaction status
    transaction_status = derive_transaction_status(row)

    # Determine categorization flags
    is_categorized = bool(row.get("category_id") or row.get("original_category"))

    return TransactionResponse(
        id=row["id"],
        date=row["date"].isoformat()
        if hasattr(row["date"], "isoformat")
        else row["date"],
        description=row["description"],
        amount=float(row["amount"]),
        account=row.get("account"),
        transaction_type=row.get("transaction_type"),
        category_id=row.get("category_id"),
        category_path=row.get("category_path"),
        ai_suggested_category=row.get("ai_suggested_category"),
        ai_suggested_category_path=row.get("ai_category"),
        ai_category_confidence=float(row["ai_confidence"])
        if row.get("ai_confidence")
        else None,
        is_categorized=is_categorized,
        is_user_modified=row.get("is_user_modified", False),
        user_corrected=row.get("user_corrected", False),
        entity_id=row.get("entity_id"),
        upload_id=row.get("upload_id"),
        status=transaction_status,
        vendor_name=row.get("vendor_name"),
        original_category=row.get("original_category"),
        ai_category=row.get("ai_category"),
    )


@router.get("/fast", response_model=PaginatedTransactionResponse)
@cache_response(
    cache_type="transaction_list",
    ttl_override=60,  # 1 minute cache for fast endpoint
    key_params=["cursor", "limit"],
)
async def list_transactions_fast(
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    cursor: Optional[str] = Query(None, description="Cursor for pagination"),
    limit: int = Query(100, ge=1, le=1000, description="Number of items to return"),
) -> PaginatedTransactionResponse:
    """
    Fast transaction listing using cursor-based pagination.
    Optimized for dashboard performance with <100ms response time.
    """
    query_start = time.time()

    try:
        # Build base query
        base_query = """
            SELECT 
                id,
                date,
                description,
                amount,
                account,
                transaction_type,
                category_id,
                original_category,
                ai_category,
                ai_confidence,
                vendor_name,
                entity_id,
                upload_id,
                CASE 
                    WHEN category_id IS NOT NULL THEN true 
                    ELSE false 
                END as is_user_modified,
                false as user_corrected
            FROM transactions
            WHERE tenant_id = $1
        """

        # Add cursor condition if provided
        query_params = [tenant_id]
        if cursor:
            try:
                cursor_date, cursor_id = cursor.split(":")
                base_query += " AND (date < $2 OR (date = $2 AND id < $3))"
                query_params.extend([cursor_date, cursor_id])
            except ValueError:
                logger.warning(f"Invalid cursor format: {cursor}")

        # Add ordering and limit
        base_query += " ORDER BY date DESC, id DESC LIMIT $" + str(
            len(query_params) + 1
        )
        query_params.append(limit + 1)  # Fetch one extra to determine has_more

        # Execute query
        rows = await conn.fetch(base_query, *query_params)

        # Process results
        has_more = len(rows) > limit
        if has_more:
            rows = rows[:limit]

        # Determine next cursor
        next_cursor = None
        if has_more and rows:
            last_row = rows[-1]
            next_cursor = f"{last_row['date'].isoformat()}:{last_row['id']}"

        # Convert rows to response objects
        items = [row_to_transaction_response(dict(row)) for row in rows]

        # Log performance metrics
        total_time = (time.time() - query_start) * 1000
        logger.info(
            f"Fast retrieved {len(items)} transactions for tenant {tenant_id} in {total_time:.2f}ms"
        )

        if total_time > 100:
            logger.warning(f"🚨 SLOW FAST QUERY: {total_time:.2f}ms (target: <100ms)")
        else:
            logger.debug(f"⚡ FAST QUERY SUCCESS: {total_time:.2f}ms (target: <100ms)")

        return PaginatedTransactionResponse(
            items=items,
            total_count=-1,  # Not available in cursor-based pagination
            page=1,
            page_size=limit,
            total_pages=-1,
            next_cursor=next_cursor,
            has_more=has_more,
        )

    except Exception as e:
        logger.error(
            f"Error in fast transactions fetch for tenant {tenant_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch transactions",
        )


@router.get("/", response_model=PaginatedTransactionResponse)
async def list_transactions(
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    upload_id: Optional[str] = Query(None, description="Filter by upload ID"),
    start_date: Optional[str] = Query(
        None, description="Start date filter (YYYY-MM-DD)"
    ),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    category_id: Optional[str] = Query(None, description="Filter by category ID"),
    search_term: Optional[str] = Query(None, description="Search in description"),
    min_amount: Optional[float] = Query(None, description="Minimum amount"),
    max_amount: Optional[float] = Query(None, description="Maximum amount"),
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=1000),
    sort_by: str = Query("date", description="Field to sort by"),
    sort_direction: str = Query("desc", pattern="^(asc|desc)$"),
    skip_count: bool = Query(False, description="Skip total count for performance"),
) -> PaginatedTransactionResponse:
    """
    List transactions with comprehensive filtering and pagination.

    Supports filtering by:
    - Date range (start_date, end_date)
    - Transaction status (uncategorized, categorized, ai_suggested, user_modified)
    - Category
    - Upload batch
    - Amount range
    - Description search

    Performance optimization:
    - Set skip_count=true to skip total count calculation
    - Use smaller page_size for faster response
    """
    start_time = time.time()

    try:
        # Build WHERE clause
        where_conditions = ["tenant_id = $1"]
        query_params = [tenant_id]
        param_count = 1

        # Add filters
        if upload_id:
            param_count += 1
            where_conditions.append(f"upload_id = ${param_count}")
            query_params.append(upload_id)

        if start_date:
            param_count += 1
            where_conditions.append(f"date >= ${param_count}")
            # Convert string to date object for PostgreSQL
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                query_params.append(start_date_obj)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid start_date format. Use YYYY-MM-DD",
                )

        if end_date:
            param_count += 1
            where_conditions.append(f"date <= ${param_count}")
            # Convert string to date object for PostgreSQL
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
                query_params.append(end_date_obj)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid end_date format. Use YYYY-MM-DD",
                )

        if min_amount is not None:
            param_count += 1
            where_conditions.append(f"amount >= ${param_count}")
            query_params.append(min_amount)

        if max_amount is not None:
            param_count += 1
            where_conditions.append(f"amount <= ${param_count}")
            query_params.append(max_amount)

        if search_term:
            param_count += 1
            where_conditions.append(f"description ILIKE ${param_count}")
            query_params.append(f"%{search_term}%")

        if category_id:
            param_count += 1
            where_conditions.append(f"category_id = ${param_count}")
            query_params.append(int(category_id))

        # Status filter
        if status_filter and status_filter != "all":
            if status_filter == "uncategorized":
                where_conditions.append(
                    "category_id IS NULL AND original_category IS NULL"
                )
            elif status_filter == "user_modified":
                where_conditions.append("category_id IS NOT NULL")
            elif status_filter == "ai_suggested":
                where_conditions.append("ai_category IS NOT NULL")

        where_clause = " AND ".join(where_conditions)

        # Get total count if not skipped
        total_count = 0
        if not skip_count:
            count_query = f"SELECT COUNT(*) FROM transactions WHERE {where_clause}"
            total_count = await conn.fetchval(count_query, *query_params)

        # Build main query
        offset = (page - 1) * page_size

        # Map sort fields
        sort_field_map = {
            "date": "date",
            "amount": "amount",
            "description": "description",
        }
        sort_field = sort_field_map.get(sort_by, "date")

        main_query = f"""
            SELECT 
                id,
                date,
                description,
                amount,
                account,
                transaction_type,
                category_id,
                original_category,
                ai_category,
                ai_confidence,
                vendor_name,
                entity_id,
                upload_id,
                CASE 
                    WHEN category_id IS NOT NULL THEN true 
                    ELSE false 
                END as is_user_modified,
                false as user_corrected
            FROM transactions
            WHERE {where_clause}
            ORDER BY {sort_field} {sort_direction.upper()}, id DESC
            LIMIT ${param_count + 1} OFFSET ${param_count + 2}
        """

        query_params.extend([page_size, offset])

        # Execute query
        rows = await conn.fetch(main_query, *query_params)

        # Convert to response objects
        items = [row_to_transaction_response(dict(row)) for row in rows]

        # Calculate pagination
        total_pages = (
            (total_count + page_size - 1) // page_size if total_count > 0 else 0
        )

        # Log performance
        elapsed_time = (time.time() - start_time) * 1000
        logger.info(
            f"Retrieved {len(items)} transactions for tenant {tenant_id} "
            f"(page {page}/{total_pages}) in {elapsed_time:.2f}ms"
        )

        return PaginatedTransactionResponse(
            items=items,
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    except Exception as e:
        logger.error(f"Error fetching transactions: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch transactions",
        )


# New endpoint models for review queue and stats - MOVED BEFORE WILDCARD ROUTE
class ReviewQueueItem(BaseModel):
    """Review queue item schema."""

    id: str
    description: str
    amount: float
    date: str
    suggested_categories: List[dict]
    flags: List[str]
    ai_reasoning: Optional[str] = None


class ReviewQueueResponse(BaseModel):
    """Review queue response schema."""

    review_queue: List[ReviewQueueItem]
    queue_size: int
    average_confidence: float
    recommendations: Optional[dict] = None


class ConfidenceDistribution(BaseModel):
    """Confidence distribution schema."""

    high_confidence: int  # >= 0.9
    medium_confidence: int  # 0.7 - 0.89
    low_confidence: int  # < 0.7
    total: int


class TransactionStats(BaseModel):
    """Transaction statistics schema."""

    total_transactions: int
    needs_review: int
    approved: int
    confidence_distribution: ConfidenceDistribution
    processing_summary: dict


@router.get("/review-queue", response_model=ReviewQueueResponse)
async def get_review_queue(
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    confidence_threshold: Optional[float] = Query(
        0.85, description="Confidence threshold for review"
    ),
    limit: Optional[int] = Query(
        50, ge=1, le=200, description="Maximum number of items to return"
    ),
    category_id: Optional[str] = Query(None, description="Filter by category ID"),
    min_amount: Optional[float] = Query(None, description="Minimum amount filter"),
    max_amount: Optional[float] = Query(None, description="Maximum amount filter"),
    start_date: Optional[str] = Query(
        None, description="Start date filter (YYYY-MM-DD)"
    ),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
) -> ReviewQueueResponse:
    """
    Get transactions that need manual review based on confidence thresholds and other criteria.

    Returns transactions that:
    - Have AI confidence below the specified threshold
    - Are uncategorized
    - Have conflicting categorizations
    - Meet any additional filter criteria
    """
    try:
        # Build WHERE clause for review queue criteria
        where_conditions = ["tenant_id = $1"]
        query_params = [tenant_id]
        param_count = 1

        # Main review criteria: low confidence or uncategorized
        review_criteria = []

        # Low confidence AI suggestions
        param_count += 1
        review_criteria.append(
            f"(ai_confidence IS NOT NULL AND ai_confidence < ${param_count})"
        )
        query_params.append(confidence_threshold)

        # Uncategorized transactions
        review_criteria.append("(category_id IS NULL AND original_category IS NULL)")

        # Combine review criteria with OR
        where_conditions.append(f"({' OR '.join(review_criteria)})")

        # Add additional filters
        if category_id:
            param_count += 1
            where_conditions.append(f"category_id = ${param_count}")
            query_params.append(int(category_id))

        if min_amount is not None:
            param_count += 1
            where_conditions.append(f"amount >= ${param_count}")
            query_params.append(min_amount)

        if max_amount is not None:
            param_count += 1
            where_conditions.append(f"amount <= ${param_count}")
            query_params.append(max_amount)

        if start_date:
            param_count += 1
            where_conditions.append(f"date >= ${param_count}")
            # Convert string to date object for PostgreSQL
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                query_params.append(start_date_obj)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid start_date format. Use YYYY-MM-DD",
                )

        if end_date:
            param_count += 1
            where_conditions.append(f"date <= ${param_count}")
            # Convert string to date object for PostgreSQL
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
                query_params.append(end_date_obj)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid end_date format. Use YYYY-MM-DD",
                )

        where_clause = " AND ".join(where_conditions)

        # Main query for review queue items
        main_query = f"""
            SELECT 
                id,
                description,
                amount,
                date,
                ai_category,
                ai_confidence,
                category_id,
                original_category,
                vendor_name,
                transaction_type
            FROM transactions
            WHERE {where_clause}
            ORDER BY 
                CASE 
                    WHEN ai_confidence IS NULL THEN 0
                    ELSE ai_confidence 
                END ASC,
                amount DESC,
                date DESC
            LIMIT ${param_count + 1}
        """
        query_params.append(limit)

        rows = await conn.fetch(main_query, *query_params)

        # Process results into review queue items
        review_items = []
        total_confidence = 0
        confidence_count = 0

        for row in rows:
            # Build suggested categories
            suggested_categories = []
            if row["ai_category"]:
                suggested_categories.append(
                    {
                        "name": row["ai_category"],
                        "confidence": float(row["ai_confidence"])
                        if row["ai_confidence"]
                        else 0.0,
                        "category_id": str(row["category_id"])
                        if row["category_id"]
                        else None,
                    }
                )

            # Determine flags for this transaction
            flags = []
            if not row["ai_category"] and not row["category_id"]:
                flags.append("uncategorized")
            if row["ai_confidence"] and row["ai_confidence"] < confidence_threshold:
                flags.append("low_confidence")
            if row["amount"] and abs(row["amount"]) > 1000:
                flags.append("high_amount")
            if not row["vendor_name"]:
                flags.append("missing_vendor")

            # Generate AI reasoning (simplified)
            ai_reasoning = None
            if row["ai_category"] and row["ai_confidence"]:
                confidence_pct = round(row["ai_confidence"] * 100)
                ai_reasoning = f"AI suggested '{row['ai_category']}' with {confidence_pct}% confidence"

            review_items.append(
                ReviewQueueItem(
                    id=row["id"],
                    description=row["description"],
                    amount=float(row["amount"]),
                    date=row["date"].isoformat()
                    if hasattr(row["date"], "isoformat")
                    else str(row["date"]),
                    suggested_categories=suggested_categories,
                    flags=flags,
                    ai_reasoning=ai_reasoning,
                )
            )

            # Track confidence for average calculation
            if row["ai_confidence"]:
                total_confidence += row["ai_confidence"]
                confidence_count += 1

        # Calculate average confidence
        avg_confidence = (
            total_confidence / confidence_count if confidence_count > 0 else 0.0
        )

        # Generate recommendations
        recommendations = {
            "high_priority": [],
            "bulk_actions": [],
        }

        # High priority: uncategorized high-value transactions
        high_value_uncategorized = [
            item
            for item in review_items
            if "uncategorized" in item.flags and abs(item.amount) > 500
        ]
        if high_value_uncategorized:
            recommendations["high_priority"].append(
                f"Review {len(high_value_uncategorized)} high-value uncategorized transactions"
            )

        # Bulk actions: low confidence but same suggested category
        if len(review_items) > 5:
            recommendations["bulk_actions"].append(
                "Consider batch approval for similar low-confidence suggestions"
            )

        return ReviewQueueResponse(
            review_queue=review_items,
            queue_size=len(review_items),
            average_confidence=round(avg_confidence, 3),
            recommendations=recommendations,
        )

    except Exception as e:
        logger.error(f"Error fetching review queue: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch review queue",
        )


@router.get("/stats", response_model=TransactionStats)
async def get_transaction_stats(
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    start_date: Optional[str] = Query(
        None, description="Start date filter (YYYY-MM-DD)"
    ),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
) -> TransactionStats:
    """
    Get comprehensive transaction statistics for dashboard display.

    Returns:
    - Total transaction count
    - Count of transactions needing review
    - Count of approved/categorized transactions
    - Confidence distribution
    - Processing summary with top categories
    """
    try:
        # Build WHERE clause for date filtering
        where_conditions = ["tenant_id = $1"]
        query_params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            where_conditions.append(f"date >= ${param_count}")
            # Convert string to date object for PostgreSQL
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                query_params.append(start_date_obj)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid start_date format. Use YYYY-MM-DD",
                )

        if end_date:
            param_count += 1
            where_conditions.append(f"date <= ${param_count}")
            # Convert string to date object for PostgreSQL
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
                query_params.append(end_date_obj)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid end_date format. Use YYYY-MM-DD",
                )

        where_clause = " AND ".join(where_conditions)

        # Get total counts and confidence distribution
        stats_query = f"""
            SELECT 
                COUNT(*) as total_transactions,
                COUNT(CASE WHEN (category_id IS NULL AND original_category IS NULL) 
                          OR (ai_confidence IS NOT NULL AND ai_confidence < 0.85) 
                      THEN 1 END) as needs_review,
                COUNT(CASE WHEN category_id IS NOT NULL OR original_category IS NOT NULL 
                      THEN 1 END) as approved,
                COUNT(CASE WHEN ai_confidence >= 0.9 THEN 1 END) as high_confidence,
                COUNT(CASE WHEN ai_confidence >= 0.7 AND ai_confidence < 0.9 THEN 1 END) as medium_confidence,
                COUNT(CASE WHEN ai_confidence < 0.7 THEN 1 END) as low_confidence,
                AVG(ai_confidence) as avg_confidence
            FROM transactions
            WHERE {where_clause}
        """

        stats_row = await conn.fetchrow(stats_query, *query_params)

        # Get top categories
        categories_query = f"""
            SELECT 
                COALESCE(ai_category, original_category, 'Uncategorized') as category_name,
                COUNT(*) as category_count,
                AVG(COALESCE(ai_confidence, 0)) as avg_confidence
            FROM transactions
            WHERE {where_clause}
            GROUP BY COALESCE(ai_category, original_category, 'Uncategorized')
            ORDER BY category_count DESC
            LIMIT 10
        """

        category_rows = await conn.fetch(categories_query, *query_params)

        # Process top categories
        top_categories = []
        for row in category_rows:
            top_categories.append(
                {
                    "name": row["category_name"],
                    "count": row["category_count"],
                    "avg_confidence": round(float(row["avg_confidence"]), 3),
                }
            )

        # Build confidence distribution
        confidence_distribution = ConfidenceDistribution(
            high_confidence=stats_row["high_confidence"] or 0,
            medium_confidence=stats_row["medium_confidence"] or 0,
            low_confidence=stats_row["low_confidence"] or 0,
            total=stats_row["total_transactions"] or 0,
        )

        # Build processing summary
        processing_summary = {
            "avg_confidence": round(
                float(stats_row["avg_confidence"])
                if stats_row["avg_confidence"]
                else 0.0,
                3,
            ),
            "top_categories": top_categories,
        }

        return TransactionStats(
            total_transactions=stats_row["total_transactions"] or 0,
            needs_review=stats_row["needs_review"] or 0,
            approved=stats_row["approved"] or 0,
            confidence_distribution=confidence_distribution,
            processing_summary=processing_summary,
        )

    except Exception as e:
        logger.error(f"Error fetching transaction stats: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch transaction statistics",
        )


@router.get("/export")
async def export_transactions(
    format: str = Query("csv", description="Export format (csv, xlsx)"),
    date_from: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    include_categories: bool = Query(True, description="Include category information"),
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
):
    """Export transactions to CSV or Excel format."""
    try:
        from fastapi.responses import StreamingResponse
        import io
        import csv
        
        # Build query
        where_conditions = ["tenant_id = $1"]
        query_params = [tenant_id]
        param_count = 1
        
        if date_from:
            param_count += 1
            where_conditions.append(f"date >= ${param_count}")
            query_params.append(date_from)
            
        if date_to:
            param_count += 1
            where_conditions.append(f"date <= ${param_count}")
            query_params.append(date_to)
        
        where_clause = " AND ".join(where_conditions)
        
        # Query transactions
        query = f"""
            SELECT 
                id,
                date,
                description,
                amount,
                account,
                transaction_type,
                {'category_id, original_category, ai_category,' if include_categories else ''}
                vendor_name
            FROM transactions
            WHERE {where_clause}
            ORDER BY date DESC
        """
        
        rows = await conn.fetch(query, *query_params)
        
        # Create CSV
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write headers
        headers = ["ID", "Date", "Description", "Amount", "Account", "Type"]
        if include_categories:
            headers.extend(["Category ID", "Original Category", "AI Category"])
        headers.append("Vendor")
        writer.writerow(headers)
        
        # Write data
        for row in rows:
            row_data = [
                row["id"],
                row["date"].isoformat() if row["date"] else "",
                row["description"],
                row["amount"],
                row["account"] or "",
                row["transaction_type"] or "",
            ]
            if include_categories:
                row_data.extend([
                    row["category_id"] or "",
                    row["original_category"] or "",
                    row["ai_category"] or "",
                ])
            row_data.append(row["vendor_name"] or "")
            writer.writerow(row_data)
        
        output.seek(0)
        
        # Return as streaming response
        return StreamingResponse(
            io.BytesIO(output.getvalue().encode("utf-8")),
            media_type="text/csv; charset=utf-8",
            headers={"Content-Disposition": f"attachment; filename=transactions_export.csv"}
        )
        
    except Exception as e:
        logger.error(f"Export failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export transactions",
        )


@router.get("/categorization-insights")
async def get_categorization_insights(
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
):
    """Get categorization insights and patterns."""
    try:
        # Query categorization insights
        insights_query = """
            SELECT 
                COALESCE(original_category, 'Uncategorized') as category_name,
                COUNT(*) as total_transactions,
                COALESCE(AVG(ai_confidence), 0) as ai_accuracy,
                COUNT(CASE WHEN category_id IS NOT NULL THEN 1 END) as user_corrections,
                ARRAY_AGG(DISTINCT vendor_name) FILTER (WHERE vendor_name IS NOT NULL) as common_vendors
            FROM transactions
            WHERE tenant_id = $1
            GROUP BY COALESCE(original_category, 'Uncategorized')
            ORDER BY total_transactions DESC
            LIMIT 20
        """
        
        insights_rows = await conn.fetch(insights_query, tenant_id)
        
        insights = []
        for row in insights_rows:
            insights.append({
                "category_name": row["category_name"],
                "total_transactions": row["total_transactions"],
                "ai_accuracy": float(row["ai_accuracy"]),
                "user_corrections": row["user_corrections"],
                "common_vendors": row["common_vendors"] or [],
            })
        
        return {"insights": insights}
        
    except Exception as e:
        logger.error(f"Failed to get categorization insights: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get categorization insights",
        )


@router.get("/{transaction_id}", response_model=TransactionResponse)
async def get_transaction(
    transaction_id: str,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> TransactionResponse:
    """Get a single transaction by ID."""
    query = """
        SELECT 
            id,
            date,
            description,
            amount,
            account,
            transaction_type,
            category_id,
            original_category,
            ai_category,
            ai_confidence,
            vendor_name,
            entity_id,
            upload_id,
            CASE 
                WHEN category_id IS NOT NULL THEN true 
                ELSE false 
            END as is_user_modified,
            false as user_corrected
        FROM transactions
        WHERE id = $1 AND tenant_id = $2
    """

    row = await conn.fetchrow(query, transaction_id, tenant_id)

    if not row:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found",
        )

    return row_to_transaction_response(dict(row))


@router.put("/batch/category")
@invalidate_cache_on_change(["transactions", "dashboard", "stats"])
async def update_batch_categories(
    request: dict,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> dict:
    """Update categories for multiple transactions, with optional AI categorization."""

    try:
        updated_count = 0
        failed_count = 0
        errors = []

        # Extract request parameters
        transaction_ids = request.get("transaction_ids", [])
        use_ai = request.get("use_ai", False)
        category_id = request.get("category_id")
        _confidence_threshold = request.get("confidence_threshold", 0.7)  # Future use

        # If using AI categorization
        if use_ai:
            logger.info(
                f"Starting PARALLEL AI categorization for {len(transaction_ids)} transactions"
            )

            # Get all transaction details at once
            query = """
                SELECT id, description, amount, transaction_type, date
                FROM transactions 
                WHERE id = ANY($1) AND tenant_id = $2
            """
            rows = await conn.fetch(query, transaction_ids, tenant_id)

            if not rows:
                failed_count = len(transaction_ids)
                errors = [
                    {"transaction_id": tid, "error": "Transaction not found"}
                    for tid in transaction_ids
                ]
            else:
                # Prepare all transactions for parallel processing
                transactions_for_ai = []
                for row in rows:
                    transactions_for_ai.append(
                        {
                            "id": row["id"],
                            "description": row["description"],
                            "amount": float(row["amount"]),
                            "transaction_type": row.get("transaction_type", "debit"),
                            "date": row["date"],
                        }
                    )

                # Use MIS categorization service for batch processing
                from ..categories.mis_categorization_service import (
                    MISCategorizationService,
                )

                # Initialize MIS categorization service
                mis_service = MISCategorizationService(conn)

                # Extract transaction IDs for processing
                transaction_ids_for_ai = [t["id"] for t in transactions_for_ai]

                # Process ALL transactions using MIS categorization
                batch_results = await mis_service.categorize_transactions(
                    tenant_id=tenant_id,
                    transaction_ids=transaction_ids_for_ai,
                    confidence_threshold=_confidence_threshold,
                )

                # Update database with batch results
                if batch_results.get("success") and "results" in batch_results:
                    for result in batch_results["results"]:
                        transaction_id = result.get("transaction_id")
                        category = result.get("suggested_category")
                        confidence = result.get("confidence", 0.85)

                        # Log the result for debugging
                        logger.info(
                            f"Categorization result: transaction_id={transaction_id}, category={category}, confidence={confidence}"
                        )
                        logger.debug(f"Full result: {result}")

                        if transaction_id and category:
                            update_query = """
                                UPDATE transactions 
                                SET ai_category = $1, ai_confidence = $2, updated_at = NOW()
                                WHERE id = $3 AND tenant_id = $4
                            """

                            await conn.execute(
                                update_query,
                                category,
                                confidence,
                                transaction_id,
                                tenant_id,
                            )
                            updated_count += 1
                        else:
                            failed_count += 1
                            errors.append(
                                {
                                    "transaction_id": transaction_id,
                                    "error": f"Invalid categorization result - category: {category}, transaction_id: {transaction_id}",
                                }
                            )
                else:
                    failed_count = len(transaction_ids)
                    errors = [
                        {
                            "transaction_id": tid,
                            "error": "Parallel categorization failed",
                        }
                        for tid in transaction_ids
                    ]

        else:
            # Manual category assignment
            if category_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="category_id is required when not using AI categorization",
                )

            # Verify category exists and belongs to tenant
            category_check_query = "SELECT id FROM categories WHERE id = $1 AND tenant_id = $2"
            category_exists = await conn.fetchval(category_check_query, category_id, tenant_id)

            if not category_exists:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Category {category_id} not found or does not belong to your account",
                )

            # Update all transactions with the specified category
            update_query = """
                UPDATE transactions 
                SET category_id = $1, updated_at = NOW()
                WHERE id = ANY($2) AND tenant_id = $3
            """

            result = await conn.execute(
                update_query, category_id, transaction_ids, tenant_id
            )

            # Parse affected rows from result
            updated_count = int(result.split()[-1]) if result else 0

        return {
            "success": True,
            "message": f"Updated {updated_count} transactions, {failed_count} failed",
            "updated_count": updated_count,
            "failed_count": failed_count,
            "errors": errors[:10],  # Limit errors for response size
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in batch category update: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update batch categories: {str(e)}",
        )


@router.put("/{transaction_id}/category")
@invalidate_cache_on_change(["transactions", "dashboard", "stats"])
async def update_transaction_category(
    transaction_id: str,
    category_id: int,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> dict:
    """Update transaction category (user modification)."""
    try:
        # Verify transaction exists and belongs to tenant
        check_query = "SELECT id FROM transactions WHERE id = $1 AND tenant_id = $2"
        exists = await conn.fetchval(check_query, transaction_id, tenant_id)

        if not exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction not found",
            )

        # Verify category exists and belongs to tenant
        category_check_query = "SELECT id FROM categories WHERE id = $1 AND tenant_id = $2"
        category_exists = await conn.fetchval(category_check_query, category_id, tenant_id)

        if not category_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Category {category_id} not found or does not belong to your account",
            )

        # Update category
        update_query = """
            UPDATE transactions 
            SET category_id = $1, updated_at = NOW()
            WHERE id = $2 AND tenant_id = $3
            RETURNING id
        """

        result = await conn.fetchval(
            update_query, category_id, transaction_id, tenant_id
        )

        if result:
            logger.info(
                f"Updated category for transaction {transaction_id} to {category_id}"
            )
            return {"success": True, "transaction_id": transaction_id}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update transaction",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating transaction category: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update transaction category",
        )


@router.post("/{transaction_id}/approve-ai-suggestion")
async def approve_ai_suggestion(
    transaction_id: str,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> dict:
    """Approve AI-suggested category for a transaction."""
    try:
        # Get AI suggestion
        query = """
            SELECT ai_category, ai_confidence 
            FROM transactions 
            WHERE id = $1 AND tenant_id = $2
        """
        row = await conn.fetchrow(query, transaction_id, tenant_id)

        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction not found",
            )

        if not row["ai_category"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No AI suggestion available for this transaction",
            )

        # Update transaction to mark as approved
        update_query = """
            UPDATE transactions 
            SET needs_review = FALSE, is_categorized = TRUE, updated_at = NOW()
            WHERE id = $1 AND tenant_id = $2
        """
        await conn.execute(update_query, transaction_id, tenant_id)
        
        # Emit WebSocket event for real-time updates
        from ...shared.services.websocket_service import WebSocketService
        ws_service = WebSocketService()
        await ws_service.emit_event(
            event_type="transaction.approved",
            data={
                "transaction_id": transaction_id,
                "category": row["ai_category"],
                "confidence": float(row["ai_confidence"]) if row["ai_confidence"] else None,
                "approved_by": current_user.id,
                "approved_at": datetime.now().isoformat()
            },
            tenant_id=tenant_id
        )

        logger.info(
            f"Approved AI suggestion '{row['ai_category']}' "
            f"(confidence: {row['ai_confidence']}) for transaction {transaction_id}"
        )

        return {
            "success": True,
            "transaction_id": transaction_id,
            "ai_category": row["ai_category"],
            "confidence": float(row["ai_confidence"]) if row["ai_confidence"] else None,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error approving AI suggestion: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to approve AI suggestion",
        )


@router.post("/bulk-approve")
async def bulk_approve_transactions(
    transaction_ids: List[str],
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> dict:
    """Approve multiple transactions in bulk."""
    try:
        if not transaction_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No transaction IDs provided",
            )
        
        # Update all transactions to approved
        update_query = """
            UPDATE transactions 
            SET needs_review = FALSE, is_categorized = TRUE, updated_at = NOW()
            WHERE id = ANY($1::text[]) AND tenant_id = $2
            RETURNING id, ai_category, ai_confidence
        """
        rows = await conn.fetch(update_query, transaction_ids, tenant_id)
        
        # Count successful updates
        approved_count = len(rows)
        
        if approved_count > 0:
            # Emit WebSocket event for bulk approval
            from ...shared.services.websocket_service import WebSocketService
            ws_service = WebSocketService()
            await ws_service.emit_event(
                event_type="transaction.bulk_approved",
                data={
                    "transaction_ids": [str(row["id"]) for row in rows],
                    "approved_count": approved_count,
                    "approved_by": current_user.id,
                    "approved_at": datetime.now().isoformat()
                },
                tenant_id=tenant_id
            )
            
            logger.info(f"Bulk approved {approved_count} transactions by user {current_user.id}")
        
        return {
            "success": True,
            "approved_count": approved_count,
            "requested_count": len(transaction_ids),
            "transaction_ids": [str(row["id"]) for row in rows],
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk approval: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to approve transactions",
        )


@router.delete("/{transaction_id}")
@invalidate_cache_on_change(["transactions", "dashboard", "stats"])
async def delete_transaction(
    transaction_id: str,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> dict:
    """Delete a transaction."""
    try:
        from .service import TransactionService
        
        service = TransactionService(conn)
        success = await service.delete_transaction(int(transaction_id), tenant_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction not found or could not be deleted",
            )
        
        logger.info(f"Transaction {transaction_id} deleted by user {current_user.id}")
        return {"success": True, "message": "Transaction deleted successfully"}
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid transaction ID format",
        )
    except Exception as e:
        logger.error(f"Failed to delete transaction {transaction_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete transaction",
        )


class TransactionUpdateRequest(BaseModel):
    """Request schema for updating transaction details."""
    description: Optional[str] = None
    amount: Optional[float] = None
    account: Optional[str] = None
    transaction_type: Optional[str] = None
    category_id: Optional[int] = None


@router.put("/{transaction_id}")
@invalidate_cache_on_change(["transactions", "dashboard", "stats"])
async def update_transaction(
    transaction_id: str,
    update_request: TransactionUpdateRequest,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
) -> TransactionResponse:
    """Update transaction details."""
    try:
        from .service import TransactionService
        
        service = TransactionService(conn)
        
        # Convert request to dict, excluding None values
        update_data = {k: v for k, v in update_request.dict().items() if v is not None}
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No update data provided",
            )
        
        # Update the transaction
        updated_transaction = await service.update_transaction(
            int(transaction_id), update_data, tenant_id
        )
        
        logger.info(f"Transaction {transaction_id} updated by user {current_user.id}")
        
        # Convert to response format
        return TransactionResponse(
            id=str(updated_transaction.id),
            date=updated_transaction.date.isoformat() if updated_transaction.date else "",
            description=updated_transaction.description or "",
            amount=float(updated_transaction.amount) if updated_transaction.amount else 0.0,
            account=updated_transaction.account,
            transaction_type=updated_transaction.transaction_type,
            category_id=updated_transaction.category_id,
            category_path=getattr(updated_transaction, 'category_path', None),
            ai_suggested_category=updated_transaction.ai_suggested_category,
            ai_suggested_category_path=getattr(updated_transaction, 'ai_suggested_category_path', None),
            confidence=getattr(updated_transaction, 'confidence', None),
            needs_review=getattr(updated_transaction, 'needs_review', False),
            tenant_id=updated_transaction.tenant_id,
        )
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid transaction ID format",
        )
    except Exception as e:
        logger.error(f"Failed to update transaction {transaction_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update transaction",
        )


class CategorizationImprovementRequest(BaseModel):
    """Request schema for categorization improvement endpoint."""
    
    model_config = ConfigDict(from_attributes=True)
    
    transaction_ids: List[str]
    improvement_type: str = "accuracy"  # accuracy, business_appropriateness, confidence
    feedback: Optional[str] = None


class CategorizationImprovementResponse(BaseModel):
    """Response schema for categorization improvement."""
    
    model_config = ConfigDict(from_attributes=True)
    
    improvement_id: str
    transaction_count: int
    improvements_suggested: int
    processing_status: str
    analysis: dict
    recommendations: List[dict]


@router.post("/improve-categorization", response_model=CategorizationImprovementResponse)
async def improve_categorization(
    improvement_request: CategorizationImprovementRequest,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
):
    """
    Analyze transactions and suggest categorization improvements.
    
    This endpoint analyzes the specified transactions and provides AI-powered
    suggestions for improving categorization accuracy, business appropriateness,
    and confidence levels.
    
    Returns:
        Categorization ImprovementResponse with analysis and recommendations
    """
    try:
        from uuid import uuid4

        from ..categories.categorization_agent import CategorizationAgent
        
        improvement_id = str(uuid4())
        transaction_ids = improvement_request.transaction_ids
        
        if not transaction_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No transaction IDs provided",
            )
        
        logger.info(f"Processing categorization improvement for {len(transaction_ids)} transactions")
        
        # Fetch transactions from database using integer IDs
        query = """
            SELECT 
                id, description, amount, date, account, transaction_type,
                category_id, ai_confidence, ai_category, original_category,
                tenant_id
            FROM transactions 
            WHERE tenant_id = $1 AND id = ANY($2::integer[])
        """
        
        # Validate integer format for transaction IDs
        try:
            validated_transaction_ids = []
            for tid in transaction_ids:
                # Validate each transaction ID as a valid integer
                int_id = int(tid)
                validated_transaction_ids.append(int_id)
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid transaction ID format (must be integer): {e}",
            )
        
        transactions = await conn.fetch(query, tenant_id, validated_transaction_ids)
        
        if not transactions:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No transactions found with provided IDs",
            )
        
        # Initialize categorization agent
        agent = CategorizationAgent()
        
        # Analyze each transaction for improvement opportunities
        improvements_suggested = 0
        analysis = {
            "total_analyzed": len(transactions),
            "low_confidence_count": 0,
            "miscategorized_count": 0,
            "business_inappropriate_count": 0,
            "avg_confidence": 0.0,
        }
        
        recommendations = []
        total_confidence = 0.0
        
        for tx in transactions:
            tx_confidence = float(tx["ai_confidence"]) if tx["ai_confidence"] else 0.0
            total_confidence += tx_confidence
            
            # Analyze low confidence transactions
            if tx_confidence < 0.85:
                analysis["low_confidence_count"] += 1
                improvements_suggested += 1
                
                # Get AI suggestions for improvement
                try:
                    suggestions = await agent.suggest_categories(
                        transaction_description=tx["description"] or "",
                        amount=float(tx["amount"]) if tx["amount"] else 0.0,
                        tenant_id=tenant_id,
                        conn=conn,
                    )
                    
                    if suggestions:
                        best_suggestion = suggestions[0]
                        recommendations.append({
                            "transaction_id": str(tx["id"]),
                            "description": tx["description"],
                            "current_category": tx["ai_category"] or tx["original_category"] or "Uncategorized",
                            "current_confidence": tx_confidence,
                            "suggested_category": best_suggestion.category_name,
                            "suggested_confidence": best_suggestion.confidence,
                            "improvement_reason": f"Confidence improvement: {tx_confidence:.2f} → {best_suggestion.confidence:.2f}",
                            "ai_reasoning": best_suggestion.reasoning,
                        })
                        
                except Exception as e:
                    logger.warning(f"Failed to get suggestions for transaction {tx['id']}: {e}")
            
            # Check for business appropriateness issues
            tx_dict = dict(tx)
            try:
                appropriateness = await agent._validate_business_appropriateness(tx_dict)
                if not appropriateness.get("is_appropriate", True):
                    analysis["business_inappropriate_count"] += 1
                    improvements_suggested += 1
                    
                    recommendations.append({
                        "transaction_id": str(tx["id"]),
                        "description": tx["description"],
                        "current_category": tx["ai_category"] or tx["original_category"] or "Uncategorized",
                        "improvement_type": "business_appropriateness",
                        "issue": appropriateness.get("issue", "Not business appropriate"),
                        "suggested_action": appropriateness.get("suggested_action", "Review and recategorize"),
                    })
                    
            except Exception as e:
                logger.warning(f"Failed to validate business appropriateness for transaction {tx['id']}: {e}")
        
        # Calculate average confidence
        analysis["avg_confidence"] = total_confidence / len(transactions) if transactions else 0.0
        
        # Processing status
        processing_status = "completed"
        if improvements_suggested == 0:
            processing_status = "no_improvements_needed"
        elif improvements_suggested > len(transactions) * 0.5:
            processing_status = "significant_improvements_available"
        
        logger.info(f"Categorization improvement analysis completed: {improvements_suggested} improvements suggested")
        
        return CategorizationImprovementResponse(
            improvement_id=improvement_id,
            transaction_count=len(transactions),
            improvements_suggested=improvements_suggested,
            processing_status=processing_status,
            analysis=analysis,
            recommendations=recommendations,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to process categorization improvement: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process categorization improvement",
        )
