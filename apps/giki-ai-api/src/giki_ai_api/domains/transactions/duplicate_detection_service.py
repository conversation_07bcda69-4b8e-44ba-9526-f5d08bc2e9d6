"""
Tenant-Aware Duplicate Detection Service
=======================================

Comprehensive duplicate detection to prevent data bloat while maintaining tenant isolation.

Key Features:
- Multiple detection strategies (exact, fuzzy, date-amount)
- Tenant-aware isolation (never cross-tenant duplicates)
- Configurable sensitivity levels
- Batch processing for large uploads
- Audit trail of duplicate decisions
"""

import hashlib
import logging
from dataclasses import dataclass
from datetime import datetime
from difflib import SequenceMatcher
from typing import Any, Dict, List, Optional, Set

from asyncpg import Connection

logger = logging.getLogger(__name__)


@dataclass
class DuplicateMatch:
    """Represents a potential duplicate match."""

    existing_transaction_id: str
    confidence: float
    match_type: str
    match_details: Dict[str, Any]
    recommended_action: str  # 'skip', 'merge', 'keep_both'


@dataclass
class DeduplicationResult:
    """Result of duplicate detection process."""

    total_checked: int
    duplicates_found: int
    duplicates_skipped: int
    unique_transactions: int
    duplicate_matches: List[DuplicateMatch]
    processing_time_ms: float


class DuplicateDetectionService:
    """
    Service for detecting and handling duplicate transactions with tenant isolation.
    """

    def __init__(self, conn: Connection):
        self.conn = conn

        # Detection thresholds
        self.EXACT_MATCH_THRESHOLD = 1.0
        self.FUZZY_MATCH_THRESHOLD = 0.95
        self.DATE_AMOUNT_THRESHOLD = 0.9
        self.DESCRIPTION_SIMILARITY_THRESHOLD = 0.85

        # Date tolerance for near-duplicate detection (3 days)
        self.DATE_TOLERANCE_DAYS = 3

        logger.info("DuplicateDetectionService initialized with tenant-aware detection")

    async def detect_duplicates_batch(
        self,
        tenant_id: int,
        transactions: List[Dict[str, Any]] = None,
        detection_strategy: str = "comprehensive",
        skip_duplicates: bool = True,
        check_existing: bool = True,  # Added for test compatibility
        **kwargs,
    ) -> DeduplicationResult:
        """
        Detect duplicates in a batch of transactions for a specific tenant.

        Args:
            tenant_id: Tenant ID for isolation
            transactions: List of transaction data to check
            detection_strategy: 'exact', 'fuzzy', 'comprehensive'
            skip_duplicates: Whether to automatically skip duplicates

        Returns:
            DeduplicationResult with findings and recommendations
        """
        import time

        start_time = time.time()
        
        # Handle None transactions for test compatibility
        if transactions is None:
            transactions = []

        logger.info(
            f"Starting duplicate detection for {len(transactions)} transactions (tenant {tenant_id})"
        )

        duplicate_matches = []
        duplicates_found = 0
        duplicates_skipped = 0
        unique_transactions = 0

        # Get existing transactions for this tenant
        existing_transactions = await self._get_existing_transactions(tenant_id)
        existing_lookup = self._build_lookup_index(existing_transactions)

        for transaction in transactions:
            matches = await self._detect_transaction_duplicates(
                transaction, existing_lookup, detection_strategy, tenant_id
            )

            if matches:
                duplicate_matches.extend(matches)
                duplicates_found += 1
                if skip_duplicates:
                    duplicates_skipped += 1
                    logger.debug(
                        f"Skipping duplicate: {transaction.get('description', '')[:50]}"
                    )
            else:
                unique_transactions += 1

        processing_time_ms = (time.time() - start_time) * 1000

        result = DeduplicationResult(
            total_checked=len(transactions),
            duplicates_found=duplicates_found,
            duplicates_skipped=duplicates_skipped,
            unique_transactions=unique_transactions,
            duplicate_matches=duplicate_matches,
            processing_time_ms=processing_time_ms,
        )

        logger.info(
            f"Duplicate detection completed: {unique_transactions} unique, "
            f"{duplicates_found} duplicates found, {duplicates_skipped} skipped "
            f"(tenant {tenant_id}, {processing_time_ms:.1f}ms)"
        )

        return result

    async def _get_existing_transactions(self, tenant_id: int) -> List[Dict[str, Any]]:
        """Get all existing transactions for a tenant."""
        query = """
            SELECT id, date, description, amount, account, transaction_type, created_at
            FROM transactions 
            WHERE tenant_id = $1
            ORDER BY date DESC, created_at DESC
        """

        rows = await self.conn.fetch(query, tenant_id)
        return [dict(row) for row in rows]

    def _build_lookup_index(self, transactions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build optimized lookup indexes for duplicate detection."""
        lookup = {
            "exact_hash": {},  # exact matches by hash
            "date_amount": {},  # matches by date+amount
            "description_tokens": {},  # fuzzy description matching
            "all_transactions": transactions,
        }

        for tx in transactions:
            # Exact hash (date, amount, description normalized)
            exact_hash = self._generate_transaction_hash(tx)
            if exact_hash not in lookup["exact_hash"]:
                lookup["exact_hash"][exact_hash] = []
            lookup["exact_hash"][exact_hash].append(tx)

            # Date + Amount index
            date_amount_key = f"{tx['date']}_{tx['amount']}"
            if date_amount_key not in lookup["date_amount"]:
                lookup["date_amount"][date_amount_key] = []
            lookup["date_amount"][date_amount_key].append(tx)

            # Description tokens for fuzzy matching
            description_tokens = self._extract_description_tokens(
                tx.get("description", "")
            )
            for token in description_tokens:
                if token not in lookup["description_tokens"]:
                    lookup["description_tokens"][token] = []
                lookup["description_tokens"][token].append(tx)

        return lookup

    async def _detect_transaction_duplicates(
        self,
        transaction: Dict[str, Any],
        existing_lookup: Dict[str, Any],
        strategy: str,
        tenant_id: int,
    ) -> List[DuplicateMatch]:
        """Detect duplicates for a single transaction."""
        matches = []

        # Strategy 1: Exact Hash Match
        if strategy in ["exact", "comprehensive"]:
            exact_matches = self._find_exact_matches(transaction, existing_lookup, tenant_id)
            matches.extend(exact_matches)

        # Strategy 2: Date + Amount Match
        if strategy in ["fuzzy", "comprehensive"] and not matches:
            date_amount_matches = self._find_date_amount_matches(
                transaction, existing_lookup, tenant_id
            )
            matches.extend(date_amount_matches)

        # Strategy 3: Fuzzy Description Match
        if strategy in ["fuzzy", "comprehensive"] and not matches:
            fuzzy_matches = self._find_fuzzy_description_matches(
                transaction, existing_lookup, tenant_id
            )
            matches.extend(fuzzy_matches)

        return matches

    def _find_exact_matches(
        self, transaction: Dict[str, Any], existing_lookup: Dict[str, Any], tenant_id: int
    ) -> List[DuplicateMatch]:
        """Find exact duplicate matches."""
        exact_hash = self._generate_transaction_hash(transaction)
        exact_matches = existing_lookup["exact_hash"].get(exact_hash, [])

        matches = []
        for existing_tx in exact_matches:
            match = DuplicateMatch(
                existing_transaction_id=existing_tx["id"],
                confidence=1.0,
                match_type="exact_hash",
                match_details={
                    "hash": exact_hash,
                    "existing_description": existing_tx.get("description", ""),
                    "new_description": transaction.get("description", ""),
                },
                recommended_action="skip",
            )
            matches.append(match)

        return matches

    def _find_date_amount_matches(
        self, transaction: Dict[str, Any], existing_lookup: Dict[str, Any], tenant_id: int
    ) -> List[DuplicateMatch]:
        """Find matches based on date and amount proximity."""
        matches = []
        tx_date = transaction.get("date")
        tx_amount = transaction.get("amount")

        if not tx_date or tx_amount is None:
            return matches

        # Check exact date+amount first
        date_amount_key = f"{tx_date}_{tx_amount}"
        exact_date_amount_matches = existing_lookup["date_amount"].get(
            date_amount_key, []
        )

        for existing_tx in exact_date_amount_matches:
            # Calculate description similarity
            desc_similarity = self._calculate_description_similarity(
                transaction.get("description", ""), existing_tx.get("description", "")
            )

            if desc_similarity >= self.DESCRIPTION_SIMILARITY_THRESHOLD:
                confidence = (desc_similarity + self.DATE_AMOUNT_THRESHOLD) / 2
                match = DuplicateMatch(
                    existing_transaction_id=existing_tx["id"],
                    confidence=confidence,
                    match_type="date_amount_description",
                    match_details={
                        "date_match": True,
                        "amount_match": True,
                        "description_similarity": desc_similarity,
                        "existing_description": existing_tx.get("description", ""),
                        "new_description": transaction.get("description", ""),
                    },
                    recommended_action="skip" if confidence > 0.95 else "merge",
                )
                matches.append(match)

        return matches

    def _find_fuzzy_description_matches(
        self, transaction: Dict[str, Any], existing_lookup: Dict[str, Any], tenant_id: int
    ) -> List[DuplicateMatch]:
        """Find matches using fuzzy description matching."""
        matches = []
        tx_description = transaction.get("description", "")
        tx_amount = transaction.get("amount")
        tx_date = transaction.get("date")

        if not tx_description or not tx_date or tx_amount is None:
            return matches

        # Get transactions within date tolerance
        candidate_transactions = []

        for existing_tx in existing_lookup["all_transactions"]:
            existing_date = existing_tx.get("date")
            if (
                existing_date
                and abs((tx_date - existing_date).days) <= self.DATE_TOLERANCE_DAYS
            ):
                candidate_transactions.append(existing_tx)

        # Check fuzzy matches among candidates
        for existing_tx in candidate_transactions:
            existing_amount = existing_tx.get("amount")
            existing_description = existing_tx.get("description", "")

            # Amount must be exact or very close
            amount_match = (
                tx_amount == existing_amount
                or abs(float(tx_amount) - float(existing_amount)) < 0.01
            )

            if amount_match:
                desc_similarity = self._calculate_description_similarity(
                    tx_description, existing_description
                )

                if desc_similarity >= self.FUZZY_MATCH_THRESHOLD:
                    confidence = desc_similarity * 0.9  # Slightly lower for fuzzy
                    match = DuplicateMatch(
                        existing_transaction_id=existing_tx["id"],
                        confidence=confidence,
                        match_type="fuzzy_description",
                        match_details={
                            "description_similarity": desc_similarity,
                            "date_diff_days": abs((tx_date - existing_tx["date"]).days),
                            "amount_match": amount_match,
                            "existing_description": existing_description,
                            "new_description": tx_description,
                        },
                        recommended_action="merge"
                        if confidence > 0.98
                        else "keep_both",
                    )
                    matches.append(match)

        return matches

    def _generate_transaction_hash(self, transaction: Dict[str, Any]) -> str:
        """Generate a hash for exact duplicate detection."""
        # Normalize data for consistent hashing
        date_str = str(transaction.get("date", ""))
        amount_str = f"{float(transaction.get('amount', 0)):.2f}"
        description = self._normalize_description(transaction.get("description", ""))
        account = transaction.get("account", "")

        # Create hash from key fields
        hash_input = f"{date_str}|{amount_str}|{description}|{account}"
        return hashlib.md5(hash_input.encode()).hexdigest()

    def _normalize_description(self, description: str) -> str:
        """Normalize description for consistent comparison."""
        if not description:
            return ""

        # Remove extra whitespace, convert to lowercase
        normalized = " ".join(description.lower().split())

        # Remove common transaction prefixes/suffixes
        prefixes_to_remove = ["pos ", "atm ", "check ", "transfer ", "payment "]
        suffixes_to_remove = [" inc", " llc", " corp", " ltd"]

        for prefix in prefixes_to_remove:
            if normalized.startswith(prefix):
                normalized = normalized[len(prefix) :]

        for suffix in suffixes_to_remove:
            if normalized.endswith(suffix):
                normalized = normalized[: -len(suffix)]

        return normalized.strip()

    def _extract_description_tokens(self, description: str) -> Set[str]:
        """Extract meaningful tokens from description for indexing."""
        if not description:
            return set()

        # Normalize and split
        normalized = self._normalize_description(description)
        tokens = set(normalized.split())

        # Filter out very short tokens and common words
        common_words = {"the", "and", "or", "at", "to", "from", "for", "in", "on", "of"}
        meaningful_tokens = {
            token for token in tokens if len(token) >= 3 and token not in common_words
        }

        return meaningful_tokens

    def _calculate_description_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity between two descriptions."""
        if not desc1 or not desc2:
            return 0.0

        # Normalize both descriptions
        norm_desc1 = self._normalize_description(desc1)
        norm_desc2 = self._normalize_description(desc2)

        if norm_desc1 == norm_desc2:
            return 1.0

        # Use sequence matcher for similarity
        similarity = SequenceMatcher(None, norm_desc1, norm_desc2).ratio()
        return similarity

    async def log_duplicate_decision(
        self,
        tenant_id: int,
        transaction_data: Dict[str, Any],
        duplicate_match: DuplicateMatch,
        action_taken: str,
        transaction_id: Optional[str] = None,
    ) -> None:
        """Log duplicate detection decision for audit trail."""
        try:
            log_query = """
                INSERT INTO duplicate_detection_log 
                (tenant_id, new_transaction_hash, existing_transaction_id, 
                 confidence, match_type, action_taken, match_details, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
            """

            # Use provided transaction_id if available, otherwise generate hash
            tx_id = transaction_id or self._generate_transaction_hash(transaction_data)
            transaction_hash = tx_id if isinstance(tx_id, str) else self._generate_transaction_hash(transaction_data)
            
            await self.conn.execute(
                log_query,
                tenant_id,
                transaction_hash,
                duplicate_match.existing_transaction_id,
                duplicate_match.confidence,
                duplicate_match.match_type,
                action_taken,
                duplicate_match.match_details,
            )

        except Exception as e:
            # Don't fail the main process for logging issues
            logger.warning(f"Failed to log duplicate decision: {e}")

    async def get_duplicate_statistics(
        self, 
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """Get duplicate detection statistics for a tenant."""
        try:
            # Build query with optional date filtering
            query_parts = ["""
                SELECT 
                    COUNT(*) as total_checks,
                    COUNT(CASE WHEN action_taken = 'skip' THEN 1 END) as skipped_duplicates,
                    COUNT(CASE WHEN action_taken = 'merge' THEN 1 END) as merged_duplicates,
                    AVG(confidence) as avg_confidence,
                    COUNT(DISTINCT match_type) as detection_methods_used
                FROM duplicate_detection_log
                WHERE tenant_id = $1
            """]
            
            params = [tenant_id]
            param_count = 1
            
            # Add date filters if provided
            if start_date:
                param_count += 1
                query_parts.append(f"AND created_at >= ${param_count}")
                params.append(start_date)
                
            if end_date:
                param_count += 1
                query_parts.append(f"AND created_at <= ${param_count}")
                params.append(end_date)
            else:
                # Default to last 30 days if no end_date provided
                query_parts.append("AND created_at > NOW() - INTERVAL '30 days'")
            
            stats_query = " ".join(query_parts)
            result = await self.conn.fetchrow(stats_query, *params)
            return dict(result) if result else {}

        except Exception as e:
            logger.warning(f"Failed to get duplicate statistics: {e}")
            return {}
