from datetime import (
    date as date_type,
    datetime,
)  # Aliased date, import datetime explicitly for strptime

from pydantic import BaseModel, ConfigDict, Field, field_validator


class BaseTransactionSchema(BaseModel):  # Renamed from TransactionBase
    """Base model for transaction data, shared fields"""

    date: date_type | None = Field(default=None, description="Transaction date")
    description: str | None = Field(default=None, description="Transaction description")
    amount: float | None = Field(default=None, description="Transaction amount")
    account: str | None = Field(default=None, description="Account identifier")
    tags: list[str] | None = Field(default_factory=list, description="List of tags")
    transaction_type: str | None = Field(
        default=None, description="Type of transaction (e.g., expense, income)"
    )
    # Invoice specific fields
    is_invoice: bool | None = Field(
        default=False, description="Is this transaction an invoice?"
    )
    invoice_number: str | None = Field(
        default=None, description="Invoice number, if applicable."
    )
    invoice_due_date: date_type | None = Field(
        default=None, description="Due date for the invoice."
    )
    invoice_status: str | None = Field(
        default=None, description="Status of the invoice (e.g., Draft, Sent, Paid)."
    )
    related_invoice_id: str | None = Field(
        default=None,
        description="ID of a related invoice (e.g., if this is a payment).",
    )
    invoice_details: dict | None = Field(
        default=None,
        description="Additional structured details for the invoice (e.g., line items).",
    )
    agent_verified: bool | None = Field(
        default=None,
        description="Indicates if an agent has verified this invoice/transaction.",
    )
    is_paid: bool | None = Field(
        default=None, description="Indicates if the invoice has been paid."
    )


class CreateTransactionSchema(
    BaseTransactionSchema
):  # Renamed and inherits from new base
    """Model for creating a transaction"""

    date: date_type = Field(default=..., description="Transaction date")
    description: str = Field(default=..., description="Transaction description")
    amount: float = Field(default=..., description="Transaction amount")
    # Optional fields from BaseTransactionSchema can be overridden if needed,
    # but by default, they will be optional here as well unless re-declared as required.
    # For a "Create" schema, we usually want core fields to be required.
    # The current setup makes date, description, amount required, others remain optional from base.

    # Pydantic v2 handles date parsing automatically, validator might not be needed
    # or can be simplified if specific string formats must be accepted before conversion.
    # For now, assuming Pydantic handles direct date type or common string conversions.
    # If specific string input like "YYYY-MM-DD" is required from API, keep/adjust validator.
    # The router's process_file_with_mapping now tries to parse dates into datetime.date objects.

    @field_validator("date", mode="before")
    @classmethod
    def parse_date_str(cls, value):
        if isinstance(value, str):
            formats_to_try = [
                "%m/%d/%Y",  # MM/DD/YYYY e.g., 11/30/2023
                "%d/%m/%Y",  # DD/MM/YYYY
                "%Y-%m-%d",  # YYYY-MM-DD (ISO)
                "%d-%m-%Y",  # DD-MM-YYYY
                "%m-%d-%Y",  # MM-DD-YYYY
                "%m/%d/%y",  # MM/DD/YY e.g., 5/29/24
                "%Y-%m-%d %H:%M:%S",  # YYYY-MM-DD HH:MM:SS e.g., 2024-09-04 00:00:00
            ]
            for fmt in formats_to_try:
                try:
                    # For datetime strings, convert to date object
                    dt_obj = datetime.strptime(value, fmt)
                    return dt_obj.date()
                except ValueError:
                    continue
            # Try parsing if it's just a date part of a timestamp (e.g. "YYYY-MM-DD" from "YYYY-MM-DD HH:MM:SS")
            if " " in value:
                try:
                    date_part = value.split(" ")[0]
                    dt_obj = datetime.strptime(
                        date_part, "%Y-%m-%d"
                    )  # Assuming ISO format for the date part
                    return dt_obj.date()
                except ValueError:
                    pass

            raise ValueError(
                f"Date string '{value}' not in a recognized format (e.g., MM/DD/YYYY, YYYY-MM-DD, M/D/YY)"
            )
        # If it's already a date or datetime object, Pydantic will handle it
        return value


class TransactionUpdate(BaseTransactionSchema):
    """Model for updating a transaction, all fields optional"""

    # All fields are optional as inherited from BaseTransactionSchema
    # Any fields that are provided will replace existing values
    pass


class TransactionResponse(BaseTransactionSchema):
    """Model for transaction response, includes ID, timestamps, and categorization details"""

    id: str

    # User-assigned category
    category_id: int | None = Field(default=None)  # Added Field
    category_path: str | None = Field(default=None)  # Added Field

    # AI-suggested category
    ai_suggested_category: str | None = Field(default=None)  # Changed to str
    ai_suggested_category_path: str | None = Field(default=None)  # Added Field
    ai_category_confidence: float | None = Field(default=None)  # Added Field

    # Category status flags
    is_categorized: bool = False
    is_user_modified: bool = False  # Matches model field name

    upload_id: str
    tenant_id: int  # Assuming tenant_id is always present for a transaction

    created_at: datetime
    updated_at: datetime

    # Invoice specific fields from BaseTransactionSchema are inherited
    # No need to repeat them unless their optionality/type changes for response

    model_config = ConfigDict(from_attributes=True)  # Pydantic v2 for ORM mode


class TransactionListResponse(BaseModel):  # Renamed from TransactionList
    """Model for list of transactions with pagination info"""

    items: list[TransactionResponse]
    total: int
    skip: int
    limit: int


class TransactionQueryFilters(BaseModel):
    """Query parameters for filtering transactions."""

    start_date: date_type | None = Field(
        default=None, description="Filter by start date (YYYY-MM-DD)"
    )  # Using date_type
    end_date: date_type | None = Field(
        default=None, description="Filter by end date (YYYY-MM-DD)"
    )  # Using date_type
    categorization_status: str | None = Field(
        default=None,
        description="Filter by categorization status (e.g., 'pending_review', 'confirmed', 'uncategorized')",
    )


class PaginationParams(BaseModel):
    """Query parameters for pagination."""

    skip: int = Field(0, ge=0, description="Number of records to skip for pagination")
    limit: int = Field(
        10000, ge=1, le=50000, description="Maximum number of records to return"
    )


# ProcessedFileResponse is now defined in and imported from schemas.upload
class TransactionReadWithAISuggestion(BaseModel):
    """
    Schema for transaction responses that include AI category suggestions.
    """

    id: str
    description: str
    # Core transaction fields
    amount: float
    date: str  # ISO format date string
    # Category fields
    category_id: int | None = Field(default=None)  # Added Field
    category_path: str | None = Field(default=None)  # Added Field
    # AI suggestion fields
    ai_suggested_category: str | None = Field(default=None)  # Changed to str
    ai_suggested_category_path: str | None = Field(default=None)  # Added Field
    ai_category_confidence: float | None = Field(default=None)  # Added Field
    # Status flags
    is_categorized: bool = False
    is_user_modified: bool = False

    # Invoice specific fields
    is_invoice: bool | None = Field(default=False)
    invoice_number: str | None = Field(default=None)
    invoice_due_date: date_type | None = Field(default=None)
    invoice_status: str | None = Field(default=None)
    related_invoice_id: str | None = Field(default=None)
    invoice_details: dict | None = Field(default=None)

    model_config = ConfigDict(from_attributes=True)


class BatchCategoryUpdateRequest(BaseModel):
    """Request model for batch category update."""

    transaction_ids: list[str] = Field(
        ..., description="List of transaction IDs to update"
    )
    category_id: int | None = Field(default=None, description="Category ID to assign")
    use_ai: bool = Field(
        default=False, description="Whether to use AI for categorization"
    )
    rag_corpus_id: str | None = Field(
        default=None, description="RAG corpus ID for AI categorization"
    )
    confidence_threshold: float | None = Field(
        default=0.7, description="Confidence threshold for AI categorization"
    )


class BatchCategoryUpdateResponse(BaseModel):
    """Response model for batch category update."""

    success: bool = Field(..., description="Whether the update was successful")
    message: str = Field(..., description="Status message")
    updated_count: int = Field(0, description="Number of transactions updated")
    failed_count: int = Field(
        0, description="Number of transactions that failed to update"
    )
    job_id: str | None = Field(None, description="Job ID for async processing")


# Backward compatibility aliases
TransactionCreate = CreateTransactionSchema

# Forward references are typically resolved by calling model_rebuild()
# on models that use them, often done at the end of the schemas package __init__.py.
# Calls to update_forward_refs() removed from here.
