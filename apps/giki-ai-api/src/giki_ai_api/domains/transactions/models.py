"""
Transaction domain models.

Data models for financial transactions without SQLAlchemy.
"""

import datetime
from decimal import Decimal
from typing import List, Optional, Union

from pydantic import BaseModel, ConfigDict, Field


class Upload(BaseModel):
    """
    Represents a file upload record.

    Tracks uploaded transaction files and their processing status.
    """

    model_config = ConfigDict(from_attributes=True)

    id: str = Field(..., description="Unique upload identifier (UUID)")
    file_name: Optional[str] = Field(None, description="Original file name")
    filename: Optional[str] = Field(None, description="Stored filename")
    content_type: Optional[str] = Field(None, description="MIME type of uploaded file")
    file_path: Optional[str] = Field(None, description="Storage path of file")
    size: Optional[int] = Field(None, description="File size in bytes")
    file_size: Optional[int] = Field(None, description="File size in bytes (duplicate)")
    file_type: Optional[str] = Field(None, description="Type of file (e.g., csv, xlsx)")

    # Status fields
    upload_status: str = Field(
        default="pending", description="Upload processing status"
    )
    processing_status: str = Field(
        default="pending", description="File processing status"
    )
    status: str = Field(default="uploaded", description="Overall status")

    # Processing metrics
    row_count: int = Field(default=0, description="Total rows in file")
    processed_count: int = Field(default=0, description="Rows processed successfully")
    error_count: int = Field(default=0, description="Rows with errors")
    error_message: Optional[str] = Field(None, description="Error details if any")

    # File content metadata
    headers: Optional[List[str]] = Field(None, description="Column headers from file")
    upload_type: Optional[str] = Field(
        None, description="Type of upload (onboarding/production)"
    )
    has_category_labels: Optional[bool] = Field(
        None, description="Whether file contains category labels"
    )
    baseline_accuracy: Optional[float] = Field(
        None, description="Accuracy score if applicable"
    )
    transaction_count: Optional[int] = Field(
        None, description="Number of transactions in file"
    )

    # Relationships
    user_id: Optional[int] = Field(None, description="ID of user who uploaded")
    tenant_id: int = Field(..., description="Tenant ID")

    # Timestamps
    created_at: datetime.datetime = Field(default_factory=lambda: datetime.datetime.now())
    updated_at: datetime.datetime = Field(default_factory=lambda: datetime.datetime.now())


class Transaction(BaseModel):
    """Represents a financial transaction."""

    model_config = ConfigDict(from_attributes=True)

    id: str  # UUID as string
    date: datetime.date
    description: str
    amount: Union[float, Decimal]

    # Account and transaction type fields
    account: Optional[str] = None
    transaction_type: Optional[str] = None
    vendor_name: Optional[str] = None

    # User-assigned category
    category_id: Optional[int] = None

    # Original category for historical data
    original_category: Optional[str] = None

    # AI-generated category
    ai_category: Optional[str] = None
    ai_confidence: Optional[float] = None
    ai_suggested_category: Optional[str] = None  # Changed from ai_suggested_category_id

    # Review and categorization status
    needs_review: Optional[bool] = None
    is_categorized: Optional[bool] = None

    # Tenant isolation
    tenant_id: int

    # Entity reference (optional)
    entity_id: Optional[int] = None

    # Upload tracking
    upload_id: Optional[str] = None

    # Timestamps
    created_at: datetime.datetime = Field(default_factory=lambda: datetime.datetime.now())
    updated_at: datetime.datetime = Field(default_factory=lambda: datetime.datetime.now())

    def get_is_categorized(self) -> bool:
        """Check if transaction has been categorized."""
        if self.is_categorized is not None:
            return self.is_categorized
        return bool(self.category_id or self.original_category)

    @property
    def has_ai_suggestion(self) -> bool:
        """Check if transaction has AI category suggestion."""
        return bool(self.ai_category)

    @property
    def confidence_level(self) -> str:
        """Get confidence level as string."""
        if not self.ai_confidence:
            return "none"
        if self.ai_confidence >= 0.8:
            return "high"
        elif self.ai_confidence >= 0.6:
            return "medium"
        return "low"


class TransactionCreate(BaseModel):
    """Schema for creating a transaction."""

    date: datetime.date
    description: str
    amount: Union[float, Decimal]
    account: Optional[str] = None
    transaction_type: Optional[str] = None
    vendor_name: Optional[str] = None
    category_id: Optional[int] = None
    original_category: Optional[str] = None
    tenant_id: int
    entity_id: Optional[int] = None
    upload_id: Optional[str] = None


class TransactionUpdate(BaseModel):
    """Schema for updating a transaction."""

    date: Optional[datetime.date] = None
    description: Optional[str] = None
    amount: Optional[Union[float, Decimal]] = None
    account: Optional[str] = None
    transaction_type: Optional[str] = None
    vendor_name: Optional[str] = None
    category_id: Optional[int] = None
    ai_category: Optional[str] = None
    ai_confidence: Optional[float] = None


class TransactionFilter(BaseModel):
    """Schema for filtering transactions."""

    start_date: Optional[datetime.date] = None
    end_date: Optional[datetime.date] = None
    category_id: Optional[int] = None
    upload_id: Optional[str] = None
    search_term: Optional[str] = None
    min_amount: Optional[Union[float, Decimal]] = None
    max_amount: Optional[Union[float, Decimal]] = None
    is_categorized: Optional[bool] = None
    has_ai_suggestion: Optional[bool] = None
