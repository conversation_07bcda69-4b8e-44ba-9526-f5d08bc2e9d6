"""
Debit Credit Agent - Intelligent Debit/Credit Inference
=======================================================

This agent provides intelligent debit/credit inference using accounting
principles for transaction processing from Excel/CSV files using the
ADK v1.3.0 pattern.

Key capabilities:
- Infer transaction direction from description patterns
- Apply double-entry accounting principles
- Handle regional banking terminology variations
- Validate debit/credit assignments
- Support multiple account types
- Provide confidence scores for inferences
"""

import json
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import asyncpg

logger = logging.getLogger(__name__)

# Real ADK imports
from google.adk.tools import FunctionTool
from vertexai.generative_models import GenerativeModel

from ...shared.ai.prompt_registry import get_prompt_registry

# Import StandardGikiAgent base class
from ...shared.ai.standard_giki_agent import StandardGikiAgent

# ===== DEBIT/CREDIT INFERENCE TOOLS =====


async def infer_debit_credit_tool_function(
    transaction_description: str,
    amount: float,
    account_type: Optional[str] = None,
    additional_context: Optional[Dict[str, Any]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Infer transaction direction using accounting principles.

    Analyzes transaction descriptions and amounts to determine whether
    a transaction is a debit or credit based on accounting rules and
    common banking patterns.

    Args:
        transaction_description: Description of the transaction
        amount: Transaction amount (can be positive or negative)
        account_type: Type of account (asset, liability, equity, revenue, expense)
        additional_context: Optional context like merchant info, category

    Returns:
        Dictionary with debit/credit inference and confidence
    """
    logger.info(
        f"Inferring debit/credit for transaction: {transaction_description[:50]}..."
    )

    try:
        from vertexai.generative_models import GenerativeModel

        # Initialize model for inference
        model = GenerativeModel("gemini-2.0-flash-001")

        # Create inference prompt using registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("transaction_debit_credit_inference")
        inference_prompt = prompt_version.format(
            transaction_description=transaction_description,
            amount=amount,
            account_type=account_type or "Not specified",
            additional_context=json.dumps(additional_context or {}, indent=2)
        )

        # Generate inference
        response = await model.generate_content_async(
            inference_prompt,
            generation_config=prompt_version.model_config,
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Inferred {result['transaction_type']} with confidence {result['confidence']}"
        )
        return result

    except Exception as e:
        logger.error(f"Debit/credit inference failed: {e}")
        raise Exception(f"AI inference failed: {e}")


# ===== BATCH PROCESSING TOOLS =====


async def batch_infer_debit_credit_tool_function(
    transactions: List[Dict[str, Any]],
    account_type: Optional[str] = None,
    inference_rules: Optional[Dict[str, Any]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Batch process multiple transactions for debit/credit inference.

    Efficiently processes multiple transactions using pattern recognition
    and consistent application of accounting rules across the batch.

    Args:
        transactions: List of transaction dictionaries
        account_type: Default account type for all transactions
        inference_rules: Optional custom inference rules

    Returns:
        Dictionary with batch inference results
    """
    logger.info(f"Batch inferring debit/credit for {len(transactions)} transactions")

    try:
        from vertexai.generative_models import GenerativeModel

        # Initialize model for batch inference
        model = GenerativeModel("gemini-2.0-flash-001")

        # Create batch inference prompt using registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("batch_debit_credit_inference")
        
        # Prepare data
        transactions_note = ""
        if len(transactions) > 20:
            transactions_note = f"... and {len(transactions) - 20} more transactions"
        
        batch_prompt = prompt_version.format(
            transactions_batch=json.dumps(transactions[:20], indent=2),  # Limit to 20 for prompt size
            additional_transactions_note=transactions_note,
            account_type=account_type or "Bank/Cash Account",
            inference_rules=json.dumps(inference_rules or {}, indent=2),
            total_transactions=len(transactions)
        )

        # Generate batch inference
        response = await model.generate_content_async(
            batch_prompt,
            generation_config=prompt_version.model_config,
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Batch inference complete: {result['summary']['debits_count']} debits, "
            f"{result['summary']['credits_count']} credits"
        )
        return result

    except Exception as e:
        logger.error(f"Batch debit/credit inference failed: {e}")
        raise Exception(f"AI batch inference failed: {e}")


# ===== REGIONAL VARIATION TOOLS =====


async def handle_regional_banking_terms_tool_function(
    transaction_data: Dict[str, Any],
    region: str = "US",
    banking_system: Optional[str] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Handle regional banking terminology variations.

    Adapts debit/credit inference to handle different banking conventions
    and terminology used in various regions and banking systems.

    Args:
        transaction_data: Transaction with regional terms
        region: Geographic region (US, UK, EU, APAC, etc.)
        banking_system: Specific banking system if known

    Returns:
        Dictionary with standardized interpretation
    """
    logger.info(f"Handling regional banking terms for {region}")

    try:
        from vertexai.generative_models import GenerativeModel

        # Initialize model for regional handling
        model = GenerativeModel("gemini-2.0-flash-001")

        # Create regional handling prompt using registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("regional_banking_terms")
        regional_prompt = prompt_version.format(
            transaction_data=json.dumps(transaction_data, indent=2),
            region=region,
            banking_system=banking_system or "Standard retail banking"
        )

        # Generate regional standardization
        response = await model.generate_content_async(
            regional_prompt,
            generation_config=prompt_version.model_config,
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Regional standardization complete for {region}: "
            f"{result['standardized_transaction']['type']}"
        )
        return result

    except Exception as e:
        logger.error(f"Regional banking term handling failed: {e}")
        raise Exception(f"AI regional handling failed: {e}")


# ===== VALIDATION TOOLS =====


async def validate_double_entry_tool_function(
    debit_entries: List[Dict[str, Any]],
    credit_entries: List[Dict[str, Any]],
    tolerance: float = 0.01,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Validate double-entry accounting balance.

    Ensures that debits equal credits according to fundamental
    accounting equation, with configurable tolerance for rounding.

    Args:
        debit_entries: List of debit transactions
        credit_entries: List of credit transactions
        tolerance: Acceptable difference for rounding errors

    Returns:
        Dictionary with validation results
    """
    logger.info("Validating double-entry accounting balance")

    try:
        # Calculate totals
        total_debits = sum(abs(entry.get("amount", 0)) for entry in debit_entries)
        total_credits = sum(abs(entry.get("amount", 0)) for entry in credit_entries)

        # Check balance
        difference = abs(total_debits - total_credits)
        is_balanced = difference <= tolerance

        # Analyze imbalance if any
        imbalance_analysis = {}
        if not is_balanced:
            imbalance_analysis = {
                "difference": round(difference, 2),
                "debit_excess": round(total_debits - total_credits, 2),
                "percentage_off": round(
                    (difference / max(total_debits, total_credits)) * 100, 2
                ),
                "likely_cause": "Missing entries"
                if difference > 10
                else "Rounding errors",
            }

        # Group by categories for analysis
        debit_categories = {}
        credit_categories = {}

        for entry in debit_entries:
            category = entry.get("category", "Uncategorized")
            if category not in debit_categories:
                debit_categories[category] = 0
            debit_categories[category] += abs(entry.get("amount", 0))

        for entry in credit_entries:
            category = entry.get("category", "Uncategorized")
            if category not in credit_categories:
                credit_categories[category] = 0
            credit_categories[category] += abs(entry.get("amount", 0))

        result = {
            "is_balanced": is_balanced,
            "totals": {
                "debits": round(total_debits, 2),
                "credits": round(total_credits, 2),
                "difference": round(difference, 2),
            },
            "entry_counts": {
                "debit_entries": len(debit_entries),
                "credit_entries": len(credit_entries),
            },
            "category_breakdown": {
                "debit_categories": {
                    k: round(v, 2) for k, v in debit_categories.items()
                },
                "credit_categories": {
                    k: round(v, 2) for k, v in credit_categories.items()
                },
            },
            "validation_details": {
                "tolerance_used": tolerance,
                "passed": is_balanced,
                "message": "Books are balanced"
                if is_balanced
                else "Books are not balanced",
            },
        }

        if not is_balanced:
            result["imbalance_analysis"] = imbalance_analysis

        logger.info(
            f"Double-entry validation: {'PASSED' if is_balanced else 'FAILED'} "
            f"(Difference: {difference})"
        )
        return result

    except Exception as e:
        logger.error(f"Double-entry validation failed: {e}")
        return {
            "is_balanced": False,
            "error": str(e),
            "validation_details": {
                "passed": False,
                "message": f"Validation error: {str(e)}",
            },
        }


# ===== PATTERN LEARNING TOOLS =====


async def learn_debit_credit_patterns_tool_function(
    historical_transactions: List[Dict[str, Any]],
    pattern_type: str = "merchant",
    **_kwargs,
) -> Dict[str, Any]:
    """
    Learn debit/credit patterns from historical data.

    Analyzes historical transactions to identify patterns that can
    improve future debit/credit inference accuracy.

    Args:
        historical_transactions: List of past transactions with known types
        pattern_type: Type of pattern to learn (merchant, description, amount)

    Returns:
        Dictionary with learned patterns
    """
    logger.info(
        f"Learning {pattern_type} patterns from {len(historical_transactions)} transactions"
    )

    try:
        from vertexai.generative_models import GenerativeModel

        # Initialize model for pattern learning
        model = GenerativeModel("gemini-2.0-flash-001")

        # Create pattern learning prompt
        learning_prompt = f"""
        You are an expert in financial pattern recognition.
        Analyze these historical transactions to identify debit/credit patterns.
        
        Historical Transactions (with known types):
        {json.dumps(historical_transactions[:50], indent=2)}  # Limit for prompt size
        
        Pattern Type to Learn: {pattern_type}
        
        PATTERN ANALYSIS TASKS:
        1. Identify consistent patterns in descriptions
        2. Find merchant-specific debit/credit tendencies
        3. Recognize amount-based patterns
        4. Detect time-based patterns (recurring transactions)
        5. Identify exception cases that break normal patterns
        
        PATTERN TYPES:
        - Merchant: Same merchant always has same transaction type
        - Description: Keywords that predict debit/credit
        - Amount: Amount ranges that correlate with type
        - Temporal: Time-based patterns (salary credits, bill debits)
        - Composite: Combinations of above
        
        Return JSON format:
        {{
            "patterns_learned": [
                {{
                    "pattern_id": "merchant_starbucks",
                    "pattern_type": "merchant",
                    "pattern_rule": "STARBUCKS transactions are always debits",
                    "confidence": 0.99,
                    "occurrence_count": 45,
                    "exceptions": 0
                }},
                {{
                    "pattern_id": "salary_credit",
                    "pattern_type": "description",
                    "pattern_rule": "Descriptions with 'SALARY' or 'PAYROLL' are credits",
                    "confidence": 1.0,
                    "occurrence_count": 12,
                    "exceptions": 0
                }}
            ],
            "pattern_summary": {{
                "total_patterns": 15,
                "high_confidence_patterns": 12,
                "coverage_percentage": 85.5
            }},
            "recommendations": [
                "Use merchant patterns for highest accuracy",
                "Salary/income patterns are 100% reliable"
            ]
        }}
        
        JSON:"""

        # Generate pattern analysis
        response = await model.generate_content_async(
            learning_prompt,
            generation_config={
                "temperature": 0.2,
                "max_output_tokens": 2000,
                "top_p": 0.9,
            },
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Pattern learning complete: {result['pattern_summary']['total_patterns']} patterns identified"
        )
        return result

    except Exception as e:
        logger.error(f"Pattern learning failed: {e}")
        raise Exception(f"AI pattern learning failed: {e}")


# ===== CONFIDENCE SCORING TOOLS =====


async def calculate_inference_confidence_tool_function(
    transaction: Dict[str, Any],
    inference_result: Dict[str, Any],
    supporting_patterns: Optional[List[Dict[str, Any]]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Calculate confidence score for debit/credit inference.

    Provides detailed confidence scoring based on multiple factors
    including pattern matches, keyword presence, and historical accuracy.

    Args:
        transaction: Original transaction data
        inference_result: Result from inference tool
        supporting_patterns: Optional patterns that support inference

    Returns:
        Dictionary with detailed confidence scoring
    """
    logger.info("Calculating inference confidence score")

    try:
        # Base confidence from inference
        base_confidence = inference_result.get("confidence", 0.5)

        # Confidence factors
        factors = {
            "base_inference": base_confidence,
            "keyword_match": 0.0,
            "pattern_support": 0.0,
            "amount_consistency": 0.0,
            "description_clarity": 0.0,
        }

        # Check keyword matches
        description = transaction.get("description", "").upper()
        strong_debit_keywords = ["PAYMENT", "PURCHASE", "WITHDRAWAL", "FEE", "CHARGE"]
        strong_credit_keywords = ["DEPOSIT", "SALARY", "REFUND", "INTEREST", "INCOME"]

        if any(keyword in description for keyword in strong_debit_keywords):
            if inference_result.get("transaction_type") == "debit":
                factors["keyword_match"] = 0.9
        elif any(keyword in description for keyword in strong_credit_keywords):
            if inference_result.get("transaction_type") == "credit":
                factors["keyword_match"] = 0.9

        # Check pattern support
        if supporting_patterns:
            matching_patterns = sum(
                1 for p in supporting_patterns if p.get("confidence", 0) > 0.8
            )
            factors["pattern_support"] = min(matching_patterns * 0.2, 0.9)

        # Check amount consistency
        amount = transaction.get("amount", 0)
        if (amount < 0 and inference_result.get("transaction_type") == "debit") or (
            amount > 0 and inference_result.get("transaction_type") == "credit"
        ):
            factors["amount_consistency"] = 0.8

        # Check description clarity
        if len(description) > 10 and not any(
            c in description for c in ["?", "UNKNOWN", "MISC"]
        ):
            factors["description_clarity"] = 0.7

        # Calculate weighted confidence
        weights = {
            "base_inference": 0.4,
            "keyword_match": 0.25,
            "pattern_support": 0.2,
            "amount_consistency": 0.1,
            "description_clarity": 0.05,
        }

        final_confidence = sum(factors[k] * weights[k] for k in factors)

        # Determine confidence level
        if final_confidence >= 0.9:
            confidence_level = "very_high"
        elif final_confidence >= 0.7:
            confidence_level = "high"
        elif final_confidence >= 0.5:
            confidence_level = "medium"
        else:
            confidence_level = "low"

        result = {
            "final_confidence": round(final_confidence, 3),
            "confidence_level": confidence_level,
            "confidence_factors": {k: round(v, 3) for k, v in factors.items()},
            "factor_weights": weights,
            "recommendation": "Accept" if final_confidence >= 0.7 else "Manual Review",
            "explanation": f"Inference confidence is {confidence_level} based on "
            f"{'strong keyword match' if factors['keyword_match'] > 0.8 else 'inference analysis'}",
        }

        logger.info(
            f"Confidence calculation complete: {final_confidence} ({confidence_level})"
        )
        return result

    except Exception as e:
        logger.error(f"Confidence calculation failed: {e}")
        return {
            "final_confidence": 0.0,
            "confidence_level": "error",
            "error": str(e),
            "recommendation": "Manual Review",
        }


# Create FunctionTool instances
infer_debit_credit_tool = FunctionTool(func=infer_debit_credit_tool_function)
batch_infer_debit_credit_tool = FunctionTool(
    func=batch_infer_debit_credit_tool_function
)
handle_regional_banking_terms_tool = FunctionTool(
    func=handle_regional_banking_terms_tool_function
)
validate_double_entry_tool = FunctionTool(func=validate_double_entry_tool_function)
learn_debit_credit_patterns_tool = FunctionTool(
    func=learn_debit_credit_patterns_tool_function
)
calculate_inference_confidence_tool = FunctionTool(
    func=calculate_inference_confidence_tool_function
)


@dataclass
class DebitCreditAgentConfig:
    """Configuration for DebitCreditAgent."""

    model_name: str = "gemini-2.0-flash-001"
    project: str | None = None
    location: str | None = None
    tools: List[FunctionTool] | None = None
    enable_code_execution: bool = False


class DebitCreditAgent(StandardGikiAgent):
    """
    Specialist agent for intelligent debit/credit inference.

    This agent handles:
    - Transaction direction inference from descriptions
    - Double-entry accounting principle application
    - Regional banking terminology standardization
    - Pattern learning from historical data
    - Confidence scoring for inferences
    - Batch processing for efficiency
    """

    def __init__(
        self,
        config: DebitCreditAgentConfig | None = None,
        db: asyncpg.Connection | None = None,
        **kwargs,
    ):
        """Initialize the DebitCreditAgent using StandardGikiAgent inheritance."""
        if config is None:
            config = DebitCreditAgentConfig(
                model_name=kwargs.get("model_name", "gemini-2.0-flash-001"),
                project=kwargs.get("project"),
                location=kwargs.get("location"),
            )

        # Store config and extract values
        self._config = config
        model_name = config.model_name
        project = config.project
        location = config.location

        # Set up debit/credit inference tools
        custom_tools = [
            infer_debit_credit_tool,
            batch_infer_debit_credit_tool,
            handle_regional_banking_terms_tool,
            validate_double_entry_tool,
            learn_debit_credit_patterns_tool,
            calculate_inference_confidence_tool,
        ]
        if config.tools:
            custom_tools.extend(config.tools)

        # Initialize StandardGikiAgent with debit/credit-specific configuration
        super().__init__(
            name="debit_credit_agent",
            description="Intelligent debit/credit inference using accounting principles",
            custom_tools=custom_tools,
            enable_interactive_tools=False,  # Inference is automated
            enable_code_execution=config.enable_code_execution,
            model_name=model_name,
            instruction="""You are an expert in double-entry accounting and banking systems.
            Infer debit/credit transaction direction using accounting principles.
            Handle regional banking terminology and cultural variations.
            Apply consistent rules across batch processing.
            Provide confidence scores for all inferences.
            Ensure double-entry balance is maintained.""",
            project_id=project,
            location=location or "global",
            **kwargs,
        )

        # Store attributes after initialization
        self._model_name = model_name
        self._project = project
        self._location = location
        self._db = db

        # Initialize accounting-specific components
        self._vertex_model = GenerativeModel(model_name=model_name)

        logger.info(f"DebitCreditAgent initialized with model: {model_name}")

    async def infer_debit_credit(
        self,
        transaction_description: str,
        amount: float,
        account_type: Optional[str] = None,
        additional_context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Infer transaction direction.

        Args:
            transaction_description: Description of the transaction
            amount: Transaction amount
            account_type: Type of account
            additional_context: Optional context

        Returns:
            Dictionary with inference result
        """
        return await infer_debit_credit_tool_function(
            transaction_description=transaction_description,
            amount=amount,
            account_type=account_type,
            additional_context=additional_context,
        )

    async def batch_infer_debit_credit(
        self,
        transactions: List[Dict[str, Any]],
        account_type: Optional[str] = None,
        inference_rules: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Batch process transactions.

        Args:
            transactions: List of transactions
            account_type: Default account type
            inference_rules: Custom rules

        Returns:
            Dictionary with batch results
        """
        return await batch_infer_debit_credit_tool_function(
            transactions=transactions,
            account_type=account_type,
            inference_rules=inference_rules,
        )

    async def handle_regional_banking_terms(
        self,
        transaction_data: Dict[str, Any],
        region: str = "US",
        banking_system: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Handle regional variations.

        Args:
            transaction_data: Transaction with regional terms
            region: Geographic region
            banking_system: Banking system

        Returns:
            Dictionary with standardized interpretation
        """
        return await handle_regional_banking_terms_tool_function(
            transaction_data=transaction_data,
            region=region,
            banking_system=banking_system,
        )

    async def validate_double_entry(
        self,
        debit_entries: List[Dict[str, Any]],
        credit_entries: List[Dict[str, Any]],
        tolerance: float = 0.01,
    ) -> Dict[str, Any]:
        """
        Validate accounting balance.

        Args:
            debit_entries: List of debits
            credit_entries: List of credits
            tolerance: Acceptable difference

        Returns:
            Dictionary with validation results
        """
        return await validate_double_entry_tool_function(
            debit_entries=debit_entries,
            credit_entries=credit_entries,
            tolerance=tolerance,
        )

    async def learn_debit_credit_patterns(
        self,
        historical_transactions: List[Dict[str, Any]],
        pattern_type: str = "merchant",
    ) -> Dict[str, Any]:
        """
        Learn patterns from history.

        Args:
            historical_transactions: Past transactions
            pattern_type: Type of pattern

        Returns:
            Dictionary with learned patterns
        """
        return await learn_debit_credit_patterns_tool_function(
            historical_transactions=historical_transactions,
            pattern_type=pattern_type,
        )

    async def calculate_inference_confidence(
        self,
        transaction: Dict[str, Any],
        inference_result: Dict[str, Any],
        supporting_patterns: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Calculate confidence score.

        Args:
            transaction: Original transaction
            inference_result: Inference result
            supporting_patterns: Supporting patterns

        Returns:
            Dictionary with confidence scoring
        """
        return await calculate_inference_confidence_tool_function(
            transaction=transaction,
            inference_result=inference_result,
            supporting_patterns=supporting_patterns,
        )
