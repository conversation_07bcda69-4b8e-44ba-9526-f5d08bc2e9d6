"""
Dashboard domain ADK tools.

This module provides ADK tool functions for dashboard functionality that can be used
by agents to retrieve metrics, analytics, and insights for dashboard displays.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, Optional

import asyncpg

from .service import get_dashboard_service

logger = logging.getLogger(__name__)


async def get_dashboard_metrics_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to get comprehensive dashboard metrics.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        start_date: Optional start date (YYYY-MM-DD format)
        end_date: Optional end date (YYYY-MM-DD format)

    Returns:
        Comprehensive dashboard metrics including income, expenses, and categorization rates
    """
    try:
        service = get_dashboard_service(db)

        # Parse date strings if provided
        parsed_start_date = None
        parsed_end_date = None

        if start_date:
            parsed_start_date = datetime.fromisoformat(start_date).date()
        if end_date:
            parsed_end_date = datetime.fromisoformat(end_date).date()

        metrics = await service.get_dashboard_metrics(
            tenant_id=tenant_id,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
        )

        return {
            "success": True,
            "tenant_id": tenant_id,
            "metrics": metrics,
            "summary": {
                "financial_health": "positive"
                if metrics["net_income"] > 0
                else "negative",
                "categorization_status": "good"
                if metrics["categorization_rate"] > 80
                else "needs_improvement",
                "activity_level": "high"
                if metrics["total_transactions"] > 100
                else "moderate"
                if metrics["total_transactions"] > 20
                else "low",
            },
        }

    except Exception as e:
        logger.error(f"Failed to get dashboard metrics for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
        }


async def get_recent_transactions_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    limit: int = 10,
    uncategorized_only: bool = False,
) -> Dict[str, Any]:
    """
    ADK tool function to get recent transactions for dashboard display.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        limit: Maximum number of transactions to return
        uncategorized_only: Whether to return only uncategorized transactions

    Returns:
        Recent transactions with categorization status
    """
    try:
        service = get_dashboard_service(db)

        transactions = await service.get_recent_transactions(
            tenant_id=tenant_id,
            limit=limit,
            include_uncategorized_only=uncategorized_only,
        )

        return {
            "success": True,
            "tenant_id": tenant_id,
            "transactions": transactions,
            "filter_applied": "uncategorized_only" if uncategorized_only else "all",
        }

    except Exception as e:
        logger.error(f"Failed to get recent transactions for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
            "transactions": {"items": [], "total": 0},
        }


async def get_category_breakdown_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    breakdown_type: str = "expenses",
    limit: int = 10,
) -> Dict[str, Any]:
    """
    ADK tool function to get spending/income breakdown by category.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        start_date: Optional start date (YYYY-MM-DD format)
        end_date: Optional end date (YYYY-MM-DD format)
        breakdown_type: Type of breakdown ("expenses", "income", "both")
        limit: Maximum number of categories to return

    Returns:
        Category breakdown with amounts and percentages
    """
    try:
        service = get_dashboard_service(db)

        # Parse date strings if provided
        parsed_start_date = None
        parsed_end_date = None

        if start_date:
            parsed_start_date = datetime.fromisoformat(start_date).date()
        if end_date:
            parsed_end_date = datetime.fromisoformat(end_date).date()

        breakdown = await service.get_category_breakdown(
            tenant_id=tenant_id,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            breakdown_type=breakdown_type,
            limit=limit,
        )

        return {
            "success": True,
            "tenant_id": tenant_id,
            "breakdown": breakdown,
            "insights": {
                "top_category": breakdown["items"][0]["category"]
                if breakdown["items"]
                else None,
                "top_category_amount": breakdown["items"][0]["amount"]
                if breakdown["items"]
                else 0,
                "category_diversity": len(breakdown["items"]),
                "average_per_category": breakdown["total_amount"]
                / max(len(breakdown["items"]), 1),
            },
        }

    except Exception as e:
        logger.error(f"Failed to get category breakdown for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
            "breakdown": {"items": [], "total_amount": 0},
        }


async def get_monthly_trends_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    months: int = 6,
) -> Dict[str, Any]:
    """
    ADK tool function to get monthly income/expense trends.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        months: Number of months to analyze

    Returns:
        Monthly trends with income, expenses, and growth analysis
    """
    try:
        service = get_dashboard_service(db)

        trends = await service.get_monthly_trends(
            tenant_id=tenant_id,
            months=months,
        )

        # Calculate growth trends
        trend_analysis = {"growth_direction": "stable", "growth_rate": 0.0}
        if len(trends["trends"]) >= 2:
            recent_net = trends["trends"][0]["net_income"]
            previous_net = trends["trends"][1]["net_income"]

            if previous_net != 0:
                growth_rate = ((recent_net - previous_net) / abs(previous_net)) * 100
                trend_analysis["growth_rate"] = round(growth_rate, 2)
                trend_analysis["growth_direction"] = (
                    "improving"
                    if growth_rate > 5
                    else "declining"
                    if growth_rate < -5
                    else "stable"
                )

        return {
            "success": True,
            "tenant_id": tenant_id,
            "trends": trends,
            "analysis": trend_analysis,
            "summary": {
                "months_analyzed": trends["months_analyzed"],
                "average_monthly_income": sum(t["income"] for t in trends["trends"])
                / max(len(trends["trends"]), 1),
                "average_monthly_expenses": sum(t["expenses"] for t in trends["trends"])
                / max(len(trends["trends"]), 1),
            },
        }

    except Exception as e:
        logger.error(f"Failed to get monthly trends for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
            "trends": {"trends": [], "months_analyzed": 0},
        }


async def get_entity_insights_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 10,
) -> Dict[str, Any]:
    """
    ADK tool function to get insights about entities (merchants/vendors).

    Args:
        db: Database connection
        tenant_id: Tenant ID
        start_date: Optional start date (YYYY-MM-DD format)
        end_date: Optional end date (YYYY-MM-DD format)
        limit: Maximum number of entities to return

    Returns:
        Entity insights with spending patterns and frequency analysis
    """
    try:
        service = get_dashboard_service(db)

        # Parse date strings if provided
        parsed_start_date = None
        parsed_end_date = None

        if start_date:
            parsed_start_date = datetime.fromisoformat(start_date).date()
        if end_date:
            parsed_end_date = datetime.fromisoformat(end_date).date()

        insights = await service.get_entity_insights(
            tenant_id=tenant_id,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            limit=limit,
        )

        return {
            "success": True,
            "tenant_id": tenant_id,
            "entity_insights": insights,
            "patterns": {
                "most_frequent_vendor": insights["entities"][0]["entity_name"]
                if insights["entities"]
                else None,
                "highest_spending_vendor": insights["entities"][0]["entity_name"]
                if insights["entities"]
                else None,
                "vendor_diversity": len(insights["entities"]),
                "average_transaction_per_vendor": sum(
                    e["transaction_count"] for e in insights["entities"]
                )
                / max(len(insights["entities"]), 1),
            },
        }

    except Exception as e:
        logger.error(f"Failed to get entity insights for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
            "entity_insights": {"entities": [], "total_entities": 0},
        }


async def get_upload_summary_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    limit: int = 5,
) -> Dict[str, Any]:
    """
    ADK tool function to get summary of recent file uploads.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        limit: Maximum number of uploads to return

    Returns:
        Upload summary with processing status and transaction counts
    """
    try:
        service = get_dashboard_service(db)

        summary = await service.get_upload_summary(
            tenant_id=tenant_id,
            limit=limit,
        )

        return {
            "success": True,
            "tenant_id": tenant_id,
            "upload_summary": summary,
            "status_overview": {
                "recent_uploads": summary["total_uploads"],
                "total_transactions_uploaded": sum(
                    u["transaction_count"] for u in summary["uploads"]
                ),
                "upload_activity": "active"
                if summary["total_uploads"] > 0
                else "inactive",
            },
        }

    except Exception as e:
        logger.error(f"Failed to get upload summary for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
            "upload_summary": {"uploads": [], "total_uploads": 0},
        }


async def get_financial_health_score_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    months: int = 3,
) -> Dict[str, Any]:
    """
    ADK tool function to calculate financial health score based on multiple factors.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        months: Number of months to analyze for health score

    Returns:
        Financial health score with detailed breakdown
    """
    try:
        service = get_dashboard_service(db)

        # Get recent metrics for health calculation
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=months * 30)

        metrics = await service.get_dashboard_metrics(
            tenant_id=tenant_id,
            start_date=start_date,
            end_date=end_date,
        )

        trends = await service.get_monthly_trends(
            tenant_id=tenant_id,
            months=months,
        )

        # Calculate health score components
        income_stability = 100 if metrics["total_income"] > 0 else 0
        expense_control = max(
            0, 100 - (metrics["total_expenses"] / max(metrics["total_income"], 1) * 100)
        )
        categorization_health = metrics["categorization_rate"]

        # Growth trend component
        growth_health = 50  # Default neutral
        if len(trends["trends"]) >= 2:
            recent_net = trends["trends"][0]["net_income"]
            previous_net = trends["trends"][1]["net_income"]
            if previous_net != 0:
                growth_rate = ((recent_net - previous_net) / abs(previous_net)) * 100
                growth_health = min(100, max(0, 50 + growth_rate))

        # Calculate overall health score (weighted average)
        health_score = (
            income_stability * 0.3
            + expense_control * 0.3
            + categorization_health * 0.2
            + growth_health * 0.2
        )

        # Determine health status
        if health_score >= 80:
            health_status = "excellent"
        elif health_score >= 60:
            health_status = "good"
        elif health_score >= 40:
            health_status = "fair"
        else:
            health_status = "poor"

        return {
            "success": True,
            "tenant_id": tenant_id,
            "health_score": round(health_score, 1),
            "health_status": health_status,
            "components": {
                "income_stability": round(income_stability, 1),
                "expense_control": round(expense_control, 1),
                "categorization_health": round(categorization_health, 1),
                "growth_health": round(growth_health, 1),
            },
            "recommendations": [
                "Increase income sources" if income_stability < 70 else None,
                "Review and optimize expenses" if expense_control < 50 else None,
                "Improve transaction categorization"
                if categorization_health < 80
                else None,
                "Focus on growth strategies" if growth_health < 50 else None,
            ],
            "analysis_period": {
                "months": months,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
        }

    except Exception as e:
        logger.error(
            f"Failed to calculate financial health score for tenant {tenant_id}: {e}"
        )
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
            "health_score": 0,
            "health_status": "unknown",
        }
