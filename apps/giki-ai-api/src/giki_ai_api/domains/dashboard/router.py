"""
Dashboard API router.

Provides aggregated data for the dashboard view.
"""

from datetime import datetime, timedelta
from typing import Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, Query

from ...core.dependencies import get_current_tenant_id, get_db_session
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user

router = APIRouter(prefix="/api/v1/dashboard", tags=["Dashboard"])


@router.get("/metrics")
async def get_dashboard_metrics(
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
):
    """Get dashboard metrics for the current tenant."""

    # Default date range if not provided
    if not end_date:
        end_date = datetime.now().date()
    else:
        end_date = datetime.fromisoformat(end_date).date()

    if not start_date:
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.fromisoformat(start_date).date()

    # Get all metrics in a single query for efficiency
    query = """
        SELECT 
            COUNT(*) as total_count,
            COALESCE(SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END), 0) as total_income,
            COALESCE(SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END), 0) as total_expenses,
            COUNT(CASE WHEN original_category IS NOT NULL OR ai_category IS NOT NULL THEN 1 END) as categorized_count
        FROM transactions
        WHERE tenant_id = $1
        AND date >= $2
        AND date <= $3
    """

    row = await conn.fetchrow(query, tenant_id, start_date, end_date)

    total_count = row["total_count"] or 0
    total_income = float(row["total_income"] or 0)
    total_expenses = float(row["total_expenses"] or 0)
    categorized_count = row["categorized_count"] or 0

    return {
        "total_transactions": total_count,
        "total_income": total_income,
        "total_expenses": total_expenses,
        "net_income": total_income - total_expenses,
        "categorized_transactions": categorized_count,
        "uncategorized_transactions": total_count - categorized_count,
        "categorization_rate": (categorized_count / total_count * 100)
        if total_count > 0
        else 0,
        "date_range": {
            "start": start_date.isoformat() if start_date else None,
            "end": end_date.isoformat() if end_date else None,
        },
    }


@router.get("/recent-transactions")
async def get_recent_transactions(
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    limit: int = Query(10, ge=1, le=50),
):
    """Get recent transactions for the dashboard."""

    query = """
        SELECT id, date, description, amount, original_category, ai_category
        FROM transactions
        WHERE tenant_id = $1
        ORDER BY date DESC, id DESC
        LIMIT $2
    """

    rows = await conn.fetch(query, tenant_id, limit)

    return {
        "items": [
            {
                "id": row["id"],
                "date": row["date"].isoformat() if row["date"] else None,
                "description": row["description"],
                "amount": float(row["amount"]),
                "category": row["original_category"] or row["ai_category"],
                "ai_suggested_category": row["ai_category"],
                "is_categorized": bool(row["original_category"] or row["ai_category"]),
            }
            for row in rows
        ],
        "total": len(rows),
    }


@router.get("/category-breakdown")
async def get_category_breakdown(
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
):
    """Get spending breakdown by category."""

    # Default date range if not provided
    if not end_date:
        end_date = datetime.now().date()
    else:
        end_date = datetime.fromisoformat(end_date).date()

    if not start_date:
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.fromisoformat(start_date).date()

    # Get category breakdown using raw SQL
    query = """
        SELECT 
            COALESCE(original_category, ai_category) as category,
            SUM(ABS(amount)) as total_amount,
            COUNT(*) as transaction_count
        FROM transactions
        WHERE tenant_id = $1
        AND (original_category IS NOT NULL OR ai_category IS NOT NULL)
        AND amount < 0  -- Only expenses
        AND date >= $2
        AND date <= $3
        GROUP BY COALESCE(original_category, ai_category)
        ORDER BY SUM(ABS(amount)) DESC
        LIMIT 10
    """

    rows = await conn.fetch(query, tenant_id, start_date, end_date)

    total_spending = sum(float(row["total_amount"]) for row in rows)

    return {
        "items": [
            {
                "category": row["category"],
                "amount": float(row["total_amount"]),
                "transaction_count": row["transaction_count"],
                "percentage": (float(row["total_amount"]) / total_spending * 100)
                if total_spending > 0
                else 0,
            }
            for row in rows
        ],
        "total_categories": len(rows),
        "total_spending": total_spending,
    }
