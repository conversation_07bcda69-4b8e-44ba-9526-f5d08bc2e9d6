"""
Reports Domain Models
====================

Pydantic models for reports domain.
"""

from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class CustomReport(BaseModel):
    """Custom report configuration model."""

    id: int = Field(..., description="Report ID")
    tenant_id: int = Field(..., description="Tenant ID")
    user_id: int = Field(..., description="User ID who created the report")
    name: str = Field(..., max_length=255, description="Report name")
    description: Optional[str] = Field(None, description="Report description")
    report_type: str = Field(..., max_length=100, description="Type of report")
    configuration: Dict[str, Any] = Field(
        default_factory=dict, description="Report configuration JSON"
    )
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
