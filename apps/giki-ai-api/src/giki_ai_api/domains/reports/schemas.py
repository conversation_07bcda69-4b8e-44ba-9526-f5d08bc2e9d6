from datetime import date as date_type
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ReportDateRangeQueryFilters(BaseModel):
    """Query parameters for filtering reports by date range."""

    start_date: date_type | None = Field(
        default=None, description="Filter by start date (YYYY-MM-DD)"
    )
    end_date: date_type | None = Field(
        default=None, description="Filter by end date (YYYY-MM-DD)"
    )


# Schemas for PRF-06: Reporting Data Aggregation (Spending by Category)
class SpendingByCategoryItem(BaseModel):
    """Represents aggregated spending for a single category."""

    category_path: str = Field(description="The full path of the category.")
    total_amount: float = Field(description="Total amount spent in this category.")
    transaction_count: int = Field(
        description="Number of transactions in this category."
    )

    model_config = {"from_attributes": True}


class SpendingByCategoryResponse(BaseModel):
    """Response model for spending by category report."""

    items: list[SpendingByCategoryItem]
    total_records: int = Field(
        description="Total number of unique categories with spending."
    )


# Schemas for PRF-07: Entity Data Retrieval (Spending by Entity/Vendor)
class EntitySpendingItem(BaseModel):
    """Represents aggregated spending for a single entity/vendor."""

    entity_name: str = Field(
        description="The name of the entity/vendor (derived from transaction description)."
    )
    total_amount: float = Field(description="Total amount spent with this entity.")
    transaction_count: int = Field(
        description="Number of transactions with this entity."
    )

    model_config = {"from_attributes": True}


class EntitySpendingResponse(BaseModel):
    """Response model for spending by entity/vendor report."""

    items: list[EntitySpendingItem]
    total_records: int = Field(
        description="Total number of unique entities with spending."
    )


# Schemas for Income vs. Expense Report
class IncomeExpenseSummaryResponse(BaseModel):
    """Response model for the Income vs. Expense summary report."""

    total_income: float = Field(description="Total income for the period.")
    total_expenses: float = Field(description="Total expenses for the period.")
    net_income_loss: float = Field(
        description="Net income or loss (Income - Expenses)."
    )

    model_config = {"from_attributes": True}


# Schemas for Monthly Trends Report
class MonthlyTrendItem(BaseModel):
    """Represents income and expense data for a single month."""

    month: str = Field(description="Month in YYYY-MM format")
    total_income: float = Field(description="Total income for the month")
    total_expenses: float = Field(description="Total expenses for the month")
    net_amount: float = Field(
        description="Net income/loss for the month (income - expenses)"
    )
    transaction_count: int = Field(description="Number of transactions in the month")

    model_config = {"from_attributes": True}


class MonthlyTrendsResponse(BaseModel):
    """Response model for monthly trends report."""

    items: list[MonthlyTrendItem]
    total_months: int = Field(description="Total number of months with data")

    model_config = {"from_attributes": True}


class ReportRequest(BaseModel):
    """Request model for generating reports."""

    report_type: str = Field(..., description="Type of report to generate")
    date_range: Optional[Dict[str, Any]] = Field(
        None, description="Date range for the report"
    )
    tenant_id: int = Field(..., description="Tenant identifier")
    group_by: Optional[str] = Field(None, description="Grouping option")
    granularity: Optional[str] = Field(None, description="Time granularity")
    export_format: Optional[str] = Field(None, description="Export format")
    sharing: Optional[Dict[str, Any]] = Field(None, description="Sharing configuration")
    schedule: Optional[Dict[str, Any]] = Field(
        None, description="Schedule configuration"
    )
    analytics: Optional[Dict[str, Any]] = Field(
        None, description="Analytics configuration"
    )


class CustomReportConfig(BaseModel):
    """Configuration for custom reports."""

    name: str = Field(..., description="Report name")
    description: Optional[str] = Field(None, description="Report description")
    data_sources: Optional[List[str]] = Field(None, description="Data sources")
    filters: Optional[Dict[str, Any]] = Field(None, description="Report filters")
    grouping: Optional[List[str]] = Field(None, description="Grouping options")
    aggregations: Optional[List[str]] = Field(None, description="Aggregation functions")
    visualizations: Optional[List[str]] = Field(None, description="Visualization types")
    customization: Optional[Dict[str, Any]] = Field(
        None, description="Customization options"
    )
    interactive_features: Optional[Dict[str, Any]] = Field(
        None, description="Interactive features"
    )
    accessibility: Optional[Dict[str, Any]] = Field(
        None, description="Accessibility options"
    )
    tenant_id: int = Field(..., description="Tenant identifier")


# Custom Report API Schemas
class CustomReportGenerateRequest(BaseModel):
    """Request model for generating a custom report."""

    report_type: str = Field(..., description="Type of report to generate")
    filters: Dict[str, Any] = Field(default_factory=dict, description="Report filters")
    grouping: Optional[List[str]] = Field(None, description="Fields to group by")
    aggregations: Optional[List[str]] = Field(
        None, description="Aggregation functions to apply"
    )
    date_range: Optional[Dict[str, str]] = Field(
        None, description="Date range for the report"
    )
    include_raw_data: bool = Field(
        default=False, description="Include raw transaction data"
    )


class CustomReportSaveRequest(BaseModel):
    """Request model for saving a custom report configuration."""

    name: str = Field(..., description="Report name", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Report description")
    report_type: str = Field(..., description="Type of report")
    configuration: Dict[str, Any] = Field(..., description="Report configuration")


class CustomReportResponse(BaseModel):
    """Response model for a custom report."""

    id: int = Field(..., description="Report ID")
    name: str = Field(..., description="Report name")
    description: Optional[str] = Field(None, description="Report description")
    report_type: str = Field(..., description="Type of report")
    configuration: Dict[str, Any] = Field(..., description="Report configuration")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")

    model_config = {"from_attributes": True}


class CustomReportListResponse(BaseModel):
    """Response model for listing custom reports."""

    reports: List[CustomReportResponse] = Field(
        ..., description="List of custom reports"
    )
    total: int = Field(..., description="Total number of reports")


class CustomReportDataResponse(BaseModel):
    """Response model for custom report data."""

    report_id: Optional[int] = Field(None, description="Report ID if saved")
    report_type: str = Field(..., description="Type of report generated")
    filters: Dict[str, Any] = Field(..., description="Applied filters")
    date_range: Optional[Dict[str, str]] = Field(None, description="Applied date range")
    data: Dict[str, Any] = Field(..., description="Report data")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Report metadata"
    )
    generated_at: str = Field(..., description="Generation timestamp")
