"""
Report domain ADK tools.

This module provides ADK tool functions for report generation that can be used
by agents to create financial reports and analytics.
"""

import logging
from datetime import date, timedelta
from typing import Any, Dict, List, Optional

import asyncpg

from ...shared.exceptions import ServiceError

logger = logging.getLogger(__name__)


async def generate_spending_by_category_report_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    limit: int = 20,
) -> Dict[str, Any]:
    """
    ADK tool function to generate spending by category report.

    Args:
        db: Database session
        tenant_id: Tenant ID
        start_date: Start date filter
        end_date: End date filter
        limit: Maximum number of categories to return

    Returns:
        Dictionary containing category spending breakdown
    """
    try:
        # Build SQL query for spending by category
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                category_path,
                SUM(ABS(amount)) as total_amount,
                COUNT(id) as transaction_count
            FROM transactions 
            WHERE tenant_id = $1 
              AND amount < 0 
              AND category_path IS NOT NULL
              {date_filter}
            GROUP BY category_path
            ORDER BY SUM(ABS(amount)) DESC
            LIMIT {limit}
        """

        rows = await db.fetch(sql, *params)

        # Format results
        categories = []
        total_spending = 0

        for row in rows:
            amount = float(row["total_amount"]) if row["total_amount"] else 0
            categories.append(
                {
                    "category": row["category_path"],
                    "amount": amount,
                    "transaction_count": row["transaction_count"],
                    "percentage": 0,  # Will calculate after totaling
                }
            )
            total_spending += amount

        # Calculate percentages
        if total_spending > 0:
            for cat in categories:
                cat["percentage"] = round((cat["amount"] / total_spending) * 100, 2)

        return {
            "tenant_id": tenant_id,
            "date_range": {
                "start": start_date.isoformat() if start_date else None,
                "end": end_date.isoformat() if end_date else None,
            },
            "total_spending": total_spending,
            "category_count": len(categories),
            "categories": categories,
        }

    except Exception as e:
        logger.error(f"Error generating category spending report: {e}")
        raise ServiceError(
            message=f"Failed to generate category spending report: {str(e)}",
            error_code="REPORT_GENERATION_ERROR",
            tenant_id=tenant_id,
        )


async def generate_spending_by_entity_report_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    limit: int = 20,
) -> Dict[str, Any]:
    """
    ADK tool function to generate spending by entity/vendor report.

    Args:
        db: Database session
        tenant_id: Tenant ID
        start_date: Start date filter
        end_date: End date filter
        limit: Maximum number of entities to return

    Returns:
        Dictionary containing entity spending breakdown
    """
    try:
        # Build SQL query for spending by entity with LEFT JOIN
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND t.date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND t.date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                COALESCE(e.name, t.description) as entity_name,
                SUM(ABS(t.amount)) as total_amount,
                COUNT(t.id) as transaction_count
            FROM transactions t
            LEFT JOIN entities e ON t.entity_id = e.id
            WHERE t.tenant_id = $1 
              AND t.amount < 0
              {date_filter}
            GROUP BY COALESCE(e.name, t.description)
            ORDER BY SUM(ABS(t.amount)) DESC
            LIMIT {limit}
        """

        rows = await db.fetch(sql, *params)

        # Format results
        entities = []
        total_spending = 0

        for row in rows:
            amount = float(row["total_amount"]) if row["total_amount"] else 0
            entities.append(
                {
                    "entity": row["entity_name"],
                    "amount": amount,
                    "transaction_count": row["transaction_count"],
                    "percentage": 0,  # Will calculate after totaling
                }
            )
            total_spending += amount

        # Calculate percentages
        if total_spending > 0:
            for ent in entities:
                ent["percentage"] = round((ent["amount"] / total_spending) * 100, 2)

        return {
            "tenant_id": tenant_id,
            "date_range": {
                "start": start_date.isoformat() if start_date else None,
                "end": end_date.isoformat() if end_date else None,
            },
            "total_spending": total_spending,
            "entity_count": len(entities),
            "entities": entities,
        }

    except Exception as e:
        logger.error(f"Error generating entity spending report: {e}")
        raise ServiceError(
            message=f"Failed to generate entity spending report: {str(e)}",
            error_code="REPORT_GENERATION_ERROR",
            tenant_id=tenant_id,
        )


async def generate_income_expense_summary_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to generate income vs expense summary.

    Args:
        db: Database session
        tenant_id: Tenant ID
        start_date: Start date filter
        end_date: End date filter

    Returns:
        Dictionary containing income and expense summary
    """
    try:
        # Build date filter
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        # Get income (positive amounts) and expenses (negative amounts) in one query
        sql = f"""
            SELECT 
                SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_income,
                COUNT(CASE WHEN amount > 0 THEN 1 END) as income_count,
                SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_expenses,
                COUNT(CASE WHEN amount < 0 THEN 1 END) as expense_count
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
        """

        row = await db.fetchrow(sql, *params)

        # Calculate totals
        total_income = float(row["total_income"]) if row["total_income"] else 0
        total_expenses = float(row["total_expenses"]) if row["total_expenses"] else 0
        net_income = total_income - total_expenses

        # Calculate savings rate
        savings_rate = 0
        if total_income > 0:
            savings_rate = round((net_income / total_income) * 100, 2)

        return {
            "tenant_id": tenant_id,
            "date_range": {
                "start": start_date.isoformat() if start_date else None,
                "end": end_date.isoformat() if end_date else None,
            },
            "income": {
                "total": total_income,
                "transaction_count": row["income_count"] or 0,
            },
            "expenses": {
                "total": total_expenses,
                "transaction_count": row["expense_count"] or 0,
            },
            "net_income": net_income,
            "savings_rate": savings_rate,
            "summary": {
                "is_profitable": net_income > 0,
                "expense_ratio": round((total_expenses / total_income * 100), 2)
                if total_income > 0
                else 0,
            },
        }

    except Exception as e:
        logger.error(f"Error generating income/expense summary: {e}")
        raise ServiceError(
            message=f"Failed to generate income/expense summary: {str(e)}",
            error_code="REPORT_GENERATION_ERROR",
            tenant_id=tenant_id,
        )


async def generate_monthly_trend_report_tool(
    db: asyncpg.Connection, tenant_id: int, months: int = 6
) -> Dict[str, Any]:
    """
    ADK tool function to generate monthly trend report.

    Args:
        db: Database session
        tenant_id: Tenant ID
        months: Number of months to include

    Returns:
        Dictionary containing monthly trends
    """
    try:
        # Calculate date range
        end_date = date.today()
        start_date = end_date - timedelta(days=months * 30)

        # Query monthly aggregates with asyncpg
        sql = """
            SELECT 
                TO_CHAR(date, 'YYYY-MM') as month,
                SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as income,
                SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as expenses
            FROM transactions 
            WHERE tenant_id = $1 
              AND date >= $2 
              AND date <= $3
            GROUP BY TO_CHAR(date, 'YYYY-MM')
            ORDER BY TO_CHAR(date, 'YYYY-MM')
        """

        rows = await db.fetch(sql, tenant_id, start_date, end_date)

        # Format results
        trends = []
        for row in rows:
            month_str = row["month"]
            income = float(row["income"]) if row["income"] else 0
            expenses = float(row["expenses"]) if row["expenses"] else 0

            trends.append(
                {
                    "month": month_str,
                    "income": income,
                    "expenses": expenses,
                    "net_income": income - expenses,
                    "savings_rate": round((income - expenses) / income * 100, 2)
                    if income > 0
                    else 0,
                }
            )

        # Calculate averages
        if trends:
            avg_income = sum(t["income"] for t in trends) / len(trends)
            avg_expenses = sum(t["expenses"] for t in trends) / len(trends)
            avg_net = avg_income - avg_expenses
        else:
            avg_income = avg_expenses = avg_net = 0

        return {
            "tenant_id": tenant_id,
            "period_months": months,
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat(),
            },
            "monthly_trends": trends,
            "averages": {
                "income": round(avg_income, 2),
                "expenses": round(avg_expenses, 2),
                "net_income": round(avg_net, 2),
            },
            "insights": _generate_trend_insights(trends),
        }

    except Exception as e:
        logger.error(f"Error generating monthly trend report: {e}")
        raise ServiceError(
            message=f"Failed to generate monthly trend report: {str(e)}",
            error_code="REPORT_GENERATION_ERROR",
            tenant_id=tenant_id,
        )


def _generate_trend_insights(trends: List[Dict[str, Any]]) -> List[str]:
    """Generate insights from trend data."""
    insights = []

    if not trends:
        return insights

    # Check for growth
    if len(trends) >= 2:
        latest = trends[-1]
        previous = trends[-2]

        income_change = (
            ((latest["income"] - previous["income"]) / previous["income"] * 100)
            if previous["income"] > 0
            else 0
        )
        expense_change = (
            ((latest["expenses"] - previous["expenses"]) / previous["expenses"] * 100)
            if previous["expenses"] > 0
            else 0
        )

        if income_change > 10:
            insights.append(f"Income increased by {income_change:.1f}% last month")
        elif income_change < -10:
            insights.append(f"Income decreased by {abs(income_change):.1f}% last month")

        if expense_change > 10:
            insights.append(f"Expenses increased by {expense_change:.1f}% last month")
        elif expense_change < -10:
            insights.append(
                f"Expenses decreased by {abs(expense_change):.1f}% last month"
            )

    # Check savings rate
    avg_savings_rate = sum(t["savings_rate"] for t in trends) / len(trends)
    if avg_savings_rate > 20:
        insights.append(f"Excellent average savings rate of {avg_savings_rate:.1f}%")
    elif avg_savings_rate < 5:
        insights.append(
            f"Low average savings rate of {avg_savings_rate:.1f}% - consider reducing expenses"
        )

    return insights
