"""
Reports domain package.

Exports all report-related components.
"""

from .custom_report_service import CustomReportService
from .models import CustomReport
from .router import router
from .schemas import (
    CustomReportConfig,
    CustomReportDataResponse,
    CustomReportGenerateRequest,
    CustomReportListResponse,
    CustomReportResponse,
    CustomReportSaveRequest,
    EntitySpendingItem,
    EntitySpendingResponse,
    IncomeExpenseSummaryResponse,
    ReportDateRangeQueryFilters,
    ReportRequest,
    SpendingByCategoryItem,
    SpendingByCategoryResponse,
)
from .service import ReportGenerator

__all__ = [
    # Models
    "CustomReport",
    # Schemas
    "ReportDateRangeQueryFilters",
    "SpendingByCategoryItem",
    "SpendingByCategoryResponse",
    "EntitySpendingItem",
    "EntitySpendingResponse",
    "IncomeExpenseSummaryResponse",
    "ReportRequest",
    "CustomReportConfig",
    "CustomReportGenerateRequest",
    "CustomReportSaveRequest",
    "CustomReportResponse",
    "CustomReportListResponse",
    "CustomReportDataResponse",
    # Services
    "ReportGenerator",
    "CustomReportService",
    # Router
    "router",
]
