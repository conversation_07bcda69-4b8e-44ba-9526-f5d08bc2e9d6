"""
Custom Report Service
====================

Service for managing and generating custom reports.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import asyncpg

from ...shared.exceptions import ErrorSeverity, ServiceError
from ..transactions.models import Transaction
from .models import CustomReport
from .schemas import (
    CustomReportDataResponse,
    CustomReportGenerateRequest,
    CustomReportResponse,
    CustomReportSaveRequest,
)

logger = logging.getLogger(__name__)


class CustomReportService:
    """Service for managing custom reports."""

    def __init__(self, db: asyncpg.Connection):
        """Initialize the custom report service."""
        self.db = db

    async def generate_custom_report(
        self,
        request: CustomReportGenerateRequest,
        tenant_id: int,
        user_id: int,
    ) -> CustomReportDataResponse:
        """Generate a custom report based on the provided configuration."""
        logger.info(
            f"Generating custom report '{request.report_type}' for tenant {tenant_id}"
        )

        try:
            # Build the base SQL query using asyncpg
            where_conditions = ["tenant_id = $1"]
            params = [tenant_id]
            param_count = 1

            # Apply date range filters
            if request.date_range:
                if request.date_range.get("start_date"):
                    # Convert string date to datetime for proper comparison
                    start_date = datetime.fromisoformat(
                        request.date_range["start_date"].replace("Z", "+00:00")
                    )
                    param_count += 1
                    where_conditions.append(f"date >= ${param_count}")
                    params.append(start_date)
                if request.date_range.get("end_date"):
                    # Convert string date to datetime for proper comparison
                    end_date = datetime.fromisoformat(
                        request.date_range["end_date"].replace("Z", "+00:00")
                    )
                    param_count += 1
                    where_conditions.append(f"date <= ${param_count}")
                    params.append(end_date)

            # Apply additional filters
            if request.filters:
                # Category filter
                if request.filters.get("categories"):
                    category_paths = request.filters["categories"]
                    if isinstance(category_paths, list) and category_paths:
                        # Match any category that starts with the provided paths
                        category_conditions = []
                        for path in category_paths:
                            param_count += 1
                            category_conditions.append(
                                f"category_path LIKE ${param_count}"
                            )
                            params.append(f"{path}%")
                        where_conditions.append(f"({' OR '.join(category_conditions)})")

                # Transaction type filter
                if request.filters.get("transaction_types"):
                    types = request.filters["transaction_types"]
                    if isinstance(types, list) and types:
                        param_count += 1
                        where_conditions.append(
                            f"transaction_type = ANY(${param_count})"
                        )
                        params.append(types)

                # Amount range filter
                if request.filters.get("min_amount") is not None:
                    param_count += 1
                    where_conditions.append(f"amount >= ${param_count}")
                    params.append(request.filters["min_amount"])
                if request.filters.get("max_amount") is not None:
                    param_count += 1
                    where_conditions.append(f"amount <= ${param_count}")
                    params.append(request.filters["max_amount"])

                # Entity filter
                if request.filters.get("entity_ids"):
                    entity_ids = request.filters["entity_ids"]
                    if isinstance(entity_ids, list) and entity_ids:
                        param_count += 1
                        where_conditions.append(f"entity_id = ANY(${param_count})")
                        params.append(entity_ids)

            # Build final SQL query
            where_clause = " AND ".join(where_conditions)
            sql = f"SELECT * FROM transactions WHERE {where_clause} ORDER BY date DESC"

            # Execute query using asyncpg
            rows = await self.db.fetch(sql, *params)
            transactions = [Transaction.model_validate(dict(row)) for row in rows]

            # Generate report data based on type
            report_data = await self._process_report_data(
                transactions,
                request.report_type,
                request.grouping,
                request.aggregations,
                request.include_raw_data,
            )

            # Build response
            return CustomReportDataResponse(
                report_type=request.report_type,
                filters=request.filters,
                date_range=request.date_range,
                data=report_data,
                metadata={
                    "total_transactions": len(transactions),
                    "tenant_id": tenant_id,
                    "user_id": user_id,
                },
                generated_at=datetime.utcnow().isoformat(),
            )

        except Exception as e:
            logger.error(f"Failed to generate custom report: {str(e)}")
            raise ServiceError(
                message=f"Failed to generate custom report: {str(e)}",
                service_name="CustomReportService",
                operation="generate_custom_report",
                error_code="CUSTOM_REPORT_GENERATION_FAILED",
                context={"tenant_id": tenant_id, "user_id": user_id},
                severity=ErrorSeverity.HIGH,
            )

    async def save_custom_report(
        self,
        request: CustomReportSaveRequest,
        tenant_id: int,
        user_id: int,
    ) -> CustomReportResponse:
        """Save a custom report configuration."""
        logger.info(f"Saving custom report '{request.name}' for tenant {tenant_id}")

        try:
            # Create new custom report using asyncpg
            import json

            current_time = datetime.utcnow()

            sql = """
                INSERT INTO custom_reports (
                    tenant_id, user_id, name, description, report_type, 
                    configuration, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING *
            """

            row = await self.db.fetchrow(
                sql,
                tenant_id,
                user_id,
                request.name,
                request.description,
                request.report_type,
                json.dumps(request.configuration),
                current_time,
                current_time,
            )
            custom_report = CustomReport.model_validate(dict(row))

            return CustomReportResponse(
                id=custom_report.id,
                name=custom_report.name,
                description=custom_report.description,
                report_type=custom_report.report_type,
                configuration=custom_report.configuration,
                created_at=custom_report.created_at.isoformat(),
                updated_at=custom_report.updated_at.isoformat(),
            )

        except Exception as e:
            logger.error(f"Failed to save custom report: {str(e)}")
            pass  # No rollback needed for asyncpg single operations
            raise ServiceError(
                message=f"Failed to save custom report: {str(e)}",
                service_name="CustomReportService",
                operation="save_custom_report",
                error_code="CUSTOM_REPORT_SAVE_FAILED",
                context={"tenant_id": tenant_id, "user_id": user_id},
                severity=ErrorSeverity.HIGH,
            )

    async def list_custom_reports(
        self, tenant_id: int, user_id: Optional[int] = None
    ) -> List[CustomReportResponse]:
        """List custom reports for a tenant."""
        logger.info(f"Listing custom reports for tenant {tenant_id}")

        try:
            # Build SQL query using asyncpg
            where_conditions = ["tenant_id = $1"]
            params = [tenant_id]
            param_count = 1

            # Optionally filter by user
            if user_id:
                param_count += 1
                where_conditions.append(f"user_id = ${param_count}")
                params.append(user_id)

            where_clause = " AND ".join(where_conditions)
            sql = f"SELECT * FROM custom_reports WHERE {where_clause} ORDER BY updated_at DESC"

            rows = await self.db.fetch(sql, *params)
            reports = [CustomReport.model_validate(dict(row)) for row in rows]

            return [
                CustomReportResponse(
                    id=report.id,
                    name=report.name,
                    description=report.description,
                    report_type=report.report_type,
                    configuration=report.configuration,
                    created_at=report.created_at.isoformat(),
                    updated_at=report.updated_at.isoformat(),
                )
                for report in reports
            ]

        except Exception as e:
            logger.error(f"Failed to list custom reports: {str(e)}")
            raise ServiceError(
                message=f"Failed to list custom reports: {str(e)}",
                service_name="CustomReportService",
                operation="list_custom_reports",
                error_code="CUSTOM_REPORT_LIST_FAILED",
                context={"tenant_id": tenant_id, "user_id": user_id},
                severity=ErrorSeverity.MEDIUM,
            )

    async def get_custom_report(
        self, report_id: int, tenant_id: int
    ) -> Optional[CustomReportResponse]:
        """Get a specific custom report."""
        logger.info(f"Getting custom report {report_id} for tenant {tenant_id}")

        try:
            sql = (
                "SELECT * FROM custom_reports WHERE id = $1 AND tenant_id = $2 LIMIT 1"
            )
            row = await self.db.fetchrow(sql, report_id, tenant_id)
            report = CustomReport.model_validate(dict(row)) if row else None

            if not report:
                return None

            return CustomReportResponse(
                id=report.id,
                name=report.name,
                description=report.description,
                report_type=report.report_type,
                configuration=report.configuration,
                created_at=report.created_at.isoformat(),
                updated_at=report.updated_at.isoformat(),
            )

        except Exception as e:
            logger.error(f"Failed to get custom report: {str(e)}")
            raise ServiceError(
                message=f"Failed to get custom report: {str(e)}",
                service_name="CustomReportService",
                operation="get_custom_report",
                error_code="CUSTOM_REPORT_GET_FAILED",
                context={"tenant_id": tenant_id, "report_id": report_id},
                severity=ErrorSeverity.MEDIUM,
            )

    async def _process_report_data(
        self,
        transactions: List[Transaction],
        report_type: str,
        grouping: Optional[List[str]],
        aggregations: Optional[List[str]],
        include_raw_data: bool,
    ) -> Dict[str, Any]:
        """Process transactions and generate report data."""
        data = {}

        # Summary statistics
        data["summary"] = {
            "total_transactions": len(transactions),
            "total_amount": sum(t.amount for t in transactions if t.amount),
            "date_range": {
                "start": (
                    min(t.date for t in transactions if t.date).isoformat()
                    if transactions and any(t.date for t in transactions)
                    else None
                ),
                "end": (
                    max(t.date for t in transactions if t.date).isoformat()
                    if transactions and any(t.date for t in transactions)
                    else None
                ),
            },
        }

        # Group by category
        if not grouping or "category" in grouping:
            category_data = {}
            for transaction in transactions:
                category = transaction.category_path or "Uncategorized"
                if category not in category_data:
                    category_data[category] = {
                        "count": 0,
                        "total_amount": 0.0,
                        "transactions": [],
                    }
                category_data[category]["count"] += 1
                category_data[category]["total_amount"] += float(
                    transaction.amount or 0
                )
                if include_raw_data:
                    category_data[category]["transactions"].append(
                        {
                            "id": transaction.id,
                            "date": (
                                transaction.date.isoformat()
                                if transaction.date
                                else None
                            ),
                            "description": transaction.description,
                            "amount": float(transaction.amount or 0),
                        }
                    )
            data["by_category"] = category_data

        # Group by entity
        if not grouping or "entity" in grouping:
            entity_data = {}
            for transaction in transactions:
                entity_name = (
                    transaction.entity.name
                    if transaction.entity
                    else transaction.description[:50]
                )
                if entity_name not in entity_data:
                    entity_data[entity_name] = {
                        "count": 0,
                        "total_amount": 0.0,
                        "transactions": [],
                    }
                entity_data[entity_name]["count"] += 1
                entity_data[entity_name]["total_amount"] += float(
                    transaction.amount or 0
                )
                if include_raw_data:
                    entity_data[entity_name]["transactions"].append(
                        {
                            "id": transaction.id,
                            "date": (
                                transaction.date.isoformat()
                                if transaction.date
                                else None
                            ),
                            "description": transaction.description,
                            "amount": float(transaction.amount or 0),
                            "category": transaction.category_path,
                        }
                    )
            data["by_entity"] = entity_data

        # Group by time period (monthly)
        if not grouping or "month" in grouping:
            monthly_data = {}
            for transaction in transactions:
                if transaction.date:
                    month_key = transaction.date.strftime("%Y-%m")
                    if month_key not in monthly_data:
                        monthly_data[month_key] = {
                            "count": 0,
                            "total_amount": 0.0,
                            "income": 0.0,
                            "expense": 0.0,
                        }
                    monthly_data[month_key]["count"] += 1
                    amount = float(transaction.amount or 0)
                    monthly_data[month_key]["total_amount"] += amount
                    if transaction.transaction_type == "income":
                        monthly_data[month_key]["income"] += amount
                    else:
                        monthly_data[month_key]["expense"] += abs(amount)
            data["by_month"] = dict(sorted(monthly_data.items()))

        # Transaction type breakdown
        type_data = {"income": 0.0, "expense": 0.0}
        for transaction in transactions:
            if transaction.transaction_type == "income":
                type_data["income"] += float(transaction.amount or 0)
            else:
                type_data["expense"] += abs(float(transaction.amount or 0))
        data["by_type"] = type_data

        return data
