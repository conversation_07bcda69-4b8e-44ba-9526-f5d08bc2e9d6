"""
Professional CSV Export Service

Provides high-quality CSV export functionality with flexible formatting options
for financial data integration with external systems.
"""

import csv
import logging
from datetime import datetime
from io import String<PERSON>
from typing import Any, Dict, List, Optional

import asyncpg

# Transaction model no longer needed as we're using raw query results

logger = logging.getLogger(__name__)


class ProfessionalCSVExportService:
    """
    Professional CSV export service for financial data.
    Supports multiple CSV formats and encoding options for integration compatibility.
    """

    def __init__(self, db: asyncpg.Connection):
        self.db = db

    async def generate_transactions_csv(
        self,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        include_metadata: bool = True,
        format_style: str = "standard",  # standard, quickbooks, sage, xero
    ) -> StringIO:
        """
        Generate CSV export of transaction data with various format options.

        Args:
            tenant_id: Tenant ID for data filtering
            start_date: Start date for export period
            end_date: End date for export period
            include_metadata: Whether to include metadata columns
            format_style: CSV format style for different accounting systems

        Returns:
            StringIO: CSV content as string buffer
        """
        # Get transactions data
        transactions = await self._get_transactions_data(
            tenant_id, start_date, end_date
        )

        # Create CSV buffer
        output = StringIO()

        # Define columns based on format style
        columns = self._get_columns_for_format(format_style, include_metadata)

        # Create CSV writer
        writer = csv.DictWriter(output, fieldnames=columns)
        writer.writeheader()

        # Write transaction data
        for transaction in transactions:
            row_data = self._format_transaction_row(
                transaction, format_style, include_metadata
            )
            writer.writerow(row_data)

        output.seek(0)
        return output

    async def generate_category_summary_csv(
        self,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> StringIO:
        """
        Generate CSV export of category summary data.

        Args:
            tenant_id: Tenant ID for data filtering
            start_date: Start date for summary period
            end_date: End date for summary period

        Returns:
            StringIO: CSV content as string buffer
        """
        # Get category summary data
        category_data = await self._get_category_summary_data(
            tenant_id, start_date, end_date
        )

        # Create CSV buffer
        output = StringIO()

        # Define columns
        columns = [
            "category_path",
            "transaction_count",
            "total_amount",
            "average_amount",
            "min_amount",
            "max_amount",
            "percentage_of_total",
        ]

        # Create CSV writer
        writer = csv.DictWriter(output, fieldnames=columns)
        writer.writeheader()

        # Calculate total for percentages
        total_amount = sum(item["total_amount"] for item in category_data)

        # Write category data
        for item in category_data:
            percentage = (
                (item["total_amount"] / total_amount * 100) if total_amount > 0 else 0
            )

            row_data = {
                "category": item.get("category_path", "Uncategorized"),
                "transaction_count": item["transaction_count"],
                "total_amount": f"{item['total_amount']:.2f}",
                "average_amount": f"{item['average_amount']:.2f}",
                "min_amount": f"{item['min_amount']:.2f}",
                "max_amount": f"{item['max_amount']:.2f}",
                "percentage_of_total": f"{percentage:.2f}",
            }
            writer.writerow(row_data)

        output.seek(0)
        return output

    async def generate_monthly_summary_csv(
        self,
        tenant_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> StringIO:
        """
        Generate CSV export of monthly summary data.

        Args:
            tenant_id: Tenant ID for data filtering
            start_date: Start date for summary period
            end_date: End date for summary period

        Returns:
            StringIO: CSV content as string buffer
        """
        # Get monthly summary data
        monthly_data = await self._get_monthly_summary_data(
            tenant_id, start_date, end_date
        )

        # Create CSV buffer
        output = StringIO()

        # Define columns
        columns = [
            "year_month",
            "transaction_count",
            "total_amount",
            "average_amount",
            "expense_count",
            "expense_amount",
            "income_count",
            "income_amount",
        ]

        # Create CSV writer
        writer = csv.DictWriter(output, fieldnames=columns)
        writer.writeheader()

        # Write monthly data
        for item in monthly_data:
            row_data = {
                "year_month": item["year_month"],
                "transaction_count": item["transaction_count"],
                "total_amount": f"{item['total_amount']:.2f}",
                "average_amount": f"{item['average_amount']:.2f}",
                "expense_count": item["expense_count"],
                "expense_amount": f"{item['expense_amount']:.2f}",
                "income_count": item["income_count"],
                "income_amount": f"{item['income_amount']:.2f}",
            }
            writer.writerow(row_data)

        output.seek(0)
        return output

    def _get_columns_for_format(
        self, format_style: str, include_metadata: bool
    ) -> List[str]:
        """Get column names based on export format style."""

        if format_style == "quickbooks":
            columns = [
                "Date",
                "Description",
                "Amount",
                "Account",
                "Memo",
                "Category",
                "GL Code",
                "GL Account Name",
                "Category Path",
            ]
        elif format_style == "sage":
            columns = [
                "Date",
                "Reference",
                "Description",
                "Net Amount",
                "GL Code",
                "GL Account Name",
                "Category Path",
                "Department",
            ]
        elif format_style == "xero":
            columns = [
                "Date",
                "Description",
                "Amount",
                "Account Code",
                "GL Code",
                "GL Account Name",
                "Category Path",
                "Account Type",
            ]
        else:  # standard
            columns = [
                "date",
                "description",
                "amount",
                "transaction_type",
                "category_name",
                "category_path",
                "gl_code",
                "gl_account_name",
                "gl_account_type",
                "category_level",
                "categorization_status",
                "confidence",
            ]

        if include_metadata and format_style == "standard":
            columns.extend(
                [
                    "account",
                    "entity_id",
                    "upload_id",
                    "ai_suggested_category_path",
                    "ai_category_confidence",
                    "is_categorized",
                    "is_user_modified",
                    "user_corrected",
                    "created_at",
                    "updated_at",
                ]
            )

        return columns

    def _format_transaction_row(
        self, transaction: Dict[str, Any], format_style: str, include_metadata: bool
    ) -> Dict[str, Any]:
        """Format a transaction row based on the export format."""
        # Extract date and format it safely
        transaction_date = transaction.get("date")
        date_str = transaction_date.strftime("%Y-%m-%d") if transaction_date else ""

        if format_style == "quickbooks":
            return {
                "Date": transaction_date.strftime("%m/%d/%Y")
                if transaction_date
                else "",
                "Description": transaction.get("description", ""),
                "Amount": f"{transaction.get('amount', 0):.2f}",
                "Account": transaction.get("account_number", ""),
                "Memo": transaction.get("memo", ""),
                "Category": transaction.get("category_name", ""),
                "GL Code": transaction.get("gl_code", ""),
                "GL Account Name": transaction.get("gl_account_name", ""),
                "Category Path": transaction.get("category_path", ""),
            }
        elif format_style == "sage":
            return {
                "Date": transaction_date.strftime("%d/%m/%Y")
                if transaction_date
                else "",
                "Reference": transaction.get(
                    "reference_number", transaction.get("id", "")
                ),
                "Description": transaction.get("description", ""),
                "Net Amount": f"{transaction.get('amount', 0):.2f}",
                "GL Code": transaction.get("gl_code", ""),
                "GL Account Name": transaction.get("gl_account_name", ""),
                "Category Path": transaction.get("category_path", ""),
                "Department": "",
            }
        elif format_style == "xero":
            return {
                "Date": transaction_date.strftime("%d/%m/%Y")
                if transaction_date
                else "",
                "Description": transaction.get("description", ""),
                "Amount": f"{transaction.get('amount', 0):.2f}",
                "Account Code": transaction.get(
                    "gl_code", "400"
                ),  # Use GL code or default
                "GL Code": transaction.get("gl_code", ""),
                "GL Account Name": transaction.get("gl_account_name", ""),
                "Category Path": transaction.get("category_path", ""),
                "Account Type": transaction.get("gl_account_type", ""),
            }
        else:  # standard
            row_data = {
                "date": date_str,
                "description": transaction.get("description", ""),
                "amount": f"{transaction.get('amount', 0):.2f}",
                "transaction_type": "Debit"
                if transaction.get("is_debit", False)
                else "Credit",
                "category_name": transaction.get("category_name", ""),
                "category_path": transaction.get("category_path", ""),
                "gl_code": transaction.get("gl_code", ""),
                "gl_account_name": transaction.get("gl_account_name", ""),
                "gl_account_type": transaction.get("gl_account_type", ""),
                "category_level": transaction.get("category_level", ""),
                "categorization_status": transaction.get("categorization_status", ""),
                "confidence": f"{transaction.get('confidence', 0):.2f}"
                if transaction.get("confidence")
                else "",
            }

            if include_metadata:
                row_data.update(
                    {
                        "account": transaction.get("account", ""),
                        "entity_id": transaction.get("entity_id", ""),
                        "upload_id": transaction.get("upload_id", ""),
                        "ai_suggested_category_path": transaction.get("ai_suggested_category_path", ""),
                        "ai_category_confidence": transaction.get("ai_category_confidence", ""),
                        "is_categorized": transaction.get("is_categorized", False),
                        "is_user_modified": transaction.get("is_user_modified", False),
                        "user_corrected": transaction.get("user_corrected", False),
                        "created_at": transaction["created_at"].isoformat()
                        if transaction.get("created_at")
                        else "",
                        "updated_at": transaction["updated_at"].isoformat()
                        if transaction.get("updated_at")
                        else "",
                    }
                )

            return row_data

    async def _get_transactions_data(
        self,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> List[Dict[str, Any]]:
        """Get transactions data for export with proper category and GL code information."""
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND t.date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND t.date <= ${param_count}"
            params.append(end_date)

        # Updated SQL to use actual schema with categorization data in transactions table
        sql = f"""
            SELECT 
                t.id,
                t.date,
                t.description,
                t.amount,
                t.notes as memo,
                t.account,
                NULL as reference_number,
                NULL as check_number,
                CASE WHEN t.amount < 0 THEN true ELSE false END as is_debit,
                t.created_at,
                t.updated_at,
                -- Category information from AI categorization
                t.ai_suggested_category as category_id,
                t.ai_category as category_name,
                NULL as gl_code,
                NULL as gl_account_name,
                NULL as gl_account_type,
                t.ai_category as category_path,
                NULL as category_level,
                'approved' as categorization_status,
                t.ai_confidence as confidence
            FROM transactions t
            WHERE t.tenant_id = $1{date_filter}
            ORDER BY t.date DESC, t.id DESC
        """

        rows = await self.db.fetch(sql, *params)
        return [dict(row) for row in rows]

    async def _get_category_summary_data(
        self,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> List[Dict[str, Any]]:
        """Get category summary data for export."""
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                category_path,
                COUNT(id) as transaction_count,
                SUM(amount) as total_amount,
                AVG(amount) as average_amount,
                MIN(amount) as min_amount,
                MAX(amount) as max_amount
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
            GROUP BY category_path
            ORDER BY SUM(amount) DESC
        """

        rows = await self.db.fetch(sql, *params)

        return [
            {
                "category_path": row["category_path"],
                "transaction_count": row["transaction_count"],
                "total_amount": float(row["total_amount"]),
                "average_amount": float(row["average_amount"]),
                "min_amount": float(row["min_amount"]),
                "max_amount": float(row["max_amount"]),
            }
            for row in rows
        ]

    async def _get_monthly_summary_data(
        self,
        tenant_id: int,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
    ) -> List[Dict[str, Any]]:
        """Get monthly summary data for export."""
        date_filter = ""
        params = [tenant_id]
        param_count = 1

        if start_date:
            param_count += 1
            date_filter += f" AND date >= ${param_count}"
            params.append(start_date)
        if end_date:
            param_count += 1
            date_filter += f" AND date <= ${param_count}"
            params.append(end_date)

        sql = f"""
            SELECT 
                TO_CHAR(date, 'YYYY-MM') as year_month,
                COUNT(id) as transaction_count,
                SUM(amount) as total_amount,
                AVG(amount) as average_amount,
                COUNT(CASE WHEN transaction_type = 'expense' THEN 1 END) as expense_count,
                SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as expense_amount,
                COUNT(CASE WHEN transaction_type = 'income' THEN 1 END) as income_count,
                SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as income_amount
            FROM transactions 
            WHERE tenant_id = $1{date_filter}
            GROUP BY TO_CHAR(date, 'YYYY-MM')
            ORDER BY TO_CHAR(date, 'YYYY-MM')
        """

        rows = await self.db.fetch(sql, *params)

        return [
            {
                "year_month": row["year_month"],
                "transaction_count": row["transaction_count"],
                "total_amount": float(row["total_amount"] or 0),
                "average_amount": float(row["average_amount"] or 0),
                "expense_count": row["expense_count"] or 0,
                "expense_amount": float(row["expense_amount"] or 0),
                "income_count": row["income_count"] or 0,
                "income_amount": float(row["income_amount"] or 0),
            }
            for row in rows
        ]
