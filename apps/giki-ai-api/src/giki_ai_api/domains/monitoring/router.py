"""
Performance monitoring API endpoints.

Provides real-time performance metrics for critical workflows.
"""

from typing import Optional

from fastapi import APIRouter
from pydantic import BaseModel

from ...shared.monitoring.performance import performance_monitor

router = APIRouter(tags=["Monitoring"])


class PerformanceMetricsResponse(BaseModel):
    """Response model for performance metrics."""

    total_operations: int
    operations: dict
    summary: dict


@router.get("/performance/metrics", response_model=PerformanceMetricsResponse)
async def get_performance_metrics(operation_filter: Optional[str] = None):
    """
    Get performance metrics for monitored operations.

    Args:
        operation_filter: Optional filter for specific operation types

    Returns:
        Performance metrics summary
    """
    summary = performance_monitor.get_performance_summary(5)  # 5-minute window

    return PerformanceMetricsResponse(
        total_operations=summary["api_performance"].get("total_requests", 0),
        operations=summary.get("endpoint_performance", {}),
        summary={
            "filter": operation_filter or "all",
            "api_performance": summary["api_performance"],
            "database_performance": summary["database_performance"],
        },
    )


@router.post("/performance/clear")
async def clear_performance_metrics():
    """Clear all recorded performance metrics."""
    performance_monitor.reset_all_metrics()
    return {"status": "success", "message": "Performance metrics cleared"}


@router.get("/performance/health")
async def get_performance_health():
    """
    Get performance health status based on recent metrics.

    Returns health indicators for each operation type.
    """
    summary = performance_monitor.get_performance_summary(5)  # 5-minute window

    # Use the overall health assessment from the performance monitor
    overall_health = summary.get("overall_health", {})
    api_performance = summary.get("api_performance", {})
    db_performance = summary.get("database_performance", {})

    return {
        "status": overall_health.get("status", "unknown"),
        "score": overall_health.get("score", 0),
        "issues": overall_health.get("issues", []),
        "recommendation": overall_health.get("recommendation", "No data available"),
        "api_metrics": {
            "total_requests": api_performance.get("total_requests", 0),
            "avg_response_time_ms": api_performance.get("avg_response_time_ms", 0),
            "error_rate_percent": api_performance.get("error_rate_percent", 0),
            "slow_request_rate_percent": api_performance.get(
                "slow_request_rate_percent", 0
            ),
        },
        "database_metrics": {
            "total_operations": db_performance.get("total_operations", 0),
            "avg_duration_ms": db_performance.get("avg_duration_ms", 0),
            "slow_operation_rate_percent": db_performance.get(
                "slow_operation_rate_percent", 0
            ),
        },
    }
