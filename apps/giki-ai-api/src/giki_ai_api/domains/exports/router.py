"""
Export Router - API Endpoints for Transaction Export
===================================================

Provides REST API endpoints for exporting categorized transactions
to various accounting software formats.
"""

import logging
from datetime import date
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Response, status
from pydantic import BaseModel, Field

from ...core.database import get_db_session
from ...core.dependencies import get_current_tenant_id
from ...shared.exceptions import ServiceError, ValidationError
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .export_formats import ExportFormat
from .service import export_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/exports", tags=["exports"])


class ExportFormatInfo(BaseModel):
    """Information about an available export format."""
    id: str
    name: str
    description: str
    file_extension: str
    supports_multi_currency: bool
    requires_account_codes: bool
    required_fields: List[str]


class ExportRequest(BaseModel):
    """Request model for transaction export."""
    format_id: str = Field(..., description="Export format identifier")
    date_from: Optional[date] = Field(None, description="Start date filter")
    date_to: Optional[date] = Field(None, description="End date filter")
    category_ids: Optional[List[int]] = Field(None, description="Filter by category IDs")
    account_ids: Optional[List[int]] = Field(None, description="Filter by account IDs")
    include_uncategorized: bool = Field(True, description="Include uncategorized transactions")
    
    class Config:
        json_schema_extra = {
            "example": {
                "format_id": "quickbooks_online",
                "date_from": "2024-01-01",
                "date_to": "2024-12-31",
                "include_uncategorized": False
            }
        }


class ExportReadinessResponse(BaseModel):
    """Response model for export readiness check."""
    is_ready: bool
    messages: List[str]
    format_name: str


@router.get(
    "/formats",
    response_model=List[ExportFormatInfo],
    summary="Get available export formats",
    description="Returns a list of all supported accounting software export formats."
)
async def get_export_formats(
    current_user: User = Depends(get_current_active_user),
) -> List[ExportFormatInfo]:
    """Get list of available export formats."""
    try:
        formats = await export_service.get_available_formats()
        return [ExportFormatInfo(**fmt) for fmt in formats]
    except Exception as e:
        logger.error(f"Failed to get export formats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve export formats"
        )


@router.get(
    "/readiness/{format_id}",
    response_model=ExportReadinessResponse,
    summary="Check export readiness",
    description="Checks if transactions are ready for export in the specified format."
)
async def check_export_readiness(
    format_id: str,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn = Depends(get_db_session),
) -> ExportReadinessResponse:
    """Check if transactions are ready for export."""
    try:
        # Validate format ID
        try:
            export_format = ExportFormat(format_id)
        except ValueError:
            raise ValidationError(f"Invalid export format: {format_id}")
        
        # Check readiness
        is_ready, messages = await export_service.validate_export_readiness(
            conn, tenant_id, export_format
        )
        
        # Get format name
        format_info = await export_service.get_available_formats()
        format_name = next(
            (f["name"] for f in format_info if f["id"] == format_id),
            format_id
        )
        
        return ExportReadinessResponse(
            is_ready=is_ready,
            messages=messages,
            format_name=format_name
        )
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Export readiness check failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check export readiness"
        )


@router.post(
    "/download",
    summary="Export transactions",
    description="Export categorized transactions in the specified accounting software format.",
    responses={
        200: {
            "description": "File download",
            "content": {
                "text/csv": {},
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {},
                "text/plain": {},
                "application/xml": {}
            }
        }
    }
)
async def export_transactions(
    request: ExportRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn = Depends(get_db_session),
) -> Response:
    """Export transactions in the requested format."""
    try:
        # Validate format ID
        try:
            export_format = ExportFormat(request.format_id)
        except ValueError:
            raise ValidationError(f"Invalid export format: {request.format_id}")
        
        # Prepare filters
        filters = {}
        if request.date_from:
            filters["date_from"] = request.date_from
        if request.date_to:
            filters["date_to"] = request.date_to
        if request.category_ids:
            filters["category_ids"] = request.category_ids
        if request.account_ids:
            filters["account_ids"] = request.account_ids
        filters["include_uncategorized"] = request.include_uncategorized
        
        # Export transactions
        content, filename, content_type = await export_service.export_transactions(
            conn, tenant_id, export_format, filters
        )
        
        # Log export activity
        logger.info(
            f"User {current_user.id} exported transactions in {export_format.value} format"
        )
        
        # Return file response
        return Response(
            content=content,
            media_type=content_type,
            headers={
                "Content-Disposition": f'attachment; filename="{filename}"',
                "X-Export-Format": export_format.value,
                "X-Transaction-Count": str(len(content))  # Approximate
            }
        )
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ServiceError as e:
        logger.error(f"Export service error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Export service encountered an error"
        )
    except Exception as e:
        logger.error(f"Export failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export transactions"
        )


@router.post(
    "/quickbooks",
    summary="Export to QuickBooks",
    description="Shortcut endpoint for QuickBooks export (auto-detects Desktop vs Online).",
    responses={
        200: {
            "description": "QuickBooks file download",
            "content": {
                "text/csv": {},
                "text/plain": {}
            }
        }
    }
)
async def export_to_quickbooks(
    date_from: Optional[date] = Query(None, description="Start date"),
    date_to: Optional[date] = Query(None, description="End date"),
    format_type: str = Query("online", description="'desktop' or 'online'"),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn = Depends(get_db_session),
) -> Response:
    """Export transactions to QuickBooks format."""
    # Determine format
    format_id = "quickbooks_desktop" if format_type.lower() == "desktop" else "quickbooks_online"
    
    # Create request
    request = ExportRequest(
        format_id=format_id,
        date_from=date_from,
        date_to=date_to,
        include_uncategorized=False
    )
    
    # Use main export endpoint
    return await export_transactions(request, current_user, tenant_id, conn)


@router.post(
    "/zoho-books",
    summary="Export to Zoho Books",
    description="Shortcut endpoint for Zoho Books CSV export.",
    responses={
        200: {
            "description": "Zoho Books CSV file",
            "content": {"text/csv": {}}
        }
    }
)
async def export_to_zoho_books(
    date_from: Optional[date] = Query(None, description="Start date"),
    date_to: Optional[date] = Query(None, description="End date"),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn = Depends(get_db_session),
) -> Response:
    """Export transactions to Zoho Books format."""
    request = ExportRequest(
        format_id="zoho_books",
        date_from=date_from,
        date_to=date_to,
        include_uncategorized=False
    )
    
    return await export_transactions(request, current_user, tenant_id, conn)


@router.post(
    "/tally-prime",
    summary="Export to Tally Prime",
    description="Shortcut endpoint for Tally Prime XML export.",
    responses={
        200: {
            "description": "Tally Prime XML file",
            "content": {"application/xml": {}}
        }
    }
)
async def export_to_tally_prime(
    date_from: Optional[date] = Query(None, description="Start date"),
    date_to: Optional[date] = Query(None, description="End date"),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn = Depends(get_db_session),
) -> Response:
    """Export transactions to Tally Prime format."""
    request = ExportRequest(
        format_id="tally_prime",
        date_from=date_from,
        date_to=date_to,
        include_uncategorized=False
    )
    
    return await export_transactions(request, current_user, tenant_id, conn)