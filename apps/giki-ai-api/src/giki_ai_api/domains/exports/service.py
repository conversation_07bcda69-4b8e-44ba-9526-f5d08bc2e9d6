"""
Export Service - Transaction Export to Accounting Software Formats
=================================================================

This service handles exporting categorized transactions to various
accounting software formats using the format definitions.
"""

import csv
import io
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd
from asyncpg import Connection

from ...shared.exceptions import ServiceError, ValidationError
from .export_formats import (
    ExportFormat,
    ExportFormatSpec,
    export_format_registry,
    format_amount,
    format_date,
)

logger = logging.getLogger(__name__)


class ExportService:
    """Service for exporting transactions to accounting software formats."""
    
    async def export_transactions(
        self,
        conn: Connection,
        tenant_id: int,
        format_id: ExportFormat,
        filters: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Tuple[bytes, str, str]:
        """
        Export transactions in the specified format.
        
        Args:
            conn: Database connection
            tenant_id: Tenant ID
            format_id: Export format identifier
            filters: Optional filters (date_from, date_to, category_ids, etc.)
            options: Optional export options (include_uncategorized, etc.)
            
        Returns:
            Tuple of (file_content, filename, content_type)
        """
        try:
            # Get format specification
            format_spec = export_format_registry.get_format(format_id)
            if not format_spec:
                raise ValidationError(f"Unsupported export format: {format_id}")
            
            # Fetch transactions
            transactions = await self._fetch_transactions(conn, tenant_id, filters)
            
            if not transactions:
                raise ValidationError("No transactions found for the specified criteria")
            
            # Transform transactions to export format
            export_data = self._transform_transactions(transactions, format_spec, options)
            
            # Generate file content
            if format_spec.file_extension == "csv":
                content = self._generate_csv(export_data, format_spec)
            elif format_spec.file_extension == "xlsx":
                content = self._generate_excel(export_data, format_spec)
            elif format_spec.file_extension == "iif":
                content = self._generate_iif(export_data, format_spec)
            elif format_spec.file_extension == "xml":
                content = self._generate_xml(export_data, format_spec)
            else:
                raise ValidationError(f"Unsupported file format: {format_spec.file_extension}")
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"giki_export_{format_spec.name.lower().replace(' ', '_')}_{timestamp}.{format_spec.file_extension}"
            
            # Determine content type
            content_type = self._get_content_type(format_spec.file_extension)
            
            logger.info(
                f"Successfully exported {len(transactions)} transactions for tenant {tenant_id} "
                f"in {format_spec.name} format"
            )
            
            return content, filename, content_type
            
        except Exception as e:
            logger.error(f"Export failed: {str(e)}", exc_info=True)
            raise ServiceError("Export Service", "export_transactions", f"Export failed: {str(e)}") from e
    
    async def _fetch_transactions(
        self,
        conn: Connection,
        tenant_id: int,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Fetch transactions from database with optional filters."""
        
        # Base query - using actual transaction table schema
        query = """
            SELECT 
                t.id,
                t.date,
                t.description,
                t.amount,
                t.vendor,
                t.vendor_name,
                t.transaction_type,
                t.ai_category,
                t.ai_confidence,
                t.ai_suggested_category,
                t.original_category,
                t.category_id,
                t.user_confirmed_category_id,
                t.entity_name,
                t.notes,
                c.name as category_name,
                c.gl_account_type as category_type,
                c.parent_id as parent_category_id,
                c.gl_code as category_gl_code
            FROM transactions t
            LEFT JOIN categories c ON COALESCE(t.user_confirmed_category_id, t.category_id) = c.id
            WHERE t.tenant_id = $1
        """
        
        params = [tenant_id]
        param_count = 1
        
        # Apply filters
        if filters:
            if filters.get("date_from"):
                param_count += 1
                query += f" AND t.date >= ${param_count}"
                params.append(filters["date_from"])
            
            if filters.get("date_to"):
                param_count += 1
                query += f" AND t.date <= ${param_count}"
                params.append(filters["date_to"])
            
            if filters.get("category_ids"):
                param_count += 1
                query += f" AND COALESCE(t.user_confirmed_category_id, t.category_id) = ANY(${param_count})"
                params.append(filters["category_ids"])
            
            # Note: account_id column doesn't exist in current schema
            # if filters.get("account_ids"):
            #     param_count += 1
            #     query += f" AND t.account_id = ANY(${param_count})"
            #     params.append(filters["account_ids"])
            
            if not filters.get("include_uncategorized", True):
                query += " AND (t.user_confirmed_category_id IS NOT NULL OR t.category_id IS NOT NULL)"
        
        query += " ORDER BY t.date, t.id"
        
        rows = await conn.fetch(query, *params)
        
        # Convert to dictionaries
        transactions = []
        for row in rows:
            transaction = dict(row)
            
            # Calculate debit/credit amounts
            amount = float(transaction["amount"])
            if transaction["transaction_type"] == "debit" or amount < 0:
                transaction["debit_amount"] = abs(amount)
                transaction["credit_amount"] = 0
            else:
                transaction["debit_amount"] = 0
                transaction["credit_amount"] = abs(amount)
            
            # Use category name hierarchy: user confirmed > AI category > original category
            if not transaction["category_name"]:
                if transaction["ai_category"]:
                    transaction["category_name"] = transaction["ai_category"]
                elif transaction["ai_suggested_category"]:
                    transaction["category_name"] = transaction["ai_suggested_category"]
                elif transaction["original_category"]:
                    transaction["category_name"] = transaction["original_category"]
                else:
                    transaction["category_name"] = "Uncategorized"
            
            # Add default values for fields that might be missing
            transaction["reference_number"] = transaction.get("reference_number", "")
            transaction["memo"] = transaction.get("memo", transaction.get("notes", ""))
            transaction["payment_method"] = transaction.get("payment_method", "")
            transaction["check_number"] = transaction.get("check_number", "")
            transaction["gl_code"] = transaction.get("category_gl_code", "")
            transaction["account_name"] = transaction.get("account", "")
            transaction["account_number"] = ""
            transaction["account_type"] = ""
            
            # Set metadata if not present
            transaction["metadata"] = transaction.get("metadata", {})
            
            transactions.append(transaction)
        
        return transactions
    
    def _transform_transactions(
        self,
        transactions: List[Dict[str, Any]],
        format_spec: ExportFormatSpec,
        options: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Transform transactions to match export format requirements."""
        
        export_data = []
        
        for transaction in transactions:
            row = {}
            
            # Map fields according to column mappings
            mappings = format_spec.column_mappings
            
            # Handle both dict and ColumnMapping objects
            if isinstance(mappings, dict):
                # Convert dict to list of tuples for consistent processing
                mappings = [(source, target) for source, target in mappings.items()]
            
            for mapping in mappings:
                # Handle different mapping formats
                if isinstance(mapping, tuple):
                    source_field, target_column = mapping
                    default_value = None
                elif hasattr(mapping, 'source_field'):
                    source_field = mapping.source_field
                    target_column = mapping.target_column
                    default_value = getattr(mapping, 'default_value', None)
                else:
                    # Assume it's a dict with source/target keys
                    source_field = mapping.get('source_field', mapping.get('source', ''))
                    target_column = mapping.get('target_column', mapping.get('target', ''))
                    default_value = mapping.get('default_value')
                
                source_value = transaction.get(source_field)
                
                # Use default value if source is missing
                if source_value is None:
                    source_value = default_value
                
                # Apply format function if specified
                format_function = None
                if hasattr(mapping, 'format_function'):
                    format_function = mapping.format_function
                elif isinstance(mapping, dict):
                    format_function = mapping.get('format_function')
                
                if format_function == "format_date" and source_value:
                    source_value = format_date(source_value, format_spec.date_format)
                elif format_function == "format_amount" and source_value is not None:
                    source_value = format_amount(source_value, format_spec.decimal_places)
                elif format_function == "format_tally_date" and source_value:
                    source_value = format_date(source_value, format_spec.date_format)
                
                row[target_column] = source_value
            
            # Handle amount format conventions
            if format_spec.amount_format == format_spec.amount_format.SIGNED_AMOUNT:
                # Single amount column with +/- signs
                amount = float(transaction["amount"])
                if transaction["transaction_type"] == "debit":
                    amount = -abs(amount)
                else:
                    amount = abs(amount)
                
                # Find the amount column name from the format specification
                amount_column = None
                for mapping in format_spec.column_mappings:
                    if mapping.source_field == "amount":
                        amount_column = mapping.target_column
                        break
                
                if amount_column:
                    row[amount_column] = format_amount(amount, format_spec.decimal_places)
            
            export_data.append(row)
        
        return export_data
    
    def _generate_csv(
        self,
        data: List[Dict[str, Any]],
        format_spec: ExportFormatSpec
    ) -> bytes:
        """Generate CSV file content."""
        
        output = io.StringIO()
        
        # Write CSV
        writer = csv.DictWriter(
            output,
            fieldnames=format_spec.column_order,
            delimiter=format_spec.delimiter,
            quotechar=format_spec.quote_char,
            quoting=csv.QUOTE_MINIMAL,
            lineterminator=format_spec.line_terminator
        )
        
        if format_spec.include_header:
            writer.writeheader()
        
        writer.writerows(data)
        
        # Convert to bytes
        content = output.getvalue().encode(format_spec.encoding)
        output.close()
        
        return content
    
    def _generate_excel(
        self,
        data: List[Dict[str, Any]],
        format_spec: ExportFormatSpec
    ) -> bytes:
        """Generate Excel file content."""
        
        # Create DataFrame
        df = pd.DataFrame(data, columns=format_spec.column_order)
        
        # Create Excel writer
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Transactions')
            
            # Get worksheet for formatting
            worksheet = writer.sheets['Transactions']
            
            # Ensure at least one sheet is visible
            worksheet.sheet_state = 'visible'
            
            # Apply formatting if specified
            if format_spec.metadata.get("formatting"):
                formatting = format_spec.metadata["formatting"]
                
                # Header formatting
                if formatting.get("header_style"):
                    for cell in worksheet[1]:
                        cell.font = cell.font.copy(bold=True, color="FFFFFF")
                        cell.fill = cell.fill.copy(fgColor="295343")
                
                # Column widths
                if formatting.get("column_widths"):
                    for col_name, width in formatting["column_widths"].items():
                        if col_name in format_spec.column_order:
                            col_idx = format_spec.column_order.index(col_name) + 1
                            col_letter = chr(64 + col_idx)
                            worksheet.column_dimensions[col_letter].width = width
                
                # Freeze panes
                if formatting.get("freeze_panes"):
                    worksheet.freeze_panes = formatting["freeze_panes"]
                
                # Auto filter
                if formatting.get("auto_filter"):
                    worksheet.auto_filter.ref = worksheet.dimensions
        
        output.seek(0)
        content = output.read()
        output.close()
        
        return content
    
    def _generate_iif(
        self,
        data: List[Dict[str, Any]],
        format_spec: ExportFormatSpec
    ) -> bytes:
        """Generate QuickBooks IIF file content."""
        
        output = io.StringIO()
        
        # Write header
        if format_spec.metadata.get("header_required"):
            output.write(format_spec.metadata["header_required"] + "\n")
        
        # Write transactions
        for row in data:
            # TRNS line
            trns_values = [str(row.get(col, "")) for col in format_spec.column_order]
            output.write(format_spec.delimiter.join(trns_values) + "\n")
            
            # SPL line (split for double-entry)
            if format_spec.metadata.get("split_required"):
                spl_values = trns_values.copy()
                spl_values[0] = "!SPL"  # Change type
                # Reverse amount for split
                if spl_values[5]:  # Amount column
                    try:
                        amount = float(spl_values[5])
                        spl_values[5] = str(-amount)
                    except ValueError:
                        pass
                output.write(format_spec.delimiter.join(spl_values) + "\n")
            
            # End marker
            if format_spec.metadata.get("end_marker"):
                output.write(format_spec.metadata["end_marker"] + "\n")
        
        content = output.getvalue().encode(format_spec.encoding)
        output.close()
        
        return content
    
    def _generate_xml(
        self,
        data: List[Dict[str, Any]],
        format_spec: ExportFormatSpec
    ) -> bytes:
        """Generate XML file content (for Tally Prime)."""
        
        # Build XML structure
        xml_content = '<?xml version="1.0" encoding="UTF-8"?>\n'
        xml_content += '<ENVELOPE>\n'
        xml_content += '  <HEADER>\n'
        xml_content += '    <TALLYREQUEST>Import Data</TALLYREQUEST>\n'
        xml_content += '  </HEADER>\n'
        xml_content += '  <BODY>\n'
        xml_content += '    <IMPORTDATA>\n'
        xml_content += '      <REQUESTDESC>\n'
        xml_content += '        <REPORTNAME>Vouchers</REPORTNAME>\n'
        xml_content += '      </REQUESTDESC>\n'
        xml_content += '      <REQUESTDATA>\n'
        
        # Add each transaction as a voucher
        for row in data:
            xml_content += '        <TALLYMESSAGE>\n'
            xml_content += '          <VOUCHER>\n'
            
            for col in format_spec.column_order:
                value = row.get(col, "")
                if value:
                    xml_content += f'            <{col}>{value}</{col}>\n'
            
            xml_content += '          </VOUCHER>\n'
            xml_content += '        </TALLYMESSAGE>\n'
        
        xml_content += '      </REQUESTDATA>\n'
        xml_content += '    </IMPORTDATA>\n'
        xml_content += '  </BODY>\n'
        xml_content += '</ENVELOPE>\n'
        
        return xml_content.encode(format_spec.encoding)
    
    def _get_content_type(self, extension: str) -> str:
        """Get content type for file extension."""
        content_types = {
            "csv": "text/csv",
            "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "iif": "text/plain",
            "xml": "application/xml",
        }
        return content_types.get(extension, "application/octet-stream")
    
    async def get_available_formats(self) -> List[Dict[str, Any]]:
        """Get list of available export formats."""
        formats = []
        
        for format_id, format_spec in export_format_registry.get_all_formats().items():
            formats.append({
                "id": format_id.value,
                "name": format_spec.name,
                "description": format_spec.description,
                "file_extension": format_spec.file_extension,
                "supports_multi_currency": format_spec.supports_multi_currency,
                "requires_account_codes": format_spec.requires_account_codes,
                "required_fields": [
                    mapping.source_field 
                    for mapping in format_spec.column_mappings 
                    if mapping.required and mapping.default_value is None
                ]
            })
        
        return formats
    
    async def validate_export_readiness(
        self,
        conn: Connection,
        tenant_id: int,
        format_id: ExportFormat
    ) -> Tuple[bool, List[str]]:
        """
        Check if transactions are ready for export in the specified format.
        
        Returns:
            Tuple of (is_ready, messages)
        """
        format_spec = export_format_registry.get_format(format_id)
        if not format_spec:
            return False, [f"Unknown format: {format_id}"]
        
        messages = []
        
        # Check for uncategorized transactions
        uncategorized_count = await conn.fetchval(
            """
            SELECT COUNT(*) FROM transactions 
            WHERE tenant_id = $1 AND user_confirmed_category_id IS NULL AND category_id IS NULL
            """,
            tenant_id
        )
        
        if uncategorized_count > 0:
            messages.append(f"{uncategorized_count} transactions are not categorized")
        
        # Check for missing GL codes if required
        if format_spec.requires_account_codes:
            missing_gl_count = await conn.fetchval(
                """
                SELECT COUNT(*) FROM transactions t
                LEFT JOIN categories c ON COALESCE(t.user_confirmed_category_id, t.category_id) = c.id
                WHERE t.tenant_id = $1 AND (c.gl_code IS NULL OR c.gl_code = '')
                """,
                tenant_id
            )
            
            if missing_gl_count > 0:
                messages.append(
                    f"{missing_gl_count} transactions have categories without GL codes"
                )
        
        # Check for missing tax codes if required
        if format_spec.requires_tax_codes:
            messages.append("Tax code assignment is required for this format")
        
        return len(messages) == 0, messages


# Export service instance
export_service = ExportService()