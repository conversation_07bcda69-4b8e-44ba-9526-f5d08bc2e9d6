"""
Secure authentication router without dangerous optimizations.

Every request validates against the database for current user state.
"""

import logging
from typing import Annotated

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel

from ...core.config import settings
from ...core.database import get_db_session
from .models import User, UserCreate
from .schemas import Token, TokenRefreshRequest


# Simple user response without extra fields
class SimpleUserResponse(BaseModel):
    id: int
    email: str
    tenant_id: int
    is_active: bool
    is_admin: bool
    full_name: str | None = None


logger = logging.getLogger(__name__)

# ALWAYS use secure authentication - no optimization flags
logger.info("Using SECURE authentication with RS256")
from .secure_auth import (
    authenticate_user_with_db,
    create_access_token,
    create_refresh_token,
    decode_refresh_token,
    get_current_active_user,
    get_password_hash,
)


# Dummy functions for compatibility
def get_auth_cache_stats():
    return {"message": "Caching disabled"}

def clear_auth_caches():
    pass

# No optimization flag
USE_OPTIMIZED_AUTH = False


router = APIRouter()


@router.post("/token", response_model=Token)
@router.post("/login", response_model=Token)
async def login(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends(OAuth2PasswordRequestForm)],
    conn: Annotated[Connection, Depends(get_db_session)],
):
    """
    Secure login endpoint that always validates against the database.

    NO CACHING of authentication results for security.
    """
    # Log authentication attempt
    logger.info(f"Login attempt for username: {form_data.username}")

    # Authenticate user (queries database, verifies password)
    user = await authenticate_user_with_db(conn, form_data.username, form_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token and refresh token
    access_token = create_access_token(user)
    refresh_token = create_refresh_token(user)

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES
        * 60,  # Convert minutes to seconds
    }


@router.get("/me", response_model=SimpleUserResponse)
async def read_users_me(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Get current user information.

    Always returns fresh data from database.
    """
    return current_user  # Return the User model directly, it will be serialized to UserResponse


@router.post("/logout")
async def logout(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Logout endpoint.

    Since we don't cache sessions, logout is effectively a no-op.
    The client should discard the token.
    """
    logger.info(f"User {current_user.email} logged out")
    return {"message": "Successfully logged out"}


@router.post("/debug-login")
async def debug_login(
    request: Request,
    conn: Annotated[Connection, Depends(get_db_session)],
):
    """
    Debug login endpoint to test raw form data parsing.
    """
    # Get raw request body
    body = await request.body()
    logger.warning(f"Raw request body: {body}")
    logger.warning(f"Raw request body decoded: {body.decode()}")

    # Parse form data manually
    from urllib.parse import parse_qs

    form_data = parse_qs(body.decode())
    logger.warning(f"Parsed form data: {form_data}")

    username = form_data.get("username", [""])[0]
    password = form_data.get("password", [""])[0]

    logger.info(f"Manual form parsing for username: {username}")

    # Test authentication
    user = await authenticate_user_with_db(conn, username, password)

    if not user:
        return {"status": "failed", "message": "Authentication failed"}

    # Create access token
    access_token = create_access_token(user)

    return {
        "status": "success",
        "access_token": access_token,
        "token_type": "bearer",
        "debug_info": {
            "raw_body": body.decode(),
            "parsed_username": username,
            "parsed_password_length": len(password),
            "parsed_password_repr": repr(password),
        },
    }


@router.post("/refresh", response_model=Token)
async def refresh_access_token(
    token_request: TokenRefreshRequest,
    conn: Annotated[Connection, Depends(get_db_session)],
):
    """
    Refresh an access token using a refresh token.

    Always validates against the database for current user state.
    """
    try:
        # Decode the refresh token
        user_id, tenant_id = decode_refresh_token(token_request.refresh_token)

        # Get user from database to ensure they're still active
        if tenant_id is None:
            query = """
                SELECT 
                    id, email, email as username, hashed_password,
                    is_active, false as is_verified, is_superuser, tenant_id,
                    created_at, updated_at, null as last_login, 0 as login_count
                FROM users 
                WHERE id = $1 AND tenant_id IS NULL
            """
            row = await conn.fetchrow(query, user_id)
        else:
            query = """
                SELECT 
                    id, email, email as username, hashed_password,
                    is_active, false as is_verified, is_superuser, tenant_id,
                    created_at, updated_at, null as last_login, 0 as login_count
                FROM users 
                WHERE id = $1 AND tenant_id = $2
            """
            row = await conn.fetchrow(query, user_id, tenant_id)

        if not row:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Convert to User model
        from .models import UserDB as UserModel

        user = UserModel(**dict(row))

        # Verify user is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is deactivated",
            )

        # Create new access token and refresh token
        access_token = create_access_token(user)
        new_refresh_token = create_refresh_token(user)

        return {
            "access_token": access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES
            * 60,  # Convert minutes to seconds
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error refreshing token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.get("/verify")
async def verify_token(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Verify if a token is valid.

    Always checks database for current user state.
    """
    return {
        "valid": True,
        "user_id": current_user.id,
        "email": current_user.email,
        "tenant_id": current_user.tenant_id,
    }


@router.post("/register", response_model=SimpleUserResponse)
async def register_user(
    user_data: UserCreate,
    conn: Annotated[Connection, Depends(get_db_session)],
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Create a new user account (admin only).

    Security: Only superusers can create new accounts.
    """
    # Check if current user is admin
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can create new users",
        )

    try:
        # Check if user already exists
        existing_user = await conn.fetchrow(
            "SELECT id FROM users WHERE email = $1", user_data.email
        )
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered",
            )

        # Hash the password
        hashed_password = get_password_hash(user_data.password)

        # Set tenant_id if not provided (use current user's tenant)
        tenant_id = user_data.tenant_id or current_user.tenant_id

        # Create the user
        user_query = """
            INSERT INTO users (
                username, email, hashed_password, tenant_id, is_superuser, 
                is_active, is_verified, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            RETURNING id, email, username, tenant_id, is_active, is_superuser, 
                     created_at, updated_at
        """

        new_user_row = await conn.fetchrow(
            user_query,
            user_data.username,
            user_data.email,
            hashed_password,
            tenant_id,
            user_data.is_superuser,
            True,  # is_active
            False,  # is_verified
        )

        if not new_user_row:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user",
            )

        logger.info(
            f"New user created: {user_data.email} by admin {current_user.email}"
        )

        return SimpleUserResponse(
            id=new_user_row["id"],
            email=new_user_row["email"],
            tenant_id=new_user_row["tenant_id"],
            is_active=new_user_row["is_active"],
            is_admin=new_user_row["is_superuser"],
            full_name=new_user_row["username"],
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user {user_data.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during user creation",
        )


@router.post("/public-register", response_model=SimpleUserResponse)
async def public_register_user(
    user_data: UserCreate,
    conn: Annotated[Connection, Depends(get_db_session)],
):
    """
    Public user registration endpoint.

    Allows new users to create accounts without admin approval.
    """
    try:
        # Check if user already exists
        existing_user = await conn.fetchrow(
            "SELECT id FROM users WHERE email = $1", user_data.email
        )
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="User with this email already exists",
            )

        # Hash the password
        hashed_password = get_password_hash(user_data.password)

        # Get or create default tenant
        default_tenant = await conn.fetchrow(
            "SELECT id FROM tenants WHERE name = 'default' LIMIT 1"
        )
        if not default_tenant:
            # Create default tenant if it doesn't exist
            tenant_id = await conn.fetchval(
                """
                INSERT INTO tenants (name, is_active, created_at, updated_at) 
                VALUES ('default', true, NOW(), NOW()) 
                RETURNING id
                """
            )
        else:
            tenant_id = default_tenant["id"]

        # Create the user
        user_id = await conn.fetchval(
            """
            INSERT INTO users (email, hashed_password, is_active, is_superuser, tenant_id, created_at, updated_at) 
            VALUES ($1, $2, true, false, $3, NOW(), NOW()) 
            RETURNING id
            """,
            user_data.email,
            hashed_password,
            tenant_id,
        )

        logger.info(f"Public user registration successful for {user_data.email}")

        return SimpleUserResponse(
            id=user_id,
            email=user_data.email,
            tenant_id=tenant_id,
            is_active=True,
            is_admin=False,
            full_name=None,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in public user registration for {user_data.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during user registration",
        )


@router.get("/quick-actions")
async def get_quick_actions(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Get quick actions for the dashboard.
    Returns frequently used actions based on user role and preferences.
    """
    try:
        # Return common quick actions for financial platform
        quick_actions = [
            {
                "id": "upload_file",
                "title": "Upload File",
                "description": "Import transactions from Excel/CSV",
                "icon": "upload",
                "url": "/upload",
                "category": "data_import",
            },
            {
                "id": "review_transactions",
                "title": "Review Transactions",
                "description": "Check categorization and make corrections",
                "icon": "list-checks",
                "url": "/transactions",
                "category": "review",
            },
            {
                "id": "generate_report",
                "title": "Generate Report",
                "description": "Create financial reports and insights",
                "icon": "chart-bar",
                "url": "/reports",
                "category": "reporting",
            },
            {
                "id": "manage_categories",
                "title": "Manage Categories",
                "description": "Edit categorization rules and hierarchies",
                "icon": "tag",
                "url": "/categories",
                "category": "configuration",
            },
        ]

        # Add admin-specific actions if user is admin
        if hasattr(current_user, "is_admin") and current_user.is_admin:
            quick_actions.extend(
                [
                    {
                        "id": "user_management",
                        "title": "User Management",
                        "description": "Manage users and permissions",
                        "icon": "users",
                        "url": "/settings",
                        "category": "admin",
                    },
                    {
                        "id": "system_health",
                        "title": "System Health",
                        "description": "Monitor API and database performance",
                        "icon": "activity",
                        "url": "/health/performance",
                        "category": "admin",
                    },
                ]
            )

        return {
            "actions": quick_actions,
            "total": len(quick_actions),
            "user_id": current_user.id,
            "user_role": "admin"
            if (hasattr(current_user, "is_admin") and current_user.is_admin)
            else "user",
        }

    except Exception as e:
        logger.error(f"Error fetching quick actions for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching quick actions",
        )


@router.get("/cache-stats")
async def get_cache_statistics(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Get authentication cache statistics (admin only).

    Returns cache hit rates and performance metrics.
    """
    # Check if current user is admin
    if not (hasattr(current_user, "is_superuser") and current_user.is_superuser):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can view cache statistics",
        )

    return get_auth_cache_stats()


@router.post("/clear-cache")
async def clear_authentication_cache(
    current_user: Annotated[User, Depends(get_current_active_user)],
):
    """
    Clear authentication caches (admin only).

    This will force all tokens and user data to be re-validated from database.
    """
    # Check if current user is admin
    if not (hasattr(current_user, "is_superuser") and current_user.is_superuser):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can clear caches",
        )

    clear_auth_caches()

    return {
        "message": "Authentication caches cleared successfully",
        "optimized_auth_enabled": USE_OPTIMIZED_AUTH,
    }
