"""
Authentication domain schemas.

Pydantic schemas for authentication, user management, and JWT tokens.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, EmailStr, Field, field_validator


class UserBase(BaseModel):
    """Base user schema."""

    email: EmailStr
    is_active: bool = True
    is_admin: bool = False


class UserCreate(UserBase):
    """User creation schema."""

    password: str = Field(
        ..., min_length=8, description="Password must be at least 8 characters"
    )
    confirm_password: str = Field(..., description="Password confirmation")
    tenant_id: Optional[int] = Field(None, description="Tenant ID for user association")

    @field_validator("confirm_password")
    @classmethod
    def passwords_match(cls, v, info):
        if "password" in info.data and v != info.data["password"]:
            raise ValueError("Passwords do not match")
        return v

    @field_validator("password")
    @classmethod
    def validate_password_strength(cls, v):
        """Validate password meets security requirements."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)

        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain uppercase, lowercase, and numeric characters"
            )

        return v


class UserUpdate(BaseModel):
    """User update schema."""

    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None
    is_admin: Optional[bool] = None
    password: Optional[str] = Field(None, min_length=8)


class UserResponse(UserBase):
    """User response schema."""

    id: int
    tenant_id: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]
    last_login: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)


class OptimizedUserResponse(BaseModel):
    """Optimized user response for performance-critical operations."""

    id: int
    email: str
    is_active: bool
    is_admin: bool = False
    tenant_id: Optional[int]
    full_name: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class LoginRequest(BaseModel):
    """Login request schema."""

    email: EmailStr
    password: str
    remember_me: bool = False


class LoginResponse(BaseModel):
    """Login response schema."""

    access_token: str
    token_type: str = "bearer"
    expires_in: int
    refresh_token: Optional[str] = None
    user: OptimizedUserResponse


class Token(BaseModel):
    """JWT token schema."""

    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    expires_in: int = 3600


class TokenData(BaseModel):
    """JWT token data schema."""

    sub: str  # User email/ID
    email: Optional[str] = None
    tenant_id: Optional[int] = None
    is_admin: bool = False
    exp: Optional[int] = None
    iat: Optional[int] = None


class TokenRefreshRequest(BaseModel):
    """Token refresh request schema."""

    refresh_token: str


class TokenRefreshResponse(BaseModel):
    """Token refresh response schema."""

    access_token: str
    token_type: str = "bearer"
    expires_in: int


class PasswordResetRequest(BaseModel):
    """Password reset request schema."""

    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """Password reset confirmation schema."""

    token: str
    new_password: str = Field(..., min_length=8)
    confirm_password: str

    @field_validator("confirm_password")
    @classmethod
    def passwords_match(cls, v, info):
        if "new_password" in info.data and v != info.data["new_password"]:
            raise ValueError("Passwords do not match")
        return v


class EmailVerificationRequest(BaseModel):
    """Email verification request schema."""

    email: EmailStr


class EmailVerificationConfirm(BaseModel):
    """Email verification confirmation schema."""

    token: str
    email: EmailStr


class UserListResponse(BaseModel):
    """User list response schema."""

    users: List[UserResponse]
    total: int
    page: int
    per_page: int


class AuthMetrics(BaseModel):
    """Authentication metrics schema."""

    total_users: int
    active_users: int
    admin_users: int
    recent_logins: int
    failed_login_attempts: int
    password_resets_requested: int


class UserProfile(BaseModel):
    """Extended user profile schema."""

    id: int
    email: str
    is_active: bool
    is_admin: bool
    tenant_id: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]
    last_login: Optional[datetime]
    login_count: int = 0
    preferences: Dict[str, Any] = {}

    model_config = ConfigDict(from_attributes=True)


class UserPreferencesUpdate(BaseModel):
    """User preferences update schema."""

    preferences: Dict[str, Any]


class TenantUserAssignment(BaseModel):
    """Tenant user assignment schema."""

    user_id: int
    tenant_id: int
    role: str = "user"  # user, admin, owner


class BulkUserOperation(BaseModel):
    """Bulk user operations schema."""

    user_ids: List[int]
    operation: str  # activate, deactivate, delete, assign_tenant
    parameters: Dict[str, Any] = {}


class AuthenticationStats(BaseModel):
    """Authentication statistics schema."""

    daily_logins: List[Dict[str, Any]]
    top_active_users: List[Dict[str, Any]]
    security_events: List[Dict[str, Any]]
    system_health: Dict[str, Any]


class PermissionCheck(BaseModel):
    """Permission check request schema."""

    user_id: int
    permission: str
    resource_id: Optional[str] = None


class PermissionResponse(BaseModel):
    """Permission check response schema."""

    has_permission: bool
    reason: Optional[str] = None
    expires_at: Optional[datetime] = None


class SessionInfo(BaseModel):
    """User session information schema."""

    session_id: str
    user_id: int
    created_at: datetime
    last_activity: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    is_active: bool = True


class ActiveSessionsResponse(BaseModel):
    """Active sessions response schema."""

    sessions: List[SessionInfo]
    total_sessions: int
    current_session_id: Optional[str] = None
