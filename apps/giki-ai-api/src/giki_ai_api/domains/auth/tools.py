"""
Authentication domain ADK tools.

This module provides ADK tool functions for authentication agent capabilities that can be used
by agents to manage user authentication, authorization, and account operations.
"""

import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, Optional

import asyncpg

from .service import get_auth_service

logger = logging.getLogger(__name__)


async def authenticate_user_tool(
    db: asyncpg.Connection,
    username: str,
    password: str,
) -> Dict[str, Any]:
    """
    ADK tool function to authenticate user with username and password.

    Args:
        db: Database connection
        username: User's email/username
        password: Plain text password

    Returns:
        Authentication result with user info and access token
    """
    try:
        service = get_auth_service(db)

        user = await service.authenticate_user(username, password)

        if not user:
            return {
                "success": False,
                "authenticated": False,
                "message": "Invalid username or password",
                "username": username,
            }

        # Create access token
        access_token = service.create_access_token(
            user_id=user.id,
            tenant_id=user.tenant_id,
        )

        return {
            "success": True,
            "authenticated": True,
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "email": user.email,
                "tenant_id": user.tenant_id,
                "full_name": user.full_name,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
            },
            "message": "Authentication successful",
        }

    except Exception as e:
        logger.error(f"Authentication failed for {username}: {e}")
        return {
            "success": False,
            "authenticated": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "username": username,
        }


async def create_user_tool(
    db: asyncpg.Connection,
    email: str,
    password: str,
    tenant_id: int,
    full_name: Optional[str] = None,
    is_active: bool = True,
    is_superuser: bool = False,
) -> Dict[str, Any]:
    """
    ADK tool function to create a new user account.

    Args:
        db: Database connection
        email: User's email address
        password: Plain text password
        tenant_id: Tenant ID for the user
        full_name: Optional full name
        is_active: Whether user is active
        is_superuser: Whether user has superuser privileges

    Returns:
        User creation result with user details
    """
    try:
        service = get_auth_service(db)

        user = await service.create_user(
            email=email,
            password=password,
            tenant_id=tenant_id,
            full_name=full_name,
            is_active=is_active,
            is_superuser=is_superuser,
        )

        return {
            "success": True,
            "user_created": True,
            "user": {
                "id": user.id,
                "email": user.email,
                "tenant_id": user.tenant_id,
                "full_name": user.full_name,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "created_at": user.created_at.isoformat() if user.created_at else None,
            },
            "message": f"User {email} created successfully",
        }

    except Exception as e:
        logger.error(f"Failed to create user {email}: {e}")
        return {
            "success": False,
            "user_created": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "email": email,
        }


async def get_user_by_id_tool(
    db: asyncpg.Connection,
    user_id: int,
) -> Dict[str, Any]:
    """
    ADK tool function to get user by ID.

    Args:
        db: Database connection
        user_id: User's ID

    Returns:
        User details if found
    """
    try:
        service = get_auth_service(db)

        user = await service.get_user_by_id(user_id)

        if not user:
            return {
                "success": False,
                "user_found": False,
                "message": f"User {user_id} not found",
                "user_id": user_id,
            }

        return {
            "success": True,
            "user_found": True,
            "user": {
                "id": user.id,
                "email": user.email,
                "tenant_id": user.tenant_id,
                "full_name": user.full_name,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            },
        }

    except Exception as e:
        logger.error(f"Failed to get user {user_id}: {e}")
        return {
            "success": False,
            "user_found": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "user_id": user_id,
        }


async def get_user_by_email_tool(
    db: asyncpg.Connection,
    email: str,
) -> Dict[str, Any]:
    """
    ADK tool function to get user by email address.

    Args:
        db: Database connection
        email: User's email address

    Returns:
        User details if found
    """
    try:
        service = get_auth_service(db)

        user = await service.get_user_by_email(email)

        if not user:
            return {
                "success": False,
                "user_found": False,
                "message": f"User with email {email} not found",
                "email": email,
            }

        return {
            "success": True,
            "user_found": True,
            "user": {
                "id": user.id,
                "email": user.email,
                "tenant_id": user.tenant_id,
                "full_name": user.full_name,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            },
        }

    except Exception as e:
        logger.error(f"Failed to get user by email {email}: {e}")
        return {
            "success": False,
            "user_found": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "email": email,
        }


async def validate_access_token_tool(
    db: asyncpg.Connection,
    access_token: str,
    tenant_id: Optional[int] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to validate access token and get user permissions.

    Args:
        db: Database connection
        access_token: JWT access token
        tenant_id: Optional tenant ID to validate against

    Returns:
        Token validation result with user info and permissions
    """
    try:
        service = get_auth_service(db)

        # Decode token to get user_id and tenant_id
        user_id, token_tenant_id = service.decode_access_token(access_token)

        # Use tenant_id from token if not provided
        if tenant_id is None:
            tenant_id = token_tenant_id

        # Validate user access
        access_info = await service.validate_user_access(user_id, tenant_id)

        return {
            "success": True,
            "token_valid": True,
            "user_info": access_info,
            "permissions": access_info["permissions"],
            "message": "Access token is valid",
        }

    except Exception as e:
        logger.error(f"Access token validation failed: {e}")
        return {
            "success": False,
            "token_valid": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "message": "Invalid or expired access token",
        }


async def update_user_tool(
    db: asyncpg.Connection,
    user_id: int,
    updates: Dict[str, Any],
) -> Dict[str, Any]:
    """
    ADK tool function to update user information.

    Args:
        db: Database connection
        user_id: User's ID
        updates: Dictionary of fields to update

    Returns:
        User update result with updated user info
    """
    try:
        service = get_auth_service(db)

        updated_user = await service.update_user(user_id, updates)

        if not updated_user:
            return {
                "success": False,
                "user_updated": False,
                "message": f"Failed to update user {user_id}",
                "user_id": user_id,
            }

        return {
            "success": True,
            "user_updated": True,
            "user": {
                "id": updated_user.id,
                "email": updated_user.email,
                "tenant_id": updated_user.tenant_id,
                "full_name": updated_user.full_name,
                "is_active": updated_user.is_active,
                "is_superuser": updated_user.is_superuser,
                "updated_at": updated_user.updated_at.isoformat()
                if updated_user.updated_at
                else None,
            },
            "updated_fields": list(updates.keys()),
            "message": f"User {user_id} updated successfully",
        }

    except Exception as e:
        logger.error(f"Failed to update user {user_id}: {e}")
        return {
            "success": False,
            "user_updated": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "user_id": user_id,
        }


async def deactivate_user_tool(
    db: asyncpg.Connection,
    user_id: int,
) -> Dict[str, Any]:
    """
    ADK tool function to deactivate user account (soft delete).

    Args:
        db: Database connection
        user_id: User's ID

    Returns:
        User deactivation result
    """
    try:
        service = get_auth_service(db)

        success = await service.delete_user(user_id)

        return {
            "success": success,
            "user_deactivated": success,
            "user_id": user_id,
            "message": f"User {user_id} deactivated successfully"
            if success
            else f"Failed to deactivate user {user_id}",
        }

    except Exception as e:
        logger.error(f"Failed to deactivate user {user_id}: {e}")
        return {
            "success": False,
            "user_deactivated": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "user_id": user_id,
        }


async def get_tenant_users_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    active_only: bool = True,
) -> Dict[str, Any]:
    """
    ADK tool function to get all users for a tenant.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        active_only: Whether to return only active users

    Returns:
        List of users for the tenant
    """
    try:
        service = get_auth_service(db)

        users = await service.get_users_by_tenant(tenant_id, active_only)

        return {
            "success": True,
            "tenant_id": tenant_id,
            "user_count": len(users),
            "active_only": active_only,
            "users": [
                {
                    "id": user.id,
                    "email": user.email,
                    "full_name": user.full_name,
                    "is_active": user.is_active,
                    "is_superuser": user.is_superuser,
                    "created_at": user.created_at.isoformat()
                    if user.created_at
                    else None,
                }
                for user in users
            ],
        }

    except Exception as e:
        logger.error(f"Failed to get users for tenant {tenant_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tenant_id": tenant_id,
            "users": [],
            "user_count": 0,
        }


async def change_password_tool(
    db: asyncpg.Connection,
    user_id: int,
    current_password: str,
    new_password: str,
) -> Dict[str, Any]:
    """
    ADK tool function to change user password with current password verification.

    Args:
        db: Database connection
        user_id: User's ID
        current_password: Current password for verification
        new_password: New password to set

    Returns:
        Password change result
    """
    try:
        service = get_auth_service(db)

        # Get user to verify current password
        user = await service.get_user_by_id(user_id)
        if not user:
            return {
                "success": False,
                "password_changed": False,
                "message": f"User {user_id} not found",
                "user_id": user_id,
            }

        # Verify current password
        if not service.verify_password(current_password, user.hashed_password):
            return {
                "success": False,
                "password_changed": False,
                "message": "Current password is incorrect",
                "user_id": user_id,
            }

        # Update password
        updated_user = await service.update_user(user_id, {"password": new_password})

        if not updated_user:
            return {
                "success": False,
                "password_changed": False,
                "message": "Failed to update password",
                "user_id": user_id,
            }

        return {
            "success": True,
            "password_changed": True,
            "user_id": user_id,
            "message": "Password changed successfully",
            "updated_at": updated_user.updated_at.isoformat()
            if updated_user.updated_at
            else None,
        }

    except Exception as e:
        logger.error(f"Failed to change password for user {user_id}: {e}")
        return {
            "success": False,
            "password_changed": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "user_id": user_id,
        }


async def create_access_token_tool(
    db: asyncpg.Connection,
    user_id: int,
    tenant_id: Optional[int] = None,
    expires_hours: Optional[int] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to create access token for user.

    Args:
        db: Database connection
        user_id: User's ID
        tenant_id: Optional tenant ID
        expires_hours: Optional custom expiration in hours

    Returns:
        Access token creation result
    """
    try:
        service = get_auth_service(db)

        # Validate user exists
        user = await service.get_user_by_id(user_id)
        if not user:
            return {
                "success": False,
                "token_created": False,
                "message": f"User {user_id} not found",
                "user_id": user_id,
            }

        # Use user's tenant_id if not specified
        if tenant_id is None:
            tenant_id = user.tenant_id

        # Set custom expiration if specified
        expires_delta = timedelta(hours=expires_hours) if expires_hours else None

        # Create token
        access_token = service.create_access_token(
            user_id=user_id,
            tenant_id=tenant_id,
            expires_delta=expires_delta,
        )

        return {
            "success": True,
            "token_created": True,
            "access_token": access_token,
            "token_type": "bearer",
            "user_id": user_id,
            "tenant_id": tenant_id,
            "expires_hours": expires_hours,
            "message": "Access token created successfully",
        }

    except Exception as e:
        logger.error(f"Failed to create access token for user {user_id}: {e}")
        return {
            "success": False,
            "token_created": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "user_id": user_id,
        }
