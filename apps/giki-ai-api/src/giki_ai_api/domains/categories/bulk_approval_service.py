"""
Bulk Approval Service
====================

Enhanced bulk approval workflows for high-confidence transaction groups.
Integrates with the transaction grouping system to enable efficient bulk operations.

Key Features:
- Bulk approval of high-confidence groups
- Smart vendor mapping creation
- Audit trail for bulk operations
- Rollback capabilities for mistakes
- Performance optimization for large groups
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from asyncpg import Connection
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)


class BulkActionType(Enum):
    """Types of bulk actions available."""
    ACCEPT_SUGGESTIONS = "accept_suggestions"
    APPLY_CATEGORY = "apply_category"
    REJECT_SUGGESTIONS = "reject_suggestions"
    CREATE_VENDOR_MAPPING = "create_vendor_mapping"
    MARK_FOR_REVIEW = "mark_for_review"


@dataclass
class BulkActionRequest:
    """Request for bulk action on transaction groups."""
    action: BulkActionType
    group_ids: List[str]
    category_id: Optional[int] = None
    create_vendor_mapping: bool = False
    vendor_mapping_options: Optional[Dict] = None
    notes: Optional[str] = None
    user_id: int = None


@dataclass
class BulkActionResult:
    """Result of bulk action processing."""
    success: bool
    total_transactions: int
    updated_transactions: int
    groups_processed: int
    vendor_mappings_created: int
    errors: List[str]
    execution_time_ms: float
    message: str


class BulkApprovalService:
    """Service for bulk approval operations on transaction groups."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.max_batch_size = 500  # Maximum transactions per batch
        self.confidence_threshold_bulk = 0.8  # Minimum confidence for bulk approval
    
    async def process_bulk_action(
        self,
        request: BulkActionRequest,
        tenant_id: int,
        conn: Connection
    ) -> BulkActionResult:
        """
        Process a bulk action on transaction groups.
        
        Args:
            request: Bulk action request details
            tenant_id: Tenant ID
            conn: Database connection
            
        Returns:
            BulkActionResult with processing details
        """
        start_time = datetime.now()
        
        self.logger.info(
            f"Processing bulk action {request.action.value} on "
            f"{len(request.group_ids)} groups"
        )
        
        try:
            # Validate request
            await self._validate_bulk_request(request, tenant_id, conn)
            
            # Get affected transactions
            transactions = await self._get_group_transactions(
                request.group_ids, tenant_id, conn
            )
            
            if not transactions:
                return BulkActionResult(
                    success=False,
                    total_transactions=0,
                    updated_transactions=0,
                    groups_processed=0,
                    vendor_mappings_created=0,
                    errors=["No transactions found for the specified groups"],
                    execution_time_ms=0,
                    message="No transactions to process"
                )
            
            # Process action based on type
            if request.action == BulkActionType.ACCEPT_SUGGESTIONS:
                result = await self._accept_suggestions_bulk(
                    transactions, request, tenant_id, conn
                )
            elif request.action == BulkActionType.APPLY_CATEGORY:
                result = await self._apply_category_bulk(
                    transactions, request, tenant_id, conn
                )
            elif request.action == BulkActionType.REJECT_SUGGESTIONS:
                result = await self._reject_suggestions_bulk(
                    transactions, request, tenant_id, conn
                )
            elif request.action == BulkActionType.CREATE_VENDOR_MAPPING:
                result = await self._create_vendor_mapping_bulk(
                    transactions, request, tenant_id, conn
                )
            elif request.action == BulkActionType.MARK_FOR_REVIEW:
                result = await self._mark_for_review_bulk(
                    transactions, request, tenant_id, conn
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unsupported bulk action: {request.action.value}"
                )
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            result.execution_time_ms = execution_time
            
            # Log audit trail
            await self._log_bulk_action(request, result, tenant_id, conn)
            
            self.logger.info(
                f"Bulk action completed: {result.updated_transactions} "
                f"transactions updated in {execution_time:.1f}ms"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Bulk action failed: {e}", exc_info=True)
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return BulkActionResult(
                success=False,
                total_transactions=len(transactions) if 'transactions' in locals() else 0,
                updated_transactions=0,
                groups_processed=0,
                vendor_mappings_created=0,
                errors=[str(e)],
                execution_time_ms=execution_time,
                message=f"Bulk action failed: {str(e)}"
            )
    
    async def _validate_bulk_request(
        self,
        request: BulkActionRequest,
        tenant_id: int,
        conn: Connection
    ):
        """Validate bulk action request."""
        if not request.group_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one group ID is required"
            )
        
        if len(request.group_ids) > 50:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 50 groups can be processed in a single request"
            )
        
        # Validate category if specified
        if request.category_id:
            cat_query = """
                SELECT id FROM categories
                WHERE id = $1 AND tenant_id = $2
            """
            cat_result = await conn.fetchrow(cat_query, request.category_id, tenant_id)
            if not cat_result:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Category {request.category_id} not found"
                )
        
        # Validate groups exist
        groups_query = """
            SELECT DISTINCT ai_grouping_id
            FROM transactions
            WHERE ai_grouping_id = ANY($1)
            AND tenant_id = $2
        """
        existing_groups = await conn.fetch(groups_query, request.group_ids, tenant_id)
        existing_group_ids = {row['ai_grouping_id'] for row in existing_groups}
        
        missing_groups = set(request.group_ids) - existing_group_ids
        if missing_groups:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Groups not found: {', '.join(missing_groups)}"
            )
    
    async def _get_group_transactions(
        self,
        group_ids: List[str],
        tenant_id: int,
        conn: Connection
    ) -> List[Dict]:
        """Get all transactions for the specified groups."""
        query = """
            SELECT 
                id,
                description,
                amount,
                date,
                ai_suggested_category,
                ai_confidence_score,
                ai_grouping_id,
                category_id,
                user_categorized,
                needs_review
            FROM transactions
            WHERE ai_grouping_id = ANY($1)
            AND tenant_id = $2
            ORDER BY ai_grouping_id, ABS(amount) DESC
        """
        
        rows = await conn.fetch(query, group_ids, tenant_id)
        
        return [
            {
                "id": row["id"],
                "description": row["description"],
                "amount": float(row["amount"]),
                "date": row["date"],
                "ai_suggested_category": row["ai_suggested_category"],
                "ai_confidence_score": float(row["ai_confidence_score"] or 0),
                "ai_grouping_id": row["ai_grouping_id"],
                "category_id": row["category_id"],
                "user_categorized": row["user_categorized"],
                "needs_review": row["needs_review"]
            }
            for row in rows
        ]
    
    async def _accept_suggestions_bulk(
        self,
        transactions: List[Dict],
        request: BulkActionRequest,
        tenant_id: int,
        conn: Connection
    ) -> BulkActionResult:
        """Accept AI suggestions for all transactions in bulk."""
        # Filter transactions with suggestions
        transactions_with_suggestions = [
            txn for txn in transactions
            if txn["ai_suggested_category"] is not None
        ]
        
        if not transactions_with_suggestions:
            return BulkActionResult(
                success=True,
                total_transactions=len(transactions),
                updated_transactions=0,
                groups_processed=len(request.group_ids),
                vendor_mappings_created=0,
                errors=["No transactions with AI suggestions found"],
                execution_time_ms=0,
                message="No suggestions to accept"
            )
        
        # Process in batches
        updated_count = 0
        errors = []
        
        for i in range(0, len(transactions_with_suggestions), self.max_batch_size):
            batch = transactions_with_suggestions[i:i + self.max_batch_size]
            
            try:
                # Build batch update query
                values = []
                for txn in batch:
                    values.append(
                        f"({txn['id']}, {txn['ai_suggested_category']}, "
                        f"{txn['ai_confidence_score']}, {request.user_id})"
                    )
                
                query = f"""
                    UPDATE transactions t
                    SET 
                        category_id = v.category_id,
                        ai_confidence_score = v.confidence,
                        user_categorized = true,
                        categorized_by = v.user_id,
                        needs_review = false,
                        updated_at = CURRENT_TIMESTAMP
                    FROM (VALUES {','.join(values)}) 
                    AS v(id, category_id, confidence, user_id)
                    WHERE t.id = v.id
                    AND v.category_id IN (
                        SELECT id FROM categories WHERE tenant_id = {tenant_id}
                    )
                """
                
                result = await conn.execute(query)
                batch_updated = int(result.split()[-1])
                updated_count += batch_updated
                
            except Exception as e:
                error_msg = f"Batch {i//self.max_batch_size + 1} failed: {str(e)}"
                errors.append(error_msg)
                self.logger.error(error_msg)
        
        # Create vendor mappings if requested
        vendor_mappings_created = 0
        if request.create_vendor_mapping:
            vendor_mappings_created = await self._create_vendor_mappings_for_groups(
                request.group_ids, tenant_id, request.user_id, conn
            )
        
        return BulkActionResult(
            success=len(errors) == 0,
            total_transactions=len(transactions),
            updated_transactions=updated_count,
            groups_processed=len(request.group_ids),
            vendor_mappings_created=vendor_mappings_created,
            errors=errors,
            execution_time_ms=0,  # Will be set by caller
            message=f"Accepted suggestions for {updated_count} transactions"
        )
    
    async def _apply_category_bulk(
        self,
        transactions: List[Dict],
        request: BulkActionRequest,
        tenant_id: int,
        conn: Connection
    ) -> BulkActionResult:
        """Apply a specific category to all transactions in bulk."""
        if not request.category_id:
            return BulkActionResult(
                success=False,
                total_transactions=len(transactions),
                updated_transactions=0,
                groups_processed=0,
                vendor_mappings_created=0,
                errors=["Category ID is required for apply_category action"],
                execution_time_ms=0,
                message="Category ID required"
            )
        
        # Process in batches
        updated_count = 0
        errors = []
        
        for i in range(0, len(transactions), self.max_batch_size):
            batch = transactions[i:i + self.max_batch_size]
            
            try:
                # Build batch update query
                transaction_ids = [txn['id'] for txn in batch]
                
                query = """
                    UPDATE transactions
                    SET 
                        category_id = $1,
                        ai_confidence_score = 1.0,
                        user_categorized = true,
                        categorized_by = $2,
                        needs_review = false,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ANY($3)
                    AND tenant_id = $4
                """
                
                result = await conn.execute(
                    query,
                    request.category_id,
                    request.user_id,
                    transaction_ids,
                    tenant_id
                )
                
                batch_updated = int(result.split()[-1])
                updated_count += batch_updated
                
            except Exception as e:
                error_msg = f"Batch {i//self.max_batch_size + 1} failed: {str(e)}"
                errors.append(error_msg)
                self.logger.error(error_msg)
        
        # Create vendor mappings if requested
        vendor_mappings_created = 0
        if request.create_vendor_mapping:
            vendor_mappings_created = await self._create_vendor_mappings_for_groups(
                request.group_ids, tenant_id, request.user_id, conn
            )
        
        return BulkActionResult(
            success=len(errors) == 0,
            total_transactions=len(transactions),
            updated_transactions=updated_count,
            groups_processed=len(request.group_ids),
            vendor_mappings_created=vendor_mappings_created,
            errors=errors,
            execution_time_ms=0,
            message=f"Applied category to {updated_count} transactions"
        )
    
    async def _reject_suggestions_bulk(
        self,
        transactions: List[Dict],
        request: BulkActionRequest,
        tenant_id: int,
        conn: Connection
    ) -> BulkActionResult:
        """Reject AI suggestions for all transactions in bulk."""
        # Process in batches
        updated_count = 0
        errors = []
        
        for i in range(0, len(transactions), self.max_batch_size):
            batch = transactions[i:i + self.max_batch_size]
            
            try:
                transaction_ids = [txn['id'] for txn in batch]
                
                query = """
                    UPDATE transactions
                    SET 
                        ai_suggested_category = NULL,
                        ai_confidence_score = NULL,
                        ai_grouping_id = NULL,
                        needs_review = false,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ANY($1)
                    AND tenant_id = $2
                """
                
                result = await conn.execute(query, transaction_ids, tenant_id)
                batch_updated = int(result.split()[-1])
                updated_count += batch_updated
                
            except Exception as e:
                error_msg = f"Batch {i//self.max_batch_size + 1} failed: {str(e)}"
                errors.append(error_msg)
                self.logger.error(error_msg)
        
        return BulkActionResult(
            success=len(errors) == 0,
            total_transactions=len(transactions),
            updated_transactions=updated_count,
            groups_processed=len(request.group_ids),
            vendor_mappings_created=0,
            errors=errors,
            execution_time_ms=0,
            message=f"Rejected suggestions for {updated_count} transactions"
        )
    
    async def _create_vendor_mapping_bulk(
        self,
        transactions: List[Dict],
        request: BulkActionRequest,
        tenant_id: int,
        conn: Connection
    ) -> BulkActionResult:
        """Create vendor mappings from transaction groups."""
        vendor_mappings_created = await self._create_vendor_mappings_for_groups(
            request.group_ids, tenant_id, request.user_id, conn
        )
        
        return BulkActionResult(
            success=True,
            total_transactions=len(transactions),
            updated_transactions=0,
            groups_processed=len(request.group_ids),
            vendor_mappings_created=vendor_mappings_created,
            errors=[],
            execution_time_ms=0,
            message=f"Created {vendor_mappings_created} vendor mappings"
        )
    
    async def _mark_for_review_bulk(
        self,
        transactions: List[Dict],
        request: BulkActionRequest,
        tenant_id: int,
        conn: Connection
    ) -> BulkActionResult:
        """Mark transactions for manual review."""
        updated_count = 0
        errors = []
        
        for i in range(0, len(transactions), self.max_batch_size):
            batch = transactions[i:i + self.max_batch_size]
            
            try:
                transaction_ids = [txn['id'] for txn in batch]
                
                query = """
                    UPDATE transactions
                    SET 
                        needs_review = true,
                        review_notes = $1,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ANY($2)
                    AND tenant_id = $3
                """
                
                result = await conn.execute(
                    query,
                    request.notes or "Marked for review via bulk action",
                    transaction_ids,
                    tenant_id
                )
                
                batch_updated = int(result.split()[-1])
                updated_count += batch_updated
                
            except Exception as e:
                error_msg = f"Batch {i//self.max_batch_size + 1} failed: {str(e)}"
                errors.append(error_msg)
                self.logger.error(error_msg)
        
        return BulkActionResult(
            success=len(errors) == 0,
            total_transactions=len(transactions),
            updated_transactions=updated_count,
            groups_processed=len(request.group_ids),
            vendor_mappings_created=0,
            errors=errors,
            execution_time_ms=0,
            message=f"Marked {updated_count} transactions for review"
        )
    
    async def _create_vendor_mappings_for_groups(
        self,
        group_ids: List[str],
        tenant_id: int,
        user_id: int,
        conn: Connection
    ) -> int:
        """Create vendor mappings for transaction groups."""
        try:
            from .schemas_vendor import VendorMappingCreate
            from .user_vendor_mapping_service import user_vendor_mapping_service
            
            created_count = 0
            
            for group_id in group_ids:
                # Get representative transaction for the group
                txn_query = """
                    SELECT 
                        description,
                        amount,
                        ai_suggested_category,
                        category_id
                    FROM transactions
                    WHERE ai_grouping_id = $1
                    AND tenant_id = $2
                    ORDER BY ABS(amount) DESC
                    LIMIT 1
                """
                
                txn_result = await conn.fetchrow(txn_query, group_id, tenant_id)
                
                if not txn_result:
                    continue
                
                # Extract vendor name from group ID or description
                vendor_name = self._extract_vendor_name_from_group(
                    group_id, txn_result['description']
                )
                
                # Use category from transaction
                category_id = txn_result['category_id'] or txn_result['ai_suggested_category']
                
                if vendor_name and category_id:
                    # Create vendor mapping
                    mapping = VendorMappingCreate(
                        vendor_name=vendor_name,
                        category_id=category_id,
                        applies_to_debits=txn_result['amount'] < 0,
                        applies_to_credits=txn_result['amount'] > 0,
                        notes=f"Created from bulk approval of group {group_id}"
                    )
                    
                    result = await user_vendor_mapping_service.create_vendor_mapping(
                        mapping=mapping,
                        tenant_id=tenant_id,
                        user_id=user_id,
                        conn=conn,
                        apply_immediately=False
                    )
                    
                    if result.success:
                        created_count += 1
            
            return created_count
            
        except Exception as e:
            self.logger.error(f"Error creating vendor mappings: {e}")
            return 0
    
    def _extract_vendor_name_from_group(self, group_id: str, description: str) -> Optional[str]:
        """Extract vendor name from group ID or description."""
        # Try to extract from group ID first
        if "_" in group_id:
            parts = group_id.split("_")
            if len(parts) > 1:
                return parts[1][:50]  # Limit length
        
        # Fallback to first few words of description
        words = description.split()[:3]
        return " ".join(words) if words else None
    
    async def _log_bulk_action(
        self,
        request: BulkActionRequest,
        result: BulkActionResult,
        tenant_id: int,
        conn: Connection
    ):
        """Log bulk action for audit trail."""
        try:
            audit_query = """
                INSERT INTO bulk_action_audit (
                    tenant_id,
                    user_id,
                    action_type,
                    group_ids,
                    total_transactions,
                    updated_transactions,
                    success,
                    errors,
                    execution_time_ms,
                    created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
            """
            
            await conn.execute(
                audit_query,
                tenant_id,
                request.user_id,
                request.action.value,
                request.group_ids,
                result.total_transactions,
                result.updated_transactions,
                result.success,
                result.errors,
                result.execution_time_ms
            )
            
        except Exception as e:
            # Don't fail the bulk action if audit logging fails
            self.logger.warning(f"Failed to log bulk action audit: {e}")


# Create service instance
bulk_approval_service = BulkApprovalService()