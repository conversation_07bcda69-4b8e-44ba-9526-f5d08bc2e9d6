"""
Categories router for category management endpoints.
"""

import logging
from typing import Any, Dict, List

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, status

from ...core.dependencies import get_current_tenant_id, get_db_session
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .categorization_metrics import CategorizationMetricsService
from .enhancement_detection_service import enhancement_detection_service
from .mis_validation_service import MISValidationService
from .models import Category as CategoryModel
from .progressive_setup_service import progressive_setup_service
from .schemas import (
    Category,
    CategoryCreate,
    CategoryTree,
    CategoryUpdate,
    CategoryVisualization,
    EnhancementAnalysisResponse,
    EnhancementOpportunity,
    HierarchicalResults,
)
from .schemas_vendor import (
    BulkVendorMappingRequest,
    VendorGrouping,
    VendorMapping,
    VendorMappingCreate,
    VendorMappingResult,
    VendorMappingUpdate,
)
from .service import CategoryService
from .user_vendor_mapping_service import user_vendor_mapping_service
from .vendor_detection_service import vendor_detection_service
from .vendor_lookup_service import vendor_lookup_service

logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["Categories"],
    responses={
        404: {"description": "Not found"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized"},
    },
)


async def get_category_service(
    conn: Connection = Depends(get_db_session),
) -> CategoryService:
    """Dependency to get CategoryService instance."""
    return CategoryService(conn)


async def get_multilevel_category_service(
    conn: Connection = Depends(get_db_session),
) -> CategoryService:
    """Dependency to get CategoryService instance with GL code support."""
    return CategoryService(conn)


@router.get("", response_model=list[CategoryTree])
async def read_tenant_categories(
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
    include_usage_counts: bool = False,  # Default to False for better performance
):
    """
    Read all categories for the current tenant.
    OPTIMIZED: Added performance monitoring to track slow queries.
    """
    logger.info(
        f"Reading categories for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        import time

        start_time = time.time()

        categories = await category_service.get_categories(
            tenant_id_int, include_usage_counts=include_usage_counts
        )

        execution_time = (time.time() - start_time) * 1000
        logger.info(
            f"Categories query executed in {execution_time:.2f}ms, returned {len(categories)} categories"
        )

        # Warn if query is slow - use consistent 400ms threshold for standard operations
        if (
            execution_time > 400
        ):  # 400ms threshold consistent with performance middleware
            logger.warning(
                f"Slow query detected: read_tenant_categories took {execution_time:.2f}ms (target: <400ms)"
            )

        return categories
    except Exception as e:
        logger.error(
            f"Error retrieving categories for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving categories: {str(e)}",
        )


@router.get("/with-counts", response_model=list[CategoryTree])
async def read_tenant_categories_with_counts(
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    """
    Read all categories for the current tenant WITH usage counts.
    PERFORMANCE: Separate endpoint for when counts are actually needed.
    """
    logger.info(
        f"Reading categories WITH COUNTS for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        import time

        start_time = time.time()

        categories = await category_service.get_categories(
            tenant_id_int, include_usage_counts=True
        )

        execution_time = (time.time() - start_time) * 1000
        logger.info(
            f"Categories with counts query executed in {execution_time:.2f}ms, returned {len(categories)} categories"
        )

        # Warn if query is slow
        if execution_time > 800:  # Higher threshold for counts query
            logger.warning(
                f"Slow query detected: read_tenant_categories_with_counts took {execution_time:.2f}ms (target: <800ms)"
            )

        return categories
    except Exception as e:
        logger.error(
            f"Error retrieving categories with counts for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving categories: {str(e)}",
        )


@router.get("/{category_id}", response_model=CategoryTree)
async def read_category(
    category_id: int,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    logger.info(
        f"Reading category {category_id} for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        category = await category_service.get_category_by_id(category_id, tenant_id_int)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Category not found"
            )
        return category
    except Exception as e:
        logger.error(f"Error reading category {category_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reading category: {str(e)}",
        )


@router.post("", response_model=Category, status_code=status.HTTP_201_CREATED)
async def create_category(
    category_in: CategoryCreate,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    logger.info(
        f"Creating category '{category_in.name}' for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        # Get the database connection from the service
        db_conn = category_service.conn

        # Pass the CategoryCreate object and db connection to the service
        created_category = await category_service.create_category(
            db=db_conn,
            category_data=category_in,
            tenant_id=tenant_id_int,
            user_id=current_user.id,
        )
        return created_category
    except Exception as e:
        logger.error(
            f"Error creating category '{category_in.name}' for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating category: {str(e)}",
        )


@router.put("/{category_id}", response_model=Category)
async def update_category(
    category_id: int,
    category_in: CategoryUpdate,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    logger.info(
        f"Updating category {category_id} for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        # Pass the CategoryUpdate object directly to the service
        updated_category = await category_service.update_category(
            category_id=category_id,
            update_data=category_in,
            user_id=current_user.id,
        )
        if not updated_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Category not found"
            )
        return updated_category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error updating category {category_id} for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating category: {str(e)}",
        )


@router.delete("/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_category(
    category_id: int,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
):
    logger.info(
        f"Deleting category {category_id} for tenant ID: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        # Delete category without reassigning (force_delete=True for now)
        deleted = await category_service.delete_category(
            category_id=category_id, force_delete=True
        )
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Category not found"
            )
    except ValueError as e:
        # Handle children constraint error
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error deleting category {category_id} for tenant {tenant_id_int}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting category: {str(e)}",
        )


async def _build_category_tree(
    category_model: CategoryModel,
    conn: Connection,
) -> CategoryTree:
    # Query children using raw SQL
    query = """
        SELECT id, name, path, parent_id, tenant_id, gl_code, gl_account_name, 
               gl_account_type, level, created_at, updated_at
        FROM categories
        WHERE parent_id = $1
        ORDER BY name
    """
    rows = await conn.fetch(query, category_model.id)

    child_trees = []
    for row in rows:
        child_model = CategoryModel(**dict(row))
        child_tree = await _build_category_tree(child_model, conn)
        child_trees.append(child_tree)

    return CategoryTree(
        id=category_model.id,
        name=category_model.name,
        path=category_model.path,
        parent_id=category_model.parent_id,
        tenant_id=category_model.tenant_id or 0,  # Handle nullable tenant_id
        children=child_trees,
    )


async def _update_category_paths(
    category_model: CategoryModel, conn: Connection
) -> None:
    if category_model.parent_id is not None:
        # Get parent using raw SQL
        parent_query = """
            SELECT id, name, path, parent_id, tenant_id 
            FROM categories 
            WHERE id = $1
        """
        parent_row = await conn.fetchrow(parent_query, category_model.parent_id)
        if parent_row:
            parent_path = parent_row["path"]
            category_model.path = f"{parent_path} - {category_model.name}"
        else:
            logger.warning(
                f"Parent category ID {category_model.parent_id} not found for category ID {category_model.id} during path update. Path may be incorrect."
            )
            category_model.path = category_model.name
    else:
        category_model.path = category_model.name

    # Update the category path in database
    update_query = """
        UPDATE categories 
        SET path = $1, updated_at = NOW() 
        WHERE id = $2
    """
    await conn.execute(update_query, category_model.path, category_model.id)

    # Get children using raw SQL
    children_query = """
        SELECT id, name, path, parent_id, tenant_id, gl_code, gl_account_name, 
               gl_account_type, level, created_at, updated_at
        FROM categories 
        WHERE parent_id = $1
    """
    children_rows = await conn.fetch(children_query, category_model.id)

    for child_row in children_rows:
        child_model = CategoryModel(**dict(child_row))
        await _update_category_paths(child_model, conn)


# ============================================================================
# GL CODE MANAGEMENT ENDPOINTS (Multilevel Category System)
# ============================================================================

from typing import Optional

from pydantic import BaseModel


class GLCodeUpdate(BaseModel):
    """Schema for updating GL code mapping."""

    gl_code: Optional[str] = None
    gl_account_name: Optional[str] = None
    gl_account_type: Optional[str] = None


class BulkGLUpdate(BaseModel):
    """Schema for bulk GL code updates."""

    mappings: List[Dict[str, Any]]


class GLMappingExport(BaseModel):
    """Schema for GL mapping export request."""

    format_type: str = "csv"


class GLCodeValidationRequest(BaseModel):
    """Schema for GL code validation request."""

    gl_code: str


class GLCodeSuggestionRequest(BaseModel):
    """Schema for GL code suggestion request."""

    category_name: str
    category_path: str
    account_type: Optional[str] = None


@router.post("/validate-gl-code")
async def validate_gl_code(
    request: GLCodeValidationRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Validate GL code format and check for duplicates.

    Performs format validation and duplicate checking for GL codes
    within the tenant's scope.
    """
    logger.info(
        f"Validating GL code for tenant: {tenant_id_int}, user: {current_user.id}"
    )

    try:
        validation_result = await category_service.validate_gl_code(
            request.gl_code, tenant_id_int
        )
        return validation_result
    except Exception as e:
        logger.error(f"Error validating GL code: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating GL code: {str(e)}",
        )


@router.post("/suggest-gl-codes")
async def suggest_gl_codes(
    request: GLCodeSuggestionRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Get AI-powered GL code suggestions for a category.

    Uses AI to suggest appropriate GL codes based on category name,
    path in hierarchy, and account type.
    """
    logger.info(
        f"Getting GL code suggestions for tenant: {tenant_id_int}, user: {current_user.id}"
    )

    try:
        suggestions = await category_service.suggest_gl_codes(
            category_name=request.category_name,
            category_path=request.category_path,
            account_type=request.account_type,
            tenant_id=tenant_id_int,
        )
        return suggestions
    except Exception as e:
        logger.error(f"Error getting GL code suggestions: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting GL code suggestions: {str(e)}",
        )


@router.put("/{category_id}/gl-mapping", response_model=Category)
async def update_category_gl_mapping(
    category_id: int,
    gl_update: GLCodeUpdate,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Update GL code mapping for a category.

    This endpoint supports the core business requirement that categories map to
    GL (General Ledger) codes for accounting integration.
    """
    logger.info(
        f"Updating GL mapping for category {category_id}, tenant: {tenant_id_int}, user: {current_user.id}"
    )
    try:
        updated_category = await category_service.update_gl_code_mapping(
            category_id=category_id,
            gl_code=gl_update.gl_code,
            gl_account_name=gl_update.gl_account_name,
            gl_account_type=gl_update.gl_account_type,
            user_id=current_user.id,
        )
        return updated_category

    except Exception as e:
        logger.error(
            f"Error updating GL mapping for category {category_id}: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating GL mapping: {str(e)}",
        )


@router.post("/bulk-gl-update", response_model=Dict[str, Any])
async def bulk_update_gl_mappings(
    bulk_update: BulkGLUpdate,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Bulk update GL code mappings for multiple categories.

    Allows customers to update GL codes and category structures via UI.
    """
    logger.info(
        f"Bulk updating GL mappings for {len(bulk_update.mappings)} categories, tenant: {tenant_id_int}"
    )
    try:
        result = await category_service.bulk_update_gl_mappings(
            mappings=bulk_update.mappings,
            tenant_id=tenant_id_int,
            user_id=current_user.id,
        )
        return result

    except Exception as e:
        logger.error(f"Error in bulk GL mapping update: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating GL mappings: {str(e)}",
        )


@router.get("/gl-mappings/export")
async def export_gl_mappings(
    format_type: str = "csv",
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Export GL code mappings for accounting software integration.

    Supports integration with QuickBooks, SAP, Xero, and other accounting systems.
    """
    logger.info(
        f"Exporting GL mappings in {format_type} format for tenant: {tenant_id_int}"
    )
    try:
        export_data = await category_service.export_gl_mappings(
            tenant_id=tenant_id_int, format_type=format_type
        )

        if format_type == "csv":
            from fastapi.responses import Response

            return Response(
                content=export_data,
                media_type="text/csv",
                headers={
                    "Content-Disposition": f"attachment; filename=gl_mappings_tenant_{tenant_id_int}.csv"
                },
            )
        else:
            return {"data": export_data, "format": format_type}

    except Exception as e:
        logger.error(f"Error exporting GL mappings: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error exporting GL mappings: {str(e)}",
        )


@router.post("/learn-from-onboarding", response_model=Dict[str, Any])
async def learn_categories_from_onboarding(
    transactions: List[Dict[str, Any]],
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Learn multilevel category structure from onboarding transaction data.

    This implements the core business requirement that categories are ALWAYS
    multilevel and learned from onboarding data for each tenant.
    """
    logger.info(
        f"Learning categories from {len(transactions)} onboarding transactions for tenant: {tenant_id_int}"
    )
    try:
        learning_result = await category_service.learn_categories_from_onboarding_data(
            transactions=transactions, tenant_id=tenant_id_int, user_id=current_user.id
        )
        return learning_result

    except Exception as e:
        logger.error(
            f"Error learning categories from onboarding data: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error learning categories: {str(e)}",
        )


@router.get("/hierarchy", response_model=Dict[str, Any])
async def get_category_hierarchy(
    include_usage_counts: bool = True,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_multilevel_category_service),
):
    """
    Get complete multilevel category hierarchy for a tenant.

    Returns hierarchical tree structure with GL code information and usage statistics.
    """
    logger.info(f"Getting category hierarchy for tenant: {tenant_id_int}")
    try:
        hierarchy_tree = await category_service.get_category_hierarchy(
            tenant_id=tenant_id_int, include_usage_counts=include_usage_counts
        )

        return {
            "root_categories": hierarchy_tree.root_categories,
            "total_categories": hierarchy_tree.total_categories,
            "max_depth": hierarchy_tree.max_depth,
            "category_counts": hierarchy_tree.category_counts,
        }

    except Exception as e:
        logger.error(f"Error getting category hierarchy: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving category hierarchy: {str(e)}",
        )


@router.post("/create-multilevel-hierarchies")
async def create_multilevel_hierarchies(
    clear_existing: bool = False,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
    conn: Connection = Depends(get_db_session),
):
    """
    Create multilevel category hierarchies from customer transaction data.

    This endpoint:
    1. Loads customer transaction data from input files
    2. Analyzes original categorization patterns
    3. Detects hierarchical relationships
    4. Creates parent-child category structures
    5. Returns statistics on created hierarchies

    Args:
        clear_existing: Whether to clear existing categories first
    """
    try:
        logger.info(f"Creating multilevel hierarchies for tenant {tenant_id}")

        # Load customer transaction data from database
        # Query existing transactions for this tenant with original category labels
        query = """
            SELECT t.description, t.original_category_label as category, t.amount 
            FROM transactions t 
            WHERE t.tenant_id = $1 
            AND t.original_category_label IS NOT NULL
            AND t.original_category_label != ''
        """
        transactions = await conn.fetch(query, tenant_id)

        if not transactions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No customer transaction data found. Please upload data files first.",
            )

        # Convert to the expected format
        customer_transactions = []
        for transaction in transactions:
            if transaction["description"] and transaction["category"]:
                customer_transactions.append(
                    {
                        "description": str(transaction["description"]).strip(),
                        "category": str(transaction["category"]).strip(),
                        "amount": float(transaction["amount"])
                        if transaction["amount"]
                        else 0,
                        "source": "database",
                    }
                )

        logger.info(f"Total customer transactions loaded: {len(customer_transactions)}")

        # Create multilevel hierarchies
        result = (
            await category_service.create_multilevel_hierarchies_from_customer_data(
                tenant_id=tenant_id,
                customer_transactions=customer_transactions,
                clear_existing=clear_existing,
            )
        )

        return {
            "success": True,
            "message": f"Successfully created {result['total_categories_created']} categories",
            "statistics": {
                "total_categories": result["total_categories_created"],
                "parent_categories": result["parent_categories"],
                "child_categories": result["child_categories"],
                "hierarchies_detected": result["hierarchies_detected"],
                "unique_patterns": result["unique_patterns"],
                "data_source": "database_transactions",
                "total_transactions_analyzed": len(customer_transactions),
            },
            "categories_created": result["categories"][:20],  # First 20 for preview
        }

    except Exception as e:
        logger.error(f"Error creating multilevel hierarchies: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create multilevel hierarchies: {str(e)}",
        )


@router.post("/dynamic-hierarchies", response_model=Dict[str, Any])
async def build_dynamic_category_hierarchies(
    category_names: List[str],
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Build dynamic category hierarchies using business context and AI analysis.

    This endpoint creates intelligent category hierarchies based on:
    - Industry-specific templates
    - Company size considerations
    - AI-powered analysis of category relationships
    - Business context from onboarding

    Request body should contain a list of category names to organize.
    Returns suggested hierarchies without creating categories in the database.
    """
    logger.info(
        f"Building dynamic hierarchies for {len(category_names)} categories, tenant: {tenant_id_int}"
    )

    try:
        from .categorization_agent import CategorizationAgent

        # Initialize categorization agent
        agent = CategorizationAgent(conn=conn)

        # Build dynamic hierarchies
        hierarchies = await agent.build_dynamic_category_hierarchies(
            tenant_id=tenant_id_int,
            categories=set(category_names),
            business_context=None,  # Agent will fetch business context
        )

        # Get business context for response
        business_context = await agent.get_tenant_business_context(tenant_id_int)

        return {
            "success": True,
            "tenant_id": tenant_id_int,
            "business_context": {
                "industry": business_context.get("industry", ""),
                "company_size": business_context.get("company_size", ""),
                "company_website": business_context.get("company_website", ""),
            },
            "input_categories": category_names,
            "suggested_hierarchies": hierarchies,
            "statistics": {
                "total_hierarchies": len(hierarchies),
                "categories_organized": sum(
                    len(h.get("children", [])) for h in hierarchies
                ),
                "categories_input": len(category_names),
            },
            "usage_note": "These are suggested hierarchies. Use POST /categories/multilevel-hierarchies to actually create categories in the database.",
        }

    except Exception as e:
        logger.error(f"Error building dynamic hierarchies: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to build dynamic hierarchies: {str(e)}",
        )


# ============================================================================
# MIS VALIDATION ENDPOINTS
# ============================================================================


async def get_mis_validation_service(
    conn: Connection = Depends(get_db_session),
) -> MISValidationService:
    """Dependency to get MISValidationService instance."""
    return MISValidationService(conn)


@router.get("/validate/quick", response_model=Dict[str, Any])
async def quick_mis_validation(
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    validation_service: MISValidationService = Depends(get_mis_validation_service),
):
    """
    Quick MIS structure validation for dashboard display.

    Provides essential validation metrics for real-time dashboard updates
    without the overhead of comprehensive validation.
    """
    logger.info(
        f"Quick MIS validation for tenant: {tenant_id_int}, user: {current_user.id}"
    )

    try:
        validation_result = await validation_service.quick_validation_check(
            tenant_id_int
        )
        return validation_result
    except Exception as e:
        logger.error(f"Error in quick MIS validation: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating MIS structure: {str(e)}",
        )


@router.get("/validate/comprehensive", response_model=Dict[str, Any])
async def comprehensive_mis_validation(
    industry: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    validation_service: MISValidationService = Depends(get_mis_validation_service),
):
    """
    Comprehensive MIS structure validation with detailed report.

    Performs complete validation including:
    - Structure compliance
    - GL code validation
    - Business rules checking
    - Industry-specific compliance
    - Financial reporting readiness
    """
    logger.info(
        f"Comprehensive MIS validation for tenant: {tenant_id_int}, user: {current_user.id}, industry: {industry}"
    )

    try:
        validation_report = (
            await validation_service.validate_comprehensive_mis_structure(
                tenant_id_int, industry
            )
        )

        # Convert dataclass to dict for JSON response
        return {
            "tenant_id": validation_report.tenant_id,
            "overall_valid": validation_report.overall_valid,
            "validation_timestamp": validation_report.validation_timestamp.isoformat(),
            "structure_validation": {
                "is_valid": validation_report.structure_validation.is_valid,
                "validation_type": validation_report.structure_validation.validation_type,
                "issues": validation_report.structure_validation.issues,
                "warnings": validation_report.structure_validation.warnings,
                "recommendations": validation_report.structure_validation.recommendations,
                "severity": validation_report.structure_validation.severity,
            },
            "gl_code_validation": {
                "is_valid": validation_report.gl_code_validation.is_valid,
                "validation_type": validation_report.gl_code_validation.validation_type,
                "issues": validation_report.gl_code_validation.issues,
                "warnings": validation_report.gl_code_validation.warnings,
                "recommendations": validation_report.gl_code_validation.recommendations,
                "severity": validation_report.gl_code_validation.severity,
            },
            "business_rules_validation": {
                "is_valid": validation_report.business_rules_validation.is_valid,
                "validation_type": validation_report.business_rules_validation.validation_type,
                "issues": validation_report.business_rules_validation.issues,
                "warnings": validation_report.business_rules_validation.warnings,
                "recommendations": validation_report.business_rules_validation.recommendations,
                "severity": validation_report.business_rules_validation.severity,
            },
            "industry_compliance_validation": {
                "is_valid": validation_report.industry_compliance_validation.is_valid,
                "validation_type": validation_report.industry_compliance_validation.validation_type,
                "issues": validation_report.industry_compliance_validation.issues,
                "warnings": validation_report.industry_compliance_validation.warnings,
                "recommendations": validation_report.industry_compliance_validation.recommendations,
                "severity": validation_report.industry_compliance_validation.severity,
            },
            "financial_reporting_validation": {
                "is_valid": validation_report.financial_reporting_validation.is_valid,
                "validation_type": validation_report.financial_reporting_validation.validation_type,
                "issues": validation_report.financial_reporting_validation.issues,
                "warnings": validation_report.financial_reporting_validation.warnings,
                "recommendations": validation_report.financial_reporting_validation.recommendations,
                "severity": validation_report.financial_reporting_validation.severity,
            },
            "summary_statistics": validation_report.summary_statistics,
            "recommendations": validation_report.recommendations,
        }

    except Exception as e:
        logger.error(f"Error in comprehensive MIS validation: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating MIS structure: {str(e)}",
        )


@router.get("/validate/structure-health", response_model=Dict[str, Any])
async def get_structure_health_metrics(
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    validation_service: MISValidationService = Depends(get_mis_validation_service),
):
    """
    Get structure health metrics for enterprise dashboard display.

    Provides key health indicators that can be monitored over time
    and displayed in executive dashboards.
    """
    logger.info(
        f"Getting structure health metrics for tenant: {tenant_id_int}, user: {current_user.id}"
    )

    try:
        # Get quick validation for health status
        quick_validation = await validation_service.quick_validation_check(
            tenant_id_int
        )

        # Get additional health metrics
        from .mis_categorization_service import MISCategorizationService

        mis_service = MISCategorizationService(validation_service.db)
        basic_validation = await mis_service.validate_mis_structure(tenant_id_int)

        health_score = 100
        health_issues = []

        # Calculate health score based on validation results
        if not quick_validation["is_valid"]:
            health_score -= 30
            health_issues.append("Critical structure issues detected")

        if quick_validation["missing_gl_codes"] > 0:
            missing_percentage = (
                quick_validation["missing_gl_codes"]
                / basic_validation["statistics"]["total_categories"]
            ) * 100
            if missing_percentage > 20:
                health_score -= 25
                health_issues.append(
                    f"{missing_percentage:.1f}% categories missing GL codes"
                )
            elif missing_percentage > 10:
                health_score -= 15
                health_issues.append(
                    f"{missing_percentage:.1f}% categories missing GL codes"
                )

        if quick_validation["categorization_rate"] < 80:
            health_score -= 20
            health_issues.append(
                f"Low categorization rate: {quick_validation['categorization_rate']}%"
            )

        # Determine health status
        if health_score >= 90:
            health_status = "excellent"
        elif health_score >= 75:
            health_status = "good"
        elif health_score >= 60:
            health_status = "fair"
        else:
            health_status = "poor"

        return {
            "tenant_id": tenant_id_int,
            "health_score": max(0, health_score),
            "health_status": health_status,
            "structure_valid": quick_validation["is_valid"],
            "categorization_rate": quick_validation["categorization_rate"],
            "gl_coverage": quick_validation["gl_coverage"],
            "total_categories": basic_validation["statistics"]["total_categories"],
            "hierarchy_depth": basic_validation["statistics"]["max_hierarchy_depth"],
            "health_issues": health_issues,
            "recommendations": [
                "Ensure all categories have GL codes assigned",
                "Maintain proper Income/Expense hierarchy",
                "Review uncategorized transactions regularly",
            ]
            if health_issues
            else ["MIS structure is healthy"],
        }

    except Exception as e:
        logger.error(f"Error getting structure health metrics: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving health metrics: {str(e)}",
        )


@router.post("/setup-standard-hierarchy")
async def setup_standard_hierarchy(
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    db: Connection = Depends(get_db_session),
):
    """
    Setup the standard MIS hierarchy for a tenant.
    
    Creates the complete 3-level hierarchy:
    - Level 0: Income/Expenses
    - Level 1: Major categories (Sales & Services, Employee Costs, etc.)
    - Level 2: Subcategories
    
    This ensures consistent categorization across all tenants.
    """
    from .hierarchy_setup_service import HierarchySetupService
    
    logger.info(f"Setting up standard MIS hierarchy for tenant: {tenant_id}")
    
    try:
        hierarchy_service = HierarchySetupService(db)
        result = await hierarchy_service.setup_standard_mis_hierarchy(tenant_id)
        
        return {
            "success": True,
            "created_categories": len(result["created_categories"]),
            "existing_categories": len(result["existing_categories"]),
            "errors": result["errors"],
            "summary": {
                "total_categories": len(result["created_categories"]) + len(result["existing_categories"]),
                "new_categories": [cat["path"] for cat in result["created_categories"][:10]],  # First 10
                "message": "Standard MIS hierarchy setup complete"
            }
        }
    except Exception as e:
        logger.error(f"Error setting up hierarchy: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error setting up hierarchy: {str(e)}",
        )


@router.post("/fix-hierarchy-integrity")
async def fix_hierarchy_integrity(
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    db: Connection = Depends(get_db_session),
):
    """
    Fix hierarchy integrity issues for a tenant.
    
    Repairs:
    - Incorrect level values
    - Missing or incorrect paths
    - Orphaned categories
    
    This ensures the category tree is properly structured.
    """
    from .hierarchy_setup_service import HierarchySetupService
    
    logger.info(f"Fixing hierarchy integrity for tenant: {tenant_id}")
    
    try:
        hierarchy_service = HierarchySetupService(db)
        result = await hierarchy_service.fix_hierarchy_integrity(tenant_id)
        
        return {
            "success": True,
            "levels_fixed": result["levels_fixed"],
            "paths_fixed": result["paths_fixed"],
            "orphans_fixed": result.get("orphans_fixed", 0),
            "message": "Hierarchy integrity fixed successfully"
        }
    except Exception as e:
        logger.error(f"Error fixing hierarchy: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fixing hierarchy: {str(e)}",
        )


@router.post("/convert-flat-hierarchies")
async def convert_flat_hierarchies(
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    db: Connection = Depends(get_db_session),
):
    """
    Convert flat hierarchical categories to proper parent-child structure.
    
    Fixes categories like:
    - "Travel Expenses > Airfare > International > Economy Class"
    - "Office & Admin > Utilities > Electricity"
    
    By converting them into:
    - Travel Expenses (parent)
      - Airfare (child)
        - International (grandchild)
          - Economy Class (great-grandchild)
    
    This ensures proper hierarchy structure for MIS reporting.
    """
    from .hierarchy_setup_service import HierarchySetupService
    
    logger.info(f"Converting flat hierarchical categories for tenant: {tenant_id}")
    
    try:
        hierarchy_service = HierarchySetupService(db)
        result = await hierarchy_service.convert_flat_to_hierarchical(tenant_id)
        
        return {
            "success": True,
            "converted_categories": result["converted_categories"],
            "created_parents": result["created_parents"],
            "errors": result["errors"],
            "details": result["details"][:10] if result["details"] else [],  # First 10 conversions
            "message": f"Converted {result['converted_categories']} flat categories into proper hierarchy"
        }
    except Exception as e:
        logger.error(f"Error converting flat hierarchies: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error converting flat hierarchies: {str(e)}",
        )


@router.post("/clear-test-data")
async def clear_test_data(
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    db: Connection = Depends(get_db_session),
):
    """
    Clear all test data (transactions, uploads) to remove foreign key constraints.
    
    This allows category hierarchy operations to run without constraint violations.
    USE WITH CAUTION - This will delete all transaction and upload data!
    """
    logger.info(f"Clearing test data for tenant: {tenant_id}")
    
    try:
        # Clear transactions first (main foreign key constraint)
        result = await db.execute("DELETE FROM transactions WHERE tenant_id = $1", tenant_id)
        transactions_deleted = int(result.split()[-1]) if result else 0
        
        # Clear vendor mappings if table exists
        try:
            result = await db.execute("DELETE FROM vendor_category_mappings WHERE tenant_id = $1", tenant_id)
            mappings_deleted = int(result.split()[-1]) if result else 0
        except Exception:
            mappings_deleted = 0
        
        return {
            "success": True,
            "message": "All test data cleared successfully",
            "tenant_id": tenant_id,
            "transactions_deleted": transactions_deleted,
            "mappings_deleted": mappings_deleted
        }
    except Exception as e:
        logger.error(f"Error clearing test data: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error clearing test data: {str(e)}",
        )


@router.post("/reset-to-clean-hierarchy")
async def reset_to_clean_hierarchy(
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    db: Connection = Depends(get_db_session),
):
    """
    NUCLEAR OPTION: Delete ALL categories and rebuild with clean standard MIS hierarchy.
    
    This completely resets the category system to a pristine state with only
    the standard 3-level MIS hierarchy (Income/Expenses > Major > Subcategories).
    
    USE WITH EXTREME CAUTION - This will delete ALL existing categories!
    """
    from .hierarchy_setup_service import HierarchySetupService
    
    logger.info(f"RESETTING to clean hierarchy for tenant: {tenant_id}")
    
    try:
        # Get count before deletion
        before_count = await db.fetchval("SELECT COUNT(*) FROM categories WHERE tenant_id = $1", tenant_id)
        
        # Delete ALL categories for this tenant (CASCADE will handle child relationships)
        await db.execute("DELETE FROM categories WHERE tenant_id = $1", tenant_id)
        
        logger.info(f"Deleted {before_count} categories for tenant {tenant_id}")
        
        # Setup fresh standard MIS hierarchy
        hierarchy_service = HierarchySetupService(db)
        result = await hierarchy_service.setup_standard_mis_hierarchy(tenant_id)
        
        # Get final count
        after_count = await db.fetchval("SELECT COUNT(*) FROM categories WHERE tenant_id = $1", tenant_id)
        
        # Check final distribution
        level_distribution = await db.fetch(
            "SELECT level, COUNT(*) as count FROM categories WHERE tenant_id = $1 GROUP BY level ORDER BY level",
            tenant_id
        )
        
        return {
            "success": True,
            "message": "Category hierarchy completely reset to clean standard MIS structure",
            "tenant_id": tenant_id,
            "categories_deleted": before_count,
            "categories_created": after_count,
            "level_distribution": {row["level"]: row["count"] for row in level_distribution},
            "hierarchy_setup_result": {
                "created_categories": len(result["created_categories"]),
                "existing_categories": len(result["existing_categories"]),
                "errors": result["errors"]
            }
        }
    except Exception as e:
        logger.error(f"Error resetting hierarchy: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error resetting hierarchy: {str(e)}",
        )


@router.get("/hierarchical-results/{upload_id}", response_model=HierarchicalResults)
async def get_hierarchical_results(
    upload_id: str,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    category_service: CategoryService = Depends(get_category_service),
    conn: Connection = Depends(get_db_session),
):
    """
    Get hierarchical categorization results with visualization data.
    
    Returns a complete hierarchical breakdown of categorized transactions
    including income/expense hierarchies, statistics, and enhancement suggestions.
    """
    logger.info(
        f"Getting hierarchical results for upload: {upload_id}, tenant: {tenant_id_int}"
    )
    
    try:
        # Get upload metadata
        upload_query = """
            SELECT 
                id, 
                filename, 
                created_at,
                metadata
            FROM uploads
            WHERE id = $1 AND tenant_id = $2
        """
        upload_data = await conn.fetchrow(upload_query, upload_id, tenant_id_int)
        
        if not upload_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Upload {upload_id} not found"
            )
        
        # Get categorization statistics
        stats_query = """
            SELECT 
                COUNT(*) as total_transactions,
                COUNT(category_id) as categorized_transactions,
                COUNT(*) - COUNT(category_id) as uncategorized_transactions,
                AVG(CASE WHEN ai_confidence_score IS NOT NULL THEN ai_confidence_score ELSE 0 END) as avg_confidence,
                SUM(CASE WHEN ai_confidence_score >= 0.8 THEN 1 ELSE 0 END) as high_confidence,
                SUM(CASE WHEN ai_confidence_score >= 0.5 AND ai_confidence_score < 0.8 THEN 1 ELSE 0 END) as medium_confidence,
                SUM(CASE WHEN ai_confidence_score < 0.5 THEN 1 ELSE 0 END) as low_confidence,
                SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_income,
                SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_expenses
            FROM transactions
            WHERE upload_id = $1 AND tenant_id = $2
        """
        stats = await conn.fetchrow(stats_query, upload_id, tenant_id_int)
        
        # Get hierarchical category usage with amounts
        hierarchy_query = """
            WITH category_usage AS (
                SELECT 
                    c.id,
                    c.name,
                    c.path,
                    c.level,
                    c.parent_id,
                    c.color,
                    c.gl_code,
                    COUNT(t.id) as transaction_count,
                    COALESCE(SUM(ABS(t.amount)), 0) as total_amount,
                    CASE 
                        WHEN SUM(t.amount) > 0 THEN 'income'
                        ELSE 'expense'
                    END as category_type
                FROM categories c
                LEFT JOIN transactions t ON t.category_id = c.id AND t.upload_id = $1
                WHERE c.tenant_id = $2
                GROUP BY c.id, c.name, c.path, c.level, c.parent_id, c.color, c.gl_code
                HAVING COUNT(t.id) > 0
            )
            SELECT * FROM category_usage
            ORDER BY level, path
        """
        category_data = await conn.fetch(hierarchy_query, upload_id, tenant_id_int)
        
        # Build hierarchical structure
        income_hierarchy = []
        expense_hierarchy = []
        category_map = {}
        
        # First pass: create all category objects
        for row in category_data:
            cat_viz = CategoryVisualization(
                id=row['id'],
                name=row['name'],
                path=row['path'],
                level=row['level'],
                transaction_count=row['transaction_count'],
                total_amount=float(row['total_amount']),
                percentage=0.0,  # Will calculate after
                color=row['color'],
                gl_code=row['gl_code'],
                children=[]
            )
            category_map[row['id']] = cat_viz
        
        # Second pass: build hierarchy
        for row in category_data:
            cat_viz = category_map[row['id']]
            
            # Calculate percentage
            if row['category_type'] == 'income' and stats['total_income'] > 0:
                cat_viz.percentage = (cat_viz.total_amount / float(stats['total_income'])) * 100
            elif row['category_type'] == 'expense' and stats['total_expenses'] > 0:
                cat_viz.percentage = (cat_viz.total_amount / float(stats['total_expenses'])) * 100
            
            # Add to appropriate hierarchy
            if row['parent_id'] is None:
                if row['category_type'] == 'income':
                    income_hierarchy.append(cat_viz)
                else:
                    expense_hierarchy.append(cat_viz)
            elif row['parent_id'] in category_map:
                category_map[row['parent_id']].children.append(cat_viz)
        
        # Get top categories
        top_categories_query = """
            SELECT 
                c.name,
                c.path,
                COUNT(t.id) as usage_count,
                SUM(ABS(t.amount)) as total_amount
            FROM categories c
            JOIN transactions t ON t.category_id = c.id
            WHERE t.upload_id = $1 AND t.tenant_id = $2
            GROUP BY c.id, c.name, c.path
            ORDER BY usage_count DESC
            LIMIT 10
        """
        top_cats = await conn.fetch(top_categories_query, upload_id, tenant_id_int)
        
        top_categories = [
            {
                "name": row['name'],
                "path": row['path'],
                "usage_count": row['usage_count'],
                "total_amount": float(row['total_amount'])
            }
            for row in top_cats
        ]
        
        # Calculate hierarchy depth
        max_level_query = """
            SELECT MAX(c.level) as max_level
            FROM categories c
            JOIN transactions t ON t.category_id = c.id
            WHERE t.upload_id = $1 AND t.tenant_id = $2
        """
        max_level_result = await conn.fetchrow(max_level_query, upload_id, tenant_id_int)
        hierarchy_depth = max_level_result['max_level'] or 0
        
        # Generate enhancement suggestions
        enhancement_suggestions = []
        
        # Check for uncategorized transactions
        if stats['uncategorized_transactions'] > 0:
            enhancement_suggestions.append({
                "type": "uncategorized",
                "priority": "high",
                "message": f"{stats['uncategorized_transactions']} transactions need categorization",
                "potential_accuracy_gain": min(10, stats['uncategorized_transactions'] / stats['total_transactions'] * 100)
            })
        
        # Check for low confidence categories
        if stats['low_confidence'] > 10:
            enhancement_suggestions.append({
                "type": "low_confidence",
                "priority": "medium",
                "message": f"{stats['low_confidence']} transactions have low confidence scores",
                "potential_accuracy_gain": 5
            })
        
        # Check for vendor mapping opportunities
        vendor_query = """
            SELECT COUNT(DISTINCT description) as unique_vendors
            FROM transactions
            WHERE upload_id = $1 AND tenant_id = $2 AND ai_confidence_score < 0.8
        """
        vendor_result = await conn.fetchrow(vendor_query, upload_id, tenant_id_int)
        
        if vendor_result['unique_vendors'] > 20:
            enhancement_suggestions.append({
                "type": "vendor_mapping",
                "priority": "medium", 
                "message": f"Map {vendor_result['unique_vendors']} vendors for better accuracy",
                "potential_accuracy_gain": 8
            })
        
        # Calculate processing time
        processing_time = 0.0
        if upload_data['metadata'] and 'processing_time' in upload_data['metadata']:
            processing_time = float(upload_data['metadata']['processing_time'])
        
        return HierarchicalResults(
            upload_id=upload_id,
            total_transactions=stats['total_transactions'],
            categorized_transactions=stats['categorized_transactions'],
            uncategorized_transactions=stats['uncategorized_transactions'],
            accuracy_score=float(stats['avg_confidence']) * 100,
            hierarchy_depth=hierarchy_depth + 1,  # Add 1 because level is 0-based
            total_categories_used=len(category_data),
            processing_time=processing_time,
            income_hierarchy=income_hierarchy,
            expense_hierarchy=expense_hierarchy,
            total_income=float(stats['total_income']),
            total_expenses=float(stats['total_expenses']),
            top_categories=top_categories,
            high_confidence_count=stats['high_confidence'],
            medium_confidence_count=stats['medium_confidence'],
            low_confidence_count=stats['low_confidence'],
            enhancement_suggestions=enhancement_suggestions
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting hierarchical results: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting hierarchical results: {str(e)}"
        )


@router.get("/enhancement-analysis/{upload_id}", response_model=EnhancementAnalysisResponse)
async def analyze_enhancements(
    upload_id: str,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Analyze an upload for enhancement opportunities.
    
    Detects opportunities for progressive MIS enhancement including:
    - Uncategorized transaction patterns
    - Low confidence categorizations
    - Vendor mapping opportunities
    - Schema enhancement with GL codes
    - Historical pattern learning
    
    Returns actionable recommendations with potential accuracy gains.
    """
    logger.info(
        f"Analyzing enhancements for upload: {upload_id}, tenant: {tenant_id_int}"
    )
    
    try:
        # Verify upload belongs to tenant
        upload_query = """
            SELECT id FROM uploads
            WHERE id = $1 AND tenant_id = $2
        """
        upload_exists = await conn.fetchrow(upload_query, upload_id, tenant_id_int)
        
        if not upload_exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Upload {upload_id} not found"
            )
        
        # Perform enhancement analysis
        analysis = await enhancement_detection_service.analyze_upload_for_enhancements(
            upload_id=upload_id,
            tenant_id=tenant_id_int,
            conn=conn
        )
        
        # Convert to response model
        return EnhancementAnalysisResponse(
            current_accuracy=analysis.current_accuracy,
            baseline_accuracy=analysis.baseline_accuracy,
            opportunities=[
                EnhancementOpportunity(
                    type=opp.type,
                    priority=opp.priority,
                    description=opp.description,
                    affected_transactions=opp.affected_transactions,
                    potential_accuracy_gain=opp.potential_accuracy_gain,
                    implementation_effort=opp.implementation_effort,
                    data=opp.data
                )
                for opp in analysis.opportunities
            ],
            total_potential_gain=analysis.total_potential_gain,
            recommended_actions=analysis.recommended_actions,
            analysis_timestamp=analysis.analysis_timestamp
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing enhancements: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing enhancements: {str(e)}"
        )


@router.get("/vendors/detect/{upload_id}", response_model=Dict[str, Any])
async def detect_vendor_opportunities(
    upload_id: str,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Detect vendor patterns and opportunities for improved categorization.
    
    Returns vendors that would benefit from consistent mapping.
    """
    logger.info(f"Detecting vendor opportunities for upload {upload_id}")
    
    try:
        # Verify upload belongs to tenant
        upload_query = """
            SELECT id FROM uploads
            WHERE id = $1 AND tenant_id = $2
        """
        upload_exists = await conn.fetchrow(upload_query, upload_id, tenant_id_int)
        
        if not upload_exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Upload {upload_id} not found"
            )
        
        # Detect vendor patterns
        detection_result = await vendor_detection_service.detect_vendors(
            tenant_id=tenant_id_int,
            upload_id=upload_id,
            conn=conn,
            min_transactions=3
        )
        
        # Get vendors for intelligent lookup
        vendors_for_lookup = await vendor_detection_service.get_vendors_for_google_lookup(
            tenant_id=tenant_id_int,
            conn=conn,
            limit=20
        )
        
        return {
            "total_vendors_detected": detection_result.total_vendors_detected,
            "high_value_vendors": [
                {
                    "vendor_name": v.normalized_name,
                    "transaction_count": v.transaction_count,
                    "total_amount": v.total_amount,
                    "avg_amount": v.avg_amount,
                    "confidence_score": v.confidence_score,
                    "category_variations": v.category_variations,
                    "sample_descriptions": v.sample_descriptions
                }
                for v in detection_result.high_value_vendors[:10]
            ],
            "vendors_needing_lookup": [
                {
                    "vendor_name": v["vendor_name"],
                    "transaction_count": v["transaction_count"],
                    "total_amount": v["total_amount"],
                    "avg_confidence": v["avg_confidence"],
                    "priority_score": v["priority_score"]
                }
                for v in vendors_for_lookup[:10]
            ],
            "vendors_with_mappings": len(detection_result.vendors_with_mappings),
            "potential_accuracy_gain": detection_result.potential_accuracy_gain
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error detecting vendors: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error detecting vendors: {str(e)}"
        )


@router.post("/vendors/apply-intelligent-mappings", response_model=Dict[str, Any])
async def apply_intelligent_vendor_mappings(
    upload_id: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Apply intelligent vendor mappings using context-aware lookup.
    
    This endpoint:
    1. Detects high-value vendors without mappings
    2. Performs context-aware lookup (pattern matching, simulated Google search)
    3. Creates vendor-to-category mappings with confidence scores
    4. Updates existing transactions with new mappings
    
    Returns accuracy improvement and mapping details.
    """
    logger.info(f"Applying intelligent vendor mappings for tenant {tenant_id_int}")
    
    try:
        # Apply intelligent vendor mappings
        result = await progressive_setup_service.apply_intelligent_vendor_mappings(
            tenant_id=tenant_id_int,
            upload_id=upload_id,
            conn=conn
        )
        
        return {
            "success": result.success,
            "enhancement_type": result.enhancement_type,
            "message": result.message,
            "transactions_affected": result.transactions_affected,
            "accuracy_improvement": result.accuracy_improvement,
            "details": result.details
        }
        
    except Exception as e:
        logger.error(f"Error applying intelligent vendor mappings: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error applying vendor mappings: {str(e)}"
        )


@router.get("/vendors/lookup-preview", response_model=Dict[str, Any])
async def preview_vendor_lookup(
    vendor_name: str,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Preview what a vendor lookup would return without creating mappings.
    
    Useful for understanding how the context-aware lookup works.
    """
    logger.info(f"Previewing vendor lookup for: {vendor_name}")
    
    try:
        from .vendor_detection_service import VendorPattern
        
        # Create a simple vendor pattern for preview
        pattern = VendorPattern(
            pattern=vendor_name,
            normalized_name=vendor_name,
            transaction_count=10,  # Simulated
            total_amount=1000.0,   # Simulated
            avg_amount=100.0,      # Simulated
            category_variations=2,  # Simulated
            confidence_score=0.6,   # Low confidence to trigger lookup
            sample_descriptions=[vendor_name]
        )
        
        # Perform lookup
        result = await vendor_lookup_service.lookup_vendor_with_context(
            vendor_pattern=pattern,
            tenant_id=tenant_id_int,
            conn=conn
        )
        
        if result:
            return {
                "vendor_name": result.vendor_name,
                "business_type": result.business_type,
                "business_category": result.business_category,
                "confidence_score": result.confidence_score,
                "suggested_category": result.suggested_category_path,
                "search_query_used": result.search_query_used,
                "validation_reasons": result.validation_reasons,
                "data_source": result.data_source
            }
        else:
            return {
                "error": "Could not determine vendor type",
                "vendor_name": vendor_name
            }
        
    except Exception as e:
        logger.error(f"Error previewing vendor lookup: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error previewing vendor: {str(e)}"
        )


# ============================================================================
# USER VENDOR MAPPING ENDPOINTS
# ============================================================================


@router.get("/vendors/groupings", response_model=List[VendorGrouping])
async def get_vendor_groupings(
    upload_id: Optional[str] = None,
    include_mapped: bool = False,
    min_transactions: int = 2,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Get vendor groupings for the transaction categorization UI.
    
    Groups transactions by vendor and shows:
    - Transaction counts (total, debits, credits)
    - Amount totals and breakdowns
    - Current categorization patterns
    - Existing mappings if any
    
    This integrates with the bulk categorization UI.
    """
    logger.info(f"Getting vendor groupings for tenant {tenant_id_int}")
    
    try:
        groupings = await user_vendor_mapping_service.get_vendor_groupings(
            tenant_id=tenant_id_int,
            upload_id=upload_id,
            user_id=current_user.id,
            conn=conn,
            include_mapped=include_mapped,
            min_transactions=min_transactions
        )
        
        return groupings
        
    except Exception as e:
        logger.error(f"Error getting vendor groupings: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting vendor groupings: {str(e)}"
        )


@router.post("/vendors/user-mapping", response_model=VendorMappingResult)
async def create_user_vendor_mapping(
    mapping: VendorMappingCreate,
    apply_immediately: bool = True,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Create a user-defined vendor-to-category mapping.
    
    Key features:
    - Credit/debit awareness: Map same vendor differently for income vs expenses
    - Amount filtering: Only apply to transactions within amount range
    - Pattern matching: Use regex for complex vendor descriptions
    - Immediate application: Optionally categorize existing transactions
    
    Example:
    - Map "AMAZON" to "Office Supplies" for debits < $100
    - Map "AMAZON" to "Equipment" for debits > $100
    - Map "STRIPE" to "Revenue" for credits
    """
    logger.info(
        f"Creating user vendor mapping: {mapping.vendor_name} -> {mapping.category_id} "
        f"(debits: {mapping.applies_to_debits}, credits: {mapping.applies_to_credits})"
    )
    
    try:
        result = await user_vendor_mapping_service.create_vendor_mapping(
            mapping=mapping,
            tenant_id=tenant_id_int,
            user_id=current_user.id,
            conn=conn,
            apply_immediately=apply_immediately
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error creating vendor mapping: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating vendor mapping: {str(e)}"
        )


@router.post("/vendors/bulk-user-mappings", response_model=VendorMappingResult)
async def create_bulk_user_vendor_mappings(
    request: BulkVendorMappingRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Create multiple vendor mappings at once.
    
    Useful when categorizing multiple vendors from the grouping UI.
    """
    logger.info(f"Creating {len(request.mappings)} vendor mappings in bulk")
    
    try:
        result = await user_vendor_mapping_service.create_bulk_vendor_mappings(
            mappings=request.mappings,
            tenant_id=tenant_id_int,
            user_id=current_user.id,
            conn=conn,
            apply_immediately=request.apply_to_existing
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error creating bulk vendor mappings: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating bulk mappings: {str(e)}"
        )


@router.get("/vendors/user-mappings", response_model=List[VendorMapping])
async def get_user_vendor_mappings(
    include_inactive: bool = False,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Get all vendor mappings for the current tenant.
    
    Shows:
    - Mapping rules (vendor, category, direction, amounts)
    - Usage statistics (times applied, last used)
    - Created by information
    """
    logger.info(f"Getting vendor mappings for tenant {tenant_id_int}")
    
    try:
        mappings = await user_vendor_mapping_service.get_user_vendor_mappings(
            tenant_id=tenant_id_int,
            user_id=current_user.id,
            conn=conn,
            include_inactive=include_inactive
        )
        
        return mappings
        
    except Exception as e:
        logger.error(f"Error getting vendor mappings: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting vendor mappings: {str(e)}"
        )


@router.put("/vendors/user-mapping/{mapping_id}", response_model=Dict[str, Any])
async def update_user_vendor_mapping(
    mapping_id: int,
    update: VendorMappingUpdate,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Update an existing vendor mapping.
    
    Can update category, direction rules, amount ranges, or deactivate.
    """
    logger.info(f"Updating vendor mapping {mapping_id}")
    
    try:
        # Verify mapping belongs to tenant
        verify_query = """
            SELECT id FROM vendor_category_mappings
            WHERE id = $1 AND tenant_id = $2
        """
        exists = await conn.fetchrow(verify_query, mapping_id, tenant_id_int)
        
        if not exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor mapping not found"
            )
        
        # Build update query
        updates = []
        params = []
        param_count = 0
        
        if update.category_id is not None:
            param_count += 1
            updates.append(f"category_id = ${param_count}")
            params.append(update.category_id)
            
        if update.applies_to_debits is not None:
            param_count += 1
            updates.append(f"applies_to_debits = ${param_count}")
            params.append(update.applies_to_debits)
            
        if update.applies_to_credits is not None:
            param_count += 1
            updates.append(f"applies_to_credits = ${param_count}")
            params.append(update.applies_to_credits)
            
        if update.min_amount is not None:
            param_count += 1
            updates.append(f"min_amount = ${param_count}")
            params.append(update.min_amount)
            
        if update.max_amount is not None:
            param_count += 1
            updates.append(f"max_amount = ${param_count}")
            params.append(update.max_amount)
            
        if update.notes is not None:
            param_count += 1
            updates.append(f"notes = ${param_count}")
            params.append(update.notes)
            
        if update.is_active is not None:
            param_count += 1
            updates.append(f"is_active = ${param_count}")
            params.append(update.is_active)
        
        # Always update timestamps and user
        updates.extend([
            "updated_at = CURRENT_TIMESTAMP",
            f"updated_by = ${param_count + 1}"
        ])
        params.extend([current_user.id, mapping_id])
        
        update_query = f"""
            UPDATE vendor_category_mappings
            SET {', '.join(updates)}
            WHERE id = ${param_count + 2}
        """
        
        await conn.execute(update_query, *params)
        
        return {
            "success": True,
            "message": f"Vendor mapping {mapping_id} updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating vendor mapping: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating vendor mapping: {str(e)}"
        )


@router.delete("/vendors/user-mapping/{mapping_id}")
async def delete_user_vendor_mapping(
    mapping_id: int,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Delete (deactivate) a vendor mapping.
    
    Note: Mappings are soft-deleted to preserve history.
    """
    logger.info(f"Deleting vendor mapping {mapping_id}")
    
    try:
        # Soft delete by setting inactive
        query = """
            UPDATE vendor_category_mappings
            SET 
                is_active = false,
                updated_at = CURRENT_TIMESTAMP,
                updated_by = $1
            WHERE id = $2 AND tenant_id = $3
            RETURNING vendor_name
        """
        
        result = await conn.fetchrow(
            query, current_user.id, mapping_id, tenant_id_int
        )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor mapping not found"
            )
        
        return {
            "success": True,
            "message": f"Vendor mapping for '{result['vendor_name']}' deactivated"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting vendor mapping: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting vendor mapping: {str(e)}"
        )


# ============================================================================
# CATEGORIZATION METRICS ENDPOINTS
# ============================================================================


@router.get("/metrics/categorization", response_model=Dict[str, Any])
async def get_categorization_metrics(
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Get real categorization metrics for dashboard display.
    
    Returns actual data-based metrics including:
    - Categorization rate (categorized vs total transactions)
    - Categorization breakdown by source (AI, user, business logic)
    - Confidence distribution (high/medium/low confidence counts)
    - Top categories by transaction count
    - Processing status (pending review, user confirmed)
    - Enhancement status indicators
    
    Replaces fake accuracy displays with meaningful categorization status.
    """
    logger.info(f"Getting categorization metrics for tenant {tenant_id_int}")
    
    try:
        metrics_service = CategorizationMetricsService(conn)
        metrics = await metrics_service.get_categorization_metrics(tenant_id_int)
        
        # Convert to API response format
        return {
            "tenant_id": tenant_id_int,
            "categorization_summary": {
                "total_transactions": metrics.total_transactions,
                "categorized_transactions": metrics.categorized_transactions,
                "uncategorized_transactions": metrics.uncategorized_transactions,
                "categorization_rate": metrics.categorization_rate,
            },
            "categorization_breakdown": {
                "ai_categorized": metrics.ai_categorized,
                "user_categorized": metrics.user_categorized,
                "business_logic_categorized": metrics.business_logic_categorized,
                "user_engagement_rate": metrics.user_engagement_rate,
            },
            "confidence_distribution": {
                "high_confidence": metrics.high_confidence_count,
                "medium_confidence": metrics.medium_confidence_count, 
                "low_confidence": metrics.low_confidence_count,
                "confidence_percentages": metrics.confidence_distribution,
            },
            "top_categories": metrics.top_categories,
            "processing_status": {
                "pending_review": metrics.pending_review_count,
                "user_confirmed": metrics.user_confirmed_count,
            },
            "enhancement_status": {
                "has_historical_data": metrics.has_historical_data,
                "has_vendor_mappings": metrics.has_vendor_mappings,
                "has_pattern_rules": metrics.has_pattern_rules,
            },
            "metrics_type": "real_data",
            "computed_at": "real_time"
        }
        
    except Exception as e:
        logger.error(f"Error getting categorization metrics: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting categorization metrics: {str(e)}"
        )


@router.get("/metrics/trends", response_model=Dict[str, Any])
async def get_categorization_trends(
    days: int = 30,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Get categorization trends over time.
    
    Returns daily categorization rates and confidence trends for
    the specified number of days (default 30).
    """
    logger.info(f"Getting categorization trends for {days} days, tenant {tenant_id_int}")
    
    try:
        metrics_service = CategorizationMetricsService(conn)
        trends = await metrics_service.get_categorization_trends(tenant_id_int, days)
        
        return trends
        
    except Exception as e:
        logger.error(f"Error getting categorization trends: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting categorization trends: {str(e)}"
        )


@router.get("/metrics/confidence-insights", response_model=Dict[str, Any])
async def get_confidence_insights(
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Get confidence scoring insights by category.
    
    Returns detailed analysis of confidence scores for different
    categories and their validation rates.
    """
    logger.info(f"Getting confidence insights for tenant {tenant_id_int}")
    
    try:
        metrics_service = CategorizationMetricsService(conn)
        insights = await metrics_service.get_confidence_insights(tenant_id_int)
        
        return insights
        
    except Exception as e:
        logger.error(f"Error getting confidence insights: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting confidence insights: {str(e)}"
        )
