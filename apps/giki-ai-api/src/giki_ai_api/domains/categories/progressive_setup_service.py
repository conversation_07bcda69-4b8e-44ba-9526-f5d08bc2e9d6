"""
Progressive MIS Setup Service
=============================

This service handles the progressive enhancement of the MIS system
based on detected opportunities and user selections.

Features:
- Apply vendor mappings
- Add GL codes to categories
- Process historical data for pattern learning
- Re-categorize low confidence transactions
- Setup automated enhancement workflows
"""

import logging
from dataclasses import dataclass
from typing import Dict, List, Optional

import asyncpg

from .vendor_detection_service import vendor_detection_service
from .vendor_lookup_service import vendor_lookup_service

logger = logging.getLogger(__name__)


@dataclass
class ProgressiveSetupResult:
    """Result of progressive MIS setup operation."""
    
    enhancement_type: str
    success: bool
    transactions_affected: int
    accuracy_improvement: float
    message: str
    details: Dict[str, any]


class ProgressiveMISSetupService:
    """Service for applying progressive MIS enhancements."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def apply_vendor_mappings(
        self,
        tenant_id: int,
        vendor_mappings: List[Dict[str, any]],
        conn: asyncpg.Connection
    ) -> ProgressiveSetupResult:
        """
        Apply vendor to category mappings for consistent categorization.
        
        Args:
            tenant_id: The tenant ID
            vendor_mappings: List of vendor mappings to apply
            conn: Database connection
            
        Returns:
            ProgressiveSetupResult with details of the operation
        """
        self.logger.info(f"Applying {len(vendor_mappings)} vendor mappings for tenant {tenant_id}")
        
        try:
            # category_service = CategoryService(conn)  # TODO: Use if needed
            
            successful_mappings = 0
            transactions_updated = 0
            
            for mapping in vendor_mappings:
                vendor_name = mapping['vendor_name']
                category_id = mapping['category_id']
                
                # Create vendor mapping in vendor_category_mappings table
                try:
                    insert_query = """
                        INSERT INTO vendor_category_mappings 
                        (vendor_name, category_id, tenant_id, confidence_threshold, created_at)
                        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
                        ON CONFLICT (vendor_name, tenant_id) 
                        DO UPDATE SET 
                            category_id = EXCLUDED.category_id,
                            confidence_threshold = EXCLUDED.confidence_threshold,
                            updated_at = CURRENT_TIMESTAMP
                    """
                    await conn.execute(
                        insert_query,
                        vendor_name.lower(),
                        category_id,
                        tenant_id,
                        0.85
                    )
                    successful_mappings += 1
                    
                    # Update existing transactions
                    update_query = """
                        UPDATE transactions
                        SET 
                            category_id = $1,
                            ai_confidence_score = 0.95,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE tenant_id = $2
                        AND LOWER(description) LIKE LOWER($3)
                        AND (category_id IS NULL OR ai_confidence_score < 0.85)
                    """
                    result = await conn.execute(
                        update_query,
                        category_id,
                        tenant_id,
                        f"%{vendor_name}%"
                    )
                    
                    # Extract affected rows count
                    count = int(result.split()[-1])
                    transactions_updated += count
                    
                except Exception as e:
                    self.logger.warning(f"Failed to map vendor '{vendor_name}': {e}")
            
            # Calculate accuracy improvement
            accuracy_improvement = min(8.0, successful_mappings * 0.5)
            
            return ProgressiveSetupResult(
                enhancement_type="vendor_mapping",
                success=successful_mappings > 0,
                transactions_affected=transactions_updated,
                accuracy_improvement=accuracy_improvement,
                message=f"Successfully mapped {successful_mappings} vendors, updating {transactions_updated} transactions",
                details={
                    "successful_mappings": successful_mappings,
                    "failed_mappings": len(vendor_mappings) - successful_mappings,
                    "transactions_updated": transactions_updated
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error applying vendor mappings: {e}", exc_info=True)
            return ProgressiveSetupResult(
                enhancement_type="vendor_mapping",
                success=False,
                transactions_affected=0,
                accuracy_improvement=0.0,
                message=f"Failed to apply vendor mappings: {str(e)}",
                details={"error": str(e)}
            )
    
    async def add_gl_codes_to_categories(
        self,
        tenant_id: int,
        gl_mappings: List[Dict[str, any]],
        conn: asyncpg.Connection
    ) -> ProgressiveSetupResult:
        """
        Add GL codes to categories for enhanced financial reporting.
        
        Args:
            tenant_id: The tenant ID
            gl_mappings: List of GL code mappings to apply
            conn: Database connection
            
        Returns:
            ProgressiveSetupResult with details of the operation
        """
        self.logger.info(f"Adding GL codes to {len(gl_mappings)} categories for tenant {tenant_id}")
        
        try:
            # category_service = CategoryService(conn)  # TODO: Use if needed
            
            successful_updates = 0
            transactions_affected = 0
            
            for mapping in gl_mappings:
                category_id = mapping['category_id']
                gl_code = mapping['gl_code']
                gl_account_name = mapping.get('gl_account_name', '')
                gl_account_type = mapping.get('gl_account_type', 'Expense')
                
                try:
                    # Update category with GL code
                    update_query = """
                        UPDATE categories
                        SET 
                            gl_code = $1,
                            gl_account_name = $2,
                            gl_account_type = $3,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = $4 AND tenant_id = $5
                    """
                    await conn.execute(
                        update_query,
                        gl_code,
                        gl_account_name,
                        gl_account_type,
                        category_id,
                        tenant_id
                    )
                    successful_updates += 1
                    
                    # Count affected transactions
                    count_query = """
                        SELECT COUNT(*) as count
                        FROM transactions
                        WHERE category_id = $1 AND tenant_id = $2
                    """
                    result = await conn.fetchrow(count_query, category_id, tenant_id)
                    transactions_affected += result['count']
                    
                except Exception as e:
                    self.logger.warning(f"Failed to update category {category_id} with GL code: {e}")
            
            # Calculate accuracy improvement (GL codes provide significant benefit)
            accuracy_improvement = min(20.0, successful_updates * 2.0)
            
            return ProgressiveSetupResult(
                enhancement_type="gl_code_mapping",
                success=successful_updates > 0,
                transactions_affected=transactions_affected,
                accuracy_improvement=accuracy_improvement,
                message=f"Successfully added GL codes to {successful_updates} categories",
                details={
                    "categories_updated": successful_updates,
                    "failed_updates": len(gl_mappings) - successful_updates,
                    "transactions_affected": transactions_affected
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error adding GL codes: {e}", exc_info=True)
            return ProgressiveSetupResult(
                enhancement_type="gl_code_mapping",
                success=False,
                transactions_affected=0,
                accuracy_improvement=0.0,
                message=f"Failed to add GL codes: {str(e)}",
                details={"error": str(e)}
            )
    
    async def process_historical_enhancement(
        self,
        tenant_id: int,
        upload_id: str,
        conn: asyncpg.Connection
    ) -> ProgressiveSetupResult:
        """
        Process historical data to learn patterns and improve categorization.
        
        Args:
            tenant_id: The tenant ID
            upload_id: The upload to enhance with historical patterns
            conn: Database connection
            
        Returns:
            ProgressiveSetupResult with details of the operation
        """
        self.logger.info(f"Processing historical enhancement for upload {upload_id}")
        
        try:
            # Get historical patterns
            pattern_query = """
                WITH historical_patterns AS (
                    SELECT 
                        description,
                        category_id,
                        COUNT(*) as frequency,
                        AVG(ai_confidence_score) as avg_confidence
                    FROM transactions
                    WHERE tenant_id = $1
                    AND date < (SELECT MIN(date) FROM transactions WHERE upload_id = $2)
                    AND category_id IS NOT NULL
                    AND ai_confidence_score > 0.8
                    GROUP BY description, category_id
                    HAVING COUNT(*) >= 3
                )
                SELECT * FROM historical_patterns
                ORDER BY frequency DESC
                LIMIT 100
            """
            patterns = await conn.fetch(pattern_query, tenant_id, upload_id)
            
            transactions_updated = 0
            
            # Apply historical patterns to current upload
            for pattern in patterns:
                update_query = """
                    UPDATE transactions
                    SET 
                        category_id = $1,
                        ai_confidence_score = $2,
                        ai_suggested_category = $1,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE upload_id = $3
                    AND tenant_id = $4
                    AND LOWER(description) = LOWER($5)
                    AND (category_id IS NULL OR ai_confidence_score < $2)
                """
                result = await conn.execute(
                    update_query,
                    pattern['category_id'],
                    float(pattern['avg_confidence']),
                    upload_id,
                    tenant_id,
                    pattern['description']
                )
                
                # Extract affected rows count
                count = int(result.split()[-1])
                transactions_updated += count
            
            # Calculate accuracy improvement
            accuracy_improvement = min(15.0, len(patterns) * 0.2)
            
            return ProgressiveSetupResult(
                enhancement_type="historical_enhancement",
                success=transactions_updated > 0,
                transactions_affected=transactions_updated,
                accuracy_improvement=accuracy_improvement,
                message=f"Applied {len(patterns)} historical patterns, updating {transactions_updated} transactions",
                details={
                    "patterns_found": len(patterns),
                    "transactions_updated": transactions_updated
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error processing historical enhancement: {e}", exc_info=True)
            return ProgressiveSetupResult(
                enhancement_type="historical_enhancement",
                success=False,
                transactions_affected=0,
                accuracy_improvement=0.0,
                message=f"Failed to process historical enhancement: {str(e)}",
                details={"error": str(e)}
            )
    
    async def recategorize_low_confidence(
        self,
        tenant_id: int,
        upload_id: str,
        confidence_threshold: float,
        conn: asyncpg.Connection
    ) -> ProgressiveSetupResult:
        """
        Re-categorize transactions with low confidence scores.
        
        Args:
            tenant_id: The tenant ID
            upload_id: The upload to process
            confidence_threshold: Confidence threshold below which to recategorize
            conn: Database connection
            
        Returns:
            ProgressiveSetupResult with details of the operation
        """
        self.logger.info(f"Re-categorizing low confidence transactions for upload {upload_id}")
        
        try:
            # Get low confidence transactions
            low_conf_query = """
                SELECT id, description, amount, date
                FROM transactions
                WHERE upload_id = $1
                AND tenant_id = $2
                AND ai_confidence_score < $3
                AND category_id IS NOT NULL
            """
            low_conf_transactions = await conn.fetch(
                low_conf_query,
                upload_id,
                tenant_id,
                confidence_threshold
            )
            
            # Import MIS categorization service
            from .mis_categorization_service import MISCategorizationService
            mis_service = MISCategorizationService()
            
            transactions_improved = 0
            
            for transaction in low_conf_transactions:
                try:
                    # Re-categorize using MIS service
                    result = await mis_service.categorize_transaction(
                        description=transaction['description'],
                        amount=float(transaction['amount']),
                        date=transaction['date'],
                        tenant_id=tenant_id,
                        conn=conn
                    )
                    
                    # Update if confidence improved
                    if result.confidence > confidence_threshold:
                        update_query = """
                            UPDATE transactions
                            SET 
                                category_id = $1,
                                ai_confidence_score = $2,
                                updated_at = CURRENT_TIMESTAMP
                            WHERE id = $3
                        """
                        await conn.execute(
                            update_query,
                            result.category_id,
                            result.confidence,
                            transaction['id']
                        )
                        transactions_improved += 1
                        
                except Exception as e:
                    self.logger.warning(f"Failed to recategorize transaction {transaction['id']}: {e}")
            
            # Calculate accuracy improvement
            improvement_rate = transactions_improved / len(low_conf_transactions) if low_conf_transactions else 0
            accuracy_improvement = min(5.0, improvement_rate * 10)
            
            return ProgressiveSetupResult(
                enhancement_type="confidence_improvement",
                success=transactions_improved > 0,
                transactions_affected=transactions_improved,
                accuracy_improvement=accuracy_improvement,
                message=f"Improved categorization for {transactions_improved} out of {len(low_conf_transactions)} low confidence transactions",
                details={
                    "total_low_confidence": len(low_conf_transactions),
                    "improved": transactions_improved,
                    "improvement_rate": improvement_rate * 100
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error recategorizing low confidence transactions: {e}", exc_info=True)
            return ProgressiveSetupResult(
                enhancement_type="confidence_improvement",
                success=False,
                transactions_affected=0,
                accuracy_improvement=0.0,
                message=f"Failed to recategorize: {str(e)}",
                details={"error": str(e)}
            )
    
    async def apply_intelligent_vendor_mappings(
        self,
        tenant_id: int,
        upload_id: Optional[str],
        conn: asyncpg.Connection
    ) -> ProgressiveSetupResult:
        """
        Apply intelligent vendor mappings using context-aware Google lookup.
        
        Args:
            tenant_id: The tenant ID
            upload_id: Optional upload ID to focus on
            conn: Database connection
            
        Returns:
            ProgressiveSetupResult with details of the operation
        """
        self.logger.info(f"Applying intelligent vendor mappings for tenant {tenant_id}")
        
        try:
            # Detect vendor patterns
            detection_result = await vendor_detection_service.detect_vendors(
                tenant_id=tenant_id,
                upload_id=upload_id,
                conn=conn,
                min_transactions=3
            )
            
            if not detection_result.vendors_needing_lookup:
                return ProgressiveSetupResult(
                    enhancement_type="intelligent_vendor_mapping",
                    success=True,
                    transactions_affected=0,
                    accuracy_improvement=0.0,
                    message="No vendors require intelligent lookup",
                    details={
                        "vendors_detected": detection_result.total_vendors_detected,
                        "already_mapped": len(detection_result.vendors_with_mappings)
                    }
                )
            
            # Get high-priority vendors for Google lookup
            vendors_for_lookup = await vendor_detection_service.get_vendors_for_google_lookup(
                tenant_id=tenant_id,
                conn=conn,
                limit=10  # Limit to top 10 to avoid API overuse
            )
            
            # Perform intelligent lookup with context
            lookup_results = await vendor_lookup_service.batch_lookup_vendors(
                vendors=vendors_for_lookup,
                tenant_id=tenant_id,
                conn=conn
            )
            
            # Apply the lookup results
            mappings_created, accuracy_improvement = await vendor_lookup_service.apply_lookup_results(
                lookup_results=lookup_results,
                tenant_id=tenant_id,
                conn=conn
            )
            
            # Apply the new mappings to existing transactions
            transactions_updated, additional_improvement = await vendor_detection_service.apply_vendor_mappings(
                tenant_id=tenant_id,
                upload_id=upload_id,
                conn=conn
            )
            
            total_accuracy_improvement = accuracy_improvement + additional_improvement
            
            # Get details about lookup results
            high_confidence_results = [r for r in lookup_results if r.confidence_score >= 0.85]
            medium_confidence_results = [r for r in lookup_results if 0.7 <= r.confidence_score < 0.85]
            low_confidence_results = [r for r in lookup_results if r.confidence_score < 0.7]
            
            return ProgressiveSetupResult(
                enhancement_type="intelligent_vendor_mapping",
                success=mappings_created > 0,
                transactions_affected=transactions_updated,
                accuracy_improvement=total_accuracy_improvement,
                message=f"Created {mappings_created} intelligent vendor mappings, updating {transactions_updated} transactions",
                details={
                    "vendors_analyzed": len(vendors_for_lookup),
                    "mappings_created": mappings_created,
                    "high_confidence_mappings": len(high_confidence_results),
                    "medium_confidence_mappings": len(medium_confidence_results),
                    "low_confidence_mappings": len(low_confidence_results),
                    "transactions_updated": transactions_updated,
                    "lookup_results": [
                        {
                            "vendor": r.vendor_name,
                            "business_type": r.business_type,
                            "confidence": r.confidence_score,
                            "category": r.suggested_category_path,
                            "source": r.data_source
                        }
                        for r in lookup_results[:5]  # Top 5 for display
                    ]
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error applying intelligent vendor mappings: {e}", exc_info=True)
            return ProgressiveSetupResult(
                enhancement_type="intelligent_vendor_mapping",
                success=False,
                transactions_affected=0,
                accuracy_improvement=0.0,
                message=f"Failed to apply intelligent vendor mappings: {str(e)}",
                details={"error": str(e)}
            )


# Singleton instance
progressive_setup_service = ProgressiveMISSetupService()