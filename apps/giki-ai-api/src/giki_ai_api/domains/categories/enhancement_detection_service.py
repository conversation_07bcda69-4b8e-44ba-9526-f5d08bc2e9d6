"""
Enhancement Detection Service - Progressive MIS Setup
=====================================================

This service detects opportunities for progressive enhancement of the MIS
categorization system based on transaction patterns and data quality metrics.

Features:
- Detects uncategorized transaction patterns
- Identifies low-confidence categorizations
- Finds vendor mapping opportunities
- Suggests schema enhancements
- Tracks historical accuracy improvements
"""

import logging
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Dict, List, Optional

import asyncpg

logger = logging.getLogger(__name__)


@dataclass
class EnhancementOpportunity:
    """Represents a detected enhancement opportunity."""
    
    type: str  # vendor_mapping, schema_enhancement, historical_pattern, etc
    priority: str  # high, medium, low
    description: str
    affected_transactions: int
    potential_accuracy_gain: float
    implementation_effort: str  # easy, medium, hard
    data: Dict[str, any]  # Type-specific data


@dataclass
class EnhancementAnalysis:
    """Complete enhancement analysis for an upload or tenant."""
    
    current_accuracy: float
    baseline_accuracy: float
    opportunities: List[EnhancementOpportunity]
    total_potential_gain: float
    recommended_actions: List[str]
    analysis_timestamp: datetime


class EnhancementDetectionService:
    """Service for detecting MIS enhancement opportunities."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def analyze_upload_for_enhancements(
        self,
        upload_id: str,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> EnhancementAnalysis:
        """
        Analyze an upload for enhancement opportunities.
        
        Args:
            upload_id: The upload to analyze
            tenant_id: The tenant ID
            conn: Database connection
            
        Returns:
            EnhancementAnalysis with detected opportunities
        """
        self.logger.info(f"Analyzing upload {upload_id} for enhancement opportunities")
        
        # Get current accuracy metrics
        current_accuracy = await self._calculate_current_accuracy(upload_id, conn)
        baseline_accuracy = 87.0  # MIS baseline
        
        opportunities = []
        
        # 1. Check for uncategorized transactions
        uncategorized_opp = await self._detect_uncategorized_patterns(
            upload_id, tenant_id, conn
        )
        if uncategorized_opp:
            opportunities.append(uncategorized_opp)
        
        # 2. Check for low confidence categorizations
        low_confidence_opp = await self._detect_low_confidence_patterns(
            upload_id, tenant_id, conn
        )
        if low_confidence_opp:
            opportunities.append(low_confidence_opp)
        
        # 3. Check for vendor mapping opportunities
        vendor_opp = await self._detect_vendor_opportunities(
            upload_id, tenant_id, conn
        )
        if vendor_opp:
            opportunities.append(vendor_opp)
        
        # 4. Check for schema enhancement opportunities
        schema_opp = await self._detect_schema_opportunities(
            upload_id, tenant_id, conn
        )
        if schema_opp:
            opportunities.append(schema_opp)
        
        # 5. Check for historical pattern opportunities
        historical_opp = await self._detect_historical_patterns(
            upload_id, tenant_id, conn
        )
        if historical_opp:
            opportunities.append(historical_opp)
        
        # Calculate total potential gain
        total_potential_gain = sum(opp.potential_accuracy_gain for opp in opportunities)
        
        # Generate recommended actions
        recommended_actions = self._generate_recommendations(opportunities)
        
        return EnhancementAnalysis(
            current_accuracy=current_accuracy,
            baseline_accuracy=baseline_accuracy,
            opportunities=opportunities,
            total_potential_gain=min(total_potential_gain, 100 - current_accuracy),
            recommended_actions=recommended_actions,
            analysis_timestamp=datetime.now(timezone.utc)
        )
    
    async def _calculate_current_accuracy(
        self,
        upload_id: str,
        conn: asyncpg.Connection
    ) -> float:
        """Calculate current accuracy for an upload."""
        query = """
            SELECT AVG(COALESCE(ai_confidence_score, 0)) as avg_confidence
            FROM transactions
            WHERE upload_id = $1
        """
        result = await conn.fetchrow(query, upload_id)
        return float(result['avg_confidence']) * 100 if result['avg_confidence'] else 0.0
    
    async def _detect_uncategorized_patterns(
        self,
        upload_id: str,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> Optional[EnhancementOpportunity]:
        """Detect patterns in uncategorized transactions."""
        query = """
            SELECT 
                COUNT(*) as uncategorized_count,
                COUNT(DISTINCT description) as unique_descriptions,
                SUM(ABS(amount)) as total_amount
            FROM transactions
            WHERE upload_id = $1 
            AND tenant_id = $2
            AND category_id IS NULL
        """
        result = await conn.fetchrow(query, upload_id, tenant_id)
        
        if result['uncategorized_count'] > 0:
            # Get sample uncategorized transactions
            samples_query = """
                SELECT description, amount, date
                FROM transactions
                WHERE upload_id = $1 AND category_id IS NULL
                LIMIT 5
            """
            samples = await conn.fetch(samples_query, upload_id)
            
            potential_gain = min(10, (result['uncategorized_count'] / 100) * 5)
            
            return EnhancementOpportunity(
                type="uncategorized_transactions",
                priority="high" if result['uncategorized_count'] > 50 else "medium",
                description=f"{result['uncategorized_count']} transactions need categorization",
                affected_transactions=result['uncategorized_count'],
                potential_accuracy_gain=potential_gain,
                implementation_effort="easy",
                data={
                    "unique_descriptions": result['unique_descriptions'],
                    "total_amount": float(result['total_amount']),
                    "samples": [dict(s) for s in samples]
                }
            )
        
        return None
    
    async def _detect_low_confidence_patterns(
        self,
        upload_id: str,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> Optional[EnhancementOpportunity]:
        """Detect patterns in low confidence categorizations."""
        query = """
            SELECT 
                COUNT(*) as low_confidence_count,
                COUNT(DISTINCT c.name) as affected_categories,
                AVG(t.ai_confidence_score) as avg_confidence
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            WHERE t.upload_id = $1 
            AND t.tenant_id = $2
            AND t.ai_confidence_score < 0.7
            AND t.ai_confidence_score IS NOT NULL
        """
        result = await conn.fetchrow(query, upload_id, tenant_id)
        
        if result['low_confidence_count'] > 10:
            # Get most affected categories
            categories_query = """
                SELECT 
                    c.name,
                    c.path,
                    COUNT(*) as transaction_count,
                    AVG(t.ai_confidence_score) as avg_confidence
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                WHERE t.upload_id = $1 
                AND t.ai_confidence_score < 0.7
                GROUP BY c.id, c.name, c.path
                ORDER BY transaction_count DESC
                LIMIT 5
            """
            categories = await conn.fetch(categories_query, upload_id)
            
            return EnhancementOpportunity(
                type="low_confidence_categorization",
                priority="medium",
                description=f"{result['low_confidence_count']} transactions have low confidence scores",
                affected_transactions=result['low_confidence_count'],
                potential_accuracy_gain=5.0,
                implementation_effort="medium",
                data={
                    "affected_categories": result['affected_categories'],
                    "avg_confidence": float(result['avg_confidence']),
                    "top_affected_categories": [dict(c) for c in categories]
                }
            )
        
        return None
    
    async def _detect_vendor_opportunities(
        self,
        upload_id: str,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> Optional[EnhancementOpportunity]:
        """Detect vendor mapping opportunities."""
        query = """
            WITH vendor_patterns AS (
                SELECT 
                    description,
                    COUNT(*) as transaction_count,
                    COUNT(DISTINCT category_id) as category_variations,
                    AVG(ai_confidence_score) as avg_confidence
                FROM transactions
                WHERE upload_id = $1 
                AND tenant_id = $2
                GROUP BY description
                HAVING COUNT(*) >= 3
                AND COUNT(DISTINCT category_id) > 1
            )
            SELECT 
                COUNT(*) as mappable_vendors,
                SUM(transaction_count) as affected_transactions
            FROM vendor_patterns
        """
        result = await conn.fetchrow(query, upload_id, tenant_id)
        
        if result['mappable_vendors'] > 5:
            # Get top vendors needing mapping
            vendors_query = """
                SELECT 
                    description,
                    COUNT(*) as transaction_count,
                    COUNT(DISTINCT category_id) as category_variations,
                    AVG(ai_confidence_score) as avg_confidence
                FROM transactions
                WHERE upload_id = $1 
                AND tenant_id = $2
                GROUP BY description
                HAVING COUNT(*) >= 3
                AND COUNT(DISTINCT category_id) > 1
                ORDER BY transaction_count DESC
                LIMIT 10
            """
            vendors = await conn.fetch(vendors_query, upload_id, tenant_id)
            
            potential_gain = min(8, result['mappable_vendors'] * 0.5)
            
            return EnhancementOpportunity(
                type="vendor_mapping",
                priority="medium" if result['mappable_vendors'] > 20 else "low",
                description=f"Map {result['mappable_vendors']} recurring vendors for consistency",
                affected_transactions=result['affected_transactions'],
                potential_accuracy_gain=potential_gain,
                implementation_effort="easy",
                data={
                    "mappable_vendors": result['mappable_vendors'],
                    "top_vendors": [dict(v) for v in vendors]
                }
            )
        
        return None
    
    async def _detect_schema_opportunities(
        self,
        upload_id: str,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> Optional[EnhancementOpportunity]:
        """Detect schema enhancement opportunities."""
        # Check if GL codes are being used
        query = """
            SELECT 
                COUNT(DISTINCT c.id) as total_categories,
                COUNT(DISTINCT c.id) FILTER (WHERE c.gl_code IS NOT NULL) as with_gl_code,
                COUNT(DISTINCT t.id) as total_transactions,
                COUNT(DISTINCT t.id) FILTER (WHERE c.gl_code IS NOT NULL) as transactions_with_gl
            FROM transactions t
            JOIN categories c ON t.category_id = c.id
            WHERE t.upload_id = $1 
            AND t.tenant_id = $2
        """
        result = await conn.fetchrow(query, upload_id, tenant_id)
        
        gl_coverage = result['with_gl_code'] / result['total_categories'] if result['total_categories'] > 0 else 0
        
        if gl_coverage < 0.5:
            return EnhancementOpportunity(
                type="schema_enhancement",
                priority="high" if gl_coverage < 0.2 else "medium",
                description="Add GL codes for better financial reporting",
                affected_transactions=result['total_transactions'] - result['transactions_with_gl'],
                potential_accuracy_gain=20.0,  # Schema enhancement provides significant gain
                implementation_effort="medium",
                data={
                    "total_categories": result['total_categories'],
                    "categories_with_gl": result['with_gl_code'],
                    "gl_coverage_percentage": gl_coverage * 100
                }
            )
        
        return None
    
    async def _detect_historical_patterns(
        self,
        upload_id: str,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> Optional[EnhancementOpportunity]:
        """Detect historical pattern opportunities."""
        # Check for historical data availability
        query = """
            SELECT 
                COUNT(DISTINCT DATE_TRUNC('month', date)) as months_of_data,
                MIN(date) as earliest_date,
                MAX(date) as latest_date,
                COUNT(*) as total_transactions
            FROM transactions
            WHERE tenant_id = $1
        """
        result = await conn.fetchrow(query, tenant_id)
        
        if result['months_of_data'] >= 6:
            # Check for seasonal patterns
            seasonal_query = """
                SELECT 
                    DATE_PART('month', date) as month,
                    COUNT(DISTINCT category_id) as category_count,
                    COUNT(*) as transaction_count
                FROM transactions
                WHERE tenant_id = $1
                GROUP BY DATE_PART('month', date)
                ORDER BY month
            """
            seasonal_data = await conn.fetch(seasonal_query, tenant_id)
            
            return EnhancementOpportunity(
                type="historical_enhancement",
                priority="medium",
                description=f"Use {result['months_of_data']} months of historical data for pattern learning",
                affected_transactions=result['total_transactions'],
                potential_accuracy_gain=15.0,  # Historical patterns provide good improvement
                implementation_effort="hard",
                data={
                    "months_of_data": result['months_of_data'],
                    "date_range": {
                        "from": result['earliest_date'].isoformat() if result['earliest_date'] else None,
                        "to": result['latest_date'].isoformat() if result['latest_date'] else None
                    },
                    "seasonal_patterns": [dict(s) for s in seasonal_data]
                }
            )
        
        return None
    
    def _generate_recommendations(
        self,
        opportunities: List[EnhancementOpportunity]
    ) -> List[str]:
        """Generate actionable recommendations based on opportunities."""
        recommendations = []
        
        # Sort by priority and potential gain
        sorted_opps = sorted(
            opportunities,
            key=lambda x: (
                {'high': 3, 'medium': 2, 'low': 1}[x.priority],
                x.potential_accuracy_gain
            ),
            reverse=True
        )
        
        for opp in sorted_opps[:3]:  # Top 3 recommendations
            if opp.type == "uncategorized_transactions":
                recommendations.append(
                    f"Review and categorize {opp.affected_transactions} uncategorized transactions "
                    f"to gain +{opp.potential_accuracy_gain:.1f}% accuracy"
                )
            elif opp.type == "vendor_mapping":
                recommendations.append(
                    f"Create vendor mappings for {opp.data['mappable_vendors']} recurring vendors "
                    f"to improve consistency by +{opp.potential_accuracy_gain:.1f}%"
                )
            elif opp.type == "schema_enhancement":
                recommendations.append(
                    f"Add GL codes to categories (currently {opp.data['gl_coverage_percentage']:.0f}% coverage) "
                    f"for +{opp.potential_accuracy_gain:.1f}% accuracy"
                )
            elif opp.type == "historical_enhancement":
                recommendations.append(
                    f"Enable historical pattern learning using {opp.data['months_of_data']} months of data "
                    f"for +{opp.potential_accuracy_gain:.1f}% accuracy"
                )
            elif opp.type == "low_confidence_categorization":
                recommendations.append(
                    f"Review {opp.affected_transactions} low-confidence categorizations "
                    f"to improve accuracy by +{opp.potential_accuracy_gain:.1f}%"
                )
        
        return recommendations


# Singleton instance
enhancement_detection_service = EnhancementDetectionService()