"""
Review Queue Router
===================

API endpoints for managing the transaction review queue after AI categorization.
Handles medium-confidence suggestions, low-confidence groups, and manual categorization.
"""

import logging
from typing import Any, Dict, List, Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field

from ...core.dependencies import get_current_tenant_id, get_db_session
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user

logger = logging.getLogger(__name__)


# Request/Response schemas
class ReviewQueueSummary(BaseModel):
    """Summary of items in the review queue."""
    
    upload_id: str
    total_transactions: int
    auto_categorized: int
    auto_categorized_percentage: float
    needs_review: int
    needs_review_percentage: float
    grouped: int
    grouped_percentage: float
    uncategorized: int
    uncategorized_percentage: float
    groups_count: int


class TransactionSuggestion(BaseModel):
    """Transaction with AI suggestion for review."""
    
    id: int
    date: str
    description: str
    amount: float
    suggested_category_id: int
    suggested_category_path: str
    confidence_score: float
    needs_review: bool


class TransactionGroup(BaseModel):
    """Group of similar transactions for bulk action."""
    
    group_id: str
    pattern: str
    transaction_count: int
    total_amount: float
    sample_descriptions: List[str]
    suggested_category_id: Optional[int]
    suggested_category_path: Optional[str]
    avg_confidence: float
    transactions: List[TransactionSuggestion]


class ReviewQueueResponse(BaseModel):
    """Complete review queue for an upload."""
    
    summary: ReviewQueueSummary
    suggestions: List[TransactionSuggestion]
    groups: List[TransactionGroup]
    uncategorized: List[TransactionSuggestion]


class BulkReviewAction(BaseModel):
    """Action to take on multiple transactions."""
    
    action: str = Field(..., pattern="^(accept|reject|categorize)$")
    transaction_ids: List[int]
    category_id: Optional[int] = None
    notes: Optional[str] = None


class GroupReviewAction(BaseModel):
    """Action to take on a transaction group."""
    
    group_id: str
    action: str = Field(..., pattern="^(accept|reject|categorize)$")
    category_id: Optional[int] = None
    create_vendor_mapping: bool = False
    vendor_mapping_options: Optional[Dict[str, Any]] = None


router = APIRouter(
    prefix="/review",
    tags=["Review Queue"],
    responses={
        404: {"description": "Not found"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized"},
    },
)


@router.get("/queue/{upload_id}", response_model=ReviewQueueResponse)
async def get_review_queue(
    upload_id: str,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Get the complete review queue for an upload.
    
    Returns:
    - Summary statistics
    - Medium confidence suggestions
    - Low confidence groups
    - Uncategorized transactions
    """
    logger.info(f"Getting review queue for upload {upload_id}")
    
    try:
        # Get summary using the database function
        summary_query = "SELECT * FROM get_review_queue_summary($1, $2)"
        summary_rows = await conn.fetch(summary_query, tenant_id_int, upload_id)
        
        # Convert to dict for easier access
        summary_data = {row['status']: {
            'count': row['count'],
            'percentage': float(row['percentage'])
        } for row in summary_rows}
        
        # Get total transactions
        total_query = """
            SELECT COUNT(*) as total FROM transactions
            WHERE tenant_id = $1 AND upload_id = $2
        """
        total_result = await conn.fetchrow(total_query, tenant_id_int, upload_id)
        total_transactions = total_result['total']
        
        # Count groups
        groups_query = """
            SELECT COUNT(DISTINCT ai_grouping_id) as groups_count
            FROM transactions
            WHERE tenant_id = $1 AND upload_id = $2
            AND ai_grouping_id IS NOT NULL
        """
        groups_result = await conn.fetchrow(groups_query, tenant_id_int, upload_id)
        groups_count = groups_result['groups_count']
        
        # Build summary
        summary = ReviewQueueSummary(
            upload_id=upload_id,
            total_transactions=total_transactions,
            auto_categorized=summary_data.get('auto_categorized', {}).get('count', 0),
            auto_categorized_percentage=summary_data.get('auto_categorized', {}).get('percentage', 0),
            needs_review=summary_data.get('needs_review', {}).get('count', 0),
            needs_review_percentage=summary_data.get('needs_review', {}).get('percentage', 0),
            grouped=summary_data.get('grouped', {}).get('count', 0),
            grouped_percentage=summary_data.get('grouped', {}).get('percentage', 0),
            uncategorized=summary_data.get('uncategorized', {}).get('count', 0),
            uncategorized_percentage=summary_data.get('uncategorized', {}).get('percentage', 0),
            groups_count=groups_count
        )
        
        # Get suggestions (medium confidence)
        suggestions_query = """
            SELECT 
                t.id,
                t.date,
                t.description,
                t.amount,
                t.ai_suggested_category as suggested_category_id,
                c.path as suggested_category_path,
                t.ai_confidence_score as confidence_score,
                t.needs_review
            FROM transactions t
            LEFT JOIN categories c ON t.ai_suggested_category = c.id
            WHERE t.tenant_id = $1 
            AND t.upload_id = $2
            AND t.needs_review = true
            AND t.ai_suggested_category IS NOT NULL
            AND t.ai_grouping_id IS NULL
            ORDER BY t.ai_confidence_score DESC, ABS(t.amount) DESC
        """
        suggestion_rows = await conn.fetch(suggestions_query, tenant_id_int, upload_id)
        
        suggestions = [
            TransactionSuggestion(
                id=row['id'],
                date=row['date'].isoformat() if row['date'] else '',
                description=row['description'],
                amount=float(row['amount']),
                suggested_category_id=row['suggested_category_id'],
                suggested_category_path=row['suggested_category_path'] or '',
                confidence_score=float(row['confidence_score'] or 0),
                needs_review=row['needs_review']
            )
            for row in suggestion_rows
        ]
        
        # Get groups
        groups_query = """
            SELECT * FROM transaction_groups
            WHERE tenant_id = $1 AND upload_id = $2
            ORDER BY transaction_count DESC
        """
        group_rows = await conn.fetch(groups_query, tenant_id_int, upload_id)
        
        groups = []
        for group_row in group_rows:
            # Get transactions in this group
            group_txns_query = """
                SELECT 
                    t.id,
                    t.date,
                    t.description,
                    t.amount,
                    t.ai_suggested_category as suggested_category_id,
                    c.path as suggested_category_path,
                    t.ai_confidence_score as confidence_score,
                    t.needs_review
                FROM transactions t
                LEFT JOIN categories c ON t.ai_suggested_category = c.id
                WHERE t.tenant_id = $1 
                AND t.upload_id = $2
                AND t.ai_grouping_id = $3
                ORDER BY ABS(t.amount) DESC
            """
            group_txn_rows = await conn.fetch(
                group_txns_query, 
                tenant_id_int, 
                upload_id, 
                group_row['ai_grouping_id']
            )
            
            group_transactions = [
                TransactionSuggestion(
                    id=row['id'],
                    date=row['date'].isoformat() if row['date'] else '',
                    description=row['description'],
                    amount=float(row['amount']),
                    suggested_category_id=row['suggested_category_id'] or 0,
                    suggested_category_path=row['suggested_category_path'] or '',
                    confidence_score=float(row['confidence_score'] or 0),
                    needs_review=row['needs_review']
                )
                for row in group_txn_rows
            ]
            
            # Get suggested category info if available
            suggested_path = None
            if group_row['suggested_category_id']:
                cat_query = "SELECT path FROM categories WHERE id = $1"
                cat_result = await conn.fetchrow(
                    cat_query, group_row['suggested_category_id']
                )
                if cat_result:
                    suggested_path = cat_result['path']
            
            groups.append(TransactionGroup(
                group_id=group_row['ai_grouping_id'],
                pattern=group_row['ai_grouping_id'].split('_')[1] if '_' in group_row['ai_grouping_id'] else '',
                transaction_count=group_row['transaction_count'],
                total_amount=float(group_row['total_amount']),
                sample_descriptions=group_row['sample_descriptions'][:3],
                suggested_category_id=group_row['suggested_category_id'],
                suggested_category_path=suggested_path,
                avg_confidence=float(group_row['avg_confidence'] or 0),
                transactions=group_transactions
            ))
        
        # Get uncategorized transactions
        uncategorized_query = """
            SELECT 
                t.id,
                t.date,
                t.description,
                t.amount,
                t.ai_suggested_category as suggested_category_id,
                c.path as suggested_category_path,
                t.ai_confidence_score as confidence_score,
                t.needs_review
            FROM transactions t
            LEFT JOIN categories c ON t.ai_suggested_category = c.id
            WHERE t.tenant_id = $1 
            AND t.upload_id = $2
            AND t.category_id IS NULL
            AND t.ai_suggested_category IS NULL
            AND NOT t.auto_categorized
            ORDER BY ABS(t.amount) DESC
        """
        uncategorized_rows = await conn.fetch(uncategorized_query, tenant_id_int, upload_id)
        
        uncategorized = [
            TransactionSuggestion(
                id=row['id'],
                date=row['date'].isoformat() if row['date'] else '',
                description=row['description'],
                amount=float(row['amount']),
                suggested_category_id=0,
                suggested_category_path='',
                confidence_score=0.0,
                needs_review=True
            )
            for row in uncategorized_rows
        ]
        
        return ReviewQueueResponse(
            summary=summary,
            suggestions=suggestions,
            groups=groups,
            uncategorized=uncategorized
        )
        
    except Exception as e:
        logger.error(f"Error getting review queue: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving review queue: {str(e)}"
        )


@router.post("/bulk-action", response_model=Dict[str, Any])
async def process_bulk_review_action(
    action: BulkReviewAction,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Process a bulk action on multiple transactions.
    
    Actions:
    - accept: Accept AI suggestions
    - reject: Clear suggestions and mark for manual review
    - categorize: Apply specific category
    """
    logger.info(
        f"Processing bulk action: {action.action} on {len(action.transaction_ids)} transactions"
    )
    
    try:
        if action.action == "accept":
            # Accept AI suggestions - but only for categories that exist
            query = """
                UPDATE transactions
                SET 
                    category_id = ai_suggested_category,
                    ai_confidence_score = 1.0,
                    user_categorized = true,
                    categorized_by = $1,
                    needs_review = false,
                    review_notes = $2,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ANY($3)
                AND tenant_id = $4
                AND ai_suggested_category IS NOT NULL
                AND ai_suggested_category IN (
                    SELECT id FROM categories WHERE tenant_id = $4
                )
            """
            result = await conn.execute(
                query,
                current_user.id,
                action.notes or "Bulk accepted AI suggestion",
                action.transaction_ids,
                tenant_id_int
            )
            
        elif action.action == "reject":
            # Reject suggestions
            query = """
                UPDATE transactions
                SET 
                    ai_suggested_category = NULL,
                    ai_confidence_score = NULL,
                    needs_review = false,
                    review_notes = $1,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ANY($2)
                AND tenant_id = $3
            """
            result = await conn.execute(
                query,
                action.notes or "Bulk rejected AI suggestion",
                action.transaction_ids,
                tenant_id_int
            )
            
        elif action.action == "categorize":
            # Apply specific category
            if not action.category_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="category_id required for categorize action"
                )
            
            query = """
                UPDATE transactions
                SET 
                    category_id = $1,
                    ai_confidence_score = 1.0,
                    user_categorized = true,
                    categorized_by = $2,
                    needs_review = false,
                    review_notes = $3,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ANY($4)
                AND tenant_id = $5
            """
            result = await conn.execute(
                query,
                action.category_id,
                current_user.id,
                action.notes or "Bulk manual categorization",
                action.transaction_ids,
                tenant_id_int
            )
        
        # Extract count
        count = int(result.split()[-1])
        
        return {
            "success": True,
            "action": action.action,
            "transactions_updated": count,
            "message": f"Successfully processed {count} transactions"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing bulk action: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing bulk action: {str(e)}"
        )


@router.post("/group-action", response_model=Dict[str, Any])
async def process_group_review_action(
    action: GroupReviewAction,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Process an action on a transaction group.
    
    Can optionally create a vendor mapping for future transactions.
    """
    logger.info(f"Processing group action: {action.action} on group {action.group_id}")
    
    try:
        # First, handle the categorization
        if action.action in ["accept", "categorize"]:
            if action.action == "accept":
                # Use suggested category - but only for categories that exist
                query = """
                    UPDATE transactions t
                    SET 
                        category_id = t.ai_suggested_category,
                        ai_confidence_score = 1.0,
                        user_categorized = true,
                        categorized_by = $1,
                        needs_review = false,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE ai_grouping_id = $2
                    AND tenant_id = $3
                    AND ai_suggested_category IS NOT NULL
                    AND ai_suggested_category IN (
                        SELECT id FROM categories WHERE tenant_id = $3
                    )
                """
                result = await conn.execute(
                    query,
                    current_user.id,
                    action.group_id,
                    tenant_id_int
                )
            else:
                # Use specified category
                if not action.category_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="category_id required for categorize action"
                    )
                
                query = """
                    UPDATE transactions
                    SET 
                        category_id = $1,
                        ai_confidence_score = 1.0,
                        user_categorized = true,
                        categorized_by = $2,
                        needs_review = false,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE ai_grouping_id = $3
                    AND tenant_id = $4
                """
                result = await conn.execute(
                    query,
                    action.category_id,
                    current_user.id,
                    action.group_id,
                    tenant_id_int
                )
        
        elif action.action == "reject":
            # Clear group and suggestions
            query = """
                UPDATE transactions
                SET 
                    ai_suggested_category = NULL,
                    ai_confidence_score = NULL,
                    ai_grouping_id = NULL,
                    needs_review = false,
                    updated_at = CURRENT_TIMESTAMP
                WHERE ai_grouping_id = $1
                AND tenant_id = $2
            """
            result = await conn.execute(
                query,
                action.group_id,
                tenant_id_int
            )
        
        count = int(result.split()[-1])
        
        # Create vendor mapping if requested
        vendor_mapping_created = False
        if action.create_vendor_mapping and action.action != "reject":
            # Get vendor pattern from group
            vendor_query = """
                SELECT 
                    ai_grouping_id,
                    MODE() WITHIN GROUP (ORDER BY description) as common_description
                FROM transactions
                WHERE ai_grouping_id = $1
                AND tenant_id = $2
                GROUP BY ai_grouping_id
            """
            vendor_result = await conn.fetchrow(vendor_query, action.group_id, tenant_id_int)
            
            if vendor_result:
                # Extract vendor name from pattern
                pattern = vendor_result['ai_grouping_id']
                if '_' in pattern:
                    vendor_name = pattern.split('_')[1]
                else:
                    vendor_name = pattern[:20]
                
                # Import vendor mapping service
                from .schemas_vendor import VendorMappingCreate
                from .user_vendor_mapping_service import user_vendor_mapping_service
                
                # Create mapping
                mapping_options = action.vendor_mapping_options or {}
                mapping = VendorMappingCreate(
                    vendor_name=vendor_name,
                    category_id=action.category_id or 0,  # Will be determined from transactions
                    applies_to_debits=mapping_options.get('applies_to_debits', True),
                    applies_to_credits=mapping_options.get('applies_to_credits', False),
                    notes=f"Created from group review of {count} transactions"
                )
                
                # If we accepted suggestions, get the category from the first transaction
                if action.action == "accept" and not action.category_id:
                    cat_query = """
                        SELECT category_id FROM transactions
                        WHERE ai_grouping_id = $1 AND tenant_id = $2
                        AND category_id IS NOT NULL
                        LIMIT 1
                    """
                    cat_result = await conn.fetchrow(cat_query, action.group_id, tenant_id_int)
                    if cat_result:
                        mapping.category_id = cat_result['category_id']
                
                if mapping.category_id:
                    mapping_result = await user_vendor_mapping_service.create_vendor_mapping(
                        mapping=mapping,
                        tenant_id=tenant_id_int,
                        user_id=current_user.id,
                        conn=conn,
                        apply_immediately=False  # Don't re-apply to just-categorized transactions
                    )
                    vendor_mapping_created = mapping_result.success
        
        return {
            "success": True,
            "action": action.action,
            "transactions_updated": count,
            "vendor_mapping_created": vendor_mapping_created,
            "message": f"Successfully processed {count} transactions in group"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing group action: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing group action: {str(e)}"
        )


@router.post("/accept-all-suggestions", response_model=Dict[str, Any])
async def accept_all_suggestions(
    upload_id: str,
    min_confidence: float = 0.85,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Accept all AI suggestions above a confidence threshold.
    
    Quick action for accepting high-confidence suggestions.
    """
    logger.info(
        f"Accepting all suggestions for upload {upload_id} "
        f"with confidence >= {min_confidence}"
    )
    
    try:
        query = """
            UPDATE transactions
            SET 
                category_id = ai_suggested_category,
                ai_confidence_score = 1.0,
                user_categorized = true,
                categorized_by = $1,
                needs_review = false,
                review_notes = $2,
                updated_at = CURRENT_TIMESTAMP
            WHERE upload_id = $3
            AND tenant_id = $4
            AND ai_suggested_category IS NOT NULL
            AND ai_confidence_score >= $5
            AND category_id IS NULL
            AND ai_suggested_category IN (
                SELECT id FROM categories WHERE tenant_id = $4
            )
        """
        
        result = await conn.execute(
            query,
            current_user.id,
            f"Bulk accepted suggestions with confidence >= {min_confidence}",
            upload_id,
            tenant_id_int,
            min_confidence
        )
        
        count = int(result.split()[-1])
        
        return {
            "success": True,
            "transactions_accepted": count,
            "min_confidence": min_confidence,
            "message": f"Accepted {count} suggestions with confidence >= {min_confidence}"
        }
        
    except Exception as e:
        logger.error(f"Error accepting suggestions: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error accepting suggestions: {str(e)}"
        )