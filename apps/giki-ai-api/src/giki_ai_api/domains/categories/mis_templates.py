"""
MIS Category Templates for Common Industries
============================================

This module provides industry-specific Income/Expense hierarchies
that follow standard MIS (Management Information System) structures
for proper P&L statement generation.

Each industry template includes:
- Standard Income categories with GL codes
- Standard Expense categories with GL codes
- Industry-specific subcategories
- Proper hierarchical structure for reporting
"""

from typing import Any, Dict, List


class MISTemplates:
    """Industry-specific MIS category templates for financial reporting."""

    @staticmethod
    def get_industry_template(industry: str) -> Dict[str, Any]:
        """
        Get MIS category template for a specific industry.

        Args:
            industry: Industry name (e.g., "Technology", "Retail", "Healthcare")

        Returns:
            Dict with Income and Expense hierarchies
        """
        industry_lower = industry.lower()

        # Map common industry variations
        industry_map = {
            "tech": "technology",
            "it": "technology",
            "software": "technology",
            "saas": "technology",
            "ecommerce": "retail",
            "e-commerce": "retail",
            "medical": "healthcare",
            "consulting": "professional_services",
            "consultancy": "professional_services",
            "finance": "financial_services",
            "fintech": "financial_services",
            "f&b": "hospitality",
            "restaurant": "hospitality",
            "hotel": "hospitality",
        }

        normalized_industry = industry_map.get(industry_lower, industry_lower)

        # Get template method
        template_method = f"_get_{normalized_industry.replace(' ', '_')}_template"
        if hasattr(MISTemplates, template_method):
            return getattr(MISTemplates, template_method)()

        # Default to general business template
        return MISTemplates._get_general_business_template()

    @staticmethod
    def _get_technology_template() -> Dict[str, Any]:
        """Technology/Software/SaaS company MIS template."""
        return {
            "industry": "Technology",
            "income": {
                "name": "Income",
                "gl_code": "4000",
                "subcategories": [
                    {
                        "name": "Software Revenue",
                        "gl_code": "4100",
                        "subcategories": [
                            {"name": "Subscription Revenue", "gl_code": "4110"},
                            {"name": "License Revenue", "gl_code": "4120"},
                            {"name": "Implementation Revenue", "gl_code": "4130"},
                            {"name": "Maintenance Revenue", "gl_code": "4140"},
                        ],
                    },
                    {
                        "name": "Service Revenue",
                        "gl_code": "4200",
                        "subcategories": [
                            {"name": "Consulting Services", "gl_code": "4210"},
                            {"name": "Training Services", "gl_code": "4220"},
                            {"name": "Support Services", "gl_code": "4230"},
                            {"name": "Custom Development", "gl_code": "4240"},
                        ],
                    },
                    {
                        "name": "Other Income",
                        "gl_code": "4900",
                        "subcategories": [
                            {"name": "Interest Income", "gl_code": "4910"},
                            {"name": "Partnership Revenue", "gl_code": "4920"},
                        ],
                    },
                ],
            },
            "expenses": {
                "name": "Expenses",
                "gl_code": "5000",
                "subcategories": [
                    {
                        "name": "Cost of Revenue",
                        "gl_code": "5100",
                        "subcategories": [
                            {"name": "Cloud Infrastructure", "gl_code": "5110"},
                            {"name": "Software Licenses", "gl_code": "5120"},
                            {"name": "Third-party Services", "gl_code": "5130"},
                            {"name": "Customer Support Costs", "gl_code": "5140"},
                        ],
                    },
                    {
                        "name": "Research & Development",
                        "gl_code": "5200",
                        "subcategories": [
                            {"name": "R&D Salaries", "gl_code": "5210"},
                            {"name": "Development Tools", "gl_code": "5220"},
                            {"name": "Testing Infrastructure", "gl_code": "5230"},
                            {"name": "Technical Training", "gl_code": "5240"},
                        ],
                    },
                    {
                        "name": "Sales & Marketing",
                        "gl_code": "5300",
                        "subcategories": [
                            {"name": "Digital Marketing", "gl_code": "5310"},
                            {"name": "Sales Commissions", "gl_code": "5320"},
                            {"name": "Marketing Software", "gl_code": "5330"},
                            {"name": "Conferences & Events", "gl_code": "5340"},
                            {"name": "Content Marketing", "gl_code": "5350"},
                        ],
                    },
                    {
                        "name": "General & Administrative",
                        "gl_code": "5400",
                        "subcategories": [
                            {"name": "Office Expenses", "gl_code": "5410"},
                            {"name": "Legal & Compliance", "gl_code": "5420"},
                            {"name": "Accounting Services", "gl_code": "5430"},
                            {"name": "Insurance", "gl_code": "5440"},
                            {"name": "Administrative Software", "gl_code": "5450"},
                        ],
                    },
                ],
            },
        }

    @staticmethod
    def _get_retail_template() -> Dict[str, Any]:
        """Retail/E-commerce company MIS template."""
        return {
            "industry": "Retail",
            "income": {
                "name": "Income",
                "gl_code": "4000",
                "subcategories": [
                    {
                        "name": "Sales Revenue",
                        "gl_code": "4100",
                        "subcategories": [
                            {"name": "Product Sales", "gl_code": "4110"},
                            {"name": "Online Sales", "gl_code": "4120"},
                            {"name": "Wholesale Revenue", "gl_code": "4130"},
                            {"name": "Returns & Refunds", "gl_code": "4140"},
                        ],
                    },
                    {
                        "name": "Service Revenue",
                        "gl_code": "4200",
                        "subcategories": [
                            {"name": "Shipping Revenue", "gl_code": "4210"},
                            {"name": "Extended Warranties", "gl_code": "4220"},
                            {"name": "Installation Services", "gl_code": "4230"},
                        ],
                    },
                ],
            },
            "expenses": {
                "name": "Expenses",
                "gl_code": "5000",
                "subcategories": [
                    {
                        "name": "Cost of Goods Sold",
                        "gl_code": "5100",
                        "subcategories": [
                            {"name": "Product Purchases", "gl_code": "5110"},
                            {"name": "Freight In", "gl_code": "5120"},
                            {"name": "Import Duties", "gl_code": "5130"},
                            {"name": "Inventory Adjustments", "gl_code": "5140"},
                        ],
                    },
                    {
                        "name": "Operating Expenses",
                        "gl_code": "5200",
                        "subcategories": [
                            {"name": "Store Operations", "gl_code": "5210"},
                            {"name": "Warehouse Costs", "gl_code": "5220"},
                            {"name": "Shipping & Delivery", "gl_code": "5230"},
                            {"name": "Payment Processing", "gl_code": "5240"},
                        ],
                    },
                    {
                        "name": "Marketing & Advertising",
                        "gl_code": "5300",
                        "subcategories": [
                            {"name": "Online Advertising", "gl_code": "5310"},
                            {"name": "Social Media Marketing", "gl_code": "5320"},
                            {"name": "Email Marketing", "gl_code": "5330"},
                            {"name": "Promotional Discounts", "gl_code": "5340"},
                        ],
                    },
                ],
            },
        }

    @staticmethod
    def _get_healthcare_template() -> Dict[str, Any]:
        """Healthcare/Medical company MIS template."""
        return {
            "industry": "Healthcare",
            "income": {
                "name": "Income",
                "gl_code": "4000",
                "subcategories": [
                    {
                        "name": "Patient Revenue",
                        "gl_code": "4100",
                        "subcategories": [
                            {"name": "Consultation Fees", "gl_code": "4110"},
                            {"name": "Procedure Revenue", "gl_code": "4120"},
                            {"name": "Diagnostic Revenue", "gl_code": "4130"},
                            {"name": "Insurance Reimbursements", "gl_code": "4140"},
                        ],
                    },
                    {
                        "name": "Other Medical Revenue",
                        "gl_code": "4200",
                        "subcategories": [
                            {"name": "Pharmacy Sales", "gl_code": "4210"},
                            {"name": "Medical Supplies", "gl_code": "4220"},
                            {"name": "Health Programs", "gl_code": "4230"},
                        ],
                    },
                ],
            },
            "expenses": {
                "name": "Expenses",
                "gl_code": "5000",
                "subcategories": [
                    {
                        "name": "Medical Costs",
                        "gl_code": "5100",
                        "subcategories": [
                            {"name": "Medical Supplies", "gl_code": "5110"},
                            {"name": "Pharmaceuticals", "gl_code": "5120"},
                            {"name": "Lab Equipment", "gl_code": "5130"},
                            {"name": "Medical Licenses", "gl_code": "5140"},
                        ],
                    },
                    {
                        "name": "Personnel Costs",
                        "gl_code": "5200",
                        "subcategories": [
                            {"name": "Medical Staff Salaries", "gl_code": "5210"},
                            {"name": "Nursing Salaries", "gl_code": "5220"},
                            {"name": "Administrative Salaries", "gl_code": "5230"},
                            {"name": "Continuing Education", "gl_code": "5240"},
                        ],
                    },
                    {
                        "name": "Facility Expenses",
                        "gl_code": "5300",
                        "subcategories": [
                            {"name": "Medical Equipment Lease", "gl_code": "5310"},
                            {"name": "Facility Maintenance", "gl_code": "5320"},
                            {"name": "Utilities", "gl_code": "5330"},
                            {"name": "Medical Waste Disposal", "gl_code": "5340"},
                        ],
                    },
                ],
            },
        }

    @staticmethod
    def _get_professional_services_template() -> Dict[str, Any]:
        """Professional Services/Consulting company MIS template."""
        return {
            "industry": "Professional Services",
            "income": {
                "name": "Income",
                "gl_code": "4000",
                "subcategories": [
                    {
                        "name": "Consulting Revenue",
                        "gl_code": "4100",
                        "subcategories": [
                            {"name": "Project Revenue", "gl_code": "4110"},
                            {"name": "Retainer Fees", "gl_code": "4120"},
                            {"name": "Hourly Billing", "gl_code": "4130"},
                            {"name": "Success Fees", "gl_code": "4140"},
                        ],
                    },
                    {
                        "name": "Other Professional Revenue",
                        "gl_code": "4200",
                        "subcategories": [
                            {"name": "Training Revenue", "gl_code": "4210"},
                            {"name": "Speaking Fees", "gl_code": "4220"},
                            {"name": "Report Sales", "gl_code": "4230"},
                        ],
                    },
                ],
            },
            "expenses": {
                "name": "Expenses",
                "gl_code": "5000",
                "subcategories": [
                    {
                        "name": "Service Delivery Costs",
                        "gl_code": "5100",
                        "subcategories": [
                            {"name": "Consultant Salaries", "gl_code": "5110"},
                            {"name": "Subcontractor Costs", "gl_code": "5120"},
                            {"name": "Project Expenses", "gl_code": "5130"},
                            {"name": "Research Tools", "gl_code": "5140"},
                        ],
                    },
                    {
                        "name": "Business Development",
                        "gl_code": "5200",
                        "subcategories": [
                            {"name": "Sales Salaries", "gl_code": "5210"},
                            {"name": "Proposal Development", "gl_code": "5220"},
                            {"name": "Client Entertainment", "gl_code": "5230"},
                            {"name": "Industry Events", "gl_code": "5240"},
                        ],
                    },
                    {
                        "name": "Professional Development",
                        "gl_code": "5300",
                        "subcategories": [
                            {"name": "Training & Certifications", "gl_code": "5310"},
                            {"name": "Professional Memberships", "gl_code": "5320"},
                            {"name": "Industry Publications", "gl_code": "5330"},
                            {"name": "Conference Attendance", "gl_code": "5340"},
                        ],
                    },
                ],
            },
        }

    @staticmethod
    def _get_hospitality_template() -> Dict[str, Any]:
        """Hospitality/Restaurant/Hotel company MIS template."""
        return {
            "industry": "Hospitality",
            "income": {
                "name": "Income",
                "gl_code": "4000",
                "subcategories": [
                    {
                        "name": "Room Revenue",
                        "gl_code": "4100",
                        "subcategories": [
                            {"name": "Room Sales", "gl_code": "4110"},
                            {"name": "Suite Revenue", "gl_code": "4120"},
                            {"name": "Extended Stay Revenue", "gl_code": "4130"},
                        ],
                    },
                    {
                        "name": "Food & Beverage Revenue",
                        "gl_code": "4200",
                        "subcategories": [
                            {"name": "Restaurant Sales", "gl_code": "4210"},
                            {"name": "Bar Revenue", "gl_code": "4220"},
                            {"name": "Room Service", "gl_code": "4230"},
                            {"name": "Catering Revenue", "gl_code": "4240"},
                        ],
                    },
                    {
                        "name": "Other Revenue",
                        "gl_code": "4300",
                        "subcategories": [
                            {"name": "Event Space Rental", "gl_code": "4310"},
                            {"name": "Spa & Amenities", "gl_code": "4320"},
                            {"name": "Parking Revenue", "gl_code": "4330"},
                        ],
                    },
                ],
            },
            "expenses": {
                "name": "Expenses",
                "gl_code": "5000",
                "subcategories": [
                    {
                        "name": "Cost of Goods Sold",
                        "gl_code": "5100",
                        "subcategories": [
                            {"name": "Food Costs", "gl_code": "5110"},
                            {"name": "Beverage Costs", "gl_code": "5120"},
                            {"name": "Guest Supplies", "gl_code": "5130"},
                            {"name": "Linen & Laundry", "gl_code": "5140"},
                        ],
                    },
                    {
                        "name": "Labor Costs",
                        "gl_code": "5200",
                        "subcategories": [
                            {"name": "Front Desk Salaries", "gl_code": "5210"},
                            {"name": "Housekeeping Wages", "gl_code": "5220"},
                            {"name": "Kitchen Staff", "gl_code": "5230"},
                            {"name": "Service Staff", "gl_code": "5240"},
                        ],
                    },
                    {
                        "name": "Property Operations",
                        "gl_code": "5300",
                        "subcategories": [
                            {"name": "Utilities", "gl_code": "5310"},
                            {"name": "Property Maintenance", "gl_code": "5320"},
                            {"name": "Property Insurance", "gl_code": "5330"},
                            {"name": "Property Taxes", "gl_code": "5340"},
                        ],
                    },
                ],
            },
        }

    @staticmethod
    def _get_general_business_template() -> Dict[str, Any]:
        """General business MIS template (default)."""
        return {
            "industry": "General Business",
            "income": {
                "name": "Income",
                "gl_code": "4000",
                "subcategories": [
                    {
                        "name": "Operating Revenue",
                        "gl_code": "4100",
                        "subcategories": [
                            {"name": "Product Sales", "gl_code": "4110"},
                            {"name": "Service Revenue", "gl_code": "4120"},
                            {"name": "Recurring Revenue", "gl_code": "4130"},
                        ],
                    },
                    {
                        "name": "Other Income",
                        "gl_code": "4900",
                        "subcategories": [
                            {"name": "Interest Income", "gl_code": "4910"},
                            {"name": "Investment Income", "gl_code": "4920"},
                            {"name": "Miscellaneous Income", "gl_code": "4990"},
                        ],
                    },
                ],
            },
            "expenses": {
                "name": "Expenses",
                "gl_code": "5000",
                "subcategories": [
                    {
                        "name": "Cost of Sales",
                        "gl_code": "5100",
                        "subcategories": [
                            {"name": "Direct Materials", "gl_code": "5110"},
                            {"name": "Direct Labor", "gl_code": "5120"},
                            {"name": "Manufacturing Overhead", "gl_code": "5130"},
                        ],
                    },
                    {
                        "name": "Operating Expenses",
                        "gl_code": "5200",
                        "subcategories": [
                            {"name": "Salaries & Wages", "gl_code": "5210"},
                            {"name": "Employee Benefits", "gl_code": "5220"},
                            {"name": "Payroll Taxes", "gl_code": "5230"},
                        ],
                    },
                    {
                        "name": "Administrative Expenses",
                        "gl_code": "5300",
                        "subcategories": [
                            {"name": "Office Supplies", "gl_code": "5310"},
                            {"name": "Professional Fees", "gl_code": "5320"},
                            {"name": "Insurance", "gl_code": "5330"},
                            {"name": "Utilities", "gl_code": "5340"},
                        ],
                    },
                    {
                        "name": "Marketing & Sales",
                        "gl_code": "5400",
                        "subcategories": [
                            {"name": "Advertising", "gl_code": "5410"},
                            {"name": "Marketing Materials", "gl_code": "5420"},
                            {"name": "Trade Shows", "gl_code": "5430"},
                            {"name": "Sales Commissions", "gl_code": "5440"},
                        ],
                    },
                ],
            },
        }

    @staticmethod
    def get_all_industries() -> List[str]:
        """Get list of all supported industries."""
        return [
            "Technology",
            "Retail",
            "Healthcare",
            "Professional Services",
            "Hospitality",
            "Financial Services",
            "Manufacturing",
            "Education",
            "Real Estate",
            "General Business",
        ]

    @staticmethod
    def create_categories_from_template(
        template: Dict[str, Any], tenant_id: int
    ) -> List[Dict[str, Any]]:
        """
        Convert a template into a list of category records ready for database insertion.

        Args:
            template: Industry template from get_industry_template()
            tenant_id: Tenant ID for the categories

        Returns:
            List of category dictionaries with proper hierarchy
        """
        categories = []

        # Process Income categories
        income_root = {
            "name": template["income"]["name"],
            "gl_code": template["income"]["gl_code"],
            "tenant_id": tenant_id,
            "level": 0,
            "parent_id": None,
            "path": "Income",
            "is_system": True,
        }
        categories.append(income_root)

        # Process Income subcategories
        for level1 in template["income"]["subcategories"]:
            level1_cat = {
                "name": level1["name"],
                "gl_code": level1["gl_code"],
                "tenant_id": tenant_id,
                "level": 1,
                "parent_name": "Income",
                "path": f"Income > {level1['name']}",
                "is_system": True,
            }
            categories.append(level1_cat)

            if "subcategories" in level1:
                for level2 in level1["subcategories"]:
                    level2_cat = {
                        "name": level2["name"],
                        "gl_code": level2["gl_code"],
                        "tenant_id": tenant_id,
                        "level": 2,
                        "parent_name": level1["name"],
                        "path": f"Income > {level1['name']} > {level2['name']}",
                        "is_system": True,
                    }
                    categories.append(level2_cat)

        # Process Expense categories
        expense_root = {
            "name": template["expenses"]["name"],
            "gl_code": template["expenses"]["gl_code"],
            "tenant_id": tenant_id,
            "level": 0,
            "parent_id": None,
            "path": "Expenses",
            "is_system": True,
        }
        categories.append(expense_root)

        # Process Expense subcategories
        for level1 in template["expenses"]["subcategories"]:
            level1_cat = {
                "name": level1["name"],
                "gl_code": level1["gl_code"],
                "tenant_id": tenant_id,
                "level": 1,
                "parent_name": "Expenses",
                "path": f"Expenses > {level1['name']}",
                "is_system": True,
            }
            categories.append(level1_cat)

            if "subcategories" in level1:
                for level2 in level1["subcategories"]:
                    level2_cat = {
                        "name": level2["name"],
                        "gl_code": level2["gl_code"],
                        "tenant_id": tenant_id,
                        "level": 2,
                        "parent_name": level1["name"],
                        "path": f"Expenses > {level1['name']} > {level2['name']}",
                        "is_system": True,
                    }
                    categories.append(level2_cat)

        return categories
