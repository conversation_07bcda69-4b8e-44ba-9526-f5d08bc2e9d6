"""
Category domain models.

Pydantic models for categories with hierarchical structure and GL code mapping.
"""

import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class Category(BaseModel):
    """
    Represents a transaction category with hierarchical structure and GL code mapping.

    Categories are always multilevel and learned from onboarding data for each tenant.
    They map to GL (General Ledger) codes for accounting integration, as different
    companies categorize transactions differently based on their accounting systems.
    """

    id: int
    name: str = Field(
        ..., max_length=200
    )  # Increased to accommodate hierarchical category names
    color: Optional[str] = Field(default="#6B7280", max_length=7)  # Hex color code

    # GL Code mapping for accounting integration
    gl_code: Optional[str] = Field(None, max_length=50)
    gl_account_name: Optional[str] = Field(None, max_length=200)
    gl_account_type: Optional[str] = Field(
        None, max_length=50
    )  # Asset, Liability, Revenue, Expense, Equity

    # Hierarchical structure - always multilevel
    parent_id: Optional[int] = None

    # Full hierarchical path for efficient queries
    path: Optional[str] = Field(
        None, max_length=500
    )  # Increased size for deeper hierarchies
    level: int = Field(default=0)  # Depth in hierarchy

    # Learning metadata - categories are learned from tenant data
    learned_from_onboarding: bool = Field(default=True)
    frequency_score: Optional[float] = Field(
        None, ge=0, le=999.99
    )  # How often this category appears
    confidence_score: Optional[float] = Field(
        None, ge=0, le=999.99
    )  # AI confidence in this categorization

    # Dynamic schema discovery metadata (NEW - for bidirectional mapping)
    original_labels: Optional[Dict[str, str]] = (
        None  # Original category labels from customer files {"file_1": "Payroll", "file_2": "Staff Costs"}
    )
    is_unified_category: bool = Field(
        default=False
    )  # True if this is unified category, False if original customer category
    unified_category_id: Optional[int] = None  # Maps original to unified category
    schema_discovery_session_id: Optional[str] = Field(
        None, max_length=100
    )  # Links to schema discovery session
    source_files: Optional[List[str]] = (
        None  # List of source files where this category was found
    )

    tenant_id: Optional[int] = None

    created_at: datetime.datetime
    updated_at: datetime.datetime

    # Note: children and parent relationships will be handled through service layer queries

    model_config = ConfigDict(from_attributes=True)

    def get_full_path(self) -> str:
        """Get the full hierarchical path of the category."""
        # This would need to be implemented in the service layer with DB queries
        return self.path or self.name

    def is_leaf(self) -> bool:
        """Check if this category is a leaf node (has no children)."""
        # This would need to be implemented in the service layer with DB queries
        return True  # Placeholder

    def is_root(self) -> bool:
        """Check if this category is a root node (has no parent)."""
        return self.parent_id is None

    # Schema discovery helper methods (NEW)
    def get_original_label_for_file(self, filename: str) -> Optional[str]:
        """Get the original category label for a specific file."""
        if not self.original_labels:
            return None
        return self.original_labels.get(filename)

    def add_original_label(self, filename: str, label: str) -> None:
        """Add an original category label for a specific file."""
        if not self.original_labels:
            self.original_labels = {}
        self.original_labels[filename] = label

    def get_all_original_labels(self) -> List[str]:
        """Get all original category labels across all files."""
        if not self.original_labels:
            return []
        return list(self.original_labels.values())

    def is_customer_original_category(self) -> bool:
        """Check if this is an original customer category (not unified)."""
        return not self.is_unified_category and bool(self.original_labels)

    def get_unified_category_name(self) -> Optional[str]:
        """Get the name of the unified category this maps to."""
        # This would need to be implemented in the service layer with DB queries
        return None  # Placeholder

    def translate_to_original(self, target_filename: Optional[str] = None) -> str:
        """
        Translate unified category back to original customer schema.

        Args:
            target_filename: Specific file to get original label for.
                          If None, returns first available original label.

        Returns:
            Original category label or current name if no mapping exists.
        """
        if target_filename and self.original_labels:
            original_label = self.original_labels.get(target_filename)
            if original_label:
                return original_label

        # Return first available original label
        if self.original_labels:
            return list(self.original_labels.values())[0]

        # Fallback to current name
        return self.name


class CategoryAccuracy(BaseModel):
    """
    Model to track baseline accuracy results over time.

    This is specifically for onboarding accuracy measurement,
    NOT for runtime categorization tracking.
    """

    id: int
    tenant_id: int
    validation_id: str = Field(
        ..., max_length=36, description="Temporal validation session ID"
    )
    month: str = Field(
        ..., max_length=7, description="Month being validated (e.g., '2024-07')"
    )
    accuracy: float = Field(
        ..., ge=0, le=100.00, description="Accuracy percentage (0.00 to 100.00)"
    )
    total_transactions: int = Field(..., description="Total transactions tested")
    correct_predictions: int = Field(
        ..., description="Number of correct AI predictions"
    )
    confusion_matrix: Optional[dict] = Field(
        None, description="Confusion matrix data for detailed analysis"
    )

    # Timestamps
    created_at: datetime.datetime

    model_config = ConfigDict(from_attributes=True)
