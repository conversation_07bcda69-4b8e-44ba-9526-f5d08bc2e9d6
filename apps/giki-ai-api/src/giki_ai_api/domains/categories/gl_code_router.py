"""
GL Code Management Router
Provides REST API endpoints for GL code operations and M3 milestone compliance.
"""

from typing import Any, Dict, List, Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from ...core.database import get_db_session
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .service import CategoryService

router = APIRouter(prefix="/gl-codes", tags=["GL Code Management"])


class GLCodeMappingRequest(BaseModel):
    category_id: int
    gl_code: str
    gl_account_name: str
    gl_account_type: str
    confidence_score: Optional[float] = None


class BulkGLCodeRequest(BaseModel):
    mappings: List[GLCodeMappingRequest]
    validate_hierarchy: bool = True


class GLCodeSuggestionRequest(BaseModel):
    category_name: str
    description: Optional[str] = None
    transaction_amount: Optional[float] = None
    existing_hierarchy: Optional[List[str]] = None


class GLCodeValidationResponse(BaseModel):
    is_valid: bool
    validation_errors: List[str]
    suggestions: List[str]
    compliance_score: float


class GLCodeHierarchyResponse(BaseModel):
    hierarchy: List[Dict[str, Any]]
    total_categories: int
    categories_with_gl_codes: int
    compliance_percentage: float


class GLCodeAnalyticsResponse(BaseModel):
    total_categories: int
    categorized_count: int
    compliance_percentage: float
    accuracy_score: float
    missing_gl_codes: List[Dict[str, Any]]
    validation_errors: List[Dict[str, Any]]
    export_readiness: Dict[str, Any]


@router.get("/hierarchy", response_model=GLCodeHierarchyResponse)
async def get_gl_code_hierarchy(
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
    include_stats: bool = Query(True, description="Include compliance statistics"),
):
    """
    Get complete GL code hierarchy with compliance metrics for M3 validation.
    """
    try:
        category_service = CategoryService(conn)

        # Get hierarchy data
        hierarchy = await category_service.get_gl_code_hierarchy(current_user.tenant_id)

        # Calculate compliance metrics
        analytics = await category_service.get_gl_code_analytics(current_user.tenant_id)

        return GLCodeHierarchyResponse(
            hierarchy=hierarchy,
            total_categories=analytics.get("total_categories", 0),
            categories_with_gl_codes=analytics.get("categorized_count", 0),
            compliance_percentage=analytics.get("compliance_percentage", 0.0),
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve GL hierarchy: {str(e)}"
        )


@router.post("/mappings")
async def bulk_update_gl_mappings(
    request: BulkGLCodeRequest,
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
):
    """
    Bulk update GL code mappings with validation and compliance checking.
    """
    try:
        category_service = CategoryService(conn)

        # Validate mappings if requested
        if request.validate_hierarchy:
            for mapping in request.mappings:
                validation = await category_service.validate_gl_code(
                    mapping.gl_code, current_user.tenant_id
                )
                if not validation["is_valid"]:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid GL code {mapping.gl_code}: {validation['errors']}",
                    )

        # Execute bulk update
        result = await category_service.bulk_update_gl_mappings(
            [
                (
                    m.category_id,
                    m.gl_code,
                    m.gl_account_name,
                    m.gl_account_type,
                    m.confidence_score,
                )
                for m in request.mappings
            ],
            current_user.tenant_id,
        )

        # Calculate updated compliance
        analytics = await category_service.get_gl_code_analytics(current_user.tenant_id)

        return {
            "success": True,
            "updated_count": result["updated_count"],
            "compliance_percentage": analytics.get("compliance_percentage", 0.0),
            "validation_results": result.get("validation_results", []),
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Bulk update failed: {str(e)}")


@router.get("/validate/{gl_code}", response_model=GLCodeValidationResponse)
async def validate_gl_code(
    gl_code: str,
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
    account_type: Optional[str] = Query(
        None, description="Expected account type for validation"
    ),
):
    """
    Validate GL code format, uniqueness, and business rules compliance.
    """
    try:
        category_service = CategoryService(conn)

        validation_result = await category_service.validate_gl_code(
            gl_code, current_user.tenant_id
        )

        return GLCodeValidationResponse(
            is_valid=validation_result["is_valid"],
            validation_errors=validation_result.get("errors", []),
            suggestions=validation_result.get("suggestions", []),
            compliance_score=validation_result.get("compliance_score", 0.0),
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


@router.post("/suggest")
async def get_gl_code_suggestions(
    request: GLCodeSuggestionRequest,
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
    limit: int = Query(5, ge=1, le=20, description="Maximum suggestions to return"),
):
    """
    Get AI-powered GL code suggestions based on category and transaction context.
    """
    try:
        category_service = CategoryService(conn)

        suggestions = await category_service.suggest_gl_codes(
            category_name=request.category_name,
            category_path=request.category_name,  # Use category_name as path for now
            account_type=None,  # Map from description if needed
            tenant_id=current_user.tenant_id,
        )

        return {
            "suggestions": suggestions,
            "confidence_threshold": 0.7,
            "recommendation": "Use suggestions with confidence > 0.7 for automatic assignment",
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Suggestion generation failed: {str(e)}"
        )


@router.get("/analytics", response_model=GLCodeAnalyticsResponse)
async def get_gl_code_analytics(
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
    include_export_validation: bool = Query(
        True, description="Include export format validation"
    ),
):
    """
    Get comprehensive GL code compliance analytics for M3 milestone tracking.
    """
    try:
        category_service = CategoryService(conn)

        analytics = await category_service.get_gl_code_analytics(current_user.tenant_id)

        # Add export validation if requested
        export_readiness = {}
        if include_export_validation:
            export_readiness = await category_service.validate_export_readiness(
                current_user.tenant_id
            )

        return GLCodeAnalyticsResponse(
            total_categories=analytics.get("total_categories", 0),
            categorized_count=analytics.get("categorized_count", 0),
            compliance_percentage=analytics.get("compliance_percentage", 0.0),
            accuracy_score=analytics.get("accuracy_score", 0.0),
            missing_gl_codes=analytics.get("missing_gl_codes", []),
            validation_errors=analytics.get("validation_errors", []),
            export_readiness=export_readiness,
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Analytics generation failed: {str(e)}"
        )


@router.post("/auto-assign")
async def auto_assign_gl_codes(
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
    confidence_threshold: float = Query(
        0.8, ge=0.1, le=1.0, description="Minimum confidence for auto-assignment"
    ),
    dry_run: bool = Query(False, description="Preview assignments without applying"),
):
    """
    Auto-assign GL codes to categories using AI with configurable confidence threshold.
    """
    try:
        category_service = CategoryService(conn)

        assignment_results = await category_service.auto_assign_gl_codes(
            tenant_id=current_user.tenant_id,
            user_id=current_user.id,  # Pass user_id as required by service
            dry_run=dry_run,
        )

        return {
            "success": True,
            "dry_run": dry_run,
            "assignments_made": assignment_results.get("assignments_made", 0),
            "total_categories": assignment_results.get("total_categories", 0),
            "confidence_threshold": confidence_threshold,
            "preview_assignments": assignment_results.get("preview_assignments", []),
            "updated_compliance": assignment_results.get("updated_compliance", 0.0),
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Auto-assignment failed: {str(e)}")


@router.get("/export/{format}")
async def export_gl_mappings(
    format: str,
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
    include_validation: bool = Query(
        True, description="Include validation data in export"
    ),
):
    """
    Export GL code mappings in various formats (CSV, JSON, QuickBooks, Sage, Xero).
    """
    try:
        category_service = CategoryService(conn)

        # Validate format
        supported_formats = ["csv", "json", "quickbooks", "sage", "xero"]
        if format.lower() not in supported_formats:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported format: {format}. Supported: {supported_formats}",
            )

        export_data = await category_service.export_gl_mappings(
            tenant_id=current_user.tenant_id,
            format=format.lower(),
            include_validation=include_validation,
        )

        # Set appropriate content type
        content_type_map = {
            "csv": "text/csv",
            "json": "application/json",
            "quickbooks": "application/vnd.intuit.quickbooks",
            "sage": "application/vnd.sage.accounting",
            "xero": "application/vnd.xero.accounting",
        }

        return JSONResponse(
            content=export_data,
            headers={
                "Content-Type": content_type_map.get(
                    format.lower(), "application/json"
                ),
                "Content-Disposition": f"attachment; filename=gl_mappings.{format.lower()}",
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")


@router.post("/import/chart-of-accounts")
async def import_chart_of_accounts(
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
    file_data: Dict[str, Any] = None,
    auto_map: bool = Query(
        True, description="Automatically map to existing categories"
    ),
):
    """
    Import chart of accounts file to create GL code hierarchy.
    """
    try:
        category_service = CategoryService(conn)

        if not file_data:
            raise HTTPException(
                status_code=400, detail="Chart of accounts data required"
            )

        import_results = await category_service.import_chart_of_accounts(
            chart_data=file_data,
            tenant_id=current_user.tenant_id,
            auto_map_categories=auto_map,
        )

        return {
            "success": True,
            "imported_gl_codes": import_results.get("imported_count", 0),
            "auto_mapped_categories": import_results.get("mapped_count", 0),
            "conflicts_detected": import_results.get("conflicts", []),
            "hierarchy_created": import_results.get("hierarchy_levels", 0),
            "compliance_improvement": import_results.get("compliance_delta", 0.0),
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Chart of accounts import failed: {str(e)}"
        )
