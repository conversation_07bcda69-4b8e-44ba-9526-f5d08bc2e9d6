"""
Transaction Grouping Service
===========================

Implements similarity-based transaction grouping for the confidence-based review queue.
Groups transactions by similarity patterns to enable bulk review and approval actions.

Based on mockup requirements:
- Groups similar transactions for bulk actions
- Analyzes description patterns, amounts, and vendors
- Supports confidence-based grouping thresholds
- Enables bulk approval workflows
"""

import logging
import re
from dataclasses import dataclass
from difflib import <PERSON>quence<PERSON>atch<PERSON>
from typing import Dict, List, Optional, Set, Tuple

from asyncpg import Connection

logger = logging.getLogger(__name__)


@dataclass
class TransactionPattern:
    """Represents a transaction pattern for grouping."""
    pattern_id: str
    description_pattern: str
    amount_range: Tuple[float, float]
    vendor_pattern: Optional[str]
    confidence_threshold: float
    sample_transactions: List[int]
    transaction_count: int
    avg_confidence: float


@dataclass
class TransactionGroup:
    """Represents a group of similar transactions."""
    group_id: str
    pattern: TransactionPattern
    transactions: List[Dict]
    suggested_category_id: Optional[int]
    avg_confidence: float
    total_amount: float
    can_bulk_approve: bool


class TransactionGroupingService:
    """Service for grouping transactions by similarity patterns."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Grouping thresholds
        self.description_similarity_threshold = 0.7
        self.amount_similarity_threshold = 0.3  # 30% variance
        self.min_group_size = 2
        self.bulk_approval_confidence_threshold = 0.85
        
        # Pattern matching rules
        self.vendor_patterns = [
            r'(\b\w+\s+\w+\b)',  # Two word combinations
            r'(\b[A-Z][a-z]+\b)',  # Capitalized words
            r'(\b\d{4,}\b)',  # Long numbers (often transaction IDs)
        ]
        
        self.common_words_to_ignore = {
            'payment', 'purchase', 'transaction', 'transfer', 'deposit',
            'withdrawal', 'fee', 'charge', 'service', 'online', 'pos',
            'debit', 'credit', 'card', 'account', 'bank', 'atm'
        }
    
    async def group_transactions_for_review(
        self,
        upload_id: str,
        tenant_id: int,
        conn: Connection,
        confidence_threshold: float = 0.7
    ) -> List[TransactionGroup]:
        """
        Group transactions by similarity for review queue.
        
        Args:
            upload_id: Upload ID to group transactions for
            tenant_id: Tenant ID
            conn: Database connection
            confidence_threshold: Minimum confidence for grouping
        
        Returns:
            List of transaction groups
        """
        self.logger.info(f"Grouping transactions for upload {upload_id}")
        
        # Get transactions that need grouping
        transactions = await self._get_transactions_for_grouping(
            upload_id, tenant_id, conn, confidence_threshold
        )
        
        if not transactions:
            self.logger.info("No transactions found for grouping")
            return []
        
        # Generate patterns and group transactions
        patterns = self._generate_transaction_patterns(transactions)
        groups = self._create_transaction_groups(patterns, transactions)
        
        # Update database with grouping results
        await self._save_grouping_results(groups, conn)
        
        self.logger.info(f"Created {len(groups)} transaction groups")
        return groups
    
    async def _get_transactions_for_grouping(
        self,
        upload_id: str,
        tenant_id: int,
        conn: Connection,
        confidence_threshold: float
    ) -> List[Dict]:
        """Get transactions that need grouping based on confidence."""
        
        query = """
            SELECT 
                id,
                date,
                description,
                amount,
                ai_suggested_category,
                ai_confidence_score,
                category_id,
                ai_grouping_id
            FROM transactions
            WHERE tenant_id = $1
            AND upload_id = $2
            AND (
                (ai_confidence_score < $3 AND ai_confidence_score > 0.3)
                OR (category_id IS NULL AND ai_suggested_category IS NULL)
            )
            AND ai_grouping_id IS NULL
            ORDER BY ABS(amount) DESC, description
        """
        
        rows = await conn.fetch(query, tenant_id, upload_id, confidence_threshold)
        
        return [
            {
                "id": row["id"],
                "date": row["date"],
                "description": row["description"],
                "amount": float(row["amount"]),
                "ai_suggested_category": row["ai_suggested_category"],
                "ai_confidence_score": float(row["ai_confidence_score"] or 0),
                "category_id": row["category_id"],
                "ai_grouping_id": row["ai_grouping_id"]
            }
            for row in rows
        ]
    
    def _generate_transaction_patterns(self, transactions: List[Dict]) -> List[TransactionPattern]:
        """Generate patterns from transactions for grouping."""
        patterns = []
        processed_transactions = set()
        
        for _i, transaction in enumerate(transactions):
            if transaction["id"] in processed_transactions:
                continue
                
            # Find similar transactions
            similar_transactions = self._find_similar_transactions(
                transaction, transactions, processed_transactions
            )
            
            if len(similar_transactions) >= self.min_group_size:
                pattern = self._create_pattern_from_transactions(similar_transactions)
                patterns.append(pattern)
                
                # Mark transactions as processed
                for txn in similar_transactions:
                    processed_transactions.add(txn["id"])
        
        return patterns
    
    def _find_similar_transactions(
        self,
        target_transaction: Dict,
        all_transactions: List[Dict],
        processed_transactions: Set[int]
    ) -> List[Dict]:
        """Find transactions similar to the target transaction."""
        similar_transactions = [target_transaction]
        
        for transaction in all_transactions:
            if (transaction["id"] == target_transaction["id"] or 
                transaction["id"] in processed_transactions):
                continue
            
            # Check description similarity
            desc_similarity = self._calculate_description_similarity(
                target_transaction["description"],
                transaction["description"]
            )
            
            # Check amount similarity
            amount_similarity = self._calculate_amount_similarity(
                target_transaction["amount"],
                transaction["amount"]
            )
            
            # Check if similar enough to group
            if (desc_similarity >= self.description_similarity_threshold or
                amount_similarity >= (1 - self.amount_similarity_threshold)):
                similar_transactions.append(transaction)
        
        return similar_transactions
    
    def _calculate_description_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity between two transaction descriptions."""
        # Normalize descriptions
        desc1_clean = self._normalize_description(desc1)
        desc2_clean = self._normalize_description(desc2)
        
        # Use sequence matcher for basic similarity
        basic_similarity = SequenceMatcher(None, desc1_clean, desc2_clean).ratio()
        
        # Check for common patterns
        pattern_similarity = self._calculate_pattern_similarity(desc1, desc2)
        
        # Return higher of the two similarities
        return max(basic_similarity, pattern_similarity)
    
    def _calculate_amount_similarity(self, amount1: float, amount2: float) -> float:
        """Calculate similarity between two amounts."""
        if amount1 == 0 and amount2 == 0:
            return 1.0
        
        if amount1 == 0 or amount2 == 0:
            return 0.0
        
        # Calculate relative difference
        diff = abs(amount1 - amount2)
        avg = (abs(amount1) + abs(amount2)) / 2
        
        if avg == 0:
            return 1.0
        
        relative_diff = diff / avg
        return max(0, 1 - relative_diff)
    
    def _normalize_description(self, description: str) -> str:
        """Normalize transaction description for comparison."""
        # Convert to lowercase
        desc = description.lower()
        
        # Remove common words
        words = desc.split()
        filtered_words = [
            word for word in words 
            if word not in self.common_words_to_ignore
        ]
        
        # Remove special characters except spaces
        desc = ' '.join(filtered_words)
        desc = re.sub(r'[^\w\s]', '', desc)
        
        # Remove extra spaces
        desc = ' '.join(desc.split())
        
        return desc
    
    def _calculate_pattern_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity based on extracted patterns."""
        patterns1 = self._extract_patterns(desc1)
        patterns2 = self._extract_patterns(desc2)
        
        if not patterns1 or not patterns2:
            return 0.0
        
        # Find common patterns
        common_patterns = set(patterns1) & set(patterns2)
        total_patterns = set(patterns1) | set(patterns2)
        
        if not total_patterns:
            return 0.0
        
        return len(common_patterns) / len(total_patterns)
    
    def _extract_patterns(self, description: str) -> List[str]:
        """Extract patterns from transaction description."""
        patterns = []
        
        for pattern_regex in self.vendor_patterns:
            matches = re.findall(pattern_regex, description)
            patterns.extend(matches)
        
        return patterns
    
    def _create_pattern_from_transactions(self, transactions: List[Dict]) -> TransactionPattern:
        """Create a transaction pattern from similar transactions."""
        # Generate pattern ID
        pattern_id = f"pattern_{transactions[0]['id']}_{len(transactions)}"
        
        # Find common description pattern
        descriptions = [txn["description"] for txn in transactions]
        description_pattern = self._find_common_pattern(descriptions)
        
        # Calculate amount range
        amounts = [txn["amount"] for txn in transactions]
        amount_range = (min(amounts), max(amounts))
        
        # Find vendor pattern
        vendor_pattern = self._find_vendor_pattern(descriptions)
        
        # Calculate average confidence
        confidences = [txn["ai_confidence_score"] for txn in transactions]
        avg_confidence = sum(confidences) / len(confidences)
        
        # Get transaction IDs
        transaction_ids = [txn["id"] for txn in transactions]
        
        return TransactionPattern(
            pattern_id=pattern_id,
            description_pattern=description_pattern,
            amount_range=amount_range,
            vendor_pattern=vendor_pattern,
            confidence_threshold=avg_confidence,
            sample_transactions=transaction_ids,
            transaction_count=len(transactions),
            avg_confidence=avg_confidence
        )
    
    def _find_common_pattern(self, descriptions: List[str]) -> str:
        """Find common pattern among descriptions."""
        if not descriptions:
            return ""
        
        if len(descriptions) == 1:
            return descriptions[0]
        
        # Find longest common substring
        common = descriptions[0]
        for desc in descriptions[1:]:
            common = self._longest_common_substring(common, desc)
        
        return common.strip() if common else descriptions[0][:20]
    
    def _longest_common_substring(self, s1: str, s2: str) -> str:
        """Find longest common substring between two strings."""
        m = len(s1)
        n = len(s2)
        
        # Create matrix for dynamic programming
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        # Variables to store length and ending position
        length = 0
        ending_position = 0
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i - 1] == s2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                    if dp[i][j] > length:
                        length = dp[i][j]
                        ending_position = i
                else:
                    dp[i][j] = 0
        
        # Extract the longest common substring
        start = ending_position - length
        return s1[start:ending_position]
    
    def _find_vendor_pattern(self, descriptions: List[str]) -> Optional[str]:
        """Find common vendor pattern in descriptions."""
        # Extract potential vendor names from all descriptions
        vendor_candidates = []
        
        for desc in descriptions:
            patterns = self._extract_patterns(desc)
            vendor_candidates.extend(patterns)
        
        if not vendor_candidates:
            return None
        
        # Find most common vendor pattern
        vendor_counts = {}
        for vendor in vendor_candidates:
            vendor_counts[vendor] = vendor_counts.get(vendor, 0) + 1
        
        # Return most common vendor if it appears in majority of transactions
        max_count = max(vendor_counts.values())
        threshold = len(descriptions) * 0.7  # 70% of transactions
        
        if max_count >= threshold:
            return max(vendor_counts, key=vendor_counts.get)
        
        return None
    
    def _create_transaction_groups(
        self,
        patterns: List[TransactionPattern],
        transactions: List[Dict]
    ) -> List[TransactionGroup]:
        """Create transaction groups from patterns."""
        groups = []
        
        for pattern in patterns:
            # Get transactions for this pattern
            group_transactions = [
                txn for txn in transactions
                if txn["id"] in pattern.sample_transactions
            ]
            
            if not group_transactions:
                continue
            
            # Calculate group statistics
            total_amount = sum(txn["amount"] for txn in group_transactions)
            avg_confidence = sum(txn["ai_confidence_score"] for txn in group_transactions) / len(group_transactions)
            
            # Determine if group can be bulk approved
            can_bulk_approve = (
                avg_confidence >= self.bulk_approval_confidence_threshold and
                len(group_transactions) >= self.min_group_size
            )
            
            # Find suggested category (most common among transactions)
            suggested_categories = [
                txn["ai_suggested_category"] for txn in group_transactions
                if txn["ai_suggested_category"] is not None
            ]
            
            suggested_category_id = None
            if suggested_categories:
                # Use most common suggested category
                category_counts = {}
                for cat_id in suggested_categories:
                    category_counts[cat_id] = category_counts.get(cat_id, 0) + 1
                suggested_category_id = max(category_counts, key=category_counts.get)
            
            # Create group
            group = TransactionGroup(
                group_id=pattern.pattern_id,
                pattern=pattern,
                transactions=group_transactions,
                suggested_category_id=suggested_category_id,
                avg_confidence=avg_confidence,
                total_amount=total_amount,
                can_bulk_approve=can_bulk_approve
            )
            
            groups.append(group)
        
        return groups
    
    async def _save_grouping_results(self, groups: List[TransactionGroup], conn: Connection):
        """Save grouping results to database."""
        for group in groups:
            # Update transactions with group ID
            transaction_ids = [txn["id"] for txn in group.transactions]
            
            if transaction_ids:
                update_query = """
                    UPDATE transactions
                    SET ai_grouping_id = $1
                    WHERE id = ANY($2)
                """
                
                await conn.execute(update_query, group.group_id, transaction_ids)
        
        self.logger.info(f"Updated {len(groups)} transaction groups in database")


# Create service instance
transaction_grouping_service = TransactionGroupingService()