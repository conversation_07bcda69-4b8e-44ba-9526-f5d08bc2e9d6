"""
Batch Categorization Service Tool
=================================

Service layer tool for batch processing transactions with MIS categorization.
This tool bridges agents and the service layer, providing batch processing
capabilities independent of any specific agent protocol.
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List

from asyncpg import Connection

from .mis_categorization_agent import MISCategorizationAgent

logger = logging.getLogger(__name__)


@dataclass
class BatchCategorizationRequest:
    """Request for batch categorization"""

    transactions: List[Dict[str, Any]]
    tenant_id: int
    use_google_search: bool = True
    batch_size: int = 10


@dataclass
class BatchCategorizationResult:
    """Result from batch categorization"""

    total_transactions: int
    categorized_count: int
    results: List[Dict[str, Any]]
    processing_time_seconds: float


class BatchCategorizationService:
    """
    Service layer for batch transaction categorization.

    This service:
    - Processes transactions in configurable batches
    - Uses MIS-focused categorization
    - Integrates with database for context
    - Returns structured results for agents
    """

    def __init__(self):
        # No stored connections - acquire per operation
        self._agents_cache: Dict[int, MISCategorizationAgent] = {}

    async def get_or_create_agent(
        self, tenant_id: int, db_conn: Connection
    ) -> MISCategorizationAgent:
        """Get cached agent or create new one for tenant"""
        if tenant_id not in self._agents_cache:
            self._agents_cache[tenant_id] = MISCategorizationAgent(tenant_id=tenant_id)
        return self._agents_cache[tenant_id]

    async def categorize_batch(
        self, request: BatchCategorizationRequest, db_conn: Connection
    ) -> BatchCategorizationResult:
        """
        Process a batch of transactions for categorization.

        This is the main service method that agents call.
        """
        start_time = datetime.now()
        results = []
        categorized_count = 0

        # Get MIS agent for this tenant
        agent = await self.get_or_create_agent(request.tenant_id, db_conn)

        # Process transactions in batches using MIS categorization
        batch_size = min(request.batch_size, 20)  # Cap at 20 for optimal performance

        for i in range(0, len(request.transactions), batch_size):
            batch = request.transactions[i : i + batch_size]

            # Process each transaction using MIS categorization
            try:
                # Process transactions in the batch
                for txn in batch:
                    try:
                        # Use MIS categorization for each transaction
                        mis_result = await agent.categorize_transaction(
                            description=txn.get("description", ""),
                            amount=float(txn.get("amount", 0)),
                            db_conn=db_conn,
                            transaction_date=txn.get("date"),
                            remarks=txn.get("remarks"),  # For M1 Nuvie hints
                        )

                        # Format MIS result
                        formatted_result = {
                            "transaction_id": txn.get("id"),
                            "category_name": mis_result.category_name,
                            "parent_category": mis_result.parent_category,
                            "confidence": mis_result.confidence,
                            "reasoning": mis_result.reasoning,
                            "gl_code": mis_result.gl_code,
                            "vendor_info": mis_result.vendor_info,
                        }

                        results.append(formatted_result)

                        if formatted_result["confidence"] > 0.5:
                            categorized_count += 1

                    except Exception as e:
                        logger.error(
                            f"Failed to categorize transaction {txn.get('id')}: {e}"
                        )
                        # Record failed transactions honestly
                        results.append(
                            {
                                "transaction_id": txn.get("id"),
                                "error": str(e),
                                "category_name": None,  # No fake categorization
                                "parent_category": None,
                                "confidence": 0.0,  # Zero confidence indicates failure
                            }
                        )

            except Exception as e:
                logger.error(f"Batch categorization failed: {e}")
                # Record batch failure honestly
                for txn in batch:
                    results.append(
                        {
                            "transaction_id": txn.get("id"),
                            "error": str(e),
                            "category_name": None,  # No fake categorization
                            "parent_category": None,
                            "confidence": 0.0,  # Zero confidence indicates failure
                        }
                    )

        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()

        return BatchCategorizationResult(
            total_transactions=len(request.transactions),
            categorized_count=categorized_count,
            results=results,
            processing_time_seconds=processing_time,
        )

    async def update_database(
        self,
        tenant_id: int,
        categorization_results: List[Dict[str, Any]],
        db_conn: Connection,
    ) -> int:
        """
        Update database with categorization results.

        Returns count of successfully updated transactions.
        """
        updated_count = 0

        for result in categorization_results:
            try:
                # Look up category ID
                category_query = """
                SELECT c.id, c.gl_code
                FROM categories c
                JOIN categories p ON c.parent_id = p.id
                WHERE c.name = $1 AND p.name = $2 AND c.tenant_id = $3
                """

                category_row = await db_conn.fetchrow(
                    category_query,
                    result["category_name"],
                    result["parent_category"],
                    tenant_id,
                )

                if category_row:
                    # Update transaction
                    update_query = """
                    UPDATE transactions
                    SET category_id = $1,
                        ai_category = $2,
                        confidence_score = $3,
                        metadata = COALESCE(metadata, '{}'::jsonb) || 
                                  jsonb_build_object(
                                      'ai_reasoning', $4,
                                      'gl_code', $5,
                                      'vendor_info', $6::jsonb
                                  )
                    WHERE id = $7
                    """

                    await db_conn.execute(
                        update_query,
                        category_row["id"],
                        result["category_name"],
                        result["confidence"],
                        result["reasoning"],
                        category_row["gl_code"],
                        result.get("vendor_info", {}),
                        result["transaction_id"],
                    )

                    updated_count += 1

            except Exception as e:
                logger.error(
                    f"Failed to update transaction {result.get('transaction_id')}: {e}"
                )

        return updated_count


# Create ADK Function Tool for agents to use
async def batch_categorize_transactions_tool(
    transactions: List[Dict[str, Any]],
    tenant_id: int,
    db_conn: Connection,
    use_google_search: bool = True,
    batch_size: int = 10,
    update_database: bool = True,
) -> Dict[str, Any]:
    """
    ADK tool function for batch transaction categorization.

    This tool can be used by any agent that needs to categorize transactions.
    It provides independence from agent protocols while leveraging service layer.

    Args:
        transactions: List of transaction dicts with id, description, amount, date
        tenant_id: Tenant ID for context
        db_conn: Database connection
        use_google_search: Whether to use Google Search for vendor info
        batch_size: Number of transactions to process at once
        update_database: Whether to update database with results

    Returns:
        Dict with categorization results and statistics
    """
    service = BatchCategorizationService()

    # Create request
    request = BatchCategorizationRequest(
        transactions=transactions,
        tenant_id=tenant_id,
        use_google_search=use_google_search,
        batch_size=batch_size,
    )

    # Process batch
    result = await service.categorize_batch(request, db_conn)

    # Update database if requested
    updated_count = 0
    if update_database:
        updated_count = await service.update_database(
            tenant_id, result.results, db_conn
        )

    return {
        "success": True,
        "total_transactions": result.total_transactions,
        "categorized_count": result.categorized_count,
        "updated_in_db": updated_count,
        "processing_time_seconds": result.processing_time_seconds,
        "results": result.results,
    }
