"""
Prompt Manager for Categorization

Follows best practices for prompt management:
- External prompt files in YAML format
- Template system with variable substitution
- Validation and error handling
- Caching for performance
- Version control and metadata
"""

import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional

import yaml

logger = logging.getLogger(__name__)


@dataclass
class PromptTemplate:
    """Represents a prompt template with metadata"""

    name: str
    description: str
    version: str
    system_instruction: str
    user_template: str
    model_params: Dict[str, Any]
    required_fields: list[str]
    response_format: str


class PromptManager:
    """Manages categorization prompts with best practices"""

    def __init__(self, prompts_dir: Optional[Path] = None):
        if prompts_dir is None:
            prompts_dir = Path(__file__).parent / "prompts"
        self.prompts_dir = prompts_dir
        self._cache: Dict[str, PromptTemplate] = {}

    def load_prompt(self, prompt_name: str) -> PromptTemplate:
        """Load and cache a prompt template"""
        if prompt_name in self._cache:
            return self._cache[prompt_name]

        prompt_file = self.prompts_dir / f"{prompt_name}.yaml"

        if not prompt_file.exists():
            raise FileNotFoundError(f"Prompt file not found: {prompt_file}")

        try:
            with open(prompt_file, "r", encoding="utf-8") as f:
                data = yaml.safe_load(f)

            template = PromptTemplate(
                name=data["name"],
                description=data["description"],
                version=data["version"],
                system_instruction=data["system_instruction"],
                user_template=data["user_template"],
                model_params=data["model_params"],
                required_fields=data["required_fields"],
                response_format=data["response_format"],
            )

            self._cache[prompt_name] = template
            logger.info(f"Loaded prompt template: {prompt_name} v{template.version}")
            return template

        except Exception as e:
            logger.error(f"Error loading prompt {prompt_name}: {e}")
            raise

    def format_prompt(
        self, prompt_name: str, **kwargs
    ) -> tuple[str, str, Dict[str, Any]]:
        """
        Format a prompt template with provided variables

        Returns:
            tuple: (system_instruction, formatted_user_prompt, model_params)
        """
        template = self.load_prompt(prompt_name)

        # Validate required fields
        missing_fields = [
            field for field in template.required_fields if field not in kwargs
        ]
        if missing_fields:
            raise ValueError(
                f"Missing required fields for prompt {prompt_name}: {missing_fields}"
            )

        try:
            formatted_user_prompt = template.user_template.format(**kwargs)
            return (
                template.system_instruction,
                formatted_user_prompt,
                template.model_params,
            )

        except KeyError as e:
            raise ValueError(
                f"Template variable not provided for prompt {prompt_name}: {e}"
            )

    def get_zero_onboarding_prompt(
        self, description: str, amount: float, transaction_type: str
    ) -> tuple[str, str, Dict[str, Any]]:
        """Get formatted zero-onboarding categorization prompt"""
        return self.format_prompt(
            "zero_onboarding",
            description=description,
            amount=amount,
            transaction_type=transaction_type,
        )

    def get_historical_data_prompt(
        self,
        description: str,
        amount: float,
        transaction_type: str,
        existing_categories_count: int,
        rag_matches_count: int,
        best_rag_confidence: float,
        rag_context: str,
        categories_context: str,
    ) -> tuple[str, str, Dict[str, Any]]:
        """Get formatted historical data categorization prompt"""
        return self.format_prompt(
            "historical_data",
            description=description,
            amount=amount,
            transaction_type=transaction_type,
            existing_categories_count=existing_categories_count,
            rag_matches_count=rag_matches_count,
            best_rag_confidence=best_rag_confidence,
            rag_context=rag_context,
            categories_context=categories_context,
        )

    def list_available_prompts(self) -> list[str]:
        """List all available prompt templates"""
        prompt_files = list(self.prompts_dir.glob("*.yaml"))
        return [f.stem for f in prompt_files]

    def validate_prompt(self, prompt_name: str) -> bool:
        """Validate that a prompt template is properly formatted"""
        try:
            template = self.load_prompt(prompt_name)

            # Check required attributes
            required_attrs = [
                "name",
                "description",
                "version",
                "system_instruction",
                "user_template",
                "model_params",
                "required_fields",
                "response_format",
            ]

            for attr in required_attrs:
                if not hasattr(template, attr):
                    logger.error(
                        f"Prompt {prompt_name} missing required attribute: {attr}"
                    )
                    return False

            # Check that user_template contains placeholders for required fields
            for field in template.required_fields:
                if f"{{{field}}}" not in template.user_template:
                    logger.error(
                        f"Prompt {prompt_name} missing placeholder for required field: {field}"
                    )
                    return False

            logger.info(f"Prompt {prompt_name} validation passed")
            return True

        except Exception as e:
            logger.error(f"Prompt {prompt_name} validation failed: {e}")
            return False


# Global prompt manager instance
prompt_manager = PromptManager()
