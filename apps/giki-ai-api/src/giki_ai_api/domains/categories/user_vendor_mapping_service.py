"""
User Vendor Mapping Service
===========================

Handles user-driven vendor-to-category mappings with credit/debit awareness.
Integrates with the transaction grouping UI for bulk categorization.

Key Features:
- User-controlled vendor mappings
- Credit/debit transaction direction awareness
- Amount range filtering
- Conflict detection and resolution
- Integration with bulk categorization UI
"""

import logging
import re
from typing import Dict, List, Optional

import asyncpg

from .schemas_vendor import (
    VendorGrouping,
    VendorMapping,
    VendorMappingCreate,
    VendorMappingResult,
)

logger = logging.getLogger(__name__)


class UserVendorMappingService:
    """Service for user-controlled vendor-to-category mappings."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def get_vendor_groupings(
        self,
        tenant_id: int,
        upload_id: Optional[str],
        user_id: int,
        conn: asyncpg.Connection,
        include_mapped: bool = False,
        min_transactions: int = 2
    ) -> List[VendorGrouping]:
        """
        Get vendor groupings for the transaction categorization UI.
        
        Groups transactions by vendor and provides categorization suggestions.
        """
        self.logger.info(f"Getting vendor groupings for tenant {tenant_id}")
        
        # Query to group transactions by vendor
        query = """
            WITH vendor_groups AS (
                SELECT 
                    -- Normalize vendor name
                    LOWER(REGEXP_REPLACE(description, '[0-9#*]+', '', 'g')) as vendor_key,
                    description as original_description,
                    COUNT(*) as transaction_count,
                    SUM(amount) as total_amount,
                    
                    -- Direction breakdown
                    COUNT(*) FILTER (WHERE amount < 0) as debit_count,
                    SUM(amount) FILTER (WHERE amount < 0) as debit_total,
                    COUNT(*) FILTER (WHERE amount > 0) as credit_count,
                    SUM(amount) FILTER (WHERE amount > 0) as credit_total,
                    
                    -- Categories used
                    ARRAY_AGG(DISTINCT c.path) FILTER (WHERE c.path IS NOT NULL) as categories_used,
                    MODE() WITHIN GROUP (ORDER BY c.path) as most_common_category,
                    
                    -- Sample transactions
                    ARRAY_AGG(
                        json_build_object(
                            'id', t.id,
                            'date', t.date,
                            'description', t.description,
                            'amount', t.amount,
                            'category_id', t.category_id,
                            'category_path', c.path
                        ) ORDER BY t.date DESC
                    ) as transactions
                    
                FROM transactions t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.tenant_id = $1
                %s
                GROUP BY vendor_key, original_description
                HAVING COUNT(*) >= $2
            ),
            vendor_mappings AS (
                SELECT 
                    vendor_name,
                    normalized_name,
                    category_id,
                    applies_to_debits,
                    applies_to_credits,
                    confidence_threshold,
                    is_active
                FROM vendor_category_mappings
                WHERE tenant_id = $1 AND is_active = true
            )
            SELECT 
                vg.*,
                vm.vendor_name as mapped_vendor_name,
                vm.category_id as mapped_category_id,
                vm.applies_to_debits,
                vm.applies_to_credits
            FROM vendor_groups vg
            LEFT JOIN vendor_mappings vm ON 
                LOWER(vm.vendor_name) = vg.vendor_key OR
                LOWER(vm.normalized_name) = vg.vendor_key
            %s
            ORDER BY vg.transaction_count DESC
        """
        
        # Add upload filter if specified
        upload_filter = "AND t.upload_id = $3" if upload_id else ""
        
        # Add mapping filter if requested
        mapping_filter = "" if include_mapped else "WHERE vm.vendor_name IS NULL"
        
        # Build parameters
        params = [tenant_id, min_transactions]
        if upload_id:
            params.append(upload_id)
        
        results = await conn.fetch(
            query % (upload_filter, mapping_filter), 
            *params
        )
        
        groupings = []
        for row in results:
            # Get the most representative vendor name
            vendor_name = self._extract_vendor_name(row['original_description'])
            
            # Build existing mapping info if present
            current_mapping = None
            if row['mapped_vendor_name']:
                current_mapping = {
                    'vendor_name': row['mapped_vendor_name'],
                    'category_id': row['mapped_category_id'],
                    'applies_to_debits': row['applies_to_debits'],
                    'applies_to_credits': row['applies_to_credits']
                }
            
            grouping = VendorGrouping(
                vendor_name=vendor_name,
                normalized_name=row['vendor_key'],
                transaction_count=row['transaction_count'],
                total_amount=float(row['total_amount']),
                debit_count=row['debit_count'],
                debit_total=float(row['debit_total'] or 0),
                credit_count=row['credit_count'],
                credit_total=float(row['credit_total'] or 0),
                categories_used=row['categories_used'] or [],
                most_common_category=row['most_common_category'],
                has_mapping=row['mapped_vendor_name'] is not None,
                current_mapping=current_mapping,
                sample_transactions=row['transactions'][:5]  # Top 5 samples
            )
            
            groupings.append(grouping)
        
        return groupings
    
    async def create_vendor_mapping(
        self,
        mapping: VendorMappingCreate,
        tenant_id: int,
        user_id: int,
        conn: asyncpg.Connection,
        apply_immediately: bool = True
    ) -> VendorMappingResult:
        """
        Create a user-defined vendor-to-category mapping.
        
        Handles credit/debit awareness and optional immediate application.
        """
        self.logger.info(
            f"Creating vendor mapping: {mapping.vendor_name} -> {mapping.category_id}"
        )
        
        try:
            # Validate category exists and belongs to tenant
            category_query = """
                SELECT id, path FROM categories
                WHERE id = $1 AND tenant_id = $2
            """
            category = await conn.fetchrow(
                category_query, mapping.category_id, tenant_id
            )
            
            if not category:
                return VendorMappingResult(
                    mappings_created=0,
                    mappings_updated=0,
                    transactions_affected=0,
                    debits_categorized=0,
                    credits_categorized=0,
                    conflicts_found=0,
                    conflict_details=[],
                    success=False,
                    message="Invalid category ID"
                )
            
            # Check for existing mapping
            existing_query = """
                SELECT id FROM vendor_category_mappings
                WHERE LOWER(vendor_name) = LOWER($1) AND tenant_id = $2
            """
            existing = await conn.fetchrow(
                existing_query, mapping.vendor_name, tenant_id
            )
            
            if existing:
                # Update existing mapping
                update_query = """
                    UPDATE vendor_category_mappings
                    SET 
                        category_id = $1,
                        applies_to_debits = $2,
                        applies_to_credits = $3,
                        description_pattern = $4,
                        min_amount = $5,
                        max_amount = $6,
                        notes = $7,
                        updated_at = CURRENT_TIMESTAMP,
                        updated_by = $8
                    WHERE id = $9
                """
                await conn.execute(
                    update_query,
                    mapping.category_id,
                    mapping.applies_to_debits,
                    mapping.applies_to_credits,
                    mapping.description_pattern,
                    mapping.min_amount,
                    mapping.max_amount,
                    mapping.notes,
                    user_id,
                    existing['id']
                )
                mappings_updated = 1
                mappings_created = 0
            else:
                # Create new mapping
                insert_query = """
                    INSERT INTO vendor_category_mappings (
                        vendor_name,
                        normalized_name,
                        category_id,
                        tenant_id,
                        applies_to_debits,
                        applies_to_credits,
                        description_pattern,
                        min_amount,
                        max_amount,
                        notes,
                        confidence_threshold,
                        created_by,
                        created_at,
                        updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, 
                             CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """
                
                normalized = self._normalize_vendor_name(mapping.vendor_name)
                await conn.execute(
                    insert_query,
                    mapping.vendor_name.lower(),
                    normalized,
                    mapping.category_id,
                    tenant_id,
                    mapping.applies_to_debits,
                    mapping.applies_to_credits,
                    mapping.description_pattern,
                    mapping.min_amount,
                    mapping.max_amount,
                    mapping.notes,
                    1.0,  # User mappings have high confidence
                    user_id
                )
                mappings_created = 1
                mappings_updated = 0
            
            # Apply to existing transactions if requested
            transactions_affected = 0
            debits_categorized = 0
            credits_categorized = 0
            
            if apply_immediately:
                result = await self._apply_vendor_mapping(
                    vendor_name=mapping.vendor_name,
                    category_id=mapping.category_id,
                    applies_to_debits=mapping.applies_to_debits,
                    applies_to_credits=mapping.applies_to_credits,
                    min_amount=mapping.min_amount,
                    max_amount=mapping.max_amount,
                    description_pattern=mapping.description_pattern,
                    tenant_id=tenant_id,
                    conn=conn
                )
                
                transactions_affected = result['total']
                debits_categorized = result['debits']
                credits_categorized = result['credits']
            
            return VendorMappingResult(
                mappings_created=mappings_created,
                mappings_updated=mappings_updated,
                transactions_affected=transactions_affected,
                debits_categorized=debits_categorized,
                credits_categorized=credits_categorized,
                conflicts_found=0,
                conflict_details=[],
                success=True,
                message=f"Successfully mapped {mapping.vendor_name} to {category['path']}"
            )
            
        except Exception as e:
            self.logger.error(f"Error creating vendor mapping: {e}", exc_info=True)
            return VendorMappingResult(
                mappings_created=0,
                mappings_updated=0,
                transactions_affected=0,
                debits_categorized=0,
                credits_categorized=0,
                conflicts_found=0,
                conflict_details=[],
                success=False,
                message=f"Error creating mapping: {str(e)}"
            )
    
    async def create_bulk_vendor_mappings(
        self,
        mappings: List[VendorMappingCreate],
        tenant_id: int,
        user_id: int,
        conn: asyncpg.Connection,
        apply_immediately: bool = True
    ) -> VendorMappingResult:
        """Create multiple vendor mappings at once."""
        
        total_created = 0
        total_updated = 0
        total_transactions = 0
        total_debits = 0
        total_credits = 0
        conflicts = []
        
        for mapping in mappings:
            result = await self.create_vendor_mapping(
                mapping=mapping,
                tenant_id=tenant_id,
                user_id=user_id,
                conn=conn,
                apply_immediately=apply_immediately
            )
            
            if result.success:
                total_created += result.mappings_created
                total_updated += result.mappings_updated
                total_transactions += result.transactions_affected
                total_debits += result.debits_categorized
                total_credits += result.credits_categorized
            else:
                conflicts.append({
                    'vendor': mapping.vendor_name,
                    'error': result.message
                })
        
        return VendorMappingResult(
            mappings_created=total_created,
            mappings_updated=total_updated,
            transactions_affected=total_transactions,
            debits_categorized=total_debits,
            credits_categorized=total_credits,
            conflicts_found=len(conflicts),
            conflict_details=conflicts,
            success=len(conflicts) == 0,
            message=f"Processed {len(mappings)} mappings, {total_created} created, "
                   f"{total_updated} updated, {len(conflicts)} conflicts"
        )
    
    async def _apply_vendor_mapping(
        self,
        vendor_name: str,
        category_id: int,
        applies_to_debits: bool,
        applies_to_credits: bool,
        min_amount: Optional[float],
        max_amount: Optional[float],
        description_pattern: Optional[str],
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> Dict[str, int]:
        """Apply a vendor mapping to existing transactions."""
        
        # Build the update query with direction awareness
        conditions = ["t.tenant_id = $1", "LOWER(t.description) LIKE LOWER($2)"]
        params = [tenant_id, f"%{vendor_name}%"]
        param_count = 2
        
        # Add direction filter
        direction_conditions = []
        if applies_to_debits:
            direction_conditions.append("t.amount < 0")
        if applies_to_credits:
            direction_conditions.append("t.amount > 0")
        
        if direction_conditions:
            conditions.append(f"({' OR '.join(direction_conditions)})")
        
        # Add amount filters
        if min_amount is not None:
            param_count += 1
            conditions.append(f"ABS(t.amount) >= ${param_count}")
            params.append(min_amount)
        
        if max_amount is not None:
            param_count += 1
            conditions.append(f"ABS(t.amount) <= ${param_count}")
            params.append(max_amount)
        
        # Add pattern filter if specified
        if description_pattern:
            param_count += 1
            conditions.append(f"t.description ~ ${param_count}")
            params.append(description_pattern)
        
        # Only update uncategorized or low-confidence transactions
        conditions.append("(t.category_id IS NULL OR t.ai_confidence_score < 0.7)")
        
        # Build the update query
        update_query = f"""
            UPDATE transactions t
            SET 
                category_id = ${param_count + 1},
                ai_confidence_score = 1.0,
                ai_suggested_category = ${param_count + 1},
                updated_at = CURRENT_TIMESTAMP
            WHERE {' AND '.join(conditions)}
            RETURNING 
                CASE WHEN amount < 0 THEN 1 ELSE 0 END as is_debit,
                CASE WHEN amount > 0 THEN 1 ELSE 0 END as is_credit
        """
        
        params.append(category_id)
        
        # Execute update and count results
        results = await conn.fetch(update_query, *params)
        
        debit_count = sum(1 for r in results if r['is_debit'])
        credit_count = sum(1 for r in results if r['is_credit'])
        
        # Update mapping usage stats
        if results:
            stats_query = """
                UPDATE vendor_category_mappings
                SET 
                    times_applied = times_applied + $1,
                    last_applied = CURRENT_TIMESTAMP
                WHERE LOWER(vendor_name) = LOWER($2) AND tenant_id = $3
            """
            await conn.execute(stats_query, len(results), vendor_name, tenant_id)
        
        return {
            'total': len(results),
            'debits': debit_count,
            'credits': credit_count
        }
    
    def _extract_vendor_name(self, description: str) -> str:
        """Extract a clean vendor name from transaction description."""
        # Remove common suffixes and clean up
        cleaned = re.sub(r'#\d+|\*[A-Z0-9]+|\.COM.*$', '', description.upper())
        cleaned = re.sub(r'[^A-Z0-9\s&]', ' ', cleaned)
        cleaned = ' '.join(cleaned.split())
        return cleaned.title()
    
    def _normalize_vendor_name(self, vendor_name: str) -> str:
        """Normalize vendor name for consistent matching."""
        return self._extract_vendor_name(vendor_name)
    
    async def get_user_vendor_mappings(
        self,
        tenant_id: int,
        user_id: int,
        conn: asyncpg.Connection,
        include_inactive: bool = False
    ) -> List[VendorMapping]:
        """Get all vendor mappings for a user."""
        
        query = """
            SELECT 
                vcm.*,
                c.path as category_path,
                u.email as created_by_email
            FROM vendor_category_mappings vcm
            JOIN categories c ON vcm.category_id = c.id
            JOIN users u ON vcm.created_by = u.id
            WHERE vcm.tenant_id = $1
            %s
            ORDER BY vcm.vendor_name
        """
        
        active_filter = "" if include_inactive else "AND vcm.is_active = true"
        
        results = await conn.fetch(query % active_filter, tenant_id)
        
        mappings = []
        for row in results:
            mapping = VendorMapping(
                id=row['id'],
                vendor_name=row['vendor_name'],
                normalized_name=row['normalized_name'],
                category_id=row['category_id'],
                category_path=row['category_path'],
                applies_to_debits=row['applies_to_debits'],
                applies_to_credits=row['applies_to_credits'],
                description_pattern=row['description_pattern'],
                min_amount=row['min_amount'],
                max_amount=row['max_amount'],
                confidence_threshold=row['confidence_threshold'],
                business_type=row['business_type'],
                notes=row['notes'],
                is_active=row['is_active'],
                times_applied=row['times_applied'],
                last_applied=row['last_applied'],
                created_at=row['created_at'],
                created_by=row['created_by'],
                updated_at=row['updated_at']
            )
            mappings.append(mapping)
        
        return mappings


# Singleton instance
user_vendor_mapping_service = UserVendorMappingService()