"""
Business Category Mapper - Real Transaction Categorization Logic
================================================================

This module provides intelligent business category mapping for transactions
based on description patterns, vendor names, and transaction characteristics.
It replaces the fake "Uncategorized" fallbacks with actual business logic.
"""

import logging
import re
from dataclasses import dataclass
from typing import Dict, List, Optional

from .category_path_resolver import CategoryPathResolver
from .confidence_scorer import confidence_scorer
from .pattern_recognition import pattern_recognizer

logger = logging.getLogger(__name__)


@dataclass
class CategoryMatch:
    """Represents a category match with confidence score."""
    category_name: str
    parent_category: str
    confidence: float
    reasoning: str
    gl_code: Optional[str] = None
    category_id: Optional[int] = None
    category_path: Optional[str] = None


class BusinessCategoryMapper:
    """
    Intelligent business category mapper using pattern recognition.
    
    This replaces fake AI responses with real business logic for categorizing
    transactions based on description patterns and vendor characteristics.
    """
    
    def __init__(self):
        self.expense_patterns = self._build_expense_patterns()
        self.income_patterns = self._build_income_patterns()
        self.vendor_mappings = self._build_vendor_mappings()
        self._path_resolver = None  # Will be initialized when needed
    
    def categorize_transaction(
        self, 
        description: str, 
        amount: float, 
        vendor: Optional[str] = None
    ) -> CategoryMatch:
        """
        Categorize a transaction using enhanced pattern recognition and business logic.
        
        Args:
            description: Transaction description
            amount: Transaction amount (negative for expenses, positive for income)
            vendor: Optional vendor name
            
        Returns:
            CategoryMatch with real categorization and confidence score
        """
        description_clean = description.lower().strip()
        
        # Determine if expense or income based on amount
        is_expense = amount < 0
        patterns = self.expense_patterns if is_expense else self.income_patterns
        parent_category = "Expenses" if is_expense else "Income"
        
        # Special handling for consulting/service income
        if amount > 0 and ("consulting" in description_clean or "ach credit" in description_clean):
            parent_category = "Income"
            patterns = self.income_patterns
        
        # Special handling for payment processors - always income
        if "stripe" in description_clean or "paypal" in description_clean or "square" in description_clean:
            parent_category = "Income"
            patterns = self.income_patterns
        
        # ENHANCED: Use pattern recognition system for intelligent categorization
        recognition_result = pattern_recognizer.recognize_patterns(description, amount)
        
        # Try all categorization methods and pick the best one
        candidates = []
        
        # 1. Try pattern recognition first (highest confidence)
        if recognition_result.primary_category and recognition_result.confidence_score > 0.6:
            # Extract category and parent from recognition result
            if ' > ' in recognition_result.primary_category:
                rec_parent = recognition_result.primary_category.split(' > ')[0]
                rec_category = recognition_result.primary_category.split(' > ')[-1]
            else:
                rec_parent = parent_category
                rec_category = recognition_result.primary_category
            
            # Use recognition result's parent if it makes sense
            if rec_parent in ["Income", "Expenses"]:
                parent_category = rec_parent
            
            candidates.append(CategoryMatch(
                category_name=rec_category,
                parent_category=parent_category,
                confidence=recognition_result.confidence_score,
                reasoning=f"Advanced pattern recognition: {recognition_result.confidence_score:.2f} confidence from {len(recognition_result.patterns)} patterns",
                gl_code=None  # Could be enhanced with GL code mapping
            ))
        
        # 2. Try vendor-specific mapping (high confidence)
        if vendor or recognition_result.vendor_identified:
            vendor_to_use = vendor or recognition_result.vendor_identified
            
            # Special handling for payment processors - force income categorization
            payment_processors = ["stripe", "paypal", "square"]
            if any(proc in vendor_to_use.lower() for proc in payment_processors):
                vendor_match = CategoryMatch(
                    category_name="Sales Revenue",
                    parent_category="Income",
                    confidence=0.95,  # Very high confidence to override pattern recognition
                    reasoning=f"Payment processor '{vendor_to_use}' - forced income categorization (high priority)"
                )
                candidates.append(vendor_match)
            else:
                vendor_match = self._match_vendor(vendor_to_use, is_expense)
                if vendor_match:
                    # Check if vendor forces income categorization (e.g., payment processors)
                    if hasattr(vendor_match, 'force_income') or "force_income" in str(vendor_match.reasoning):
                        vendor_match.parent_category = "Income"
                        patterns = self.income_patterns
                    
                    # Boost confidence if pattern recognition also identified this vendor
                    if recognition_result.vendor_identified and vendor_to_use == recognition_result.vendor_identified:
                        vendor_match.confidence = min(vendor_match.confidence + 0.1, 1.0)
                        vendor_match.reasoning += " | Confirmed by pattern recognition"
                    candidates.append(vendor_match)
        
        # 3. Try exact pattern matching (high confidence)
        exact_match = self._match_exact_patterns(description_clean, patterns, parent_category)
        if exact_match:
            candidates.append(exact_match)
        
        # 4. Try keyword-based matching (medium confidence)
        keyword_match = self._match_keywords(description_clean, patterns, parent_category)
        if keyword_match:
            candidates.append(keyword_match)
        
        # 5. Try amount-based heuristics (low confidence)
        amount_match = self._match_by_amount(amount, description_clean, parent_category)
        if amount_match:
            candidates.append(amount_match)
        
        # 6. Default to broad category (very low confidence)
        default_match = self._default_categorization(is_expense, description_clean)
        candidates.append(default_match)
        
        # Use real confidence scorer to select the best candidate
        best_match = None
        best_confidence = 0.0
        
        for candidate in candidates:
            # Special handling for payment processors - use their confidence as-is
            if "forced income categorization" in candidate.reasoning and candidate.confidence >= 0.95:
                # Payment processor categorization should override everything else
                best_match = candidate
                best_confidence = candidate.confidence
                break
            
            # Calculate real confidence using confidence scorer
            real_confidence = confidence_scorer.calculate_confidence(
                category_name=candidate.category_name,
                description=description,
                amount=amount,
                vendor=vendor or recognition_result.vendor_identified
            )
            
            # Boost confidence if multiple methods agree
            if candidate.confidence > 0.7 and real_confidence > 0.7:
                real_confidence = min(real_confidence + 0.05, 1.0)
            
            if real_confidence > best_confidence:
                best_confidence = real_confidence
                best_match = candidate
        
        # Update the selected match with real confidence and enhanced reasoning
        if best_match:
            confidence_explanation = confidence_scorer.get_confidence_explanation(
                best_match.category_name, description, amount, vendor or recognition_result.vendor_identified
            )
            
            best_match.confidence = best_confidence
            
            # Enhanced reasoning with pattern recognition insights
            pattern_insights = []
            if recognition_result.vendor_identified:
                pattern_insights.append(f"Vendor: {recognition_result.vendor_identified}")
            if recognition_result.recurring_pattern:
                pattern_insights.append("Recurring transaction")
            if len(recognition_result.patterns) > 0:
                pattern_insights.append(f"{len(recognition_result.patterns)} patterns matched")
            
            insight_text = " | ".join(pattern_insights) if pattern_insights else "No additional patterns"
            best_match.reasoning = f"{best_match.reasoning} | Real confidence: {confidence_explanation['confidence_level']} ({confidence_explanation['primary_reason']}) | {insight_text}"
        
        return best_match or default_match
    
    async def resolve_category_paths_to_ids(
        self,
        category_matches: List[CategoryMatch],
        tenant_id: int,
        db_connection
    ) -> List[CategoryMatch]:
        """
        Resolve category paths to IDs for a list of CategoryMatch objects.
        
        Args:
            category_matches: List of CategoryMatch objects to resolve
            tenant_id: Tenant ID for scoping
            db_connection: Database connection
            
        Returns:
            Updated CategoryMatch objects with resolved IDs and paths
        """
        if not self._path_resolver:
            self._path_resolver = CategoryPathResolver(db_connection)
        
        for match in category_matches:
            # Build full path from parent and category name
            if match.parent_category and match.category_name:
                full_path = f"{match.parent_category} > {match.category_name}"
            else:
                full_path = match.category_name
            
            # Resolve path to ID
            resolution = await self._path_resolver.resolve_path_to_id(
                full_path, tenant_id, create_if_missing=True
            )
            
            # Update the match with resolved information
            match.category_id = resolution.category_id
            match.category_path = resolution.category_path
            
            # Log if we created a new category
            if resolution.created_new:
                logger.info(f"Created new category: {resolution.category_path} (ID: {resolution.category_id})")
            
            # Adjust confidence if fuzzy match
            if not resolution.exact_match and resolution.confidence < match.confidence:
                match.confidence = resolution.confidence
                match.reasoning += f" | Path resolution: fuzzy match ({resolution.confidence:.2f})"
        
        return category_matches
    
    async def categorize_transaction_with_id(
        self,
        description: str,
        amount: float,
        tenant_id: int,
        db_connection,
        vendor: Optional[str] = None
    ) -> CategoryMatch:
        """
        Categorize a transaction and resolve the category path to ID.
        
        This is the main method that should be used by other services.
        """
        # Get the basic categorization
        basic_match = self.categorize_transaction(description, amount, vendor)
        
        # Resolve path to ID
        resolved_matches = await self.resolve_category_paths_to_ids(
            [basic_match], tenant_id, db_connection
        )
        
        return resolved_matches[0]
    
    def _match_vendor(self, vendor: str, is_expense: bool) -> Optional[CategoryMatch]:
        """Match based on known vendor patterns."""
        vendor_clean = vendor.lower().strip()
        
        for pattern, category_info in self.vendor_mappings.items():
            if re.search(pattern, vendor_clean):
                # Handle force_income vendors (payment processors)
                if category_info.get("force_income", False):
                    return CategoryMatch(
                        category_name=category_info["category"],
                        parent_category="Income",
                        confidence=0.9,
                        reasoning=f"Vendor '{vendor}' matched pattern '{pattern}' | force_income",
                        gl_code=category_info.get("gl_code")
                    )
                
                # Check if this vendor category is appropriate for expense/income
                elif is_expense and category_info["type"] == "expense":
                    return CategoryMatch(
                        category_name=category_info["category"],
                        parent_category="Expenses",
                        confidence=0.9,  # High confidence for vendor matches
                        reasoning=f"Vendor '{vendor}' matched pattern '{pattern}'",
                        gl_code=category_info.get("gl_code")
                    )
                elif not is_expense and category_info["type"] == "income":
                    return CategoryMatch(
                        category_name=category_info["category"],
                        parent_category="Income",
                        confidence=0.9,
                        reasoning=f"Vendor '{vendor}' matched pattern '{pattern}'",
                        gl_code=category_info.get("gl_code")
                    )
        return None
    
    def _match_exact_patterns(
        self, 
        description: str, 
        patterns: Dict, 
        parent_category: str
    ) -> Optional[CategoryMatch]:
        """Match using exact regex patterns with priority scoring."""
        matches = []
        
        for category, pattern_list in patterns.items():
            for pattern in pattern_list:
                if re.search(pattern, description):
                    # Calculate match strength based on pattern specificity
                    pattern_strength = len(pattern) / 20.0  # Longer patterns are more specific
                    confidence = min(0.8 + pattern_strength * 0.1, 0.95)
                    
                    matches.append({
                        'match': CategoryMatch(
                            category_name=category,
                            parent_category=parent_category,
                            confidence=confidence,
                            reasoning=f"Description matched pattern '{pattern}' (strength: {pattern_strength:.2f})"
                        ),
                        'strength': pattern_strength
                    })
        
        # Return the match with highest strength
        if matches:
            best_match = max(matches, key=lambda x: x['strength'])
            return best_match['match']
        
        return None
    
    def _match_keywords(
        self, 
        description: str, 
        patterns: Dict, 
        parent_category: str
    ) -> Optional[CategoryMatch]:
        """Match using weighted keyword scoring."""
        category_scores = {}
        
        for category, pattern_list in patterns.items():
            score = 0
            matched_keywords = []
            
            for pattern in pattern_list:
                # Convert regex pattern to simple keywords for scoring
                keywords = self._extract_keywords_from_pattern(pattern)
                for keyword in keywords:
                    if keyword in description:
                        # Weight keywords by importance (longer = more specific)
                        weight = max(1.0, len(keyword) / 5.0)
                        score += weight
                        matched_keywords.append(f"{keyword}({weight:.1f})")
            
            if score > 0:
                category_scores[category] = (score, matched_keywords)
        
        if category_scores:
            # Get category with highest score
            best_category = max(category_scores.keys(), key=lambda k: category_scores[k][0])
            score, keywords = category_scores[best_category]
            
            # Improved confidence calculation
            confidence = min(0.75, 0.4 + (score * 0.05))
            
            return CategoryMatch(
                category_name=best_category,
                parent_category=parent_category,
                confidence=confidence,
                reasoning=f"Keywords matched: {', '.join(keywords[:3])} (score: {score:.1f})"
            )
        
        return None
    
    def _match_by_amount(
        self, 
        amount: float, 
        description: str, 
        parent_category: str
    ) -> Optional[CategoryMatch]:
        """Match based on transaction amount patterns."""
        abs_amount = abs(amount)
        
        # Large amounts often indicate specific categories
        if abs_amount > 10000:
            if parent_category == "Expenses":
                return CategoryMatch(
                    category_name="Equipment & Assets",
                    parent_category=parent_category,
                    confidence=0.4,
                    reasoning=f"Large expense amount: ${abs_amount:,.2f}"
                )
            else:
                return CategoryMatch(
                    category_name="Sales Revenue",
                    parent_category=parent_category,
                    confidence=0.4,
                    reasoning=f"Large income amount: ${abs_amount:,.2f}"
                )
        
        # Small amounts often indicate specific categories
        elif abs_amount < 50:
            if parent_category == "Expenses":
                return CategoryMatch(
                    category_name="Miscellaneous Expenses",
                    parent_category=parent_category,
                    confidence=0.3,
                    reasoning=f"Small expense amount: ${abs_amount:,.2f}"
                )
        
        return None
    
    def _default_categorization(self, is_expense: bool, description: str) -> CategoryMatch:
        """Provide default categorization with low confidence."""
        if is_expense:
            return CategoryMatch(
                category_name="General Business Expenses",
                parent_category="Expenses",
                confidence=0.2,  # Low confidence for defaults
                reasoning="No specific pattern matched, using general business category"
            )
        else:
            return CategoryMatch(
                category_name="Other Income",
                parent_category="Income",
                confidence=0.2,
                reasoning="No specific pattern matched, using general income category"
            )
    
    def _extract_keywords_from_pattern(self, pattern: str) -> List[str]:
        """Extract keywords from regex pattern for scoring."""
        # Simple extraction - remove regex characters and split
        cleaned = re.sub(r'[\\^$*+?{}[\]|().]', ' ', pattern)
        keywords = [k.strip() for k in cleaned.split() if len(k.strip()) > 2]
        return keywords
    
    def _build_expense_patterns(self) -> Dict[str, List[str]]:
        """Build comprehensive expense category patterns."""
        return {
            "Software & Technology": [
                r"microsoft.*office|office.*365|microsoft.*subscription",
                r"adobe|google.*workspace|slack|zoom|teams",
                r"software|subscription|saas|cloud|app.*store",
                r"aws|azure|hosting|domain|github|gitlab",
                r"amazon.*web.*services|aws.*cloud|cloud.*services",
                r"license|licensing|api.*subscription"
            ],
            "Office Supplies": [
                r"office.*supply|paper|pen|ink|toner|printer(?!.*subscription)",
                r"staples(?!.*microsoft)|office.*depot(?!.*software)",
                r"copy|printing|stationery|desk.*supply"
            ],
            "Marketing & Advertising": [
                r"marketing|advertising|promotion|facebook.*ads|google.*ads",
                r"adwords|linkedin.*ads|social.*media",
                r"campaign|seo|sem|digital.*marketing"
            ],
            "Travel & Transportation": [
                r"airline|flight|hotel|uber|lyft|taxi",
                r"travel|mileage|gas|fuel|parking",
                r"car.*rental|enterprise|hertz|budget",
                r"hotel.*booking|business.*trip|accommodation",
                r"booking.*business.*trip|trip.*hotel|hotel.*business",
                r"hotel.*booking.*for.*business.*trip|business.*trip.*hotel"
            ],
            "Meals & Entertainment": [
                r"restaurant|lunch|dinner|breakfast|catering",
                r"starbucks|mcdonalds|chipotle|uber.*eats|grubhub",
                r"business.*meal|client.*dinner|client.*lunch",
                r"coffee.*meeting|meal.*meeting|dining",
                r"food.*expense|entertainment|bar|pub",
                r"business.*lunch|business.*dinner|lunch.*restaurant"
            ],
            "Utilities": [
                r"electric|electricity|power|gas|water|sewer",
                r"internet|phone|telephone|wireless",
                r"pg&e|at&t|verizon|comcast"
            ],
            "Professional Services": [
                r"legal|attorney|lawyer|consultant|accounting",
                r"professional.*service|advisory|consulting",
                r"contract.*service|freelancer"
            ],
            "Insurance": [
                r"insurance|premium|coverage|policy",
                r"liability|workers.*comp|health.*insurance",
                r"property.*insurance|auto.*insurance"
            ],
            "Banking & Finance": [
                r"bank.*fee|service.*charge|wire.*fee|atm.*fee",
                r"interest|loan.*payment|credit.*card.*fee",
                r"processing.*fee|merchant.*fee",
                r"bank.*service.*charge|banking.*fee|bank.*charge"
            ],
            "Equipment & Assets": [
                r"equipment|machinery|computer|laptop|furniture",
                r"capital.*purchase|asset|depreciation",
                r"office.*equipment|hardware"
            ]
        }
    
    def _build_income_patterns(self) -> Dict[str, List[str]]:
        """Build comprehensive income category patterns."""
        return {
            "Consulting Income": [
                r"consulting.*payment|consulting.*fee|consultant",
                r"ach.*credit.*consulting|consulting.*income",
                r"advisory.*fee|consulting.*service"
            ],
            "Sales Revenue": [
                r"sale|revenue|invoice|payment.*received",
                r"customer.*payment|client.*payment(?!.*consulting)",
                r"stripe|paypal|square|payment.*processor"
            ],
            "Service Revenue": [
                r"service(?!.*consulting)|professional.*fee",
                r"hourly|contract|project.*fee",
                r"maintenance|support.*fee|development.*payment",
                r"web.*development|development.*service|client.*payment.*development"
            ],
            "Interest Income": [
                r"interest.*income|dividend|investment.*income",
                r"bank.*interest|savings.*interest",
                r"return.*on.*investment|roi"
            ],
            "Rental Income": [
                r"rent|rental.*income|lease.*payment",
                r"property.*income|real.*estate",
                r"tenant.*payment"
            ],
            "Grants & Funding": [
                r"grant|funding|subsidy|government.*payment",
                r"sba|loan.*proceeds|investment",
                r"venture.*capital|angel.*investment"
            ],
            "Refunds & Credits": [
                r"refund|credit|rebate|cashback",
                r"tax.*refund|insurance.*claim",
                r"vendor.*credit|supplier.*credit"
            ]
        }
    
    def _build_vendor_mappings(self) -> Dict[str, Dict[str, str]]:
        """Build vendor-specific category mappings."""
        return {
            # Technology vendors
            r"microsoft|azure": {
                "category": "Software & Technology",
                "type": "expense",
                "gl_code": "6200"
            },
            r"amazon.*aws|aws": {
                "category": "Software & Technology", 
                "type": "expense",
                "gl_code": "6200"
            },
            r"google.*workspace|gmail": {
                "category": "Software & Technology",
                "type": "expense", 
                "gl_code": "6200"
            },
            
            # Office supplies
            r"staples|office.*depot": {
                "category": "Office Supplies",
                "type": "expense",
                "gl_code": "6300"
            },
            
            # Utilities
            r"pg&e|pacific.*gas": {
                "category": "Utilities",
                "type": "expense",
                "gl_code": "6400"
            },
            r"at&t|verizon|comcast": {
                "category": "Utilities",
                "type": "expense",
                "gl_code": "6400"
            },
            
            # Travel
            r"uber|lyft": {
                "category": "Travel & Transportation",
                "type": "expense",
                "gl_code": "6500"
            },
            
            # Food & beverages
            r"starbucks|coffee": {
                "category": "Meals & Entertainment",
                "type": "expense",
                "gl_code": "6600"
            },
            r"restaurant|cafe|diner": {
                "category": "Meals & Entertainment",
                "type": "expense",
                "gl_code": "6600"
            },
            
            # Additional software vendors
            r"adobe|slack|zoom|github": {
                "category": "Software & Technology",
                "type": "expense",
                "gl_code": "6200"
            },
            
            # Payment processors (income) - force income categorization
            r"stripe.*payment|stripe.*customer|paypal|square": {
                "category": "Sales Revenue",
                "type": "income",
                "gl_code": "4000",
                "force_income": True
            },
            
            # Service charge mappings
            r"bank.*service.*charge|service.*charge.*bank": {
                "category": "Banking & Finance",
                "type": "expense",
                "gl_code": "6700"
            }
        }


# Global instance for use across the application
business_category_mapper = BusinessCategoryMapper()