"""
Transaction Description Pattern Recognition System

This module provides advanced pattern recognition capabilities for categorizing
transactions based on description analysis, vendor identification, and contextual clues.
"""

import logging
import re
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class PatternType(Enum):
    """Types of patterns that can be recognized in transaction descriptions."""
    VENDOR = "vendor"
    PAYMENT_METHOD = "payment_method"
    LOCATION = "location"
    RECURRING = "recurring"
    REFUND = "refund"
    TRANSFER = "transfer"
    FEE = "fee"
    SUBSCRIPTION = "subscription"
    UTILITY = "utility"
    RETAIL = "retail"
    PROFESSIONAL_SERVICE = "professional_service"
    TRAVEL = "travel"
    FUEL = "fuel"
    RESTAURANT = "restaurant"
    GROCERY = "grocery"
    INSURANCE = "insurance"
    LOAN = "loan"
    INVESTMENT = "investment"


@dataclass
class PatternMatch:
    """Represents a pattern match in a transaction description."""
    pattern_type: PatternType
    matched_text: str
    confidence: float
    start_pos: int
    end_pos: int
    category_suggestion: Optional[str] = None
    vendor_name: Optional[str] = None


@dataclass
class RecognitionResult:
    """Complete pattern recognition result for a transaction description."""
    original_description: str
    normalized_description: str
    patterns: List[PatternMatch]
    primary_category: Optional[str]
    confidence_score: float
    vendor_identified: Optional[str]
    recurring_pattern: bool


class TransactionPatternRecognizer:
    """
    Advanced pattern recognition for transaction descriptions.
    
    Uses regex patterns, keyword matching, and contextual analysis to identify
    vendors, payment methods, transaction types, and suggest appropriate categories.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._load_patterns()
    
    def _load_patterns(self):
        """Load all pattern definitions for transaction recognition."""
        
        # Payment method patterns
        self.payment_patterns = {
            PatternType.PAYMENT_METHOD: [
                (r'\bACH\s+(?:CREDIT|DEBIT)\b', 0.95, "ACH Transfer"),
                (r'\bDEBIT\s+CARD\s+PURCHASE\b', 0.95, "Debit Card"),
                (r'\bCREDIT\s+CARD\s+PAYMENT\b', 0.95, "Credit Card"),
                (r'\bWIRE\s+TRANSFER\b', 0.95, "Wire Transfer"),
                (r'\bATM\s+WITHDRAWAL\b', 0.95, "ATM"),
                (r'\bCHECK\s+#?\d+\b', 0.90, "Check"),
                (r'\bDIRECT\s+DEPOSIT\b', 0.95, "Direct Deposit"),
                (r'\bAUTO\s+PAY\b', 0.90, "Automatic Payment"),
                (r'\bONLINE\s+PAYMENT\b', 0.85, "Online Payment"),
                (r'\bMOBILE\s+PAYMENT\b', 0.85, "Mobile Payment"),
            ]
        }
        
        # Vendor identification patterns
        self.vendor_patterns = {
            PatternType.VENDOR: [
                # Technology & Software
                (r'\b(?:MICROSOFT|MSFT)\b', 0.95, "Expenses > Technology > Software", "Microsoft"),
                (r'\b(?:GOOGLE|ALPHABET)\b', 0.95, "Expenses > Technology > Software", "Google"),
                (r'\b(?:AMAZON|AMZN)\b', 0.90, "Expenses > Office Supplies", "Amazon"),
                (r'\b(?:APPLE|AAPL)\b', 0.95, "Expenses > Technology > Software", "Apple"),
                (r'\b(?:ZOOM|ZM)\b', 0.95, "Expenses > Technology > Software", "Zoom"),
                
                # Financial Services
                (r'\b(?:PAYPAL|PP)\b', 0.95, "Expenses > Financial Services", "PayPal"),
                (r'\b(?:STRIPE|SQUARE)\b', 0.95, "Expenses > Financial Services", "Payment Processor"),
                (r'\b(?:QUICKBOOKS|QB)\b', 0.95, "Expenses > Technology > Software", "QuickBooks"),
                
                # Utilities
                (r'\b(?:ELECTRIC|ELECTRICITY|PG&E|EDISON)\b', 0.90, "Expenses > Utilities > Electricity", "Electric Utility"),
                (r'\b(?:GAS|NATURAL\s+GAS)\b', 0.85, "Expenses > Utilities > Gas", "Gas Utility"),
                (r'\b(?:WATER|H2O)\b', 0.85, "Expenses > Utilities > Water", "Water Utility"),
                (r'\b(?:INTERNET|WIFI|BROADBAND)\b', 0.85, "Expenses > Utilities > Internet", "Internet Provider"),
                (r'\b(?:PHONE|MOBILE|CELLULAR)\b', 0.80, "Expenses > Utilities > Phone", "Phone Service"),
                
                # Insurance
                (r'\b(?:INSURANCE|INSUR|INS)\b', 0.85, "Expenses > Insurance Expense", "Insurance"),
                (r'\b(?:STATE\s+FARM|GEICO|ALLSTATE)\b', 0.95, "Expenses > Insurance Expense", "Insurance Company"),
                
                # Professional Services
                (r'\b(?:ATTORNEY|LAWYER|LAW\s+FIRM)\b', 0.90, "Expenses > Professional Services > Legal", "Legal Services"),
                (r'\b(?:ACCOUNTANT|CPA|TAX\s+PREP)\b', 0.90, "Expenses > Professional Services > Accounting", "Accounting Services"),
                (r'\b(?:CONSULTANT|CONSULTING)\b', 0.85, "Expenses > Professional Services > Consulting", "Consulting"),
                
                # Transportation
                (r'\b(?:UBER|LYFT|TAXI|CAB)\b', 0.95, "Expenses > Travel > Transportation", "Ride Service"),
                (r'\b(?:SHELL|CHEVRON|EXXON|BP|MOBIL)\b', 0.95, "Expenses > Vehicle > Fuel", "Gas Station"),
                (r'\b(?:PARKING|GARAGE)\b', 0.80, "Expenses > Travel > Parking", "Parking"),
                
                # Office & Supplies
                (r'\b(?:STAPLES|OFFICE\s+DEPOT|BEST\s+BUY)\b', 0.95, "Expenses > Office Supplies", "Office Supplies"),
                (r'\b(?:FEDEX|UPS|USPS|DHL)\b', 0.95, "Expenses > Shipping", "Shipping"),
                
                # Food & Entertainment
                (r'\b(?:STARBUCKS|COFFEE)\b', 0.90, "Expenses > Meals & Entertainment", "Coffee Shop"),
                (r'\b(?:RESTAURANT|CAFE|DINER)\b', 0.80, "Expenses > Meals & Entertainment", "Restaurant"),
                (r'\b(?:GROCERY|SUPERMARKET|SAFEWAY|KROGER)\b', 0.85, "Expenses > Office Supplies", "Grocery Store"),
            ]
        }
        
        # Transaction type patterns
        self.transaction_type_patterns = {
            PatternType.RECURRING: [
                (r'\b(?:MONTHLY|RECURRING|SUBSCRIPTION|AUTO\s+RENEW)\b', 0.85, "Recurring"),
                (r'\b(?:ANNUAL|YEARLY)\b', 0.80, "Annual"),
            ],
            
            PatternType.REFUND: [
                (r'\b(?:REFUND|RETURN|REVERSAL|CREDIT)\b', 0.90, "Refund"),
                (r'\b(?:CHARGEBACK|DISPUTE)\b', 0.95, "Chargeback"),
            ],
            
            PatternType.TRANSFER: [
                (r'\b(?:TRANSFER|XFER|P2P)\b', 0.85, "Transfer"),
                (r'\b(?:FROM|TO)\s+SAVINGS\b', 0.90, "Savings Transfer"),
            ],
            
            PatternType.FEE: [
                (r'\b(?:FEE|CHARGE|SERVICE\s+FEE)\b', 0.85, "Service Fee"),
                (r'\b(?:OVERDRAFT|NSF|INSUFFICIENT)\b', 0.95, "Bank Fee"),
                (r'\b(?:ATM\s+FEE|FOREIGN\s+TRANSACTION)\b', 0.90, "Bank Fee"),
            ]
        }
        
        # Location patterns
        self.location_patterns = {
            PatternType.LOCATION: [
                (r'\b[A-Z]{2}\s+\d{5}\b', 0.70, "ZIP Code"),  # US ZIP codes
                (r'\b(?:CA|NY|TX|FL|IL|PA|OH|GA|NC|MI)\b', 0.60, "State"),  # Common states
                (r'\b(?:NYC|LA|SF|CHI|MIA|ATL|BOS|SEA|DEN|LAS)\b', 0.80, "City"),  # Major cities
            ]
        }
        
        # Amount-based category suggestions
        self.amount_patterns = {
            "small_expense": (0, 50, "Expenses > Miscellaneous"),
            "medium_expense": (50, 500, "Expenses > Office Supplies"),
            "large_expense": (500, 5000, "Expenses > Equipment"),
            "major_expense": (5000, float('inf'), "Expenses > Major Equipment"),
        }
    
    def recognize_patterns(self, description: str, amount: Optional[float] = None) -> RecognitionResult:
        """
        Perform comprehensive pattern recognition on a transaction description.
        
        Args:
            description: Transaction description to analyze
            amount: Transaction amount for context (optional)
            
        Returns:
            Complete recognition result with patterns, categories, and confidence scores
        """
        if not description:
            return RecognitionResult(
                original_description="",
                normalized_description="",
                patterns=[],
                primary_category=None,
                confidence_score=0.0,
                vendor_identified=None,
                recurring_pattern=False
            )
        
        # Normalize description
        normalized_desc = self._normalize_description(description)
        
        # Find all patterns
        all_patterns = []
        
        # Check payment methods
        payment_patterns = self._find_patterns(normalized_desc, self.payment_patterns[PatternType.PAYMENT_METHOD])
        all_patterns.extend(payment_patterns)
        
        # Check vendors
        vendor_patterns = self._find_patterns(normalized_desc, self.vendor_patterns[PatternType.VENDOR])
        all_patterns.extend(vendor_patterns)
        
        # Check transaction types
        for pattern_type, patterns in self.transaction_type_patterns.items():
            type_patterns = self._find_patterns(normalized_desc, patterns, pattern_type)
            all_patterns.extend(type_patterns)
        
        # Check locations
        location_patterns = self._find_patterns(normalized_desc, self.location_patterns[PatternType.LOCATION], PatternType.LOCATION)
        all_patterns.extend(location_patterns)
        
        # Determine primary category and vendor
        primary_category, vendor_identified = self._determine_primary_category(all_patterns, amount)
        
        # Calculate overall confidence
        confidence_score = self._calculate_confidence(all_patterns, primary_category)
        
        # Check for recurring patterns
        recurring_pattern = any(p.pattern_type == PatternType.RECURRING for p in all_patterns)
        
        return RecognitionResult(
            original_description=description,
            normalized_description=normalized_desc,
            patterns=all_patterns,
            primary_category=primary_category,
            confidence_score=confidence_score,
            vendor_identified=vendor_identified,
            recurring_pattern=recurring_pattern
        )
    
    def _normalize_description(self, description: str) -> str:
        """Normalize transaction description for better pattern matching."""
        # Convert to uppercase for consistent matching
        normalized = description.upper()
        
        # Remove common transaction prefixes/suffixes
        normalized = re.sub(r'^(?:DEBIT CARD PURCHASE|CREDIT CARD PAYMENT|ACH DEBIT|ACH CREDIT)\s*', '', normalized)
        normalized = re.sub(r'\s*(?:PENDING|POSTED|CLEARED)$', '', normalized)
        
        # Clean up extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        # Remove common date patterns
        normalized = re.sub(r'\b\d{2}/\d{2}/\d{4}\b', '', normalized)
        normalized = re.sub(r'\b\d{2}-\d{2}-\d{4}\b', '', normalized)
        
        # Remove reference numbers (but keep important identifiers)
        normalized = re.sub(r'\bREF#?\s*\w+\b', '', normalized)
        normalized = re.sub(r'\b#\d+\b', '', normalized)
        
        return normalized.strip()
    
    def _find_patterns(self, description: str, patterns: List[Tuple], pattern_type: PatternType = PatternType.VENDOR) -> List[PatternMatch]:
        """Find all matching patterns in the description."""
        matches = []
        
        for pattern, confidence, category_or_name, *extra in patterns:
            for match in re.finditer(pattern, description, re.IGNORECASE):
                matched_text = match.group()
                
                # For vendor patterns, extra[0] might contain vendor name
                vendor_name = None
                category_suggestion = category_or_name
                
                if pattern_type == PatternType.VENDOR and extra:
                    vendor_name = extra[0]
                elif pattern_type != PatternType.VENDOR:
                    # For non-vendor patterns, category_or_name is the name/type
                    category_suggestion = None
                
                pattern_match = PatternMatch(
                    pattern_type=pattern_type,
                    matched_text=matched_text,
                    confidence=confidence,
                    start_pos=match.start(),
                    end_pos=match.end(),
                    category_suggestion=category_suggestion,
                    vendor_name=vendor_name
                )
                matches.append(pattern_match)
        
        return matches
    
    def _determine_primary_category(self, patterns: List[PatternMatch], amount: Optional[float] = None) -> Tuple[Optional[str], Optional[str]]:
        """Determine the primary category and vendor from all matched patterns."""
        
        # Prioritize vendor-based categories (highest confidence)
        vendor_patterns = [p for p in patterns if p.pattern_type == PatternType.VENDOR and p.category_suggestion]
        if vendor_patterns:
            best_vendor = max(vendor_patterns, key=lambda x: x.confidence)
            return best_vendor.category_suggestion, best_vendor.vendor_name
        
        # Check for specific transaction types
        refund_patterns = [p for p in patterns if p.pattern_type == PatternType.REFUND]
        if refund_patterns:
            return "Income > Refunds", None
        
        fee_patterns = [p for p in patterns if p.pattern_type == PatternType.FEE]
        if fee_patterns:
            return "Expenses > Bank Charges", None
        
        transfer_patterns = [p for p in patterns if p.pattern_type == PatternType.TRANSFER]
        if transfer_patterns:
            return "Transfers", None
        
        # Use amount-based categorization as fallback
        if amount is not None:
            abs_amount = abs(amount)
            for _category_name, (min_amt, max_amt, category) in self.amount_patterns.items():
                if min_amt <= abs_amount < max_amt:
                    return category, None
        
        # Default fallback
        return "Expenses > Business Expense", None
    
    def _calculate_confidence(self, patterns: List[PatternMatch], primary_category: Optional[str]) -> float:
        """Calculate overall confidence score for the recognition result."""
        if not patterns:
            return 0.1  # Low confidence for no patterns
        
        # Get the highest confidence pattern
        max_confidence = max(p.confidence for p in patterns)
        
        # Boost confidence if we have multiple supporting patterns
        pattern_count_boost = min(0.1 * (len(patterns) - 1), 0.2)
        
        # Boost confidence if we have a vendor match
        vendor_boost = 0.1 if any(p.pattern_type == PatternType.VENDOR for p in patterns) else 0.0
        
        # Calculate final confidence
        final_confidence = min(max_confidence + pattern_count_boost + vendor_boost, 1.0)
        
        return final_confidence
    
    def extract_vendor_name(self, description: str) -> Optional[str]:
        """Extract vendor name from transaction description."""
        result = self.recognize_patterns(description)
        return result.vendor_identified
    
    def suggest_category(self, description: str, amount: Optional[float] = None) -> Tuple[Optional[str], float]:
        """Suggest a category for the transaction with confidence score."""
        result = self.recognize_patterns(description, amount)
        return result.primary_category, result.confidence_score
    
    def is_recurring_transaction(self, description: str) -> bool:
        """Check if transaction appears to be recurring."""
        result = self.recognize_patterns(description)
        return result.recurring_pattern
    
    def get_transaction_insights(self, description: str, amount: Optional[float] = None) -> Dict:
        """Get comprehensive insights about a transaction."""
        result = self.recognize_patterns(description, amount)
        
        insights = {
            "vendor": result.vendor_identified,
            "category": result.primary_category,
            "confidence": result.confidence_score,
            "is_recurring": result.recurring_pattern,
            "patterns_found": len(result.patterns),
            "pattern_types": [p.pattern_type.value for p in result.patterns],
            "normalized_description": result.normalized_description,
        }
        
        # Add pattern details
        insights["pattern_details"] = [
            {
                "type": p.pattern_type.value,
                "text": p.matched_text,
                "confidence": p.confidence,
                "category": p.category_suggestion,
                "vendor": p.vendor_name
            }
            for p in result.patterns
        ]
        
        return insights


# Global instance
pattern_recognizer = TransactionPatternRecognizer()


def recognize_transaction_patterns(description: str, amount: Optional[float] = None) -> RecognitionResult:
    """Convenience function for pattern recognition."""
    return pattern_recognizer.recognize_patterns(description, amount)


def extract_vendor_from_description(description: str) -> Optional[str]:
    """Convenience function to extract vendor name."""
    return pattern_recognizer.extract_vendor_name(description)


def suggest_category_from_description(description: str, amount: Optional[float] = None) -> Tuple[Optional[str], float]:
    """Convenience function to suggest category."""
    return pattern_recognizer.suggest_category(description, amount)