"""
Vendor Lookup Service - Context-Aware Google Search Integration
===============================================================

Intelligent vendor lookup service that uses Google search with proper business
context to identify vendor types and suggest appropriate categories.

Features:
- Context-aware search query building
- Business type validation
- Confidence scoring for search results
- Result caching and verification
- Fallback strategies for ambiguous results
"""

import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

import asyncpg

from .vendor_detection_service import VendorPattern

logger = logging.getLogger(__name__)


@dataclass
class VendorSearchContext:
    """Context information for vendor search."""
    
    vendor_name: str
    transaction_patterns: List[str]
    avg_amount: float
    transaction_count: int
    existing_categories: List[str]
    business_industry: Optional[str]  # User's business industry
    location: Optional[str]  # Geographic context


@dataclass
class VendorLookupResult:
    """Result from vendor lookup."""
    
    vendor_name: str
    business_type: str
    business_category: str
    confidence_score: float
    search_query_used: str
    validation_reasons: List[str]
    suggested_category_path: str
    data_source: str  # 'google', 'pattern', 'cache'


class VendorLookupService:
    """Service for intelligent vendor lookup with context validation."""
    
    # Business type keywords for validation
    BUSINESS_TYPE_KEYWORDS = {
        'restaurant': ['restaurant', 'dining', 'food', 'cafe', 'bistro', 'eatery'],
        'retail': ['store', 'shop', 'retail', 'merchant', 'mart', 'outlet'],
        'gas_station': ['gas', 'fuel', 'petrol', 'station', 'petroleum'],
        'subscription': ['subscription', 'service', 'monthly', 'saas', 'software'],
        'transportation': ['transport', 'taxi', 'uber', 'lyft', 'transit', 'ride'],
        'hotel': ['hotel', 'lodging', 'accommodation', 'inn', 'resort'],
        'airline': ['airline', 'flight', 'aviation', 'airways', 'travel'],
        'utility': ['utility', 'electric', 'gas', 'water', 'power', 'energy'],
        'telecom': ['phone', 'mobile', 'telecom', 'wireless', 'carrier', 'cellular'],
        'insurance': ['insurance', 'coverage', 'policy', 'premium'],
        'healthcare': ['medical', 'health', 'clinic', 'hospital', 'pharmacy'],
        'entertainment': ['entertainment', 'theater', 'cinema', 'gaming', 'streaming']
    }
    
    # Category mapping based on business type
    BUSINESS_TYPE_TO_CATEGORY = {
        'restaurant': 'Expenses/Meals & Entertainment/Restaurants',
        'retail': 'Expenses/Operating Expenses/General Supplies',
        'gas_station': 'Expenses/Vehicle/Fuel',
        'subscription': 'Expenses/Operating Expenses/Software & Subscriptions',
        'transportation': 'Expenses/Travel/Transportation',
        'hotel': 'Expenses/Travel/Accommodation',
        'airline': 'Expenses/Travel/Airfare',
        'utility': 'Expenses/Utilities',
        'telecom': 'Expenses/Utilities/Phone & Internet',
        'insurance': 'Expenses/Insurance',
        'healthcare': 'Expenses/Healthcare',
        'entertainment': 'Expenses/Meals & Entertainment/Entertainment'
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._cache = {}  # Simple in-memory cache
    
    async def lookup_vendor_with_context(
        self,
        vendor_pattern: VendorPattern,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> Optional[VendorLookupResult]:
        """
        Lookup vendor with intelligent context building and validation.
        
        Args:
            vendor_pattern: The vendor pattern to lookup
            tenant_id: The tenant ID
            conn: Database connection
            
        Returns:
            VendorLookupResult with validated business information
        """
        self.logger.info(f"Looking up vendor: {vendor_pattern.normalized_name}")
        
        # Check cache first
        cache_key = f"{tenant_id}:{vendor_pattern.normalized_name.lower()}"
        if cache_key in self._cache:
            cached = self._cache[cache_key]
            cached.data_source = 'cache'
            return cached
        
        # Build search context
        context = await self._build_search_context(
            vendor_pattern, tenant_id, conn
        )
        
        # Try pattern-based identification first
        pattern_result = self._identify_by_pattern(vendor_pattern, context)
        if pattern_result and pattern_result.confidence_score >= 0.9:
            self._cache[cache_key] = pattern_result
            return pattern_result
        
        # Build intelligent search query
        search_query = self._build_contextual_search_query(context)
        
        # Simulate Google search (would integrate with actual API)
        search_result = await self._perform_google_search(search_query, context)
        
        if search_result:
            # Validate search result against context
            validated_result = self._validate_search_result(
                search_result, context, search_query
            )
            
            if validated_result.confidence_score >= 0.7:
                self._cache[cache_key] = validated_result
                return validated_result
        
        # Fallback to pattern result if available
        if pattern_result:
            return pattern_result
        
        # Return low-confidence default
        return self._create_default_result(vendor_pattern, context)
    
    async def _build_search_context(
        self,
        vendor_pattern: VendorPattern,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> VendorSearchContext:
        """Build comprehensive search context."""
        # Get existing category patterns for this vendor
        category_query = """
            SELECT DISTINCT c.path
            FROM transactions t
            JOIN categories c ON t.category_id = c.id
            WHERE t.tenant_id = $1
            AND t.description LIKE $2
            LIMIT 10
        """
        categories = await conn.fetch(
            category_query,
            tenant_id,
            f"%{vendor_pattern.normalized_name}%"
        )
        
        # Get tenant's business context
        business_query = """
            SELECT business_name, industry
            FROM tenants
            WHERE id = $1
        """
        business_info = await conn.fetchrow(business_query, tenant_id)
        
        return VendorSearchContext(
            vendor_name=vendor_pattern.normalized_name,
            transaction_patterns=vendor_pattern.sample_descriptions[:3],
            avg_amount=vendor_pattern.avg_amount,
            transaction_count=vendor_pattern.transaction_count,
            existing_categories=[cat['path'] for cat in categories],
            business_industry=business_info['industry'] if business_info else None,
            location=None  # Could be enhanced with location detection
        )
    
    def _identify_by_pattern(
        self,
        vendor_pattern: VendorPattern,
        context: VendorSearchContext
    ) -> Optional[VendorLookupResult]:
        """Try to identify vendor by known patterns."""
        normalized = vendor_pattern.normalized_name.upper()
        
        # Check known patterns
        known_vendors = {
            'AMAZON': ('retail', 'E-commerce giant', 0.95),
            'STARBUCKS': ('restaurant', 'Coffee shop chain', 0.95),
            'UBER': ('transportation', 'Ride sharing service', 0.95),
            'UBER EATS': ('restaurant', 'Food delivery service', 0.95),
            'WALMART': ('retail', 'Retail chain store', 0.95),
            'TARGET': ('retail', 'Retail department store', 0.95),
            'CVS': ('healthcare', 'Pharmacy chain', 0.90),
            'WALGREENS': ('healthcare', 'Pharmacy chain', 0.90),
            'GOOGLE': ('subscription', 'Technology services', 0.85),
            'MICROSOFT': ('subscription', 'Software services', 0.85),
        }
        
        for vendor_key, (biz_type, description, confidence) in known_vendors.items():
            if vendor_key in normalized:
                return VendorLookupResult(
                    vendor_name=vendor_pattern.normalized_name,
                    business_type=biz_type,
                    business_category=description,
                    confidence_score=confidence,
                    search_query_used='pattern_matching',
                    validation_reasons=[
                        f"Matched known vendor pattern: {vendor_key}",
                        f"Transaction volume: {vendor_pattern.transaction_count}",
                        f"Average amount: ${vendor_pattern.avg_amount:.2f}"
                    ],
                    suggested_category_path=self.BUSINESS_TYPE_TO_CATEGORY.get(
                        biz_type, 'Expenses/Other'
                    ),
                    data_source='pattern'
                )
        
        return None
    
    def _build_contextual_search_query(
        self,
        context: VendorSearchContext
    ) -> str:
        """Build intelligent search query with business context."""
        # Start with vendor name
        query_parts = [context.vendor_name]
        
        # Add "what is" for business identification
        query_parts.insert(0, "what is")
        
        # Add business type qualifier
        query_parts.append("business type")
        
        # Add industry context if available
        if context.business_industry:
            query_parts.append(f"for {context.business_industry}")
        
        # Add amount context for business size hints
        if context.avg_amount > 1000:
            query_parts.append("enterprise")
        elif context.avg_amount < 50:
            query_parts.append("retail")
        
        # Build final query
        search_query = " ".join(query_parts)
        
        self.logger.info(f"Built search query: {search_query}")
        return search_query
    
    async def _perform_google_search(
        self,
        search_query: str,
        context: VendorSearchContext
    ) -> Optional[Dict[str, any]]:
        """
        Perform Google search with the query.
        
        This is a simulation - would integrate with actual Google API.
        """
        # Simulate search based on query patterns
        vendor_lower = context.vendor_name.lower()
        
        # Simulate some search results
        if 'coffee' in vendor_lower or 'cafe' in vendor_lower:
            return {
                'business_type': 'restaurant',
                'description': 'Coffee shop',
                'confidence': 0.8
            }
        elif 'mart' in vendor_lower or 'store' in vendor_lower:
            return {
                'business_type': 'retail',
                'description': 'Retail store',
                'confidence': 0.75
            }
        elif any(word in vendor_lower for word in ['gas', 'fuel', 'petro']):
            return {
                'business_type': 'gas_station',
                'description': 'Gas station',
                'confidence': 0.85
            }
        
        # Default uncertain result
        return {
            'business_type': 'unknown',
            'description': 'Business',
            'confidence': 0.3
        }
    
    def _validate_search_result(
        self,
        search_result: Dict[str, any],
        context: VendorSearchContext,
        search_query: str
    ) -> VendorLookupResult:
        """Validate search result against context."""
        business_type = search_result.get('business_type', 'unknown')
        confidence = search_result.get('confidence', 0.5)
        
        validation_reasons = []
        
        # Validate against transaction amounts
        if business_type == 'restaurant' and context.avg_amount > 200:
            confidence *= 0.7  # Reduce confidence for high restaurant amounts
            validation_reasons.append("High average amount for restaurant")
        elif business_type == 'gas_station' and context.avg_amount > 500:
            confidence *= 0.8  # Unusual for gas station
            validation_reasons.append("Unusually high amount for gas station")
        
        # Validate against existing categories
        if context.existing_categories:
            category_keywords = ' '.join(context.existing_categories).lower()
            if business_type in category_keywords:
                confidence *= 1.2  # Boost confidence
                validation_reasons.append("Matches existing category patterns")
            
        # Validate against transaction frequency
        if context.transaction_count > 50:
            confidence *= 1.1  # High frequency suggests correct identification
            validation_reasons.append(f"High transaction frequency ({context.transaction_count})")
        
        # Cap confidence at 0.95
        confidence = min(confidence, 0.95)
        
        validation_reasons.append(f"Search confidence: {search_result.get('confidence', 0.5):.2f}")
        
        return VendorLookupResult(
            vendor_name=context.vendor_name,
            business_type=business_type,
            business_category=search_result.get('description', 'Unknown business'),
            confidence_score=confidence,
            search_query_used=search_query,
            validation_reasons=validation_reasons,
            suggested_category_path=self.BUSINESS_TYPE_TO_CATEGORY.get(
                business_type, 'Expenses/Other'
            ),
            data_source='google'
        )
    
    def _create_default_result(
        self,
        vendor_pattern: VendorPattern,
        context: VendorSearchContext
    ) -> VendorLookupResult:
        """Create default low-confidence result."""
        # Try to guess based on amount patterns
        if context.avg_amount < 20:
            business_type = 'retail'
            category = 'Expenses/Operating Expenses/General Supplies'
        elif context.avg_amount < 100:
            business_type = 'restaurant'
            category = 'Expenses/Meals & Entertainment'
        elif context.avg_amount > 1000:
            business_type = 'subscription'
            category = 'Expenses/Operating Expenses/Software & Subscriptions'
        else:
            business_type = 'unknown'
            category = 'Expenses/Other'
        
        return VendorLookupResult(
            vendor_name=vendor_pattern.normalized_name,
            business_type=business_type,
            business_category='Unknown vendor type',
            confidence_score=0.5,
            search_query_used='fallback_pattern_analysis',
            validation_reasons=[
                f"Based on average amount: ${context.avg_amount:.2f}",
                f"Transaction count: {context.transaction_count}",
                "No confident match found"
            ],
            suggested_category_path=category,
            data_source='pattern'
        )
    
    async def batch_lookup_vendors(
        self,
        vendors: List[Dict[str, any]],
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> List[VendorLookupResult]:
        """
        Batch lookup multiple vendors efficiently.
        
        Args:
            vendors: List of vendor data from get_vendors_for_google_lookup
            tenant_id: The tenant ID
            conn: Database connection
            
        Returns:
            List of lookup results
        """
        results = []
        
        for vendor_data in vendors:
            # Convert to VendorPattern
            pattern = VendorPattern(
                pattern=vendor_data['vendor_name'],
                normalized_name=vendor_data['vendor_name'],
                transaction_count=vendor_data['transaction_count'],
                total_amount=vendor_data['total_amount'],
                avg_amount=vendor_data['total_amount'] / vendor_data['transaction_count'],
                category_variations=0,  # Not used here
                confidence_score=vendor_data['avg_confidence'],
                sample_descriptions=[vendor_data['sample_description']]
            )
            
            result = await self.lookup_vendor_with_context(
                pattern, tenant_id, conn
            )
            
            if result:
                results.append(result)
        
        return results
    
    async def apply_lookup_results(
        self,
        lookup_results: List[VendorLookupResult],
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> Tuple[int, float]:
        """
        Apply lookup results to create vendor mappings.
        
        Args:
            lookup_results: Results from vendor lookup
            tenant_id: The tenant ID
            conn: Database connection
            
        Returns:
            Tuple of (mappings_created, accuracy_improvement)
        """
        mappings_created = 0
        high_confidence_mappings = 0
        
        for result in lookup_results:
            if result.confidence_score >= 0.7:
                # Get category ID for suggested path
                category_query = """
                    SELECT id FROM categories
                    WHERE tenant_id = $1 AND path = $2
                """
                category = await conn.fetchrow(
                    category_query,
                    tenant_id,
                    result.suggested_category_path
                )
                
                if category:
                    # Create vendor mapping
                    try:
                        await conn.execute("""
                            INSERT INTO vendor_category_mappings (
                                vendor_name,
                                normalized_name,
                                category_id,
                                tenant_id,
                                business_type,
                                confidence_threshold,
                                created_at
                            ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
                            ON CONFLICT (vendor_name, tenant_id) DO NOTHING
                        """,
                            result.vendor_name.lower(),
                            result.vendor_name,
                            category['id'],
                            tenant_id,
                            result.business_type,
                            result.confidence_score
                        )
                        
                        mappings_created += 1
                        if result.confidence_score >= 0.85:
                            high_confidence_mappings += 1
                        
                        self.logger.info(
                            f"Created mapping: {result.vendor_name} -> "
                            f"{result.suggested_category_path} "
                            f"(confidence: {result.confidence_score:.2f})"
                        )
                        
                    except Exception as e:
                        self.logger.warning(
                            f"Failed to create mapping for {result.vendor_name}: {e}"
                        )
        
        # Calculate accuracy improvement
        # High confidence mappings provide more improvement
        accuracy_improvement = (
            high_confidence_mappings * 0.8 +
            (mappings_created - high_confidence_mappings) * 0.4
        )
        accuracy_improvement = min(accuracy_improvement, 10.0)
        
        return mappings_created, accuracy_improvement


# Singleton instance
vendor_lookup_service = VendorLookupService()