"""
MIS Improvement Router
======================

API endpoints for managing MIS accuracy improvements and enhancement workflows.
Handles accuracy tracking, improvement recommendations, and enhancement options.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from ...core.dependencies import get_current_tenant_id, get_db_session
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user

logger = logging.getLogger(__name__)


# Request/Response schemas
class AccuracyMetrics(BaseModel):
    """Current accuracy metrics for a tenant."""
    
    current_accuracy: float
    baseline_accuracy: float
    target_accuracy: float
    improvement_potential: float
    total_transactions: int
    categorized_transactions: int
    confidence_distribution: Dict[str, int]


class AccuracyImprovement(BaseModel):
    """Single accuracy improvement event."""
    
    timestamp: datetime
    previous_accuracy: float
    new_accuracy: float
    improvement: float
    source: str
    description: str
    transactions_affected: int


class EnhancementOption(BaseModel):
    """Available enhancement option."""
    
    id: str
    title: str
    description: str
    accuracy_improvement: float
    time_estimate: str
    difficulty: str
    status: str
    requirements: List[str]
    benefits: List[str]


class EnhancementRequest(BaseModel):
    """Request to start an enhancement."""
    
    enhancement_id: str
    parameters: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None


class CompanyProfileData(BaseModel):
    """Company profile data for enhancement."""
    
    company_name: str
    industry: str
    business_type: str
    employee_count: Optional[int] = None
    annual_revenue: Optional[str] = None
    common_vendors: List[str] = []
    business_categories: List[str] = []


class CategoryRuleData(BaseModel):
    """Category rule data for enhancement."""
    
    vendor_pattern: str
    category_id: int
    applies_to_debits: bool = True
    applies_to_credits: bool = False
    amount_threshold: Optional[float] = None
    notes: Optional[str] = None


router = APIRouter(
    prefix="/improvements",
    tags=["MIS Improvements"],
    responses={
        404: {"description": "Not found"},
        401: {"description": "Not authenticated"},
        403: {"description": "Not authorized"},
    },
)


@router.get("/metrics", response_model=AccuracyMetrics)
async def get_accuracy_metrics(
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Get current accuracy metrics for the tenant.
    
    Returns comprehensive accuracy statistics including current performance,
    improvement potential, and confidence distribution.
    """
    logger.info(f"Getting accuracy metrics for tenant {tenant_id_int}")
    
    try:
        # Get overall transaction statistics
        stats_query = """
            SELECT 
                COUNT(*) as total_transactions,
                COUNT(CASE WHEN category_id IS NOT NULL THEN 1 END) as categorized_transactions,
                AVG(CASE WHEN ai_confidence_score IS NOT NULL THEN ai_confidence_score END) as avg_confidence,
                COUNT(CASE WHEN ai_confidence_score >= 0.9 THEN 1 END) as high_confidence,
                COUNT(CASE WHEN ai_confidence_score >= 0.7 AND ai_confidence_score < 0.9 THEN 1 END) as medium_confidence,
                COUNT(CASE WHEN ai_confidence_score < 0.7 THEN 1 END) as low_confidence
            FROM transactions
            WHERE tenant_id = $1
        """
        
        stats = await conn.fetchrow(stats_query, tenant_id_int)
        
        # Calculate current accuracy (percentage of transactions categorized with confidence)
        total_transactions = stats['total_transactions']
        categorized_transactions = stats['categorized_transactions']
        
        if total_transactions > 0:
            current_accuracy = (categorized_transactions / total_transactions) * 100
            avg_confidence = float(stats['avg_confidence'] or 0)
            
            # Weight accuracy by confidence scores
            if avg_confidence > 0:
                current_accuracy = current_accuracy * (avg_confidence / 1.0)
        else:
            current_accuracy = 0
            avg_confidence = 0
        
        # Calculate improvement potential
        baseline_accuracy = 87.0  # Standard baseline
        target_accuracy = 95.0    # Standard target
        improvement_potential = max(0, target_accuracy - current_accuracy)
        
        # Confidence distribution
        confidence_distribution = {
            "high": stats['high_confidence'],
            "medium": stats['medium_confidence'],
            "low": stats['low_confidence']
        }
        
        return AccuracyMetrics(
            current_accuracy=current_accuracy,
            baseline_accuracy=baseline_accuracy,
            target_accuracy=target_accuracy,
            improvement_potential=improvement_potential,
            total_transactions=total_transactions,
            categorized_transactions=categorized_transactions,
            confidence_distribution=confidence_distribution
        )
        
    except Exception as e:
        logger.error(f"Error getting accuracy metrics: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving accuracy metrics: {str(e)}"
        )


@router.get("/history", response_model=List[AccuracyImprovement])
async def get_improvement_history(
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Get accuracy improvement history for the tenant.
    
    Returns a list of improvement events with details about accuracy changes.
    """
    logger.info(f"Getting improvement history for tenant {tenant_id_int}")
    
    try:
        # This would typically come from an accuracy_improvements table
        # For now, we'll simulate based on transaction update history
        
        history_query = """
            SELECT 
                DATE_TRUNC('hour', updated_at) as improvement_time,
                COUNT(*) as transactions_affected,
                AVG(ai_confidence_score) as avg_confidence,
                MAX(updated_at) as last_update
            FROM transactions
            WHERE tenant_id = $1
            AND user_categorized = true
            AND updated_at >= NOW() - INTERVAL '30 days'
            GROUP BY DATE_TRUNC('hour', updated_at)
            ORDER BY improvement_time DESC
            LIMIT $2
        """
        
        history_rows = await conn.fetch(history_query, tenant_id_int, limit)
        
        improvements = []
        previous_accuracy = 87.0  # Starting baseline
        
        for row in history_rows:
            # Simulate accuracy improvement based on transaction updates
            confidence_boost = (float(row['avg_confidence']) - 0.7) * 10
            accuracy_improvement = min(2.0, max(0.1, confidence_boost))
            new_accuracy = previous_accuracy + accuracy_improvement
            
            improvement = AccuracyImprovement(
                timestamp=row['improvement_time'],
                previous_accuracy=previous_accuracy,
                new_accuracy=new_accuracy,
                improvement=accuracy_improvement,
                source="Manual Review",
                description=f"Reviewed and updated {row['transactions_affected']} transactions",
                transactions_affected=row['transactions_affected']
            )
            
            improvements.append(improvement)
            previous_accuracy = new_accuracy
        
        return improvements
        
    except Exception as e:
        logger.error(f"Error getting improvement history: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving improvement history: {str(e)}"
        )


@router.get("/enhancement-options", response_model=List[EnhancementOption])
async def get_enhancement_options(
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Get available enhancement options for the tenant.
    
    Returns a list of enhancement options with current status and requirements.
    """
    logger.info(f"Getting enhancement options for tenant {tenant_id_int}")
    
    try:
        # Check what enhancements are available based on current data
        stats_query = """
            SELECT 
                COUNT(*) as total_transactions,
                COUNT(CASE WHEN ai_confidence_score >= 0.7 AND ai_confidence_score < 0.9 THEN 1 END) as medium_confidence,
                COUNT(CASE WHEN ai_confidence_score < 0.7 THEN 1 END) as low_confidence,
                COUNT(DISTINCT description) as unique_descriptions,
                COUNT(CASE WHEN user_categorized = true THEN 1 END) as user_reviewed
            FROM transactions
            WHERE tenant_id = $1
        """
        
        stats = await conn.fetchrow(stats_query, tenant_id_int)
        
        # Check if company profile exists
        profile_query = """
            SELECT company_name FROM tenant_settings 
            WHERE tenant_id = $1 AND company_name IS NOT NULL
        """
        has_profile = await conn.fetchrow(profile_query, tenant_id_int)
        
        # Check if custom rules exist
        rules_query = """
            SELECT COUNT(*) as rule_count FROM vendor_category_mappings
            WHERE tenant_id = $1
        """
        rules_count = await conn.fetchval(rules_query, tenant_id_int)
        
        # Build enhancement options
        options = []
        
        # Quick Review
        if stats['medium_confidence'] > 0:
            options.append(EnhancementOption(
                id="quick-review",
                title="Quick Review",
                description="Review and approve medium-confidence categorizations in bulk",
                accuracy_improvement=8.0,
                time_estimate="5-10 minutes",
                difficulty="easy",
                status="available",
                requirements=["Transactions with 70-89% confidence available"],
                benefits=[
                    "Immediate accuracy boost",
                    "Bulk approval workflow",
                    "Review medium-confidence transactions",
                    "Quick wins for common patterns"
                ]
            ))
        
        # Company Profile
        options.append(EnhancementOption(
            id="company-profile",
            title="Company Profile",
            description="Provide business context to improve industry-specific categorization",
            accuracy_improvement=15.0,
            time_estimate="15-20 minutes",
            difficulty="medium",
            status="completed" if has_profile else "available",
            requirements=["Business information", "Industry classification"],
            benefits=[
                "Industry-specific categories",
                "Business context awareness",
                "Vendor pattern recognition",
                "Seasonal adjustment capability"
            ]
        ))
        
        # Category Rules
        options.append(EnhancementOption(
            id="category-rules",
            title="Category Rules",
            description="Create custom rules for specific vendors and transaction patterns",
            accuracy_improvement=12.0,
            time_estimate="10-15 minutes",
            difficulty="advanced",
            status="completed" if rules_count > 0 else "available",
            requirements=["Identified vendor patterns", "Custom categorization needs"],
            benefits=[
                "Custom vendor mappings",
                "Rule-based categorization",
                "Exception handling",
                "Future-proof accuracy"
            ]
        ))
        
        # Historical Data
        options.append(EnhancementOption(
            id="historical-data",
            title="Historical Data",
            description="Upload historical transactions to improve pattern recognition",
            accuracy_improvement=20.0,
            time_estimate="20-30 minutes",
            difficulty="medium",
            status="available",
            requirements=["Historical transaction files", "Data upload capability"],
            benefits=[
                "Pattern learning from history",
                "Seasonal trend recognition",
                "Vendor behavior analysis",
                "Comprehensive accuracy boost"
            ]
        ))
        
        return options
        
    except Exception as e:
        logger.error(f"Error getting enhancement options: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving enhancement options: {str(e)}"
        )


@router.post("/start-enhancement", response_model=Dict[str, Any])
async def start_enhancement(
    request: EnhancementRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Start a specific enhancement workflow.
    
    Initiates the enhancement process and returns guidance for completion.
    """
    logger.info(f"Starting enhancement {request.enhancement_id} for tenant {tenant_id_int}")
    
    try:
        enhancement_id = request.enhancement_id
        
        if enhancement_id == "quick-review":
            # Get transactions that need review
            review_query = """
                SELECT id, description, ai_confidence_score, ai_suggested_category
                FROM transactions
                WHERE tenant_id = $1
                AND ai_confidence_score >= 0.7 AND ai_confidence_score < 0.9
                AND category_id IS NULL
                ORDER BY ai_confidence_score DESC
                LIMIT 50
            """
            
            review_transactions = await conn.fetch(review_query, tenant_id_int)
            
            return {
                "success": True,
                "enhancement_id": enhancement_id,
                "status": "ready",
                "next_steps": [
                    "Review suggested categorizations",
                    "Approve or modify categories",
                    "Bulk approve similar transactions"
                ],
                "data": {
                    "transactions_count": len(review_transactions),
                    "estimated_improvement": "8-12%",
                    "redirect_url": "/review-queue"
                }
            }
            
        elif enhancement_id == "company-profile":
            return {
                "success": True,
                "enhancement_id": enhancement_id,
                "status": "ready",
                "next_steps": [
                    "Provide company information",
                    "Select industry classification",
                    "Identify common vendors"
                ],
                "data": {
                    "estimated_improvement": "15-20%",
                    "redirect_url": "/company-profile"
                }
            }
            
        elif enhancement_id == "category-rules":
            return {
                "success": True,
                "enhancement_id": enhancement_id,
                "status": "ready",
                "next_steps": [
                    "Identify vendor patterns",
                    "Create categorization rules",
                    "Test rule effectiveness"
                ],
                "data": {
                    "estimated_improvement": "12-18%",
                    "redirect_url": "/category-rules"
                }
            }
            
        elif enhancement_id == "historical-data":
            return {
                "success": True,
                "enhancement_id": enhancement_id,
                "status": "ready",
                "next_steps": [
                    "Prepare historical data files",
                    "Upload transaction history",
                    "Review pattern analysis"
                ],
                "data": {
                    "estimated_improvement": "20-25%",
                    "redirect_url": "/historical-upload"
                }
            }
            
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown enhancement type: {enhancement_id}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting enhancement: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting enhancement: {str(e)}"
        )


@router.post("/company-profile", response_model=Dict[str, Any])
async def update_company_profile(
    profile_data: CompanyProfileData,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Update company profile for enhanced categorization.
    
    Stores company information to improve AI categorization accuracy.
    """
    logger.info(f"Updating company profile for tenant {tenant_id_int}")
    
    try:
        # Update or insert company profile
        profile_query = """
            INSERT INTO tenant_settings (
                tenant_id, company_name, industry, business_type, 
                employee_count, annual_revenue, common_vendors, business_categories,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
            ON CONFLICT (tenant_id) DO UPDATE SET
                company_name = EXCLUDED.company_name,
                industry = EXCLUDED.industry,
                business_type = EXCLUDED.business_type,
                employee_count = EXCLUDED.employee_count,
                annual_revenue = EXCLUDED.annual_revenue,
                common_vendors = EXCLUDED.common_vendors,
                business_categories = EXCLUDED.business_categories,
                updated_at = CURRENT_TIMESTAMP
        """
        
        await conn.execute(
            profile_query,
            tenant_id_int,
            profile_data.company_name,
            profile_data.industry,
            profile_data.business_type,
            profile_data.employee_count,
            profile_data.annual_revenue,
            profile_data.common_vendors,
            profile_data.business_categories
        )
        
        # Simulate accuracy improvement
        accuracy_improvement = 15.0 + (len(profile_data.common_vendors) * 0.5)
        
        return {
            "success": True,
            "enhancement_id": "company-profile",
            "status": "completed",
            "accuracy_improvement": accuracy_improvement,
            "message": f"Company profile updated successfully. Expected accuracy improvement: {accuracy_improvement:.1f}%",
            "next_recommendations": [
                "Review quick-review transactions",
                "Consider adding category rules",
                "Upload historical data for better patterns"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error updating company profile: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating company profile: {str(e)}"
        )


@router.post("/category-rule", response_model=Dict[str, Any])
async def create_category_rule(
    rule_data: CategoryRuleData,
    current_user: User = Depends(get_current_active_user),
    tenant_id_int: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Create a custom category rule for vendor patterns.
    
    Adds rule-based categorization to improve future accuracy.
    """
    logger.info(f"Creating category rule for tenant {tenant_id_int}")
    
    try:
        # Create vendor category mapping
        rule_query = """
            INSERT INTO vendor_category_mappings (
                tenant_id, vendor_pattern, category_id, applies_to_debits, 
                applies_to_credits, amount_threshold, notes, created_by, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
            RETURNING id
        """
        
        rule_id = await conn.fetchval(
            rule_query,
            tenant_id_int,
            rule_data.vendor_pattern,
            rule_data.category_id,
            rule_data.applies_to_debits,
            rule_data.applies_to_credits,
            rule_data.amount_threshold,
            rule_data.notes,
            current_user.id
        )
        
        # Apply rule to existing transactions
        apply_query = """
            UPDATE transactions
            SET 
                category_id = $2,
                ai_confidence_score = 1.0,
                user_categorized = true,
                categorized_by = $3,
                updated_at = CURRENT_TIMESTAMP
            WHERE tenant_id = $1
            AND LOWER(description) LIKE LOWER($4)
            AND category_id IS NULL
            AND (
                ($5 = true AND amount < 0) OR
                ($6 = true AND amount > 0)
            )
        """
        
        result = await conn.execute(
            apply_query,
            tenant_id_int,
            rule_data.category_id,
            current_user.id,
            f"%{rule_data.vendor_pattern}%",
            rule_data.applies_to_debits,
            rule_data.applies_to_credits
        )
        
        transactions_affected = int(result.split()[-1])
        
        return {
            "success": True,
            "enhancement_id": "category-rule",
            "rule_id": rule_id,
            "status": "completed",
            "transactions_affected": transactions_affected,
            "message": f"Category rule created successfully. Applied to {transactions_affected} transactions.",
            "accuracy_improvement": min(15.0, transactions_affected * 0.5)
        }
        
    except Exception as e:
        logger.error(f"Error creating category rule: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating category rule: {str(e)}"
        )