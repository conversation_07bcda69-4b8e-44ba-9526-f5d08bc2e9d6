"""
Baseline Accuracy Service

This service handles accuracy measurement during onboarding by comparing
AI predictions against customer's original historical labels.

Key Concepts:
- Baseline Accuracy: How well AI learned from historical patterns
- Original Category: Customer's historical label from their data
- AI Category: What the AI predicted based on learned patterns
- Success Criteria: >85% accuracy required for production approval
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional

import asyncpg

from ...shared.exceptions import ServiceError

logger = logging.getLogger(__name__)


class BaselineAccuracyService:
    """
    Service for measuring baseline accuracy during onboarding.

    This is ONLY used during the onboarding phase to validate that
    the AI has successfully learned the customer's categorization patterns.
    """

    def __init__(self, db: asyncpg.Connection):
        self.db = db

    async def measure_accuracy(
        self,
        tenant_id: int,
        start_date: datetime,
        end_date: datetime,
        training_cutoff_date: datetime,
    ) -> Dict[str, any]:
        """
        Measure accuracy for a specific time period during temporal validation.

        Args:
            tenant_id: The tenant to measure accuracy for
            start_date: Start of the test period
            end_date: End of the test period
            training_cutoff_date: Transactions before this date were used for training

        Returns:
            Dict containing accuracy metrics and detailed results
        """
        try:
            # Get test transactions (after training cutoff) using asyncpg
            from ..transactions.models import Transaction

            sql = """
                SELECT * FROM transactions 
                WHERE tenant_id = $1 
                  AND date >= $2 
                  AND date <= $3 
                  AND original_category IS NOT NULL
            """
            rows = await self.db.fetch(sql, tenant_id, start_date, end_date)
            test_transactions = [Transaction.model_validate(dict(row)) for row in rows]

            if not test_transactions:
                logger.warning(
                    f"No test transactions found for tenant {tenant_id} in period {start_date} to {end_date}"
                )
                return {
                    "accuracy": 0.0,
                    "total_transactions": 0,
                    "correct_predictions": 0,
                    "confusion_matrix": {},
                    "category_accuracy": {},
                }

            # Calculate accuracy
            correct_predictions = 0
            confusion_matrix = {}  # original -> predicted -> count
            category_counts = {}  # category -> {total, correct}

            for transaction in test_transactions:
                original = transaction.original_category
                predicted = transaction.ai_suggested_category

                # Skip if no AI prediction
                if not predicted:
                    continue

                # Update confusion matrix
                if original not in confusion_matrix:
                    confusion_matrix[original] = {}
                if predicted not in confusion_matrix[original]:
                    confusion_matrix[original][predicted] = 0
                confusion_matrix[original][predicted] += 1

                # Update category counts
                if original not in category_counts:
                    category_counts[original] = {"total": 0, "correct": 0}
                category_counts[original]["total"] += 1

                # Check if prediction is correct
                if self._categories_match(original, predicted):
                    correct_predictions += 1
                    category_counts[original]["correct"] += 1

            # Calculate overall accuracy
            total_with_predictions = sum(
                1 for t in test_transactions if t.ai_suggested_category is not None
            )

            accuracy = (
                (correct_predictions / total_with_predictions * 100)
                if total_with_predictions > 0
                else 0.0
            )

            # Calculate per-category accuracy
            category_accuracy = {}
            for category, counts in category_counts.items():
                if counts["total"] > 0:
                    category_accuracy[category] = {
                        "accuracy": (counts["correct"] / counts["total"]) * 100,
                        "total": counts["total"],
                        "correct": counts["correct"],
                    }

            # Find problematic categories (accuracy < 70%)
            problematic_categories = [
                cat
                for cat, stats in category_accuracy.items()
                if stats["accuracy"] < 70.0
            ]

            return {
                "accuracy": round(accuracy, 2),
                "total_transactions": len(test_transactions),
                "transactions_with_predictions": total_with_predictions,
                "correct_predictions": correct_predictions,
                "confusion_matrix": confusion_matrix,
                "category_accuracy": category_accuracy,
                "problematic_categories": problematic_categories,
                "meets_threshold": accuracy >= 85.0,
            }

        except Exception as e:
            logger.error(f"Error measuring baseline accuracy: {e}")
            raise ServiceError(f"Failed to measure baseline accuracy: {str(e)}")

    async def get_training_statistics(
        self, tenant_id: int, training_end_date: datetime
    ) -> Dict[str, any]:
        """
        Get statistics about the training data used for building the RAG corpus.

        Args:
            tenant_id: The tenant ID
            training_end_date: Include transactions up to this date in training

        Returns:
            Statistics about the training data
        """
        try:
            # Count transactions by category in training data using asyncpg
            sql = """
                SELECT 
                    original_category,
                    COUNT(id) as count
                FROM transactions 
                WHERE tenant_id = $1 
                  AND date <= $2 
                  AND original_category IS NOT NULL
                GROUP BY original_category
            """
            rows = await self.db.fetch(sql, tenant_id, training_end_date)
            category_distribution = {
                row["original_category"]: row["count"] for row in rows
            }

            total_training = sum(category_distribution.values())

            # Find categories with few examples (< 10)
            sparse_categories = [
                cat for cat, count in category_distribution.items() if count < 10
            ]

            return {
                "total_training_transactions": total_training,
                "unique_categories": len(category_distribution),
                "category_distribution": category_distribution,
                "sparse_categories": sparse_categories,
                "average_per_category": total_training / len(category_distribution)
                if category_distribution
                else 0,
            }

        except Exception as e:
            logger.error(f"Error getting training statistics: {e}")
            raise ServiceError(f"Failed to get training statistics: {str(e)}")

    async def store_accuracy_results(
        self,
        tenant_id: int,
        validation_id: str,
        month: str,
        accuracy_results: Dict[str, any],
    ) -> None:
        """
        Store accuracy results for a specific validation month.

        Args:
            tenant_id: The tenant ID
            validation_id: The temporal validation ID
            month: The month being validated (e.g., "2024-07")
            accuracy_results: The accuracy measurement results
        """
        try:
            # Store in a dedicated accuracy tracking table using asyncpg
            # This helps track accuracy improvements over time
            sql = """
                INSERT INTO baseline_accuracy_results 
                (tenant_id, validation_id, month, accuracy, total_transactions, 
                 correct_predictions, confusion_matrix, created_at)
                VALUES 
                ($1, $2, $3, $4, $5, $6, $7, NOW())
            """

            await self.db.execute(
                sql,
                tenant_id,
                validation_id,
                month,
                accuracy_results["accuracy"],
                accuracy_results["total_transactions"],
                accuracy_results["correct_predictions"],
                accuracy_results["confusion_matrix"],
            )

        except Exception as e:
            logger.error(f"Error storing accuracy results: {e}")
            # Don't raise - this is not critical for the validation process

    def _categories_match(self, original: str, predicted: str) -> bool:
        """
        Check if two categories match, handling slight variations.

        Args:
            original: The original category from historical data
            predicted: The AI predicted category

        Returns:
            True if categories match
        """
        # Normalize for comparison
        original_normalized = original.lower().strip()
        predicted_normalized = predicted.lower().strip()

        # Exact match
        if original_normalized == predicted_normalized:
            return True

        # Handle common variations
        # Example: "Food & Dining" vs "Food and Dining"
        original_clean = original_normalized.replace("&", "and").replace("-", " ")
        predicted_clean = predicted_normalized.replace("&", "and").replace("-", " ")

        if original_clean == predicted_clean:
            return True

        # Handle abbreviations
        # Example: "Entertainment" vs "Ent"
        if original_normalized.startswith(
            predicted_normalized
        ) or predicted_normalized.startswith(original_normalized):
            if len(original_normalized) > 3 and len(predicted_normalized) > 3:
                return True

        return False

    async def get_accuracy_trend(
        self, tenant_id: int, validation_id: Optional[str] = None
    ) -> List[Dict[str, any]]:
        """
        Get accuracy trend over time for a tenant.

        Args:
            tenant_id: The tenant ID
            validation_id: Optional specific validation to filter by

        Returns:
            List of accuracy measurements over time
        """
        try:
            # Build dynamic SQL query using asyncpg
            sql = """
                SELECT month, accuracy, total_transactions, created_at
                FROM baseline_accuracy_results
                WHERE tenant_id = $1
            """
            params = [tenant_id]

            if validation_id:
                sql += " AND validation_id = $2"
                params.append(validation_id)

            sql += " ORDER BY month ASC"

            rows = await self.db.fetch(sql, *params)

            return [
                {
                    "month": row["month"],
                    "accuracy": float(row["accuracy"]),
                    "total_transactions": row["total_transactions"],
                    "measured_at": row["created_at"].isoformat(),
                }
                for row in rows
            ]

        except Exception as e:
            logger.error(f"Error getting accuracy trend: {e}")
            return []
