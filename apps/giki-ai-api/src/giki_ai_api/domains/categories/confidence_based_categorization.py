"""
Confidence-Based Categorization System
======================================

AI-assisted MIS completion with confidence thresholds:
- High confidence (>90%): Auto-commit categorization
- Medium confidence (70-90%): Pre-fill suggestions for review
- Low confidence (<70%): Group similar transactions for bulk action

The goal is to maximize AI assistance while ensuring accuracy through
human review of uncertain categorizations.
"""

import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Tuple

import asyncpg

from .transaction_grouping_service import transaction_grouping_service

logger = logging.getLogger(__name__)


class CategorizationConfidence(Enum):
    """Confidence levels for categorization decisions."""
    HIGH = "high"          # >90% - Auto-commit
    MEDIUM = "medium"      # 70-90% - Suggest for review
    LOW = "low"            # <70% - Group for bulk action
    NONE = "none"          # No suggestion


@dataclass
class ConfidenceThresholds:
    """Configurable confidence thresholds."""
    auto_commit: float = 0.90      # Threshold for automatic categorization
    suggest: float = 0.70           # Threshold for suggestions
    group_similarity: float = 0.80  # Similarity threshold for grouping


@dataclass
class TransactionGroup:
    """Group of similar uncertain transactions."""
    group_id: str
    pattern: str
    transaction_ids: List[int]
    transaction_count: int
    total_amount: float
    suggested_category_id: Optional[int]
    suggested_category_path: Optional[str]
    confidence_score: float
    sample_descriptions: List[str]
    grouping_reason: str


@dataclass
class CategorizationResult:
    """Result of confidence-based categorization."""
    total_transactions: int
    
    # High confidence - auto-committed
    auto_categorized: int
    auto_categorized_confidence_avg: float
    
    # Medium confidence - suggested
    suggested_count: int
    suggested_confidence_avg: float
    
    # Low confidence - grouped
    grouped_count: int
    groups_created: int
    
    # No confidence - left blank
    uncategorized_count: int
    
    # Groups for review
    transaction_groups: List[TransactionGroup]


class ConfidenceBasedCategorizationService:
    """Service for confidence-based categorization with intelligent grouping."""
    
    def __init__(self, thresholds: Optional[ConfidenceThresholds] = None):
        self.logger = logging.getLogger(__name__)
        self.thresholds = thresholds or ConfidenceThresholds()
    
    async def categorize_with_confidence(
        self,
        upload_id: str,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> CategorizationResult:
        """
        Categorize transactions with confidence-based handling.
        
        1. Run AI categorization on all transactions
        2. Auto-commit high confidence results
        3. Create suggestions for medium confidence
        4. Group low confidence transactions by similarity
        5. Leave very uncertain ones uncategorized
        """
        self.logger.info(
            f"Starting confidence-based categorization for upload {upload_id}"
        )
        
        # Get all uncategorized transactions
        uncategorized = await self._get_uncategorized_transactions(
            upload_id, tenant_id, conn
        )
        
        if not uncategorized:
            return CategorizationResult(
                total_transactions=0,
                auto_categorized=0,
                auto_categorized_confidence_avg=0,
                suggested_count=0,
                suggested_confidence_avg=0,
                grouped_count=0,
                groups_created=0,
                uncategorized_count=0,
                transaction_groups=[]
            )
        
        # Run AI categorization on all transactions with real-time updates
        categorization_results = await self._run_ai_categorization(
            uncategorized, tenant_id, conn, upload_id=upload_id
        )
        
        # Separate by confidence level
        high_confidence = []
        medium_confidence = []
        low_confidence = []
        no_confidence = []
        
        for txn_id, result in categorization_results.items():
            if result['confidence'] >= self.thresholds.auto_commit:
                high_confidence.append((txn_id, result))
            elif result['confidence'] >= self.thresholds.suggest:
                medium_confidence.append((txn_id, result))
            elif result['category_id'] is not None:
                low_confidence.append((txn_id, result))
            else:
                no_confidence.append(txn_id)
        
        # Process each confidence level
        auto_count = await self._auto_commit_high_confidence(
            high_confidence, conn
        )
        
        suggest_count = await self._create_suggestions(
            medium_confidence, conn
        )
        
        groups = await self._group_low_confidence(
            low_confidence, uncategorized, tenant_id, conn
        )
        
        # Calculate averages
        auto_avg = (
            sum(r[1]['confidence'] for r in high_confidence) / len(high_confidence)
            if high_confidence else 0
        )
        
        suggest_avg = (
            sum(r[1]['confidence'] for r in medium_confidence) / len(medium_confidence)
            if medium_confidence else 0
        )
        
        return CategorizationResult(
            total_transactions=len(uncategorized),
            auto_categorized=auto_count,
            auto_categorized_confidence_avg=auto_avg,
            suggested_count=suggest_count,
            suggested_confidence_avg=suggest_avg,
            grouped_count=len(low_confidence),
            groups_created=len(groups),
            uncategorized_count=len(no_confidence),
            transaction_groups=groups
        )
    
    async def _get_uncategorized_transactions(
        self,
        upload_id: str,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> List[Dict]:
        """Get all uncategorized transactions."""
        query = """
            SELECT 
                id,
                description,
                amount,
                date,
                ai_suggested_category,
                ai_confidence_score
            FROM transactions
            WHERE upload_id = $1
            AND tenant_id = $2
            AND category_id IS NULL
            ORDER BY ABS(amount) DESC
        """
        
        results = await conn.fetch(query, upload_id, tenant_id)
        return [dict(r) for r in results]
    
    async def _run_ai_categorization(
        self,
        transactions: List[Dict],
        tenant_id: int,
        conn: asyncpg.Connection,
        upload_id: Optional[str] = None,
        batch_size: int = 8,
        emit_updates: bool = True
    ) -> Dict[int, Dict]:
        """Run AI categorization on transactions with real-time updates."""
        # Import the MIS categorization service and WebSocket service
        from ...shared.services.websocket_service import WebSocketService
        from .mis_categorization_service import MISCategorizationService
        
        mis_service = MISCategorizationService(conn)
        ws_service = WebSocketService() if emit_updates else None
        
        results = {}
        total_transactions = len(transactions)
        processed_count = 0
        
        # Emit start event
        if emit_updates and upload_id:
            await ws_service.emit_event(
                event_type="batch.categorization.started",
                data={
                    "upload_id": upload_id,
                    "total_transactions": total_transactions,
                    "batch_size": batch_size,
                },
                tenant_id=tenant_id,
            )
        
        # Process in batches for better performance and updates
        for batch_idx, i in enumerate(range(0, total_transactions, batch_size)):
            batch = transactions[i:i + batch_size]
            
            # Emit batch start
            if emit_updates and upload_id:
                await ws_service.emit_event(
                    event_type="batch.categorization.batch_started",
                    data={
                        "upload_id": upload_id,
                        "batch_index": batch_idx,
                        "batch_size": len(batch),
                    },
                    tenant_id=tenant_id,
                )
            
            for txn in batch:
                try:
                    # Use existing AI suggestions if available and recent
                    if (txn['ai_suggested_category'] and 
                        txn['ai_confidence_score'] and
                        txn['ai_confidence_score'] > 0):
                        results[txn['id']] = {
                            'category_id': txn['ai_suggested_category'],
                            'confidence': float(txn['ai_confidence_score']),
                            'reasoning': 'Previous AI suggestion'
                        }
                    else:
                        # Run fresh categorization
                        result = await mis_service.categorize_transaction(
                            tenant_id=tenant_id,
                            transaction_id=str(txn['id']),
                            description=txn['description'],
                            amount=float(txn['amount']),
                            transaction_date=txn.get('date'),
                            vendor=txn.get('vendor_name'),
                            remarks=txn.get('remarks'),
                            metadata=txn.get('metadata'),
                        )
                        
                        results[txn['id']] = {
                            'category_id': result.category_id,
                            'category_name': result.category_name,
                            'parent_category': result.parent_category,
                            'confidence': result.confidence,
                            'reasoning': result.reasoning,
                            'gl_code': result.gl_code,
                            'vendor_info': result.vendor_info,
                        }
                    
                    processed_count += 1
                    
                    # Emit transaction result
                    if emit_updates and upload_id:
                        await ws_service.emit_event(
                            event_type="batch.categorization.transaction",
                            data={
                                "upload_id": upload_id,
                                "transaction_id": txn['id'],
                                "category": results[txn['id']].get('category_name', 'Unknown'),
                                "confidence": results[txn['id']]['confidence'],
                                "status": "success",
                            },
                            tenant_id=tenant_id,
                        )
                        
                except Exception as e:
                    self.logger.warning(
                        f"Failed to categorize transaction {txn['id']}: {e}"
                    )
                    results[txn['id']] = {
                        'category_id': None,
                        'confidence': 0.0,
                        'reasoning': f'Error: {str(e)}'
                    }
                    processed_count += 1
                    
                    # Emit error
                    if emit_updates and upload_id:
                        await ws_service.emit_event(
                            event_type="batch.categorization.transaction",
                            data={
                                "upload_id": upload_id,
                                "transaction_id": txn['id'],
                                "status": "error",
                                "error": str(e),
                            },
                            tenant_id=tenant_id,
                        )
            
            # Emit progress
            if emit_updates and upload_id:
                progress_percentage = (processed_count / total_transactions) * 100
                await ws_service.emit_event(
                    event_type="batch.categorization.progress",
                    data={
                        "upload_id": upload_id,
                        "processed_count": processed_count,
                        "total_count": total_transactions,
                        "progress_percentage": round(progress_percentage, 2),
                    },
                    tenant_id=tenant_id,
                )
        
        # Emit completion
        if emit_updates and upload_id:
            await ws_service.emit_event(
                event_type="batch.categorization.completed",
                data={
                    "upload_id": upload_id,
                    "total_transactions": total_transactions,
                    "processed_count": processed_count,
                },
                tenant_id=tenant_id,
            )
        
        return results
    
    async def _auto_commit_high_confidence(
        self,
        high_confidence: List[Tuple[int, Dict]],
        conn: asyncpg.Connection
    ) -> int:
        """Auto-commit high confidence categorizations."""
        if not high_confidence:
            return 0
        
        # Batch update high confidence categorizations
        values = []
        for txn_id, result in high_confidence:
            values.append(
                f"({txn_id}, {result['category_id']}, "
                f"{result['confidence']}, true)"
            )
        
        query = f"""
            UPDATE transactions t
            SET 
                category_id = v.category_id,
                ai_confidence_score = v.confidence,
                ai_suggested_category = v.category_id,
                auto_categorized = v.auto_categorized,
                updated_at = CURRENT_TIMESTAMP
            FROM (VALUES {','.join(values)}) 
            AS v(id, category_id, confidence, auto_categorized)
            WHERE t.id = v.id
        """
        
        result = await conn.execute(query)
        count = int(result.split()[-1])
        
        self.logger.info(
            f"Auto-committed {count} high confidence categorizations"
        )
        
        return count
    
    async def _create_suggestions(
        self,
        medium_confidence: List[Tuple[int, Dict]],
        conn: asyncpg.Connection
    ) -> int:
        """Create suggestions for medium confidence categorizations."""
        if not medium_confidence:
            return 0
        
        # Update with suggestions but don't commit
        values = []
        for txn_id, result in medium_confidence:
            values.append(
                f"({txn_id}, {result['category_id']}, {result['confidence']})"
            )
        
        query = f"""
            UPDATE transactions t
            SET 
                ai_suggested_category = v.suggested_category,
                ai_confidence_score = v.confidence,
                needs_review = true,
                updated_at = CURRENT_TIMESTAMP
            FROM (VALUES {','.join(values)}) 
            AS v(id, suggested_category, confidence)
            WHERE t.id = v.id
        """
        
        result = await conn.execute(query)
        count = int(result.split()[-1])
        
        self.logger.info(
            f"Created {count} medium confidence suggestions for review"
        )
        
        return count
    
    async def _group_low_confidence(
        self,
        low_confidence: List[Tuple[int, Dict]],
        all_transactions: List[Dict],
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> List[TransactionGroup]:
        """Group low confidence transactions by similarity using the grouping service."""
        if not low_confidence:
            return []
        
        self.logger.info(f"Grouping {len(low_confidence)} low confidence transactions")
        
        # First, update low confidence transactions with their AI suggestions
        # This prepares them for grouping
        await self._update_low_confidence_suggestions(low_confidence, conn)
        
        # Get upload_id from one of the transactions
        upload_id = None
        if all_transactions:
            upload_query = """
                SELECT upload_id FROM transactions
                WHERE id = $1
            """
            upload_result = await conn.fetchrow(upload_query, all_transactions[0]['id'])
            upload_id = upload_result['upload_id'] if upload_result else None
        
        if not upload_id:
            self.logger.warning("No upload_id found for grouping")
            return []
        
        # Use the transaction grouping service
        try:
            grouping_results = await transaction_grouping_service.group_transactions_for_review(
                upload_id=upload_id,
                tenant_id=tenant_id,
                conn=conn,
                confidence_threshold=self.thresholds.suggest
            )
            
            # Convert to our TransactionGroup format
            transaction_groups = []
            for group in grouping_results:
                # Get category path if available
                suggested_cat_path = None
                if group.suggested_category_id:
                    cat_query = """
                        SELECT path FROM categories
                        WHERE id = $1 AND tenant_id = $2
                    """
                    cat_result = await conn.fetchrow(
                        cat_query, group.suggested_category_id, tenant_id
                    )
                    if cat_result:
                        suggested_cat_path = cat_result['path']
                
                # Create TransactionGroup
                transaction_group = TransactionGroup(
                    group_id=group.group_id,
                    pattern=group.pattern.description_pattern,
                    transaction_ids=[txn['id'] for txn in group.transactions],
                    transaction_count=len(group.transactions),
                    total_amount=group.total_amount,
                    suggested_category_id=group.suggested_category_id,
                    suggested_category_path=suggested_cat_path,
                    confidence_score=group.avg_confidence,
                    sample_descriptions=[txn['description'] for txn in group.transactions[:3]],
                    grouping_reason=f"Similar transactions grouped by pattern: {group.pattern.description_pattern}"
                )
                
                transaction_groups.append(transaction_group)
            
            self.logger.info(f"Created {len(transaction_groups)} transaction groups")
            return transaction_groups
            
        except Exception as e:
            self.logger.error(f"Error in transaction grouping: {e}")
            # Fallback to simple grouping if the service fails
            return await self._fallback_grouping(low_confidence, all_transactions, tenant_id, conn)
    
    async def _update_low_confidence_suggestions(
        self,
        low_confidence: List[Tuple[int, Dict]],
        conn: asyncpg.Connection
    ):
        """Update low confidence transactions with AI suggestions."""
        if not low_confidence:
            return
        
        # Update with suggestions but don't commit
        values = []
        for txn_id, result in low_confidence:
            values.append(
                f"({txn_id}, {result['category_id'] or 'NULL'}, {result['confidence']})"
            )
        
        query = f"""
            UPDATE transactions t
            SET 
                ai_suggested_category = v.suggested_category,
                ai_confidence_score = v.confidence,
                needs_review = true,
                updated_at = CURRENT_TIMESTAMP
            FROM (VALUES {','.join(values)}) 
            AS v(id, suggested_category, confidence)
            WHERE t.id = v.id
        """
        
        result = await conn.execute(query)
        count = int(result.split()[-1])
        
        self.logger.info(f"Updated {count} low confidence transactions for grouping")
    
    async def _fallback_grouping(
        self,
        low_confidence: List[Tuple[int, Dict]],
        all_transactions: List[Dict],
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> List[TransactionGroup]:
        """Fallback grouping method if the main service fails."""
        self.logger.warning("Using fallback grouping method")
        
        # Create transaction lookup
        txn_lookup = {t['id']: t for t in all_transactions}
        
        # Simple grouping by pattern
        groups = {}
        
        for txn_id, result in low_confidence:
            txn = txn_lookup[txn_id]
            
            # Extract simple pattern
            pattern = self._extract_pattern(txn['description'])
            
            if pattern not in groups:
                groups[pattern] = {
                    'transaction_ids': [],
                    'amounts': [],
                    'descriptions': [],
                    'suggested_categories': {},
                    'confidences': []
                }
            
            groups[pattern]['transaction_ids'].append(txn_id)
            groups[pattern]['amounts'].append(float(txn['amount']))
            groups[pattern]['descriptions'].append(txn['description'])
            groups[pattern]['confidences'].append(result['confidence'])
            
            # Track category suggestions
            cat_id = result['category_id']
            if cat_id:
                if cat_id not in groups[pattern]['suggested_categories']:
                    groups[pattern]['suggested_categories'][cat_id] = 0
                groups[pattern]['suggested_categories'][cat_id] += 1
        
        # Convert to TransactionGroup objects
        transaction_groups = []
        
        for pattern, data in groups.items():
            if len(data['transaction_ids']) < 2:  # Skip single transactions
                continue
                
            # Find most common suggested category
            suggested_cat_id = None
            suggested_cat_path = None
            
            if data['suggested_categories']:
                suggested_cat_id = max(
                    data['suggested_categories'].items(),
                    key=lambda x: x[1]
                )[0]
                
                # Get category path
                cat_query = """
                    SELECT path FROM categories
                    WHERE id = $1 AND tenant_id = $2
                """
                cat_result = await conn.fetchrow(
                    cat_query, suggested_cat_id, tenant_id
                )
                if cat_result:
                    suggested_cat_path = cat_result['path']
            
            # Determine grouping reason
            if abs(sum(data['amounts'])) > 1000:
                reason = "High value similar transactions"
            elif len(data['transaction_ids']) > 10:
                reason = "Frequent recurring pattern"
            else:
                reason = "Similar transaction pattern"
            
            group = TransactionGroup(
                group_id=f"group_{pattern[:20]}_{len(transaction_groups)}",
                pattern=pattern,
                transaction_ids=data['transaction_ids'],
                transaction_count=len(data['transaction_ids']),
                total_amount=sum(data['amounts']),
                suggested_category_id=suggested_cat_id,
                suggested_category_path=suggested_cat_path,
                confidence_score=sum(data['confidences']) / len(data['confidences']),
                sample_descriptions=list(set(data['descriptions']))[:3],
                grouping_reason=reason
            )
            
            transaction_groups.append(group)
        
        # Sort by transaction count (most transactions first)
        transaction_groups.sort(key=lambda g: g.transaction_count, reverse=True)
        
        # Update transactions with group information
        for group in transaction_groups:
            if group.suggested_category_id:
                values = []
                for txn_id in group.transaction_ids:
                    values.append(
                        f"({txn_id}, {group.suggested_category_id}, "
                        f"{group.confidence_score}, '{group.group_id}')"
                    )
                
                update_query = f"""
                    UPDATE transactions t
                    SET 
                        ai_suggested_category = v.suggested_category,
                        ai_confidence_score = v.confidence,
                        ai_grouping_id = v.group_id,
                        needs_review = true,
                        updated_at = CURRENT_TIMESTAMP
                    FROM (VALUES {','.join(values)}) 
                    AS v(id, suggested_category, confidence, group_id)
                    WHERE t.id = v.id
                """
                
                await conn.execute(update_query)
        
        self.logger.info(
            f"Created {len(transaction_groups)} groups for "
            f"{len(low_confidence)} low confidence transactions"
        )
        
        return transaction_groups
    
    def _extract_pattern(self, description: str) -> str:
        """Extract vendor/pattern from description."""
        import re
        
        # Remove numbers, dates, and special characters
        pattern = re.sub(r'\b\d{4,}\b', '', description)  # Long numbers
        pattern = re.sub(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', '', pattern)  # Dates
        pattern = re.sub(r'#\d+', '#', pattern)  # Reference numbers
        pattern = re.sub(r'\*[A-Z0-9]+', '*', pattern)  # Transaction IDs
        pattern = re.sub(r'[^\w\s]', ' ', pattern)  # Special chars
        pattern = ' '.join(pattern.split())  # Normalize spaces
        
        return pattern.upper()[:50]  # Limit length
    
    async def apply_bulk_categorization(
        self,
        group_id: str,
        category_id: int,
        tenant_id: int,
        user_id: int,
        conn: asyncpg.Connection
    ) -> Dict[str, int]:
        """Apply categorization to all transactions in a group."""
        
        # Update all transactions in the group
        query = """
            UPDATE transactions
            SET 
                category_id = $1,
                ai_confidence_score = 1.0,  -- User confirmed
                needs_review = false,
                auto_categorized = false,
                user_categorized = true,
                categorized_by = $2,
                updated_at = CURRENT_TIMESTAMP
            WHERE tenant_id = $3
            AND ai_grouping_id = $4
            AND category_id IS NULL
        """
        
        result = await conn.execute(
            query, category_id, user_id, tenant_id, group_id
        )
        
        count = int(result.split()[-1])
        
        return {
            'transactions_updated': count,
            'group_id': group_id,
            'category_id': category_id
        }


# Singleton instance
confidence_based_categorization = ConfidenceBasedCategorizationService()