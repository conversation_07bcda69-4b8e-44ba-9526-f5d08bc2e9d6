from datetime import datetime

from pydantic import BaseModel, ConfigDict, field_serializer, model_validator
from typing_extensions import Self


class CategoryBase(BaseModel):
    """Base category schema with common attributes."""

    name: str
    description: str | None = None
    code: str | None = None
    color: str | None = "#6B7280"  # Default gray color
    parent_id: int | None = None

    # GL Code mapping fields
    gl_code: str | None = None
    gl_account_name: str | None = None
    gl_account_type: str | None = None  # Asset, Liability, Revenue, Expense, Equity


class CategoryCreate(CategoryBase):
    """Schema for creating a new category."""

    pass


class CategoryUpdate(BaseModel):
    """Schema for updating an existing category."""

    name: str | None = None
    description: str | None = None
    code: str | None = None
    color: str | None = None
    parent_id: int | None = None

    # GL Code mapping fields
    gl_code: str | None = None
    gl_account_name: str | None = None
    gl_account_type: str | None = None


class Category(BaseModel):
    """Basic category schema for responses."""

    id: int
    name: str
    color: str | None = None
    path: str | None = None
    parent_id: int | None = None
    level: int = 0
    tenant_id: int

    # GL Code mapping fields - customers can update these via UI
    gl_code: str | None = None
    gl_account_name: str | None = None
    gl_account_type: str | None = None

    # Learning metadata
    learned_from_onboarding: bool = True
    frequency_score: float | None = None
    confidence_score: float | None = None

    created_at: str | None = None
    updated_at: str | None = None

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("created_at", "updated_at", mode="wrap")
    def serialize_datetime(self, dt: datetime | None, _info) -> str | None:
        """Convert datetime to ISO format string."""
        if dt is None:
            return None
        if isinstance(dt, str):
            return dt
        if isinstance(dt, datetime):
            return dt.isoformat()
        return str(dt)


class CategoryTree(Category):
    """Category schema that includes children for hierarchical responses."""

    children: list["CategoryTree"] = []

    model_config = ConfigDict(from_attributes=True)


# Forward references are typically resolved by calling model_rebuild()
# on models that use them, often done at the end of the schemas package __init__.py
# CategoryTree.update_forward_refs() # Removed, will be handled in __init__.py


class TransactionCategoryUpdate(BaseModel):
    """Schema for updating a transaction's category."""

    category_id: int | None = None
    category_path: str | None = None  # Allow update by path as well

    # Ensure at least one is provided
    @model_validator(mode="after")
    def check_at_least_one_identifier(self) -> Self:
        if not self.category_path and not self.category_id:
            raise ValueError("Either category_id or category_path must be provided")
        return self


class CategoryWithConfidence(Category):
    """Category schema with confidence score for AI suggestions."""

    confidence_score: float
    reasoning: str | None = None


class CategorySuggestion(BaseModel):
    """Category suggestion schema."""

    category: Category
    confidence: float
    reasoning: str


class CategoryBulkUpdate(BaseModel):
    """Schema for bulk category updates."""

    category_ids: list[int]
    updates: CategoryUpdate


class CategoryMoveRequest(BaseModel):
    """Schema for moving categories in hierarchy."""

    category_id: int
    new_parent_id: int | None = None


class CategoryAnalytics(BaseModel):
    """Category analytics schema."""

    category_id: int
    usage_count: int
    average_confidence: float
    last_used: str | None = None


class CategoryMapping(BaseModel):
    """Category mapping schema."""

    source_name: str
    target_category_id: int
    confidence: float = 1.0


class CategoryImportRequest(BaseModel):
    """Category import request schema."""

    categories: list[CategoryCreate]
    overwrite_existing: bool = False


class CategoryExportResponse(BaseModel):
    """Category export response schema."""

    categories: list[Category]
    export_timestamp: str
    total_count: int


class CategoryHierarchyValidation(BaseModel):
    """Category hierarchy validation schema."""

    is_valid: bool
    errors: list[str] = []
    warnings: list[str] = []


class CategorySearchResult(BaseModel):
    """Category search result schema."""

    categories: list[Category]
    total_count: int
    search_term: str


class CategoryRecommendation(BaseModel):
    """Category recommendation schema."""

    transaction_description: str
    recommended_categories: list[CategoryWithConfidence]
    metadata: dict = {}


class GLCodeMapping(BaseModel):
    """GL Code mapping schema."""

    id: int
    category_id: int
    gl_code: str
    gl_account_name: str
    gl_account_type: str

    model_config = ConfigDict(from_attributes=True)


class GLCodeMappingCreate(BaseModel):
    """GL Code mapping creation schema."""

    category_id: int
    gl_code: str
    gl_account_name: str
    gl_account_type: str


class GLCodeBulkMapping(BaseModel):
    """GL Code bulk mapping schema."""

    mappings: list[GLCodeMappingCreate]


# TransactionReadWithAISuggestion moved to schemas/transaction.py


class CategoryVisualization(BaseModel):
    """Category with visualization data for hierarchical display."""
    
    id: int
    name: str
    path: str
    level: int
    transaction_count: int = 0
    total_amount: float = 0.0
    percentage: float = 0.0
    color: str | None = None
    gl_code: str | None = None
    children: list["CategoryVisualization"] = []


class HierarchicalResults(BaseModel):
    """Hierarchical categorization results with visualization data."""
    
    upload_id: str
    total_transactions: int
    categorized_transactions: int
    uncategorized_transactions: int
    accuracy_score: float
    hierarchy_depth: int
    total_categories_used: int
    processing_time: float
    
    # Hierarchical breakdown
    income_hierarchy: list[CategoryVisualization] = []
    expense_hierarchy: list[CategoryVisualization] = []
    
    # Summary statistics
    total_income: float = 0.0
    total_expenses: float = 0.0
    
    # Top categories by usage
    top_categories: list[dict] = []
    
    # Accuracy metrics
    high_confidence_count: int = 0
    medium_confidence_count: int = 0
    low_confidence_count: int = 0
    
    # Enhancement opportunities
    enhancement_suggestions: list[dict] = []


# Enable forward references for nested CategoryVisualization
CategoryVisualization.model_rebuild()


class EnhancementOpportunity(BaseModel):
    """Enhancement opportunity detected in the MIS system."""
    
    type: str
    priority: str
    description: str
    affected_transactions: int
    potential_accuracy_gain: float
    implementation_effort: str
    data: dict


class EnhancementAnalysisResponse(BaseModel):
    """Response for enhancement analysis endpoint."""
    
    current_accuracy: float
    baseline_accuracy: float
    opportunities: list[EnhancementOpportunity]
    total_potential_gain: float
    recommended_actions: list[str]
    analysis_timestamp: datetime
