name: "batch_historical_data_categorization"
description: "Batch processing for historical data categorization with RAG context"
version: "1.0"
model_params:
  temperature: 0.1
  max_output_tokens: 4000

system_instruction: |
  You are an expert financial categorization AI with access to historical transaction patterns.
  You will categorize multiple transactions using existing company categories and RAG context.
  Prioritize existing categories and maintain consistency with historical patterns.

user_template: |
  BATCH CATEGORIZATION REQUEST for Tenant {tenant_id}
  
  Categorize the following {transaction_count} transactions using historical context.
  
  TRANSACTIONS TO CATEGORIZE:
  {transactions_list}
  
  EXISTING CATEGORY HIERARCHY:
  {categories_context}
  
  RAG CONTEXT (Similar Historical Transactions):
  {rag_context}
  
  HISTORICAL DATA MODE INSTRUCTIONS:
  - PRIORITIZE existing categories when there's a clear match
  - Use RAG context to identify similar historical transaction patterns
  - Only suggest new categories if no existing category fits appropriately
  - Maintain consistency with the company's established categorization patterns
  - Consider merchant patterns, amount ranges, and transaction descriptions
  
  CATEGORIZATION STRATEGY:
  1. **Exact Match**: Look for identical or very similar transactions in RAG context
  2. **Pattern Match**: Use merchant names, amounts, and descriptions to find patterns
  3. **Category Fit**: Map to existing categories that logically contain this transaction type
  4. **Hierarchy Respect**: Use the company's established hierarchy structure
  5. **Confidence Scoring**: Higher confidence for exact matches, lower for inferences
  
  REQUIREMENTS:
  - Return structured JSON array
  - Each item: {{"index": N, "category": "existing_category_name", "confidence": 0.XX, "reasoning": "match_explanation", "use_existing": true/false}}
  - Use existing categories whenever possible (use_existing: true)
  - Only create new categories when absolutely necessary (use_existing: false)
  - Confidence scores between 0.6 and 1.0 based on match quality
  - Reference RAG context matches in reasoning
  
  JSON Response Format:
  [
    {{
      "index": 1,
      "category": "Existing or new category name",
      "confidence": 0.90,
      "use_existing": true,
      "reasoning": "Matches historical pattern from [RAG context] and fits existing category structure",
      "rag_match_confidence": 0.85,
      "similar_transactions": ["Brief description of similar historical transactions"],
      "category_path": "Full hierarchy path if known"
    }}
  ]

required_fields:
  - tenant_id
  - transaction_count
  - transactions_list
  - categories_context
  - rag_context

response_format: "json"