name: "historical_data_categorization"
description: "Prompt for AI-enhanced RAG categorization with historical data"
version: "1.0"
model_params:
  temperature: 0.1
  max_output_tokens: 600

system_instruction: |
  You are an expert business accounting AI with access to historical transaction patterns.

user_template: |
  TRANSACTION TO CATEGORIZE:
  Description: {description}
  Amount: ${amount}
  Type: {transaction_type}

  HISTORICAL CONTEXT:
  This company has {existing_categories_count} existing categories from their historical data.
  RAG similarity search found {rag_matches_count} similar transactions (best match: {best_rag_confidence:.2f}).
  {rag_context}
  {categories_context}

  AI CATEGORIZATION INTELLIGENCE:
  1. **Pattern Analysis**: Look for exact or near-exact matches in historical data
  2. **Contextual Enhancement**: If RAG confidence > 0.7, validate and potentially use that category
  3. **Hierarchy Optimization**: Choose the most specific existing category that fits
  4. **Smart Creation**: If no good match exists, create a new category that fits the hierarchy
  5. **Business Logic**: Apply accounting best practices and business context

  CATEGORIZATION STRATEGY:
  - If RAG confidence > 0.8: Validate and likely use the RAG suggestion
  - If RAG confidence 0.6-0.8: Consider RAG but apply AI enhancement
  - If RAG confidence < 0.6: Use existing hierarchy or create new appropriate category
  - Always prefer existing categories when semantically appropriate

  RESPONSE FORMAT (JSON):
  {{
      "category": "Final specific category name",
      "use_existing": true/false,
      "existing_category_match": "Exact existing category if found",
      "rag_influence": "How RAG context influenced decision",
      "confidence": 0.95,
      "reasoning": "Detailed explanation of categorization logic",
      "hierarchy_fit": "How this fits in existing hierarchy",
      "create_new_hierarchy": "Parent > Child > Specific" or null
  }}

  IMPORTANT: Leverage historical patterns but enhance with AI intelligence. Don't just blindly follow RAG.

required_fields:
  - description
  - amount
  - transaction_type
  - existing_categories_count
  - rag_matches_count
  - best_rag_confidence
  - rag_context
  - categories_context

response_format: "json"