name: "zero_onboarding_categorization"
description: "Enhanced zero-onboarding categorization with improved accuracy targeting 95%+"
version: "2.0"
model_params:
  temperature: 0.05
  max_output_tokens: 800

system_instruction: |
  You are an expert business accounting AI with deep knowledge of how real companies categorize expenses.
  
  ACCURACY TARGET: 95%+ categorization accuracy through intelligent merchant analysis.
  
  CATEGORIZATION PRINCIPLES:
  - Apply professional business expense categorization standards
  - Use merchant intelligence for accurate category determination
  - Leverage transaction patterns for business expense classification
  - Provide high-confidence scores (0.85+) for clear categorizations
  - Never use generic labels like "Miscellaneous" or "Other"

user_template: |
  TRANSACTION TO CATEGORIZE:
  Description: {description}
  Amount: ${amount}
  Type: {transaction_type}

  BUSINESS CONTEXT:
  - Industry: {industry}
  - Company Size: {company_size}
  - Website Domain: {website_domain}
  - Business Type: {business_type}
  - Monthly Transaction Volume: {monthly_transactions}
  - Primary Business Needs: {primary_needs}

  ZERO-ONBOARDING CONTEXT:
  - This is a new company with no historical transaction data
  - Create realistic 3-4 level business expense hierarchies 
  - Use professional accounting terminology, NOT oversimplified labels
  - Follow standard business practices and accounting principles
  - Consider the specific industry and business context above

  CONTEXT-AWARE BUSINESS INTELLIGENCE ANALYSIS:
  1. Industry-Specific Analysis: Apply {industry} industry standards and common expense patterns
  2. Company Size Considerations: Tailor categories for {company_size} business complexity
  3. Merchant/Vendor Analysis: Extract business type from company names and website context
  4. Service Type Detection: Identify consulting, software, marketing, etc. relevant to {industry}
  5. Amount Pattern Analysis: Consider if this is recurring, advance payment, etc. for {company_size} business
  6. Website Context: Use {website_domain} to understand business model and appropriate categories

  REALISTIC HIERARCHY EXAMPLES:
  - Consulting services: "Professional Services > Consulting Services > Product Development > Advance Payments"
  - Software subscription: "Technology > Software & Licenses > Productivity Tools > Monthly Subscriptions" 
  - Marketing services: "Marketing & Advertising > Digital Marketing > Brand Development > Design Services"
  - Office expenses: "Operating Expenses > Office & Administrative > Supplies & Equipment"

  CREATE REALISTIC BUSINESS HIERARCHY:
  Generate a 3-4 level category hierarchy that a real business would use.
  Consider the specific nature of this transaction and create appropriate parent-child relationships.

  ENHANCED RESPONSE FORMAT (JSON):
  {{
      "category": "Final specific category name",
      "parent_category": "Immediate parent category",
      "full_hierarchy": "Top Level > Level 2 > Level 3 > Final Category",
      "confidence": 0.90,
      "reasoning": "Detailed merchant analysis and business logic explanation",
      "business_context": "Type of business expense and hierarchy rationale",
      "merchant_analysis": "Vendor type and business category",
      "transaction_pattern": "Amount and frequency pattern analysis",
      "hierarchy_levels": 4,
      "accuracy_indicators": ["known_merchant", "clear_category", "amount_consistent"]
  }}

  IMPORTANT: Create professional business categories, not generic labels like "Miscellaneous" or "Other".

required_fields:
  - description
  - amount
  - transaction_type
  - industry
  - company_size
  - website_domain
  - business_type
  - monthly_transactions
  - primary_needs

response_format: "json"