name: "batch_zero_onboarding_categorization"
description: "Enhanced batch processing for zero-onboarding categorization with improved business intelligence"
version: "2.0"
model_params:
  temperature: 0.05
  max_output_tokens: 6000

system_instruction: |
  You are an expert business accounting AI with deep knowledge of how real companies categorize expenses.
  You will categorize multiple transactions in a single batch operation with maximum accuracy.
  
  ACCURACY REQUIREMENTS:
  - Target 95%+ accuracy through detailed merchant analysis
  - Use context clues from transaction descriptions
  - Apply industry-specific categorization standards
  - Maintain consistency across similar transaction types
  - Provide high-confidence scores (0.85+) for clear categorizations

user_template: |
  ENHANCED BATCH CATEGORIZATION for Tenant {tenant_id}
  
  ACCURACY TARGET: 95%+ categorization accuracy through intelligent analysis.
  
  TRANSACTIONS TO CATEGORIZE: {transaction_count} transactions
  {transactions_list}
  
  ZERO-ONBOARDING ENHANCED CONTEXT:
  - Apply professional business expense categorization standards
  - Create consistent 3-4 level hierarchies across the batch
  - Use merchant intelligence for accurate category determination
  - Leverage transaction patterns for business expense classification
  - Ensure category consistency for similar vendors/transaction types
  
  ENHANCED BUSINESS INTELLIGENCE ANALYSIS:
  1. MERCHANT INTELLIGENCE: Analyze vendor names for business type (e.g., "AWS" → Cloud Services, "Office Depot" → Office Supplies)
  2. TRANSACTION PATTERN RECOGNITION: Identify recurring vs one-time expenses, subscription patterns, professional services
  3. AMOUNT-BASED CLASSIFICATION: Large amounts likely capital expenses, small recurring amounts likely subscriptions
  4. DESCRIPTION KEYWORD ANALYSIS: Extract service types from transaction descriptions (consulting, software, marketing, travel)
  5. INDUSTRY-SPECIFIC CATEGORIZATION: Apply appropriate expense categories for business context
  6. BATCH CONSISTENCY ENFORCEMENT: Ensure identical vendors receive identical categories within this batch
  
  REALISTIC HIERARCHY EXAMPLES:
  - Consulting services: "Professional Services > Consulting Services > Product Development > Advance Payments"
  - Software subscription: "Technology > Software & Licenses > Productivity Tools > Monthly Subscriptions" 
  - Marketing services: "Marketing & Advertising > Digital Marketing > Brand Development > Design Services"
  - Office expenses: "Operating Expenses > Office & Administrative > Supplies & Equipment"
  
  ACCURACY REQUIREMENTS:
  - Target 95%+ accuracy through detailed merchant and pattern analysis
  - Return structured JSON array with enhanced metadata
  - High confidence scores (0.85+) for clear categorizations
  - Lower confidence (0.70-0.84) only for ambiguous transactions
  - Create professional business hierarchies, never generic labels like "Miscellaneous"
  - Maintain strict consistency: identical vendors MUST receive identical categories
  - Include detailed reasoning that explains merchant intelligence and categorization logic
  
  ENHANCED JSON Response Format:
  [
    {{
      "index": 1,
      "category": "Final specific category name",
      "parent_category": "Immediate parent category", 
      "full_hierarchy": "Top Level > Level 2 > Level 3 > Final Category",
      "confidence": 0.90,
      "reasoning": "Detailed merchant analysis: 'Office Depot' identified as office supply vendor, amount pattern suggests bulk purchase",
      "business_context": "Office supply purchase from recognized vendor",
      "merchant_analysis": "Vendor type: Office supplies retailer",
      "transaction_pattern": "One-time bulk purchase based on amount",
      "hierarchy_levels": 4,
      "accuracy_indicators": ["known_merchant", "clear_category", "amount_consistent"]
    }}
  ]

required_fields:
  - tenant_id
  - transaction_count
  - transactions_list

response_format: "json"