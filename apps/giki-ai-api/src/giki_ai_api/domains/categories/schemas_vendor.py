"""
Vendor-specific schemas for category mapping
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class VendorMappingCreate(BaseModel):
    """Schema for creating a vendor-to-category mapping."""
    
    vendor_name: str = Field(description="The vendor name to map")
    category_id: int = Field(description="The category to map to")
    
    # Transaction direction awareness
    applies_to_debits: bool = Field(default=True, description="Apply to expense transactions")
    applies_to_credits: bool = Field(default=False, description="Apply to income transactions")
    
    # Optional fields for better matching
    description_pattern: Optional[str] = Field(
        default=None, 
        description="Optional regex pattern for more specific matching"
    )
    min_amount: Optional[float] = Field(
        default=None,
        description="Only apply to transactions >= this amount"
    )
    max_amount: Optional[float] = Field(
        default=None,
        description="Only apply to transactions <= this amount"
    )
    
    # User notes
    notes: Optional[str] = Field(
        default=None,
        description="User notes about this mapping"
    )


class VendorMappingUpdate(BaseModel):
    """Schema for updating a vendor mapping."""
    
    category_id: Optional[int] = None
    applies_to_debits: Optional[bool] = None
    applies_to_credits: Optional[bool] = None
    description_pattern: Optional[str] = None
    min_amount: Optional[float] = None
    max_amount: Optional[float] = None
    notes: Optional[str] = None
    is_active: Optional[bool] = None


class VendorMapping(BaseModel):
    """Complete vendor mapping information."""
    
    id: int
    vendor_name: str
    normalized_name: str
    category_id: int
    category_path: str
    
    # Direction settings
    applies_to_debits: bool
    applies_to_credits: bool
    
    # Matching rules
    description_pattern: Optional[str]
    min_amount: Optional[float]
    max_amount: Optional[float]
    
    # Metadata
    confidence_threshold: float
    business_type: Optional[str]
    notes: Optional[str]
    is_active: bool
    
    # Usage stats
    times_applied: int
    last_applied: Optional[datetime]
    
    # Audit
    created_at: datetime
    created_by: int
    updated_at: datetime


class VendorGrouping(BaseModel):
    """Group of transactions by vendor for bulk actions."""
    
    vendor_name: str
    normalized_name: str
    transaction_count: int
    total_amount: float
    
    # Transaction direction breakdown
    debit_count: int
    debit_total: float
    credit_count: int
    credit_total: float
    
    # Current categorization
    categories_used: List[str]
    most_common_category: Optional[str]
    
    # Existing mapping
    has_mapping: bool
    current_mapping: Optional[VendorMapping]
    
    # Sample transactions
    sample_transactions: List[dict]


class BulkVendorMappingRequest(BaseModel):
    """Request to create multiple vendor mappings at once."""
    
    mappings: List[VendorMappingCreate]
    apply_to_existing: bool = Field(
        default=True,
        description="Apply mappings to existing uncategorized transactions"
    )
    apply_to_future: bool = Field(
        default=True,
        description="Apply mappings to future transactions"
    )


class VendorMappingResult(BaseModel):
    """Result of applying vendor mappings."""
    
    mappings_created: int
    mappings_updated: int
    transactions_affected: int
    
    # Breakdown by direction
    debits_categorized: int
    credits_categorized: int
    
    # Conflicts
    conflicts_found: int
    conflict_details: List[dict]
    
    success: bool
    message: str