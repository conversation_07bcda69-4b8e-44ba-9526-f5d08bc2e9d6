"""
Enhanced MIS Validation Service
==============================

Comprehensive validation layer for MIS (Management Information System)
structure compliance, extending the basic validation with business logic
and enterprise-grade validation rules.

This service provides:
- Advanced Income/Expense structure validation
- GL code range and format validation
- Business rule compliance checking
- Industry-specific validation rules
- Financial reporting compliance checks
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

import asyncpg

from ...shared.exceptions import ValidationError
from .gl_industry_templates import GLIndustryTemplates
from .mis_categorization_service import MISCategorizationService

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of a MIS validation check."""

    is_valid: bool
    validation_type: str
    issues: List[str]
    warnings: List[str]
    recommendations: List[str]
    severity: str  # 'critical', 'high', 'medium', 'low'


@dataclass
class ComprehensiveValidationReport:
    """Complete validation report for a tenant's MIS structure."""

    tenant_id: int
    overall_valid: bool
    validation_timestamp: datetime
    structure_validation: ValidationResult
    gl_code_validation: ValidationResult
    business_rules_validation: ValidationResult
    industry_compliance_validation: ValidationResult
    financial_reporting_validation: ValidationResult
    summary_statistics: Dict[str, Any]
    recommendations: List[str]


class MISValidationService:
    """
    Enhanced validation service for MIS structure compliance.

    Provides comprehensive validation beyond basic structure checks,
    including business logic, GL code compliance, and industry standards.
    """

    def __init__(self, db: asyncpg.Connection):
        self.db = db
        self.mis_service = MISCategorizationService(db)

        # Standard GL code ranges for validation
        self.GL_RANGES = {
            "assets": {"start": 1000, "end": 1999},
            "liabilities": {"start": 2000, "end": 2999},
            "equity": {"start": 3000, "end": 3999},
            "revenue": {"start": 4000, "end": 4999},
            "cogs": {"start": 5000, "end": 5499},
            "operating_expenses": {"start": 5500, "end": 7999},
            "other_income": {"start": 8000, "end": 8999},
            "other_expenses": {"start": 9000, "end": 9999},
        }

    async def validate_comprehensive_mis_structure(
        self, tenant_id: int, industry: Optional[str] = None
    ) -> ComprehensiveValidationReport:
        """
        Perform comprehensive MIS structure validation.

        This is the primary validation method that runs all validation checks
        and provides a complete report with recommendations.
        """
        try:
            validation_timestamp = datetime.now()

            # Run all validation checks in parallel
            structure_result = await self._validate_structure(tenant_id)
            gl_code_result = await self._validate_gl_codes(tenant_id)
            business_rules_result = await self._validate_business_rules(tenant_id)
            industry_result = await self._validate_industry_compliance(
                tenant_id, industry
            )
            reporting_result = await self._validate_financial_reporting_compliance(
                tenant_id
            )

            # Generate summary statistics
            summary_stats = await self._generate_summary_statistics(tenant_id)

            # Determine overall validity
            overall_valid = all(
                [
                    structure_result.is_valid,
                    gl_code_result.is_valid,
                    business_rules_result.is_valid,
                    industry_result.is_valid,
                    reporting_result.is_valid,
                ]
            )

            # Generate comprehensive recommendations
            recommendations = self._generate_comprehensive_recommendations(
                [
                    structure_result,
                    gl_code_result,
                    business_rules_result,
                    industry_result,
                    reporting_result,
                ]
            )

            return ComprehensiveValidationReport(
                tenant_id=tenant_id,
                overall_valid=overall_valid,
                validation_timestamp=validation_timestamp,
                structure_validation=structure_result,
                gl_code_validation=gl_code_result,
                business_rules_validation=business_rules_result,
                industry_compliance_validation=industry_result,
                financial_reporting_validation=reporting_result,
                summary_statistics=summary_stats,
                recommendations=recommendations,
            )

        except Exception as e:
            logger.error(
                f"Comprehensive MIS validation failed for tenant {tenant_id}: {e}"
            )
            raise ValidationError(f"Validation failed: {e}")

    async def _validate_structure(self, tenant_id: int) -> ValidationResult:
        """Validate basic MIS structure with enhanced checks."""
        issues = []
        warnings = []
        recommendations = []

        # Use existing basic validation
        basic_validation = await self.mis_service.validate_mis_structure(tenant_id)
        issues.extend(basic_validation.get("issues", []))

        # Enhanced structure checks

        # 1. Check for balanced hierarchy depth
        depth_sql = """
            SELECT level, COUNT(*) as count
            FROM categories
            WHERE tenant_id = $1
            GROUP BY level
            ORDER BY level
        """
        depth_distribution = await self.db.fetch(depth_sql, tenant_id)
        depth_dict = {row["level"]: row["count"] for row in depth_distribution}

        if len(depth_dict) > 4:
            warnings.append(
                "Category hierarchy is very deep (>4 levels) - may be overcomplicated"
            )

        if depth_dict.get(1, 0) < 3:
            warnings.append(
                "Very few level-1 categories - consider organizing expenses better"
            )

        # 2. Check for category naming consistency
        naming_sql = """
            SELECT name, COUNT(*) as count
            FROM categories
            WHERE tenant_id = $1 AND level > 0
            GROUP BY name
            HAVING COUNT(*) > 1
        """
        duplicate_names = await self.db.fetch(naming_sql, tenant_id)
        if duplicate_names:
            issues.append(f"Found {len(duplicate_names)} duplicate category names")

        # 3. Check for required expense categories
        required_expense_categories = [
            "Operating Expenses",
            "Cost of Goods Sold",
            "Administrative Expenses",
        ]
        existing_categories_sql = """
            SELECT DISTINCT name
            FROM categories
            WHERE tenant_id = $1 AND level = 1
        """
        existing_cats = [
            row["name"]
            for row in await self.db.fetch(existing_categories_sql, tenant_id)
        ]

        missing_required = [
            cat for cat in required_expense_categories if cat not in existing_cats
        ]
        if missing_required:
            recommendations.extend(
                [
                    f"Consider adding standard category: {cat}"
                    for cat in missing_required
                ]
            )

        # Determine validity
        is_valid = len(issues) == 0
        severity = "critical" if issues else ("medium" if warnings else "low")

        return ValidationResult(
            is_valid=is_valid,
            validation_type="structure",
            issues=issues,
            warnings=warnings,
            recommendations=recommendations,
            severity=severity,
        )

    async def _validate_gl_codes(self, tenant_id: int) -> ValidationResult:
        """Validate GL code compliance and formatting."""
        issues = []
        warnings = []
        recommendations = []

        # 1. Check GL code format and ranges
        gl_codes_sql = """
            SELECT name, gl_code, level, 
                   CASE 
                       WHEN parent_id IS NULL THEN 'root'
                       ELSE (SELECT name FROM categories p WHERE p.id = categories.parent_id)
                   END as parent_name
            FROM categories
            WHERE tenant_id = $1 AND gl_code IS NOT NULL
        """
        gl_records = await self.db.fetch(gl_codes_sql, tenant_id)

        for record in gl_records:
            gl_code = record["gl_code"]
            parent_name = record["parent_name"]
            category_name = record["name"]

            # Validate GL code format (should be numeric)
            if not gl_code.isdigit():
                issues.append(f"Invalid GL code format '{gl_code}' for {category_name}")
                continue

            gl_num = int(gl_code)

            # Validate ranges based on parent category
            if parent_name == "Income":
                if not (4000 <= gl_num <= 4999):
                    issues.append(
                        f"Income category '{category_name}' has GL code {gl_code} outside revenue range (4000-4999)"
                    )
            elif parent_name == "Expenses":
                if not (5000 <= gl_num <= 9999):
                    issues.append(
                        f"Expense category '{category_name}' has GL code {gl_code} outside expense ranges (5000-9999)"
                    )

            # Check for GL code conflicts
            conflict_sql = """
                SELECT COUNT(*) as count
                FROM categories
                WHERE tenant_id = $1 AND gl_code = $2 AND name != $3
            """
            conflict_count = await self.db.fetchval(
                conflict_sql, tenant_id, gl_code, category_name
            )
            if conflict_count > 0:
                issues.append(f"GL code {gl_code} is used by multiple categories")

        # 2. Check for missing GL codes
        missing_gl_sql = """
            SELECT COUNT(*) as count
            FROM categories
            WHERE tenant_id = $1 AND gl_code IS NULL AND level > 0
        """
        missing_count = await self.db.fetchval(missing_gl_sql, tenant_id)
        if missing_count > 0:
            warnings.append(f"{missing_count} categories missing GL codes")
            recommendations.append(
                "Assign GL codes to all categories for proper financial reporting"
            )

        # 3. Check GL code sequence and gaps
        used_codes_sql = """
            SELECT CAST(gl_code AS INTEGER) as code
            FROM categories
            WHERE tenant_id = $1 AND gl_code IS NOT NULL AND gl_code ~ '^[0-9]+$'
            ORDER BY CAST(gl_code AS INTEGER)
        """
        used_codes = [
            row["code"] for row in await self.db.fetch(used_codes_sql, tenant_id)
        ]

        if used_codes:
            # Check for large gaps in GL code sequence
            gaps = []
            for i in range(len(used_codes) - 1):
                gap = used_codes[i + 1] - used_codes[i]
                if gap > 50:  # Large gap threshold
                    gaps.append((used_codes[i], used_codes[i + 1]))

            if gaps:
                warnings.append(f"Found {len(gaps)} large gaps in GL code sequence")
                recommendations.append(
                    "Consider organizing GL codes in logical sequences"
                )

        # Determine validity
        is_valid = len(issues) == 0
        severity = "high" if issues else ("low" if warnings else "low")

        return ValidationResult(
            is_valid=is_valid,
            validation_type="gl_codes",
            issues=issues,
            warnings=warnings,
            recommendations=recommendations,
            severity=severity,
        )

    async def _validate_business_rules(self, tenant_id: int) -> ValidationResult:
        """Validate business logic and accounting rules."""
        issues = []
        warnings = []
        recommendations = []

        # 1. Check transaction distribution across categories
        txn_distribution_sql = """
            SELECT c.name, c.path, COUNT(t.id) as transaction_count,
                   SUM(ABS(t.amount)) as total_amount
            FROM categories c
            LEFT JOIN transactions t ON t.category_id = c.id AND t.tenant_id = $1
            WHERE c.tenant_id = $1
            GROUP BY c.id, c.name, c.path
            ORDER BY transaction_count DESC
        """
        distribution = await self.db.fetch(txn_distribution_sql, tenant_id)

        total_transactions = sum(row["transaction_count"] for row in distribution)

        if total_transactions > 0:
            # Check for over-concentration in single categories
            for row in distribution:
                percentage = (row["transaction_count"] / total_transactions) * 100
                if percentage > 50:
                    warnings.append(
                        f"Category '{row['name']}' contains {percentage:.1f}% of all transactions - "
                        "consider creating subcategories"
                    )

            # Check for unused categories
            unused_categories = [
                row["name"] for row in distribution if row["transaction_count"] == 0
            ]
            if len(unused_categories) > 5:
                warnings.append(
                    f"{len(unused_categories)} categories have no transactions"
                )
                recommendations.append(
                    "Consider removing or consolidating unused categories"
                )

        # 2. Validate Income/Expense balance logical checks
        balance_sql = """
            WITH category_totals AS (
                SELECT 
                    CASE 
                        WHEN c.path LIKE 'Income%' THEN 'Income'
                        WHEN c.path LIKE 'Expenses%' THEN 'Expenses'
                        ELSE 'Other'
                    END as category_type,
                    SUM(t.amount) as total
                FROM transactions t
                JOIN categories c ON c.id = t.category_id
                WHERE t.tenant_id = $1
                GROUP BY category_type
            )
            SELECT category_type, total FROM category_totals
        """
        balance_data = await self.db.fetch(balance_sql, tenant_id)
        balance_dict = {
            row["category_type"]: float(row["total"]) for row in balance_data
        }

        income_total = balance_dict.get("Income", 0)
        expense_total = balance_dict.get("Expenses", 0)

        # Check for unusual ratios
        if income_total > 0 and expense_total > 0:
            expense_ratio = expense_total / income_total
            if expense_ratio > 1.5:
                warnings.append(
                    f"Expenses ({expense_ratio:.1f}x) significantly exceed income - "
                    "verify categorization accuracy"
                )
            elif expense_ratio < 0.3:
                warnings.append(
                    f"Expenses are unusually low ({expense_ratio:.1f}x of income) - "
                    "verify all expenses are captured"
                )

        # 3. Check for proper expense subcategory usage
        expense_subcat_sql = """
            SELECT c.name, COUNT(t.id) as txn_count
            FROM categories c
            JOIN transactions t ON t.category_id = c.id
            WHERE c.tenant_id = $1 
                AND c.path LIKE 'Expenses%'
                AND c.level = 1
            GROUP BY c.name
        """
        expense_subcats = await self.db.fetch(expense_subcat_sql, tenant_id)

        if len(expense_subcats) < 3:
            recommendations.append(
                "Consider creating more specific expense subcategories for better reporting"
            )

        # Determine validity (business rules are typically warnings, not errors)
        is_valid = len(issues) == 0
        severity = "medium" if issues or warnings else "low"

        return ValidationResult(
            is_valid=is_valid,
            validation_type="business_rules",
            issues=issues,
            warnings=warnings,
            recommendations=recommendations,
            severity=severity,
        )

    async def _validate_industry_compliance(
        self, tenant_id: int, industry: Optional[str]
    ) -> ValidationResult:
        """Validate compliance with industry-specific standards."""
        issues = []
        warnings = []
        recommendations = []

        if not industry:
            # Try to get industry from business context
            context_sql = """
                SELECT COALESCE(tn.settings->'business_context'->>'industry', 'General Business') as industry
                FROM tenants tn
                WHERE tn.id = $1
            """
            industry_row = await self.db.fetchrow(context_sql, tenant_id)
            industry = industry_row["industry"] if industry_row else "General Business"

        if industry and industry != "General Business":
            # Get industry-specific GL templates
            industry_templates = GLIndustryTemplates.get_industry_template(industry)

            if industry_templates:
                expected_categories = set()
                for category_data in industry_templates.get("categories", []):
                    expected_categories.add(category_data["name"])

                # Check for missing industry-standard categories
                existing_categories_sql = """
                    SELECT name FROM categories
                    WHERE tenant_id = $1 AND level >= 1
                """
                existing_cats = {
                    row["name"]
                    for row in await self.db.fetch(existing_categories_sql, tenant_id)
                }

                missing_standard = expected_categories - existing_cats
                if (
                    missing_standard and len(missing_standard) <= 5
                ):  # Don't overwhelm with recommendations
                    recommendations.extend(
                        [
                            f"Consider adding industry-standard category: {cat}"
                            for cat in list(missing_standard)[:5]
                        ]
                    )

                # Check for industry-specific GL code ranges
                gl_recommendations = industry_templates.get(
                    "gl_code_recommendations", {}
                )
                if gl_recommendations:
                    recommendations.append(
                        f"Consider using industry-specific GL code patterns for {industry}"
                    )

        # Industry compliance is typically about recommendations
        is_valid = len(issues) == 0
        severity = "low"  # Industry compliance is usually about optimization

        return ValidationResult(
            is_valid=is_valid,
            validation_type="industry_compliance",
            issues=issues,
            warnings=warnings,
            recommendations=recommendations,
            severity=severity,
        )

    async def _validate_financial_reporting_compliance(
        self, tenant_id: int
    ) -> ValidationResult:
        """Validate compliance with financial reporting standards."""
        issues = []
        warnings = []
        recommendations = []

        # 1. Check for proper P&L structure
        pl_structure_sql = """
            SELECT 
                c.name, c.level, c.path,
                COUNT(t.id) as transaction_count,
                SUM(CASE WHEN t.amount > 0 THEN t.amount ELSE 0 END) as positive_amount,
                SUM(CASE WHEN t.amount < 0 THEN ABS(t.amount) ELSE 0 END) as negative_amount
            FROM categories c
            LEFT JOIN transactions t ON t.category_id = c.id
            WHERE c.tenant_id = $1 AND c.level <= 2
            GROUP BY c.id, c.name, c.level, c.path
            ORDER BY c.path
        """
        pl_structure = await self.db.fetch(pl_structure_sql, tenant_id)

        # Check for standard financial statement categories
        required_for_pl = {
            "Revenue": False,
            "Income": False,
            "Cost of Goods Sold": False,
            "COGS": False,
            "Operating Expenses": False,
            "Administrative Expenses": False,
        }

        for row in pl_structure:
            for required_cat in required_for_pl.keys():
                if required_cat.lower() in row["name"].lower():
                    required_for_pl[required_cat] = True

        # Check if we have revenue recognition
        has_revenue = required_for_pl["Revenue"] or required_for_pl["Income"]
        if not has_revenue:
            warnings.append("No clear revenue/income categories identified")

        # Check for COGS if applicable
        has_cogs = required_for_pl["Cost of Goods Sold"] or required_for_pl["COGS"]
        total_transactions = sum(row["transaction_count"] for row in pl_structure)

        if (
            total_transactions > 50 and not has_cogs
        ):  # Only for businesses with substantial activity
            recommendations.append(
                "Consider adding Cost of Goods Sold category for better P&L reporting"
            )

        # 2. Check for month-end/year-end closing readiness
        period_sql = """
            SELECT 
                DATE_TRUNC('month', t.date) as month,
                COUNT(*) as transaction_count,
                COUNT(CASE WHEN c.gl_code IS NULL THEN 1 END) as missing_gl_count
            FROM transactions t
            LEFT JOIN categories c ON c.id = t.category_id
            WHERE t.tenant_id = $1
            GROUP BY DATE_TRUNC('month', t.date)
            ORDER BY month DESC
            LIMIT 3
        """
        period_data = await self.db.fetch(period_sql, tenant_id)

        for row in period_data:
            if row["missing_gl_count"] > 0:
                missing_percentage = (
                    row["missing_gl_count"] / row["transaction_count"]
                ) * 100
                if missing_percentage > 10:
                    warnings.append(
                        f"Month {row['month'].strftime('%Y-%m')} has {missing_percentage:.1f}% "
                        "transactions without GL codes"
                    )

        # 3. Check for proper expense categorization for tax reporting
        tax_categories_sql = """
            SELECT c.name, c.path, SUM(ABS(t.amount)) as amount
            FROM categories c
            JOIN transactions t ON t.category_id = c.id
            WHERE c.tenant_id = $1 
                AND c.path LIKE 'Expenses%'
                AND t.amount < 0
            GROUP BY c.name, c.path
            HAVING SUM(ABS(t.amount)) > 1000
        """
        tax_categories = await self.db.fetch(tax_categories_sql, tenant_id)

        generic_expense_names = {"other", "miscellaneous", "general", "various"}
        total_expense_amount = sum(row["amount"] for row in tax_categories)

        for row in tax_categories:
            if any(generic in row["name"].lower() for generic in generic_expense_names):
                percentage = (
                    (row["amount"] / total_expense_amount) * 100
                    if total_expense_amount > 0
                    else 0
                )
                if percentage > 15:
                    warnings.append(
                        f"Large amount ({percentage:.1f}%) in generic category '{row['name']}' - "
                        "consider more specific categorization for tax compliance"
                    )

        # Financial reporting compliance is critical for business
        is_valid = len(issues) == 0
        severity = "high" if issues else ("medium" if warnings else "low")

        return ValidationResult(
            is_valid=is_valid,
            validation_type="financial_reporting",
            issues=issues,
            warnings=warnings,
            recommendations=recommendations,
            severity=severity,
        )

    async def _generate_summary_statistics(self, tenant_id: int) -> Dict[str, Any]:
        """Generate comprehensive statistics about the MIS structure."""

        stats_sql = """
            WITH category_stats AS (
                SELECT 
                    COUNT(*) as total_categories,
                    COUNT(CASE WHEN level = 0 THEN 1 END) as root_categories,
                    COUNT(CASE WHEN level = 1 THEN 1 END) as level1_categories,
                    COUNT(CASE WHEN level = 2 THEN 1 END) as level2_categories,
                    COUNT(CASE WHEN gl_code IS NOT NULL THEN 1 END) as categories_with_gl,
                    MAX(level) as max_depth
                FROM categories
                WHERE tenant_id = $1
            ),
            transaction_stats AS (
                SELECT 
                    COUNT(*) as total_transactions,
                    COUNT(CASE WHEN c.id IS NOT NULL THEN 1 END) as categorized_transactions,
                    SUM(CASE WHEN t.amount > 0 THEN t.amount ELSE 0 END) as total_income,
                    SUM(CASE WHEN t.amount < 0 THEN ABS(t.amount) ELSE 0 END) as total_expenses
                FROM transactions t
                LEFT JOIN categories c ON c.id = t.category_id
                WHERE t.tenant_id = $1
            )
            SELECT 
                cs.total_categories, cs.root_categories, cs.level1_categories, 
                cs.level2_categories, cs.categories_with_gl, cs.max_depth,
                ts.total_transactions, ts.categorized_transactions,
                ts.total_income, ts.total_expenses
            FROM category_stats cs, transaction_stats ts
        """

        stats = await self.db.fetchrow(stats_sql, tenant_id)

        # Calculate additional metrics
        categorization_rate = 0
        if stats["total_transactions"] > 0:
            categorization_rate = (
                stats["categorized_transactions"] / stats["total_transactions"]
            ) * 100

        gl_code_coverage = 0
        if stats["total_categories"] > 0:
            gl_code_coverage = (
                stats["categories_with_gl"] / stats["total_categories"]
            ) * 100

        return {
            "total_categories": stats["total_categories"],
            "hierarchy_depth": stats["max_depth"],
            "root_categories": stats["root_categories"],
            "level1_categories": stats["level1_categories"],
            "level2_categories": stats["level2_categories"],
            "gl_code_coverage_percentage": round(gl_code_coverage, 1),
            "total_transactions": stats["total_transactions"],
            "categorization_rate_percentage": round(categorization_rate, 1),
            "total_income": float(stats["total_income"] or 0),
            "total_expenses": float(stats["total_expenses"] or 0),
            "expense_to_income_ratio": round(
                (
                    float(stats["total_expenses"] or 0)
                    / float(stats["total_income"] or 1)
                ),
                2,
            ),
        }

    def _generate_comprehensive_recommendations(
        self, validation_results: List[ValidationResult]
    ) -> List[str]:
        """Generate prioritized recommendations from all validation results."""

        all_recommendations = []
        critical_issues = []

        for result in validation_results:
            # Add critical issues first
            if result.severity == "critical":
                critical_issues.extend(result.issues)

            # Add recommendations
            all_recommendations.extend(result.recommendations)

        # Prioritize recommendations
        prioritized = []

        # Critical fixes first
        if critical_issues:
            prioritized.append("CRITICAL: Fix structural issues before proceeding")
            prioritized.extend([f"- {issue}" for issue in critical_issues[:3]])

        # Add unique recommendations (avoid duplicates)
        seen_recommendations = set()
        for rec in all_recommendations:
            if rec not in seen_recommendations:
                prioritized.append(rec)
                seen_recommendations.add(rec)

                # Limit to top 10 recommendations
                if len(prioritized) >= 10:
                    break

        return prioritized

    async def quick_validation_check(self, tenant_id: int) -> Dict[str, Any]:
        """Perform a quick validation check for dashboard display."""
        try:
            # Basic structure check
            basic_validation = await self.mis_service.validate_mis_structure(tenant_id)

            # Quick GL code check
            missing_gl_sql = """
                SELECT COUNT(*) as missing_count
                FROM categories
                WHERE tenant_id = $1 AND gl_code IS NULL AND level > 0
            """
            missing_gl_count = await self.db.fetchval(missing_gl_sql, tenant_id)

            # Quick stats
            stats = await self._generate_summary_statistics(tenant_id)

            return {
                "is_valid": basic_validation["valid"],
                "critical_issues_count": len(basic_validation.get("issues", [])),
                "missing_gl_codes": missing_gl_count,
                "categorization_rate": stats["categorization_rate_percentage"],
                "gl_coverage": stats["gl_code_coverage_percentage"],
                "structure_health": "good"
                if basic_validation["valid"]
                else "needs_attention",
            }

        except Exception as e:
            logger.error(f"Quick validation failed for tenant {tenant_id}: {e}")
            return {"is_valid": False, "error": str(e), "structure_health": "error"}
