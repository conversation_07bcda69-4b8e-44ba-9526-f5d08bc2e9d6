"""
Admin Router - Administrative Operations and System Management

Provides endpoints for:
- User and tenant management
- System health monitoring
- Database operations
- Configuration management
"""

import logging
from typing import Any, Dict, List, Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Security, status
from fastapi.security import APIKeyHeader
from pydantic import BaseModel, Field

from ...core.config import settings
from ...core.dependencies import get_db_session
from ..auth.secure_auth import get_current_active_user
from .service import AdminService

logger = logging.getLogger(__name__)

# API key security scheme for admin endpoints
API_KEY_NAME = "X-Admin-Key"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)

# Get the admin API key from settings (loads from .env file)
ADMIN_API_KEY = settings.ADMIN_API_KEY
if not ADMIN_API_KEY:
    logger.warning(
        "ADMIN_API_KEY not configured in settings. Admin endpoints will require proper configuration."
    )

router = APIRouter(tags=["Admin"])


# Request/Response Models
class CreateTenantRequest(BaseModel):
    """Request model for creating a tenant."""

    name: str = Field(..., description="Tenant name")
    email: str = Field(..., description="Tenant contact email")
    settings: Optional[Dict[str, Any]] = Field(None, description="Tenant settings")
    is_active: bool = Field(True, description="Whether tenant is active")


class TenantResponse(BaseModel):
    """Response model for tenant information."""

    id: int = Field(..., description="Tenant ID")
    name: str = Field(..., description="Tenant name")
    email: str = Field(..., description="Tenant email")
    is_active: bool = Field(..., description="Whether tenant is active")
    created_at: Optional[str] = Field(None, description="Creation timestamp")
    updated_at: Optional[str] = Field(None, description="Last update timestamp")


class TenantsListResponse(BaseModel):
    """Response model for tenant list."""

    tenants: List[TenantResponse] = Field(..., description="List of tenants")
    total: int = Field(..., description="Total number of tenants")
    limit: int = Field(..., description="Page limit")
    offset: int = Field(..., description="Page offset")
    has_more: bool = Field(..., description="Whether there are more results")


class UpdateTenantRequest(BaseModel):
    """Request model for updating a tenant."""

    name: Optional[str] = Field(None, description="Tenant name")
    email: Optional[str] = Field(None, description="Tenant email")
    settings: Optional[Dict[str, Any]] = Field(None, description="Tenant settings")
    is_active: Optional[bool] = Field(None, description="Whether tenant is active")


class CreateUserRequest(BaseModel):
    """Request model for creating a user."""

    email: str = Field(..., description="User email")
    password: str = Field(..., description="User password")
    is_active: bool = Field(True, description="Whether user is active")
    is_verified: bool = Field(False, description="Whether user is verified")


class UserResponse(BaseModel):
    """Response model for user information."""

    id: int = Field(..., description="User ID")
    email: str = Field(..., description="User email")
    tenant_id: int = Field(..., description="Tenant ID")
    is_active: bool = Field(..., description="Whether user is active")
    is_verified: bool = Field(..., description="Whether user is verified")
    created_at: Optional[str] = Field(None, description="Creation timestamp")
    last_login: Optional[str] = Field(None, description="Last login timestamp")


class SystemHealthResponse(BaseModel):
    """Response model for system health."""

    status: str = Field(..., description="Overall system status")
    timestamp: Optional[str] = Field(None, description="Health check timestamp")
    services: Dict[str, Any] = Field(..., description="Service health status")
    metrics: Dict[str, Any] = Field(..., description="System metrics")


class MigrationResponse(BaseModel):
    """Response model for database migration."""

    status: str = Field(..., description="Migration status")
    message: str = Field(..., description="Migration message")
    stdout: Optional[str] = Field(None, description="Migration stdout")
    stderr: Optional[str] = Field(None, description="Migration stderr")


# Security dependency
async def get_admin_api_key(api_key: str = Security(api_key_header)):
    """Validate the admin API key."""
    if not ADMIN_API_KEY:
        logger.error("ADMIN_API_KEY environment variable not set")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Admin API key not configured on server",
        )

    if not api_key or api_key != ADMIN_API_KEY:
        logger.warning("Invalid admin API key provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid admin API key",
        )

    return api_key


# Service dependency
async def get_admin_service(conn: Connection = Depends(get_db_session)) -> AdminService:
    """Get admin service instance."""
    return AdminService(conn=conn)


# Endpoints
@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health(
    admin_service: AdminService = Depends(get_admin_service),
    api_key: str = Depends(get_admin_api_key),
):
    """Get system health status and metrics."""
    try:
        health_data = await admin_service.get_system_health()
        return SystemHealthResponse(**health_data)
    except Exception as e:
        logger.error(f"System health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system health",
        )


@router.post("/tenants", response_model=TenantResponse)
async def create_tenant(
    request: CreateTenantRequest,
    admin_service: AdminService = Depends(get_admin_service),
    current_user=Depends(get_current_active_user),
    api_key: str = Depends(get_admin_api_key),
):
    """Create a new tenant."""
    try:
        tenant_data = request.dict()
        result = await admin_service.create_tenant(
            tenant_data=tenant_data, admin_user_id=current_user.id
        )
        return TenantResponse(**result)
    except Exception as e:
        logger.error(f"Tenant creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create tenant",
        )


@router.get("/tenants", response_model=TenantsListResponse)
async def get_tenants(
    limit: int = 100,
    offset: int = 0,
    active_only: bool = False,
    admin_service: AdminService = Depends(get_admin_service),
    api_key: str = Depends(get_admin_api_key),
):
    """Get list of tenants with pagination."""
    try:
        result = await admin_service.get_tenants(
            limit=limit, offset=offset, active_only=active_only
        )

        # Convert tenant data to response models
        tenant_responses = [TenantResponse(**tenant) for tenant in result["tenants"]]

        return TenantsListResponse(
            tenants=tenant_responses,
            total=result["total"],
            limit=result["limit"],
            offset=result["offset"],
            has_more=result["has_more"],
        )
    except Exception as e:
        logger.error(f"Tenant retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tenants",
        )


@router.put("/tenants/{tenant_id}", response_model=TenantResponse)
async def update_tenant(
    tenant_id: int,
    request: UpdateTenantRequest,
    admin_service: AdminService = Depends(get_admin_service),
    current_user=Depends(get_current_active_user),
    api_key: str = Depends(get_admin_api_key),
):
    """Update tenant information."""
    try:
        update_data = {k: v for k, v in request.dict().items() if v is not None}
        result = await admin_service.update_tenant(
            tenant_id=tenant_id, update_data=update_data, admin_user_id=current_user.id
        )
        return TenantResponse(**result)
    except Exception as e:
        logger.error(f"Tenant update failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update tenant",
        )


@router.post("/tenants/{tenant_id}/users", response_model=UserResponse)
async def create_user(
    tenant_id: int,
    request: CreateUserRequest,
    admin_service: AdminService = Depends(get_admin_service),
    current_user=Depends(get_current_active_user),
    api_key: str = Depends(get_admin_api_key),
):
    """Create a new user for a tenant."""
    try:
        user_data = request.dict()
        result = await admin_service.create_user(
            user_data=user_data, tenant_id=tenant_id, admin_user_id=current_user.id
        )
        return UserResponse(**result)
    except Exception as e:
        logger.error(f"User creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user",
        )


@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    admin_service: AdminService = Depends(get_admin_service),
    api_key: str = Depends(get_admin_api_key),
):
    """Get user information by ID."""
    try:
        result = await admin_service.get_user_by_id(user_id)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )
        return UserResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user",
        )


@router.post("/users/{user_id}/deactivate", response_model=UserResponse)
async def deactivate_user(
    user_id: int,
    admin_service: AdminService = Depends(get_admin_service),
    current_user=Depends(get_current_active_user),
    api_key: str = Depends(get_admin_api_key),
):
    """Deactivate a user account."""
    try:
        result = await admin_service.deactivate_user(
            user_id=user_id, admin_user_id=current_user.id
        )
        return UserResponse(**result)
    except Exception as e:
        logger.error(f"User deactivation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate user",
        )


@router.post("/database/migrate", response_model=MigrationResponse)
async def run_database_migration(
    admin_service: AdminService = Depends(get_admin_service),
    api_key: str = Depends(get_admin_api_key),
):
    """Run database migrations."""
    try:
        result = await admin_service.run_database_migration()
        return MigrationResponse(**result)
    except Exception as e:
        logger.error(f"Database migration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to run database migration",
        )


@router.get("/status")
async def admin_status():
    """Simple admin status check (no auth required)."""
    return {
        "status": "admin_ready",
        "service": "admin",
        "auth_configured": ADMIN_API_KEY is not None,
    }
