"""
Admin Service - Administrative Operations and System Management

Provides functionality for:
- User management (create, update, delete users)
- Tenant management (create, configure tenants)
- System health monitoring
- Database operations
- Configuration management
"""

import logging
import os
import subprocess
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from asyncpg import Connection

from ...domains.auth.models import Tenant, User
from ...shared.exceptions import ServiceError

logger = logging.getLogger(__name__)


class AdminService:
    """
    Administrative service providing:
    - User and tenant management
    - System monitoring and health checks
    - Database operations
    - Configuration management
    """

    def __init__(self, conn: Connection):
        """Initialize admin service with database connection."""
        self.conn = conn

    async def create_tenant(
        self, tenant_data: Dict[str, Any], admin_user_id: int
    ) -> Dict[str, Any]:
        """
        Create a new tenant with initial configuration.

        Args:
            tenant_data: Tenant creation data
            admin_user_id: ID of admin user creating the tenant

        Returns:
            Dict with created tenant information
        """
        try:
            # Validate required fields
            required_fields = ["name", "email"]
            for field in required_fields:
                if field not in tenant_data:
                    raise ServiceError(
                        f"Missing required field: {field}",
                        service_name="AdminService",
                        operation="create_tenant",
                        error_code="VALIDATION_ERROR",
                    )

            # Check if tenant already exists
            query = "SELECT * FROM tenants WHERE name = $1"
            existing_row = await self.conn.fetchrow(query, tenant_data["name"])

            if existing_row:
                raise ServiceError(
                    f"Tenant with name '{tenant_data['name']}' already exists",
                    service_name="AdminService",
                    operation="create_tenant",
                    error_code="TENANT_EXISTS",
                )

            # Create tenant
            tenant_id = str(uuid.uuid4())
            created_at = datetime.utcnow()

            insert_query = """
                INSERT INTO tenants (id, name, email, settings, is_active, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $6)
                RETURNING *
            """

            import json

            tenant_row = await self.conn.fetchrow(
                insert_query,
                tenant_id,
                tenant_data["name"],
                tenant_data["email"],
                json.dumps(tenant_data.get("settings", {})),
                tenant_data.get("is_active", True),
                created_at,
            )

            new_tenant = Tenant(**dict(tenant_row))

            logger.info(f"Created tenant '{tenant_data['name']}' (ID: {new_tenant.id})")

            return {
                "id": new_tenant.id,
                "name": new_tenant.name,
                "email": new_tenant.email,
                "is_active": new_tenant.is_active,
                "created_at": new_tenant.created_at.isoformat()
                if new_tenant.created_at
                else None,
            }

        except Exception as e:
            logger.error(f"Failed to create tenant: {e}")
            raise ServiceError(
                "Failed to create tenant",
                service_name="AdminService",
                operation="create_tenant",
                error_code="TENANT_CREATION_ERROR",
                original_error=e,
            )

    async def get_tenants(
        self, limit: int = 100, offset: int = 0, active_only: bool = False
    ) -> Dict[str, Any]:
        """
        Get list of tenants with pagination.

        Returns:
            Dict with tenants list and pagination info
        """
        try:
            # Build query
            where_clause = "WHERE is_active = true" if active_only else ""

            # Count total
            count_query = f"SELECT COUNT(*) FROM tenants {where_clause}"
            total = await self.conn.fetchval(count_query)

            # Get tenants with pagination
            query = f"""
                SELECT * FROM tenants
                {where_clause}
                ORDER BY created_at DESC
                LIMIT $1 OFFSET $2
            """
            rows = await self.conn.fetch(query, limit, offset)
            tenants = [Tenant(**dict(row)) for row in rows]

            tenant_list = []
            for tenant in tenants:
                tenant_list.append(
                    {
                        "id": tenant.id,
                        "name": tenant.name,
                        "email": tenant.email,
                        "is_active": tenant.is_active,
                        "created_at": tenant.created_at.isoformat()
                        if tenant.created_at
                        else None,
                        "updated_at": tenant.updated_at.isoformat()
                        if tenant.updated_at
                        else None,
                    }
                )

            return {
                "tenants": tenant_list,
                "total": total,
                "limit": limit,
                "offset": offset,
                "has_more": offset + len(tenant_list) < total,
            }

        except Exception as e:
            logger.error(f"Failed to get tenants: {e}")
            raise ServiceError(
                "Failed to retrieve tenants",
                service_name="AdminService",
                operation="get_tenants",
                error_code="TENANT_RETRIEVAL_ERROR",
                original_error=e,
            )

    async def update_tenant(
        self, tenant_id: int, update_data: Dict[str, Any], admin_user_id: int
    ) -> Dict[str, Any]:
        """
        Update tenant information.

        Args:
            tenant_id: ID of tenant to update
            update_data: Data to update
            admin_user_id: ID of admin user making the update

        Returns:
            Dict with updated tenant information
        """
        try:
            # Get tenant
            # Check if tenant exists
            query = "SELECT * FROM tenants WHERE id = $1"
            tenant_row = await self.conn.fetchrow(query, tenant_id)

            if not tenant_row:
                raise ServiceError(
                    f"Tenant with ID {tenant_id} not found",
                    service_name="AdminService",
                    operation="update_tenant",
                    error_code="TENANT_NOT_FOUND",
                )

            # Build update query dynamically
            update_fields = []
            params = []
            param_count = 1

            allowed_fields = ["name", "email", "settings", "is_active"]
            for field in allowed_fields:
                if field in update_data:
                    update_fields.append(f"{field} = ${param_count + 1}")
                    if field == "settings":
                        import json

                        params.append(json.dumps(update_data[field]))
                    else:
                        params.append(update_data[field])
                    param_count += 1

            if update_fields:
                update_fields.append(f"updated_at = ${param_count + 1}")
                params.append(datetime.utcnow())

                update_query = f"""
                    UPDATE tenants 
                    SET {", ".join(update_fields)}
                    WHERE id = $1
                    RETURNING *
                """

                tenant_row = await self.conn.fetchrow(update_query, tenant_id, *params)
                tenant = Tenant(**dict(tenant_row))
            else:
                tenant = Tenant(**dict(tenant_row))

            logger.info(f"Updated tenant {tenant_id}")

            return {
                "id": tenant.id,
                "name": tenant.name,
                "email": tenant.email,
                "is_active": tenant.is_active,
                "settings": tenant.settings,
                "updated_at": tenant.updated_at.isoformat()
                if tenant.updated_at
                else None,
            }

        except Exception as e:
            logger.error(f"Failed to update tenant {tenant_id}: {e}")
            raise ServiceError(
                "Failed to update tenant",
                service_name="AdminService",
                operation="update_tenant",
                error_code="TENANT_UPDATE_ERROR",
                original_error=e,
            )

    async def create_user(
        self, user_data: Dict[str, Any], tenant_id: int, admin_user_id: int
    ) -> Dict[str, Any]:
        """
        Create a new user for a tenant.

        Args:
            user_data: User creation data
            tenant_id: ID of tenant to create user for
            admin_user_id: ID of admin user creating the user

        Returns:
            Dict with created user information
        """
        try:
            from passlib.context import CryptContext

            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

            # Validate required fields
            required_fields = ["email", "password"]
            for field in required_fields:
                if field not in user_data:
                    raise ServiceError(
                        f"Missing required field: {field}",
                        service_name="AdminService",
                        operation="create_user",
                        error_code="VALIDATION_ERROR",
                    )

            # Check if user already exists
            query = "SELECT * FROM users WHERE email = $1"
            existing_row = await self.conn.fetchrow(query, user_data["email"])

            if existing_row:
                raise ServiceError(
                    f"User with email '{user_data['email']}' already exists",
                    service_name="AdminService",
                    operation="create_user",
                    error_code="USER_EXISTS",
                )

            # Hash password
            hashed_password = pwd_context.hash(user_data["password"])

            # Create user
            user_id = str(uuid.uuid4())
            created_at = datetime.utcnow()

            insert_query = """
                INSERT INTO users (id, email, hashed_password, tenant_id, is_active, is_verified, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $7)
                RETURNING *
            """

            user_row = await self.conn.fetchrow(
                insert_query,
                user_id,
                user_data["email"],
                hashed_password,
                tenant_id,
                user_data.get("is_active", True),
                user_data.get("is_verified", False),
                created_at,
            )

            new_user = User(**dict(user_row))
            # Note: asyncpg handles transactions automatically

            logger.info(f"Created user '{user_data['email']}' for tenant {tenant_id}")

            return {
                "id": new_user.id,
                "email": new_user.email,
                "tenant_id": new_user.tenant_id,
                "is_active": new_user.is_active,
                "is_verified": new_user.is_verified,
                "created_at": new_user.created_at.isoformat()
                if new_user.created_at
                else None,
            }

        except Exception as e:
            logger.error(f"Failed to create user: {e}")
            raise ServiceError(
                "Failed to create user",
                service_name="AdminService",
                operation="create_user",
                error_code="USER_CREATION_ERROR",
                original_error=e,
            )

    async def get_system_health(self) -> Dict[str, Any]:
        """
        Get system health status and metrics.

        Returns:
            Dict with system health information
        """
        try:
            health_data = {
                "status": "healthy",
                "timestamp": None,
                "services": {},
                "metrics": {},
            }

            # Database health check
            try:
                result = await self.conn.fetchval("SELECT 1")
                db_status = "healthy" if result == 1 else "unhealthy"
            except Exception as db_error:
                db_status = "unhealthy"
                logger.warning(f"Database health check failed: {db_error}")

            health_data["services"]["database"] = {"status": db_status}

            # Get basic metrics
            try:
                # Count tenants
                tenant_count = await self.conn.fetchval("SELECT COUNT(*) FROM tenants")

                # Count active tenants
                active_tenant_count = await self.conn.fetchval(
                    "SELECT COUNT(*) FROM tenants WHERE is_active = true"
                )

                # Count users
                user_count = await self.conn.fetchval("SELECT COUNT(*) FROM users")

                health_data["metrics"] = {
                    "total_tenants": tenant_count,
                    "active_tenants": active_tenant_count,
                    "total_users": user_count,
                }

            except Exception as metrics_error:
                logger.warning(f"Failed to get system metrics: {metrics_error}")
                health_data["metrics"] = {"error": "Failed to retrieve metrics"}

            # Overall status
            if db_status != "healthy":
                health_data["status"] = "unhealthy"

            from datetime import datetime

            health_data["timestamp"] = datetime.utcnow().isoformat()

            return health_data

        except Exception as e:
            logger.error(f"Failed to get system health: {e}")
            return {"status": "unhealthy", "error": str(e), "timestamp": None}

    async def run_database_migration(self) -> Dict[str, Any]:
        """
        Run database migrations.

        Returns:
            Dict with migration results
        """
        try:
            # Get the database URL from environment
            db_url = os.environ.get("DATABASE_URL")
            if not db_url:
                raise ServiceError(
                    "DATABASE_URL environment variable not set",
                    service_name="AdminService",
                    operation="run_database_migration",
                    error_code="CONFIG_ERROR",
                )

            # Run alembic upgrade head
            result = subprocess.run(
                ["alembic", "upgrade", "head"],
                capture_output=True,
                text=True,
                check=False,
                cwd="/app",  # Assume we're in containerized environment
            )

            if result.returncode == 0:
                logger.info("Database migration successful")
                return {
                    "status": "success",
                    "message": "Database migration completed successfully",
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                }
            else:
                logger.error(f"Database migration failed: {result.stderr}")
                return {
                    "status": "error",
                    "message": "Database migration failed",
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                }

        except Exception as e:
            logger.error(f"Migration execution failed: {e}")
            raise ServiceError(
                "Failed to run database migration",
                service_name="AdminService",
                operation="run_database_migration",
                error_code="MIGRATION_ERROR",
                original_error=e,
            )

    async def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user information by ID."""
        try:
            query = "SELECT * FROM users WHERE id = $1"
            user_row = await self.conn.fetchrow(query, user_id)

            if not user_row:
                return None

            user = User(**dict(user_row))

            return {
                "id": user.id,
                "email": user.email,
                "tenant_id": user.tenant_id,
                "is_active": user.is_active,
                "is_verified": user.is_verified,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "last_login": user.last_login.isoformat()
                if hasattr(user, "last_login") and user.last_login
                else None,
            }

        except Exception as e:
            logger.error(f"Failed to get user {user_id}: {e}")
            raise ServiceError(
                "Failed to retrieve user",
                service_name="AdminService",
                operation="get_user_by_id",
                error_code="USER_RETRIEVAL_ERROR",
                original_error=e,
            )

    async def deactivate_user(self, user_id: int, admin_user_id: int) -> Dict[str, Any]:
        """Deactivate a user account."""
        try:
            # Check if user exists and update
            update_query = """
                UPDATE users 
                SET is_active = false, updated_at = $2
                WHERE id = $1
                RETURNING *
            """

            user_row = await self.conn.fetchrow(
                update_query, user_id, datetime.utcnow()
            )

            if not user_row:
                raise ServiceError(
                    f"User with ID {user_id} not found",
                    service_name="AdminService",
                    operation="deactivate_user",
                    error_code="USER_NOT_FOUND",
                )

            user = User(**dict(user_row))

            logger.info(f"Deactivated user {user_id} by admin {admin_user_id}")

            return {
                "id": user.id,
                "email": user.email,
                "is_active": user.is_active,
                "message": "User deactivated successfully",
            }

        except Exception as e:
            logger.error(f"Failed to deactivate user {user_id}: {e}")
            raise ServiceError(
                "Failed to deactivate user",
                service_name="AdminService",
                operation="deactivate_user",
                error_code="USER_DEACTIVATION_ERROR",
                original_error=e,
            )
