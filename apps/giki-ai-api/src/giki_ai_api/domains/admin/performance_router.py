"""
Performance Monitoring Router
Real-time API performance metrics and optimization monitoring
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...core.database import get_db_session
from ...core.dependencies import get_current_tenant_id
from ...shared.cache.performance_cache import get_cache_health, performance_cache
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/performance",
    tags=["Performance Monitoring"],
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Not found"},
        status.HTTP_401_UNAUTHORIZED: {"description": "Not authenticated"},
        status.HTTP_403_FORBIDDEN: {"description": "Not authorized"},
    },
)


class PerformanceMetrics(BaseModel):
    """Performance metrics response schema"""

    api_health: Dict[str, Any]
    database_performance: Dict[str, Any]
    cache_performance: Dict[str, Any]
    slow_queries: List[Dict[str, Any]]
    response_times: Dict[str, float]
    recommendations: List[str]


class QueryPerformanceResult(BaseModel):
    """Query performance analysis result"""

    query_type: str
    avg_execution_time_ms: float
    max_execution_time_ms: float
    query_count: int
    tenant_id: Optional[int]
    slow_query_threshold_ms: float = 500.0


@router.get("/health", response_model=Dict[str, Any])
async def get_performance_health(
    current_user: User = Depends(get_current_active_user),
    conn: Connection = Depends(get_db_session),
) -> Dict[str, Any]:
    """
    Get comprehensive performance health status
    """
    try:
        start_time = time.time()

        # Test database performance
        db_start = time.time()
        await conn.fetchval("SELECT 1")
        db_response_time = (time.time() - db_start) * 1000

        # Get cache health
        cache_health = await get_cache_health()

        # Check for slow queries in the last hour
        slow_queries = await _get_recent_slow_queries(conn)

        # Calculate API health score
        api_health_score = _calculate_health_score(
            db_response_time, cache_health.get("status") == "healthy", len(slow_queries)
        )

        total_time = (time.time() - start_time) * 1000

        health_status = {
            "overall_status": "healthy"
            if api_health_score > 80
            else "degraded"
            if api_health_score > 60
            else "unhealthy",
            "health_score": api_health_score,
            "timestamp": datetime.utcnow().isoformat(),
            "response_time_ms": round(total_time, 2),
            "database": {
                "status": "healthy" if db_response_time < 50 else "slow",
                "response_time_ms": round(db_response_time, 2),
                "connection_pool": await _get_connection_pool_stats(conn),
            },
            "cache": cache_health,
            "slow_queries_count": len(slow_queries),
            "performance_targets": {
                "api_response_time_target_ms": 500,
                "database_response_time_target_ms": 50,
                "cache_hit_rate_target_pct": 80,
            },
            "recommendations": _generate_performance_recommendations(
                db_response_time, cache_health, slow_queries
            ),
        }

        # Log performance warnings
        if api_health_score < 80:
            logger.warning(f"⚠️ API health degraded: score {api_health_score}/100")

        return health_status

    except Exception as e:
        logger.error(f"Error getting performance health: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get performance health",
        )


@router.get("/metrics", response_model=PerformanceMetrics)
async def get_performance_metrics(
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    hours_back: int = Query(1, ge=1, le=24, description="Hours of metrics to analyze"),
    conn: Connection = Depends(get_db_session),
) -> PerformanceMetrics:
    """
    Get detailed performance metrics for analysis
    """
    try:
        start_time = time.time()

        # Get API health
        api_health = {
            "uptime_hours": hours_back,
            "status": "monitoring",
            "last_check": datetime.utcnow().isoformat(),
        }

        # Database performance analysis
        db_performance = await _analyze_database_performance(
            conn, tenant_id, hours_back
        )

        # Cache performance
        cache_metrics = await performance_cache.get_cache_metrics()

        # Slow queries analysis
        slow_queries = await _get_recent_slow_queries(conn, hours_back)

        # Response time analysis
        response_times = await _analyze_response_times(conn, tenant_id, hours_back)

        # Generate recommendations
        recommendations = _generate_detailed_recommendations(
            db_performance, cache_metrics, slow_queries, response_times
        )

        metrics_time = (time.time() - start_time) * 1000
        logger.info(f"Performance metrics generated in {metrics_time:.2f}ms")

        return PerformanceMetrics(
            api_health=api_health,
            database_performance=db_performance,
            cache_performance=cache_metrics,
            slow_queries=slow_queries,
            response_times=response_times,
            recommendations=recommendations,
        )

    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get performance metrics",
        )


@router.get("/slow-queries", response_model=List[QueryPerformanceResult])
async def get_slow_queries(
    current_user: User = Depends(get_current_active_user),
    tenant_id: Optional[int] = Query(None, description="Filter by tenant ID"),
    threshold_ms: float = Query(
        500.0, description="Slow query threshold in milliseconds"
    ),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of results"),
    conn: Connection = Depends(get_db_session),
) -> List[QueryPerformanceResult]:
    """
    Get slow query analysis
    """
    try:
        # In a production environment, this would query pg_stat_statements
        # For now, we'll provide a mock implementation showing the structure

        slow_queries = []

        # Mock slow query data - in production this would come from pg_stat_statements
        mock_queries = [
            {
                "query_type": "transaction_list_filtered",
                "avg_execution_time_ms": 750.0,
                "max_execution_time_ms": 1200.0,
                "query_count": 45,
                "tenant_id": tenant_id,
            },
            {
                "query_type": "dashboard_metrics_complex",
                "avg_execution_time_ms": 650.0,
                "max_execution_time_ms": 980.0,
                "query_count": 23,
                "tenant_id": tenant_id,
            },
            {
                "query_type": "category_breakdown_monthly",
                "avg_execution_time_ms": 520.0,
                "max_execution_time_ms": 700.0,
                "query_count": 12,
                "tenant_id": tenant_id,
            },
        ]

        for query_data in mock_queries[:limit]:
            if query_data["avg_execution_time_ms"] >= threshold_ms:
                slow_queries.append(QueryPerformanceResult(**query_data))

        logger.info(
            f"Found {len(slow_queries)} slow queries above {threshold_ms}ms threshold"
        )

        return slow_queries

    except Exception as e:
        logger.error(f"Error getting slow queries: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get slow queries",
        )


@router.post("/optimize")
async def trigger_performance_optimization(
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
) -> Dict[str, Any]:
    """
    Trigger performance optimizations
    """
    try:
        start_time = time.time()
        optimizations_applied = []

        # Update database statistics
        try:
            await conn.execute("SELECT update_transaction_statistics()")
            optimizations_applied.append("Updated database statistics")
        except Exception as e:
            logger.warning(f"Failed to update database statistics: {e}")

        # Clear cache for fresh data
        await performance_cache.invalidate_by_tags(
            ["dashboard", "transactions"], tenant_id
        )
        optimizations_applied.append("Cleared tenant cache")

        # Pre-warm cache with common queries (would be implemented based on usage patterns)
        await performance_cache.warm_cache_for_tenant(tenant_id)
        optimizations_applied.append("Pre-warmed cache")

        optimization_time = (time.time() - start_time) * 1000

        result = {
            "success": True,
            "optimizations_applied": optimizations_applied,
            "execution_time_ms": round(optimization_time, 2),
            "tenant_id": tenant_id,
            "timestamp": datetime.utcnow().isoformat(),
        }

        logger.info(
            f"Performance optimization completed for tenant {tenant_id} in {optimization_time:.2f}ms"
        )

        return result

    except Exception as e:
        logger.error(f"Error in performance optimization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to optimize performance",
        )


# Helper functions


async def _get_recent_slow_queries(
    conn: Connection, hours_back: int = 1
) -> List[Dict[str, Any]]:
    """Get recent slow queries"""
    # Mock implementation - in production would query pg_stat_statements
    return [
        {
            "query": "Complex transaction filtering query",
            "avg_time_ms": 750,
            "call_count": 12,
            "first_seen": (datetime.utcnow() - timedelta(minutes=30)).isoformat(),
        }
    ]


async def _get_connection_pool_stats(conn: Connection) -> Dict[str, Any]:
    """Get database connection pool statistics"""
    # Mock implementation - would get real pool stats in production
    return {
        "active_connections": 5,
        "idle_connections": 3,
        "total_connections": 8,
        "max_connections": 20,
        "pool_utilization_pct": 40.0,
    }


def _calculate_health_score(
    db_response_time: float, cache_healthy: bool, slow_query_count: int
) -> int:
    """Calculate overall API health score (0-100)"""
    score = 100

    # Database performance impact
    if db_response_time > 100:
        score -= 30
    elif db_response_time > 50:
        score -= 15

    # Cache health impact
    if not cache_healthy:
        score -= 20

    # Slow query impact
    score -= min(slow_query_count * 5, 25)

    return max(0, score)


def _generate_performance_recommendations(
    db_response_time: float,
    cache_health: Dict[str, Any],
    slow_queries: List[Dict[str, Any]],
) -> List[str]:
    """Generate performance recommendations"""
    recommendations = []

    if db_response_time > 50:
        recommendations.append(
            "Consider adding database indexes for frequently queried columns"
        )

    if cache_health.get("status") != "healthy":
        recommendations.append("Enable Redis caching to improve response times")

    if len(slow_queries) > 0:
        recommendations.append("Optimize slow queries identified in monitoring")

    if not recommendations:
        recommendations.append("API performance is optimal")

    return recommendations


async def _analyze_database_performance(
    conn: Connection, tenant_id: int, hours_back: int
) -> Dict[str, Any]:
    """Analyze database performance metrics"""
    try:
        # Get table statistics
        stats_query = """
            SELECT 
                seq_scan,
                seq_tup_read,
                idx_scan,
                idx_tup_fetch,
                n_tup_ins,
                n_tup_upd,
                n_tup_del
            FROM pg_stat_user_tables 
            WHERE relname = 'transactions'
        """

        stats = await conn.fetchrow(stats_query)

        if stats:
            index_usage = (
                stats["idx_scan"] / max(stats["seq_scan"] + stats["idx_scan"], 1)
            ) * 100

            return {
                "index_usage_pct": round(index_usage, 2),
                "sequential_scans": stats["seq_scan"],
                "index_scans": stats["idx_scan"],
                "rows_inserted": stats["n_tup_ins"],
                "rows_updated": stats["n_tup_upd"],
                "rows_deleted": stats["n_tup_del"],
                "performance_grade": "A"
                if index_usage > 90
                else "B"
                if index_usage > 70
                else "C",
            }
        else:
            return {"status": "no_data", "message": "No statistics available"}

    except Exception as e:
        logger.error(f"Error analyzing database performance: {e}")
        return {"status": "error", "message": str(e)}


async def _analyze_response_times(
    conn: Connection, tenant_id: int, hours_back: int
) -> Dict[str, float]:
    """Analyze API response times"""
    # Mock implementation - would integrate with actual monitoring in production
    return {
        "dashboard_avg_ms": 150.0,
        "transactions_list_avg_ms": 85.0,
        "transaction_stats_avg_ms": 120.0,
        "file_upload_avg_ms": 2500.0,
        "api_target_ms": 500.0,
    }


def _generate_detailed_recommendations(
    db_performance: Dict[str, Any],
    cache_metrics: Dict[str, Any],
    slow_queries: List[Dict[str, Any]],
    response_times: Dict[str, float],
) -> List[str]:
    """Generate detailed performance recommendations"""
    recommendations = []

    # Database recommendations
    index_usage = db_performance.get("index_usage_pct", 0)
    if index_usage < 70:
        recommendations.append(
            f"Database index usage is low ({index_usage}%). Add indexes for frequent queries."
        )

    # Cache recommendations
    hit_rate = cache_metrics.get("hit_rate", 0)
    if hit_rate < 60:
        recommendations.append(
            f"Cache hit rate is low ({hit_rate}%). Review caching strategy."
        )

    # Response time recommendations
    for endpoint, avg_time in response_times.items():
        if avg_time > 500 and "_avg_ms" in endpoint:
            endpoint_name = endpoint.replace("_avg_ms", "")
            recommendations.append(
                f"{endpoint_name} endpoint averaging {avg_time}ms. Optimize queries and add caching."
            )

    # Slow query recommendations
    if len(slow_queries) > 5:
        recommendations.append(
            f"High number of slow queries ({len(slow_queries)}). Review query optimization."
        )

    if not recommendations:
        recommendations.append("All performance metrics are within optimal ranges.")

    return recommendations
