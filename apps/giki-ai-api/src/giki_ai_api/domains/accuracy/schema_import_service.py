"""
Category schema import service for accuracy testing scenario 2.

Handles importing category hierarchies from various formats (Excel, CSV, JSON, YAML)
for use in schema-only categorization testing where no historical data is available.
"""

import base64
import json
import logging
import re
from io import BytesIO, String<PERSON>
from typing import Any, Dict, List, Optional

import pandas as pd
import yaml
from openpyxl import load_workbook

from ...shared.exceptions import ServiceError

logger = logging.getLogger(__name__)


class SchemaImportError(ServiceError):
    """Schema import service error."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            service_name="CategorySchemaImportService",
            operation="schema_import",
            **kwargs,
        )


class CategorySchemaImportService:
    """Service for importing category schemas from various file formats."""

    def __init__(self):
        self.supported_formats = ["excel", "csv", "json", "yaml"]
        self.gl_code_counter = 1000  # Starting GL code for auto-generation

    async def import_schema_from_content(
        self,
        content: str,
        file_format: str,
        name: str,
        description: Optional[str] = None,
        detect_hierarchy: bool = True,
        generate_gl_codes: bool = True,
        source_filename: str = "uploaded_file",
    ) -> Dict[str, Any]:
        """
        Import category schema from file content.

        Args:
            content: File content (base64 encoded for binary files, raw for text)
            file_format: Format type (excel, csv, json, yaml)
            name: Schema name
            description: Optional description
            detect_hierarchy: Auto-detect hierarchy from flat lists
            generate_gl_codes: Auto-generate GL codes
            source_filename: Original filename

        Returns:
            Dictionary with parsed schema data and metadata
        """
        try:
            if file_format not in self.supported_formats:
                raise SchemaImportError(f"Unsupported format: {file_format}")

            # Parse content based on format
            if file_format == "excel":
                categories = await self._parse_excel_content(content)
            elif file_format == "csv":
                categories = await self._parse_csv_content(content)
            elif file_format == "json":
                categories = await self._parse_json_content(content)
            elif file_format == "yaml":
                categories = await self._parse_yaml_content(content)
            else:
                raise SchemaImportError(
                    f"Parser not implemented for format: {file_format}"
                )

            # Process and enhance categories
            if detect_hierarchy:
                categories = self._detect_and_enhance_hierarchy(categories)

            if generate_gl_codes:
                categories = self._generate_gl_codes(categories)

            # Calculate metadata
            category_count = len(categories)
            max_hierarchy_depth = self._calculate_max_depth(categories)
            has_gl_codes = any(cat.get("gl_code") for cat in categories)

            # Build schema data structure
            schema_data = {
                "categories": categories,
                "metadata": {
                    "source_format": file_format,
                    "source_filename": source_filename,
                    "hierarchy_detected": detect_hierarchy,
                    "gl_codes_generated": generate_gl_codes,
                    "import_timestamp": pd.Timestamp.now().isoformat(),
                },
                "gl_codes": {
                    cat["name"]: cat.get("gl_code")
                    for cat in categories
                    if cat.get("gl_code")
                },
                "hierarchy_map": self._build_hierarchy_map(categories),
                "validation": self._validate_schema(categories),
            }

            result = {
                "name": name,
                "description": description,
                "schema_format": file_format,
                "schema_data": schema_data,
                "category_count": category_count,
                "max_hierarchy_depth": max_hierarchy_depth,
                "has_gl_codes": has_gl_codes,
                "gl_code_mapping": schema_data["gl_codes"],
                "imported_from": source_filename,
                "validation_warnings": schema_data["validation"]["warnings"],
                "import_stats": {
                    "categories_imported": category_count,
                    "hierarchy_levels_detected": max_hierarchy_depth,
                    "gl_codes_generated": len(
                        [
                            cat
                            for cat in categories
                            if cat.get("gl_code_generated", False)
                        ]
                    ),
                },
            }

            logger.info(
                f"Successfully imported schema '{name}' with {category_count} categories"
            )
            return result

        except SchemaImportError:
            raise
        except Exception as e:
            logger.error(f"Failed to import schema: {e}")
            raise SchemaImportError(f"Failed to import schema: {e}")

    async def _parse_excel_content(self, content: str) -> List[Dict[str, Any]]:
        """Parse Excel file content and extract categories."""
        try:
            # Decode base64 content
            file_bytes = base64.b64decode(content)
            workbook = load_workbook(BytesIO(file_bytes), read_only=True)

            # Try to find the best sheet with category data
            sheet = None
            for sheet_name in workbook.sheetnames:
                ws = workbook[sheet_name]
                if ws.max_row > 1:  # Has data beyond header
                    sheet = ws
                    break

            if not sheet:
                raise SchemaImportError("No data sheets found in Excel file")

            # Convert to pandas DataFrame for easier processing
            data = []
            headers = []

            # Get headers from first row
            for cell in sheet[1]:
                headers.append(cell.value or f"Column_{len(headers) + 1}")

            # Get data rows
            for row in sheet.iter_rows(min_row=2, values_only=True):
                if any(cell is not None for cell in row):  # Skip empty rows
                    row_data = {headers[i]: cell for i, cell in enumerate(row)}
                    data.append(row_data)

            return self._extract_categories_from_data(data)

        except Exception as e:
            logger.error(f"Failed to parse Excel content: {e}")
            raise SchemaImportError(f"Failed to parse Excel content: {e}")

    async def _parse_csv_content(self, content: str) -> List[Dict[str, Any]]:
        """Parse CSV file content and extract categories."""
        try:
            # Decode if base64
            if self._is_base64(content):
                content = base64.b64decode(content).decode("utf-8")

            # Parse CSV
            df = pd.read_csv(StringIO(content))
            data = df.to_dict("records")

            return self._extract_categories_from_data(data)

        except Exception as e:
            logger.error(f"Failed to parse CSV content: {e}")
            raise SchemaImportError(f"Failed to parse CSV content: {e}")

    async def _parse_json_content(self, content: str) -> List[Dict[str, Any]]:
        """Parse JSON file content and extract categories."""
        try:
            # Decode if base64
            if self._is_base64(content):
                content = base64.b64decode(content).decode("utf-8")

            data = json.loads(content)

            # Handle different JSON structures
            if isinstance(data, list):
                # Array of categories
                return self._extract_categories_from_data(data)
            elif isinstance(data, dict):
                if "categories" in data:
                    return self._extract_categories_from_data(data["categories"])
                else:
                    # Single category object or flat structure
                    return self._extract_categories_from_data([data])
            else:
                raise SchemaImportError("Invalid JSON structure for category data")

        except json.JSONDecodeError as e:
            raise SchemaImportError(f"Invalid JSON format: {e}")
        except Exception as e:
            logger.error(f"Failed to parse JSON content: {e}")
            raise SchemaImportError(f"Failed to parse JSON content: {e}")

    async def _parse_yaml_content(self, content: str) -> List[Dict[str, Any]]:
        """Parse YAML file content and extract categories."""
        try:
            # Decode if base64
            if self._is_base64(content):
                content = base64.b64decode(content).decode("utf-8")

            data = yaml.safe_load(content)

            # Handle different YAML structures (similar to JSON)
            if isinstance(data, list):
                return self._extract_categories_from_data(data)
            elif isinstance(data, dict):
                if "categories" in data:
                    return self._extract_categories_from_data(data["categories"])
                else:
                    return self._extract_categories_from_data([data])
            else:
                raise SchemaImportError("Invalid YAML structure for category data")

        except yaml.YAMLError as e:
            raise SchemaImportError(f"Invalid YAML format: {e}")
        except Exception as e:
            logger.error(f"Failed to parse YAML content: {e}")
            raise SchemaImportError(f"Failed to parse YAML content: {e}")

    def _extract_categories_from_data(
        self, data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Extract and normalize category data from parsed content."""
        categories = []

        for item in data:
            if not isinstance(item, dict):
                continue

            # Try to find category name in various possible fields
            category_name = None
            for field in ["category", "name", "category_name", "title", "label"]:
                if field in item and item[field]:
                    category_name = str(item[field]).strip()
                    break

            if not category_name:
                continue  # Skip items without identifiable category name

            # Extract additional fields
            category = {
                "name": category_name,
                "description": item.get("description", ""),
                "parent": item.get("parent", ""),
                "level": item.get("level", 0),
                "gl_code": item.get("gl_code", ""),
                "account_type": item.get("account_type", ""),
                "is_active": item.get("is_active", True),
                "metadata": {
                    k: v
                    for k, v in item.items()
                    if k not in ["category", "name", "category_name", "title", "label"]
                },
            }

            categories.append(category)

        logger.info(f"Extracted {len(categories)} categories from data")
        return categories

    def _detect_and_enhance_hierarchy(
        self, categories: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Detect and enhance hierarchical relationships in categories."""
        try:
            enhanced_categories = []

            for category in categories:
                name = category["name"]

                # Detect hierarchy from naming patterns
                if " > " in name:
                    # Hierarchical naming (e.g., "Parent > Child > Grandchild")
                    parts = [part.strip() for part in name.split(" > ")]
                    category["hierarchy_parts"] = parts
                    category["level"] = len(parts)
                    category["parent"] = (
                        " > ".join(parts[:-1]) if len(parts) > 1 else ""
                    )
                    category["leaf_name"] = parts[-1]
                elif "/" in name:
                    # Path-like hierarchy
                    parts = [part.strip() for part in name.split("/")]
                    category["hierarchy_parts"] = parts
                    category["level"] = len(parts)
                    category["parent"] = "/".join(parts[:-1]) if len(parts) > 1 else ""
                    category["leaf_name"] = parts[-1]
                elif "." in name and name.count(".") <= 3:
                    # Numeric hierarchy (e.g., "1.1.2 Category Name")
                    match = re.match(r"^([\d.]+)\s+(.+)$", name)
                    if match:
                        number_part, text_part = match.groups()
                        level = number_part.count(".") + 1
                        category["hierarchy_parts"] = number_part.split(".") + [
                            text_part
                        ]
                        category["level"] = level
                        category["leaf_name"] = text_part
                else:
                    # Flat category
                    category["hierarchy_parts"] = [name]
                    category["level"] = 1
                    category["leaf_name"] = name

                enhanced_categories.append(category)

            logger.info(
                f"Enhanced {len(enhanced_categories)} categories with hierarchy detection"
            )
            return enhanced_categories

        except Exception as e:
            logger.warning(f"Failed to detect hierarchy, using flat structure: {e}")
            return categories

    def _generate_gl_codes(
        self, categories: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate GL codes for categories that don't have them."""
        try:
            categories_with_gl = []

            for category in categories:
                if not category.get("gl_code"):
                    # Generate GL code based on hierarchy and position
                    level = category.get("level", 1)

                    if level == 1:
                        # Top-level: 1000, 2000, 3000, etc.
                        gl_code = str(self.gl_code_counter)
                        self.gl_code_counter += 1000
                    elif level == 2:
                        # Second level: 1100, 1200, 1300, etc.
                        base = (self.gl_code_counter - 1000) + 100
                        gl_code = str(base)
                    else:
                        # Third level and below: 1110, 1120, 1130, etc.
                        base = (self.gl_code_counter - 1000) + (level - 1) * 10
                        gl_code = str(base)

                    category["gl_code"] = gl_code
                    category["gl_code_generated"] = True
                else:
                    category["gl_code_generated"] = False

                categories_with_gl.append(category)

            logger.info(
                f"Generated GL codes for {len([c for c in categories_with_gl if c.get('gl_code_generated')])} categories"
            )
            return categories_with_gl

        except Exception as e:
            logger.warning(f"Failed to generate GL codes: {e}")
            return categories

    def _calculate_max_depth(self, categories: List[Dict[str, Any]]) -> int:
        """Calculate maximum hierarchy depth."""
        try:
            max_depth = 1
            for category in categories:
                level = category.get("level", 1)
                max_depth = max(max_depth, level)
            return max_depth
        except Exception:
            return 1

    def _build_hierarchy_map(self, categories: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build hierarchical map of categories."""
        try:
            hierarchy_map = {}

            for category in categories:
                name = category["name"]
                parent = category.get("parent", "")
                level = category.get("level", 1)

                if level == 1:
                    # Top-level category
                    if name not in hierarchy_map:
                        hierarchy_map[name] = {"children": {}, "metadata": category}
                else:
                    # Child category - find parent and add
                    if parent and parent in hierarchy_map:
                        hierarchy_map[parent]["children"][name] = {"metadata": category}

            return hierarchy_map

        except Exception as e:
            logger.warning(f"Failed to build hierarchy map: {e}")
            return {}

    def _validate_schema(self, categories: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate imported schema and return validation results."""
        validation = {
            "is_valid": True,
            "warnings": [],
            "errors": [],
            "stats": {
                "total_categories": len(categories),
                "categories_with_gl_codes": 0,
                "categories_with_descriptions": 0,
                "max_hierarchy_depth": 1,
            },
        }

        try:
            category_names = set()
            gl_codes = set()

            for i, category in enumerate(categories):
                name = category.get("name", "")
                gl_code = category.get("gl_code", "")
                description = category.get("description", "")

                # Check for required fields
                if not name:
                    validation["errors"].append(f"Category {i + 1} missing name")
                    validation["is_valid"] = False

                # Check for duplicates
                if name in category_names:
                    validation["warnings"].append(f"Duplicate category name: {name}")
                else:
                    category_names.add(name)

                if gl_code:
                    validation["stats"]["categories_with_gl_codes"] += 1
                    if gl_code in gl_codes:
                        validation["warnings"].append(f"Duplicate GL code: {gl_code}")
                    else:
                        gl_codes.add(gl_code)

                if description:
                    validation["stats"]["categories_with_descriptions"] += 1

                # Update max depth
                level = category.get("level", 1)
                validation["stats"]["max_hierarchy_depth"] = max(
                    validation["stats"]["max_hierarchy_depth"], level
                )

            # Additional validations
            if len(categories) == 0:
                validation["errors"].append("No categories found in import data")
                validation["is_valid"] = False

            if validation["stats"]["categories_with_gl_codes"] == 0:
                validation["warnings"].append(
                    "No GL codes found - they will be auto-generated"
                )

            logger.info(
                f"Schema validation completed: {len(validation['errors'])} errors, {len(validation['warnings'])} warnings"
            )

        except Exception as e:
            logger.error(f"Schema validation failed: {e}")
            validation["errors"].append(f"Validation failed: {e}")
            validation["is_valid"] = False

        return validation

    def _is_base64(self, content: str) -> bool:
        """Check if content is base64 encoded."""
        try:
            if isinstance(content, str):
                # Check if it looks like base64
                if len(content) % 4 == 0 and re.match(
                    r"^[A-Za-z0-9+/]*={0,2}$", content
                ):
                    # Try to decode
                    base64.b64decode(content)
                    return True
            return False
        except Exception:
            return False
