"""
Temporal Accuracy Service for Historical Data Validation

This service implements temporal accuracy measurement:
- Training/testing data split for time-based validation
- Progressive monthly accuracy validation
- Improvement-over-original evaluation
- Temporal pattern recognition
"""

import logging
from typing import Any, Dict, List

from ...core.database import get_database_connection

# Removed primitive M2 file processor - using proper file upload system instead
from ..categories.categorization_agent import CategorizationAgent
from .ai_judge_agent import AIJudgeAgent

logger = logging.getLogger(__name__)


class TemporalAccuracyService:
    """Service for M2 temporal accuracy measurement and validation"""

    def __init__(self):
        self.ai_judge = AIJudgeAgent()
        self.categorization_agent = CategorizationAgent()

        # Temporal validation configuration
        self.training_months = [
            "2024-01",
            "2024-02",
            "2024-03",
            "2024-04",
            "2024-05",
            "2024-06",
        ]
        self.testing_months = [
            "2024-07",
            "2024-08",
            "2024-09",
            "2024-10",
            "2024-11",
            "2024-12",
        ]
        self.batch_size = 75  # Optimal batch size for evaluation
        self.improvement_target = 85.0  # >85% improvement-over-original required

    async def load_historical_data(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        Load historical data should use proper file upload endpoints
        This method now returns instructions to use the proper API workflow
        """
        return {
            "error": "Use proper file upload endpoints instead",
            "instruction": "Upload files via /api/v1/files/upload with proper schema interpretation",
            "file_paths": file_paths,
        }

    async def create_temporal_data_split(self, tenant_id: int) -> Dict[str, Any]:
        """
        Create training/testing split for temporal accuracy validation

        Args:
            tenant_id: Target tenant ID

        Returns:
            Split configuration and statistics
        """
        try:
            async with get_database_connection() as conn:
                # Get training data (Jan-Jun 2024)
                training_query = """
                    SELECT COUNT(*) as count,
                           MIN(date) as earliest_date,
                           MAX(date) as latest_date
                    FROM transactions 
                    WHERE tenant_id = $1 
                    AND date >= '2024-01-01' 
                    AND date <= '2024-06-30'
                    AND original_category_label IS NOT NULL
                """
                training_stats = await conn.fetchrow(training_query, tenant_id)

                # Get testing data (Jul-Dec 2024)
                testing_query = """
                    SELECT COUNT(*) as count,
                           MIN(date) as earliest_date,
                           MAX(date) as latest_date
                    FROM transactions 
                    WHERE tenant_id = $1 
                    AND date >= '2024-07-01' 
                    AND date <= '2024-12-31'
                    AND original_category_label IS NOT NULL
                """
                testing_stats = await conn.fetchrow(testing_query, tenant_id)

                split_config = {
                    "tenant_id": tenant_id,
                    "training_period": {
                        "start_date": "2024-01-01",
                        "end_date": "2024-06-30",
                        "transaction_count": training_stats["count"],
                        "date_range": {
                            "earliest": training_stats["earliest_date"],
                            "latest": training_stats["latest_date"],
                        },
                    },
                    "testing_period": {
                        "start_date": "2024-07-01",
                        "end_date": "2024-12-31",
                        "transaction_count": testing_stats["count"],
                        "date_range": {
                            "earliest": testing_stats["earliest_date"],
                            "latest": testing_stats["latest_date"],
                        },
                    },
                    "split_ratio": f"{training_stats['count']}:{testing_stats['count']}",
                    "total_transactions": training_stats["count"]
                    + testing_stats["count"],
                }

                logger.info(f"Temporal data split created: {split_config}")
                return split_config

        except Exception as e:
            logger.error(f"Error creating temporal data split: {e}")
            raise

    async def run_progressive_monthly_validation(
        self, tenant_id: int
    ) -> Dict[str, Any]:
        """
        Run progressive month-by-month accuracy validation

        This simulates the customer feedback loop where AI learns from corrections:
        - Month 1: Train on Jan-Jun, test on Jul
        - Month 2: Train on Jan-Jul, test on Aug
        - Continue through December

        Args:
            tenant_id: Target tenant ID

        Returns:
            Progressive accuracy results by month
        """
        try:
            validation_results = {
                "tenant_id": tenant_id,
                "validation_type": "progressive_monthly",
                "target_accuracy": self.improvement_target,
                "monthly_results": {},
                "overall_statistics": {},
                "temporal_trends": [],
            }

            # Progressive monthly testing
            test_months = [
                ("2024-07", "July"),
                ("2024-08", "August"),
                ("2024-09", "September"),
                ("2024-10", "October"),
                ("2024-11", "November"),
                ("2024-12", "December"),
            ]

            for i, (test_month, month_name) in enumerate(test_months):
                logger.info(f"Running validation for {month_name} ({test_month})")

                # Define training period (expands each month)
                training_end_month = f"2024-{6 + i:02d}"  # Jun, Jul, Aug, etc.

                monthly_result = await self._validate_single_month(
                    tenant_id=tenant_id,
                    training_start="2024-01-01",
                    training_end=f"{training_end_month}-31",
                    test_month=test_month,
                    month_name=month_name,
                )

                validation_results["monthly_results"][test_month] = monthly_result

                # Track temporal trends
                validation_results["temporal_trends"].append(
                    {
                        "month": test_month,
                        "month_name": month_name,
                        "accuracy_score": monthly_result["improvement_percentage"],
                        "training_period_months": i + 6,  # 6 base months + progressive
                        "test_transactions": monthly_result["test_transaction_count"],
                    }
                )

            # Calculate overall statistics
            validation_results[
                "overall_statistics"
            ] = await self._calculate_temporal_statistics(
                validation_results["monthly_results"]
            )

            logger.info(
                f"Progressive monthly validation complete: {validation_results['overall_statistics']}"
            )
            return validation_results

        except Exception as e:
            logger.error(f"Error in progressive monthly validation: {e}")
            raise

    async def _validate_single_month(
        self,
        tenant_id: int,
        training_start: str,
        training_end: str,
        test_month: str,
        month_name: str,
    ) -> Dict[str, Any]:
        """Validate accuracy for a single month using expanded training data"""
        try:
            async with get_database_connection() as conn:
                # Get test transactions for the month
                test_query = """
                    SELECT id, description, amount, original_category_label, vendor_name, account
                    FROM transactions 
                    WHERE tenant_id = $1 
                    AND date >= $2::date 
                    AND date < ($2::date + INTERVAL '1 month')
                    AND original_category_label IS NOT NULL
                    ORDER BY date, id
                """
                test_month_start = f"{test_month}-01"
                test_transactions = await conn.fetch(
                    test_query, tenant_id, test_month_start
                )

                if not test_transactions:
                    logger.warning(f"No test transactions found for {month_name}")
                    return {
                        "month": test_month,
                        "month_name": month_name,
                        "test_transaction_count": 0,
                        "improvement_percentage": 0.0,
                        "evaluation_results": [],
                    }

                # Process in batches for careful evaluation
                evaluation_results = []
                batch_number = 0

                for i in range(0, len(test_transactions), self.batch_size):
                    batch = test_transactions[i : i + self.batch_size]
                    batch_number += 1

                    logger.info(
                        f"Processing {month_name} batch {batch_number}: {len(batch)} transactions"
                    )

                    batch_results = await self._evaluate_improvement_batch(batch)
                    evaluation_results.extend(batch_results)

                # Calculate improvement percentage
                improvement_count = sum(
                    1
                    for result in evaluation_results
                    if result["judgment"] in ["better", "equivalent"]
                )
                improvement_percentage = (
                    improvement_count / len(evaluation_results)
                ) * 100

                month_result = {
                    "month": test_month,
                    "month_name": month_name,
                    "training_period": f"{training_start} to {training_end}",
                    "test_transaction_count": len(test_transactions),
                    "evaluation_results": evaluation_results,
                    "improvement_count": improvement_count,
                    "improvement_percentage": improvement_percentage,
                    "meets_target": improvement_percentage >= self.improvement_target,
                    "batch_count": batch_number,
                }

                logger.info(
                    f"{month_name} validation: {improvement_percentage:.1f}% improvement"
                )
                return month_result

        except Exception as e:
            logger.error(f"Error validating month {month_name}: {e}")
            raise

    async def _evaluate_improvement_batch(
        self, transactions: List[dict]
    ) -> List[Dict[str, Any]]:
        """Evaluate improvement-over-original for a batch of transactions"""
        try:
            batch_results = []

            for transaction in transactions:
                # Get AI categorization
                ai_category = await self.categorization_agent.categorize_transaction(
                    description=transaction["description"],
                    amount=float(transaction["amount"]),
                    vendor_name=transaction["vendor_name"],
                    account=transaction["account"],
                )

                # Compare AI vs original using AI judge
                evaluation = await self.ai_judge.evaluate_improvement_over_original(
                    transaction_description=transaction["description"],
                    transaction_amount=float(transaction["amount"]),
                    original_category=transaction["original_category_label"],
                    ai_category=ai_category["category"],
                    evaluation_criteria="improvement_over_original",
                )

                batch_results.append(
                    {
                        "transaction_id": transaction["id"],
                        "description": transaction["description"],
                        "amount": float(transaction["amount"]),
                        "original_category": transaction["original_category_label"],
                        "ai_category": ai_category["category"],
                        "ai_confidence": ai_category.get("confidence", 0.0),
                        "judgment": evaluation["judgment"],  # better, equivalent, worse
                        "reasoning": evaluation["reasoning"],
                        "judge_confidence": evaluation.get("confidence", 0.0),
                    }
                )

            return batch_results

        except Exception as e:
            logger.error(f"Error evaluating improvement batch: {e}")
            raise

    async def _calculate_temporal_statistics(
        self, monthly_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate overall temporal accuracy statistics"""
        try:
            if not monthly_results:
                return {}

            # Extract accuracy scores
            accuracy_scores = [
                result["improvement_percentage"]
                for result in monthly_results.values()
                if result["test_transaction_count"] > 0
            ]

            if not accuracy_scores:
                return {"error": "No valid monthly results for statistics"}

            # Calculate statistics
            overall_stats = {
                "months_tested": len(accuracy_scores),
                "average_accuracy": sum(accuracy_scores) / len(accuracy_scores),
                "minimum_accuracy": min(accuracy_scores),
                "maximum_accuracy": max(accuracy_scores),
                "months_meeting_target": sum(
                    1 for score in accuracy_scores if score >= self.improvement_target
                ),
                "temporal_consistency": self._calculate_consistency(accuracy_scores),
                "improvement_trend": self._calculate_trend(accuracy_scores),
                "total_transactions_tested": sum(
                    result["test_transaction_count"]
                    for result in monthly_results.values()
                ),
                "overall_meets_target": (sum(accuracy_scores) / len(accuracy_scores))
                >= self.improvement_target,
            }

            return overall_stats

        except Exception as e:
            logger.error(f"Error calculating temporal statistics: {e}")
            return {"error": str(e)}

    def _calculate_consistency(self, accuracy_scores: List[float]) -> float:
        """Calculate temporal consistency (low variance = high consistency)"""
        if len(accuracy_scores) < 2:
            return 100.0

        mean_score = sum(accuracy_scores) / len(accuracy_scores)
        variance = sum((score - mean_score) ** 2 for score in accuracy_scores) / len(
            accuracy_scores
        )

        # Convert to consistency percentage (lower variance = higher consistency)
        consistency = max(0, 100 - variance)
        return consistency

    def _calculate_trend(self, accuracy_scores: List[float]) -> str:
        """Calculate improvement trend across months"""
        if len(accuracy_scores) < 2:
            return "insufficient_data"

        # Simple trend analysis
        first_half = accuracy_scores[: len(accuracy_scores) // 2]
        second_half = accuracy_scores[len(accuracy_scores) // 2 :]

        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)

        if second_avg > first_avg + 1.0:
            return "improving"
        elif second_avg < first_avg - 1.0:
            return "declining"
        else:
            return "stable"

    async def get_temporal_validation_status(self, tenant_id: int) -> Dict[str, Any]:
        """Get current status of temporal validation for specified tenant"""
        try:
            async with get_database_connection() as conn:
                # Check data availability
                data_query = """
                    SELECT 
                        COUNT(*) as total_transactions,
                        COUNT(CASE WHEN original_category_label IS NOT NULL THEN 1 END) as with_original_categories,
                        MIN(date) as earliest_date,
                        MAX(date) as latest_date,
                        COUNT(CASE WHEN date >= '2024-01-01' AND date <= '2024-06-30' THEN 1 END) as training_period_count,
                        COUNT(CASE WHEN date >= '2024-07-01' AND date <= '2024-12-31' THEN 1 END) as testing_period_count
                    FROM transactions 
                    WHERE tenant_id = $1
                """
                data_stats = await conn.fetchrow(data_query, tenant_id)

                status = {
                    "tenant_id": tenant_id,
                    "data_loaded": data_stats["total_transactions"] > 0,
                    "has_original_categories": data_stats["with_original_categories"]
                    > 0,
                    "data_statistics": {
                        "total_transactions": data_stats["total_transactions"],
                        "with_original_categories": data_stats[
                            "with_original_categories"
                        ],
                        "date_range": {
                            "earliest": data_stats["earliest_date"],
                            "latest": data_stats["latest_date"],
                        },
                        "training_period_transactions": data_stats[
                            "training_period_count"
                        ],
                        "testing_period_transactions": data_stats[
                            "testing_period_count"
                        ],
                    },
                    "ready_for_validation": (
                        data_stats["with_original_categories"] > 0
                        and data_stats["training_period_count"] > 0
                        and data_stats["testing_period_count"] > 0
                    ),
                    "validation_config": {
                        "batch_size": self.batch_size,
                        "improvement_target": self.improvement_target,
                        "training_months": self.training_months,
                        "testing_months": self.testing_months,
                    },
                }

                return status

        except Exception as e:
            logger.error(f"Error getting temporal validation status: {e}")
            raise
