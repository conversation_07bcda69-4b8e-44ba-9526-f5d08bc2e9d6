"""
Accuracy measurement repository for database operations.

Handles all database interactions for accuracy tests, metrics, AI judgments,
and category schemas with proper tenant isolation.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from asyncpg import Connection

from ...shared.exceptions import ServiceError
from .models import (
    AccuracyMetric,
    AccuracyTest,
    AccuracyTestScenario,
    AccuracyTestStatus,
    AccuracyTestSummary,
    AIJudgment,
    AIJudgmentResult,
    CategorySchema,
)

logger = logging.getLogger(__name__)


class AccuracyRepositoryError(ServiceError):
    """Accuracy repository-specific service error."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            service_name="AccuracyRepository",
            operation="database_operation",
            **kwargs,
        )


class AccuracyRepository:
    """Repository for accuracy measurement database operations."""

    def __init__(self, connection: Connection):
        self.connection = connection

    # ==================== Accuracy Tests ====================

    async def create_accuracy_test(self, test_data: Dict[str, Any]) -> int:
        """Create a new accuracy test and return its ID."""
        try:
            query = """
                INSERT INTO accuracy_tests (
                    tenant_id, name, description, scenario, test_data_source,
                    category_schema_id, sample_size, status, created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING id
            """

            result = await self.connection.fetchval(
                query,
                test_data["tenant_id"],
                test_data["name"],
                test_data.get("description"),
                test_data["scenario"],
                test_data["test_data_source"],
                test_data.get("category_schema_id"),
                test_data.get("sample_size", 100),
                AccuracyTestStatus.PENDING.value,
                datetime.utcnow(),
            )

            logger.info(
                f"Created accuracy test {result} for tenant {test_data['tenant_id']}"
            )
            return result

        except Exception as e:
            logger.error(f"Failed to create accuracy test: {e}")
            raise AccuracyRepositoryError(f"Failed to create accuracy test: {e}")

    async def get_accuracy_test(
        self, test_id: int, tenant_id: int
    ) -> Optional[AccuracyTest]:
        """Get accuracy test by ID with tenant isolation."""
        try:
            query = """
                SELECT id, tenant_id, name, description, scenario, test_data_source,
                       category_schema_id, sample_size, status, created_at, started_at,
                       completed_at, total_transactions, successful_categorizations,
                       ai_judge_correct, ai_judge_incorrect, ai_judge_partially_correct,
                       precision, recall, f1_score, accuracy_percentage, error_message,
                       error_details
                FROM accuracy_tests 
                WHERE id = $1 AND tenant_id = $2
            """

            row = await self.connection.fetchrow(query, test_id, tenant_id)
            if not row:
                return None

            return AccuracyTest(**dict(row))

        except Exception as e:
            logger.error(f"Failed to get accuracy test {test_id}: {e}")
            raise AccuracyRepositoryError(f"Failed to get accuracy test: {e}")

    async def list_accuracy_tests(
        self,
        tenant_id: int,
        limit: int = 50,
        offset: int = 0,
        scenario: Optional[AccuracyTestScenario] = None,
        status: Optional[AccuracyTestStatus] = None,
    ) -> List[AccuracyTestSummary]:
        """List accuracy tests with optional filtering."""
        try:
            conditions = ["tenant_id = $1"]
            params = [tenant_id]
            param_count = 1

            if scenario:
                param_count += 1
                conditions.append(f"scenario = ${param_count}")
                params.append(scenario.value)

            if status:
                param_count += 1
                conditions.append(f"status = ${param_count}")
                params.append(status.value)

            where_clause = " AND ".join(conditions)

            query = f"""
                SELECT id, name, scenario, status, total_transactions,
                       successful_categorizations, precision, recall, f1_score,
                       accuracy_percentage, completed_at, started_at,
                       ai_judge_correct, ai_judge_incorrect, ai_judge_partially_correct
                FROM accuracy_tests 
                WHERE {where_clause}
                ORDER BY created_at DESC
                LIMIT ${param_count + 1} OFFSET ${param_count + 2}
            """

            params.extend([limit, offset])
            rows = await self.connection.fetch(query, *params)

            summaries = []
            for row in rows:
                # Calculate derived metrics
                total = row["total_transactions"] or 0
                success_rate = (
                    (row["successful_categorizations"] / total * 100)
                    if total > 0
                    else 0.0
                )

                ai_total = (
                    (row["ai_judge_correct"] or 0)
                    + (row["ai_judge_incorrect"] or 0)
                    + (row["ai_judge_partially_correct"] or 0)
                )
                ai_judge_accuracy = (
                    (row["ai_judge_correct"] / ai_total * 100) if ai_total > 0 else None
                )

                execution_duration = None
                if row["started_at"] and row["completed_at"]:
                    execution_duration = (
                        row["completed_at"] - row["started_at"]
                    ).total_seconds()

                summary = AccuracyTestSummary(
                    test_id=row["id"],
                    test_name=row["name"],
                    scenario=AccuracyTestScenario(row["scenario"]),
                    status=AccuracyTestStatus(row["status"]),
                    overall_accuracy=row["accuracy_percentage"],
                    precision=row["precision"],
                    recall=row["recall"],
                    f1_score=row["f1_score"],
                    total_transactions=total,
                    successful_categorizations=row["successful_categorizations"] or 0,
                    success_rate=success_rate,
                    ai_judge_accuracy=ai_judge_accuracy,
                    execution_duration_seconds=execution_duration,
                    completed_at=row["completed_at"],
                )
                summaries.append(summary)

            return summaries

        except Exception as e:
            logger.error(f"Failed to list accuracy tests: {e}")
            raise AccuracyRepositoryError(f"Failed to list accuracy tests: {e}")

    async def update_accuracy_test_status(
        self,
        test_id: int,
        tenant_id: int,
        status: AccuracyTestStatus,
        status_data: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Update accuracy test status and related fields."""
        try:
            update_fields = ["status = $3"]
            params = [test_id, tenant_id, status.value]
            param_count = 3

            if status == AccuracyTestStatus.RUNNING:
                param_count += 1
                update_fields.append(f"started_at = ${param_count}")
                params.append(datetime.utcnow())

            elif status == AccuracyTestStatus.COMPLETED:
                param_count += 1
                update_fields.append(f"completed_at = ${param_count}")
                params.append(datetime.utcnow())

                if status_data:
                    for field in [
                        "total_transactions",
                        "successful_categorizations",
                        "ai_judge_correct",
                        "ai_judge_incorrect",
                        "ai_judge_partially_correct",
                        "precision",
                        "recall",
                        "f1_score",
                        "accuracy_percentage",
                    ]:
                        if field in status_data:
                            param_count += 1
                            update_fields.append(f"{field} = ${param_count}")
                            params.append(status_data[field])

            elif status == AccuracyTestStatus.FAILED:
                if status_data and "error_message" in status_data:
                    param_count += 1
                    update_fields.append(f"error_message = ${param_count}")
                    params.append(status_data["error_message"])

                if status_data and "error_details" in status_data:
                    param_count += 1
                    update_fields.append(f"error_details = ${param_count}")
                    params.append(status_data["error_details"])

            query = f"""
                UPDATE accuracy_tests 
                SET {", ".join(update_fields)}
                WHERE id = $1 AND tenant_id = $2
            """

            result = await self.connection.execute(query, *params)
            success = result.split()[-1] == "1"  # Check if one row was updated

            if success:
                logger.info(f"Updated accuracy test {test_id} status to {status.value}")

            return success

        except Exception as e:
            logger.error(f"Failed to update accuracy test status: {e}")
            raise AccuracyRepositoryError(f"Failed to update accuracy test status: {e}")

    # ==================== AI Judgments ====================

    async def create_ai_judgment(self, judgment_data: Dict[str, Any]) -> int:
        """Create a new AI judgment record."""
        try:
            query = """
                INSERT INTO ai_judgments (
                    test_id, transaction_id, original_category, ai_category,
                    ai_full_hierarchy, ai_confidence, judgment_result,
                    judgment_confidence, judgment_reasoning, category_exact_match,
                    category_semantic_match, hierarchy_level_matches,
                    transaction_description, transaction_amount, transaction_type,
                    judge_model, judge_version, judged_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
                RETURNING id
            """

            result = await self.connection.fetchval(
                query,
                judgment_data["test_id"],
                judgment_data["transaction_id"],
                judgment_data.get("original_category"),
                judgment_data["ai_category"],
                judgment_data.get("ai_full_hierarchy"),
                judgment_data["ai_confidence"],
                judgment_data["judgment_result"],
                judgment_data["judgment_confidence"],
                judgment_data.get("judgment_reasoning"),
                judgment_data.get("category_exact_match", False),
                judgment_data.get("category_semantic_match", False),
                judgment_data.get("hierarchy_level_matches"),
                judgment_data["transaction_description"],
                judgment_data["transaction_amount"],
                judgment_data["transaction_type"],
                judgment_data.get("judge_model", "gemini-2.0-flash-001"),
                judgment_data.get("judge_version", "1.0"),
                datetime.utcnow(),
            )

            return result

        except Exception as e:
            logger.error(f"Failed to create AI judgment: {e}")
            raise AccuracyRepositoryError(f"Failed to create AI judgment: {e}")

    async def get_ai_judgments_for_test(
        self,
        test_id: int,
        limit: int = 1000,
        judgment_result: Optional[AIJudgmentResult] = None,
    ) -> List[AIJudgment]:
        """Get AI judgments for a specific test."""
        try:
            conditions = ["test_id = $1"]
            params = [test_id]
            param_count = 1

            if judgment_result:
                param_count += 1
                conditions.append(f"judgment_result = ${param_count}")
                params.append(judgment_result.value)

            where_clause = " AND ".join(conditions)

            query = f"""
                SELECT id, test_id, transaction_id, original_category, ai_category,
                       ai_full_hierarchy, ai_confidence, judgment_result,
                       judgment_confidence, judgment_reasoning, category_exact_match,
                       category_semantic_match, hierarchy_level_matches,
                       transaction_description, transaction_amount, transaction_type,
                       judged_at, judge_model, judge_version
                FROM ai_judgments 
                WHERE {where_clause}
                ORDER BY judged_at DESC
                LIMIT ${param_count + 1}
            """

            params.append(limit)
            rows = await self.connection.fetch(query, *params)

            return [AIJudgment(**dict(row)) for row in rows]

        except Exception as e:
            logger.error(f"Failed to get AI judgments for test {test_id}: {e}")
            raise AccuracyRepositoryError(f"Failed to get AI judgments: {e}")

    # ==================== Accuracy Metrics ====================

    async def create_accuracy_metric(self, metric_data: Dict[str, Any]) -> int:
        """Create a new accuracy metric record."""
        try:
            query = """
                INSERT INTO accuracy_metrics (
                    test_id, metric_name, metric_type, value, sample_count,
                    true_positives, false_positives, true_negatives, false_negatives,
                    category_filter, confidence_range, hierarchy_level,
                    calculation_method, calculated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                RETURNING id
            """

            result = await self.connection.fetchval(
                query,
                metric_data["test_id"],
                metric_data["metric_name"],
                metric_data["metric_type"],
                metric_data["value"],
                metric_data["sample_count"],
                metric_data.get("true_positives", 0),
                metric_data.get("false_positives", 0),
                metric_data.get("true_negatives", 0),
                metric_data.get("false_negatives", 0),
                metric_data.get("category_filter"),
                metric_data.get("confidence_range"),
                metric_data.get("hierarchy_level"),
                metric_data["calculation_method"],
                datetime.utcnow(),
            )

            return result

        except Exception as e:
            logger.error(f"Failed to create accuracy metric: {e}")
            raise AccuracyRepositoryError(f"Failed to create accuracy metric: {e}")

    async def get_accuracy_metrics_for_test(self, test_id: int) -> List[AccuracyMetric]:
        """Get all accuracy metrics for a specific test."""
        try:
            query = """
                SELECT id, test_id, metric_name, metric_type, value, sample_count,
                       true_positives, false_positives, true_negatives, false_negatives,
                       category_filter, confidence_range, hierarchy_level,
                       calculated_at, calculation_method
                FROM accuracy_metrics 
                WHERE test_id = $1
                ORDER BY metric_name, metric_type
            """

            rows = await self.connection.fetch(query, test_id)
            return [AccuracyMetric(**dict(row)) for row in rows]

        except Exception as e:
            logger.error(f"Failed to get accuracy metrics for test {test_id}: {e}")
            raise AccuracyRepositoryError(f"Failed to get accuracy metrics: {e}")

    # ==================== Category Schemas ====================

    async def create_category_schema(self, schema_data: Dict[str, Any]) -> int:
        """Create a new category schema record."""
        try:
            query = """
                INSERT INTO category_schemas (
                    tenant_id, name, description, schema_format, schema_data,
                    category_count, max_hierarchy_depth, has_gl_codes,
                    gl_code_mapping, imported_from, imported_by, imported_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                RETURNING id
            """

            result = await self.connection.fetchval(
                query,
                schema_data["tenant_id"],
                schema_data["name"],
                schema_data.get("description"),
                schema_data["schema_format"],
                schema_data["schema_data"],
                schema_data["category_count"],
                schema_data["max_hierarchy_depth"],
                schema_data.get("has_gl_codes", False),
                schema_data.get("gl_code_mapping"),
                schema_data["imported_from"],
                schema_data.get("imported_by"),
                datetime.utcnow(),
            )

            logger.info(
                f"Created category schema {result} for tenant {schema_data['tenant_id']}"
            )
            return result

        except Exception as e:
            logger.error(f"Failed to create category schema: {e}")
            raise AccuracyRepositoryError(f"Failed to create category schema: {e}")

    async def get_category_schema(
        self, schema_id: int, tenant_id: int
    ) -> Optional[CategorySchema]:
        """Get category schema by ID with tenant isolation."""
        try:
            query = """
                SELECT id, tenant_id, name, description, schema_format, schema_data,
                       category_count, max_hierarchy_depth, has_gl_codes,
                       gl_code_mapping, imported_from, imported_at, imported_by,
                       is_active, last_used_at, usage_count
                FROM category_schemas 
                WHERE id = $1 AND tenant_id = $2 AND is_active = true
            """

            row = await self.connection.fetchrow(query, schema_id, tenant_id)
            if not row:
                return None

            return CategorySchema(**dict(row))

        except Exception as e:
            logger.error(f"Failed to get category schema {schema_id}: {e}")
            raise AccuracyRepositoryError(f"Failed to get category schema: {e}")

    async def list_category_schemas(self, tenant_id: int) -> List[CategorySchema]:
        """List all active category schemas for a tenant."""
        try:
            query = """
                SELECT id, tenant_id, name, description, schema_format, schema_data,
                       category_count, max_hierarchy_depth, has_gl_codes,
                       gl_code_mapping, imported_from, imported_at, imported_by,
                       is_active, last_used_at, usage_count
                FROM category_schemas 
                WHERE tenant_id = $1 AND is_active = true
                ORDER BY imported_at DESC
            """

            rows = await self.connection.fetch(query, tenant_id)
            return [CategorySchema(**dict(row)) for row in rows]

        except Exception as e:
            logger.error(f"Failed to list category schemas: {e}")
            raise AccuracyRepositoryError(f"Failed to list category schemas: {e}")

    async def update_category_schema_usage(
        self, schema_id: int, tenant_id: int
    ) -> bool:
        """Update category schema usage tracking."""
        try:
            query = """
                UPDATE category_schemas 
                SET last_used_at = $3, usage_count = usage_count + 1
                WHERE id = $1 AND tenant_id = $2
            """

            result = await self.connection.execute(
                query, schema_id, tenant_id, datetime.utcnow()
            )
            return result.split()[-1] == "1"

        except Exception as e:
            logger.error(f"Failed to update category schema usage: {e}")
            return False
