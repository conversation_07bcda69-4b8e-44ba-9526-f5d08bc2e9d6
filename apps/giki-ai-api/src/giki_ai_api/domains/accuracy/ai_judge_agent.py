"""
AI Judge Agent for evaluating categorization accuracy.

This agent uses Vertex AI to judge whether AI-generated categories are correct
compared to original/expected categories, providing nuanced evaluation beyond
simple string matching.
"""

import asyncio
import logging
from enum import Enum
from typing import Any, Dict, List, Optional

import vertexai
from vertexai.generative_models import GenerativeModel

from ...shared.ai.prompt_registry import get_prompt_registry
from ...shared.ai.standard_giki_agent import StandardGikiAgent
from .models import AIJudgmentResult

logger = logging.getLogger(__name__)


class JudgmentConfidence(Enum):
    """Confidence levels for AI judgments."""

    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class AIJudgeAgent(StandardGikiAgent):
    """AI agent for judging categorization correctness using semantic understanding."""

    def __init__(self, name: str = "ai_judge", model: str = "gemini-2.0-flash-001"):
        """Initialize AI Judge agent."""
        super().__init__(
            name=name,
            description="AI judge for evaluating categorization correctness",
            model_name=model,
            project_id="rezolve-poc",
            location="us-central1",
            enable_standard_tools=False,  # Don't need standard tools for judging
        )

        # Initialize Vertex AI
        self._model_name = model
        vertexai.init(project="rezolve-poc", location="us-central1")
        logger.info("✅ Vertex AI initialized for AI Judge")

    async def judge_categorization(
        self,
        transaction_description: str,
        transaction_amount: float,
        original_category: Optional[str],
        ai_category: str,
        ai_hierarchy: Optional[str] = None,
        ai_confidence: float = 0.0,
        context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Judge whether an AI categorization is correct compared to the original.

        Returns:
            Dict containing:
            - judgment_result: correct/incorrect/partially_correct
            - judgment_confidence: 0.0-1.0
            - judgment_reasoning: explanation of the judgment
            - semantic_similarity: how similar the categories are semantically
            - hierarchy_alignment: whether hierarchies align properly
        """

        # Build comprehensive prompt for judgment
        prompt = self._build_judgment_prompt(
            transaction_description,
            transaction_amount,
            original_category,
            ai_category,
            ai_hierarchy,
            ai_confidence,
            context,
        )

        try:
            # Use Vertex AI directly for judgment
            model = GenerativeModel(
                model_name=self._model_name,
                system_instruction="You are an expert financial categorization judge.",
            )

            # Get model config from prompt registry
            prompt_registry = get_prompt_registry()
            prompt_version = prompt_registry.get("ai_judge_categorization")
            
            response = await model.generate_content_async(
                prompt,
                generation_config=prompt_version.model_config,
            )

            response_text = (
                response.text if hasattr(response, "text") else str(response)
            )

            # Parse judgment response
            judgment = self._parse_judgment_response(response_text)

            logger.info(
                f"AI Judge result: {judgment['judgment_result']} "
                f"(confidence: {judgment['judgment_confidence']:.2f}) "
                f"for '{transaction_description[:50]}...'"
            )

            return judgment

        except Exception as e:
            logger.error(f"AI Judge failed: {e}")
            # Return conservative judgment on error
            return {
                "judgment_result": AIJudgmentResult.INCORRECT.value,
                "judgment_confidence": 0.0,
                "judgment_reasoning": f"Judgment failed due to error: {str(e)}",
                "semantic_similarity": 0.0,
                "hierarchy_alignment": False,
                "error": str(e),
            }

    def _build_judgment_prompt(
        self,
        transaction_description: str,
        transaction_amount: float,
        original_category: Optional[str],
        ai_category: str,
        ai_hierarchy: Optional[str],
        ai_confidence: float,
        context: Optional[Dict[str, Any]],
    ) -> str:
        """Build comprehensive prompt for AI judgment."""
        from ...shared.ai.prompt_registry import get_prompt_registry

        # Get prompt from registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("ai_judge_categorization")
        
        # Format prompt with variables
        prompt = prompt_version.format(
            transaction_description=transaction_description,
            transaction_amount=f"{transaction_amount:.2f}",
            original_category=original_category or "Not provided",
            ai_category=ai_category,
            ai_hierarchy=ai_hierarchy or "Not provided",
            ai_confidence=f"{ai_confidence:.2%}"
        )

        return prompt

    def _parse_judgment_response(self, response: str) -> Dict[str, Any]:
        """Parse AI judge response into structured format."""
        import json
        import re

        try:
            # Extract JSON from response
            json_match = re.search(r"\{.*\}", response, re.DOTALL)
            if json_match:
                judgment_data = json.loads(json_match.group())

                # Validate and normalize judgment result
                judgment_result = judgment_data.get(
                    "judgment_result", "incorrect"
                ).lower()
                if judgment_result not in ["correct", "incorrect", "partially_correct"]:
                    judgment_result = "incorrect"

                # Map to enum value
                result_map = {
                    "correct": AIJudgmentResult.CORRECT.value,
                    "incorrect": AIJudgmentResult.INCORRECT.value,
                    "partially_correct": AIJudgmentResult.PARTIALLY_CORRECT.value,
                }

                return {
                    "judgment_result": result_map[judgment_result],
                    "judgment_confidence": float(
                        judgment_data.get("judgment_confidence", 0.5)
                    ),
                    "judgment_reasoning": judgment_data.get(
                        "judgment_reasoning", "No reasoning provided"
                    ),
                    "semantic_similarity": float(
                        judgment_data.get("semantic_similarity", 0.0)
                    ),
                    "hierarchy_alignment": bool(
                        judgment_data.get("hierarchy_alignment", False)
                    ),
                    "key_factors": judgment_data.get("key_factors", []),
                    "improvement_suggestions": judgment_data.get(
                        "improvement_suggestions", ""
                    ),
                }
            else:
                raise ValueError("No JSON found in response")

        except Exception as e:
            logger.error(f"Failed to parse judgment response: {e}")
            # Return conservative judgment
            return {
                "judgment_result": AIJudgmentResult.INCORRECT.value,
                "judgment_confidence": 0.0,
                "judgment_reasoning": f"Failed to parse judgment: {str(e)}",
                "semantic_similarity": 0.0,
                "hierarchy_alignment": False,
                "parse_error": str(e),
            }

    async def evaluate_improvement_over_original(
        self,
        transaction_description: str,
        transaction_amount: float,
        original_category: str,
        ai_category: str,
        evaluation_criteria: str = "improvement_over_original",
    ) -> Dict[str, Any]:
        """
        Evaluate whether AI category is an improvement over the original category.

        This is specifically for M2 Rezolve temporal accuracy validation.

        Args:
            transaction_description: Transaction description
            transaction_amount: Transaction amount
            original_category: Customer's original category
            ai_category: AI-suggested category
            evaluation_criteria: Type of evaluation

        Returns:
            Dict with judgment: "better", "equivalent", "worse"
        """
        try:
            from ...shared.ai.prompt_registry import get_prompt_registry

            # Get prompt from registry
            prompt_registry = get_prompt_registry()
            prompt_version = prompt_registry.get("ai_judge_improvement")
            
            # Format prompt with variables
            prompt = prompt_version.format(
                transaction_description=transaction_description,
                transaction_amount=f"{transaction_amount:.2f}",
                original_category=original_category,
                ai_category=ai_category
            )

            # Use Vertex AI for evaluation
            model = GenerativeModel(
                model_name=self._model_name,
                system_instruction="You are an expert financial categorization judge specialized in improvement evaluation.",
            )

            response = await model.generate_content_async(
                prompt,
                generation_config=prompt_version.model_config,
            )

            response_text = (
                response.text if hasattr(response, "text") else str(response)
            )

            # Parse the improvement evaluation response
            evaluation = self._parse_improvement_response(response_text)

            logger.info(
                f"Improvement evaluation: {evaluation['judgment']} "
                f"({evaluation['confidence']:.2f}) for '{original_category}' → '{ai_category}'"
            )

            return evaluation

        except Exception as e:
            logger.error(f"Improvement evaluation failed: {e}")
            return {
                "judgment": "worse",
                "confidence": 0.0,
                "reasoning": f"Evaluation failed: {str(e)}",
                "improvement_type": "regression",
                "business_utility_score": 0.0,
                "error": str(e),
            }

    def _parse_improvement_response(self, response: str) -> Dict[str, Any]:
        """Parse improvement evaluation response."""
        import json
        import re

        try:
            # Extract JSON from response
            json_match = re.search(r"\{.*\}", response, re.DOTALL)
            if json_match:
                evaluation_data = json.loads(json_match.group())

                # Validate judgment
                judgment = evaluation_data.get("judgment", "worse").lower()
                if judgment not in ["better", "equivalent", "worse"]:
                    judgment = "worse"

                return {
                    "judgment": judgment,
                    "confidence": float(evaluation_data.get("confidence", 0.5)),
                    "reasoning": evaluation_data.get(
                        "reasoning", "No reasoning provided"
                    ),
                    "improvement_type": evaluation_data.get(
                        "improvement_type", "unknown"
                    ),
                    "business_utility_score": float(
                        evaluation_data.get("business_utility_score", 0.0)
                    ),
                }
            else:
                raise ValueError("No JSON found in response")

        except Exception as e:
            logger.error(f"Failed to parse improvement response: {e}")
            return {
                "judgment": "worse",
                "confidence": 0.0,
                "reasoning": f"Parse error: {str(e)}",
                "improvement_type": "regression",
                "business_utility_score": 0.0,
                "parse_error": str(e),
            }

    async def batch_judge_categorizations(
        self, categorizations: List[Dict[str, Any]], batch_size: int = 10
    ) -> List[Dict[str, Any]]:
        """Judge multiple categorizations in batches for efficiency."""
        results = []

        for i in range(0, len(categorizations), batch_size):
            batch = categorizations[i : i + batch_size]
            batch_results = await asyncio.gather(
                *[
                    self.judge_categorization(
                        transaction_description=cat["transaction_description"],
                        transaction_amount=cat["transaction_amount"],
                        original_category=cat.get("original_category"),
                        ai_category=cat["ai_category"],
                        ai_hierarchy=cat.get("ai_hierarchy"),
                        ai_confidence=cat.get("ai_confidence", 0.0),
                    )
                    for cat in batch
                ]
            )
            results.extend(batch_results)

        return results
