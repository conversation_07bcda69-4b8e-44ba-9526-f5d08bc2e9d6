"""
Transaction loader for accuracy testing.

Loads transactions from various sources (Excel, CSV, JSON) for accuracy measurement.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd

logger = logging.getLogger(__name__)


class TransactionLoader:
    """Load transactions from various file formats for accuracy testing."""

    @staticmethod
    async def load_from_file(
        file_path: str,
        sample_size: Optional[int] = None,
        required_original_category: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        Load transactions from file based on extension.

        Args:
            file_path: Path to the transaction file
            sample_size: Number of transactions to load (None = all)
            required_original_category: Whether original category is required

        Returns:
            List of transaction dictionaries
        """
        path = Path(file_path)

        if not path.exists():
            raise FileNotFoundError(f"Transaction file not found: {file_path}")

        extension = path.suffix.lower()

        if extension in [".xlsx", ".xls"]:
            return await TransactionLoader._load_from_excel(
                file_path, sample_size, required_original_category
            )
        elif extension == ".csv":
            return await TransactionLoader._load_from_csv(
                file_path, sample_size, required_original_category
            )
        elif extension == ".json":
            return await TransactionLoader._load_from_json(
                file_path, sample_size, required_original_category
            )
        else:
            raise ValueError(f"Unsupported file format: {extension}")

    @staticmethod
    async def _load_from_excel(
        file_path: str, sample_size: Optional[int], required_original_category: bool
    ) -> List[Dict[str, Any]]:
        """Load transactions from Excel file."""
        try:
            # Try different sheet names
            df = None
            for sheet in ["Transactions", "Data", "Expense Ledger", "Sheet1", 0]:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet, header=0)
                    # Check if we have unnamed columns (likely wrong header row)
                    if any("Unnamed:" in str(col) for col in df.columns):
                        # Try with header=1
                        df = pd.read_excel(file_path, sheet_name=sheet, header=1)
                    logger.info(f"Loaded sheet '{sheet}' from {file_path}")
                    break
                except Exception:
                    continue

            if df is None:
                raise ValueError("Could not load any sheet from Excel file")

            # Clean column names
            df.columns = df.columns.str.strip()

            # Find relevant columns
            description_col = TransactionLoader._find_column(
                df.columns,
                [
                    "description",
                    "desc",
                    "vendor",
                    "merchant",
                    "narration",
                    "memo",
                    "payee",
                ],
            )
            amount_col = TransactionLoader._find_column(
                df.columns,
                ["amount", "total", "cost", "expense", "debit", "withdrawal"],
            )
            category_col = TransactionLoader._find_column(
                df.columns, ["category", "class", "type", "account", "classification"]
            )
            date_col = TransactionLoader._find_column(
                df.columns, ["date", "transaction_date", "posted_date", "value_dt"]
            )

            if not description_col or not amount_col:
                raise ValueError(
                    f"Required columns not found. Available: {list(df.columns)}"
                )

            if required_original_category and not category_col:
                raise ValueError(
                    f"Original category column required but not found. Available: {list(df.columns)}"
                )

            # Extract transactions
            transactions = []
            for idx, row in df.iterrows():
                try:
                    # Skip invalid rows
                    if pd.isna(row[description_col]) or pd.isna(row[amount_col]):
                        continue

                    description = str(row[description_col]).strip()
                    if len(description) < 3:
                        continue

                    # Parse amount
                    amount = TransactionLoader._parse_amount(row[amount_col])
                    if amount is None or amount <= 0:
                        continue

                    # Get original category if available
                    original_category = None
                    if category_col and not pd.isna(row[category_col]):
                        original_category = str(row[category_col]).strip()

                    # Parse date if available
                    transaction_date = datetime.now()
                    if date_col and not pd.isna(row[date_col]):
                        try:
                            transaction_date = pd.to_datetime(row[date_col])
                        except Exception:
                            pass

                    transaction = {
                        "id": f"excel_{idx}",
                        "description": description,
                        "amount": amount,
                        "transaction_type": "debit",  # Default to debit
                        "original_category": original_category,
                        "date": transaction_date.isoformat(),
                        "source": file_path,
                        "row_index": idx,
                    }

                    transactions.append(transaction)

                    # Check sample size
                    if sample_size and len(transactions) >= sample_size:
                        break

                except Exception as e:
                    logger.warning(f"Error processing row {idx}: {e}")
                    continue

            logger.info(f"Loaded {len(transactions)} transactions from Excel")
            return transactions

        except Exception as e:
            logger.error(f"Failed to load Excel file: {e}")
            raise

    @staticmethod
    async def _load_from_csv(
        file_path: str, sample_size: Optional[int], required_original_category: bool
    ) -> List[Dict[str, Any]]:
        """Load transactions from CSV file."""
        try:
            # Use same logic as Excel loader
            return await TransactionLoader._load_from_excel(
                file_path, sample_size, required_original_category
            )
        except Exception as e:
            logger.error(f"Failed to load CSV file: {e}")
            raise

    @staticmethod
    async def _load_from_json(
        file_path: str, sample_size: Optional[int], required_original_category: bool
    ) -> List[Dict[str, Any]]:
        """Load transactions from JSON file."""
        try:
            with open(file_path, "r") as f:
                data = json.load(f)

            # Handle both list and dict formats
            if isinstance(data, dict):
                transactions = data.get("transactions", [])
            else:
                transactions = data

            # Validate and normalize transactions
            normalized = []
            for i, tx in enumerate(transactions):
                if not isinstance(tx, dict):
                    continue

                # Validate required fields
                if "description" not in tx or "amount" not in tx:
                    continue

                if required_original_category and "original_category" not in tx:
                    continue

                # Normalize transaction
                normalized_tx = {
                    "id": tx.get("id", f"json_{i}"),
                    "description": str(tx["description"]).strip(),
                    "amount": float(tx["amount"]),
                    "transaction_type": tx.get("transaction_type", "debit"),
                    "original_category": tx.get("original_category"),
                    "date": tx.get("date", datetime.now().isoformat()),
                    "source": file_path,
                }

                normalized.append(normalized_tx)

                if sample_size and len(normalized) >= sample_size:
                    break

            logger.info(f"Loaded {len(normalized)} transactions from JSON")
            return normalized

        except Exception as e:
            logger.error(f"Failed to load JSON file: {e}")
            raise

    @staticmethod
    def _find_column(columns: List[str], keywords: List[str]) -> Optional[str]:
        """Find column by keywords (case-insensitive)."""
        for col in columns:
            col_lower = str(col).lower()
            for keyword in keywords:
                if keyword.lower() in col_lower:
                    return col
        return None

    @staticmethod
    def _parse_amount(value: Any) -> Optional[float]:
        """Parse amount from various formats."""
        if pd.isna(value):
            return None

        try:
            # Handle string amounts
            if isinstance(value, str):
                # Remove currency symbols and commas
                cleaned = value.replace("$", "").replace(",", "").strip()
                # Handle parentheses for negative
                if cleaned.startswith("(") and cleaned.endswith(")"):
                    cleaned = "-" + cleaned[1:-1]
                return abs(float(cleaned))
            else:
                return abs(float(value))
        except Exception:
            return None

    @staticmethod
    async def validate_transactions(
        transactions: List[Dict[str, Any]], required_fields: List[str] = None
    ) -> Dict[str, Any]:
        """Validate loaded transactions."""
        if required_fields is None:
            required_fields = ["id", "description", "amount", "transaction_type"]

        validation_results = {
            "total": len(transactions),
            "valid": 0,
            "invalid": 0,
            "has_categories": 0,
            "missing_fields": {},
            "issues": [],
        }

        for tx in transactions:
            is_valid = True

            # Check required fields
            for field in required_fields:
                if field not in tx or tx[field] is None:
                    is_valid = False
                    validation_results["missing_fields"][field] = (
                        validation_results["missing_fields"].get(field, 0) + 1
                    )

            # Additional validation
            if "amount" in tx and tx["amount"] is not None:
                if tx["amount"] <= 0:
                    is_valid = False
                    validation_results["issues"].append(
                        f"Transaction {tx.get('id', 'unknown')} has non-positive amount"
                    )

            if "description" in tx and tx["description"]:
                if len(tx["description"]) < 3:
                    is_valid = False
                    validation_results["issues"].append(
                        f"Transaction {tx.get('id', 'unknown')} has too short description"
                    )

            if is_valid:
                validation_results["valid"] += 1
            else:
                validation_results["invalid"] += 1

            if tx.get("original_category"):
                validation_results["has_categories"] += 1

        validation_results["category_coverage"] = (
            validation_results["has_categories"] / validation_results["total"]
            if validation_results["total"] > 0
            else 0
        )

        return validation_results
