"""
Accuracy measurement API schemas.

Pydantic schemas for request/response validation in accuracy measurement endpoints.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from .models import AccuracyTestScenario, AccuracyTestStatus, AIJudgmentResult

# ==================== Request Schemas ====================


class AccuracyTestCreate(BaseModel):
    """Schema for creating a new accuracy test."""

    name: str = Field(..., max_length=200, description="Test name")
    description: Optional[str] = Field(
        None, max_length=1000, description="Test description"
    )
    scenario: AccuracyTestScenario = Field(..., description="Test scenario type")
    test_data_source: str = Field(
        ..., max_length=500, description="Path to test data file or identifier"
    )
    category_schema_id: Optional[int] = Field(
        None, description="Category schema ID for schema-only scenario"
    )
    sample_size: int = Field(
        default=100, ge=1, le=10000, description="Number of transactions to test"
    )


class AccuracyTestUpdate(BaseModel):
    """Schema for updating accuracy test configuration."""

    name: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    sample_size: Optional[int] = Field(None, ge=1, le=10000)


class AccuracyTestFilter(BaseModel):
    """Schema for filtering accuracy tests."""

    scenario: Optional[AccuracyTestScenario] = None
    status: Optional[AccuracyTestStatus] = None
    limit: int = Field(default=50, ge=1, le=200)
    offset: int = Field(default=0, ge=0)


class CategorySchemaCreate(BaseModel):
    """Schema for creating a category schema."""

    name: str = Field(..., max_length=200, description="Schema name")
    description: Optional[str] = Field(
        None, max_length=1000, description="Schema description"
    )
    schema_format: str = Field(
        ..., max_length=50, description="Format: excel, csv, json, yaml"
    )
    schema_data: Dict[str, Any] = Field(
        ..., description="Parsed category hierarchy structure"
    )
    imported_from: str = Field(
        ..., max_length=500, description="Original file name or source"
    )
    imported_by: Optional[str] = Field(
        None, max_length=100, description="User identifier"
    )


class CategorySchemaImport(BaseModel):
    """Schema for importing category schema from file."""

    name: str = Field(..., max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    file_format: str = Field(..., description="excel, csv, json, yaml")
    file_content: str = Field(
        ..., description="Base64 encoded file content or raw content"
    )
    detect_hierarchy: bool = Field(
        default=True, description="Auto-detect hierarchy from flat lists"
    )
    generate_gl_codes: bool = Field(default=True, description="Auto-generate GL codes")


class AIJudgmentCreate(BaseModel):
    """Schema for creating AI judgment (internal use)."""

    test_id: int
    transaction_id: str = Field(..., max_length=100)
    original_category: Optional[str] = Field(None, max_length=500)
    ai_category: str = Field(..., max_length=500)
    transaction_description: str = Field(..., max_length=1000)
    transaction_amount: float
    transaction_type: str = Field(..., max_length=20)


# ==================== Response Schemas ====================


class AccuracyTestResponse(BaseModel):
    """Schema for accuracy test response."""

    id: int
    tenant_id: int
    name: str
    description: Optional[str]
    scenario: AccuracyTestScenario
    test_data_source: str
    category_schema_id: Optional[int]
    sample_size: int
    status: AccuracyTestStatus
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]

    # Results
    total_transactions: int
    successful_categorizations: int
    ai_judge_correct: int
    ai_judge_incorrect: int
    ai_judge_partially_correct: int

    # Metrics
    precision: Optional[float]
    recall: Optional[float]
    f1_score: Optional[float]
    accuracy_percentage: Optional[float]

    # Error info
    error_message: Optional[str]


class AccuracyTestSummaryResponse(BaseModel):
    """Schema for accuracy test summary response."""

    test_id: int
    test_name: str
    scenario: AccuracyTestScenario
    status: AccuracyTestStatus

    # Key metrics
    overall_accuracy: Optional[float]
    precision: Optional[float]
    recall: Optional[float]
    f1_score: Optional[float]

    # Sample info
    total_transactions: int
    successful_categorizations: int
    success_rate: float

    # AI judge summary
    ai_judge_accuracy: Optional[float]
    ai_judge_confidence_avg: Optional[float]

    # Quality indicators
    non_generic_categories: int = 0
    hierarchical_categories: int = 0
    categories_with_gl_codes: int = 0

    # Timing
    execution_duration_seconds: Optional[float]
    completed_at: Optional[datetime]


class AccuracyTestExecutionResponse(BaseModel):
    """Schema for test execution response."""

    test_id: int
    status: str
    message: str
    results: Optional[Dict[str, Any]] = None
    metrics: Optional[Dict[str, Any]] = None
    summary: Optional[Dict[str, Any]] = None


class AccuracyMetricResponse(BaseModel):
    """Schema for accuracy metric response."""

    id: int
    test_id: int
    metric_name: str
    metric_type: str
    value: float
    sample_count: int

    # Breakdown
    true_positives: int
    false_positives: int
    true_negatives: int
    false_negatives: int

    # Context
    category_filter: Optional[str]
    confidence_range: Optional[str]
    hierarchy_level: Optional[int]

    # Metadata
    calculated_at: datetime
    calculation_method: str


class AIJudgmentResponse(BaseModel):
    """Schema for AI judgment response."""

    id: int
    test_id: int
    transaction_id: str

    # Categorization comparison
    original_category: Optional[str]
    ai_category: str
    ai_full_hierarchy: Optional[str]
    ai_confidence: float

    # Judgment results
    judgment_result: AIJudgmentResult
    judgment_confidence: float
    judgment_reasoning: Optional[str]

    # Detailed analysis
    category_exact_match: bool
    category_semantic_match: bool
    hierarchy_level_matches: Optional[Dict[str, bool]]

    # Transaction context
    transaction_description: str
    transaction_amount: float
    transaction_type: str

    # Metadata
    judged_at: datetime
    judge_model: str
    judge_version: str


class CategorySchemaResponse(BaseModel):
    """Schema for category schema response."""

    id: int
    tenant_id: int
    name: str
    description: Optional[str]
    schema_format: str
    schema_data: Dict[str, Any]
    category_count: int
    max_hierarchy_depth: int

    # GL code info
    has_gl_codes: bool
    gl_code_mapping: Optional[Dict[str, str]]

    # Import metadata
    imported_from: str
    imported_at: datetime
    imported_by: Optional[str]

    # Usage tracking
    is_active: bool
    last_used_at: Optional[datetime]
    usage_count: int


class CategorySchemaImportResponse(BaseModel):
    """Schema for category schema import response."""

    schema_id: int
    message: str
    categories_imported: int
    hierarchy_levels_detected: int
    gl_codes_generated: int
    validation_warnings: List[str] = []


class AccuracyReportResponse(BaseModel):
    """Schema for comprehensive accuracy report response."""

    test_id: int
    test_name: str
    scenario: AccuracyTestScenario

    # Overall metrics
    overall_metrics: Dict[str, float]

    # Detailed breakdowns
    category_breakdown: List[Dict[str, Any]]
    confidence_breakdown: List[Dict[str, Any]]
    hierarchy_breakdown: List[Dict[str, Any]]

    # AI judge analysis
    ai_judge_summary: Dict[str, Any]
    judgment_distribution: Dict[str, int]

    # Quality analysis
    quality_metrics: Dict[str, Any]
    improvement_suggestions: List[str]

    # Export info
    excel_report_url: Optional[str]
    generated_at: datetime


# ==================== List Response Schemas ====================


class AccuracyTestListResponse(BaseModel):
    """Schema for accuracy test list response."""

    tests: List[AccuracyTestSummaryResponse]
    total_count: int
    page_size: int
    page_offset: int
    has_more: bool


class CategorySchemaListResponse(BaseModel):
    """Schema for category schema list response."""

    schemas: List[CategorySchemaResponse]
    total_count: int


class AccuracyMetricListResponse(BaseModel):
    """Schema for accuracy metrics list response."""

    metrics: List[AccuracyMetricResponse]
    test_id: int
    total_metrics: int


class AIJudgmentListResponse(BaseModel):
    """Schema for AI judgment list response."""

    judgments: List[AIJudgmentResponse]
    test_id: int
    total_judgments: int
    summary: Dict[str, Any]


# ==================== Error Response Schemas ====================


class AccuracyErrorResponse(BaseModel):
    """Schema for accuracy-related error responses."""

    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ValidationErrorResponse(BaseModel):
    """Schema for validation error responses."""

    error: str = "validation_error"
    message: str
    field_errors: List[Dict[str, str]]
    timestamp: datetime = Field(default_factory=datetime.utcnow)
