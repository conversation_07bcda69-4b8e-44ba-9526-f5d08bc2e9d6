"""
M2 Temporal Accuracy Validation Pipeline
========================================

Complete validation pipeline for M2 milestone implementation.
Connects temporal accuracy service with file upload system and provides
end-to-end validation workflow for Rezolve customer scenario.
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from ...core.database import get_database_connection
from .temporal_accuracy_service import TemporalAccuracyService

logger = logging.getLogger(__name__)


class M2ValidationPipeline:
    """
    Complete M2 validation pipeline for temporal accuracy measurement.

    Handles:
    - Historical data ingestion
    - Training/testing data split (Jan-Jun vs Jul-Dec 2024)
    - Progressive accuracy measurement
    - Improvement-over-original evaluation
    - M2 milestone validation reporting
    """

    def __init__(self, tenant_id: int = 2):
        """Initialize M2 validation pipeline for Rezolve tenant."""
        self.tenant_id = tenant_id  # Rezolve customer tenant
        self.temporal_service = TemporalAccuracyService()

        # M2 validation thresholds
        self.minimum_improvement_rate = 85.0  # >85% improvement required
        self.minimum_transactions = 500  # Minimum transactions for valid test
        self.confidence_threshold = 0.8  # Minimum confidence for acceptance

    async def validate_historical_data_availability(self) -> Dict[str, Any]:
        """
        Validate that sufficient historical data exists for M2 testing.

        Returns:
            Validation status and data statistics
        """
        try:
            async with get_database_connection() as conn:
                # Check training period data (Jan-Jun 2024)
                training_query = """
                    SELECT 
                        COUNT(*) as transaction_count,
                        COUNT(DISTINCT EXTRACT(MONTH FROM date)) as month_coverage,
                        MIN(date) as start_date,
                        MAX(date) as end_date,
                        COUNT(CASE WHEN original_category_label IS NOT NULL THEN 1 END) as categorized_count
                    FROM transactions 
                    WHERE tenant_id = $1 
                    AND date >= '2024-01-01' 
                    AND date <= '2024-06-30'
                """
                training_stats = await conn.fetchrow(training_query, self.tenant_id)

                # Check testing period data (Jul-Dec 2024)
                testing_query = """
                    SELECT 
                        COUNT(*) as transaction_count,
                        COUNT(DISTINCT EXTRACT(MONTH FROM date)) as month_coverage,
                        MIN(date) as start_date,
                        MAX(date) as end_date,
                        COUNT(CASE WHEN original_category_label IS NOT NULL THEN 1 END) as categorized_count
                    FROM transactions 
                    WHERE tenant_id = $1 
                    AND date >= '2024-07-01' 
                    AND date <= '2024-12-31'
                """
                testing_stats = await conn.fetchrow(testing_query, self.tenant_id)

                # Validate data sufficiency
                validation_result = {
                    "tenant_id": self.tenant_id,
                    "validation_timestamp": datetime.utcnow().isoformat(),
                    "training_period": {
                        "transactions": training_stats["transaction_count"],
                        "months_covered": training_stats["month_coverage"],
                        "categorized_transactions": training_stats["categorized_count"],
                        "period": f"{training_stats['start_date']} to {training_stats['end_date']}",
                        "sufficient": training_stats["transaction_count"]
                        >= self.minimum_transactions,
                    },
                    "testing_period": {
                        "transactions": testing_stats["transaction_count"],
                        "months_covered": testing_stats["month_coverage"],
                        "categorized_transactions": testing_stats["categorized_count"],
                        "period": f"{testing_stats['start_date']} to {testing_stats['end_date']}",
                        "sufficient": testing_stats["transaction_count"]
                        >= self.minimum_transactions,
                    },
                }

                # Overall validation status
                validation_result["overall_status"] = {
                    "ready_for_m2": (
                        validation_result["training_period"]["sufficient"]
                        and validation_result["testing_period"]["sufficient"]
                        and training_stats["month_coverage"] >= 6
                        and testing_stats["month_coverage"] >= 6
                    ),
                    "total_transactions": training_stats["transaction_count"]
                    + testing_stats["transaction_count"],
                    "issues": [],
                }

                # Identify any issues
                if not validation_result["training_period"]["sufficient"]:
                    validation_result["overall_status"]["issues"].append(
                        f"Insufficient training data: {training_stats['transaction_count']} < {self.minimum_transactions}"
                    )

                if not validation_result["testing_period"]["sufficient"]:
                    validation_result["overall_status"]["issues"].append(
                        f"Insufficient testing data: {testing_stats['transaction_count']} < {self.minimum_transactions}"
                    )

                if training_stats["month_coverage"] < 6:
                    validation_result["overall_status"]["issues"].append(
                        f"Incomplete training period coverage: {training_stats['month_coverage']}/6 months"
                    )

                if testing_stats["month_coverage"] < 6:
                    validation_result["overall_status"]["issues"].append(
                        f"Incomplete testing period coverage: {testing_stats['month_coverage']}/6 months"
                    )

                logger.info(
                    f"M2 data validation complete: {validation_result['overall_status']['ready_for_m2']}"
                )
                return validation_result

        except Exception as e:
            logger.error(f"M2 data validation failed: {e}")
            return {
                "error": str(e),
                "tenant_id": self.tenant_id,
                "overall_status": {"ready_for_m2": False, "issues": [str(e)]},
            }

    async def run_progressive_validation(self) -> Dict[str, Any]:
        """
        Run complete M2 progressive temporal accuracy validation.

        Implements the progressive monthly validation approach:
        1. Train on Jan-Jun 2024 data
        2. Test on Jul-Dec 2024 data with monthly progression
        3. Measure improvement-over-original for each month
        4. Calculate overall M2 milestone completion

        Returns:
            Complete M2 validation results
        """
        try:
            logger.info(
                f"Starting M2 progressive validation for tenant {self.tenant_id}"
            )

            # Step 1: Validate data availability
            data_validation = await self.validate_historical_data_availability()
            if not data_validation.get("overall_status", {}).get("ready_for_m2", False):
                return {
                    "error": "Insufficient data for M2 validation",
                    "data_validation": data_validation,
                    "milestone_status": "FAILED - Data Insufficient",
                }

            # Step 2: Create temporal data split
            split_result = await self.temporal_service.create_temporal_data_split(
                self.tenant_id
            )

            # Step 3: Run progressive monthly validation
            validation_result = (
                await self.temporal_service.run_progressive_temporal_validation(
                    self.tenant_id
                )
            )

            # Step 4: Calculate M2 milestone status
            milestone_status = await self._calculate_m2_milestone_status(
                validation_result
            )

            # Step 5: Generate comprehensive report
            final_result = {
                "tenant_id": self.tenant_id,
                "validation_timestamp": datetime.utcnow().isoformat(),
                "milestone": "M2 - Rezolve Temporal Accuracy",
                "data_validation": data_validation,
                "temporal_split": split_result,
                "progressive_validation": validation_result,
                "milestone_status": milestone_status,
                "summary": {
                    "total_months_tested": len(
                        validation_result.get("monthly_results", [])
                    ),
                    "overall_improvement_rate": milestone_status.get(
                        "improvement_rate", 0
                    ),
                    "passed_threshold": milestone_status.get("passed", False),
                    "improvement_target": self.minimum_improvement_rate,
                },
            }

            logger.info(
                f"M2 validation complete: {milestone_status.get('status', 'Unknown')}"
            )
            return final_result

        except Exception as e:
            logger.error(f"M2 progressive validation failed: {e}")
            return {
                "error": str(e),
                "tenant_id": self.tenant_id,
                "milestone_status": "FAILED - Validation Error",
            }

    async def _calculate_m2_milestone_status(
        self, validation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Calculate M2 milestone completion status based on validation results.

        Args:
            validation_result: Results from progressive temporal validation

        Returns:
            M2 milestone status and metrics
        """
        try:
            monthly_results = validation_result.get("monthly_results", [])

            if not monthly_results:
                return {
                    "status": "FAILED",
                    "reason": "No monthly validation results",
                    "passed": False,
                }

            # Calculate overall improvement rate
            improvement_rates = []
            monthly_summaries = []

            for month_result in monthly_results:
                month_improvement = month_result.get("improvement_rate", 0)
                improvement_rates.append(month_improvement)

                monthly_summaries.append(
                    {
                        "month": month_result.get("month", "Unknown"),
                        "improvement_rate": month_improvement,
                        "transactions_tested": month_result.get(
                            "transactions_tested", 0
                        ),
                        "confidence": month_result.get("confidence", 0),
                        "passed_threshold": month_improvement
                        >= self.minimum_improvement_rate,
                    }
                )

            # Overall improvement rate (average of monthly rates)
            overall_improvement_rate = sum(improvement_rates) / len(improvement_rates)

            # Calculate success metrics
            months_passed = sum(
                1 for rate in improvement_rates if rate >= self.minimum_improvement_rate
            )
            pass_rate = months_passed / len(improvement_rates) * 100

            # Determine M2 milestone status
            milestone_passed = overall_improvement_rate >= self.minimum_improvement_rate

            status_result = {
                "status": "PASSED" if milestone_passed else "FAILED",
                "passed": milestone_passed,
                "improvement_rate": round(overall_improvement_rate, 2),
                "improvement_target": self.minimum_improvement_rate,
                "months_tested": len(improvement_rates),
                "months_passed": months_passed,
                "pass_rate": round(pass_rate, 1),
                "monthly_breakdown": monthly_summaries,
                "validation_summary": {
                    "minimum_threshold_met": milestone_passed,
                    "best_month_improvement": max(improvement_rates)
                    if improvement_rates
                    else 0,
                    "worst_month_improvement": min(improvement_rates)
                    if improvement_rates
                    else 0,
                    "consistency_score": round(
                        100 - (max(improvement_rates) - min(improvement_rates)), 1
                    )
                    if improvement_rates
                    else 0,
                },
            }

            # Add success/failure reasons
            if milestone_passed:
                status_result["success_factors"] = [
                    f"Overall improvement rate {overall_improvement_rate:.1f}% exceeds {self.minimum_improvement_rate}% target",
                    f"{months_passed}/{len(improvement_rates)} months passed individual thresholds",
                    "Consistent temporal accuracy improvement demonstrated",
                ]
            else:
                status_result["failure_reasons"] = [
                    f"Overall improvement rate {overall_improvement_rate:.1f}% below {self.minimum_improvement_rate}% target",
                    f"Only {months_passed}/{len(improvement_rates)} months passed individual thresholds",
                    "Insufficient temporal accuracy improvement",
                ]

            return status_result

        except Exception as e:
            logger.error(f"M2 milestone status calculation failed: {e}")
            return {"status": "ERROR", "error": str(e), "passed": False}

    async def generate_m2_report(
        self, output_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate comprehensive M2 validation report.

        Args:
            output_path: Optional path to save report file

        Returns:
            Report data and file path if saved
        """
        try:
            # Run complete validation
            validation_results = await self.run_progressive_validation()

            # Generate report
            report = {
                "report_type": "M2 Temporal Accuracy Validation",
                "generated_at": datetime.utcnow().isoformat(),
                "tenant_id": self.tenant_id,
                "customer": "Rezolve",
                "validation_results": validation_results,
                "executive_summary": {
                    "milestone_status": validation_results.get(
                        "milestone_status", {}
                    ).get("status", "Unknown"),
                    "improvement_rate": validation_results.get("summary", {}).get(
                        "overall_improvement_rate", 0
                    ),
                    "target_threshold": self.minimum_improvement_rate,
                    "data_coverage": {
                        "training_period": "Jan-Jun 2024",
                        "testing_period": "Jul-Dec 2024",
                        "total_transactions": validation_results.get(
                            "data_validation", {}
                        )
                        .get("overall_status", {})
                        .get("total_transactions", 0),
                    },
                },
            }

            # Save report if path provided
            if output_path:
                report_path = Path(output_path)
                report_path.parent.mkdir(parents=True, exist_ok=True)

                with open(report_path, "w") as f:
                    json.dump(report, f, indent=2, default=str)

                report["saved_to"] = str(report_path)
                logger.info(f"M2 report saved to {report_path}")

            return report

        except Exception as e:
            logger.error(f"M2 report generation failed: {e}")
            return {
                "error": str(e),
                "report_type": "M2 Temporal Accuracy Validation - ERROR",
            }


# Convenience functions for M2 validation
async def validate_m2_readiness(tenant_id: int = 2) -> Dict[str, Any]:
    """Quick check for M2 validation readiness."""
    pipeline = M2ValidationPipeline(tenant_id)
    return await pipeline.validate_historical_data_availability()


async def run_m2_validation(tenant_id: int = 2) -> Dict[str, Any]:
    """Run complete M2 validation pipeline."""
    pipeline = M2ValidationPipeline(tenant_id)
    return await pipeline.run_progressive_validation()


async def generate_m2_report(
    tenant_id: int = 2, output_path: Optional[str] = None
) -> Dict[str, Any]:
    """Generate M2 validation report."""
    pipeline = M2ValidationPipeline(tenant_id)
    return await pipeline.generate_m2_report(output_path)


# CLI integration for standalone execution
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="M2 Temporal Accuracy Validation Pipeline"
    )
    parser.add_argument(
        "--tenant-id", type=int, default=2, help="Tenant ID (default: 2 for Rezolve)"
    )
    parser.add_argument(
        "--action",
        choices=["readiness", "validate", "report"],
        default="validate",
        help="Action to perform",
    )
    parser.add_argument("--output", type=str, help="Output file path for report")

    args = parser.parse_args()

    async def main():
        if args.action == "readiness":
            result = await validate_m2_readiness(args.tenant_id)
        elif args.action == "validate":
            result = await run_m2_validation(args.tenant_id)
        elif args.action == "report":
            result = await generate_m2_report(args.tenant_id, args.output)

        print(json.dumps(result, indent=2, default=str))

    asyncio.run(main())
