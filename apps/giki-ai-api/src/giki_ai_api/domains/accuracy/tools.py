"""
Accuracy domain ADK tools.

This module provides ADK tool functions for ML tracking and validation operations that can be used
by agents to create accuracy tests, run evaluations, and analyze AI performance metrics.
"""

import logging
from typing import Any, Dict, Optional

import asyncpg

from .models import AccuracyTestScenario, AccuracyTestStatus
from .service import AccuracyMeasurementService

logger = logging.getLogger(__name__)


async def create_accuracy_test_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    name: str,
    scenario: str,
    test_data_source: str,
    description: Optional[str] = None,
    category_schema_id: Optional[int] = None,
    sample_size: int = 100,
) -> Dict[str, Any]:
    """
    ADK tool function to create a new accuracy test configuration.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        name: Test name
        scenario: Test scenario (historical_data/schema_only/zero_onboarding)
        test_data_source: Data source path or upload_id
        description: Optional test description
        category_schema_id: Category schema ID (required for schema_only)
        sample_size: Number of transactions to test

    Returns:
        Test creation result with test_id and configuration
    """
    try:
        service = AccuracyMeasurementService(db)

        # Convert string scenario to enum
        scenario_enum = AccuracyTestScenario(scenario)

        test_id = await service.create_accuracy_test(
            tenant_id=tenant_id,
            name=name,
            scenario=scenario_enum,
            test_data_source=test_data_source,
            description=description,
            category_schema_id=category_schema_id,
            sample_size=sample_size,
        )

        return {
            "success": True,
            "test_id": test_id,
            "name": name,
            "scenario": scenario,
            "test_data_source": test_data_source,
            "sample_size": sample_size,
            "status": "pending",
            "message": f"Accuracy test '{name}' created successfully",
        }

    except Exception as e:
        logger.error(f"Failed to create accuracy test: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "message": "Failed to create accuracy test",
        }


async def run_accuracy_test_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    test_id: int,
) -> Dict[str, Any]:
    """
    ADK tool function to execute an accuracy test and get comprehensive results.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        test_id: Test ID to execute

    Returns:
        Complete test execution results with metrics and analysis
    """
    try:
        service = AccuracyMeasurementService(db)

        results = await service.run_accuracy_test(test_id, tenant_id)

        return {
            "success": True,
            "test_id": test_id,
            "execution_results": results,
            "status": results.get("status", "completed"),
            "summary": results.get("summary", {}),
            "metrics": results.get("metrics", {}),
            "total_transactions": results.get("results", {}).get(
                "total_transactions", 0
            ),
            "successful_categorizations": results.get("results", {}).get(
                "successful_categorizations", 0
            ),
            "ai_judge_accuracy": results.get("summary", {}).get("ai_judge_accuracy", 0),
            "overall_f1_score": results.get("summary", {}).get("overall_f1_score", 0),
        }

    except Exception as e:
        logger.error(f"Failed to run accuracy test {test_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "test_id": test_id,
            "status": "failed",
        }


async def get_accuracy_test_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    test_id: int,
) -> Dict[str, Any]:
    """
    ADK tool function to get accuracy test details and status.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        test_id: Test ID

    Returns:
        Test details with current status and configuration
    """
    try:
        service = AccuracyMeasurementService(db)

        test = await service.get_accuracy_test(test_id, tenant_id)

        if not test:
            return {
                "success": False,
                "error": f"Test {test_id} not found",
                "test_id": test_id,
            }

        return {
            "success": True,
            "test_id": test.id,
            "name": test.name,
            "description": test.description,
            "scenario": test.scenario.value,
            "status": test.status.value,
            "test_data_source": test.test_data_source,
            "sample_size": test.sample_size,
            "category_schema_id": test.category_schema_id,
            "created_at": test.created_at.isoformat() if test.created_at else None,
            "completed_at": test.completed_at.isoformat()
            if test.completed_at
            else None,
            "total_transactions": test.total_transactions,
            "successful_categorizations": test.successful_categorizations,
            "accuracy_percentage": float(test.accuracy_percentage)
            if test.accuracy_percentage
            else None,
            "precision": float(test.precision) if test.precision else None,
            "recall": float(test.recall) if test.recall else None,
            "f1_score": float(test.f1_score) if test.f1_score else None,
        }

    except Exception as e:
        logger.error(f"Failed to get accuracy test {test_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "test_id": test_id,
        }


async def list_accuracy_tests_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    limit: int = 50,
    offset: int = 0,
    scenario: Optional[str] = None,
    status: Optional[str] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to list accuracy tests with optional filtering.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        limit: Maximum number of tests to return
        offset: Number of tests to skip
        scenario: Optional scenario filter
        status: Optional status filter

    Returns:
        List of accuracy tests with summary information
    """
    try:
        service = AccuracyMeasurementService(db)

        # Convert string filters to enums if provided
        scenario_enum = AccuracyTestScenario(scenario) if scenario else None
        status_enum = AccuracyTestStatus(status) if status else None

        tests = await service.list_accuracy_tests(
            tenant_id=tenant_id,
            limit=limit,
            offset=offset,
            scenario=scenario_enum,
            status=status_enum,
        )

        return {
            "success": True,
            "tests": [
                {
                    "test_id": test.id,
                    "name": test.name,
                    "scenario": test.scenario.value,
                    "status": test.status.value,
                    "created_at": test.created_at.isoformat()
                    if test.created_at
                    else None,
                    "total_transactions": test.total_transactions,
                    "accuracy_percentage": float(test.accuracy_percentage)
                    if test.accuracy_percentage
                    else None,
                    "f1_score": float(test.f1_score) if test.f1_score else None,
                }
                for test in tests
            ],
            "count": len(tests),
            "filters": {
                "scenario": scenario,
                "status": status,
                "limit": limit,
                "offset": offset,
            },
        }

    except Exception as e:
        logger.error(f"Failed to list accuracy tests: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "tests": [],
            "count": 0,
        }


async def get_test_results_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    test_id: int,
) -> Dict[str, Any]:
    """
    ADK tool function to get detailed test results and metrics.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        test_id: Test ID

    Returns:
        Detailed test results with all metrics and analysis
    """
    try:
        service = AccuracyMeasurementService(db)

        results = await service.get_test_results(test_id, tenant_id)

        return {
            "success": True,
            "test_id": test_id,
            "results": results,
            "status": results.get("status"),
            "performance_summary": {
                "total_transactions": results.get("total_transactions", 0),
                "successful_categorizations": results.get(
                    "successful_categorizations", 0
                ),
                "accuracy_percentage": results.get("accuracy_percentage", 0),
                "precision": results.get("precision", 0),
                "recall": results.get("recall", 0),
                "f1_score": results.get("f1_score", 0),
            },
            "detailed_metrics": results.get("metrics", []),
        }

    except Exception as e:
        logger.error(f"Failed to get test results for {test_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "test_id": test_id,
        }


async def validate_ml_performance_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    performance_threshold: float = 0.8,
    include_recent_tests: bool = True,
) -> Dict[str, Any]:
    """
    ADK tool function to validate current ML performance against thresholds.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        performance_threshold: Minimum acceptable F1 score
        include_recent_tests: Include recent test results in analysis

    Returns:
        ML performance validation results with recommendations
    """
    try:
        service = AccuracyMeasurementService(db)

        # Get recent completed tests
        recent_tests = await service.list_accuracy_tests(
            tenant_id=tenant_id,
            limit=10,
            status=AccuracyTestStatus.COMPLETED,
        )

        if not recent_tests:
            return {
                "success": True,
                "performance_status": "no_data",
                "message": "No completed accuracy tests found",
                "recommendations": [
                    "Run accuracy tests to establish ML performance baseline",
                    "Create tests for all three scenarios: historical_data, schema_only, zero_onboarding",
                ],
            }

        # Calculate average performance metrics
        total_f1 = sum(float(test.f1_score) for test in recent_tests if test.f1_score)
        total_accuracy = sum(
            float(test.accuracy_percentage)
            for test in recent_tests
            if test.accuracy_percentage
        )
        test_count = len(recent_tests)

        avg_f1_score = total_f1 / test_count if test_count > 0 else 0.0
        avg_accuracy = total_accuracy / test_count if test_count > 0 else 0.0

        # Determine performance status
        meets_threshold = avg_f1_score >= performance_threshold
        performance_status = "passing" if meets_threshold else "failing"

        # Generate recommendations
        recommendations = []
        if not meets_threshold:
            recommendations.extend(
                [
                    f"Current F1 score ({avg_f1_score:.3f}) is below threshold ({performance_threshold})",
                    "Consider retraining with additional historical data",
                    "Review category schema for clarity and consistency",
                ]
            )

        if avg_accuracy < 85:
            recommendations.append(
                "Overall accuracy below 85% - investigate categorization patterns"
            )

        # Check for scenario-specific performance
        scenario_performance = {}
        for scenario in [
            AccuracyTestScenario.HISTORICAL_DATA,
            AccuracyTestScenario.SCHEMA_ONLY,
            AccuracyTestScenario.ZERO_ONBOARDING,
        ]:
            scenario_tests = [t for t in recent_tests if t.scenario == scenario]
            if scenario_tests:
                scenario_avg = sum(
                    float(t.f1_score) for t in scenario_tests if t.f1_score
                ) / len(scenario_tests)
                scenario_performance[scenario.value] = scenario_avg

                if scenario_avg < performance_threshold:
                    recommendations.append(
                        f"Low performance in {scenario.value} scenario - needs attention"
                    )

        return {
            "success": True,
            "performance_status": performance_status,
            "meets_threshold": meets_threshold,
            "performance_threshold": performance_threshold,
            "average_metrics": {
                "f1_score": avg_f1_score,
                "accuracy_percentage": avg_accuracy,
                "test_count": test_count,
            },
            "scenario_performance": scenario_performance,
            "recent_tests": [
                {
                    "test_id": test.id,
                    "name": test.name,
                    "scenario": test.scenario.value,
                    "f1_score": float(test.f1_score) if test.f1_score else 0.0,
                    "accuracy_percentage": float(test.accuracy_percentage)
                    if test.accuracy_percentage
                    else 0.0,
                }
                for test in recent_tests
            ],
            "recommendations": recommendations,
        }

    except Exception as e:
        logger.error(f"Failed to validate ML performance: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "performance_status": "error",
        }


async def analyze_categorization_patterns_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    test_id: Optional[int] = None,
    scenario: Optional[str] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to analyze categorization patterns and identify improvement opportunities.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        test_id: Optional specific test to analyze
        scenario: Optional scenario to focus analysis on

    Returns:
        Categorization pattern analysis with insights and recommendations
    """
    try:
        service = AccuracyMeasurementService(db)

        # Get target tests for analysis
        if test_id:
            # Analyze specific test
            test = await service.get_accuracy_test(test_id, tenant_id)
            if not test:
                return {
                    "success": False,
                    "error": f"Test {test_id} not found",
                }
            tests = [test]
        else:
            # Get all completed tests, optionally filtered by scenario
            scenario_enum = AccuracyTestScenario(scenario) if scenario else None
            tests = await service.list_accuracy_tests(
                tenant_id=tenant_id,
                limit=20,
                status=AccuracyTestStatus.COMPLETED,
                scenario=scenario_enum,
            )

        if not tests:
            return {
                "success": True,
                "analysis": "no_data",
                "message": "No completed tests found for analysis",
            }

        # Analyze patterns across tests
        patterns = {
            "performance_trends": {},
            "common_errors": [],
            "scenario_comparison": {},
            "improvement_opportunities": [],
        }

        # Group by scenario for comparison
        for test in tests:
            scenario_key = test.scenario.value
            if scenario_key not in patterns["scenario_comparison"]:
                patterns["scenario_comparison"][scenario_key] = {
                    "test_count": 0,
                    "avg_f1_score": 0.0,
                    "avg_accuracy": 0.0,
                    "tests": [],
                }

            scenario_data = patterns["scenario_comparison"][scenario_key]
            scenario_data["test_count"] += 1
            scenario_data["tests"].append(
                {
                    "test_id": test.id,
                    "name": test.name,
                    "f1_score": float(test.f1_score) if test.f1_score else 0.0,
                    "accuracy": float(test.accuracy_percentage)
                    if test.accuracy_percentage
                    else 0.0,
                }
            )

        # Calculate averages and identify patterns
        for _scenario_key, data in patterns["scenario_comparison"].items():
            if data["tests"]:
                data["avg_f1_score"] = sum(t["f1_score"] for t in data["tests"]) / len(
                    data["tests"]
                )
                data["avg_accuracy"] = sum(t["accuracy"] for t in data["tests"]) / len(
                    data["tests"]
                )

        # Generate improvement opportunities
        best_scenario = max(
            patterns["scenario_comparison"].items(),
            key=lambda x: x[1]["avg_f1_score"],
            default=None,
        )
        worst_scenario = min(
            patterns["scenario_comparison"].items(),
            key=lambda x: x[1]["avg_f1_score"],
            default=None,
        )

        if best_scenario and worst_scenario:
            if (
                best_scenario[1]["avg_f1_score"] - worst_scenario[1]["avg_f1_score"]
                > 0.1
            ):
                patterns["improvement_opportunities"].append(
                    f"Significant performance gap between {best_scenario[0]} ({best_scenario[1]['avg_f1_score']:.3f}) "
                    f"and {worst_scenario[0]} ({worst_scenario[1]['avg_f1_score']:.3f})"
                )

        # Add general recommendations
        if any(
            data["avg_f1_score"] < 0.8
            for data in patterns["scenario_comparison"].values()
        ):
            patterns["improvement_opportunities"].append(
                "Some scenarios below 80% F1 score - consider targeted improvements"
            )

        return {
            "success": True,
            "analysis": "completed",
            "patterns": patterns,
            "summary": {
                "tests_analyzed": len(tests),
                "scenarios_covered": list(patterns["scenario_comparison"].keys()),
                "overall_performance": {
                    "best_scenario": best_scenario[0] if best_scenario else None,
                    "best_f1_score": best_scenario[1]["avg_f1_score"]
                    if best_scenario
                    else 0.0,
                    "worst_scenario": worst_scenario[0] if worst_scenario else None,
                    "worst_f1_score": worst_scenario[1]["avg_f1_score"]
                    if worst_scenario
                    else 0.0,
                },
            },
        }

    except Exception as e:
        logger.error(f"Failed to analyze categorization patterns: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "analysis": "error",
        }
