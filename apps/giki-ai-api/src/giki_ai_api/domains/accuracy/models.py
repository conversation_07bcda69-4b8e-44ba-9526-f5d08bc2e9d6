"""
Accuracy measurement domain models.

Pydantic models for accuracy testing, metrics calculation, and AI judgment evaluation.
"""

import datetime
from enum import Enum
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class AccuracyTestScenario(str, Enum):
    """Categorization accuracy test scenarios."""

    HISTORICAL_DATA = (
        "historical_data"  # Scenario 1: RAG-based with historical transactions
    )
    SCHEMA_ONLY = "schema_only"  # Scenario 2: Category schema without transactions
    ZERO_ONBOARDING = "zero_onboarding"  # Scenario 3: No schema, no historical data


class AccuracyTestStatus(str, Enum):
    """Status of accuracy test execution."""

    PENDING = "pending"  # Test created but not started
    RUNNING = "running"  # Test in progress
    COMPLETED = "completed"  # Test finished successfully
    FAILED = "failed"  # Test encountered errors
    CANCELLED = "cancelled"  # Test cancelled by user


class AIJudgmentResult(str, Enum):
    """AI judge evaluation of categorization correctness."""

    CORRECT = "correct"  # AI categorization matches expected/original
    INCORRECT = "incorrect"  # AI categorization does not match
    PARTIALLY_CORRECT = "partially_correct"  # Some elements match
    INDETERMINATE = "indeterminate"  # Cannot determine correctness


class AccuracyTest(BaseModel):
    """
    Represents an accuracy test configuration and execution.

    Tests measure categorization quality across three scenarios using
    precision, recall, F1-score, and AI judge evaluation.
    """

    id: int
    tenant_id: int
    name: str = Field(..., max_length=200)
    description: Optional[str] = Field(None, max_length=1000)

    # Test configuration
    scenario: AccuracyTestScenario
    test_data_source: str = Field(..., max_length=500)  # File path or data identifier
    category_schema_id: Optional[int] = None  # For schema_only scenario
    sample_size: int = Field(
        default=100, ge=1, le=10000
    )  # Number of transactions to test

    # Execution tracking
    status: AccuracyTestStatus = AccuracyTestStatus.PENDING
    created_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    started_at: Optional[datetime.datetime] = None
    completed_at: Optional[datetime.datetime] = None

    # Results summary
    total_transactions: int = Field(default=0)
    successful_categorizations: int = Field(default=0)
    ai_judge_correct: int = Field(default=0)
    ai_judge_incorrect: int = Field(default=0)
    ai_judge_partially_correct: int = Field(default=0)

    # Calculated metrics
    precision: Optional[float] = Field(None, ge=0.0, le=1.0)
    recall: Optional[float] = Field(None, ge=0.0, le=1.0)
    f1_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    accuracy_percentage: Optional[float] = Field(None, ge=0.0, le=100.0)

    # Error tracking
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None


class AccuracyMetric(BaseModel):
    """
    Individual accuracy metrics for a specific test.

    Stores detailed metrics calculations and breakdowns by category,
    hierarchy level, and confidence score ranges.
    """

    id: int
    test_id: int

    # Metric identification
    metric_name: str = Field(
        ..., max_length=100
    )  # "overall", "category_X", "level_1", etc.
    metric_type: str = Field(
        ..., max_length=50
    )  # "precision", "recall", "f1_score", "accuracy"

    # Metric values
    value: float = Field(..., ge=0.0, le=1.0)
    sample_count: int = Field(..., ge=0)

    # Breakdown details
    true_positives: int = Field(default=0)
    false_positives: int = Field(default=0)
    true_negatives: int = Field(default=0)
    false_negatives: int = Field(default=0)

    # Categorization context
    category_filter: Optional[str] = None  # Specific category for filtered metrics
    confidence_range: Optional[str] = None  # "0.0-0.5", "0.5-0.8", "0.8-1.0"
    hierarchy_level: Optional[int] = None  # Hierarchy level for level-specific metrics

    # Metadata
    calculated_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    calculation_method: str = Field(
        ..., max_length=100
    )  # Description of calculation method


class AIJudgment(BaseModel):
    """
    AI judge evaluation of individual categorization correctness.

    Stores detailed judgment reasoning and confidence for each
    transaction categorization evaluated by the AI judge.
    """

    id: int
    test_id: int
    transaction_id: str = Field(..., max_length=100)  # Reference to transaction

    # Original vs AI categorization
    original_category: Optional[str] = Field(None, max_length=500)
    ai_category: str = Field(..., max_length=500)
    ai_full_hierarchy: Optional[str] = Field(None, max_length=1000)
    ai_confidence: float = Field(..., ge=0.0, le=1.0)

    # AI judge evaluation
    judgment_result: AIJudgmentResult
    judgment_confidence: float = Field(..., ge=0.0, le=1.0)
    judgment_reasoning: Optional[str] = Field(None, max_length=2000)

    # Detailed comparison
    category_exact_match: bool = Field(default=False)
    category_semantic_match: bool = Field(default=False)
    hierarchy_level_matches: Optional[Dict[str, bool]] = (
        None  # Level-by-level comparison
    )
    semantic_similarity: Optional[float] = Field(None, ge=0.0, le=1.0)
    hierarchy_alignment: Optional[bool] = None
    key_factors: Optional[str] = None  # JSON string of key judgment factors
    improvement_suggestions: Optional[str] = None

    # Transaction context
    transaction_description: str = Field(..., max_length=1000)
    transaction_amount: float
    transaction_type: str = Field(..., max_length=20)  # "debit", "credit"

    # Evaluation metadata
    judged_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    judge_model: str = Field(..., max_length=100)  # AI model used for judgment
    judge_version: str = Field(default="1.0", max_length=20)


class CategorySchema(BaseModel):
    """
    Imported category schema for scenario 2 testing.

    Stores category hierarchies imported from Excel, CSV, JSON, or other formats
    for use in schema-only categorization testing.
    """

    id: int
    tenant_id: int
    name: str = Field(..., max_length=200)
    description: Optional[str] = Field(None, max_length=1000)

    # Schema content
    schema_format: str = Field(..., max_length=50)  # "excel", "csv", "json", "yaml"
    schema_data: Dict[str, Any]  # Parsed category hierarchy structure
    category_count: int = Field(..., ge=0)
    max_hierarchy_depth: int = Field(..., ge=1)

    # GL code mapping
    has_gl_codes: bool = Field(default=False)
    gl_code_mapping: Optional[Dict[str, str]] = None

    # Import metadata
    imported_from: str = Field(..., max_length=500)  # Original file name or source
    imported_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    imported_by: Optional[str] = Field(None, max_length=100)  # User identifier

    # Usage tracking
    is_active: bool = Field(default=True)
    last_used_at: Optional[datetime.datetime] = None
    usage_count: int = Field(default=0)


class AccuracyTestSummary(BaseModel):
    """
    Summary view of accuracy test results for dashboard display.

    Provides high-level metrics and insights for quick assessment
    of categorization quality across different scenarios.
    """

    test_id: int
    test_name: str
    scenario: AccuracyTestScenario
    status: AccuracyTestStatus

    # Key metrics
    overall_accuracy: Optional[float] = Field(None, ge=0.0, le=100.0)
    precision: Optional[float] = Field(None, ge=0.0, le=1.0)
    recall: Optional[float] = Field(None, ge=0.0, le=1.0)
    f1_score: Optional[float] = Field(None, ge=0.0, le=1.0)

    # Sample size and success rate
    total_transactions: int
    successful_categorizations: int
    success_rate: float = Field(..., ge=0.0, le=100.0)

    # AI judge summary
    ai_judge_accuracy: Optional[float] = Field(None, ge=0.0, le=100.0)
    ai_judge_confidence_avg: Optional[float] = Field(None, ge=0.0, le=1.0)

    # Quality indicators
    non_generic_categories: int = Field(default=0)
    hierarchical_categories: int = Field(default=0)
    categories_with_gl_codes: int = Field(default=0)

    # Timing
    execution_duration_seconds: Optional[float] = None
    completed_at: Optional[datetime.datetime] = None

    # Comparison context
    previous_test_accuracy: Optional[float] = None  # For trend analysis
    improvement_percentage: Optional[float] = None  # Improvement over previous test
