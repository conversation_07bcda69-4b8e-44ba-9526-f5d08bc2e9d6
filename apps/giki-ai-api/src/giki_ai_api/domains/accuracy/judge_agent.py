"""
AI Judge Agent for evaluating categorization correctness.

This agent evaluates whether AI-generated transaction categorizations
are correct, incorrect, or partially correct compared to expected results.
"""

import logging
import re
from pathlib import Path
from typing import Any, Dict, Optional

import yaml
from google.cloud import aiplatform
from google.cloud.aiplatform_v1beta1.types import content as aiplatform_content
from google.oauth2 import service_account

from ...core.config import settings
from ...shared.ai.standard_giki_agent import StandardGikiAgent
from .models import AIJudgmentResult

logger = logging.getLogger(__name__)


class AIJudgeAgent(StandardGikiAgent):
    """
    AI Judge Agent for evaluating categorization correctness.

    Inherits from StandardGikiAgent and provides specialized evaluation
    of transaction categorization quality using sophisticated AI prompts.
    """

    def __init__(
        self,
        name: str = "ai_judge_agent",
        description: str = "AI agent for evaluating transaction categorization correctness",
        model_name: str = "gemini-2.0-flash-001",
        project: str = None,
        location: str = "us-central1",
    ):
        """Initialize AI Judge Agent with evaluation capabilities."""
        super().__init__(
            name=name,
            description=description,
            model=model_name,
            tools=[],  # AI Judge doesn't need external tools
        )

        self.model_name = model_name
        self.project = project or settings.VERTEX_PROJECT_ID
        self.location = location
        self.client = None
        self.prompt_template = None
        self.system_instruction = None
        self.model_params = {}

        # Load AI judge prompts
        self._load_judge_prompts()

    def _load_judge_prompts(self):
        """Load AI judge prompts from YAML file."""
        try:
            prompts_dir = Path(__file__).parent / "prompts"
            prompt_file = prompts_dir / "ai_judge.yaml"

            if not prompt_file.exists():
                logger.error(f"AI judge prompt file not found: {prompt_file}")
                raise FileNotFoundError(
                    f"AI judge prompt file not found: {prompt_file}"
                )

            with open(prompt_file, "r", encoding="utf-8") as f:
                prompt_data = yaml.safe_load(f)

            self.system_instruction = prompt_data.get("system_instruction", "")
            self.prompt_template = prompt_data.get("user_prompt_template", "")
            self.model_params = prompt_data.get("model_params", {})

            logger.info("AI judge prompts loaded successfully")

        except Exception as e:
            logger.error(f"Failed to load AI judge prompts: {e}")
            raise

    async def initialize_vertex_ai(self):
        """Initialize Vertex AI client for AI judge evaluation."""
        try:
            if self.client:
                return  # Already initialized

            # Initialize Vertex AI
            if settings.GOOGLE_APPLICATION_CREDENTIALS:
                credentials = service_account.Credentials.from_service_account_file(
                    settings.GOOGLE_APPLICATION_CREDENTIALS
                )
                aiplatform.init(
                    project=self.project,
                    location=self.location,
                    credentials=credentials,
                )
            else:
                aiplatform.init(project=self.project, location=self.location)

            self.client = aiplatform.gapic.PredictionServiceClient()
            logger.info("AI judge Vertex AI client initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Vertex AI for AI judge: {e}")
            raise

    async def evaluate_categorization(
        self,
        transaction_description: str,
        amount: float,
        transaction_type: str,
        transaction_date: str,
        original_category: Optional[str],
        ai_category: str,
        ai_hierarchy: Optional[str] = None,
        ai_confidence: float = 0.0,
    ) -> Dict[str, Any]:
        """
        Evaluate whether AI categorization is correct.

        Args:
            transaction_description: Original transaction description
            amount: Transaction amount
            transaction_type: "debit" or "credit"
            transaction_date: Transaction date in ISO format
            original_category: Expected/original category (if available)
            ai_category: AI-generated category
            ai_hierarchy: Full AI-generated hierarchy
            ai_confidence: AI confidence score

        Returns:
            Dict containing judgment result, confidence, reasoning, and analysis
        """
        try:
            await self.initialize_vertex_ai()

            # Format prompt with transaction details
            user_prompt = self.prompt_template.format(
                transaction_description=transaction_description,
                amount=amount,
                transaction_type=transaction_type,
                transaction_date=transaction_date,
                original_category=original_category or "Not provided",
                ai_category=ai_category,
                ai_hierarchy=ai_hierarchy or ai_category,
                ai_confidence=ai_confidence,
            )

            # Call Vertex AI for judgment
            response = await self._call_vertex_ai(user_prompt)

            # Parse AI judge response
            judgment_data = self._parse_judgment_response(response)

            logger.info(
                f"AI judge evaluation: {judgment_data['judgment_result']} (confidence: {judgment_data['judgment_confidence']:.2f})"
            )

            return judgment_data

        except Exception as e:
            logger.error(f"Failed to evaluate categorization: {e}")
            # Return default judgment on error
            return {
                "judgment_result": AIJudgmentResult.INDETERMINATE,
                "judgment_confidence": 0.0,
                "judgment_reasoning": f"Evaluation failed: {str(e)}",
                "category_exact_match": False,
                "category_semantic_match": False,
                "hierarchy_level_matches": {},
            }

    async def _call_vertex_ai(self, user_prompt: str) -> str:
        """Call Vertex AI for AI judge evaluation."""
        try:
            # Create the model endpoint
            endpoint = f"projects/{self.project}/locations/{self.location}/publishers/google/models/{self.model_name}"

            # Prepare the content
            system_content = aiplatform_content.Content(
                role="system",
                parts=[aiplatform_content.Part(text=self.system_instruction)],
            )

            user_content = aiplatform_content.Content(
                role="user", parts=[aiplatform_content.Part(text=user_prompt)]
            )

            # Prepare generation config
            generation_config = aiplatform_content.GenerationConfig(
                temperature=self.model_params.get("temperature", 0.1),
                max_output_tokens=self.model_params.get("max_tokens", 2000),
                top_p=self.model_params.get("top_p", 0.95),
            )

            # Make the request
            request = aiplatform_content.GenerateContentRequest(
                model=endpoint,
                contents=[system_content, user_content],
                generation_config=generation_config,
            )

            response = await self.client.generate_content(request)

            if response.candidates:
                return response.candidates[0].content.parts[0].text
            else:
                raise Exception("No response candidates from Vertex AI")

        except Exception as e:
            logger.error(f"Vertex AI call failed: {e}")
            raise

    def _parse_judgment_response(self, response: str) -> Dict[str, Any]:
        """Parse AI judge response and extract structured judgment data."""
        try:
            # Initialize default values
            judgment_data = {
                "judgment_result": AIJudgmentResult.INDETERMINATE,
                "judgment_confidence": 0.0,
                "judgment_reasoning": response,
                "category_exact_match": False,
                "category_semantic_match": False,
                "business_logic_sound": False,
                "hierarchy_appropriate": False,
                "hierarchy_level_matches": {},
            }

            # Extract structured fields using regex
            patterns = {
                "judgment": r"JUDGMENT:\s*(CORRECT|INCORRECT|PARTIALLY_CORRECT)",
                "confidence": r"CONFIDENCE:\s*([\d.]+)",
                "reasoning": r"REASONING:\s*(.+?)(?=\n[A-Z_]+:|$)",
                "exact_match": r"EXACT_MATCH:\s*(true|false)",
                "semantic_match": r"SEMANTIC_MATCH:\s*(true|false)",
                "business_logic": r"BUSINESS_LOGIC_SOUND:\s*(true|false)",
                "hierarchy": r"HIERARCHY_APPROPRIATE:\s*(true|false)",
            }

            for field, pattern in patterns.items():
                match = re.search(pattern, response, re.IGNORECASE | re.DOTALL)
                if match:
                    value = match.group(1).strip()

                    if field == "judgment":
                        if value.upper() == "CORRECT":
                            judgment_data["judgment_result"] = AIJudgmentResult.CORRECT
                        elif value.upper() == "INCORRECT":
                            judgment_data["judgment_result"] = (
                                AIJudgmentResult.INCORRECT
                            )
                        elif value.upper() == "PARTIALLY_CORRECT":
                            judgment_data["judgment_result"] = (
                                AIJudgmentResult.PARTIALLY_CORRECT
                            )

                    elif field == "confidence":
                        try:
                            judgment_data["judgment_confidence"] = float(value)
                        except ValueError:
                            judgment_data["judgment_confidence"] = 0.0

                    elif field == "reasoning":
                        judgment_data["judgment_reasoning"] = value

                    elif field == "exact_match":
                        judgment_data["category_exact_match"] = value.lower() == "true"

                    elif field == "semantic_match":
                        judgment_data["category_semantic_match"] = (
                            value.lower() == "true"
                        )

                    elif field == "business_logic":
                        judgment_data["business_logic_sound"] = value.lower() == "true"

                    elif field == "hierarchy":
                        judgment_data["hierarchy_appropriate"] = value.lower() == "true"

            return judgment_data

        except Exception as e:
            logger.error(f"Failed to parse judgment response: {e}")
            # Return default parsing with full response as reasoning
            return {
                "judgment_result": AIJudgmentResult.INDETERMINATE,
                "judgment_confidence": 0.0,
                "judgment_reasoning": f"Failed to parse response: {response}",
                "category_exact_match": False,
                "category_semantic_match": False,
                "business_logic_sound": False,
                "hierarchy_appropriate": False,
                "hierarchy_level_matches": {},
            }

    async def batch_evaluate_categorizations(
        self, categorizations: list[Dict[str, Any]]
    ) -> list[Dict[str, Any]]:
        """Evaluate multiple categorizations in batch for efficiency."""
        try:
            results = []

            for i, cat_data in enumerate(categorizations):
                logger.info(f"Evaluating categorization {i + 1}/{len(categorizations)}")

                result = await self.evaluate_categorization(
                    transaction_description=cat_data["transaction_description"],
                    amount=cat_data["amount"],
                    transaction_type=cat_data["transaction_type"],
                    transaction_date=cat_data.get("transaction_date", ""),
                    original_category=cat_data.get("original_category"),
                    ai_category=cat_data["ai_category"],
                    ai_hierarchy=cat_data.get("ai_hierarchy"),
                    ai_confidence=cat_data.get("ai_confidence", 0.0),
                )

                results.append(result)

            logger.info(
                f"Completed batch evaluation of {len(categorizations)} categorizations"
            )
            return results

        except Exception as e:
            logger.error(f"Batch evaluation failed: {e}")
            raise

    def get_evaluation_summary(
        self, evaluations: list[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate summary statistics from evaluation results."""
        try:
            total = len(evaluations)
            if total == 0:
                return {
                    "total": 0,
                    "correct": 0,
                    "incorrect": 0,
                    "partially_correct": 0,
                    "accuracy_rate": 0.0,
                }

            correct = sum(
                1
                for eval_result in evaluations
                if eval_result["judgment_result"] == AIJudgmentResult.CORRECT
            )
            incorrect = sum(
                1
                for eval_result in evaluations
                if eval_result["judgment_result"] == AIJudgmentResult.INCORRECT
            )
            partially_correct = sum(
                1
                for eval_result in evaluations
                if eval_result["judgment_result"] == AIJudgmentResult.PARTIALLY_CORRECT
            )
            indeterminate = total - correct - incorrect - partially_correct

            # Calculate accuracy rate (correct + 50% of partially correct)
            accuracy_rate = (correct + partially_correct * 0.5) / total * 100

            # Calculate average confidence
            avg_confidence = (
                sum(eval_result["judgment_confidence"] for eval_result in evaluations)
                / total
            )

            return {
                "total": total,
                "correct": correct,
                "incorrect": incorrect,
                "partially_correct": partially_correct,
                "indeterminate": indeterminate,
                "accuracy_rate": accuracy_rate,
                "average_confidence": avg_confidence,
                "exact_matches": sum(
                    1
                    for eval_result in evaluations
                    if eval_result["category_exact_match"]
                ),
                "semantic_matches": sum(
                    1
                    for eval_result in evaluations
                    if eval_result["category_semantic_match"]
                ),
            }

        except Exception as e:
            logger.error(f"Failed to generate evaluation summary: {e}")
            return {"error": str(e)}
