"""
ADK Agent Router - Google Agent Development Kit Integration

Provides endpoints for agent discovery, session management, memory operations,
tool invocation, and A2A protocol support following ADK v1.3.0 patterns.
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from ...core.database import get_db_session
from ...core.dependencies import (
    get_current_tenant_id,
)
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/adk", tags=["ADK Agents"])


# Request/Response Models
class ADKAgent(BaseModel):
    id: str
    name: str
    type: str
    status: str = "available"  # available, busy, offline, error
    capabilities: List[str]
    currentTask: Optional[str] = None
    lastActivity: str
    memorySize: int
    toolsAvailable: List[str]


class AgentSession(BaseModel):
    id: str
    agentId: str
    conversationId: str
    memoryContext: Dict[str, Any]
    startTime: str
    lastInteraction: str
    isActive: bool


class StartSessionRequest(BaseModel):
    preloadMemory: bool = True
    contextSize: str = "standard"


class AgentTransferRequest(BaseModel):
    fromAgentId: str
    toAgentId: str
    context: Dict[str, Any]
    reason: str
    preserveMemory: bool = True


class PreloadMemoryRequest(BaseModel):
    contextData: Dict[str, Any]


class ToolInvocationRequest(BaseModel):
    toolName: str
    parameters: Dict[str, Any]
    agentId: str
    sessionId: Optional[str] = None


class NetworkStatus(BaseModel):
    totalAgents: int
    availableAgents: int
    busyAgents: int
    offlineAgents: int
    networkHealth: str  # healthy, degraded, offline


# Agent registry (in production, this would be in a database)
AVAILABLE_AGENTS = [
    {
        "id": "gl-code-agent",
        "name": "GL Code Agent",
        "type": "gl_code",
        "status": "available",
        "capabilities": [
            "assign_gl_codes",
            "create_category_hierarchy",
            "map_to_23_toplevel_structure",
            "calculate_rollup_values",
            "export_chart_of_accounts",
            "validate_gl_code_uniqueness",
        ],
        "lastActivity": datetime.now(timezone.utc).isoformat(),
        "memorySize": 1024,
        "toolsAvailable": [
            "load_memory",
            "preload_memory",
            "transfer_to_agent",
            "assign_gl_code_tool",
            "create_category_hierarchy_tool",
        ],
    },
    {
        "id": "debit-credit-agent",
        "name": "Debit Credit Agent",
        "type": "debit_credit",
        "status": "available",
        "capabilities": [
            "infer_transaction_type",
            "validate_accounting_rules",
            "detect_anomalies",
            "process_amount_fields",
            "determine_cash_flow_direction",
        ],
        "lastActivity": datetime.now(timezone.utc).isoformat(),
        "memorySize": 512,
        "toolsAvailable": [
            "load_artifacts",
            "openapi_tool",
            "vertex_ai_search_tool",
            "infer_debit_credit_logic",
        ],
    },
    {
        "id": "reports-agent",
        "name": "Reports Agent",
        "type": "reports",
        "status": "available",
        "capabilities": [
            "generate_reports",
            "financial_analysis",
            "export_data",
            "create_visualizations",
            "generate_insights",
        ],
        "currentTask": None,
        "lastActivity": datetime.now(timezone.utc).isoformat(),
        "memorySize": 2048,
        "toolsAvailable": [
            "apihub_tool",
            "google_search_tool",
            "LongRunningFunctionTool",
            "generate_spending_report_tool",
            "create_visualization_tool",
        ],
    },
    {
        "id": "categorization-agent",
        "name": "Categorization Agent",
        "type": "categorization",
        "status": "available",
        "capabilities": [
            "lookup_transaction_category",
            "batch_lookup_categorization",
            "search_historical_patterns",
            "update_lookup_corpus",
            "validate_category_accuracy",
        ],
        "lastActivity": datetime.now(timezone.utc).isoformat(),
        "memorySize": 1536,
        "toolsAvailable": [
            "lookup_transaction_category",
            "batch_lookup_categorization",
            "search_historical_patterns",
            "vertex_ai_search_tool",
        ],
    },
    {
        "id": "coordinator-agent",
        "name": "Coordinator Agent",
        "type": "coordinator",
        "status": "available",
        "capabilities": [
            "orchestrate_workflows",
            "manage_agent_handoffs",
            "optimize_resources",
            "route_query_to_agent",
            "coordinate_multi_agent_task",
        ],
        "lastActivity": datetime.now(timezone.utc).isoformat(),
        "memorySize": 4096,
        "toolsAvailable": [
            "transfer_to_agent",
            "exit_loop_tool",
            "get_user_choice",
            "route_query_to_agent",
            "coordinate_multi_agent_task",
        ],
    },
    {
        "id": "customer-agent",
        "name": "Customer Agent",
        "type": "customer",
        "status": "available",
        "capabilities": [
            "ui_equivalence",
            "conversation_handling",
            "user_assistance",
            "process_voice_commands",
            "display_charts_in_chat",
        ],
        "lastActivity": datetime.now(timezone.utc).isoformat(),
        "memorySize": 1536,
        "toolsAvailable": [
            "load_memory",
            "transfer_to_agent",
            "get_user_choice_tool",
            "upload_files_via_chat",
            "navigate_user_to_page",
        ],
    },
]

# In-memory session store (in production, use Redis or database)
ACTIVE_SESSIONS: Dict[str, Dict[str, Any]] = {}
AGENT_MEMORY: Dict[str, Dict[str, Any]] = {}


@router.get("/agents/discover", response_model=List[ADKAgent])
async def discover_agents(
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
):
    """
    Discover available ADK agents in the system.
    Returns a list of all registered agents with their capabilities and status.
    """
    try:
        # Update agent status based on current system state
        for agent in AVAILABLE_AGENTS:
            agent["lastActivity"] = datetime.now(timezone.utc).isoformat()

        return [ADKAgent(**agent) for agent in AVAILABLE_AGENTS]

    except Exception as e:
        logger.error(f"Agent discovery failed for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to discover agents",
        )


@router.post("/agents/{agent_id}/sessions", response_model=AgentSession)
async def start_agent_session(
    agent_id: str,
    request: StartSessionRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Start a new agent session with optional memory preloading.
    Creates a new conversation context for the specified agent.
    """
    try:
        # Verify agent exists
        agent = next((a for a in AVAILABLE_AGENTS if a["id"] == agent_id), None)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_id} not found",
            )

        # Create new session
        session_id = f"session-{tenant_id}-{agent_id}-{int(datetime.now(timezone.utc).timestamp())}"
        conversation_id = f"conv-{tenant_id}-{current_user.id}-{int(datetime.now(timezone.utc).timestamp())}"

        # Initialize memory context
        memory_context = {
            "tenant_id": tenant_id,
            "user_id": current_user.id,
            "user_email": current_user.email,
            "user_name": current_user.full_name,
            "agent_capabilities": agent["capabilities"],
            "context_size": request.contextSize,
            "conversation_history": [],
            "user_preferences": {},
            "artifacts": [],
        }

        # Preload memory if requested
        if request.preloadMemory and agent_id in AGENT_MEMORY:
            memory_context.update(AGENT_MEMORY[agent_id])

        session = {
            "id": session_id,
            "agentId": agent_id,
            "conversationId": conversation_id,
            "memoryContext": memory_context,
            "startTime": datetime.now(timezone.utc).isoformat(),
            "lastInteraction": datetime.now(timezone.utc).isoformat(),
            "isActive": True,
        }

        # Store session
        ACTIVE_SESSIONS[session_id] = session

        # Update agent status
        for a in AVAILABLE_AGENTS:
            if a["id"] == agent_id:
                a["status"] = "busy"
                a["currentTask"] = f"Session with {current_user.full_name}"
                break

        return AgentSession(**session)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start agent session for {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start agent session",
        )


@router.post("/agents/transfer", response_model=AgentSession)
async def transfer_to_agent(
    request: AgentTransferRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Transfer conversation to another agent using A2A protocol.
    Implements opaque agent-to-agent communication with context preservation.
    """
    try:
        # Verify both agents exist
        from_agent = next(
            (a for a in AVAILABLE_AGENTS if a["id"] == request.fromAgentId), None
        )
        to_agent = next(
            (a for a in AVAILABLE_AGENTS if a["id"] == request.toAgentId), None
        )

        if not from_agent or not to_agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="One or both agents not found",
            )

        # End current agent's task
        for a in AVAILABLE_AGENTS:
            if a["id"] == request.fromAgentId:
                a["status"] = "available"
                a["currentTask"] = None
                break

        # Create new session for target agent
        session_id = f"session-{tenant_id}-{request.toAgentId}-{int(datetime.now(timezone.utc).timestamp())}"
        conversation_id = (
            f"conv-transfer-{tenant_id}-{int(datetime.now(timezone.utc).timestamp())}"
        )

        # Transfer context with A2A protocol compliance
        memory_context = {
            "tenant_id": tenant_id,
            "user_id": current_user.id,
            "user_email": current_user.email,
            "user_name": current_user.full_name,
            "agent_capabilities": to_agent["capabilities"],
            "transfer_reason": request.reason,
            "previous_agent": request.fromAgentId,
            "transferred_context": request.context,
            "conversation_history": [],
            "user_preferences": {},
            "artifacts": [],
        }

        # Preserve memory if requested
        if request.preserveMemory and request.fromAgentId in AGENT_MEMORY:
            memory_context["preserved_memory"] = AGENT_MEMORY[request.fromAgentId]

        session = {
            "id": session_id,
            "agentId": request.toAgentId,
            "conversationId": conversation_id,
            "memoryContext": memory_context,
            "startTime": datetime.now(timezone.utc).isoformat(),
            "lastInteraction": datetime.now(timezone.utc).isoformat(),
            "isActive": True,
        }

        # Store new session
        ACTIVE_SESSIONS[session_id] = session

        # Update target agent status
        for a in AVAILABLE_AGENTS:
            if a["id"] == request.toAgentId:
                a["status"] = "busy"
                a["currentTask"] = f"Transferred session from {from_agent['name']}"
                break

        return AgentSession(**session)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to transfer agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to transfer to agent",
        )


@router.get("/agents/{agent_id}/memory")
async def load_agent_memory(
    agent_id: str,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
):
    """
    Load persistent memory for an agent.
    Returns the agent's memory context including conversation history and preferences.
    """
    try:
        # Verify agent exists
        agent = next((a for a in AVAILABLE_AGENTS if a["id"] == agent_id), None)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_id} not found",
            )

        # Return agent memory or default context
        if agent_id in AGENT_MEMORY:
            return AGENT_MEMORY[agent_id]
        else:
            # Return default memory context
            return {
                "conversationHistory": [],
                "userPreferences": {},
                "contextData": {
                    "agent_id": agent_id,
                    "agent_name": agent["name"],
                    "capabilities": agent["capabilities"],
                },
                "artifacts": [],
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to load agent memory for {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load agent memory",
        )


@router.post("/agents/{agent_id}/memory/preload")
async def preload_memory(
    agent_id: str,
    request: PreloadMemoryRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
):
    """
    Preload memory context for faster agent responses.
    Allows pre-warming agent memory with relevant context data.
    """
    try:
        # Verify agent exists
        agent = next((a for a in AVAILABLE_AGENTS if a["id"] == agent_id), None)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_id} not found",
            )

        # Initialize or update agent memory
        if agent_id not in AGENT_MEMORY:
            AGENT_MEMORY[agent_id] = {
                "conversationHistory": [],
                "userPreferences": {},
                "contextData": {},
                "artifacts": [],
            }

        # Update with provided context data
        AGENT_MEMORY[agent_id]["contextData"].update(request.contextData)
        AGENT_MEMORY[agent_id]["lastPreload"] = datetime.now(timezone.utc).isoformat()
        AGENT_MEMORY[agent_id]["preloadedBy"] = current_user.id

        return {"message": f"Memory preloaded successfully for agent {agent_id}"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to preload memory for {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to preload memory",
        )


@router.post("/tools/invoke")
async def invoke_adk_tool(
    request: ToolInvocationRequest,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Invoke advanced ADK tools (load_artifacts, openapi_tool, vertex_ai_search_tool, etc.).
    Executes agent tools with proper authentication and context.
    """
    try:
        # Verify agent exists
        agent = next((a for a in AVAILABLE_AGENTS if a["id"] == request.agentId), None)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {request.agentId} not found",
            )

        # Verify tool is available to agent
        if request.toolName not in agent["toolsAvailable"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Tool {request.toolName} not available to agent {request.agentId}",
            )

        # Execute tool based on type
        result = None

        if request.toolName == "load_artifacts":
            # Simulate loading artifacts
            artifact_ids = request.parameters.get("artifact_ids", [])
            result = [
                {
                    "id": aid,
                    "type": "financial_report",
                    "data": {"content": f"Artifact content for {aid}"},
                    "metadata": {"loaded_at": datetime.now(timezone.utc).isoformat()},
                }
                for aid in artifact_ids
            ]

        elif request.toolName == "vertex_ai_search_tool":
            # Simulate Vertex AI search
            query = request.parameters.get("query", "")
            result = [
                {
                    "relevance": 0.95,
                    "content": f"Search result for: {query}",
                    "source": "financial-knowledge-base",
                }
            ]

        elif request.toolName == "google_search_tool":
            # Simulate Google search
            query = request.parameters.get("query", "")
            result = [
                {
                    "title": f"Search result for: {query}",
                    "snippet": "Financial data and insights...",
                    "url": "https://example.com",
                }
            ]

        elif request.toolName == "get_user_choice":
            # Simulate user choice tool
            request.parameters.get("prompt", "")
            options = request.parameters.get("options", [])
            result = {"choice": options[0] if options else None}

        else:
            # Generic tool execution
            result = {
                "tool": request.toolName,
                "parameters": request.parameters,
                "executed_at": datetime.now(timezone.utc).isoformat(),
                "status": "success",
            }

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to invoke tool {request.toolName}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to invoke tool: {str(e)}",
        )


@router.get("/network/status", response_model=NetworkStatus)
async def get_agent_network_status(
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
):
    """
    Get real-time agent network status.
    Returns overall health and availability of the agent network.
    """
    try:
        # Calculate network statistics
        total_agents = len(AVAILABLE_AGENTS)
        available_agents = len(
            [a for a in AVAILABLE_AGENTS if a["status"] == "available"]
        )
        busy_agents = len([a for a in AVAILABLE_AGENTS if a["status"] == "busy"])
        offline_agents = len([a for a in AVAILABLE_AGENTS if a["status"] == "offline"])

        # Determine network health
        if available_agents >= total_agents * 0.7:
            network_health = "healthy"
        elif available_agents >= total_agents * 0.3:
            network_health = "degraded"
        else:
            network_health = "offline"

        return NetworkStatus(
            totalAgents=total_agents,
            availableAgents=available_agents,
            busyAgents=busy_agents,
            offlineAgents=offline_agents,
            networkHealth=network_health,
        )

    except Exception as e:
        logger.error(f"Failed to get network status: {e}")
        # Return offline status on error
        return NetworkStatus(
            totalAgents=0,
            availableAgents=0,
            busyAgents=0,
            offlineAgents=0,
            networkHealth="offline",
        )


@router.post("/sessions/{session_id}/end")
async def end_agent_session(
    session_id: str,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
):
    """
    End an active agent session.
    Cleans up session data and updates agent availability.
    """
    try:
        # Find and deactivate session
        if session_id in ACTIVE_SESSIONS:
            session = ACTIVE_SESSIONS[session_id]
            session["isActive"] = False
            session["endTime"] = datetime.now(timezone.utc).isoformat()

            # Update agent status
            agent_id = session["agentId"]
            for a in AVAILABLE_AGENTS:
                if a["id"] == agent_id:
                    a["status"] = "available"
                    a["currentTask"] = None
                    break

            # Remove from active sessions
            del ACTIVE_SESSIONS[session_id]

            return {"message": f"Session {session_id} ended successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Session {session_id} not found",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to end session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to end session",
        )


@router.post("/agents/{agent_id}/start")
async def start_agent(
    agent_id: str,
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
    conn: Connection = Depends(get_db_session),
):
    """
    Start an agent and make it available for processing.

    This endpoint activates an agent that was previously offline or in error state.
    """
    try:
        # Find the agent
        agent = next((a for a in AVAILABLE_AGENTS if a.id == agent_id), None)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_id} not found",
            )

        # Update agent status
        agent.status = "available"
        agent.lastActivity = datetime.now(timezone.utc).isoformat()
        agent.currentTask = None

        logger.info(f"Started agent {agent_id} for tenant {tenant_id}")

        return {
            "success": True,
            "agent_id": agent_id,
            "status": agent.status,
            "message": f"Agent {agent.name} started successfully",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start agent {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start agent",
        )


@router.get("/health")
async def health_check():
    """Health check endpoint for ADK agent service."""
    try:
        # Check if we have agents and can perform basic operations
        agent_count = len(AVAILABLE_AGENTS)
        session_count = len(ACTIVE_SESSIONS)

        return {
            "status": "healthy",
            "service": "adk_agents",
            "agent_count": agent_count,
            "active_sessions": session_count,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error(f"ADK health check failed: {e}")
        return {
            "status": "unhealthy",
            "service": "adk_agents",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
