"""
Intelligence Domain Schemas - Transaction Intelligence Operations

Pydantic models for intelligence service requests and responses.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class AccountingSystemDetectionRequest(BaseModel):
    """Request model for accounting system detection."""

    columns: List[str] = Field(
        ..., description="Column names from the transaction file"
    )
    sample_data: Optional[Dict[str, Any]] = Field(
        None, description="Sample transaction data"
    )
    filename: Optional[str] = Field(None, description="Original filename for context")


class AccountingSystemDetectionResponse(BaseModel):
    """Response model for accounting system detection."""

    system_type: str = Field(..., description="Detected accounting system type")
    cultural_context: str = Field(
        ..., description="Cultural context (indian/us/global)"
    )
    confidence: float = Field(..., description="Detection confidence (0.0-1.0)")
    features: Dict[str, Any] = Field(..., description="Detected system features")
    reasoning: str = Field(..., description="Detection reasoning")
    institution_info: Dict[str, str] = Field(..., description="Institution information")


class EntityExtractionRequest(BaseModel):
    """Request model for entity extraction."""

    descriptions: List[str] = Field(
        ..., description="Transaction descriptions to analyze"
    )
    cultural_context: Optional[str] = Field(
        "global", description="Cultural context for processing"
    )


class EntityExtractionResponse(BaseModel):
    """Response model for entity extraction."""

    entities: Dict[str, List[str]] = Field(
        ..., description="Extracted entities by type"
    )
    total_processed: int = Field(..., description="Number of descriptions processed")


class AmountProcessingRequest(BaseModel):
    """Request model for amount field processing."""

    transaction_data: Dict[str, Any] = Field(
        ..., description="Transaction data to process"
    )
    column_mapping: Dict[str, str] = Field(
        ..., description="Column mapping configuration"
    )
    accounting_system: Dict[str, Any] = Field(
        ..., description="Detected accounting system info"
    )


class AmountProcessingResponse(BaseModel):
    """Response model for amount field processing."""

    amount: float = Field(..., description="Processed amount value")
    transaction_type: str = Field(..., description="Determined transaction type")
    currency: str = Field(..., description="Detected currency")
    cash_flow_direction: str = Field(
        ..., description="Cash flow direction (inflow/outflow)"
    )


class DescriptionNormalizationRequest(BaseModel):
    """Request model for description normalization."""

    descriptions: List[str] = Field(
        ..., description="Transaction descriptions to normalize"
    )
    cultural_context: Optional[str] = Field(
        "global", description="Cultural context for processing"
    )


class DescriptionNormalizationResponse(BaseModel):
    """Response model for description normalization."""

    normalized_descriptions: List[str] = Field(
        ..., description="Normalized descriptions"
    )
    total_processed: int = Field(..., description="Number of descriptions processed")


class BatchAnalysisRequest(BaseModel):
    """Request model for transaction batch analysis."""

    transactions: List[Dict[str, Any]] = Field(
        ..., description="Batch of transactions to analyze"
    )
    context: Optional[Dict[str, Any]] = Field(
        None, description="Additional context for analysis"
    )


class BatchAnalysisResponse(BaseModel):
    """Response model for transaction batch analysis."""

    batch_size: int = Field(..., description="Number of transactions in batch")
    accounting_system: Dict[str, Any] = Field(
        ..., description="Detected accounting system"
    )
    processing_summary: Dict[str, Any] = Field(..., description="Processing summary")
    quality_metrics: Dict[str, Any] = Field(..., description="Quality metrics")
    insights: Dict[str, Any] = Field(..., description="Generated insights")
    recommendations: List[str] = Field(..., description="Processing recommendations")


class IntelligenceHealthResponse(BaseModel):
    """Response model for health check."""

    status: str = Field(..., description="Service status")
    service: str = Field(..., description="Service name")
    version: Optional[str] = Field(None, description="Service version")


# Additional schemas for intelligence operations
class EntityAnalysis(BaseModel):
    """Entity analysis result."""

    entity_type: str = Field(
        ..., description="Type of entity (merchant, account, amount)"
    )
    entity_value: str = Field(..., description="Extracted entity value")
    confidence: float = Field(..., description="Extraction confidence (0.0-1.0)")
    context: Optional[str] = Field(None, description="Context where entity was found")


class SystemFeatures(BaseModel):
    """Detected accounting system features."""

    date_format: Optional[str] = Field(None, description="Detected date format")
    amount_columns: List[str] = Field(
        default_factory=list, description="Amount column names"
    )
    description_column: Optional[str] = Field(
        None, description="Description column name"
    )
    has_balance: bool = Field(False, description="Has running balance column")
    transaction_id_column: Optional[str] = Field(
        None, description="Transaction ID column"
    )
    currency_indicators: List[str] = Field(
        default_factory=list, description="Currency indicators found"
    )


class ProcessingQualityMetrics(BaseModel):
    """Quality metrics for transaction processing."""

    total_transactions: int = Field(..., description="Total number of transactions")
    successful_extractions: int = Field(
        ..., description="Successful entity extractions"
    )
    failed_extractions: int = Field(..., description="Failed entity extractions")
    average_confidence: float = Field(..., description="Average confidence score")
    data_completeness: float = Field(
        ..., description="Data completeness ratio (0.0-1.0)"
    )
    anomalies_detected: int = Field(..., description="Number of anomalies detected")


class TransactionInsights(BaseModel):
    """Insights generated from transaction analysis."""

    data_quality: str = Field(..., description="Overall data quality assessment")
    completeness: float = Field(..., description="Data completeness score (0.0-1.0)")
    patterns: List[str] = Field(..., description="Detected patterns in data")
    anomalies: List[str] = Field(..., description="Detected anomalies")
    recommendations: List[str] = Field(..., description="Processing recommendations")
    risk_factors: List[str] = Field(
        default_factory=list, description="Identified risk factors"
    )
