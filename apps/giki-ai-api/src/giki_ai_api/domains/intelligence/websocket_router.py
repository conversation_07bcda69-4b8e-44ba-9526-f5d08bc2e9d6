"""
WebSocket Router for Real-time Agent Communication

Provides WebSocket endpoints for real-time updates between agents and UI.
"""

import logging
from typing import Optional

import asyncpg
from fastapi import APIRouter, Depends, Query, WebSocket, WebSocketDisconnect, status

from ...core.dependencies import get_db_session
from ...shared.services.websocket_service import WebSocketService
from ..auth.secure_auth import get_current_active_user_ws

logger = logging.getLogger(__name__)

router = APIRouter(tags=["WebSocket"])

# WebSocket service instance
ws_service = WebSocketService()


@router.websocket("/agent")
async def websocket_agent_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(
        None, description="JWT access token for authentication"
    ),
    db: asyncpg.Connection = Depends(get_db_session),
):
    """
    WebSocket endpoint for real-time agent communication.

    Features:
    - Real-time agent processing updates
    - Progress notifications for long operations
    - Bidirectional communication
    - Multi-tenant isolation
    """
    await _handle_websocket_connection(websocket, token, db)


@router.websocket("/files")
async def websocket_files_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(
        None, description="JWT access token for authentication"
    ),
    db: asyncpg.Connection = Depends(get_db_session),
):
    """
    WebSocket endpoint for real-time file processing updates.

    Features:
    - Real-time file upload progress
    - Processing status notifications
    - Categorization progress updates
    - Multi-tenant isolation
    """
    await _handle_websocket_connection(websocket, token, db)


async def _handle_websocket_connection(
    websocket: WebSocket,
    token: Optional[str],
    db: asyncpg.Connection,
):
    """
    Shared WebSocket connection handler for all WebSocket endpoints.

    Features:
    - Authentication and authorization
    - Multi-tenant isolation
    - Real-time bidirectional communication
    - Automatic connection management
    """
    # Authenticate WebSocket connection
    try:
        logger.warning(f"WebSocket auth attempt - token provided: {token is not None}")
        if token:
            logger.warning(f"Token length: {len(token)}, starts with: {token[:20]}...")

        if not token:
            logger.warning("WebSocket rejected: No token provided")
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Validate token and get user
        logger.warning("Attempting to validate token for WebSocket...")
        user = await get_current_active_user_ws(token, db)
        logger.warning(f"Token validation result: {user is not None}")

        if not user:
            logger.warning("WebSocket rejected: Token validation failed")
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Get tenant_id for the user
        tenant_id = user.tenant_id if hasattr(user, "tenant_id") else 1

        logger.info(
            f"WebSocket connection established for user {user.id}, tenant {tenant_id}"
        )

        # Handle WebSocket connection
        await ws_service.handle_websocket(
            websocket=websocket, tenant_id=tenant_id, user_id=str(user.id)
        )

    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
