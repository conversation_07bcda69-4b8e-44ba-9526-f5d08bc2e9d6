"""
Intelligence domain ADK tools.

This module provides ADK tool functions for AI-powered intelligence operations that can be used
by agents to analyze transactions, detect systems, and extract insights.
"""

import logging
from typing import Any, Dict, List, Optional

import asyncpg

from .service import IntelligenceService

logger = logging.getLogger(__name__)


async def detect_accounting_system_tool(
    db: asyncpg.Connection,
    columns: List[str],
    sample_data: Optional[Dict[str, Any]] = None,
    filename: Optional[str] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to detect accounting system from file structure.

    Args:
        db: Database connection
        columns: List of column names from the file
        sample_data: Optional sample data for better detection
        filename: Optional filename for context

    Returns:
        System detection results with confidence and features
    """
    try:
        intelligence_service = IntelligenceService()
        result = await intelligence_service.detect_accounting_system(
            columns=columns,
            sample_data=sample_data,
            filename=filename,
        )

        return {
            "success": True,
            "detection_result": result,
            "system_type": result.get("system_type", "unknown"),
            "confidence": result.get("confidence", 0.5),
            "cultural_context": result.get("cultural_context", "global"),
            "features": result.get("features", {}),
            "institution_info": result.get("institution_info", {}),
            "reasoning": result.get("reasoning", ""),
        }

    except Exception as e:
        logger.error(f"Accounting system detection failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "system_type": "unknown",
            "confidence": 0.0,
        }


async def extract_entities_tool(
    db: asyncpg.Connection,
    description: str,
    cultural_context: str = "global",
) -> Dict[str, Any]:
    """
    ADK tool function to extract entities from transaction descriptions.

    Args:
        db: Database connection
        description: Transaction description text
        cultural_context: Cultural context (indian/us/global)

    Returns:
        Extracted entities (merchants, account numbers, amounts)
    """
    try:
        intelligence_service = IntelligenceService()
        entities = await intelligence_service.extract_entities(
            description=description,
            cultural_context=cultural_context,
        )

        return {
            "success": True,
            "entities": entities,
            "merchants": entities.get("merchants", []),
            "account_numbers": entities.get("account_numbers", []),
            "amounts": entities.get("amounts", []),
            "entity_count": (
                len(entities.get("merchants", []))
                + len(entities.get("account_numbers", []))
                + len(entities.get("amounts", []))
            ),
        }

    except Exception as e:
        logger.error(f"Entity extraction failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "entities": {"merchants": [], "account_numbers": [], "amounts": []},
        }


async def normalize_description_tool(
    db: asyncpg.Connection,
    description: str,
    cultural_context: str = "global",
) -> Dict[str, Any]:
    """
    ADK tool function to normalize transaction descriptions.

    Args:
        db: Database connection
        description: Original transaction description
        cultural_context: Cultural context for normalization

    Returns:
        Normalized description with success status
    """
    try:
        intelligence_service = IntelligenceService()
        normalized = await intelligence_service.normalize_description(
            description=description,
            cultural_context=cultural_context,
        )

        return {
            "success": True,
            "original_description": description,
            "normalized_description": normalized,
            "normalization_applied": normalized != description,
            "character_reduction": len(description) - len(normalized),
        }

    except Exception as e:
        logger.error(f"Description normalization failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "original_description": description,
            "normalized_description": description,  # Fallback to original
        }


async def process_amount_fields_tool(
    db: asyncpg.Connection,
    transaction_data: Dict[str, Any],
    column_mapping: Dict[str, str],
    accounting_system: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to process amount fields and determine transaction type.

    Args:
        db: Database connection
        transaction_data: Raw transaction data
        column_mapping: Column mapping configuration
        accounting_system: Optional accounting system info

    Returns:
        Processed amount information with transaction type
    """
    try:
        intelligence_service = IntelligenceService()

        # Provide default accounting system if not provided
        if not accounting_system:
            accounting_system = {
                "cultural_context": "global",
                "system_type": "unknown",
            }

        result = await intelligence_service.process_amount_fields(
            transaction_data=transaction_data,
            column_mapping=column_mapping,
            accounting_system=accounting_system,
        )

        return {
            "success": True,
            "amount_info": result,
            "amount": result.get("amount", 0.0),
            "transaction_type": result.get("transaction_type", "debit"),
            "currency": result.get("currency", "USD"),
            "cash_flow_direction": result.get("cash_flow_direction", "outflow"),
        }

    except Exception as e:
        logger.error(f"Amount processing failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "amount_info": {
                "amount": 0.0,
                "transaction_type": "debit",
                "currency": "USD",
                "cash_flow_direction": "outflow",
            },
        }


async def analyze_transaction_batch_tool(
    db: asyncpg.Connection,
    transactions: List[Dict[str, Any]],
    context: Dict[str, Any],
    tenant_id: int,
) -> Dict[str, Any]:
    """
    ADK tool function to perform comprehensive batch analysis of transactions.

    Args:
        db: Database connection
        transactions: List of transaction data
        context: Analysis context (filename, etc.)
        tenant_id: Tenant ID

    Returns:
        Comprehensive batch analysis results
    """
    try:
        intelligence_service = IntelligenceService()
        analysis = await intelligence_service.analyze_transaction_batch(
            transactions=transactions,
            context=context,
            tenant_id=tenant_id,
        )

        return {
            "success": True,
            "analysis": analysis,
            "batch_size": analysis.get("batch_size", 0),
            "accounting_system": analysis.get("accounting_system", {}),
            "quality_metrics": analysis.get("quality_metrics", {}),
            "insights": analysis.get("insights", {}),
            "recommendations": analysis.get("recommendations", []),
            "processing_summary": analysis.get("processing_summary", {}),
        }

    except Exception as e:
        logger.error(f"Transaction batch analysis failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "analysis": {
                "batch_size": len(transactions) if transactions else 0,
                "error_occurred": True,
            },
        }


async def process_confirmed_interpretation_tool(
    db: asyncpg.Connection,
    upload_id: str,
    user_id: int,
    tenant_id: int,
    confirmed_data: Dict[str, Any],
    original_file_path: str,
) -> Dict[str, Any]:
    """
    ADK tool function to process confirmed column mapping and create transactions.

    Args:
        db: Database connection
        upload_id: Upload identifier
        user_id: User ID
        tenant_id: Tenant ID
        confirmed_data: Confirmed column mapping data
        original_file_path: Path to the original file

    Returns:
        Processing results with transaction creation status
    """
    try:
        intelligence_service = IntelligenceService()
        result = await intelligence_service.process_confirmed_interpretation(
            db=db,
            upload_id=upload_id,
            user_id=user_id,
            tenant_id=tenant_id,
            confirmed_data=confirmed_data,
            original_file_path=original_file_path,
        )

        return {
            "success": True,
            "processing_result": result,
            "status_message": result.get("status_message", ""),
            "processing_status": result.get("processing_status", "UNKNOWN"),
            "successful_transactions": result.get("successful_transactions", 0),
            "failed_transactions": result.get("failed_transactions", 0),
            "total_rows_processed": result.get("total_rows_processed", 0),
            "errors": result.get("errors", []),
        }

    except Exception as e:
        logger.error(f"Confirmed interpretation processing failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "processing_result": {
                "processing_status": "FAILED",
                "successful_transactions": 0,
                "failed_transactions": 0,
                "status_message": f"Processing failed: {str(e)}",
            },
        }


async def get_intelligence_insights_tool(
    db: asyncpg.Connection,
    tenant_id: int,
    analysis_type: str = "overview",
    time_period: Optional[str] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to get intelligence insights for a tenant.

    Args:
        db: Database connection
        tenant_id: Tenant ID
        analysis_type: Type of analysis (overview/detailed/trends)
        time_period: Optional time period filter

    Returns:
        Intelligence insights and recommendations
    """
    try:
        # Query recent transactions for analysis
        query = """
            SELECT description, amount, date, category_path, account
            FROM transactions 
            WHERE tenant_id = $1 
            ORDER BY date DESC 
            LIMIT 100
        """
        rows = await db.fetch(query, tenant_id)

        if not rows:
            return {
                "success": True,
                "insights": {
                    "message": "No transaction data available for analysis",
                    "transaction_count": 0,
                },
                "recommendations": [
                    "Upload transaction data to get personalized insights"
                ],
            }

        # Convert to list of dicts for analysis
        transactions = [dict(row) for row in rows]

        intelligence_service = IntelligenceService()
        analysis = await intelligence_service.analyze_transaction_batch(
            transactions=transactions,
            context={"analysis_type": analysis_type, "time_period": time_period},
            tenant_id=tenant_id,
        )

        return {
            "success": True,
            "insights": analysis.get("insights", {}),
            "recommendations": analysis.get("recommendations", []),
            "quality_metrics": analysis.get("quality_metrics", {}),
            "transaction_count": len(transactions),
            "analysis_type": analysis_type,
        }

    except Exception as e:
        logger.error(f"Intelligence insights failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "insights": {"error_occurred": True},
            "recommendations": ["Unable to generate insights due to error"],
        }
