"""
Intelligence Service - Consolidated Transaction Intelligence Operations

Provides comprehensive intelligence functionality including:
- Accounting system detection
- Entity extraction from descriptions
- Amount field processing
- Transaction intelligence analysis
- Description processing and normalization
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional

# Google Gen AI SDK - Recommended approach (replaces deprecated vertexai.generative_models)
import google.genai as genai

from ...core.config import settings
from ...shared.exceptions import ServiceError

logger = logging.getLogger(__name__)


class IntelligenceService:
    """
    Consolidated intelligence service providing:
    - Accounting system detection
    - Entity extraction (merchants, vendors, accounts)
    - Amount processing and validation
    - Description normalization
    - Transaction batch analysis
    """

    def __init__(self):
        """Initialize intelligence service with Google Gen AI SDK."""
        try:
            # Initialize Google Gen AI client with Vertex AI
            if settings.VERTEX_PROJECT_ID and settings.VERTEX_LOCATION:
                self.client = genai.Client(
                    vertexai=True,
                    project=settings.VERTEX_PROJECT_ID,
                    location=settings.VERTEX_LOCATION
                )
                logger.info(f"Initialized Google Gen AI with Vertex AI - project: {settings.VERTEX_PROJECT_ID}")
            else:
                raise ValueError("VERTEX_PROJECT_ID and VERTEX_LOCATION must be configured")
            
            # Get the Gemini model
            self.model_name = "gemini-2.0-flash-001"
            logger.info("Successfully initialized IntelligenceService with Google Gen AI SDK")
            
        except Exception as e:
            logger.error(f"Failed to initialize Google Gen AI client: {e}")
            raise ServiceError(
                "Failed to initialize AI model",
                service_name="IntelligenceService",
                operation="__init__",
                error_code="AI_INIT_ERROR",
                original_error=e,
            )

    async def detect_accounting_system(
        self,
        columns: List[str],
        sample_data: Optional[Dict[str, Any]] = None,
        filename: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Detect accounting system from column names and sample data.

        Returns:
            Dict containing system_type, cultural_context, confidence, features, etc.
        """
        try:
            # Build context for AI analysis
            context_parts = [
                f"Columns: {', '.join(columns)}",
            ]

            if filename:
                context_parts.append(f"Filename: {filename}")

            if sample_data:
                # Include sample data for better detection
                sample_str = json.dumps(sample_data, indent=2, default=str)[:500]
                context_parts.append(f"Sample data:\n{sample_str}")

            prompt = f"""
            You are an expert at identifying accounting systems and financial data formats.
            
            Analyze the following transaction data structure:
            {chr(10).join(context_parts)}
            
            Identify:
            1. The accounting system or bank this data comes from
            2. The cultural/regional context (indian/us/global)
            3. Key features and patterns in the data
            4. Institution information if identifiable
            
            Common patterns:
            - Indian banks: Often have "Narration", "Chq./Ref.No.", "Dr Amount", "Cr Amount"
            - US banks: Often have "Description", "Debit", "Credit", "Balance"
            - QuickBooks: Has specific export formats
            - Credit card statements: Have "Payment", "Purchase", "Transaction Date"
            
            Return JSON format:
            {{
                "system_type": "bank_name or accounting_system",
                "cultural_context": "indian|us|global",
                "confidence": 0.0-1.0,
                "features": {{
                    "date_format": "detected format",
                    "amount_columns": ["column names"],
                    "description_column": "column name",
                    "has_balance": true/false,
                    "transaction_id_column": "if exists"
                }},
                "reasoning": "explanation of detection",
                "institution_info": {{
                    "name": "institution name if detected",
                    "type": "bank|credit_card|accounting_software",
                    "country": "detected country"
                }}
            }}
            
            JSON:"""

            # Generate content using Google Gen AI SDK
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=prompt,
                config=genai.types.GenerateContentConfig(
                    temperature=0.1,
                    max_output_tokens=500,
                    top_p=0.8,
                ),
            )

            # Parse and validate response
            response_text = response.text.strip()

            try:
                result = json.loads(response_text)

                # Ensure all required fields exist
                result.setdefault("system_type", "unknown")
                result.setdefault("cultural_context", "global")
                result.setdefault("confidence", 0.5)
                result.setdefault("features", {})
                result.setdefault("reasoning", "Unable to determine specific system")
                result.setdefault(
                    "institution_info",
                    {"name": "Unknown", "type": "unknown", "country": "unknown"},
                )

                # Validate confidence is in range
                result["confidence"] = max(
                    0.0, min(1.0, float(result.get("confidence", 0.5)))
                )

                return result

            except json.JSONDecodeError:
                logger.warning(f"Failed to parse AI response: {response_text[:200]}")
                return {
                    "system_type": "unknown",
                    "cultural_context": "global",
                    "confidence": 0.3,
                    "features": {"detected_columns": columns},
                    "reasoning": "AI response parsing failed, using fallback detection",
                    "institution_info": {
                        "name": "Unknown",
                        "type": "unknown",
                        "country": "unknown",
                    },
                }

        except Exception as e:
            logger.error(f"Accounting system detection failed: {e}")
            raise ServiceError(
                "Failed to detect accounting system",
                service_name="IntelligenceService",
                operation="detect_accounting_system",
                error_code="SYSTEM_DETECTION_ERROR",
                original_error=e,
            )

    async def extract_entities(
        self, description: str, cultural_context: str = "global"
    ) -> Dict[str, List[str]]:
        """
        Extract entities from transaction description using AI.

        Returns:
            Dict with merchants, account_numbers, amounts lists
        """
        try:
            if not description:
                return {"merchants": [], "account_numbers": [], "amounts": []}

            prompt = f"""
            You are an expert at extracting entities from financial transaction descriptions.
            Cultural context: {cultural_context}
            
            Transaction Description: "{description}"
            
            Extract the following entities:
            1. **Merchants/Vendors**: Company names, store names, service providers
               - For Indian context: Look for UPI IDs, NEFT references
               - For US context: Look for merchant names, store locations
            2. **Account Numbers**: Bank account numbers, card numbers (including masked ones)
               - Include UPI IDs, IFSC codes, reference numbers
            3. **Amounts**: Any monetary amounts mentioned (with currency symbols)
            
            Rules:
            - Only extract entities that are clearly present
            - For merchants: Extract the main business name, not generic terms
            - For account numbers: Include both full and masked numbers
            - For amounts: Include currency symbol if present
            - Return empty arrays if no entities found
            
            Return JSON format:
            {{
                "merchants": ["merchant1", "merchant2"],
                "account_numbers": ["acc1", "acc2"],
                "amounts": ["$10.00", "₹500"]
            }}
            
            JSON:"""

            response = self.client.models.generate_content(
                model=self.model_name,
                contents=prompt,
                config=genai.types.GenerateContentConfig(
                    temperature= 0.1,
                    max_output_tokens= 200,
                    top_p= 0.8,
                ),
            )

            # Parse response
            response_text = response.text.strip()

            try:
                result = json.loads(response_text)

                # Validate and clean results
                entities = {
                    "merchants": result.get("merchants", [])[:5],  # Limit to 5
                    "account_numbers": result.get("account_numbers", [])[:3],
                    "amounts": result.get("amounts", [])[:3],
                }

                # Ensure all values are lists
                for key in entities:
                    if not isinstance(entities[key], list):
                        entities[key] = []

                return entities

            except json.JSONDecodeError:
                logger.warning(
                    f"Failed to parse entity extraction: {response_text[:200]}"
                )
                return {"merchants": [], "account_numbers": [], "amounts": []}

        except Exception as e:
            logger.error(f"Entity extraction failed: {e}")
            # Don't raise error for entity extraction - return empty result
            return {"merchants": [], "account_numbers": [], "amounts": []}

    async def process_confirmed_interpretation(
        self,
        db,
        upload_id: str,
        user_id: int,
        tenant_id: int,
        confirmed_data: Dict[str, Any],
        original_file_path: str,
    ) -> Dict[str, Any]:
        """
        Process confirmed column mapping interpretation and create transactions.

        This is the critical method that converts uploaded file data into transaction records.
        """
        try:
            import os
            import uuid
            from datetime import date, datetime

            import pandas as pd
            # Using asyncpg - no sqlalchemy imports needed

            logger.info(f"Processing confirmed interpretation for upload {upload_id}")
            logger.info(f"File path: {original_file_path}")
            logger.info(f"Confirmed data: {confirmed_data}")

            # Validate file exists
            if not original_file_path or not os.path.exists(original_file_path):
                raise ServiceError(
                    f"File not found: {original_file_path}",
                    service_name="IntelligenceService",
                    operation="process_confirmed_interpretation",
                )

            # Read the file with error handling
            try:
                if original_file_path.endswith(".csv"):
                    df = pd.read_csv(original_file_path)
                else:
                    df = pd.read_excel(original_file_path)
                logger.info(
                    f"Successfully loaded {len(df)} rows from {original_file_path}"
                )
            except Exception as e:
                logger.error(f"Failed to read file {original_file_path}: {e}")
                raise ServiceError(f"Failed to read file: {e}")

            # Extract column mappings - handle both 'mapping' and 'mappings' formats
            column_map = {}
            if "mapping" in confirmed_data:
                # Direct mapping format: {"mapping": {"Date": "date", "Amount": "amount"}}
                column_map = {v: k for k, v in confirmed_data["mapping"].items() if v}
            elif "mappings" in confirmed_data:
                # Array format: {"mappings": [{"original_name": "Date", "mapped_field": "date"}]}
                for mapping in confirmed_data["mappings"]:
                    if (
                        mapping.get("mapped_field")
                        and mapping.get("mapped_field") != "unmapped"
                    ):
                        column_map[mapping["mapped_field"]] = mapping["original_name"]

            logger.info(f"Column mappings: {column_map}")

            if not column_map:
                raise ServiceError("No valid column mappings found in confirmed data")

            # Create transactions from DataFrame (process first 100 rows for testing)
            successful_transactions = 0
            failed_transactions = 0
            errors = []
            max_rows = min(len(df), 100)  # Limit to 100 rows for initial testing

            logger.info(f"Processing {max_rows} rows...")

            for index, row in df.head(max_rows).iterrows():
                try:
                    # Extract transaction data using column mappings
                    description = str(
                        row.get(
                            column_map.get("description", ""), "Unknown Transaction"
                        )
                    ).strip()

                    # Handle amount field
                    amount_value = row.get(column_map.get("amount", ""), 0)
                    if pd.isna(amount_value):
                        amount_value = 0
                    try:
                        # Clean amount string and convert to float
                        amount_str = (
                            str(amount_value)
                            .replace(",", "")
                            .replace("$", "")
                            .replace("(", "-")
                            .replace(")", "")
                            .strip()
                        )
                        amount = (
                            float(amount_str)
                            if amount_str and amount_str != "nan"
                            else 0.0
                        )
                    except (ValueError, AttributeError):
                        amount = 0.0

                    # Handle date field
                    date_value = row.get(column_map.get("date", ""))
                    if pd.isna(date_value):
                        tx_date = date.today()
                    else:
                        try:
                            if isinstance(date_value, (datetime, pd.Timestamp)):
                                tx_date = date_value.date()
                            else:
                                tx_date = pd.to_datetime(str(date_value)).date()
                        except Exception:
                            tx_date = date.today()

                    # Extract other fields
                    account = (
                        str(row.get(column_map.get("account", ""), "")).strip() or None
                    )
                    category = (
                        str(row.get(column_map.get("category", ""), "")).strip() or None
                    )

                    # Create transaction ID
                    transaction_id = str(uuid.uuid4())

                    # Insert transaction directly via SQL with timeout protection
                    await db.execute(
                        """
                        INSERT INTO transactions (
                            id, tenant_id, upload_id, description, amount, date, 
                            account, category_path, created_at, updated_at
                        ) VALUES (
                            $1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW()
                        )
                        """,
                        transaction_id,
                        tenant_id,
                        upload_id,
                        description[:500],  # Truncate if too long
                        amount,
                        tx_date,
                        account[:100] if account else None,  # Truncate if too long
                        category[:255] if category else None,  # Truncate if too long
                    )

                    successful_transactions += 1

                    # Note: asyncpg handles transactions automatically
                    if successful_transactions % 25 == 0:
                        logger.info(
                            f"Processed {successful_transactions} transactions so far..."
                        )

                except Exception as e:
                    failed_transactions += 1
                    error_msg = f"Row {index}: {str(e)}"
                    errors.append(error_msg)
                    logger.warning(
                        f"Failed to create transaction from row {index}: {e}"
                    )

                    # Continue processing other rows
                    continue

            # Note: asyncpg handles transactions automatically
            logger.info(
                f"Processing completed. Total transactions: {successful_transactions}"
            )

            # Update upload status
            await db.execute(
                """
                UPDATE uploads SET status = 'processed' WHERE id = $1
                """,
                upload_id,
            )

            result = {
                "status_message": f"Created {successful_transactions} transactions from {max_rows} rows processed",
                "processing_status": "COMPLETED",
                "successful_transactions": successful_transactions,
                "failed_transactions": failed_transactions,
                "total_rows_processed": max_rows,
                "total_rows_in_file": len(df),
                "errors": errors[:5] if errors else [],  # Limit errors to first 5
            }

            logger.info(f"Transaction creation completed: {result}")
            return result

        except Exception as e:
            logger.error(f"Failed to process confirmed interpretation: {e}")
            # Note: asyncpg handles rollback automatically on error
            raise ServiceError(
                f"Transaction creation failed: {str(e)}",
                service_name="IntelligenceService",
                operation="process_confirmed_interpretation",
                original_error=e,
            )

    async def process_amount_fields(
        self,
        transaction_data: Dict[str, Any],
        column_mapping: Dict[str, str],
        accounting_system: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Process amount fields to determine transaction type and normalize amounts.

        Returns:
            Dict with amount, transaction_type, currency, cash_flow_direction
        """
        try:
            # Extract amount values based on column mapping
            debit_col = column_mapping.get("debit_amount")
            credit_col = column_mapping.get("credit_amount")
            amount_col = column_mapping.get("amount")

            debit_value = transaction_data.get(debit_col) if debit_col else None
            credit_value = transaction_data.get(credit_col) if credit_col else None
            amount_value = transaction_data.get(amount_col) if amount_col else None

            # Build context for AI analysis
            context = {
                "debit": debit_value,
                "credit": credit_value,
                "amount": amount_value,
                "description": transaction_data.get(
                    column_mapping.get("description", ""), ""
                ),
                "cultural_context": accounting_system.get("cultural_context", "global"),
            }

            prompt = f"""
            You are an expert at processing financial transaction amounts.
            
            Transaction data:
            {json.dumps(context, indent=2, default=str)}
            
            Determine:
            1. The final amount (as a positive float)
            2. Transaction type (debit/credit)
            3. Currency (detect from symbols or context)
            4. Cash flow direction (inflow/outflow from user perspective)
            
            Rules:
            - Debit = money out (expense), Credit = money in (income)
            - For Indian context: ₹ symbol, amounts may have commas (1,00,000)
            - For US context: $ symbol, amounts may have commas (100,000)
            - Handle negative amounts appropriately
            
            Return JSON format:
            {{
                "amount": 1234.56,
                "transaction_type": "debit|credit",
                "currency": "USD|INR|EUR|etc",
                "cash_flow_direction": "inflow|outflow"
            }}
            
            JSON:"""

            response = self.client.models.generate_content(
                model=self.model_name,
                contents=prompt,
                config=genai.types.GenerateContentConfig(
                    temperature= 0.1,
                    max_output_tokens= 200,
                    top_p= 0.8,
                ),
            )

            # Parse response
            response_text = response.text.strip()

            try:
                result = json.loads(response_text)

                # Validate and ensure proper types
                return {
                    "amount": float(result.get("amount", 0.0)),
                    "transaction_type": result.get("transaction_type", "debit"),
                    "currency": result.get("currency", "USD"),
                    "cash_flow_direction": result.get("cash_flow_direction", "outflow"),
                }

            except (json.JSONDecodeError, ValueError):
                # Fallback logic
                amount = 0.0
                transaction_type = "debit"

                if debit_value:
                    amount = abs(float(str(debit_value).replace(",", "")))
                    transaction_type = "debit"
                elif credit_value:
                    amount = abs(float(str(credit_value).replace(",", "")))
                    transaction_type = "credit"
                elif amount_value:
                    amount = abs(float(str(amount_value).replace(",", "")))
                    transaction_type = "debit" if amount < 0 else "credit"

                return {
                    "amount": amount,
                    "transaction_type": transaction_type,
                    "currency": "INR"
                    if accounting_system.get("cultural_context") == "indian"
                    else "USD",
                    "cash_flow_direction": "outflow"
                    if transaction_type == "debit"
                    else "inflow",
                }

        except Exception as e:
            logger.error(f"Amount processing failed: {e}")
            raise ServiceError(
                "Failed to process amount fields",
                service_name="IntelligenceService",
                operation="process_amount_fields",
                error_code="AMOUNT_PROCESSING_ERROR",
                original_error=e,
            )

    async def normalize_description(
        self, description: str, cultural_context: str = "global"
    ) -> str:
        """
        Normalize and clean transaction descriptions.

        Returns:
            Cleaned description string
        """
        try:
            if not description:
                return ""

            prompt = f"""
            You are an expert at normalizing financial transaction descriptions.
            Cultural context: {cultural_context}
            
            Original description: "{description}"
            
            Normalize this description by:
            1. Removing unnecessary transaction codes and reference numbers
            2. Extracting the main merchant/vendor name
            3. Keeping relevant transaction context
            4. Making it human-readable
            
            Examples:
            - "UPI-PHONEPE-9876543210@ybl-HDFC1234567-Transaction" → "PhonePe Transfer"
            - "POS 123456 WALMART STORE #1234 AUSTIN TX" → "Walmart Austin TX"
            - "NEFT CR-ICIC0001234-SALARY JAN 2024" → "Salary Transfer Jan 2024"
            
            Return only the normalized description (no JSON):"""

            response = self.client.models.generate_content(
                model=self.model_name,
                contents=prompt,
                config=genai.types.GenerateContentConfig(
                    temperature= 0.1,
                    max_output_tokens= 100,
                    top_p= 0.8,
                ),
            )

            normalized = response.text.strip()

            # Basic validation
            if len(normalized) > 200:
                normalized = normalized[:200]

            return normalized or description

        except Exception as e:
            logger.warning(f"Description normalization failed: {e}")
            # Return original on error
            return description

    async def analyze_transaction_batch(
        self,
        transactions: List[Dict[str, Any]],
        context: Dict[str, Any],
        tenant_id: int,
    ) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of a batch of transactions.

        Returns:
            Analysis results including system detection, quality metrics, insights
        """
        try:
            analysis_result = {
                "batch_size": len(transactions),
                "accounting_system": {},
                "processing_summary": {},
                "quality_metrics": {},
                "insights": {},
                "recommendations": [],
            }

            if not transactions:
                return analysis_result

            # Detect accounting system from batch
            sample_transaction = transactions[0]
            columns = list(sample_transaction.keys())

            accounting_system = await self.detect_accounting_system(
                columns=columns,
                sample_data=sample_transaction,
                filename=context.get("filename"),
            )
            analysis_result["accounting_system"] = accounting_system

            # Process quality metrics
            total_amount = 0.0
            transaction_types = {"debit": 0, "credit": 0}

            for transaction in transactions[:100]:  # Analyze first 100
                # Get amount info (simplified)
                for key, value in transaction.items():
                    if any(amt in key.lower() for amt in ["amount", "debit", "credit"]):
                        try:
                            amount = abs(float(str(value).replace(",", "")))
                            total_amount += amount

                            if "debit" in key.lower():
                                transaction_types["debit"] += 1
                            elif "credit" in key.lower():
                                transaction_types["credit"] += 1
                        except (ValueError, TypeError):
                            pass

            # Generate insights
            cultural_context = accounting_system.get("cultural_context", "global")

            prompt = f"""
            You are a financial analyst reviewing transaction data.
            
            Summary:
            - Total transactions: {len(transactions)}
            - Accounting system: {accounting_system.get("system_type", "unknown")}
            - Cultural context: {cultural_context}
            - Sample columns: {", ".join(columns[:10])}
            - Transaction types: {transaction_types}
            
            Provide:
            1. Key insights about the data quality
            2. Detected patterns or anomalies
            3. Recommendations for processing
            
            Return JSON format:
            {{
                "data_quality": "excellent|good|fair|poor",
                "completeness": 0.0-1.0,
                "patterns": ["pattern1", "pattern2"],
                "anomalies": ["anomaly1", "anomaly2"],
                "recommendations": ["rec1", "rec2"]
            }}
            
            JSON:"""

            response = self.client.models.generate_content(
                model=self.model_name,
                contents=prompt,
                config=genai.types.GenerateContentConfig(
                    temperature= 0.2,
                    max_output_tokens= 500,
                    top_p= 0.8,
                ),
            )

            try:
                insights = json.loads(response.text.strip())
                analysis_result["insights"] = insights
                analysis_result["recommendations"] = insights.get("recommendations", [])
            except json.JSONDecodeError:
                analysis_result["insights"] = {
                    "data_quality": "good",
                    "completeness": 0.8,
                    "patterns": ["Regular transaction flow detected"],
                    "anomalies": [],
                    "recommendations": ["Proceed with standard processing"],
                }

            # Quality metrics
            analysis_result["quality_metrics"] = {
                "total_transactions": len(transactions),
                "transaction_types": transaction_types,
                "average_amount": total_amount / max(1, len(transactions[:100])),
                "detected_system_confidence": accounting_system.get("confidence", 0.5),
            }

            analysis_result["processing_summary"] = {
                "total_processed": len(transactions),
                "processing_errors": 0,
                "success_rate": 1.0,
            }

            return analysis_result

        except Exception as e:
            logger.error(f"Batch analysis failed: {e}")
            raise ServiceError(
                "Failed to analyze transaction batch",
                service_name="IntelligenceService",
                operation="analyze_transaction_batch",
                error_code="BATCH_ANALYSIS_ERROR",
                tenant_id=tenant_id,
                original_error=e,
            )


# Service singleton instance
_intelligence_service = None


def get_intelligence_service() -> IntelligenceService:
    """Get or create intelligence service singleton."""
    global _intelligence_service
    if _intelligence_service is None:
        _intelligence_service = IntelligenceService()
    return _intelligence_service
