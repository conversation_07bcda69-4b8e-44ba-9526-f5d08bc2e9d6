"""
Unified tools for CustomerFacingAgent to reduce cognitive load.

This module consolidates multiple tools into 3 unified tools:
1. UnifiedDataAccessTool - All data queries and searches
2. UnifiedReportingTool - All reporting and visualization
3. UnifiedUIOperationsTool - All UI operations via chat
"""

import logging
from typing import Any, Dict

# SQLAlchemy migrated to asyncpg
from asyncpg import Connection

logger = logging.getLogger(__name__)


async def unified_data_access_tool_function(
    operation: str,
    tenant_id: int,
    conn: Connection,
    **kwargs,
) -> Dict[str, Any]:
    """
    Unified tool for all data access operations.

    Operations:
    - "query": Query customer transactions with filters
    - "search": Search across all customer data
    - "qa": Answer questions about transactions
    - "metrics": Get accuracy and performance metrics

    Args:
        operation: The type of data access operation to perform
        tenant_id: The tenant ID for data isolation
        conn: asyncpg connection
        **kwargs: Operation-specific parameters

    Returns:
        Dict with operation results
    """
    logger.info(f"UnifiedDataAccess: {operation} for tenant {tenant_id}")

    try:
        if operation == "query":
            # Import and use existing function
            from .customer_agent import query_customer_transactions_tool_function

            return await query_customer_transactions_tool_function(
                tenant_id=tenant_id, conn=conn, **kwargs
            )

        elif operation == "search":
            # Import and use existing function
            from .customer_agent import search_customer_data_tool_function

            return await search_customer_data_tool_function(
                tenant_id=tenant_id, conn=conn, **kwargs
            )

        elif operation == "qa":
            # Import and use existing function
            from .ui_equivalence_tools import (
                answer_transaction_questions_lookup_tool_function,
            )

            return await answer_transaction_questions_lookup_tool_function(
                tenant_id=tenant_id, conn=conn, **kwargs
            )

        elif operation == "metrics":
            # Import and use existing function
            from .customer_agent import get_accuracy_metrics_tool_function

            return await get_accuracy_metrics_tool_function(
                tenant_id=tenant_id, conn=conn, **kwargs
            )

        else:
            return {
                "success": False,
                "error": f"Unknown operation: {operation}",
                "valid_operations": ["query", "search", "qa", "metrics"],
            }

    except Exception as e:
        logger.error(f"UnifiedDataAccess error: {e}", exc_info=True)
        return {"success": False, "error": str(e), "operation": operation}


async def unified_reporting_tool_function(
    operation: str,
    tenant_id: int,
    conn: Connection,
    **kwargs,
) -> Dict[str, Any]:
    """
    Unified tool for all reporting and visualization operations.

    Operations:
    - "insights": Generate AI insights from data
    - "report": Generate downloadable reports
    - "chart": Display charts in chat
    - "export": Export data in various formats

    Args:
        operation: The type of reporting operation to perform
        tenant_id: The tenant ID for data isolation
        conn: asyncpg connection
        **kwargs: Operation-specific parameters

    Returns:
        Dict with operation results
    """
    logger.info(f"UnifiedReporting: {operation} for tenant {tenant_id}")

    try:
        if operation == "insights":
            # Import and use existing function
            from .customer_agent import generate_insights_tool_function

            return await generate_insights_tool_function(
                tenant_id=tenant_id, conn=conn, **kwargs
            )

        elif operation == "report":
            # Import and use existing function
            from .ui_equivalence_tools import (
                generate_downloadable_reports_tool_function,
            )

            return await generate_downloadable_reports_tool_function(
                tenant_id=tenant_id, conn=conn, **kwargs
            )

        elif operation == "chart":
            # Import and use existing function
            from .ui_equivalence_tools import display_charts_in_chat_tool_function

            return await display_charts_in_chat_tool_function(
                tenant_id=tenant_id, conn=conn, **kwargs
            )

        elif operation == "export":
            # Import and use existing function
            from .customer_agent import export_customer_data_tool_function

            return await export_customer_data_tool_function(
                tenant_id=tenant_id, conn=conn, **kwargs
            )

        else:
            return {
                "success": False,
                "error": f"Unknown operation: {operation}",
                "valid_operations": ["insights", "report", "chart", "export"],
            }

    except Exception as e:
        logger.error(f"UnifiedReporting error: {e}", exc_info=True)
        return {"success": False, "error": str(e), "operation": operation}


async def unified_ui_operations_tool_function(
    operation: str,
    tenant_id: int,
    conn: Connection,
    **kwargs,
) -> Dict[str, Any]:
    """
    Unified tool for all UI operations via chat.

    Operations:
    - "upload": Upload files via chat
    - "navigate": Navigate user to specific pages
    - "voice": Process voice commands

    Args:
        operation: The type of UI operation to perform
        tenant_id: The tenant ID for data isolation
        conn: asyncpg connection
        **kwargs: Operation-specific parameters

    Returns:
        Dict with operation results
    """
    logger.info(f"UnifiedUIOperations: {operation} for tenant {tenant_id}")

    try:
        if operation == "upload":
            # Import and use existing function
            from .ui_equivalence_tools import upload_files_via_chat_tool_function

            return await upload_files_via_chat_tool_function(
                tenant_id=tenant_id, conn=conn, **kwargs
            )

        elif operation == "navigate":
            # Import and use existing function
            from .ui_equivalence_tools import navigate_user_to_page_tool_function

            return await navigate_user_to_page_tool_function(
                **kwargs  # This doesn't need tenant_id/db
            )

        elif operation == "voice":
            # Import and use existing function if available
            # For now, return not implemented
            return {
                "success": False,
                "error": "Voice commands not yet implemented",
                "message": "Please use text input for now",
            }

        else:
            return {
                "success": False,
                "error": f"Unknown operation: {operation}",
                "valid_operations": ["upload", "navigate", "voice"],
            }

    except Exception as e:
        logger.error(f"UnifiedUIOperations error: {e}", exc_info=True)
        return {"success": False, "error": str(e), "operation": operation}
