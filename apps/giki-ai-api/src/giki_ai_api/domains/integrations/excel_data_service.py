"""
Excel Data Service for processing external transaction files like Nuvie data.
Supports zero-onboarding by parsing raw transaction data without categories.
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd

logger = logging.getLogger(__name__)


class ExcelDataService:
    """Service for processing Excel files with transaction data"""

    def __init__(self):
        self.data_dir = (
            Path(__file__).parent.parent.parent.parent.parent / "data" / "test_files"
        )

    async def process_nuvie_file(self) -> Dict[str, Any]:
        """Process the Nuvie expense ledger file"""
        file_path = self.data_dir / "nuvie_expense_ledger.xlsx"

        if not file_path.exists():
            raise FileNotFoundError(f"Nuvie file not found at {file_path}")

        logger.info(f"Processing Nuvie file: {file_path}")

        try:
            # Read Excel file
            excel_file = pd.ExcelFile(file_path)

            # Get all sheet names
            sheet_names = excel_file.sheet_names
            logger.info(f"Found sheets: {sheet_names}")

            # Process each sheet
            sheets_data = {}
            for sheet_name in sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)

                # Check if first row contains headers (common in financial files)
                df_processed = self._process_headers(df)
                sheets_data[sheet_name] = self._analyze_sheet_structure(
                    df_processed, sheet_name
                )

            # Also extract actual transaction data for testing
            main_sheet = sheet_names[0]
            df = pd.read_excel(file_path, sheet_name=main_sheet)
            df_processed = self._process_headers(df)

            # Extract transactions
            transactions_with_categories = self._extract_transactions(
                df_processed, include_categories=True
            )
            transactions_clean = self._extract_transactions(
                df_processed, include_categories=False
            )

            return {
                "file_path": str(file_path),
                "sheets": sheets_data,
                "total_sheets": len(sheet_names),
                "transactions_with_original_categories": transactions_with_categories,
                "transactions_clean_for_testing": transactions_clean,
                "total_transactions": len(transactions_clean),
                "processed_at": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error processing Nuvie file: {e}")
            raise

    def _process_headers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process headers to handle cases where first row contains actual column names"""

        if len(df) == 0:
            return df

        # Check if first row looks like headers
        first_row = df.iloc[0]

        # Count how many values in first row are strings and look like column names
        header_indicators = 0
        total_non_null = 0

        for value in first_row:
            if pd.notna(value):
                total_non_null += 1
                if isinstance(value, str):
                    # Check for common header patterns
                    value_lower = value.lower()
                    if any(
                        header_word in value_lower
                        for header_word in [
                            "date",
                            "amount",
                            "description",
                            "narration",
                            "category",
                            "balance",
                            "withdrawal",
                            "deposit",
                            "credit",
                            "debit",
                            "vendor",
                            "remark",
                        ]
                    ):
                        header_indicators += 1

        # If more than 50% of non-null values look like headers, use first row as headers
        if total_non_null > 0 and header_indicators / total_non_null > 0.3:
            logger.info("Detected headers in first row, using as column names")

            # Create new column names from first row
            new_columns = []
            for i, col_name in enumerate(first_row):
                if pd.notna(col_name):
                    new_columns.append(str(col_name))
                else:
                    new_columns.append(f"Column_{i}")

            # Create new dataframe with proper headers
            df_new = df.iloc[1:].copy()  # Skip first row
            df_new.columns = new_columns[: len(df_new.columns)]  # Match column count
            df_new = df_new.reset_index(drop=True)

            return df_new

        return df

    def _analyze_sheet_structure(
        self, df: pd.DataFrame, sheet_name: str
    ) -> Dict[str, Any]:
        """Analyze the structure of a sheet to understand its data format"""

        # Basic info
        info = {
            "sheet_name": sheet_name,
            "rows": len(df),
            "columns": len(df.columns),
            "column_names": list(df.columns),
            "sample_data": df.head(3).to_dict("records") if len(df) > 0 else [],
        }

        # Try to identify transaction-like data
        transaction_indicators = self._identify_transaction_columns(df)
        if transaction_indicators["is_transaction_data"]:
            info["transaction_mapping"] = transaction_indicators
            info["suggested_schema"] = self._suggest_transaction_schema(
                df, transaction_indicators
            )

        return info

    def _identify_transaction_columns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Identify which columns contain transaction data"""

        # Handle non-string column names (e.g., numbers, floats)
        columns = [str(col).lower() for col in df.columns]

        # Look for common transaction column patterns
        date_columns = [
            col
            for col in columns
            if any(date_word in col for date_word in ["date", "time", "when"])
        ]
        amount_columns = [
            col
            for col in columns
            if any(
                amount_word in col
                for amount_word in [
                    "amount",
                    "value",
                    "price",
                    "cost",
                    "total",
                    "withdrawal",
                    "deposit",
                    "balance",
                ]
            )
        ]
        description_columns = [
            col
            for col in columns
            if any(
                desc_word in col
                for desc_word in [
                    "description",
                    "desc",
                    "memo",
                    "details",
                    "transaction",
                    "merchant",
                    "vendor",
                    "narration",
                    "particulars",
                ]
            )
        ]
        category_columns = [
            col
            for col in columns
            if any(
                cat_word in col for cat_word in ["category", "type", "class", "group"]
            )
        ]

        is_transaction_data = bool(
            date_columns and amount_columns and description_columns
        )

        return {
            "is_transaction_data": is_transaction_data,
            "date_columns": date_columns,
            "amount_columns": amount_columns,
            "description_columns": description_columns,
            "category_columns": category_columns,
            "confidence": self._calculate_confidence(
                df, date_columns, amount_columns, description_columns
            ),
        }

    def _calculate_confidence(
        self,
        df: pd.DataFrame,
        date_cols: List[str],
        amount_cols: List[str],
        desc_cols: List[str],
    ) -> float:
        """Calculate confidence that this is transaction data"""
        confidence = 0.0

        if date_cols:
            confidence += 0.3
        if amount_cols:
            confidence += 0.4
        if desc_cols:
            confidence += 0.3

        # Check if we have numeric data in amount columns
        if amount_cols and len(df) > 0:
            try:
                amount_col = (
                    df.columns[0]
                    if amount_cols[0] in [col.lower() for col in df.columns]
                    else df.columns[0]
                )
                numeric_ratio = pd.to_numeric(
                    df[amount_col], errors="coerce"
                ).notna().sum() / len(df)
                confidence += numeric_ratio * 0.2
            except Exception:
                pass

        return min(confidence, 1.0)

    def _suggest_transaction_schema(
        self, df: pd.DataFrame, indicators: Dict[str, Any]
    ) -> Dict[str, Optional[str]]:
        """Suggest mapping from sheet columns to our transaction schema"""

        columns = list(df.columns)
        mapping = {
            "date": None,
            "description": None,
            "amount": None,
            "category": None,
            "account": None,
        }

        # Map date column
        if indicators["date_columns"]:
            for col in columns:
                if str(col).lower() in indicators["date_columns"]:
                    mapping["date"] = col
                    break

        # Map amount column
        if indicators["amount_columns"]:
            for col in columns:
                if str(col).lower() in indicators["amount_columns"]:
                    mapping["amount"] = col
                    break

        # Map description column (prefer 'narration' for bank statements)
        if indicators["description_columns"]:
            # Priority order for description columns
            description_priority = [
                "narration",
                "description",
                "memo",
                "details",
                "particulars",
            ]
            for priority_col in description_priority:
                for col in columns:
                    if str(col).lower() == priority_col:
                        mapping["description"] = col
                        break
                if mapping["description"]:
                    break

            # If no priority match, use first found
            if not mapping["description"]:
                for col in columns:
                    if str(col).lower() in indicators["description_columns"]:
                        mapping["description"] = col
                        break

        # Map category column (might not exist in zero-onboarding scenario)
        if indicators["category_columns"]:
            for col in columns:
                if str(col).lower() in indicators["category_columns"]:
                    mapping["category"] = col
                    break

        return mapping

    async def extract_transactions(
        self, sheet_name: str = None
    ) -> List[Dict[str, Any]]:
        """Extract transactions in our standard format"""

        file_analysis = await self.process_nuvie_file()

        # Find the best sheet for transaction data
        best_sheet = None
        highest_confidence = 0

        for sheet_data in file_analysis["sheets"].values():
            if (
                sheet_data.get("transaction_mapping", {}).get("confidence", 0)
                > highest_confidence
            ):
                highest_confidence = sheet_data["transaction_mapping"]["confidence"]
                best_sheet = sheet_data

        if not best_sheet:
            raise ValueError("No transaction data found in Excel file")

        # Use specified sheet or best sheet
        target_sheet = sheet_name or best_sheet["sheet_name"]
        logger.info(f"Extracting transactions from sheet: {target_sheet}")

        # Read the specific sheet
        file_path = self.data_dir / "nuvie_expense_ledger.xlsx"
        df = pd.read_excel(file_path, sheet_name=target_sheet)
        df = self._process_headers(df)  # Process headers properly

        # Get the suggested schema mapping
        sheet_info = file_analysis["sheets"][target_sheet]
        schema_mapping = sheet_info["suggested_schema"]

        transactions = []
        for index, row in df.iterrows():
            try:
                transaction = self._map_row_to_transaction(row, schema_mapping)
                if transaction:  # Only add valid transactions
                    transactions.append(transaction)
            except Exception as e:
                logger.warning(f"Error processing row {index}: {e}")
                continue

        logger.info(f"Extracted {len(transactions)} transactions from {target_sheet}")
        return transactions

    def _map_row_to_transaction(
        self, row: pd.Series, schema_mapping: Dict[str, Optional[str]]
    ) -> Optional[Dict[str, Any]]:
        """Map a row to our transaction format"""

        transaction = {}

        # Map date
        if schema_mapping.get("date"):
            date_value = row[schema_mapping["date"]]
            if pd.notna(date_value):
                try:
                    if isinstance(date_value, str):
                        transaction["date"] = (
                            pd.to_datetime(date_value).date().isoformat()
                        )
                    else:
                        transaction["date"] = date_value.date().isoformat()
                except Exception:
                    return None  # Skip rows with invalid dates

        # Map description
        if schema_mapping.get("description"):
            desc_value = row[schema_mapping["description"]]
            if pd.notna(desc_value):
                transaction["description"] = str(desc_value).strip()

        # Map amount (handle withdrawal/deposit columns)
        amount = None
        transaction_type = None

        # Check for withdrawal amount
        if "Withdrawal Amt." in row and pd.notna(row["Withdrawal Amt."]):
            amount = float(row["Withdrawal Amt."])
            transaction_type = "debit"
        # Check for deposit amount
        elif "Deposit Amt." in row and pd.notna(row["Deposit Amt."]):
            amount = float(row["Deposit Amt."])
            transaction_type = "credit"
        # Fallback to mapped amount column
        elif schema_mapping.get("amount") and pd.notna(row[schema_mapping["amount"]]):
            try:
                amount_value = row[schema_mapping["amount"]]
                if isinstance(amount_value, str):
                    clean_amount = (
                        amount_value.replace("$", "").replace(",", "").replace("₹", "")
                    )
                    amount = float(clean_amount)
                else:
                    amount = float(amount_value)
            except Exception:
                return None

        if amount is not None:
            transaction["amount"] = amount
            if transaction_type:
                transaction["transaction_type"] = transaction_type

        # Map category if available (for reference, not used in zero-onboarding)
        if schema_mapping.get("category"):
            cat_value = row[schema_mapping["category"]]
            if pd.notna(cat_value):
                transaction["original_category"] = str(cat_value).strip()

        # Map account if available
        if schema_mapping.get("account"):
            acc_value = row[schema_mapping["account"]]
            if pd.notna(acc_value):
                transaction["account"] = str(acc_value).strip()

        # Only return transaction if we have the essential fields
        if all(key in transaction for key in ["date", "description", "amount"]):
            # Add metadata
            transaction["source"] = "nuvie_excel"
            transaction["currency"] = "USD"  # Default, could be detected
            return transaction

        return None


# Usage example for testing
if __name__ == "__main__":
    import asyncio

    async def test_nuvie_processing():
        service = ExcelDataService()

        # Analyze file structure
        analysis = await service.process_nuvie_file()
        print("File Analysis:")
        print(f"Total sheets: {analysis['total_sheets']}")

        for sheet_name, sheet_data in analysis["sheets"].items():
            print(f"\nSheet: {sheet_name}")
            print(f"Rows: {sheet_data['rows']}, Columns: {sheet_data['columns']}")
            print(f"Columns: {sheet_data['column_names']}")

            if sheet_data.get("transaction_mapping", {}).get("is_transaction_data"):
                print(
                    f"Transaction confidence: {sheet_data['transaction_mapping']['confidence']}"
                )
                print(f"Suggested mapping: {sheet_data['suggested_schema']}")

        # Extract transactions
        try:
            transactions = await service.extract_transactions()
            print(f"\nExtracted {len(transactions)} transactions")
            if transactions:
                print("Sample transaction:")
                print(transactions[0])
        except Exception as e:
            print(f"Error extracting transactions: {e}")

    asyncio.run(test_nuvie_processing())
