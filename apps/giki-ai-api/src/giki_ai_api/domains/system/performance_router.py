"""
Performance monitoring API endpoints for production metrics.
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...domains.auth.models import User
from ...domains.auth.secure_auth import get_current_active_user
from ...shared.services.system.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/performance", tags=["Performance Monitoring"])


class PerformanceSummaryResponse(BaseModel):
    """Response model for performance summary."""

    time_period_minutes: int
    timestamp: float
    api_performance: Dict[str, Any]
    database_performance: Dict[str, Any]
    endpoint_performance: Dict[str, Any]
    overall_health: Dict[str, Any]


class SystemStatusResponse(BaseModel):
    """Response model for system status."""

    timestamp: float
    total_requests_lifetime: int
    slow_requests_lifetime: int
    error_requests_lifetime: int
    metrics_in_memory: Dict[str, int]
    tracked_endpoints: int
    memory_usage_mb: float


@router.get("/summary", response_model=PerformanceSummaryResponse)
async def get_performance_summary(
    minutes: int = Query(default=5, ge=1, le=60, description="Time period in minutes"),
    current_user: User = Depends(get_current_active_user),
) -> PerformanceSummaryResponse:
    """
    Get performance summary for the specified time period.

    Requires: Authenticated user
    """
    try:
        summary = performance_monitor.get_performance_summary(minutes=minutes)
        return PerformanceSummaryResponse(**summary)
    except Exception as e:
        logger.error(f"Failed to get performance summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance summary",
        )


@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status(
    current_user: User = Depends(get_current_active_user),
) -> SystemStatusResponse:
    """
    Get current system status and real-time metrics.

    Requires: Authenticated user
    """
    try:
        status_data = performance_monitor.get_current_status()
        return SystemStatusResponse(**status_data)
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system status",
        )


@router.get("/health")
async def get_health_check() -> Dict[str, Any]:
    """
    Public health check endpoint for load balancer and monitoring.

    No authentication required.
    """
    try:
        # Get recent performance data
        summary = performance_monitor.get_performance_summary(minutes=1)
        health = summary.get("overall_health", {})

        # Basic health indicators
        return {
            "status": "healthy",
            "timestamp": summary.get("timestamp"),
            "performance_status": health.get("status", "unknown"),
            "performance_score": health.get("score", 0),
            "api_requests_last_minute": summary.get("api_performance", {}).get(
                "total_requests", 0
            ),
            "avg_response_time_ms": summary.get("api_performance", {}).get(
                "avg_response_time_ms", 0
            ),
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": None,
            "error": "Health check failed",
            "performance_status": "unknown",
        }


@router.get("/endpoints")
async def get_endpoint_performance(
    limit: int = Query(
        default=20, ge=1, le=100, description="Number of endpoints to return"
    ),
    sort_by: str = Query(
        default="requests",
        pattern="^(requests|avg_time|errors)$",
        description="Sort criteria",
    ),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """
    Get performance statistics for all tracked endpoints.

    Requires: Authenticated user
    """
    try:
        # Get all endpoint stats
        endpoint_stats = dict(performance_monitor.endpoint_stats)

        # Convert to list format
        endpoints = []
        for endpoint, stats in endpoint_stats.items():
            if stats.total_requests > 0:  # Only include endpoints with activity
                endpoints.append(
                    {
                        "endpoint": endpoint,
                        "total_requests": stats.total_requests,
                        "avg_time_ms": stats.avg_time_ms,
                        "min_time_ms": stats.min_time_ms,
                        "max_time_ms": stats.max_time_ms,
                        "error_count": stats.error_count,
                        "error_rate_percent": (stats.error_count / stats.total_requests)
                        * 100,
                        "slow_requests": stats.slow_requests,
                        "slow_request_rate_percent": (
                            stats.slow_requests / stats.total_requests
                        )
                        * 100,
                    }
                )

        # Sort by specified criteria
        if sort_by == "requests":
            endpoints.sort(key=lambda x: x["total_requests"], reverse=True)
        elif sort_by == "avg_time":
            endpoints.sort(key=lambda x: x["avg_time_ms"], reverse=True)
        elif sort_by == "errors":
            endpoints.sort(key=lambda x: x["error_count"], reverse=True)

        # Apply limit
        endpoints = endpoints[:limit]

        return {
            "endpoints": endpoints,
            "total_tracked": len(endpoint_stats),
            "returned": len(endpoints),
            "sorted_by": sort_by,
        }

    except Exception as e:
        logger.error(f"Failed to get endpoint performance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve endpoint performance",
        )


@router.post("/reset")
async def reset_performance_metrics(
    current_user: User = Depends(get_current_active_user),
    confirm: bool = Query(default=False, description="Must be true to confirm reset"),
) -> Dict[str, str]:
    """
    Reset all performance metrics (admin only, use with caution).

    Requires: Superuser privileges
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only superusers can reset performance metrics",
        )

    if not confirm:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Must set confirm=true to reset metrics",
        )

    try:
        performance_monitor.reset_all_metrics()
        logger.warning(f"Performance metrics reset by user {current_user.email}")
        return {"message": "Performance metrics have been reset"}
    except Exception as e:
        logger.error(f"Failed to reset performance metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset performance metrics",
        )


@router.get("/alerts")
async def get_performance_alerts(
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """
    Get current performance alerts and warnings.

    Requires: Authenticated user
    """
    try:
        # Get performance summary for alerts
        summary = performance_monitor.get_performance_summary(minutes=5)
        health = summary.get("overall_health", {})
        api_perf = summary.get("api_performance", {})
        db_perf = summary.get("database_performance", {})

        alerts = []
        warnings = []

        # Check for critical issues
        if health.get("score", 100) < 40:
            alerts.append(
                {
                    "type": "critical",
                    "message": "Critical performance degradation detected",
                    "score": health.get("score", 0),
                    "recommendation": health.get("recommendation", ""),
                }
            )

        # Check for warnings
        if api_perf.get("error_rate_percent", 0) > 5:
            warnings.append(
                {
                    "type": "error_rate",
                    "message": f"High error rate: {api_perf.get('error_rate_percent', 0):.1f}%",
                    "threshold": "5%",
                }
            )

        if api_perf.get("avg_response_time_ms", 0) > 500:
            warnings.append(
                {
                    "type": "response_time",
                    "message": f"High average response time: {api_perf.get('avg_response_time_ms', 0):.1f}ms",
                    "threshold": "500ms",
                }
            )

        if db_perf.get("avg_duration_ms", 0) > 200:
            warnings.append(
                {
                    "type": "database",
                    "message": f"High database operation time: {db_perf.get('avg_duration_ms', 0):.1f}ms",
                    "threshold": "200ms",
                }
            )

        return {
            "timestamp": summary.get("timestamp"),
            "health_status": health.get("status", "unknown"),
            "health_score": health.get("score", 0),
            "alerts": alerts,
            "warnings": warnings,
            "issues": health.get("issues", []),
            "recommendation": health.get("recommendation", ""),
        }

    except Exception as e:
        logger.error(f"Failed to get performance alerts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance alerts",
        )
