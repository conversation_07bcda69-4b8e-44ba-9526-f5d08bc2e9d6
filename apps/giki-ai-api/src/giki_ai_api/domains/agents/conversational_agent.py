"""
ConversationalAgent - Central command router for all 11 frontend commands

This agent handles command routing and coordination between frontend and backend services.
It provides a unified interface for all agent interactions, routing commands to appropriate
domain services while maintaining conversation context.

Supported Commands:
- /upload: File upload and processing
- /filter: Transaction filtering
- /export: Data export operations
- /categorize: Transaction categorization
- /delete: Transaction deletion
- /report: Report generation
- /analyze: Data analysis
- /settings: Settings management
- /search: Data search operations
- /create: Create new entities
- /refresh: Refresh/reload data
"""

import logging
import time
from typing import Any, Dict, List, Optional

from asyncpg import Connection
from fastapi import HTTPException

logger = logging.getLogger(__name__)


class ConversationalAgent:
    """
    Central command router for frontend agent interactions.

    Routes all 11 frontend commands to appropriate domain services
    and maintains conversation context for seamless user experience.
    """

    def __init__(self, conn: Connection, vertex_client=None):
        self.conn = conn
        self.vertex_client = vertex_client
        
        # Initialize WebSocket service for real-time communication
        from ...shared.services.websocket_service import WebSocketService
        self.ws_service = WebSocketService()

        # Command routing map
        self.command_handlers = {
            "/upload": self.handle_upload_command,
            "/filter": self.handle_filter_command,
            "/export": self.handle_export_command,
            "/categorize": self.handle_categorize_command,
            "/delete": self.handle_delete_command,
            "/report": self.handle_report_command,
            "/analyze": self.handle_analyze_command,
            "/settings": self.handle_settings_command,
            "/search": self.handle_search_command,
            "/create": self.handle_create_command,
            "/refresh": self.handle_refresh_command,
        }
    
    async def emit_agent_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        tenant_id: int,
        user_id: Optional[int] = None,
    ):
        """
        Emit real-time agent events to frontend via WebSocket.
        
        Args:
            event_type: Type of event (e.g., 'agent.processing', 'agent.response')
            data: Event data payload
            tenant_id: Tenant ID for proper routing
            user_id: Optional user ID for user-specific events
        """
        try:
            await self.ws_service.emit_event(
                event_type=event_type,
                data={
                    **data,
                    "user_id": user_id,
                    "timestamp": time.time(),
                },
                tenant_id=tenant_id,
            )
        except Exception as e:
            logger.warning(f"Failed to emit agent event {event_type}: {e}")
    
    async def emit_agent_typing(self, tenant_id: int, user_id: Optional[int] = None):
        """Emit typing indicator for agent responses."""
        await self.emit_agent_event("agent.typing", {}, tenant_id, user_id)
    
    async def emit_agent_response(
        self,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        tenant_id: int = 0,
        user_id: Optional[int] = None,
    ):
        """Emit agent response to frontend."""
        await self.emit_agent_event(
            "agent.response",
            {
                "message": message,
                "data": data or {},
            },
            tenant_id,
            user_id,
        )

    async def process_command(
        self,
        command: str,
        parameters: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None,
        tenant_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Process a frontend command and route to appropriate handler.

        Args:
            command: Command name (with or without /)
            parameters: Command-specific parameters
            context: Additional context information
            user_id: User ID for authentication
            tenant_id: Tenant ID for data isolation

        Returns:
            Structured response with command results
        """
        start_time = time.time()

        try:
            # Normalize command (ensure starts with /)
            normalized_command = command if command.startswith("/") else f"/{command}"

            # Validate command exists
            if normalized_command not in self.command_handlers:
                raise HTTPException(
                    status_code=400,
                    detail=f"Unknown command: {normalized_command}. Supported commands: {list(self.command_handlers.keys())}",
                )

            # Set up context
            full_context = {
                "user_id": user_id,
                "tenant_id": tenant_id,
                "command": normalized_command,
                **(context or {}),
            }

            # Emit processing start event
            await self.emit_agent_event(
                "agent.processing_started",
                {
                    "command": normalized_command,
                    "parameters": parameters,
                },
                tenant_id or 0,
                user_id,
            )

            # Route to appropriate handler
            handler = self.command_handlers[normalized_command]
            result = await handler(parameters or {}, full_context)
            
            # Emit processing completed event
            await self.emit_agent_event(
                "agent.processing_completed",
                {
                    "command": normalized_command,
                    "result": result,
                },
                tenant_id or 0,
                user_id,
            )

            # Calculate execution time
            execution_time_ms = (time.time() - start_time) * 1000

            # Return structured response
            return {
                "success": True,
                "command": normalized_command,
                "result": result,
                "message": result.get(
                    "message", f"{normalized_command} command executed successfully"
                ),
                "agent_type": result.get("agent_type", "conversational"),
                "execution_time_ms": execution_time_ms,
                "context": full_context,
            }

        except Exception as e:
            execution_time_ms = (time.time() - start_time) * 1000
            logger.error(f"Command processing failed for {command}: {e}")

            # Emit error event
            await self.emit_agent_event(
                "agent.error",
                {
                    "command": command,
                    "error": str(e),
                },
                tenant_id or 0,
                user_id,
            )

            return {
                "success": False,
                "command": command,
                "result": {},
                "message": f"Command failed: {str(e)}",
                "agent_type": "conversational",
                "execution_time_ms": execution_time_ms,
                "error": str(e),
            }

    async def handle_upload_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle file upload and processing commands."""
        try:
            from ..categories.categorization_agent import CategorizationAgent
            from ..files.service import FileService
            from ..transactions.service import TransactionService

            file_service = FileService(self.conn)
            tenant_id = context.get("tenant_id")
            user_id = context.get("user_id")

            # Check if this is file upload or status check
            if "file_data" in parameters:
                # Process file upload
                file_content = parameters["file_data"]
                filename = parameters.get("filename", "upload.xlsx")

                # Process the file to extract data
                processing_result = await file_service.process_file(
                    file_content=file_content, filename=filename, tenant_id=tenant_id
                )

                # If column mapping is provided, extract transactions
                if "column_mapping" in parameters and processing_result.sheets:
                    transaction_service = TransactionService(self.conn)

                    # Extract transactions from first sheet by default
                    sheet_data = processing_result.sheets[0].sample_data
                    transactions_data = await file_service.extract_transactions(
                        sheet_data=sheet_data,
                        column_mapping=parameters["column_mapping"],
                        tenant_id=tenant_id,
                    )

                    # Create transactions in batch
                    from ..transactions.schemas import TransactionCreate

                    transaction_creates = [
                        TransactionCreate(
                            description=tx.get("description", ""),
                            amount=tx.get("amount", 0),
                            date=tx.get("date"),
                            account=tx.get("account"),
                            transaction_type=tx.get("transaction_type"),
                        )
                        for tx in transactions_data
                    ]

                    result = await transaction_service.bulk_create_transactions(
                        transactions_data=transaction_creates,
                        tenant_id=tenant_id,
                        upload_id=processing_result.upload_id,
                    )

                    # If onboarding mode, learn categories
                    if parameters.get("onboarding_mode", False):
                        categorization_agent = CategorizationAgent(self.conn)
                        learning_result = await categorization_agent.learn_categories_from_onboarding_data(
                            transactions=transactions_data,
                            tenant_id=tenant_id,
                            user_id=user_id,
                        )

                        return {
                            "agent_type": "file_processing",
                            "message": f"Onboarding successful! Processed {result['successful_creations']} transactions and learned {learning_result.get('total_categories_created', 0)} categories.",
                            "upload_id": processing_result.upload_id,
                            "status": "completed",
                            "transactions_created": result["successful_creations"],
                            "categories_learned": learning_result.get(
                                "total_categories_created", 0
                            ),
                            "processing_errors": result.get("errors", []),
                        }
                    else:
                        return {
                            "agent_type": "file_processing",
                            "message": f"File processed successfully! Created {result['successful_creations']} transactions.",
                            "upload_id": processing_result.upload_id,
                            "status": "completed",
                            "transactions_created": result["successful_creations"],
                            "failed_transactions": result["failed_creations"],
                            "processing_errors": result.get("errors", []),
                        }
                else:
                    # Return file analysis for column mapping
                    return {
                        "agent_type": "file_processing",
                        "message": f"File analyzed successfully. Found {len(processing_result.sheets)} sheet(s) with {processing_result.total_rows} total rows.",
                        "upload_id": processing_result.upload_id,
                        "status": "needs_mapping",
                        "sheets": [
                            {
                                "name": sheet.name,
                                "columns": sheet.columns,
                                "row_count": sheet.row_count,
                                "sample_data": sheet.sample_data[
                                    :3
                                ],  # First 3 rows for preview
                            }
                            for sheet in processing_result.sheets
                        ],
                        "suggested_mapping": file_service._detect_transaction_columns(
                            processing_result.sheets[0].columns
                        )
                        if processing_result.sheets
                        else {},
                    }
            else:
                # Return upload status or instructions
                supported_formats = await file_service.get_supported_formats()
                return {
                    "agent_type": "file_processing",
                    "message": "Ready to process your financial data. Upload an Excel or CSV file to begin.",
                    "supported_formats": supported_formats,
                    "max_file_size": "10MB",
                    "upload_instructions": {
                        "step1": "Select your transaction file (Excel or CSV)",
                        "step2": "Map columns to transaction fields if needed",
                        "step3": "Review and confirm the import",
                    },
                }

        except Exception as e:
            logger.error(f"Upload command failed: {e}")
            return {
                "agent_type": "file_processing",
                "message": f"Upload failed: {str(e)}",
                "error": str(e),
                "status": "failed",
            }

    async def handle_filter_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle transaction filtering commands."""
        try:
            from ..transactions.service import TransactionService

            transaction_service = TransactionService(self.conn)
            tenant_id = context.get("tenant_id")

            # Build comprehensive filters
            filters = {}

            # Date range filters
            if parameters.get("date_from"):
                filters["date_from"] = parameters["date_from"]
            if parameters.get("date_to"):
                filters["date_to"] = parameters["date_to"]

            # Amount range filters
            if parameters.get("min_amount") is not None:
                filters["amount_min"] = parameters["min_amount"]
            if parameters.get("max_amount") is not None:
                filters["amount_max"] = parameters["max_amount"]

            # Category filter
            if parameters.get("category"):
                filters["category"] = parameters["category"]
            if parameters.get("category_id"):
                filters["category_id"] = parameters["category_id"]

            # Text search in description
            if parameters.get("description"):
                filters["description"] = parameters["description"]

            # Account filter
            if parameters.get("account"):
                filters["account"] = parameters["account"]

            # Transaction type filter
            if parameters.get("transaction_type"):
                filters["transaction_type"] = parameters["transaction_type"]

            # Pagination
            page = parameters.get("page", 1)
            per_page = parameters.get("per_page", 50)

            # Get filtered transactions
            result = await transaction_service.get_transactions(
                tenant_id=tenant_id,
                filters=filters,
                page=page,
                per_page=per_page,
                order_by=parameters.get("order_by", "date"),
                order_dir=parameters.get("order_dir", "desc"),
            )

            # Calculate summary statistics
            transactions = result["transactions"]
            total_amount = sum(t.amount for t in transactions if t.amount)

            # Group by category for insights
            category_breakdown = {}
            for tx in transactions:
                cat = tx.category_id or "Uncategorized"
                if cat not in category_breakdown:
                    category_breakdown[cat] = {"count": 0, "amount": 0}
                category_breakdown[cat]["count"] += 1
                category_breakdown[cat]["amount"] += (
                    float(tx.amount) if tx.amount else 0
                )

            return {
                "agent_type": "data_query",
                "message": f"Found {result['total']} transactions matching your filters.",
                "transactions": [
                    {
                        "id": tx.id,
                        "date": tx.date.isoformat() if tx.date else None,
                        "description": tx.description,
                        "amount": float(tx.amount) if tx.amount else 0,
                        "category_id": tx.category_id,
                        "account": tx.account,
                        "transaction_type": tx.transaction_type,
                    }
                    for tx in transactions
                ],
                "filters_applied": filters,
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total": result["total"],
                    "total_pages": (result["total"] + per_page - 1) // per_page,
                },
                "summary": {
                    "total_amount": total_amount,
                    "average_amount": total_amount / len(transactions)
                    if transactions
                    else 0,
                    "category_breakdown": category_breakdown,
                },
            }

        except Exception as e:
            logger.error(f"Filter command failed: {e}")
            return {
                "agent_type": "data_query",
                "message": f"Filter failed: {str(e)}",
                "error": str(e),
            }

    async def handle_export_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle data export operations."""
        try:
            import base64
            from datetime import datetime

            from ..categories.crud_service import CategoryCrudService
            from ..reports.service import ReportGenerator

            report_service = ReportGenerator(self.conn)
            tenant_id = context.get("tenant_id")

            export_format = parameters.get("format", "excel").lower()
            export_type = parameters.get("type", "transactions")

            # Validate export format
            valid_formats = ["excel", "csv", "pdf"]
            if export_format not in valid_formats:
                return {
                    "agent_type": "data_export",
                    "message": f"Invalid export format: {export_format}",
                    "supported_formats": valid_formats,
                }

            if export_type == "transactions":
                # Build date range
                date_range = parameters.get("date_range", {})
                if not date_range:
                    # Default to current month
                    today = datetime.now()
                    date_range = {
                        "start_date": today.replace(day=1).strftime("%Y-%m-%d"),
                        "end_date": today.strftime("%Y-%m-%d"),
                    }

                # Generate report with filters
                filters = parameters.get("filters", {})
                report_data = await report_service.generate_report(
                    tenant_id=tenant_id,
                    report_type="transaction_summary",
                    date_range=date_range,
                    filters=filters,
                )

                # Generate export content
                if export_format == "excel":
                    export_content = await report_service._generate_excel_export(
                        report_data, tenant_id
                    )
                    mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    file_extension = "xlsx"
                elif export_format == "csv":
                    # Convert transactions to CSV
                    import csv
                    import io

                    output = io.StringIO()
                    writer = csv.writer(output)

                    # Write headers
                    writer.writerow(
                        ["Date", "Description", "Amount", "Category", "Account", "Type"]
                    )

                    # Write transaction data
                    for tx in report_data.get("data", []):
                        writer.writerow(
                            [
                                tx.get("date", ""),
                                tx.get("description", ""),
                                tx.get("amount", 0),
                                tx.get("category_id", "Uncategorized"),
                                tx.get("account", ""),
                                tx.get("transaction_type", ""),
                            ]
                        )

                    export_content = output.getvalue().encode("utf-8")
                    mime_type = "text/csv"
                    file_extension = "csv"
                else:  # PDF
                    export_content = await report_service._generate_pdf_report(
                        report_data, tenant_id
                    )
                    mime_type = "application/pdf"
                    file_extension = "pdf"

                # Create download link (base64 encoded for immediate download)
                download_data = base64.b64encode(export_content).decode("utf-8")

                return {
                    "agent_type": "data_export",
                    "message": f"Transaction export ready ({len(report_data.get('data', []))} records).",
                    "export_type": export_type,
                    "format": export_format,
                    "date_range": date_range,
                    "filters_applied": filters,
                    "record_count": len(report_data.get("data", [])),
                    "file_size_bytes": len(export_content),
                    "download": {
                        "filename": f"transactions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}",
                        "mime_type": mime_type,
                        "data_base64": download_data
                        if len(export_content) < 5 * 1024 * 1024
                        else None,  # Include data if < 5MB
                        "download_url": f"/api/v1/exports/{tenant_id}/{export_type}_{datetime.now().timestamp()}.{file_extension}"
                        if len(export_content) >= 5 * 1024 * 1024
                        else None,
                    },
                }

            elif export_type == "categories":
                # Export category hierarchy
                category_service = CategoryCrudService(self.conn)
                hierarchy = await category_service.get_category_hierarchy(tenant_id)

                # Create CSV export of categories
                import csv
                import io

                output = io.StringIO()
                writer = csv.writer(output)

                # Write headers
                writer.writerow(
                    [
                        "Name",
                        "Code",
                        "Path",
                        "Level",
                        "Parent",
                        "GL Code",
                        "Usage Count",
                    ]
                )

                # Flatten hierarchy for export
                def flatten_categories(categories, parent_name=""):
                    rows = []
                    for cat in categories:
                        rows.append(
                            [
                                cat.get("name", ""),
                                cat.get("code", ""),
                                cat.get("path", ""),
                                cat.get("level", 0),
                                parent_name,
                                cat.get("gl_code", ""),
                                cat.get("usage_count", 0),
                            ]
                        )
                        if cat.get("children"):
                            rows.extend(
                                flatten_categories(cat["children"], cat["name"])
                            )
                    return rows

                for row in flatten_categories(hierarchy.root_categories):
                    writer.writerow(row)

                export_content = output.getvalue().encode("utf-8")
                download_data = base64.b64encode(export_content).decode("utf-8")

                return {
                    "agent_type": "data_export",
                    "message": f"Category export ready ({hierarchy.total_categories} categories).",
                    "export_type": export_type,
                    "format": "csv",
                    "category_count": hierarchy.total_categories,
                    "max_depth": hierarchy.max_depth,
                    "download": {
                        "filename": f"categories_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                        "mime_type": "text/csv",
                        "data_base64": download_data,
                    },
                }

            elif export_type == "report":
                # Export specific report type
                report_type = parameters.get("report_type", "comprehensive")

                if report_type == "comprehensive":
                    report_data = await report_service.generate_comprehensive_report(
                        tenant_id=tenant_id, report_config=parameters.get("config", {})
                    )
                else:
                    report_data = await report_service.generate_report(
                        tenant_id=tenant_id,
                        report_type=report_type,
                        date_range=parameters.get("date_range", {}),
                        filters=parameters.get("filters", {}),
                    )

                # Generate PDF report
                export_content = await report_service._generate_pdf_report(
                    report_data, tenant_id
                )
                download_data = base64.b64encode(export_content).decode("utf-8")

                return {
                    "agent_type": "data_export",
                    "message": f"{report_type.replace('_', ' ').title()} report exported successfully.",
                    "export_type": "report",
                    "report_type": report_type,
                    "format": "pdf",
                    "download": {
                        "filename": f"{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                        "mime_type": "application/pdf",
                        "data_base64": download_data,
                    },
                }

            else:
                return {
                    "agent_type": "data_export",
                    "message": f"Unsupported export type: {export_type}",
                    "supported_types": ["transactions", "categories", "report"],
                    "usage_examples": [
                        "/export type: transactions format: excel",
                        "/export type: categories",
                        "/export type: report report_type: monthly_trends",
                    ],
                }

        except Exception as e:
            logger.error(f"Export command failed: {e}")
            return {
                "agent_type": "data_export",
                "message": f"Export failed: {str(e)}",
                "error": str(e),
            }

    async def handle_categorize_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle transaction categorization commands with hierarchical support."""
        try:
            from ..categories.categorization_agent import CategorizationAgent
            from ..categories.crud_service import CategoryCrudService
            from ..transactions.service import TransactionService

            tenant_id = context.get("tenant_id")
            
            # Check if hierarchical categorization is requested
            if parameters.get("mode") == "hierarchical" or "hierarchical" in parameters:
                return await self._handle_hierarchical_categorization(parameters, context, tenant_id)
            
            # Standard categorization flow
            categorization_agent = CategorizationAgent(self.conn)

            # Handle batch categorization of transactions
            if "transaction_ids" in parameters:
                transaction_service = TransactionService(self.conn)
                transaction_ids = parameters["transaction_ids"]
                if not isinstance(transaction_ids, list):
                    transaction_ids = [transaction_ids]

                categorized_count = 0
                categorization_results = []

                for tx_id in transaction_ids:
                    try:
                        # Get transaction
                        transaction = await transaction_service.get_transaction(
                            tx_id, tenant_id
                        )
                        if not transaction:
                            categorization_results.append(
                                {
                                    "transaction_id": tx_id,
                                    "status": "not_found",
                                    "error": "Transaction not found",
                                }
                            )
                            continue

                        # Get AI suggestions
                        suggestions = await categorization_agent.suggest_categories(
                            transaction_description=transaction.description,
                            amount=float(transaction.amount)
                            if transaction.amount
                            else None,
                            tenant_id=tenant_id,
                        )

                        if suggestions:
                            best_suggestion = suggestions[0]
                            # Update transaction with suggested category
                            await transaction_service.update_transaction(
                                transaction_id=tx_id,
                                update_data={
                                    "ai_suggested_category": best_suggestion.category_name,
                                    "ai_confidence": best_suggestion.confidence,
                                },
                                tenant_id=tenant_id,
                            )

                            categorization_results.append(
                                {
                                    "transaction_id": tx_id,
                                    "status": "categorized",
                                    "category": best_suggestion.category_name,
                                    "confidence": best_suggestion.confidence,
                                    "reasoning": best_suggestion.reasoning,
                                }
                            )
                            categorized_count += 1
                        else:
                            categorization_results.append(
                                {
                                    "transaction_id": tx_id,
                                    "status": "no_suggestion",
                                    "category": "Uncategorized",
                                }
                            )

                    except Exception as tx_error:
                        logger.warning(
                            f"Failed to categorize transaction {tx_id}: {tx_error}"
                        )
                        categorization_results.append(
                            {
                                "transaction_id": tx_id,
                                "status": "error",
                                "error": str(tx_error),
                            }
                        )

                return {
                    "agent_type": "categorization",
                    "message": f"Categorized {categorized_count} out of {len(transaction_ids)} transactions.",
                    "batch_mode": True,
                    "total_processed": len(transaction_ids),
                    "successfully_categorized": categorized_count,
                    "results": categorization_results,
                }

            elif "transaction_description" in parameters:
                # Categorize single transaction by description
                suggestions = await categorization_agent.suggest_categories(
                    transaction_description=parameters["transaction_description"],
                    amount=parameters.get("amount"),
                    tenant_id=tenant_id,
                )

                # Get existing categories for context
                category_service = CategoryCrudService(self.conn)
                existing_categories = await category_service.get_categories(
                    tenant_id=tenant_id, limit=100
                )

                return {
                    "agent_type": "categorization",
                    "message": f"Found {len(suggestions)} category suggestions for your transaction.",
                    "suggestions": [
                        {
                            "category": s.category_name,
                            "confidence": s.confidence,
                            "reasoning": s.reasoning,
                            "alternatives": s.alternatives or [],
                            "exists": any(
                                cat["name"] == s.category_name
                                for cat in existing_categories
                            ),
                        }
                        for s in suggestions
                    ],
                    "best_match": suggestions[0].category_name
                    if suggestions
                    else "Uncategorized",
                    "existing_categories_count": len(existing_categories),
                }

            elif "learn_from_file" in parameters:
                # Learn categories from uploaded file
                file_id = parameters["learn_from_file"]
                transaction_service = TransactionService(self.conn)

                # Get transactions from upload
                transactions, _ = await transaction_service.get_transactions(
                    tenant_id=tenant_id, filters={"upload_id": file_id}, limit=1000
                )

                if not transactions:
                    return {
                        "agent_type": "categorization",
                        "message": "No transactions found for the specified upload.",
                        "error": "NO_TRANSACTIONS",
                    }

                # Convert to dict format for learning
                transactions_data = [
                    {
                        "description": tx.description,
                        "amount": float(tx.amount) if tx.amount else 0,
                        "date": tx.date.isoformat() if tx.date else None,
                        "category": tx.category_id,  # Use existing category if available
                    }
                    for tx in transactions
                ]

                # Learn categories
                learning_result = (
                    await categorization_agent.learn_categories_from_onboarding_data(
                        transactions=transactions_data,
                        tenant_id=tenant_id,
                        user_id=context.get("user_id"),
                    )
                )

                return {
                    "agent_type": "categorization",
                    "message": f"Successfully learned {learning_result['total_categories_created']} categories from {len(transactions)} transactions.",
                    "learning_mode": True,
                    "categories_created": learning_result["total_categories_created"],
                    "parent_categories": learning_result["parent_categories"],
                    "child_categories": learning_result["child_categories"],
                    "hierarchies_detected": learning_result["hierarchies_detected"],
                }

            else:
                # Return categorization help and status
                category_service = CategoryCrudService(self.conn)
                category_stats = await category_service.get_category_usage_stats(
                    tenant_id
                )

                return {
                    "agent_type": "categorization",
                    "message": "AI-powered categorization ready. Provide transaction details or IDs to categorize.",
                    "usage_examples": [
                        '/categorize transaction_description: "Office supplies from Staples" amount: 45.67',
                        '/categorize transaction_ids: ["tx-123", "tx-456"]',
                        '/categorize learn_from_file: "upload-789"',
                    ],
                    "current_status": {
                        "total_categories": category_stats.get("total_categories", 0),
                        "categories_in_use": category_stats.get(
                            "categories_with_usage", 0
                        ),
                        "most_used": category_stats.get("most_used", []),
                    },
                }

        except Exception as e:
            logger.error(f"Categorize command failed: {e}")
            return {
                "agent_type": "categorization",
                "message": f"Categorization failed: {str(e)}",
                "error": str(e),
            }

    async def _handle_hierarchical_categorization(
        self, parameters: Dict[str, Any], context: Dict[str, Any], tenant_id: int
    ) -> Dict[str, Any]:
        """Handle hierarchical categorization with 3-level categories and bulk processing."""
        try:
            from ..categories.mis_categorization_agent import MISCategorizationAgent
            from ..transactions.service import TransactionService

            agent = MISCategorizationAgent(tenant_id=tenant_id)
            transaction_service = TransactionService(self.conn)

            # Handle bulk categorization
            if "transaction_ids" in parameters:
                transaction_ids = parameters["transaction_ids"]
                
                # Handle special case for "all_uncategorized"
                if transaction_ids == "all_uncategorized":
                    transactions, _ = await transaction_service.get_transactions(
                        tenant_id=tenant_id,
                        filters={"category_id": None},  # Uncategorized transactions
                        limit=1000
                    )
                    transaction_ids = [tx.id for tx in transactions]
                elif not isinstance(transaction_ids, list):
                    transaction_ids = [transaction_ids]

                # Get transactions data
                transactions = []
                for tx_id in transaction_ids:
                    tx = await transaction_service.get_transaction(tx_id, tenant_id)
                    if tx:
                        transactions.append({
                            "id": str(tx.id),
                            "description": tx.description,
                            "amount": float(tx.amount) if tx.amount else 0,
                            "date": tx.date.isoformat() if tx.date else None
                        })

                if not transactions:
                    return {
                        "agent_type": "hierarchical_categorization",
                        "message": "No transactions found for categorization",
                        "results": {}
                    }

                # Perform bulk categorization with grouping
                results = await agent.categorize_transactions_bulk(
                    transactions=transactions,
                    db_conn=self.conn,
                    group_similar=parameters.get("group_similar", True)
                )

                # Update transactions with hierarchical categories
                updates_successful = 0
                updates_failed = 0
                
                for tx_id, result in results.items():
                    try:
                        # Update transaction with hierarchical data
                        await transaction_service.update_transaction(
                            transaction_id=tx_id,
                            update_data={
                                "ai_suggested_category": result.level3_category,
                                "ai_confidence": result.confidence,
                                "notes": f"Hierarchical: {result.category_path}"
                            },
                            tenant_id=tenant_id
                        )
                        updates_successful += 1
                        
                    except Exception as update_error:
                        logger.error(f"Failed to update transaction {tx_id}: {update_error}")
                        updates_failed += 1

                # Count grouped transactions
                grouped_count = sum(1 for result in results.values() if result.similar_transactions)
                
                return {
                    "agent_type": "hierarchical_categorization",
                    "message": f"Successfully categorized {updates_successful} transactions with hierarchical structure. {grouped_count} transaction groups identified.",
                    "mode": "hierarchical",
                    "transactions_processed": len(transactions),
                    "updates_successful": updates_successful,
                    "updates_failed": updates_failed,
                    "grouped_transactions": grouped_count,
                    "results": {
                        tx_id: {
                            "category_path": result.category_path,
                            "confidence": result.confidence,
                            "similar_count": len(result.similar_transactions or [])
                        }
                        for tx_id, result in results.items()
                    }
                }

            # Handle single transaction categorization
            elif "transaction_id" in parameters:
                tx_id = parameters["transaction_id"]
                transaction = await transaction_service.get_transaction(tx_id, tenant_id)
                
                if not transaction:
                    return {
                        "agent_type": "hierarchical_categorization",
                        "message": "Transaction not found",
                        "error": "TRANSACTION_NOT_FOUND"
                    }

                # Categorize single transaction
                result = await agent.categorize_transaction_hierarchical(
                    description=transaction.description,
                    amount=float(transaction.amount) if transaction.amount else 0,
                    db_conn=self.conn,
                    transaction_date=transaction.date.isoformat() if transaction.date else None
                )

                # Update transaction
                await transaction_service.update_transaction(
                    transaction_id=tx_id,
                    update_data={
                        "ai_suggested_category": result.level3_category,
                        "ai_confidence": result.confidence,
                        "notes": f"Hierarchical: {result.category_path}"
                    },
                    tenant_id=tenant_id
                )

                return {
                    "agent_type": "hierarchical_categorization",
                    "message": f"Transaction categorized as: {result.category_path}",
                    "mode": "hierarchical",
                    "transaction_id": tx_id,
                    "category_path": result.category_path,
                    "confidence": result.confidence,
                    "reasoning": result.reasoning
                }

            else:
                return {
                    "agent_type": "hierarchical_categorization",
                    "message": "Please specify transaction_ids or transaction_id for hierarchical categorization",
                    "error": "MISSING_TRANSACTION_PARAMETERS",
                    "examples": [
                        "/categorize mode: hierarchical transaction_ids: [1,2,3] group_similar: true",
                        "/categorize mode: hierarchical transaction_ids: all_uncategorized",
                        "/categorize mode: hierarchical transaction_id: 123"
                    ]
                }

        except Exception as e:
            logger.error(f"Hierarchical categorization failed: {e}")
            return {
                "agent_type": "hierarchical_categorization",
                "message": f"Hierarchical categorization failed: {str(e)}",
                "error": str(e),
            }

    async def handle_delete_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle transaction deletion commands."""
        try:
            from ..categories.crud_service import CategoryCrudService
            from ..transactions.service import TransactionService

            tenant_id = context.get("tenant_id")

            # Determine what to delete
            delete_type = parameters.get("type", "transactions")

            if delete_type == "transactions":
                transaction_service = TransactionService(self.conn)

                if "transaction_ids" in parameters:
                    # Delete specific transactions
                    transaction_ids = parameters["transaction_ids"]
                    if not isinstance(transaction_ids, list):
                        transaction_ids = [transaction_ids]

                    # Validate transactions exist and belong to tenant
                    valid_transactions = []
                    invalid_transactions = []

                    for tx_id in transaction_ids:
                        tx = await transaction_service.get_transaction(tx_id, tenant_id)
                        if tx:
                            valid_transactions.append(tx_id)
                        else:
                            invalid_transactions.append(tx_id)

                    # Require confirmation for bulk delete
                    if len(valid_transactions) > 10 and not parameters.get(
                        "confirm", False
                    ):
                        return {
                            "agent_type": "data_management",
                            "message": f"Bulk delete requires confirmation. You are about to delete {len(valid_transactions)} transactions.",
                            "requires_confirmation": True,
                            "valid_count": len(valid_transactions),
                            "invalid_count": len(invalid_transactions),
                            "action": "Please add confirm: true to proceed with deletion",
                        }

                    # Perform deletions
                    deleted_count = 0
                    deletion_errors = []

                    for tx_id in valid_transactions:
                        try:
                            success = await transaction_service.delete_transaction(
                                transaction_id=tx_id, tenant_id=tenant_id
                            )
                            if success:
                                deleted_count += 1
                            else:
                                deletion_errors.append(
                                    {"id": tx_id, "error": "Deletion failed"}
                                )
                        except Exception as del_error:
                            deletion_errors.append(
                                {"id": tx_id, "error": str(del_error)}
                            )

                    return {
                        "agent_type": "data_management",
                        "message": f"Deleted {deleted_count} transactions successfully.",
                        "delete_type": "transactions",
                        "summary": {
                            "requested": len(transaction_ids),
                            "valid": len(valid_transactions),
                            "deleted": deleted_count,
                            "failed": len(deletion_errors),
                        },
                        "invalid_ids": invalid_transactions,
                        "errors": deletion_errors,
                    }

                elif "filter" in parameters:
                    # Delete by filter criteria
                    filters = parameters["filter"]

                    # Get transactions matching filter
                    transactions, total = await transaction_service.get_transactions(
                        tenant_id=tenant_id,
                        filters=filters,
                        limit=1000,  # Safety limit
                    )

                    if not transactions:
                        return {
                            "agent_type": "data_management",
                            "message": "No transactions found matching the filter criteria.",
                            "filters_applied": filters,
                        }

                    # Require confirmation for filtered delete
                    if not parameters.get("confirm", False):
                        return {
                            "agent_type": "data_management",
                            "message": f"Found {len(transactions)} transactions to delete. Confirmation required.",
                            "requires_confirmation": True,
                            "preview": [
                                {
                                    "id": tx.id,
                                    "date": tx.date.isoformat() if tx.date else None,
                                    "description": tx.description,
                                    "amount": float(tx.amount) if tx.amount else 0,
                                }
                                for tx in transactions[:5]  # Show first 5
                            ],
                            "total_count": len(transactions),
                            "filters_applied": filters,
                            "action": "Add confirm: true to proceed with deletion",
                        }

                    # Delete transactions
                    deleted_count = 0
                    for tx in transactions:
                        success = await transaction_service.delete_transaction(
                            transaction_id=tx.id, tenant_id=tenant_id
                        )
                        if success:
                            deleted_count += 1

                    return {
                        "agent_type": "data_management",
                        "message": f"Deleted {deleted_count} transactions matching filter criteria.",
                        "delete_type": "transactions_by_filter",
                        "filters_applied": filters,
                        "deleted_count": deleted_count,
                    }

                else:
                    return {
                        "agent_type": "data_management",
                        "message": "Specify transaction IDs or filters to delete transactions.",
                        "usage_examples": [
                            '/delete type: transactions transaction_ids: ["tx-123", "tx-456"]',
                            '/delete type: transactions filter: {"date_from": "2024-01-01", "category": "Test"}',
                        ],
                    }

            elif delete_type == "category":
                category_service = CategoryCrudService(self.conn)

                if "category_id" in parameters:
                    category_id = parameters["category_id"]

                    # Check if category exists
                    category = await category_service.get_category_by_id(
                        db=self.conn, category_id=category_id, tenant_id=tenant_id
                    )

                    if not category:
                        return {
                            "agent_type": "data_management",
                            "message": f"Category {category_id} not found.",
                            "error": "CATEGORY_NOT_FOUND",
                        }

                    # Check for usage
                    usage_stats = await category_service.get_category_usage_stats(
                        tenant_id
                    )
                    category_usage = next(
                        (
                            stat
                            for stat in usage_stats.get("usage_stats", [])
                            if stat["category_id"] == category_id
                        ),
                        None,
                    )

                    if (
                        category_usage
                        and category_usage["usage_count"] > 0
                        and not parameters.get("force", False)
                    ):
                        return {
                            "agent_type": "data_management",
                            "message": f'Category "{category["name"]}" is used by {category_usage["usage_count"]} transactions.',
                            "requires_confirmation": True,
                            "category": {
                                "id": category["id"],
                                "name": category["name"],
                                "usage_count": category_usage["usage_count"],
                            },
                            "action": "Add force: true to delete category with transactions",
                        }

                    # Delete category
                    success = await category_service.delete_category(
                        db=self.conn, category_id=category_id, tenant_id=tenant_id
                    )

                    return {
                        "agent_type": "data_management",
                        "message": f'Category "{category["name"]}" deleted successfully.'
                        if success
                        else "Failed to delete category.",
                        "delete_type": "category",
                        "category_id": category_id,
                        "category_name": category["name"],
                        "success": success,
                    }

                else:
                    return {
                        "agent_type": "data_management",
                        "message": "Specify category_id to delete a category.",
                        "usage_example": "/delete type: category category_id: 123",
                    }

            else:
                return {
                    "agent_type": "data_management",
                    "message": f"Unsupported delete type: {delete_type}",
                    "supported_types": ["transactions", "category"],
                    "usage_examples": [
                        '/delete type: transactions transaction_ids: ["tx-123"]',
                        "/delete type: category category_id: 123",
                    ],
                }

        except Exception as e:
            logger.error(f"Delete command failed: {e}")
            return {
                "agent_type": "data_management",
                "message": f"Delete operation failed: {str(e)}",
                "error": str(e),
            }

    async def handle_report_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle report generation commands."""
        try:
            from datetime import datetime, timedelta

            from ..reports.service import ReportGenerator

            report_service = ReportGenerator(self.conn)
            tenant_id = context.get("tenant_id")

            # Get report type and validate
            report_type = parameters.get("type", "transaction_summary")
            available_types = await report_service.get_available_report_types()

            if report_type not in available_types:
                return {
                    "agent_type": "reporting",
                    "message": f"Unknown report type: {report_type}",
                    "available_types": available_types,
                    "usage_examples": [
                        "/report type: transaction_summary",
                        '/report type: category_breakdown date_range: {"start_date": "2024-01-01", "end_date": "2024-12-31"}',
                        "/report type: monthly_trends",
                    ],
                }

            # Build date range with defaults
            date_range = parameters.get("date_range", {})
            if not date_range.get("start_date") or not date_range.get("end_date"):
                # Default to current month
                today = datetime.now()
                if report_type == "monthly_trends":
                    # Last 6 months for trends
                    date_range = {
                        "start_date": (today - timedelta(days=180)).strftime(
                            "%Y-%m-%d"
                        ),
                        "end_date": today.strftime("%Y-%m-%d"),
                    }
                else:
                    # Current month for other reports
                    date_range = {
                        "start_date": today.replace(day=1).strftime("%Y-%m-%d"),
                        "end_date": today.strftime("%Y-%m-%d"),
                    }

            # Apply filters if provided
            filters = parameters.get("filters", {})

            # Generate the report
            report_data = await report_service.generate_report(
                tenant_id=tenant_id,
                report_type=report_type,
                date_range=date_range,
                filters=filters,
            )

            # Format response based on report type
            if report_type == "transaction_summary":
                message = f"Generated summary of {report_data['summary']['total_transactions']} transactions"
            elif report_type == "category_breakdown":
                unique_categories = len(report_data.get("data", []))
                message = f"Category analysis complete: {unique_categories} categories analyzed"
            elif report_type == "monthly_trends":
                months_analyzed = len(report_data.get("data", []))
                message = f"Monthly trends analyzed for {months_analyzed} months"
            elif report_type == "expense_analysis":
                expense_count = report_data["summary"]["total_transactions"]
                message = (
                    f"Expense analysis complete: {expense_count} expense transactions"
                )
            elif report_type == "income_analysis":
                income_count = report_data["summary"]["total_transactions"]
                message = (
                    f"Income analysis complete: {income_count} income transactions"
                )
            else:
                message = f"{report_type.replace('_', ' ').title()} report generated successfully"

            # Add insights based on data
            insights = []
            if report_data.get("summary", {}).get("total_amount"):
                total = report_data["summary"]["total_amount"]
                insights.append(f"Total amount: ${total:,.2f}")

            if report_type == "category_breakdown" and report_data.get("data"):
                top_category = max(
                    report_data["data"], key=lambda x: x.get("total_amount", 0)
                )
                insights.append(
                    f"Highest spending category: {top_category.get('category_id', 'Unknown')} (${top_category.get('total_amount', 0):,.2f})"
                )

            if report_type == "monthly_trends" and report_data.get("data"):
                trend_data = report_data["data"]
                if len(trend_data) > 1:
                    latest = trend_data[-1]["total_amount"]
                    previous = trend_data[-2]["total_amount"]
                    change = ((latest - previous) / previous * 100) if previous else 0
                    insights.append(f"Month-over-month change: {change:+.1f}%")

            return {
                "agent_type": "reporting",
                "message": message,
                "report_type": report_type,
                "date_range": date_range,
                "filters_applied": filters,
                "summary": report_data.get("summary", {}),
                "insights": insights,
                "data": report_data.get("data", []),
                "visualization_ready": report_type
                in ["monthly_trends", "category_breakdown"],
                "export_available": True,
                "export_hint": "Use /export type: report to download this report",
            }

        except Exception as e:
            logger.error(f"Report command failed: {e}")
            return {
                "agent_type": "reporting",
                "message": f"Report generation failed: {str(e)}",
                "error": str(e),
            }

    async def handle_analyze_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle data analysis commands."""
        try:
            from datetime import datetime, timedelta

            from ..transactions.service import TransactionService

            transaction_service = TransactionService(self.conn)
            tenant_id = context.get("tenant_id")
            analysis_type = parameters.get("type", "spending_patterns")

            # Define time period for analysis
            end_date = datetime.now()
            start_date = end_date - timedelta(days=parameters.get("days", 30))
            time_period = (start_date, end_date)

            # Get filters from parameters
            filters = {}
            if parameters.get("category"):
                filters["category"] = parameters["category"]
            if parameters.get("account"):
                filters["account"] = parameters["account"]

            # Perform transaction pattern analysis
            analysis_result = await transaction_service.analyze_transaction_patterns(
                tenant_id=tenant_id,
                filters=filters,
                time_period=time_period,
            )

            # Generate specific analysis based on type
            if analysis_type == "spending_patterns":
                # Analyze spending trends
                insights = analysis_result.insights + [
                    f"Total spending: ${analysis_result.statistics.get('total_amount', 0):,.2f}",
                    f"Average transaction: ${analysis_result.statistics.get('average_amount', 0):,.2f}",
                    f"Transaction count: {analysis_result.statistics.get('total_transactions', 0)}",
                ]

                # Add category-specific insights
                if analysis_result.patterns.get("categories"):
                    top_categories = sorted(
                        analysis_result.patterns["categories"].items(),
                        key=lambda x: x[1],
                        reverse=True,
                    )[:3]
                    for cat, count in top_categories:
                        insights.append(f"{cat}: {count} transactions")

            elif analysis_type == "temporal_patterns":
                # Analyze time-based patterns
                insights = [
                    "Analyzing transaction patterns over time...",
                    f"Date range: {analysis_result.statistics.get('date_range', {}).get('start', 'N/A')} to {analysis_result.statistics.get('date_range', {}).get('end', 'N/A')}",
                ]

                # Add monthly/weekly patterns if available
                if "time_patterns" in analysis_result.patterns:
                    insights.extend(analysis_result.patterns["time_patterns"])

            elif analysis_type == "accuracy_analysis":
                # Analyze categorization accuracy
                insights = [
                    "Categorization accuracy analysis:",
                    f"Categories in use: {analysis_result.statistics.get('categories_count', 0)}",
                    "AI categorization confidence levels analyzed",
                ]

            else:
                # General analysis
                insights = analysis_result.insights

            # Enhanced recommendations based on patterns
            recommendations = analysis_result.recommendations
            if analysis_result.patterns.get("amounts", {}).get("max", 0) > 10000:
                recommendations.append(
                    "Large transactions detected - consider approval workflow"
                )
            if analysis_result.statistics.get("categories_count", 0) < 5:
                recommendations.append(
                    "Limited category usage - consider expanding category structure"
                )

            return {
                "agent_type": "analytics",
                "message": f"Analysis of {analysis_type.replace('_', ' ')} completed successfully.",
                "analysis_type": analysis_type,
                "time_period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                    "days": (end_date - start_date).days,
                },
                "insights": insights,
                "recommendations": recommendations,
                "statistics": analysis_result.statistics,
                "patterns": {
                    "categories": analysis_result.patterns.get("categories", {}),
                    "amounts": analysis_result.patterns.get("amounts", {}),
                    "frequency": analysis_result.patterns.get("frequency", {}),
                },
            }

        except Exception as e:
            logger.error(f"Analyze command failed: {e}")
            return {
                "agent_type": "analytics",
                "message": f"Analysis failed: {str(e)}",
                "error": str(e),
            }

    async def handle_settings_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle settings management commands."""
        try:
            from ..categories.crud_service import CategoryCrudService

            action = parameters.get("action", "view")
            tenant_id = context.get("tenant_id")
            # user_id = context.get('user_id')  # Not used in current implementation

            # Settings are stored in a tenant_settings table (to be created)
            # For now, we'll use in-memory defaults and some derived settings

            if action == "view":
                # Get current settings
                category_service = CategoryCrudService(self.conn)
                category_stats = await category_service.get_category_usage_stats(
                    tenant_id
                )

                # Check for existing settings in database
                settings_query = """
                    SELECT key, value, data_type 
                    FROM tenant_settings 
                    WHERE tenant_id = $1
                """

                try:
                    rows = await self.conn.fetch(settings_query, tenant_id)
                    db_settings = {row["key"]: row["value"] for row in rows}
                except Exception:
                    # Table doesn't exist yet, use defaults
                    db_settings = {}

                # Combine defaults with database settings
                current_settings = {
                    "general": {
                        "default_currency": db_settings.get("default_currency", "USD"),
                        "date_format": db_settings.get("date_format", "MM/dd/yyyy"),
                        "fiscal_year_start": db_settings.get(
                            "fiscal_year_start", "January"
                        ),
                        "timezone": db_settings.get("timezone", "UTC"),
                    },
                    "categorization": {
                        "auto_categorize": db_settings.get("auto_categorize", "true")
                        == "true",
                        "confidence_threshold": float(
                            db_settings.get("confidence_threshold", "0.8")
                        ),
                        "review_required_below": float(
                            db_settings.get("review_threshold", "0.6")
                        ),
                        "total_categories": category_stats.get("total_categories", 0),
                    },
                    "import": {
                        "detect_duplicates": db_settings.get(
                            "detect_duplicates", "true"
                        )
                        == "true",
                        "date_format_preference": db_settings.get(
                            "import_date_format", "auto"
                        ),
                        "amount_format": db_settings.get("amount_format", "auto"),
                        "skip_header_rows": int(
                            db_settings.get("skip_header_rows", "0")
                        ),
                    },
                    "export": {
                        "default_format": db_settings.get("export_format", "excel"),
                        "include_categories": db_settings.get(
                            "export_categories", "true"
                        )
                        == "true",
                        "date_format": db_settings.get(
                            "export_date_format", "YYYY-MM-DD"
                        ),
                    },
                    "notifications": {
                        "email_reports": db_settings.get("email_reports", "false")
                        == "true",
                        "anomaly_alerts": db_settings.get("anomaly_alerts", "true")
                        == "true",
                        "weekly_summary": db_settings.get("weekly_summary", "false")
                        == "true",
                    },
                }

                return {
                    "agent_type": "configuration",
                    "message": "Current settings retrieved successfully.",
                    "settings": current_settings,
                    "editable_settings": [
                        "default_currency",
                        "date_format",
                        "auto_categorize",
                        "confidence_threshold",
                        "export_format",
                        "email_reports",
                    ],
                }

            elif action == "update":
                # Update specific settings
                updates = parameters.get("updates", {})
                if not updates:
                    return {
                        "agent_type": "configuration",
                        "message": "No settings to update. Provide updates parameter.",
                        "example": '/settings action: update updates: {"default_currency": "EUR", "auto_categorize": false}',
                    }

                # Validate setting values
                valid_settings = {
                    "default_currency": ["USD", "EUR", "GBP", "CAD", "AUD", "INR"],
                    "date_format": ["MM/dd/yyyy", "dd/MM/yyyy", "yyyy-MM-dd"],
                    "export_format": ["excel", "csv", "pdf"],
                    "fiscal_year_start": ["January", "April", "July", "October"],
                }

                updated_settings = []
                errors = []

                for key, value in updates.items():
                    # Validate if setting has restricted values
                    if key in valid_settings and value not in valid_settings[key]:
                        errors.append(
                            {
                                "setting": key,
                                "error": f"Invalid value. Must be one of: {valid_settings[key]}",
                            }
                        )
                        continue

                    # Validate numeric ranges
                    if key == "confidence_threshold" and not (0 <= float(value) <= 1):
                        errors.append(
                            {"setting": key, "error": "Value must be between 0 and 1"}
                        )
                        continue

                    # Update setting (would save to database)
                    updated_settings.append(
                        {
                            "setting": key,
                            "old_value": "previous_value",  # Would fetch from DB
                            "new_value": value,
                        }
                    )

                return {
                    "agent_type": "configuration",
                    "message": f"Updated {len(updated_settings)} settings successfully.",
                    "updated": updated_settings,
                    "errors": errors,
                    "restart_required": any(
                        key in ["default_currency", "timezone"]
                        for key in updates.keys()
                    ),
                }

            elif action == "reset":
                # Reset to defaults
                category = parameters.get("category", "all")

                if not parameters.get("confirm", False):
                    return {
                        "agent_type": "configuration",
                        "message": f"Reset {category} settings to defaults?",
                        "requires_confirmation": True,
                        "warning": "This will remove all customizations",
                        "action": "Add confirm: true to proceed",
                    }

                # Perform reset (would delete from database)
                return {
                    "agent_type": "configuration",
                    "message": "Settings reset to defaults successfully.",
                    "reset_category": category,
                    "affected_settings": 12 if category == "all" else 3,  # Mock count
                }

            else:
                return {
                    "agent_type": "configuration",
                    "message": f"Unknown settings action: {action}",
                    "available_actions": ["view", "update", "reset"],
                    "usage_examples": [
                        "/settings action: view",
                        '/settings action: update updates: {"auto_categorize": true}',
                        "/settings action: reset category: categorization confirm: true",
                    ],
                }

        except Exception as e:
            logger.error(f"Settings command failed: {e}")
            return {
                "agent_type": "configuration",
                "message": f"Settings operation failed: {str(e)}",
                "error": str(e),
            }

    async def handle_search_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle data search operations."""
        try:
            from ..categories.crud_service import CategoryCrudService
            from ..transactions.service import TransactionService

            search_query = parameters.get("query", "").strip()
            search_type = parameters.get("type", "all")
            tenant_id = context.get("tenant_id")

            if not search_query:
                return {
                    "agent_type": "search",
                    "message": "Please provide a search query.",
                    "usage_examples": [
                        '/search query: "office supplies"',
                        '/search query: "Staples" type: transactions',
                        '/search query: "travel" type: categories',
                    ],
                }

            results = {"transactions": [], "categories": [], "total_results": 0}

            # Search transactions
            if search_type in ["transactions", "all"]:
                transaction_service = TransactionService(self.conn)
                tx_results = await transaction_service.search_transactions(
                    tenant_id=tenant_id,
                    query=search_query,
                    limit=parameters.get("limit", 50),
                )

                # Format transaction results
                results["transactions"] = [
                    {
                        "id": tx.id,
                        "date": tx.date.isoformat() if tx.date else None,
                        "description": tx.description,
                        "amount": float(tx.amount) if tx.amount else 0,
                        "category_id": tx.category_id,
                        "relevance_score": self._calculate_relevance_score(
                            search_query, tx.description
                        ),
                    }
                    for tx in tx_results
                ]

                # Sort by relevance
                results["transactions"].sort(
                    key=lambda x: x["relevance_score"], reverse=True
                )

            # Search categories
            if search_type in ["categories", "all"]:
                category_service = CategoryCrudService(self.conn)
                all_categories = await category_service.get_categories(
                    tenant_id=tenant_id, limit=1000
                )

                # Filter categories by search query
                search_lower = search_query.lower()
                matching_categories = [
                    cat
                    for cat in all_categories
                    if search_lower in cat.get("name", "").lower()
                    or search_lower in cat.get("description", "").lower()
                    or search_lower in cat.get("gl_code", "").lower()
                ]

                # Get usage stats for relevance
                usage_stats = await category_service.get_category_usage_stats(tenant_id)
                usage_map = {
                    stat["category_id"]: stat["usage_count"]
                    for stat in usage_stats.get("usage_stats", [])
                }

                results["categories"] = [
                    {
                        "id": cat["id"],
                        "name": cat["name"],
                        "path": cat.get("path", cat["name"]),
                        "description": cat.get("description"),
                        "gl_code": cat.get("gl_code"),
                        "usage_count": usage_map.get(cat["id"], 0),
                        "relevance_score": self._calculate_relevance_score(
                            search_query, cat["name"]
                        ),
                    }
                    for cat in matching_categories
                ]

                # Sort by usage and relevance
                results["categories"].sort(
                    key=lambda x: (x["relevance_score"], x["usage_count"]), reverse=True
                )

            # Calculate totals
            results["total_results"] = len(results["transactions"]) + len(
                results["categories"]
            )

            # Generate insights
            insights = []
            if results["transactions"]:
                total_amount = sum(tx["amount"] for tx in results["transactions"])
                insights.append(
                    f"Total amount in matching transactions: ${total_amount:,.2f}"
                )

            if results["categories"] and results["categories"][0]["usage_count"] > 0:
                top_cat = results["categories"][0]
                insights.append(
                    f"Most used matching category: {top_cat['name']} ({top_cat['usage_count']} uses)"
                )

            return {
                "agent_type": "search",
                "message": f'Found {results["total_results"]} results for "{search_query}"',
                "query": search_query,
                "search_type": search_type,
                "results": results,
                "insights": insights,
                "search_suggestions": self._generate_search_suggestions(
                    search_query, results
                ),
            }

        except Exception as e:
            logger.error(f"Search command failed: {e}")
            return {
                "agent_type": "search",
                "message": f"Search failed: {str(e)}",
                "error": str(e),
            }

    def _calculate_relevance_score(self, query: str, text: str) -> float:
        """Calculate simple relevance score for search results."""
        if not text:
            return 0.0

        query_lower = query.lower()
        text_lower = text.lower()

        # Exact match
        if query_lower == text_lower:
            return 1.0

        # Contains exact query
        if query_lower in text_lower:
            return 0.8

        # All words present
        query_words = query_lower.split()
        if all(word in text_lower for word in query_words):
            return 0.6

        # Some words present
        matches = sum(1 for word in query_words if word in text_lower)
        if matches > 0:
            return 0.4 * (matches / len(query_words))

        return 0.0

    def _generate_search_suggestions(
        self, query: str, results: Dict[str, Any]
    ) -> List[str]:
        """Generate search suggestions based on current results."""
        suggestions = []

        if not results["transactions"] and not results["categories"]:
            suggestions.append("Try searching for partial words or different terms")
            suggestions.append("Use broader search terms")

        if results["transactions"] and not results["categories"]:
            suggestions.append(
                f'Try searching in categories: /search query: "{query}" type: categories'
            )

        if results["categories"] and not results["transactions"]:
            suggestions.append(
                f'Try searching in transactions: /search query: "{query}" type: transactions'
            )

        return suggestions

    async def handle_create_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle create new entities commands."""
        try:
            from ..categories.categorization_agent import CategorizationAgent
            from ..categories.crud_service import CategoryCrudService
            from ..categories.schemas import CategoryCreate

            entity_type = parameters.get("type", "category")
            tenant_id = context.get("tenant_id")
            user_id = context.get("user_id")

            if entity_type == "category":
                category_service = CategoryCrudService(self.conn)

                # Validate required fields
                if not parameters.get("name"):
                    return {
                        "agent_type": "entity_creation",
                        "message": "Category name is required.",
                        "required_fields": ["name"],
                        "optional_fields": [
                            "description",
                            "parent_id",
                            "gl_code",
                            "gl_account_type",
                            "color",
                        ],
                        "example": '/create type: category name: "Office Supplies" description: "General office supplies and stationery"',
                    }

                # Check if category already exists
                existing_categories = await category_service.get_categories(
                    tenant_id=tenant_id, limit=1000
                )

                name_lower = parameters["name"].lower()
                duplicate = next(
                    (
                        cat
                        for cat in existing_categories
                        if cat["name"].lower() == name_lower
                    ),
                    None,
                )

                if duplicate:
                    return {
                        "agent_type": "entity_creation",
                        "message": f'Category "{parameters["name"]}" already exists.',
                        "existing_category": {
                            "id": duplicate["id"],
                            "name": duplicate["name"],
                            "path": duplicate.get("path", duplicate["name"]),
                        },
                        "suggestion": "Use a different name or update the existing category",
                    }

                # Get AI suggestions for GL code if not provided
                gl_code = parameters.get("gl_code")
                gl_suggestions = []

                if not gl_code:
                    categorization_agent = CategorizationAgent(self.conn)
                    gl_suggestions = await categorization_agent.suggest_gl_codes(
                        category_name=parameters["name"],
                        category_path=parameters[
                            "name"
                        ],  # Will be updated if parent exists
                        account_type=parameters.get("gl_account_type", "Expense"),
                        tenant_id=tenant_id,
                    )

                    if gl_suggestions:
                        gl_code = gl_suggestions[0]["gl_code"]

                # Handle parent category
                parent_id = parameters.get("parent_id")
                parent_name = None

                if parent_id:
                    parent_cat = await category_service.get_category_by_id(
                        db=self.conn, category_id=parent_id, tenant_id=tenant_id
                    )
                    if parent_cat:
                        parent_name = parent_cat["name"]
                    else:
                        return {
                            "agent_type": "entity_creation",
                            "message": f"Parent category with ID {parent_id} not found.",
                            "error": "PARENT_NOT_FOUND",
                        }

                # Create the category
                category_data = CategoryCreate(
                    name=parameters["name"],
                    description=parameters.get("description", ""),
                    parent_id=parent_id,
                    code=parameters.get("code"),
                    gl_code=gl_code,
                    gl_account_name=parameters.get(
                        "gl_account_name", parameters["name"]
                    ),
                    gl_account_type=parameters.get("gl_account_type", "Expense"),
                    color=parameters.get(
                        "color", category_service._generate_category_color(0)
                    ),
                )

                new_category = await category_service.create_category(
                    db=self.conn,
                    category=category_data,
                    tenant_id=tenant_id,
                    user_id=user_id,
                )

                # Build hierarchy path
                hierarchy_path = new_category["name"]
                if parent_name:
                    hierarchy_path = f"{parent_name} > {new_category['name']}"

                return {
                    "agent_type": "entity_creation",
                    "message": f'Category "{new_category["name"]}" created successfully.',
                    "entity_type": entity_type,
                    "created_entity": {
                        "id": new_category["id"],
                        "name": new_category["name"],
                        "description": new_category.get("description"),
                        "path": hierarchy_path,
                        "parent_id": new_category.get("parent_id"),
                        "gl_code": new_category.get("gl_code"),
                        "gl_account_type": new_category.get("gl_account_type"),
                        "color": new_category.get("color"),
                    },
                    "gl_suggestions": gl_suggestions[:3] if gl_suggestions else [],
                    "next_steps": [
                        f'Add subcategories: /create type: category name: "Printer Supplies" parent_id: {new_category["id"]}',
                        f"View hierarchy: /filter type: categories parent_id: {new_category['id']}",
                    ],
                }

            elif entity_type == "rule":
                # Categorization rule creation
                rule_type = parameters.get("rule_type", "keyword")

                if not parameters.get("pattern") or not parameters.get("category_id"):
                    return {
                        "agent_type": "entity_creation",
                        "message": "Pattern and category_id are required for rule creation.",
                        "required_fields": ["pattern", "category_id"],
                        "optional_fields": ["rule_type", "priority", "conditions"],
                        "rule_types": ["keyword", "amount_range", "regex", "vendor"],
                        "example": '/create type: rule pattern: "AMZN" category_id: 123 rule_type: vendor',
                    }

                # Validate category exists
                category_service = CategoryCrudService(self.conn)
                target_category = await category_service.get_category_by_id(
                    db=self.conn,
                    category_id=parameters["category_id"],
                    tenant_id=tenant_id,
                )

                if not target_category:
                    return {
                        "agent_type": "entity_creation",
                        "message": f"Category with ID {parameters['category_id']} not found.",
                        "error": "CATEGORY_NOT_FOUND",
                    }

                # Create rule (would be saved to categorization_rules table)
                rule_data = {
                    "tenant_id": tenant_id,
                    "rule_type": rule_type,
                    "pattern": parameters["pattern"],
                    "category_id": parameters["category_id"],
                    "category_name": target_category["name"],
                    "priority": parameters.get("priority", 100),
                    "conditions": parameters.get("conditions", {}),
                    "created_by": user_id,
                    "is_active": True,
                }

                # Mock rule creation (would insert into database)
                rule_id = "rule_" + str(int(time.time()))

                return {
                    "agent_type": "entity_creation",
                    "message": f"{rule_type.title()} rule created successfully.",
                    "entity_type": "rule",
                    "created_entity": {
                        "id": rule_id,
                        "type": rule_type,
                        "pattern": parameters["pattern"],
                        "category": target_category["name"],
                        "category_id": parameters["category_id"],
                        "priority": rule_data["priority"],
                    },
                    "rule_preview": f'Transactions matching "{parameters["pattern"]}" will be categorized as "{target_category["name"]}"',
                }

            elif entity_type == "gl_mapping":
                # GL code mapping creation
                if not parameters.get("category_id") or not parameters.get("gl_code"):
                    return {
                        "agent_type": "entity_creation",
                        "message": "Category ID and GL code are required for mapping.",
                        "required_fields": ["category_id", "gl_code"],
                        "optional_fields": ["gl_account_name", "gl_account_type"],
                        "example": '/create type: gl_mapping category_id: 123 gl_code: "5100" gl_account_name: "Office Expenses"',
                    }

                # Update category with GL mapping
                category_service = CategoryCrudService(self.conn)
                from ..categories.schemas import CategoryUpdate

                update_data = CategoryUpdate(
                    gl_code=parameters["gl_code"],
                    gl_account_name=parameters.get("gl_account_name"),
                    gl_account_type=parameters.get("gl_account_type", "Expense"),
                )

                updated_category = await category_service.update_category(
                    db=self.conn,
                    category_id=parameters["category_id"],
                    category_update=update_data,
                    tenant_id=tenant_id,
                )

                if updated_category:
                    return {
                        "agent_type": "entity_creation",
                        "message": f'GL mapping created for category "{updated_category["name"]}".',
                        "entity_type": "gl_mapping",
                        "created_entity": {
                            "category_id": updated_category["id"],
                            "category_name": updated_category["name"],
                            "gl_code": updated_category["gl_code"],
                            "gl_account_name": updated_category.get("gl_account_name"),
                            "gl_account_type": updated_category.get("gl_account_type"),
                        },
                    }
                else:
                    return {
                        "agent_type": "entity_creation",
                        "message": "Failed to create GL mapping.",
                        "error": "UPDATE_FAILED",
                    }

            else:
                return {
                    "agent_type": "entity_creation",
                    "message": f"Unknown entity type: {entity_type}",
                    "supported_types": ["category", "rule", "gl_mapping"],
                    "usage_examples": [
                        '/create type: category name: "Travel Expenses"',
                        '/create type: rule pattern: "UBER" category_id: 123',
                        '/create type: gl_mapping category_id: 123 gl_code: "5200"',
                    ],
                }

        except Exception as e:
            logger.error(f"Create command failed: {e}")
            return {
                "agent_type": "entity_creation",
                "message": f"Create failed: {str(e)}",
                "error": str(e),
            }

    async def handle_refresh_command(
        self, parameters: Dict[str, Any], context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle refresh/reload data commands."""
        try:
            from datetime import datetime

            from ..categories.categorization_agent import CategorizationAgent
            from ..categories.crud_service import CategoryCrudService
            from ..transactions.service import TransactionService

            refresh_type = parameters.get("type", "cache")
            tenant_id = context.get("tenant_id")

            refreshed_items = []
            refresh_stats = {}

            if refresh_type in ["all", "cache"]:
                # Clear any in-memory caches (if implemented)
                refreshed_items.append("cache")
                refresh_stats["cache"] = {"status": "cleared", "items": 0}

            if refresh_type in ["all", "transactions"]:
                # Recalculate transaction statistics
                transaction_service = TransactionService(self.conn)
                tx_stats = await transaction_service.get_transaction_statistics(
                    tenant_id
                )

                refreshed_items.append("transactions")
                refresh_stats["transactions"] = {
                    "total_count": tx_stats["total_transactions"],
                    "total_amount": tx_stats["total_amount"],
                    "status": "statistics updated",
                }

            if refresh_type in ["all", "categories"]:
                # Refresh category usage statistics
                category_service = CategoryCrudService(self.conn)
                usage_stats = await category_service.get_category_usage_stats(tenant_id)

                # Rebuild hierarchy
                hierarchy = await category_service.get_category_hierarchy(tenant_id)

                refreshed_items.append("categories")
                refresh_stats["categories"] = {
                    "total_categories": usage_stats["total_categories"],
                    "categories_in_use": usage_stats["categories_with_usage"],
                    "hierarchy_depth": hierarchy.max_depth,
                    "status": "hierarchy rebuilt",
                }

            if refresh_type in ["all", "categorization"]:
                # Re-categorize uncategorized transactions
                transaction_service = TransactionService(self.conn)
                categorization_agent = CategorizationAgent(self.conn)

                # Get uncategorized transactions
                uncategorized, _ = await transaction_service.get_transactions(
                    tenant_id=tenant_id,
                    filters={"category_id": None},
                    limit=100,  # Process first 100
                )

                categorized_count = 0
                for tx in uncategorized:
                    try:
                        suggestions = await categorization_agent.suggest_categories(
                            transaction_description=tx.description,
                            amount=float(tx.amount) if tx.amount else None,
                            tenant_id=tenant_id,
                        )

                        if suggestions and suggestions[0].confidence > 0.7:
                            # Update transaction with high-confidence suggestion
                            await transaction_service.update_transaction(
                                transaction_id=tx.id,
                                update_data={
                                    "ai_suggested_category": suggestions[
                                        0
                                    ].category_name,
                                    "ai_confidence": suggestions[0].confidence,
                                },
                                tenant_id=tenant_id,
                            )
                            categorized_count += 1
                    except Exception as cat_error:
                        logger.warning(
                            f"Failed to categorize transaction {tx.id}: {cat_error}"
                        )

                refreshed_items.append("categorization")
                refresh_stats["categorization"] = {
                    "processed": len(uncategorized),
                    "categorized": categorized_count,
                    "status": "AI categorization updated",
                }

            if refresh_type == "sync":
                # Sync with external systems (placeholder)
                refreshed_items.append("external_sync")
                refresh_stats["external_sync"] = {
                    "status": "not_configured",
                    "message": "External system sync not yet configured",
                }

            # Generate summary message
            if not refreshed_items:
                message = f"Unknown refresh type: {refresh_type}"
                available_types = [
                    "cache",
                    "transactions",
                    "categories",
                    "categorization",
                    "all",
                ]
            else:
                message = f"Refresh completed for: {', '.join(refreshed_items)}"
                available_types = None

            return {
                "agent_type": "data_refresh",
                "message": message,
                "refresh_type": refresh_type,
                "refreshed_items": refreshed_items,
                "statistics": refresh_stats,
                "refresh_timestamp": datetime.now().isoformat(),
                "available_types": available_types,
                "next_actions": [
                    "View updated statistics: /analyze type: spending_patterns",
                    'Check categorization: /search type: transactions query: "uncategorized"',
                ]
                if refreshed_items
                else None,
            }

        except Exception as e:
            logger.error(f"Refresh command failed: {e}")
            return {
                "agent_type": "data_refresh",
                "message": f"Refresh failed: {str(e)}",
                "error": str(e),
                "available_types": [
                    "cache",
                    "transactions",
                    "categories",
                    "categorization",
                    "all",
                ],
            }

    async def process_natural_language(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None,
        tenant_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Process natural language message and extract command/intent.

        Args:
            message: Natural language message from user
            context: Additional context information
            user_id: User ID for authentication
            tenant_id: Tenant ID for data isolation

        Returns:
            Structured response with extracted command and results
        """
        try:
            import re

            message_lower = message.lower().strip()

            # Command mapping based on keywords
            command_patterns = {
                "/upload": ["upload", "import", "file", "process", "load data"],
                "/filter": ["filter", "show", "find", "search for", "display"],
                "/export": ["export", "download", "save", "extract"],
                "/categorize": ["categorize", "classify", "organize", "sort"],
                "/delete": ["delete", "remove", "clean", "clear"],
                "/report": ["report", "generate", "create report", "summary"],
                "/analyze": ["analyze", "analysis", "trends", "patterns", "insights"],
                "/settings": ["settings", "configure", "setup", "preferences"],
                "/search": ["search", "find", "lookup", "locate"],
                "/create": ["create", "add", "new", "make"],
                "/refresh": ["refresh", "reload", "update", "sync"],
            }

            # Extract command based on keywords
            detected_command = None
            for command, keywords in command_patterns.items():
                if any(keyword in message_lower for keyword in keywords):
                    detected_command = command
                    break

            # If no specific command detected, use general help/query
            if not detected_command:
                return {
                    "success": True,
                    "message": "I can help you with various tasks. Here are some things you can ask me:",
                    "data": {
                        "suggestions": [
                            "Upload a new transaction file",
                            "Show me recent transactions",
                            "Generate a spending report",
                            "Analyze my expenses",
                            "Search for specific transactions",
                        ]
                    },
                    "actions": [
                        {"label": "Upload File", "command": "/upload"},
                        {"label": "View Reports", "command": "/report"},
                        {"label": "Analyze Data", "command": "/analyze"},
                    ],
                    "agent_type": "conversational_help",
                }

            # Extract parameters from natural language
            parameters = {}

            # Date extraction
            date_patterns = {
                "this month": {
                    "date_from": "current_month_start",
                    "date_to": "current_month_end",
                },
                "last month": {
                    "date_from": "last_month_start",
                    "date_to": "last_month_end",
                },
                "this year": {
                    "date_from": "current_year_start",
                    "date_to": "current_year_end",
                },
                "today": {"date_from": "today", "date_to": "today"},
                "yesterday": {"date_from": "yesterday", "date_to": "yesterday"},
            }

            for date_phrase, date_params in date_patterns.items():
                if date_phrase in message_lower:
                    parameters.update(date_params)
                    break

            # Amount extraction
            amount_match = re.search(r"\$?(\d+(?:,\d{3})*(?:\.\d{2})?)", message)
            if amount_match:
                amount = float(amount_match.group(1).replace(",", ""))
                if (
                    "over" in message_lower
                    or "above" in message_lower
                    or "more than" in message_lower
                ):
                    parameters["min_amount"] = amount
                elif (
                    "under" in message_lower
                    or "below" in message_lower
                    or "less than" in message_lower
                ):
                    parameters["max_amount"] = amount
                else:
                    parameters["amount"] = amount

            # Category extraction (simple keyword matching)
            common_categories = [
                "food",
                "gas",
                "groceries",
                "travel",
                "office",
                "supplies",
                "marketing",
                "operations",
            ]
            for category in common_categories:
                if category in message_lower:
                    parameters["category"] = category
                    break

            # Process the detected command with extracted parameters
            result = await self.process_command(
                command=detected_command,
                parameters=parameters,
                context=context,
                user_id=user_id,
                tenant_id=tenant_id,
            )

            # Enhance response with natural language context
            result["data"] = result.get("result", {})
            result["natural_language_query"] = message
            result["detected_command"] = detected_command
            result["extracted_parameters"] = parameters

            return result

        except Exception as e:
            logger.error(f"Natural language processing failed: {e}")
            return {
                "success": False,
                "message": "I had trouble understanding your request. Could you try rephrasing it or use a specific command?",
                "error": str(e),
                "suggestions": [
                    "Try: 'Upload a file'",
                    "Try: 'Show recent transactions'",
                    "Try: 'Generate a report'",
                ],
            }
