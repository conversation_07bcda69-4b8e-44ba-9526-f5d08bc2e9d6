"""
Debit Credit Agent - Advanced Accounting Logic Inference
========================================================

This agent specializes in debit/credit inference beyond basic schema interpretation,
handling complex accounting scenarios using the ADK v1.3.0 pattern.

Key capabilities:
- Advanced debit/credit inference using accounting principles
- Regional banking terminology and cultural pattern recognition
- Complex transaction analysis (splits, transfers, reversals)
- Accounting rule validation and correction
- Multi-currency transaction direction inference
- Banking format analysis and standardization
"""

import json
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import asyncpg

logger = logging.getLogger(__name__)

# Real ADK imports
from google.adk.tools import FunctionTool
from vertexai.generative_models import GenerativeModel

# Import StandardGikiAgent base class
from ...shared.ai.standard_giki_agent import StandardGikiAgent

# ===== DEBIT/CREDIT INFERENCE TOOLS =====


async def infer_transaction_direction_tool_function(
    transaction_data: Dict[str, Any],
    account_context: Optional[Dict[str, Any]] = None,
    regional_settings: Optional[Dict[str, str]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Advanced debit/credit inference using accounting principles.

    Analyzes transaction characteristics and account context to determine
    the correct debit/credit direction using advanced accounting logic.

    Args:
        transaction_data: Transaction details (amount, description, merchant, etc.)
        account_context: Account type and characteristics
        regional_settings: Regional banking conventions

    Returns:
        Dictionary with inferred transaction direction and reasoning
    """
    logger.info("Inferring transaction direction using advanced accounting principles")

    try:
        from vertexai.generative_models import GenerativeModel

        from ...shared.ai.prompt_registry import get_prompt_registry

        # Initialize model for accounting analysis
        model = GenerativeModel("gemini-2.0-flash-001")

        # Get prompt from registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("infer_transaction_direction")
        
        # Format prompt with variables
        inference_prompt = prompt_version.format(
            transaction_data=json.dumps(transaction_data, indent=2),
            account_context=json.dumps(account_context or {}, indent=2),
            regional_settings=json.dumps(regional_settings or {"region": "global", "convention": "standard"}, indent=2)
        )

        # Generate inference using model config from prompt
        response = await model.generate_content_async(
            inference_prompt,
            generation_config=prompt_version.model_config,
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Direction inferred: {result['inferred_direction']} with {result['confidence'] * 100:.1f}% confidence"
        )
        return result

    except Exception as e:
        logger.error(f"Transaction direction inference failed: {e}")
        raise Exception(f"AI direction inference failed: {e}")


async def analyze_banking_patterns_tool_function(
    transactions: List[Dict[str, Any]],
    bank_metadata: Optional[Dict[str, Any]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Analyze regional banking terminology and patterns.

    Identifies specific banking conventions, terminology patterns, and
    regional variations in transaction formatting and direction indication.

    Args:
        transactions: List of transactions to analyze
        bank_metadata: Optional bank-specific information

    Returns:
        Dictionary with banking pattern analysis
    """
    logger.info(f"Analyzing banking patterns for {len(transactions)} transactions")

    try:
        from vertexai.generative_models import GenerativeModel

        from ...shared.ai.prompt_registry import get_prompt_registry

        # Initialize model for pattern analysis
        model = GenerativeModel("gemini-2.0-flash-001")

        # Sample transactions for analysis (limit to prevent token overflow)
        sample_transactions = (
            transactions[:20] if len(transactions) > 20 else transactions
        )

        # Get prompt from registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("analyze_banking_patterns")
        
        # Format prompt with variables
        pattern_prompt = prompt_version.format(
            transactions=json.dumps(sample_transactions, indent=2),
            bank_metadata=json.dumps(bank_metadata or {}, indent=2)
        )

        # Generate analysis using model config from prompt
        response = await model.generate_content_async(
            pattern_prompt,
            generation_config=prompt_version.model_config,
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Banking patterns analyzed: {result['banking_region']} region, {len(result['inference_rules'])} rules identified"
        )
        return result

    except Exception as e:
        logger.error(f"Banking pattern analysis failed: {e}")
        raise Exception(f"AI pattern analysis failed: {e}")


async def handle_complex_transactions_tool_function(
    transaction: Dict[str, Any],
    transaction_type: str,
    context_transactions: Optional[List[Dict[str, Any]]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Handle complex transaction scenarios (splits, transfers, reversals).

    Analyzes complex transaction types that require special handling
    for accurate debit/credit determination.

    Args:
        transaction: Primary transaction to analyze
        transaction_type: Type of complex transaction
        context_transactions: Related transactions for context

    Returns:
        Dictionary with complex transaction analysis
    """
    logger.info(f"Handling complex transaction type: {transaction_type}")

    try:
        from vertexai.generative_models import GenerativeModel

        from ...shared.ai.prompt_registry import get_prompt_registry

        # Initialize model for complex analysis
        model = GenerativeModel("gemini-2.0-flash-001")

        # Get prompt from registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("handle_complex_transactions")
        
        # Format prompt with variables
        complex_prompt = prompt_version.format(
            transaction=json.dumps(transaction, indent=2),
            transaction_type=transaction_type,
            context_transactions=json.dumps(context_transactions or [], indent=2)
        )

        # Generate analysis using model config from prompt
        response = await model.generate_content_async(
            complex_prompt,
            generation_config=prompt_version.model_config,
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Complex transaction analyzed: {len(result['components'])} components, balanced={result['validation']['is_balanced']}"
        )
        return result

    except Exception as e:
        logger.error(f"Complex transaction analysis failed: {e}")
        raise Exception(f"AI complex transaction analysis failed: {e}")


async def validate_accounting_rules_tool_function(
    transactions: List[Dict[str, Any]],
    validation_rules: Optional[Dict[str, Any]] = None,
    **_kwargs,
) -> Dict[str, Any]:
    """
    Validate and correct accounting rule compliance.

    Checks transaction directions against accounting principles and
    suggests corrections for any violations.

    Args:
        transactions: List of transactions to validate
        validation_rules: Optional custom validation rules

    Returns:
        Dictionary with validation results and corrections
    """
    logger.info(f"Validating accounting rules for {len(transactions)} transactions")

    validation_results = {
        "total_transactions": len(transactions),
        "valid_transactions": 0,
        "invalid_transactions": 0,
        "warnings": [],
        "errors": [],
        "corrections": [],
        "summary": {},
    }

    try:
        # Default validation rules
        default_rules = {
            "require_positive_expenses": True,
            "require_negative_income": False,
            "validate_balance_consistency": True,
            "check_amount_direction_match": True,
            "flag_large_amounts": True,
            "large_amount_threshold": 10000.0,
        }

        if validation_rules:
            default_rules.update(validation_rules)

        # Validate each transaction
        for i, txn in enumerate(transactions):
            transaction_valid = True
            txn_issues = []

            amount = txn.get("amount", 0)
            direction = txn.get("direction", "").lower()
            description = txn.get("description", "")
            category = txn.get("category", "")

            # Rule 1: Amount direction consistency
            if default_rules["check_amount_direction_match"]:
                if direction == "debit" and amount > 0:
                    # For most systems, debits should be negative or explicitly marked
                    if not any(
                        word in description.lower()
                        for word in ["deposit", "refund", "credit"]
                    ):
                        txn_issues.append(
                            {
                                "rule": "amount_direction_mismatch",
                                "severity": "warning",
                                "message": f"Debit transaction with positive amount: {amount}",
                                "suggested_correction": "Verify transaction direction or amount sign",
                            }
                        )

                elif direction == "credit" and amount < 0:
                    # Credits should typically be positive
                    if not any(
                        word in description.lower()
                        for word in ["payment", "withdrawal", "fee"]
                    ):
                        txn_issues.append(
                            {
                                "rule": "amount_direction_mismatch",
                                "severity": "warning",
                                "message": f"Credit transaction with negative amount: {amount}",
                                "suggested_correction": "Verify transaction direction or amount sign",
                            }
                        )

            # Rule 2: Large amount flagging
            if default_rules["flag_large_amounts"]:
                if abs(amount) > default_rules["large_amount_threshold"]:
                    txn_issues.append(
                        {
                            "rule": "large_amount_detected",
                            "severity": "info",
                            "message": f"Large transaction amount: {abs(amount)}",
                            "suggested_correction": "Review for accuracy",
                        }
                    )

            # Rule 3: Category-direction consistency
            expense_categories = [
                "food",
                "dining",
                "transportation",
                "utilities",
                "rent",
                "shopping",
            ]
            income_categories = ["salary", "income", "deposit", "refund"]

            if any(cat in category.lower() for cat in expense_categories):
                if direction == "credit":
                    txn_issues.append(
                        {
                            "rule": "category_direction_mismatch",
                            "severity": "error",
                            "message": f"Expense category '{category}' with credit direction",
                            "suggested_correction": "Change direction to debit for expense transaction",
                        }
                    )
                    transaction_valid = False

            elif any(cat in category.lower() for cat in income_categories):
                if direction == "debit":
                    txn_issues.append(
                        {
                            "rule": "category_direction_mismatch",
                            "severity": "error",
                            "message": f"Income category '{category}' with debit direction",
                            "suggested_correction": "Change direction to credit for income transaction",
                        }
                    )
                    transaction_valid = False

            # Rule 4: Description-amount consistency
            payment_keywords = ["payment", "purchase", "bill", "fee", "charge"]
            income_keywords = ["deposit", "salary", "refund", "credit", "interest"]

            desc_lower = description.lower()
            if any(keyword in desc_lower for keyword in payment_keywords):
                if amount > 0 and direction != "debit":
                    txn_issues.append(
                        {
                            "rule": "description_direction_mismatch",
                            "severity": "warning",
                            "message": "Payment description with non-debit direction",
                            "suggested_correction": "Verify direction matches payment nature",
                        }
                    )

            elif any(keyword in desc_lower for keyword in income_keywords):
                if amount < 0 and direction != "credit":
                    txn_issues.append(
                        {
                            "rule": "description_direction_mismatch",
                            "severity": "warning",
                            "message": "Income description with non-credit direction",
                            "suggested_correction": "Verify direction matches income nature",
                        }
                    )

            # Store results
            if transaction_valid:
                validation_results["valid_transactions"] += 1
            else:
                validation_results["invalid_transactions"] += 1

            # Add issues to appropriate lists
            for issue in txn_issues:
                issue["transaction_index"] = i
                issue["transaction_id"] = txn.get("id", f"txn_{i}")

                if issue["severity"] == "error":
                    validation_results["errors"].append(issue)
                elif issue["severity"] == "warning":
                    validation_results["warnings"].append(issue)

                if "suggested_correction" in issue:
                    validation_results["corrections"].append(issue)

        # Generate summary
        validation_results["summary"] = {
            "overall_validity": validation_results["valid_transactions"]
            / len(transactions)
            * 100,
            "error_rate": validation_results["invalid_transactions"]
            / len(transactions)
            * 100,
            "warning_count": len(validation_results["warnings"]),
            "correction_suggestions": len(validation_results["corrections"]),
            "most_common_issues": {},
            "validation_passed": validation_results["invalid_transactions"] == 0,
        }

        # Count issue frequencies
        issue_counts = {}
        for issue in validation_results["errors"] + validation_results["warnings"]:
            rule = issue["rule"]
            issue_counts[rule] = issue_counts.get(rule, 0) + 1

        validation_results["summary"]["most_common_issues"] = issue_counts

        logger.info(
            f"Validation complete: {validation_results['valid_transactions']}/{len(transactions)} valid, "
            f"{len(validation_results['errors'])} errors, {len(validation_results['warnings'])} warnings"
        )
        return validation_results

    except Exception as e:
        logger.error(f"Accounting rules validation failed: {e}")
        validation_results["errors"].append(
            {
                "rule": "validation_error",
                "severity": "error",
                "message": f"Validation process failed: {str(e)}",
                "transaction_index": -1,
            }
        )
        return validation_results


async def infer_multicurrency_direction_tool_function(
    transaction: Dict[str, Any],
    exchange_rates: Optional[Dict[str, float]] = None,
    base_currency: str = "USD",
    **_kwargs,
) -> Dict[str, Any]:
    """
    Handle multi-currency transaction direction inference.

    Analyzes foreign currency transactions to determine proper
    debit/credit direction considering currency conversion effects.

    Args:
        transaction: Transaction with currency information
        exchange_rates: Current exchange rates
        base_currency: Base accounting currency

    Returns:
        Dictionary with multi-currency direction analysis
    """
    logger.info("Inferring direction for multi-currency transaction")

    try:
        from vertexai.generative_models import GenerativeModel

        from ...shared.ai.prompt_registry import get_prompt_registry

        # Initialize model for currency analysis
        model = GenerativeModel("gemini-2.0-flash-001")

        # Get prompt from registry
        prompt_registry = get_prompt_registry()
        prompt_version = prompt_registry.get("infer_multicurrency_direction")
        
        # Format prompt with variables
        currency_prompt = prompt_version.format(
            transaction=json.dumps(transaction, indent=2),
            exchange_rates=json.dumps(exchange_rates or {}, indent=2),
            base_currency=base_currency
        )

        # Generate analysis using model config from prompt
        response = await model.generate_content_async(
            currency_prompt,
            generation_config=prompt_version.model_config,
        )

        # Parse response
        response_text = response.text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Multi-currency analysis complete: {result['currency_analysis']['transaction_currency']} → {base_currency}, "
            f"direction: {result['direction_inference']['recommended_direction']}"
        )
        return result

    except Exception as e:
        logger.error(f"Multi-currency direction inference failed: {e}")
        raise Exception(f"AI currency analysis failed: {e}")


# Create FunctionTool instances
infer_transaction_direction_tool = FunctionTool(
    func=infer_transaction_direction_tool_function
)
analyze_banking_patterns_tool = FunctionTool(
    func=analyze_banking_patterns_tool_function
)
handle_complex_transactions_tool = FunctionTool(
    func=handle_complex_transactions_tool_function
)
validate_accounting_rules_tool = FunctionTool(
    func=validate_accounting_rules_tool_function
)
infer_multicurrency_direction_tool = FunctionTool(
    func=infer_multicurrency_direction_tool_function
)


@dataclass
class DebitCreditAgentConfig:
    """Configuration for DebitCreditAgent."""

    model_name: str = "gemini-2.0-flash-001"
    project: str | None = None
    location: str | None = None
    tools: List[FunctionTool] | None = None
    enable_code_execution: bool = False


class DebitCreditAgent(StandardGikiAgent):
    """
    Advanced debit/credit inference agent for complex accounting scenarios.

    This agent handles:
    - Advanced debit/credit inference using accounting principles
    - Regional banking terminology and cultural pattern recognition
    - Complex transaction analysis (splits, transfers, reversals)
    - Accounting rule validation and correction
    - Multi-currency transaction direction inference
    """

    def __init__(
        self,
        config: DebitCreditAgentConfig | None = None,
        db: asyncpg.Connection | None = None,
        **kwargs,
    ):
        """Initialize the DebitCreditAgent using StandardGikiAgent inheritance."""
        if config is None:
            config = DebitCreditAgentConfig(
                model_name=kwargs.get("model_name", "gemini-2.0-flash-001"),
                project=kwargs.get("project"),
                location=kwargs.get("location"),
            )

        # Store config and extract values
        self._config = config
        model_name = config.model_name
        project = config.project
        location = config.location

        # Set up debit/credit analysis tools
        custom_tools = [
            infer_transaction_direction_tool,
            analyze_banking_patterns_tool,
            handle_complex_transactions_tool,
            validate_accounting_rules_tool,
            infer_multicurrency_direction_tool,
        ]
        if config.tools:
            custom_tools.extend(config.tools)

        # Initialize StandardGikiAgent with debit/credit specific configuration
        super().__init__(
            name="debit_credit_agent",
            description="Advanced debit/credit inference and accounting logic specialist",
            custom_tools=custom_tools,
            enable_interactive_tools=False,  # Debit/credit operations are analytical
            enable_code_execution=config.enable_code_execution,
            model_name=model_name,
            instruction="""You are an expert in advanced accounting principles and debit/credit inference.
            Handle complex accounting scenarios including splits, transfers, reversals, and multi-currency transactions.
            Apply regional banking conventions and ensure compliance with accounting standards.
            Use lookup-based logic for 100% accuracy in financial categorization.""",
            project_id=project,
            location=location or "global",
            **kwargs,
        )

        # Store attributes after initialization
        self._model_name = model_name
        self._project = project
        self._location = location
        self._db = db

        # Initialize debit/credit specific components
        self._vertex_model = GenerativeModel(model_name=model_name)

        logger.info(f"DebitCreditAgent initialized with model: {model_name}")

    async def infer_transaction_direction(
        self,
        transaction_data: Dict[str, Any],
        account_context: Optional[Dict[str, Any]] = None,
        regional_settings: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Infer debit/credit direction using advanced accounting principles.

        Args:
            transaction_data: Transaction details
            account_context: Account type and characteristics
            regional_settings: Regional banking conventions

        Returns:
            Dictionary with inferred direction and reasoning
        """
        return await infer_transaction_direction_tool_function(
            transaction_data=transaction_data,
            account_context=account_context,
            regional_settings=regional_settings,
        )

    async def analyze_banking_patterns(
        self,
        transactions: List[Dict[str, Any]],
        bank_metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Analyze regional banking terminology and patterns.

        Args:
            transactions: List of transactions to analyze
            bank_metadata: Optional bank-specific information

        Returns:
            Dictionary with banking pattern analysis
        """
        return await analyze_banking_patterns_tool_function(
            transactions=transactions,
            bank_metadata=bank_metadata,
        )

    async def handle_complex_transactions(
        self,
        transaction: Dict[str, Any],
        transaction_type: str,
        context_transactions: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        Handle complex transaction scenarios.

        Args:
            transaction: Primary transaction to analyze
            transaction_type: Type of complex transaction
            context_transactions: Related transactions for context

        Returns:
            Dictionary with complex transaction analysis
        """
        return await handle_complex_transactions_tool_function(
            transaction=transaction,
            transaction_type=transaction_type,
            context_transactions=context_transactions,
        )

    async def validate_accounting_rules(
        self,
        transactions: List[Dict[str, Any]],
        validation_rules: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Validate and correct accounting rule compliance.

        Args:
            transactions: List of transactions to validate
            validation_rules: Optional custom validation rules

        Returns:
            Dictionary with validation results
        """
        return await validate_accounting_rules_tool_function(
            transactions=transactions,
            validation_rules=validation_rules,
        )

    async def infer_multicurrency_direction(
        self,
        transaction: Dict[str, Any],
        exchange_rates: Optional[Dict[str, float]] = None,
        base_currency: str = "USD",
    ) -> Dict[str, Any]:
        """
        Handle multi-currency transaction direction inference.

        Args:
            transaction: Transaction with currency information
            exchange_rates: Current exchange rates
            base_currency: Base accounting currency

        Returns:
            Dictionary with multi-currency analysis
        """
        return await infer_multicurrency_direction_tool_function(
            transaction=transaction,
            exchange_rates=exchange_rates,
            base_currency=base_currency,
        )
