"""
File domain ADK tools.

This module provides ADK tool functions for file processing operations that can be used
by agents to assist with file uploads, validation, and transaction extraction.
"""

import logging
from typing import Any, Dict, List, Optional

import asyncpg

from .service import FileService

logger = logging.getLogger(__name__)


async def process_file_upload_tool(
    db: asyncpg.Connection,
    file_content: bytes,
    filename: str,
    tenant_id: int,
    upload_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    ADK tool function to process uploaded files and extract sheet information.

    Args:
        db: Database connection
        file_content: Binary file content
        filename: Original filename
        tenant_id: Tenant ID
        upload_id: Optional upload ID for tracking

    Returns:
        Processing result with sheet information and metadata
    """
    try:
        file_service = FileService(db)
        result = await file_service.process_file(
            file_content=file_content,
            filename=filename,
            tenant_id=tenant_id,
            upload_id=upload_id,
        )

        return {
            "filename": result.filename,
            "file_size": result.file_size,
            "sheets": [
                {
                    "name": sheet.name,
                    "row_count": sheet.row_count,
                    "column_count": sheet.column_count,
                    "columns": sheet.columns,
                    "preview_data": sheet.preview_data,
                    "suggested_mapping": sheet.suggested_mapping,
                }
                for sheet in result.sheets
            ],
            "processing_time_ms": result.processing_time_ms,
            "warnings": result.warnings,
            "errors": result.errors,
        }

    except Exception as e:
        logger.error(f"File processing failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
        }


async def extract_transactions_tool(
    db: asyncpg.Connection,
    file_content: bytes,
    filename: str,
    sheet_name: str,
    column_mapping: Dict[str, str],
    tenant_id: int,
) -> Dict[str, Any]:
    """
    ADK tool function to extract transactions from a file using specified column mapping.

    Args:
        db: Database connection
        file_content: Binary file content
        filename: Original filename
        sheet_name: Name of the sheet to process
        column_mapping: Mapping of columns to transaction fields
        tenant_id: Tenant ID

    Returns:
        Extracted transactions with success/error information
    """
    try:
        file_service = FileService(db)
        transactions = await file_service.extract_transactions(
            file_content=file_content,
            filename=filename,
            sheet_name=sheet_name,
            column_mapping=column_mapping,
            tenant_id=tenant_id,
        )

        return {
            "success": True,
            "transaction_count": len(transactions),
            "transactions": [
                {
                    "date": t.date.isoformat() if t.date else None,
                    "description": t.description,
                    "amount": float(t.amount) if t.amount else 0.0,
                    "account": t.account,
                    "category": t.category,
                    "original_category": t.original_category,
                    "entity": t.entity,
                }
                for t in transactions
            ],
        }

    except Exception as e:
        logger.error(f"Transaction extraction failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
        }


async def validate_column_mapping_tool(
    db: asyncpg.Connection,
    column_mapping: Dict[str, str],
    available_columns: List[str],
) -> Dict[str, Any]:
    """
    ADK tool function to validate column mapping for file processing.

    Args:
        db: Database connection
        column_mapping: Proposed column mapping
        available_columns: Available columns in the file

    Returns:
        Validation result with errors and suggestions
    """
    try:
        file_service = FileService(db)
        validation_result = await file_service.validate_column_mapping(
            column_mapping=column_mapping,
            available_columns=available_columns,
        )

        return {
            "is_valid": validation_result.is_valid,
            "errors": validation_result.errors,
            "warnings": validation_result.warnings,
            "suggestions": validation_result.suggestions,
            "required_fields": validation_result.required_fields,
            "optional_fields": validation_result.optional_fields,
        }

    except Exception as e:
        logger.error(f"Column mapping validation failed: {e}")
        return {
            "is_valid": False,
            "error": str(e),
            "error_type": type(e).__name__,
        }


async def get_supported_file_formats_tool(
    db: asyncpg.Connection,
) -> Dict[str, Any]:
    """
    ADK tool function to get supported file formats for upload.

    Args:
        db: Database connection

    Returns:
        List of supported file formats and their descriptions
    """
    try:
        file_service = FileService(db)
        formats = await file_service.get_supported_formats()

        return {
            "success": True,
            "supported_formats": formats,
            "description": "Supported file formats for transaction upload",
        }

    except Exception as e:
        logger.error(f"Failed to get supported formats: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
        }


async def analyze_file_structure_tool(
    db: asyncpg.Connection,
    file_content: bytes,
    filename: str,
) -> Dict[str, Any]:
    """
    ADK tool function to analyze file structure without full processing.

    Args:
        db: Database connection
        file_content: Binary file content
        filename: Original filename

    Returns:
        File structure analysis with sheet information and column detection
    """
    try:
        file_service = FileService(db)

        # Process file to get basic structure
        result = await file_service.process_file(
            file_content=file_content,
            filename=filename,
            tenant_id=1,  # Use dummy tenant for analysis
        )

        return {
            "success": True,
            "filename": result.filename,
            "file_type": filename.split(".")[-1].lower(),
            "sheet_count": len(result.sheets),
            "sheets": [
                {
                    "name": sheet.name,
                    "row_count": sheet.row_count,
                    "column_count": sheet.column_count,
                    "columns": sheet.columns,
                    "has_headers": len(sheet.columns) > 0,
                    "suggested_mapping": sheet.suggested_mapping,
                }
                for sheet in result.sheets
            ],
            "analysis": {
                "likely_transaction_file": any(
                    "amount" in str(sheet.suggested_mapping).lower()
                    or "date" in str(sheet.suggested_mapping).lower()
                    for sheet in result.sheets
                ),
                "total_rows": sum(sheet.row_count for sheet in result.sheets),
                "processing_time_ms": result.processing_time_ms,
            },
        }

    except Exception as e:
        logger.error(f"File structure analysis failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
        }
