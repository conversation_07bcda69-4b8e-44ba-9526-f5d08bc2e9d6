"""
Enhanced Files Router
Provides enhanced API endpoints for the marketing-focused upload experience
"""
import asyncio
import csv
import io
import json
import logging
from datetime import datetime
from typing import Any, Dict

from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    HTTPException,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.responses import StreamingResponse

from ...core.database import get_db_session
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .enhanced_processing_service import enhanced_processing_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/files/enhanced", tags=["Enhanced Files"])

# WebSocket connection manager for real-time progress updates
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, upload_id: str, websocket: WebSocket):
        await websocket.accept()
        self.active_connections[upload_id] = websocket

    def disconnect(self, upload_id: str):
        if upload_id in self.active_connections:
            del self.active_connections[upload_id]

    async def send_progress_update(self, upload_id: str, data: Dict[str, Any]):
        if upload_id in self.active_connections:
            try:
                await self.active_connections[upload_id].send_text(json.dumps(data))
            except Exception as e:
                logger.warning(f"Failed to send progress update: {str(e)}")
                self.disconnect(upload_id)

connection_manager = ConnectionManager()


@router.websocket("/progress/{upload_id}")
async def websocket_progress(websocket: WebSocket, upload_id: str):
    """
    WebSocket endpoint for real-time progress updates
    """
    await connection_manager.connect(upload_id, websocket)
    try:
        while True:
            # Keep connection alive and send any pending updates
            await asyncio.sleep(1)
            
            # Check if processing is complete
            status = enhanced_processing_service.get_processing_status(upload_id)
            if status and status.get("status") in ["completed", "error"]:
                await connection_manager.send_progress_update(upload_id, {
                    "type": "final_status",
                    "status": status.get("status"),
                    "progress": status.get("progress", 0),
                    "message": status.get("current_message", "")
                })
                break
                
    except WebSocketDisconnect:
        connection_manager.disconnect(upload_id)
    except Exception as e:
        logger.error(f"WebSocket error for upload {upload_id}: {str(e)}")
        connection_manager.disconnect(upload_id)


@router.post("/upload/{upload_id}/start-enhanced-processing")
async def start_enhanced_processing(
    upload_id: str,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_active_user),
    db = Depends(get_db_session)
):
    """
    Start enhanced processing for an uploaded file
    """
    try:
        # Progress callback for real-time updates
        async def progress_callback(upload_id: str, progress: float, message: str):
            await connection_manager.send_progress_update(upload_id, {
                "type": "progress_update",
                "progress": progress,
                "message": message,
                "timestamp": datetime.utcnow().isoformat()
            })

        # Start processing in background
        background_tasks.add_task(
            enhanced_processing_service.start_enhanced_processing,
            upload_id,
            user,
            progress_callback
        )
        
        return {
            "upload_id": upload_id,
            "status": "processing_started",
            "message": "Enhanced processing started successfully",
            "websocket_url": f"/api/v1/files/enhanced/progress/{upload_id}"
        }
        
    except Exception as e:
        logger.error(f"Failed to start enhanced processing: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/upload/{upload_id}/status")
async def get_enhanced_status(
    upload_id: str,
    user: User = Depends(get_current_active_user)
):
    """
    Get enhanced processing status
    """
    try:
        status = enhanced_processing_service.get_processing_status(upload_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Upload not found")
        
        return {
            "upload_id": upload_id,
            "status": status.get("status"),
            "progress": status.get("progress", 0),
            "current_step": status.get("current_step"),
            "current_message": status.get("current_message"),
            "transaction_count": status.get("transaction_count", 0),
            "processed_count": status.get("processed_count", 0),
            "categories_applied": status.get("categories_applied", []),
            "estimated_completion": status.get("estimated_completion"),
            "start_time": status.get("start_time"),
            "last_updated": status.get("last_updated"),
            "results": status.get("results") if status.get("status") == "completed" else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get enhanced status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/upload/{upload_id}/results")
async def get_processing_results(
    upload_id: str,
    user: User = Depends(get_current_active_user)
):
    """
    Get comprehensive processing results
    """
    try:
        status = enhanced_processing_service.get_processing_status(upload_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Upload not found")
        
        if status.get("status") != "completed":
            raise HTTPException(status_code=400, detail="Processing not completed")
        
        results = status.get("results")
        if not results:
            raise HTTPException(status_code=404, detail="Results not available")
        
        # Add additional metadata for the frontend
        enhanced_results = {
            **results,
            "metadata": {
                "processing_duration": status.get("end_time") - status.get("start_time") if status.get("end_time") and status.get("start_time") else None,
                "user_id": user.id,
                "upload_timestamp": status.get("start_time"),
                "completion_timestamp": status.get("end_time")
            }
        }
        
        return enhanced_results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get processing results: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/upload/{upload_id}/export/{format}")
async def export_results(
    upload_id: str,
    format: str,
    user: User = Depends(get_current_active_user),
    db = Depends(get_db_session)
):
    """
    Export processing results in various formats
    """
    try:
        if format not in ["xlsx", "csv", "pdf"]:
            raise HTTPException(status_code=400, detail="Invalid export format")
        
        status = enhanced_processing_service.get_processing_status(upload_id)
        
        if not status or status.get("status") != "completed":
            raise HTTPException(status_code=400, detail="Processing not completed")
        
        results = status.get("results")
        if not results:
            raise HTTPException(status_code=404, detail="Results not available")
        
        # Generate export file based on format
        export_content = await _generate_export_file(upload_id, format, results)
        
        # Set appropriate headers
        filename = f"categorized_transactions_{upload_id}.{format}"
        media_type = {
            "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "csv": "text/csv",
            "pdf": "application/pdf"
        }[format]
        
        return StreamingResponse(
            export_content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export results: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload/{upload_id}/feedback")
async def submit_processing_feedback(
    upload_id: str,
    feedback_data: Dict[str, Any],
    user: User = Depends(get_current_active_user)
):
    """
    Submit feedback about processing results for continuous improvement
    """
    try:
        # Store feedback for machine learning improvements
        feedback_entry = {
            "upload_id": upload_id,
            "user_id": user.id,
            "timestamp": datetime.utcnow(),
            "accuracy_rating": feedback_data.get("accuracy_rating"),
            "category_corrections": feedback_data.get("category_corrections", []),
            "general_feedback": feedback_data.get("general_feedback"),
            "suggested_improvements": feedback_data.get("suggested_improvements", [])
        }
        
        # In a real implementation, this would be stored in a feedback database
        logger.info(f"Received feedback for upload {upload_id}: {feedback_entry}")
        
        return {
            "message": "Feedback submitted successfully",
            "upload_id": upload_id,
            "feedback_id": f"feedback_{upload_id}_{int(datetime.utcnow().timestamp())}"
        }
        
    except Exception as e:
        logger.error(f"Failed to submit feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/upload/{upload_id}/cleanup")
async def cleanup_processing_data(
    upload_id: str,
    user: User = Depends(get_current_active_user)
):
    """
    Clean up processing data after user is done
    """
    try:
        enhanced_processing_service.cleanup_processing_state(upload_id)
        connection_manager.disconnect(upload_id)
        
        return {
            "message": "Processing data cleaned up successfully",
            "upload_id": upload_id
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup processing data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def _generate_export_file(upload_id: str, format: str, results: Dict[str, Any]):
    """
    Generate export file content based on format
    """
    if format == "csv":
        return _generate_csv_export(results)
    elif format == "xlsx":
        return _generate_xlsx_export(results)
    elif format == "pdf":
        return _generate_pdf_export(results)
    else:
        raise ValueError(f"Unsupported export format: {format}")


async def _generate_csv_export(results: Dict[str, Any]):
    """Generate CSV export"""
    
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Headers
    writer.writerow([
        "Date", "Description", "Amount", "Category", "Confidence Score", "GL Code"
    ])
    
    # Sample data (in real implementation, would fetch from database)
    for i in range(results.get("total_transactions", 10)):
        writer.writerow([
            f"2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}",
            f"Transaction {i+1}",
            f"{(i + 1) * 12.34:.2f}",
            results["categories_applied"][i % len(results["categories_applied"])],
            f"{0.85 + (i % 15) / 100:.2f}",
            f"GL-{1000 + (i % 9000)}"
        ])
    
    output.seek(0)
    return io.BytesIO(output.getvalue().encode())


async def _generate_xlsx_export(results: Dict[str, Any]):
    """Generate Excel export"""
    # In a real implementation, would use openpyxl or xlswriter
    return await _generate_csv_export(results)  # Simplified for demo


async def _generate_pdf_export(results: Dict[str, Any]):
    """Generate PDF export"""
    # In a real implementation, would use reportlab or similar
    return await _generate_csv_export(results)  # Simplified for demo