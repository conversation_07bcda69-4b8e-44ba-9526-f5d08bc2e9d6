"""
File Service - Handles file upload and processing operations.

Provides functionality for:
- Excel/CSV file processing
- Column mapping and validation
- Data extraction and transformation
- Multi-sheet handling
"""

import io
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd
from asyncpg import Connection

from ...shared.exceptions import ServiceError, ValidationError
from ...shared.monitoring.performance import FileUploadTracker
from .schemas import (
    FileProcessingResult,
    SheetInfo,
)

logger = logging.getLogger(__name__)


class FileService:
    """
    Service for handling file uploads and processing.

    Supports Excel (xlsx, xls) and CSV file formats with:
    - Multi-sheet processing
    - Automatic column detection
    - Data validation
    - Transaction extraction
    """

    def __init__(self, conn: Connection):
        self.conn = conn
        self.supported_extensions = {".xlsx", ".xls", ".csv"}

    async def process_file(
        self,
        file_content: bytes,
        filename: str,
        tenant_id: int,
        upload_id: Optional[str] = None,
    ) -> FileProcessingResult:
        """
        Process uploaded file and extract transaction data.

        Args:
            file_content: Raw file bytes
            filename: Original filename
            tenant_id: Tenant ID for data isolation
            upload_id: Optional upload tracking ID

        Returns:
            FileProcessingResult with extracted data and metadata
        """
        # Track file upload performance
        with FileUploadTracker(filename, len(file_content)):
            try:
                # Validate file extension
                file_ext = Path(filename).suffix.lower()
                if file_ext not in self.supported_extensions:
                    raise ValidationError(
                        f"Unsupported file type: {file_ext}. "
                        f"Supported types: {', '.join(self.supported_extensions)}"
                    )

                # Process based on file type
                if file_ext == ".csv":
                    sheets_data = await self._process_csv(file_content, filename)
                else:
                    sheets_data = await self._process_excel(file_content, filename)

                # Calculate totals
                total_rows = sum(sheet.row_count for sheet in sheets_data)

                return FileProcessingResult(
                    success=True,
                    filename=filename,
                    upload_id=upload_id or self._generate_upload_id(),
                    sheets=sheets_data,
                    total_rows=total_rows,
                    processed_at=datetime.now(timezone.utc).isoformat(),
                )

            except Exception as e:
                logger.error(f"File processing failed for {filename}: {e}")
                raise ServiceError(
                    f"Failed to process file: {str(e)}",
                    service_name="FileService",
                    operation="process_file",
                    error_code="FILE_PROCESSING_ERROR",
                    context={"tenant_id": tenant_id, "filename": filename},
                    original_error=e,
                )

    async def _process_csv(self, file_content: bytes, filename: str) -> List[SheetInfo]:
        """Process CSV file and return sheet data."""
        try:
            # Read CSV file
            df = pd.read_csv(io.BytesIO(file_content))

            # Extract metadata
            # Convert numeric column headers to strings
            columns = [str(col) for col in df.columns.tolist()]

            # Also need to convert column names in sample data to strings
            sample_data = []
            for row in df.head(5).to_dict("records"):
                # Convert all keys to strings to handle numeric column headers
                sample_data.append({str(k): v for k, v in row.items()})

            sheet_info = SheetInfo(
                name="Sheet1",
                columns=columns,
                row_count=len(df),
                sample_data=sample_data,
            )

            return [sheet_info]

        except Exception as e:
            logger.error(f"CSV processing error: {e}")
            raise

    async def _process_excel(
        self, file_content: bytes, filename: str
    ) -> List[SheetInfo]:
        """Process Excel file with multiple sheets and smart header detection."""
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(io.BytesIO(file_content))
            sheets_data = []

            for sheet_name in excel_file.sheet_names:
                # Smart header detection - try different header rows
                df = self._read_excel_with_smart_headers(excel_file, sheet_name)

                # Skip empty sheets
                if df.empty:
                    continue

                # Clean column names - remove unnamed patterns
                columns = self._clean_column_names(df.columns.tolist())
                df.columns = columns

                # Also need to convert column names in sample data to strings
                sample_data = []
                for row in df.head(5).to_dict("records"):
                    # Convert all keys to strings to handle numeric column headers
                    sample_data.append({str(k): v for k, v in row.items()})

                sheet_info = SheetInfo(
                    name=sheet_name,
                    columns=columns,
                    row_count=len(df),
                    sample_data=sample_data,
                )
                sheets_data.append(sheet_info)

            return sheets_data

        except Exception as e:
            logger.error(f"Excel processing error: {e}")
            raise

    def _read_excel_with_smart_headers(
        self, excel_file, sheet_name: str
    ) -> pd.DataFrame:
        """Read Excel with intelligent header detection."""
        # Try different header row positions
        for header_row in [0, 1, 2]:
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_row)

                # Check if this gives us good column names
                if self._has_valid_headers(df.columns):
                    return df

            except Exception:
                continue

        # Fallback to default reading
        return pd.read_excel(excel_file, sheet_name=sheet_name)

    def _has_valid_headers(self, columns) -> bool:
        """Check if column names look like real headers."""
        unnamed_count = sum(1 for col in columns if str(col).startswith("Unnamed"))
        numeric_count = sum(1 for col in columns if str(col).replace(".", "").isdigit())

        # Valid if less than 50% are unnamed or numeric
        total_columns = len(columns)
        if total_columns == 0:
            return False

        bad_columns = unnamed_count + numeric_count
        return (bad_columns / total_columns) < 0.5

    def _clean_column_names(self, columns: List) -> List[str]:
        """Clean and normalize column names."""
        cleaned = []
        for i, col in enumerate(columns):
            col_str = str(col)

            # Replace unnamed columns with descriptive names
            if col_str.startswith("Unnamed"):
                col_str = f"Column_{i + 1}"
            elif col_str.replace(".", "").isdigit():
                # Numeric column names - try to make them descriptive
                col_str = f"Value_{col_str}"

            cleaned.append(col_str)

        return cleaned

    async def validate_column_mapping(
        self, columns: List[str], mapping: Dict[str, str]
    ) -> bool:
        """
        Validate that column mapping is valid.

        Args:
            columns: Available columns in the file
            mapping: Proposed column mapping

        Returns:
            True if mapping is valid
        """
        # Check that all mapped columns exist
        for source_col in mapping.keys():
            if source_col not in columns:
                raise ValidationError(
                    f"Column '{source_col}' not found in file. "
                    f"Available columns: {', '.join(columns)}"
                )

        # Check required fields are mapped
        required_fields = {"date", "description", "amount"}
        mapped_fields = set(mapping.values())

        missing_fields = required_fields - mapped_fields
        if missing_fields:
            raise ValidationError(
                f"Required fields not mapped: {', '.join(missing_fields)}"
            )

        return True

    async def extract_transactions(
        self,
        sheet_data: List[Dict[str, Any]],
        column_mapping: Dict[str, str],
        tenant_id: int,
    ) -> List[Dict[str, Any]]:
        """
        Extract transactions from sheet data using column mapping.
        Enhanced to support AI-detected debit/credit columns.

        Args:
            sheet_data: Raw data from sheet
            column_mapping: Mapping of columns to transaction fields
            tenant_id: Tenant ID for data isolation

        Returns:
            List of transaction dictionaries
        """
        transactions = []

        logger.info(f"Extracting transactions with column mapping: {column_mapping}")

        for row in sheet_data:
            try:
                # Extract mapped fields
                transaction = {}

                for source_col, target_field in column_mapping.items():
                    value = row.get(source_col)

                    # Skip if value is NaN, None, or empty string
                    if pd.isna(value) or value is None or str(value).strip() == "":
                        continue

                    # Handle special field processing
                    if target_field == "date" and value:
                        # Convert to standard date format
                        if isinstance(value, pd.Timestamp):
                            value = value.strftime("%Y-%m-%d")
                        elif isinstance(value, str):
                            # Try to parse date string
                            try:
                                parsed_date = pd.to_datetime(value)
                                value = parsed_date.strftime("%Y-%m-%d")
                            except (ValueError, TypeError, pd.errors.ParserError):
                                pass

                    elif target_field in [
                        "description",
                        "vendor_name",
                        "account",
                        "category",
                    ]:
                        # Clean up text fields
                        value = str(value).strip()
                        # Skip if field contains "nan" or is empty
                        if value.lower() in ["nan", "null", "none", ""]:
                            continue
                    elif (
                        target_field in ["amount", "debit_amount", "credit_amount"]
                        and value
                    ):
                        # Ensure amount is numeric
                        try:
                            # Handle various currency formats
                            value_str = str(value).strip()
                            # Remove currency symbols and thousands separators
                            value_str = (
                                value_str.replace(",", "")
                                .replace("$", "")
                                .replace("£", "")
                                .replace("€", "")
                            )
                            # Handle parentheses for negative values (accounting format)
                            if value_str.startswith("(") and value_str.endswith(")"):
                                value_str = "-" + value_str[1:-1]
                            value = float(value_str)
                        except (ValueError, TypeError):
                            logger.warning(f"Could not parse amount: {value}")
                            value = 0.0

                    transaction[target_field] = value

                # Add tenant ID
                transaction["tenant_id"] = tenant_id

                # Calculate final amount from debit/credit or use amount directly
                debit_amount = transaction.get("debit_amount", 0)
                credit_amount = transaction.get("credit_amount", 0)
                amount = transaction.get("amount", 0)

                # Handle NaN values from pandas
                import math

                # Handle various NaN representations
                def is_nan_value(val):
                    if val is None:
                        return True
                    if isinstance(val, float) and math.isnan(val):
                        return True
                    if str(val).lower() in ["nan", "null", "none", ""]:
                        return True
                    return False

                if is_nan_value(debit_amount):
                    debit_amount = 0
                if is_nan_value(credit_amount):
                    credit_amount = 0
                if is_nan_value(amount):
                    amount = 0

                # For debit/credit systems: debit is negative, credit is positive
                if debit_amount or credit_amount:
                    final_amount = float(credit_amount or 0) - float(debit_amount or 0)
                    transaction["amount"] = final_amount
                elif amount:
                    transaction["amount"] = float(amount)
                else:
                    transaction["amount"] = 0.0

                # Only add if we have required fields (date, description, and any amount)
                if (
                    transaction.get("date")
                    and transaction.get("description")
                    and transaction.get("amount") != 0
                ):
                    transactions.append(transaction)

            except Exception as e:
                logger.warning(f"Error processing row: {e}")
                continue

        return transactions

    def _generate_upload_id(self) -> str:
        """Generate unique upload ID."""
        import uuid

        return str(uuid.uuid4())

    async def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats."""
        return list(self.supported_extensions)

    # Test compatibility methods
    def _detect_transaction_columns(self, columns_or_df) -> Dict[str, str]:
        """Test compatibility method for automatic column detection."""
        try:
            # Handle both DataFrame and column list inputs
            if hasattr(columns_or_df, "columns"):
                columns = list(columns_or_df.columns)
            else:
                columns = columns_or_df

            # Standard column mapping detection
            mapping = {}

            # Map common column names to transaction fields
            column_mappings = {
                # Date fields
                "date": [
                    "date",
                    "transaction_date",
                    "trans_date",
                    "posting_date",
                    "value_date",
                ],
                # Description fields
                "description": [
                    "description",
                    "memo",
                    "details",
                    "narration",
                    "reference",
                    "payee",
                    "vendor",
                    "merchant",
                ],
                # Amount fields
                "amount": ["amount", "value", "transaction_amount", "sum", "total"],
                "debit_amount": ["debit", "debit_amount", "withdrawal", "expense"],
                "credit_amount": ["credit", "credit_amount", "deposit", "income"],
            }

            # Find best match for each transaction field
            for target_field, possible_names in column_mappings.items():
                for col in columns:
                    col_lower = col.lower().replace(" ", "_").replace("-", "_")
                    if any(name in col_lower for name in possible_names):
                        mapping[target_field] = (
                            col  # Map field to column name (reversed for test compatibility)
                        )
                        break

            # Special logic: if we have both debit and credit but no amount, add amount using debit column
            if (
                "debit_amount" in mapping
                and "credit_amount" in mapping
                and "amount" not in mapping
            ):
                mapping["amount"] = mapping[
                    "debit_amount"
                ]  # Use debit column as primary amount

            logger.info(f"Auto-detected column mapping: {mapping}")
            return mapping

        except Exception as e:
            logger.error(f"Column detection failed: {e}")
            return {}

    def _validate_date_format(self, date_value: Any) -> bool:
        """Test compatibility method for date format validation."""
        try:
            if not date_value or pd.isna(date_value):
                return False

            # Try to parse as date
            if isinstance(date_value, (pd.Timestamp, datetime)):
                return True

            # Try parsing string dates
            if isinstance(date_value, str):
                try:
                    pd.to_datetime(date_value)
                    return True
                except (ValueError, TypeError, pd.errors.ParserError):
                    return False

            return False

        except Exception as e:
            logger.warning(f"Date validation error: {e}")
            return False

    def _validate_amount_format(self, amount_value: Any) -> Optional[float]:
        """Test compatibility method for amount format validation."""
        try:
            if amount_value is None or pd.isna(amount_value):
                return None

            # Try converting to float
            if isinstance(amount_value, (int, float)):
                return float(amount_value) if not pd.isna(amount_value) else None

            if isinstance(amount_value, str):
                # Remove currency symbols and formatting
                cleaned = (
                    amount_value.strip()
                    .replace(",", "")
                    .replace("$", "")
                    .replace("£", "")
                    .replace("€", "")
                )

                # Handle accounting format (parentheses for negative)
                if cleaned.startswith("(") and cleaned.endswith(")"):
                    cleaned = "-" + cleaned[1:-1]

                try:
                    return float(cleaned)
                except (ValueError, TypeError):
                    return None

            return None

        except Exception as e:
            logger.warning(f"Amount validation error: {e}")
            return None

    def _extract_file_metadata(
        self, file_content: bytes, filename: str
    ) -> Dict[str, Any]:
        """Test compatibility method for file metadata extraction."""
        try:
            import hashlib

            file_size = len(file_content)
            file_ext = Path(filename).suffix.lower()

            # Calculate file hash
            file_hash = hashlib.sha256(file_content).hexdigest()

            metadata = {
                "filename": filename,
                "file_size": file_size,
                "file_extension": file_ext,
                "file_hash": file_hash,
                "mime_type": self._get_mime_type(file_ext),
                "created_at": datetime.now(timezone.utc).isoformat(),
                "encoding": "utf-8",  # Default encoding
            }

            # Try to detect encoding for text files
            if file_ext == ".csv":
                try:
                    import chardet

                    detected = chardet.detect(file_content)
                    metadata["encoding"] = detected.get("encoding", "utf-8")
                    metadata["encoding_confidence"] = detected.get("confidence", 0.0)
                except ImportError:
                    pass  # chardet not available

            # For Excel files, get sheet count
            if file_ext in [".xlsx", ".xls"]:
                try:
                    excel_file = pd.ExcelFile(io.BytesIO(file_content))
                    metadata["sheet_count"] = len(excel_file.sheet_names)
                    metadata["sheet_names"] = excel_file.sheet_names
                except Exception:
                    metadata["sheet_count"] = 0
                    metadata["sheet_names"] = []

            return metadata

        except Exception as e:
            logger.error(f"Metadata extraction failed: {e}")
            return {
                "filename": filename,
                "file_size": len(file_content),
                "error": str(e),
            }

    def _get_mime_type(self, file_ext: str) -> str:
        """Get MIME type for file extension."""
        mime_types = {
            ".csv": "text/csv",
            ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".xls": "application/vnd.ms-excel",
        }
        return mime_types.get(file_ext, "application/octet-stream")
