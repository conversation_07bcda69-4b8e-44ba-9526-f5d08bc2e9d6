"""
File Upload Deduplication Service
=================================

Integrates duplicate detection into the file upload pipeline to prevent 
customers from accidentally creating duplicate transactions when uploading 
the same file multiple times.

Key Features:
- File-level duplicate detection (same filename, size, hash)
- Transaction-level duplicate detection within uploads
- Configurable policies (skip, merge, warn)
- Audit trail for customer transparency
"""

import hashlib
import json
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from asyncpg import Connection

from ..transactions.duplicate_detection_service import (
    DeduplicationResult,
    DuplicateDetectionService,
)

logger = logging.getLogger(__name__)


@dataclass
class FileUploadPolicy:
    """Configuration for duplicate handling during file uploads."""
    
    # STRICT POLICY: NEVER ALLOW DUPLICATES
    skip_duplicate_files: bool = True  # Always reject duplicate files
    skip_duplicate_transactions: bool = True  # Always reject duplicate transactions
    confidence_threshold: float = 0.85  # Minimum confidence for duplicate detection
    
    # No flexibility - strict duplicate prevention
    allow_force_upload: bool = False  # NEVER allow force upload of duplicates
    merge_similar_transactions: bool = False  # NEVER merge - reject duplicates


@dataclass
class FileDeduplicationResult:
    """Result of file upload deduplication process."""
    
    # File-level results
    is_duplicate_file: bool
    duplicate_upload_id: Optional[str]
    file_hash: str
    
    # Transaction-level results
    transaction_deduplication: Optional[DeduplicationResult]
    unique_transactions_count: int
    duplicate_transactions_count: int
    
    # Recommendations
    recommended_action: str  # 'proceed', 'skip', 'warn', 'force_required'
    customer_message: str
    duplicate_details: Dict[str, Any]


class UploadDeduplicationService:
    """Service to handle duplicate detection during file uploads."""
    
    def __init__(self, conn: Connection, policy: Optional[FileUploadPolicy] = None):
        self.conn = conn
        self.policy = policy or FileUploadPolicy()
        self.duplicate_service = DuplicateDetectionService(conn)
        
    async def check_file_upload_duplicates(
        self,
        tenant_id: int,
        filename: str,
        file_content: bytes,
        transactions: List[Dict[str, Any]]
    ) -> FileDeduplicationResult:
        """
        Comprehensive duplicate check for file uploads.
        
        Checks both file-level and transaction-level duplicates.
        """
        
        # Calculate file hash for duplicate detection
        file_hash = hashlib.sha256(file_content).hexdigest()
        
        # Check for file-level duplicates
        duplicate_upload = await self._check_duplicate_file(tenant_id, filename, file_hash)
        is_duplicate_file = duplicate_upload is not None
        
        # Check for transaction-level duplicates within the file
        transaction_dedup = await self._check_transaction_duplicates(tenant_id, transactions)
        
        # Determine recommended action and customer message
        action, message = self._determine_action_and_message(
            is_duplicate_file, duplicate_upload, transaction_dedup
        )
        
        return FileDeduplicationResult(
            is_duplicate_file=is_duplicate_file,
            duplicate_upload_id=duplicate_upload.get("id") if duplicate_upload else None,
            file_hash=file_hash,
            transaction_deduplication=transaction_dedup,
            unique_transactions_count=transaction_dedup.unique_transactions if transaction_dedup else len(transactions),
            duplicate_transactions_count=transaction_dedup.duplicates_found if transaction_dedup else 0,
            recommended_action=action,
            customer_message=message,
            duplicate_details={
                "duplicate_file": duplicate_upload,
                "transaction_duplicates": transaction_dedup.duplicate_matches if transaction_dedup else []
            }
        )
    
    async def _check_duplicate_file(
        self, 
        tenant_id: int, 
        filename: str, 
        file_hash: str
    ) -> Optional[Dict[str, Any]]:
        """Check if this exact file has been uploaded before."""
        
        query = """
        SELECT id, filename, created_at, status
        FROM uploads
        WHERE tenant_id = $1 
        AND (filename = $2 OR file_hash = $3)
        AND status IN ('completed', 'processing')
        ORDER BY created_at DESC
        LIMIT 1
        """
        
        result = await self.conn.fetchrow(query, tenant_id, filename, file_hash)
        return dict(result) if result else None
    
    async def _check_transaction_duplicates(
        self,
        tenant_id: int,
        transactions: List[Dict[str, Any]]
    ) -> Optional[DeduplicationResult]:
        """Check for duplicate transactions within the upload."""
        
        if not transactions:
            return None
            
        # Use the existing duplicate detection service
        return await self.duplicate_service.detect_duplicates_batch(
            tenant_id=tenant_id,
            transactions=transactions,
            detection_strategy="comprehensive",  # Use all detection methods
            skip_duplicates=True  # Skip duplicates according to strict policy
        )
    
    def _determine_action_and_message(
        self,
        is_duplicate_file: bool,
        duplicate_upload: Optional[Dict[str, Any]],
        transaction_dedup: Optional[DeduplicationResult]
    ) -> Tuple[str, str]:
        """Determine recommended action and customer message. STRICT: NEVER ALLOW DUPLICATES."""
        
        # STRICT FILE-LEVEL DUPLICATE REJECTION
        if is_duplicate_file:
            upload_date = duplicate_upload["created_at"].strftime("%Y-%m-%d %H:%M")
            return "reject", f"DUPLICATE FILE: This exact file was already uploaded on {upload_date}. Upload rejected."
        
        # STRICT TRANSACTION-LEVEL DUPLICATE REJECTION
        if transaction_dedup and transaction_dedup.duplicates_found > 0:
            duplicate_count = transaction_dedup.duplicates_found
            # unique_count = transaction_dedup.unique_transactions  # Not used
            
            if duplicate_count == transaction_dedup.total_checked:
                return "reject", f"DUPLICATE TRANSACTIONS: All {duplicate_count} transactions already exist. Upload rejected."
            else:
                return "reject", f"DUPLICATE TRANSACTIONS: Found {duplicate_count} duplicate transactions out of {transaction_dedup.total_checked}. Upload rejected to maintain data integrity."
        
        # No duplicates detected - proceed
        return "proceed", "No duplicates detected. Upload will proceed."

    async def store_file_hash(self, upload_id: str, file_hash: str) -> None:
        """Store file hash for future duplicate detection."""
        
        query = """
        UPDATE uploads 
        SET file_hash = $1, metadata = COALESCE(metadata, '{}')::jsonb || $2::jsonb
        WHERE id = $3
        """
        
        metadata_update = json.dumps({"file_hash": file_hash})
        await self.conn.execute(query, file_hash, metadata_update, upload_id)
        
        logger.info(f"Stored file hash for upload {upload_id}: {file_hash[:8]}...")


# Convenience function for integration
async def check_upload_duplicates(
    conn: Connection,
    tenant_id: int,
    filename: str,
    file_content: bytes,
    transactions: List[Dict[str, Any]],
    policy: Optional[FileUploadPolicy] = None
) -> FileDeduplicationResult:
    """
    Convenience function to check for upload duplicates.
    
    Usage in file upload endpoints:
    
    dedup_result = await check_upload_duplicates(
        conn, tenant_id, filename, file_content, transactions
    )
    
    if dedup_result.recommended_action == "skip":
        raise HTTPException(409, dedup_result.customer_message)
    elif dedup_result.recommended_action == "warn":
        # Log warning and proceed or require force flag
        pass
    """
    
    service = UploadDeduplicationService(conn, policy)
    return await service.check_file_upload_duplicates(
        tenant_id, filename, file_content, transactions
    )