"""
Batch Upload Processing Service Tool
====================================

Service layer tool for batch processing file uploads with schema interpretation.
Follows the architectural pattern of service tools that agents can use.
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

from asyncpg import Connection

from ..categories.batch_categorization_tool import (
    BatchCategorizationRequest,
    BatchCategorizationService,
)
from .schema_interpretation_agent import SchemaInterpretationAgent

logger = logging.getLogger(__name__)


@dataclass
class UploadProcessingRequest:
    """Request for processing file uploads"""

    upload_id: int
    tenant_id: int
    use_schema_interpretation: bool = True
    auto_categorize: bool = True
    batch_size: int = 100


@dataclass
class UploadProcessingResult:
    """Result from upload processing"""

    upload_id: int
    total_rows: int
    processed_rows: int
    categorized_rows: int
    schema_mapping: Optional[Dict[str, str]]
    processing_time_seconds: float
    errors: List[str]


class BatchUploadProcessingService:
    """
    Service layer for batch processing file uploads.

    This service:
    - Processes uploaded files with schema interpretation
    - Extracts transactions from mapped columns
    - Optionally categorizes transactions
    - Updates database with results
    """

    def __init__(self):
        # No stored connections - acquire per operation
        self.schema_agent = SchemaInterpretationAgent()
        self.categorization_service = BatchCategorizationService()

    async def process_upload(
        self, request: UploadProcessingRequest
    ) -> UploadProcessingResult:
        """
        Process an uploaded file with schema interpretation and categorization.
        """
        start_time = datetime.now()
        errors = []

        try:
            # Get upload details
            upload_query = """
            SELECT u.*, f.file_content, f.column_headers, f.sample_data
            FROM uploads u
            LEFT JOIN file_processing_reports f ON u.id = f.upload_id
            WHERE u.id = $1 AND u.tenant_id = $2
            """
            upload_row = await self.db_conn.fetchrow(
                upload_query, request.upload_id, request.tenant_id
            )

            if not upload_row:
                raise ValueError(f"Upload {request.upload_id} not found")

            # Get file data
            filename = upload_row["filename"]
            headers = upload_row.get("column_headers", [])
            sample_data = upload_row.get("sample_data", [])

            # Schema interpretation
            schema_mapping = None
            if request.use_schema_interpretation and headers:
                # Get tenant context for better interpretation
                context = await self._get_tenant_context(request.tenant_id)

                # Use schema interpretation agent
                mapping_result = (
                    await self.schema_agent.suggest_schema_mapping_tool_function(
                        file_name=filename,
                        file_headers=headers,
                        sample_data=sample_data[:10],  # First 10 rows
                        column_stats=None,
                        previous_patterns=context.get("previous_mappings"),
                    )
                )

                schema_mapping = mapping_result.get("mapping", {})

                # Store schema mapping
                await self._store_schema_mapping(request.upload_id, schema_mapping)

            # Extract transactions using schema mapping
            transactions = await self._extract_transactions(upload_row, schema_mapping)

            # Categorize if requested
            categorized_count = 0
            if request.auto_categorize and transactions:
                # Get remarks column mapping for M1
                remarks_column = None
                if request.tenant_id == 3:  # Nuvie M1
                    remarks_column = schema_mapping.get("remarks")

                # Prepare transactions for categorization
                txn_data = []
                for txn in transactions:
                    txn_dict = {
                        "id": txn["id"],
                        "description": txn["description"],
                        "amount": float(txn["amount"]),
                        "date": txn["transaction_date"],
                    }

                    # Add remarks if available
                    if remarks_column and txn.get("metadata"):
                        txn_dict["remarks"] = txn["metadata"].get(remarks_column)

                    txn_data.append(txn_dict)

                # Batch categorize
                cat_request = BatchCategorizationRequest(
                    transactions=txn_data,
                    tenant_id=request.tenant_id,
                    batch_size=request.batch_size,
                )

                cat_result = await self.categorization_service.categorize_batch(
                    cat_request
                )

                # Update database
                categorized_count = await self.categorization_service.update_database(
                    request.tenant_id, cat_result.results
                )

            # Update upload status
            await self.db_conn.execute(
                """
                UPDATE uploads 
                SET processing_status = 'completed',
                    processed_at = CURRENT_TIMESTAMP,
                    metadata = COALESCE(metadata, '{}'::jsonb) || 
                              jsonb_build_object(
                                  'total_rows', $1,
                                  'processed_rows', $2,
                                  'categorized_rows', $3,
                                  'schema_mapping', $4::jsonb
                              )
                WHERE id = $5
                """,
                len(transactions),
                len(transactions),
                categorized_count,
                schema_mapping or {},
                request.upload_id,
            )

            processing_time = (datetime.now() - start_time).total_seconds()

            return UploadProcessingResult(
                upload_id=request.upload_id,
                total_rows=len(transactions),
                processed_rows=len(transactions),
                categorized_rows=categorized_count,
                schema_mapping=schema_mapping,
                processing_time_seconds=processing_time,
                errors=errors,
            )

        except Exception as e:
            logger.error(f"Upload processing failed: {e}")
            errors.append(str(e))

            # Update upload status to failed
            await self.db_conn.execute(
                """
                UPDATE uploads 
                SET processing_status = 'failed',
                    error_message = $1
                WHERE id = $2
                """,
                str(e),
                request.upload_id,
            )

            return UploadProcessingResult(
                upload_id=request.upload_id,
                total_rows=0,
                processed_rows=0,
                categorized_rows=0,
                schema_mapping=None,
                processing_time_seconds=(datetime.now() - start_time).total_seconds(),
                errors=errors,
            )

    async def _get_tenant_context(self, tenant_id: int) -> Dict[str, Any]:
        """Get tenant-specific context for processing"""
        # Get previous successful mappings
        mappings_query = """
        SELECT DISTINCT metadata->>'schema_mapping' as mapping
        FROM uploads
        WHERE tenant_id = $1 
          AND processing_status = 'completed'
          AND metadata->>'schema_mapping' IS NOT NULL
        ORDER BY processed_at DESC
        LIMIT 5
        """

        rows = await self.db_conn.fetch(mappings_query, tenant_id)
        previous_mappings = [row["mapping"] for row in rows if row["mapping"]]

        return {"tenant_id": tenant_id, "previous_mappings": previous_mappings}

    async def _store_schema_mapping(
        self, upload_id: int, schema_mapping: Dict[str, str], db_conn: Connection
    ):
        """Store schema mapping for future reference"""
        await db_conn.execute(
            """
            INSERT INTO schema_interpretations (
                upload_id, 
                interpreted_schema,
                confidence_score,
                created_at
            ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
            ON CONFLICT (upload_id) DO UPDATE
            SET interpreted_schema = $2,
                confidence_score = $3,
                updated_at = CURRENT_TIMESTAMP
            """,
            upload_id,
            schema_mapping,
            0.85,  # Default confidence
        )

    async def _extract_transactions(
        self, upload_row: Any, schema_mapping: Optional[Dict[str, str]]
    ) -> List[Dict[str, Any]]:
        """Extract transactions from upload using schema mapping"""
        # This would normally parse the actual file content
        # For now, get from existing transactions
        txn_query = """
        SELECT * FROM transactions
        WHERE upload_id = $1
        ORDER BY id
        """

        rows = await self.db_conn.fetch(txn_query, upload_row["id"])

        return [dict(row) for row in rows]


# Create ADK Function Tool for agents
async def batch_process_uploads_tool(
    upload_ids: List[int],
    tenant_id: int,
    db_conn: Connection,
    use_schema_interpretation: bool = True,
    auto_categorize: bool = True,
    batch_size: int = 100,
) -> Dict[str, Any]:
    """
    ADK tool function for batch processing file uploads.

    Args:
        upload_ids: List of upload IDs to process
        tenant_id: Tenant ID
        db_conn: Database connection
        use_schema_interpretation: Whether to use AI for schema mapping
        auto_categorize: Whether to categorize transactions
        batch_size: Batch size for processing

    Returns:
        Processing results for all uploads
    """
    service = BatchUploadProcessingService(db_conn)
    results = []

    for upload_id in upload_ids:
        request = UploadProcessingRequest(
            upload_id=upload_id,
            tenant_id=tenant_id,
            use_schema_interpretation=use_schema_interpretation,
            auto_categorize=auto_categorize,
            batch_size=batch_size,
        )

        result = await service.process_upload(request)
        results.append(
            {
                "upload_id": result.upload_id,
                "success": len(result.errors) == 0,
                "processed_rows": result.processed_rows,
                "categorized_rows": result.categorized_rows,
                "errors": result.errors,
            }
        )

    return {
        "total_uploads": len(upload_ids),
        "successful": sum(1 for r in results if r["success"]),
        "results": results,
    }
