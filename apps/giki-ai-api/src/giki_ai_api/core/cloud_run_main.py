"""
Cloud Run optimized entry point for FastAPI application
"""

import os

import uvicorn

from .main import app

if __name__ == "__main__":
    # Get port from environment variable (Cloud Run requirement)
    port = int(os.environ.get("PORT", 8080))

    # Run with Cloud Run optimized settings
    uvicorn.run(
        app,
        host="0.0.0.0",  # Must bind to all interfaces for Cloud Run
        port=port,
        workers=1,  # Single worker for Cloud Run
        access_log=True,
        timeout_keep_alive=30,
    )
