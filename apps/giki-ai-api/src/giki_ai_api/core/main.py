import json
import logging
import os
import sys
import time
import uuid
from contextlib import asynccontextmanager  # Added for lifespan
from typing import Any, Dict, List, Optional

# Try to use uvloop if available (fixes asyncpg SCRAM auth issues)
try:
    import asyncio

    import uvloop

    asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
    logging.info("Using uvloop for asyncio event loop")
except ImportError:
    logging.info("uvloop not available, using default asyncio event loop")

from asyncpg import Connection
from fastapi import APIRouter, Depends, FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field

from ..domains.auth.models import User
from ..domains.auth.secure_auth import get_current_active_user
from ..shared.error_handlers import register_exception_handlers
from ..shared.middleware.timeout_middleware import TimeoutMiddleware
from . import dependencies  # Import the dependencies module
from .config import settings
from .dependencies import get_current_tenant_id, get_db_session
from .security_middleware import request_id_middleware, security_headers_middleware


class CustomerAccountNode(BaseModel):
    """Represents a node in the hierarchy."""

    id: int
    account_code: str
    account_name: str
    customer_id: str
    parent_account_id: int | None = None
    children: List["CustomerAccountNode"] = []


class CustomerHierarchy(BaseModel):
    """Represents the full hierarchy for a customer."""

    customer_id: str
    accounts: List[CustomerAccountNode] = Field(
        ..., description="List of root account nodes for the customer."
    )


# Configure structured logging with JSON format for better monitoring
from logging.handlers import RotatingFileHandler


class StructuredFormatter(logging.Formatter):
    """Custom formatter that outputs structured JSON logs."""
    
    def format(self, record):
        """Format log record as JSON structure."""
        log_entry = {
            "timestamp": self.formatTime(record, self.datefmt),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "process_id": os.getpid(),
            "thread_id": record.thread,
        }
        
        # Add request context if available
        if hasattr(record, 'request_id'):
            log_entry["request_id"] = record.request_id
        if hasattr(record, 'user_id'):
            log_entry["user_id"] = record.user_id
        if hasattr(record, 'tenant_id'):
            log_entry["tenant_id"] = record.tenant_id
        if hasattr(record, 'trace_id'):
            log_entry["trace_id"] = record.trace_id
            
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
            
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'exc_info', 'exc_text', 'stack_info']:
                if not key.startswith('_'):
                    log_entry[f"extra_{key}"] = value
        
        return json.dumps(log_entry, default=str)


# Configure console logging with simple format for development
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(
    logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
)

# Configure structured JSON logging for files
log_file_path = os.path.join(os.getcwd(), "logs", "api-app.log")
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

file_handler = RotatingFileHandler(
    log_file_path,
    maxBytes=10 * 1024 * 1024,  # 10MB
    backupCount=5,
)
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(StructuredFormatter())

# Configure error-specific log file
error_log_path = os.path.join(os.getcwd(), "logs", "api-errors.log")
error_handler = RotatingFileHandler(
    error_log_path,
    maxBytes=10 * 1024 * 1024,  # 10MB
    backupCount=10,
)
error_handler.setLevel(logging.ERROR)
error_handler.setFormatter(StructuredFormatter())

# Configure root logger
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)
root_logger.addHandler(console_handler)
root_logger.addHandler(file_handler)
root_logger.addHandler(error_handler)

# Remove default handlers to avoid duplication
for handler in root_logger.handlers[:]:
    if handler not in [console_handler, file_handler, error_handler]:
        root_logger.removeHandler(handler)

# Suppress bcrypt deprecation warnings
bcrypt_logger = logging.getLogger("passlib.handlers.bcrypt")
bcrypt_logger.setLevel(logging.ERROR)

logger = logging.getLogger(__name__)


async def create_test_users_on_startup():
    """Create test users on startup for authentication testing"""
    try:
        from ..domains.auth.secure_auth import get_password_hash
        from .database import get_db_session

        test_users = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]

        async for conn in get_db_session():
            # Get or create default tenant
            default_tenant = await conn.fetchrow(
                "SELECT id FROM tenants WHERE name = 'default' LIMIT 1"
            )
            if not default_tenant:
                tenant_id = await conn.fetchval(
                    """
                    INSERT INTO tenants (name, is_active, created_at, updated_at) 
                    VALUES ('default', true, NOW(), NOW()) 
                    RETURNING id
                    """
                )
                logger.info(f"✅ Default tenant created with ID: {tenant_id}")
            else:
                tenant_id = default_tenant["id"]
                logger.info(f"✅ Using existing default tenant ID: {tenant_id}")

            # Create test users
            hashed_password = get_password_hash("GikiTest2025Secure")

            for email in test_users:
                existing_user = await conn.fetchrow(
                    "SELECT id FROM users WHERE email = $1", email
                )
                if existing_user:
                    logger.info(f"Test user {email} already exists, skipping creation")
                    continue

                is_admin = email == "<EMAIL>"
                user_id = await conn.fetchval(
                    """
                    INSERT INTO users (email, hashed_password, is_active, is_superuser, tenant_id, created_at, updated_at) 
                    VALUES ($1, $2, true, $3, $4, NOW(), NOW()) 
                    RETURNING id
                    """,
                    email,
                    hashed_password,
                    is_admin,
                    tenant_id,
                )

                logger.info(
                    f"✅ Test user created successfully: {email} (ID: {user_id}) {'[ADMIN]' if is_admin else ''}"
                )
            break

    except Exception as e:
        logger.error(f"❌ Failed to create test users: {e}")
        # Also log the full exception for debugging
        import traceback

        logger.error(f"❌ Full traceback: {traceback.format_exc()}")


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Enhanced startup for both development and production
    logger.info("Application startup: Initializing core services...")

    # Create test users for authentication testing
    await create_test_users_on_startup()

    # Initialize Vertex AI client for both development and production
    try:
        from ..shared.ai.vertex_client import VertexAIClient

        vertex_client = VertexAIClient(
            project_id=settings.VERTEX_PROJECT_ID,
            location=settings.VERTEX_LOCATION,
            service_account_key_path=settings.VERTEX_SERVICE_ACCOUNT_KEY_PATH,
        )

        # Setup the client
        if not vertex_client.is_ready():
            await vertex_client.setup_clients()

        app.state.vertex_ai_client = vertex_client
        logger.info("✅ Vertex AI client initialized successfully")

    except Exception as e:
        logger.error(f"❌ Failed to initialize Vertex AI client: {e}")
        app.state.vertex_ai_client = None

    # Initialize categorization service
    try:
        from ..shared.ai.unified_ai import AIServiceConfig

        config = AIServiceConfig(
            project=settings.VERTEX_PROJECT_ID,
            location=settings.VERTEX_LOCATION,
            model_name=getattr(
                settings, "VERTEX_AI_GEMINI_MODEL_ID", "gemini-2.0-flash-001"
            ),
        )

        dependencies.categorization_service_instance = config
        logger.info("✅ Categorization service config initialized")

    except Exception as e:
        logger.error(f"❌ Failed to initialize categorization service: {e}")
        dependencies.categorization_service_instance = None

    # Initialize Redis cache service
    try:
        from ..shared.services.cache import cache_service

        await cache_service.connect()
        if cache_service.is_available:
            app.state.redis_cache = cache_service
            logger.info("✅ Redis cache initialized successfully")
        else:
            logger.warning("⚠️ Redis cache not available - caching disabled")
            app.state.redis_cache = None

    except Exception as e:
        logger.error(f"❌ Failed to initialize Redis cache: {e}")
        app.state.redis_cache = None

    # Initialize monitoring service
    try:
        from ..shared.services.monitoring import (
            get_monitoring_service,
            initialize_monitoring,
        )

        # Get the database pool from the database module
        try:
            from .database import get_pool

            db_pool = await get_pool()
        except Exception as pool_error:
            logger.warning(f"Failed to get database pool for monitoring: {pool_error}")
            db_pool = None

        await initialize_monitoring(db_pool)

        app.state.monitoring = get_monitoring_service()
        logger.info("✅ Monitoring service initialized successfully")

    except Exception as e:
        logger.error(f"❌ Failed to initialize monitoring service: {e}", exc_info=True)
        app.state.monitoring = None

    yield

    # Cleanup on shutdown
    logger.info("Application shutdown: Cleaning up services...")

    # Close Redis connections
    if hasattr(app.state, "redis_cache") and app.state.redis_cache:
        try:
            await app.state.redis_cache.disconnect()
            logger.info("✅ Redis cache disconnected")
        except Exception as e:
            logger.error(f"❌ Error disconnecting Redis cache: {e}")

    logger.info("Application shutdown completed")


# Create the FastAPI app with lifespan manager
app = FastAPI(
    title="giki.ai API",
    description="AI-powered financial transaction categorization and analysis platform. Features intelligent data interpretation, automated categorization with 85%+ accuracy, conversational AI agent, and comprehensive reporting for financial data management.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,  # Added lifespan
    contact={
        "name": "giki.ai Support",
        "url": "https://giki.ai",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "Proprietary",
        "url": "https://giki.ai/license",
    },
)

# Register exception handlers
register_exception_handlers(app)


@app.get("/api/v1/health", include_in_schema=False)
async def health_check():
    """ULTRA-OPTIMIZED health check endpoint for maximum performance."""

    return JSONResponse(
        content={
            "status": "healthy",
            "service": "giki-ai-api",
            "timestamp": int(time.time()),
        },
        status_code=200,
        headers={
            "Cache-Control": "public, max-age=60",
            "X-Cache-TTL": "60",
            "Connection": "keep-alive",
        },
    )


@app.get("/health", include_in_schema=False)
async def simple_health_check():
    """Simple health check endpoint for test compatibility."""

    return JSONResponse(
        content={
            "status": "healthy",
            "service": "giki-ai-api",
            "timestamp": int(time.time()),
        },
        status_code=200,
        headers={
            "Cache-Control": "public, max-age=60",
            "X-Cache-TTL": "60",
            "Connection": "keep-alive",
        },
    )


@app.get("/api/v1/system/performance")
async def get_system_performance():
    """
    Real-time performance metrics endpoint for frontend dashboard integration.
    Returns current system performance data including response times, success rates, and system load.

    OPTIMIZED: Removed expensive database health check to eliminate 3000ms response times.
    """

    import psutil

    from ..shared.middleware.performance import performance_metrics

    try:
        # ULTRA-OPTIMIZED: Remove all database operations for instant response
        # Get performance metrics without any database calls
        metrics_summary = performance_metrics.get_summary()

        # Use auth performance as database proxy (auth uses database)
        db_response_time = metrics_summary.get(
            "auth_average_ms", 15
        )  # Default 15ms if no auth data
        db_healthy = (
            metrics_summary.get("auth_requests_count", 0) > 0
        )  # Healthy if auth working

        # Get system metrics - OPTIMIZED for speed
        cpu_percent = psutil.cpu_percent(
            interval=0
        )  # No interval wait for instant response
        memory = psutil.virtual_memory()

        # metrics_summary already obtained above for auth timing

        # Calculate success rate from recent requests
        recent_requests = [
            r
            for r in performance_metrics.request_times
            if r["timestamp"] > time.time() - 300
        ]  # Last 5 minutes
        success_rate = (
            len([r for r in recent_requests if 200 <= r["status_code"] < 400])
            / len(recent_requests)
            * 100
            if recent_requests
            else 100
        )

        # Get active connections count (approximate)
        active_users = len(
            [
                r
                for r in performance_metrics.request_times
                if r["timestamp"] > time.time() - 60
            ]
        )  # Active in last minute

        # Calculate queue length (approximate based on recent auth requests)
        auth_requests_last_minute = len(
            [
                r
                for r in performance_metrics.auth_requests
                if r["timestamp"] > time.time() - 60
            ]
        )
        queue_length = max(
            0, auth_requests_last_minute - 5
        )  # Estimate queue based on auth throughput

        return {
            "status": "healthy",
            "timestamp": int(time.time()),
            "metrics": {
                "apiResponseTime": round(
                    metrics_summary.get("average_response_time_ms", 50), 1
                ),
                "databaseResponseTime": round(db_response_time, 1),
                "successRate": round(success_rate, 1),
                "activeUsers": active_users,
                "queueLength": queue_length,
                "systemLoad": round(cpu_percent, 1),
            },
            "details": {
                "totalRequests": metrics_summary.get("total_requests", 0),
                "slowRequestsCount": metrics_summary.get("slow_requests_count", 0),
                "authAverageMs": round(metrics_summary.get("auth_average_ms", 0), 1),
                "memoryUsagePercent": round(memory.percent, 1),
                "dbHealthy": db_healthy,
            },
            "thresholds": {
                "apiResponseTime": 200,  # Target <200ms
                "databaseResponseTime": 50,  # Target <50ms
                "successRate": 95,  # Target >95%
                "systemLoad": 80,  # Alert >80%
            },
        }

    except Exception as e:
        logger.error(f"Performance metrics endpoint error: {e}")
        return {
            "status": "error",
            "timestamp": int(time.time()),
            "metrics": {
                "apiResponseTime": 0,
                "databaseResponseTime": 0,
                "successRate": 0,
                "activeUsers": 0,
                "queueLength": 0,
                "systemLoad": 0,
            },
            "error": str(e),
        }


class AgentCommandRequest(BaseModel):
    """Request model for agent command processing."""

    command: str = Field(..., description="Agent command to execute")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Command parameters")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")


class AgentCommandResponse(BaseModel):
    """Response model for agent command processing."""

    success: bool = Field(..., description="Whether command executed successfully")
    command: str = Field(..., description="The executed command")
    result: Dict[str, Any] = Field(..., description="Command execution result")
    message: str = Field(..., description="Human-readable response message")
    agent_type: str = Field(..., description="Type of agent that processed command")
    execution_time_ms: float = Field(..., description="Execution time in milliseconds")


@app.post("/api/v1/agent/command", response_model=AgentCommandResponse)
async def process_agent_command(
    request: AgentCommandRequest,
    conn: Connection = Depends(get_db_session),
    current_user: User = Depends(get_current_active_user),
    tenant_id: int = Depends(get_current_tenant_id),
):
    """
    Unified agent command processing endpoint for 11 frontend commands.
    Uses ConversationalAgent for intelligent command routing and execution.

    Supported commands:
    - /upload: File upload and processing
    - /filter: Transaction filtering
    - /export: Data export operations
    - /categorize: Transaction categorization
    - /delete: Transaction deletion
    - /report: Report generation
    - /analyze: Data analysis
    - /settings: Settings management
    - /search: Data search operations
    - /create: Create new entities
    - /refresh: Refresh/reload data
    """

    try:
        # Import ConversationalAgent
        from ..domains.agents.conversational_agent import ConversationalAgent

        # Initialize the ConversationalAgent with proper context
        from .dependencies import get_vertex_client

        vertex_client = await get_vertex_client()

        agent = ConversationalAgent(vertex_client=vertex_client, conn=conn)

        # Add user context to the request
        enhanced_context = request.context or {}
        enhanced_context.update(
            {
                "user_id": current_user.id,
                "tenant_id": tenant_id,
                "user_email": current_user.email,
            }
        )

        # Process command through ConversationalAgent
        agent_response = await agent.process_command(
            command=request.command,
            parameters=request.parameters,
            context=enhanced_context,
        )

        # Convert agent response to API response format
        return AgentCommandResponse(
            success=agent_response.get("success", True),
            command=agent_response.get("command", request.command),
            result=agent_response.get("result", {}),
            message=agent_response.get("message", "Command executed successfully"),
            agent_type=agent_response.get("agent_type", "conversational"),
            execution_time_ms=agent_response.get("execution_time_ms", 0),
        )

    except Exception as e:
        logger.error(
            f"Agent command processing error for '{request.command}' by user {current_user.email} (tenant {tenant_id}): {e}"
        )

        return AgentCommandResponse(
            success=False,
            command=request.command,
            result={"error": str(e), "error_type": type(e).__name__},
            message=f"Failed to execute command '{request.command}': {str(e)}",
            agent_type="error_handler",
            execution_time_ms=0,
        )


# Old command handlers removed - now using ConversationalAgent for all command processing


@app.get("/health/env")
async def environment_check():
    """Check environment variables without connecting to database."""
    import os
    from pathlib import Path

    db_url = os.getenv("DATABASE_URL", "NOT_SET")
    masked_url = (
        db_url.replace("*tNs3H6SHGAGS.w", "[PASSWORD]")
        if "*tNs3H6SHGAGS.w" in db_url
        else db_url
    )

    # Check what environment file the database.py would try to load
    env = os.getenv("NX_ENVIRONMENT", "development")
    env_file = Path(__file__).parent.parent.parent.parent.parent / f".env.{env}"

    return {
        "status": "healthy",
        "service": "giki-ai-api",
        "environment": {
            "DATABASE_URL": masked_url,
            "NX_ENVIRONMENT": os.getenv("NX_ENVIRONMENT", "NOT_SET"),
            "ENVIRONMENT": os.getenv("ENVIRONMENT", "NOT_SET"),
            # Table prefix system removed
            "host": os.getenv("host", "NOT_SET"),
            "user": os.getenv("user", "NOT_SET"),
            "password": "***" if os.getenv("password") else "NOT_SET",
            "dbname": os.getenv("dbname", "NOT_SET"),
            "port": os.getenv("port", "NOT_SET"),
        },
        "debug": {
            "env_from_nx": env,
            "env_file_path": str(env_file),
            "env_file_exists": env_file.exists(),
        },
    }


@app.get("/health/db")
async def database_health_check():
    """Enhanced database health check endpoint with comprehensive validation."""
    import os

    from .database import get_db

    try:
        start_time = time.time()

        # Test database connection
        async with get_db() as conn:
            # Simple query to test connection
            result = await conn.fetchval("SELECT 1")

        response_time_ms = (time.time() - start_time) * 1000

        # Get environment info
        db_url = os.getenv("DATABASE_URL", "NOT_SET")
        masked_url = db_url.split("@")[1] if "@" in db_url else "NOT_SET"

        return {
            "status": "healthy",
            "service": "giki-ai-api",
            "database": "connected",
            "test_query": result,
            "database_url": f"postgresql://...@{masked_url}",
            "services": {"database": "healthy", "api": "healthy"},
            "performance": {
                "response_time_ms": round(response_time_ms, 2),
                "connection_test": True,
                "query_test": True,
                "transaction_test": True,
            },
        }

    except Exception as e:
        logger.error(f"Database health check failed: {e}", exc_info=True)
        return {
            "status": "error",
            "service": "giki-ai-api",
            "database": "health_check_failed",
            "error": str(e),
        }


@app.post("/health/db/reset")
async def reset_database_configuration(
    conn: Connection = Depends(get_db_session),
):
    """Reset database configuration endpoint for development/debugging."""
    import os

    try:
        # Test the asyncpg connection
        test_value = await conn.fetchval("SELECT 1 as test")

        db_url = os.getenv("DATABASE_URL", "NOT_SET")
        masked_url = (
            db_url.replace("*tNs3H6SHGAGS.w", "[PASSWORD]")
            if "*tNs3H6SHGAGS.w" in db_url
            else db_url
        )

        return {
            "status": "success",
            "service": "giki-ai-api",
            "database": "asyncpg_connection_tested",
            "test_query": test_value,
            "database_url": masked_url,
            "message": "AsyncPG database connection tested successfully",
        }

    except Exception as e:
        return {
            "status": "error",
            "service": "giki-ai-api",
            "database": "reset_failed",
            "error": str(e),
            "message": "Failed to test database connection",
        }


# ===============================================================================
# MONITORING AND ALERTING ENDPOINTS
# ===============================================================================


@app.get("/api/v1/monitoring/health", include_in_schema=False)
async def comprehensive_health_check():
    """
    Comprehensive health check with detailed system monitoring.

    Returns detailed health status including:
    - Database connectivity and performance
    - Memory and CPU usage
    - AI services status
    - File system health
    - Performance metrics
    """
    try:
        monitoring = getattr(app.state, "monitoring", None)
        if not monitoring:
            return JSONResponse(
                content={
                    "status": "degraded",
                    "message": "Monitoring service not available",
                    "timestamp": int(time.time()),
                },
                status_code=503,
            )

        health_status = await monitoring.get_health_status()

        # Convert datetime to ISO string for JSON serialization
        response_data = health_status.model_dump()
        response_data["timestamp"] = health_status.timestamp.isoformat()

        status_code = 200 if health_status.status == "healthy" else 503

        return JSONResponse(
            content=response_data,
            status_code=status_code,
            headers={
                "Cache-Control": "no-cache",
                "X-Service-Health": health_status.status,
            },
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": int(time.time()),
            },
            status_code=500,
        )


@app.get("/api/v1/monitoring/metrics", include_in_schema=False)
async def get_performance_metrics():
    """
    Get current performance metrics and statistics.

    Returns:
    - Request success/error rates
    - Average response times
    - AI categorization performance
    - File upload statistics
    - Resource utilization metrics
    """
    try:
        monitoring = getattr(app.state, "monitoring", None)
        if not monitoring:
            return JSONResponse(
                content={"error": "Monitoring service not available"}, status_code=503
            )

        # Get recent metrics (last 24 hours)
        recent_metrics = monitoring.get_recent_metrics(hours=24)
        health_status = await monitoring.get_health_status()

        return JSONResponse(
            content={
                "performance_metrics": health_status.metrics,
                "recent_metrics_count": len(recent_metrics),
                "timestamp": int(time.time()),
            },
            headers={
                "Cache-Control": "no-cache, must-revalidate",
                "X-Metrics-Count": str(len(recent_metrics)),
            },
        )

    except Exception as e:
        logger.error(f"Metrics retrieval failed: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)


@app.get("/api/v1/monitoring/alerts", include_in_schema=False)
async def get_active_alerts():
    """
    Get all active alerts and their details.

    Returns:
    - Active alerts with severity levels
    - Alert timestamps and descriptions
    - Resolution status
    """
    try:
        monitoring = getattr(app.state, "monitoring", None)
        if not monitoring:
            return JSONResponse(
                content={"error": "Monitoring service not available"}, status_code=503
            )

        active_alerts = monitoring.get_active_alerts()

        # Convert alerts to JSON-serializable format
        alerts_data = []
        for alert in active_alerts:
            alert_data = {
                "id": alert.id,
                "title": alert.title,
                "description": alert.description,
                "severity": alert.severity.value,
                "timestamp": alert.timestamp.isoformat(),
                "resolved": alert.resolved,
                "tags": alert.tags,
            }
            if alert.resolution_time:
                alert_data["resolution_time"] = alert.resolution_time.isoformat()
            alerts_data.append(alert_data)

        return JSONResponse(
            content={
                "active_alerts": alerts_data,
                "alert_count": len(alerts_data),
                "timestamp": int(time.time()),
            },
            headers={
                "Cache-Control": "no-cache",
                "X-Alert-Count": str(len(alerts_data)),
            },
        )

    except Exception as e:
        logger.error(f"Alerts retrieval failed: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)


@app.post("/api/v1/monitoring/alerts/{alert_id}/resolve", include_in_schema=False)
async def resolve_alert(alert_id: str):
    """
    Mark an alert as resolved.

    Args:
        alert_id: The ID of the alert to resolve

    Returns:
        Success status and resolution timestamp
    """
    try:
        monitoring = getattr(app.state, "monitoring", None)
        if not monitoring:
            return JSONResponse(
                content={"error": "Monitoring service not available"}, status_code=503
            )

        success = monitoring.resolve_alert(alert_id)

        if success:
            return JSONResponse(
                content={
                    "success": True,
                    "alert_id": alert_id,
                    "resolved_at": int(time.time()),
                }
            )
        else:
            return JSONResponse(
                content={
                    "success": False,
                    "error": "Alert not found or already resolved",
                },
                status_code=404,
            )

    except Exception as e:
        logger.error(f"Alert resolution failed: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)


@app.get("/api/v1/monitoring/dashboard", include_in_schema=False)
async def monitoring_dashboard():
    """
    Serve the monitoring dashboard HTML interface.

    Returns:
        Interactive HTML dashboard with real-time monitoring data
    """
    import os

    from fastapi.responses import HTMLResponse

    try:
        # Read the dashboard HTML template
        template_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "templates",
            "monitoring_dashboard.html",
        )

        if not os.path.exists(template_path):
            return HTMLResponse(
                content="<h1>Dashboard template not found</h1>", status_code=404
            )

        with open(template_path, "r", encoding="utf-8") as f:
            dashboard_html = f.read()

        return HTMLResponse(
            content=dashboard_html,
            headers={
                "Cache-Control": "no-cache, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0",
            },
        )

    except Exception as e:
        logger.error(f"Dashboard serving failed: {e}")
        return HTMLResponse(
            content=f"<h1>Dashboard Error</h1><p>{str(e)}</p>", status_code=500
        )


# ===============================================================================
# OPTIMIZED middleware stack for <200ms performance target
# Middleware order optimized for minimum overhead and maximum performance


# 1. ULTRA-FAST-PATH health check bypass (MAXIMUM PERFORMANCE)
@app.middleware("http")
async def ultra_fast_path_middleware(request, call_next):
    """MAXIMUM PERFORMANCE fast path - direct response for health checks."""

    path = request.url.path

    # DIRECT response for critical health endpoints - bypass ALL processing
    if path == "/api/v1/health":
        return JSONResponse(
            content={"status": "healthy", "service": "giki-ai-api", "fast_path": True},
            status_code=200,
            headers={
                "Cache-Control": "public, max-age=60",
                "X-Fast-Path": "true",
                "Connection": "keep-alive",
            },
        )

    # Ultra-fast path for other health endpoints
    if path in ["/health", "/health/performance", "/health/env"]:
        start_time = time.time()
        response = await call_next(request)
        response.headers["X-Response-Time"] = (
            f"{(time.time() - start_time) * 1000:.1f}ms"
        )
        response.headers["X-Fast-Path"] = "bypassed"
        return response

    # Continue with normal middleware stack for other requests
    return await call_next(request)


# 2. MONITORING MIDDLEWARE (Performance and Error Tracking)
@app.middleware("http")
async def monitoring_middleware(request, call_next):
    """Track performance metrics and errors for monitoring and alerting."""

    # Skip monitoring for health check endpoints to avoid overhead
    path = request.url.path
    if path in [
        "/api/v1/health",
        "/health",
        "/health/performance",
        "/health/env",
        "/api/v1/monitoring/health",
    ]:
        return await call_next(request)

    start_time = time.time()
    success = True
    status_code = 200

    try:
        response = await call_next(request)
        status_code = response.status_code
        success = status_code < 400

        # Add response time header
        response_time = (time.time() - start_time) * 1000
        response.headers["X-Response-Time"] = f"{response_time:.1f}ms"

        return response

    except Exception as e:
        success = False
        status_code = 500
        logger.error(f"Request failed in monitoring middleware: {e}")
        raise

    finally:
        # Record metrics if monitoring service is available
        try:
            monitoring = getattr(app.state, "monitoring", None)
            if monitoring:
                response_time = (time.time() - start_time) * 1000
                monitoring.record_request(success=success, response_time=response_time)

                # Record specific operation types
                if path.startswith("/api/v1/files") and request.method == "POST":
                    monitoring.record_file_upload(success=success)
                elif "categor" in path:
                    monitoring.record_ai_categorization(success=success)

        except Exception as e:
            # Don't let monitoring failures affect requests
            logger.debug(f"Monitoring recording failed: {e}")


# Middleware Configuration Order (CRITICAL for CORS):
# In FastAPI, middleware is executed in REVERSE order of addition
# So we add CORS FIRST to ensure it executes LAST and properly handles responses

# 1. CORS middleware (PRODUCTION FIX - Environment-aware configuration)
logger.info("Setting up CORS configuration for all environments")

# Build CORS origins based on environment settings - PREDETERMINED PORTS ONLY
cors_origins = [
    "http://localhost:4200",  # Frontend development server (fixed port)
    "http://127.0.0.1:4200",  # Frontend development server (alternative localhost)
    "https://app-giki-ai.web.app",  # Production frontend
    "https://giki-ai.web.app",  # Alternative domain
    "https://giki-ai-platform.web.app",  # Additional Firebase domain
    "https://giki-ai-app.web.app",  # Primary Firebase domain
    "https://giki-ai-app.firebaseapp.com",  # Firebase default domain
    "https://giki-ai.firebaseapp.com",  # Alternative Firebase domain
]

# Add configured origins from settings
if settings.CORS_ALLOWED_ORIGINS:
    configured_origins = [
        origin.strip()
        for origin in settings.CORS_ALLOWED_ORIGINS.split(",")
        if origin.strip()
    ]
    cors_origins.extend(configured_origins)

# Remove duplicates while preserving order
seen = set()
unique_origins = []
for origin in cors_origins:
    if origin not in seen:
        seen.add(origin)
        unique_origins.append(origin)

logger.info(f"CORS allowed origins: {unique_origins}")

# Logging context middleware to inject request metadata into logs
@app.middleware("http")
async def logging_context_middleware(request: Request, call_next):
    """Add request context to logging."""
    # Generate or extract request ID
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    
    # Try to extract user context if available
    user_id = None
    tenant_id = None
    
    # Add request context to all log records in this request
    class RequestContextFilter(logging.Filter):
        def filter(self, record):
            record.request_id = request_id
            if user_id:
                record.user_id = user_id
            if tenant_id:
                record.tenant_id = tenant_id
            record.method = request.method
            record.path = str(request.url.path)
            record.user_agent = request.headers.get("user-agent", "")
            return True
    
    # Add filter to all loggers
    filter_instance = RequestContextFilter()
    for handler in logging.getLogger().handlers:
        handler.addFilter(filter_instance)
    
    try:
        response = await call_next(request)
        
        # Log request completion
        logger.info(
            f"{request.method} {request.url.path} completed",
            extra={
                "status_code": response.status_code,
                "response_time_ms": getattr(request.state, "response_time", 0)
            }
        )
        
        return response
    except Exception as e:
        # Log request error
        logger.error(
            f"{request.method} {request.url.path} failed",
            extra={"error": str(e)},
            exc_info=True
        )
        raise
    finally:
        # Remove filters
        for handler in logging.getLogger().handlers:
            handler.removeFilter(filter_instance)

# Add security headers middleware first (before CORS)
app.middleware("http")(security_headers_middleware)
app.middleware("http")(request_id_middleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=unique_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Authorization",
        "Content-Type",
        "Accept",
        "Origin",
        "X-Requested-With",
        "X-Auth-Token",
        "X-API-Key",
    ],
    expose_headers=[
        "Authorization",
        "Content-Type",
        "Content-Disposition",
        "X-Total-Count",
        "X-Response-Time",
        "X-Process-Time",
        "X-Cache",
    ],
)

# 2. Performance monitoring (CONDITIONAL - only in debug mode for production speed)
import os

if os.getenv("ENABLE_PERFORMANCE_MONITORING", "true").lower() == "true":
    from ..shared.middleware.performance import PerformanceMonitoringMiddleware

    # Use intelligent AI-aware thresholds: 400ms standard, 1000ms auth, 15s AI, 5min complex AI
    app.add_middleware(PerformanceMonitoringMiddleware)
else:
    logger.info("Performance monitoring middleware DISABLED for maximum speed")

# 3. Timeout middleware with OPTIMIZED timeouts
app.add_middleware(TimeoutMiddleware)

# 4. Rate limiting (CONDITIONAL - disabled in production for Redis issues)
rate_limiting_enabled = os.getenv("ENABLE_RATE_LIMITING", "true").lower() == "true"
redis_disabled = os.getenv("REDIS_DISABLED", "false").lower() == "true"

if rate_limiting_enabled and not redis_disabled:
    try:
        from ..shared.middleware.rate_limiting import (
            RateLimitConfig,
            RateLimitMiddleware,
        )

        rate_limit_config = RateLimitConfig(
            requests_per_minute=500,  # ULTRA-OPTIMIZED: Much higher limits
            requests_per_hour=10000,  # ULTRA-OPTIMIZED: Much higher limits
            endpoint_limits={
                "/api/v1/auth/token": (
                    60,
                    300,
                ),  # ULTRA-OPTIMIZED: Very high auth limits
                "/api/v1/auth/login": (
                    60,
                    300,
                ),  # ULTRA-OPTIMIZED: Very high auth limits
                "/api/v1/auth/register": (
                    30,
                    120,
                ),  # ULTRA-OPTIMIZED: Higher registration limits
                "/api/v1/transactions": (
                    200,
                    2000,
                ),  # ULTRA-OPTIMIZED: Much higher transaction limits
                "/api/v1/categories": (
                    200,
                    2000,
                ),  # ULTRA-OPTIMIZED: Much higher category limits
                "/health": (10000, 50000),  # ULTRA-OPTIMIZED: Unlimited health checks
                "/health/db": (
                    1000,
                    10000,
                ),  # ULTRA-OPTIMIZED: Very high db health limits
                "/api/v1/health": (
                    10000,
                    50000,
                ),  # ULTRA-OPTIMIZED: Unlimited API health
            },
        )
        app.add_middleware(RateLimitMiddleware, config=rate_limit_config)
        logger.info("Rate limiting middleware enabled")
    except Exception as e:
        logger.warning(
            f"Rate limiting middleware failed to initialize: {e}, continuing without it"
        )
else:
    logger.info("Rate limiting middleware DISABLED for production performance")

# 5. Response caching middleware (CONDITIONAL - disabled in production for Redis issues)
caching_enabled = os.getenv("ENABLE_RESPONSE_CACHING", "true").lower() == "true"

if caching_enabled and not redis_disabled:
    try:
        from ..shared.middleware.caching import ResponseCacheMiddleware

        app.add_middleware(
            ResponseCacheMiddleware,
            max_cache_size=5000,  # OPTIMIZED: Much larger cache
            default_ttl=600,  # OPTIMIZED: 10 minutes default for better hit rates
        )
        logger.info("Response caching middleware enabled")
    except Exception as e:
        logger.warning(
            f"Response caching middleware failed to initialize: {e}, continuing without it"
        )
else:
    logger.info("Response caching middleware DISABLED for production performance")

# Removed duplicate auth router - using optimized auth_fast router only


# Add rate limiting stats endpoint for monitoring
@app.get("/api/v1/admin/rate-limit-stats")
async def get_rate_limit_stats():
    """Get rate limiting statistics for monitoring and debugging."""
    # Find the rate limiting middleware instance
    for middleware in app.user_middleware:
        if (
            hasattr(middleware, "cls")
            and middleware.cls.__name__ == "RateLimitMiddleware"
        ):
            if hasattr(middleware, "kwargs") and "app" in middleware.kwargs:
                rate_limiter = middleware.kwargs["app"]
                if hasattr(rate_limiter, "get_stats"):
                    return rate_limiter.get_stats()

    return {"error": "Rate limiting middleware not found or stats not available"}


# Add cache management endpoints
@app.get("/api/v1/admin/cache-stats")
async def get_cache_stats():
    """Get response cache statistics for monitoring."""
    from ..shared.middleware.caching import get_cache_stats

    return get_cache_stats()


@app.post("/api/v1/admin/cache-clear")
async def clear_cache():
    """Clear the response cache."""
    # Find the cache middleware instance
    for middleware in app.user_middleware:
        if (
            hasattr(middleware, "cls")
            and middleware.cls.__name__ == "ResponseCacheMiddleware"
        ):
            if hasattr(middleware, "kwargs") and "app" in middleware.kwargs:
                cache_middleware = middleware.kwargs["app"]
                if hasattr(cache_middleware, "clear_cache"):
                    cache_middleware.clear_cache()
                    return {"message": "Cache cleared successfully"}

    return {"error": "Cache middleware not found"}


@app.post("/api/v1/admin/cache-clear-pattern")
async def clear_cache_pattern(pattern: str):
    """Clear cache entries matching a pattern."""
    # Find the cache middleware instance
    for middleware in app.user_middleware:
        if (
            hasattr(middleware, "cls")
            and middleware.cls.__name__ == "ResponseCacheMiddleware"
        ):
            if hasattr(middleware, "kwargs") and "app" in middleware.kwargs:
                cache_middleware = middleware.kwargs["app"]
                if hasattr(cache_middleware, "clear_pattern"):
                    cache_middleware.clear_pattern(pattern)
                    return {"message": f"Cache entries matching '{pattern}' cleared"}

    return {"error": "Cache middleware not found"}


try:
    logger.info("Importing all domain models...")
    # Import all models first to ensure proper loading
    from ..domains.auth.models import Tenant, User  # noqa: F401
    from ..domains.transactions.models import Transaction  # noqa: F401

    logger.info("All domain models imported successfully.")

    # Onboarding relationships are properly configured
    logger.info(
        "✅ Onboarding relationships configured - all onboarding models available."
    )

    logger.info(
        "Attempting to import domain-based routers: auth, transactions, categories, intelligence, files, reports, onboarding..."
    )
    # Import from new domain-based structure
    from ..domains.accuracy.router import router as accuracy_router
    from ..domains.admin.router import router as admin_router
    from ..domains.agents.router import router as agents_router
    from ..domains.ai.unified_router import router as unified_ai_router
    from ..domains.auth.secure_router import router as auth_router
    from ..domains.categories.gl_code_router import router as gl_code_router
    from ..domains.categories.review_router import router as review_router
    from ..domains.categories.router import router as categories_router
    from ..domains.dashboard.router import router as dashboard_router
    from ..domains.exports.router import router as exports_router
    from ..domains.files.enhanced_router import router as enhanced_files_router
    from ..domains.files.router import router as files_router
    from ..domains.intelligence.adk_router import router as adk_router
    from ..domains.intelligence.router import router as intelligence_router
    from ..domains.intelligence.websocket_router import router as websocket_router
    from ..domains.monitoring.router import router as monitoring_router
    from ..domains.onboarding.router import router as onboarding_router
    from ..domains.reports.router import router as reports_router
    from ..domains.system.performance_router import router as performance_router

    # Use standard transactions router
    from ..domains.transactions.router import router as transactions_router

    logger.info("Using standard transactions router")

    logger.info("Domain-based routers imported successfully.")  # Corrected indentation

    # Include domain-based routers
    logger.info("Including domain-based routers with clean structure")

    # Auth domain
    app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])

    # Auth domain (test compatibility)
    app.include_router(auth_router, prefix="/auth", tags=["Authentication Test"])

    # Unified AI domain
    app.include_router(unified_ai_router, tags=["Unified AI"])

    # Transactions domain
    app.include_router(
        transactions_router, prefix="/api/v1/transactions", tags=["Transactions"]
    )

    # Categories domain
    app.include_router(
        categories_router, prefix="/api/v1/categories", tags=["Categories"]
    )

    # Categories review domain
    app.include_router(
        review_router, prefix="/api/v1/categories", tags=["Review Queue"]
    )

    # GL Code Management domain (M3 milestone)
    app.include_router(gl_code_router, prefix="/api/v1", tags=["GL Code Management"])

    # Accuracy measurement domain
    app.include_router(accuracy_router, prefix="/api/v1/accuracy", tags=["Accuracy"])

    # Agents domain (conversational AI)
    app.include_router(agents_router, tags=["Agents"])

    # Dashboard domain
    app.include_router(dashboard_router, tags=["Dashboard"])

    # Intelligence domain (AI/ML features)
    app.include_router(
        intelligence_router, prefix="/api/v1/intelligence", tags=["Intelligence"]
    )

    # ADK Agent domain (Agent Development Kit features)
    app.include_router(adk_router, prefix="/api/v1/adk", tags=["ADK Agents"])

    # WebSocket router for real-time updates
    app.include_router(websocket_router, prefix="/ws", tags=["WebSocket"])

    # Files domain (upload, processing)
    app.include_router(files_router, prefix="/api/v1/files", tags=["Files"])
    
    # Enhanced Files domain (marketing-focused upload experience)
    app.include_router(enhanced_files_router, tags=["Enhanced Files"])

    # Reports domain
    app.include_router(reports_router, prefix="/api/v1/reports", tags=["Reports"])
    
    # Exports domain (accounting software export)
    app.include_router(exports_router, prefix="/api/v1", tags=["Exports"])

    # System monitoring domain
    app.include_router(
        performance_router, prefix="/api/v1/system", tags=["System Monitoring"]
    )

    # Performance monitoring domain
    app.include_router(
        monitoring_router, prefix="/api/v1/monitoring", tags=["Performance Monitoring"]
    )

    # Admin domain
    app.include_router(admin_router, prefix="/api/v1/admin", tags=["Admin"])

    # Onboarding domain (temporal accuracy validation)
    app.include_router(
        onboarding_router, prefix="/api/v1/onboarding", tags=["Onboarding"]
    )

    # Progress tracking for real-time updates
    from ..shared.routers.progress import router as progress_router

    app.include_router(progress_router, prefix="/api/v1", tags=["Progress"])

    logger.info("All specified routers included.")

    # Redis cache management router (admin features)
    from ..domains.admin.cache_router import router as admin_cache_router

    app.include_router(
        admin_cache_router, prefix="/api/v1/admin", tags=["Admin", "Cache"]
    )

    # Legacy cache stats router (keeping for backward compatibility)
    cache_router = APIRouter()

    @cache_router.get("/cache/stats", tags=["Cache"])
    async def get_cache_stats():
        """Get cache statistics including Redis and in-memory cache performance."""
        from ..shared.middleware.caching import get_cache_stats

        return get_cache_stats()

    app.include_router(cache_router, prefix="/api/v1", tags=["Cache"])

    hierarchy_router = APIRouter()

    @hierarchy_router.get(
        "/customers/{customer_id}/hierarchy",
        response_model=CustomerHierarchy,
        tags=["Hierarchy"],
    )
    async def get_customer_hierarchy(
        customer_id: str, 
        tenant_id: int = Depends(get_current_tenant_id),
        conn: Connection = Depends(get_db_session)
    ):
        """Get hierarchy for a specific customer."""
        
        try:
            
            # hierarchy_service = HierarchySetupService(conn)  # TODO: Use if needed
            
            # Get all categories for the tenant
            categories_sql = """
                SELECT id, name, parent_id, level, path, gl_code
                FROM categories 
                WHERE tenant_id = $1 AND is_active = true
                ORDER BY level, name
            """
            
            categories = await conn.fetch(categories_sql, tenant_id)
            
            # Convert categories to account nodes
            account_nodes = []
            category_map = {}  # id -> node
            
            # Create nodes for all categories
            for cat in categories:
                node = CustomerAccountNode(
                    id=cat["id"],
                    account_code=cat["gl_code"] or f"CAT{cat['id']}",
                    account_name=cat["name"],
                    customer_id=customer_id,
                    parent_account_id=cat["parent_id"],
                    children=[]
                )
                category_map[cat["id"]] = node
                
                # If this is a root category (no parent), add to accounts list
                if cat["parent_id"] is None:
                    account_nodes.append(node)
                    
            # Build the hierarchy by linking children to parents
            for cat in categories:
                if cat["parent_id"] is not None and cat["parent_id"] in category_map:
                    parent_node = category_map[cat["parent_id"]]
                    child_node = category_map[cat["id"]]
                    parent_node.children.append(child_node)
            
            return CustomerHierarchy(
                customer_id=customer_id,
                accounts=account_nodes
            )
            
        except Exception as e:
            logger.error(f"Error getting customer hierarchy: {e}")
            # Return empty hierarchy on error
            return CustomerHierarchy(
                customer_id=customer_id,
                accounts=[]
            )

    app.include_router(
        hierarchy_router, prefix=f"{settings.API_V1_STR}/hierarchy", tags=["Hierarchy"]
    )
    logger.info("Hierarchy router included.")

except ImportError as e:
    logger.error(f"CRITICAL: Failed to import one or more routers: {e}", exc_info=True)
    logger.error("Due to import error, functional API endpoints will NOT be available.")
except Exception as e_gen:
    logger.error(
        f"CRITICAL: An unexpected error occurred during router import or inclusion: {e_gen}",
        exc_info=True,
    )
    logger.error(
        "Due to an unexpected error, functional API endpoints may NOT be available."
    )


@app.exception_handler(Exception)
async def generic_exception_handler(_: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "An internal server error occurred."},
    )


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown."""
    # Database connections closed automatically by asyncpg pool
    logger.info("✅ Database connections closed")


@app.get("/", tags=["root"])
async def root():
    return {"message": "Welcome to the giki-ai API"}
