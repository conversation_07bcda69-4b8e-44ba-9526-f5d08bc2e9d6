"""
Database connection pre-warming to reduce first-request latency.
"""

import asyncio
import logging
import time
from typing import Optional

import asyncpg

# Database URL function will be imported when needed

logger = logging.getLogger(__name__)


class DatabaseWarmup:
    """
    Pre-warm database connections to reduce cold start latency.
    Uses asyncpg connection pooling for optimal performance.
    """

    def __init__(self, pool_size: int = 5):
        self.pool_size = pool_size
        self.warmed_up = False
        self.warmup_time = 0.0
        self.connection_pool: Optional[asyncpg.Pool] = None
        self.health_metrics = {
            "total_connections": 0,
            "successful_connections": 0,
            "failed_connections": 0,
            "average_response_time": 0.0,
        }

    async def warmup_connections(self) -> bool:
        """
        Pre-warm database connections by creating a connection pool and testing them.

        Returns:
            True if warmup successful, False otherwise
        """
        start_time = time.time()
        logger.info(f"🔥 Database warmup executing with pool size: {self.pool_size}")

        try:
            # Get database URL
            from .database import get_database_url

            database_url = get_database_url()

            # Create connection pool
            self.connection_pool = await asyncpg.create_pool(
                database_url,
                min_size=1,
                max_size=self.pool_size,
                server_settings={
                    "application_name": "giki_ai_warmup",
                    "statement_timeout": "30s",
                },
            )

            # Test all connections in pool
            connection_tests = []
            for i in range(self.pool_size):
                connection_tests.append(self._test_single_connection(i + 1))

            # Execute all connection tests concurrently
            test_results = await asyncio.gather(
                *connection_tests, return_exceptions=True
            )

            # Analyze results
            successful_tests = 0
            failed_tests = 0
            response_times = []

            for result in test_results:
                if isinstance(result, Exception):
                    failed_tests += 1
                    logger.warning(f"Connection test failed: {result}")
                else:
                    successful_tests += 1
                    response_times.append(result)

            # Update metrics
            self.health_metrics.update(
                {
                    "total_connections": len(test_results),
                    "successful_connections": successful_tests,
                    "failed_connections": failed_tests,
                    "average_response_time": sum(response_times) / len(response_times)
                    if response_times
                    else 0.0,
                }
            )

            # Determine if warmup was successful
            success_rate = successful_tests / len(test_results) if test_results else 0
            self.warmed_up = success_rate >= 0.8  # At least 80% success rate

            self.warmup_time = time.time() - start_time

            if self.warmed_up:
                logger.info(
                    f"✅ Database warmup completed successfully in {self.warmup_time:.3f}s"
                )
                logger.info(
                    f"   📊 {successful_tests}/{len(test_results)} connections successful"
                )
                logger.info(
                    f"   ⚡ Average response time: {self.health_metrics['average_response_time']:.2f}ms"
                )
            else:
                logger.warning(
                    f"⚠️ Database warmup completed with issues in {self.warmup_time:.3f}s"
                )
                logger.warning(
                    f"   📊 Only {successful_tests}/{len(test_results)} connections successful"
                )

            return self.warmed_up

        except Exception as e:
            self.warmup_time = time.time() - start_time
            logger.error(f"❌ Database warmup failed: {e}")
            self.warmed_up = False
            return False

    async def _test_single_connection(self, connection_num: int) -> float:
        """Test a single database connection and return response time in ms."""
        if not self.connection_pool:
            raise Exception("Connection pool not initialized")

        conn_start = time.time()

        async with self.connection_pool.acquire() as conn:
            # Test basic query
            await conn.fetchval("SELECT 1")

            # Test transaction table exists (important for our app)
            await conn.fetchval("SELECT COUNT(*) FROM transactions LIMIT 1")

            # Test categories table exists
            await conn.fetchval("SELECT COUNT(*) FROM categories LIMIT 1")

            # Test users table exists
            await conn.fetchval("SELECT COUNT(*) FROM users LIMIT 1")

        response_time_ms = (time.time() - conn_start) * 1000
        logger.debug(f"Connection {connection_num} test: {response_time_ms:.2f}ms")
        return response_time_ms

    async def health_check(self) -> dict:
        """
        Perform a comprehensive health check on the database connection.

        Returns:
            Dictionary with health check results
        """
        start_time = time.time()

        health_result = {
            "status": "unknown",
            "response_time_ms": 0.0,
            "warmed_up": self.warmed_up,
            "warmup_time_s": round(self.warmup_time, 3),
            "pool_status": {},
            "connectivity": {},
        }

        try:
            if not self.connection_pool:
                health_result.update(
                    {
                        "status": "no_pool",
                        "response_time_ms": 0.0,
                        "error": "Connection pool not initialized",
                    }
                )
                return health_result

            # Test pool status
            pool_size = self.connection_pool.get_size()
            pool_idle = self.connection_pool.get_idle_size()

            health_result["pool_status"] = {
                "size": pool_size,
                "idle": pool_idle,
                "active": pool_size - pool_idle,
                "max_size": self.pool_size,
            }

            # Test basic connectivity
            async with self.connection_pool.acquire() as conn:
                # Test basic query
                test_start = time.time()
                result = await conn.fetchval("SELECT 1")
                basic_response_time = (time.time() - test_start) * 1000

                # Test application tables
                table_tests = {}
                for table in ["transactions", "categories", "users"]:
                    table_start = time.time()
                    try:
                        count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                        table_time = (time.time() - table_start) * 1000
                        table_tests[table] = {
                            "exists": True,
                            "response_time_ms": round(table_time, 2),
                            "row_count": count,
                        }
                    except Exception as e:
                        table_time = (time.time() - table_start) * 1000
                        table_tests[table] = {
                            "exists": False,
                            "response_time_ms": round(table_time, 2),
                            "error": str(e),
                        }

                health_result["connectivity"] = {
                    "basic_query": {
                        "success": result == 1,
                        "response_time_ms": round(basic_response_time, 2),
                    },
                    "table_tests": table_tests,
                }

            # Calculate overall response time
            total_response_time = (time.time() - start_time) * 1000
            health_result["response_time_ms"] = round(total_response_time, 2)

            # Determine overall status
            all_tables_ok = all(
                test.get("exists", False) for test in table_tests.values()
            )

            if all_tables_ok and total_response_time < 1000:  # Under 1 second
                health_result["status"] = "healthy"
            elif all_tables_ok:
                health_result["status"] = "slow"
            else:
                health_result["status"] = "degraded"

            # Include warmup metrics
            health_result["warmup_metrics"] = self.health_metrics

        except Exception as e:
            total_response_time = (time.time() - start_time) * 1000
            health_result.update(
                {
                    "status": "error",
                    "response_time_ms": round(total_response_time, 2),
                    "error": str(e),
                    "error_type": type(e).__name__,
                }
            )
            logger.error(f"Health check failed: {e}")

        return health_result

    async def close(self):
        """Close the connection pool."""
        if self.connection_pool:
            await self.connection_pool.close()
            self.connection_pool = None
            logger.info("🔌 Database warmup connection pool closed")


# Global warmup instance with OPTIMIZED pool size for connection pre-warming
db_warmup = DatabaseWarmup(pool_size=3)  # OPTIMIZED: 3 connections to warm the pool


async def startup_database_warmup():
    """
    Re-enabled database warmup after fixing prepared statement conflicts with URL parameters.
    Should now work properly with prepared_statement_cache_size=0&statement_cache_size=0.
    """
    logger.info(
        "🚀 Initiating database warmup for improved authentication performance..."
    )
    logger.info("🔥 Starting database connection warmup (pool_size: 5)...")

    try:
        # Create warmup instance with better pool size configuration
        warmup = DatabaseWarmup(pool_size=5)
        success = await warmup.warmup_connections()

        if success:
            logger.info("✅ Database warmup completed successfully")
            return True
        else:
            logger.warning("⚠️ Database warmup completed with some failed connections")
            return False

    except Exception as e:
        logger.error(f"❌ Database warmup failed: {e}")
        # Don't fail startup if warmup fails
        return False


async def get_warmup_status():
    """
    Get current warmup status and perform health check.

    Returns:
        Dictionary with warmup and health status
    """
    health = await db_warmup.health_check()

    return {
        "warmup_completed": db_warmup.warmed_up,
        "warmup_time_seconds": db_warmup.warmup_time,
        "pool_size": db_warmup.pool_size,
        "health_check": health,
    }
