"""
Secure configuration system for giki.ai API - CRITICAL SECURITY IMPLEMENTATION

This module replaces the existing config.py with enhanced security:
- NO hardcoded credentials anywhere
- Mandatory Secret Manager for production
- Secure fallbacks for development only
- Comprehensive validation and monitoring
"""

import hashlib
import logging
import os
import secrets
import string
from typing import Any, Dict, Optional
from urllib.parse import urlparse

from pydantic import ConfigDict, ValidationInfo, field_validator
from pydantic_settings import BaseSettings

# Enhanced logging for security events
logging.basicConfig(level=logging.INFO)
security_logger = logging.getLogger("giki.security")

# Google Cloud Secret Manager with error handling
try:
    from ..shared.services.google_cloud_secrets import get_secrets_manager

    SECRETS_MANAGER_AVAILABLE = True
except ImportError:
    SECRETS_MANAGER_AVAILABLE = False
    get_secrets_manager = None


def _get_env_file_path() -> Optional[str]:
    """Get the appropriate environment file path from infrastructure directory."""
    environment = os.getenv("ENVIRONMENT", "development").lower()
    
    # Get the absolute path to the workspace root
    # The API is run from workspace root, so we can use relative paths
    workspace_root = os.getcwd()
    if not workspace_root.endswith("giki-ai-workspace"):
        # If not in workspace root, find it
        current_dir = os.path.abspath(os.path.dirname(__file__))
        while current_dir != "/" and not current_dir.endswith("giki-ai-workspace"):
            current_dir = os.path.dirname(current_dir)
        if current_dir.endswith("giki-ai-workspace"):
            workspace_root = current_dir
    
    # Look for infrastructure environment files
    infrastructure_path = os.path.join(workspace_root, "infrastructure", "environments", environment, f".env.{environment}")
    
    if os.path.exists(infrastructure_path):
        return infrastructure_path
    
    # Log warning if not found
    security_logger.warning(f"⚠️ Environment file not found: {infrastructure_path}")
    return None


class SecureSettings(BaseSettings):
    """
    ULTRA-SECURE application configuration with ZERO hardcoded credentials.

    SECURITY PRINCIPLES:
    1. NO credentials in code, environment files, or defaults
    2. ALL production secrets MUST come from Google Secret Manager
    3. Development uses secure auto-generation with warnings
    4. Comprehensive validation prevents security misconfigurations
    5. Audit logging for all security-related operations
    """

    model_config = ConfigDict(
        # Load environment-specific .env files from infrastructure directory
        env_file=_get_env_file_path(),
        env_file_encoding="utf-8",
        extra="ignore",
        validate_assignment=True,
    )

    # Core Application Settings
    APP_NAME: str = "giki-ai API"
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"
    MAX_UPLOAD_FILE_SIZE_MB: int = 10

    # Environment Detection (Critical for security decisions)
    ENVIRONMENT: str = "production"  # Default to production-safe

    # CRITICAL: Database configuration - NO DEFAULTS WITH CREDENTIALS
    DATABASE_URL: str = ""  # Will be securely populated during initialization

    # CRITICAL: Authentication secrets - NO DEFAULTS
    AUTH_SECRET_KEY: str = ""
    SECRET_KEY: str = ""
    ALGORITHM: str = "RS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15  # Reduced for security
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # CORS Configuration - Production-safe defaults
    CORS_ALLOWED_ORIGINS: str = ""  # Will be set based on environment
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: str = "GET,POST,PUT,DELETE,OPTIONS"
    CORS_ALLOW_HEADERS: str = "Authorization,Content-Type"

    # Admin Configuration - NO DEFAULT API KEY
    ADMIN_API_KEY: str = ""

    # File Storage Configuration
    UPLOAD_STORAGE_PATH: str = "uploads"

    # Google Cloud Configuration - NO SERVICE ACCOUNT PATHS IN DEFAULTS
    VERTEX_PROJECT_ID: str = ""  # Must be set explicitly
    VERTEX_LOCATION: str = "us-central1"
    GOOGLE_APPLICATION_CREDENTIALS: str = ""

    # Performance Configuration
    DB_POOL_SIZE: int = 10
    DB_MAX_OVERFLOW: int = 20
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600

    # Security Monitoring
    SECURITY_AUDIT_ENABLED: bool = True
    FAILED_AUTH_THRESHOLD: int = 5
    AUTH_RATE_LIMIT_PER_MINUTE: int = 10

    def __init__(self, **kwargs):
        """Initialize with comprehensive security checks and auto-configuration."""
        super().__init__(**kwargs)

        # Detect environment
        self.ENVIRONMENT = os.getenv("ENVIRONMENT", "production").lower()
        self.DEBUG = self.ENVIRONMENT == "development"

        # Log security initialization
        security_logger.info(
            f"🔒 Security initialization: Environment={self.ENVIRONMENT}, "
            f"Debug={self.DEBUG}, SecretManager={SECRETS_MANAGER_AVAILABLE}"
        )

        # Initialize all security-critical configurations
        self._initialize_database_config()
        self._initialize_auth_secrets()
        self._initialize_cors_config()
        self._initialize_gcp_config()

        # Final security validation
        self._validate_security_configuration()

    def _initialize_database_config(self) -> None:
        """Initialize database configuration with ZERO hardcoded credentials."""
        if not self.DATABASE_URL:
            if self.ENVIRONMENT in ["development", "test"]:
                # Development/Test: Check for secure environment variable first
                env_db_url = os.getenv("DATABASE_URL")
                if env_db_url and "localhost" in env_db_url:
                    self.DATABASE_URL = env_db_url
                    security_logger.warning(
                        f"🚨 {self.ENVIRONMENT.title()} database using local credentials from environment"
                    )
                else:
                    # Fail securely - no default credentials
                    raise ValueError(
                        "🚨 SECURITY: DATABASE_URL must be set via environment variable. "
                        "NO hardcoded database credentials allowed."
                    )
            else:
                # Production: MUST use Secret Manager
                self.DATABASE_URL = self._get_secret_or_fail("database-url")
                security_logger.info("✅ Database URL loaded from Secret Manager")

    def _initialize_auth_secrets(self) -> None:
        """Initialize authentication secrets with secure generation/retrieval."""
        # AUTH_SECRET_KEY
        if not self.AUTH_SECRET_KEY:
            env_auth_key = os.getenv("AUTH_SECRET_KEY")
            if env_auth_key:
                self.AUTH_SECRET_KEY = env_auth_key
                security_logger.info("✅ AUTH_SECRET_KEY loaded from environment")
            elif self.ENVIRONMENT in ["development", "test"]:
                self.AUTH_SECRET_KEY = self._generate_secure_key("AUTH_SECRET_KEY")
            else:
                self.AUTH_SECRET_KEY = self._get_secret_or_fail("auth-secret-key")

        # SECRET_KEY (compatibility)
        if not self.SECRET_KEY:
            env_secret_key = os.getenv("SECRET_KEY")
            if env_secret_key:
                self.SECRET_KEY = env_secret_key
                security_logger.info("✅ SECRET_KEY loaded from environment")
            elif self.ENVIRONMENT in ["development", "test"]:
                self.SECRET_KEY = self._generate_secure_key("SECRET_KEY")
            else:
                self.SECRET_KEY = self._get_secret_or_fail("secret-key")

    def _initialize_cors_config(self) -> None:
        """Initialize CORS configuration based on environment."""
        if not self.CORS_ALLOWED_ORIGINS:
            if self.ENVIRONMENT in ["development", "test"]:
                self.CORS_ALLOWED_ORIGINS = (
                    "http://localhost:4200,http://127.0.0.1:4200"
                )
                security_logger.info(f"🔧 {self.ENVIRONMENT.title()} CORS: localhost origins")
            else:
                # Production: Get from Secret Manager or use secure default
                try:
                    cors_origins = self._get_secret("cors-origins")
                    if cors_origins:
                        self.CORS_ALLOWED_ORIGINS = cors_origins
                    else:
                        self.CORS_ALLOWED_ORIGINS = "https://app-giki-ai.web.app"
                        security_logger.warning(
                            "📡 Using default production CORS origin"
                        )
                except Exception as e:
                    security_logger.warning(
                        f"📡 Failed to get CORS origins from Secret Manager: {e}"
                    )
                    self.CORS_ALLOWED_ORIGINS = "https://app-giki-ai.web.app"

    def _initialize_gcp_config(self) -> None:
        """Initialize Google Cloud configuration securely."""
        if not self.VERTEX_PROJECT_ID:
            project_id = os.getenv("VERTEX_PROJECT_ID")
            if project_id:
                self.VERTEX_PROJECT_ID = project_id
            else:
                # Get from metadata service in Cloud Run
                try:
                    import requests

                    response = requests.get(
                        "http://metadata.google.internal/computeMetadata/v1/project/project-id",
                        headers={"Metadata-Flavor": "Google"},
                        timeout=5,
                    )
                    if response.status_code == 200:
                        self.VERTEX_PROJECT_ID = response.text
                        security_logger.info("✅ Project ID from metadata service")
                except Exception as e:
                    security_logger.warning(
                        f"📡 Failed to get project ID from metadata: {e}"
                    )
                    if self.ENVIRONMENT not in ["development", "test"]:
                        raise ValueError(
                            "🚨 VERTEX_PROJECT_ID must be set in production"
                        )

        # Service account credentials
        if not self.GOOGLE_APPLICATION_CREDENTIALS:
            cred_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
            if cred_path and os.path.exists(cred_path):
                self.GOOGLE_APPLICATION_CREDENTIALS = cred_path
            elif self.ENVIRONMENT in ["development", "test"]:
                # Check common development/test paths
                dev_paths = [
                    "/Users/<USER>/giki-ai-workspace/infrastructure/service-accounts/development/service-account.json",
                    "/Users/<USER>/giki-ai-workspace/infrastructure/service-accounts/development/dev-service-account.json",
                    "./infrastructure/service-accounts/development/service-account.json",
                    "./infrastructure/service-accounts/development/dev-service-account.json",
                ]
                for path in dev_paths:
                    if os.path.exists(path):
                        self.GOOGLE_APPLICATION_CREDENTIALS = path
                        security_logger.warning(f"🔑 Using dev service account: {path}")
                        break

    def _get_secret(self, secret_name: str) -> Optional[str]:
        """Get secret from Secret Manager with error handling."""
        if not SECRETS_MANAGER_AVAILABLE:
            return None

        try:
            secrets_manager = get_secrets_manager()
            return secrets_manager.get_secret(secret_name)
        except Exception as e:
            security_logger.error(f"Secret Manager error for {secret_name}: {e}")
            return None

    def _get_secret_or_fail(self, secret_name: str) -> str:
        """Get secret from Secret Manager or fail securely."""
        secret_value = self._get_secret(secret_name)
        if not secret_value:
            raise ValueError(
                f"🚨 PRODUCTION SECURITY ERROR: Failed to retrieve {secret_name} from Secret Manager. "
                f"Ensure secret exists and service account has access."
            )
        return secret_value

    def _generate_secure_key(self, key_name: str) -> str:
        """Generate cryptographically secure key for development."""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*()_+-="
        secure_key = "".join(secrets.choice(alphabet) for _ in range(64))

        # Create a unique key per session to prevent reuse
        session_id = hashlib.sha256(str(os.getpid()).encode()).hexdigest()[:8]
        secure_key = f"dev_{session_id}_{secure_key}"

        security_logger.warning(
            f"🔑 Generated secure {key_name} for development session {session_id}"
        )
        return secure_key

    def _validate_security_configuration(self) -> None:
        """Comprehensive security validation with failure on issues."""
        security_issues = []
        warnings = []

        # Validate critical secrets
        if len(self.AUTH_SECRET_KEY) < 32:
            security_issues.append("AUTH_SECRET_KEY must be at least 32 characters")

        if len(self.SECRET_KEY) < 32:
            security_issues.append("SECRET_KEY must be at least 32 characters")

        if not self.DATABASE_URL:
            security_issues.append("DATABASE_URL must be configured")

        # Validate database URL security
        if self.DATABASE_URL:
            # Check for dangerous patterns
            dangerous_patterns = ["password123", "test", "development", "weak"]
            db_url_lower = self.DATABASE_URL.lower()

            for pattern in dangerous_patterns:
                if pattern in db_url_lower and self.ENVIRONMENT == "production":
                    security_issues.append(
                        f"Database URL contains unsafe pattern: {pattern}"
                    )

            # Check for localhost in production
            if "localhost" in db_url_lower and self.ENVIRONMENT == "production":
                security_issues.append(
                    "Production environment cannot use localhost database"
                )

        # Validate CORS origins
        if not self.CORS_ALLOWED_ORIGINS:
            warnings.append("CORS_ALLOWED_ORIGINS not configured")

        # Validate GCP configuration
        if not self.VERTEX_PROJECT_ID and self.ENVIRONMENT == "production":
            security_issues.append("VERTEX_PROJECT_ID required in production")

        # Fail on security issues
        if security_issues:
            error_msg = (
                "🚨 CRITICAL SECURITY CONFIGURATION ERRORS 🚨\n"
                + "\n".join(f"❌ {issue}" for issue in security_issues)
                + "\n\n🔒 ALL SECURITY ISSUES MUST BE RESOLVED BEFORE STARTUP"
            )
            security_logger.error(error_msg)
            raise RuntimeError(error_msg)

        # Log warnings
        if warnings:
            warning_msg = "⚠️ SECURITY CONFIGURATION WARNINGS:\n" + "\n".join(
                f"⚠️ {warning}" for warning in warnings
            )
            security_logger.warning(warning_msg)

        security_logger.info("✅ Security configuration validation passed")

    @field_validator("DATABASE_URL")
    @classmethod
    def validate_database_url_security(cls, v: str, info: ValidationInfo) -> str:
        """Validate database URL for security compliance."""
        if not v:
            return v  # Will be caught by _validate_security_configuration

        # Parse URL for validation
        try:
            parsed = urlparse(v)
        except Exception:
            raise ValueError("Invalid DATABASE_URL format")

        # Security checks
        environment = os.getenv("ENVIRONMENT", "production").lower()

        if environment == "production":
            # Production security requirements
            if not parsed.password:
                raise ValueError("Production database URL must include password")

            if len(parsed.password) < 16:
                raise ValueError(
                    "Production database password must be at least 16 characters"
                )

            # Check for weak passwords
            weak_patterns = ["password", "123", "test", "admin"]
            password_lower = parsed.password.lower()
            for pattern in weak_patterns:
                if pattern in password_lower:
                    raise ValueError(
                        f"Database password contains weak pattern: {pattern}"
                    )

        return v

    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT in ("production", "prod") and not self.DEBUG

    def get_cors_origins(self) -> list[str]:
        """Parse CORS allowed origins safely."""
        if not self.CORS_ALLOWED_ORIGINS:
            return []
        return [
            origin.strip()
            for origin in self.CORS_ALLOWED_ORIGINS.split(",")
            if origin.strip()
        ]

    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers for responses."""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
        }

    def log_security_event(self, event_type: str, details: Dict[str, Any]) -> None:
        """Log security events for monitoring."""
        security_logger.info(
            f"🔒 SECURITY EVENT: {event_type} - {details}",
            extra={
                "security_event": True,
                "event_type": event_type,
                "details": details,
            },
        )


# Create secure settings instance
def create_secure_settings() -> SecureSettings:
    """Factory function to create secure settings with comprehensive error handling."""
    try:
        settings = SecureSettings()
        security_logger.info("✅ Secure configuration initialized successfully")
        return settings
    except Exception as e:
        security_logger.error(f"🚨 FATAL: Secure configuration failed: {e}")
        raise


# Global settings instance
settings = create_secure_settings()

# Log successful initialization
settings.log_security_event(
    "configuration_initialized",
    {
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
        "secret_manager_available": SECRETS_MANAGER_AVAILABLE,
        "cors_origins_count": len(settings.get_cors_origins()),
    },
)
