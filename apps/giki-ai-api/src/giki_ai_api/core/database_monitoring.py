"""
Database Performance Monitoring Module
=====================================

Provides real-time database performance monitoring, query analysis,
and connection pool management for production optimization.
"""

import asyncio
import logging
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import asyncpg

# Database URL function will be imported when needed

logger = logging.getLogger(__name__)


@dataclass
class QueryMetrics:
    """Metrics for individual database queries."""

    query_hash: str
    query_text: str
    execution_time_ms: float
    rows_affected: int
    timestamp: datetime
    connection_id: Optional[str] = None
    error: Optional[str] = None


@dataclass
class ConnectionPoolMetrics:
    """Metrics for asyncpg connection pool."""

    pool_size: int
    pool_idle_size: int
    pool_free_size: int
    pool_max_size: int
    total_connections: int
    active_connections: int
    timestamp: datetime
    connection_failures: int = 0


class DatabaseMonitor:
    """
    Database performance monitoring and analytics for asyncpg.
    """

    def __init__(self):
        self.query_metrics: List[QueryMetrics] = []
        self.connection_metrics: List[ConnectionPoolMetrics] = []
        self.monitoring_active = False
        self.slow_query_threshold_ms = 1000  # 1 second
        self.max_stored_metrics = 1000  # Prevent memory bloat
        self.connection_pool: Optional[asyncpg.Pool] = None
        self.monitoring_task: Optional[asyncio.Task] = None
        self.connection_failures = 0

    async def start_monitoring(self) -> None:
        """Start database monitoring with query and connection tracking."""
        if self.monitoring_active:
            logger.warning("Database monitoring already active")
            return

        self.monitoring_active = True
        logger.info("Starting database performance monitoring with asyncpg")

        try:
            # Create connection pool for monitoring
            from .database import get_database_url

            database_url = get_database_url()
            self.connection_pool = await asyncpg.create_pool(
                database_url,
                min_size=1,
                max_size=5,
                server_settings={
                    "application_name": "giki_ai_monitor",
                    "statement_timeout": "30s",
                },
            )

            # Start background monitoring task
            self.monitoring_task = asyncio.create_task(self._monitor_connection_pool())
            logger.info("✅ Database monitoring started successfully")

        except Exception as e:
            logger.error(f"❌ Failed to start database monitoring: {e}")
            self.monitoring_active = False
            raise

    async def stop_monitoring(self) -> None:
        """Stop database monitoring."""
        self.monitoring_active = False

        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        if self.connection_pool:
            await self.connection_pool.close()
            self.connection_pool = None

        logger.info("Stopped database performance monitoring")

    def _add_query_metrics(self, metrics: QueryMetrics) -> None:
        """Add query metrics with memory management."""
        self.query_metrics.append(metrics)

        # Prevent memory bloat by keeping only recent metrics
        if len(self.query_metrics) > self.max_stored_metrics:
            self.query_metrics = self.query_metrics[-self.max_stored_metrics :]

    async def _monitor_connection_pool(self) -> None:
        """Monitor asyncpg connection pool status periodically."""
        while self.monitoring_active:
            try:
                if self.connection_pool:
                    # Get pool metrics
                    pool_size = self.connection_pool.get_size()
                    pool_idle_size = self.connection_pool.get_idle_size()
                    pool_max_size = self.connection_pool.get_max_size()
                    pool_free_size = pool_idle_size  # In asyncpg, idle connections are free connections
                    active_connections = pool_size - pool_idle_size

                    metrics = ConnectionPoolMetrics(
                        pool_size=pool_size,
                        pool_idle_size=pool_idle_size,
                        pool_free_size=pool_free_size,
                        pool_max_size=pool_max_size,
                        total_connections=pool_size,
                        active_connections=active_connections,
                        timestamp=datetime.now(),
                        connection_failures=self.connection_failures,
                    )

                    self.connection_metrics.append(metrics)

                    # Prevent memory bloat
                    if len(self.connection_metrics) > self.max_stored_metrics:
                        self.connection_metrics = self.connection_metrics[
                            -self.max_stored_metrics :
                        ]

                    # Log warnings for connection pool issues
                    utilization = (
                        (active_connections / pool_size * 100) if pool_size > 0 else 0
                    )
                    if utilization > 80:
                        logger.warning(
                            f"High connection pool usage: {active_connections}/{pool_size} ({utilization:.1f}%)"
                        )

                    # Test basic connectivity
                    try:
                        async with self.connection_pool.acquire() as conn:
                            await conn.fetchval("SELECT 1")
                    except Exception as e:
                        self.connection_failures += 1
                        logger.error(f"Connection test failed: {e}")

                    logger.debug(
                        f"Pool status: {active_connections}/{pool_size} active, "
                        f"{pool_idle_size} idle, {self.connection_failures} failures"
                    )

            except Exception as e:
                logger.error(f"Error monitoring connection pool: {e}")
                self.connection_failures += 1

            # Wait before next check
            await asyncio.sleep(30)  # Check every 30 seconds

    async def get_performance_summary(self, minutes: int = 10) -> Dict[str, Any]:
        """
        Get performance summary for the last N minutes.

        Args:
            minutes: Number of minutes to analyze

        Returns:
            Performance summary with key metrics
        """
        cutoff_time = datetime.now() - timedelta(minutes=minutes)

        # Filter recent query metrics
        recent_queries = [q for q in self.query_metrics if q.timestamp >= cutoff_time]

        # Filter recent connection metrics
        recent_connections = [
            c for c in self.connection_metrics if c.timestamp >= cutoff_time
        ]

        if not recent_queries:
            return {
                "status": "no_data",
                "message": f"No query metrics available for last {minutes} minutes",
                "timestamp": datetime.now().isoformat(),
            }

        # Calculate query statistics
        execution_times = [q.execution_time_ms for q in recent_queries]
        total_queries = len(recent_queries)
        slow_queries = [
            q
            for q in recent_queries
            if q.execution_time_ms > self.slow_query_threshold_ms
        ]

        avg_execution_time = (
            sum(execution_times) / total_queries if total_queries > 0 else 0
        )
        max_execution_time = max(execution_times) if execution_times else 0
        min_execution_time = min(execution_times) if execution_times else 0

        # Calculate connection statistics
        if recent_connections:
            latest_connection_metrics = recent_connections[-1]
            avg_active_connections = sum(
                c.active_connections for c in recent_connections
            ) / len(recent_connections)
            max_active_connections = max(
                c.active_connections for c in recent_connections
            )
        else:
            latest_connection_metrics = None
            avg_active_connections = 0
            max_active_connections = 0

        return {
            "status": "success",
            "time_period_minutes": minutes,
            "timestamp": datetime.now().isoformat(),
            "query_metrics": {
                "total_queries": total_queries,
                "slow_queries": len(slow_queries),
                "slow_query_percentage": (len(slow_queries) / total_queries * 100)
                if total_queries > 0
                else 0,
                "average_execution_time_ms": round(avg_execution_time, 2),
                "max_execution_time_ms": round(max_execution_time, 2),
                "min_execution_time_ms": round(min_execution_time, 2),
                "queries_per_minute": round(total_queries / minutes, 2),
            },
            "connection_metrics": {
                "pool_status": asdict(latest_connection_metrics)
                if latest_connection_metrics
                else None,
                "average_active_connections": round(avg_active_connections, 2),
                "max_active_connections": max_active_connections,
                "connection_efficiency": round(
                    (avg_active_connections / latest_connection_metrics.pool_size * 100)
                    if latest_connection_metrics
                    and latest_connection_metrics.pool_size > 0
                    else 0,
                    2,
                ),
            },
        }

    async def get_slow_queries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get the slowest queries in recent history.

        Args:
            limit: Maximum number of slow queries to return

        Returns:
            List of slow query details
        """
        slow_queries = [
            q
            for q in self.query_metrics
            if q.execution_time_ms > self.slow_query_threshold_ms
        ]

        # Sort by execution time (slowest first)
        slow_queries.sort(key=lambda q: q.execution_time_ms, reverse=True)

        return [
            {
                "execution_time_ms": q.execution_time_ms,
                "query_text": q.query_text,
                "rows_affected": q.rows_affected,
                "timestamp": q.timestamp.isoformat(),
                "connection_id": q.connection_id,
            }
            for q in slow_queries[:limit]
        ]

    async def get_connection_pool_status(self) -> Dict[str, Any]:
        """Get current asyncpg connection pool status and health."""
        if not self.connection_pool:
            return {
                "status": "unavailable",
                "message": "Database connection pool not available",
            }

        try:
            # Get current pool metrics
            pool_size = self.connection_pool.get_size()
            pool_idle_size = self.connection_pool.get_idle_size()
            pool_max_size = self.connection_pool.get_max_size()
            active_connections = pool_size - pool_idle_size

            current_metrics = ConnectionPoolMetrics(
                pool_size=pool_size,
                pool_idle_size=pool_idle_size,
                pool_free_size=pool_idle_size,
                pool_max_size=pool_max_size,
                total_connections=pool_size,
                active_connections=active_connections,
                timestamp=datetime.now(),
                connection_failures=self.connection_failures,
            )

            # Calculate pool health
            utilization = (
                (current_metrics.active_connections / current_metrics.pool_size * 100)
                if current_metrics.pool_size > 0
                else 0
            )

            if utilization < 50:
                pool_health = "excellent"
            elif utilization < 75:
                pool_health = "good"
            elif utilization < 90:
                pool_health = "warning"
            else:
                pool_health = "critical"

            # Test current connectivity
            connectivity_status = "unknown"
            connection_test_time_ms = 0
            try:
                test_start = time.time()
                async with self.connection_pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")
                connection_test_time_ms = (time.time() - test_start) * 1000
                connectivity_status = "healthy"
            except Exception as e:
                logger.warning(f"Connection test failed: {e}")
                connectivity_status = "failed"

            return {
                "status": "healthy" if connectivity_status == "healthy" else "degraded",
                "pool_health": pool_health,
                "utilization_percentage": round(utilization, 2),
                "connectivity_status": connectivity_status,
                "connection_test_time_ms": round(connection_test_time_ms, 2),
                "metrics": asdict(current_metrics),
                "recommendations": self._get_pool_recommendations(
                    current_metrics, utilization
                ),
            }

        except Exception as e:
            logger.error(f"Error getting connection pool status: {e}")
            return {"status": "error", "error": str(e)}

    def _get_pool_recommendations(
        self, metrics: ConnectionPoolMetrics, utilization: float
    ) -> List[str]:
        """Get recommendations for asyncpg pool optimization."""
        recommendations = []

        if utilization > 90:
            recommendations.append(
                f"Consider increasing pool size - high utilization detected ({utilization:.1f}%)"
            )
        elif utilization < 20:
            recommendations.append(
                f"Consider reducing pool size - low utilization detected ({utilization:.1f}%)"
            )

        if metrics.pool_size >= metrics.pool_max_size:
            recommendations.append(
                "Pool at maximum size - consider increasing max_size if needed"
            )

        if metrics.connection_failures > 0:
            recommendations.append(
                f"Connection failures detected ({metrics.connection_failures}) - check database health"
            )

        if metrics.pool_idle_size == 0 and utilization > 80:
            recommendations.append(
                "No idle connections available - consider increasing pool size"
            )

        if not recommendations:
            recommendations.append("Connection pool is optimally configured")

        return recommendations

    async def record_query(
        self,
        query_text: str,
        execution_time_ms: float,
        rows_affected: int = 0,
        connection_id: Optional[str] = None,
        error: Optional[str] = None,
    ) -> None:
        """Record a query execution for performance monitoring."""
        if not self.monitoring_active:
            return

        import hashlib

        query_hash = hashlib.md5(query_text.encode()).hexdigest()[:16]

        metrics = QueryMetrics(
            query_hash=query_hash,
            query_text=query_text,
            execution_time_ms=execution_time_ms,
            rows_affected=rows_affected,
            timestamp=datetime.now(),
            connection_id=connection_id,
            error=error,
        )

        self._add_query_metrics(metrics)

        # Log slow queries
        if execution_time_ms > self.slow_query_threshold_ms:
            logger.warning(
                f"Slow query detected ({execution_time_ms:.1f}ms): {query_text[:100]}..."
            )

    async def execute_monitored_query(
        self, query: str, *args, connection: Optional[asyncpg.Connection] = None
    ) -> Any:
        """Execute a query with performance monitoring."""
        start_time = time.time()
        error = None
        result = None
        rows_affected = 0

        try:
            if connection:
                # Use provided connection
                if args:
                    result = await connection.fetch(query, *args)
                else:
                    result = await connection.fetch(query)
            elif self.connection_pool:
                # Use monitoring pool
                async with self.connection_pool.acquire() as conn:
                    if args:
                        result = await conn.fetch(query, *args)
                    else:
                        result = await conn.fetch(query)
            else:
                raise Exception("No database connection available for monitoring")

            rows_affected = len(result) if result else 0

        except Exception as e:
            error = str(e)
            raise
        finally:
            execution_time_ms = (time.time() - start_time) * 1000

            # Record the query metrics
            await self.record_query(
                query_text=query,
                execution_time_ms=execution_time_ms,
                rows_affected=rows_affected,
                error=error,
            )

        return result

    async def analyze_query_patterns(self, hours: int = 1) -> Dict[str, Any]:
        """
        Analyze query patterns for optimization insights.

        Args:
            hours: Number of hours to analyze

        Returns:
            Query pattern analysis
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_queries = [q for q in self.query_metrics if q.timestamp >= cutoff_time]

        if not recent_queries:
            return {
                "status": "no_data",
                "message": f"No queries found in last {hours} hours",
            }

        # Group queries by similar patterns
        query_patterns = {}
        for query in recent_queries:
            # Simple pattern detection (first 50 chars)
            pattern = query.query_text[:50].strip()
            if pattern not in query_patterns:
                query_patterns[pattern] = []
            query_patterns[pattern].append(query)

        # Analyze each pattern
        pattern_analysis = []
        for pattern, queries in query_patterns.items():
            avg_time = sum(q.execution_time_ms for q in queries) / len(queries)
            max_time = max(q.execution_time_ms for q in queries)
            total_calls = len(queries)

            pattern_analysis.append(
                {
                    "pattern": pattern,
                    "total_calls": total_calls,
                    "average_time_ms": round(avg_time, 2),
                    "max_time_ms": round(max_time, 2),
                    "total_time_ms": round(
                        sum(q.execution_time_ms for q in queries), 2
                    ),
                    "optimization_priority": "high"
                    if avg_time > self.slow_query_threshold_ms
                    else "low",
                }
            )

        # Sort by total time consumed (optimization priority)
        pattern_analysis.sort(key=lambda p: p["total_time_ms"], reverse=True)

        return {
            "status": "success",
            "analysis_period_hours": hours,
            "total_unique_patterns": len(query_patterns),
            "total_queries": len(recent_queries),
            "patterns": pattern_analysis[:20],  # Top 20 patterns
            "optimization_recommendations": self._get_optimization_recommendations(
                pattern_analysis
            ),
        }

    def _get_optimization_recommendations(
        self, patterns: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate optimization recommendations based on query patterns."""
        recommendations = []

        high_priority_patterns = [
            p for p in patterns if p["optimization_priority"] == "high"
        ]
        frequent_patterns = [p for p in patterns if p["total_calls"] > 100]

        if high_priority_patterns:
            recommendations.append(
                f"Optimize {len(high_priority_patterns)} slow query patterns"
            )

        if frequent_patterns:
            recommendations.append(
                f"Consider caching for {len(frequent_patterns)} frequently called patterns"
            )

        if not recommendations:
            recommendations.append("Query performance is optimal")

        return recommendations


# Global database monitor instance
db_monitor = DatabaseMonitor()


async def start_database_monitoring() -> None:
    """Start global database monitoring."""
    await db_monitor.start_monitoring()


async def get_database_performance_summary(minutes: int = 10) -> Dict[str, Any]:
    """Get database performance summary."""
    return await db_monitor.get_performance_summary(minutes)


async def get_database_slow_queries(limit: int = 10) -> List[Dict[str, Any]]:
    """Get recent slow queries."""
    return await db_monitor.get_slow_queries(limit)


async def get_database_connection_status() -> Dict[str, Any]:
    """Get database connection pool status."""
    return await db_monitor.get_connection_pool_status()
