"""
Database health monitoring and circuit breaker implementation.
Prevents cascading failures from database connection issues.
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Dict

# SQLAlchemy health monitoring disabled - migrated to asyncpg
# from .database import get_database_config

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""

    CLOSED = "closed"  # Normal operation
    OPEN = "open"  # Failing, stop trying
    HALF_OPEN = "half_open"  # Testing if service recovered


class DatabaseCircuitBreaker:
    """
    Circuit breaker pattern for database connections.
    Prevents repeated attempts when database is unavailable.
    """

    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        request_timeout: int = 30,
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.request_timeout = request_timeout

        self.failure_count = 0
        self.last_failure_time = 0
        self.state = CircuitState.CLOSED

    async def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection."""

        if self.state == CircuitState.OPEN:
            # Check if we should try to recover
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = CircuitState.HALF_OPEN
                logger.info(
                    "🔄 Database circuit breaker: Attempting recovery (HALF_OPEN)"
                )
            else:
                raise Exception("Database circuit breaker OPEN - service unavailable")

        try:
            # Execute the function with timeout
            result = await asyncio.wait_for(
                func(*args, **kwargs), timeout=self.request_timeout
            )

            # Success - reset if we were in HALF_OPEN
            if self.state == CircuitState.HALF_OPEN:
                self.reset()
                logger.info("✅ Database circuit breaker: Service recovered (CLOSED)")

            return result

        except Exception as e:
            self.record_failure()
            logger.warning(f"Database circuit breaker: Request failed - {e}")
            raise

    def record_failure(self):
        """Record a failure and update circuit state."""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            logger.error(
                f"🚨 Database circuit breaker OPEN: {self.failure_count} failures"
            )

    def reset(self):
        """Reset circuit breaker to normal operation."""
        self.failure_count = 0
        self.state = CircuitState.CLOSED

    def get_status(self) -> Dict:
        """Get current circuit breaker status."""
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "last_failure_time": self.last_failure_time,
            "recovery_timeout": self.recovery_timeout,
        }


class DatabaseHealthMonitor:
    """
    Comprehensive database health monitoring.
    """

    def __init__(self):
        self.circuit_breaker = DatabaseCircuitBreaker(
            failure_threshold=3,  # Fail fast
            recovery_timeout=30,  # Quick recovery attempts
            request_timeout=5,  # Short timeout for health checks
        )
        self.last_successful_check = 0
        self.consecutive_failures = 0

    async def health_check(self) -> Dict:
        """
        Perform comprehensive database health check.
        """
        start_time = time.time()

        try:
            # Use circuit breaker for health check
            await self.circuit_breaker.call(self._perform_health_check)

            self.last_successful_check = time.time()
            self.consecutive_failures = 0

            response_time = (time.time() - start_time) * 1000

            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "circuit_breaker": self.circuit_breaker.get_status(),
                "last_successful_check": self.last_successful_check,
                "consecutive_failures": self.consecutive_failures,
            }

        except Exception as e:
            self.consecutive_failures += 1
            response_time = (time.time() - start_time) * 1000

            return {
                "status": "unhealthy",
                "error": str(e)[:200],  # Limit error message length
                "response_time_ms": round(response_time, 2),
                "circuit_breaker": self.circuit_breaker.get_status(),
                "last_successful_check": self.last_successful_check,
                "consecutive_failures": self.consecutive_failures,
            }

    async def _perform_health_check(self):
        """Internal health check implementation."""

        import asyncpg

        # Get database URL from settings
        from .database import get_database_url

        try:
            database_url = get_database_url()
        except ValueError:
            raise Exception("DATABASE_URL not configured")

        # Parse and convert to asyncpg format
        if "postgresql+asyncpg://" in database_url:
            database_url = database_url.replace(
                "postgresql+asyncpg://", "postgresql://"
            )

        # Attempt connection and simple query
        conn = await asyncpg.connect(database_url)
        try:
            # Simple query to test connection
            result = await conn.fetchval("SELECT 1")
            return result == 1
        finally:
            await conn.close()

    async def is_healthy(self) -> bool:
        """Quick health status check."""
        if self.circuit_breaker.state == CircuitState.OPEN:
            return False

        # Consider healthy if last check was recent and successful
        time_since_check = time.time() - self.last_successful_check
        return time_since_check < 60 and self.consecutive_failures == 0


# Global health monitor instance
db_health_monitor = DatabaseHealthMonitor()


async def get_database_health() -> Dict:
    """
    Get current database health status.
    Used by health check endpoints.
    """
    return await db_health_monitor.health_check()


async def ensure_database_health():
    """
    Ensure database is healthy before proceeding.
    Raises exception if database is unhealthy.
    """
    if not await db_health_monitor.is_healthy():
        health_status = await db_health_monitor.health_check()
        if health_status["status"] != "healthy":
            raise Exception(
                f"Database health check failed: {health_status.get('error', 'Unknown error')}"
            )
