"""Database status check script."""

import asyncio
from pathlib import Path

from dotenv import load_dotenv

from .database import get_database_config


async def check_environment(env: str) -> bool:
    """Check database connection for an environment."""
    print(f"\nChecking {env} environment:")

    # Set environment-specific prefix
    prefix_map = {"development": "dev_", "staging": "staging_", "production": "prod_"}
    prefix = prefix_map.get(env, "")

    # Load environment config
    env_file = Path(__file__).parent.parent.parent.parent.parent / f".env.{env}"
    if env_file.exists():
        load_dotenv(env_file)
        print(f"[OK] Loaded config from {env_file}")
    else:
        load_dotenv()
        print("[WARN] Using default .env")

    try:
        # Initialize database and get session factory
        session_factory = get_database_config()
        print("[OK] Database configuration loaded")

        # Validate session factory
        if session_factory is None:
            raise RuntimeError("Database session factory not initialized")

        # Test connection using asyncpg
        async with session_factory() as db:
            # Test basic connectivity
            result = await db.fetchval("SELECT 1")
            if result == 1:
                print("[OK] Database connection successful")

            # Get schema info using environment-specific prefix
            sql = "SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE $1"
            rows = await db.fetch(sql, f"{prefix}%")
            tables = [row["tablename"] for row in rows]
            print(f"[OK] Found {len(tables)} tables with prefix '{prefix}':")
            for table in tables:
                print(f"  - {table}")

            # No explicit commit needed for read operations in asyncpg

        return True

    except Exception as e:
        print(f"[ERROR] {str(e)}")
        return False


async def main():
    """Check all environments."""
    print("=== Database Environment Status ===")

    environments = ["development", "staging", "production"]
    results = []

    for env in environments:
        success = await check_environment(env)
        results.append((env, success))

    print("\nSummary:")
    for env, success in results:
        status = "[OK] Connected" if success else "[ERROR] Failed"
        print(f"{env:.<15}{status}")


if __name__ == "__main__":
    asyncio.run(main())
