"""
JWT RSA Key Management for Production-Grade Security

This module handles RSA key generation and management for JWT tokens.
Using RS256 (RSA with SHA-256) provides better security than HS256:
- Private key is only needed on auth server
- Public key can be distributed for verification
- Supports key rotation without breaking existing tokens
"""

import logging
import os
from pathlib import Path

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa

logger = logging.getLogger(__name__)

# Key storage directory
KEY_DIR = Path(os.getenv("JWT_KEY_DIR", "/tmp/giki_jwt_keys"))
PRIVATE_KEY_PATH = KEY_DIR / "jwt_private.pem"
PUBLIC_KEY_PATH = KEY_DIR / "jwt_public.pem"


def generate_rsa_key_pair():
    """Generate a new RSA key pair for JWT signing."""
    # Generate private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
        backend=default_backend()
    )
    
    # Get public key
    public_key = private_key.public_key()
    
    # Serialize private key
    private_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    # Serialize public key
    public_pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    
    return private_pem, public_pem


def ensure_jwt_keys():
    """Ensure JWT RSA keys exist, generate if missing."""
    KEY_DIR.mkdir(parents=True, exist_ok=True)
    
    if not PRIVATE_KEY_PATH.exists() or not PUBLIC_KEY_PATH.exists():
        logger.info("Generating new RSA key pair for JWT...")
        private_pem, public_pem = generate_rsa_key_pair()
        
        # Write keys to disk
        PRIVATE_KEY_PATH.write_bytes(private_pem)
        PUBLIC_KEY_PATH.write_bytes(public_pem)
        
        # Set proper permissions (private key should be readable only by owner)
        os.chmod(PRIVATE_KEY_PATH, 0o600)
        os.chmod(PUBLIC_KEY_PATH, 0o644)
        
        logger.info("RSA key pair generated successfully")
    else:
        logger.info("Using existing RSA key pair")


def get_private_key() -> bytes:
    """Get the private key for signing JWTs."""
    ensure_jwt_keys()
    return PRIVATE_KEY_PATH.read_bytes()


def get_public_key() -> bytes:
    """Get the public key for verifying JWTs."""
    ensure_jwt_keys()
    return PUBLIC_KEY_PATH.read_bytes()


# Production key rotation support
def rotate_keys():
    """Rotate JWT keys (should be done periodically)."""
    # Backup old keys
    if PRIVATE_KEY_PATH.exists():
        backup_dir = KEY_DIR / "backup"
        backup_dir.mkdir(exist_ok=True)
        
        import time
        timestamp = int(time.time())
        PRIVATE_KEY_PATH.rename(backup_dir / f"jwt_private_{timestamp}.pem")
        PUBLIC_KEY_PATH.rename(backup_dir / f"jwt_public_{timestamp}.pem")
    
    # Generate new keys
    ensure_jwt_keys()
    logger.info("JWT keys rotated successfully")