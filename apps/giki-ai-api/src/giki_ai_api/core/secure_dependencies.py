"""
Secure dependencies without dangerous optimizations.

All user verification happens against the database on every request.
"""

import logging

import asyncpg
from fastapi import Depends, HTTPException, status

# SQLAlchemy migrated to asyncpg
from ..domains.auth.models import Tenant as TenantModel, User
from ..domains.auth.secure_auth import get_current_active_user
from .database import get_db_session

logger = logging.getLogger(__name__)


async def get_current_user_with_tenant(
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_db_session),
) -> tuple[User, TenantModel]:
    """
    Get current user with their tenant information.

    ALWAYS queries database to ensure tenant still exists and user
    still has access to it.
    """
    # Query tenant from database (no caching) using asyncpg
    sql = "SELECT * FROM tenants WHERE id = $1"
    row = await db.fetchrow(sql, current_user.tenant_id)
    tenant = TenantModel.model_validate(dict(row)) if row else None

    if not tenant:
        logger.error(
            f"Tenant {current_user.tenant_id} not found for user {current_user.email}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant not found or access denied",
        )

    return current_user, tenant


async def get_current_tenant_id(
    current_user: User = Depends(get_current_active_user),
) -> int:
    """
    Get current user's tenant ID.

    Since get_current_active_user always queries the database,
    we know the tenant_id is current and valid.
    """
    return current_user.tenant_id


# Re-export secure authentication dependencies
__all__ = [
    "get_current_user_with_tenant",
    "get_current_tenant_id",
    "get_current_active_user",  # Re-exported from secure_auth
]
