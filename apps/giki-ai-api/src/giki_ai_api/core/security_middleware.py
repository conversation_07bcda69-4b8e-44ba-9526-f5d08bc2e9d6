"""
Security Headers Middleware for Production-Grade Protection

This middleware adds essential security headers to all responses to protect
against common web vulnerabilities like XSS, clickjacking, and MIME sniffing.
"""

import time

from fastapi import Request


async def security_headers_middleware(request: Request, call_next):
    """
    Add security headers to all responses.
    
    Security headers included:
    - X-Content-Type-Options: Prevents MIME type sniffing
    - X-Frame-Options: Prevents clickjacking attacks
    - X-XSS-Protection: Enables browser XSS filtering
    - Strict-Transport-Security: Forces HTTPS connections
    - Content-Security-Policy: Controls resource loading
    - Referrer-Policy: Controls referrer information
    - Permissions-Policy: Controls browser features
    """
    # Process the request
    response = await call_next(request)
    
    # Add security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    
    # HSTS - Only add in production when serving over HTTPS
    if request.url.scheme == "https":
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    
    # Content Security Policy - Restrictive by default
    # Allow only same-origin resources, inline styles/scripts blocked
    csp_directives = [
        "default-src 'self'",
        "script-src 'self'",
        "style-src 'self' 'unsafe-inline'",  # Allow inline styles for now
        "img-src 'self' data: https:",
        "font-src 'self'",
        "connect-src 'self'",
        "frame-ancestors 'none'",
        "base-uri 'self'",
        "form-action 'self'"
    ]
    response.headers["Content-Security-Policy"] = "; ".join(csp_directives)
    
    # Referrer Policy - Don't leak referrer info
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    
    # Permissions Policy - Disable unnecessary browser features
    permissions = [
        "geolocation=()",
        "camera=()",
        "microphone=()",
        "payment=()",
        "usb=()",
        "magnetometer=()",
        "accelerometer=()"
    ]
    response.headers["Permissions-Policy"] = ", ".join(permissions)
    
    # Add request ID for tracing
    request_id = getattr(request.state, "request_id", None)
    if request_id:
        response.headers["X-Request-ID"] = request_id
    
    return response


async def request_id_middleware(request: Request, call_next):
    """
    Add unique request ID for tracing and debugging.
    """
    # Generate unique request ID
    request_id = f"{int(time.time() * 1000)}-{id(request)}"
    request.state.request_id = request_id
    
    # Add to request headers for downstream services
    request.headers.__dict__["_list"].append(
        (b"x-request-id", request_id.encode())
    )
    
    response = await call_next(request)
    
    # Add request ID to response
    response.headers["X-Request-ID"] = request_id
    
    return response