class GikiAIError(Exception):
    """Base exception for Giki AI API AI-related errors."""

    pass


class ConfigurationError(GikiAIError):
    """Error related to application configuration."""

    pass


# Vertex AI Client specific errors (can be re-raised or wrapped by service-level errors)
# These align with and can extend or be used by _VertexAIClient
class VertexAIClientError(GikiAIError):
    """Base exception for _VertexAIClient errors."""

    pass


class VertexAIInitializationError(VertexAIClientError):
    """Error during Vertex AI client initialization."""

    pass


class VertexAIRAGOperationError(VertexAIClientError):
    """Error during Vertex AI RAG operations."""

    pass


class VertexAIGenerationError(VertexAIClientError):
    """Error during Vertex AI content generation."""

    pass


class VertexAIGCSError(VertexAIClientError):
    """Error during GCS operations via Vertex AI client."""

    pass


# Old simple exceptions removed - using enhanced system below


class ServiceNotInitializedError(GikiAIError):
    """Error when a service is used before it's properly initialized."""

    pass


class LLMInteractionError(GikiAIError):
    """Error during interaction with an LLM, such as prompt issues or response parsing."""

    pass


# ValidationError and FileProcessingError are defined later with enhanced capabilities


# Enhanced Data Processing Exceptions
class DataProcessingError(GikiAIError):
    """Base exception for data processing pipeline errors."""

    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}


class SchemaDetectionError(DataProcessingError):
    """Error during schema detection and validation."""

    pass


class TransactionProcessingError(DataProcessingError):
    """Error during transaction creation and processing."""

    pass


# CategorizationError moved to enhanced system below


class EntityExtractionError(DataProcessingError):
    """Error during entity extraction from transactions."""

    pass


class MultiSheetProcessingError(DataProcessingError):
    """Error during multi-sheet Excel processing."""

    pass


class RecoveryError(DataProcessingError):
    """Error during error recovery operations."""

    pass


class LearningError(GikiAIError):
    """Error during user feedback learning operations."""

    pass


# ServiceError is defined later with enhanced capabilities


# ============================================================================
# Enhanced Exception Hierarchy for Comprehensive Error Handling
# ============================================================================

import traceback
from enum import Enum
from typing import Any, Dict, List, Optional


class ErrorSeverity(Enum):
    """Error severity levels for proper handling and logging."""

    LOW = "low"  # Warnings, non-critical issues
    MEDIUM = "medium"  # Recoverable errors
    HIGH = "high"  # Service degradation
    CRITICAL = "critical"  # System failure


class GikiAIBaseError(Exception):
    """
    Enhanced base exception class for all Giki AI errors.

    Provides comprehensive error context, correlation tracking,
    and proper error handling capabilities.
    """

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        recovery_suggestions: Optional[List[str]] = None,
        original_error: Optional[Exception] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.severity = severity
        self.context = context or {}
        self.correlation_id = correlation_id
        self.recovery_suggestions = recovery_suggestions or []
        self.original_error = original_error
        self.traceback_info = traceback.format_exc() if original_error else None

    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging and API responses."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "severity": self.severity.value,
            "context": self.context,
            "correlation_id": self.correlation_id,
            "recovery_suggestions": self.recovery_suggestions,
            "original_error": str(self.original_error) if self.original_error else None,
        }

    def __str__(self) -> str:
        """Enhanced string representation with context."""
        base_msg = f"[{self.error_code}] {self.message}"
        if self.correlation_id:
            base_msg += f" (ID: {self.correlation_id})"
        if self.context:
            base_msg += f" | Context: {self.context}"
        return base_msg


class ServiceError(GikiAIBaseError):
    """Base class for service-level errors with operation context."""

    def __init__(self, message: str, service_name: str, operation: str, **kwargs):
        super().__init__(message, **kwargs)
        self.service_name = service_name
        self.operation = operation
        self.context.update({"service": service_name, "operation": operation})


class AgentError(GikiAIBaseError):
    """Base class for ADK agent errors with agent context."""

    def __init__(
        self, message: str, agent_name: str, tool_name: Optional[str] = None, **kwargs
    ):
        super().__init__(message, **kwargs)
        self.agent_name = agent_name
        self.tool_name = tool_name
        self.context.update({"agent": agent_name, "tool": tool_name})


class RetryableError(GikiAIBaseError):
    """Error that can be retried with exponential backoff."""

    def __init__(
        self, message: str, max_retries: int = 3, retry_delay: float = 1.0, **kwargs
    ):
        super().__init__(message, **kwargs)
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.context.update({"max_retries": max_retries, "retry_delay": retry_delay})


class CircuitBreakerError(GikiAIBaseError):
    """Error indicating circuit breaker is open."""

    def __init__(self, service_name: str, **kwargs):
        message = f"Circuit breaker open for service: {service_name}"
        super().__init__(message, **kwargs)
        self.service_name = service_name
        self.severity = ErrorSeverity.HIGH
        self.context.update({"service": service_name})


# Enhanced specific exception classes
class CategorizationError(ServiceError):
    """Enhanced categorization error with AI context."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            service_name="CategoryService",
            operation="categorization",
            **kwargs,
        )


class ValidationError(ServiceError):
    """Enhanced validation error with field context."""

    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            service_name="ValidationService",
            operation="validation",
            **kwargs,
        )
        if field_name:
            self.context.update(
                {
                    "field_name": field_name,
                    "field_value": str(field_value)
                    if field_value is not None
                    else None,
                }
            )


class AIServiceError(ServiceError):
    """Enhanced AI service error with model context."""

    def __init__(
        self,
        message: str,
        model_name: Optional[str] = None,
        operation_type: Optional[str] = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            service_name="AIService",
            operation=operation_type or "ai_operation",
            **kwargs,
        )
        if model_name:
            self.context.update({"model": model_name})


class DatabaseError(ServiceError):
    """Enhanced database error with query context."""

    def __init__(
        self,
        message: str,
        query_type: Optional[str] = None,
        table_name: Optional[str] = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            service_name="DatabaseService",
            operation=query_type or "database_operation",
            **kwargs,
        )
        if table_name:
            self.context.update({"table": table_name})


class AuthenticationError(ServiceError):
    """Enhanced authentication error with user context."""

    def __init__(self, message: str, user_id: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            service_name="AuthService",
            operation="authentication",
            **kwargs,
        )
        self.severity = ErrorSeverity.HIGH
        if user_id:
            self.context.update({"user_id": user_id})


class FileProcessingError(ServiceError):
    """Enhanced file processing error with file context."""

    def __init__(
        self,
        message: str,
        filename: Optional[str] = None,
        file_type: Optional[str] = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            service_name="FileProcessingService",
            operation="file_processing",
            **kwargs,
        )
        if filename:
            self.context.update({"filename": filename, "file_type": file_type})


class OnboardingError(ServiceError):
    """Enhanced onboarding error with tenant context."""

    def __init__(
        self,
        message: str,
        tenant_id: Optional[int] = None,
        step: Optional[str] = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            service_name="OnboardingService",
            operation=step or "onboarding",
            **kwargs,
        )
        if tenant_id:
            self.context.update({"tenant_id": tenant_id, "onboarding_step": step})


class RAGError(ServiceError):
    """Enhanced RAG service error with corpus context."""

    def __init__(
        self,
        message: str,
        corpus_id: Optional[str] = None,
        query_type: Optional[str] = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            service_name="RAGService",
            operation=query_type or "rag_operation",
            **kwargs,
        )
        if corpus_id:
            self.context.update({"corpus_id": corpus_id})


# Legacy exception classes that inherit from enhanced system
class RAGContextError(CategorizationError):
    """Error related to RAG context retrieval or processing."""

    pass


class RAGProcessingError(GikiAIError):
    """Error during RAG processing operations."""

    pass


class ModelResponseError(CategorizationError):
    """Error related to parsing or validity of the model's response."""

    pass


class GCSInteractionError(CategorizationError):
    """Error during GCS interaction."""

    pass


class DataPreparationError(CategorizationError):
    """Error during data preparation for RAG or model."""

    pass
