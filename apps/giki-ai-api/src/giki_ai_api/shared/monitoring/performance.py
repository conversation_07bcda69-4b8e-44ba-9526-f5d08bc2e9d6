"""
Performance tracking decorators and utilities for monitoring categorization operations.
"""

import asyncio
import functools
import logging
import time
from typing import Any, Callable, Dict, Optional

logger = logging.getLogger(__name__)


def track_performance(operation_name: str) -> Callable:
    """
    Decorator to track performance of categorization operations.

    Args:
        operation_name: Name of the operation being tracked

    Returns:
        Decorator function
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                elapsed_ms = (time.time() - start_time) * 1000

                if elapsed_ms > 100:  # Log slow operations
                    logger.info(
                        f"Performance: {operation_name} took {elapsed_ms:.2f}ms"
                    )

                return result
            except Exception as e:
                elapsed_ms = (time.time() - start_time) * 1000
                logger.error(
                    f"Performance: {operation_name} failed after {elapsed_ms:.2f}ms: {str(e)}"
                )
                raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                elapsed_ms = (time.time() - start_time) * 1000

                if elapsed_ms > 100:  # Log slow operations
                    logger.info(
                        f"Performance: {operation_name} took {elapsed_ms:.2f}ms"
                    )

                return result
            except Exception as e:
                elapsed_ms = (time.time() - start_time) * 1000
                logger.error(
                    f"Performance: {operation_name} failed after {elapsed_ms:.2f}ms: {str(e)}"
                )
                raise

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def track_categorization(
    transaction_id: str,
    category: Optional[str] = None,
    confidence: Optional[float] = None,
    metadata: Optional[Dict[str, Any]] = None,
) -> Callable:
    """
    Decorator to track categorization operations with detailed metrics.

    Args:
        transaction_id: ID of the transaction being categorized
        category: Category assigned (if available)
        confidence: Confidence score (if available)
        metadata: Additional metadata to track

    Returns:
        Decorator function
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                elapsed_ms = (time.time() - start_time) * 1000

                # Log categorization metrics
                logger.info(
                    f"Categorization: transaction={transaction_id}, "
                    f"category={category or 'N/A'}, "
                    f"confidence={confidence or 'N/A'}, "
                    f"elapsed_ms={elapsed_ms:.2f}, "
                    f"metadata={metadata or {}}"
                )

                return result
            except Exception as e:
                elapsed_ms = (time.time() - start_time) * 1000
                logger.error(
                    f"Categorization failed: transaction={transaction_id}, "
                    f"elapsed_ms={elapsed_ms:.2f}, error={str(e)}"
                )
                raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                elapsed_ms = (time.time() - start_time) * 1000

                # Log categorization metrics
                logger.info(
                    f"Categorization: transaction={transaction_id}, "
                    f"category={category or 'N/A'}, "
                    f"confidence={confidence or 'N/A'}, "
                    f"elapsed_ms={elapsed_ms:.2f}, "
                    f"metadata={metadata or {}}"
                )

                return result
            except Exception as e:
                elapsed_ms = (time.time() - start_time) * 1000
                logger.error(
                    f"Categorization failed: transaction={transaction_id}, "
                    f"elapsed_ms={elapsed_ms:.2f}, error={str(e)}"
                )
                raise

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def track_file_upload(file_name: str, file_size: int, upload_type: str = "production"):
    """
    Decorator for tracking file upload operations.

    Args:
        file_name: Name of the file being uploaded
        file_size: Size of the file in bytes
        upload_type: Type of upload (production, historical, schema, etc.)
    """

    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            error = None

            # Log file upload start
            logger.info(
                "File upload started",
                extra={
                    "operation": "file_upload",
                    "file_name": file_name,
                    "file_size": file_size,
                    "upload_type": upload_type,
                    "func_name": func.__name__,
                },
            )

            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                error = str(e)
                logger.error(
                    f"File upload failed: {error}",
                    extra={
                        "operation": "file_upload",
                        "file_name": file_name,
                        "file_size": file_size,
                        "upload_type": upload_type,
                        "func_name": func.__name__,
                        "error": error,
                    },
                )
                raise
            finally:
                duration = time.time() - start_time

                # Track upload performance
                performance_monitor.record_system_metric(
                    metric_name=f"file_upload_{upload_type}",
                    value=duration * 1000,  # Convert to milliseconds
                    unit="ms",
                )

                # Log additional metadata separately
                logger.info(
                    "File upload metrics tracked",
                    extra={
                        "file_name": file_name,
                        "file_size": file_size,
                        "upload_type": upload_type,
                        "duration_ms": duration * 1000,
                        "success": error is None,
                        "error": error,
                    },
                )

                logger.info(
                    f"File upload completed in {duration:.2f}s",
                    extra={
                        "operation": "file_upload",
                        "file_name": file_name,
                        "file_size": file_size,
                        "upload_type": upload_type,
                        "duration": duration,
                        "success": error is None,
                        "func_name": func.__name__,
                    },
                )

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For sync functions, just pass through
            return func(*args, **kwargs)

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


class FileUploadTracker:
    """Context manager for tracking file upload operations."""

    def __init__(self, file_name: str, file_size: int, upload_type: str = "production"):
        self.file_name = file_name
        self.file_size = file_size
        self.upload_type = upload_type
        self.start_time = None
        self.error = None

    def __enter__(self):
        self.start_time = time.time()
        logger.info(
            "File upload started",
            extra={
                "operation": "file_upload",
                "file_name": self.file_name,
                "file_size": self.file_size,
                "upload_type": self.upload_type,
            },
        )
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time

        if exc_type is not None:
            self.error = str(exc_val)
            logger.error(
                f"File upload failed: {self.error}",
                extra={
                    "operation": "file_upload",
                    "file_name": self.file_name,
                    "file_size": self.file_size,
                    "upload_type": self.upload_type,
                    "error": self.error,
                    "duration": duration,
                },
            )

        # Track upload performance
        performance_monitor.record_system_metric(
            metric_name=f"file_upload_{self.upload_type}",
            value=duration * 1000,  # Convert to milliseconds
            unit="ms",
        )

        # Log additional metadata separately
        logger.info(
            "File upload metrics tracked",
            extra={
                "file_name": self.file_name,
                "file_size": self.file_size,
                "upload_type": self.upload_type,
                "duration_ms": duration * 1000,
                "success": self.error is None,
                "error": self.error,
            },
        )

        logger.info(
            f"File upload completed in {duration:.2f}s",
            extra={
                "operation": "file_upload",
                "file_name": self.file_name,
                "file_size": self.file_size,
                "upload_type": self.upload_type,
                "duration": duration,
                "success": self.error is None,
            },
        )

        # Don't suppress exceptions
        return False


# Import and re-export the performance monitor instance
from ..services.system.performance_monitor import performance_monitor

__all__ = [
    "track_performance",
    "track_categorization",
    "track_file_upload",
    "FileUploadTracker",
    "performance_monitor",
]
