"""
Google Cloud Secret Manager integration for environment-aware configuration.
"""

import os
from typing import Optional

try:
    from google.cloud import secretmanager
    from google.oauth2 import service_account

    GOOGLE_CLOUD_AVAILABLE = True
except ImportError:
    GOOGLE_CLOUD_AVAILABLE = False
    secretmanager = None
    service_account = None


class GoogleCloudSecretsManager:
    """Manages secrets from Google Cloud Secret Manager with environment awareness."""

    def __init__(self, project_id: Optional[str] = None):
        if not GOOGLE_CLOUD_AVAILABLE:
            raise ImportError(
                "Google Cloud Secret Manager is not available. "
                "Install with: pip install google-cloud-secret-manager"
            )

        self.project_id = project_id or os.getenv("VERTEX_PROJECT_ID", "rezolve-poc")
        self.environment = os.getenv("ENVIRONMENT", "development").lower()

        # Initialize client with proper authentication
        self.client = self._initialize_client()

    def _initialize_client(self) -> secretmanager.SecretManagerServiceClient:
        """Initialize Secret Manager client with proper authentication."""
        credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")

        if credentials_path and os.path.exists(credentials_path):
            # Use service account key file
            credentials = service_account.Credentials.from_service_account_file(
                credentials_path
            )
            return secretmanager.SecretManagerServiceClient(credentials=credentials)
        else:
            # Use default credentials (for Cloud Run)
            return secretmanager.SecretManagerServiceClient()

    def get_secret(self, secret_name: str, version: str = "latest") -> Optional[str]:
        """
        Retrieve a secret from Google Cloud Secret Manager.

        Args:
            secret_name: Base name of the secret (will be prefixed with environment)
            version: Version of the secret to retrieve

        Returns:
            Secret value as string, or None if not found
        """
        try:
            # Construct environment-aware secret name
            if secret_name.startswith("giki-ai-"):
                # Already has prefix, use as-is
                full_secret_name = secret_name
            else:
                # Add environment prefix
                full_secret_name = f"giki-ai-{self.environment}-{secret_name}"

            # Construct the resource name
            name = f"projects/{self.project_id}/secrets/{full_secret_name}/versions/{version}"

            # Access the secret version
            response = self.client.access_secret_version(request={"name": name})

            # Return the decoded payload
            return response.payload.data.decode("UTF-8")

        except Exception as e:
            print(f"Warning: Failed to retrieve secret '{full_secret_name}': {e}")
            return None

    def get_database_url(self) -> Optional[str]:
        """Get environment-specific database URL from Secret Manager."""
        return self.get_secret("database-url")

    def get_auth_secret_key(self) -> Optional[str]:
        """Get environment-specific auth secret key from Secret Manager."""
        return self.get_secret("auth-secret-key")

    def get_secret_key(self) -> Optional[str]:
        """Get environment-specific application secret key from Secret Manager."""
        return self.get_secret("secret-key")

    def get_admin_api_key(self) -> Optional[str]:
        """Get environment-specific admin API key from Secret Manager."""
        return self.get_secret("admin-api-key")

    def get_cors_origins(self) -> Optional[str]:
        """Get environment-specific CORS origins from Secret Manager."""
        return self.get_secret("cors-origins")

    def get_gemini_api_key(self) -> Optional[str]:
        """Get environment-specific Gemini API key from Secret Manager."""
        return self.get_secret("gemini-api-key")


def get_secrets_manager() -> GoogleCloudSecretsManager:
    """Factory function to get a configured secrets manager instance."""
    return GoogleCloudSecretsManager()


def get_secret_with_fallback(
    secret_name: str, env_var_name: str = None
) -> Optional[str]:
    """
    Get a secret from Secret Manager with environment variable fallback.

    Args:
        secret_name: Name of the secret in Secret Manager
        env_var_name: Environment variable name to use as fallback

    Returns:
        Secret value or None if not found
    """
    # Try environment variable first (faster)
    if env_var_name:
        env_value = os.getenv(env_var_name)
        if env_value:
            return env_value

    # Fall back to Secret Manager
    if GOOGLE_CLOUD_AVAILABLE:
        try:
            secrets_manager = get_secrets_manager()
            return secrets_manager.get_secret(secret_name)
        except Exception as e:
            print(f"Warning: Secret Manager access failed for '{secret_name}': {e}")

    return None
