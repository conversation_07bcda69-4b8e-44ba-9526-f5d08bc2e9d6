"""
Google Cloud Logging Integration
===============================

Provides structured logging using Google Cloud Logging,
replacing basic Python logging with cloud-native logging.

Key Features:
- Structured JSON logging for cloud environments
- Automatic log aggregation in Google Cloud Console
- Performance metrics and error tracking
- Production-ready logging configuration
"""

import logging
import os
import sys
from datetime import datetime
from typing import Any, Dict, Optional

try:
    from google.cloud import logging as cloud_logging
    from google.cloud.logging.handlers import CloudLoggingHandler

    GOOGLE_CLOUD_LOGGING_AVAILABLE = True
except ImportError:
    GOOGLE_CLOUD_LOGGING_AVAILABLE = False
    cloud_logging = None
    CloudLoggingHandler = None


class GoogleCloudLoggingSetup:
    """
    Google Cloud Logging integration for enterprise-grade logging.

    Provides structured logging with automatic cloud aggregation.
    """

    def __init__(
        self, project_id: Optional[str] = None, service_name: str = "giki-ai-api"
    ):
        self.project_id = project_id or os.getenv("VERTEX_PROJECT_ID", "rezolve-poc")
        self.service_name = service_name
        self.cloud_client = None
        self._setup_complete = False

        if GOOGLE_CLOUD_LOGGING_AVAILABLE:
            try:
                self.cloud_client = cloud_logging.Client(project=self.project_id)
                self._setup_cloud_logging()
            except Exception as e:
                print(f"WARNING: Failed to initialize Google Cloud Logging: {e}")
                print("Falling back to standard Python logging")
                self._setup_standard_logging()
        else:
            print("google-cloud-logging not installed, using standard Python logging")
            self._setup_standard_logging()

    def _setup_cloud_logging(self):
        """Set up Google Cloud Logging with structured format."""
        try:
            # Connect Python logging to Google Cloud Logging
            self.cloud_client.setup_logging()

            # Create custom handler for application logs
            cloud_handler = CloudLoggingHandler(
                self.cloud_client,
                name=self.service_name,
                resource=cloud_logging.Resource(
                    type="gae_app",  # Google App Engine resource type
                    labels={
                        "project_id": self.project_id,
                        "service": self.service_name,
                        "version": "v1",
                    },
                ),
            )

            # Set up root logger
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.INFO)
            root_logger.addHandler(cloud_handler)

            # Configure application loggers
            app_logger = logging.getLogger("giki_ai_api")
            app_logger.setLevel(logging.INFO)

            # Add structured formatter for local development
            if os.getenv("DEBUG", "false").lower() in ("true", "1", "yes"):
                console_handler = logging.StreamHandler(sys.stdout)
                console_formatter = logging.Formatter(
                    "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S",
                )
                console_handler.setFormatter(console_formatter)
                app_logger.addHandler(console_handler)

            self._setup_complete = True
            print(f"Google Cloud Logging initialized for project: {self.project_id}")

        except Exception as e:
            print(f"Failed to setup Google Cloud Logging: {e}")
            self._setup_standard_logging()

    def _setup_standard_logging(self):
        """Fallback to standard Python logging with structured format."""
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)

        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Create console handler with structured format
        console_handler = logging.StreamHandler(sys.stdout)

        # Structured formatter for better log parsing
        formatter = logging.Formatter(
            "%(asctime)s [%(levelname)s] %(name)s %(filename)s:%(lineno)d - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        console_handler.setFormatter(formatter)

        # Add handler to root logger
        root_logger.addHandler(console_handler)

        # Configure application logger
        app_logger = logging.getLogger("giki_ai_api")
        app_logger.setLevel(logging.INFO)

        # In debug mode, ensure app logger has console handler
        if os.getenv("DEBUG", "").lower() in ("true", "1", "yes"):
            app_logger.addHandler(console_handler)
            app_logger.setLevel(logging.DEBUG)

        self._setup_complete = True
        print("Standard Python logging configured with structured format")

    def log_performance_metric(
        self,
        operation: str,
        duration_ms: float,
        status: str = "success",
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        Log performance metrics in structured format.

        Args:
            operation: Name of the operation (e.g., "api_request", "database_query")
            duration_ms: Duration in milliseconds
            status: Operation status ("success", "error", "timeout")
            metadata: Additional context data
        """
        logger = logging.getLogger("giki_ai_api.performance")

        metric_data = {
            "metric_type": "performance",
            "operation": operation,
            "duration_ms": duration_ms,
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "service": self.service_name,
            "project": self.project_id,
        }

        if metadata:
            metric_data.update(metadata)

        # Log at appropriate level based on performance
        if duration_ms > 5000:  # > 5 seconds
            logger.warning(f"SLOW_OPERATION: {operation}", extra=metric_data)
        elif duration_ms > 1000:  # > 1 second
            logger.info(f"PERFORMANCE: {operation}", extra=metric_data)
        else:
            logger.debug(f"PERFORMANCE: {operation}", extra=metric_data)

    def log_security_event(
        self, event_type: str, severity: str, details: Dict[str, Any]
    ):
        """
        Log security events for monitoring and alerting.

        Args:
            event_type: Type of security event ("auth_failure", "suspicious_access", etc.)
            severity: Event severity ("low", "medium", "high", "critical")
            details: Event details and context
        """
        logger = logging.getLogger("giki_ai_api.security")

        security_data = {
            "event_type": "security",
            "security_event": event_type,
            "severity": severity,
            "timestamp": datetime.utcnow().isoformat(),
            "service": self.service_name,
            "project": self.project_id,
            **details,
        }

        # Log at appropriate level based on severity
        if severity == "critical":
            logger.critical(f"SECURITY_CRITICAL: {event_type}", extra=security_data)
        elif severity == "high":
            logger.error(f"SECURITY_HIGH: {event_type}", extra=security_data)
        elif severity == "medium":
            logger.warning(f"SECURITY_MEDIUM: {event_type}", extra=security_data)
        else:
            logger.info(f"SECURITY_LOW: {event_type}", extra=security_data)

    def log_business_event(
        self,
        event_type: str,
        tenant_id: Optional[int] = None,
        user_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        Log business events for analytics and monitoring.

        Args:
            event_type: Business event type ("file_uploaded", "transaction_categorized", etc.)
            tenant_id: Tenant ID for multi-tenant tracking
            user_id: User ID for user activity tracking
            metadata: Additional business context
        """
        logger = logging.getLogger("giki_ai_api.business")

        business_data = {
            "event_type": "business",
            "business_event": event_type,
            "timestamp": datetime.utcnow().isoformat(),
            "service": self.service_name,
            "project": self.project_id,
        }

        if tenant_id:
            business_data["tenant_id"] = tenant_id
        if user_id:
            business_data["user_id"] = user_id
        if metadata:
            business_data.update(metadata)

        logger.info(f"BUSINESS_EVENT: {event_type}", extra=business_data)

    def log_ai_event(
        self,
        operation: str,
        model_used: str,
        confidence_score: Optional[float] = None,
        processing_time_ms: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        Log AI/ML operations for monitoring and analysis.

        Args:
            operation: AI operation ("categorization", "entity_extraction", etc.)
            model_used: AI model identifier ("gemini-2.0-flash", "vertex-ai-rag", etc.)
            confidence_score: AI confidence score (0.0-1.0)
            processing_time_ms: Processing time in milliseconds
            metadata: Additional AI context
        """
        logger = logging.getLogger("giki_ai_api.ai")

        ai_data = {
            "event_type": "ai_operation",
            "ai_operation": operation,
            "model_used": model_used,
            "timestamp": datetime.utcnow().isoformat(),
            "service": self.service_name,
            "project": self.project_id,
        }

        if confidence_score is not None:
            ai_data["confidence_score"] = confidence_score
        if processing_time_ms is not None:
            ai_data["processing_time_ms"] = processing_time_ms
        if metadata:
            ai_data.update(metadata)

        logger.info(f"AI_OPERATION: {operation}", extra=ai_data)

    def is_setup_complete(self) -> bool:
        """Check if logging setup completed successfully."""
        return self._setup_complete


# Global logging setup instance
_logging_setup: Optional[GoogleCloudLoggingSetup] = None


def setup_google_cloud_logging(
    project_id: Optional[str] = None, service_name: str = "giki-ai-api"
):
    """
    Initialize Google Cloud Logging for the application.

    Args:
        project_id: Google Cloud project ID
        service_name: Service name for logging identification
    """
    global _logging_setup
    if _logging_setup is None:
        _logging_setup = GoogleCloudLoggingSetup(project_id, service_name)
    return _logging_setup


def get_logging_setup() -> Optional[GoogleCloudLoggingSetup]:
    """Get the current logging setup instance."""
    return _logging_setup


def log_performance(
    operation: str, duration_ms: float, status: str = "success", **metadata
):
    """Convenience function to log performance metrics."""
    if _logging_setup:
        _logging_setup.log_performance_metric(operation, duration_ms, status, metadata)


def log_security(event_type: str, severity: str, **details):
    """Convenience function to log security events."""
    if _logging_setup:
        _logging_setup.log_security_event(event_type, severity, details)


def log_business(
    event_type: str,
    tenant_id: Optional[int] = None,
    user_id: Optional[int] = None,
    **metadata,
):
    """Convenience function to log business events."""
    if _logging_setup:
        _logging_setup.log_business_event(event_type, tenant_id, user_id, metadata)


def log_ai_operation(
    operation: str,
    model_used: str,
    confidence_score: Optional[float] = None,
    processing_time_ms: Optional[float] = None,
    **metadata,
):
    """Convenience function to log AI operations."""
    if _logging_setup:
        _logging_setup.log_ai_event(
            operation, model_used, confidence_score, processing_time_ms, metadata
        )
