"""
Production performance monitoring service with comprehensive metrics collection.
"""

import logging
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class MetricData:
    """Individual metric data point."""

    timestamp: float
    value: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EndpointStats:
    """Statistics for a specific endpoint."""

    total_requests: int = 0
    total_time_ms: float = 0.0
    min_time_ms: float = float("inf")
    max_time_ms: float = 0.0
    error_count: int = 0
    slow_requests: int = 0  # Requests over threshold

    @property
    def avg_time_ms(self) -> float:
        """Calculate average response time."""
        return (
            self.total_time_ms / self.total_requests if self.total_requests > 0 else 0.0
        )

    def update(
        self, response_time_ms: float, is_error: bool = False, is_slow: bool = False
    ):
        """Update statistics with new request data."""
        self.total_requests += 1
        self.total_time_ms += response_time_ms
        self.min_time_ms = min(self.min_time_ms, response_time_ms)
        self.max_time_ms = max(self.max_time_ms, response_time_ms)

        if is_error:
            self.error_count += 1
        if is_slow:
            self.slow_requests += 1


class PerformanceMonitor:
    """
    Comprehensive performance monitoring service for production environments.

    Features:
    - Real-time metrics collection
    - Endpoint-specific performance tracking
    - Database operation monitoring
    - System resource tracking
    - Configurable retention periods
    """

    def __init__(
        self,
        max_metrics_age_minutes: int = 60,
        slow_request_threshold_ms: float = 200.0,
    ):
        """
        Initialize performance monitor.

        Args:
            max_metrics_age_minutes: How long to retain metrics in memory
            slow_request_threshold_ms: Threshold for classifying requests as slow
        """
        self.max_metrics_age = max_metrics_age_minutes * 60  # Convert to seconds
        self.slow_threshold_ms = slow_request_threshold_ms

        # Metrics storage
        self.api_metrics: deque = deque()
        self.db_metrics: deque = deque()
        self.system_metrics: deque = deque()

        # Endpoint-specific statistics
        self.endpoint_stats: Dict[str, EndpointStats] = defaultdict(EndpointStats)

        # Real-time counters
        self.total_requests = 0
        self.slow_requests = 0
        self.error_requests = 0

        logger.info(
            f"Performance monitor initialized with {max_metrics_age_minutes}min retention"
        )

    def record_api_request(
        self,
        method: str,
        endpoint: str,
        response_time_ms: float,
        status_code: int,
        tenant_id: Optional[int] = None,
    ):
        """Record API request performance metrics."""
        now = time.time()
        is_error = status_code >= 400
        is_slow = response_time_ms > self.slow_threshold_ms

        # Update counters
        self.total_requests += 1
        if is_slow:
            self.slow_requests += 1
        if is_error:
            self.error_requests += 1

        # Store detailed metric
        metric = MetricData(
            timestamp=now,
            value=response_time_ms,
            metadata={
                "method": method,
                "endpoint": endpoint,
                "status_code": status_code,
                "tenant_id": tenant_id,
                "is_slow": is_slow,
                "is_error": is_error,
            },
        )
        self.api_metrics.append(metric)

        # Update endpoint-specific stats
        endpoint_key = f"{method} {endpoint}"
        self.endpoint_stats[endpoint_key].update(response_time_ms, is_error, is_slow)

        # Clean old metrics
        self._clean_old_metrics()

        # Log significant events
        if is_slow and not is_error:
            logger.warning(
                f"Slow request: {endpoint_key} took {response_time_ms:.2f}ms"
            )
        elif is_error:
            logger.error(
                f"Error request: {endpoint_key} returned {status_code} after {response_time_ms:.2f}ms"
            )

    def record_db_operation(
        self,
        operation: str,
        duration_ms: float,
        table: Optional[str] = None,
        tenant_id: Optional[int] = None,
    ):
        """Record database operation performance metrics."""
        now = time.time()
        is_slow = duration_ms > 500  # 500ms threshold for DB operations

        metric = MetricData(
            timestamp=now,
            value=duration_ms,
            metadata={
                "operation": operation,
                "table": table,
                "tenant_id": tenant_id,
                "is_slow": is_slow,
            },
        )
        self.db_metrics.append(metric)

        if is_slow:
            logger.warning(
                f"Slow DB operation: {operation} on {table} took {duration_ms:.2f}ms"
            )

    def record_system_metric(self, metric_name: str, value: float, unit: str = ""):
        """Record system-level performance metric."""
        now = time.time()

        metric = MetricData(
            timestamp=now,
            value=value,
            metadata={"metric_name": metric_name, "unit": unit},
        )
        self.system_metrics.append(metric)

    def track_operation(self, operation: str, **metadata):
        """
        Track an operation with context manager.

        This method provides backward compatibility for older code that expects
        a track_operation method returning a context manager.
        """

        class OperationTracker:
            def __init__(self, monitor, operation_name, metadata):
                self.monitor = monitor
                self.operation_name = operation_name
                self.metadata = metadata
                self.start_time = None

            def __enter__(self):
                self.start_time = time.time()
                return self

            def __exit__(self, exc_type, exc_val, exc_tb):
                if self.start_time:
                    duration_ms = (time.time() - self.start_time) * 1000
                    self.monitor.record_system_metric(
                        metric_name=f"operation_{self.operation_name}",
                        value=duration_ms,
                        unit="ms",
                    )

        return OperationTracker(self, operation, metadata)

    def get_performance_summary(self, minutes: int = 5) -> Dict[str, Any]:
        """
        Get performance summary for the specified time period.

        Args:
            minutes: Time period to analyze (default: 5 minutes)

        Returns:
            Comprehensive performance summary
        """
        cutoff_time = time.time() - (minutes * 60)

        # Filter recent API metrics
        recent_api = [m for m in self.api_metrics if m.timestamp >= cutoff_time]
        recent_db = [m for m in self.db_metrics if m.timestamp >= cutoff_time]

        # Calculate API metrics
        api_summary = self._calculate_api_summary(recent_api)
        db_summary = self._calculate_db_summary(recent_db)
        endpoint_summary = self._get_top_endpoints(minutes)

        return {
            "time_period_minutes": minutes,
            "timestamp": time.time(),
            "api_performance": api_summary,
            "database_performance": db_summary,
            "endpoint_performance": endpoint_summary,
            "overall_health": self._assess_health(api_summary, db_summary),
        }

    def _calculate_api_summary(self, metrics: List[MetricData]) -> Dict[str, Any]:
        """Calculate API performance summary from metrics."""
        if not metrics:
            return {"total_requests": 0, "message": "No API requests in time period"}

        response_times = [m.value for m in metrics]
        error_count = sum(1 for m in metrics if m.metadata.get("is_error", False))
        slow_count = sum(1 for m in metrics if m.metadata.get("is_slow", False))

        return {
            "total_requests": len(metrics),
            "avg_response_time_ms": sum(response_times) / len(response_times),
            "min_response_time_ms": min(response_times),
            "max_response_time_ms": max(response_times),
            "error_count": error_count,
            "error_rate_percent": (error_count / len(metrics)) * 100,
            "slow_requests": slow_count,
            "slow_request_rate_percent": (slow_count / len(metrics)) * 100,
            "requests_per_minute": len(metrics) / 5,  # Assuming 5-minute window
        }

    def _calculate_db_summary(self, metrics: List[MetricData]) -> Dict[str, Any]:
        """Calculate database performance summary from metrics."""
        if not metrics:
            return {"total_operations": 0, "message": "No DB operations in time period"}

        durations = [m.value for m in metrics]
        slow_count = sum(1 for m in metrics if m.metadata.get("is_slow", False))

        return {
            "total_operations": len(metrics),
            "avg_duration_ms": sum(durations) / len(durations),
            "min_duration_ms": min(durations),
            "max_duration_ms": max(durations),
            "slow_operations": slow_count,
            "slow_operation_rate_percent": (slow_count / len(metrics)) * 100,
        }

    def _get_top_endpoints(self, minutes: int) -> Dict[str, Any]:
        """Get top endpoints by various metrics."""
        # Sort endpoints by different criteria
        by_requests = sorted(
            self.endpoint_stats.items(), key=lambda x: x[1].total_requests, reverse=True
        )[:10]

        by_avg_time = sorted(
            self.endpoint_stats.items(), key=lambda x: x[1].avg_time_ms, reverse=True
        )[:10]

        by_errors = sorted(
            self.endpoint_stats.items(), key=lambda x: x[1].error_count, reverse=True
        )[:10]

        return {
            "top_by_requests": [
                {
                    "endpoint": endpoint,
                    "total_requests": stats.total_requests,
                    "avg_time_ms": stats.avg_time_ms,
                }
                for endpoint, stats in by_requests
                if stats.total_requests > 0
            ],
            "slowest_endpoints": [
                {
                    "endpoint": endpoint,
                    "avg_time_ms": stats.avg_time_ms,
                    "total_requests": stats.total_requests,
                }
                for endpoint, stats in by_avg_time
                if stats.total_requests > 0
            ],
            "most_errors": [
                {
                    "endpoint": endpoint,
                    "error_count": stats.error_count,
                    "total_requests": stats.total_requests,
                    "error_rate_percent": (stats.error_count / stats.total_requests)
                    * 100,
                }
                for endpoint, stats in by_errors
                if stats.error_count > 0
            ],
        }

    def _assess_health(
        self, api_summary: Dict[str, Any], db_summary: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Assess overall system health based on performance metrics."""
        health_score = 100
        issues = []

        # Check API performance
        if api_summary.get("avg_response_time_ms", 0) > 500:
            health_score -= 20
            issues.append("High average API response time")

        if api_summary.get("error_rate_percent", 0) > 5:
            health_score -= 30
            issues.append("High API error rate")

        if api_summary.get("slow_request_rate_percent", 0) > 10:
            health_score -= 15
            issues.append("High percentage of slow requests")

        # Check DB performance
        if db_summary.get("avg_duration_ms", 0) > 200:
            health_score -= 15
            issues.append("High average database operation time")

        if db_summary.get("slow_operation_rate_percent", 0) > 20:
            health_score -= 20
            issues.append("High percentage of slow database operations")

        # Determine status
        if health_score >= 90:
            status = "excellent"
        elif health_score >= 75:
            status = "good"
        elif health_score >= 60:
            status = "fair"
        elif health_score >= 40:
            status = "poor"
        else:
            status = "critical"

        return {
            "status": status,
            "score": max(0, health_score),
            "issues": issues,
            "recommendation": self._get_health_recommendation(status, issues),
        }

    def _get_health_recommendation(self, status: str, issues: List[str]) -> str:
        """Get health recommendation based on status and issues."""
        if status == "excellent":
            return "System performance is excellent. Continue monitoring."
        elif status == "good":
            return "System performance is good. Monitor for trends."
        elif status == "fair":
            return "Performance degradation detected. Review recent changes and consider optimization."
        elif status == "poor":
            return "Significant performance issues detected. Immediate investigation recommended."
        else:
            return "Critical performance issues detected. Immediate action required."

    def _clean_old_metrics(self):
        """Remove metrics older than the retention period."""
        cutoff_time = time.time() - self.max_metrics_age

        # Clean API metrics
        while self.api_metrics and self.api_metrics[0].timestamp < cutoff_time:
            self.api_metrics.popleft()

        # Clean DB metrics
        while self.db_metrics and self.db_metrics[0].timestamp < cutoff_time:
            self.db_metrics.popleft()

        # Clean system metrics
        while self.system_metrics and self.system_metrics[0].timestamp < cutoff_time:
            self.system_metrics.popleft()

    def reset_all_metrics(self):
        """Reset all metrics (use with caution in production)."""
        self.api_metrics.clear()
        self.db_metrics.clear()
        self.system_metrics.clear()
        self.endpoint_stats.clear()

        self.total_requests = 0
        self.slow_requests = 0
        self.error_requests = 0

        logger.warning("All performance metrics have been reset")

    def get_current_status(self) -> Dict[str, Any]:
        """Get current real-time status."""
        return {
            "timestamp": time.time(),
            "total_requests_lifetime": self.total_requests,
            "slow_requests_lifetime": self.slow_requests,
            "error_requests_lifetime": self.error_requests,
            "metrics_in_memory": {
                "api_metrics": len(self.api_metrics),
                "db_metrics": len(self.db_metrics),
                "system_metrics": len(self.system_metrics),
            },
            "tracked_endpoints": len(self.endpoint_stats),
            "memory_usage_mb": self._estimate_memory_usage(),
        }

    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage of performance monitor in MB."""
        # Rough estimation based on data structures
        api_size = len(self.api_metrics) * 200  # Bytes per metric
        db_size = len(self.db_metrics) * 150
        system_size = len(self.system_metrics) * 100
        endpoint_size = len(self.endpoint_stats) * 300

        total_bytes = api_size + db_size + system_size + endpoint_size
        return total_bytes / (1024 * 1024)  # Convert to MB


# Global performance monitor instance
performance_monitor = PerformanceMonitor()

# Export for easy access
__all__ = ["performance_monitor", "PerformanceMonitor", "MetricData", "EndpointStats"]
