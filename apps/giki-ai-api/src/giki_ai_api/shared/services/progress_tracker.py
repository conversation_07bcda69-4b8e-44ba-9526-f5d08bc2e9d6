"""
Progress tracking service for real-time updates during file processing.

This service manages progress updates that can be streamed to clients via SSE.
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class ProgressUpdate:
    """Single progress update event."""

    stage: str
    message: str
    progress: float  # 0.0 to 1.0
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None


@dataclass
class ProcessingStatus:
    """Overall processing status for a task."""

    task_id: str
    status: str  # "pending", "processing", "completed", "failed"
    total_stages: int
    current_stage: int
    progress: float  # Overall progress 0.0 to 1.0
    updates: List[ProgressUpdate] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    completed_at: Optional[float] = None
    error: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


class ProgressTracker:
    """
    Manages progress tracking for async tasks.

    This is an in-memory implementation. For production, consider using Redis.
    """

    def __init__(self):
        self._tasks: Dict[str, ProcessingStatus] = {}
        self._subscribers: Dict[str, List[asyncio.Queue]] = {}
        self._lock = asyncio.Lock()
        self._cleanup_task: Optional[asyncio.Task] = None
        self._ttl_seconds = 3600  # Keep completed tasks for 1 hour

    async def start(self):
        """Start the progress tracker with cleanup task."""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("Progress tracker started")

    async def stop(self):
        """Stop the progress tracker."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
            logger.info("Progress tracker stopped")

    async def create_task(
        self, task_id: str, total_stages: int = 1
    ) -> ProcessingStatus:
        """Create a new task for tracking."""
        async with self._lock:
            status = ProcessingStatus(
                task_id=task_id,
                status="pending",
                total_stages=total_stages,
                current_stage=0,
                progress=0.0,
            )
            self._tasks[task_id] = status
            logger.info(f"Created task {task_id} with {total_stages} stages")
            return status

    async def update_progress(
        self,
        task_id: str,
        stage: str,
        message: str,
        progress: float,
        metadata: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
    ):
        """Update task progress and notify subscribers."""
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"Task {task_id} not found")
                return

            task = self._tasks[task_id]

            # Create update
            update = ProgressUpdate(
                stage=stage,
                message=message,
                progress=progress,
                metadata=metadata or {},
                error=error,
            )

            # Update task status
            task.updates.append(update)
            task.progress = progress

            if error:
                task.status = "failed"
                task.error = error
                task.completed_at = time.time()
            elif progress >= 1.0:
                task.status = "completed"
                task.completed_at = time.time()
            else:
                task.status = "processing"

            # Notify subscribers
            if task_id in self._subscribers:
                for queue in self._subscribers[task_id]:
                    try:
                        await queue.put(update)
                    except asyncio.QueueFull:
                        logger.warning(f"Queue full for task {task_id}")

    async def set_stage(
        self, task_id: str, stage_number: int, stage_name: str, message: str
    ):
        """Update to a specific stage with automatic progress calculation."""
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"Task {task_id} not found")
                return

            task = self._tasks[task_id]
            task.current_stage = stage_number

            # Calculate overall progress based on stages
            progress = stage_number / task.total_stages

        # Update progress (releases lock)
        await self.update_progress(
            task_id=task_id, stage=stage_name, message=message, progress=progress
        )

    async def complete_task(
        self, task_id: str, result: Optional[Dict[str, Any]] = None
    ):
        """Mark task as completed."""
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"Task {task_id} not found")
                return

            task = self._tasks[task_id]
            task.status = "completed"
            task.progress = 1.0
            task.completed_at = time.time()
            task.result = result

        # Send final update
        await self.update_progress(
            task_id=task_id,
            stage="completed",
            message="Processing completed successfully",
            progress=1.0,
            metadata=result or {},
        )

    async def fail_task(self, task_id: str, error: str):
        """Mark task as failed."""
        await self.update_progress(
            task_id=task_id,
            stage="failed",
            message="Processing failed",
            progress=self._tasks.get(
                task_id, ProcessingStatus(task_id, "failed", 1, 0, 0)
            ).progress,
            error=error,
        )

    async def get_status(self, task_id: str) -> Optional[ProcessingStatus]:
        """Get current status of a task."""
        async with self._lock:
            return self._tasks.get(task_id)

    async def subscribe(self, task_id: str) -> asyncio.Queue:
        """Subscribe to updates for a task."""
        async with self._lock:
            if task_id not in self._subscribers:
                self._subscribers[task_id] = []

            queue = asyncio.Queue(maxsize=100)
            self._subscribers[task_id].append(queue)

            # Send current status if task exists
            if task_id in self._tasks:
                task = self._tasks[task_id]
                for update in task.updates[-10:]:  # Last 10 updates
                    try:
                        await queue.put(update)
                    except asyncio.QueueFull:
                        break

            return queue

    async def unsubscribe(self, task_id: str, queue: asyncio.Queue):
        """Unsubscribe from task updates."""
        async with self._lock:
            if task_id in self._subscribers:
                try:
                    self._subscribers[task_id].remove(queue)
                    if not self._subscribers[task_id]:
                        del self._subscribers[task_id]
                except ValueError:
                    pass

    async def _cleanup_loop(self):
        """Background task to clean up old completed tasks."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes

                async with self._lock:
                    current_time = time.time()
                    tasks_to_remove = []

                    for task_id, task in self._tasks.items():
                        if (
                            task.completed_at
                            and (current_time - task.completed_at) > self._ttl_seconds
                        ):
                            tasks_to_remove.append(task_id)

                    for task_id in tasks_to_remove:
                        del self._tasks[task_id]
                        if task_id in self._subscribers:
                            del self._subscribers[task_id]

                    if tasks_to_remove:
                        logger.info(f"Cleaned up {len(tasks_to_remove)} old tasks")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")


# Global progress tracker instance
progress_tracker = ProgressTracker()


# Context manager for tracking progress
class track_progress:
    """Context manager for automatic progress tracking."""

    def __init__(
        self, task_id: str, total_stages: int = 1, stage_name: str = "Processing"
    ):
        self.task_id = task_id
        self.total_stages = total_stages
        self.stage_name = stage_name
        self.stage_number = 0

    async def __aenter__(self):
        await progress_tracker.create_task(self.task_id, self.total_stages)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            await progress_tracker.fail_task(self.task_id, str(exc_val))
        else:
            await progress_tracker.complete_task(self.task_id)

    async def update_stage(self, stage_name: str, message: str):
        """Move to next stage."""
        self.stage_number += 1
        await progress_tracker.set_stage(
            self.task_id, self.stage_number, stage_name, message
        )

    async def update(
        self, message: str, progress: float, metadata: Optional[Dict[str, Any]] = None
    ):
        """Update progress within current stage."""
        await progress_tracker.update_progress(
            self.task_id, self.stage_name, message, progress, metadata
        )
