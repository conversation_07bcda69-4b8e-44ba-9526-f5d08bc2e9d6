"""
Production Monitoring and Alerting System for giki.ai

This module provides comprehensive monitoring capabilities including:
- Application health monitoring
- Performance metrics collection
- Error tracking and alerting
- Resource utilization monitoring
- Business metrics tracking
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional

import asyncpg
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class AlertSeverity(str, Enum):
    """Alert severity levels"""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class MetricType(str, Enum):
    """Types of metrics we collect"""

    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class Metric:
    """Represents a system metric"""

    name: str
    value: float
    metric_type: MetricType
    timestamp: datetime = field(default_factory=datetime.utcnow)
    tags: Dict[str, str] = field(default_factory=dict)
    description: str = ""


@dataclass
class Alert:
    """Represents a system alert"""

    id: str
    title: str
    description: str
    severity: AlertSeverity
    timestamp: datetime = field(default_factory=datetime.utcnow)
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    tags: Dict[str, str] = field(default_factory=dict)


class HealthStatus(BaseModel):
    """Health check response model"""

    status: str
    timestamp: datetime
    service: str
    version: str = "1.0.0"
    checks: Dict[str, Dict[str, Any]]
    metrics: Dict[str, float]


class MonitoringService:
    """
    Comprehensive monitoring service for production applications.

    Features:
    - Real-time health monitoring
    - Performance metrics collection
    - Automatic alerting based on thresholds
    - Business metrics tracking
    - Resource utilization monitoring
    """

    def __init__(self, db_pool: Optional[asyncpg.Pool] = None):
        self.db_pool = db_pool
        self.metrics: List[Metric] = []
        self.alerts: List[Alert] = []
        self._health_checks = {}
        self._performance_counters = {
            "requests_total": 0,
            "requests_failed": 0,
            "response_time_sum": 0.0,
            "response_time_count": 0,
            "ai_categorizations_total": 0,
            "ai_categorizations_failed": 0,
            "file_uploads_total": 0,
            "file_uploads_failed": 0,
        }

        # Alert thresholds
        self._alert_thresholds = {
            "response_time_p95": 2000,  # 2 seconds
            "error_rate": 0.05,  # 5%
            "ai_error_rate": 0.10,  # 10%
            "database_connection_pool_usage": 0.80,  # 80%
            "memory_usage": 0.85,  # 85%
            "cpu_usage": 0.80,  # 80%
        }

    async def initialize(self):
        """Initialize monitoring service"""
        logger.info("Initializing monitoring service...")

        # Register default health checks
        await self._register_default_health_checks()

        # Start background monitoring tasks
        asyncio.create_task(self._periodic_health_check())
        asyncio.create_task(self._periodic_metrics_collection())

        logger.info("Monitoring service initialized successfully")

    async def _register_default_health_checks(self):
        """Register default health checks"""

        # Database health check
        self._health_checks["database"] = self._check_database_health

        # Memory health check
        self._health_checks["memory"] = self._check_memory_health

        # AI services health check
        self._health_checks["ai_services"] = self._check_ai_services_health

        # File system health check
        self._health_checks["filesystem"] = self._check_filesystem_health

    async def get_health_status(self) -> HealthStatus:
        """Get comprehensive health status"""
        checks = {}
        overall_status = "healthy"

        for check_name, check_func in self._health_checks.items():
            try:
                result = await check_func()
                checks[check_name] = result

                if result["status"] != "healthy":
                    overall_status = "unhealthy"

            except Exception as e:
                checks[check_name] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat(),
                }
                overall_status = "unhealthy"

        # Calculate performance metrics
        metrics = self._calculate_performance_metrics()

        return HealthStatus(
            status=overall_status,
            timestamp=datetime.utcnow(),
            service="giki-ai-api",
            checks=checks,
            metrics=metrics,
        )

    async def _check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            if not self.db_pool:
                return {
                    "status": "healthy",
                    "message": "Database pool not initialized",
                    "timestamp": datetime.utcnow().isoformat(),
                }

            start_time = time.time()

            # Test database connection
            async with self.db_pool.acquire() as conn:
                await conn.fetchval("SELECT 1")

            response_time = (time.time() - start_time) * 1000  # ms

            # Check connection pool usage
            pool_size = self.db_pool.get_size()
            active_connections = pool_size - self.db_pool.get_idle_size()
            pool_usage = active_connections / pool_size if pool_size > 0 else 0

            status = "healthy"
            if response_time > 1000:  # 1 second
                status = "degraded"
            if pool_usage > 0.8:
                status = "degraded"

            return {
                "status": status,
                "response_time_ms": response_time,
                "pool_size": pool_size,
                "active_connections": active_connections,
                "pool_usage_percent": pool_usage * 100,
                "timestamp": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }

    async def _check_memory_health(self) -> Dict[str, Any]:
        """Check memory usage"""
        try:
            import psutil

            memory = psutil.virtual_memory()
            memory_usage = memory.percent / 100

            status = "healthy"
            if memory_usage > 0.85:
                status = "critical"
            elif memory_usage > 0.75:
                status = "degraded"

            return {
                "status": status,
                "memory_usage_percent": memory_usage * 100,
                "available_mb": memory.available / 1024 / 1024,
                "total_mb": memory.total / 1024 / 1024,
                "timestamp": datetime.utcnow().isoformat(),
            }

        except ImportError:
            return {
                "status": "unknown",
                "message": "psutil not available",
                "timestamp": datetime.utcnow().isoformat(),
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }

    async def _check_ai_services_health(self) -> Dict[str, Any]:
        """Check AI services connectivity"""
        try:
            # Simple connectivity check to Vertex AI
            # We'll just verify the configuration is valid
            import os

            vertex_project = os.getenv("VERTEX_PROJECT_ID")
            vertex_location = os.getenv("VERTEX_LOCATION")
            google_creds = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")

            status = "healthy"
            issues = []

            if not vertex_project:
                issues.append("VERTEX_PROJECT_ID not set")
                status = "unhealthy"

            if not vertex_location:
                issues.append("VERTEX_LOCATION not set")
                status = "unhealthy"

            if not google_creds or not os.path.exists(google_creds):
                issues.append("GOOGLE_APPLICATION_CREDENTIALS not valid")
                status = "unhealthy"

            return {
                "status": status,
                "vertex_project": vertex_project,
                "vertex_location": vertex_location,
                "credentials_available": bool(
                    google_creds and os.path.exists(google_creds)
                ),
                "issues": issues,
                "timestamp": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }

    async def _check_filesystem_health(self) -> Dict[str, Any]:
        """Check filesystem health"""
        try:
            import shutil

            disk_usage = shutil.disk_usage("/")
            total = disk_usage.total
            used = disk_usage.used
            free = disk_usage.free

            usage_percent = (used / total) * 100

            status = "healthy"
            if usage_percent > 90:
                status = "critical"
            elif usage_percent > 80:
                status = "degraded"

            return {
                "status": status,
                "disk_usage_percent": usage_percent,
                "free_gb": free / 1024 / 1024 / 1024,
                "total_gb": total / 1024 / 1024 / 1024,
                "timestamp": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }

    def record_request(self, success: bool = True, response_time: float = 0.0):
        """Record API request metrics"""
        self._performance_counters["requests_total"] += 1
        if not success:
            self._performance_counters["requests_failed"] += 1

        if response_time > 0:
            self._performance_counters["response_time_sum"] += response_time
            self._performance_counters["response_time_count"] += 1

    def record_ai_categorization(self, success: bool = True):
        """Record AI categorization metrics"""
        self._performance_counters["ai_categorizations_total"] += 1
        if not success:
            self._performance_counters["ai_categorizations_failed"] += 1

    def record_file_upload(self, success: bool = True):
        """Record file upload metrics"""
        self._performance_counters["file_uploads_total"] += 1
        if not success:
            self._performance_counters["file_uploads_failed"] += 1

    def _calculate_performance_metrics(self) -> Dict[str, float]:
        """Calculate performance metrics from counters"""
        metrics = {}

        # Request metrics
        total_requests = self._performance_counters["requests_total"]
        failed_requests = self._performance_counters["requests_failed"]

        if total_requests > 0:
            metrics["error_rate"] = failed_requests / total_requests
            metrics["success_rate"] = 1 - metrics["error_rate"]
        else:
            metrics["error_rate"] = 0.0
            metrics["success_rate"] = 1.0

        # Response time metrics
        response_time_count = self._performance_counters["response_time_count"]
        if response_time_count > 0:
            metrics["avg_response_time_ms"] = (
                self._performance_counters["response_time_sum"] / response_time_count
            )
        else:
            metrics["avg_response_time_ms"] = 0.0

        # AI metrics
        total_ai = self._performance_counters["ai_categorizations_total"]
        failed_ai = self._performance_counters["ai_categorizations_failed"]

        if total_ai > 0:
            metrics["ai_error_rate"] = failed_ai / total_ai
            metrics["ai_success_rate"] = 1 - metrics["ai_error_rate"]
        else:
            metrics["ai_error_rate"] = 0.0
            metrics["ai_success_rate"] = 1.0

        # File upload metrics
        total_uploads = self._performance_counters["file_uploads_total"]
        failed_uploads = self._performance_counters["file_uploads_failed"]

        if total_uploads > 0:
            metrics["upload_error_rate"] = failed_uploads / total_uploads
            metrics["upload_success_rate"] = 1 - metrics["upload_error_rate"]
        else:
            metrics["upload_error_rate"] = 0.0
            metrics["upload_success_rate"] = 1.0

        return metrics

    async def check_alert_thresholds(self):
        """Check if any metrics exceed alert thresholds"""
        metrics = self._calculate_performance_metrics()
        health_status = await self.get_health_status()

        alerts = []

        # Check error rate
        if metrics.get("error_rate", 0) > self._alert_thresholds["error_rate"]:
            alerts.append(
                Alert(
                    id=f"error_rate_{int(time.time())}",
                    title="High Error Rate Detected",
                    description=f"Error rate is {metrics['error_rate']:.1%}, threshold is {self._alert_thresholds['error_rate']:.1%}",
                    severity=AlertSeverity.HIGH,
                    tags={"metric": "error_rate", "value": str(metrics["error_rate"])},
                )
            )

        # Check AI error rate
        if metrics.get("ai_error_rate", 0) > self._alert_thresholds["ai_error_rate"]:
            alerts.append(
                Alert(
                    id=f"ai_error_rate_{int(time.time())}",
                    title="High AI Error Rate Detected",
                    description=f"AI error rate is {metrics['ai_error_rate']:.1%}, threshold is {self._alert_thresholds['ai_error_rate']:.1%}",
                    severity=AlertSeverity.HIGH,
                    tags={
                        "metric": "ai_error_rate",
                        "value": str(metrics["ai_error_rate"]),
                    },
                )
            )

        # Check average response time
        if (
            metrics.get("avg_response_time_ms", 0)
            > self._alert_thresholds["response_time_p95"]
        ):
            alerts.append(
                Alert(
                    id=f"response_time_{int(time.time())}",
                    title="High Response Time Detected",
                    description=f"Average response time is {metrics['avg_response_time_ms']:.0f}ms, threshold is {self._alert_thresholds['response_time_p95']}ms",
                    severity=AlertSeverity.MEDIUM,
                    tags={
                        "metric": "response_time",
                        "value": str(metrics["avg_response_time_ms"]),
                    },
                )
            )

        # Check database health
        db_check = health_status.checks.get("database", {})
        if db_check.get("status") == "unhealthy":
            alerts.append(
                Alert(
                    id=f"database_{int(time.time())}",
                    title="Database Health Critical",
                    description="Database connectivity issues detected",
                    severity=AlertSeverity.CRITICAL,
                    tags={"component": "database"},
                )
            )

        # Check memory usage
        memory_check = health_status.checks.get("memory", {})
        memory_usage = memory_check.get("memory_usage_percent", 0) / 100
        if memory_usage > self._alert_thresholds["memory_usage"]:
            alerts.append(
                Alert(
                    id=f"memory_{int(time.time())}",
                    title="High Memory Usage",
                    description=f"Memory usage is {memory_usage:.1%}, threshold is {self._alert_thresholds['memory_usage']:.1%}",
                    severity=AlertSeverity.HIGH,
                    tags={"metric": "memory_usage", "value": str(memory_usage)},
                )
            )

        # Store alerts
        for alert in alerts:
            self.alerts.append(alert)
            logger.warning(f"ALERT: {alert.title} - {alert.description}")

        return alerts

    async def _periodic_health_check(self):
        """Run periodic health checks"""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                await self.check_alert_thresholds()
            except Exception as e:
                logger.error(f"Error in periodic health check: {e}")

    async def _periodic_metrics_collection(self):
        """Collect metrics periodically"""
        while True:
            try:
                await asyncio.sleep(60)  # Collect every minute

                # Collect current metrics
                health_status = await self.get_health_status()
                timestamp = datetime.utcnow()

                # Store metrics as Metric objects
                for metric_name, value in health_status.metrics.items():
                    metric = Metric(
                        name=metric_name,
                        value=value,
                        metric_type=MetricType.GAUGE,
                        timestamp=timestamp,
                        tags={"service": "giki-ai-api"},
                    )
                    self.metrics.append(metric)

                # Keep only last 1000 metrics to prevent memory issues
                if len(self.metrics) > 1000:
                    self.metrics = self.metrics[-1000:]

            except Exception as e:
                logger.error(f"Error in periodic metrics collection: {e}")

    def get_recent_metrics(self, hours: int = 24) -> List[Metric]:
        """Get metrics from the last N hours"""
        cutoff = datetime.utcnow() - timedelta(hours=hours)
        return [m for m in self.metrics if m.timestamp >= cutoff]

    def get_active_alerts(self) -> List[Alert]:
        """Get all active (unresolved) alerts"""
        return [a for a in self.alerts if not a.resolved]

    def resolve_alert(self, alert_id: str) -> bool:
        """Mark an alert as resolved"""
        for alert in self.alerts:
            if alert.id == alert_id and not alert.resolved:
                alert.resolved = True
                alert.resolution_time = datetime.utcnow()
                logger.info(f"Alert resolved: {alert.title}")
                return True
        return False


# Global monitoring service instance
monitoring_service: Optional[MonitoringService] = None


def get_monitoring_service() -> MonitoringService:
    """Get the global monitoring service instance"""
    global monitoring_service
    if monitoring_service is None:
        monitoring_service = MonitoringService()
    return monitoring_service


async def initialize_monitoring(db_pool: Optional[asyncpg.Pool] = None):
    """Initialize the global monitoring service"""
    global monitoring_service
    monitoring_service = MonitoringService(db_pool)
    await monitoring_service.initialize()
