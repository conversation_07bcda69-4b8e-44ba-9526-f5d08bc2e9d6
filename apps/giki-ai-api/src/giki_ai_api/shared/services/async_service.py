"""
Enhanced Async Performance Service

This module implements advanced asyncio patterns for high-performance backend operations:
- aiofiles for async file I/O
- asyncio.Semaphore for advanced rate limiting
- asyncio.gather for concurrent operations
- asyncio.Queue for background task processing
- Advanced async orchestration patterns

Replaces primitive synchronous operations with enterprise-grade async patterns.
"""

import asyncio
import json
import logging
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union

import aiofiles
import aiofiles.os

logger = logging.getLogger(__name__)


@dataclass
class AsyncTaskResult:
    """Result from async task execution."""

    task_id: str
    success: bool
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: float = 0.0


class EnhancedAsyncService:
    """
    Enterprise-grade async service with advanced asyncio patterns.

    Features:
    - Async file I/O with aiofiles
    - Semaphore-based rate limiting
    - Concurrent batch processing
    - Background task queues
    - Thread pool integration
    """

    def __init__(
        self,
        max_concurrent_files: int = 10,
        max_concurrent_operations: int = 20,
        background_workers: int = 5,
    ):
        # Advanced asyncio patterns
        self.file_semaphore = asyncio.Semaphore(max_concurrent_files)
        self.operation_semaphore = asyncio.Semaphore(max_concurrent_operations)
        self.background_queue: asyncio.Queue = asyncio.Queue(maxsize=1000)

        # Thread pool for CPU-bound operations
        self.thread_pool = ThreadPoolExecutor(max_workers=background_workers)

        # Background worker tasks
        self._workers: List[asyncio.Task] = []
        self._running = False

        logger.info(
            f"EnhancedAsyncService initialized with {max_concurrent_files} file slots, "
            f"{max_concurrent_operations} operation slots, {background_workers} workers"
        )

    async def start_background_workers(self):
        """Start background worker tasks for queue processing."""
        if self._running:
            return

        self._running = True

        # Start background workers
        for i in range(len(self._workers), 5):  # Default 5 workers
            worker_task = asyncio.create_task(self._background_worker(f"worker-{i}"))
            self._workers.append(worker_task)

        logger.info(f"Started {len(self._workers)} background workers")

    async def stop_background_workers(self):
        """Stop background worker tasks."""
        self._running = False

        # Cancel all workers
        for worker in self._workers:
            worker.cancel()

        # Wait for cancellation
        if self._workers:
            await asyncio.gather(*self._workers, return_exceptions=True)

        self._workers.clear()
        logger.info("Stopped all background workers")

    async def _background_worker(self, worker_name: str):
        """Background worker that processes queued tasks."""
        logger.info(f"Background worker {worker_name} started")

        try:
            while self._running:
                try:
                    # Get task from queue with timeout
                    task_func, args, kwargs, result_future = await asyncio.wait_for(
                        self.background_queue.get(), timeout=1.0
                    )

                    try:
                        # Execute task
                        if asyncio.iscoroutinefunction(task_func):
                            result = await task_func(*args, **kwargs)
                        else:
                            # Run in thread pool for sync functions
                            result = await asyncio.get_event_loop().run_in_executor(
                                self.thread_pool, task_func, *args, **kwargs
                            )

                        result_future.set_result(result)

                    except Exception as e:
                        result_future.set_exception(e)

                    # Mark task as done
                    self.background_queue.task_done()

                except asyncio.TimeoutError:
                    # No task available, continue loop
                    continue
                except Exception as e:
                    logger.error(f"Background worker {worker_name} error: {e}")

        except asyncio.CancelledError:
            logger.info(f"Background worker {worker_name} cancelled")
            raise

    async def read_file_async(self, file_path: Union[str, Path]) -> str:
        """Read file content asynchronously with semaphore control."""
        async with self.file_semaphore:
            try:
                async with aiofiles.open(file_path, mode="r", encoding="utf-8") as file:
                    content = await file.read()
                    logger.debug(f"Read file {file_path} ({len(content)} chars)")
                    return content
            except Exception as e:
                logger.error(f"Failed to read file {file_path}: {e}")
                raise

    async def write_file_async(self, file_path: Union[str, Path], content: str) -> bool:
        """Write file content asynchronously with semaphore control."""
        async with self.file_semaphore:
            try:
                # Ensure directory exists
                path = Path(file_path)
                path.parent.mkdir(parents=True, exist_ok=True)

                async with aiofiles.open(file_path, mode="w", encoding="utf-8") as file:
                    await file.write(content)
                    logger.debug(f"Wrote file {file_path} ({len(content)} chars)")
                    return True
            except Exception as e:
                logger.error(f"Failed to write file {file_path}: {e}")
                raise

    async def read_json_file_async(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Read JSON file asynchronously with semaphore control."""
        content = await self.read_file_async(file_path)
        try:
            return json.loads(content)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from {file_path}: {e}")
            raise

    async def write_json_file_async(
        self, file_path: Union[str, Path], data: Dict[str, Any]
    ) -> bool:
        """Write JSON file asynchronously with semaphore control."""
        content = json.dumps(data, indent=2, ensure_ascii=False)
        return await self.write_file_async(file_path, content)

    async def process_files_concurrently(
        self,
        file_paths: List[Union[str, Path]],
        processor_func: Callable[[str], Any],
        max_concurrent: Optional[int] = None,
    ) -> List[AsyncTaskResult]:
        """Process multiple files concurrently using asyncio.gather."""

        # Use custom semaphore if specified
        semaphore = (
            asyncio.Semaphore(max_concurrent)
            if max_concurrent
            else self.operation_semaphore
        )

        async def process_single_file(file_path: Union[str, Path]) -> AsyncTaskResult:
            """Process a single file with semaphore control."""
            task_id = f"file_{Path(file_path).name}"
            start_time = time.time()

            async with semaphore:
                try:
                    # Read file content
                    content = await self.read_file_async(file_path)

                    # Process content (run processor in thread pool if it's sync)
                    if asyncio.iscoroutinefunction(processor_func):
                        result = await processor_func(content)
                    else:
                        result = await asyncio.get_event_loop().run_in_executor(
                            self.thread_pool, processor_func, content
                        )

                    execution_time = time.time() - start_time

                    return AsyncTaskResult(
                        task_id=task_id,
                        success=True,
                        result=result,
                        execution_time=execution_time,
                    )

                except Exception as e:
                    execution_time = time.time() - start_time
                    logger.error(f"Failed to process file {file_path}: {e}")

                    return AsyncTaskResult(
                        task_id=task_id,
                        success=False,
                        error=str(e),
                        execution_time=execution_time,
                    )

        # Process all files concurrently using asyncio.gather
        logger.info(f"Processing {len(file_paths)} files concurrently")
        start_time = time.time()

        results = await asyncio.gather(
            *[process_single_file(path) for path in file_paths], return_exceptions=True
        )

        total_time = time.time() - start_time
        successful_results = [
            r for r in results if isinstance(r, AsyncTaskResult) and r.success
        ]

        logger.info(
            f"Processed {len(file_paths)} files in {total_time:.2f}s "
            f"({len(successful_results)} successful)"
        )

        return results

    async def queue_background_task(self, task_func: Callable, *args, **kwargs) -> Any:
        """Queue a task for background processing."""
        if not self._running:
            await self.start_background_workers()

        # Create future for result
        result_future = asyncio.Future()

        # Add task to queue
        await self.background_queue.put((task_func, args, kwargs, result_future))

        # Wait for result
        return await result_future

    async def batch_process_with_rate_limiting(
        self,
        tasks: List[Callable],
        max_concurrent: int = 5,
        delay_between_batches: float = 0.1,
    ) -> List[AsyncTaskResult]:
        """Process tasks in batches with rate limiting using Semaphore."""

        rate_limit_semaphore = asyncio.Semaphore(max_concurrent)

        async def rate_limited_task(
            task_func: Callable, task_id: str
        ) -> AsyncTaskResult:
            """Execute task with rate limiting."""
            async with rate_limit_semaphore:
                start_time = time.time()

                try:
                    if asyncio.iscoroutinefunction(task_func):
                        result = await task_func()
                    else:
                        result = await asyncio.get_event_loop().run_in_executor(
                            self.thread_pool, task_func
                        )

                    execution_time = time.time() - start_time

                    return AsyncTaskResult(
                        task_id=task_id,
                        success=True,
                        result=result,
                        execution_time=execution_time,
                    )

                except Exception as e:
                    execution_time = time.time() - start_time

                    return AsyncTaskResult(
                        task_id=task_id,
                        success=False,
                        error=str(e),
                        execution_time=execution_time,
                    )
                finally:
                    # Add delay between tasks for rate limiting
                    if delay_between_batches > 0:
                        await asyncio.sleep(delay_between_batches)

        # Create task coroutines
        task_coroutines = [
            rate_limited_task(task, f"task_{i}") for i, task in enumerate(tasks)
        ]

        # Execute all tasks concurrently with rate limiting
        logger.info(
            f"Processing {len(tasks)} tasks with rate limiting "
            f"(max_concurrent={max_concurrent})"
        )

        results = await asyncio.gather(*task_coroutines, return_exceptions=True)

        successful_tasks = [
            r for r in results if isinstance(r, AsyncTaskResult) and r.success
        ]
        logger.info(
            f"Completed {len(successful_tasks)}/{len(tasks)} tasks successfully"
        )

        return results

    async def monitor_file_changes(
        self,
        directory: Union[str, Path],
        callback: Callable[[str], Any],
        poll_interval: float = 1.0,
    ):
        """Monitor directory for file changes (simple polling implementation)."""
        directory = Path(directory)
        last_scan = {}

        logger.info(f"Monitoring directory {directory} for changes")

        try:
            while self._running:
                try:
                    current_files = {}

                    # Scan directory
                    if directory.exists():
                        for file_path in directory.rglob("*"):
                            if file_path.is_file():
                                stat = await aiofiles.os.stat(file_path)
                                current_files[str(file_path)] = stat.st_mtime

                    # Check for changes
                    for file_path, mtime in current_files.items():
                        if file_path not in last_scan or last_scan[file_path] != mtime:
                            logger.debug(f"File changed: {file_path}")

                            # Queue callback for background processing
                            await self.queue_background_task(callback, file_path)

                    last_scan = current_files

                except Exception as e:
                    logger.error(f"Error monitoring directory {directory}: {e}")

                # Wait before next scan
                await asyncio.sleep(poll_interval)

        except asyncio.CancelledError:
            logger.info(f"File monitoring for {directory} cancelled")
            raise

    async def cleanup(self):
        """Cleanup resources."""
        await self.stop_background_workers()
        self.thread_pool.shutdown(wait=True)
        logger.info("EnhancedAsyncService cleanup completed")


# Global service instance
_async_service: Optional[EnhancedAsyncService] = None


def get_async_service() -> EnhancedAsyncService:
    """Get or create enhanced async service singleton."""
    global _async_service
    if _async_service is None:
        _async_service = EnhancedAsyncService()
    return _async_service


async def startup_async_service():
    """Startup async service with background workers."""
    service = get_async_service()
    await service.start_background_workers()
    logger.info("Enhanced async service started")


async def shutdown_async_service():
    """Shutdown async service and cleanup resources."""
    global _async_service
    if _async_service:
        await _async_service.cleanup()
        _async_service = None
    logger.info("Enhanced async service shutdown")


# Convenience functions for common operations
async def read_files_async(file_paths: List[Union[str, Path]]) -> List[str]:
    """Read multiple files asynchronously."""
    service = get_async_service()

    async def read_single(path):
        return await service.read_file_async(path)

    tasks = [read_single(path) for path in file_paths]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Handle exceptions
    contents = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Failed to read {file_paths[i]}: {result}")
            contents.append("")
        else:
            contents.append(result)

    return contents


async def process_files_batch(
    file_paths: List[Union[str, Path]],
    processor: Callable[[str], Any],
    max_concurrent: int = 10,
) -> List[AsyncTaskResult]:
    """Process files in batch with concurrency control."""
    service = get_async_service()
    return await service.process_files_concurrently(
        file_paths, processor, max_concurrent
    )
