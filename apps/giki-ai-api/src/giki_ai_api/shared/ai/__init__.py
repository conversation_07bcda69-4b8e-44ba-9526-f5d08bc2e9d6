"""
AI services for giki.ai platform.

This module contains all AI-powered services using Google Agent Development Kit (ADK).
All AI functionality MUST use ADK framework - no primitive AI implementations allowed.

Services:
- UnifiedAIService: Consolidated AI operations (categorization, insights, analytics, hierarchy inference)
- VertexAIClient: Base Vertex AI client for ADK integration

ADK Requirements:
- All AI features implemented as proper agents within ADK framework
- Use Vertex AI credentials through ADK only
- Agent-first design for all AI functionality
- No direct LLM prompting or basic function calling

Consolidated Services:
- Replaces: CategorizationService, HierarchyInferenceService, LLMCategorizer
- All functionality moved to UnifiedAIService for true consolidation
"""

# Temporarily disabled due to import issues with old structure
# from .unified_ai import UnifiedAIService
from .standard_giki_agent import StandardGikiAgent, create_enhanced_function_tool
from .vertex_client import VertexAIClient

__all__ = ["VertexAIClient", "StandardGikiAgent", "create_enhanced_function_tool"]
