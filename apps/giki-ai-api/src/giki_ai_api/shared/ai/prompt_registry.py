"""
Prompt Registry V2 - Modular prompt management for giki.ai
==========================================================

This module provides a centralized system for managing, versioning, and tracking
AI prompts across the application. Prompts are now stored in separate modules
for better maintainability and organization.

Key features:
- Modular prompt storage by category
- Version control for prompts
- Performance tracking
- A/B testing capabilities
- Easy review and modification
- Integration preparation for Langfuse

Future: This will integrate with Langfuse for production prompt management.
"""

import hashlib
import json
import logging
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class PromptCategory(Enum):
    """Categories for organizing prompts."""

    SCHEMA_INTERPRETATION = "schema_interpretation"
    CATEGORIZATION = "categorization"
    DEBIT_CREDIT_INFERENCE = "debit_credit_inference"
    REGIONAL_DETECTION = "regional_detection"
    HIERARCHY_DETECTION = "hierarchy_detection"
    CORRECTION_SUGGESTION = "correction_suggestion"
    TRANSACTION_EXTRACTION = "transaction_extraction"
    AI_JUDGE = "ai_judge"
    BUSINESS_APPROPRIATENESS = "business_appropriateness"
    ACCOUNTING = "accounting"


class PromptVersion:
    """Represents a versioned prompt with metadata."""

    def __init__(
        self,
        id: str,
        category: PromptCategory,
        name: str,
        template: str,
        version: str,
        variables: List[str],
        model_config: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
    ):
        self.id = id
        self.category = category
        self.name = name
        self.template = template
        self.version = version
        self.variables = variables
        self.model_config = model_config
        self.metadata = metadata or {}
        self.created_at = datetime.now(timezone.utc)
        self.hash = self._calculate_hash()

    def _calculate_hash(self) -> str:
        """Calculate hash of prompt content for change detection."""
        content = f"{self.template}{json.dumps(self.model_config, sort_keys=True)}"
        return hashlib.sha256(content.encode()).hexdigest()[:12]

    def format(self, **kwargs) -> str:
        """Format prompt with provided variables."""
        missing_vars = set(self.variables) - set(kwargs.keys())
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")

        return self.template.format(**kwargs)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/serialization."""
        return {
            "id": self.id,
            "category": self.category.value,
            "name": self.name,
            "template": self.template,
            "version": self.version,
            "variables": self.variables,
            "model_config": self.model_config,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "hash": self.hash,
        }


class PromptRegistry:
    """
    Centralized registry for all AI prompts in giki.ai.

    This provides a single source of truth for all prompts, making it easy to:
    - Review and modify prompts
    - Track performance metrics
    - Version control prompts
    - Prepare for Langfuse integration
    
    Prompts are now loaded from modular files in the prompts/ directory.
    """

    def __init__(self):
        self._prompts: Dict[str, Dict[str, PromptVersion]] = {}
        self._performance_metrics: Dict[str, List[Dict[str, Any]]] = {}
        self._initialize_prompts()

    def _initialize_prompts(self):
        """Initialize prompts from modular prompt files."""
        # Import all prompts from the prompts package
        from .prompts import ALL_PROMPTS
        
        # Register all prompts
        for _prompt_id, prompt_version in ALL_PROMPTS.items():
            self.register(prompt_version)
        
        logger.info(f"Initialized {len(self._prompts)} prompts from modular files")

    def register(self, prompt: PromptVersion) -> None:
        """Register a new prompt version."""
        if prompt.id not in self._prompts:
            self._prompts[prompt.id] = {}

        self._prompts[prompt.id][prompt.version] = prompt
        logger.debug(f"Registered prompt: {prompt.id} v{prompt.version}")

    def get(self, prompt_id: str, version: Optional[str] = None) -> PromptVersion:
        """Get a prompt by ID and optional version."""
        if prompt_id not in self._prompts:
            raise ValueError(f"Prompt '{prompt_id}' not found in registry")

        versions = self._prompts[prompt_id]

        if version:
            if version not in versions:
                raise ValueError(
                    f"Version '{version}' not found for prompt '{prompt_id}'"
                )
            return versions[version]

        # Return latest version (highest version number)
        latest_version = max(versions.keys())
        return versions[latest_version]

    def list_prompts(self, category: Optional[PromptCategory] = None) -> List[str]:
        """List all prompt IDs, optionally filtered by category."""
        if category:
            return [
                prompt_id
                for prompt_id, versions in self._prompts.items()
                if any(p.category == category for p in versions.values())
            ]
        return list(self._prompts.keys())

    def get_versions(self, prompt_id: str) -> List[str]:
        """Get all versions of a prompt."""
        if prompt_id not in self._prompts:
            raise ValueError(f"Prompt '{prompt_id}' not found")
        return list(self._prompts[prompt_id].keys())

    def track_performance(
        self,
        prompt_id: str,
        version: str,
        success: bool,
        latency_ms: float,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Track performance metrics for a prompt."""
        if prompt_id not in self._performance_metrics:
            self._performance_metrics[prompt_id] = []

        metric = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "version": version,
            "success": success,
            "latency_ms": latency_ms,
            "metadata": metadata or {},
        }

        self._performance_metrics[prompt_id].append(metric)

        # Keep only last 1000 metrics per prompt to prevent unbounded growth
        if len(self._performance_metrics[prompt_id]) > 1000:
            self._performance_metrics[prompt_id] = self._performance_metrics[
                prompt_id
            ][-1000:]

    def get_performance_stats(
        self, prompt_id: str, version: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get performance statistics for a prompt."""
        if prompt_id not in self._performance_metrics:
            return {
                "total_calls": 0,
                "success_rate": 0.0,
                "avg_latency_ms": 0.0,
                "metrics": [],
            }

        metrics = self._performance_metrics[prompt_id]

        if version:
            metrics = [m for m in metrics if m["version"] == version]

        if not metrics:
            return {
                "total_calls": 0,
                "success_rate": 0.0,
                "avg_latency_ms": 0.0,
                "metrics": [],
            }

        total_calls = len(metrics)
        successful_calls = sum(1 for m in metrics if m["success"])
        total_latency = sum(m["latency_ms"] for m in metrics)

        return {
            "total_calls": total_calls,
            "success_rate": successful_calls / total_calls if total_calls > 0 else 0.0,
            "avg_latency_ms": total_latency / total_calls if total_calls > 0 else 0.0,
            "metrics": metrics[-10:],  # Last 10 metrics
        }

    def export_prompts(self) -> Dict[str, Any]:
        """Export all prompts for backup or migration."""
        export_data = {
            "export_timestamp": datetime.now(timezone.utc).isoformat(),
            "prompts": {},
            "performance_metrics": self._performance_metrics,
        }

        for prompt_id, versions in self._prompts.items():
            export_data["prompts"][prompt_id] = {
                version: prompt.to_dict() for version, prompt in versions.items()
            }

        return export_data

    def update_prompt(
        self,
        prompt_id: str,
        new_template: str,
        new_version: str,
        model_config_updates: Optional[Dict[str, Any]] = None,
    ) -> PromptVersion:
        """Update a prompt with a new version."""
        if prompt_id not in self._prompts:
            raise ValueError(f"Prompt '{prompt_id}' not found")

        # Get the latest version as base
        current_prompt = self.get(prompt_id)

        # Create new model config
        new_model_config = current_prompt.model_config.copy()
        if model_config_updates:
            new_model_config.update(model_config_updates)

        # Create new prompt version
        new_prompt = PromptVersion(
            id=prompt_id,
            category=current_prompt.category,
            name=current_prompt.name,
            template=new_template,
            version=new_version,
            variables=current_prompt.variables,  # Keep same variables
            model_config=new_model_config,
            metadata={
                **current_prompt.metadata,
                "updated_from": current_prompt.version,
                "updated_at": datetime.now(timezone.utc).isoformat(),
            },
        )

        # Register the new version
        self.register(new_prompt)

        return new_prompt


# Singleton instance
_prompt_registry_instance = None


def get_prompt_registry() -> PromptRegistry:
    """Get the singleton prompt registry instance."""
    global _prompt_registry_instance
    if _prompt_registry_instance is None:
        _prompt_registry_instance = PromptRegistry()
    return _prompt_registry_instance


# For backward compatibility during migration
get_prompt_registry = get_prompt_registry