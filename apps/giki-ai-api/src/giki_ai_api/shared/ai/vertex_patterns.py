"""
Advanced VertexAI Implementation Patterns

This module implements sophisticated VertexAI capabilities that replace primitive prompt engineering:
- Proper Tool/ToolConfig usage instead of basic FunctionTool
- Persistent ChatSession instead of stateless generate_content_async calls
- Structured FunctionDeclaration schemas instead of basic prompts
- Advanced conversation context management
- Enterprise-grade AI architecture patterns

Replaces: Basic GenerativeModel.generate_content_async() patterns throughout the codebase
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from vertexai.generative_models import (
    ChatSession,
    Content,
    FunctionDeclaration,
    GenerationConfig,
    GenerativeModel,
    Tool,
    ToolConfig,
)

logger = logging.getLogger(__name__)


@dataclass
class AdvancedToolConfig:
    """Configuration for sophisticated VertexAI tool usage."""

    function_calling_mode: str = "AUTO"  # AUTO, ANY, NONE
    allowed_function_names: Optional[List[str]] = None
    max_function_calls_per_turn: int = 5


class AdvancedVertexAIClient:
    """
    Enterprise-grade VertexAI client using proper Tool/ChatSession architecture.

    This replaces primitive prompt engineering with structured tool calling,
    persistent conversation sessions, and sophisticated context management.
    """

    def __init__(self, model_name: str = "gemini-2.0-flash-001"):
        self.model_name = model_name
        self.model = GenerativeModel(model_name)
        self._active_sessions: Dict[str, ChatSession] = {}
        self._session_tools: Dict[str, List[Tool]] = {}

    def create_function_declaration(
        self,
        name: str,
        description: str,
        parameters: Dict[str, Any],
        required: Optional[List[str]] = None,
    ) -> FunctionDeclaration:
        """Create proper FunctionDeclaration with schema validation."""

        return FunctionDeclaration(
            name=name,
            description=description,
            parameters={
                "type": "object",
                "properties": parameters,
                "required": required or [],
            },
        )

    def create_structured_tool(
        self, function_declarations: List[FunctionDeclaration]
    ) -> Tool:
        """Create sophisticated Tool from function declarations."""

        return Tool.from_function_declarations(function_declarations)

    def configure_tools(
        self, tools: List[Tool], config: AdvancedToolConfig = None
    ) -> ToolConfig:
        """Configure advanced tool behavior."""

        config = config or AdvancedToolConfig()

        return ToolConfig(
            function_calling_config=ToolConfig.FunctionCallingConfig(
                mode=config.function_calling_mode,
                allowed_function_names=config.allowed_function_names,
            )
        )

    async def start_persistent_session(
        self,
        session_id: str,
        tools: List[Tool],
        system_instruction: str,
        tool_config: Optional[ToolConfig] = None,
        history: Optional[List[Content]] = None,
    ) -> ChatSession:
        """Start sophisticated ChatSession with persistent context."""

        # Configure model with tools and system instruction
        configured_model = GenerativeModel(
            self.model_name,
            tools=tools,
            tool_config=tool_config,
            system_instruction=system_instruction,
        )

        # Start chat session with conversation history
        chat_session = configured_model.start_chat(
            history=history, response_validation=True
        )

        # Store session for reuse
        self._active_sessions[session_id] = chat_session
        self._session_tools[session_id] = tools

        logger.info(
            f"Started persistent ChatSession {session_id} with {len(tools)} tools"
        )

        return chat_session

    async def continue_conversation(
        self,
        session_id: str,
        message: str,
        generation_config: Optional[GenerationConfig] = None,
    ) -> Any:
        """Continue conversation in persistent session."""

        if session_id not in self._active_sessions:
            raise ValueError(f"Session {session_id} not found. Start session first.")

        chat_session = self._active_sessions[session_id]

        # Send message and get response with function calling support
        response = await chat_session.send_message_async(
            message, generation_config=generation_config
        )

        logger.debug(f"Session {session_id} conversation continued successfully")

        return response

    async def execute_structured_categorization(
        self,
        session_id: str,
        description: str,
        amount: float,
        cultural_context: str = "global",
    ) -> Dict[str, Any]:
        """
        Advanced categorization using structured tool calling.

        Replaces primitive prompt engineering with proper function declarations
        and persistent conversation context.
        """

        # Define categorization function schema
        categorize_function = self.create_function_declaration(
            name="categorize_transaction",
            description="Categorize financial transaction using advanced AI analysis",
            parameters={
                "category": {
                    "type": "string",
                    "description": "Primary transaction category",
                },
                "subcategory": {
                    "type": "string",
                    "description": "Detailed subcategory",
                },
                "confidence": {
                    "type": "number",
                    "description": "Confidence score 0.0-1.0",
                },
                "reasoning": {
                    "type": "string",
                    "description": "Explanation of categorization decision",
                },
                "cultural_adaptations": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Cultural context adaptations applied",
                },
            },
            required=["category", "confidence", "reasoning"],
        )

        # Create sophisticated tool
        categorization_tool = self.create_structured_tool([categorize_function])

        # Configure advanced tool behavior
        tool_config = self.configure_tools(
            [categorization_tool],
            AdvancedToolConfig(
                function_calling_mode="ANY",
                allowed_function_names=["categorize_transaction"],
                max_function_calls_per_turn=1,
            ),
        )

        # Start persistent session if not exists
        if session_id not in self._active_sessions:
            system_instruction = f"""
            You are an expert financial categorization AI with deep understanding of {cultural_context} financial patterns.
            
            Use the categorize_transaction function to provide structured, high-confidence categorization of financial transactions.
            Consider cultural context, merchant patterns, amount ranges, and transaction descriptions.
            
            Provide confidence scores and detailed reasoning for all categorizations.
            """

            await self.start_persistent_session(
                session_id=session_id,
                tools=[categorization_tool],
                system_instruction=system_instruction,
                tool_config=tool_config,
            )

        # Execute categorization with structured response
        message = f"""
        Categorize this transaction:
        Description: {description}
        Amount: ${amount:.2f}
        Cultural Context: {cultural_context}
        
        Use the categorize_transaction function to provide structured results.
        """

        response = await self.continue_conversation(
            session_id=session_id,
            message=message,
            generation_config=GenerationConfig(
                temperature=0.1, max_output_tokens=500, top_p=0.8
            ),
        )

        # Extract structured function call results
        return self._extract_function_response(response, "categorize_transaction")

    async def execute_structured_schema_interpretation(
        self,
        session_id: str,
        columns: List[str],
        sample_data: Dict[str, Any],
        filename: str,
    ) -> Dict[str, Any]:
        """
        Advanced schema interpretation using structured tool calling.

        Replaces primitive keyword matching with sophisticated function declarations
        and persistent conversation context.
        """

        # Define schema interpretation function
        interpret_schema_function = self.create_function_declaration(
            name="interpret_file_schema",
            description="Interpret financial file schema using advanced AI analysis",
            parameters={
                "field_mappings": {
                    "type": "object",
                    "properties": {
                        "date": {"type": "string"},
                        "description": {"type": "string"},
                        "amount": {"type": "string"},
                        "debit_amount": {"type": "string"},
                        "credit_amount": {"type": "string"},
                        "balance": {"type": "string"},
                        "category": {"type": "string"},
                        "account": {"type": "string"},
                    },
                    "description": "Mapping of standard fields to actual column names",
                },
                "confidence_scores": {
                    "type": "object",
                    "description": "Confidence for each field mapping (0.0-1.0)",
                },
                "file_type": {
                    "type": "string",
                    "description": "Detected file type (bank_statement, quickbooks_export, etc.)",
                },
                "cultural_context": {
                    "type": "string",
                    "description": "Detected cultural/regional context",
                },
                "recommendations": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Processing recommendations",
                },
            },
            required=["field_mappings", "confidence_scores", "file_type"],
        )

        # Create sophisticated tool
        schema_tool = self.create_structured_tool([interpret_schema_function])

        # Configure advanced tool behavior
        tool_config = self.configure_tools(
            [schema_tool],
            AdvancedToolConfig(
                function_calling_mode="ANY",
                allowed_function_names=["interpret_file_schema"],
            ),
        )

        # Start persistent session
        if session_id not in self._active_sessions:
            system_instruction = """
            You are an expert financial data schema interpreter with deep knowledge of:
            - Global banking formats (US, Indian, European, etc.)
            - Accounting software exports (QuickBooks, Xero, SAP, etc.)
            - Credit card statements and bank statements
            - Regional date/currency formats and naming conventions
            
            Use the interpret_file_schema function to provide structured, high-confidence schema interpretation.
            Analyze column names, sample data patterns, and cultural context clues.
            """

            await self.start_persistent_session(
                session_id=session_id,
                tools=[schema_tool],
                system_instruction=system_instruction,
                tool_config=tool_config,
            )

        # Format sample data for analysis
        sample_data_str = json.dumps(sample_data, indent=2, default=str)[:1000]

        message = f"""
        Interpret the schema for this financial file:
        
        Filename: {filename}
        Columns: {columns}
        
        Sample Data:
        {sample_data_str}
        
        Use the interpret_file_schema function to provide structured results with confidence scores.
        """

        response = await self.continue_conversation(
            session_id=session_id,
            message=message,
            generation_config=GenerationConfig(
                temperature=0.1, max_output_tokens=800, top_p=0.8
            ),
        )

        return self._extract_function_response(response, "interpret_file_schema")

    def _extract_function_response(
        self, response: Any, function_name: str
    ) -> Dict[str, Any]:
        """Extract structured function call results from response."""

        try:
            # Check if response contains function calls
            if hasattr(response, "candidates") and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, "content") and candidate.content.parts:
                    for part in candidate.content.parts:
                        if hasattr(part, "function_call") and part.function_call:
                            if part.function_call.name == function_name:
                                # Extract function arguments
                                args = {}
                                for key, value in part.function_call.args.items():
                                    args[key] = value
                                return args

            # Fallback: try to extract JSON from text response
            if hasattr(response, "text"):
                import re

                json_match = re.search(r"\{.*\}", response.text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())

            logger.warning(f"Could not extract function response for {function_name}")
            return {}

        except Exception as e:
            logger.error(f"Error extracting function response: {e}")
            return {}

    async def close_session(self, session_id: str):
        """Close persistent session and clean up resources."""

        if session_id in self._active_sessions:
            del self._active_sessions[session_id]
            del self._session_tools[session_id]
            logger.info(f"Closed session {session_id}")

    async def close_all_sessions(self):
        """Close all active sessions."""

        session_ids = list(self._active_sessions.keys())
        for session_id in session_ids:
            await self.close_session(session_id)

        logger.info(f"Closed {len(session_ids)} active sessions")


# Global advanced client instance
_advanced_vertex_client = None


def get_advanced_vertex_client() -> AdvancedVertexAIClient:
    """Get or create advanced VertexAI client singleton."""
    global _advanced_vertex_client
    if _advanced_vertex_client is None:
        _advanced_vertex_client = AdvancedVertexAIClient()
    return _advanced_vertex_client


async def migrate_from_primitive_patterns():
    """
    Migration utility to replace primitive patterns with advanced implementations.

    This function demonstrates how to replace:
    - GenerativeModel.generate_content_async() -> ChatSession.send_message_async()
    - Basic prompts -> Structured FunctionDeclarations
    - Stateless calls -> Persistent conversation sessions
    """

    client = get_advanced_vertex_client()

    logger.info(
        "🚀 Migrating from primitive VertexAI patterns to advanced architecture"
    )

    # Example: Replace primitive categorization
    result = await client.execute_structured_categorization(
        session_id="categorization_demo",
        description="AMAZON.COM PURCHASE $45.67",
        amount=45.67,
        cultural_context="us",
    )

    logger.info(f"✅ Advanced categorization result: {result}")

    # Example: Replace primitive schema interpretation
    schema_result = await client.execute_structured_schema_interpretation(
        session_id="schema_demo",
        columns=["Date", "Description", "Amount", "Balance"],
        sample_data={
            "Date": "2024-01-15",
            "Description": "ACH DEPOSIT SALARY",
            "Amount": "2500.00",
            "Balance": "3200.45",
        },
        filename="bank_statement.csv",
    )

    logger.info(f"✅ Advanced schema interpretation result: {schema_result}")

    # Clean up demo sessions
    await client.close_all_sessions()

    logger.info("🎯 Migration to advanced VertexAI patterns completed successfully")


if __name__ == "__main__":
    # Demo of advanced patterns
    asyncio.run(migrate_from_primitive_patterns())
