"""
Accounting Prompts
==================

Prompts for debit/credit inference, banking patterns analysis,
complex transaction handling, and multi-currency operations.
"""

from ..prompt_registry import PromptCategory, PromptVersion

ACCOUNTING_PROMPTS = {
    # Debit/Credit Agent Prompts
    "infer_transaction_direction": PromptVersion(
        id="infer_transaction_direction",
        category=PromptCategory.ACCOUNTING,
        name="Advanced Transaction Direction Inference",
        template="""You are an expert in advanced accounting principles and banking conventions.
Analyze this transaction to determine the correct debit/credit direction.

Transaction Data:
{transaction_data}

Account Context:
{account_context}

Regional Settings:
{regional_settings}

ADVANCED ACCOUNTING PRINCIPLES:

1. ACCOUNT TYPE RULES:
   - Assets: Debit increases, Credit decreases
   - Liabilities: Credit increases, Debit decreases  
   - Equity: Credit increases, Debit decreases
   - Revenue: Credit increases, Debit decreases
   - Expenses: Debit increases, Credit decreases

2. CASH FLOW PERSPECTIVE (Bank Account View):
   - Money IN: Credit (from bank's perspective, liability increase)
   - Money OUT: Debit (from bank's perspective, liability decrease)
   - But for customer: Money IN = Debit to Cash, Money OUT = Credit from Cash

3. TRANSACTION TYPE ANALYSIS:
   - Purchases/Payments: Usually Debits (expense increases)
   - Deposits/Income: Usually Credits (revenue increases)
   - Transfers: Depends on account relationship
   - Refunds: Opposite of original transaction
   - Fees: Usually Debits (expense increases)

4. DESCRIPTION PATTERNS:
   - "DEBIT", "WITHDRAWAL", "PAYMENT": Likely debit transactions
   - "CREDIT", "DEPOSIT", "REFUND": Likely credit transactions
   - "TRANSFER", "ACH": Need context analysis
   - "FEE", "CHARGE": Usually debit transactions

5. AMOUNT SIGN CONVENTIONS:
   - Negative amounts often indicate outflows (debits from cash)
   - Positive amounts often indicate inflows (credits to cash)
   - But this varies by bank and account type

6. REGIONAL VARIATIONS:
   - US: Standard accounting rules
   - UK: Similar but some terminology differences
   - India: Follows IFRS with local adaptations
   - Other: May have unique conventions

7. MERCHANT/VENDOR ANALYSIS:
   - Known vendors: Likely expense transactions (debits)
   - Government entities: Could be taxes (debits) or refunds (credits)
   - Banks: Could be fees (debits) or interest (credits)
   - Salary/payroll: Usually income (credits)

Return JSON format:
{{
    "inferred_direction": "debit|credit",
    "confidence": 0.95,
    "account_perspective": "customer|bank",
    "reasoning": {{
        "primary_factors": ["factor1", "factor2"],
        "account_type_analysis": "Based on asset account, outflow should be credit",
        "amount_sign_analysis": "Negative amount suggests outflow",
        "description_analysis": "Contains payment keyword indicating expense",
        "merchant_analysis": "Known vendor suggests purchase transaction",
        "regional_considerations": "Standard US accounting conventions apply"
    }},
    "alternative_possibilities": [
        {{
            "direction": "credit", 
            "probability": 0.05,
            "scenario": "If this were a refund instead of purchase"
        }}
    ],
    "validation_checks": {{
        "amount_direction_consistent": true,
        "description_amount_consistent": true,
        "account_type_logical": true,
        "regional_convention_followed": true
    }},
    "recommended_action": "apply_inference|request_clarification|flag_for_review"
}}

JSON:""",
        version="1.0.0",
        variables=[
            "transaction_data",
            "account_context",
            "regional_settings",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 1500,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Advanced debit/credit inference using accounting principles",
        },
    ),
    
    "analyze_banking_patterns": PromptVersion(
        id="analyze_banking_patterns",
        category=PromptCategory.ACCOUNTING,
        name="Regional Banking Pattern Analysis",
        template="""You are a banking systems expert specializing in regional variations.
Analyze these transactions to identify banking patterns and conventions.

Sample Transactions:
{transactions}

Bank Metadata:
{bank_metadata}

PATTERN ANALYSIS FOCUS:

1. TERMINOLOGY PATTERNS:
   - How are debits/credits indicated? (DR/CR, +/-, words)
   - Common transaction type keywords
   - Regional vocabulary differences
   - Currency formatting conventions

2. AMOUNT SIGN CONVENTIONS:
   - Positive = inflow or outflow?
   - Negative = inflow or outflow?
   - Are there explicit direction indicators?
   - Consistency across transaction types

3. DESCRIPTION FORMATTING:
   - Standard prefixes/suffixes
   - Merchant name formatting
   - Date/time inclusion patterns
   - Reference number patterns

4. REGIONAL BANKING CHARACTERISTICS:
   - US: ACH, Wire, Check patterns
   - UK: BACS, Faster Payments, Standing Order
   - India: NEFT, RTGS, UPI patterns
   - Other: Region-specific patterns

5. BANK-SPECIFIC CONVENTIONS:
   - Large banks vs regional banks
   - Credit unions vs commercial banks
   - Online banks vs traditional banks

6. TRANSACTION CATEGORIZATION HINTS:
   - Automatic categorization indicators
   - Merchant category codes (if present)
   - Internal bank categorization

Return JSON format:
{{
    "banking_region": "US|UK|India|Other",
    "bank_type": "commercial|credit_union|online|regional",
    "patterns_identified": {{
        "amount_sign_convention": {{
            "positive_means": "inflow|outflow",
            "negative_means": "inflow|outflow", 
            "confidence": 0.95,
            "evidence": ["pattern1", "pattern2"]
        }},
        "direction_indicators": {{
            "explicit_markers": ["DR", "CR", "DEBIT", "CREDIT"],
            "implicit_patterns": ["negative for outflow", "positive for inflow"],
            "consistency_score": 0.9
        }},
        "terminology": {{
            "debit_terms": ["debit", "withdrawal", "payment"],
            "credit_terms": ["credit", "deposit", "refund"],
            "neutral_terms": ["transfer", "ach", "wire"],
            "regional_variations": ["specific terms found"]
        }},
        "formatting": {{
            "description_pattern": "MERCHANT NAME LOCATION",
            "date_format": "MM/DD/YYYY|DD/MM/YYYY|YYYY-MM-DD",
            "currency_symbol": "$|£|₹|€",
            "decimal_separator": ".|,"
        }}
    }},
    "inference_rules": [
        {{
            "rule": "If amount < 0 and description contains 'PAYMENT', then debit",
            "confidence": 0.9,
            "applicability": "all_transactions|subset"
        }}
    ],
    "anomalies": [
        {{
            "transaction_index": 5,
            "anomaly_type": "inconsistent_sign_convention",
            "description": "Transaction breaks expected pattern"
        }}
    ],
    "recommendations": {{
        "apply_rules_automatically": true,
        "manual_review_needed": false,
        "confidence_threshold": 0.8,
        "special_handling": ["transfers", "refunds"]
    }}
}}

JSON:""",
        version="1.0.0",
        variables=[
            "transactions",
            "bank_metadata",
        ],
        model_config={
            "temperature": 0.2,
            "max_output_tokens": 2000,
            "top_p": 0.9,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Banking pattern analysis for regional variations",
        },
    ),
    
    "handle_complex_transactions": PromptVersion(
        id="handle_complex_transactions",
        category=PromptCategory.ACCOUNTING,
        name="Complex Transaction Analysis",
        template="""You are an expert in complex accounting transaction analysis.
Analyze this complex transaction scenario for proper debit/credit handling.

Primary Transaction:
{transaction}

Transaction Type: {transaction_type}

Context Transactions:
{context_transactions}

COMPLEX TRANSACTION TYPES:

1. SPLIT TRANSACTIONS:
   - Single payment split across multiple categories
   - Each split should sum to total amount
   - Maintain consistent debit/credit direction
   - Preserve transaction linkage

2. TRANSFER TRANSACTIONS:
   - Money movement between accounts
   - Debit from source, Credit to destination
   - Net effect should be zero across accounts
   - Handle fees as separate transactions

3. REVERSAL TRANSACTIONS:
   - Correction of previous transaction
   - Opposite direction of original
   - May be partial or full reversal
   - Maintain audit trail linkage

4. REFUND TRANSACTIONS:
   - Return of previous payment
   - Usually opposite of original transaction
   - May include processing fees
   - Timeline considerations important

5. FOREIGN EXCHANGE:
   - Currency conversion involved
   - May have exchange rate fees
   - Base currency vs foreign currency
   - Timing of conversion matters

6. RECURRING TRANSACTIONS:
   - Subscription payments
   - Loan payments (principal + interest)
   - Salary deposits
   - Consistent pattern expected

ANALYSIS REQUIREMENTS:
- Identify all component parts
- Determine individual debit/credit directions
- Calculate balancing entries
- Flag any inconsistencies
- Suggest proper accounting treatment

Return JSON format:
{{
    "complex_type": "{transaction_type}",
    "analysis": {{
        "total_amount": 1000.00,
        "currency": "USD",
        "component_count": 2,
        "requires_splitting": true,
        "balancing_required": true
    }},
    "components": [
        {{
            "description": "Primary transaction component",
            "amount": 750.00,
            "direction": "debit",
            "account_category": "expense",
            "reasoning": "Main purchase amount"
        }},
        {{
            "description": "Fee component", 
            "amount": 250.00,
            "direction": "debit",
            "account_category": "fee_expense",
            "reasoning": "Processing fee"
        }}
    ],
    "balancing_entries": [
        {{
            "description": "Cash reduction",
            "amount": 1000.00,
            "direction": "credit",
            "account_category": "cash",
            "reasoning": "Total cash outflow"
        }}
    ],
    "validation": {{
        "total_debits": 1000.00,
        "total_credits": 1000.00,
        "is_balanced": true,
        "consistency_check": true
    }},
    "special_handling": {{
        "requires_approval": false,
        "manual_review": false,
        "link_to_original": "transaction_id_if_reversal",
        "category_override": false
    }},
    "recommendations": [
        "Split into separate line items",
        "Maintain transaction linkage",
        "Apply standard expense categorization"
    ]
}}

JSON:""",
        version="1.0.0",
        variables=[
            "transaction",
            "transaction_type",
            "context_transactions",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 2000,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Complex transaction handling for splits, transfers, reversals",
        },
    ),
    
    "infer_multicurrency_direction": PromptVersion(
        id="infer_multicurrency_direction",
        category=PromptCategory.ACCOUNTING,
        name="Multi-Currency Transaction Direction",
        template="""You are an expert in international accounting and foreign exchange.
Analyze this multi-currency transaction for proper debit/credit treatment.

Transaction:
{transaction}

Exchange Rates:
{exchange_rates}

Base Currency: {base_currency}

MULTI-CURRENCY ACCOUNTING PRINCIPLES:

1. CURRENCY IDENTIFICATION:
   - Transaction currency vs base currency
   - Exchange rate at transaction date
   - Rate source and reliability

2. CONVERSION RULES:
   - Convert foreign currency to base currency
   - Record both amounts if significant
   - Track exchange rate used

3. DIRECTION DETERMINATION:
   - Apply standard debit/credit rules to base currency amount
   - Consider currency gain/loss implications
   - Handle rounding differences appropriately

4. EXCHANGE RATE EFFECTS:
   - Favorable vs unfavorable rates
   - Rate volatility considerations
   - Timing of conversion

5. DOCUMENTATION REQUIREMENTS:
   - Record original currency and amount
   - Document exchange rate used
   - Note conversion date
   - Flag significant rate differences

Return JSON format:
{{
    "currency_analysis": {{
        "transaction_currency": "EUR",
        "base_currency": "{base_currency}",
        "original_amount": 100.00,
        "converted_amount": 110.00,
        "exchange_rate": 1.10,
        "rate_source": "transaction_date|current|manual",
        "rate_reliability": "high|medium|low"
    }},
    "direction_inference": {{
        "recommended_direction": "debit|credit",
        "confidence": 0.95,
        "reasoning": "Based on converted amount and transaction nature",
        "currency_impact": "Exchange rate favorable to base currency"
    }},
    "accounting_entries": [
        {{
            "account": "Foreign Currency Expense",
            "currency": "EUR", 
            "original_amount": 100.00,
            "base_amount": 110.00,
            "direction": "debit"
        }},
        {{
            "account": "Cash",
            "currency": "{base_currency}",
            "base_amount": 110.00,
            "direction": "credit"
        }}
    ],
    "exchange_considerations": {{
        "rate_variance_threshold": 0.05,
        "requires_rate_documentation": true,
        "potential_gain_loss": 0.00,
        "rounding_adjustment": 0.00
    }},
    "validation": {{
        "conversion_accurate": true,
        "direction_consistent": true,
        "documentation_complete": true,
        "requires_review": false
    }}
}}

JSON:""",
        version="1.0.0",
        variables=[
            "transaction",
            "exchange_rates",
            "base_currency",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 1500,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Multi-currency transaction direction inference",
        },
    ),
    
    # Transaction Processing Debit/Credit Prompts
    "transaction_debit_credit_inference": PromptVersion(
        id="transaction_debit_credit_inference",
        category=PromptCategory.ACCOUNTING,
        name="Transaction Debit/Credit Inference",
        template="""You are an expert in double-entry accounting and banking systems.
Determine whether this transaction is a DEBIT or CREDIT based on accounting principles.

Transaction Details:
- Description: {transaction_description}
- Amount: {amount}
- Account Type: {account_type}
- Additional Context: {additional_context}

ACCOUNTING PRINCIPLES:
1. Double-Entry Rules:
   - Assets: Debit increases, Credit decreases
   - Liabilities: Credit increases, Debit decreases
   - Equity: Credit increases, Debit decreases
   - Revenue: Credit increases, Debit decreases
   - Expenses: Debit increases, Credit decreases

2. Common Banking Patterns:
   - Deposits/Income: Usually CREDIT (increases cash asset)
   - Withdrawals/Payments: Usually DEBIT (decreases cash asset)
   - Fees/Charges: DEBIT (expense)
   - Interest Earned: CREDIT (revenue)
   - Transfers: Context-dependent

3. Keywords Analysis:
   - CREDIT indicators: deposit, payment received, refund, credit, interest earned
   - DEBIT indicators: payment, purchase, withdrawal, fee, charge, debit

4. Amount Sign Convention:
   - Some systems use negative for debits, positive for credits
   - Others use opposite convention
   - Focus on transaction nature, not just sign

Return JSON format:
{{
    "transaction_type": "debit|credit",
    "confidence": 0.95,
    "reasoning": "Clear payment description indicates expense",
    "accounting_impact": {{
        "account_affected": "Cash/Bank Account",
        "effect": "decrease|increase",
        "double_entry": "Debit Expense, Credit Cash"
    }},
    "keywords_found": ["payment", "purchase"],
    "pattern_match": "expense_payment"
}}

JSON:""",
        version="1.0.0",
        variables=[
            "transaction_description",
            "amount",
            "account_type",
            "additional_context",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 800,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Transaction-level debit/credit inference",
        },
    ),
    
    "batch_debit_credit_inference": PromptVersion(
        id="batch_debit_credit_inference",
        category=PromptCategory.ACCOUNTING,
        name="Batch Debit/Credit Inference",
        template="""You are an expert in double-entry accounting. Analyze this batch of transactions
and determine debit/credit for each one using consistent accounting principles.

Transactions Batch:
{transactions_batch}
{additional_transactions_note}

Default Account Type: {account_type}

Custom Rules (if any):
{inference_rules}

BATCH PROCESSING GUIDELINES:
1. Apply consistent rules across all transactions
2. Look for patterns in similar transactions
3. Consider transaction relationships (e.g., matching debits/credits)
4. Flag any transactions that need manual review
5. Group similar transactions for efficiency

Return JSON format:
{{
    "batch_results": [
        {{
            "transaction_id": "txn_001",
            "description": "STARBUCKS PURCHASE",
            "amount": -5.50,
            "inferred_type": "debit",
            "confidence": 0.98,
            "pattern_group": "food_beverage_expense"
        }}
    ],
    "patterns_identified": {{
        "food_beverage_expense": {{
            "count": 15,
            "consistent_type": "debit",
            "keywords": ["STARBUCKS", "COFFEE", "CAFE"]
        }}
    }},
    "summary": {{
        "total_transactions": {total_transactions},
        "debits_count": 45,
        "credits_count": 30,
        "unclear_count": 5,
        "average_confidence": 0.92
    }},
    "manual_review_needed": []
}}

JSON:""",
        version="1.0.0",
        variables=[
            "transactions_batch",
            "additional_transactions_note",
            "account_type",
            "inference_rules",
            "total_transactions",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 3000,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Batch transaction debit/credit inference with pattern recognition",
        },
    ),
    
    "accounting_system_detection": PromptVersion(
        id="accounting_system_detection",
        category=PromptCategory.ACCOUNTING,
        name="Accounting System Detection from Data Structure",
        template="""Analyze this financial data structure and determine the accounting system type and cultural context.

Column Headers: {columns}
Sample Data: {sample_data}

Determine:
1. System Type:
   - "columnar": Separate Credit/Debit columns (e.g., Capital One format)
   - "categorical": Amount + transaction type indicator (Cr/Dr)
   - "algebraic": Single amount field with positive/negative values

2. Cultural Context:
   - "indian": Uses "Narration" field, Indian banking terminology
   - "us": Uses "Description" field, US banking terminology
   - "global": Mixed or unclear terminology

3. Confidence level (0.0 to 1.0)

4. Key features detected

Respond in JSON format:
{{
    "system_type": "columnar|categorical|algebraic",
    "cultural_context": "indian|us|global",
    "confidence": 0.95,
    "features": {{
        "separate_credit_debit": true/false,
        "transaction_type_column": true/false,
        "single_amount": true/false,
        "uses_narration": true/false,
        "uses_description": true/false
    }},
    "reasoning": "Brief explanation of detection logic"
}}""",
        version="1.0.0",
        variables=[
            "columns",
            "sample_data",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 500,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Detect accounting system type and cultural context from file structure",
        },
    ),
}