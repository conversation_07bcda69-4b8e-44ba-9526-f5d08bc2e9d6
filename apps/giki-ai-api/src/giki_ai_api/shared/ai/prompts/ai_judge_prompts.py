"""
AI Judge Prompts
================

Prompts for AI-based judgment of categorization correctness,
improvement evaluation, and accuracy assessment.
"""

from ..prompt_registry import PromptCategory, PromptVersion

AI_JUDGE_PROMPTS = {
    "ai_judge_categorization": PromptVersion(
        id="ai_judge_categorization",
        category=PromptCategory.AI_JUDGE,
        name="AI Judge Categorization Evaluation",
        template="""You are an expert financial categorization judge. Your task is to evaluate whether an AI-generated category is correct compared to the original/expected category.

Transaction Details:
- Description: {transaction_description}
- Amount: ${transaction_amount}
- Original Category: {original_category}
- AI Category: {ai_category}
- AI Hierarchy: {ai_hierarchy}
- AI Confidence: {ai_confidence}

Evaluation Criteria:
1. Semantic Correctness: Does the AI category capture the same meaning as the original?
2. Business Context: Is the categorization appropriate for business expense tracking?
3. Hierarchy Alignment: If hierarchical, does it follow logical parent-child relationships?
4. Specificity: Is the AI category appropriately specific (not too generic)?

Consider that categories may be expressed differently but still be correct:
- "Software" and "Technology > Software & Licenses" are semantically equivalent
- "Marketing" and "Marketing & Advertising" are close enough to be correct
- "Salary" and "Employee Compensation > Salaries and Wages" are the same concept

Provide your judgment in the following JSON format:
{{
    "judgment_result": "correct" | "incorrect" | "partially_correct",
    "judgment_confidence": 0.0-1.0,
    "judgment_reasoning": "Detailed explanation of your judgment",
    "semantic_similarity": 0.0-1.0,
    "hierarchy_alignment": true | false,
    "key_factors": ["list", "of", "key", "factors", "in", "decision"],
    "improvement_suggestions": "How the AI categorization could be improved (if applicable)"
}}

Important:
- Be fair and consider semantic equivalence, not just exact string matching
- "partially_correct" means the category is in the right general area but not quite specific enough
- Consider common variations and abbreviations
- Focus on business utility - would a CFO consider these categories equivalent?""",
        version="1.0.0",
        variables=[
            "transaction_description",
            "transaction_amount",
            "original_category",
            "ai_category",
            "ai_hierarchy",
            "ai_confidence",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 1024,
            "response_mime_type": "text/plain",
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "AI categorization correctness evaluation",
        },
    ),
    
    "ai_judge_improvement": PromptVersion(
        id="ai_judge_improvement",
        category=PromptCategory.AI_JUDGE,
        name="AI Judge Improvement Evaluation",
        template="""You are an expert financial categorization judge evaluating whether an AI category is an improvement over a customer's original category.

Transaction Details:
- Description: {transaction_description}
- Amount: ${transaction_amount}
- Original Category: {original_category}
- AI Category: {ai_category}

Evaluation Task: Compare the AI category to the original category and determine if it's an improvement.

Improvement Criteria:
1. SPECIFICITY: Is the AI category more specific and useful than the original?
   - "Misc Expense" → "Office Supplies" = IMPROVEMENT (more specific)
   - "General" → "Software Subscriptions" = IMPROVEMENT (more specific)
   
2. ACCURACY: Is the AI category more accurate for this specific transaction?
   - "Travel" → "Transportation" for Uber = EQUIVALENT (same meaning)
   - "Marketing" → "Technology" for software = WORSE (less accurate)
   
3. BUSINESS UTILITY: Would a business prefer the AI category for financial reporting?
   - "Expenses" → "Technology > Software & Licenses" = IMPROVEMENT (better for reporting)
   - "Office Supplies" → "Office Supplies" = EQUIVALENT (already good)

Important Guidelines:
- "better" = AI category is more specific, accurate, or business-useful
- "equivalent" = Categories capture the same business meaning, no regression
- "worse" = AI category is less specific, less accurate, or less useful

Provide your evaluation in this JSON format:
{{
    "judgment": "better" | "equivalent" | "worse",
    "confidence": 0.0-1.0,
    "reasoning": "Detailed explanation of why the AI category is better/equivalent/worse",
    "improvement_type": "specificity" | "accuracy" | "maintained" | "regression",
    "business_utility_score": 0.0-1.0
}}

Focus on business value: Would a CFO prefer the AI categorization for financial analysis and reporting?""",
        version="1.0.0",
        variables=[
            "transaction_description",
            "transaction_amount",
            "original_category",
            "ai_category",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 512,
            "response_mime_type": "text/plain",
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "M2 Rezolve temporal accuracy validation",
        },
    ),
}