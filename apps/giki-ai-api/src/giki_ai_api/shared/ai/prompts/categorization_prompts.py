"""
Categorization Prompts
=====================

Prompts for MIS categorization, business appropriateness evaluation,
and other categorization-related tasks.
"""

from ..prompt_registry import PromptCategory, PromptVersion

CATEGORIZATION_PROMPTS = {
    "mis_categorization_main": PromptVersion(
        id="mis_categorization_main",
        category=PromptCategory.CATEGORIZATION,
        name="MIS Categorization for Indian Businesses",
        template="""You are a professional MIS (Management Information System) categorization expert for {industry} businesses.

Business Context:
- Industry: {industry}
- Business Type: {business_type}
- Location: India
- Purpose: Generate accurate P&L statements and MIS reports

Transaction Details:
- Description: {description}
- Amount: ₹{amount:,.2f} ({parent_category})
- Type: {transaction_type}{hint_context}{vendor_context}{bank_context}

Available MIS Categories for {parent_category}:
{categories_str}

Instructions:
1. Select the MOST appropriate category from the list above
2. Consider the business context and industry norms
3. Use vendor research to inform your decision
4. Ensure the category will produce meaningful MIS reports
5. For Indian businesses, consider GST and compliance implications

Return JSON only:
{{
    "category_name": "exact category name from list",
    "parent_category": "{parent_category}",
    "confidence": 0.0-1.0,
    "reasoning": "brief explanation considering business context and MIS reporting",
    "gl_code": "GL code if tenant uses GL codes"
}}""",
        version="1.0.0",
        variables=[
            "industry",
            "business_type", 
            "description",
            "amount",
            "parent_category",
            "transaction_type",
            "hint_context",
            "vendor_context",
            "bank_context",
            "categories_str",
        ],
        model_config={
            "temperature": 0.2,
            "max_output_tokens": 500,
            "response_mime_type": "application/json",
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "MIS-focused business categorization for P&L statements",
            "research_source": "Indian business categorization patterns",
            "pattern_count": "20+ business types and categories",
        },
    ),
    
    "mis_categorization_hierarchical": PromptVersion(
        id="mis_categorization_hierarchical",
        category=PromptCategory.CATEGORIZATION,
        name="MIS Hierarchical Categorization (3-Level)",
        template="""You are an MIS categorization expert for Indian businesses. Categorize this transaction using our standard hierarchy.

Transaction Details:
- Description: {description}
- Amount: ₹{amount:,.2f} 
- Type: {parent_category}
- Vendor: {vendor_name}{vendor_context}

STANDARD {parent_category_upper} HIERARCHY FOR INDIAN BUSINESSES:

Level 1: {parent_category} (already determined)

Level 2 Categories (MUST choose one of these):
{level2_categories}

Level 3 Examples (choose most appropriate sub-category):
{level3_examples}

CATEGORIZATION RULES:
1. Common vendors: Swiggy/Zomato → Office & Admin > Staff Welfare, Uber/Ola → Operations > Travel
2. Utilities: Electricity/Water/Internet → Office & Admin > Utilities  
3. Salaries: Any salary/payroll → Employee Costs > Salaries & Wages
4. Rent: Office/shop rent → Office & Admin > Rent
5. Marketing: Facebook/Google Ads → Sales & Marketing > Digital Marketing
6. Banking: Bank charges/fees → Finance Costs > Bank Charges

Return JSON with your categorization:
{{
    "level1_category": "{parent_category}",
    "level2_category": "<choose from Level 2 list above>",
    "level3_category": "<specific sub-category>",
    "confidence": 0.7-1.0,
    "reasoning": "1-line explanation",
    "gl_code": null
}}""",
        version="1.0.0",
        variables=[
            "description",
            "amount",
            "parent_category",
            "parent_category_upper",
            "vendor_name",
            "vendor_context",
            "level2_categories",
            "level3_examples",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 300,
            "response_mime_type": "application/json",
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "3-level hierarchical MIS categorization",
            "research_source": "Standard Indian business hierarchy patterns",
            "pattern_count": "30+ categorization rules and vendor patterns",
        },
    ),
    
    "business_appropriateness_evaluation": PromptVersion(
        id="business_appropriateness_evaluation",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="Business Appropriateness Evaluation for Zero-Onboarding",
        template="""You are an expert financial categorization evaluator. Evaluate whether an AI-generated category is "business-appropriate" for a transaction using these weighted criteria:

TRANSACTION DETAILS:
Description: {transaction_description}
Amount: ${transaction_amount}
AI Category: {ai_category}
Merchant: {merchant_name}
Date: {transaction_date}

EVALUATION CRITERIA (WEIGHTED):
1. CONTEXT APPROPRIATENESS ({context_weight}%): Does the category make sense for this transaction context?
2. INDUSTRY STANDARDS ({industry_weight}%): Does the category align with standard business categorization practices?
3. MERCHANT ALIGNMENT ({merchant_weight}%): Does the category match what this merchant typically provides?
4. AMOUNT REASONABLENESS ({amount_weight}%): Does the amount make sense for this category?

SCORING INSTRUCTIONS:
- Score each component from 0.0 to 1.0 (0% to 100%)
- 0.85+ is considered "business-appropriate" for M1 validation
- Consider common business scenarios and practices
- Focus on practical business value, not perfect categorization

CONTEXT APPROPRIATENESS (40%):
- Does the category logically fit the transaction description?
- Would a business owner understand why this transaction is in this category?
- Are there obvious context clues that support or contradict the categorization?

INDUSTRY STANDARDS (30%):
- Is this how most businesses would categorize this type of transaction?
- Does it follow standard accounting or business categorization practices?
- Would this categorization be acceptable in business reporting?

MERCHANT ALIGNMENT (20%):
- Does the category match what this merchant typically sells/provides?
- If merchant is unknown, does the category fit typical merchants for this transaction type?
- Are there clear merchant-category mismatches?

AMOUNT REASONABLENESS (10%):
- Is the amount typical for this type of category?
- Are there obvious amount-category mismatches (e.g., $5000 for "Coffee")?
- Does the amount scale appropriately for the category?

ADDITIONAL CONTEXT:
{additional_context}

Return JSON format:
{{
    "context_score": 0.85,
    "context_reasoning": "Detailed explanation of context appropriateness score",
    "industry_score": 0.90,
    "industry_reasoning": "Detailed explanation of industry standards alignment",
    "merchant_score": 0.80,
    "merchant_reasoning": "Detailed explanation of merchant alignment",
    "amount_score": 0.95,
    "amount_reasoning": "Detailed explanation of amount reasonableness",
    "overall_reasoning": "Comprehensive assessment of business appropriateness",
    "improvement_suggestions": ["Specific suggestions if score < 0.85"],
    "business_scenarios": ["Common business situations where this categorization would be appropriate"]
}}

Focus on practical business value and appropriateness rather than perfect categorization. The goal is zero-onboarding categorization that business owners would find useful and reasonable.

JSON:""",
        version="1.0.0",
        variables=[
            "transaction_description",
            "transaction_amount",
            "ai_category",
            "merchant_name",
            "transaction_date",
            "context_weight",
            "industry_weight",
            "merchant_weight",
            "amount_weight",
            "additional_context",
        ],
        model_config={
            "temperature": 0.2,
            "max_output_tokens": 2000,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-06-27",
            "purpose": "M1 zero-onboarding business appropriateness evaluation",
            "target_threshold": 0.85,
            "weighting": "40% context, 30% industry, 20% merchant, 10% amount",
        },
    ),
    
    "batch_learned_categorization": PromptVersion(
        id="batch_learned_categorization",
        category=PromptCategory.CATEGORIZATION,
        name="Batch Transaction Categorization with Learned Categories",
        template="""You are categorizing transactions using ONLY the learned categories from this tenant's onboarding data.

Transactions to categorize:
{transactions_text}
{rag_context}

CRITICAL RULES:
1. Use ONLY categories from the learned taxonomy shown above
2. Match transactions to the most appropriate learned category based on similar examples
3. If no good match exists in learned categories, use "Uncategorized"
4. Return ONLY the exact category names from the learned taxonomy
5. Return one category per line, in the same order as transactions
6. No explanations, just the category names

Categories (one per line):""",
        version="1.0.0",
        variables=[
            "transactions_text",
            "rag_context",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 2000,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Batch categorization using tenant's learned categories from onboarding",
        },
    ),
}