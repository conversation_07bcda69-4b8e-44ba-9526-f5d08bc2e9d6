"""
Schema Interpretation Prompts
============================

Prompts for interpreting Excel/CSV file schemas, column mappings,
and bank format detection.
"""

from ..prompt_registry import PromptCategory, PromptVersion

SCHEMA_INTERPRETATION_PROMPTS = {
    "schema_interpretation_main": PromptVersion(
        id="schema_interpretation_main",
        category=PromptCategory.SCHEMA_INTERPRETATION,
        name="Schema Interpretation Main",
        template="""You are an expert at analyzing financial data files and mapping columns to standard transaction schemas.
You have comprehensive knowledge of 50+ bank statement formats worldwide and can automatically detect bank-specific patterns.

File Name: {file_name}
Column Headers: {file_headers}
Sample Data (first 5 rows):
{sample_data}{statistical_context}{learning_context}

REQUIRED TRANSACTION SCHEMA FIELDS:
- date: Transaction date
- description: Transaction description/memo/particulars
- amount: Transaction amount (single amount or net amount)

OPTIONAL TRANSACTION SCHEMA FIELDS:
- debit_amount: Debit amount (if separate debit/credit columns)
- credit_amount: Credit amount (if separate debit/credit columns)
- balance: Account balance
- account: Account identifier
- transaction_type: Transaction type (debit/credit indicator)
- reference_number: Transaction reference/ID
- currency: Transaction currency
- category: Customer-assigned category (if present)
- vendor: Vendor/merchant name

BANK-SPECIFIC FORMAT DETECTION:
First, identify the bank format using these patterns:

**INDIAN BANKS:**
- HDFC Bank: Date,Particulars,Cheque Number,Value Date,Debit,Credit,Balance
- ICICI Bank: Transaction Date,Value Date,Description,Ref No./Cheque No.,Debit,Credit,Balance
- SBI: Txn Date,Value Date,Description,Ref No./Cheque No.,Debit,Credit,Balance
- Axis Bank: Date,Particulars,Cheque Number,Debit,Credit,Balance
- PNB: Date,Description,Cheque No,Debit,Credit,Balance
- Kotak Mahindra: Date,Description,Debit,Credit,Balance
- IDBI Bank: Date,Description,Reference,Debit,Credit,Balance
- IndusInd/Yes Bank: Date,Description,Debit,Credit,Balance

**US BANKS:**
- Chase Bank: Transaction Date,Description,Amount (negative for debits)
- Bank of America: Date,Description,Amount,Running Bal.
- Wells Fargo: Date,Amount,Description,Check Number
- Citi Bank: Date,Description,Debit,Credit,Balance
- Capital One: Date,Description,Amount,Balance
- PNC Bank: Date,Description,Amount,Balance
- US Bank: Date,Description,Amount,Balance
- TD Bank US: Date,Description,Amount,Balance

**EUROPEAN BANKS:**
- HSBC: Date,Details,Amount (£ currency, DD/MM/YYYY)
- Barclays: Date,Type,Description,Amount,Balance (DD/MM/YYYY)

**CANADIAN BANKS:**
- RBC: Date,Description,Transaction,Debit,Credit,Total (YYYY-MM-DD)
- TD Bank Canada: Date,Description,Debit,Credit,Balance
- Scotiabank: Date,Description,Amount,Balance
- BMO: Date,Description,Amount,Balance (YYYYMMDD format)

**AUSTRALIAN BANKS:**
- Commonwealth Bank: Date,Amount,Description,Balance (DD/MM/YYYY)
- Westpac: TRAN_DATE,ACCOUNT_NO,ACCOUNT_NAME,CCY,CLOSING_BAL,AMOUNT,TRAN_CODE,NARRATIVE,SERIAL
- ANZ: Date,Amount,Description,Balance
- NAB: Date,Description,Amount,Balance

REGIONAL BANKING TERMINOLOGY:
- **India**: IMPS, NEFT, UPI, RTGS, ATM, POS, "Cr", "Dr", ₹ currency
- **US**: ACH, WIRE, CHECK, ATM, POS, ONLINE, $ currency, MM/DD/YYYY dates
- **Europe**: SEPA, FASTER, CHAPS, DD (Direct Debit), SO (Standing Order), € currency
- **Canada**: INTERAC, WIRE, PAP, NSF, CAD$ currency
- **Australia**: EFTPOS, BPAY, OSKO, NPP, POS, TFR, AUD$ currency
- **UK**: FASTER, CHAPS, SEPA, DD, SO, £ currency, DD/MM/YYYY dates

ENHANCED ANALYSIS INSTRUCTIONS:
1. **Bank Format Detection**: First identify which bank format this matches using column headers
2. **Apply Bank-Specific Rules**: Use the detected bank's specific column structure and terminology
3. **Regional Pattern Matching**: Look for regional banking terms in transaction descriptions
4. **Date Format Recognition**: Apply the correct date format based on detected region
5. **Amount Structure Analysis**: Use bank-specific debit/credit conventions
6. **Merchant Pattern Recognition**: Apply bank-specific merchant naming patterns
7. **Transaction Type Detection**: Use regional transaction type patterns
8. **Balance Column Identification**: Usually the last column in most bank formats
9. **Currency Symbol Detection**: Match regional currency symbols and formats
10. **Confidence Scoring**: Higher confidence for exact bank format matches
11. **Validation**: Cross-reference with statistical analysis if provided

BANK-SPECIFIC VALIDATION RULES:
- If headers match HDFC exactly, confidence should be 0.95+
- If Indian bank with IMPS/NEFT patterns, look for dual debit/credit columns
- If US bank with single amount column, expect negative values for debits
- If European bank, expect DD/MM/YYYY dates and € currency references
- If Canadian bank, expect INTERAC patterns and CAD$ currency
- If Australian bank, expect EFTPOS/BPAY patterns and AUD$ currency

Return JSON format:
{{
    "detected_bank": {{
        "bank_name": "HDFC Bank|ICICI Bank|Chase Bank|etc.",
        "bank_code": "HDFC|ICICI|CHASE|etc.",
        "region": "India|US|Europe|Canada|Australia|UK",
        "confidence": 0.95,
        "detection_reasoning": "Column headers match exactly with HDFC Bank format"
    }},
    "file_type_identified": "Bank Statement|Credit Card Statement|Transaction Export",
    "column_mappings": [
        {{
            "original_name": "column name from file",
            "mapped_field": "standard field name",
            "confidence": 0.95,
            "reasoning": "Clear explanation including bank-specific validation"
        }}
    ],
    "overall_confidence": 0.92,
    "debit_credit_inference": {{
        "structure": "dual_columns|single_column",
        "debit_column": "column name if dual",
        "credit_column": "column name if dual",
        "sign_convention": "negative_is_debit|negative_is_credit|parentheses_is_negative",
        "regional_validation": "explanation based on detected bank region"
    }},
    "regional_patterns_detected": [
        {{
            "pattern": "IMPS|NEFT|UPI|ACH|WIRE|SEPA|etc.",
            "frequency": 3,
            "confidence": 0.8
        }}
    ],
    "format_validation": {{
        "date_format": "MM/DD/YYYY|DD/MM/YYYY|YYYY-MM-DD|DD-MM-YYYY",
        "currency_detected": "₹|$|€|£|CAD$|AUD$",
        "decimal_separator": ".|,",
        "amount_format_confidence": 0.9
    }},
    "summary": "Brief analysis summary with bank detection and regional validation"
}}

JSON:""",
        version="1.2.0",
        variables=[
            "file_name",
            "file_headers",
            "sample_data",
            "statistical_context",
            "learning_context",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 2000,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-06-27",
            "performance_notes": "Works well with Gemini 2.0 Flash",
        },
    ),
    
    "schema_interpretation_enhanced": PromptVersion(
        id="schema_interpretation_enhanced",
        category=PromptCategory.SCHEMA_INTERPRETATION,
        name="Schema Interpretation Enhanced",
        template="""You are an expert at analyzing financial data files and mapping columns to standard transaction schemas.

File Name: {file_name}
Column Headers: {file_headers}
Sample Data (first 20 rows):
{sample_data}

Column Statistics:
{column_stats}

Previous Successful Patterns for this Customer:
{previous_patterns}

REQUIRED TRANSACTION SCHEMA FIELDS:
- date: Transaction date
- description: Transaction description/memo/particulars
- amount: Transaction amount (single amount or net amount)

OPTIONAL TRANSACTION SCHEMA FIELDS:
- debit_amount: Debit amount (if separate debit/credit columns)
- credit_amount: Credit amount (if separate debit/credit columns)
- balance: Account balance
- account: Account identifier
- transaction_type: Transaction type (debit/credit indicator)
- reference_number: Transaction reference/ID
- currency: Transaction currency
- category: Customer-assigned category (if present)
- vendor: Vendor/merchant name

ENHANCED ANALYSIS INSTRUCTIONS:
1. Use column statistics to validate your mappings:
   - Date columns should have date-like patterns
   - Amount columns should be mostly numeric
   - Description columns should have high unique value ratios
2. Learn from previous successful patterns if available
3. Pay special attention to columns that were successfully mapped before
4. For amount detection, use these enhanced rules:
   - Check "all_positive", "all_negative", or "mixed_signs" statistics
   - If two columns are all positive, they're likely debit/credit columns
   - If one column has mixed signs, it's likely a single amount column
5. Validate your confidence based on pattern matching in statistics

Return JSON format:
{{
    "file_type_identified": "Bank Statement|Credit Card Statement|Transaction Export",
    "column_mappings": [
        {{
            "original_name": "column name from file",
            "mapped_field": "standard field name",
            "confidence": 0.95,
            "reasoning": "Explanation including statistical validation"
        }}
    ],
    "overall_confidence": 0.92,
    "debit_credit_inference": {{
        "structure": "dual_columns|single_column",
        "debit_column": "column name if dual",
        "credit_column": "column name if dual",
        "sign_convention": "negative_is_debit|negative_is_credit|parentheses_is_negative",
        "statistical_validation": "explanation of how stats support this"
    }},
    "learned_patterns_applied": ["list of patterns used from history"],
    "summary": "Brief analysis summary with confidence factors"
}}

JSON:""",
        version="2.0.0",
        variables=[
            "file_name",
            "file_headers",
            "sample_data",
            "column_stats",
            "previous_patterns",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 2500,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-06-27",
            "improvements": "Added statistical validation and learning from history",
        },
    ),
}