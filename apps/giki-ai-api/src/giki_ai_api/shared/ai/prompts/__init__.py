"""
Prompts Package - Organized prompt storage for giki.ai
=====================================================

This package contains all AI prompts organized by category.
Each category has its own module for better maintainability.
"""

from .accounting_prompts import ACCOUNTING_PROMPTS
from .agent_prompts import AGENT_PROMPTS
from .ai_judge_prompts import AI_JUDGE_PROMPTS
from .analysis_prompts import ANALYSIS_PROMPTS
from .categorization_prompts import CATEGORIZATION_PROMPTS
from .schema_interpretation_prompts import SCHEMA_INTERPRETATION_PROMPTS

# Aggregate all prompts
ALL_PROMPTS = {
    **SCHEMA_INTERPRETATION_PROMPTS,
    **CATEGORIZATION_PROMPTS,
    **ACCOUNTING_PROMPTS,
    **AI_JUDGE_PROMPTS,
    **ANALYSIS_PROMPTS,
    **AGENT_PROMPTS,
}

__all__ = [
    "ACCOUNTING_PROMPTS",
    "AGENT_PROMPTS",
    "AI_JUDGE_PROMPTS",
    "ANALYSIS_PROMPTS",
    "CATEGORIZATION_PROMPTS",
    "SCHEMA_INTERPRETATION_PROMPTS",
    "ALL_PROMPTS",
]