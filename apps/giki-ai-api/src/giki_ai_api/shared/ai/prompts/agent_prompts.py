"""
Agent-Specific Prompts
=====================

Prompts for various domain agents including conversational,
coordination, reporting, and other specialized agent tasks.
"""

from ..prompt_registry import PromptCategory, PromptVersion

AGENT_PROMPTS = {
    # Categorization Agent Prompts
    "category_suggestion": PromptVersion(
        id="category_suggestion",
        category=PromptCategory.CATEGORIZATION,
        name="AI Category Suggestion with GL Code Awareness",
        template="""Analyze this transaction and suggest the most appropriate category with GL code awareness:

TRANSACTION:
Description: "{description}"
Amount: ${amount}
{business_context}
{categories_context}
{gl_code_context}

Create industry-appropriate categories that consider:
1. Business context and industry standards
2. Existing GL code structure and hierarchy
3. Professional accounting classification (Revenue/Expense/Asset/Liability/Equity)
4. Amount-based categorization logic (large amounts may indicate capital expenses)

If existing categories with GL codes match well, prefer those for consistency.
For new categories, suggest appropriate GL account type alignment.

Respond in JSON format:
{{
    "category": "suggested category name",
    "confidence": 0.85,
    "reasoning": "explanation including GL code and business context considerations",
    "alternatives": [["alt1", 0.7], ["alt2", 0.6]],
    "suggested_gl_account_type": "Expense|Revenue|Asset|Liability|Equity"
}}""",
        version="1.0.0",
        variables=[
            "description",
            "amount",
            "business_context",
            "categories_context",
            "gl_code_context",
        ],
        model_config={
            "temperature": 0.05,
            "max_output_tokens": 800,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Business-aware category suggestions with GL code integration",
        },
    ),
    
    "gl_code_suggestion": PromptVersion(
        id="gl_code_suggestion",
        category=PromptCategory.CATEGORIZATION,
        name="GL Code Suggestion for Categories",
        template="""Suggest appropriate GL (General Ledger) codes for this category:

Category: {category_name}
Full Path: {category_path}
Account Type: {account_type}
Existing codes in use: {existing_codes}
Codes for this account type: {type_codes}

Provide 3 GL code suggestions following standard accounting practices:
- Asset accounts: 1000-1999
- Liability accounts: 2000-2999  
- Equity accounts: 3000-3999
- Revenue accounts: 4000-4999
- Expense accounts: 5000-9999

Respond in JSON format:
{{
    "suggestions": [
        {{"code": "5200", "name": "Office Expenses", "reasoning": "Standard expense code for office-related costs"}},
        {{"code": "5210", "name": "Office Supplies", "reasoning": "More specific subcategory for supplies"}},
        {{"code": "5220", "name": "General Office", "reasoning": "Alternative general office expense code"}}
    ]
}}""",
        version="1.0.0",
        variables=[
            "category_name",
            "category_path",
            "account_type",
            "existing_codes",
            "type_codes",
        ],
        model_config={
            "temperature": 0.2,
            "max_output_tokens": 400,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Standard GL code suggestions for financial categories",
        },
    ),
    
    "category_hierarchy_generation": PromptVersion(
        id="category_hierarchy_generation",
        category=PromptCategory.CATEGORIZATION,
        name="Context-Aware Category Hierarchy Generation",
        template="""Based on the business context, generate appropriate category hierarchies:

BUSINESS CONTEXT:
- Industry: {industry}
- Business Type: {business_type}
- Company Size: {company_size}

EXISTING CATEGORIES:
{existing_categories}

Create a hierarchy of parent-child category relationships that would be useful for this business.
Consider industry-specific needs and standard accounting practices.

Example hierarchies:
- "Marketing & Sales > Online Marketing > Social Media Ads"
- "Operations > Inventory > Raw Materials"
- "Administration > Legal & Professional > Legal Fees"

Respond in JSON format:
{{
    "hierarchies": [
        {{
            "parent": "Marketing & Sales",
            "children": ["Online Marketing", "Offline Marketing", "Sales Tools"],
            "reasoning": "Essential for tracking marketing spend and ROI",
            "industry_specific": true
        }}
    ],
    "ungrouped_categories": ["Category that doesn't fit anywhere"],
    "confidence": 0.85
}}

Focus on creating hierarchies that would be useful for financial reporting and business analysis in this specific industry.""",
        version="1.0.0",
        variables=[
            "industry",
            "business_type",
            "company_size",
            "existing_categories",
        ],
        model_config={
            "temperature": 0.2,
            "max_output_tokens": 800,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Industry-specific category hierarchy generation",
        },
    ),
    
    # Onboarding Agent Prompts
    "onboarding_report_generation": PromptVersion(
        id="onboarding_report_generation",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="Onboarding Report Generation",
        template="""Generate a comprehensive onboarding report for this business:

{report_data}

Create a structured report with the following sections:
1. Executive Summary
2. File Analysis Results
3. Transaction Insights
4. Category Recommendations
5. Next Steps

Focus on clarity, actionable insights, and business value.

Return JSON format:
{{
    "executive_summary": "Brief overview of findings",
    "file_analysis": {{
        "total_files": 0,
        "successful_files": 0,
        "total_transactions": 0,
        "date_range": "start - end",
        "key_findings": ["finding1", "finding2"]
    }},
    "transaction_insights": {{
        "volume_trends": "description",
        "spending_patterns": "description",
        "vendor_analysis": "top vendors and frequencies"
    }},
    "category_recommendations": {{
        "suggested_categories": ["cat1", "cat2"],
        "optimization_opportunities": ["opp1", "opp2"]
    }},
    "next_steps": ["step1", "step2", "step3"]
}}""",
        version="1.0.0",
        variables=[
            "report_data",
        ],
        model_config={
            "temperature": 0.3,
            "max_output_tokens": 2000,
            "top_p": 0.9,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Business onboarding report generation",
        },
    ),
    
    # MIS Template Selection Agent Prompts
    "mis_template_selection": PromptVersion(
        id="mis_template_selection",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="MIS Industry Template Selection",
        template="""Analyze this business context and select the MOST APPROPRIATE industry template.

Business Context:
{business_context}

Based on the signals, select the PRIMARY industry from the available options.
Consider:
1. Company name and website content
2. Business description
3. Transaction patterns and vendor types
4. Existing category names
5. Company size and complexity

Provide:
1. Selected industry (MUST be from available_industries list)
2. Confidence score (0.0-1.0)
3. Clear reasoning for selection
4. 2-3 suggested customizations for this specific business
5. Top 2 alternative industries with confidence scores

Format response as JSON:
{{
    "industry": "selected industry name",
    "confidence": 0.85,
    "reasoning": "clear explanation",
    "customizations": ["suggestion 1", "suggestion 2"],
    "alternatives": [
        {{"industry": "alt1", "confidence": 0.65}},
        {{"industry": "alt2", "confidence": 0.45}}
    ]
}}""",
        version="1.0.0",
        variables=[
            "business_context",
        ],
        model_config={
            "temperature": 0.2,
            "max_output_tokens": 800,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "AI-powered industry template selection during onboarding",
        },
    ),
    
    # Reports Agent Prompts
    "trend_analysis": PromptVersion(
        id="trend_analysis",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="Financial Trend Analysis",
        template="""Analyze these financial trends and provide insights:

Monthly Data:
{monthly_data}

Category Trends:
{category_trends}

Analysis Type: {analysis_type}
Time Period: {time_period}

Provide analysis including:
1. Overall spending/revenue trends
2. Significant changes or anomalies
3. Category-specific trends
4. Seasonal patterns if any
5. Predictive insights for next period

Return JSON format:
{{
    "trend_summary": {{
        "direction": "increasing|decreasing|stable",
        "percentage_change": 15.5,
        "key_insights": ["insight1", "insight2"]
    }},
    "anomalies": [
        {{
            "period": "2024-03",
            "type": "spike|dip",
            "description": "Unusual spending spike",
            "impact": "high|medium|low"
        }}
    ],
    "category_trends": [
        {{
            "category": "Transportation",
            "trend": "increasing",
            "change_percentage": 25.0,
            "insight": "Transportation costs rising steadily"
        }}
    ],
    "predictions": {{
        "next_period_estimate": {{
            "revenue": 5000,
            "expenses": 4000,
            "confidence": 0.85
        }},
        "recommendations": ["recommendation1", "recommendation2"]
    }}
}}

JSON:""",
        version="1.0.0",
        variables=[
            "monthly_data",
            "category_trends",
            "analysis_type",
            "time_period",
        ],
        model_config={
            "temperature": 0.3,
            "max_output_tokens": 2000,
            "top_p": 0.9,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Financial trend analysis and predictive insights",
        },
    ),
    
    # Transaction Agent Prompts
    "regional_banking_terms": PromptVersion(
        id="regional_banking_terms",
        category=PromptCategory.ACCOUNTING,
        name="Regional Banking Terms Standardization",
        template="""You are an expert in international banking systems and terminology.
Standardize this transaction's debit/credit interpretation for the specified region.

Transaction Data:
{transaction_data}

Region: {region}
Banking System: {banking_system}

REGIONAL VARIATIONS:

US Banking:
- DR = Debit, CR = Credit
- Checks: "Check #" indicates payment (debit)
- ACH: Electronic transfers
- Wire: Wire transfers

UK Banking:
- Standing Order: Recurring payment
- Direct Debit: Authorized withdrawal
- BACS: Bank transfer system
- Cheque: UK spelling of check

EU Banking:
- SEPA: Single Euro Payments Area
- IBAN: International Bank Account Number
- Giro: Payment transfer system

APAC Banking:
- GIRO: Singapore payment system
- NEFT/RTGS: Indian transfer systems
- Remittance: Common term for transfers

Return JSON format:
{{
    "standardized_transaction": {{
        "description": "Standardized description",
        "type": "debit|credit",
        "original_terms": ["GIRO", "Standing Order"],
        "standardized_terms": ["Recurring Payment", "Direct Debit"]
    }},
    "regional_context": {{
        "region": "{region}",
        "banking_convention": "Description of regional convention",
        "special_considerations": ["List of region-specific rules"]
    }},
    "confidence": 0.95,
    "requires_manual_review": false
}}

JSON:""",
        version="1.0.0",
        variables=[
            "transaction_data",
            "region",
            "banking_system",
        ],
        model_config={
            "temperature": 0.2,
            "max_output_tokens": 1000,
            "top_p": 0.9,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Regional banking terminology standardization",
        },
    ),
    
    # Coordinator Agent Prompts
    "multi_agent_coordination": PromptVersion(
        id="multi_agent_coordination",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="Multi-Agent Task Coordination",
        template="""You are an expert at coordinating multiple AI agents for complex financial workflows.
Create an execution plan for this task.

Task: {task_description}

Agents and Subtasks:
{agents_involved}

Coordination Strategy: {coordination_strategy}

COORDINATION STRATEGIES:
- Sequential: Each agent completes before next starts
- Parallel: Independent agents run simultaneously
- Mixed: Some parallel, some sequential based on dependencies

WORKFLOW PATTERNS:
1. Data Pipeline: files → processing → categorization → reporting
2. Analysis Branch: parallel analysis by different agents, then synthesis
3. Validation Loop: process → validate → refine
4. Hierarchical: coordinator → specialists → aggregation

DEPENDENCIES TO CONSIDER:
- Data dependencies (agent B needs output from agent A)
- Resource constraints (database locks, API limits)
- Temporal requirements (certain operations must happen in order)
- Error propagation (failure handling strategies)

Return JSON format:
{{
    "execution_plan": {{
        "phases": [
            {{
                "phase_id": "phase_1",
                "phase_name": "Data Processing",
                "agents": ["files_agent", "schema_interpretation_agent"],
                "execution_mode": "parallel",
                "dependencies": [],
                "expected_duration": "30s"
            }}
        ],
        "total_phases": 3,
        "estimated_total_time": "2m"
    }},
    "agent_assignments": {{
        "files_agent": {{
            "task": "Process uploaded files",
            "input_required": ["file_paths"],
            "output_expected": ["processed_data"],
            "phase": "phase_1"
        }}
    }},
    "coordination_metadata": {{
        "strategy": "{coordination_strategy}",
        "parallelism_level": 3,
        "critical_path": ["phase_1", "phase_2"],
        "fallback_plan": "Retry failed agents once, then fail gracefully"
    }}
}}

JSON:""",
        version="1.0.0",
        variables=[
            "task_description",
            "agents_involved",
            "coordination_strategy",
        ],
        model_config={
            "temperature": 0.3,
            "max_output_tokens": 2000,
            "top_p": 0.9,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Multi-agent workflow coordination and planning",
        },
    ),
    
    "agent_response_synthesis": PromptVersion(
        id="agent_response_synthesis",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="Multi-Agent Response Synthesis",
        template="""You are an expert at synthesizing responses from multiple specialized agents.
Combine these agent responses into a coherent, unified response.

Original Query: {original_query}

Agent Responses:
{agent_responses}

Aggregation Strategy: {aggregation_strategy}

AGGREGATION STRATEGIES:
- Synthesis: Create unified narrative combining all insights
- Summary: Concise summary of key points from all agents
- Detailed: Include all details organized by topic
- Prioritized: Focus on most important/relevant information

SYNTHESIS PRINCIPLES:
1. Maintain accuracy from specialized agents
2. Eliminate redundancy
3. Preserve important details
4. Create logical flow
5. Highlight key insights
6. Flag any conflicts or inconsistencies

Return JSON format:
{{
    "synthesized_response": {{
        "summary": "One paragraph executive summary",
        "key_findings": ["Finding 1", "Finding 2"],
        "detailed_sections": {{
            "section_name": {{
                "content": "Section content",
                "source_agents": ["agent1", "agent2"],
                "confidence": 0.95
            }}
        }},
        "action_items": ["Action 1", "Action 2"],
        "caveats": ["Any limitations or warnings"]
    }},
    "metadata": {{
        "agents_consulted": ["agent1", "agent2"],
        "response_quality": "high|medium|low",
        "conflicts_found": [],
        "aggregation_strategy": "{aggregation_strategy}"
    }},
    "user_response": "Natural language response for the user"
}}

JSON:""",
        version="1.0.0",
        variables=[
            "original_query",
            "agent_responses",
            "aggregation_strategy",
        ],
        model_config={
            "temperature": 0.3,
            "max_output_tokens": 2500,
            "top_p": 0.9,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Synthesize multiple agent responses into coherent output",
        },
    ),
    
    "agent_failure_recovery": PromptVersion(
        id="agent_failure_recovery",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="Agent Failure Recovery Planning",
        template="""You are an expert at handling failures in multi-agent financial workflows.
Analyze this failure and recommend a recovery strategy.

Failed Agent: {failed_agent}
Error Details: {error_details}
Workflow State: {workflow_state}
Requested Strategy: {recovery_strategy}

RECOVERY STRATEGIES:
- Retry: Try the same agent again with same/modified inputs
- Fallback: Use alternative agent or simplified approach
- Skip: Continue workflow without this agent's contribution
- Abort: Stop workflow and report failure
- Compensate: Use different agents to achieve similar result

ERROR CATEGORIES:
- Transient: Network issues, rate limits, temporary unavailability
- Data: Invalid input, missing required data
- Logic: Agent logic errors, unexpected conditions
- Resource: Out of memory, quota exceeded
- Critical: Unrecoverable errors requiring human intervention

Return JSON format:
{{
    "error_analysis": {{
        "error_category": "transient|data|logic|resource|critical",
        "severity": "low|medium|high|critical",
        "is_recoverable": true,
        "root_cause": "Brief explanation of root cause"
    }},
    "recovery_plan": {{
        "strategy": "retry|fallback|skip|abort|compensate",
        "actions": [
            {{
                "action": "retry_with_backoff",
                "parameters": {{"wait_time": 5, "max_attempts": 3}},
                "fallback_action": "use_simplified_approach"
            }}
        ],
        "alternative_agents": ["agent1", "agent2"],
        "modified_workflow": {{"description": "How to adjust workflow"}}
    }},
    "impact_assessment": {{
        "workflow_impact": "minimal|moderate|severe",
        "data_loss_risk": "none|low|high",
        "user_visible": false,
        "requires_notification": false
    }}
}}

JSON:""",
        version="1.0.0",
        variables=[
            "failed_agent",
            "error_details",
            "workflow_state",
            "recovery_strategy",
        ],
        model_config={
            "temperature": 0.2,
            "max_output_tokens": 1500,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Error recovery planning for multi-agent workflows",
        },
    ),
    
    # Conversational Agent Prompts
    "conversational_assistant": PromptVersion(
        id="conversational_assistant",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="Conversational Assistant Interaction",
        template="""You are a helpful AI assistant for a financial categorization platform.

User Query: {command}

Recent Context:
{recent_context}

Please provide a helpful, concise response. If the user is asking about specific functionality,
guide them to use the appropriate commands or features.""",
        version="1.0.0",
        variables=[
            "command",
            "recent_context",
        ],
        model_config={
            "temperature": 0.7,
            "max_output_tokens": 1000,
            "top_p": 0.9,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Natural language interaction with financial categorization platform",
        },
    ),
    
    # Transaction Agent Prompts
    "process_us_banking_description": PromptVersion(
        id="process_us_banking_description",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="US Banking Description Processing",
        template="""You are an expert at processing US banking transaction descriptions to make them more readable.

Original Description: "{description}"

Instructions:
1. Expand common US banking abbreviations to full terms
2. Make the description more human-readable
3. Preserve the core meaning and merchant information
4. Keep the result concise and clear
5. Return ONLY the processed description, no explanation

Common US Banking Terms:
- ACH → ACH Transfer
- POS → Point of Sale
- ATM → ATM Withdrawal
- CHK → Check
- DDA → Checking Account
- SAV → Savings Account

Processed Description:""",
        version="1.0.0",
        variables=[
            "description",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 100,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Process US banking descriptions for readability",
        },
    ),
    
    "extract_transaction_entities": PromptVersion(
        id="extract_transaction_entities",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="Transaction Entity Extraction",
        template="""You are an expert at extracting structured information from financial transaction descriptions.

Transaction Description: "{description}"

Extract the following entities and return them in JSON format:
1. merchants: Business names, vendor names, merchant names
2. account_numbers: Account numbers, reference numbers, transaction IDs
3. amounts: Currency amounts mentioned in the description

Instructions:
- Only extract clear, identifiable entities
- Ignore generic banking terms
- Return clean, readable names
- If no entities found for a category, return empty array

Return JSON format:
{{
    "merchants": ["merchant1", "merchant2"],
    "account_numbers": ["acc1", "acc2"],
    "amounts": ["$10.00", "₹500"]
}}

JSON:""",
        version="1.0.0",
        variables=[
            "description",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 200,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Extract structured entities from transaction descriptions",
        },
    ),
    
    "extract_merchant_name": PromptVersion(
        id="extract_merchant_name",
        category=PromptCategory.BUSINESS_APPROPRIATENESS,
        name="Merchant Name Extraction",
        template="""You are an expert at extracting merchant/vendor names from financial transaction descriptions.

Transaction Description: "{description_clean}"

Instructions:
1. Extract the primary merchant, vendor, or business name from this transaction description
2. Ignore generic banking terms (transfer, payment, deposit, withdrawal, fee, charge, interest, ATM, POS)
3. Ignore system codes and reference numbers
4. Focus on the actual business or merchant name
5. Return a clean, readable business name (max 50 characters)
6. If no meaningful merchant can be identified, return "SKIP"

Examples:
- "STARBUCKS STORE #1234 SEATTLE WA" → "Starbucks"
- "AMAZON.COM AMZN.COM/BILL WA" → "Amazon"
- "UBER TRIP 123ABC" → "Uber"
- "ATM WITHDRAWAL FEE" → "SKIP"
- "TRANSFER TO SAVINGS" → "SKIP"

Extracted Merchant Name:""",
        version="1.0.0",
        variables=[
            "description_clean",
        ],
        model_config={
            "temperature": 0.1,
            "max_output_tokens": 30,
            "top_p": 0.8,
        },
        metadata={
            "author": "giki.ai team",
            "last_updated": "2025-07-08",
            "purpose": "Extract merchant names from transaction descriptions",
        },
    ),
}