"""
Standard Giki Agent - Base Pattern for All giki.ai Financial Agents

This module implements the StandardGikiAgent base class that provides a consistent
pattern for all financial agents in the giki.ai platform, following the latest
Google ADK v1.2.1 and A2A v0.2.1 specifications.

Features:
- Latest ADK tools integration (google_search, vertex_ai_search_tool, code_execution)
- Advanced financial agent instruction templates
- Consistent tool configuration across all agents
- Full A2A protocol compliance with agent cards
- Enhanced context management and session continuity
- Vector-based RAG integration

References:
- Google ADK v1.2.1 Documentation
- A2A Protocol v0.2.1 Specification
- docs/specifications/comprehensive-agent-architecture.md
"""

import logging
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

# Official Google ADK v1.3.0 imports (Production Ready)
from google.adk.agents import LlmAgent
from google.adk.tools import (
    FunctionTool,
    VertexAiSearchTool,
    apihub_tool,
    exit_loop,
    get_user_choice,
    google_search,
    load_artifacts,
    load_memory,
    openapi_tool,
    preload_memory,
    transfer_to_agent,
)

logger = logging.getLogger(__name__)


@dataclass
class AgentCapabilities:
    """A2A Protocol agent capabilities definition."""

    streaming: bool = True
    push_notifications: bool = True
    multimodal: bool = True
    financial_processing: bool = True
    real_time_data: bool = True
    vector_search: bool = True
    code_execution: bool = True


@dataclass
class AgentCard:
    """A2A Protocol agent card for discovery."""

    name: str
    description: str
    version: str
    capabilities: AgentCapabilities
    skills: List[Dict[str, Any]] = field(default_factory=list)
    authentication_schemes: List[str] = field(default_factory=lambda: ["bearer_token"])
    created_at: datetime = field(default_factory=datetime.utcnow)


def create_vertex_ai_search_tool(
    project_id: Optional[str] = None, location: str = "global"
) -> Optional[VertexAiSearchTool]:
    """Create VertexAiSearchTool instance with production configuration."""
    try:
        # Production configuration with proper data store
        return VertexAiSearchTool(data_store_id="giki-financial-knowledge-base")
    except Exception as e:
        logger.warning(f"Failed to create VertexAI search tool: {e}")
        return None


def create_enhanced_function_tool(
    func: Callable,
    name: str,
    description: str,
    parameters_schema: Optional[Dict[str, Any]] = None,
    category: str = "financial",
) -> FunctionTool:
    """Enhanced pattern for creating ADK function tools with proper schemas."""
    return FunctionTool(func=func)


class StandardGikiAgent(LlmAgent):
    """
    Official Google ADK v1.3.0 pattern for giki.ai financial agents.

    This base class provides:
    - Official ADK v1.3.0 LlmAgent inheritance (Production Ready)
    - Built-in tools (google_search, vertex_ai_search_tool, memory tools)
    - A2A protocol compliance with agent cards
    - Financial domain instruction templates
    - Standardized tool configuration

    All giki.ai agents MUST inherit from this class to ensure:
    - Compliance with official Google ADK patterns
    - A2A protocol compatibility for agent interoperability
    - Consistent financial agent behavior
    """

    def __init__(
        self,
        name: str,
        description: str,
        custom_tools: Optional[List[FunctionTool]] = None,
        enable_interactive_tools: bool = False,
        enable_code_execution: bool = True,
        enable_standard_tools: bool = False,  # NEW: Make standard tools optional
        standard_tool_set: Optional[List[str]] = None,  # NEW: Selective standard tools
        model_name: str = "gemini-2.0-flash-001",
        project_id: Optional[str] = None,
        location: str = "global",
        **kwargs,
    ):
        """
        Initialize StandardGikiAgent with optional tool registration for efficiency.

        Args:
            name: Agent name (should follow pattern: giki-ai-{purpose})
            description: Agent description for A2A discovery
            custom_tools: Domain-specific tools for this agent
            enable_interactive_tools: Whether to include get_user_choice tool
            enable_code_execution: Whether to include code_execution tool
            enable_standard_tools: Whether to include standard ADK tools (default: False for efficiency)
            standard_tool_set: List of specific standard tools to include (optional)
                Available: ['google_search', 'transfer_to_agent', 'load_memory', 'preload_memory',
                          'load_artifacts', 'openapi_tool', 'apihub_tool', 'exit_loop', 'vertex_search']
            model_name: Gemini model to use (default: gemini-2.0-flash-001)
            project_id: Google Cloud project ID for VertexAI tools
            location: Location for VertexAI tools (default: global)
            **kwargs: Additional arguments passed to LlmAgent
        """

        # Tool registry for selective inclusion
        available_standard_tools = {
            "google_search": google_search,
            "transfer_to_agent": transfer_to_agent,
            "load_memory": load_memory,
            "preload_memory": preload_memory,
            "load_artifacts": load_artifacts,
            "openapi_tool": openapi_tool,
            "apihub_tool": apihub_tool,
            "exit_loop": exit_loop,
        }

        # Build standard tools based on configuration
        standard_tools = []

        if enable_standard_tools:
            if standard_tool_set:
                # Use only specified standard tools for cognitive efficiency
                for tool_name in standard_tool_set:
                    if tool_name in available_standard_tools:
                        standard_tools.append(available_standard_tools[tool_name])
                    elif tool_name == "vertex_search":
                        # Special handling for VertexAI search tool
                        vertex_search_tool = create_vertex_ai_search_tool(
                            project_id, location
                        )
                        if vertex_search_tool:
                            standard_tools.append(vertex_search_tool)
            else:
                # Legacy mode: include all standard tools (for backward compatibility)
                standard_tools = list(available_standard_tools.values())
                # Add enhanced VertexAI search tool with production configuration
                vertex_search_tool = create_vertex_ai_search_tool(project_id, location)
                if vertex_search_tool:
                    standard_tools.append(vertex_search_tool)

        # Add interactive tools for customer-facing agents
        if enable_interactive_tools or "customer" in name.lower():
            standard_tools.append(get_user_choice)

        # Combine with domain-specific tools (prioritize custom tools for efficiency)
        all_tools = (custom_tools or []) + standard_tools

        # Generate standard instruction
        instruction = self._get_standard_instruction(
            name, description, enable_interactive_tools
        )

        # Debug: Log what we're passing to LlmAgent
        logger.info(
            f"StandardGikiAgent passing to LlmAgent: name={name}, model={model_name}"
        )
        logger.info(f"StandardGikiAgent kwargs keys: {list(kwargs.keys())}")

        # Remove any fields that might conflict with LlmAgent's fields
        # LlmAgent already defines: name, model, description, instruction, tools
        filtered_parent_kwargs = {
            k: v
            for k, v in kwargs.items()
            if k
            not in [
                "name",
                "model",
                "description",
                "instruction",
                "tools",
                "model_name",
            ]
        }
        logger.info(
            f"StandardGikiAgent filtered kwargs keys: {list(filtered_parent_kwargs.keys())}"
        )

        # Initialize LlmAgent with official ADK v1.3.0 pattern
        # Note: LlmAgent doesn't accept instruction in __init__, it's set as a property
        super().__init__(
            name=name,
            model=model_name,
            **filtered_parent_kwargs,
        )

        # Set properties after initialization
        self.description = description
        self.instruction = instruction
        self.tools = all_tools

        # Store configuration for A2A integration
        self._agent_config = {
            "name": name,
            "description": description,
            "version": "1.0.0",
            "capabilities": {
                "streaming": True,
                "pushNotifications": True,
                "multimodal": True,
                "financial_processing": True,
                "real_time_data": True,
            },
            "built_in_tools": [
                tool.__class__.__name__
                for tool in standard_tools
                if hasattr(tool, "__class__")
            ],
            "custom_tools": [
                tool.name if hasattr(tool, "name") else str(tool)
                for tool in (custom_tools or [])
            ],
        }

        # Log tool efficiency information
        tool_efficiency_info = (
            f"StandardGikiAgent '{name}' initialized with {len(all_tools)} tools "
        )
        if enable_standard_tools:
            if standard_tool_set:
                tool_efficiency_info += f"({len(custom_tools or [])} custom + {len(standard_tools)} selective standard)"
            else:
                tool_efficiency_info += f"({len(custom_tools or [])} custom + {len(standard_tools)} legacy standard)"
        else:
            tool_efficiency_info += (
                f"({len(custom_tools or [])} custom only - EFFICIENT MODE)"
            )

        logger.info(tool_efficiency_info)

    def _get_standard_instruction(
        self, name: str, description: str, enable_interactive: bool
    ) -> str:
        """Generate standard instruction template for financial agents."""

        base_instruction = f"""
        You are {name}, a specialized financial AI agent for the giki.ai platform.
        
        Purpose: {description}
        
        You are optimized for cognitive efficiency with carefully selected tools that directly support your purpose.
        Focus on using your domain-specific tools for the best results.
        """

        if enable_interactive:
            base_instruction += """
            
        **Interactive Tools Available:**
        - get_user_choice: Use for decision workflows requiring user confirmation
        """

        base_instruction += """
        
        **Core Principles:**
        - Use your specialized tools efficiently for maximum accuracy
        - Work with multi-tenant financial data maintaining proper isolation
        - Handle various accounting systems (Indian, US, Global formats)
        - Provide precise, professional financial analysis
        - Maintain high accuracy with confidence scoring
        
        **Performance:**
        - Focus on your domain expertise and core tools
        - Deliver concrete results through your specialized capabilities
        - Be efficient and direct in your responses
        """

        return base_instruction.strip()

    def get_agent_card_info(self) -> Dict[str, Any]:
        """Get agent card information for A2A protocol discovery."""
        return self._agent_config.copy()
