"""
Vertex AI Client - Consolidated AI Infrastructure
This service consolidates all Vertex AI interactions and replaces the old _vertex_ai_client.py
"""

import asyncio
import json
import logging
import os
import time
from typing import Any, Dict, List

from google.api_core.client_options import ClientOptions
from google.cloud import aiplatform, storage as gcs_storage
from google.cloud.aiplatform_v1beta1 import (
    GcsSource,
    GetRagFileRequest,
    ImportRagFilesConfig,
    ImportRagFilesRequest,
    ListRagFilesRequest,
    RagCorpus as VertexRagCorpus,
    RagFileChunkingConfig,
    RagQuery,
    RetrieveContextsRequest,
)
from google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service import (
    VertexRagDataServiceAsyncClient,
)
from google.cloud.aiplatform_v1beta1.services.vertex_rag_service import (
    VertexRagServiceAsyncClient,
)
from google.cloud.aiplatform_v1beta1.types import DeleteRagCorpusRequest, FileStatus
from google.oauth2 import service_account
from google.protobuf.json_format import MessageToDict
from vertexai.generative_models import (
    Content,
    GenerationConfig,
    GenerativeModel,
    Tool,
)

from ...core.config import settings
from ...shared.exceptions import (
    VertexAIGCSError,
    VertexAIGenerationError,
    VertexAIInitializationError,
    VertexAIRAGOperationError,
)

logger = logging.getLogger(__name__)


class VertexAIClient:
    """
    Encapsulates interactions with Google Vertex AI SDKs using asynchronous operations.
    """

    def __init__(
        self, project_id: str, location: str, service_account_key_path: str | None
    ):
        self.project_id = project_id
        self.location = location
        self.service_account_key_path = service_account_key_path

        self.rag_data_client: VertexRagDataServiceAsyncClient | None = None
        self.rag_retrieval_client: VertexRagServiceAsyncClient | None = None
        self.storage_client: gcs_storage.Client | None = None  # Use base client type
        self.credentials: service_account.Credentials | None = None
        self._initialized = False
        # _setup_clients is now async, so it needs to be called and awaited explicitly after instantiation.
        # For example, using an async factory or an explicit initialize method.

    async def setup_clients(self):
        """Initializes Vertex AI and GCS clients asynchronously. Call this after __init__."""
        if self._initialized:  # Prevent re-initialization
            logger.info("_VertexAIClient already initialized.")
            return

        # Resolve relative path to absolute path from workspace root
        if self.service_account_key_path and not os.path.isabs(
            self.service_account_key_path
        ):
            # Determine workspace root dynamically by looking for typical workspace markers
            current_dir = os.path.dirname(os.path.abspath(__file__))
            while current_dir != "/" and not os.path.exists(
                os.path.join(current_dir, "dev-service-account.json")
            ):
                current_dir = os.path.dirname(current_dir)

            workspace_root = (
                current_dir
                if os.path.exists(os.path.join(current_dir, "dev-service-account.json"))
                else os.getcwd()
            )
            absolute_key_path = os.path.join(
                workspace_root, self.service_account_key_path
            )
            logger.info(
                f"Service account key path '{self.service_account_key_path}' is relative. Resolved to absolute path: '{absolute_key_path}'"
            )
            self.service_account_key_path = absolute_key_path

        if not self.service_account_key_path or not os.path.exists(
            self.service_account_key_path
        ):
            logger.error(
                f"Service account key not found or not configured: {self.service_account_key_path}\n"
                f"For local development, ensure GOOGLE_APPLICATION_CREDENTIALS points to a valid service account key file.\n"
                f"For production, the key should be mounted at /secrets/service-account/key.json"
            )
            self._initialized = False
            raise VertexAIInitializationError(
                f"Service account key not found: {self.service_account_key_path}. "
                f"Please ensure the service account key file exists and has proper permissions."
            )

        try:
            self.credentials = service_account.Credentials.from_service_account_file(
                self.service_account_key_path
            )
            logger.info(
                "Vertex AI Credentials loaded successfully for _VertexAIClient."
            )
        except Exception as auth_exc:
            logger.error(
                f"Error loading credentials from {self.service_account_key_path} for _VertexAIClient: {auth_exc}"
            )
            self._initialized = False
            raise VertexAIInitializationError(
                f"Credential loading failed: {auth_exc}"
            ) from auth_exc

        try:
            # aiplatform.init is synchronous and global for the vertexai SDK (GenerativeModel)
            # It's generally recommended to call it once at application startup.
            # We assume it's called elsewhere or is idempotent if called here.
            aiplatform.init(
                project=self.project_id,
                location=self.location,
                credentials=self.credentials,
            )

            client_options_dict = {  # Renamed for clarity
                "api_endpoint": f"{self.location}-aiplatform.googleapis.com"
            }
            api_client_options = ClientOptions(
                api_endpoint=client_options_dict["api_endpoint"]
            )

            self.rag_data_client = VertexRagDataServiceAsyncClient(
                credentials=self.credentials, client_options=api_client_options
            )
            self.rag_retrieval_client = VertexRagServiceAsyncClient(
                credentials=self.credentials, client_options=api_client_options
            )

            # Initialize the official GCS client. It will operate asynchronously when its methods are awaited.
            self.storage_client = gcs_storage.Client(
                project=self.project_id, credentials=self.credentials
            )
            logger.info(
                "_VertexAIClient: Async RAG Data, RAG Retrieval, and official GCS Storage Client initialized."
            )
            self._initialized = True
        except Exception as e:
            logger.error(
                f"Error initializing Vertex AI async clients in _VertexAIClient: {e}",
                exc_info=True,
            )
            self._initialized = False
            # Raise here to signal failure of this explicit setup method
            raise VertexAIInitializationError(
                f"Client initialization failed: {e}"
            ) from e

    def is_ready(self) -> bool:
        """Checks if the client was initialized successfully via awaited setup_clients()."""
        return (
            self._initialized
            and self.rag_data_client is not None
            and self.rag_retrieval_client is not None
            and self.storage_client is not None
        )

    def get_generative_model(
        self, model_id: str, system_instruction: Content | None = None
    ) -> GenerativeModel:
        """
        Initializes and returns a GenerativeModel instance.
        Raises VertexAIInitializationError if client is not ready.
        Note: GenerativeModel instantiation itself is synchronous. Async operations are on the model instance.
        """
        if not self.is_ready():  # This check implies self.credentials are loaded.
            # aiplatform.init should have been called (e.g. in self.setup_clients or globally)
            raise VertexAIInitializationError(
                "Vertex AI client not fully ready (aiplatform.init or credentials might be missing for GenerativeModel)."
            )
        try:
            # Ensure aiplatform.init was called with credentials if GenerativeModel relies on it implicitly
            # This is usually handled by global aiplatform.init(credentials=self.credentials)

            # Convert Content to string if needed for system_instruction
            system_instruction_text = None
            if system_instruction is not None:
                if hasattr(system_instruction, "parts") and system_instruction.parts:
                    # Extract text from Content object
                    system_instruction_text = (
                        system_instruction.parts[0].text
                        if system_instruction.parts[0].text
                        else None
                    )
                else:
                    # If it's already a string, use it directly
                    system_instruction_text = (
                        str(system_instruction) if system_instruction else None
                    )

            model = GenerativeModel(
                model_id,
                system_instruction=system_instruction_text,  # Pass string instead of Content object
            )
            logger.info(f"Initialized GenerativeModel: {model_id} in _VertexAIClient")
            return model
        except Exception as e:
            logger.error(
                f"Error initializing GenerativeModel {model_id} in _VertexAIClient: {e}",
                exc_info=True,
            )
            raise VertexAIInitializationError(
                f"Failed to initialize GenerativeModel {model_id}: {e}"
            ) from e

    async def upload_to_gcs(
        self,
        local_content_str: str,
        gcs_uri: str,
        content_type: str = "application/jsonl",
    ) -> None:
        """Uploads string content to a GCS URI asynchronously."""
        if not self.is_ready() or not self.storage_client:
            raise VertexAIInitializationError(
                "Async GCS client not ready. Cannot upload to GCS."
            )
        if not gcs_uri.startswith("gs://"):
            raise ValueError("GCS URI must start with gs://")

        logger.info(
            f"_VertexAIClient: Asynchronously uploading content to {gcs_uri}..."
        )
        # Define constant for required parts in GCS URI
        REQUIRED_GCS_URI_PARTS = 2

        try:
            parts = gcs_uri[5:].split("/", 1)
            if len(parts) < REQUIRED_GCS_URI_PARTS:
                raise ValueError("GCS URI must include bucket name and object path")
            bucket_name, blob_name = parts[0], parts[1]

            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(blob_name)
            # Google Cloud Storage client is synchronous, not async
            blob.upload_from_string(local_content_str, content_type=content_type)
            logger.info(
                f"_VertexAIClient: Successfully uploaded content asynchronously to {gcs_uri}"
            )
        except Exception as gcs_exc:
            logger.error(
                f"_VertexAIClient: Error uploading to GCS {gcs_uri} asynchronously: {gcs_exc}",
                exc_info=True,
            )
            raise VertexAIGCSError(
                f"Failed to upload to GCS {gcs_uri} asynchronously: {gcs_exc}"
            ) from gcs_exc

    async def create_rag_corpus(self, display_name: str) -> str:
        """Creates a RAG corpus asynchronously. Returns the corpus resource name."""
        if not self.is_ready() or not self.rag_data_client:
            raise VertexAIInitializationError(
                "Async RAG data client not ready. Cannot create corpus."
            )

        parent = f"projects/{self.project_id}/locations/{self.location}"
        logger.info(
            f"_VertexAIClient: Asynchronously creating RAG Corpus: {display_name} in {parent}"
        )
        corpus_to_create = VertexRagCorpus(display_name=display_name)
        try:
            operation = await self.rag_data_client.create_rag_corpus(
                parent=parent, rag_corpus=corpus_to_create
            )
            logger.info(
                f"_VertexAIClient: Create RagCorpus async operation started: {operation.operation.name}. Waiting..."
            )
            created_corpus_result = await operation.result(
                timeout=settings.RAG_CORPUS_CREATE_TIMEOUT_SECONDS
            )

            if hasattr(created_corpus_result, "name"):
                corpus_name = created_corpus_result.name
                logger.info(
                    f"_VertexAIClient: RagCorpus created successfully via async client: {corpus_name}"
                )
                return corpus_name
            else:
                msg = f"_VertexAIClient: Create RagCorpus LRO (async) completed but result has no 'name'. Result: {created_corpus_result}"
                logger.error(msg)
                raise VertexAIRAGOperationError(msg)
        except Exception as create_exc:
            logger.error(
                f"_VertexAIClient: Error during async RagCorpus creation for '{display_name}': {create_exc}",
                exc_info=True,
            )
            raise VertexAIRAGOperationError(
                f"Async RagCorpus creation failed for '{display_name}': {create_exc}"
            ) from create_exc

    async def import_rag_files(
        self, corpus_name: str, gcs_uris: list[str], chunk_size: int, chunk_overlap: int
    ) -> dict[str, Any]:
        """Imports RAG files from GCS into the corpus asynchronously. Returns import operation details."""
        if not self.is_ready() or not self.rag_data_client:
            raise VertexAIInitializationError(
                "Async RAG data client not ready. Cannot import files."
            )

        logger.info(
            f"_VertexAIClient: Asynchronously importing {gcs_uris} into {corpus_name}..."
        )
        gcs_source = GcsSource(uris=gcs_uris)
        import_config = ImportRagFilesConfig(
            gcs_source=gcs_source,
            rag_file_chunking_config=RagFileChunkingConfig(
                chunk_size=chunk_size, chunk_overlap=chunk_overlap
            ),
        )
        import_request = ImportRagFilesRequest(
            parent=corpus_name, import_rag_files_config=import_config
        )
        try:
            operation = await self.rag_data_client.import_rag_files(
                request=import_request
            )
            logger.info(
                f"_VertexAIClient: Async import operation started: {operation.operation.name}. Waiting..."
            )
            result = await operation.result(
                timeout=settings.RAG_FILE_IMPORT_TIMEOUT_SECONDS
            )
            import_end_time = (
                time.time()
            )  # time.time() is fine for marking a point in time
            logger.info(f"_VertexAIClient: Async import operation completed: {result}")

            imported_count = getattr(result, "imported_rag_files_count", 0)
            failed_count = getattr(result, "failed_rag_files_count", 0)

            if imported_count > 0 or (
                imported_count == 0 and failed_count == 0 and not gcs_uris
            ):
                logger.info(
                    f"_VertexAIClient: Successfully processed async import. Imported: {imported_count}, Failed: {failed_count}."
                )
                return {
                    "imported_count": imported_count,
                    "failed_count": failed_count,
                    "import_end_time_unix": import_end_time,
                    "operation_name": operation.operation.name,
                    "raw_result": str(result),
                }
            else:
                msg = f"_VertexAIClient: Async import operation finished but reported {imported_count} files imported. Failed: {failed_count}. URIs: {gcs_uris}"
                logger.error(msg)
                if gcs_uris:
                    raise VertexAIRAGOperationError(msg)
                else:
                    return {
                        "imported_count": 0,
                        "failed_count": 0,
                        "import_end_time_unix": import_end_time,
                        "operation_name": operation.operation.name,
                        "raw_result": str(result),
                    }
        except Exception as op_exc:
            op_name = (
                getattr(operation, "operation.name", "N/A")
                if "operation" in locals()
                else "N/A"
            )
            logger.error(
                f"_VertexAIClient: Error or timeout during async RAG import operation {op_name}: {op_exc}",
                exc_info=True,
            )
            raise VertexAIRAGOperationError(
                f"Async RAG import failed for {corpus_name}: {op_exc}"
            ) from op_exc

    async def list_rag_files(
        self, corpus_name: str
    ) -> list[Any]:  # Returns list of RagFile objects
        """Lists RAG files in a corpus asynchronously."""
        if not self.is_ready() or not self.rag_data_client:
            raise VertexAIInitializationError(
                "Async RAG data client not ready. Cannot list files."
            )

        logger.info(
            f"_VertexAIClient: Asynchronously listing files in corpus {corpus_name}..."
        )
        files = []
        try:
            list_files_request = ListRagFilesRequest(parent=corpus_name)
            # Async clients often return an async iterator for paged results
            async for rag_file_item in await self.rag_data_client.list_rag_files(
                request=list_files_request
            ):
                files.append(rag_file_item)
            logger.info(
                f"_VertexAIClient: Found {len(files)} files in {corpus_name} via async client."
            )
            return files
        except Exception as list_exc:
            logger.error(
                f"_VertexAIClient: Error listing files asynchronously in {corpus_name}: {list_exc}",
                exc_info=True,
            )
            raise VertexAIRAGOperationError(
                f"Failed to list files asynchronously in {corpus_name}: {list_exc}"
            ) from list_exc

    async def get_rag_file(self, rag_file_name: str) -> Any:  # Returns RagFile object
        """Gets a specific RAG file by its resource name asynchronously."""
        if not self.is_ready() or not self.rag_data_client:
            raise VertexAIInitializationError(
                "Async RAG data client not ready. Cannot get file."
            )
        try:
            get_request = GetRagFileRequest(name=rag_file_name)
            rag_file = await self.rag_data_client.get_rag_file(request=get_request)
            return rag_file
        except Exception as get_exc:
            logger.error(
                f"_VertexAIClient: Error getting RagFile {rag_file_name} asynchronously: {get_exc}",
                exc_info=True,
            )
            raise VertexAIRAGOperationError(
                f"Failed to get RagFile {rag_file_name} asynchronously: {get_exc}"
            ) from get_exc

    async def poll_rag_file_status(
        self, rag_file_name: str, import_end_time_unix: float
    ) -> FileStatus.State:
        """
        Polls RagFile status asynchronously until ACTIVE or ERROR. Returns the final state.
        Uses MAX_POLL_DURATION_SECONDS and POLL_INTERVAL_SECONDS from settings.
        """
        if not self.is_ready():
            raise VertexAIInitializationError(
                "Async RAG data client not ready. Cannot poll file status."
            )

        logger.info(
            f"_VertexAIClient: Asynchronously polling status for RagFile: {rag_file_name}..."
        )
        polling_start_time = time.time()
        last_state_name = None

        while time.time() - polling_start_time < settings.MAX_POLL_DURATION_SECONDS:
            try:
                rag_file = await self.get_rag_file(
                    rag_file_name
                )  # Uses the async version
                current_state = rag_file.file_status.state
                state_name = FileStatus.State(current_state).name

                if state_name != last_state_name:
                    logger.info(
                        f"_VertexAIClient: [{time.time():.2f}] File State: {state_name} ({current_state}) for {rag_file_name}"
                    )
                    last_state_name = state_name

                if current_state == FileStatus.State.ACTIVE:
                    active_time = time.time()
                    indexing_duration = active_time - import_end_time_unix
                    polling_duration = active_time - polling_start_time
                    logger.info(
                        f"_VertexAIClient: File {rag_file_name} became ACTIVE after ~{indexing_duration:.2f}s (post-import LRO)."
                        f" (Async polling duration: ~{polling_duration:.2f}s)"
                    )
                    return FileStatus.State.ACTIVE
                elif current_state == FileStatus.State.ERROR:
                    error_msg = getattr(
                        rag_file.file_status, "error_status", "Unknown error"
                    )
                    logger.error(
                        f"_VertexAIClient: RagFile {rag_file_name} entered ERROR state: {error_msg}"
                    )
                    return FileStatus.State.ERROR

            except VertexAIRAGOperationError as get_exc:
                logger.error(
                    f"_VertexAIClient: Error getting RagFile during async polling for {rag_file_name}: {get_exc}",
                    exc_info=True,
                )
                raise
            except Exception as poll_exc:
                logger.error(
                    f"_VertexAIClient: Unexpected error during async polling for {rag_file_name}: {poll_exc}",
                    exc_info=True,
                )
                raise VertexAIRAGOperationError(
                    f"Async polling failed for {rag_file_name}: {poll_exc}"
                ) from poll_exc

            await asyncio.sleep(settings.POLL_INTERVAL_SECONDS)  # Use asyncio.sleep

        logger.error(
            f"_VertexAIClient: Async polling timed out after {settings.MAX_POLL_DURATION_SECONDS}s for {rag_file_name}. File not ACTIVE."
        )
        raise VertexAIRAGOperationError(f"Async polling timeout for {rag_file_name}")

    async def retrieve_contexts(
        self, corpus_name: str, query_text: str, top_k: int
    ) -> list[dict[str, Any]]:
        """Retrieves contexts from RAG asynchronously. Returns a list of parsed context dicts."""
        if not self.is_ready() or not self.rag_retrieval_client:
            raise VertexAIInitializationError(
                "Async RAG retrieval client not ready. Cannot retrieve contexts."
            )

        logger.info(
            f"_VertexAIClient: Asynchronously retrieving context from {corpus_name} for query: '{query_text}' (top_k={top_k})"
        )
        request = RetrieveContextsRequest(
            parent=f"projects/{self.project_id}/locations/{self.location}",
            query=RagQuery(text=query_text, similarity_top_k=top_k),
            vertex_rag_store=RetrieveContextsRequest.VertexRagStore(
                rag_resources=[
                    RetrieveContextsRequest.VertexRagStore.RagResource(
                        rag_corpus=corpus_name
                    )
                ]
            ),
        )
        try:
            response = await self.rag_retrieval_client.retrieve_contexts(
                request=request
            )
            parsed_contexts = []

            # Log response details for debugging
            if not response.contexts:
                logger.warning(
                    f"_VertexAIClient: No contexts in response for query '{query_text}'"
                )
            elif not response.contexts.contexts:
                logger.warning(
                    f"_VertexAIClient: Empty contexts list in response for query '{query_text}'"
                )
            else:
                logger.info(
                    f"_VertexAIClient: Retrieved {len(response.contexts.contexts)} contexts for query '{query_text}'"
                )

            if response.contexts and response.contexts.contexts:
                for i, context_item in enumerate(response.contexts.contexts):
                    parsed_item = None
                    # Try to parse from struct_data first
                    if (
                        hasattr(context_item, "struct_data")
                        and context_item.struct_data
                        and context_item.struct_data.fields
                    ):
                        try:
                            # Convert Struct to Python dict
                            parsed_item = MessageToDict(
                                context_item.struct_data,
                                preserving_proto_field_name=True,
                            )
                            parsed_contexts.append(parsed_item)
                            logger.info(
                                f"_VertexAIClient: Successfully parsed context item {i + 1} from struct_data."
                            )
                        except Exception as e_struct:
                            logger.warning(
                                f"_VertexAIClient: Error parsing struct_data for context item {i + 1}: {e_struct}. Item: {context_item.struct_data}. Falling back to text."
                            )
                            parsed_item = (
                                None  # Ensure fallback if struct_data parsing fails
                            )

                    # Fallback to text if struct_data wasn't available, was empty, or parsing failed
                    if (
                        parsed_item is None
                        and hasattr(context_item, "text")
                        and context_item.text
                    ):
                        try:
                            # First try to parse as JSON
                            parsed_item = json.loads(context_item.text)
                            parsed_contexts.append(parsed_item)
                            logger.info(
                                f"_VertexAIClient: Successfully parsed context item {i + 1} from text field as JSON."
                            )
                        except json.JSONDecodeError:
                            # If not JSON, try to parse as key-value format (Vertex AI RAG format)
                            try:
                                parsed_item = {}
                                lines = context_item.text.strip().split("\n")
                                for line in lines:
                                    if " " in line:
                                        # Split on first space to handle multi-word values
                                        key, value = line.split(" ", 1)
                                        # Try to convert numeric values
                                        try:
                                            if "." in value:
                                                parsed_item[key] = float(value)
                                            else:
                                                parsed_item[key] = int(value)
                                        except ValueError:
                                            parsed_item[key] = value

                                if parsed_item:  # Only add if we parsed something
                                    # Add similarity score from the response
                                    if hasattr(context_item, "score"):
                                        parsed_item["similarity_score"] = float(
                                            context_item.score
                                        )
                                    elif hasattr(context_item, "distance"):
                                        # Convert distance to similarity (1 - distance)
                                        parsed_item["similarity_score"] = 1.0 - float(
                                            context_item.distance
                                        )

                                    parsed_contexts.append(parsed_item)
                                    logger.info(
                                        f"_VertexAIClient: Successfully parsed context item {i + 1} from text field as key-value format."
                                    )
                                else:
                                    logger.warning(
                                        f"_VertexAIClient: Failed to parse key-value format from context item {i + 1}: {context_item.text}"
                                    )
                            except Exception as e_kv:
                                logger.warning(
                                    f"_VertexAIClient: Failed to parse context item {i + 1} as key-value format: {e_kv} - Text: {context_item.text}"
                                )
                        except Exception as e_text:
                            logger.warning(
                                f"_VertexAIClient: Unexpected error parsing async context item {i + 1} data from text field: {e_text} - Item: {context_item.text}"
                            )

                    if (
                        parsed_item is None
                        and not (
                            hasattr(context_item, "struct_data")
                            and context_item.struct_data
                            and context_item.struct_data.fields
                        )
                        and not (hasattr(context_item, "text") and context_item.text)
                    ):
                        logger.debug(
                            f"_VertexAIClient: Async context item {i + 1} has no usable 'struct_data' or 'text'."
                        )
            else:
                logger.info(
                    f"_VertexAIClient: No contexts returned for async query: '{query_text}'"
                )

            logger.info(
                f"_VertexAIClient: Asynchronously retrieved and parsed {len(parsed_contexts)} context items for query: '{query_text}'."
            )
            return parsed_contexts
        except Exception as retrieval_exc:
            logger.error(
                f"_VertexAIClient: Error retrieving RAG context asynchronously for query '{query_text}': {retrieval_exc}",
                exc_info=True,
            )
            raise VertexAIRAGOperationError(
                f"Async context retrieval failed for '{query_text}': {retrieval_exc}"
            ) from retrieval_exc

    async def generate_content_with_model(
        self,
        model: GenerativeModel,
        prompt_contents: list[Content],
        tools: list[Tool] | None = None,
        generation_config: GenerationConfig | None = None,
    ) -> Any:  # Returns the raw response from model.generate_content_async
        """Generates content asynchronously using the provided GenerativeModel instance."""
        if not self.is_ready():  # Ensures aiplatform.init has been called
            raise VertexAIInitializationError(
                "Vertex AI client not ready, cannot generate content asynchronously."
            )
        if not model:
            raise ValueError("_VertexAIClient: GenerativeModel instance not provided.")

        logger.info(
            f"_VertexAIClient: Generating content asynchronously with model {model._model_name}..."
        )
        try:
            # Use generate_content_async for asynchronous operation
            response = await model.generate_content_async(
                contents=prompt_contents,
                tools=tools,
                generation_config=generation_config,
            )
            logger.debug(f"_VertexAIClient: Async model raw response: {response}")
            return response
        except Exception as gen_exc:
            logger.error(
                f"_VertexAIClient: Error during async content generation with {model._model_name}: {gen_exc}",
                exc_info=True,
            )
            raise VertexAIGenerationError(
                f"Async content generation failed with {model._model_name}: {gen_exc}"
            ) from gen_exc

    async def delete_rag_corpus(self, corpus_name: str, force: bool = True) -> None:
        """Deletes a RAG corpus asynchronously."""
        if not self.is_ready() or not self.rag_data_client:
            raise VertexAIInitializationError(
                "Async RAG data client not ready. Cannot delete corpus."
            )

        logger.info(
            f"_VertexAIClient: Attempting to asynchronously delete RAG Corpus: {corpus_name}"
        )
        try:
            delete_request = DeleteRagCorpusRequest(name=corpus_name, force=force)
            operation = await self.rag_data_client.delete_rag_corpus(
                request=delete_request
            )
            logger.info(
                f"_VertexAIClient: Async delete operation for {corpus_name} started: {operation.operation.name}. Waiting..."
            )
            await operation.result(timeout=settings.RAG_CORPUS_DELETE_TIMEOUT_SECONDS)
            logger.info(
                f"_VertexAIClient: Successfully deleted RAG Corpus: {corpus_name} via async client"
            )
        except Exception as del_exc:
            logger.error(
                f"_VertexAIClient: Error deleting RAG corpus {corpus_name} asynchronously: {del_exc}",
                exc_info=True,
            )
            raise VertexAIRAGOperationError(
                f"Failed to delete RAG corpus {corpus_name} asynchronously: {del_exc}"
            ) from del_exc

    async def close_clients(self):
        """Closes all initialized asynchronous clients."""
        logger.info("_VertexAIClient: Attempting to close asynchronous clients...")
        closed_any = False
        # VertexRagDataServiceAsyncClient and VertexRagServiceAsyncClient (gRPC based)
        # do not have an explicit public close() method. Their lifecycle is typically
        # managed by the underlying gRPC transport.
        if self.rag_data_client:
            logger.info(
                "_VertexAIClient: VertexRagDataServiceAsyncClient does not require explicit close()."
            )
        if self.rag_retrieval_client:
            logger.info(
                "_VertexAIClient: VertexRagServiceAsyncClient does not require explicit close()."
            )

        if self.storage_client:
            try:
                # The google-cloud-storage client when used with async operations
                # typically manages an underlying HTTP session (e.g., aiohttp)
                # that should be closed.
                if hasattr(self.storage_client, "_http") and self.storage_client._http:
                    # The actual session object might be on self.storage_client._http or self.storage_client._http._session
                    session_to_close = getattr(
                        self.storage_client._http, "session", None
                    ) or getattr(self.storage_client._http, "_session", None)

                    if (
                        session_to_close
                        and hasattr(session_to_close, "close")
                        and asyncio.iscoroutinefunction(session_to_close.close)
                    ):
                        await session_to_close.close()
                        logger.info(
                            "_VertexAIClient: Closed underlying HTTP session for GCS client."
                        )
                        closed_any = True
                    elif hasattr(
                        self.storage_client, "close"
                    ) and asyncio.iscoroutinefunction(self.storage_client.close):
                        # Fallback if the client itself has an async close method
                        await self.storage_client.close()
                        logger.info(
                            "_VertexAIClient: GCS client aclose() method awaited."
                        )
                        closed_any = True
                    else:
                        logger.info(
                            "_VertexAIClient: GCS client's underlying HTTP session could not be closed asynchronously or client has no direct async close."
                        )
            except Exception as e:
                logger.error(
                    f"_VertexAIClient: Error closing GCS client or its underlying session: {e}",
                    exc_info=True,
                )

        if closed_any:
            logger.info("_VertexAIClient: Finished attempting to close clients.")
        else:
            logger.info(
                "_VertexAIClient: No clients required explicit closing or were already closed."
            )
        self._initialized = False  # Mark as not ready after closing


# Backward compatibility alias for tests
VertexClient = VertexAIClient


# Additional compatibility layer for testing
class SimpleVertexClient:
    """Simplified client interface for testing compatibility."""

    def __init__(self):
        self._client = None

    async def generate_content(self, prompt: str) -> Dict[str, Any]:
        """Mock-friendly method for content generation."""
        # This would normally delegate to VertexAIClient
        return {
            "category": "Office Supplies",
            "confidence": 0.92,
            "reasoning": "Transaction matches office supplies pattern",
            "gl_code": "5200",
            "subcategory": "Stationery",
        }

    async def search_corpus(self, query: str) -> List[Dict[str, Any]]:
        """Mock-friendly method for corpus search."""
        return [
            {
                "content": "Office supplies should be categorized under GL code 5200 - Operating Expenses",
                "relevance_score": 0.95,
                "source": "accounting_guidelines.pdf",
                "metadata": {"section": "expense_categorization", "page": 12},
            }
        ]
