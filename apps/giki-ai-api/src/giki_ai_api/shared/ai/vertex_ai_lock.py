"""
Global Vertex AI Concurrency Lock
=================================

This module provides a GLOBAL lock to prevent ANY concurrent Vertex AI operations
across the entire application. This is necessary because Vertex AI GenerativeModel
has concurrency limitations that cause "cannot perform operation: another operation 
is in progress" errors.

Usage:
    from ...shared.ai.vertex_ai_lock import get_global_vertex_ai_lock

    async def my_vertex_ai_function():
        async with get_global_vertex_ai_lock():
            # Your Vertex AI operations here
            model = GenerativeModel("gemini-2.0-flash-001")
            response = await model.generate_content_async(prompt)
            return response.text
"""

import asyncio
import logging
from typing import Any, Dict, Optional

from vertexai.generative_models import GenerativeModel

logger = logging.getLogger(__name__)

# GLOBAL lock for ALL Vertex AI operations across the entire application
_global_vertex_ai_lock = asyncio.Lock()

# GLOBAL shared model instance 
_global_vertex_ai_model = None


def get_global_vertex_ai_lock() -> asyncio.Lock:
    """
    Get the global Vertex AI lock that prevents ALL concurrent operations.
    
    This lock must be acquired before ANY Vertex AI operation anywhere in the application.
    """
    return _global_vertex_ai_lock


async def get_global_vertex_ai_model() -> GenerativeModel:
    """
    Get or create the global shared Vertex AI model instance.
    
    This ensures only ONE GenerativeModel instance exists across the entire application.
    """
    global _global_vertex_ai_model
    
    if _global_vertex_ai_model is None:
        _global_vertex_ai_model = GenerativeModel("gemini-2.0-flash-001")
        logger.info("Created global shared Vertex AI model instance")
    
    return _global_vertex_ai_model


async def safe_vertex_ai_call(prompt: str, generation_config: Optional[Dict[str, Any]] = None) -> str:
    """
    Make a safe Vertex AI call with improved concurrency and retry logic.
    
    Uses a timeout-based lock to allow reasonable concurrency while preventing
    the "another operation is in progress" errors that Vertex AI throws.
    
    Args:
        prompt: The prompt to send to the AI
        generation_config: Optional generation configuration
    
    Returns:
        The response text from the AI
    """
    # Attempt to acquire lock with timeout to prevent indefinite blocking
    timeout = 30.0  # Maximum 30 seconds wait
    
    try:
        # Try to acquire lock with timeout using asyncio.wait_for
        await asyncio.wait_for(_global_vertex_ai_lock.acquire(), timeout=timeout)
    except asyncio.TimeoutError:
        logger.error(f"Vertex AI lock acquisition timed out after {timeout}s")
        raise Exception(f"AI service busy - could not acquire lock within {timeout} seconds")
    
    try:
        model = await get_global_vertex_ai_model()
        
        config = generation_config or {
            "temperature": 0.1,
            "max_output_tokens": 500,
            "response_mime_type": "application/json",
        }
        
        # Add retry logic for transient Vertex AI errors
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = await model.generate_content_async(prompt, generation_config=config)
                return response.text
            except Exception as e:
                if attempt < max_retries - 1 and "operation is in progress" in str(e).lower():
                    # Wait briefly and retry for concurrency errors
                    await asyncio.sleep(0.5 * (attempt + 1))
                    continue
                raise
                
    except Exception as e:
        logger.error(f"Vertex AI call failed after retries: {e}")
        raise
    finally:
        # Always release the lock
        _global_vertex_ai_lock.release()


async def reset_global_vertex_ai_model():
    """
    Reset the global Vertex AI model instance.
    
    This can be useful for testing or if the model needs to be recreated.
    """
    global _global_vertex_ai_model
    async with _global_vertex_ai_lock:
        _global_vertex_ai_model = None
        logger.info("Reset global Vertex AI model instance")