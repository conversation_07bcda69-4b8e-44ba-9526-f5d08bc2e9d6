"""
Comprehensive Error Handling Middleware for FastAPI
==================================================

Provides global error handling, logging, and response formatting.
"""

import logging
import traceback
import uuid
from typing import Any, Dict

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ..exceptions import (
    AgentError,
    AuthenticationError,
    ErrorSeverity,
    GikiAIBaseError,
    ServiceError,
    ValidationError,
)

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """
    Global error handling middleware for consistent error responses.

    Features:
    - Converts all exceptions to proper HTTP responses
    - Adds correlation IDs for error tracking
    - Provides structured error logging
    - Handles different error severities appropriately
    """

    async def dispatch(self, request: Request, call_next):
        """Process request with comprehensive error handling."""

        # Generate correlation ID for this request
        correlation_id = str(uuid.uuid4())
        request.state.correlation_id = correlation_id

        try:
            response = await call_next(request)
            return response

        except HTTPException as e:
            # FastAPI HTTP exceptions - pass through with correlation ID
            return self._create_error_response(
                error_code="HTTP_ERROR",
                message=e.detail,
                status_code=e.status_code,
                correlation_id=correlation_id,
            )

        except GikiAIBaseError as e:
            # Our custom exceptions - convert with full context
            logger.error(
                f"Giki AI Error: {e}",
                extra={
                    "correlation_id": correlation_id,
                    "error_type": type(e).__name__,
                    "error_context": e.context,
                    "severity": e.severity.value,
                    "path": request.url.path,
                    "method": request.method,
                },
            )

            return self._create_error_response(
                error_code=e.error_code,
                message=e.message,
                status_code=self._get_status_code_for_error(e),
                correlation_id=correlation_id,
                context=e.context,
                recovery_suggestions=e.recovery_suggestions,
                severity=e.severity.value,
            )

        except Exception as e:
            # Unexpected errors - log with full traceback and return generic error
            logger.critical(
                f"Unexpected error in {request.method} {request.url.path}: {e}",
                extra={
                    "correlation_id": correlation_id,
                    "traceback": traceback.format_exc(),
                    "path": request.url.path,
                    "method": request.method,
                },
            )

            return self._create_error_response(
                error_code="INTERNAL_SERVER_ERROR",
                message="An unexpected error occurred. Please try again later.",
                status_code=500,
                correlation_id=correlation_id,
                severity="critical",
            )

    def _get_status_code_for_error(self, error: GikiAIBaseError) -> int:
        """Map error types to appropriate HTTP status codes."""

        if isinstance(error, ValidationError):
            return 400
        elif isinstance(error, AuthenticationError):
            return 401
        elif isinstance(error, ServiceError):
            if error.severity == ErrorSeverity.CRITICAL:
                return 503  # Service Unavailable
            elif error.severity == ErrorSeverity.HIGH:
                return 500  # Internal Server Error
            else:
                return 422  # Unprocessable Entity
        elif isinstance(error, AgentError):
            return 502  # Bad Gateway (agent communication issue)
        else:
            return 500  # Internal Server Error

    def _create_error_response(
        self,
        error_code: str,
        message: str,
        status_code: int,
        correlation_id: str,
        context: Dict[str, Any] = None,
        recovery_suggestions: list = None,
        severity: str = "medium",
    ) -> JSONResponse:
        """Create standardized error response."""

        response_data = {
            "error": {
                "code": error_code,
                "message": message,
                "correlation_id": correlation_id,
                "severity": severity,
                "timestamp": logger.name,  # This will be replaced with actual timestamp
            }
        }

        # Add optional fields if present
        if context:
            response_data["error"]["context"] = context

        if recovery_suggestions:
            response_data["error"]["suggestions"] = recovery_suggestions

        # Add debug info for development
        if logger.level == logging.DEBUG:
            response_data["error"]["debug"] = {
                "status_code": status_code,
                "error_type": error_code,
            }

        return JSONResponse(
            status_code=status_code,
            content=response_data,
            headers={"X-Correlation-ID": correlation_id},
        )


def setup_error_handling(app):
    """Setup global error handling for FastAPI app."""

    app.add_middleware(ErrorHandlingMiddleware)

    # Add correlation ID to request state
    @app.middleware("http")
    async def add_correlation_id(request: Request, call_next):
        if not hasattr(request.state, "correlation_id"):
            request.state.correlation_id = str(uuid.uuid4())
        response = await call_next(request)
        response.headers["X-Correlation-ID"] = request.state.correlation_id
        return response

    logger.info("Global error handling middleware configured")
