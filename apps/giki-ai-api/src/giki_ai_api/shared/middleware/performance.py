"""
Performance monitoring middleware for tracking API response times and bottlenecks.
"""

import logging
import time
from typing import Callable

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware to monitor API performance and log slow requests.
    """

    def __init__(
        self, app, slow_request_threshold: float = 400.0
    ):  # DOUBLED from 200ms to 400ms
        """
        Initialize AI-AWARE performance monitoring middleware.

        Args:
            app: FastAPI application
            slow_request_threshold: Threshold in milliseconds for non-AI operations (DOUBLED to 400ms)
        """
        super().__init__(app)
        self.standard_threshold = slow_request_threshold / 1000.0  # Convert to seconds

        # AI-aware thresholds for different operation types
        self.ai_endpoints = [
            "/categorize",
            "/upload",
            "/process",
            "/batch",
            "/insights",
            "/schema",
        ]
        self.ai_threshold = 15.0  # 15 seconds for AI operations
        self.complex_ai_threshold = (
            300.0  # 5 minutes for complex AI operations like file processing
        )

        self.skip_health_paths = {
            "/health",
            "/api/v1/health",
            "/health/env",
            "/health/performance",
        }

    def get_appropriate_threshold(self, path: str) -> float:
        """Get the appropriate performance threshold for the given endpoint path."""
        # Complex AI operations (file processing)
        if any(
            complex_path in path for complex_path in ["/upload", "/process", "/batch"]
        ):
            return self.complex_ai_threshold

        # Standard AI operations (categorization, insights)
        if any(ai_path in path for ai_path in self.ai_endpoints):
            return self.ai_threshold

        # Standard non-AI operations
        return self.standard_threshold

    async def dispatch(self, request: Request, call_next: Callable):
        """
        ULTRA-OPTIMIZED request processing with minimal overhead.
        """
        # Skip performance monitoring for health checks to maximize speed
        if request.url.path in self.skip_health_paths:
            return await call_next(request)

        start_time = time.time()
        request.state.start_time = start_time

        try:
            # Process the request
            response = await call_next(request)

            # Calculate response time
            end_time = time.time()
            response_time = end_time - start_time
            response_time_ms = response_time * 1000

            # Get AI-aware threshold for this endpoint
            appropriate_threshold = self.get_appropriate_threshold(request.url.path)

            # Add performance headers
            response.headers["X-Response-Time"] = f"{response_time_ms:.2f}ms"
            response.headers["X-Process-Time"] = f"{response_time:.6f}"

            # OPTIMIZED metrics recording - minimal overhead
            if response_time_ms > 50:  # Only record requests > 50ms to reduce overhead
                try:
                    performance_metrics.add_request(
                        method=request.method,
                        url=request.url.path,
                        response_time_ms=response_time_ms,
                        status_code=response.status_code,
                        appropriate_threshold_ms=appropriate_threshold * 1000,
                    )
                except Exception:
                    pass  # Silently skip metrics on errors for performance

            # AI-AWARE logging for performance - use appropriate thresholds
            if response_time > appropriate_threshold:
                threshold_ms = appropriate_threshold * 1000
                operation_type = (
                    "AI"
                    if any(ai_path in request.url.path for ai_path in self.ai_endpoints)
                    else "STANDARD"
                )
                logger.warning(
                    f"SLOW {operation_type}: {request.method} {request.url.path} - {response_time_ms:.1f}ms (>{threshold_ms:.0f}ms)"
                )

            # Critical auth performance monitoring (DOUBLED threshold)
            if "/auth/" in request.url.path:
                if response_time_ms > 1000:  # DOUBLED from 500ms to 1000ms
                    logger.warning(
                        f"AUTH SLOW: {request.url.path} - {response_time_ms:.0f}ms (target: <1000ms)"
                    )
                elif response_time_ms > 200:
                    logger.info(
                        f"AUTH OK: {request.url.path} - {response_time_ms:.0f}ms"
                    )
                else:
                    logger.debug(
                        f"AUTH FAST: {request.url.path} - {response_time_ms:.0f}ms"
                    )

            return response

        except Exception as e:
            # Calculate response time even for errors
            end_time = time.time()
            response_time = end_time - start_time
            response_time_ms = response_time * 1000

            logger.error(
                f"ERROR REQUEST: {request.method} {request.url} - "
                f"{response_time_ms:.2f}ms - Exception: {str(e)}"
            )
            raise


class DatabasePerformanceTracker:
    """
    Track database operation performance.
    """

    @staticmethod
    def track_query(operation_name: str):
        """
        Decorator to track database query performance.
        """

        def decorator(func):
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    end_time = time.time()
                    query_time = (end_time - start_time) * 1000

                    # OPTIMIZED: Only log slow queries (>100ms) to reduce overhead
                    if query_time > 100:
                        logger.warning(f"SLOW DB: {operation_name} {query_time:.0f}ms")

                    return result
                except Exception as e:
                    end_time = time.time()
                    query_time = (end_time - start_time) * 1000
                    logger.error(
                        f"FAILED DB QUERY: {operation_name} failed after {query_time:.2f}ms - {str(e)}"
                    )
                    raise

            return wrapper

        return decorator


# Performance metrics collection
class PerformanceMetrics:
    """
    Collect and store performance metrics.
    """

    def __init__(self):
        self.request_times = []
        self.slow_requests = []
        self.auth_requests = []

    def add_request(
        self,
        method: str,
        url: str,
        response_time_ms: float,
        status_code: int,
        appropriate_threshold_ms: float = 200.0,
    ):
        """Add a request to metrics with AI-aware slow request detection."""
        request_data = {
            "method": method,
            "url": url,
            "response_time_ms": response_time_ms,
            "status_code": status_code,
            "timestamp": time.time(),
            "threshold_ms": appropriate_threshold_ms,
        }

        self.request_times.append(request_data)

        # Use AI-aware threshold for determining slow requests
        if response_time_ms > appropriate_threshold_ms:
            self.slow_requests.append(request_data)

        if "/auth/" in url:
            self.auth_requests.append(request_data)

    def get_summary(self):
        """Get performance summary."""
        if not self.request_times:
            return {"message": "No requests recorded"}

        response_times = [r["response_time_ms"] for r in self.request_times]

        return {
            "total_requests": len(self.request_times),
            "average_response_time_ms": sum(response_times) / len(response_times),
            "max_response_time_ms": max(response_times),
            "min_response_time_ms": min(response_times),
            "slow_requests_count": len(self.slow_requests),
            "auth_requests_count": len(self.auth_requests),
            "auth_average_ms": sum(r["response_time_ms"] for r in self.auth_requests)
            / len(self.auth_requests)
            if self.auth_requests
            else 0,
        }


# Global metrics instance
performance_metrics = PerformanceMetrics()


class PerformanceMonitor:
    """
    Performance monitoring class for testing and programmatic access.

    This class provides performance monitoring functionality that can be used
    independently of the middleware for testing and analysis.
    """

    def __init__(self):
        """Initialize performance monitor."""
        self.metrics = PerformanceMetrics()
        self.start_time = time.time()

    def start_monitoring(self) -> None:
        """Start performance monitoring."""
        self.start_time = time.time()
        logger.info("Performance monitoring started")

    def stop_monitoring(self) -> dict:
        """Stop monitoring and return results."""
        end_time = time.time()
        duration = end_time - self.start_time

        summary = self.metrics.get_summary()
        summary["total_monitoring_time_seconds"] = duration

        logger.info(f"Performance monitoring stopped after {duration:.2f} seconds")
        return summary

    def record_request(
        self, method: str, url: str, response_time_ms: float, status_code: int = 200
    ) -> None:
        """Record a request for performance tracking."""
        self.metrics.add_request(method, url, response_time_ms, status_code)

    def get_current_stats(self) -> dict:
        """Get current performance statistics."""
        return self.metrics.get_summary()

    def reset_stats(self) -> None:
        """Reset all performance statistics."""
        self.metrics = PerformanceMetrics()
        self.start_time = time.time()
        logger.info("Performance monitor statistics reset")

    def is_performance_acceptable(self, threshold_ms: float = 200.0) -> bool:
        """Check if current performance meets acceptable thresholds (AI-aware)."""
        summary = self.metrics.get_summary()

        if not summary or "average_response_time_ms" not in summary:
            return True  # No data means acceptable

        # AI-aware performance evaluation: slow requests are now calculated using appropriate thresholds
        # so the slow_request_ratio already accounts for AI vs non-AI operations
        slow_request_ratio = summary.get("slow_requests_count", 0) / max(
            summary.get("total_requests", 1), 1
        )

        # Performance is acceptable if <10% of requests exceed their appropriate thresholds
        # Note: We no longer check average response time against a fixed threshold since
        # AI operations legitimately have much higher response times
        return slow_request_ratio < 0.1


# Performance monitoring utilities (consolidated from performance_middleware.py)
class PerformanceUtils:
    """Utility functions for performance monitoring."""

    @staticmethod
    def get_current_metrics():
        """Get current performance metrics."""
        try:
            from ..services.system.performance_monitor import performance_monitor

            return performance_monitor.get_performance_summary(minutes=5)
        except ImportError as e:
            # No fallback metrics - proper performance monitoring required
            from ..exceptions import ServiceError

            raise ServiceError(
                message="Performance monitoring service not available. Proper performance monitoring is required for production systems.",
                service_name="PerformanceMiddleware",
                operation="get_performance_summary",
                error_code="PERFORMANCE_MONITOR_REQUIRED",
                original_error=e,
            )

    @staticmethod
    def get_detailed_metrics(minutes: int = 15):
        """Get detailed performance metrics for specified time period."""
        try:
            from ..services.system.performance_monitor import performance_monitor

            return performance_monitor.get_performance_summary(minutes=minutes)
        except ImportError as e:
            # No fallback metrics - proper performance monitoring required
            from ..exceptions import ServiceError

            raise ServiceError(
                message=f"Performance monitoring service not available for {minutes} minute analysis. Proper performance monitoring is required for production systems.",
                service_name="PerformanceMiddleware",
                operation="get_detailed_metrics",
                error_code="PERFORMANCE_MONITOR_REQUIRED",
                original_error=e,
            )

    @staticmethod
    def get_endpoint_stats():
        """Get per-endpoint performance statistics."""
        try:
            from ..services.system.performance_monitor import performance_monitor

            return dict(performance_monitor.endpoint_stats)
        except ImportError as e:
            # No fallback endpoint stats - proper performance monitoring required
            from ..exceptions import ServiceError

            raise ServiceError(
                message="Performance monitoring service not available for endpoint statistics. Proper performance monitoring is required for production systems.",
                service_name="PerformanceMiddleware",
                operation="get_endpoint_stats",
                error_code="PERFORMANCE_MONITOR_REQUIRED",
                original_error=e,
            )

    @staticmethod
    def reset_metrics():
        """Reset all performance metrics (use with caution)."""
        try:
            from ..services.system.performance_monitor import performance_monitor

            performance_monitor.api_metrics.clear()
            performance_monitor.db_metrics.clear()
            performance_monitor.system_metrics.clear()
            performance_monitor.endpoint_stats.clear()
            logger.info("Performance metrics reset")
        except ImportError as e:
            # No fallback metrics reset - proper performance monitoring required
            from ..exceptions import ServiceError

            raise ServiceError(
                message="Performance monitoring service not available for metrics reset. Proper performance monitoring is required for production systems.",
                service_name="PerformanceMiddleware",
                operation="reset_metrics",
                error_code="PERFORMANCE_MONITOR_REQUIRED",
                original_error=e,
            )
