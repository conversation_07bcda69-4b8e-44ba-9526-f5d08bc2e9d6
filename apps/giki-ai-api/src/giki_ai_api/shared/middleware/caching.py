"""
Response caching middleware for improved API performance.
Supports both Redis (persistent) and in-memory (fallback) caching.
"""

import hashlib
import json
import logging
import os
import time
from dataclasses import dataclass
from typing import Any, Dict, Optional, Set

import redis.asyncio as aioredis
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with metadata."""

    data: bytes
    content_type: str
    status_code: int
    timestamp: float
    ttl: float
    access_count: int = 0
    last_accessed: float = 0


class ResponseCacheMiddleware(BaseHTTPMiddleware):
    """
    Middleware to cache API responses for improved performance.
    Supports both Redis (persistent) and in-memory (fallback) caching.
    """

    def __init__(self, app, max_cache_size: int = 5000, default_ttl: int = 600):
        """
        Initialize response cache middleware.

        Args:
            app: FastAPI application
            max_cache_size: Maximum number of cached responses (for in-memory fallback)
            default_ttl: Default time-to-live in seconds
        """
        super().__init__(app)
        self.cache: Dict[str, CacheEntry] = {}  # In-memory fallback cache
        self.max_cache_size = max_cache_size
        self.default_ttl = default_ttl
        self.redis_client: Optional[aioredis.Redis] = None
        self.redis_enabled = False

        # Initialize Redis connection
        self._init_redis()

        # ULTRA-OPTIMIZED TTL for different endpoint patterns
        self.endpoint_ttl = {
            "/health": 60,  # Health checks - extended TTL for performance
            "/api/v1/health": 60,  # API health - extended TTL
            "/api/v1/categories": 1200,  # Categories - long TTL (20 min)
            "/api/v1/transactions": 300,  # Transactions - medium TTL (5 min)
            "/api/v1/dashboard": 180,  # Dashboard - shorter TTL (3 min)
            "/api/v1/reports": 600,  # Reports - medium TTL (10 min)
            "/api/v1/system/performance": 30,  # Performance metrics - short TTL
        }

        # ULTRA-OPTIMIZED: Minimal no-cache patterns for maximum performance
        self.no_cache_patterns: Set[str] = {
            "/api/v1/auth/token",  # Authentication tokens (POST)
            "/api/v1/auth/login",  # Login (POST)
            "/api/v1/files/upload",  # File uploads
            "/health/db/reset",  # Database reset
            "/api/v1/agent/command",  # Agent commands (dynamic)
        }

        # Only cache GET requests by default
        self.cacheable_methods = {"GET"}

        # Cache statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_requests": 0,
            "redis_hits": 0,
            "redis_misses": 0,
            "redis_errors": 0,
            "memory_hits": 0,
            "memory_misses": 0,
        }

    def _init_redis(self):
        """Initialize Redis connection if available."""
        try:
            redis_url = os.getenv("REDIS_URL")
            redis_host = os.getenv("REDIS_HOST")
            redis_port = os.getenv("REDIS_PORT", "6379")
            redis_enabled = os.getenv("REDIS_ENABLED", "false").lower() == "true"

            if not redis_enabled:
                logger.info("Redis caching disabled via REDIS_ENABLED=false")
                return

            if redis_url:
                # Use Redis URL if provided
                logger.info(f"Initializing Redis connection with URL: {redis_url}")
                self.redis_client = aioredis.from_url(redis_url, decode_responses=False)
                self.redis_enabled = True
            elif redis_host:
                # Use host/port if provided with timeout configuration
                redis_timeout = float(os.getenv("REDIS_TIMEOUT", "2"))
                logger.info(
                    f"Initializing Redis connection with host: {redis_host}:{redis_port}, timeout: {redis_timeout}s"
                )
                self.redis_client = aioredis.Redis(
                    host=redis_host,
                    port=int(redis_port),
                    decode_responses=False,
                    socket_timeout=redis_timeout,
                    socket_connect_timeout=redis_timeout,
                    health_check_interval=30,
                )
                self.redis_enabled = True
            else:
                logger.info("No Redis configuration found, using in-memory cache only")

        except Exception as e:
            logger.warning(f"Failed to initialize Redis connection: {e}")
            logger.info("Falling back to in-memory cache only")
            self.redis_client = None
            self.redis_enabled = False

    def _should_cache(self, request: Request) -> bool:
        """Determine if request should be cached."""
        # Only cache GET requests
        if request.method not in self.cacheable_methods:
            return False

        # Check if endpoint is in no-cache list
        path = request.url.path
        for pattern in self.no_cache_patterns:
            if pattern in path:
                return False

        # Don't cache requests with query parameters that indicate dynamic content
        query_params = request.query_params
        if any(param in query_params for param in ["timestamp", "random", "nocache"]):
            return False

        return True

    def _get_cache_key(self, request: Request) -> str:
        """Generate cache key for request."""
        # Include method, path, and query parameters
        key_data = {
            "method": request.method,
            "path": request.url.path,
            "query": str(request.query_params),
        }

        # Include user ID if available (for user-specific caching)
        if hasattr(request.state, "user_id"):
            key_data["user_id"] = request.state.user_id

        # Create hash of key data
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_string.encode()).hexdigest()

    def _get_ttl(self, path: str) -> int:
        """Get TTL for specific endpoint."""
        # Check for exact matches first
        if path in self.endpoint_ttl:
            return self.endpoint_ttl[path]

        # Check for pattern matches
        for pattern, ttl in self.endpoint_ttl.items():
            if path.startswith(pattern.rstrip("*")):
                return ttl

        return self.default_ttl

    async def _redis_get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cache entry from Redis."""
        if not self.redis_enabled or not self.redis_client:
            return None

        try:
            data = await self.redis_client.get(key)
            if data:
                return json.loads(data.decode("utf-8"))
            return None
        except Exception as e:
            logger.warning(f"Redis GET error for key {key}: {e}")
            self.stats["redis_errors"] += 1
            return None

    async def _redis_set(self, key: str, value: Dict[str, Any], ttl: int) -> bool:
        """Set cache entry in Redis."""
        if not self.redis_enabled or not self.redis_client:
            return False

        try:
            await self.redis_client.setex(key, ttl, json.dumps(value).encode("utf-8"))
            return True
        except Exception as e:
            logger.warning(f"Redis SET error for key {key}: {e}")
            self.stats["redis_errors"] += 1
            return False

    def _is_cache_valid(self, entry: CacheEntry) -> bool:
        """Check if cache entry is still valid."""
        current_time = time.time()
        return (current_time - entry.timestamp) < entry.ttl

    def _evict_lru(self):
        """Evict least recently used entries if cache is full."""
        if len(self.cache) < self.max_cache_size:
            return

        # Find LRU entry
        lru_key = min(
            self.cache.keys(),
            key=lambda k: self.cache[k].last_accessed or self.cache[k].timestamp,
        )

        del self.cache[lru_key]
        self.stats["evictions"] += 1
        logger.debug(f"Evicted LRU cache entry: {lru_key}")

    def _store_response(self, key: str, response: Response, ttl: int):
        """Store response in cache."""
        # Evict old entries if needed
        self._evict_lru()

        # Read response body
        if hasattr(response, "body"):
            body = response.body
        else:
            # For streaming responses, we can't cache easily
            return

        # Create cache entry
        entry = CacheEntry(
            data=body,
            content_type=response.headers.get("content-type", "application/json"),
            status_code=response.status_code,
            timestamp=time.time(),
            ttl=ttl,
            last_accessed=time.time(),
        )

        self.cache[key] = entry
        logger.debug(f"Cached response for key: {key} (TTL: {ttl}s)")

    async def _store_response_redis_and_memory(
        self, key: str, response: Response, ttl: int
    ):
        """Store response in both Redis and in-memory cache."""
        # Read response body
        if hasattr(response, "body"):
            body = response.body
        else:
            # For streaming responses, we can't cache easily
            return

        # Prepare cache data
        cache_data = {
            "data": body.decode("utf-8") if isinstance(body, bytes) else str(body),
            "content_type": response.headers.get("content-type", "application/json"),
            "status_code": response.status_code,
            "timestamp": time.time(),
            "ttl": ttl,
        }

        # Store in Redis first
        redis_stored = await self._redis_set(key, cache_data, ttl)

        if redis_stored:
            logger.debug(f"Cached response in Redis for key: {key} (TTL: {ttl}s)")
        else:
            logger.debug(f"Failed to cache in Redis, storing in memory for key: {key}")

        # Always store in memory as backup
        self._store_response(key, response, ttl)

    def _create_cached_response(self, entry: CacheEntry) -> Response:
        """Create response from cache entry."""
        # Update access statistics
        entry.access_count += 1
        entry.last_accessed = time.time()

        # Create response
        response = Response(
            content=entry.data,
            status_code=entry.status_code,
            headers={
                "content-type": entry.content_type,
                "x-cache": "HIT",
                "x-cache-age": str(int(time.time() - entry.timestamp)),
                "x-cache-ttl": str(int(entry.ttl)),
            },
        )

        return response

    async def dispatch(self, request: Request, call_next):
        """Process request with caching (Redis first, in-memory fallback)."""
        self.stats["total_requests"] += 1

        # Check if request should be cached
        if not self._should_cache(request):
            response = await call_next(request)
            response.headers["x-cache"] = "BYPASS"
            return response

        # Generate cache key
        cache_key = self._get_cache_key(request)

        # Try Redis cache first
        redis_data = await self._redis_get(cache_key)
        if redis_data:
            # Redis cache hit
            self.stats["hits"] += 1
            self.stats["redis_hits"] += 1
            logger.debug(f"Redis Cache HIT for {request.method} {request.url.path}")

            response = Response(
                content=redis_data["data"],
                status_code=redis_data["status_code"],
                headers={
                    "content-type": redis_data["content_type"],
                    "x-cache": "HIT-REDIS",
                    "x-cache-age": str(int(time.time() - redis_data["timestamp"])),
                    "x-cache-ttl": str(redis_data["ttl"]),
                },
            )
            return response

        # Try in-memory cache as fallback
        if cache_key in self.cache:
            entry = self.cache[cache_key]
            if self._is_cache_valid(entry):
                # In-memory cache hit
                self.stats["hits"] += 1
                self.stats["memory_hits"] += 1
                logger.debug(
                    f"Memory Cache HIT for {request.method} {request.url.path}"
                )
                response = self._create_cached_response(entry)
                response.headers["x-cache"] = "HIT-MEMORY"
                return response
            else:
                # Cache expired
                del self.cache[cache_key]
                logger.debug(f"Cache EXPIRED for {request.method} {request.url.path}")

        # Cache miss - process request
        self.stats["misses"] += 1
        if self.redis_enabled:
            self.stats["redis_misses"] += 1
        else:
            self.stats["memory_misses"] += 1

        logger.debug(f"Cache MISS for {request.method} {request.url.path}")

        response = await call_next(request)

        # Cache successful responses
        if 200 <= response.status_code < 300:
            ttl = self._get_ttl(request.url.path)
            await self._store_response_redis_and_memory(cache_key, response, ttl)

        # Add cache headers
        response.headers["x-cache"] = "MISS"

        return response

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.stats["total_requests"]
        hit_rate = (
            (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        )

        redis_hit_rate = (
            (self.stats["redis_hits"] / total_requests * 100)
            if total_requests > 0
            else 0
        )

        memory_hit_rate = (
            (self.stats["memory_hits"] / total_requests * 100)
            if total_requests > 0
            else 0
        )

        return {
            "cache_size": len(self.cache),
            "max_cache_size": self.max_cache_size,
            "hit_rate_percent": round(hit_rate, 2),
            "redis_hit_rate_percent": round(redis_hit_rate, 2),
            "memory_hit_rate_percent": round(memory_hit_rate, 2),
            "total_requests": total_requests,
            "cache_hits": self.stats["hits"],
            "cache_misses": self.stats["misses"],
            "redis_hits": self.stats["redis_hits"],
            "redis_misses": self.stats["redis_misses"],
            "redis_errors": self.stats["redis_errors"],
            "memory_hits": self.stats["memory_hits"],
            "memory_misses": self.stats["memory_misses"],
            "evictions": self.stats["evictions"],
            "redis_enabled": self.redis_enabled,
            "memory_usage_estimate": sum(
                len(entry.data) for entry in self.cache.values()
            ),
        }

    async def clear_cache(self):
        """Clear all cached responses (both Redis and in-memory)."""
        # Clear in-memory cache
        self.cache.clear()

        # Clear Redis cache if enabled
        if self.redis_enabled and self.redis_client:
            try:
                await self.redis_client.flushdb()
                logger.info("Redis cache cleared")
            except Exception as e:
                logger.warning(f"Failed to clear Redis cache: {e}")

        logger.info("Response cache cleared")

    def clear_cache_sync(self):
        """Synchronous version of clear_cache for backward compatibility."""
        self.cache.clear()
        logger.info("In-memory response cache cleared")

    def clear_pattern(self, pattern: str):
        """Clear cache entries matching a pattern."""
        keys_to_remove = []
        for key in self.cache.keys():
            # This is a simple pattern match - could be enhanced
            if pattern in key:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.cache[key]

        logger.info(
            f"Cleared {len(keys_to_remove)} cache entries matching pattern: {pattern}"
        )


# Global cache instance for statistics access
response_cache_middleware = None


def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics from global middleware instance."""
    if response_cache_middleware:
        return response_cache_middleware.get_cache_stats()
    return {"error": "Cache middleware not initialized"}
