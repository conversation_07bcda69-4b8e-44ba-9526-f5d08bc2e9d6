"""
Error Recovery Mechanisms and Circuit Breakers
=============================================

Provides automatic retry logic, circuit breakers, and recovery strategies.
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Callable, Optional, TypeVar

from ..exceptions import CircuitBreakerError, ErrorSeverity, RetryableError

logger = logging.getLogger(__name__)

T = TypeVar("T")


class CircuitBreakerState(Enum):
    """Circuit breaker states."""

    CLOSED = "closed"  # Normal operation
    OPEN = "open"  # Failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreaker:
    """
    Circuit breaker implementation for external service calls.

    Prevents cascading failures by opening circuit when error rate
    exceeds threshold, then gradually allows requests to test recovery.
    """

    def __init__(
        self,
        service_name: str,
        failure_threshold: int = 5,
        reset_timeout: float = 60.0,
        expected_exception: type = Exception,
    ):
        self.service_name = service_name
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.expected_exception = expected_exception

        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED

    async def call(self, func: Callable[..., T], *args, **kwargs) -> T:
        """Execute function with circuit breaker protection."""

        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
                logger.info(
                    f"Circuit breaker for {self.service_name} moving to HALF_OPEN"
                )
            else:
                raise CircuitBreakerError(self.service_name)

        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result

        except self.expected_exception as e:
            self._on_failure()
            raise e

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        return (
            self.last_failure_time
            and time.time() - self.last_failure_time >= self.reset_timeout
        )

    def _on_success(self):
        """Handle successful call."""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
        if self.state == CircuitBreakerState.HALF_OPEN:
            logger.info(f"Circuit breaker for {self.service_name} reset to CLOSED")

    def _on_failure(self):
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            logger.warning(
                f"Circuit breaker for {self.service_name} opened after {self.failure_count} failures"
            )


class RetryStrategy:
    """
    Configurable retry strategy with exponential backoff.

    Supports different retry policies based on error type and operation context.
    """

    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter

    async def execute_with_retry(
        self, func: Callable[..., T], *args, operation_name: str = "operation", **kwargs
    ) -> T:
        """Execute function with retry logic."""

        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                result = await func(*args, **kwargs)
                if attempt > 0:
                    logger.info(
                        f"Operation {operation_name} succeeded on attempt {attempt + 1}"
                    )
                return result

            except Exception as e:
                last_exception = e

                # Log the attempt
                logger.warning(
                    f"Attempt {attempt + 1} of {operation_name} failed: {e}",
                    extra={
                        "operation": operation_name,
                        "attempt": attempt + 1,
                        "max_retries": self.max_retries,
                        "error_type": type(e).__name__,
                    },
                )

                # Don't retry on the last attempt
                if attempt == self.max_retries:
                    break

                # Check if error is retryable
                if not self._is_retryable_error(e):
                    logger.info(
                        f"Error not retryable for {operation_name}: {type(e).__name__}"
                    )
                    break

                # Calculate delay and wait
                delay = self._calculate_delay(attempt)
                logger.info(f"Retrying {operation_name} in {delay:.1f}s...")
                await asyncio.sleep(delay)

        # All attempts failed
        raise RetryableError(
            message=f"Operation {operation_name} failed after {self.max_retries + 1} attempts",
            max_retries=self.max_retries,
            original_error=last_exception,
            severity=ErrorSeverity.HIGH,
        )

    def _is_retryable_error(self, error: Exception) -> bool:
        """Determine if an error should be retried."""

        # Explicitly retryable errors
        if isinstance(error, RetryableError):
            return True

        # Network/connection errors are usually retryable
        error_name = type(error).__name__
        retryable_errors = {
            "ConnectionError",
            "TimeoutError",
            "HTTPError",
            "ServiceUnavailable",
            "InternalServerError",
        }

        return error_name in retryable_errors

    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for next retry attempt."""

        delay = self.base_delay * (self.exponential_base**attempt)
        delay = min(delay, self.max_delay)

        # Add jitter to prevent thundering herd
        if self.jitter:
            import random

            delay += random.uniform(0, 0.1 * delay)

        return delay


# Global instances for common use cases
default_retry_strategy = RetryStrategy(max_retries=3, base_delay=1.0)
ai_service_circuit_breaker = CircuitBreaker(
    "ai_service", failure_threshold=5, reset_timeout=30.0
)
database_circuit_breaker = CircuitBreaker(
    "database", failure_threshold=3, reset_timeout=60.0
)


async def with_recovery(
    func: Callable[..., T],
    *args,
    operation_name: str = "operation",
    use_circuit_breaker: bool = False,
    circuit_breaker: Optional[CircuitBreaker] = None,
    retry_strategy: Optional[RetryStrategy] = None,
    **kwargs,
) -> T:
    """
    Execute function with comprehensive error recovery.

    Args:
        func: Function to execute
        operation_name: Name for logging
        use_circuit_breaker: Whether to use circuit breaker
        circuit_breaker: Custom circuit breaker instance
        retry_strategy: Custom retry strategy
        *args, **kwargs: Function arguments

    Returns:
        Function result
    """

    retry_strategy = retry_strategy or default_retry_strategy

    async def execute():
        if use_circuit_breaker and circuit_breaker:
            return await circuit_breaker.call(func, *args, **kwargs)
        else:
            return await func(*args, **kwargs)

    return await retry_strategy.execute_with_retry(
        execute, operation_name=operation_name
    )
