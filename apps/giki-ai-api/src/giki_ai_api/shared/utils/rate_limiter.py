"""
Simple rate limiter for API calls to avoid quota errors.
"""

import asyncio
import logging
import time

logger = logging.getLogger(__name__)


class RateLimiter:
    """Simple rate limiter to avoid API quota errors."""

    def __init__(self, calls_per_minute: int = 10):
        self.calls_per_minute = calls_per_minute
        self.min_interval = 60.0 / calls_per_minute  # seconds between calls
        self.last_call_time = 0.0
        self._lock = asyncio.Lock()

    async def wait_if_needed(self) -> float:
        """Wait if necessary to respect rate limit. Returns wait time."""
        async with self._lock:
            current_time = time.time()
            time_since_last_call = current_time - self.last_call_time

            if time_since_last_call < self.min_interval:
                wait_time = self.min_interval - time_since_last_call
                logger.debug(f"Rate limiting: waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
                self.last_call_time = time.time()
                return wait_time
            else:
                self.last_call_time = current_time
                return 0.0

    async def execute_with_limit(self, func, *args, **kwargs):
        """Execute a function with rate limiting."""
        await self.wait_if_needed()
        return await func(*args, **kwargs)


# Global rate limiter for Gemini API calls
# Gemini has a limit of 60 requests per minute for free tier
# Set to 20 calls per minute to be extra conservative for temporal validation
gemini_rate_limiter = RateLimiter(
    calls_per_minute=20
)  # Extra conservative to avoid 429s
