<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>giki.ai - Production Monitoring Dashboard</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-healthy {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-degraded {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-unhealthy {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            color: #1e293b;
            margin-bottom: 16px;
            font-size: 18px;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: 600;
            color: #1e293b;
        }
        
        .alert {
            background: white;
            border-left: 4px solid #ef4444;
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .alert-high {
            border-left-color: #f59e0b;
        }
        
        .alert-medium {
            border-left-color: #3b82f6;
        }
        
        .alert-low {
            border-left-color: #10b981;
        }
        
        .alert-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .alert-description {
            color: #64748b;
            font-size: 14px;
        }
        
        .refresh-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .refresh-btn:hover {
            background: #2563eb;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #64748b;
        }
        
        .error {
            background: #fee2e2;
            color: #991b1b;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .last-updated {
            color: #64748b;
            font-size: 14px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>giki.ai Production Monitoring</h1>
            <div id="overall-status">
                <span class="status-indicator status-healthy">System Status: Loading...</span>
            </div>
            <div class="last-updated" id="last-updated">Last updated: Never</div>
        </div>
        
        <div id="error-message" class="error" style="display: none;"></div>
        
        <div class="grid">
            <div class="card">
                <h3>Performance Metrics</h3>
                <div id="performance-metrics">
                    <div class="loading">Loading metrics...</div>
                </div>
            </div>
            
            <div class="card">
                <h3>Health Checks</h3>
                <div id="health-checks">
                    <div class="loading">Loading health status...</div>
                </div>
            </div>
            
            <div class="card">
                <h3>System Resources</h3>
                <div id="system-resources">
                    <div class="loading">Loading system data...</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>Active Alerts</h3>
            <div id="alerts-section">
                <div class="loading">Loading alerts...</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button class="refresh-btn" onclick="refreshDashboard()">Refresh Data</button>
        </div>
    </div>

    <script>
        let refreshInterval;
        
        async function fetchData(endpoint) {
            try {
                const response = await fetch(endpoint);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error(`Error fetching ${endpoint}:`, error);
                throw error;
            }
        }
        
        function formatPercent(value) {
            return (value * 100).toFixed(1) + '%';
        }
        
        function formatMs(value) {
            return value.toFixed(1) + 'ms';
        }
        
        function formatMB(value) {
            return value.toFixed(0) + 'MB';
        }
        
        function formatTimestamp(timestamp) {
            return new Date(timestamp).toLocaleString();
        }
        
        function getStatusClass(status) {
            switch (status) {
                case 'healthy': return 'status-healthy';
                case 'degraded': return 'status-degraded';
                case 'unhealthy': return 'status-unhealthy';
                default: return 'status-degraded';
            }
        }
        
        function getAlertClass(severity) {
            switch (severity) {
                case 'critical': return '';
                case 'high': return 'alert-high';
                case 'medium': return 'alert-medium';
                case 'low': return 'alert-low';
                default: return '';
            }
        }
        
        async function updateDashboard() {
            try {
                // Clear any previous error
                document.getElementById('error-message').style.display = 'none';
                
                // Fetch all data in parallel
                const [healthData, metricsData, alertsData] = await Promise.all([
                    fetchData('/api/v1/monitoring/health'),
                    fetchData('/api/v1/monitoring/metrics'),
                    fetchData('/api/v1/monitoring/alerts')
                ]);
                
                // Update overall status
                const statusElement = document.getElementById('overall-status');
                statusElement.innerHTML = `<span class="status-indicator ${getStatusClass(healthData.status)}">System Status: ${healthData.status.toUpperCase()}</span>`;
                
                // Update performance metrics
                const performanceElement = document.getElementById('performance-metrics');
                const metrics = metricsData.performance_metrics;
                performanceElement.innerHTML = `
                    <div class="metric">
                        <span>Error Rate</span>
                        <span class="metric-value">${formatPercent(metrics.error_rate || 0)}</span>
                    </div>
                    <div class="metric">
                        <span>Success Rate</span>
                        <span class="metric-value">${formatPercent(metrics.success_rate || 0)}</span>
                    </div>
                    <div class="metric">
                        <span>Avg Response Time</span>
                        <span class="metric-value">${formatMs(metrics.avg_response_time_ms || 0)}</span>
                    </div>
                    <div class="metric">
                        <span>AI Success Rate</span>
                        <span class="metric-value">${formatPercent(metrics.ai_success_rate || 0)}</span>
                    </div>
                    <div class="metric">
                        <span>Upload Success Rate</span>
                        <span class="metric-value">${formatPercent(metrics.upload_success_rate || 0)}</span>
                    </div>
                `;
                
                // Update health checks
                const healthElement = document.getElementById('health-checks');
                const checks = healthData.checks;
                let healthHtml = '';
                for (const [name, check] of Object.entries(checks)) {
                    healthHtml += `
                        <div class="metric">
                            <span>${name.charAt(0).toUpperCase() + name.slice(1)}</span>
                            <span class="metric-value status-indicator ${getStatusClass(check.status)}">${check.status}</span>
                        </div>
                    `;
                }
                healthElement.innerHTML = healthHtml;
                
                // Update system resources
                const resourcesElement = document.getElementById('system-resources');
                const memoryCheck = checks.memory;
                const diskCheck = checks.filesystem;
                const dbCheck = checks.database;
                
                resourcesElement.innerHTML = `
                    ${memoryCheck ? `
                        <div class="metric">
                            <span>Memory Usage</span>
                            <span class="metric-value">${memoryCheck.memory_usage_percent ? memoryCheck.memory_usage_percent.toFixed(1) + '%' : 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span>Available Memory</span>
                            <span class="metric-value">${memoryCheck.available_mb ? formatMB(memoryCheck.available_mb) : 'N/A'}</span>
                        </div>
                    ` : ''}
                    ${diskCheck ? `
                        <div class="metric">
                            <span>Disk Usage</span>
                            <span class="metric-value">${diskCheck.disk_usage_percent ? diskCheck.disk_usage_percent.toFixed(1) + '%' : 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span>Free Space</span>
                            <span class="metric-value">${diskCheck.free_gb ? diskCheck.free_gb.toFixed(1) + 'GB' : 'N/A'}</span>
                        </div>
                    ` : ''}
                    ${dbCheck ? `
                        <div class="metric">
                            <span>DB Response Time</span>
                            <span class="metric-value">${dbCheck.response_time_ms ? formatMs(dbCheck.response_time_ms) : 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span>DB Pool Usage</span>
                            <span class="metric-value">${dbCheck.pool_usage_percent ? dbCheck.pool_usage_percent.toFixed(1) + '%' : 'N/A'}</span>
                        </div>
                    ` : ''}
                `;
                
                // Update alerts
                const alertsElement = document.getElementById('alerts-section');
                const alerts = alertsData.active_alerts;
                if (alerts.length === 0) {
                    alertsElement.innerHTML = '<div style="text-align: center; padding: 20px; color: #10b981;">No active alerts 🎉</div>';
                } else {
                    let alertsHtml = '';
                    alerts.forEach(alert => {
                        alertsHtml += `
                            <div class="alert ${getAlertClass(alert.severity)}">
                                <div class="alert-title">${alert.title} (${alert.severity.toUpperCase()})</div>
                                <div class="alert-description">${alert.description}</div>
                                <div style="font-size: 12px; color: #64748b; margin-top: 8px;">
                                    ${formatTimestamp(alert.timestamp)}
                                </div>
                            </div>
                        `;
                    });
                    alertsElement.innerHTML = alertsHtml;
                }
                
                // Update last updated timestamp
                document.getElementById('last-updated').textContent = `Last updated: ${new Date().toLocaleString()}`;
                
            } catch (error) {
                const errorElement = document.getElementById('error-message');
                errorElement.textContent = `Error loading dashboard data: ${error.message}`;
                errorElement.style.display = 'block';
                console.error('Dashboard update error:', error);
            }
        }
        
        function refreshDashboard() {
            updateDashboard();
        }
        
        function startAutoRefresh() {
            // Refresh every 30 seconds
            refreshInterval = setInterval(updateDashboard, 30000);
        }
        
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            updateDashboard();
            startAutoRefresh();
        });
        
        // Stop auto-refresh when page is hidden
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        });
    </script>
</body>
</html>