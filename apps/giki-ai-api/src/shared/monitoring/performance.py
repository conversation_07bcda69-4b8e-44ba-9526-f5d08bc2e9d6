"""
Performance monitoring utilities for critical customer workflows.

Tracks performance metrics for:
- File upload processing
- Transaction categorization
- MIS setup workflows
- AI agent response times
"""

import logging
import time
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime
from functools import wraps
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Represents a single performance measurement."""

    operation: str
    duration_ms: float
    timestamp: datetime
    success: bool
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert metric to dictionary for logging/storage."""
        return {
            "operation": self.operation,
            "duration_ms": self.duration_ms,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "metadata": self.metadata,
        }


class PerformanceMonitor:
    """Tracks and reports performance metrics for critical operations."""

    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.thresholds = {
            "file_upload": 5000,  # 5 seconds
            "categorization": 500,  # 500ms per transaction
            "mis_setup": 300000,  # 5 minutes
            "ai_response": 3000,  # 3 seconds
            "db_query": 100,  # 100ms
            "api_call": 2000,  # 2 seconds
        }

    @contextmanager
    def track_operation(self, operation: str, **metadata):
        """Context manager to track operation performance."""
        start_time = time.time()
        success = True

        try:
            yield
        except Exception as e:
            success = False
            metadata["error"] = str(e)
            raise
        finally:
            duration_ms = (time.time() - start_time) * 1000

            metric = PerformanceMetric(
                operation=operation,
                duration_ms=duration_ms,
                timestamp=datetime.now(),
                success=success,
                metadata=metadata,
            )

            self.record_metric(metric)

    def record_metric(self, metric: PerformanceMetric):
        """Record a performance metric and check against thresholds."""
        self.metrics.append(metric)

        # Check if operation exceeded threshold
        operation_type = metric.operation.split(".")[0]
        threshold = self.thresholds.get(operation_type)

        if threshold and metric.duration_ms > threshold:
            logger.warning(
                f"Performance threshold exceeded for {metric.operation}: "
                f"{metric.duration_ms:.2f}ms > {threshold}ms",
                extra={"metric": metric.to_dict(), "threshold": threshold},
            )
        else:
            logger.info(
                f"Performance metric: {metric.operation} completed in {metric.duration_ms:.2f}ms",
                extra={"metric": metric.to_dict()},
            )

    def get_metrics_summary(
        self, operation_filter: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get summary statistics for recorded metrics."""
        filtered_metrics = [
            m
            for m in self.metrics
            if not operation_filter or m.operation.startswith(operation_filter)
        ]

        if not filtered_metrics:
            return {"count": 0, "operations": {}}

        operations_summary = {}
        for metric in filtered_metrics:
            op = metric.operation
            if op not in operations_summary:
                operations_summary[op] = {
                    "count": 0,
                    "total_ms": 0,
                    "min_ms": float("inf"),
                    "max_ms": 0,
                    "success_count": 0,
                    "failure_count": 0,
                }

            summary = operations_summary[op]
            summary["count"] += 1
            summary["total_ms"] += metric.duration_ms
            summary["min_ms"] = min(summary["min_ms"], metric.duration_ms)
            summary["max_ms"] = max(summary["max_ms"], metric.duration_ms)

            if metric.success:
                summary["success_count"] += 1
            else:
                summary["failure_count"] += 1

        # Calculate averages
        for _op, summary in operations_summary.items():
            summary["avg_ms"] = summary["total_ms"] / summary["count"]
            summary["success_rate"] = summary["success_count"] / summary["count"]

        return {"count": len(filtered_metrics), "operations": operations_summary}

    def clear_metrics(self):
        """Clear all recorded metrics."""
        self.metrics = []


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def track_performance(operation: str):
    """Decorator to track function performance."""

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            with performance_monitor.track_operation(
                operation,
                function=func.__name__,
                args_count=len(args),
                kwargs_keys=list(kwargs.keys()),
            ):
                return await func(*args, **kwargs)

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            with performance_monitor.track_operation(
                operation,
                function=func.__name__,
                args_count=len(args),
                kwargs_keys=list(kwargs.keys()),
            ):
                return func(*args, **kwargs)

        # Return appropriate wrapper based on function type
        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# Convenience functions for common operations
def track_file_upload(filename: str, size_bytes: int):
    """Create a context manager for tracking file upload performance."""
    return performance_monitor.track_operation(
        "file_upload",
        filename=filename,
        size_bytes=size_bytes,
        size_mb=size_bytes / (1024 * 1024),
    )


def track_categorization(
    transaction_count: int, enhancement_type: Optional[str] = None
):
    """Create a context manager for tracking categorization performance."""
    return performance_monitor.track_operation(
        "categorization",
        transaction_count=transaction_count,
        enhancement_type=enhancement_type or "baseline",
    )


def track_mis_setup(company_name: str, data_sources: List[str]):
    """Create a context manager for tracking MIS setup performance."""
    return performance_monitor.track_operation(
        "mis_setup",
        company_name=company_name,
        data_sources=data_sources,
        data_source_count=len(data_sources),
    )


def track_ai_response(model: str, prompt_tokens: int):
    """Create a context manager for tracking AI response performance."""
    return performance_monitor.track_operation(
        "ai_response", model=model, prompt_tokens=prompt_tokens
    )
