[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "giki_ai_api"
version = "0.1.0"
description = "Main Giki AI API application"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]
# Dependencies for giki_ai_api will be installed from the Dockerfile's
# main RUN uv pip install command, so we don't need to list them here
# unless there are specific sub-dependencies of the *application code itself*
# that are not covered by the Dockerfile's direct installs.
# For now, assume all are covered.
dependencies = [
    # "giki_ai_db @ file:///app/local_deps/giki-ai-db", # This might be needed if installed as editable
    # "giki_ai_rezolve_auth @ file:///app/local_deps/giki-ai-rezolve-auth", # This might be needed
]

# This tells hatchling where to find the package
[tool.hatch.build.targets.wheel]
packages = ["giki_ai_api"]