#!/bin/bash
set -e

echo "Starting Giki AI API for Cloud Run..."
echo "Python version: $(python --version)"
echo "Current directory: $(pwd)"
echo "PYTHONPATH: $PYTHONPATH"
echo "PORT: $PORT"

# Use Cloud Run's PORT environment variable, default to 8080
PORT=${PORT:-8080}
echo "Server will bind to port: $PORT"

# Check if service account exists
if [ -f "/app/dev-service-account.json" ]; then
    echo "Service account found at /app/dev-service-account.json"
    export GOOGLE_APPLICATION_CREDENTIALS="/app/dev-service-account.json"
else
    echo "Warning: Service account not found at /app/dev-service-account.json"
fi

# Test import
echo "Testing import..."
uv run python -c "import giki_ai_api.main; print('Import successful')" || {
    echo "Import failed, checking module structure..."
    ls -la /app/src/
    exit 1
}

echo "Starting uvicorn server on 0.0.0.0:$PORT..."
# Use environment variable for port and optimize settings for Cloud Run
exec uv run python -m uvicorn giki_ai_api.main:app \
    --host 0.0.0.0 \
    --port $PORT \
    --workers 1 \
    --timeout-keep-alive 30 \
    --timeout-graceful-shutdown 30 \
    --log-level info \
    --no-access-log