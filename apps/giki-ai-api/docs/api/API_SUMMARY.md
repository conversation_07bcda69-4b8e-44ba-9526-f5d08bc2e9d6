
# giki.ai API Documentation Summary

## Statistics
- **Total Endpoints**: 237
- **Total Models**: 182
- **API Version**: 1.0.0

## Endpoint Categories
- **ADK Agents**: 20 endpoints
- **Accuracy**: 20 endpoints
- **Admin**: 22 endpoints
- **Agents**: 8 endpoints
- **Authentication**: 12 endpoints
- **Authentication Test**: 12 endpoints
- **Cache**: 6 endpoints
- **Categories**: 74 endpoints
- **Dashboard**: 6 endpoints
- **Enhanced Files**: 12 endpoints
- **Exports**: 6 endpoints
- **Files**: 30 endpoints
- **GL Code Management**: 16 endpoints
- **Hierarchy**: 2 endpoints
- **Intelligence**: 12 endpoints
- **Monitoring**: 3 endpoints
- **Onboarding**: 64 endpoints
- **Performance Monitoring**: 9 endpoints
- **Progress**: 4 endpoints
- **Reports**: 28 endpoints
- **Review Queue**: 8 endpoints
- **System Monitoring**: 6 endpoints
- **Transactions**: 24 endpoints
- **Unified AI**: 12 endpoints
- **Untagged**: 9 endpoints
- **Upload**: 15 endpoints
- **accuracy**: 20 endpoints
- **admin**: 4 endpoints
- **cache**: 4 endpoints
- **exports**: 6 endpoints
- **intelligence**: 12 endpoints
- **root**: 1 endpoints

## Authentication
- **Type**: JWT Bearer Token (RS256)
- **Security**: All endpoints require authentication except public registration

## Key Endpoints
- **Authentication**: `/api/v1/auth/token` - Login and token generation
- **File Upload**: `/api/v1/files/upload` - Transaction file processing
- **Transactions**: `/api/v1/transactions/` - Transaction management
- **Categories**: `/api/v1/categories/` - Category management
- **Reports**: `/api/v1/reports/` - Report generation
- **Agent**: `/api/v1/agent/command` - Conversational AI interface

## Data Models
The API includes comprehensive data models for:
- User management and authentication
- Transaction processing and categorization
- File upload and processing
- Reporting and analytics
- System health and monitoring

## Error Handling
All endpoints return structured error responses with:
- HTTP status codes
- Error message details
- Validation error specifics
- Request correlation IDs
