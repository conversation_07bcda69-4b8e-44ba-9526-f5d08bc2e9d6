openapi: 3.1.0
info:
  title: giki.ai API
  description: "\ngiki.ai API - AI-Powered Financial Transaction Management Platform\n\
    \n## Overview\ngiki.ai provides intelligent financial data processing with automated\
    \ categorization,\npattern recognition, and comprehensive reporting capabilities.\n\
    \n## Key Features\n- **AI-Powered Categorization**: 85%+ accuracy with confidence\
    \ scoring\n- **Intelligent File Processing**: Support for multiple formats (CSV,\
    \ Excel, PDF)\n- **Real-time Processing**: Batch processing with progress tracking\n\
    - **Conversational Agent**: Natural language interface for data operations\n-\
    \ **Comprehensive Reporting**: Export to multiple formats (Excel, CSV, QBO, IIF)\n\
    - **Tenant Isolation**: Multi-tenant architecture with secure data isolation\n\
    \n## Authentication\nThis API uses JWT (RS256) bearer token authentication. Include\
    \ the token in the \nAuthorization header: `Authorization: Bearer <token>`\n\n\
    ## Rate Limiting\nAPI requests are rate-limited to prevent abuse. Check response\
    \ headers for rate limit status.\n\n## Error Handling\nThe API returns structured\
    \ error responses with detailed error codes and messages.\nAll errors follow RFC\
    \ 7807 Problem Details for HTTP APIs standard.\n        "
  contact:
    name: giki.ai Support
    url: https://giki.ai/
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://giki.ai/license
  version: 1.0.0
paths:
  /api/v1/system/performance:
    get:
      summary: Get System Performance
      description: 'Real-time performance metrics endpoint for frontend dashboard
        integration.

        Returns current system performance data including response times, success
        rates, and system load.


        OPTIMIZED: Removed expensive database health check to eliminate 3000ms response
        times.'
      operationId: get_system_performance_api_v1_system_performance_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/agent/command:
    post:
      summary: Process Agent Command
      description: 'Unified agent command processing endpoint for 11 frontend commands.

        Uses ConversationalAgent for intelligent command routing and execution.


        Supported commands:

        - /upload: File upload and processing

        - /filter: Transaction filtering

        - /export: Data export operations

        - /categorize: Transaction categorization

        - /delete: Transaction deletion

        - /report: Report generation

        - /analyze: Data analysis

        - /settings: Settings management

        - /search: Data search operations

        - /create: Create new entities

        - /refresh: Refresh/reload data'
      operationId: process_agent_command_api_v1_agent_command_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentCommandRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentCommandResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /health/env:
    get:
      summary: Environment Check
      description: Check environment variables without connecting to database.
      operationId: environment_check_health_env_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /health/db:
    get:
      summary: Database Health Check
      description: Enhanced database health check endpoint with comprehensive validation.
      operationId: database_health_check_health_db_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /health/db/reset:
    post:
      summary: Reset Database Configuration
      description: Reset database configuration endpoint for development/debugging.
      operationId: reset_database_configuration_health_db_reset_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/admin/rate-limit-stats:
    get:
      summary: Get Rate Limit Stats
      description: Get rate limiting statistics for monitoring and debugging.
      operationId: get_rate_limit_stats_api_v1_admin_rate_limit_stats_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/admin/cache-stats:
    get:
      summary: Get Cache Stats
      description: Get response cache statistics for monitoring.
      operationId: get_cache_stats_api_v1_admin_cache_stats_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/admin/cache-clear:
    post:
      summary: Clear Cache
      description: Clear the response cache.
      operationId: clear_cache_api_v1_admin_cache_clear_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/admin/cache-clear-pattern:
    post:
      summary: Clear Cache Pattern
      description: Clear cache entries matching a pattern.
      operationId: clear_cache_pattern_api_v1_admin_cache_clear_pattern_post
      parameters:
      - name: pattern
        in: query
        required: true
        schema:
          type: string
          title: Pattern
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/login:
    post:
      tags:
      - Authentication
      summary: Login
      description: 'Secure login endpoint that always validates against the database.


        NO CACHING of authentication results for security.'
      operationId: login_api_v1_auth_login_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_login_api_v1_auth_login_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Token'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/token:
    post:
      tags:
      - Authentication
      summary: Login
      description: 'Secure login endpoint that always validates against the database.


        NO CACHING of authentication results for security.'
      operationId: login_api_v1_auth_token_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_login_api_v1_auth_token_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Token'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/me:
    get:
      tags:
      - Authentication
      summary: Read Users Me
      description: 'Get current user information.


        Always returns fresh data from database.'
      operationId: read_users_me_api_v1_auth_me_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleUserResponse'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/auth/logout:
    post:
      tags:
      - Authentication
      summary: Logout
      description: 'Logout endpoint.


        Since we don''t cache sessions, logout is effectively a no-op.

        The client should discard the token.'
      operationId: logout_api_v1_auth_logout_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /api/v1/auth/debug-login:
    post:
      tags:
      - Authentication
      summary: Debug Login
      description: Debug login endpoint to test raw form data parsing.
      operationId: debug_login_api_v1_auth_debug_login_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/auth/refresh:
    post:
      tags:
      - Authentication
      summary: Refresh Access Token
      description: 'Refresh an access token using a refresh token.


        Always validates against the database for current user state.'
      operationId: refresh_access_token_api_v1_auth_refresh_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Token'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/verify:
    get:
      tags:
      - Authentication
      summary: Verify Token
      description: 'Verify if a token is valid.


        Always checks database for current user state.'
      operationId: verify_token_api_v1_auth_verify_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /api/v1/auth/register:
    post:
      tags:
      - Authentication
      summary: Register User
      description: 'Create a new user account (admin only).


        Security: Only superusers can create new accounts.'
      operationId: register_user_api_v1_auth_register_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleUserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/auth/public-register:
    post:
      tags:
      - Authentication
      summary: Public Register User
      description: 'Public user registration endpoint.


        Allows new users to create accounts without admin approval.'
      operationId: public_register_user_api_v1_auth_public_register_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleUserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/auth/quick-actions:
    get:
      tags:
      - Authentication
      summary: Get Quick Actions
      description: 'Get quick actions for the dashboard.

        Returns frequently used actions based on user role and preferences.'
      operationId: get_quick_actions_api_v1_auth_quick_actions_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /api/v1/auth/cache-stats:
    get:
      tags:
      - Authentication
      summary: Get Cache Statistics
      description: 'Get authentication cache statistics (admin only).


        Returns cache hit rates and performance metrics.'
      operationId: get_cache_statistics_api_v1_auth_cache_stats_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /api/v1/auth/clear-cache:
    post:
      tags:
      - Authentication
      summary: Clear Authentication Cache
      description: 'Clear authentication caches (admin only).


        This will force all tokens and user data to be re-validated from database.'
      operationId: clear_authentication_cache_api_v1_auth_clear_cache_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /auth/login:
    post:
      tags:
      - Authentication Test
      summary: Login
      description: 'Secure login endpoint that always validates against the database.


        NO CACHING of authentication results for security.'
      operationId: login_auth_login_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_login_auth_login_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Token'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /auth/token:
    post:
      tags:
      - Authentication Test
      summary: Login
      description: 'Secure login endpoint that always validates against the database.


        NO CACHING of authentication results for security.'
      operationId: login_auth_token_post
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Body_login_auth_token_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Token'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /auth/me:
    get:
      tags:
      - Authentication Test
      summary: Read Users Me
      description: 'Get current user information.


        Always returns fresh data from database.'
      operationId: read_users_me_auth_me_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleUserResponse'
      security:
      - OAuth2PasswordBearer: []
  /auth/logout:
    post:
      tags:
      - Authentication Test
      summary: Logout
      description: 'Logout endpoint.


        Since we don''t cache sessions, logout is effectively a no-op.

        The client should discard the token.'
      operationId: logout_auth_logout_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /auth/debug-login:
    post:
      tags:
      - Authentication Test
      summary: Debug Login
      description: Debug login endpoint to test raw form data parsing.
      operationId: debug_login_auth_debug_login_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /auth/refresh:
    post:
      tags:
      - Authentication Test
      summary: Refresh Access Token
      description: 'Refresh an access token using a refresh token.


        Always validates against the database for current user state.'
      operationId: refresh_access_token_auth_refresh_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Token'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /auth/verify:
    get:
      tags:
      - Authentication Test
      summary: Verify Token
      description: 'Verify if a token is valid.


        Always checks database for current user state.'
      operationId: verify_token_auth_verify_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /auth/register:
    post:
      tags:
      - Authentication Test
      summary: Register User
      description: 'Create a new user account (admin only).


        Security: Only superusers can create new accounts.'
      operationId: register_user_auth_register_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleUserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /auth/public-register:
    post:
      tags:
      - Authentication Test
      summary: Public Register User
      description: 'Public user registration endpoint.


        Allows new users to create accounts without admin approval.'
      operationId: public_register_user_auth_public_register_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleUserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /auth/quick-actions:
    get:
      tags:
      - Authentication Test
      summary: Get Quick Actions
      description: 'Get quick actions for the dashboard.

        Returns frequently used actions based on user role and preferences.'
      operationId: get_quick_actions_auth_quick_actions_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /auth/cache-stats:
    get:
      tags:
      - Authentication Test
      summary: Get Cache Statistics
      description: 'Get authentication cache statistics (admin only).


        Returns cache hit rates and performance metrics.'
      operationId: get_cache_statistics_auth_cache_stats_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /auth/clear-cache:
    post:
      tags:
      - Authentication Test
      summary: Clear Authentication Cache
      description: 'Clear authentication caches (admin only).


        This will force all tokens and user data to be re-validated from database.'
      operationId: clear_authentication_cache_auth_clear_cache_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /api/v1/ai/unified/parse-report:
    post:
      tags:
      - Unified AI
      - Unified AI
      summary: Parse Report Query
      description: 'Parse natural language report query using AI.


        Performance optimization: Detect simple patterns first before using AI.'
      operationId: parse_report_query_api_v1_ai_unified_parse_report_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportParseRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportRequest'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/ai/unified/categorize:
    post:
      tags:
      - Unified AI
      - Unified AI
      summary: Categorize Transaction
      description: Categorize single transaction using AI.
      operationId: categorize_transaction_api_v1_ai_unified_categorize_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategorizationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategorizationResult'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/ai/unified/categorize/batch:
    post:
      tags:
      - Unified AI
      - Unified AI
      summary: Categorize Batch
      description: Batch categorize transactions for performance.
      operationId: categorize_batch_api_v1_ai_unified_categorize_batch_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchCategorizationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties:
                  $ref: '#/components/schemas/CategorizationResult'
                type: object
                title: Response Categorize Batch Api V1 Ai Unified Categorize Batch
                  Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/ai/unified/conversation:
    post:
      tags:
      - Unified AI
      - Unified AI
      summary: Process Conversation
      description: Process conversational query with context awareness.
      operationId: process_conversation_api_v1_ai_unified_conversation_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConversationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/ai/unified/query:
    post:
      tags:
      - Unified AI
      - Unified AI
      summary: Process Query
      description: General AI query processing - unified entry point.
      operationId: process_query_api_v1_ai_unified_query_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AIRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/ai/unified/capabilities:
    get:
      tags:
      - Unified AI
      - Unified AI
      summary: Get Capabilities
      description: Get AI service capabilities and status.
      operationId: get_capabilities_api_v1_ai_unified_capabilities_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/transactions/fast:
    get:
      tags:
      - Transactions
      - Transactions
      summary: List Transactions Fast
      description: 'Fast transaction listing using cursor-based pagination.

        Optimized for dashboard performance with <100ms response time.'
      operationId: list_transactions_fast_api_v1_transactions_fast_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: cursor
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Cursor for pagination
          title: Cursor
        description: Cursor for pagination
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          maximum: 1000
          minimum: 1
          description: Number of items to return
          default: 100
          title: Limit
        description: Number of items to return
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTransactionResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/transactions/:
    get:
      tags:
      - Transactions
      - Transactions
      summary: List Transactions
      description: 'List transactions with comprehensive filtering and pagination.


        Supports filtering by:

        - Date range (start_date, end_date)

        - Transaction status (uncategorized, categorized, ai_suggested, user_modified)

        - Category

        - Upload batch

        - Amount range

        - Description search


        Performance optimization:

        - Set skip_count=true to skip total count calculation

        - Use smaller page_size for faster response'
      operationId: list_transactions_api_v1_transactions__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Filter by upload ID
          title: Upload Id
        description: Filter by upload ID
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Start date filter (YYYY-MM-DD)
          title: Start Date
        description: Start date filter (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: End date filter (YYYY-MM-DD)
          title: End Date
        description: End date filter (YYYY-MM-DD)
      - name: status_filter
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Filter by status
          title: Status Filter
        description: Filter by status
      - name: category_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Filter by category ID
          title: Category Id
        description: Filter by category ID
      - name: search_term
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Search in description
          title: Search Term
        description: Search in description
      - name: min_amount
        in: query
        required: false
        schema:
          anyOf:
          - type: number
          - type: 'null'
          description: Minimum amount
          title: Min Amount
        description: Minimum amount
      - name: max_amount
        in: query
        required: false
        schema:
          anyOf:
          - type: number
          - type: 'null'
          description: Maximum amount
          title: Max Amount
        description: Maximum amount
      - name: page
        in: query
        required: false
        schema:
          type: integer
          minimum: 1
          default: 1
          title: Page
      - name: page_size
        in: query
        required: false
        schema:
          type: integer
          maximum: 1000
          minimum: 1
          default: 50
          title: Page Size
      - name: sort_by
        in: query
        required: false
        schema:
          type: string
          description: Field to sort by
          default: date
          title: Sort By
        description: Field to sort by
      - name: sort_direction
        in: query
        required: false
        schema:
          type: string
          pattern: ^(asc|desc)$
          default: desc
          title: Sort Direction
      - name: skip_count
        in: query
        required: false
        schema:
          type: boolean
          description: Skip total count for performance
          default: false
          title: Skip Count
        description: Skip total count for performance
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTransactionResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/transactions/review-queue:
    get:
      tags:
      - Transactions
      - Transactions
      summary: Get Review Queue
      description: 'Get transactions that need manual review based on confidence thresholds
        and other criteria.


        Returns transactions that:

        - Have AI confidence below the specified threshold

        - Are uncategorized

        - Have conflicting categorizations

        - Meet any additional filter criteria'
      operationId: get_review_queue_api_v1_transactions_review_queue_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: confidence_threshold
        in: query
        required: false
        schema:
          anyOf:
          - type: number
          - type: 'null'
          description: Confidence threshold for review
          default: 0.85
          title: Confidence Threshold
        description: Confidence threshold for review
      - name: limit
        in: query
        required: false
        schema:
          anyOf:
          - type: integer
            maximum: 200
            minimum: 1
          - type: 'null'
          description: Maximum number of items to return
          default: 50
          title: Limit
        description: Maximum number of items to return
      - name: category_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Filter by category ID
          title: Category Id
        description: Filter by category ID
      - name: min_amount
        in: query
        required: false
        schema:
          anyOf:
          - type: number
          - type: 'null'
          description: Minimum amount filter
          title: Min Amount
        description: Minimum amount filter
      - name: max_amount
        in: query
        required: false
        schema:
          anyOf:
          - type: number
          - type: 'null'
          description: Maximum amount filter
          title: Max Amount
        description: Maximum amount filter
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Start date filter (YYYY-MM-DD)
          title: Start Date
        description: Start date filter (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: End date filter (YYYY-MM-DD)
          title: End Date
        description: End date filter (YYYY-MM-DD)
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/giki_ai_api__domains__transactions__router__ReviewQueueResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/transactions/stats:
    get:
      tags:
      - Transactions
      - Transactions
      summary: Get Transaction Stats
      description: 'Get comprehensive transaction statistics for dashboard display.


        Returns:

        - Total transaction count

        - Count of transactions needing review

        - Count of approved/categorized transactions

        - Confidence distribution

        - Processing summary with top categories'
      operationId: get_transaction_stats_api_v1_transactions_stats_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Start date filter (YYYY-MM-DD)
          title: Start Date
        description: Start date filter (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: End date filter (YYYY-MM-DD)
          title: End Date
        description: End date filter (YYYY-MM-DD)
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionStats'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/transactions/{transaction_id}:
    get:
      tags:
      - Transactions
      - Transactions
      summary: Get Transaction
      description: Get a single transaction by ID.
      operationId: get_transaction_api_v1_transactions__transaction_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: transaction_id
        in: path
        required: true
        schema:
          type: string
          title: Transaction Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/giki_ai_api__domains__transactions__router__TransactionResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - Transactions
      - Transactions
      summary: Delete Transaction
      description: Delete a transaction.
      operationId: delete_transaction_api_v1_transactions__transaction_id__delete
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: transaction_id
        in: path
        required: true
        schema:
          type: string
          title: Transaction Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Delete Transaction Api V1 Transactions  Transaction
                  Id  Delete
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    put:
      tags:
      - Transactions
      - Transactions
      summary: Update Transaction
      description: Update transaction details.
      operationId: update_transaction_api_v1_transactions__transaction_id__put
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: transaction_id
        in: path
        required: true
        schema:
          type: string
          title: Transaction Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionUpdateRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/giki_ai_api__domains__transactions__router__TransactionResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/transactions/batch/category:
    put:
      tags:
      - Transactions
      - Transactions
      summary: Update Batch Categories
      description: Update categories for multiple transactions, with optional AI categorization.
      operationId: update_batch_categories_api_v1_transactions_batch_category_put
      requestBody:
        content:
          application/json:
            schema:
              additionalProperties: true
              type: object
              title: Request
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Update Batch Categories Api V1 Transactions Batch
                  Category Put
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/transactions/{transaction_id}/category:
    put:
      tags:
      - Transactions
      - Transactions
      summary: Update Transaction Category
      description: Update transaction category (user modification).
      operationId: update_transaction_category_api_v1_transactions__transaction_id__category_put
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: transaction_id
        in: path
        required: true
        schema:
          type: string
          title: Transaction Id
      - name: category_id
        in: query
        required: true
        schema:
          type: integer
          title: Category Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Update Transaction Category Api V1 Transactions  Transaction
                  Id  Category Put
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/transactions/{transaction_id}/approve-ai-suggestion:
    post:
      tags:
      - Transactions
      - Transactions
      summary: Approve Ai Suggestion
      description: Approve AI-suggested category for a transaction.
      operationId: approve_ai_suggestion_api_v1_transactions__transaction_id__approve_ai_suggestion_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: transaction_id
        in: path
        required: true
        schema:
          type: string
          title: Transaction Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Approve Ai Suggestion Api V1 Transactions  Transaction
                  Id  Approve Ai Suggestion Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/transactions/bulk-approve:
    post:
      tags:
      - Transactions
      - Transactions
      summary: Bulk Approve Transactions
      description: Approve multiple transactions in bulk.
      operationId: bulk_approve_transactions_api_v1_transactions_bulk_approve_post
      requestBody:
        content:
          application/json:
            schema:
              items:
                type: string
              type: array
              title: Transaction Ids
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Bulk Approve Transactions Api V1 Transactions Bulk
                  Approve Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/transactions/improve-categorization:
    post:
      tags:
      - Transactions
      - Transactions
      summary: Improve Categorization
      description: "Analyze transactions and suggest categorization improvements.\n\
        \nThis endpoint analyzes the specified transactions and provides AI-powered\n\
        suggestions for improving categorization accuracy, business appropriateness,\n\
        and confidence levels.\n\nReturns:\n    Categorization ImprovementResponse\
        \ with analysis and recommendations"
      operationId: improve_categorization_api_v1_transactions_improve_categorization_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategorizationImprovementRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategorizationImprovementResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories:
    get:
      tags:
      - Categories
      - Categories
      summary: Read Tenant Categories
      description: 'Read all categories for the current tenant.

        OPTIMIZED: Added performance monitoring to track slow queries.'
      operationId: read_tenant_categories_api_v1_categories_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: include_usage_counts
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Include Usage Counts
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CategoryTree'
                title: Response Read Tenant Categories Api V1 Categories Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    post:
      tags:
      - Categories
      - Categories
      summary: Create Category
      operationId: create_category_api_v1_categories_post
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoryCreate'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/with-counts:
    get:
      tags:
      - Categories
      - Categories
      summary: Read Tenant Categories With Counts
      description: 'Read all categories for the current tenant WITH usage counts.

        PERFORMANCE: Separate endpoint for when counts are actually needed.'
      operationId: read_tenant_categories_with_counts_api_v1_categories_with_counts_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CategoryTree'
                type: array
                title: Response Read Tenant Categories With Counts Api V1 Categories
                  With Counts Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/{category_id}:
    get:
      tags:
      - Categories
      - Categories
      summary: Read Category
      operationId: read_category_api_v1_categories__category_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: category_id
        in: path
        required: true
        schema:
          type: integer
          title: Category Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoryTree'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    put:
      tags:
      - Categories
      - Categories
      summary: Update Category
      operationId: update_category_api_v1_categories__category_id__put
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: category_id
        in: path
        required: true
        schema:
          type: integer
          title: Category Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoryUpdate'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - Categories
      - Categories
      summary: Delete Category
      operationId: delete_category_api_v1_categories__category_id__delete
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: category_id
        in: path
        required: true
        schema:
          type: integer
          title: Category Id
      responses:
        '204':
          description: Successful Response
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/validate-gl-code:
    post:
      tags:
      - Categories
      - Categories
      summary: Validate Gl Code
      description: 'Validate GL code format and check for duplicates.


        Performs format validation and duplicate checking for GL codes

        within the tenant''s scope.'
      operationId: validate_gl_code_api_v1_categories_validate_gl_code_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GLCodeValidationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/suggest-gl-codes:
    post:
      tags:
      - Categories
      - Categories
      summary: Suggest Gl Codes
      description: 'Get AI-powered GL code suggestions for a category.


        Uses AI to suggest appropriate GL codes based on category name,

        path in hierarchy, and account type.'
      operationId: suggest_gl_codes_api_v1_categories_suggest_gl_codes_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/giki_ai_api__domains__categories__router__GLCodeSuggestionRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/{category_id}/gl-mapping:
    put:
      tags:
      - Categories
      - Categories
      summary: Update Category Gl Mapping
      description: 'Update GL code mapping for a category.


        This endpoint supports the core business requirement that categories map to

        GL (General Ledger) codes for accounting integration.'
      operationId: update_category_gl_mapping_api_v1_categories__category_id__gl_mapping_put
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: category_id
        in: path
        required: true
        schema:
          type: integer
          title: Category Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GLCodeUpdate'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/bulk-gl-update:
    post:
      tags:
      - Categories
      - Categories
      summary: Bulk Update Gl Mappings
      description: 'Bulk update GL code mappings for multiple categories.


        Allows customers to update GL codes and category structures via UI.'
      operationId: bulk_update_gl_mappings_api_v1_categories_bulk_gl_update_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkGLUpdate'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Bulk Update Gl Mappings Api V1 Categories Bulk Gl
                  Update Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/gl-mappings/export:
    get:
      tags:
      - Categories
      - Categories
      summary: Export Gl Mappings
      description: 'Export GL code mappings for accounting software integration.


        Supports integration with QuickBooks, SAP, Xero, and other accounting systems.'
      operationId: export_gl_mappings_api_v1_categories_gl_mappings_export_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: format_type
        in: query
        required: false
        schema:
          type: string
          default: csv
          title: Format Type
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/learn-from-onboarding:
    post:
      tags:
      - Categories
      - Categories
      summary: Learn Categories From Onboarding
      description: 'Learn multilevel category structure from onboarding transaction
        data.


        This implements the core business requirement that categories are ALWAYS

        multilevel and learned from onboarding data for each tenant.'
      operationId: learn_categories_from_onboarding_api_v1_categories_learn_from_onboarding_post
      requestBody:
        content:
          application/json:
            schema:
              items:
                additionalProperties: true
                type: object
              type: array
              title: Transactions
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Learn Categories From Onboarding Api V1 Categories
                  Learn From Onboarding Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/hierarchy:
    get:
      tags:
      - Categories
      - Categories
      summary: Get Category Hierarchy
      description: 'Get complete multilevel category hierarchy for a tenant.


        Returns hierarchical tree structure with GL code information and usage statistics.'
      operationId: get_category_hierarchy_api_v1_categories_hierarchy_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: include_usage_counts
        in: query
        required: false
        schema:
          type: boolean
          default: true
          title: Include Usage Counts
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Get Category Hierarchy Api V1 Categories Hierarchy
                  Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/create-multilevel-hierarchies:
    post:
      tags:
      - Categories
      - Categories
      summary: Create Multilevel Hierarchies
      description: "Create multilevel category hierarchies from customer transaction\
        \ data.\n\nThis endpoint:\n1. Loads customer transaction data from input files\n\
        2. Analyzes original categorization patterns\n3. Detects hierarchical relationships\n\
        4. Creates parent-child category structures\n5. Returns statistics on created\
        \ hierarchies\n\nArgs:\n    clear_existing: Whether to clear existing categories\
        \ first"
      operationId: create_multilevel_hierarchies_api_v1_categories_create_multilevel_hierarchies_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: clear_existing
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Clear Existing
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/dynamic-hierarchies:
    post:
      tags:
      - Categories
      - Categories
      summary: Build Dynamic Category Hierarchies
      description: 'Build dynamic category hierarchies using business context and
        AI analysis.


        This endpoint creates intelligent category hierarchies based on:

        - Industry-specific templates

        - Company size considerations

        - AI-powered analysis of category relationships

        - Business context from onboarding


        Request body should contain a list of category names to organize.

        Returns suggested hierarchies without creating categories in the database.'
      operationId: build_dynamic_category_hierarchies_api_v1_categories_dynamic_hierarchies_post
      requestBody:
        content:
          application/json:
            schema:
              items:
                type: string
              type: array
              title: Category Names
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Build Dynamic Category Hierarchies Api V1 Categories
                  Dynamic Hierarchies Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/validate/quick:
    get:
      tags:
      - Categories
      - Categories
      summary: Quick Mis Validation
      description: 'Quick MIS structure validation for dashboard display.


        Provides essential validation metrics for real-time dashboard updates

        without the overhead of comprehensive validation.'
      operationId: quick_mis_validation_api_v1_categories_validate_quick_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Quick Mis Validation Api V1 Categories Validate Quick
                  Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/validate/comprehensive:
    get:
      tags:
      - Categories
      - Categories
      summary: Comprehensive Mis Validation
      description: 'Comprehensive MIS structure validation with detailed report.


        Performs complete validation including:

        - Structure compliance

        - GL code validation

        - Business rules checking

        - Industry-specific compliance

        - Financial reporting readiness'
      operationId: comprehensive_mis_validation_api_v1_categories_validate_comprehensive_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: industry
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Industry
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Comprehensive Mis Validation Api V1 Categories Validate
                  Comprehensive Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/validate/structure-health:
    get:
      tags:
      - Categories
      - Categories
      summary: Get Structure Health Metrics
      description: 'Get structure health metrics for enterprise dashboard display.


        Provides key health indicators that can be monitored over time

        and displayed in executive dashboards.'
      operationId: get_structure_health_metrics_api_v1_categories_validate_structure_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Get Structure Health Metrics Api V1 Categories Validate
                  Structure Health Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/setup-standard-hierarchy:
    post:
      tags:
      - Categories
      - Categories
      summary: Setup Standard Hierarchy
      description: 'Setup the standard MIS hierarchy for a tenant.


        Creates the complete 3-level hierarchy:

        - Level 0: Income/Expenses

        - Level 1: Major categories (Sales & Services, Employee Costs, etc.)

        - Level 2: Subcategories


        This ensures consistent categorization across all tenants.'
      operationId: setup_standard_hierarchy_api_v1_categories_setup_standard_hierarchy_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/fix-hierarchy-integrity:
    post:
      tags:
      - Categories
      - Categories
      summary: Fix Hierarchy Integrity
      description: 'Fix hierarchy integrity issues for a tenant.


        Repairs:

        - Incorrect level values

        - Missing or incorrect paths

        - Orphaned categories


        This ensures the category tree is properly structured.'
      operationId: fix_hierarchy_integrity_api_v1_categories_fix_hierarchy_integrity_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/convert-flat-hierarchies:
    post:
      tags:
      - Categories
      - Categories
      summary: Convert Flat Hierarchies
      description: "Convert flat hierarchical categories to proper parent-child structure.\n\
        \nFixes categories like:\n- \"Travel Expenses > Airfare > International >\
        \ Economy Class\"\n- \"Office & Admin > Utilities > Electricity\"\n\nBy converting\
        \ them into:\n- Travel Expenses (parent)\n  - Airfare (child)\n    - International\
        \ (grandchild)\n      - Economy Class (great-grandchild)\n\nThis ensures proper\
        \ hierarchy structure for MIS reporting."
      operationId: convert_flat_hierarchies_api_v1_categories_convert_flat_hierarchies_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/clear-test-data:
    post:
      tags:
      - Categories
      - Categories
      summary: Clear Test Data
      description: 'Clear all test data (transactions, uploads) to remove foreign
        key constraints.


        This allows category hierarchy operations to run without constraint violations.

        USE WITH CAUTION - This will delete all transaction and upload data!'
      operationId: clear_test_data_api_v1_categories_clear_test_data_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/reset-to-clean-hierarchy:
    post:
      tags:
      - Categories
      - Categories
      summary: Reset To Clean Hierarchy
      description: 'NUCLEAR OPTION: Delete ALL categories and rebuild with clean standard
        MIS hierarchy.


        This completely resets the category system to a pristine state with only

        the standard 3-level MIS hierarchy (Income/Expenses > Major > Subcategories).


        USE WITH EXTREME CAUTION - This will delete ALL existing categories!'
      operationId: reset_to_clean_hierarchy_api_v1_categories_reset_to_clean_hierarchy_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/hierarchical-results/{upload_id}:
    get:
      tags:
      - Categories
      - Categories
      summary: Get Hierarchical Results
      description: 'Get hierarchical categorization results with visualization data.


        Returns a complete hierarchical breakdown of categorized transactions

        including income/expense hierarchies, statistics, and enhancement suggestions.'
      operationId: get_hierarchical_results_api_v1_categories_hierarchical_results__upload_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HierarchicalResults'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/enhancement-analysis/{upload_id}:
    get:
      tags:
      - Categories
      - Categories
      summary: Analyze Enhancements
      description: 'Analyze an upload for enhancement opportunities.


        Detects opportunities for progressive MIS enhancement including:

        - Uncategorized transaction patterns

        - Low confidence categorizations

        - Vendor mapping opportunities

        - Schema enhancement with GL codes

        - Historical pattern learning


        Returns actionable recommendations with potential accuracy gains.'
      operationId: analyze_enhancements_api_v1_categories_enhancement_analysis__upload_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnhancementAnalysisResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/vendors/detect/{upload_id}:
    get:
      tags:
      - Categories
      - Categories
      summary: Detect Vendor Opportunities
      description: 'Detect vendor patterns and opportunities for improved categorization.


        Returns vendors that would benefit from consistent mapping.'
      operationId: detect_vendor_opportunities_api_v1_categories_vendors_detect__upload_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Detect Vendor Opportunities Api V1 Categories Vendors
                  Detect  Upload Id  Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/vendors/apply-intelligent-mappings:
    post:
      tags:
      - Categories
      - Categories
      summary: Apply Intelligent Vendor Mappings
      description: 'Apply intelligent vendor mappings using context-aware lookup.


        This endpoint:

        1. Detects high-value vendors without mappings

        2. Performs context-aware lookup (pattern matching, simulated Google search)

        3. Creates vendor-to-category mappings with confidence scores

        4. Updates existing transactions with new mappings


        Returns accuracy improvement and mapping details.'
      operationId: apply_intelligent_vendor_mappings_api_v1_categories_vendors_apply_intelligent_mappings_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Apply Intelligent Vendor Mappings Api V1 Categories
                  Vendors Apply Intelligent Mappings Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/vendors/lookup-preview:
    get:
      tags:
      - Categories
      - Categories
      summary: Preview Vendor Lookup
      description: 'Preview what a vendor lookup would return without creating mappings.


        Useful for understanding how the context-aware lookup works.'
      operationId: preview_vendor_lookup_api_v1_categories_vendors_lookup_preview_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: vendor_name
        in: query
        required: true
        schema:
          type: string
          title: Vendor Name
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Preview Vendor Lookup Api V1 Categories Vendors Lookup
                  Preview Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/vendors/groupings:
    get:
      tags:
      - Categories
      - Categories
      summary: Get Vendor Groupings
      description: 'Get vendor groupings for the transaction categorization UI.


        Groups transactions by vendor and shows:

        - Transaction counts (total, debits, credits)

        - Amount totals and breakdowns

        - Current categorization patterns

        - Existing mappings if any


        This integrates with the bulk categorization UI.'
      operationId: get_vendor_groupings_api_v1_categories_vendors_groupings_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Upload Id
      - name: include_mapped
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Include Mapped
      - name: min_transactions
        in: query
        required: false
        schema:
          type: integer
          default: 2
          title: Min Transactions
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VendorGrouping'
                title: Response Get Vendor Groupings Api V1 Categories Vendors Groupings
                  Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/vendors/user-mapping:
    post:
      tags:
      - Categories
      - Categories
      summary: Create User Vendor Mapping
      description: 'Create a user-defined vendor-to-category mapping.


        Key features:

        - Credit/debit awareness: Map same vendor differently for income vs expenses

        - Amount filtering: Only apply to transactions within amount range

        - Pattern matching: Use regex for complex vendor descriptions

        - Immediate application: Optionally categorize existing transactions


        Example:

        - Map "AMAZON" to "Office Supplies" for debits < $100

        - Map "AMAZON" to "Equipment" for debits > $100

        - Map "STRIPE" to "Revenue" for credits'
      operationId: create_user_vendor_mapping_api_v1_categories_vendors_user_mapping_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: apply_immediately
        in: query
        required: false
        schema:
          type: boolean
          default: true
          title: Apply Immediately
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VendorMappingCreate'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VendorMappingResult'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/vendors/bulk-user-mappings:
    post:
      tags:
      - Categories
      - Categories
      summary: Create Bulk User Vendor Mappings
      description: 'Create multiple vendor mappings at once.


        Useful when categorizing multiple vendors from the grouping UI.'
      operationId: create_bulk_user_vendor_mappings_api_v1_categories_vendors_bulk_user_mappings_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkVendorMappingRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VendorMappingResult'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/vendors/user-mappings:
    get:
      tags:
      - Categories
      - Categories
      summary: Get User Vendor Mappings
      description: 'Get all vendor mappings for the current tenant.


        Shows:

        - Mapping rules (vendor, category, direction, amounts)

        - Usage statistics (times applied, last used)

        - Created by information'
      operationId: get_user_vendor_mappings_api_v1_categories_vendors_user_mappings_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: include_inactive
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Include Inactive
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VendorMapping'
                title: Response Get User Vendor Mappings Api V1 Categories Vendors
                  User Mappings Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/vendors/user-mapping/{mapping_id}:
    put:
      tags:
      - Categories
      - Categories
      summary: Update User Vendor Mapping
      description: 'Update an existing vendor mapping.


        Can update category, direction rules, amount ranges, or deactivate.'
      operationId: update_user_vendor_mapping_api_v1_categories_vendors_user_mapping__mapping_id__put
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: mapping_id
        in: path
        required: true
        schema:
          type: integer
          title: Mapping Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VendorMappingUpdate'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Update User Vendor Mapping Api V1 Categories Vendors
                  User Mapping  Mapping Id  Put
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - Categories
      - Categories
      summary: Delete User Vendor Mapping
      description: 'Delete (deactivate) a vendor mapping.


        Note: Mappings are soft-deleted to preserve history.'
      operationId: delete_user_vendor_mapping_api_v1_categories_vendors_user_mapping__mapping_id__delete
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: mapping_id
        in: path
        required: true
        schema:
          type: integer
          title: Mapping Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/metrics/categorization:
    get:
      tags:
      - Categories
      - Categories
      summary: Get Categorization Metrics
      description: 'Get real categorization metrics for dashboard display.


        Returns actual data-based metrics including:

        - Categorization rate (categorized vs total transactions)

        - Categorization breakdown by source (AI, user, business logic)

        - Confidence distribution (high/medium/low confidence counts)

        - Top categories by transaction count

        - Processing status (pending review, user confirmed)

        - Enhancement status indicators


        Replaces fake accuracy displays with meaningful categorization status.'
      operationId: get_categorization_metrics_api_v1_categories_metrics_categorization_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Get Categorization Metrics Api V1 Categories Metrics
                  Categorization Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/metrics/trends:
    get:
      tags:
      - Categories
      - Categories
      summary: Get Categorization Trends
      description: 'Get categorization trends over time.


        Returns daily categorization rates and confidence trends for

        the specified number of days (default 30).'
      operationId: get_categorization_trends_api_v1_categories_metrics_trends_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: days
        in: query
        required: false
        schema:
          type: integer
          default: 30
          title: Days
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Get Categorization Trends Api V1 Categories Metrics
                  Trends Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/metrics/confidence-insights:
    get:
      tags:
      - Categories
      - Categories
      summary: Get Confidence Insights
      description: 'Get confidence scoring insights by category.


        Returns detailed analysis of confidence scores for different

        categories and their validation rates.'
      operationId: get_confidence_insights_api_v1_categories_metrics_confidence_insights_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Get Confidence Insights Api V1 Categories Metrics
                  Confidence Insights Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/review/queue/{upload_id}:
    get:
      tags:
      - Review Queue
      - Review Queue
      summary: Get Review Queue
      description: 'Get the complete review queue for an upload.


        Returns:

        - Summary statistics

        - Medium confidence suggestions

        - Low confidence groups

        - Uncategorized transactions'
      operationId: get_review_queue_api_v1_categories_review_queue__upload_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/giki_ai_api__domains__categories__review_router__ReviewQueueResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/categories/review/bulk-action:
    post:
      tags:
      - Review Queue
      - Review Queue
      summary: Process Bulk Review Action
      description: 'Process a bulk action on multiple transactions.


        Actions:

        - accept: Accept AI suggestions

        - reject: Clear suggestions and mark for manual review

        - categorize: Apply specific category'
      operationId: process_bulk_review_action_api_v1_categories_review_bulk_action_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkReviewAction'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Process Bulk Review Action Api V1 Categories Review
                  Bulk Action Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/review/group-action:
    post:
      tags:
      - Review Queue
      - Review Queue
      summary: Process Group Review Action
      description: 'Process an action on a transaction group.


        Can optionally create a vendor mapping for future transactions.'
      operationId: process_group_review_action_api_v1_categories_review_group_action_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupReviewAction'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Process Group Review Action Api V1 Categories Review
                  Group Action Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/categories/review/accept-all-suggestions:
    post:
      tags:
      - Review Queue
      - Review Queue
      summary: Accept All Suggestions
      description: 'Accept all AI suggestions above a confidence threshold.


        Quick action for accepting high-confidence suggestions.'
      operationId: accept_all_suggestions_api_v1_categories_review_accept_all_suggestions_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: query
        required: true
        schema:
          type: string
          title: Upload Id
      - name: min_confidence
        in: query
        required: false
        schema:
          type: number
          default: 0.85
          title: Min Confidence
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Accept All Suggestions Api V1 Categories Review Accept
                  All Suggestions Post
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/gl-codes/hierarchy:
    get:
      tags:
      - GL Code Management
      - GL Code Management
      summary: Get Gl Code Hierarchy
      description: Get complete GL code hierarchy with compliance metrics for M3 validation.
      operationId: get_gl_code_hierarchy_api_v1_gl_codes_hierarchy_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: include_stats
        in: query
        required: false
        schema:
          type: boolean
          description: Include compliance statistics
          default: true
          title: Include Stats
        description: Include compliance statistics
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GLCodeHierarchyResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/gl-codes/mappings:
    post:
      tags:
      - GL Code Management
      - GL Code Management
      summary: Bulk Update Gl Mappings
      description: Bulk update GL code mappings with validation and compliance checking.
      operationId: bulk_update_gl_mappings_api_v1_gl_codes_mappings_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkGLCodeRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/gl-codes/validate/{gl_code}:
    get:
      tags:
      - GL Code Management
      - GL Code Management
      summary: Validate Gl Code
      description: Validate GL code format, uniqueness, and business rules compliance.
      operationId: validate_gl_code_api_v1_gl_codes_validate__gl_code__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: gl_code
        in: path
        required: true
        schema:
          type: string
          title: Gl Code
      - name: account_type
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Expected account type for validation
          title: Account Type
        description: Expected account type for validation
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GLCodeValidationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/gl-codes/suggest:
    post:
      tags:
      - GL Code Management
      - GL Code Management
      summary: Get Gl Code Suggestions
      description: Get AI-powered GL code suggestions based on category and transaction
        context.
      operationId: get_gl_code_suggestions_api_v1_gl_codes_suggest_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          maximum: 20
          minimum: 1
          description: Maximum suggestions to return
          default: 5
          title: Limit
        description: Maximum suggestions to return
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/giki_ai_api__domains__categories__gl_code_router__GLCodeSuggestionRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/gl-codes/analytics:
    get:
      tags:
      - GL Code Management
      - GL Code Management
      summary: Get Gl Code Analytics
      description: Get comprehensive GL code compliance analytics for M3 milestone
        tracking.
      operationId: get_gl_code_analytics_api_v1_gl_codes_analytics_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: include_export_validation
        in: query
        required: false
        schema:
          type: boolean
          description: Include export format validation
          default: true
          title: Include Export Validation
        description: Include export format validation
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GLCodeAnalyticsResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/gl-codes/auto-assign:
    post:
      tags:
      - GL Code Management
      - GL Code Management
      summary: Auto Assign Gl Codes
      description: Auto-assign GL codes to categories using AI with configurable confidence
        threshold.
      operationId: auto_assign_gl_codes_api_v1_gl_codes_auto_assign_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: confidence_threshold
        in: query
        required: false
        schema:
          type: number
          maximum: 1.0
          minimum: 0.1
          description: Minimum confidence for auto-assignment
          default: 0.8
          title: Confidence Threshold
        description: Minimum confidence for auto-assignment
      - name: dry_run
        in: query
        required: false
        schema:
          type: boolean
          description: Preview assignments without applying
          default: false
          title: Dry Run
        description: Preview assignments without applying
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/gl-codes/export/{format}:
    get:
      tags:
      - GL Code Management
      - GL Code Management
      summary: Export Gl Mappings
      description: Export GL code mappings in various formats (CSV, JSON, QuickBooks,
        Sage, Xero).
      operationId: export_gl_mappings_api_v1_gl_codes_export__format__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: format
        in: path
        required: true
        schema:
          type: string
          title: Format
      - name: include_validation
        in: query
        required: false
        schema:
          type: boolean
          description: Include validation data in export
          default: true
          title: Include Validation
        description: Include validation data in export
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/gl-codes/import/chart-of-accounts:
    post:
      tags:
      - GL Code Management
      - GL Code Management
      summary: Import Chart Of Accounts
      description: Import chart of accounts file to create GL code hierarchy.
      operationId: import_chart_of_accounts_api_v1_gl_codes_import_chart_of_accounts_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: auto_map
        in: query
        required: false
        schema:
          type: boolean
          description: Automatically map to existing categories
          default: true
          title: Auto Map
        description: Automatically map to existing categories
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties: true
              title: File Data
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/tests:
    post:
      tags:
      - Accuracy
      - accuracy
      summary: Create Accuracy Test
      description: Create a new accuracy test configuration.
      operationId: create_accuracy_test_api_v1_accuracy_tests_post
      security:
      - OAuth2PasswordBearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccuracyTestCreate'
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccuracyTestResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    get:
      tags:
      - Accuracy
      - accuracy
      summary: List Accuracy Tests
      description: List accuracy tests with optional filtering.
      operationId: list_accuracy_tests_api_v1_accuracy_tests_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: scenario
        in: query
        required: false
        schema:
          $ref: '#/components/schemas/AccuracyTestScenario'
      - name: test_status
        in: query
        required: false
        schema:
          $ref: '#/components/schemas/AccuracyTestStatus'
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          default: 50
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          default: 0
          title: Offset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccuracyTestListResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/tests/{test_id}:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Accuracy Test
      description: Get accuracy test by ID.
      operationId: get_accuracy_test_api_v1_accuracy_tests__test_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: test_id
        in: path
        required: true
        schema:
          type: integer
          title: Test Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccuracyTestResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/tests/{test_id}/run:
    post:
      tags:
      - Accuracy
      - accuracy
      summary: Run Accuracy Test
      description: Execute an accuracy test.
      operationId: run_accuracy_test_api_v1_accuracy_tests__test_id__run_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: test_id
        in: path
        required: true
        schema:
          type: integer
          title: Test Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccuracyTestExecutionResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/tests/{test_id}/metrics:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Accuracy Metrics
      description: Get accuracy metrics for a specific test.
      operationId: get_accuracy_metrics_api_v1_accuracy_tests__test_id__metrics_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: test_id
        in: path
        required: true
        schema:
          type: integer
          title: Test Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccuracyMetricListResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/tests/{test_id}/judgments:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Ai Judgments
      description: Get AI judgments for a specific test.
      operationId: get_ai_judgments_api_v1_accuracy_tests__test_id__judgments_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: test_id
        in: path
        required: true
        schema:
          type: integer
          title: Test Id
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          default: 1000
          title: Limit
      - name: judgment_result
        in: query
        required: false
        schema:
          type: string
          title: Judgment Result
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIJudgmentListResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/schemas:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: List Category Schemas
      description: List category schemas for the tenant.
      operationId: list_category_schemas_api_v1_accuracy_schemas_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategorySchemaListResponse'
      security:
      - OAuth2PasswordBearer: []
    post:
      tags:
      - Accuracy
      - accuracy
      summary: Create Category Schema
      description: Create a new category schema.
      operationId: create_category_schema_api_v1_accuracy_schemas_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategorySchemaCreate'
        required: true
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategorySchemaResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/accuracy/schemas/import:
    post:
      tags:
      - Accuracy
      - accuracy
      summary: Import Category Schema
      description: Import category schema from file or structured data.
      operationId: import_category_schema_api_v1_accuracy_schemas_import_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategorySchemaImport'
        required: true
      responses:
        '201':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategorySchemaImportResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/accuracy/schemas/{schema_id}:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Category Schema
      description: Get category schema by ID.
      operationId: get_category_schema_api_v1_accuracy_schemas__schema_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: schema_id
        in: path
        required: true
        schema:
          type: integer
          title: Schema Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategorySchemaResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/tests/{test_id}/report:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Accuracy Report
      description: Get comprehensive accuracy report for a test.
      operationId: get_accuracy_report_api_v1_accuracy_tests__test_id__report_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: test_id
        in: path
        required: true
        schema:
          type: integer
          title: Test Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccuracyReportResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/temporal/validate/{tenant_id}:
    post:
      tags:
      - Accuracy
      - accuracy
      summary: Run Temporal Accuracy Validation
      description: 'Run progressive temporal accuracy validation for historical data.


        This endpoint implements temporal validation:

        - Training/testing data split (Jan-Jun train vs Jul-Dec test)

        - Progressive monthly validation with expanding training data

        - Improvement-over-original evaluation using AI judge

        - Temporal consistency and trend analysis'
      operationId: run_temporal_accuracy_validation_api_v1_accuracy_temporal_validate__tenant_id__post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/temporal/status/{tenant_id}:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Temporal Validation Status
      description: Get current status of temporal validation readiness for a tenant.
      operationId: get_temporal_validation_status_api_v1_accuracy_temporal_status__tenant_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/m2/readiness/{tenant_id}:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Check M2 Readiness
      description: 'Check M2 milestone readiness for given tenant.


        Validates that sufficient historical data exists for temporal accuracy testing.'
      operationId: check_m2_readiness_api_v1_accuracy_m2_readiness__tenant_id__get
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/m2/validate/{tenant_id}:
    post:
      tags:
      - Accuracy
      - accuracy
      summary: Run M2 Validation
      description: 'Run complete M2 temporal accuracy validation for given tenant.


        Executes progressive monthly validation and returns milestone completion status.'
      operationId: run_m2_validation_api_v1_accuracy_m2_validate__tenant_id__post
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/m2/report/{tenant_id}:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Generate M2 Report
      description: "Generate comprehensive M2 validation report for given tenant.\n\
        \nArgs:\n    tenant_id: Target tenant ID\n    save_file: Whether to save report\
        \ to file system"
      operationId: generate_m2_report_api_v1_accuracy_m2_report__tenant_id__get
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      - name: save_file
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Save File
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/accuracy/dashboard:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Accuracy Dashboard
      description: Get dashboard data with enhanced metrics for accuracy tracking.
      operationId: get_accuracy_dashboard_api_v1_accuracy_dashboard_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/accuracy/temporal-data:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Temporal Accuracy Data
      description: Get temporal accuracy data for M2 milestone tracking.
      operationId: get_temporal_accuracy_data_api_v1_accuracy_temporal_data_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/accuracy/category-breakdown:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Category Accuracy Breakdown
      description: Get category-level accuracy breakdown for analysis.
      operationId: get_category_accuracy_breakdown_api_v1_accuracy_category_breakdown_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/accuracy/milestones:
    get:
      tags:
      - Accuracy
      - accuracy
      summary: Get Milestone Status
      description: Get milestone status with validation details.
      operationId: get_milestone_status_api_v1_accuracy_milestones_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/agents/chat:
    post:
      tags:
      - Agents
      - Agents
      summary: Chat With Agent
      description: 'Process chat message with conversational agent.


        This endpoint handles both natural language messages and specific commands

        from the frontend UnifiedAgentPanel.'
      operationId: chat_with_agent_api_v1_agents_chat_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatMessage'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/giki_ai_api__domains__agents__router__ChatResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/agents/commands:
    get:
      tags:
      - Agents
      - Agents
      summary: Get Available Commands
      description: Get list of available commands for the agent.
      operationId: get_available_commands_api_v1_agents_commands_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /api/v1/agents/status:
    get:
      tags:
      - Agents
      - Agents
      summary: Get Agent Status
      description: Get agent status and capabilities.
      operationId: get_agent_status_api_v1_agents_status_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /api/v1/agents/suggestions:
    post:
      tags:
      - Agents
      - Agents
      summary: Get Command Suggestions
      description: Get command suggestions based on partial message input.
      operationId: get_command_suggestions_api_v1_agents_suggestions_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: partial_message
        in: query
        required: true
        schema:
          type: string
          title: Partial Message
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/dashboard/metrics:
    get:
      tags:
      - Dashboard
      - Dashboard
      summary: Get Dashboard Metrics
      description: Get dashboard metrics for the current tenant.
      operationId: get_dashboard_metrics_api_v1_dashboard_metrics_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Start date (YYYY-MM-DD)
          title: Start Date
        description: Start date (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: End date (YYYY-MM-DD)
          title: End Date
        description: End date (YYYY-MM-DD)
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/dashboard/recent-transactions:
    get:
      tags:
      - Dashboard
      - Dashboard
      summary: Get Recent Transactions
      description: Get recent transactions for the dashboard.
      operationId: get_recent_transactions_api_v1_dashboard_recent_transactions_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          maximum: 50
          minimum: 1
          default: 10
          title: Limit
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/dashboard/category-breakdown:
    get:
      tags:
      - Dashboard
      - Dashboard
      summary: Get Category Breakdown
      description: Get spending breakdown by category.
      operationId: get_category_breakdown_api_v1_dashboard_category_breakdown_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Start date (YYYY-MM-DD)
          title: Start Date
        description: Start date (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: End date (YYYY-MM-DD)
          title: End Date
        description: End date (YYYY-MM-DD)
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/intelligence/detect-accounting-system:
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Detect Accounting System
      description: Detect accounting system from column names and sample data.
      operationId: detect_accounting_system_api_v1_intelligence_detect_accounting_system_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountingSystemDetectionRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountingSystemDetectionResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/intelligence/extract-entities:
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Extract Entities
      description: Extract entities from transaction descriptions.
      operationId: extract_entities_api_v1_intelligence_extract_entities_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EntityExtractionRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityExtractionResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/intelligence/process-amount:
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Process Amount
      description: Process amount fields to determine transaction type and normalize
        amounts.
      operationId: process_amount_api_v1_intelligence_process_amount_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AmountProcessingRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AmountProcessingResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/intelligence/normalize-descriptions:
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Normalize Descriptions
      description: Normalize and clean transaction descriptions.
      operationId: normalize_descriptions_api_v1_intelligence_normalize_descriptions_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DescriptionNormalizationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DescriptionNormalizationResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/intelligence/analyze-batch:
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Analyze Batch
      description: Perform comprehensive analysis of a batch of transactions.
      operationId: analyze_batch_api_v1_intelligence_analyze_batch_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchAnalysisRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchAnalysisResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/intelligence/health:
    get:
      tags:
      - Intelligence
      - intelligence
      summary: Health Check
      description: Health check endpoint for intelligence service.
      operationId: health_check_api_v1_intelligence_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/intelligence/agent/customer/query:
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Process Customer Query
      description: 'Process customer queries using the Customer-Facing Agent.

        This endpoint handles all conversational AI interactions.'
      operationId: process_customer_query_api_v1_intelligence_agent_customer_query_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerQueryRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentApiResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/intelligence/agent/customer/audio:
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Process Audio Query
      description: 'Process audio input using Gemini 2.0 Flash direct audio token
        consumption.

        Supports WAV, MP3, AIFF, AAC, OGG Vorbis, FLAC formats.'
      operationId: process_audio_query_api_v1_intelligence_agent_customer_audio_post
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_process_audio_query_api_v1_intelligence_agent_customer_audio_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentApiResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/intelligence/agent/data/process:
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Process Data Operation
      description: 'Process data operations using the Data Processing Agent.

        This endpoint handles file processing, categorization, and RAG updates.'
      operationId: process_data_operation_api_v1_intelligence_agent_data_process_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataProcessingRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentApiResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/intelligence/accuracy-metrics:
    get:
      tags:
      - Intelligence
      - intelligence
      summary: Get Accuracy Metrics
      description: Get accuracy metrics for categorization.
      operationId: get_accuracy_metrics_api_v1_intelligence_accuracy_metrics_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Get Accuracy Metrics For Onboarding
      description: Get accuracy metrics for onboarding simulation chart.
      operationId: get_accuracy_metrics_for_onboarding_api_v1_intelligence_accuracy_metrics_post
      requestBody:
        content:
          application/json:
            schema:
              additionalProperties: true
              type: object
              title: Request
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/intelligence/chat:
    post:
      tags:
      - Intelligence
      - intelligence
      summary: Chat With Giki
      description: 'Unified chat interface for the Giki agent.


        This endpoint provides a single interface to all agent capabilities while

        hiding the multi-agent architecture from users. All responses appear to

        come from a single "Giki" agent.


        Features:

        - Natural language processing of user queries

        - Automatic routing to specialized agents (hidden from user)

        - Real-time updates via WebSocket

        - Complete UI equivalence

        - Unified "Giki" interface regardless of backend agent used'
      operationId: chat_with_giki_api_v1_intelligence_chat_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/giki_ai_api__domains__intelligence__router__ChatResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/adk/api/v1/adk/agents/discover:
    get:
      tags:
      - ADK Agents
      - ADK Agents
      summary: Discover Agents
      description: 'Discover available ADK agents in the system.

        Returns a list of all registered agents with their capabilities and status.'
      operationId: discover_agents_api_v1_adk_api_v1_adk_agents_discover_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ADKAgent'
                type: array
                title: Response Discover Agents Api V1 Adk Api V1 Adk Agents Discover
                  Get
      security:
      - OAuth2PasswordBearer: []
  /api/v1/adk/api/v1/adk/agents/{agent_id}/sessions:
    post:
      tags:
      - ADK Agents
      - ADK Agents
      summary: Start Agent Session
      description: 'Start a new agent session with optional memory preloading.

        Creates a new conversation context for the specified agent.'
      operationId: start_agent_session_api_v1_adk_api_v1_adk_agents__agent_id__sessions_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          title: Agent Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartSessionRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentSession'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/adk/api/v1/adk/agents/transfer:
    post:
      tags:
      - ADK Agents
      - ADK Agents
      summary: Transfer To Agent
      description: 'Transfer conversation to another agent using A2A protocol.

        Implements opaque agent-to-agent communication with context preservation.'
      operationId: transfer_to_agent_api_v1_adk_api_v1_adk_agents_transfer_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentTransferRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentSession'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/adk/api/v1/adk/agents/{agent_id}/memory:
    get:
      tags:
      - ADK Agents
      - ADK Agents
      summary: Load Agent Memory
      description: 'Load persistent memory for an agent.

        Returns the agent''s memory context including conversation history and preferences.'
      operationId: load_agent_memory_api_v1_adk_api_v1_adk_agents__agent_id__memory_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/adk/api/v1/adk/agents/{agent_id}/memory/preload:
    post:
      tags:
      - ADK Agents
      - ADK Agents
      summary: Preload Memory
      description: 'Preload memory context for faster agent responses.

        Allows pre-warming agent memory with relevant context data.'
      operationId: preload_memory_api_v1_adk_api_v1_adk_agents__agent_id__memory_preload_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          title: Agent Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreloadMemoryRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/adk/api/v1/adk/tools/invoke:
    post:
      tags:
      - ADK Agents
      - ADK Agents
      summary: Invoke Adk Tool
      description: 'Invoke advanced ADK tools (load_artifacts, openapi_tool, vertex_ai_search_tool,
        etc.).

        Executes agent tools with proper authentication and context.'
      operationId: invoke_adk_tool_api_v1_adk_api_v1_adk_tools_invoke_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ToolInvocationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/adk/api/v1/adk/network/status:
    get:
      tags:
      - ADK Agents
      - ADK Agents
      summary: Get Agent Network Status
      description: 'Get real-time agent network status.

        Returns overall health and availability of the agent network.'
      operationId: get_agent_network_status_api_v1_adk_api_v1_adk_network_status_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NetworkStatus'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/adk/api/v1/adk/sessions/{session_id}/end:
    post:
      tags:
      - ADK Agents
      - ADK Agents
      summary: End Agent Session
      description: 'End an active agent session.

        Cleans up session data and updates agent availability.'
      operationId: end_agent_session_api_v1_adk_api_v1_adk_sessions__session_id__end_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: session_id
        in: path
        required: true
        schema:
          type: string
          title: Session Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/adk/api/v1/adk/agents/{agent_id}/start:
    post:
      tags:
      - ADK Agents
      - ADK Agents
      summary: Start Agent
      description: 'Start an agent and make it available for processing.


        This endpoint activates an agent that was previously offline or in error state.'
      operationId: start_agent_api_v1_adk_api_v1_adk_agents__agent_id__start_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: agent_id
        in: path
        required: true
        schema:
          type: string
          title: Agent Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/adk/api/v1/adk/health:
    get:
      tags:
      - ADK Agents
      - ADK Agents
      summary: Health Check
      description: Health check endpoint for ADK agent service.
      operationId: health_check_api_v1_adk_api_v1_adk_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/files/export-transactions:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Export Transactions To Excel
      description: 'Export transactions with comprehensive analysis for different
        scenarios.


        Query Parameters:

        - format: "excel", "csv", or "pdf" (default: "excel")

        - export_type: "m1_verification", "simple", or "summary" (default: "m1_verification")


        Returns professionally formatted export with business appropriateness analysis.'
      operationId: export_transactions_to_excel_api_v1_files_export_transactions_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: format
        in: query
        required: false
        schema:
          type: string
          default: excel
          title: Format
      - name: export_type
        in: query
        required: false
        schema:
          type: string
          default: m1_verification
          title: Export Type
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get Uploads
      description: 'Retrieves uploads for the current tenant.

        OPTIMIZED: Added caching to reduce database latency impact.'
      operationId: get_uploads_api_v1_files_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UploadResponse'
                type: array
                title: Response Get Uploads Api V1 Files Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/files/processing-status:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get Processing Status
      description: 'Get the processing status of all uploads for the current tenant.

        Returns a summary of uploads and their current processing state.

        OPTIMIZED: Single query with LEFT JOIN to avoid N+1 performance issue.'
      operationId: get_processing_status_api_v1_files_processing_status_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
      security:
      - OAuth2PasswordBearer: []
  /api/v1/files/{upload_id}:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get Upload
      description: Retrieves a specific upload by ID.
      operationId: get_upload_api_v1_files__upload_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/upload:
    post:
      tags:
      - Files
      - Files
      - Upload
      summary: Upload Files
      description: 'Upload multiple transaction files (up to 10) for processing.

        Supports Excel (.xlsx, .xls) and CSV files.

        All files are processed in parallel for better performance.


        Note: This is a general-purpose endpoint. For clarity:

        - Use /api/v1/onboarding/upload-historical-data for onboarding WITH categories

        - Use /upload-production-data for production WITHOUT categories'
      operationId: upload_files_api_v1_files_upload_post
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_upload_files_api_v1_files_upload_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MultipleUploadResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/files/{upload_id}/schema:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get File Schema
      description: 'Get the AI-interpreted schema for an uploaded file.


        Returns the schema interpretation results including:

        - Column mappings with confidence scores

        - Required field mapping status

        - Overall interpretation confidence

        - Human-readable summary

        - Debit/credit column inference (if applicable)'
      operationId: get_file_schema_api_v1_files__upload_id__schema_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchemaInterpretationResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/upload-production-data:
    post:
      tags:
      - Files
      - Files
      - Upload
      summary: Upload Production Files
      description: 'Upload production transaction files for AI categorization.


        IMPORTANT: This endpoint is for NEW transactions WITHOUT category labels.

        The AI will categorize these transactions based on patterns learned during
        onboarding.


        Validation:

        - Files must NOT contain category columns

        - Tenant must have completed onboarding

        - RAG corpus must be available


        For historical data WITH categories, use /api/v1/onboarding/upload-historical-data'
      operationId: upload_production_files_api_v1_files_upload_production_data_post
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_upload_production_files_api_v1_files_upload_production_data_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MultipleUploadResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/files/{upload_id}/columns:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get Upload Columns
      description: 'Current domain column extraction using stored upload data.

        Enhanced existing implementation instead of using archived agents.'
      operationId: get_upload_columns_api_v1_files__upload_id__columns_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ColumnListResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/{upload_id}/schema-interpretation:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get Schema Interpretation
      description: 'Get intelligent schema interpretation for an uploaded file.

        Returns detailed column mappings with confidence scores and reasoning.

        OPTIMIZED: Cached results to avoid re-processing same file.'
      operationId: get_schema_interpretation_api_v1_files__upload_id__schema_interpretation_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchemaInterpretationResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/{upload_id}/map:
    post:
      tags:
      - Files
      - Files
      - Upload
      summary: Process Mapped File
      description: 'Current domain column mapping and transaction creation.

        Enhanced to use AI interpretation if mapping not provided.

        Also updates onboarding status to CATEGORIES_PENDING after successful processing.'
      operationId: process_mapped_file_api_v1_files__upload_id__map_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ColumnMappingPayload'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessedFileResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/{upload_id}/transactions:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get Transactions For Upload
      description: Retrieves transactions associated with a specific upload ID, ensuring
        tenant isolation.
      operationId: get_transactions_for_upload_api_v1_files__upload_id__transactions_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionListResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/{upload_id}/interpretation:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get Interpretation Results
      description: "Retrieve stored interpretation results for an uploaded file.\n\
        \nReturns the complete interpretation analysis including column mappings,\n\
        categorization analysis, and confidence scores from the AI interpretation\
        \ process.\n\nArgs:\n    upload_id: Unique identifier for the file upload\n\
        \    conn: Database session\n    current_user: Authenticated user information\n\
        \    tenant_id_int: Current tenant ID for data isolation\n\nReturns:\n   \
        \ Complete interpretation results with storage metadata\n\nRaises:\n    404:\
        \ If interpretation results not found for the upload\n    403: If user doesn't\
        \ have access to the interpretation results"
      operationId: get_interpretation_results_api_v1_files__upload_id__interpretation_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InterpretationStorageResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/{upload_id}/interpretation/confirm:
    post:
      tags:
      - Files
      - Files
      - Upload
      summary: Confirm Interpretation And Reprocess
      description: 'Receives confirmed or corrected column mappings and other interpretation
        details

        for an uploaded file. This input is then used by the

        IntelligentDataInterpretationService to re-process or finalize the data transformation.'
      operationId: confirm_interpretation_and_reprocess_api_v1_files__upload_id__interpretation_confirm_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfirmInterpretationRequest'
      responses:
        '202':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmInterpretationResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/{upload_id}/status:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get Upload Status
      description: 'Get the processing status for a specific upload.


        Returns detailed status information including:

        - Upload status (PENDING, PROCESSING, COMPLETED, FAILED)

        - Processing progress percentage

        - Number of transactions processed

        - Schema interpretation status

        - Categorization progress

        - Error messages if any'
      operationId: get_upload_status_api_v1_files__upload_id__status_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/{upload_id}/processing-details:
    get:
      tags:
      - Files
      - Files
      - Upload
      summary: Get Processing Details
      description: 'Get detailed processing information for a specific upload.


        This endpoint provides comprehensive processing details including:

        - Upload status and progress

        - Schema interpretation results

        - Categorization progress and results

        - Processing stages and timeline

        - Error details and resolution steps'
      operationId: get_processing_details_api_v1_files__upload_id__processing_details_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/enhanced/upload/{upload_id}/start-enhanced-processing:
    post:
      tags:
      - Enhanced Files
      - Enhanced Files
      summary: Start Enhanced Processing
      description: Start enhanced processing for an uploaded file
      operationId: start_enhanced_processing_api_v1_files_enhanced_upload__upload_id__start_enhanced_processing_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/enhanced/upload/{upload_id}/status:
    get:
      tags:
      - Enhanced Files
      - Enhanced Files
      summary: Get Enhanced Status
      description: Get enhanced processing status
      operationId: get_enhanced_status_api_v1_files_enhanced_upload__upload_id__status_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/enhanced/upload/{upload_id}/results:
    get:
      tags:
      - Enhanced Files
      - Enhanced Files
      summary: Get Processing Results
      description: Get comprehensive processing results
      operationId: get_processing_results_api_v1_files_enhanced_upload__upload_id__results_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/enhanced/upload/{upload_id}/export/{format}:
    get:
      tags:
      - Enhanced Files
      - Enhanced Files
      summary: Export Results
      description: Export processing results in various formats
      operationId: export_results_api_v1_files_enhanced_upload__upload_id__export__format__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      - name: format
        in: path
        required: true
        schema:
          type: string
          title: Format
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/enhanced/upload/{upload_id}/feedback:
    post:
      tags:
      - Enhanced Files
      - Enhanced Files
      summary: Submit Processing Feedback
      description: Submit feedback about processing results for continuous improvement
      operationId: submit_processing_feedback_api_v1_files_enhanced_upload__upload_id__feedback_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              additionalProperties: true
              title: Feedback Data
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/files/enhanced/upload/{upload_id}/cleanup:
    delete:
      tags:
      - Enhanced Files
      - Enhanced Files
      summary: Cleanup Processing Data
      description: Clean up processing data after user is done
      operationId: cleanup_processing_data_api_v1_files_enhanced_upload__upload_id__cleanup_delete
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/spending-by-category:
    get:
      tags:
      - Reports
      - Reports
      summary: Get Spending By Category
      description: 'Retrieve aggregated spending by category for the current tenant.

        Filters by date range if provided.

        OPTIMIZED: Added caching to reduce query latency.'
      operationId: get_spending_by_category_api_v1_reports_spending_by_category_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Start Date
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: End Date
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpendingByCategoryResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/spending-by-entity:
    get:
      tags:
      - Reports
      - Reports
      summary: Get Spending By Entity
      description: 'Retrieve aggregated spending by entity/vendor for the current
        tenant.

        Entities are extracted from transaction descriptions using AI and stored in
        the Entity table.

        Filters by date range if provided.'
      operationId: get_spending_by_entity_api_v1_reports_spending_by_entity_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Start Date
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: End Date
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntitySpendingResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/income-expense-summary:
    get:
      tags:
      - Reports
      - Reports
      summary: Get Income Expense Summary
      description: 'Retrieve a summary of total income, total expenses, and net income/loss

        for the current tenant over a specified period.'
      operationId: get_income_expense_summary_api_v1_reports_income_expense_summary_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Start Date
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: End Date
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IncomeExpenseSummaryResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/summary:
    get:
      tags:
      - Reports
      - Reports
      summary: Get Financial Summary
      description: 'Retrieve a comprehensive financial summary for the current tenant.

        Returns total income, expenses, net income, and transaction count.'
      operationId: get_financial_summary_api_v1_reports_summary_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Start date (YYYY-MM-DD)
          title: Start Date
        description: Start date (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: End date (YYYY-MM-DD)
          title: End Date
        description: End date (YYYY-MM-DD)
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/monthly-trends:
    get:
      tags:
      - Reports
      - Reports
      summary: Get Monthly Trends
      description: 'Retrieve monthly income and expense trends for the current tenant.

        Shows monthly breakdown of income, expenses, and net amount.'
      operationId: get_monthly_trends_api_v1_reports_monthly_trends_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Start Date
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: End Date
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MonthlyTrendsResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/custom/generate:
    post:
      tags:
      - Reports
      - Reports
      summary: Generate Custom Report
      description: 'Generate a custom report based on the provided configuration.

        Supports various report types with flexible filtering and grouping options.'
      operationId: generate_custom_report_api_v1_reports_custom_generate_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomReportGenerateRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomReportDataResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/reports/custom/save:
    post:
      tags:
      - Reports
      - Reports
      summary: Save Custom Report
      description: 'Save a custom report configuration for future use.

        The report configuration can be retrieved and used to generate reports later.'
      operationId: save_custom_report_api_v1_reports_custom_save_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomReportSaveRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomReportResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/reports/custom/list:
    get:
      tags:
      - Reports
      - Reports
      summary: List Custom Reports
      description: 'List all saved custom reports for the current tenant.

        Optionally filter to show only reports created by the current user.'
      operationId: list_custom_reports_api_v1_reports_custom_list_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: user_only
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: User Only
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomReportListResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/custom/{report_id}:
    get:
      tags:
      - Reports
      - Reports
      summary: Get Custom Report
      description: Get a specific saved custom report configuration by ID.
      operationId: get_custom_report_api_v1_reports_custom__report_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: report_id
        in: path
        required: true
        schema:
          type: integer
          title: Report Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomReportResponse'
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    delete:
      tags:
      - Reports
      - Reports
      summary: Delete Custom Report
      description: 'Delete a custom report configuration.


        Only the report creator or an admin can delete a report.'
      operationId: delete_custom_report_api_v1_reports_custom__report_id__delete
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: report_id
        in: path
        required: true
        schema:
          type: integer
          title: Report Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/export/excel:
    post:
      tags:
      - Reports
      - Reports
      summary: Export To Excel
      description: 'Export financial data to professional Excel format with advanced
        formatting.


        Features:

        - Professional Excel styling matching frontend design

        - Multiple worksheets (Transactions, Summary, Category Analysis, Monthly Trends)

        - Charts and visualizations

        - Conditional formatting

        - Auto-fitted columns and frozen headers

        - Enterprise-grade presentation quality'
      operationId: export_to_excel_api_v1_reports_export_excel_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          description: Start date for export (YYYY-MM-DD)
          title: Start Date
        description: Start date for export (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          description: End date for export (YYYY-MM-DD)
          title: End Date
        description: End date for export (YYYY-MM-DD)
      - name: include_charts
        in: query
        required: false
        schema:
          type: boolean
          description: Include charts in export
          default: true
          title: Include Charts
        description: Include charts in export
      - name: include_summary
        in: query
        required: false
        schema:
          type: boolean
          description: Include summary sheet
          default: true
          title: Include Summary
        description: Include summary sheet
      - name: professional_styling
        in: query
        required: false
        schema:
          type: boolean
          description: Apply professional Excel styling
          default: true
          title: Professional Styling
        description: Apply professional Excel styling
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/export/pdf:
    post:
      tags:
      - Reports
      - Reports
      summary: Export To Pdf
      description: 'Export financial data to professional PDF format.


        Features:

        - Professional PDF styling and formatting

        - Charts and visualizations

        - Executive summary

        - Transaction details with proper formatting

        - Enterprise-grade presentation quality'
      operationId: export_to_pdf_api_v1_reports_export_pdf_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          description: Start date for export (YYYY-MM-DD)
          title: Start Date
        description: Start date for export (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          description: End date for export (YYYY-MM-DD)
          title: End Date
        description: End date for export (YYYY-MM-DD)
      - name: include_charts
        in: query
        required: false
        schema:
          type: boolean
          description: Include charts in export
          default: true
          title: Include Charts
        description: Include charts in export
      - name: include_summary
        in: query
        required: false
        schema:
          type: boolean
          description: Include summary page
          default: true
          title: Include Summary
        description: Include summary page
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/export/csv:
    post:
      tags:
      - Reports
      - Reports
      summary: Export To Csv
      description: 'Export financial data to CSV format with multiple format options.


        Features:

        - Multiple export types (transactions, categories, monthly summaries)

        - Support for different accounting system formats (QuickBooks, Sage, Xero)

        - Flexible metadata inclusion

        - Professional CSV formatting for integration'
      operationId: export_to_csv_api_v1_reports_export_csv_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          description: Start date for export (YYYY-MM-DD)
          title: Start Date
        description: Start date for export (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          description: End date for export (YYYY-MM-DD)
          title: End Date
        description: End date for export (YYYY-MM-DD)
      - name: format_style
        in: query
        required: false
        schema:
          type: string
          description: 'CSV format style: standard, quickbooks, sage, xero'
          default: standard
          title: Format Style
        description: 'CSV format style: standard, quickbooks, sage, xero'
      - name: include_metadata
        in: query
        required: false
        schema:
          type: boolean
          description: Include metadata columns (only for standard format)
          default: true
          title: Include Metadata
        description: Include metadata columns (only for standard format)
      - name: export_type
        in: query
        required: false
        schema:
          type: string
          description: 'Export type: transactions, categories, monthly'
          default: transactions
          title: Export Type
        description: 'Export type: transactions, categories, monthly'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/reports/category-breakdown:
    get:
      tags:
      - Reports
      - Reports
      summary: Get Category Breakdown
      description: 'Get spending/income breakdown by category.


        Returns top categories by amount with percentage breakdowns.'
      operationId: get_category_breakdown_api_v1_reports_category_breakdown_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: type
        in: query
        required: false
        schema:
          type: string
          description: 'Type of breakdown: ''expense'' or ''income'''
          default: expense
          title: Type
        description: 'Type of breakdown: ''expense'' or ''income'''
      - name: start_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Start date (YYYY-MM-DD)
          title: Start Date
        description: Start date (YYYY-MM-DD)
      - name: end_date
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: End date (YYYY-MM-DD)
          title: End Date
        description: End date (YYYY-MM-DD)
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Get Category Breakdown Api V1 Reports Category Breakdown
                  Get
        '404':
          description: Not found
        '401':
          description: Not authenticated
        '403':
          description: Not authorized
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/exports/formats:
    get:
      tags:
      - Exports
      - exports
      summary: Get available export formats
      description: Returns a list of all supported accounting software export formats.
      operationId: get_export_formats_api_v1_exports_formats_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ExportFormatInfo'
                type: array
                title: Response Get Export Formats Api V1 Exports Formats Get
      security:
      - OAuth2PasswordBearer: []
  /api/v1/exports/readiness/{format_id}:
    get:
      tags:
      - Exports
      - exports
      summary: Check export readiness
      description: Checks if transactions are ready for export in the specified format.
      operationId: check_export_readiness_api_v1_exports_readiness__format_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: format_id
        in: path
        required: true
        schema:
          type: string
          title: Format Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExportReadinessResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/exports/download:
    post:
      tags:
      - Exports
      - exports
      summary: Export transactions
      description: Export categorized transactions in the specified accounting software
        format.
      operationId: export_transactions_api_v1_exports_download_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExportRequest'
        required: true
      responses:
        '200':
          description: File download
          content:
            application/json:
              schema: {}
            text/csv: {}
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet: {}
            text/plain: {}
            application/xml: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/exports/quickbooks:
    post:
      tags:
      - Exports
      - exports
      summary: Export to QuickBooks
      description: Shortcut endpoint for QuickBooks export (auto-detects Desktop vs
        Online).
      operationId: export_to_quickbooks_api_v1_exports_quickbooks_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: date_from
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          description: Start date
          title: Date From
        description: Start date
      - name: date_to
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          description: End date
          title: Date To
        description: End date
      - name: format_type
        in: query
        required: false
        schema:
          type: string
          description: '''desktop'' or ''online'''
          default: online
          title: Format Type
        description: '''desktop'' or ''online'''
      responses:
        '200':
          description: QuickBooks file download
          content:
            application/json:
              schema: {}
            text/csv: {}
            text/plain: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/exports/zoho-books:
    post:
      tags:
      - Exports
      - exports
      summary: Export to Zoho Books
      description: Shortcut endpoint for Zoho Books CSV export.
      operationId: export_to_zoho_books_api_v1_exports_zoho_books_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: date_from
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          description: Start date
          title: Date From
        description: Start date
      - name: date_to
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          description: End date
          title: Date To
        description: End date
      responses:
        '200':
          description: Zoho Books CSV file
          content:
            application/json:
              schema: {}
            text/csv: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/exports/tally-prime:
    post:
      tags:
      - Exports
      - exports
      summary: Export to Tally Prime
      description: Shortcut endpoint for Tally Prime XML export.
      operationId: export_to_tally_prime_api_v1_exports_tally_prime_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: date_from
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          description: Start date
          title: Date From
        description: Start date
      - name: date_to
        in: query
        required: false
        schema:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          description: End date
          title: Date To
        description: End date
      responses:
        '200':
          description: Tally Prime XML file
          content:
            application/json:
              schema: {}
            application/xml: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/system/performance/summary:
    get:
      tags:
      - System Monitoring
      - Performance Monitoring
      summary: Get Performance Summary
      description: 'Get performance summary for the specified time period.


        Requires: Authenticated user'
      operationId: get_performance_summary_api_v1_system_performance_summary_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: minutes
        in: query
        required: false
        schema:
          type: integer
          maximum: 60
          minimum: 1
          description: Time period in minutes
          default: 5
          title: Minutes
        description: Time period in minutes
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceSummaryResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/system/performance/status:
    get:
      tags:
      - System Monitoring
      - Performance Monitoring
      summary: Get System Status
      description: 'Get current system status and real-time metrics.


        Requires: Authenticated user'
      operationId: get_system_status_api_v1_system_performance_status_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemStatusResponse'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/system/performance/health:
    get:
      tags:
      - System Monitoring
      - Performance Monitoring
      summary: Get Health Check
      description: 'Public health check endpoint for load balancer and monitoring.


        No authentication required.'
      operationId: get_health_check_api_v1_system_performance_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Get Health Check Api V1 System Performance Health
                  Get
  /api/v1/system/performance/endpoints:
    get:
      tags:
      - System Monitoring
      - Performance Monitoring
      summary: Get Endpoint Performance
      description: 'Get performance statistics for all tracked endpoints.


        Requires: Authenticated user'
      operationId: get_endpoint_performance_api_v1_system_performance_endpoints_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          maximum: 100
          minimum: 1
          description: Number of endpoints to return
          default: 20
          title: Limit
        description: Number of endpoints to return
      - name: sort_by
        in: query
        required: false
        schema:
          type: string
          pattern: ^(requests|avg_time|errors)$
          description: Sort criteria
          default: requests
          title: Sort By
        description: Sort criteria
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Get Endpoint Performance Api V1 System Performance
                  Endpoints Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/system/performance/reset:
    post:
      tags:
      - System Monitoring
      - Performance Monitoring
      summary: Reset Performance Metrics
      description: 'Reset all performance metrics (admin only, use with caution).


        Requires: Superuser privileges'
      operationId: reset_performance_metrics_api_v1_system_performance_reset_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: confirm
        in: query
        required: false
        schema:
          type: boolean
          description: Must be true to confirm reset
          default: false
          title: Confirm
        description: Must be true to confirm reset
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: string
                title: Response Reset Performance Metrics Api V1 System Performance
                  Reset Post
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/system/performance/alerts:
    get:
      tags:
      - System Monitoring
      - Performance Monitoring
      summary: Get Performance Alerts
      description: 'Get current performance alerts and warnings.


        Requires: Authenticated user'
      operationId: get_performance_alerts_api_v1_system_performance_alerts_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Get Performance Alerts Api V1 System Performance Alerts
                  Get
      security:
      - OAuth2PasswordBearer: []
  /api/v1/monitoring/performance/metrics:
    get:
      tags:
      - Performance Monitoring
      - Monitoring
      summary: Get Performance Metrics
      description: "Get performance metrics for monitored operations.\n\nArgs:\n \
        \   operation_filter: Optional filter for specific operation types\n\nReturns:\n\
        \    Performance metrics summary"
      operationId: get_performance_metrics_api_v1_monitoring_performance_metrics_get
      parameters:
      - name: operation_filter
        in: query
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          title: Operation Filter
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceMetricsResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/monitoring/performance/clear:
    post:
      tags:
      - Performance Monitoring
      - Monitoring
      summary: Clear Performance Metrics
      description: Clear all recorded performance metrics.
      operationId: clear_performance_metrics_api_v1_monitoring_performance_clear_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/monitoring/performance/health:
    get:
      tags:
      - Performance Monitoring
      - Monitoring
      summary: Get Performance Health
      description: 'Get performance health status based on recent metrics.


        Returns health indicators for each operation type.'
      operationId: get_performance_health_api_v1_monitoring_performance_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/admin/health:
    get:
      tags:
      - Admin
      - Admin
      summary: Get System Health
      description: Get system health status and metrics.
      operationId: get_system_health_api_v1_admin_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemHealthResponse'
      security:
      - APIKeyHeader: []
  /api/v1/admin/tenants:
    post:
      tags:
      - Admin
      - Admin
      summary: Create Tenant
      description: Create a new tenant.
      operationId: create_tenant_api_v1_admin_tenants_post
      security:
      - OAuth2PasswordBearer: []
      - APIKeyHeader: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTenantRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    get:
      tags:
      - Admin
      - Admin
      summary: Get Tenants
      description: Get list of tenants with pagination.
      operationId: get_tenants_api_v1_admin_tenants_get
      security:
      - APIKeyHeader: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          default: 100
          title: Limit
      - name: offset
        in: query
        required: false
        schema:
          type: integer
          default: 0
          title: Offset
      - name: active_only
        in: query
        required: false
        schema:
          type: boolean
          default: false
          title: Active Only
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantsListResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/admin/tenants/{tenant_id}:
    put:
      tags:
      - Admin
      - Admin
      summary: Update Tenant
      description: Update tenant information.
      operationId: update_tenant_api_v1_admin_tenants__tenant_id__put
      security:
      - OAuth2PasswordBearer: []
      - APIKeyHeader: []
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTenantRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/admin/tenants/{tenant_id}/users:
    post:
      tags:
      - Admin
      - Admin
      summary: Create User
      description: Create a new user for a tenant.
      operationId: create_user_api_v1_admin_tenants__tenant_id__users_post
      security:
      - OAuth2PasswordBearer: []
      - APIKeyHeader: []
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/admin/users/{user_id}:
    get:
      tags:
      - Admin
      - Admin
      summary: Get User
      description: Get user information by ID.
      operationId: get_user_api_v1_admin_users__user_id__get
      security:
      - APIKeyHeader: []
      parameters:
      - name: user_id
        in: path
        required: true
        schema:
          type: integer
          title: User Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/admin/users/{user_id}/deactivate:
    post:
      tags:
      - Admin
      - Admin
      summary: Deactivate User
      description: Deactivate a user account.
      operationId: deactivate_user_api_v1_admin_users__user_id__deactivate_post
      security:
      - OAuth2PasswordBearer: []
      - APIKeyHeader: []
      parameters:
      - name: user_id
        in: path
        required: true
        schema:
          type: integer
          title: User Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/admin/database/migrate:
    post:
      tags:
      - Admin
      - Admin
      summary: Run Database Migration
      description: Run database migrations.
      operationId: run_database_migration_api_v1_admin_database_migrate_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MigrationResponse'
      security:
      - APIKeyHeader: []
  /api/v1/admin/status:
    get:
      tags:
      - Admin
      - Admin
      summary: Admin Status
      description: Simple admin status check (no auth required).
      operationId: admin_status_api_v1_admin_status_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/onboarding/status:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Onboarding Status
      description: Get current onboarding status for the tenant.
      operationId: get_onboarding_status_api_v1_onboarding_status_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingStatus'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/{onboarding_id}/progress:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Onboarding Progress
      description: Get onboarding progress by onboarding ID.
      operationId: get_onboarding_progress_api_v1_onboarding__onboarding_id__progress_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: onboarding_id
        in: path
        required: true
        schema:
          type: string
          title: Onboarding Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingProgressResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/start:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Start Onboarding
      description: Start the onboarding process for a new tenant.
      operationId: start_onboarding_api_v1_onboarding_start_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnboardingStartRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingStatus'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/upload-historical-data:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Upload Historical Data
      description: 'Upload historical transaction data with existing category labels.


        This is the critical first step in onboarding where customers upload

        their full year of labeled transaction data.


        The file is uploaded and processing starts in the background to prevent

        timeouts for large files.'
      operationId: upload_historical_data_api_v1_onboarding_upload_historical_data_post
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_upload_historical_data_api_v1_onboarding_upload_historical_data_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/upload-status/{upload_id}:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Upload Status
      description: Check the status of a file upload and processing operation.
      operationId: get_upload_status_api_v1_onboarding_upload_status__upload_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Get Upload Status Api V1 Onboarding Upload Status  Upload
                  Id  Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/batch-upload-files:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Batch Upload Files
      description: 'Batch upload multiple financial data files with enhanced reporting.


        This endpoint accepts multiple file uploads and processes each one:

        - Supports Excel (.xlsx) and CSV files

        - Intelligent column mapping and categorization

        - Stores transactions with original category labels

        - Provides detailed processing reports for each file


        Returns summary of upload results, transaction counts, and report IDs

        for retrieving detailed processing information including row-by-row

        results, column statistics, and data quality metrics.'
      operationId: batch_upload_files_api_v1_onboarding_batch_upload_files_post
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_batch_upload_files_api_v1_onboarding_batch_upload_files_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Batch Upload Files Api V1 Onboarding Batch Upload
                  Files Post
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/interpret-columns:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Interpret File Columns
      description: Use AI to interpret column mappings from an uploaded file.
      operationId: interpret_file_columns_api_v1_onboarding_interpret_columns_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: file_path
        in: query
        required: true
        schema:
          type: string
          title: File Path
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileColumnMapping'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/build-rag-corpus:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Build Rag Corpus
      description: 'Build RAG corpus from the tenant''s labeled transaction data.


        This creates a knowledge base from the customer''s historical categorization

        patterns that will be used for AI predictions.


        Now runs synchronously to provide immediate feedback to users.'
      operationId: build_rag_corpus_api_v1_onboarding_build_rag_corpus_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Build Rag Corpus Api V1 Onboarding Build Rag Corpus
                  Post
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/validate-temporal-accuracy:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Validate Temporal Accuracy
      description: 'Run temporal accuracy validation for July-December 2024.


        This simulates month-by-month accuracy testing where:

        - Each month uses all previous months as training data

        - Tests accuracy against the customer''s original labels

        - Must achieve >85% accuracy for production approval


        Now runs synchronously to provide immediate results.'
      operationId: validate_temporal_accuracy_api_v1_onboarding_validate_temporal_accuracy_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TemporalValidationRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemporalValidationResult'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/validation-results/{validation_id}:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Validation Results
      description: Get results of a specific validation run.
      operationId: get_validation_results_api_v1_onboarding_validation_results__validation_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: validation_id
        in: path
        required: true
        schema:
          type: string
          title: Validation Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemporalValidationResult'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/validation-progress/{validation_id}:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Validation Progress
      description: 'Get real-time progress for temporal validation with progressive
        RAG corpus building.


        Returns detailed progress including:

        - Current phase (corpus_building, testing, completed)

        - Per-month corpus building progress

        - Overall validation progress

        - Estimated completion time'
      operationId: get_validation_progress_api_v1_onboarding_validation_progress__validation_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: validation_id
        in: path
        required: true
        schema:
          type: string
          title: Validation Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemporalValidationProgress'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/approve-for-production:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Approve For Production
      description: 'Approve tenant for production usage after successful validation.


        This is the final step that enables the tenant to start using

        the AI categorization on new transactions.'
      operationId: approve_for_production_api_v1_onboarding_approve_for_production_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnboardingApprovalRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingStatus'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/sample-data:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Sample Data
      description: Get sample data files for testing the onboarding process.
      operationId: get_sample_data_api_v1_onboarding_sample_data_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/diagnose-schema/{tenant_id}:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Diagnose Schema Mappings
      description: 'Diagnose schema mapping issues for accuracy debugging.


        This endpoint helps debug why accuracy measurements may be failing

        by providing detailed information about schema mappings and category comparisons.'
      operationId: diagnose_schema_mappings_api_v1_onboarding_diagnose_schema__tenant_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/upload-with-reporting:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Upload File With Detailed Reporting
      description: 'Upload a file with enhanced processing that provides detailed
        reporting.


        This endpoint uses the new enhanced processing method that tracks:

        - Row-by-row processing results

        - Column statistics and data quality

        - Schema discovery integration

        - Detailed error and warning messages


        Returns the processing report ID that can be used to retrieve detailed reports.'
      operationId: upload_file_with_detailed_reporting_api_v1_onboarding_upload_with_reporting_post
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_upload_file_with_detailed_reporting_api_v1_onboarding_upload_with_reporting_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/file-processing-report/{upload_id}:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get File Processing Report
      description: 'Get detailed processing report for a specific file upload.


        This provides comprehensive information about how the file was processed,

        including success rates, data quality scores, and discovered categories.'
      operationId: get_file_processing_report_api_v1_onboarding_file_processing_report__upload_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileProcessingReportResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/column-statistics/{upload_id}:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Column Statistics
      description: 'Get detailed column statistics for a processed file.


        This provides information about each column including:

        - Data type detection and consistency

        - Null value percentages

        - Value distributions

        - Mapping confidence scores'
      operationId: get_column_statistics_api_v1_onboarding_column_statistics__upload_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ColumnStatisticResponse'
                title: Response Get Column Statistics Api V1 Onboarding Column Statistics  Upload
                  Id  Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/row-details/{upload_id}:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Row Processing Details
      description: 'Get paginated row-by-row processing details.


        This allows drilling down into individual row processing results,

        including parsing errors, validation issues, and data quality problems.'
      operationId: get_row_processing_details_api_v1_onboarding_row_details__upload_id__post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: upload_id
        in: path
        required: true
        schema:
          type: string
          title: Upload Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RowDetailsRequest'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RowDetailsResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/schema-mapping-report/{tenant_id}:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Schema Mapping Report
      description: 'Get comprehensive schema mapping report showing discovered categories
        and mappings.


        This report shows:

        - All categories discovered from uploaded files

        - Unified category mappings

        - Bidirectional mapping relationships

        - Mapping confidence and unmapped categories'
      operationId: get_schema_mapping_report_api_v1_onboarding_schema_mapping_report__tenant_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchemaMappingReportResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/recent-processing-reports:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Recent Processing Reports
      description: 'Get recent file processing reports for the current tenant.


        Returns a summary of recent file uploads with key metrics.'
      operationId: get_recent_processing_reports_api_v1_onboarding_recent_processing_reports_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          default: 10
          title: Limit
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProcessingReportSummary'
                title: Response Get Recent Processing Reports Api V1 Onboarding Recent
                  Processing Reports Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/start-zero:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Start Zero Onboarding
      description: Start M1 zero-onboarding process - immediate production ready.
      operationId: start_zero_onboarding_api_v1_onboarding_start_zero_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ZeroOnboardingStartRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingStatus'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/start-schema-only:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Start Schema Only Onboarding
      description: Start M3 schema-only onboarding process.
      operationId: start_schema_only_onboarding_api_v1_onboarding_start_schema_only_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SchemaOnlyStartRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingStatus'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/import-schema:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Import Category Schema
      description: Import category hierarchy for M3 schema-only onboarding.
      operationId: import_category_schema_api_v1_onboarding_import_schema_post
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_import_category_schema_api_v1_onboarding_import_schema_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/business-context:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Business Context
      description: Retrieve business context for a tenant.
      operationId: get_business_context_api_v1_onboarding_business_context_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
      - OAuth2PasswordBearer: []
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Save Business Context
      description: 'Save comprehensive business context for MIS categorization enhancement.


        This endpoint collects detailed business information that enhances:

        - Industry-specific MIS template selection

        - AI categorization accuracy

        - GL code recommendations

        - Financial reporting customization'
      operationId: save_business_context_api_v1_onboarding_business_context_post
      requestBody:
        content:
          application/json:
            schema:
              additionalProperties: true
              type: object
              title: Business Context
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/mis/setup:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Unified Mis Setup
      description: 'Unified MIS setup endpoint.


        Creates a complete Management Information System for any business

        with progressive enhancement based on available data.'
      operationId: unified_mis_setup_api_v1_onboarding_mis_setup_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MISSetupRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MISSetupResponse'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/mis/detect-enhancements:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Detect Enhancements
      description: Detect enhancement opportunities from uploaded file.
      operationId: detect_enhancements_api_v1_onboarding_mis_detect_enhancements_post
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_detect_enhancements_api_v1_onboarding_mis_detect_enhancements_post'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Detect Enhancements Api V1 Onboarding Mis Detect Enhancements
                  Post
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/mis/setup/{setup_id}/complete:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Complete Mis Setup
      description: 'Complete MIS setup and activate the system.


        This finalizes the MIS setup process and marks the tenant as ready

        to start processing transactions.'
      operationId: complete_mis_setup_api_v1_onboarding_mis_setup__setup_id__complete_post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: setup_id
        in: path
        required: true
        schema:
          type: string
          title: Setup Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Complete Mis Setup Api V1 Onboarding Mis Setup  Setup
                  Id  Complete Post
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/mis/setup/{setup_id}:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Mis Setup Status
      description: 'Get the status of an MIS setup.


        Returns the current status, progress, and available enhancement opportunities.'
      operationId: get_mis_setup_status_api_v1_onboarding_mis_setup__setup_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: setup_id
        in: path
        required: true
        schema:
          type: string
          title: Setup Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Get Mis Setup Status Api V1 Onboarding Mis Setup  Setup
                  Id  Get
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/mis/enhance/{setup_id}:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Apply Mis Enhancement
      description: 'Apply an enhancement to an existing MIS setup.


        Enhancements can include:

        - Historical data learning (+15-20% accuracy)

        - Schema/GL code mapping (+20% accuracy)

        - Vendor list integration (+5-10% accuracy)'
      operationId: apply_mis_enhancement_api_v1_onboarding_mis_enhance__setup_id__post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: setup_id
        in: path
        required: true
        schema:
          type: string
          title: Setup Id
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Body_apply_mis_enhancement_api_v1_onboarding_mis_enhance__setup_id__post'
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
                title: Response Apply Mis Enhancement Api V1 Onboarding Mis Enhance  Setup
                  Id  Post
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/onboarding/mis/enhancement-recommendations:
    get:
      tags:
      - Onboarding
      - Onboarding
      summary: Get Enhancement Recommendations
      description: 'Get recommended enhancements for the current MIS setup.


        Analyzes the current state and suggests improvements.'
      operationId: get_enhancement_recommendations_api_v1_onboarding_mis_enhancement_recommendations_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Get Enhancement Recommendations Api V1 Onboarding
                  Mis Enhancement Recommendations Get
      security:
      - OAuth2PasswordBearer: []
  /api/v1/onboarding/mis/detect-enhancements-debug:
    post:
      tags:
      - Onboarding
      - Onboarding
      summary: Detect Enhancements Debug
      description: Debug endpoint to check request format.
      operationId: detect_enhancements_debug_api_v1_onboarding_mis_detect_enhancements_debug_post
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Detect Enhancements Debug Api V1 Onboarding Mis Detect
                  Enhancements Debug Post
  /api/v1/progress/{task_id}:
    get:
      tags:
      - Progress
      - Progress
      summary: Stream Progress
      description: "Stream real-time progress updates for a task via Server-Sent Events.\n\
        \nArgs:\n    task_id: The ID of the task to track\n    current_user: Current\
        \ authenticated user\n\nReturns:\n    EventSourceResponse streaming progress\
        \ updates"
      operationId: stream_progress_api_v1_progress__task_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/status/{task_id}:
    get:
      tags:
      - Progress
      - Progress
      summary: Get Task Status
      description: "Get current status of a task.\n\nArgs:\n    task_id: The ID of\
        \ the task\n    current_user: Current authenticated user\n\nReturns:\n   \
        \ Current task status"
      operationId: get_task_status_api_v1_status__task_id__get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: task_id
        in: path
        required: true
        schema:
          type: string
          title: Task Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/admin/cache/stats:
    get:
      tags:
      - Admin
      - Cache
      - admin
      - cache
      summary: Get Cache Stats
      description: 'Get Redis cache statistics for performance monitoring.


        Returns cache hit rates, memory usage, and connection status.

        Admin users only.'
      operationId: get_cache_stats_api_v1_admin_cache_stats_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Get Cache Stats Api V1 Admin Cache Stats Get
      security:
      - OAuth2PasswordBearer: []
  /api/v1/admin/cache/invalidate/tenant/{tenant_id}:
    post:
      tags:
      - Admin
      - Cache
      - admin
      - cache
      summary: Invalidate Tenant Cache
      description: 'Invalidate all cached data for a specific tenant.


        Useful when:

        - Tenant changes categorization schema (M3 workflow)

        - Need to force re-categorization of patterns

        - Debugging categorization issues


        Admin users only.'
      operationId: invalidate_tenant_cache_api_v1_admin_cache_invalidate_tenant__tenant_id__post
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: tenant_id
        in: path
        required: true
        schema:
          type: integer
          title: Tenant Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/v1/admin/cache/health:
    get:
      tags:
      - Admin
      - Cache
      - admin
      - cache
      summary: Cache Health Check
      description: 'Check Redis cache connectivity and basic functionality.


        Returns connection status and basic performance info.

        Available to all authenticated users.'
      operationId: cache_health_check_api_v1_admin_cache_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Cache Health Check Api V1 Admin Cache Health Get
      security:
      - OAuth2PasswordBearer: []
  /api/v1/admin/cache/performance:
    get:
      tags:
      - Admin
      - Cache
      - admin
      - cache
      summary: Get Cache Performance Metrics
      description: 'Get cache performance metrics for the current tenant.


        Shows categorization performance improvements from caching.

        Useful for monitoring M1/M2/M3 workflow optimization.'
      operationId: get_cache_performance_metrics_api_v1_admin_cache_performance_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                additionalProperties: true
                type: object
                title: Response Get Cache Performance Metrics Api V1 Admin Cache Performance
                  Get
      security:
      - OAuth2PasswordBearer: []
  /api/v1/cache/stats:
    get:
      tags:
      - Cache
      - Cache
      summary: Get Cache Stats
      description: Get cache statistics including Redis and in-memory cache performance.
      operationId: get_cache_stats_api_v1_cache_stats_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/v1/hierarchy/customers/{customer_id}/hierarchy:
    get:
      tags:
      - Hierarchy
      - Hierarchy
      summary: Get Customer Hierarchy
      description: Get hierarchy for a specific customer.
      operationId: get_customer_hierarchy_api_v1_hierarchy_customers__customer_id__hierarchy_get
      security:
      - OAuth2PasswordBearer: []
      parameters:
      - name: customer_id
        in: path
        required: true
        schema:
          type: string
          title: Customer Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerHierarchy'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /:
    get:
      tags:
      - root
      summary: Root
      operationId: root__get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
components:
  schemas:
    ADKAgent:
      properties:
        id:
          type: string
          title: Id
        name:
          type: string
          title: Name
        type:
          type: string
          title: Type
        status:
          type: string
          title: Status
          default: available
        capabilities:
          items:
            type: string
          type: array
          title: Capabilities
        currentTask:
          anyOf:
          - type: string
          - type: 'null'
          title: Currenttask
        lastActivity:
          type: string
          title: Lastactivity
        memorySize:
          type: integer
          title: Memorysize
        toolsAvailable:
          items:
            type: string
          type: array
          title: Toolsavailable
      type: object
      required:
      - id
      - name
      - type
      - capabilities
      - lastActivity
      - memorySize
      - toolsAvailable
      title: ADKAgent
    AIJudgmentListResponse:
      properties:
        judgments:
          items:
            $ref: '#/components/schemas/AIJudgmentResponse'
          type: array
          title: Judgments
        test_id:
          type: integer
          title: Test Id
        total_judgments:
          type: integer
          title: Total Judgments
        summary:
          additionalProperties: true
          type: object
          title: Summary
      type: object
      required:
      - judgments
      - test_id
      - total_judgments
      - summary
      title: AIJudgmentListResponse
      description: Schema for AI judgment list response.
    AIJudgmentResponse:
      properties:
        id:
          type: integer
          title: Id
        test_id:
          type: integer
          title: Test Id
        transaction_id:
          type: string
          title: Transaction Id
        original_category:
          anyOf:
          - type: string
          - type: 'null'
          title: Original Category
        ai_category:
          type: string
          title: Ai Category
        ai_full_hierarchy:
          anyOf:
          - type: string
          - type: 'null'
          title: Ai Full Hierarchy
        ai_confidence:
          type: number
          title: Ai Confidence
        judgment_result:
          $ref: '#/components/schemas/AIJudgmentResult'
        judgment_confidence:
          type: number
          title: Judgment Confidence
        judgment_reasoning:
          anyOf:
          - type: string
          - type: 'null'
          title: Judgment Reasoning
        category_exact_match:
          type: boolean
          title: Category Exact Match
        category_semantic_match:
          type: boolean
          title: Category Semantic Match
        hierarchy_level_matches:
          anyOf:
          - additionalProperties:
              type: boolean
            type: object
          - type: 'null'
          title: Hierarchy Level Matches
        transaction_description:
          type: string
          title: Transaction Description
        transaction_amount:
          type: number
          title: Transaction Amount
        transaction_type:
          type: string
          title: Transaction Type
        judged_at:
          type: string
          format: date-time
          title: Judged At
        judge_model:
          type: string
          title: Judge Model
        judge_version:
          type: string
          title: Judge Version
      type: object
      required:
      - id
      - test_id
      - transaction_id
      - original_category
      - ai_category
      - ai_full_hierarchy
      - ai_confidence
      - judgment_result
      - judgment_confidence
      - judgment_reasoning
      - category_exact_match
      - category_semantic_match
      - hierarchy_level_matches
      - transaction_description
      - transaction_amount
      - transaction_type
      - judged_at
      - judge_model
      - judge_version
      title: AIJudgmentResponse
      description: Schema for AI judgment response.
    AIJudgmentResult:
      type: string
      enum:
      - correct
      - incorrect
      - partially_correct
      - indeterminate
      title: AIJudgmentResult
      description: AI judge evaluation of categorization correctness.
    AIRequest:
      properties:
        query:
          type: string
          title: Query
        context:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Context
        options:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Options
      type: object
      required:
      - query
      title: AIRequest
      description: General AI request model.
    AIResponse:
      properties:
        success:
          type: boolean
          title: Success
        result:
          additionalProperties: true
          type: object
          title: Result
        processing_time_ms:
          type: number
          title: Processing Time Ms
        operation_type:
          type: string
          title: Operation Type
        cached:
          type: boolean
          title: Cached
          default: false
      type: object
      required:
      - success
      - result
      - processing_time_ms
      - operation_type
      title: AIResponse
      description: General AI response model.
    AccountingSystemDetectionRequest:
      properties:
        columns:
          items:
            type: string
          type: array
          title: Columns
          description: Column names from the transaction file
        sample_data:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Sample Data
          description: Sample transaction data
        filename:
          anyOf:
          - type: string
          - type: 'null'
          title: Filename
          description: Original filename for context
      type: object
      required:
      - columns
      title: AccountingSystemDetectionRequest
    AccountingSystemDetectionResponse:
      properties:
        system_type:
          type: string
          title: System Type
          description: Detected accounting system type
        cultural_context:
          type: string
          title: Cultural Context
          description: Cultural context (indian/us/global)
        confidence:
          type: number
          title: Confidence
          description: Detection confidence (0.0-1.0)
        features:
          additionalProperties: true
          type: object
          title: Features
          description: Detected system features
        reasoning:
          type: string
          title: Reasoning
          description: Detection reasoning
        institution_info:
          additionalProperties:
            type: string
          type: object
          title: Institution Info
          description: Institution information
      type: object
      required:
      - system_type
      - cultural_context
      - confidence
      - features
      - reasoning
      - institution_info
      title: AccountingSystemDetectionResponse
    AccuracyMetricListResponse:
      properties:
        metrics:
          items:
            $ref: '#/components/schemas/AccuracyMetricResponse'
          type: array
          title: Metrics
        test_id:
          type: integer
          title: Test Id
        total_metrics:
          type: integer
          title: Total Metrics
      type: object
      required:
      - metrics
      - test_id
      - total_metrics
      title: AccuracyMetricListResponse
      description: Schema for accuracy metrics list response.
    AccuracyMetricResponse:
      properties:
        id:
          type: integer
          title: Id
        test_id:
          type: integer
          title: Test Id
        metric_name:
          type: string
          title: Metric Name
        metric_type:
          type: string
          title: Metric Type
        value:
          type: number
          title: Value
        sample_count:
          type: integer
          title: Sample Count
        true_positives:
          type: integer
          title: True Positives
        false_positives:
          type: integer
          title: False Positives
        true_negatives:
          type: integer
          title: True Negatives
        false_negatives:
          type: integer
          title: False Negatives
        category_filter:
          anyOf:
          - type: string
          - type: 'null'
          title: Category Filter
        confidence_range:
          anyOf:
          - type: string
          - type: 'null'
          title: Confidence Range
        hierarchy_level:
          anyOf:
          - type: integer
          - type: 'null'
          title: Hierarchy Level
        calculated_at:
          type: string
          format: date-time
          title: Calculated At
        calculation_method:
          type: string
          title: Calculation Method
      type: object
      required:
      - id
      - test_id
      - metric_name
      - metric_type
      - value
      - sample_count
      - true_positives
      - false_positives
      - true_negatives
      - false_negatives
      - category_filter
      - confidence_range
      - hierarchy_level
      - calculated_at
      - calculation_method
      title: AccuracyMetricResponse
      description: Schema for accuracy metric response.
    AccuracyReportResponse:
      properties:
        test_id:
          type: integer
          title: Test Id
        test_name:
          type: string
          title: Test Name
        scenario:
          $ref: '#/components/schemas/AccuracyTestScenario'
        overall_metrics:
          additionalProperties:
            type: number
          type: object
          title: Overall Metrics
        category_breakdown:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Category Breakdown
        confidence_breakdown:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Confidence Breakdown
        hierarchy_breakdown:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Hierarchy Breakdown
        ai_judge_summary:
          additionalProperties: true
          type: object
          title: Ai Judge Summary
        judgment_distribution:
          additionalProperties:
            type: integer
          type: object
          title: Judgment Distribution
        quality_metrics:
          additionalProperties: true
          type: object
          title: Quality Metrics
        improvement_suggestions:
          items:
            type: string
          type: array
          title: Improvement Suggestions
        excel_report_url:
          anyOf:
          - type: string
          - type: 'null'
          title: Excel Report Url
        generated_at:
          type: string
          format: date-time
          title: Generated At
      type: object
      required:
      - test_id
      - test_name
      - scenario
      - overall_metrics
      - category_breakdown
      - confidence_breakdown
      - hierarchy_breakdown
      - ai_judge_summary
      - judgment_distribution
      - quality_metrics
      - improvement_suggestions
      - excel_report_url
      - generated_at
      title: AccuracyReportResponse
      description: Schema for comprehensive accuracy report response.
    AccuracyTestCreate:
      properties:
        name:
          type: string
          maxLength: 200
          title: Name
          description: Test name
        description:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Description
          description: Test description
        scenario:
          $ref: '#/components/schemas/AccuracyTestScenario'
          description: Test scenario type
        test_data_source:
          type: string
          maxLength: 500
          title: Test Data Source
          description: Path to test data file or identifier
        category_schema_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Category Schema Id
          description: Category schema ID for schema-only scenario
        sample_size:
          type: integer
          maximum: 10000.0
          minimum: 1.0
          title: Sample Size
          description: Number of transactions to test
          default: 100
      type: object
      required:
      - name
      - scenario
      - test_data_source
      title: AccuracyTestCreate
      description: Schema for creating a new accuracy test.
    AccuracyTestExecutionResponse:
      properties:
        test_id:
          type: integer
          title: Test Id
        status:
          type: string
          title: Status
        message:
          type: string
          title: Message
        results:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Results
        metrics:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Metrics
        summary:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Summary
      type: object
      required:
      - test_id
      - status
      - message
      title: AccuracyTestExecutionResponse
      description: Schema for test execution response.
    AccuracyTestListResponse:
      properties:
        tests:
          items:
            $ref: '#/components/schemas/AccuracyTestSummaryResponse'
          type: array
          title: Tests
        total_count:
          type: integer
          title: Total Count
        page_size:
          type: integer
          title: Page Size
        page_offset:
          type: integer
          title: Page Offset
        has_more:
          type: boolean
          title: Has More
      type: object
      required:
      - tests
      - total_count
      - page_size
      - page_offset
      - has_more
      title: AccuracyTestListResponse
      description: Schema for accuracy test list response.
    AccuracyTestResponse:
      properties:
        id:
          type: integer
          title: Id
        tenant_id:
          type: integer
          title: Tenant Id
        name:
          type: string
          title: Name
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        scenario:
          $ref: '#/components/schemas/AccuracyTestScenario'
        test_data_source:
          type: string
          title: Test Data Source
        category_schema_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Category Schema Id
        sample_size:
          type: integer
          title: Sample Size
        status:
          $ref: '#/components/schemas/AccuracyTestStatus'
        created_at:
          type: string
          format: date-time
          title: Created At
        started_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Started At
        completed_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Completed At
        total_transactions:
          type: integer
          title: Total Transactions
        successful_categorizations:
          type: integer
          title: Successful Categorizations
        ai_judge_correct:
          type: integer
          title: Ai Judge Correct
        ai_judge_incorrect:
          type: integer
          title: Ai Judge Incorrect
        ai_judge_partially_correct:
          type: integer
          title: Ai Judge Partially Correct
        precision:
          anyOf:
          - type: number
          - type: 'null'
          title: Precision
        recall:
          anyOf:
          - type: number
          - type: 'null'
          title: Recall
        f1_score:
          anyOf:
          - type: number
          - type: 'null'
          title: F1 Score
        accuracy_percentage:
          anyOf:
          - type: number
          - type: 'null'
          title: Accuracy Percentage
        error_message:
          anyOf:
          - type: string
          - type: 'null'
          title: Error Message
      type: object
      required:
      - id
      - tenant_id
      - name
      - description
      - scenario
      - test_data_source
      - category_schema_id
      - sample_size
      - status
      - created_at
      - started_at
      - completed_at
      - total_transactions
      - successful_categorizations
      - ai_judge_correct
      - ai_judge_incorrect
      - ai_judge_partially_correct
      - precision
      - recall
      - f1_score
      - accuracy_percentage
      - error_message
      title: AccuracyTestResponse
      description: Schema for accuracy test response.
    AccuracyTestScenario:
      type: string
      enum:
      - historical_data
      - schema_only
      - zero_onboarding
      title: AccuracyTestScenario
      description: Categorization accuracy test scenarios.
    AccuracyTestStatus:
      type: string
      enum:
      - pending
      - running
      - completed
      - failed
      - cancelled
      title: AccuracyTestStatus
      description: Status of accuracy test execution.
    AccuracyTestSummaryResponse:
      properties:
        test_id:
          type: integer
          title: Test Id
        test_name:
          type: string
          title: Test Name
        scenario:
          $ref: '#/components/schemas/AccuracyTestScenario'
        status:
          $ref: '#/components/schemas/AccuracyTestStatus'
        overall_accuracy:
          anyOf:
          - type: number
          - type: 'null'
          title: Overall Accuracy
        precision:
          anyOf:
          - type: number
          - type: 'null'
          title: Precision
        recall:
          anyOf:
          - type: number
          - type: 'null'
          title: Recall
        f1_score:
          anyOf:
          - type: number
          - type: 'null'
          title: F1 Score
        total_transactions:
          type: integer
          title: Total Transactions
        successful_categorizations:
          type: integer
          title: Successful Categorizations
        success_rate:
          type: number
          title: Success Rate
        ai_judge_accuracy:
          anyOf:
          - type: number
          - type: 'null'
          title: Ai Judge Accuracy
        ai_judge_confidence_avg:
          anyOf:
          - type: number
          - type: 'null'
          title: Ai Judge Confidence Avg
        non_generic_categories:
          type: integer
          title: Non Generic Categories
          default: 0
        hierarchical_categories:
          type: integer
          title: Hierarchical Categories
          default: 0
        categories_with_gl_codes:
          type: integer
          title: Categories With Gl Codes
          default: 0
        execution_duration_seconds:
          anyOf:
          - type: number
          - type: 'null'
          title: Execution Duration Seconds
        completed_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Completed At
      type: object
      required:
      - test_id
      - test_name
      - scenario
      - status
      - overall_accuracy
      - precision
      - recall
      - f1_score
      - total_transactions
      - successful_categorizations
      - success_rate
      - ai_judge_accuracy
      - ai_judge_confidence_avg
      - execution_duration_seconds
      - completed_at
      title: AccuracyTestSummaryResponse
      description: Schema for accuracy test summary response.
    AgentApiResponse:
      properties:
        success:
          type: boolean
          title: Success
          description: Whether the operation was successful
        result:
          additionalProperties: true
          type: object
          title: Result
          description: Response data
        message:
          type: string
          title: Message
          description: Human-readable message
        agent_type:
          type: string
          title: Agent Type
          description: Type of agent that processed the request
        cached:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Cached
          description: Whether response was cached
          default: false
      type: object
      required:
      - success
      - result
      - message
      - agent_type
      title: AgentApiResponse
    AgentCommandRequest:
      properties:
        command:
          type: string
          title: Command
          description: Agent command to execute
        parameters:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Parameters
          description: Command parameters
        context:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Context
          description: Additional context
      type: object
      required:
      - command
      title: AgentCommandRequest
      description: Request model for agent command processing.
    AgentCommandResponse:
      properties:
        success:
          type: boolean
          title: Success
          description: Whether command executed successfully
        command:
          type: string
          title: Command
          description: The executed command
        result:
          additionalProperties: true
          type: object
          title: Result
          description: Command execution result
        message:
          type: string
          title: Message
          description: Human-readable response message
        agent_type:
          type: string
          title: Agent Type
          description: Type of agent that processed command
        execution_time_ms:
          type: number
          title: Execution Time Ms
          description: Execution time in milliseconds
      type: object
      required:
      - success
      - command
      - result
      - message
      - agent_type
      - execution_time_ms
      title: AgentCommandResponse
      description: Response model for agent command processing.
    AgentSession:
      properties:
        id:
          type: string
          title: Id
        agentId:
          type: string
          title: Agentid
        conversationId:
          type: string
          title: Conversationid
        memoryContext:
          additionalProperties: true
          type: object
          title: Memorycontext
        startTime:
          type: string
          title: Starttime
        lastInteraction:
          type: string
          title: Lastinteraction
        isActive:
          type: boolean
          title: Isactive
      type: object
      required:
      - id
      - agentId
      - conversationId
      - memoryContext
      - startTime
      - lastInteraction
      - isActive
      title: AgentSession
    AgentTransferRequest:
      properties:
        fromAgentId:
          type: string
          title: Fromagentid
        toAgentId:
          type: string
          title: Toagentid
        context:
          additionalProperties: true
          type: object
          title: Context
        reason:
          type: string
          title: Reason
        preserveMemory:
          type: boolean
          title: Preservememory
          default: true
      type: object
      required:
      - fromAgentId
      - toAgentId
      - context
      - reason
      title: AgentTransferRequest
    AmountProcessingRequest:
      properties:
        transaction_data:
          additionalProperties: true
          type: object
          title: Transaction Data
          description: Transaction data to process
        column_mapping:
          additionalProperties:
            type: string
          type: object
          title: Column Mapping
          description: Column mapping configuration
        accounting_system:
          additionalProperties: true
          type: object
          title: Accounting System
          description: Detected accounting system info
      type: object
      required:
      - transaction_data
      - column_mapping
      - accounting_system
      title: AmountProcessingRequest
    AmountProcessingResponse:
      properties:
        amount:
          type: number
          title: Amount
          description: Processed amount value
        transaction_type:
          type: string
          title: Transaction Type
          description: Determined transaction type
        currency:
          type: string
          title: Currency
          description: Detected currency
        cash_flow_direction:
          type: string
          title: Cash Flow Direction
          description: Cash flow direction (inflow/outflow)
      type: object
      required:
      - amount
      - transaction_type
      - currency
      - cash_flow_direction
      title: AmountProcessingResponse
    BatchAnalysisRequest:
      properties:
        transactions:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Transactions
          description: Batch of transactions to analyze
        context:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Context
          description: Additional context for analysis
      type: object
      required:
      - transactions
      title: BatchAnalysisRequest
    BatchAnalysisResponse:
      properties:
        batch_size:
          type: integer
          title: Batch Size
          description: Number of transactions in batch
        accounting_system:
          additionalProperties: true
          type: object
          title: Accounting System
          description: Detected accounting system
        processing_summary:
          additionalProperties: true
          type: object
          title: Processing Summary
          description: Processing summary
        quality_metrics:
          additionalProperties: true
          type: object
          title: Quality Metrics
          description: Quality metrics
        insights:
          additionalProperties: true
          type: object
          title: Insights
          description: Generated insights
        recommendations:
          items:
            type: string
          type: array
          title: Recommendations
          description: Processing recommendations
      type: object
      required:
      - batch_size
      - accounting_system
      - processing_summary
      - quality_metrics
      - insights
      - recommendations
      title: BatchAnalysisResponse
    BatchCategorizationRequest:
      properties:
        transactions:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Transactions
        context:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Context
      type: object
      required:
      - transactions
      title: BatchCategorizationRequest
      description: Batch categorization request.
    Body_apply_mis_enhancement_api_v1_onboarding_mis_enhance__setup_id__post:
      properties:
        type:
          type: string
          title: Type
        applyRetrospectively:
          type: string
          title: Applyretrospectively
          default: 'false'
        files:
          items:
            type: string
            format: binary
          type: array
          title: Files
      type: object
      required:
      - type
      - files
      title: Body_apply_mis_enhancement_api_v1_onboarding_mis_enhance__setup_id__post
    Body_batch_upload_files_api_v1_onboarding_batch_upload_files_post:
      properties:
        files:
          items:
            type: string
            format: binary
          type: array
          title: Files
          description: Financial data files to upload
        year:
          type: string
          title: Year
          description: Year of the data (e.g., 2024)
        has_category_labels:
          type: boolean
          title: Has Category Labels
          description: Whether files contain category labels
          default: true
      type: object
      required:
      - files
      - year
      title: Body_batch_upload_files_api_v1_onboarding_batch_upload_files_post
    Body_detect_enhancements_api_v1_onboarding_mis_detect_enhancements_post:
      properties:
        file:
          type: string
          format: binary
          title: File
      type: object
      required:
      - file
      title: Body_detect_enhancements_api_v1_onboarding_mis_detect_enhancements_post
    Body_import_category_schema_api_v1_onboarding_import_schema_post:
      properties:
        file:
          type: string
          format: binary
          title: File
      type: object
      required:
      - file
      title: Body_import_category_schema_api_v1_onboarding_import_schema_post
    Body_login_api_v1_auth_login_post:
      properties:
        grant_type:
          anyOf:
          - type: string
            pattern: ^password$
          - type: 'null'
          title: Grant Type
        username:
          type: string
          title: Username
        password:
          type: string
          format: password
          title: Password
        scope:
          type: string
          title: Scope
          default: ''
        client_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Client Id
        client_secret:
          anyOf:
          - type: string
          - type: 'null'
          format: password
          title: Client Secret
      type: object
      required:
      - username
      - password
      title: Body_login_api_v1_auth_login_post
    Body_login_api_v1_auth_token_post:
      properties:
        grant_type:
          anyOf:
          - type: string
            pattern: ^password$
          - type: 'null'
          title: Grant Type
        username:
          type: string
          title: Username
        password:
          type: string
          format: password
          title: Password
        scope:
          type: string
          title: Scope
          default: ''
        client_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Client Id
        client_secret:
          anyOf:
          - type: string
          - type: 'null'
          format: password
          title: Client Secret
      type: object
      required:
      - username
      - password
      title: Body_login_api_v1_auth_token_post
    Body_login_auth_login_post:
      properties:
        grant_type:
          anyOf:
          - type: string
            pattern: ^password$
          - type: 'null'
          title: Grant Type
        username:
          type: string
          title: Username
        password:
          type: string
          format: password
          title: Password
        scope:
          type: string
          title: Scope
          default: ''
        client_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Client Id
        client_secret:
          anyOf:
          - type: string
          - type: 'null'
          format: password
          title: Client Secret
      type: object
      required:
      - username
      - password
      title: Body_login_auth_login_post
    Body_login_auth_token_post:
      properties:
        grant_type:
          anyOf:
          - type: string
            pattern: ^password$
          - type: 'null'
          title: Grant Type
        username:
          type: string
          title: Username
        password:
          type: string
          format: password
          title: Password
        scope:
          type: string
          title: Scope
          default: ''
        client_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Client Id
        client_secret:
          anyOf:
          - type: string
          - type: 'null'
          format: password
          title: Client Secret
      type: object
      required:
      - username
      - password
      title: Body_login_auth_token_post
    Body_process_audio_query_api_v1_intelligence_agent_customer_audio_post:
      properties:
        audio_file:
          type: string
          format: binary
          title: Audio File
      type: object
      required:
      - audio_file
      title: Body_process_audio_query_api_v1_intelligence_agent_customer_audio_post
    Body_upload_file_with_detailed_reporting_api_v1_onboarding_upload_with_reporting_post:
      properties:
        file:
          type: string
          format: binary
          title: File
        year:
          type: integer
          title: Year
        has_category_labels:
          type: boolean
          title: Has Category Labels
      type: object
      required:
      - file
      - year
      - has_category_labels
      title: Body_upload_file_with_detailed_reporting_api_v1_onboarding_upload_with_reporting_post
    Body_upload_files_api_v1_files_upload_post:
      properties:
        files:
          items:
            type: string
            format: binary
          type: array
          title: Files
          description: Upload up to 10 files at once
      type: object
      required:
      - files
      title: Body_upload_files_api_v1_files_upload_post
    Body_upload_historical_data_api_v1_onboarding_upload_historical_data_post:
      properties:
        file:
          type: string
          format: binary
          title: File
        year:
          type: integer
          title: Year
        has_category_labels:
          type: boolean
          title: Has Category Labels
      type: object
      required:
      - file
      - year
      - has_category_labels
      title: Body_upload_historical_data_api_v1_onboarding_upload_historical_data_post
    Body_upload_production_files_api_v1_files_upload_production_data_post:
      properties:
        files:
          items:
            type: string
            format: binary
          type: array
          title: Files
          description: Upload production transaction files WITHOUT categories
      type: object
      required:
      - files
      title: Body_upload_production_files_api_v1_files_upload_production_data_post
    BulkGLCodeRequest:
      properties:
        mappings:
          items:
            $ref: '#/components/schemas/GLCodeMappingRequest'
          type: array
          title: Mappings
        validate_hierarchy:
          type: boolean
          title: Validate Hierarchy
          default: true
      type: object
      required:
      - mappings
      title: BulkGLCodeRequest
    BulkGLUpdate:
      properties:
        mappings:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Mappings
      type: object
      required:
      - mappings
      title: BulkGLUpdate
      description: Schema for bulk GL code updates.
    BulkReviewAction:
      properties:
        action:
          type: string
          pattern: ^(accept|reject|categorize)$
          title: Action
        transaction_ids:
          items:
            type: integer
          type: array
          title: Transaction Ids
        category_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Category Id
        notes:
          anyOf:
          - type: string
          - type: 'null'
          title: Notes
      type: object
      required:
      - action
      - transaction_ids
      title: BulkReviewAction
      description: Action to take on multiple transactions.
    BulkVendorMappingRequest:
      properties:
        mappings:
          items:
            $ref: '#/components/schemas/VendorMappingCreate'
          type: array
          title: Mappings
        apply_to_existing:
          type: boolean
          title: Apply To Existing
          description: Apply mappings to existing uncategorized transactions
          default: true
        apply_to_future:
          type: boolean
          title: Apply To Future
          description: Apply mappings to future transactions
          default: true
      type: object
      required:
      - mappings
      title: BulkVendorMappingRequest
      description: Request to create multiple vendor mappings at once.
    CategorizationColumnSchema:
      properties:
        column_name:
          type: string
          title: Column Name
          description: Column name
        categorization_relevance:
          type: number
          maximum: 1.0
          minimum: 0.0
          title: Categorization Relevance
          description: Relevance score
        categorization_role:
          type: string
          title: Categorization Role
          description: Role in categorization
        importance_weight:
          type: number
          title: Importance Weight
          description: Importance weight
          default: 1.0
        preferred_for_embedding:
          type: boolean
          title: Preferred For Embedding
          description: Use for RAG embeddings
          default: false
      type: object
      required:
      - column_name
      - categorization_relevance
      - categorization_role
      title: CategorizationColumnSchema
      description: Schema for categorization column analysis.
    CategorizationImprovementRequest:
      properties:
        transaction_ids:
          items:
            type: string
          type: array
          title: Transaction Ids
        improvement_type:
          type: string
          title: Improvement Type
          default: accuracy
        feedback:
          anyOf:
          - type: string
          - type: 'null'
          title: Feedback
      type: object
      required:
      - transaction_ids
      title: CategorizationImprovementRequest
      description: Request schema for categorization improvement endpoint.
    CategorizationImprovementResponse:
      properties:
        improvement_id:
          type: string
          title: Improvement Id
        transaction_count:
          type: integer
          title: Transaction Count
        improvements_suggested:
          type: integer
          title: Improvements Suggested
        processing_status:
          type: string
          title: Processing Status
        analysis:
          additionalProperties: true
          type: object
          title: Analysis
        recommendations:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Recommendations
      type: object
      required:
      - improvement_id
      - transaction_count
      - improvements_suggested
      - processing_status
      - analysis
      - recommendations
      title: CategorizationImprovementResponse
      description: Response schema for categorization improvement.
    CategorizationRequest:
      properties:
        description:
          type: string
          title: Description
        amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Amount
        date:
          anyOf:
          - type: string
          - type: 'null'
          title: Date
        context:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Context
      type: object
      required:
      - description
      title: CategorizationRequest
      description: Transaction categorization request.
    CategorizationResult:
      properties:
        success:
          type: boolean
          title: Success
        category:
          type: string
          title: Category
        confidence:
          type: number
          title: Confidence
        reasoning:
          type: string
          title: Reasoning
        alternatives:
          anyOf:
          - items:
              additionalProperties: true
              type: object
            type: array
          - type: 'null'
          title: Alternatives
        gl_code:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Code
        hierarchy_path:
          anyOf:
          - type: string
          - type: 'null'
          title: Hierarchy Path
      type: object
      required:
      - success
      - category
      - confidence
      - reasoning
      title: CategorizationResult
      description: Categorization result with confidence.
    Category:
      properties:
        id:
          type: integer
          title: Id
        name:
          type: string
          title: Name
        color:
          anyOf:
          - type: string
          - type: 'null'
          title: Color
        path:
          anyOf:
          - type: string
          - type: 'null'
          title: Path
        parent_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Parent Id
        level:
          type: integer
          title: Level
          default: 0
        tenant_id:
          type: integer
          title: Tenant Id
        gl_code:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Code
        gl_account_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Name
        gl_account_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Type
        learned_from_onboarding:
          type: boolean
          title: Learned From Onboarding
          default: true
        frequency_score:
          anyOf:
          - type: number
          - type: 'null'
          title: Frequency Score
        confidence_score:
          anyOf:
          - type: number
          - type: 'null'
          title: Confidence Score
        created_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Created At
        updated_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Updated At
      type: object
      required:
      - id
      - name
      - tenant_id
      title: Category
      description: Basic category schema for responses.
    CategoryCreate:
      properties:
        name:
          type: string
          title: Name
        color:
          anyOf:
          - type: string
          - type: 'null'
          title: Color
          default: '#6B7280'
        parent_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Parent Id
        gl_code:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Code
        gl_account_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Name
        gl_account_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Type
      type: object
      required:
      - name
      title: CategoryCreate
      description: Schema for creating a new category.
    CategorySchemaCreate:
      properties:
        name:
          type: string
          maxLength: 200
          title: Name
          description: Schema name
        description:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Description
          description: Schema description
        schema_format:
          type: string
          maxLength: 50
          title: Schema Format
          description: 'Format: excel, csv, json, yaml'
        schema_data:
          additionalProperties: true
          type: object
          title: Schema Data
          description: Parsed category hierarchy structure
        imported_from:
          type: string
          maxLength: 500
          title: Imported From
          description: Original file name or source
        imported_by:
          anyOf:
          - type: string
            maxLength: 100
          - type: 'null'
          title: Imported By
          description: User identifier
      type: object
      required:
      - name
      - schema_format
      - schema_data
      - imported_from
      title: CategorySchemaCreate
      description: Schema for creating a category schema.
    CategorySchemaImport:
      properties:
        name:
          type: string
          maxLength: 200
          title: Name
        description:
          anyOf:
          - type: string
            maxLength: 1000
          - type: 'null'
          title: Description
        file_format:
          type: string
          title: File Format
          description: excel, csv, json, yaml
        file_content:
          type: string
          title: File Content
          description: Base64 encoded file content or raw content
        detect_hierarchy:
          type: boolean
          title: Detect Hierarchy
          description: Auto-detect hierarchy from flat lists
          default: true
        generate_gl_codes:
          type: boolean
          title: Generate Gl Codes
          description: Auto-generate GL codes
          default: true
      type: object
      required:
      - name
      - file_format
      - file_content
      title: CategorySchemaImport
      description: Schema for importing category schema from file.
    CategorySchemaImportResponse:
      properties:
        schema_id:
          type: integer
          title: Schema Id
        message:
          type: string
          title: Message
        categories_imported:
          type: integer
          title: Categories Imported
        hierarchy_levels_detected:
          type: integer
          title: Hierarchy Levels Detected
        gl_codes_generated:
          type: integer
          title: Gl Codes Generated
        validation_warnings:
          items:
            type: string
          type: array
          title: Validation Warnings
          default: []
      type: object
      required:
      - schema_id
      - message
      - categories_imported
      - hierarchy_levels_detected
      - gl_codes_generated
      title: CategorySchemaImportResponse
      description: Schema for category schema import response.
    CategorySchemaListResponse:
      properties:
        schemas:
          items:
            $ref: '#/components/schemas/CategorySchemaResponse'
          type: array
          title: Schemas
        total_count:
          type: integer
          title: Total Count
      type: object
      required:
      - schemas
      - total_count
      title: CategorySchemaListResponse
      description: Schema for category schema list response.
    CategorySchemaResponse:
      properties:
        id:
          type: integer
          title: Id
        tenant_id:
          type: integer
          title: Tenant Id
        name:
          type: string
          title: Name
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        schema_format:
          type: string
          title: Schema Format
        schema_data:
          additionalProperties: true
          type: object
          title: Schema Data
        category_count:
          type: integer
          title: Category Count
        max_hierarchy_depth:
          type: integer
          title: Max Hierarchy Depth
        has_gl_codes:
          type: boolean
          title: Has Gl Codes
        gl_code_mapping:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          title: Gl Code Mapping
        imported_from:
          type: string
          title: Imported From
        imported_at:
          type: string
          format: date-time
          title: Imported At
        imported_by:
          anyOf:
          - type: string
          - type: 'null'
          title: Imported By
        is_active:
          type: boolean
          title: Is Active
        last_used_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Used At
        usage_count:
          type: integer
          title: Usage Count
      type: object
      required:
      - id
      - tenant_id
      - name
      - description
      - schema_format
      - schema_data
      - category_count
      - max_hierarchy_depth
      - has_gl_codes
      - gl_code_mapping
      - imported_from
      - imported_at
      - imported_by
      - is_active
      - last_used_at
      - usage_count
      title: CategorySchemaResponse
      description: Schema for category schema response.
    CategoryTree:
      properties:
        id:
          type: integer
          title: Id
        name:
          type: string
          title: Name
        color:
          anyOf:
          - type: string
          - type: 'null'
          title: Color
        path:
          anyOf:
          - type: string
          - type: 'null'
          title: Path
        parent_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Parent Id
        level:
          type: integer
          title: Level
          default: 0
        tenant_id:
          type: integer
          title: Tenant Id
        gl_code:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Code
        gl_account_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Name
        gl_account_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Type
        learned_from_onboarding:
          type: boolean
          title: Learned From Onboarding
          default: true
        frequency_score:
          anyOf:
          - type: number
          - type: 'null'
          title: Frequency Score
        confidence_score:
          anyOf:
          - type: number
          - type: 'null'
          title: Confidence Score
        created_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Created At
        updated_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Updated At
        children:
          items:
            $ref: '#/components/schemas/CategoryTree'
          type: array
          title: Children
          default: []
      type: object
      required:
      - id
      - name
      - tenant_id
      title: CategoryTree
      description: Category schema that includes children for hierarchical responses.
    CategoryUpdate:
      properties:
        name:
          anyOf:
          - type: string
          - type: 'null'
          title: Name
        color:
          anyOf:
          - type: string
          - type: 'null'
          title: Color
        parent_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Parent Id
        gl_code:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Code
        gl_account_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Name
        gl_account_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Type
      type: object
      title: CategoryUpdate
      description: Schema for updating an existing category.
    CategoryVisualization:
      properties:
        id:
          type: integer
          title: Id
        name:
          type: string
          title: Name
        path:
          type: string
          title: Path
        level:
          type: integer
          title: Level
        transaction_count:
          type: integer
          title: Transaction Count
          default: 0
        total_amount:
          type: number
          title: Total Amount
          default: 0.0
        percentage:
          type: number
          title: Percentage
          default: 0.0
        color:
          anyOf:
          - type: string
          - type: 'null'
          title: Color
        gl_code:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Code
        children:
          items:
            $ref: '#/components/schemas/CategoryVisualization'
          type: array
          title: Children
          default: []
      type: object
      required:
      - id
      - name
      - path
      - level
      title: CategoryVisualization
      description: Category with visualization data for hierarchical display.
    ChatMessage:
      properties:
        message:
          type: string
          title: Message
        command:
          anyOf:
          - type: string
          - type: 'null'
          title: Command
        parameters:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Parameters
        context:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Context
      type: object
      required:
      - message
      title: ChatMessage
      description: Chat message model for frontend communication.
    ChatRequest:
      properties:
        message:
          type: string
          title: Message
          description: User message to process
        context:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Context
          description: Additional context for the conversation
          default: {}
      type: object
      required:
      - message
      title: ChatRequest
    ColumnListResponse:
      properties:
        upload_id:
          type: string
          title: Upload Id
          description: Unique identifier for the upload process.
        columns:
          items:
            type: string
          type: array
          title: Columns
          description: List of column headers extracted from the file.
      type: object
      required:
      - upload_id
      - columns
      title: ColumnListResponse
      description: Response containing the list of column headers from an uploaded
        file.
    ColumnMappingPayload:
      properties:
        mapping:
          anyOf:
          - additionalProperties:
              anyOf:
              - type: string
              - type: 'null'
            type: object
          - type: 'null'
          title: Mapping
          description: Dictionary mapping source column names to target field names.
            If not provided, AI interpretation will be used.
      type: object
      title: ColumnMappingPayload
      description: 'Payload for submitting column mappings.

        Keys are source column names from the uploaded file, values are target internal
        field names.

        Enhanced to support AI interpretation when mapping not provided.'
    ColumnMappingSchema:
      properties:
        source_column:
          type: string
          title: Source Column
          description: Original column name from file
        target_field:
          type: string
          title: Target Field
          description: Mapped target field
        confidence_score:
          type: number
          maximum: 1.0
          minimum: 0.0
          title: Confidence Score
          description: Confidence score
        reasoning:
          anyOf:
          - type: string
          - type: 'null'
          title: Reasoning
          description: AI reasoning for mapping
        data_type_detected:
          anyOf:
          - type: string
          - type: 'null'
          title: Data Type Detected
          description: Detected data type
        is_user_modified:
          type: boolean
          title: Is User Modified
          description: Whether user modified mapping
          default: false
      type: object
      required:
      - source_column
      - target_field
      - confidence_score
      title: ColumnMappingSchema
      description: Schema for individual column mapping storage.
    ColumnStatisticResponse:
      properties:
        column_name:
          type: string
          title: Column Name
          description: Column name
        column_index:
          type: integer
          title: Column Index
          description: Column index in file
        mapped_field:
          anyOf:
          - type: string
          - type: 'null'
          title: Mapped Field
          description: Mapped field name
        total_values:
          type: integer
          title: Total Values
          description: Total number of values
        non_null_values:
          type: integer
          title: Non Null Values
          description: Number of non-null values
        null_percentage:
          type: number
          title: Null Percentage
          description: Percentage of null values
        unique_values:
          type: integer
          title: Unique Values
          description: Number of unique values
        detected_type:
          $ref: '#/components/schemas/DataType'
          description: Detected data type
        type_consistency:
          type: number
          title: Type Consistency
          description: Type consistency score (0.0 to 1.0)
        min_value:
          anyOf:
          - type: string
          - type: 'null'
          title: Min Value
          description: Minimum value
        max_value:
          anyOf:
          - type: string
          - type: 'null'
          title: Max Value
          description: Maximum value
        average_value:
          anyOf:
          - type: number
          - type: 'null'
          title: Average Value
          description: Average for numeric columns
        most_common_values:
          items:
            $ref: '#/components/schemas/ValueDistribution'
          type: array
          title: Most Common Values
          description: Most common values
        empty_string_count:
          type: integer
          title: Empty String Count
          description: Count of empty strings
          default: 0
        invalid_format_count:
          type: integer
          title: Invalid Format Count
          description: Count of invalid formats
          default: 0
        data_quality_score:
          type: number
          title: Data Quality Score
          description: Data quality score (0.0 to 1.0)
        mapping_confidence:
          anyOf:
          - type: number
          - type: 'null'
          title: Mapping Confidence
          description: Mapping confidence
        mapping_method:
          anyOf:
          - $ref: '#/components/schemas/MappingMethod'
          - type: 'null'
          description: Mapping method
      type: object
      required:
      - column_name
      - column_index
      - total_values
      - non_null_values
      - null_percentage
      - unique_values
      - detected_type
      - type_consistency
      - data_quality_score
      title: ColumnStatisticResponse
      description: Column statistics response.
    CompanyInfo:
      properties:
        name:
          type: string
          title: Name
        industry:
          type: string
          title: Industry
        size:
          type: string
          title: Size
        fiscal_year_end:
          type: string
          title: Fiscal Year End
        default_currency:
          type: string
          title: Default Currency
      type: object
      required:
      - name
      - industry
      - size
      - fiscal_year_end
      - default_currency
      title: CompanyInfo
      description: Company information for MIS setup.
    ConfidenceDistribution:
      properties:
        high_confidence:
          type: integer
          title: High Confidence
        medium_confidence:
          type: integer
          title: Medium Confidence
        low_confidence:
          type: integer
          title: Low Confidence
        total:
          type: integer
          title: Total
      type: object
      required:
      - high_confidence
      - medium_confidence
      - low_confidence
      - total
      title: ConfidenceDistribution
      description: Confidence distribution schema.
    ConfirmInterpretationRequest:
      properties:
        upload_id:
          type: string
          title: Upload Id
        confirmed_mappings:
          additionalProperties:
            type: string
          type: object
          title: Confirmed Mappings
      type: object
      required:
      - upload_id
      - confirmed_mappings
      title: ConfirmInterpretationRequest
      description: Request to confirm interpretation results.
    ConfirmInterpretationResponse:
      properties:
        upload_id:
          type: string
          title: Upload Id
        status:
          type: string
          title: Status
        transactions_created:
          type: integer
          title: Transactions Created
        message:
          type: string
          title: Message
      type: object
      required:
      - upload_id
      - status
      - transactions_created
      - message
      title: ConfirmInterpretationResponse
      description: Response after confirming interpretation.
    ConversationRequest:
      properties:
        message:
          type: string
          title: Message
        session_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Session Id
        context:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Context
      type: object
      required:
      - message
      title: ConversationRequest
      description: Conversation processing request.
    ConversationResponse:
      properties:
        success:
          type: boolean
          title: Success
        message:
          type: string
          title: Message
        session_id:
          type: string
          title: Session Id
        actions:
          anyOf:
          - items:
              additionalProperties: true
              type: object
            type: array
          - type: 'null'
          title: Actions
        suggestions:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Suggestions
        data:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Data
      type: object
      required:
      - success
      - message
      - session_id
      title: ConversationResponse
      description: Conversation response with actions.
    CreateTenantRequest:
      properties:
        name:
          type: string
          title: Name
          description: Tenant name
        email:
          type: string
          title: Email
          description: Tenant contact email
        settings:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Settings
          description: Tenant settings
        is_active:
          type: boolean
          title: Is Active
          description: Whether tenant is active
          default: true
      type: object
      required:
      - name
      - email
      title: CreateTenantRequest
      description: Request model for creating a tenant.
    CreateUserRequest:
      properties:
        email:
          type: string
          title: Email
          description: User email
        password:
          type: string
          title: Password
          description: User password
        is_active:
          type: boolean
          title: Is Active
          description: Whether user is active
          default: true
        is_verified:
          type: boolean
          title: Is Verified
          description: Whether user is verified
          default: false
      type: object
      required:
      - email
      - password
      title: CreateUserRequest
      description: Request model for creating a user.
    CustomReportDataResponse:
      properties:
        report_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Report Id
          description: Report ID if saved
        report_type:
          type: string
          title: Report Type
          description: Type of report generated
        filters:
          additionalProperties: true
          type: object
          title: Filters
          description: Applied filters
        date_range:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          title: Date Range
          description: Applied date range
        data:
          additionalProperties: true
          type: object
          title: Data
          description: Report data
        metadata:
          additionalProperties: true
          type: object
          title: Metadata
          description: Report metadata
        generated_at:
          type: string
          title: Generated At
          description: Generation timestamp
      type: object
      required:
      - report_type
      - filters
      - data
      - generated_at
      title: CustomReportDataResponse
      description: Response model for custom report data.
    CustomReportGenerateRequest:
      properties:
        report_type:
          type: string
          title: Report Type
          description: Type of report to generate
        filters:
          additionalProperties: true
          type: object
          title: Filters
          description: Report filters
        grouping:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Grouping
          description: Fields to group by
        aggregations:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Aggregations
          description: Aggregation functions to apply
        date_range:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          title: Date Range
          description: Date range for the report
        include_raw_data:
          type: boolean
          title: Include Raw Data
          description: Include raw transaction data
          default: false
      type: object
      required:
      - report_type
      title: CustomReportGenerateRequest
      description: Request model for generating a custom report.
    CustomReportListResponse:
      properties:
        reports:
          items:
            $ref: '#/components/schemas/CustomReportResponse'
          type: array
          title: Reports
          description: List of custom reports
        total:
          type: integer
          title: Total
          description: Total number of reports
      type: object
      required:
      - reports
      - total
      title: CustomReportListResponse
      description: Response model for listing custom reports.
    CustomReportResponse:
      properties:
        id:
          type: integer
          title: Id
          description: Report ID
        name:
          type: string
          title: Name
          description: Report name
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
          description: Report description
        report_type:
          type: string
          title: Report Type
          description: Type of report
        configuration:
          additionalProperties: true
          type: object
          title: Configuration
          description: Report configuration
        created_at:
          type: string
          title: Created At
          description: Creation timestamp
        updated_at:
          type: string
          title: Updated At
          description: Last update timestamp
      type: object
      required:
      - id
      - name
      - report_type
      - configuration
      - created_at
      - updated_at
      title: CustomReportResponse
      description: Response model for a custom report.
    CustomReportSaveRequest:
      properties:
        name:
          type: string
          maxLength: 255
          minLength: 1
          title: Name
          description: Report name
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
          description: Report description
        report_type:
          type: string
          title: Report Type
          description: Type of report
        configuration:
          additionalProperties: true
          type: object
          title: Configuration
          description: Report configuration
      type: object
      required:
      - name
      - report_type
      - configuration
      title: CustomReportSaveRequest
      description: Request model for saving a custom report configuration.
    CustomerAccountNode:
      properties:
        id:
          type: integer
          title: Id
        account_code:
          type: string
          title: Account Code
        account_name:
          type: string
          title: Account Name
        customer_id:
          type: string
          title: Customer Id
        parent_account_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Parent Account Id
        children:
          items:
            $ref: '#/components/schemas/CustomerAccountNode'
          type: array
          title: Children
          default: []
      type: object
      required:
      - id
      - account_code
      - account_name
      - customer_id
      title: CustomerAccountNode
      description: Represents a node in the hierarchy.
    CustomerHierarchy:
      properties:
        customer_id:
          type: string
          title: Customer Id
        accounts:
          items:
            $ref: '#/components/schemas/CustomerAccountNode'
          type: array
          title: Accounts
          description: List of root account nodes for the customer.
      type: object
      required:
      - customer_id
      - accounts
      title: CustomerHierarchy
      description: Represents the full hierarchy for a customer.
    CustomerQueryRequest:
      properties:
        query:
          type: string
          title: Query
          description: User's query or message
        query_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Query Type
          description: 'Type of query: transactions, insights, accuracy, general'
          default: general
        parameters:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Parameters
          description: Additional parameters for the query
      type: object
      required:
      - query
      title: CustomerQueryRequest
    DataProcessingRequest:
      properties:
        operation:
          type: string
          title: Operation
          description: 'Operation type: process_file, categorize, update_rag, simulate_accuracy'
        parameters:
          additionalProperties: true
          type: object
          title: Parameters
          description: Operation parameters
      type: object
      required:
      - operation
      - parameters
      title: DataProcessingRequest
    DataType:
      type: string
      enum:
      - string
      - number
      - date
      - boolean
      - mixed
      - unknown
      title: DataType
      description: Detected data type values.
    DescriptionNormalizationRequest:
      properties:
        descriptions:
          items:
            type: string
          type: array
          title: Descriptions
          description: Transaction descriptions to normalize
        cultural_context:
          anyOf:
          - type: string
          - type: 'null'
          title: Cultural Context
          description: Cultural context for processing
          default: global
      type: object
      required:
      - descriptions
      title: DescriptionNormalizationRequest
    DescriptionNormalizationResponse:
      properties:
        normalized_descriptions:
          items:
            type: string
          type: array
          title: Normalized Descriptions
          description: Normalized descriptions
        total_processed:
          type: integer
          title: Total Processed
          description: Number of descriptions processed
      type: object
      required:
      - normalized_descriptions
      - total_processed
      title: DescriptionNormalizationResponse
    EnhancementAnalysisResponse:
      properties:
        current_accuracy:
          type: number
          title: Current Accuracy
        baseline_accuracy:
          type: number
          title: Baseline Accuracy
        opportunities:
          items:
            $ref: '#/components/schemas/EnhancementOpportunity'
          type: array
          title: Opportunities
        total_potential_gain:
          type: number
          title: Total Potential Gain
        recommended_actions:
          items:
            type: string
          type: array
          title: Recommended Actions
        analysis_timestamp:
          type: string
          format: date-time
          title: Analysis Timestamp
      type: object
      required:
      - current_accuracy
      - baseline_accuracy
      - opportunities
      - total_potential_gain
      - recommended_actions
      - analysis_timestamp
      title: EnhancementAnalysisResponse
      description: Response for enhancement analysis endpoint.
    EnhancementOpportunity:
      properties:
        type:
          type: string
          title: Type
        priority:
          type: string
          title: Priority
        description:
          type: string
          title: Description
        affected_transactions:
          type: integer
          title: Affected Transactions
        potential_accuracy_gain:
          type: number
          title: Potential Accuracy Gain
        implementation_effort:
          type: string
          title: Implementation Effort
        data:
          additionalProperties: true
          type: object
          title: Data
      type: object
      required:
      - type
      - priority
      - description
      - affected_transactions
      - potential_accuracy_gain
      - implementation_effort
      - data
      title: EnhancementOpportunity
      description: Enhancement opportunity detected in the MIS system.
    EntityExtractionRequest:
      properties:
        descriptions:
          items:
            type: string
          type: array
          title: Descriptions
          description: Transaction descriptions to analyze
        cultural_context:
          anyOf:
          - type: string
          - type: 'null'
          title: Cultural Context
          description: Cultural context for processing
          default: global
      type: object
      required:
      - descriptions
      title: EntityExtractionRequest
    EntityExtractionResponse:
      properties:
        entities:
          additionalProperties:
            items:
              type: string
            type: array
          type: object
          title: Entities
          description: Extracted entities by type
        total_processed:
          type: integer
          title: Total Processed
          description: Number of descriptions processed
      type: object
      required:
      - entities
      - total_processed
      title: EntityExtractionResponse
    EntitySpendingItem:
      properties:
        entity_name:
          type: string
          title: Entity Name
          description: The name of the entity/vendor (derived from transaction description).
        total_amount:
          type: number
          title: Total Amount
          description: Total amount spent with this entity.
        transaction_count:
          type: integer
          title: Transaction Count
          description: Number of transactions with this entity.
      type: object
      required:
      - entity_name
      - total_amount
      - transaction_count
      title: EntitySpendingItem
      description: Represents aggregated spending for a single entity/vendor.
    EntitySpendingResponse:
      properties:
        items:
          items:
            $ref: '#/components/schemas/EntitySpendingItem'
          type: array
          title: Items
        total_records:
          type: integer
          title: Total Records
          description: Total number of unique entities with spending.
      type: object
      required:
      - items
      - total_records
      title: EntitySpendingResponse
      description: Response model for spending by entity/vendor report.
    ExportFormatInfo:
      properties:
        id:
          type: string
          title: Id
        name:
          type: string
          title: Name
        description:
          type: string
          title: Description
        file_extension:
          type: string
          title: File Extension
        supports_multi_currency:
          type: boolean
          title: Supports Multi Currency
        requires_account_codes:
          type: boolean
          title: Requires Account Codes
        required_fields:
          items:
            type: string
          type: array
          title: Required Fields
      type: object
      required:
      - id
      - name
      - description
      - file_extension
      - supports_multi_currency
      - requires_account_codes
      - required_fields
      title: ExportFormatInfo
      description: Information about an available export format.
    ExportReadinessResponse:
      properties:
        is_ready:
          type: boolean
          title: Is Ready
        messages:
          items:
            type: string
          type: array
          title: Messages
        format_name:
          type: string
          title: Format Name
      type: object
      required:
      - is_ready
      - messages
      - format_name
      title: ExportReadinessResponse
      description: Response model for export readiness check.
    ExportRequest:
      properties:
        format_id:
          type: string
          title: Format Id
          description: Export format identifier
        date_from:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Date From
          description: Start date filter
        date_to:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Date To
          description: End date filter
        category_ids:
          anyOf:
          - items:
              type: integer
            type: array
          - type: 'null'
          title: Category Ids
          description: Filter by category IDs
        account_ids:
          anyOf:
          - items:
              type: integer
            type: array
          - type: 'null'
          title: Account Ids
          description: Filter by account IDs
        include_uncategorized:
          type: boolean
          title: Include Uncategorized
          description: Include uncategorized transactions
          default: true
      type: object
      required:
      - format_id
      title: ExportRequest
      description: Request model for transaction export.
      example:
        date_from: '2024-01-01'
        date_to: '2024-12-31'
        format_id: quickbooks_online
        include_uncategorized: false
    FileColumnMapping:
      properties:
        date_column:
          type: string
          title: Date Column
          description: Column containing transaction date
        description_column:
          type: string
          title: Description Column
          description: Column containing transaction description
        amount_column:
          anyOf:
          - type: string
          - type: 'null'
          title: Amount Column
          description: Column containing transaction amount (single column)
        debit_column:
          anyOf:
          - type: string
          - type: 'null'
          title: Debit Column
          description: Column containing debit amounts (for dual-column systems)
        credit_column:
          anyOf:
          - type: string
          - type: 'null'
          title: Credit Column
          description: Column containing credit amounts (for dual-column systems)
        category_column:
          anyOf:
          - type: string
          - type: 'null'
          title: Category Column
          description: Column containing existing category labels
        vendor_column:
          anyOf:
          - type: string
          - type: 'null'
          title: Vendor Column
          description: Column containing vendor/merchant names
      type: object
      required:
      - date_column
      - description_column
      title: FileColumnMapping
      description: Mapping of file columns to standard transaction fields.
    FileProcessingReportResponse:
      properties:
        upload_id:
          type: string
          format: uuid
          title: Upload Id
          description: Upload ID
        file_name:
          type: string
          title: File Name
          description: Original file name
        status:
          $ref: '#/components/schemas/ProcessingStatus'
          description: Overall processing status
        total_rows:
          type: integer
          title: Total Rows
          description: Total rows in file
        successful_rows:
          type: integer
          title: Successful Rows
          description: Successfully processed rows
        failed_rows:
          type: integer
          title: Failed Rows
          description: Failed rows
        skipped_rows:
          type: integer
          title: Skipped Rows
          description: Skipped rows (e.g., headers)
        success_rate:
          type: number
          title: Success Rate
          description: Success rate percentage
        processing_started_at:
          type: string
          format: date-time
          title: Processing Started At
          description: Processing start time
        processing_completed_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Processing Completed At
          description: Processing end time
        processing_duration_seconds:
          anyOf:
          - type: number
          - type: 'null'
          title: Processing Duration Seconds
          description: Processing duration
        date_format_detected:
          anyOf:
          - type: string
          - type: 'null'
          title: Date Format Detected
          description: Detected date format
        total_columns:
          type: integer
          title: Total Columns
          description: Total columns in file
        mapped_columns:
          items:
            $ref: '#/components/schemas/giki_ai_api__domains__onboarding__processing_schemas__ColumnMapping'
          type: array
          title: Mapped Columns
          description: Successfully mapped columns
        unmapped_columns:
          items:
            type: string
          type: array
          title: Unmapped Columns
          description: Unmapped column names
        data_quality_score:
          type: number
          title: Data Quality Score
          description: Overall data quality score (0.0 to 1.0)
        validation_errors:
          items:
            $ref: '#/components/schemas/ValidationError'
          type: array
          title: Validation Errors
          description: Validation error summary
        warnings:
          items:
            type: string
          type: array
          title: Warnings
          description: Processing warnings
        categories_discovered:
          items:
            type: string
          type: array
          title: Categories Discovered
          description: Unique categories found
        category_count:
          type: integer
          title: Category Count
          description: Number of unique categories
          default: 0
        schema_confidence:
          anyOf:
          - type: number
          - type: 'null'
          title: Schema Confidence
          description: Schema interpretation confidence
        error_message:
          anyOf:
          - type: string
          - type: 'null'
          title: Error Message
          description: Error message if failed
        created_at:
          type: string
          format: date-time
          title: Created At
          description: Report creation time
        updated_at:
          type: string
          format: date-time
          title: Updated At
          description: Last update time
      type: object
      required:
      - upload_id
      - file_name
      - status
      - total_rows
      - successful_rows
      - failed_rows
      - skipped_rows
      - success_rate
      - processing_started_at
      - total_columns
      - data_quality_score
      - created_at
      - updated_at
      title: FileProcessingReportResponse
      description: Complete file processing report response.
    GLCodeAnalyticsResponse:
      properties:
        total_categories:
          type: integer
          title: Total Categories
        categorized_count:
          type: integer
          title: Categorized Count
        compliance_percentage:
          type: number
          title: Compliance Percentage
        accuracy_score:
          type: number
          title: Accuracy Score
        missing_gl_codes:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Missing Gl Codes
        validation_errors:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Validation Errors
        export_readiness:
          additionalProperties: true
          type: object
          title: Export Readiness
      type: object
      required:
      - total_categories
      - categorized_count
      - compliance_percentage
      - accuracy_score
      - missing_gl_codes
      - validation_errors
      - export_readiness
      title: GLCodeAnalyticsResponse
    GLCodeHierarchyResponse:
      properties:
        hierarchy:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Hierarchy
        total_categories:
          type: integer
          title: Total Categories
        categories_with_gl_codes:
          type: integer
          title: Categories With Gl Codes
        compliance_percentage:
          type: number
          title: Compliance Percentage
      type: object
      required:
      - hierarchy
      - total_categories
      - categories_with_gl_codes
      - compliance_percentage
      title: GLCodeHierarchyResponse
    GLCodeMappingRequest:
      properties:
        category_id:
          type: integer
          title: Category Id
        gl_code:
          type: string
          title: Gl Code
        gl_account_name:
          type: string
          title: Gl Account Name
        gl_account_type:
          type: string
          title: Gl Account Type
        confidence_score:
          anyOf:
          - type: number
          - type: 'null'
          title: Confidence Score
      type: object
      required:
      - category_id
      - gl_code
      - gl_account_name
      - gl_account_type
      title: GLCodeMappingRequest
    GLCodeUpdate:
      properties:
        gl_code:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Code
        gl_account_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Name
        gl_account_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Gl Account Type
      type: object
      title: GLCodeUpdate
      description: Schema for updating GL code mapping.
    GLCodeValidationRequest:
      properties:
        gl_code:
          type: string
          title: Gl Code
      type: object
      required:
      - gl_code
      title: GLCodeValidationRequest
      description: Schema for GL code validation request.
    GLCodeValidationResponse:
      properties:
        is_valid:
          type: boolean
          title: Is Valid
        validation_errors:
          items:
            type: string
          type: array
          title: Validation Errors
        suggestions:
          items:
            type: string
          type: array
          title: Suggestions
        compliance_score:
          type: number
          title: Compliance Score
      type: object
      required:
      - is_valid
      - validation_errors
      - suggestions
      - compliance_score
      title: GLCodeValidationResponse
    GroupReviewAction:
      properties:
        group_id:
          type: string
          title: Group Id
        action:
          type: string
          pattern: ^(accept|reject|categorize)$
          title: Action
        category_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Category Id
        create_vendor_mapping:
          type: boolean
          title: Create Vendor Mapping
          default: false
        vendor_mapping_options:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Vendor Mapping Options
      type: object
      required:
      - group_id
      - action
      title: GroupReviewAction
      description: Action to take on a transaction group.
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          type: array
          title: Detail
      type: object
      title: HTTPValidationError
    HierarchicalResults:
      properties:
        upload_id:
          type: string
          title: Upload Id
        total_transactions:
          type: integer
          title: Total Transactions
        categorized_transactions:
          type: integer
          title: Categorized Transactions
        uncategorized_transactions:
          type: integer
          title: Uncategorized Transactions
        accuracy_score:
          type: number
          title: Accuracy Score
        hierarchy_depth:
          type: integer
          title: Hierarchy Depth
        total_categories_used:
          type: integer
          title: Total Categories Used
        processing_time:
          type: number
          title: Processing Time
        income_hierarchy:
          items:
            $ref: '#/components/schemas/CategoryVisualization'
          type: array
          title: Income Hierarchy
          default: []
        expense_hierarchy:
          items:
            $ref: '#/components/schemas/CategoryVisualization'
          type: array
          title: Expense Hierarchy
          default: []
        total_income:
          type: number
          title: Total Income
          default: 0.0
        total_expenses:
          type: number
          title: Total Expenses
          default: 0.0
        top_categories:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Top Categories
          default: []
        high_confidence_count:
          type: integer
          title: High Confidence Count
          default: 0
        medium_confidence_count:
          type: integer
          title: Medium Confidence Count
          default: 0
        low_confidence_count:
          type: integer
          title: Low Confidence Count
          default: 0
        enhancement_suggestions:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Enhancement Suggestions
          default: []
      type: object
      required:
      - upload_id
      - total_transactions
      - categorized_transactions
      - uncategorized_transactions
      - accuracy_score
      - hierarchy_depth
      - total_categories_used
      - processing_time
      title: HierarchicalResults
      description: Hierarchical categorization results with visualization data.
    IncomeExpenseSummaryResponse:
      properties:
        total_income:
          type: number
          title: Total Income
          description: Total income for the period.
        total_expenses:
          type: number
          title: Total Expenses
          description: Total expenses for the period.
        net_income_loss:
          type: number
          title: Net Income Loss
          description: Net income or loss (Income - Expenses).
      type: object
      required:
      - total_income
      - total_expenses
      - net_income_loss
      title: IncomeExpenseSummaryResponse
      description: Response model for the Income vs. Expense summary report.
    InterpretationStorageResponse:
      properties:
        interpretation_id:
          type: string
          title: Interpretation Id
          description: Interpretation result ID
        upload_id:
          type: string
          title: Upload Id
          description: Upload ID
        overall_confidence:
          type: number
          title: Overall Confidence
          description: Overall interpretation confidence
        interpretation_status:
          type: string
          title: Interpretation Status
          description: Interpretation status
        column_mappings:
          items:
            $ref: '#/components/schemas/ColumnMappingSchema'
          type: array
          title: Column Mappings
        categorization_columns:
          items:
            $ref: '#/components/schemas/CategorizationColumnSchema'
          type: array
          title: Categorization Columns
        is_confirmed:
          type: boolean
          title: Is Confirmed
          description: Whether interpretation is confirmed
          default: false
        created_at:
          type: string
          title: Created At
          description: Creation timestamp
      type: object
      required:
      - interpretation_id
      - upload_id
      - overall_confidence
      - interpretation_status
      - created_at
      title: InterpretationStorageResponse
      description: Response schema for stored interpretation results.
    MISSetupRequest:
      properties:
        company_info:
          $ref: '#/components/schemas/CompanyInfo'
        uploaded_files:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Uploaded Files
      type: object
      required:
      - company_info
      title: MISSetupRequest
      description: Request for unified MIS setup.
    MISSetupResponse:
      properties:
        setupId:
          type: string
          title: Setupid
        status:
          type: string
          title: Status
        enhancementOpportunities:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Enhancementopportunities
        baselineAccuracy:
          type: number
          title: Baselineaccuracy
      type: object
      required:
      - setupId
      - status
      - enhancementOpportunities
      - baselineAccuracy
      title: MISSetupResponse
      description: Response from MIS setup.
    MappingMethod:
      type: string
      enum:
      - ai
      - pattern
      - exact
      - manual
      title: MappingMethod
      description: Column mapping method values.
    MigrationResponse:
      properties:
        status:
          type: string
          title: Status
          description: Migration status
        message:
          type: string
          title: Message
          description: Migration message
        stdout:
          anyOf:
          - type: string
          - type: 'null'
          title: Stdout
          description: Migration stdout
        stderr:
          anyOf:
          - type: string
          - type: 'null'
          title: Stderr
          description: Migration stderr
      type: object
      required:
      - status
      - message
      title: MigrationResponse
      description: Response model for database migration.
    MonthlyAccuracyResult:
      properties:
        month:
          type: string
          title: Month
          description: Month in YYYY-MM format
        training_months:
          items:
            type: string
          type: array
          title: Training Months
          description: Months used for training
        transactions_tested:
          type: integer
          title: Transactions Tested
          description: Number of transactions tested
        correct_predictions:
          type: integer
          title: Correct Predictions
          description: Number of correct predictions
        accuracy:
          type: number
          title: Accuracy
          description: Accuracy percentage (0-100)
        precision:
          type: number
          title: Precision
          description: Precision score
        recall:
          type: number
          title: Recall
          description: Recall score
        f1_score:
          type: number
          title: F1 Score
          description: F1 score
        category_breakdown:
          additionalProperties:
            additionalProperties:
              type: number
            type: object
          type: object
          title: Category Breakdown
          description: Per-category accuracy metrics
      type: object
      required:
      - month
      - training_months
      - transactions_tested
      - correct_predictions
      - accuracy
      - precision
      - recall
      - f1_score
      title: MonthlyAccuracyResult
      description: Accuracy results for a specific month.
    MonthlyTrendItem:
      properties:
        month:
          type: string
          title: Month
          description: Month in YYYY-MM format
        total_income:
          type: number
          title: Total Income
          description: Total income for the month
        total_expenses:
          type: number
          title: Total Expenses
          description: Total expenses for the month
        net_amount:
          type: number
          title: Net Amount
          description: Net income/loss for the month (income - expenses)
        transaction_count:
          type: integer
          title: Transaction Count
          description: Number of transactions in the month
      type: object
      required:
      - month
      - total_income
      - total_expenses
      - net_amount
      - transaction_count
      title: MonthlyTrendItem
      description: Represents income and expense data for a single month.
    MonthlyTrendsResponse:
      properties:
        items:
          items:
            $ref: '#/components/schemas/MonthlyTrendItem'
          type: array
          title: Items
        total_months:
          type: integer
          title: Total Months
          description: Total number of months with data
      type: object
      required:
      - items
      - total_months
      title: MonthlyTrendsResponse
      description: Response model for monthly trends report.
    MultipleUploadResponse:
      properties:
        uploads:
          items:
            $ref: '#/components/schemas/UploadResponse'
          type: array
          title: Uploads
          description: List of upload responses for each file.
        total_files:
          type: integer
          title: Total Files
          description: Total number of files uploaded.
        successful:
          type: integer
          title: Successful
          description: Number of successfully uploaded files.
        failed:
          type: integer
          title: Failed
          description: Number of failed uploads.
        message:
          type: string
          title: Message
          description: Overall upload status message.
      type: object
      required:
      - uploads
      - total_files
      - successful
      - failed
      - message
      title: MultipleUploadResponse
      description: Response after multiple files are successfully uploaded.
    NetworkStatus:
      properties:
        totalAgents:
          type: integer
          title: Totalagents
        availableAgents:
          type: integer
          title: Availableagents
        busyAgents:
          type: integer
          title: Busyagents
        offlineAgents:
          type: integer
          title: Offlineagents
        networkHealth:
          type: string
          title: Networkhealth
      type: object
      required:
      - totalAgents
      - availableAgents
      - busyAgents
      - offlineAgents
      - networkHealth
      title: NetworkStatus
    OnboardingApprovalRequest:
      properties:
        tenant_id:
          type: integer
          title: Tenant Id
        validation_id:
          type: string
          title: Validation Id
          description: ID of successful validation run
        approval_notes:
          anyOf:
          - type: string
          - type: 'null'
          title: Approval Notes
          description: Notes about the approval
      type: object
      required:
      - tenant_id
      - validation_id
      title: OnboardingApprovalRequest
      description: Request to approve tenant for production usage.
    OnboardingProgressResponse:
      properties:
        onboarding_id:
          type: string
          title: Onboarding Id
        type:
          type: string
          title: Type
          description: zero_onboarding, schema_guided, or historical_data
        status:
          type: string
          title: Status
          description: initialized, ai_training, accuracy_validation, or completed
        progress:
          type: number
          title: Progress
          description: Progress percentage (0-100)
        steps_completed:
          items:
            type: string
          type: array
          title: Steps Completed
        current_step:
          type: string
          title: Current Step
        next_step:
          type: string
          title: Next Step
        ai_training_results:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Ai Training Results
          description: Training results if available
      type: object
      required:
      - onboarding_id
      - type
      - status
      - progress
      - current_step
      - next_step
      title: OnboardingProgressResponse
      description: Progress response matching frontend expectations.
    OnboardingStartRequest:
      properties:
        tenant_id:
          type: integer
          title: Tenant Id
        tenant_name:
          type: string
          title: Tenant Name
        contact_email:
          type: string
          title: Contact Email
        expected_transaction_volume:
          type: integer
          title: Expected Transaction Volume
          description: Expected monthly transaction volume
        industry:
          anyOf:
          - type: string
          - type: 'null'
          title: Industry
          description: Business industry for context-aware categorization
        company_size:
          anyOf:
          - type: string
          - type: 'null'
          title: Company Size
          description: Company size (startup, small, medium, enterprise)
        company_website:
          anyOf:
          - type: string
          - type: 'null'
          title: Company Website
          description: Company website URL for business context analysis
        onboarding_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Onboarding Type
          description: 'Type of onboarding: zero_onboarding, historical_data, or schema_only'
          default: historical_data
      type: object
      required:
      - tenant_id
      - tenant_name
      - contact_email
      - expected_transaction_volume
      title: OnboardingStartRequest
      description: Request to start the onboarding process.
    OnboardingStatus:
      properties:
        tenant_id:
          type: integer
          title: Tenant Id
        onboarding_type:
          type: string
          title: Onboarding Type
          description: 'Types: zero_onboarding (M1), historical_data (M2), schema_only
            (M3)'
          default: historical_data
        onboarding_stage:
          type: string
          title: Onboarding Stage
          description: 'Current stage: not_started, data_uploaded, corpus_building,
            validating, completed, failed, zero_ready, schema_imported, schema_ready'
          default: not_started
        is_onboarding_complete:
          type: boolean
          title: Is Onboarding Complete
          default: false
        last_activity:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Activity
        uploaded_files:
          items:
            type: string
          type: array
          title: Uploaded Files
        total_transactions:
          type: integer
          title: Total Transactions
          default: 0
        transactions_with_labels:
          type: integer
          title: Transactions With Labels
          default: 0
        date_range_start:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Date Range Start
        date_range_end:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Date Range End
        rag_corpus_status:
          anyOf:
          - $ref: '#/components/schemas/RAGCorpusStatus'
          - type: 'null'
        validation_runs:
          items:
            type: string
          type: array
          title: Validation Runs
          description: IDs of validation runs
        latest_validation:
          anyOf:
          - $ref: '#/components/schemas/TemporalValidationResult'
          - type: 'null'
        meets_accuracy_threshold:
          type: boolean
          title: Meets Accuracy Threshold
          default: false
        approved_for_production:
          type: boolean
          title: Approved For Production
          default: false
        approved_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Approved At
        approval_notes:
          anyOf:
          - type: string
          - type: 'null'
          title: Approval Notes
      type: object
      required:
      - tenant_id
      title: OnboardingStatus
      description: Complete onboarding status for a tenant.
    PaginatedTransactionResponse:
      properties:
        items:
          items:
            $ref: '#/components/schemas/giki_ai_api__domains__transactions__router__TransactionResponse'
          type: array
          title: Items
        total_count:
          type: integer
          title: Total Count
        page:
          type: integer
          title: Page
        page_size:
          type: integer
          title: Page Size
        total_pages:
          type: integer
          title: Total Pages
        next_cursor:
          anyOf:
          - type: string
          - type: 'null'
          title: Next Cursor
        has_more:
          type: boolean
          title: Has More
          default: false
      type: object
      required:
      - items
      - total_count
      - page
      - page_size
      - total_pages
      title: PaginatedTransactionResponse
      description: Paginated transaction response.
    PerformanceMetricsResponse:
      properties:
        total_operations:
          type: integer
          title: Total Operations
        operations:
          additionalProperties: true
          type: object
          title: Operations
        summary:
          additionalProperties: true
          type: object
          title: Summary
      type: object
      required:
      - total_operations
      - operations
      - summary
      title: PerformanceMetricsResponse
      description: Response model for performance metrics.
    PerformanceSummaryResponse:
      properties:
        time_period_minutes:
          type: integer
          title: Time Period Minutes
        timestamp:
          type: number
          title: Timestamp
        api_performance:
          additionalProperties: true
          type: object
          title: Api Performance
        database_performance:
          additionalProperties: true
          type: object
          title: Database Performance
        endpoint_performance:
          additionalProperties: true
          type: object
          title: Endpoint Performance
        overall_health:
          additionalProperties: true
          type: object
          title: Overall Health
      type: object
      required:
      - time_period_minutes
      - timestamp
      - api_performance
      - database_performance
      - endpoint_performance
      - overall_health
      title: PerformanceSummaryResponse
      description: Response model for performance summary.
    PreloadMemoryRequest:
      properties:
        contextData:
          additionalProperties: true
          type: object
          title: Contextdata
      type: object
      required:
      - contextData
      title: PreloadMemoryRequest
    ProcessedFileResponse:
      properties:
        message:
          type: string
          title: Message
          description: A message providing more details about the processing outcome.
        records_processed:
          type: integer
          title: Records Processed
          description: Number of records successfully processed from the file.
        errors:
          items:
            type: string
          type: array
          title: Errors
          description: List of error messages encountered during processing.
        upload_id:
          type: string
          title: Upload Id
          description: Unique identifier for the upload process this result pertains
            to.
        categorization_job_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Categorization Job Id
          description: ID of the background job for AI categorization, if started.
        report_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Report Id
          description: ID of the detailed processing report, if enhanced reporting
            was used.
      type: object
      required:
      - message
      - records_processed
      - upload_id
      title: ProcessedFileResponse
      description: Response after a file has been processed based on column mappings.
    ProcessingReportSummary:
      properties:
        upload_id:
          type: string
          format: uuid
          title: Upload Id
          description: Upload ID
        file_name:
          type: string
          title: File Name
          description: File name
        status:
          $ref: '#/components/schemas/ProcessingStatus'
          description: Processing status
        total_rows:
          type: integer
          title: Total Rows
          description: Total rows
        success_rate:
          type: number
          title: Success Rate
          description: Success rate percentage
        data_quality_score:
          type: number
          title: Data Quality Score
          description: Data quality score
        processing_duration_seconds:
          anyOf:
          - type: number
          - type: 'null'
          title: Processing Duration Seconds
          description: Processing duration
        created_at:
          type: string
          format: date-time
          title: Created At
          description: Creation time
      type: object
      required:
      - upload_id
      - file_name
      - status
      - total_rows
      - success_rate
      - data_quality_score
      - created_at
      title: ProcessingReportSummary
      description: Summary of processing report for listing.
    ProcessingStatus:
      type: string
      enum:
      - processing
      - completed
      - failed
      title: ProcessingStatus
      description: Processing status values.
    RAGCorpusStatus:
      properties:
        tenant_id:
          type: integer
          title: Tenant Id
        corpus_exists:
          type: boolean
          title: Corpus Exists
          description: Whether RAG corpus has been created
          default: false
        corpus_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Corpus Name
          description: Name of the corpus in Vertex AI
        created_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Created At
        last_updated:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Updated
        total_patterns:
          type: integer
          title: Total Patterns
          description: Number of categorization patterns in corpus
          default: 0
        unique_categories:
          type: integer
          title: Unique Categories
          description: Number of unique categories
          default: 0
        source_files:
          items:
            type: string
          type: array
          title: Source Files
          description: Files used to build corpus
      type: object
      required:
      - tenant_id
      title: RAGCorpusStatus
      description: Status of RAG corpus for a tenant.
    ReportParseRequest:
      properties:
        query:
          type: string
          title: Query
        context:
          type: string
          title: Context
          default: financial_report_generation
      type: object
      required:
      - query
      title: ReportParseRequest
      description: Report query parsing request.
    ReportRequest:
      properties:
        type:
          type: string
          title: Type
        parameters:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Parameters
        naturalLanguageQuery:
          type: string
          title: Naturallanguagequery
      type: object
      required:
      - type
      - naturalLanguageQuery
      title: ReportRequest
      description: Parsed report request structure.
    ReviewQueueItem:
      properties:
        id:
          type: string
          title: Id
        description:
          type: string
          title: Description
        amount:
          type: number
          title: Amount
        date:
          type: string
          title: Date
        suggested_categories:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Suggested Categories
        flags:
          items:
            type: string
          type: array
          title: Flags
        ai_reasoning:
          anyOf:
          - type: string
          - type: 'null'
          title: Ai Reasoning
      type: object
      required:
      - id
      - description
      - amount
      - date
      - suggested_categories
      - flags
      title: ReviewQueueItem
      description: Review queue item schema.
    ReviewQueueSummary:
      properties:
        upload_id:
          type: string
          title: Upload Id
        total_transactions:
          type: integer
          title: Total Transactions
        auto_categorized:
          type: integer
          title: Auto Categorized
        auto_categorized_percentage:
          type: number
          title: Auto Categorized Percentage
        needs_review:
          type: integer
          title: Needs Review
        needs_review_percentage:
          type: number
          title: Needs Review Percentage
        grouped:
          type: integer
          title: Grouped
        grouped_percentage:
          type: number
          title: Grouped Percentage
        uncategorized:
          type: integer
          title: Uncategorized
        uncategorized_percentage:
          type: number
          title: Uncategorized Percentage
        groups_count:
          type: integer
          title: Groups Count
      type: object
      required:
      - upload_id
      - total_transactions
      - auto_categorized
      - auto_categorized_percentage
      - needs_review
      - needs_review_percentage
      - grouped
      - grouped_percentage
      - uncategorized
      - uncategorized_percentage
      - groups_count
      title: ReviewQueueSummary
      description: Summary of items in the review queue.
    RowDetailsRequest:
      properties:
        page:
          type: integer
          minimum: 1.0
          title: Page
          description: Page number
          default: 1
        page_size:
          type: integer
          maximum: 1000.0
          minimum: 1.0
          title: Page Size
          description: Items per page
          default: 100
        status_filter:
          anyOf:
          - $ref: '#/components/schemas/RowStatus'
          - type: 'null'
          description: Filter by status
        has_errors:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Has Errors
          description: Filter rows with errors
      type: object
      title: RowDetailsRequest
      description: Request for row details with pagination.
    RowDetailsResponse:
      properties:
        rows:
          items:
            $ref: '#/components/schemas/RowProcessingDetailResponse'
          type: array
          title: Rows
          description: Row details
        total_rows:
          type: integer
          title: Total Rows
          description: Total matching rows
        page:
          type: integer
          title: Page
          description: Current page
        page_size:
          type: integer
          title: Page Size
          description: Items per page
        total_pages:
          type: integer
          title: Total Pages
          description: Total pages
      type: object
      required:
      - rows
      - total_rows
      - page
      - page_size
      - total_pages
      title: RowDetailsResponse
      description: Paginated row details response.
    RowProcessingDetailResponse:
      properties:
        row_number:
          type: integer
          title: Row Number
          description: Row number in file
        status:
          $ref: '#/components/schemas/RowStatus'
          description: Processing status
        parsed_date:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Parsed Date
          description: Parsed date
        parsed_amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Parsed Amount
          description: Parsed amount
        parsed_description:
          anyOf:
          - type: string
          - type: 'null'
          title: Parsed Description
          description: Parsed description
        parsed_category:
          anyOf:
          - type: string
          - type: 'null'
          title: Parsed Category
          description: Parsed category
        parsed_vendor:
          anyOf:
          - type: string
          - type: 'null'
          title: Parsed Vendor
          description: Parsed vendor
        validation_errors:
          items:
            $ref: '#/components/schemas/ValidationError'
          type: array
          title: Validation Errors
          description: Validation errors
        data_quality_issues:
          items:
            type: string
          type: array
          title: Data Quality Issues
          description: Data quality issues
        raw_data:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Raw Data
          description: Original row data
      type: object
      required:
      - row_number
      - status
      title: RowProcessingDetailResponse
      description: Row processing detail response.
    RowStatus:
      type: string
      enum:
      - success
      - failed
      - skipped
      title: RowStatus
      description: Row processing status values.
    SchemaInterpretationResponse:
      properties:
        upload_id:
          type: string
          title: Upload Id
          description: Unique identifier for the upload process.
        filename:
          type: string
          title: Filename
          description: Name of the uploaded file.
        column_mappings:
          items:
            $ref: '#/components/schemas/giki_ai_api__domains__files__schemas__ColumnMapping'
          type: array
          title: Column Mappings
          description: Intelligent column mappings
        overall_confidence:
          type: number
          title: Overall Confidence
          description: Overall confidence in the interpretation
        required_fields_mapped:
          additionalProperties:
            type: boolean
          type: object
          title: Required Fields Mapped
          description: Status of required field mappings
        interpretation_summary:
          type: string
          title: Interpretation Summary
          description: Human-readable summary of the interpretation
        debit_credit_inference:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Debit Credit Inference
          description: AI inference about debit/credit column usage
          default: {}
        regional_variations:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Regional Variations
          description: Detected regional banking terminology variations
          default: []
      type: object
      required:
      - upload_id
      - filename
      - column_mappings
      - overall_confidence
      - required_fields_mapped
      - interpretation_summary
      title: SchemaInterpretationResponse
      description: Response containing intelligent schema mapping interpretation.
    SchemaMappingReportResponse:
      properties:
        tenant_id:
          type: integer
          title: Tenant Id
          description: Tenant ID
        has_schema_discovery:
          type: boolean
          title: Has Schema Discovery
          description: Whether schema discovery was performed
        session_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Session Id
          description: Schema discovery session ID
        schemas_discovered:
          type: integer
          title: Schemas Discovered
          description: Number of schemas discovered
          default: 0
        total_categories:
          type: integer
          title: Total Categories
          description: Total unique categories across all files
          default: 0
        unified_categories:
          type: integer
          title: Unified Categories
          description: Number of unified categories
          default: 0
        source_files:
          items:
            type: string
          type: array
          title: Source Files
          description: Source file names
        file_schemas:
          items:
            additionalProperties: true
            type: object
          type: array
          title: File Schemas
          description: Schema details per file
        category_mappings:
          additionalProperties:
            type: string
          type: object
          title: Category Mappings
          description: Original to unified mappings
        reverse_mappings:
          additionalProperties:
            items:
              type: string
            type: array
          type: object
          title: Reverse Mappings
          description: Unified to original mappings
        mapping_confidence:
          type: number
          title: Mapping Confidence
          description: Overall mapping confidence
          default: 0.0
        unmapped_categories:
          items:
            type: string
          type: array
          title: Unmapped Categories
          description: Categories without mappings
        created_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Created At
          description: Schema discovery time
        last_updated:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Updated
          description: Last update time
      type: object
      required:
      - tenant_id
      - has_schema_discovery
      title: SchemaMappingReportResponse
      description: Schema mapping report for a tenant.
    SchemaOnlyStartRequest:
      properties:
        tenant_id:
          type: integer
          title: Tenant Id
        tenant_name:
          type: string
          title: Tenant Name
        contact_email:
          type: string
          title: Contact Email
        category_hierarchy_file:
          anyOf:
          - type: string
          - type: 'null'
          title: Category Hierarchy File
          description: Category hierarchy file path
        industry:
          anyOf:
          - type: string
          - type: 'null'
          title: Industry
          description: Business industry for context-aware categorization
        company_size:
          anyOf:
          - type: string
          - type: 'null'
          title: Company Size
          description: Company size (startup, small, medium, enterprise)
        company_website:
          anyOf:
          - type: string
          - type: 'null'
          title: Company Website
          description: Company website URL for business context analysis
      type: object
      required:
      - tenant_id
      - tenant_name
      - contact_email
      title: SchemaOnlyStartRequest
      description: Request to start M3 schema-only onboarding process.
    SimpleUserResponse:
      properties:
        id:
          type: integer
          title: Id
        email:
          type: string
          title: Email
        tenant_id:
          type: integer
          title: Tenant Id
        is_active:
          type: boolean
          title: Is Active
        is_admin:
          type: boolean
          title: Is Admin
        full_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Full Name
      type: object
      required:
      - id
      - email
      - tenant_id
      - is_active
      - is_admin
      title: SimpleUserResponse
    SpendingByCategoryItem:
      properties:
        category_path:
          type: string
          title: Category Path
          description: The full path of the category.
        total_amount:
          type: number
          title: Total Amount
          description: Total amount spent in this category.
        transaction_count:
          type: integer
          title: Transaction Count
          description: Number of transactions in this category.
      type: object
      required:
      - category_path
      - total_amount
      - transaction_count
      title: SpendingByCategoryItem
      description: Represents aggregated spending for a single category.
    SpendingByCategoryResponse:
      properties:
        items:
          items:
            $ref: '#/components/schemas/SpendingByCategoryItem'
          type: array
          title: Items
        total_records:
          type: integer
          title: Total Records
          description: Total number of unique categories with spending.
      type: object
      required:
      - items
      - total_records
      title: SpendingByCategoryResponse
      description: Response model for spending by category report.
    StartSessionRequest:
      properties:
        preloadMemory:
          type: boolean
          title: Preloadmemory
          default: true
        contextSize:
          type: string
          title: Contextsize
          default: standard
      type: object
      title: StartSessionRequest
    SystemHealthResponse:
      properties:
        status:
          type: string
          title: Status
          description: Overall system status
        timestamp:
          anyOf:
          - type: string
          - type: 'null'
          title: Timestamp
          description: Health check timestamp
        services:
          additionalProperties: true
          type: object
          title: Services
          description: Service health status
        metrics:
          additionalProperties: true
          type: object
          title: Metrics
          description: System metrics
      type: object
      required:
      - status
      - services
      - metrics
      title: SystemHealthResponse
      description: Response model for system health.
    SystemStatusResponse:
      properties:
        timestamp:
          type: number
          title: Timestamp
        total_requests_lifetime:
          type: integer
          title: Total Requests Lifetime
        slow_requests_lifetime:
          type: integer
          title: Slow Requests Lifetime
        error_requests_lifetime:
          type: integer
          title: Error Requests Lifetime
        metrics_in_memory:
          additionalProperties:
            type: integer
          type: object
          title: Metrics In Memory
        tracked_endpoints:
          type: integer
          title: Tracked Endpoints
        memory_usage_mb:
          type: number
          title: Memory Usage Mb
      type: object
      required:
      - timestamp
      - total_requests_lifetime
      - slow_requests_lifetime
      - error_requests_lifetime
      - metrics_in_memory
      - tracked_endpoints
      - memory_usage_mb
      title: SystemStatusResponse
      description: Response model for system status.
    TemporalCorpusProgress:
      properties:
        month:
          type: string
          title: Month
          description: Month being processed (YYYY-MM)
        corpus_name:
          type: string
          title: Corpus Name
          description: Temporal corpus name
        training_data_size:
          type: integer
          title: Training Data Size
          description: Number of training transactions
        status:
          type: string
          title: Status
          description: building, completed, failed
        progress_percentage:
          type: number
          title: Progress Percentage
          description: Progress percentage (0-100)
          default: 0.0
        started_at:
          type: string
          format: date-time
          title: Started At
        completed_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Completed At
        error_message:
          anyOf:
          - type: string
          - type: 'null'
          title: Error Message
      type: object
      required:
      - month
      - corpus_name
      - training_data_size
      - status
      - started_at
      title: TemporalCorpusProgress
      description: Progress tracking for temporal RAG corpus building.
    TemporalValidationProgress:
      properties:
        validation_id:
          type: string
          title: Validation Id
        current_phase:
          type: string
          title: Current Phase
          description: corpus_building, testing, completed
        months_total:
          type: integer
          title: Months Total
          description: Total months to process
        months_completed:
          type: integer
          title: Months Completed
          description: Months completed
          default: 0
        current_month:
          anyOf:
          - type: string
          - type: 'null'
          title: Current Month
        corpus_progress:
          items:
            $ref: '#/components/schemas/TemporalCorpusProgress'
          type: array
          title: Corpus Progress
        overall_progress_percentage:
          type: number
          title: Overall Progress Percentage
          description: Overall progress (0-100)
          default: 0.0
        estimated_completion_time:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Estimated Completion Time
      type: object
      required:
      - validation_id
      - current_phase
      - months_total
      title: TemporalValidationProgress
      description: Overall progress for temporal validation with progressive RAG corpus
        building.
    TemporalValidationRequest:
      properties:
        tenant_id:
          type: integer
          title: Tenant Id
          description: Tenant ID for isolation
        start_month:
          type: string
          title: Start Month
          description: Start month for validation (YYYY-MM)
          default: 2024-07
        end_month:
          type: string
          title: End Month
          description: End month for validation (YYYY-MM)
          default: 2024-12
        accuracy_threshold:
          type: number
          title: Accuracy Threshold
          description: Minimum accuracy threshold
          default: 0.85
      type: object
      required:
      - tenant_id
      title: TemporalValidationRequest
      description: Request to run temporal accuracy validation.
    TemporalValidationResult:
      properties:
        tenant_id:
          type: integer
          title: Tenant Id
        validation_id:
          type: string
          title: Validation Id
          description: Unique validation run ID
        started_at:
          type: string
          format: date-time
          title: Started At
        completed_at:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Completed At
        status:
          type: string
          title: Status
          description: running, completed, failed
        monthly_results:
          items:
            $ref: '#/components/schemas/MonthlyAccuracyResult'
          type: array
          title: Monthly Results
        average_accuracy:
          type: number
          title: Average Accuracy
          description: Average accuracy across all months
          default: 0.0
        meets_threshold:
          type: boolean
          title: Meets Threshold
          description: Whether accuracy meets threshold
          default: false
        accuracy_threshold:
          type: number
          title: Accuracy Threshold
        total_training_transactions:
          anyOf:
          - type: integer
          - type: 'null'
          title: Total Training Transactions
          description: Total transactions used for training
        total_test_transactions:
          anyOf:
          - type: integer
          - type: 'null'
          title: Total Test Transactions
          description: Total transactions tested
        error_message:
          anyOf:
          - type: string
          - type: 'null'
          title: Error Message
      type: object
      required:
      - tenant_id
      - validation_id
      - started_at
      - status
      - accuracy_threshold
      title: TemporalValidationResult
      description: Results of temporal accuracy validation.
    TenantResponse:
      properties:
        id:
          type: integer
          title: Id
          description: Tenant ID
        name:
          type: string
          title: Name
          description: Tenant name
        email:
          type: string
          title: Email
          description: Tenant email
        is_active:
          type: boolean
          title: Is Active
          description: Whether tenant is active
        created_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Created At
          description: Creation timestamp
        updated_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Updated At
          description: Last update timestamp
      type: object
      required:
      - id
      - name
      - email
      - is_active
      title: TenantResponse
      description: Response model for tenant information.
    TenantsListResponse:
      properties:
        tenants:
          items:
            $ref: '#/components/schemas/TenantResponse'
          type: array
          title: Tenants
          description: List of tenants
        total:
          type: integer
          title: Total
          description: Total number of tenants
        limit:
          type: integer
          title: Limit
          description: Page limit
        offset:
          type: integer
          title: Offset
          description: Page offset
        has_more:
          type: boolean
          title: Has More
          description: Whether there are more results
      type: object
      required:
      - tenants
      - total
      - limit
      - offset
      - has_more
      title: TenantsListResponse
      description: Response model for tenant list.
    Token:
      properties:
        access_token:
          type: string
          title: Access Token
        refresh_token:
          anyOf:
          - type: string
          - type: 'null'
          title: Refresh Token
        token_type:
          type: string
          title: Token Type
          default: bearer
        expires_in:
          type: integer
          title: Expires In
          default: 3600
      type: object
      required:
      - access_token
      title: Token
      description: JWT token schema.
    TokenRefreshRequest:
      properties:
        refresh_token:
          type: string
          title: Refresh Token
      type: object
      required:
      - refresh_token
      title: TokenRefreshRequest
      description: Token refresh request schema.
    ToolInvocationRequest:
      properties:
        toolName:
          type: string
          title: Toolname
        parameters:
          additionalProperties: true
          type: object
          title: Parameters
        agentId:
          type: string
          title: Agentid
        sessionId:
          anyOf:
          - type: string
          - type: 'null'
          title: Sessionid
      type: object
      required:
      - toolName
      - parameters
      - agentId
      title: ToolInvocationRequest
    TransactionGroup:
      properties:
        group_id:
          type: string
          title: Group Id
        pattern:
          type: string
          title: Pattern
        transaction_count:
          type: integer
          title: Transaction Count
        total_amount:
          type: number
          title: Total Amount
        sample_descriptions:
          items:
            type: string
          type: array
          title: Sample Descriptions
        suggested_category_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Suggested Category Id
        suggested_category_path:
          anyOf:
          - type: string
          - type: 'null'
          title: Suggested Category Path
        avg_confidence:
          type: number
          title: Avg Confidence
        transactions:
          items:
            $ref: '#/components/schemas/TransactionSuggestion'
          type: array
          title: Transactions
      type: object
      required:
      - group_id
      - pattern
      - transaction_count
      - total_amount
      - sample_descriptions
      - suggested_category_id
      - suggested_category_path
      - avg_confidence
      - transactions
      title: TransactionGroup
      description: Group of similar transactions for bulk action.
    TransactionListResponse:
      properties:
        items:
          items:
            $ref: '#/components/schemas/giki_ai_api__domains__transactions__schemas__TransactionResponse'
          type: array
          title: Items
        total:
          type: integer
          title: Total
        skip:
          type: integer
          title: Skip
        limit:
          type: integer
          title: Limit
      type: object
      required:
      - items
      - total
      - skip
      - limit
      title: TransactionListResponse
      description: Model for list of transactions with pagination info
    TransactionStats:
      properties:
        total_transactions:
          type: integer
          title: Total Transactions
        needs_review:
          type: integer
          title: Needs Review
        approved:
          type: integer
          title: Approved
        confidence_distribution:
          $ref: '#/components/schemas/ConfidenceDistribution'
        processing_summary:
          additionalProperties: true
          type: object
          title: Processing Summary
      type: object
      required:
      - total_transactions
      - needs_review
      - approved
      - confidence_distribution
      - processing_summary
      title: TransactionStats
      description: Transaction statistics schema.
    TransactionSuggestion:
      properties:
        id:
          type: integer
          title: Id
        date:
          type: string
          title: Date
        description:
          type: string
          title: Description
        amount:
          type: number
          title: Amount
        suggested_category_id:
          type: integer
          title: Suggested Category Id
        suggested_category_path:
          type: string
          title: Suggested Category Path
        confidence_score:
          type: number
          title: Confidence Score
        needs_review:
          type: boolean
          title: Needs Review
      type: object
      required:
      - id
      - date
      - description
      - amount
      - suggested_category_id
      - suggested_category_path
      - confidence_score
      - needs_review
      title: TransactionSuggestion
      description: Transaction with AI suggestion for review.
    TransactionUpdateRequest:
      properties:
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Amount
        account:
          anyOf:
          - type: string
          - type: 'null'
          title: Account
        transaction_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Transaction Type
        category_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Category Id
      type: object
      title: TransactionUpdateRequest
      description: Request schema for updating transaction details.
    UpdateTenantRequest:
      properties:
        name:
          anyOf:
          - type: string
          - type: 'null'
          title: Name
          description: Tenant name
        email:
          anyOf:
          - type: string
          - type: 'null'
          title: Email
          description: Tenant email
        settings:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Settings
          description: Tenant settings
        is_active:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Active
          description: Whether tenant is active
      type: object
      title: UpdateTenantRequest
      description: Request model for updating a tenant.
    UploadResponse:
      properties:
        upload_id:
          type: string
          title: Upload Id
          description: Unique identifier for the upload process.
        filename:
          type: string
          title: Filename
          description: Name of the uploaded file.
        content_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Content Type
          description: MIME type of the uploaded file.
        size:
          type: integer
          title: Size
          description: Size of the uploaded file in bytes.
        status:
          type: string
          title: Status
          description: Current status of the upload (e.g., PENDING, PROCESSING, COMPLETED,
            FAILED).
          default: PENDING
        message:
          anyOf:
          - type: string
          - type: 'null'
          title: Message
          description: A message providing more details about the upload status.
        headers:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Headers
          description: Detected column headers from the file.
        created_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Created At
          description: Timestamp when the file was uploaded.
      type: object
      required:
      - filename
      - size
      title: UploadResponse
      description: Response after a file is successfully uploaded.
    UserCreate:
      properties:
        username:
          type: string
          title: Username
        email:
          type: string
          format: email
          title: Email
        password:
          type: string
          title: Password
        tenant_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Tenant Id
        is_superuser:
          type: boolean
          title: Is Superuser
          default: false
      type: object
      required:
      - username
      - email
      - password
      title: UserCreate
      description: Schema for creating a new user.
    UserResponse:
      properties:
        id:
          type: integer
          title: Id
          description: User ID
        email:
          type: string
          title: Email
          description: User email
        tenant_id:
          type: integer
          title: Tenant Id
          description: Tenant ID
        is_active:
          type: boolean
          title: Is Active
          description: Whether user is active
        is_verified:
          type: boolean
          title: Is Verified
          description: Whether user is verified
        created_at:
          anyOf:
          - type: string
          - type: 'null'
          title: Created At
          description: Creation timestamp
        last_login:
          anyOf:
          - type: string
          - type: 'null'
          title: Last Login
          description: Last login timestamp
      type: object
      required:
      - id
      - email
      - tenant_id
      - is_active
      - is_verified
      title: UserResponse
      description: Response model for user information.
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
            - type: string
            - type: integer
          type: array
          title: Location
        msg:
          type: string
          title: Message
        type:
          type: string
          title: Error Type
      type: object
      required:
      - loc
      - msg
      - type
      title: ValidationError
    ValueDistribution:
      properties:
        value:
          type: string
          title: Value
          description: The value
        count:
          type: integer
          title: Count
          description: Number of occurrences
        percentage:
          type: number
          title: Percentage
          description: Percentage of total
      type: object
      required:
      - value
      - count
      - percentage
      title: ValueDistribution
      description: Value distribution for a column.
    VendorGrouping:
      properties:
        vendor_name:
          type: string
          title: Vendor Name
        normalized_name:
          type: string
          title: Normalized Name
        transaction_count:
          type: integer
          title: Transaction Count
        total_amount:
          type: number
          title: Total Amount
        debit_count:
          type: integer
          title: Debit Count
        debit_total:
          type: number
          title: Debit Total
        credit_count:
          type: integer
          title: Credit Count
        credit_total:
          type: number
          title: Credit Total
        categories_used:
          items:
            type: string
          type: array
          title: Categories Used
        most_common_category:
          anyOf:
          - type: string
          - type: 'null'
          title: Most Common Category
        has_mapping:
          type: boolean
          title: Has Mapping
        current_mapping:
          anyOf:
          - $ref: '#/components/schemas/VendorMapping'
          - type: 'null'
        sample_transactions:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Sample Transactions
      type: object
      required:
      - vendor_name
      - normalized_name
      - transaction_count
      - total_amount
      - debit_count
      - debit_total
      - credit_count
      - credit_total
      - categories_used
      - most_common_category
      - has_mapping
      - current_mapping
      - sample_transactions
      title: VendorGrouping
      description: Group of transactions by vendor for bulk actions.
    VendorMapping:
      properties:
        id:
          type: integer
          title: Id
        vendor_name:
          type: string
          title: Vendor Name
        normalized_name:
          type: string
          title: Normalized Name
        category_id:
          type: integer
          title: Category Id
        category_path:
          type: string
          title: Category Path
        applies_to_debits:
          type: boolean
          title: Applies To Debits
        applies_to_credits:
          type: boolean
          title: Applies To Credits
        description_pattern:
          anyOf:
          - type: string
          - type: 'null'
          title: Description Pattern
        min_amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Min Amount
        max_amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Max Amount
        confidence_threshold:
          type: number
          title: Confidence Threshold
        business_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Business Type
        notes:
          anyOf:
          - type: string
          - type: 'null'
          title: Notes
        is_active:
          type: boolean
          title: Is Active
        times_applied:
          type: integer
          title: Times Applied
        last_applied:
          anyOf:
          - type: string
            format: date-time
          - type: 'null'
          title: Last Applied
        created_at:
          type: string
          format: date-time
          title: Created At
        created_by:
          type: integer
          title: Created By
        updated_at:
          type: string
          format: date-time
          title: Updated At
      type: object
      required:
      - id
      - vendor_name
      - normalized_name
      - category_id
      - category_path
      - applies_to_debits
      - applies_to_credits
      - description_pattern
      - min_amount
      - max_amount
      - confidence_threshold
      - business_type
      - notes
      - is_active
      - times_applied
      - last_applied
      - created_at
      - created_by
      - updated_at
      title: VendorMapping
      description: Complete vendor mapping information.
    VendorMappingCreate:
      properties:
        vendor_name:
          type: string
          title: Vendor Name
          description: The vendor name to map
        category_id:
          type: integer
          title: Category Id
          description: The category to map to
        applies_to_debits:
          type: boolean
          title: Applies To Debits
          description: Apply to expense transactions
          default: true
        applies_to_credits:
          type: boolean
          title: Applies To Credits
          description: Apply to income transactions
          default: false
        description_pattern:
          anyOf:
          - type: string
          - type: 'null'
          title: Description Pattern
          description: Optional regex pattern for more specific matching
        min_amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Min Amount
          description: Only apply to transactions >= this amount
        max_amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Max Amount
          description: Only apply to transactions <= this amount
        notes:
          anyOf:
          - type: string
          - type: 'null'
          title: Notes
          description: User notes about this mapping
      type: object
      required:
      - vendor_name
      - category_id
      title: VendorMappingCreate
      description: Schema for creating a vendor-to-category mapping.
    VendorMappingResult:
      properties:
        mappings_created:
          type: integer
          title: Mappings Created
        mappings_updated:
          type: integer
          title: Mappings Updated
        transactions_affected:
          type: integer
          title: Transactions Affected
        debits_categorized:
          type: integer
          title: Debits Categorized
        credits_categorized:
          type: integer
          title: Credits Categorized
        conflicts_found:
          type: integer
          title: Conflicts Found
        conflict_details:
          items:
            additionalProperties: true
            type: object
          type: array
          title: Conflict Details
        success:
          type: boolean
          title: Success
        message:
          type: string
          title: Message
      type: object
      required:
      - mappings_created
      - mappings_updated
      - transactions_affected
      - debits_categorized
      - credits_categorized
      - conflicts_found
      - conflict_details
      - success
      - message
      title: VendorMappingResult
      description: Result of applying vendor mappings.
    VendorMappingUpdate:
      properties:
        category_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Category Id
        applies_to_debits:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Applies To Debits
        applies_to_credits:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Applies To Credits
        description_pattern:
          anyOf:
          - type: string
          - type: 'null'
          title: Description Pattern
        min_amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Min Amount
        max_amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Max Amount
        notes:
          anyOf:
          - type: string
          - type: 'null'
          title: Notes
        is_active:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Active
      type: object
      title: VendorMappingUpdate
      description: Schema for updating a vendor mapping.
    ZeroOnboardingStartRequest:
      properties:
        tenant_id:
          type: integer
          title: Tenant Id
        tenant_name:
          type: string
          title: Tenant Name
        contact_email:
          type: string
          title: Contact Email
        business_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Business Type
          description: Type of business (optional)
        industry:
          anyOf:
          - type: string
          - type: 'null'
          title: Industry
          description: Business industry for context-aware categorization
        company_size:
          anyOf:
          - type: string
          - type: 'null'
          title: Company Size
          description: Company size (startup, small, medium, enterprise)
        company_website:
          anyOf:
          - type: string
          - type: 'null'
          title: Company Website
          description: Company website URL for business context analysis
      type: object
      required:
      - tenant_id
      - tenant_name
      - contact_email
      title: ZeroOnboardingStartRequest
      description: Request to start M1 zero-onboarding process.
    giki_ai_api__domains__agents__router__ChatResponse:
      properties:
        success:
          type: boolean
          title: Success
        message:
          type: string
          title: Message
        data:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Data
        suggestions:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Suggestions
        actions:
          anyOf:
          - items:
              additionalProperties: true
              type: object
            type: array
          - type: 'null'
          title: Actions
        processing_time_ms:
          anyOf:
          - type: number
          - type: 'null'
          title: Processing Time Ms
        error:
          anyOf:
          - type: string
          - type: 'null'
          title: Error
      type: object
      required:
      - success
      - message
      title: ChatResponse
      description: Chat response model for frontend.
    giki_ai_api__domains__categories__gl_code_router__GLCodeSuggestionRequest:
      properties:
        category_name:
          type: string
          title: Category Name
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        transaction_amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Transaction Amount
        existing_hierarchy:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Existing Hierarchy
      type: object
      required:
      - category_name
      title: GLCodeSuggestionRequest
    giki_ai_api__domains__categories__review_router__ReviewQueueResponse:
      properties:
        summary:
          $ref: '#/components/schemas/ReviewQueueSummary'
        suggestions:
          items:
            $ref: '#/components/schemas/TransactionSuggestion'
          type: array
          title: Suggestions
        groups:
          items:
            $ref: '#/components/schemas/TransactionGroup'
          type: array
          title: Groups
        uncategorized:
          items:
            $ref: '#/components/schemas/TransactionSuggestion'
          type: array
          title: Uncategorized
      type: object
      required:
      - summary
      - suggestions
      - groups
      - uncategorized
      title: ReviewQueueResponse
      description: Complete review queue for an upload.
    giki_ai_api__domains__categories__router__GLCodeSuggestionRequest:
      properties:
        category_name:
          type: string
          title: Category Name
        category_path:
          type: string
          title: Category Path
        account_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Account Type
      type: object
      required:
      - category_name
      - category_path
      title: GLCodeSuggestionRequest
      description: Schema for GL code suggestion request.
    giki_ai_api__domains__files__schemas__ColumnMapping:
      properties:
        original_name:
          type: string
          title: Original Name
          description: Original column name from the file
        mapped_field:
          type: string
          title: Mapped Field
          description: Target schema field (e.g., 'date', 'description', 'amount')
        confidence:
          type: number
          title: Confidence
          description: Confidence score (0.0 to 1.0)
        reasoning:
          type: string
          title: Reasoning
          description: Human-readable explanation for the mapping
      type: object
      required:
      - original_name
      - mapped_field
      - confidence
      - reasoning
      title: ColumnMapping
      description: Schema mapping for a single column.
    giki_ai_api__domains__intelligence__router__ChatResponse:
      properties:
        response:
          type: string
          title: Response
          description: Agent response
        agent:
          type: string
          title: Agent
          description: Agent name (always Giki)
          default: Giki
        capabilities_used:
          items:
            type: string
          type: array
          title: Capabilities Used
          description: Capabilities used to process the request
          default: []
        data:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Data
          description: Additional data returned by the agent
        success:
          type: boolean
          title: Success
          description: Whether the request was successful
          default: true
      type: object
      required:
      - response
      title: ChatResponse
    giki_ai_api__domains__onboarding__processing_schemas__ColumnMapping:
      properties:
        original_name:
          type: string
          title: Original Name
          description: Original column name in file
        mapped_field:
          type: string
          title: Mapped Field
          description: Mapped field name
        confidence:
          type: number
          title: Confidence
          description: Mapping confidence (0.0 to 1.0)
        method:
          $ref: '#/components/schemas/MappingMethod'
          description: Mapping method used
      type: object
      required:
      - original_name
      - mapped_field
      - confidence
      - method
      title: ColumnMapping
      description: Column mapping details.
    giki_ai_api__domains__transactions__router__ReviewQueueResponse:
      properties:
        review_queue:
          items:
            $ref: '#/components/schemas/ReviewQueueItem'
          type: array
          title: Review Queue
        queue_size:
          type: integer
          title: Queue Size
        average_confidence:
          type: number
          title: Average Confidence
        recommendations:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Recommendations
      type: object
      required:
      - review_queue
      - queue_size
      - average_confidence
      title: ReviewQueueResponse
      description: Review queue response schema.
    giki_ai_api__domains__transactions__router__TransactionResponse:
      properties:
        id:
          type: string
          title: Id
        date:
          type: string
          title: Date
        description:
          type: string
          title: Description
        amount:
          type: number
          title: Amount
        account:
          anyOf:
          - type: string
          - type: 'null'
          title: Account
        transaction_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Transaction Type
        category_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Category Id
        category_path:
          anyOf:
          - type: string
          - type: 'null'
          title: Category Path
        ai_suggested_category:
          anyOf:
          - type: integer
          - type: 'null'
          title: Ai Suggested Category
        ai_suggested_category_path:
          anyOf:
          - type: string
          - type: 'null'
          title: Ai Suggested Category Path
        ai_category_confidence:
          anyOf:
          - type: number
          - type: 'null'
          title: Ai Category Confidence
        is_categorized:
          type: boolean
          title: Is Categorized
          default: false
        is_user_modified:
          type: boolean
          title: Is User Modified
          default: false
        user_corrected:
          type: boolean
          title: User Corrected
          default: false
        entity_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Entity Id
        upload_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Upload Id
        status:
          type: string
          title: Status
          default: uncategorized
        vendor_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Vendor Name
        original_category:
          anyOf:
          - type: string
          - type: 'null'
          title: Original Category
        ai_category:
          anyOf:
          - type: string
          - type: 'null'
          title: Ai Category
      type: object
      required:
      - id
      - date
      - description
      - amount
      title: TransactionResponse
      description: Transaction response schema.
    giki_ai_api__domains__transactions__schemas__TransactionResponse:
      properties:
        date:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Date
          description: Transaction date
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
          description: Transaction description
        amount:
          anyOf:
          - type: number
          - type: 'null'
          title: Amount
          description: Transaction amount
        account:
          anyOf:
          - type: string
          - type: 'null'
          title: Account
          description: Account identifier
        tags:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Tags
          description: List of tags
        transaction_type:
          anyOf:
          - type: string
          - type: 'null'
          title: Transaction Type
          description: Type of transaction (e.g., expense, income)
        is_invoice:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Invoice
          description: Is this transaction an invoice?
          default: false
        invoice_number:
          anyOf:
          - type: string
          - type: 'null'
          title: Invoice Number
          description: Invoice number, if applicable.
        invoice_due_date:
          anyOf:
          - type: string
            format: date
          - type: 'null'
          title: Invoice Due Date
          description: Due date for the invoice.
        invoice_status:
          anyOf:
          - type: string
          - type: 'null'
          title: Invoice Status
          description: Status of the invoice (e.g., Draft, Sent, Paid).
        related_invoice_id:
          anyOf:
          - type: string
          - type: 'null'
          title: Related Invoice Id
          description: ID of a related invoice (e.g., if this is a payment).
        invoice_details:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          title: Invoice Details
          description: Additional structured details for the invoice (e.g., line items).
        agent_verified:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Agent Verified
          description: Indicates if an agent has verified this invoice/transaction.
        is_paid:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Paid
          description: Indicates if the invoice has been paid.
        id:
          type: string
          title: Id
        category_id:
          anyOf:
          - type: integer
          - type: 'null'
          title: Category Id
        category_path:
          anyOf:
          - type: string
          - type: 'null'
          title: Category Path
        ai_suggested_category:
          anyOf:
          - type: string
          - type: 'null'
          title: Ai Suggested Category
        ai_suggested_category_path:
          anyOf:
          - type: string
          - type: 'null'
          title: Ai Suggested Category Path
        ai_category_confidence:
          anyOf:
          - type: number
          - type: 'null'
          title: Ai Category Confidence
        is_categorized:
          type: boolean
          title: Is Categorized
          default: false
        is_user_modified:
          type: boolean
          title: Is User Modified
          default: false
        upload_id:
          type: string
          title: Upload Id
        tenant_id:
          type: integer
          title: Tenant Id
        created_at:
          type: string
          format: date-time
          title: Created At
        updated_at:
          type: string
          format: date-time
          title: Updated At
      type: object
      required:
      - id
      - upload_id
      - tenant_id
      - created_at
      - updated_at
      title: TransactionResponse
      description: Model for transaction response, includes ID, timestamps, and categorization
        details
  securitySchemes:
    JWTBearer:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from /auth/token endpoint
servers:
- url: http://localhost:8000
  description: Development server
- url: https://api.giki.ai
  description: Production server
security:
- JWTBearer: []
