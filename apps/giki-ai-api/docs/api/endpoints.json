[{"path": "/api/v1/system/performance", "method": "GET", "summary": "Get System Performance", "tags": []}, {"path": "/api/v1/agent/command", "method": "POST", "summary": "Process Agent Command", "tags": []}, {"path": "/health/env", "method": "GET", "summary": "Environment Check", "tags": []}, {"path": "/health/db", "method": "GET", "summary": "Database Health Check", "tags": []}, {"path": "/health/db/reset", "method": "POST", "summary": "Reset Database Configuration", "tags": []}, {"path": "/api/v1/admin/rate-limit-stats", "method": "GET", "summary": "Get Rate Limit Stats", "tags": []}, {"path": "/api/v1/admin/cache-stats", "method": "GET", "summary": "Get Cache Stats", "tags": []}, {"path": "/api/v1/admin/cache-clear", "method": "POST", "summary": "<PERSON>ache", "tags": []}, {"path": "/api/v1/admin/cache-clear-pattern", "method": "POST", "summary": "<PERSON> <PERSON><PERSON>", "tags": []}, {"path": "/api/v1/auth/login", "method": "POST", "summary": "<PERSON><PERSON>", "tags": ["Authentication"]}, {"path": "/api/v1/auth/token", "method": "POST", "summary": "<PERSON><PERSON>", "tags": ["Authentication"]}, {"path": "/api/v1/auth/me", "method": "GET", "summary": "Read Users Me", "tags": ["Authentication"]}, {"path": "/api/v1/auth/logout", "method": "POST", "summary": "Logout", "tags": ["Authentication"]}, {"path": "/api/v1/auth/debug-login", "method": "POST", "summary": "Debug Login", "tags": ["Authentication"]}, {"path": "/api/v1/auth/refresh", "method": "POST", "summary": "Refresh Access Token", "tags": ["Authentication"]}, {"path": "/api/v1/auth/verify", "method": "GET", "summary": "Verify <PERSON>", "tags": ["Authentication"]}, {"path": "/api/v1/auth/register", "method": "POST", "summary": "Register User", "tags": ["Authentication"]}, {"path": "/api/v1/auth/public-register", "method": "POST", "summary": "Public Register User", "tags": ["Authentication"]}, {"path": "/api/v1/auth/quick-actions", "method": "GET", "summary": "Get Quick Actions", "tags": ["Authentication"]}, {"path": "/api/v1/auth/cache-stats", "method": "GET", "summary": "Get Cache Statistics", "tags": ["Authentication"]}, {"path": "/api/v1/auth/clear-cache", "method": "POST", "summary": "Clear Authentication Cache", "tags": ["Authentication"]}, {"path": "/auth/login", "method": "POST", "summary": "<PERSON><PERSON>", "tags": ["Authentication Test"]}, {"path": "/auth/token", "method": "POST", "summary": "<PERSON><PERSON>", "tags": ["Authentication Test"]}, {"path": "/auth/me", "method": "GET", "summary": "Read Users Me", "tags": ["Authentication Test"]}, {"path": "/auth/logout", "method": "POST", "summary": "Logout", "tags": ["Authentication Test"]}, {"path": "/auth/debug-login", "method": "POST", "summary": "Debug Login", "tags": ["Authentication Test"]}, {"path": "/auth/refresh", "method": "POST", "summary": "Refresh Access Token", "tags": ["Authentication Test"]}, {"path": "/auth/verify", "method": "GET", "summary": "Verify <PERSON>", "tags": ["Authentication Test"]}, {"path": "/auth/register", "method": "POST", "summary": "Register User", "tags": ["Authentication Test"]}, {"path": "/auth/public-register", "method": "POST", "summary": "Public Register User", "tags": ["Authentication Test"]}, {"path": "/auth/quick-actions", "method": "GET", "summary": "Get Quick Actions", "tags": ["Authentication Test"]}, {"path": "/auth/cache-stats", "method": "GET", "summary": "Get Cache Statistics", "tags": ["Authentication Test"]}, {"path": "/auth/clear-cache", "method": "POST", "summary": "Clear Authentication Cache", "tags": ["Authentication Test"]}, {"path": "/api/v1/ai/unified/parse-report", "method": "POST", "summary": "Parse Report Query", "tags": ["Unified AI", "Unified AI"]}, {"path": "/api/v1/ai/unified/categorize", "method": "POST", "summary": "Categorize Transaction", "tags": ["Unified AI", "Unified AI"]}, {"path": "/api/v1/ai/unified/categorize/batch", "method": "POST", "summary": "Categorize Batch", "tags": ["Unified AI", "Unified AI"]}, {"path": "/api/v1/ai/unified/conversation", "method": "POST", "summary": "Process Conversation", "tags": ["Unified AI", "Unified AI"]}, {"path": "/api/v1/ai/unified/query", "method": "POST", "summary": "Process Query", "tags": ["Unified AI", "Unified AI"]}, {"path": "/api/v1/ai/unified/capabilities", "method": "GET", "summary": "Get Capabilities", "tags": ["Unified AI", "Unified AI"]}, {"path": "/api/v1/transactions/fast", "method": "GET", "summary": "List Transactions Fast", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/", "method": "GET", "summary": "List Transactions", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/review-queue", "method": "GET", "summary": "Get Review Queue", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/stats", "method": "GET", "summary": "Get Transaction Stats", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/{transaction_id}", "method": "GET", "summary": "Get Transaction", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/{transaction_id}", "method": "DELETE", "summary": "Delete Transaction", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/{transaction_id}", "method": "PUT", "summary": "Update Transaction", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/batch/category", "method": "PUT", "summary": "Update Batch Categories", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/{transaction_id}/category", "method": "PUT", "summary": "Update Transaction Category", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/{transaction_id}/approve-ai-suggestion", "method": "POST", "summary": "Approve Ai Suggestion", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/bulk-approve", "method": "POST", "summary": "Bulk Approve Transactions", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/transactions/improve-categorization", "method": "POST", "summary": "Improve Categorization", "tags": ["Transactions", "Transactions"]}, {"path": "/api/v1/categories", "method": "GET", "summary": "Read Tenant Categories", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories", "method": "POST", "summary": "Create Category", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/with-counts", "method": "GET", "summary": "Read Tenant Categories With Counts", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/{category_id}", "method": "GET", "summary": "Read Category", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/{category_id}", "method": "PUT", "summary": "Update Category", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/{category_id}", "method": "DELETE", "summary": "Delete Category", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/validate-gl-code", "method": "POST", "summary": "Validate Gl Code", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/suggest-gl-codes", "method": "POST", "summary": "Suggest Gl Codes", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/{category_id}/gl-mapping", "method": "PUT", "summary": "Update Category Gl Mapping", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/bulk-gl-update", "method": "POST", "summary": "Bulk Update Gl Mappings", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/gl-mappings/export", "method": "GET", "summary": "Export Gl Mappings", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/learn-from-onboarding", "method": "POST", "summary": "Learn Categories From Onboarding", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/hierarchy", "method": "GET", "summary": "Get Category Hierarchy", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/create-multilevel-hierarchies", "method": "POST", "summary": "Create Multilevel Hierarchies", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/dynamic-hierarchies", "method": "POST", "summary": "Build Dynamic Category Hierarchies", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/validate/quick", "method": "GET", "summary": "Quick Mis Validation", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/validate/comprehensive", "method": "GET", "summary": "Comprehensive Mis Validation", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/validate/structure-health", "method": "GET", "summary": "Get Structure Health Metrics", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/setup-standard-hierarchy", "method": "POST", "summary": "Setup Standard Hierarchy", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/fix-hierarchy-integrity", "method": "POST", "summary": "Fix Hierarchy Integrity", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/convert-flat-hierarchies", "method": "POST", "summary": "Convert Flat Hierarchies", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/clear-test-data", "method": "POST", "summary": "Clear Test Data", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/reset-to-clean-hierarchy", "method": "POST", "summary": "Reset To Clean Hierarchy", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/hierarchical-results/{upload_id}", "method": "GET", "summary": "Get Hierarchical Results", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/enhancement-analysis/{upload_id}", "method": "GET", "summary": "Analyze Enhancements", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/vendors/detect/{upload_id}", "method": "GET", "summary": "Detect Vendor Opportunities", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/vendors/apply-intelligent-mappings", "method": "POST", "summary": "Apply Intelligent Vendor Mappings", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/vendors/lookup-preview", "method": "GET", "summary": "Preview Vendor Lookup", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/vendors/groupings", "method": "GET", "summary": "Get Vendor Groupings", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/vendors/user-mapping", "method": "POST", "summary": "Create User Vendor Mapping", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/vendors/bulk-user-mappings", "method": "POST", "summary": "Create Bulk User Vendor Mappings", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/vendors/user-mappings", "method": "GET", "summary": "Get User Vendor Mappings", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/vendors/user-mapping/{mapping_id}", "method": "PUT", "summary": "Update User Vendor Mapping", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/vendors/user-mapping/{mapping_id}", "method": "DELETE", "summary": "Delete User Vendor Mapping", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/metrics/categorization", "method": "GET", "summary": "Get Categorization Metrics", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/metrics/trends", "method": "GET", "summary": "Get Categorization Trends", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/metrics/confidence-insights", "method": "GET", "summary": "Get Confidence Insights", "tags": ["Categories", "Categories"]}, {"path": "/api/v1/categories/review/queue/{upload_id}", "method": "GET", "summary": "Get Review Queue", "tags": ["Review Queue", "Review Queue"]}, {"path": "/api/v1/categories/review/bulk-action", "method": "POST", "summary": "Process Bulk Review Action", "tags": ["Review Queue", "Review Queue"]}, {"path": "/api/v1/categories/review/group-action", "method": "POST", "summary": "Process Group Review Action", "tags": ["Review Queue", "Review Queue"]}, {"path": "/api/v1/categories/review/accept-all-suggestions", "method": "POST", "summary": "Accept All Suggestions", "tags": ["Review Queue", "Review Queue"]}, {"path": "/api/v1/gl-codes/hierarchy", "method": "GET", "summary": "Get Gl Code Hierarchy", "tags": ["GL Code Management", "GL Code Management"]}, {"path": "/api/v1/gl-codes/mappings", "method": "POST", "summary": "Bulk Update Gl Mappings", "tags": ["GL Code Management", "GL Code Management"]}, {"path": "/api/v1/gl-codes/validate/{gl_code}", "method": "GET", "summary": "Validate Gl Code", "tags": ["GL Code Management", "GL Code Management"]}, {"path": "/api/v1/gl-codes/suggest", "method": "POST", "summary": "Get Gl Code Suggestions", "tags": ["GL Code Management", "GL Code Management"]}, {"path": "/api/v1/gl-codes/analytics", "method": "GET", "summary": "Get Gl Code Analytics", "tags": ["GL Code Management", "GL Code Management"]}, {"path": "/api/v1/gl-codes/auto-assign", "method": "POST", "summary": "Auto Assign Gl Codes", "tags": ["GL Code Management", "GL Code Management"]}, {"path": "/api/v1/gl-codes/export/{format}", "method": "GET", "summary": "Export Gl Mappings", "tags": ["GL Code Management", "GL Code Management"]}, {"path": "/api/v1/gl-codes/import/chart-of-accounts", "method": "POST", "summary": "Import Chart Of Accounts", "tags": ["GL Code Management", "GL Code Management"]}, {"path": "/api/v1/accuracy/tests", "method": "POST", "summary": "Create Accuracy Test", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/tests", "method": "GET", "summary": "List Accuracy Tests", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/tests/{test_id}", "method": "GET", "summary": "Get Accuracy Test", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/tests/{test_id}/run", "method": "POST", "summary": "Run Accuracy Test", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/tests/{test_id}/metrics", "method": "GET", "summary": "Get Accuracy Metrics", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/tests/{test_id}/judgments", "method": "GET", "summary": "Get Ai Judgments", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/schemas", "method": "GET", "summary": "List Category Schemas", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/schemas", "method": "POST", "summary": "Create Category Schema", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/schemas/import", "method": "POST", "summary": "Import Category Schema", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/schemas/{schema_id}", "method": "GET", "summary": "Get Category Schema", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/tests/{test_id}/report", "method": "GET", "summary": "Get Accuracy Report", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/temporal/validate/{tenant_id}", "method": "POST", "summary": "Run Temporal Accuracy Validation", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/temporal/status/{tenant_id}", "method": "GET", "summary": "Get Temporal Validation Status", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/m2/readiness/{tenant_id}", "method": "GET", "summary": "Check M2 Readiness", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/m2/validate/{tenant_id}", "method": "POST", "summary": "Run M2 Validation", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/m2/report/{tenant_id}", "method": "GET", "summary": "Generate M2 Report", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/dashboard", "method": "GET", "summary": "Get Accuracy Dashboard", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/temporal-data", "method": "GET", "summary": "Get Temporal Accuracy Data", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/category-breakdown", "method": "GET", "summary": "Get Category Accuracy Breakdown", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/accuracy/milestones", "method": "GET", "summary": "Get Milestone Status", "tags": ["Accuracy", "accuracy"]}, {"path": "/api/v1/agents/chat", "method": "POST", "summary": "Chat With Agent", "tags": ["Agents", "Agents"]}, {"path": "/api/v1/agents/commands", "method": "GET", "summary": "Get Available Commands", "tags": ["Agents", "Agents"]}, {"path": "/api/v1/agents/status", "method": "GET", "summary": "Get Agent Status", "tags": ["Agents", "Agents"]}, {"path": "/api/v1/agents/suggestions", "method": "POST", "summary": "Get Command Suggestions", "tags": ["Agents", "Agents"]}, {"path": "/api/v1/dashboard/metrics", "method": "GET", "summary": "Get Dashboard Metrics", "tags": ["Dashboard", "Dashboard"]}, {"path": "/api/v1/dashboard/recent-transactions", "method": "GET", "summary": "Get Recent Transactions", "tags": ["Dashboard", "Dashboard"]}, {"path": "/api/v1/dashboard/category-breakdown", "method": "GET", "summary": "Get Category Breakdown", "tags": ["Dashboard", "Dashboard"]}, {"path": "/api/v1/intelligence/detect-accounting-system", "method": "POST", "summary": "Detect Accounting System", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/extract-entities", "method": "POST", "summary": "Extract Entities", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/process-amount", "method": "POST", "summary": "Process Amount", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/normalize-descriptions", "method": "POST", "summary": "Normalize Descriptions", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/analyze-batch", "method": "POST", "summary": "<PERSON><PERSON><PERSON>", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/health", "method": "GET", "summary": "Health Check", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/agent/customer/query", "method": "POST", "summary": "Process Customer Query", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/agent/customer/audio", "method": "POST", "summary": "Process Audio Query", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/agent/data/process", "method": "POST", "summary": "Process Data Operation", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/accuracy-metrics", "method": "GET", "summary": "Get Accuracy Metrics", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/accuracy-metrics", "method": "POST", "summary": "Get Accuracy Metrics For Onboarding", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/intelligence/chat", "method": "POST", "summary": "Chat With Giki", "tags": ["Intelligence", "intelligence"]}, {"path": "/api/v1/adk/api/v1/adk/agents/discover", "method": "GET", "summary": "Discover Agents", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/adk/api/v1/adk/agents/{agent_id}/sessions", "method": "POST", "summary": "Start Agent Session", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/adk/api/v1/adk/agents/transfer", "method": "POST", "summary": "Transfer To Agent", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/adk/api/v1/adk/agents/{agent_id}/memory", "method": "GET", "summary": "Load Agent Memory", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/adk/api/v1/adk/agents/{agent_id}/memory/preload", "method": "POST", "summary": "Preload Memory", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/adk/api/v1/adk/tools/invoke", "method": "POST", "summary": "Invoke <PERSON><PERSON>", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/adk/api/v1/adk/network/status", "method": "GET", "summary": "Get Agent Network Status", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/adk/api/v1/adk/sessions/{session_id}/end", "method": "POST", "summary": "End Agent Session", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/adk/api/v1/adk/agents/{agent_id}/start", "method": "POST", "summary": "Start Agent", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/adk/api/v1/adk/health", "method": "GET", "summary": "Health Check", "tags": ["ADK Agents", "ADK Agents"]}, {"path": "/api/v1/files/export-transactions", "method": "GET", "summary": "Export Transactions To Excel", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files", "method": "GET", "summary": "Get Uploads", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/processing-status", "method": "GET", "summary": "Get Processing Status", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}", "method": "GET", "summary": "Get Upload", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/upload", "method": "POST", "summary": "Upload Files", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}/schema", "method": "GET", "summary": "Get File Schema", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/upload-production-data", "method": "POST", "summary": "Upload Production Files", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}/columns", "method": "GET", "summary": "Get Upload Columns", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}/schema-interpretation", "method": "GET", "summary": "Get Schema Interpretation", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}/map", "method": "POST", "summary": "Process Mapped File", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}/transactions", "method": "GET", "summary": "Get Transactions For Upload", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}/interpretation", "method": "GET", "summary": "Get Interpretation Results", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}/interpretation/confirm", "method": "POST", "summary": "Confirm Interpretation And Reprocess", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}/status", "method": "GET", "summary": "Get Upload Status", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/{upload_id}/processing-details", "method": "GET", "summary": "Get Processing Details", "tags": ["Files", "Files", "Upload"]}, {"path": "/api/v1/files/enhanced/upload/{upload_id}/start-enhanced-processing", "method": "POST", "summary": "Start Enhanced Processing", "tags": ["Enhanced Files", "Enhanced Files"]}, {"path": "/api/v1/files/enhanced/upload/{upload_id}/status", "method": "GET", "summary": "Get Enhanced Status", "tags": ["Enhanced Files", "Enhanced Files"]}, {"path": "/api/v1/files/enhanced/upload/{upload_id}/results", "method": "GET", "summary": "Get Processing Results", "tags": ["Enhanced Files", "Enhanced Files"]}, {"path": "/api/v1/files/enhanced/upload/{upload_id}/export/{format}", "method": "GET", "summary": "Export Results", "tags": ["Enhanced Files", "Enhanced Files"]}, {"path": "/api/v1/files/enhanced/upload/{upload_id}/feedback", "method": "POST", "summary": "Submit Processing Feedback", "tags": ["Enhanced Files", "Enhanced Files"]}, {"path": "/api/v1/files/enhanced/upload/{upload_id}/cleanup", "method": "DELETE", "summary": "Cleanup Processing Data", "tags": ["Enhanced Files", "Enhanced Files"]}, {"path": "/api/v1/reports/spending-by-category", "method": "GET", "summary": "Get Spending By Category", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/spending-by-entity", "method": "GET", "summary": "Get Spending By Entity", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/income-expense-summary", "method": "GET", "summary": "Get Income Expense Summary", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/summary", "method": "GET", "summary": "Get Financial Summary", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/monthly-trends", "method": "GET", "summary": "Get Monthly Trends", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/custom/generate", "method": "POST", "summary": "Generate Custom Report", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/custom/save", "method": "POST", "summary": "Save Custom Report", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/custom/list", "method": "GET", "summary": "List Custom Reports", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/custom/{report_id}", "method": "GET", "summary": "Get Custom Report", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/custom/{report_id}", "method": "DELETE", "summary": "Delete Custom Report", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/export/excel", "method": "POST", "summary": "Export To Excel", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/export/pdf", "method": "POST", "summary": "Export To Pdf", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/export/csv", "method": "POST", "summary": "Export To Csv", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/reports/category-breakdown", "method": "GET", "summary": "Get Category Breakdown", "tags": ["Reports", "Reports"]}, {"path": "/api/v1/exports/formats", "method": "GET", "summary": "Get available export formats", "tags": ["Exports", "exports"]}, {"path": "/api/v1/exports/readiness/{format_id}", "method": "GET", "summary": "Check export readiness", "tags": ["Exports", "exports"]}, {"path": "/api/v1/exports/download", "method": "POST", "summary": "Export transactions", "tags": ["Exports", "exports"]}, {"path": "/api/v1/exports/quickbooks", "method": "POST", "summary": "Export to QuickBooks", "tags": ["Exports", "exports"]}, {"path": "/api/v1/exports/zoho-books", "method": "POST", "summary": "Export to Zoho Books", "tags": ["Exports", "exports"]}, {"path": "/api/v1/exports/tally-prime", "method": "POST", "summary": "Export to Tally Prime", "tags": ["Exports", "exports"]}, {"path": "/api/v1/system/performance/summary", "method": "GET", "summary": "Get Performance Summary", "tags": ["System Monitoring", "Performance Monitoring"]}, {"path": "/api/v1/system/performance/status", "method": "GET", "summary": "Get System Status", "tags": ["System Monitoring", "Performance Monitoring"]}, {"path": "/api/v1/system/performance/health", "method": "GET", "summary": "Get Health Check", "tags": ["System Monitoring", "Performance Monitoring"]}, {"path": "/api/v1/system/performance/endpoints", "method": "GET", "summary": "Get Endpoint Performance", "tags": ["System Monitoring", "Performance Monitoring"]}, {"path": "/api/v1/system/performance/reset", "method": "POST", "summary": "Reset Performance Metrics", "tags": ["System Monitoring", "Performance Monitoring"]}, {"path": "/api/v1/system/performance/alerts", "method": "GET", "summary": "Get Performance Alerts", "tags": ["System Monitoring", "Performance Monitoring"]}, {"path": "/api/v1/monitoring/performance/metrics", "method": "GET", "summary": "Get Performance Metrics", "tags": ["Performance Monitoring", "Monitoring"]}, {"path": "/api/v1/monitoring/performance/clear", "method": "POST", "summary": "Clear Performance Metrics", "tags": ["Performance Monitoring", "Monitoring"]}, {"path": "/api/v1/monitoring/performance/health", "method": "GET", "summary": "Get Performance Health", "tags": ["Performance Monitoring", "Monitoring"]}, {"path": "/api/v1/admin/health", "method": "GET", "summary": "Get System Health", "tags": ["Admin", "Admin"]}, {"path": "/api/v1/admin/tenants", "method": "POST", "summary": "Create Tenant", "tags": ["Admin", "Admin"]}, {"path": "/api/v1/admin/tenants", "method": "GET", "summary": "Get Tenants", "tags": ["Admin", "Admin"]}, {"path": "/api/v1/admin/tenants/{tenant_id}", "method": "PUT", "summary": "Update Tenant", "tags": ["Admin", "Admin"]}, {"path": "/api/v1/admin/tenants/{tenant_id}/users", "method": "POST", "summary": "Create User", "tags": ["Admin", "Admin"]}, {"path": "/api/v1/admin/users/{user_id}", "method": "GET", "summary": "Get User", "tags": ["Admin", "Admin"]}, {"path": "/api/v1/admin/users/{user_id}/deactivate", "method": "POST", "summary": "Deactivate User", "tags": ["Admin", "Admin"]}, {"path": "/api/v1/admin/database/migrate", "method": "POST", "summary": "Run Database Migration", "tags": ["Admin", "Admin"]}, {"path": "/api/v1/admin/status", "method": "GET", "summary": "Admin Status", "tags": ["Admin", "Admin"]}, {"path": "/api/v1/onboarding/status", "method": "GET", "summary": "Get Onboarding Status", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/{onboarding_id}/progress", "method": "GET", "summary": "Get Onboarding Progress", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/start", "method": "POST", "summary": "Start Onboarding", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/upload-historical-data", "method": "POST", "summary": "Upload Historical Data", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/upload-status/{upload_id}", "method": "GET", "summary": "Get Upload Status", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/batch-upload-files", "method": "POST", "summary": "Batch Upload Files", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/interpret-columns", "method": "POST", "summary": "Interpret File Columns", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/build-rag-corpus", "method": "POST", "summary": "Build Rag Corpus", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/validate-temporal-accuracy", "method": "POST", "summary": "Validate Temporal Accuracy", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/validation-results/{validation_id}", "method": "GET", "summary": "Get Validation Results", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/validation-progress/{validation_id}", "method": "GET", "summary": "Get Validation Progress", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/approve-for-production", "method": "POST", "summary": "Approve For Production", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/sample-data", "method": "GET", "summary": "Get Sample Data", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/diagnose-schema/{tenant_id}", "method": "GET", "summary": "Diagnose Schema Mappings", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/upload-with-reporting", "method": "POST", "summary": "Upload File With Detailed Reporting", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/file-processing-report/{upload_id}", "method": "GET", "summary": "Get File Processing Report", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/column-statistics/{upload_id}", "method": "GET", "summary": "Get Column Statistics", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/row-details/{upload_id}", "method": "POST", "summary": "Get Row Processing Details", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/schema-mapping-report/{tenant_id}", "method": "GET", "summary": "Get Schema Mapping Report", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/recent-processing-reports", "method": "GET", "summary": "Get Recent Processing Reports", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/start-zero", "method": "POST", "summary": "Start Zero Onboarding", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/start-schema-only", "method": "POST", "summary": "Start Schema Only Onboarding", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/import-schema", "method": "POST", "summary": "Import Category Schema", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/business-context", "method": "GET", "summary": "Get Business Context", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/business-context", "method": "POST", "summary": "Save Business Context", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/mis/setup", "method": "POST", "summary": "Unified Mis Setup", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/mis/detect-enhancements", "method": "POST", "summary": "Detect Enhancements", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/mis/setup/{setup_id}/complete", "method": "POST", "summary": "Complete Mis Setup", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/mis/setup/{setup_id}", "method": "GET", "summary": "Get Mis Setup Status", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/mis/enhance/{setup_id}", "method": "POST", "summary": "Apply Mis Enhancement", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/mis/enhancement-recommendations", "method": "GET", "summary": "Get Enhancement Recommendations", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/onboarding/mis/detect-enhancements-debug", "method": "POST", "summary": "Detect Enhancements Debug", "tags": ["Onboarding", "Onboarding"]}, {"path": "/api/v1/progress/{task_id}", "method": "GET", "summary": "Stream Progress", "tags": ["Progress", "Progress"]}, {"path": "/api/v1/status/{task_id}", "method": "GET", "summary": "Get Task Status", "tags": ["Progress", "Progress"]}, {"path": "/api/v1/admin/cache/stats", "method": "GET", "summary": "Get Cache Stats", "tags": ["Admin", "<PERSON><PERSON>", "admin", "cache"]}, {"path": "/api/v1/admin/cache/invalidate/tenant/{tenant_id}", "method": "POST", "summary": "Invalidate Ten<PERSON>", "tags": ["Admin", "<PERSON><PERSON>", "admin", "cache"]}, {"path": "/api/v1/admin/cache/health", "method": "GET", "summary": "Cache Health Check", "tags": ["Admin", "<PERSON><PERSON>", "admin", "cache"]}, {"path": "/api/v1/admin/cache/performance", "method": "GET", "summary": "Get Cache Performance Metrics", "tags": ["Admin", "<PERSON><PERSON>", "admin", "cache"]}, {"path": "/api/v1/cache/stats", "method": "GET", "summary": "Get Cache Stats", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"path": "/api/v1/hierarchy/customers/{customer_id}/hierarchy", "method": "GET", "summary": "Get Customer Hierarchy", "tags": ["Hierarchy", "Hierarchy"]}, {"path": "/", "method": "GET", "summary": "Root", "tags": ["root"]}]