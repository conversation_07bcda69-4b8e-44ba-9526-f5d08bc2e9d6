[{"path": "/health", "methods": ["get"]}, {"path": "/api/v1/auth/token", "methods": ["post"]}, {"path": "/api/v1/auth/refresh", "methods": ["post"]}, {"path": "/api/v1/auth/logout", "methods": ["post"]}, {"path": "/api/v1/uploads", "methods": ["get", "post"]}, {"path": "/api/v1/uploads/{upload_id}", "methods": ["get"]}, {"path": "/api/v1/uploads/{upload_id}/columns", "methods": ["get"]}, {"path": "/api/v1/uploads/{upload_id}/map", "methods": ["post"]}, {"path": "/api/v1/uploads/{upload_id}/transactions", "methods": ["get"]}, {"path": "/api/v1/uploads/{upload_id}/interpretation", "methods": ["get"]}, {"path": "/api/v1/uploads/{upload_id}/interpretation/confirm", "methods": ["post"]}, {"path": "/api/v1/ingestion/upload", "methods": ["post"]}, {"path": "/api/v1/chat", "methods": ["post"]}, {"path": "/api/v1/chat/sessions/{session_id_str}/history", "methods": ["get"]}, {"path": "/api/v1/chat/sessions", "methods": ["get"]}, {"path": "/api/v1/categories", "methods": ["get", "post"]}, {"path": "/api/v1/categories/{category_id}", "methods": ["delete", "get", "put"]}, {"path": "/api/v1/transactions/", "methods": ["get"]}, {"path": "/api/v1/transactions/{transaction_id}", "methods": ["get"]}, {"path": "/api/v1/transactions/july-data", "methods": ["get"]}, {"path": "/api/v1/transactions/{transaction_id}/category", "methods": ["put"]}, {"path": "/api/v1/transactions/batch/category", "methods": ["put"]}, {"path": "/api/v1/reports/spending-by-category", "methods": ["get"]}, {"path": "/api/v1/reports/spending-by-entity", "methods": ["get"]}, {"path": "/api/v1/reports/income-expense-summary", "methods": ["get"]}, {"path": "/api/v1/tenants", "methods": ["get", "post"]}, {"path": "/api/v1/tenants/{tenant_id}", "methods": ["get", "put"]}, {"path": "/api/v1/compliance-data", "methods": ["get", "post"]}, {"path": "/api/v1/compliance-data/{compliance_data_id}", "methods": ["get", "put"]}, {"path": "/api/v1/hierarchy-inference/infer-hierarchy", "methods": ["post"]}, {"path": "/api/v1/tenants/{tenant_id}/inferred-hierarchies/", "methods": ["get", "post"]}, {"path": "/api/v1/tenants/{tenant_id}/inferred-hierarchies/{hierarchy_id}", "methods": ["get"]}, {"path": "/api/v1/tenants/{tenant_id}/entities", "methods": ["get", "post"]}, {"path": "/api/v1/entities/{entity_id}", "methods": ["delete", "get", "put"]}, {"path": "/api/v1/rag-corpus", "methods": ["get", "post"]}, {"path": "/api/v1/rag-corpus/{corpus_id}", "methods": ["get"]}, {"path": "/api/v1/rag-corpus/{corpus_id}/enrich", "methods": ["post"]}, {"path": "/api/v1/rag-corpus/{corpus_id}/query", "methods": ["post"]}, {"path": "/api/v1/rag-corpus/{corpus_id}/files", "methods": ["get"]}, {"path": "/api/v1/rag-corpus/{corpus_id}/enrichment-history", "methods": ["get"]}, {"path": "/api/v1/admin/run-migrations", "methods": ["post"]}, {"path": "/api/v1/admin/database-status", "methods": ["get"]}, {"path": "/api/v1/hierarchy/customers/{customer_id}/hierarchy", "methods": ["get"]}, {"path": "/", "methods": ["get"]}]