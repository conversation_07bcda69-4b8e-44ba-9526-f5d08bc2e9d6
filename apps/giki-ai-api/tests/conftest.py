"""
Test configuration and fixtures for giki.ai API testing.

Provides essential fixtures for MIS customer journey validation and integration testing.
"""

import asyncio

import pytest
from httpx import AsyncClient


# Removed custom event_loop fixture to avoid deprecation warning and conflicts with pytest-asyncio


@pytest.fixture
async def async_client():
    """
    Create AsyncClient for testing against the running API server.
    
    This fixture connects to the actual running API server at localhost:8000
    for integration testing and customer journey validation.
    """
    async with AsyncClient(
        base_url="http://localhost:8000",
        timeout=30.0,  # Extended timeout for MIS operations
        follow_redirects=True
    ) as client:
        yield client


@pytest.fixture
def test_credentials():
    """Test credentials for different business roles."""
    return {
        "owner": {"email": "<EMAIL>", "password": "GikiTest2025Secure"},
        "accountant": {"email": "<EMAIL>", "password": "GikiTest2025Secure"},
        "bookkeeper": {"email": "<EMAIL>", "password": "GikiTest2025Secure"},
        "viewer": {"email": "<EMAIL>", "password": "GikiTest2025Secure"},
    }


@pytest.fixture
async def authenticated_owner_client(async_client: AsyncClient, test_credentials):
    """Get authenticated client for business owner role."""
    auth_response = await async_client.post(
        "/auth/token",
        data={
            "username": test_credentials["owner"]["email"], 
            "password": test_credentials["owner"]["password"]
        },
    )
    assert auth_response.status_code == 200, f"Authentication failed: {auth_response.text}"
    token_data = auth_response.json()
    
    async_client.headers.update({"Authorization": f"Bearer {token_data['access_token']}"})
    return async_client


@pytest.fixture
async def authenticated_accountant_client(async_client: AsyncClient, test_credentials):
    """Get authenticated client for accountant role."""
    auth_response = await async_client.post(
        "/auth/token",
        data={
            "username": test_credentials["accountant"]["email"], 
            "password": test_credentials["accountant"]["password"]
        },
    )
    assert auth_response.status_code == 200, f"Authentication failed: {auth_response.text}"
    token_data = auth_response.json()
    
    async_client.headers.update({"Authorization": f"Bearer {token_data['access_token']}"})
    return async_client


@pytest.fixture
def sample_company_data():
    """Sample company information for MIS setup testing."""
    return {
        "company_info": {
            "name": "Test Manufacturing Corp",
            "industry": "manufacturing",
            "size": "small",
            "fiscal_year_end": "December",
            "default_currency": "USD"
        }
    }


# MIS Test Data Configuration
@pytest.fixture
def mis_test_data_paths():
    """MIS test data file paths for customer journey validation."""
    from pathlib import Path
    
    test_data_root = Path("libs/test-data")
    return {
        "quick_setup": test_data_root / "mis-quick-setup" / "nuvie_expense_ledger.xlsx",
        "historical": test_data_root / "mis-historical-enhancement" / "Capital One.xlsx",
        "schema": test_data_root / "mis-schema-enhancement" / "M3_Giki_Manufacturing_Demo.xlsx",
    }


# Health check fixture to ensure API is running (only for integration tests)
@pytest.fixture
async def ensure_api_running(async_client: AsyncClient):
    """Ensure API server is running before running integration tests."""
    try:
        health_response = await async_client.get("/api/v1/health")
        assert health_response.status_code == 200, "API server not responding to health check"
    except Exception as e:
        pytest.skip(f"API server not available: {e}")