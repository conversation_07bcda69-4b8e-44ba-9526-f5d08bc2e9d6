# Load Testing Documentation

This directory contains load tests for the giki.ai API to validate performance under various conditions.

## Test Categories

### 1. Basic Performance Tests (`test_api_performance.py`)

- **Health Endpoint Performance**: Tests health check under 100 concurrent requests
- **Authentication Performance**: Tests login endpoint under load
- **API Endpoint Performance**: Tests main API endpoints with 50 concurrent requests
- **Database Query Performance**: Tests database-heavy endpoints
- **Memory Usage**: Monitors memory consumption under load
- **Error Handling Performance**: Ensures error responses remain fast
- **Sustained Load Performance**: Tests performance over time

### 2. Specific Endpoint Tests

- **Transaction Pagination**: Tests different pagination scenarios
- **Search Performance**: Tests search functionality speed
- **File Upload Performance**: Tests file upload under load

## Performance Thresholds

### Response Time Targets

| Endpoint Type | Average | P95 | Notes |
|---------------|---------|-----|-------|
| Health Check | < 50ms | < 100ms | Critical for load balancer |
| Authentication | < 1000ms | < 2000ms | Slower due to bcrypt |
| Standard API | < 500ms | < 1000ms | General data endpoints |
| Database Queries | < 300ms | < 600ms | Database-heavy operations |
| File Uploads | < 5000ms | < 10000ms | File processing included |
| Search | < 400ms | < 800ms | Text search operations |

### Load Targets

- **Concurrent Users**: 50-100 concurrent requests
- **Sustained Load**: 10 workers × 20 requests each
- **Success Rate**: ≥ 95% for all endpoints
- **Memory Growth**: < 100MB increase during load test

## Running Load Tests

### Prerequisites

```bash
# Install required packages
pip install pytest pytest-asyncio httpx psutil

# Ensure API is running
pnpm serve:api
```

### Basic Load Test

```bash
# Run all load tests
pytest tests/load/ -v

# Run specific test
pytest tests/load/test_api_performance.py::TestAPIPerformance::test_health_endpoint_performance -v

# Run with detailed output
pytest tests/load/ -v -s
```

### Performance Monitoring

```bash
# Run tests with performance reports
pytest tests/load/ -v --tb=short | tee load_test_results.txt

# Monitor system resources during tests
htop  # or top
```

## Test Results Analysis

### Interpreting Results

1. **Response Times**: Check average and P95 times against thresholds
2. **Success Rates**: Ensure ≥95% success rate for all endpoints
3. **Memory Usage**: Monitor for memory leaks (excessive growth)
4. **Error Patterns**: Investigate any error spikes under load

### Common Issues

1. **High Response Times**
   - Check database query optimization
   - Review API endpoint efficiency
   - Consider caching strategies

2. **Memory Leaks**
   - Look for unclosed database connections
   - Check for large object accumulation
   - Review middleware and request handling

3. **Error Rate Spikes**
   - Database connection pool exhaustion
   - Rate limiting activation
   - Resource constraints

## Continuous Performance Testing

### CI/CD Integration

Add to GitHub Actions or similar:

```yaml
- name: Run Load Tests
  run: |
    pnpm serve:api &
    sleep 10
    pytest tests/load/ --tb=short
    kill %1
```

### Performance Regression Detection

- Run load tests on every release
- Compare results with baseline metrics
- Alert on significant performance degradation

## Load Test Configuration

### Environment Variables

```bash
# Test configuration
LOAD_TEST_CONCURRENCY=50      # Concurrent requests
LOAD_TEST_DURATION=60         # Test duration in seconds
LOAD_TEST_RAMP_UP=10          # Ramp-up time in seconds

# API configuration for load testing
MAX_CONNECTIONS=100           # Database connection pool
REDIS_MAX_CONNECTIONS=50      # Redis connections
RATE_LIMIT_REQUESTS=1000      # Requests per minute
```

### Performance Monitoring Tools

1. **Application Metrics**
   - Response time percentiles
   - Request throughput
   - Error rates
   - Memory/CPU usage

2. **Database Metrics**
   - Query execution times
   - Connection pool usage
   - Lock waits
   - Cache hit rates

3. **Infrastructure Metrics**
   - Server CPU/Memory usage
   - Network I/O
   - Disk I/O
   - Load balancer metrics

## Best Practices

### Writing Load Tests

1. **Realistic Scenarios**: Test realistic user patterns
2. **Data Cleanup**: Clean up test data after tests
3. **Independent Tests**: Each test should be independent
4. **Clear Assertions**: Use meaningful performance thresholds

### Performance Optimization

1. **Database Optimization**
   - Index critical queries
   - Use connection pooling
   - Implement query caching

2. **API Optimization**
   - Implement response caching
   - Use pagination for large datasets
   - Optimize middleware stack

3. **Infrastructure Optimization**
   - Use CDN for static assets
   - Implement load balancing
   - Monitor and scale resources

## Troubleshooting

### Common Load Test Failures

1. **Connection Refused**: API not running or wrong port
2. **Timeout Errors**: Increase timeout settings or reduce load
3. **Memory Errors**: Reduce concurrency or fix memory leaks
4. **Authentication Failures**: Check test user credentials

### Debugging Performance Issues

1. **Enable Debug Logging**: Set log level to DEBUG
2. **Use Profiling Tools**: Python profilers for hot spots
3. **Monitor Resource Usage**: Check CPU, memory, disk, network
4. **Analyze Slow Queries**: Use database query logs

## Future Enhancements

1. **Stress Testing**: Test beyond normal capacity
2. **Spike Testing**: Test sudden load increases
3. **Volume Testing**: Test with large datasets
4. **Endurance Testing**: Long-duration stability tests
5. **Real User Monitoring**: Production performance tracking