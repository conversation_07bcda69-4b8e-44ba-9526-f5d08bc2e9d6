"""
Load tests for API performance validation.

Tests API endpoints under load to ensure they meet performance requirements.
Uses pytest-benchmark for performance measurement and validation.
"""

import asyncio
import pytest
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from httpx import AsyncClient
import statistics


class TestAPIPerformance:
    """Test API performance under load."""

    @pytest.mark.asyncio
    async def test_health_endpoint_performance(self, async_client: AsyncClient):
        """Test health endpoint performance under load."""
        
        async def make_health_request():
            start_time = time.time()
            response = await async_client.get("/health")
            end_time = time.time()
            return {
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000  # Convert to ms
            }
        
        # Run 100 concurrent requests
        tasks = [make_health_request() for _ in range(100)]
        results = await asyncio.gather(*tasks)
        
        # Validate all requests succeeded
        success_count = sum(1 for r in results if r["status_code"] == 200)
        assert success_count >= 95, f"Only {success_count}/100 requests succeeded"
        
        # Validate response times
        response_times = [r["response_time"] for r in results if r["status_code"] == 200]
        avg_response_time = statistics.mean(response_times)
        p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)]
        
        print(f"Health endpoint - Avg: {avg_response_time:.2f}ms, P95: {p95_response_time:.2f}ms")
        
        # Health endpoint should be very fast
        assert avg_response_time < 50, f"Average response time {avg_response_time:.2f}ms exceeds 50ms"
        assert p95_response_time < 100, f"P95 response time {p95_response_time:.2f}ms exceeds 100ms"

    @pytest.mark.asyncio
    async def test_auth_endpoints_performance(self, async_client: AsyncClient):
        """Test authentication endpoints performance."""
        
        async def make_login_request():
            start_time = time.time()
            response = await async_client.post("/api/v1/auth/login", json={
                "email": "<EMAIL>",
                "password": "testpassword"
            })
            end_time = time.time()
            return {
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000
            }
        
        # Run 20 concurrent login attempts (lower concurrency for auth)
        tasks = [make_login_request() for _ in range(20)]
        results = await asyncio.gather(*tasks)
        
        # Validate response times (auth can be slower due to bcrypt)
        response_times = [r["response_time"] for r in results]
        avg_response_time = statistics.mean(response_times)
        p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)]
        
        print(f"Auth endpoint - Avg: {avg_response_time:.2f}ms, P95: {p95_response_time:.2f}ms")
        
        # Auth endpoints can be slower due to password hashing
        assert avg_response_time < 1000, f"Average auth response time {avg_response_time:.2f}ms exceeds 1000ms"
        assert p95_response_time < 2000, f"P95 auth response time {p95_response_time:.2f}ms exceeds 2000ms"

    @pytest.mark.asyncio
    async def test_api_endpoints_performance(self, authenticated_owner_client: AsyncClient):
        """Test main API endpoints performance under load."""
        
        endpoints = [
            "/api/v1/files",
            "/api/v1/transactions", 
            "/api/v1/categories",
            "/api/v1/exports/formats"
        ]
        
        async def make_api_request(endpoint):
            start_time = time.time()
            response = await authenticated_owner_client.get(endpoint)
            end_time = time.time()
            return {
                "endpoint": endpoint,
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000
            }
        
        # Test each endpoint with 50 concurrent requests
        for endpoint in endpoints:
            tasks = [make_api_request(endpoint) for _ in range(50)]
            results = await asyncio.gather(*tasks)
            
            # Calculate metrics
            success_count = sum(1 for r in results if r["status_code"] in [200, 404])  # 404 is OK if no data
            response_times = [r["response_time"] for r in results if r["status_code"] in [200, 404]]
            
            if response_times:
                avg_response_time = statistics.mean(response_times)
                p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)]
                
                print(f"{endpoint} - Success: {success_count}/50, Avg: {avg_response_time:.2f}ms, P95: {p95_response_time:.2f}ms")
                
                # Standard API endpoints should respond within reasonable time
                assert avg_response_time < 500, f"{endpoint} average response time {avg_response_time:.2f}ms exceeds 500ms"
                assert p95_response_time < 1000, f"{endpoint} P95 response time {p95_response_time:.2f}ms exceeds 1000ms"

    @pytest.mark.asyncio
    async def test_file_upload_performance(self, authenticated_owner_client: AsyncClient):
        """Test file upload performance (if endpoint exists)."""
        import tempfile
        import os
        
        # Create a small test CSV file
        csv_content = """Date,Description,Amount
2024-01-01,Test Transaction 1,100.00
2024-01-02,Test Transaction 2,200.00
2024-01-03,Test Transaction 3,300.00"""
        
        async def upload_file():
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                f.write(csv_content)
                temp_path = f.name
            
            try:
                start_time = time.time()
                with open(temp_path, 'rb') as f:
                    files = {"file": ("test.csv", f, "text/csv")}
                    response = await authenticated_owner_client.post(
                        "/api/v1/files/upload",
                        files=files
                    )
                end_time = time.time()
                
                return {
                    "status_code": response.status_code,
                    "response_time": (end_time - start_time) * 1000
                }
            finally:
                os.unlink(temp_path)
        
        # Test 10 concurrent file uploads
        tasks = [upload_file() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        
        # Validate file upload performance
        response_times = [r["response_time"] for r in results]
        avg_response_time = statistics.mean(response_times)
        p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)]
        
        print(f"File upload - Avg: {avg_response_time:.2f}ms, P95: {p95_response_time:.2f}ms")
        
        # File uploads can take longer
        assert avg_response_time < 5000, f"Average file upload time {avg_response_time:.2f}ms exceeds 5000ms"
        assert p95_response_time < 10000, f"P95 file upload time {p95_response_time:.2f}ms exceeds 10000ms"

    @pytest.mark.asyncio
    async def test_database_query_performance(self, authenticated_owner_client: AsyncClient):
        """Test database query performance under load."""
        
        # Test endpoints that likely hit the database
        db_endpoints = [
            "/api/v1/transactions",
            "/api/v1/categories",
            "/api/v1/files"
        ]
        
        async def make_db_request(endpoint):
            start_time = time.time()
            response = await authenticated_owner_client.get(f"{endpoint}?limit=10")
            end_time = time.time()
            return {
                "endpoint": endpoint,
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000
            }
        
        # Test database endpoints with moderate concurrency
        for endpoint in db_endpoints:
            tasks = [make_db_request(endpoint) for _ in range(30)]
            results = await asyncio.gather(*tasks)
            
            success_results = [r for r in results if r["status_code"] in [200, 404]]
            if success_results:
                response_times = [r["response_time"] for r in success_results]
                avg_response_time = statistics.mean(response_times)
                p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)]
                
                print(f"DB {endpoint} - Avg: {avg_response_time:.2f}ms, P95: {p95_response_time:.2f}ms")
                
                # Database queries should be reasonably fast
                assert avg_response_time < 300, f"DB {endpoint} average response time {avg_response_time:.2f}ms exceeds 300ms"
                assert p95_response_time < 600, f"DB {endpoint} P95 response time {p95_response_time:.2f}ms exceeds 600ms"

    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self, authenticated_owner_client: AsyncClient):
        """Test memory usage doesn't grow excessively under load."""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        async def make_mixed_requests():
            """Make a mix of different API requests."""
            endpoints = [
                "/api/v1/files",
                "/api/v1/transactions",
                "/api/v1/categories",
                "/health"
            ]
            
            tasks = []
            for _ in range(25):  # 25 requests per endpoint = 100 total
                for endpoint in endpoints:
                    tasks.append(authenticated_owner_client.get(endpoint))
            
            return await asyncio.gather(*tasks, return_exceptions=True)
        
        # Run load test
        await make_mixed_requests()
        
        # Check memory usage after load
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"Memory usage - Initial: {initial_memory:.1f}MB, Final: {final_memory:.1f}MB, Increase: {memory_increase:.1f}MB")
        
        # Memory increase should be reasonable (less than 100MB for this test)
        assert memory_increase < 100, f"Memory increased by {memory_increase:.1f}MB, which may indicate a memory leak"

    @pytest.mark.asyncio
    async def test_error_handling_performance(self, authenticated_owner_client: AsyncClient):
        """Test that error responses are still fast."""
        
        async def make_error_request():
            start_time = time.time()
            response = await authenticated_owner_client.get("/api/v1/nonexistent-endpoint")
            end_time = time.time()
            return {
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000
            }
        
        # Test 404 error responses
        tasks = [make_error_request() for _ in range(50)]
        results = await asyncio.gather(*tasks)
        
        error_response_times = [r["response_time"] for r in results if r["status_code"] == 404]
        if error_response_times:
            avg_error_time = statistics.mean(error_response_times)
            p95_error_time = sorted(error_response_times)[int(len(error_response_times) * 0.95)]
            
            print(f"Error responses - Avg: {avg_error_time:.2f}ms, P95: {p95_error_time:.2f}ms")
            
            # Error responses should be fast
            assert avg_error_time < 100, f"Average error response time {avg_error_time:.2f}ms exceeds 100ms"

    @pytest.mark.asyncio
    async def test_sustained_load_performance(self, authenticated_owner_client: AsyncClient):
        """Test performance under sustained load."""
        
        async def sustained_load_worker():
            """Worker that makes requests continuously."""
            request_times = []
            
            for _ in range(20):  # 20 requests per worker
                start_time = time.time()
                response = await authenticated_owner_client.get("/health")
                end_time = time.time()
                
                request_times.append({
                    "response_time": (end_time - start_time) * 1000,
                    "status_code": response.status_code
                })
                
                # Small delay between requests
                await asyncio.sleep(0.1)
            
            return request_times
        
        # Run 10 workers concurrently for sustained load
        workers = [sustained_load_worker() for _ in range(10)]
        worker_results = await asyncio.gather(*workers)
        
        # Flatten all results
        all_requests = []
        for worker_requests in worker_results:
            all_requests.extend(worker_requests)
        
        # Analyze sustained load performance
        success_requests = [r for r in all_requests if r["status_code"] == 200]
        response_times = [r["response_time"] for r in success_requests]
        
        if response_times:
            avg_sustained_time = statistics.mean(response_times)
            p95_sustained_time = sorted(response_times)[int(len(response_times) * 0.95)]
            
            print(f"Sustained load - Requests: {len(success_requests)}, Avg: {avg_sustained_time:.2f}ms, P95: {p95_sustained_time:.2f}ms")
            
            # Performance should remain stable under sustained load
            assert avg_sustained_time < 100, f"Sustained load average {avg_sustained_time:.2f}ms exceeds 100ms"
            assert p95_sustained_time < 200, f"Sustained load P95 {p95_sustained_time:.2f}ms exceeds 200ms"


class TestSpecificEndpointPerformance:
    """Test performance of specific critical endpoints."""

    @pytest.mark.asyncio
    async def test_transaction_list_pagination_performance(self, authenticated_owner_client: AsyncClient):
        """Test transaction list pagination performance."""
        
        async def test_pagination_request(page, limit):
            start_time = time.time()
            response = await authenticated_owner_client.get(
                f"/api/v1/transactions?page={page}&limit={limit}"
            )
            end_time = time.time()
            return {
                "page": page,
                "limit": limit,
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000
            }
        
        # Test different pagination scenarios
        pagination_tests = [
            (1, 10), (1, 50), (1, 100),  # Different page sizes
            (2, 50), (5, 50), (10, 50)   # Different pages
        ]
        
        tasks = [test_pagination_request(page, limit) for page, limit in pagination_tests]
        results = await asyncio.gather(*tasks)
        
        for result in results:
            if result["status_code"] in [200, 404]:  # 404 OK if no data
                print(f"Pagination page={result['page']}, limit={result['limit']} - {result['response_time']:.2f}ms")
                assert result["response_time"] < 500, f"Pagination response time {result['response_time']:.2f}ms too slow"

    @pytest.mark.asyncio
    async def test_search_performance(self, authenticated_owner_client: AsyncClient):
        """Test search functionality performance."""
        
        search_queries = [
            "office", "travel", "coffee", "gas", "restaurant",
            "supplies", "equipment", "software", "rent", "utilities"
        ]
        
        async def test_search_request(query):
            start_time = time.time()
            response = await authenticated_owner_client.get(
                f"/api/v1/transactions?search={query}"
            )
            end_time = time.time()
            return {
                "query": query,
                "status_code": response.status_code,
                "response_time": (end_time - start_time) * 1000
            }
        
        tasks = [test_search_request(query) for query in search_queries]
        results = await asyncio.gather(*tasks)
        
        search_times = [r["response_time"] for r in results if r["status_code"] in [200, 404]]
        if search_times:
            avg_search_time = statistics.mean(search_times)
            p95_search_time = sorted(search_times)[int(len(search_times) * 0.95)]
            
            print(f"Search performance - Avg: {avg_search_time:.2f}ms, P95: {p95_search_time:.2f}ms")
            
            # Search should be reasonably fast
            assert avg_search_time < 400, f"Average search time {avg_search_time:.2f}ms exceeds 400ms"
            assert p95_search_time < 800, f"P95 search time {p95_search_time:.2f}ms exceeds 800ms"


if __name__ == "__main__":
    """Run load tests directly."""
    pytest.main([__file__, "-v", "-s", "--tb=short"])