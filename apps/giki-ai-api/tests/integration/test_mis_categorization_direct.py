"""
Integration tests for MIS categorization system.
"""

import asyncio
import logging
import os
from datetime import datetime

import asyncpg
import pytest

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@pytest.mark.asyncio
async def test_mis_categorization_direct():
    """Test the MIS categorization system directly with database connection"""
    
    # Use environment variables for database connection
    database_url = os.getenv("DATABASE_URL", "postgresql://postgres:password@localhost:5432/giki_ai_dev")
    conn = await asyncpg.connect(database_url)
    
    try:
        # Import the categorization service
        from giki_ai_api.domains.categories.mis_categorization_service import (
            MISCategorizationService,
        )
        
        # Create service
        mis_service = MISCategorizationService(conn)
        
        # Test with a simple transaction
        logger.info("Testing MIS categorization with sample transaction...")
        
        result = await mis_service.categorize_transaction(
            tenant_id=3,
            transaction_id="test-123",
            description="ZOMATO PAYMENT FOR TEAM LUNCH",
            amount=-850.00,
            transaction_date=datetime.now().isoformat(),
            remarks="Office meal expense"
        )
        
        # Assert categorization was successful
        assert result.category_name is not None, "Category name should not be None"
        assert result.confidence > 0, "Confidence should be greater than 0"
        assert result.category_id is not None, "Category ID should not be None"
        
        logger.info("Categorization result:")
        logger.info(f"  Category: {result.category_name}")
        logger.info(f"  Parent: {result.parent_category}")
        logger.info(f"  Confidence: {result.confidence}")
        logger.info(f"  Reasoning: {result.reasoning}")
        logger.info(f"  Category ID: {result.category_id}")
        logger.info(f"  GL Code: {result.gl_code}")
        
        # Test with another transaction
        logger.info("\nTesting with different transaction...")
        
        result2 = await mis_service.categorize_transaction(
            tenant_id=3,
            transaction_id="test-456",
            description="GOOGLE ADS PAYMENT",
            amount=-5000.00,
            transaction_date=datetime.now().isoformat(),
            remarks="Marketing campaign"
        )
        
        # Assert second categorization was successful
        assert result2.category_name is not None, "Second category name should not be None"
        assert result2.confidence > 0, "Second confidence should be greater than 0"
        assert result2.category_id is not None, "Second category ID should not be None"
        
        logger.info("Second categorization result:")
        logger.info(f"  Category: {result2.category_name}")
        logger.info(f"  Parent: {result2.parent_category}")
        logger.info(f"  Confidence: {result2.confidence}")
        logger.info(f"  Reasoning: {result2.reasoning}")
        logger.info(f"  Category ID: {result2.category_id}")
        logger.info(f"  GL Code: {result2.gl_code}")
        
        # Test if we can get an uncategorized transaction from the database
        logger.info("\nTesting with real uncategorized transaction...")
        
        uncategorized_query = """
            SELECT id, description, amount, date
            FROM transactions
            WHERE tenant_id = 3 AND ai_confidence IS NULL AND category_id IS NULL
            LIMIT 1
        """
        
        uncategorized_tx = await conn.fetchrow(uncategorized_query)
        
        if uncategorized_tx:
            logger.info(f"Found uncategorized transaction: {uncategorized_tx['description']}")
            
            result3 = await mis_service.categorize_transaction(
                tenant_id=3,
                transaction_id=str(uncategorized_tx['id']),
                description=uncategorized_tx['description'],
                amount=float(uncategorized_tx['amount']),
                transaction_date=uncategorized_tx['date'].isoformat(),
            )
            
            # Assert real transaction categorization was successful
            assert result3.category_name is not None, "Real transaction category name should not be None"
            assert result3.confidence > 0, "Real transaction confidence should be greater than 0"
            
            logger.info("Real transaction categorization result:")
            logger.info(f"  Category: {result3.category_name}")
            logger.info(f"  Parent: {result3.parent_category}")
            logger.info(f"  Confidence: {result3.confidence}")
            logger.info(f"  Reasoning: {result3.reasoning}")
        else:
            logger.info("No uncategorized transactions found - this is okay for testing")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        raise  # Re-raise for pytest to catch
    
    finally:
        await conn.close()


# Standalone test runner for manual execution
async def run_manual_test():
    """Manual test runner when executed directly"""
    await test_mis_categorization_direct()


if __name__ == "__main__":
    asyncio.run(run_manual_test())