"""
Categorization Accuracy Testing Framework
========================================

This module provides comprehensive testing for the MIS categorization system,
measuring real accuracy metrics against known correct categories.

Key Features:
- Tests against validated test data with known correct categories
- Measures baseline accuracy (without enhancements)
- Tests pattern recognition and vendor identification
- Validates hierarchical category assignments
- Measures confidence score reliability
- Tests business logic categorization fallbacks
"""

import asyncio
import json
import logging
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd
import pytest

from giki_ai_api.domains.categories.business_category_mapper import (
    business_category_mapper,
)

logger = logging.getLogger(__name__)


@dataclass
class CategoryTestCase:
    """A single test case for categorization accuracy."""
    description: str
    amount: float
    expected_category: str
    expected_parent: str
    vendor: Optional[str] = None
    notes: Optional[str] = None
    confidence_threshold: float = 0.7
    test_type: str = "standard"  # standard, vendor, pattern, edge_case


@dataclass
class AccuracyTestResult:
    """Result of an accuracy test."""
    test_case: CategoryTestCase
    predicted_category: str
    predicted_parent: str
    predicted_confidence: float
    correct_category: bool
    correct_parent: bool
    confidence_reliable: bool
    reasoning: str
    execution_time_ms: float


@dataclass
class AccuracyMetrics:
    """Overall accuracy metrics for categorization testing."""
    total_tests: int
    correct_categories: int
    correct_parents: int
    high_confidence_correct: int
    low_confidence_incorrect: int
    category_accuracy: float
    parent_accuracy: float
    confidence_reliability: float
    average_confidence: float
    average_execution_time_ms: float
    
    # Breakdown by test type
    standard_accuracy: float
    vendor_accuracy: float
    pattern_accuracy: float
    edge_case_accuracy: float


class CategorizationAccuracyTester:
    """
    Comprehensive testing framework for categorization accuracy.
    
    This class provides methods to:
    1. Load test data with known correct categories
    2. Run categorization tests against the business logic
    3. Measure accuracy metrics and confidence reliability
    4. Generate detailed test reports
    5. Compare different categorization approaches
    """
    
    def __init__(self):
        self.test_data_dir = Path("/Users/<USER>/giki-ai-workspace/libs/test-data")
        self.results: List[AccuracyTestResult] = []
    
    def load_test_cases_from_excel(self, file_path: Path) -> List[CategoryTestCase]:
        """
        Load test cases from Excel file with expected categories.
        
        Expected columns:
        - description: Transaction description
        - amount: Transaction amount
        - expected_category: Correct category name
        - expected_parent: Correct parent category (Income/Expenses)
        - vendor: Optional vendor name
        - confidence_threshold: Optional minimum confidence expected
        - test_type: Optional test type classification
        """
        df = pd.read_excel(file_path)
        test_cases = []
        
        for _, row in df.iterrows():
            test_case = CategoryTestCase(
                description=str(row.get('description', '')),
                amount=float(row.get('amount', 0)),
                expected_category=str(row.get('expected_category', '')),
                expected_parent=str(row.get('expected_parent', '')),
                vendor=str(row.get('vendor', '')) if pd.notna(row.get('vendor')) else None,
                notes=str(row.get('notes', '')) if pd.notna(row.get('notes')) else None,
                confidence_threshold=float(row.get('confidence_threshold', 0.7)),
                test_type=str(row.get('test_type', 'standard'))
            )
            test_cases.append(test_case)
        
        return test_cases
    
    def create_standard_test_cases(self) -> List[CategoryTestCase]:
        """Create a set of standard test cases for basic accuracy testing."""
        return [
            # Insurance transactions
            CategoryTestCase(
                description="NEXT Insurance Premium Payment",
                amount=-1406.11,
                expected_category="Insurance Expense",
                expected_parent="Expenses",
                test_type="vendor"
            ),
            CategoryTestCase(
                description="Auto Insurance - State Farm",
                amount=-450.00,
                expected_category="Insurance Expense", 
                expected_parent="Expenses",
                vendor="State Farm",
                test_type="vendor"
            ),
            
            # Technology/Software expenses
            CategoryTestCase(
                description="Microsoft Office 365 Subscription",
                amount=-19.99,
                expected_category="Software & Technology",
                expected_parent="Expenses",
                vendor="Microsoft",
                test_type="vendor"
            ),
            CategoryTestCase(
                description="AWS Cloud Services",
                amount=-156.78,
                expected_category="Software & Technology",
                expected_parent="Expenses",
                vendor="Amazon",
                test_type="vendor"
            ),
            
            # Travel expenses
            CategoryTestCase(
                description="Uber ride to airport",
                amount=-35.50,
                expected_category="Travel & Transportation",
                expected_parent="Expenses",
                vendor="Uber",
                test_type="vendor"
            ),
            CategoryTestCase(
                description="Hotel booking for business trip",
                amount=-289.00,
                expected_category="Travel & Transportation",
                expected_parent="Expenses",
                test_type="pattern"
            ),
            
            # Income transactions
            CategoryTestCase(
                description="ACH Credit Consulting Payment",
                amount=45980.68,
                expected_category="Consulting Income",
                expected_parent="Income",
                test_type="pattern"
            ),
            CategoryTestCase(
                description="Client payment for web development",
                amount=5000.00,
                expected_category="Service Revenue",
                expected_parent="Income",
                test_type="pattern"
            ),
            CategoryTestCase(
                description="Stripe payment from customer",
                amount=299.99,
                expected_category="Sales Revenue",
                expected_parent="Income",
                vendor="Stripe",
                test_type="vendor"
            ),
            
            # Office expenses
            CategoryTestCase(
                description="Office supplies from Staples",
                amount=-67.43,
                expected_category="Office Supplies",
                expected_parent="Expenses",
                vendor="Staples",
                test_type="vendor"
            ),
            
            # Meals & Entertainment
            CategoryTestCase(
                description="Client lunch at restaurant",
                amount=-85.60,
                expected_category="Meals & Entertainment",
                expected_parent="Expenses",
                test_type="pattern"
            ),
            CategoryTestCase(
                description="Starbucks coffee meeting",
                amount=-12.45,
                expected_category="Meals & Entertainment",
                expected_parent="Expenses",
                vendor="Starbucks",
                test_type="vendor"
            ),
            
            # Edge cases
            CategoryTestCase(
                description="Bank service charge",
                amount=-25.00,
                expected_category="Banking & Finance",
                expected_parent="Expenses",
                test_type="edge_case"
            ),
            CategoryTestCase(
                description="Refund from cancelled order",
                amount=150.00,
                expected_category="Refunds & Credits",
                expected_parent="Income",
                test_type="edge_case"
            ),
        ]
    
    async def test_single_categorization(self, test_case: CategoryTestCase) -> AccuracyTestResult:
        """Test a single categorization case and return detailed results."""
        start_time = datetime.now()
        
        try:
            # Test the business category mapper
            category_match = business_category_mapper.categorize_transaction(
                description=test_case.description,
                amount=test_case.amount,
                vendor=test_case.vendor
            )
            
            end_time = datetime.now()
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # Check accuracy
            correct_category = category_match.category_name == test_case.expected_category
            correct_parent = category_match.parent_category == test_case.expected_parent
            
            # Check confidence reliability
            confidence_reliable = (
                (category_match.confidence >= test_case.confidence_threshold and correct_category) or
                (category_match.confidence < test_case.confidence_threshold and not correct_category)
            )
            
            return AccuracyTestResult(
                test_case=test_case,
                predicted_category=category_match.category_name,
                predicted_parent=category_match.parent_category,
                predicted_confidence=category_match.confidence,
                correct_category=correct_category,
                correct_parent=correct_parent,
                confidence_reliable=confidence_reliable,
                reasoning=category_match.reasoning,
                execution_time_ms=execution_time_ms
            )
            
        except Exception as e:
            end_time = datetime.now()
            execution_time_ms = (end_time - start_time).total_seconds() * 1000
            
            logger.error(f"Error testing categorization for '{test_case.description}': {e}")
            
            return AccuracyTestResult(
                test_case=test_case,
                predicted_category="ERROR",
                predicted_parent="ERROR",
                predicted_confidence=0.0,
                correct_category=False,
                correct_parent=False,
                confidence_reliable=False,
                reasoning=f"Test failed with error: {str(e)}",
                execution_time_ms=execution_time_ms
            )
    
    async def run_accuracy_test_suite(self, test_cases: List[CategoryTestCase]) -> AccuracyMetrics:
        """Run a complete accuracy test suite and return metrics."""
        self.results = []
        
        logger.info(f"Starting accuracy test suite with {len(test_cases)} test cases")
        
        # Run all test cases
        for test_case in test_cases:
            result = await self.test_single_categorization(test_case)
            self.results.append(result)
        
        # Calculate overall metrics
        total_tests = len(self.results)
        correct_categories = sum(1 for r in self.results if r.correct_category)
        correct_parents = sum(1 for r in self.results if r.correct_parent)
        high_confidence_correct = sum(1 for r in self.results if r.predicted_confidence >= 0.8 and r.correct_category)
        low_confidence_incorrect = sum(1 for r in self.results if r.predicted_confidence < 0.5 and not r.correct_category)
        
        category_accuracy = correct_categories / total_tests if total_tests > 0 else 0
        parent_accuracy = correct_parents / total_tests if total_tests > 0 else 0
        confidence_reliability = sum(1 for r in self.results if r.confidence_reliable) / total_tests if total_tests > 0 else 0
        average_confidence = sum(r.predicted_confidence for r in self.results) / total_tests if total_tests > 0 else 0
        average_execution_time = sum(r.execution_time_ms for r in self.results) / total_tests if total_tests > 0 else 0
        
        # Calculate accuracy by test type
        def accuracy_by_type(test_type: str) -> float:
            type_results = [r for r in self.results if r.test_case.test_type == test_type]
            if not type_results:
                return 0.0
            return sum(1 for r in type_results if r.correct_category) / len(type_results)
        
        metrics = AccuracyMetrics(
            total_tests=total_tests,
            correct_categories=correct_categories,
            correct_parents=correct_parents,
            high_confidence_correct=high_confidence_correct,
            low_confidence_incorrect=low_confidence_incorrect,
            category_accuracy=category_accuracy,
            parent_accuracy=parent_accuracy,
            confidence_reliability=confidence_reliability,
            average_confidence=average_confidence,
            average_execution_time_ms=average_execution_time,
            standard_accuracy=accuracy_by_type("standard"),
            vendor_accuracy=accuracy_by_type("vendor"),
            pattern_accuracy=accuracy_by_type("pattern"),
            edge_case_accuracy=accuracy_by_type("edge_case")
        )
        
        logger.info(f"Accuracy test completed: {category_accuracy:.1%} category accuracy, {parent_accuracy:.1%} parent accuracy")
        
        return metrics
    
    def generate_test_report(self, metrics: AccuracyMetrics, output_file: Optional[Path] = None) -> Dict[str, Any]:
        """Generate a comprehensive test report with detailed analysis."""
        
        # Analyze failed cases
        failed_cases = [r for r in self.results if not r.correct_category]
        failed_by_type = {}
        for test_type in ["standard", "vendor", "pattern", "edge_case"]:
            failed_by_type[test_type] = [r for r in failed_cases if r.test_case.test_type == test_type]
        
        # Analyze confidence distribution
        confidence_buckets = {
            "high_confidence_correct": [r for r in self.results if r.predicted_confidence >= 0.8 and r.correct_category],
            "high_confidence_incorrect": [r for r in self.results if r.predicted_confidence >= 0.8 and not r.correct_category],
            "medium_confidence": [r for r in self.results if 0.5 <= r.predicted_confidence < 0.8],
            "low_confidence": [r for r in self.results if r.predicted_confidence < 0.5]
        }
        
        report = {
            "test_summary": {
                "test_timestamp": datetime.now().isoformat(),
                "total_test_cases": metrics.total_tests,
                "framework_version": "1.0.0"
            },
            "accuracy_metrics": asdict(metrics),
            "performance_analysis": {
                "average_execution_time_ms": metrics.average_execution_time_ms,
                "fastest_test_ms": min(r.execution_time_ms for r in self.results) if self.results else 0,
                "slowest_test_ms": max(r.execution_time_ms for r in self.results) if self.results else 0,
                "performance_rating": "excellent" if metrics.average_execution_time_ms < 100 else 
                                   "good" if metrics.average_execution_time_ms < 500 else "needs_improvement"
            },
            "confidence_analysis": {
                "distribution": {
                    "high_confidence_correct": len(confidence_buckets["high_confidence_correct"]),
                    "high_confidence_incorrect": len(confidence_buckets["high_confidence_incorrect"]),
                    "medium_confidence": len(confidence_buckets["medium_confidence"]),
                    "low_confidence": len(confidence_buckets["low_confidence"])
                },
                "reliability_score": metrics.confidence_reliability,
                "average_confidence": metrics.average_confidence
            },
            "failure_analysis": {
                "total_failures": len(failed_cases),
                "failures_by_type": {test_type: len(cases) for test_type, cases in failed_by_type.items()},
                "common_failure_patterns": self._analyze_failure_patterns(failed_cases)
            },
            "detailed_results": [
                {
                    "description": r.test_case.description,
                    "amount": r.test_case.amount,
                    "expected_category": r.test_case.expected_category,
                    "predicted_category": r.predicted_category,
                    "confidence": r.predicted_confidence,
                    "correct": r.correct_category,
                    "reasoning": r.reasoning,
                    "test_type": r.test_case.test_type
                }
                for r in self.results
            ]
        }
        
        if output_file:
            output_file.parent.mkdir(parents=True, exist_ok=True)
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2)
            logger.info(f"Test report saved to {output_file}")
        
        return report
    
    def _analyze_failure_patterns(self, failed_cases: List[AccuracyTestResult]) -> List[Dict[str, Any]]:
        """Analyze common patterns in failed test cases."""
        patterns = []
        
        # Group by predicted category
        predicted_categories = {}
        for case in failed_cases:
            pred_cat = case.predicted_category
            if pred_cat not in predicted_categories:
                predicted_categories[pred_cat] = []
            predicted_categories[pred_cat].append(case)
        
        # Find categories with multiple failures
        for pred_cat, cases in predicted_categories.items():
            if len(cases) > 1:
                expected_cats = [c.test_case.expected_category for c in cases]
                patterns.append({
                    "pattern_type": "incorrect_prediction",
                    "predicted_category": pred_cat,
                    "expected_categories": list(set(expected_cats)),
                    "failure_count": len(cases),
                    "sample_descriptions": [c.test_case.description for c in cases[:3]]
                })
        
        # Analyze low confidence failures
        low_confidence_failures = [c for c in failed_cases if c.predicted_confidence < 0.5]
        if len(low_confidence_failures) > 2:
            patterns.append({
                "pattern_type": "low_confidence_failures",
                "count": len(low_confidence_failures),
                "average_confidence": sum(c.predicted_confidence for c in low_confidence_failures) / len(low_confidence_failures),
                "sample_cases": [
                    {
                        "description": c.test_case.description,
                        "expected": c.test_case.expected_category,
                        "predicted": c.predicted_category,
                        "confidence": c.predicted_confidence
                    }
                    for c in low_confidence_failures[:3]
                ]
            })
        
        return patterns


# Test fixtures and utilities for pytest integration

@pytest.fixture
def accuracy_tester():
    """Pytest fixture for the accuracy tester."""
    return CategorizationAccuracyTester()


@pytest.fixture
def standard_test_cases():
    """Pytest fixture for standard test cases."""
    tester = CategorizationAccuracyTester()
    return tester.create_standard_test_cases()


# Main test functions

@pytest.mark.asyncio
async def test_categorization_baseline_accuracy(accuracy_tester, standard_test_cases):
    """Test baseline categorization accuracy against standard test cases."""
    metrics = await accuracy_tester.run_accuracy_test_suite(standard_test_cases)
    
    # Assert minimum accuracy thresholds
    assert metrics.category_accuracy >= 0.70, f"Category accuracy {metrics.category_accuracy:.1%} below 70% threshold"
    assert metrics.parent_accuracy >= 0.85, f"Parent accuracy {metrics.parent_accuracy:.1%} below 85% threshold"
    assert metrics.confidence_reliability >= 0.60, f"Confidence reliability {metrics.confidence_reliability:.1%} below 60% threshold"
    
    # Assert performance requirements
    assert metrics.average_execution_time_ms < 1000, f"Average execution time {metrics.average_execution_time_ms:.1f}ms too slow"
    
    # Generate test report
    report_path = Path("/Users/<USER>/giki-ai-workspace/testing/results/baseline_accuracy_report.json")
    accuracy_tester.generate_test_report(metrics, report_path)


@pytest.mark.asyncio
async def test_vendor_identification_accuracy(accuracy_tester):
    """Test vendor-specific categorization accuracy."""
    vendor_test_cases = [
        CategoryTestCase(
            description="Microsoft Office subscription",
            amount=-29.99,
            expected_category="Software & Technology",
            expected_parent="Expenses",
            vendor="Microsoft",
            test_type="vendor"
        ),
        CategoryTestCase(
            description="Uber ride to client meeting",
            amount=-25.50,
            expected_category="Travel & Transportation", 
            expected_parent="Expenses",
            vendor="Uber",
            test_type="vendor"
        ),
        CategoryTestCase(
            description="Stripe payment processing fee",
            amount=-15.75,
            expected_category="Banking & Finance",
            expected_parent="Expenses",
            vendor="Stripe",
            test_type="vendor"
        ),
    ]
    
    metrics = await accuracy_tester.run_accuracy_test_suite(vendor_test_cases)
    
    # Vendor identification should have higher accuracy
    assert metrics.vendor_accuracy >= 0.80, f"Vendor accuracy {metrics.vendor_accuracy:.1%} below 80% threshold"


@pytest.mark.asyncio  
async def test_pattern_recognition_accuracy(accuracy_tester):
    """Test pattern-based categorization accuracy."""
    pattern_test_cases = [
        CategoryTestCase(
            description="Business lunch with client",
            amount=-67.89,
            expected_category="Meals & Entertainment",
            expected_parent="Expenses",
            test_type="pattern"
        ),
        CategoryTestCase(
            description="Hotel booking for conference",
            amount=-189.00,
            expected_category="Travel & Transportation",
            expected_parent="Expenses", 
            test_type="pattern"
        ),
        CategoryTestCase(
            description="Client payment received",
            amount=2500.00,
            expected_category="Service Revenue",
            expected_parent="Income",
            test_type="pattern"
        ),
    ]
    
    metrics = await accuracy_tester.run_accuracy_test_suite(pattern_test_cases)
    
    # Pattern recognition should achieve reasonable accuracy
    assert metrics.pattern_accuracy >= 0.65, f"Pattern accuracy {metrics.pattern_accuracy:.1%} below 65% threshold"


if __name__ == "__main__":
    # Allow running the test framework directly for development
    async def main():
        tester = CategorizationAccuracyTester()
        test_cases = tester.create_standard_test_cases()
        
        print("🧪 Running categorization accuracy tests...")
        metrics = await tester.run_accuracy_test_suite(test_cases)
        
        print("\n📊 Test Results:")
        print(f"   Category Accuracy: {metrics.category_accuracy:.1%}")
        print(f"   Parent Accuracy: {metrics.parent_accuracy:.1%}")
        print(f"   Confidence Reliability: {metrics.confidence_reliability:.1%}")
        print(f"   Average Execution Time: {metrics.average_execution_time_ms:.1f}ms")
        print(f"   Vendor Accuracy: {metrics.vendor_accuracy:.1%}")
        print(f"   Pattern Accuracy: {metrics.pattern_accuracy:.1%}")
        
        # Generate detailed report
        report_path = Path("/Users/<USER>/giki-ai-workspace/testing/results/categorization_accuracy_report.json")
        tester.generate_test_report(metrics, report_path)
        
        print(f"\n📝 Detailed report saved to: {report_path}")
        
        # Print summary of failed cases
        failed_cases = [r for r in tester.results if not r.correct_category]
        if failed_cases:
            print(f"\n❌ Failed cases ({len(failed_cases)}):")
            for case in failed_cases:
                print(f"   • {case.test_case.description[:50]}...")
                print(f"     Expected: {case.test_case.expected_category}")
                print(f"     Got: {case.predicted_category} (confidence: {case.predicted_confidence:.2f})")
                print(f"     Reasoning: {case.reasoning[:80]}...")
                print()
    
    asyncio.run(main())