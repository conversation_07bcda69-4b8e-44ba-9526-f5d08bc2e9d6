"""
Integration tests for export endpoints.

Tests transaction export functionality, format support, and file generation.
"""

import pytest
from httpx import AsyncClient
from datetime import date


class TestExportFormats:
    """Test export format listing and information."""

    @pytest.mark.asyncio
    async def test_get_available_formats(self, authenticated_owner_client: AsyncClient):
        """Test retrieving available export formats."""
        response = await authenticated_owner_client.get("/api/v1/exports/formats")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        if data:
            # Check format structure
            format_info = data[0]
            assert "id" in format_info
            assert "name" in format_info
            assert "file_extension" in format_info

    @pytest.mark.asyncio
    async def test_get_format_details(self, authenticated_owner_client: AsyncClient):
        """Test retrieving specific format details."""
        # First get available formats
        formats_response = await authenticated_owner_client.get("/api/v1/exports/formats")
        assert formats_response.status_code == 200
        formats = formats_response.json()
        
        if formats:
            format_id = formats[0]["id"]
            
            response = await authenticated_owner_client.get(f"/api/v1/exports/formats/{format_id}")
            
            assert response.status_code in [200, 404]
            if response.status_code == 200:
                data = response.json()
                assert data["id"] == format_id
                assert "name" in data
                assert "description" in data
                assert "required_fields" in data


class TestTransactionExport:
    """Test transaction export functionality."""

    @pytest.mark.asyncio
    async def test_export_transactions_basic(self, authenticated_owner_client: AsyncClient):
        """Test basic transaction export."""
        export_request = {
            "format_id": "quickbooks_csv",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        # May succeed or fail depending on data availability
        assert response.status_code in [200, 400, 404, 422]
        
        if response.status_code == 200:
            # Should return file content or download response
            assert response.headers.get("content-type") in [
                "text/csv",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/octet-stream"
            ]

    @pytest.mark.asyncio
    async def test_export_with_date_filters(self, authenticated_owner_client: AsyncClient):
        """Test export with date range filters."""
        export_request = {
            "format_id": "quickbooks_csv",
            "date_from": "2024-01-01",
            "date_to": "2024-01-31",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code in [200, 400, 404, 422]

    @pytest.mark.asyncio
    async def test_export_with_category_filters(self, authenticated_owner_client: AsyncClient):
        """Test export with category filters."""
        export_request = {
            "format_id": "quickbooks_csv",
            "category_ids": [1, 2, 3],
            "include_uncategorized": False
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code in [200, 400, 404, 422]

    @pytest.mark.asyncio
    async def test_export_unsupported_format(self, authenticated_owner_client: AsyncClient):
        """Test export with unsupported format."""
        export_request = {
            "format_id": "unsupported_format",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code in [400, 422]
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_export_invalid_date_range(self, authenticated_owner_client: AsyncClient):
        """Test export with invalid date range."""
        export_request = {
            "format_id": "quickbooks_csv",
            "date_from": "2024-01-31",
            "date_to": "2024-01-01",  # End before start
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code in [400, 422]


class TestSpecificFormats:
    """Test specific export format functionality."""

    @pytest.mark.asyncio
    async def test_quickbooks_csv_export(self, authenticated_owner_client: AsyncClient):
        """Test QuickBooks CSV export."""
        export_request = {
            "format_id": "quickbooks_csv",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code in [200, 400, 404, 422]
        
        if response.status_code == 200:
            assert response.headers.get("content-type") == "text/csv"
            # Check filename header
            content_disposition = response.headers.get("content-disposition")
            if content_disposition:
                assert "quickbooks" in content_disposition.lower()

    @pytest.mark.asyncio
    async def test_quickbooks_iif_export(self, authenticated_owner_client: AsyncClient):
        """Test QuickBooks IIF export."""
        export_request = {
            "format_id": "quickbooks_iif",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code in [200, 400, 404, 422]
        
        if response.status_code == 200:
            content_disposition = response.headers.get("content-disposition")
            if content_disposition:
                assert ".iif" in content_disposition

    @pytest.mark.asyncio
    async def test_xero_csv_export(self, authenticated_owner_client: AsyncClient):
        """Test Xero CSV export."""
        export_request = {
            "format_id": "xero_csv",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code in [200, 400, 404, 422]

    @pytest.mark.asyncio
    async def test_sage_csv_export(self, authenticated_owner_client: AsyncClient):
        """Test Sage CSV export."""
        export_request = {
            "format_id": "sage_csv",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code in [200, 400, 404, 422]

    @pytest.mark.asyncio
    async def test_excel_export(self, authenticated_owner_client: AsyncClient):
        """Test Excel export."""
        export_request = {
            "format_id": "excel",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code in [200, 400, 404, 422]
        
        if response.status_code == 200:
            content_type = response.headers.get("content-type")
            assert content_type in [
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.ms-excel"
            ]


class TestBulkExport:
    """Test bulk export functionality."""

    @pytest.mark.asyncio
    async def test_bulk_export_multiple_formats(self, authenticated_owner_client: AsyncClient):
        """Test bulk export in multiple formats."""
        bulk_request = {
            "format_ids": ["quickbooks_csv", "xero_csv", "excel"],
            "date_from": "2024-01-01",
            "date_to": "2024-01-31",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/bulk",
            json=bulk_request
        )
        
        assert response.status_code in [200, 400, 404, 422, 501]  # 501 if not implemented
        
        if response.status_code == 200:
            # Should return zip file or JSON with multiple file URLs
            content_type = response.headers.get("content-type")
            assert content_type in [
                "application/zip",
                "application/json"
            ]

    @pytest.mark.asyncio
    async def test_bulk_export_invalid_formats(self, authenticated_owner_client: AsyncClient):
        """Test bulk export with invalid formats."""
        bulk_request = {
            "format_ids": ["invalid_format1", "invalid_format2"],
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/bulk",
            json=bulk_request
        )
        
        assert response.status_code in [400, 422, 501]


class TestScheduledExport:
    """Test scheduled export functionality."""

    @pytest.mark.asyncio
    async def test_schedule_export(self, authenticated_owner_client: AsyncClient):
        """Test scheduling an export."""
        schedule_request = {
            "format_id": "quickbooks_csv",
            "schedule": "weekly",
            "day_of_week": 1,  # Monday
            "include_uncategorized": True,
            "email_recipients": ["<EMAIL>"]
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/schedule",
            json=schedule_request
        )
        
        # May not be implemented yet
        assert response.status_code in [200, 201, 400, 422, 501]
        
        if response.status_code in [200, 201]:
            data = response.json()
            assert "schedule_id" in data or "id" in data

    @pytest.mark.asyncio
    async def test_get_scheduled_exports(self, authenticated_owner_client: AsyncClient):
        """Test retrieving scheduled exports."""
        response = await authenticated_owner_client.get("/api/v1/exports/scheduled")
        
        assert response.status_code in [200, 404, 501]
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, list)

    @pytest.mark.asyncio
    async def test_cancel_scheduled_export(self, authenticated_owner_client: AsyncClient):
        """Test canceling a scheduled export."""
        # First try to get existing schedules
        list_response = await authenticated_owner_client.get("/api/v1/exports/scheduled")
        
        if list_response.status_code == 200:
            schedules = list_response.json()
            
            if schedules:
                schedule_id = schedules[0].get("id") or schedules[0].get("schedule_id")
                
                if schedule_id:
                    response = await authenticated_owner_client.delete(
                        f"/api/v1/exports/scheduled/{schedule_id}"
                    )
                    
                    assert response.status_code in [200, 204, 404, 501]


class TestExportValidation:
    """Test export validation and error handling."""

    @pytest.mark.asyncio
    async def test_export_missing_format_id(self, authenticated_owner_client: AsyncClient):
        """Test export without format ID."""
        export_request = {
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_export_invalid_date_format(self, authenticated_owner_client: AsyncClient):
        """Test export with invalid date format."""
        export_request = {
            "format_id": "quickbooks_csv",
            "date_from": "invalid-date",
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_export_empty_category_list(self, authenticated_owner_client: AsyncClient):
        """Test export with empty category list."""
        export_request = {
            "format_id": "quickbooks_csv",
            "category_ids": [],
            "include_uncategorized": False
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        # Should either succeed with no data or return appropriate error
        assert response.status_code in [200, 400, 404, 422]


class TestAccessControl:
    """Test access control for export endpoints."""

    @pytest.mark.asyncio
    async def test_unauthorized_access(self, async_client: AsyncClient):
        """Test accessing export endpoints without authentication."""
        response = await async_client.get("/api/v1/exports/formats")
        
        assert response.status_code == 401
        data = response.json()
        assert data["detail"] == "Not authenticated"

    @pytest.mark.asyncio
    async def test_unauthorized_export_attempt(self, async_client: AsyncClient):
        """Test attempting export without authentication."""
        export_request = {
            "format_id": "quickbooks_csv",
            "include_uncategorized": True
        }
        
        response = await async_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data["detail"] == "Not authenticated"


class TestExportPreview:
    """Test export preview functionality."""

    @pytest.mark.asyncio
    async def test_preview_export(self, authenticated_owner_client: AsyncClient):
        """Test previewing export data."""
        preview_request = {
            "format_id": "quickbooks_csv",
            "date_from": "2024-01-01",
            "date_to": "2024-01-31",
            "limit": 10  # Preview only first 10 records
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/preview",
            json=preview_request
        )
        
        # May not be implemented yet
        assert response.status_code in [200, 404, 422, 501]
        
        if response.status_code == 200:
            data = response.json()
            assert "preview_data" in data or "transactions" in data
            assert "total_count" in data


class TestExportHistory:
    """Test export history tracking."""

    @pytest.mark.asyncio
    async def test_get_export_history(self, authenticated_owner_client: AsyncClient):
        """Test retrieving export history."""
        response = await authenticated_owner_client.get("/api/v1/exports/history")
        
        # May not be implemented yet
        assert response.status_code in [200, 404, 501]
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, list)

    @pytest.mark.asyncio
    async def test_get_export_history_with_filters(self, authenticated_owner_client: AsyncClient):
        """Test retrieving export history with filters."""
        params = {
            "format_id": "quickbooks_csv",
            "date_from": "2024-01-01",
            "limit": 20
        }
        
        response = await authenticated_owner_client.get(
            "/api/v1/exports/history",
            params=params
        )
        
        assert response.status_code in [200, 404, 501]


class TestExportMetadata:
    """Test export metadata and statistics."""

    @pytest.mark.asyncio
    async def test_get_export_statistics(self, authenticated_owner_client: AsyncClient):
        """Test retrieving export statistics."""
        response = await authenticated_owner_client.get("/api/v1/exports/stats")
        
        # May not be implemented yet
        assert response.status_code in [200, 404, 501]
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, dict)
            # Might include total_exports, most_used_format, etc.

    @pytest.mark.asyncio
    async def test_get_format_usage_stats(self, authenticated_owner_client: AsyncClient):
        """Test retrieving format usage statistics."""
        response = await authenticated_owner_client.get("/api/v1/exports/stats/formats")
        
        assert response.status_code in [200, 404, 501]
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, list)


class TestErrorHandling:
    """Test comprehensive error handling."""

    @pytest.mark.asyncio
    async def test_export_server_error_handling(self, authenticated_owner_client: AsyncClient):
        """Test handling of server errors during export."""
        # This test may be difficult to trigger without mocking internal services
        export_request = {
            "format_id": "quickbooks_csv",
            "date_from": "1900-01-01",  # Very old date that might cause issues
            "date_to": "2100-01-01",   # Very future date
            "include_uncategorized": True
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=export_request
        )
        
        # Should handle gracefully
        assert response.status_code in [200, 400, 422, 500]
        
        if response.status_code == 500:
            data = response.json()
            assert "detail" in data

    @pytest.mark.asyncio
    async def test_malformed_request_handling(self, authenticated_owner_client: AsyncClient):
        """Test handling of malformed requests."""
        # Send invalid JSON structure
        malformed_request = {
            "format_id": ["not_a_string"],  # Should be string, not list
            "date_from": {"invalid": "object"},  # Should be string/date
            "include_uncategorized": "not_boolean"  # Should be boolean
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/exports/transactions",
            json=malformed_request
        )
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data