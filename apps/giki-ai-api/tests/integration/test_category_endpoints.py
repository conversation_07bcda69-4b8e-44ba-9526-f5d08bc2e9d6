"""
Integration tests for category endpoints.

Tests the core category API endpoints including CRUD operations,
hierarchy management, and GL code functionality.
"""

import pytest
from httpx import AsyncClient


class TestCategoryBasicCRUD:
    """Test basic category CRUD operations."""

    @pytest.mark.asyncio
    async def test_get_categories_list(self, authenticated_owner_client: AsyncClient):
        """Test retrieving list of categories."""
        response = await authenticated_owner_client.get("/api/v1/categories")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    @pytest.mark.asyncio
    async def test_get_categories_with_counts(self, authenticated_owner_client: AsyncClient):
        """Test retrieving categories with transaction counts."""
        response = await authenticated_owner_client.get("/api/v1/categories/with-counts")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    @pytest.mark.asyncio
    async def test_get_category_by_id(self, authenticated_owner_client: AsyncClient):
        """Test retrieving a specific category by ID."""
        # First get the list to find a valid category ID
        list_response = await authenticated_owner_client.get("/api/v1/categories")
        assert list_response.status_code == 200
        categories = list_response.json()
        
        if categories:
            category_id = categories[0]["id"]
            
            # Now retrieve the specific category
            response = await authenticated_owner_client.get(
                f"/api/v1/categories/{category_id}"
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == category_id
            assert "name" in data

    @pytest.mark.asyncio
    async def test_create_category(self, authenticated_owner_client: AsyncClient):
        """Test creating a new category."""
        category_data = {
            "name": "Test Category",
            "color": "#FF0000",
            "gl_code": "5001"
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/categories",
            json=category_data
        )
        
        # May succeed or fail depending on validation/schema issues
        assert response.status_code in [201, 400, 422, 500]
        
        if response.status_code == 201:
            data = response.json()
            assert data["name"] == category_data["name"]
            assert "id" in data

    @pytest.mark.asyncio
    async def test_update_category(self, authenticated_owner_client: AsyncClient):
        """Test updating an existing category."""
        # First get a category to update
        list_response = await authenticated_owner_client.get("/api/v1/categories")
        assert list_response.status_code == 200
        categories = list_response.json()
        
        if categories:
            category_id = categories[0]["id"]
            
            update_data = {
                "name": "Updated Test Category",
                "color": "#00FF00"
            }
            
            response = await authenticated_owner_client.put(
                f"/api/v1/categories/{category_id}",
                json=update_data
            )
            
            # May succeed or fail depending on validation/permissions
            assert response.status_code in [200, 400, 403, 404, 422]

    @pytest.mark.asyncio
    async def test_delete_category(self, authenticated_owner_client: AsyncClient):
        """Test deleting a category."""
        # First create a category to delete (if creation works)
        category_data = {
            "name": "Category to Delete",
            "color": "#0000FF"
        }
        
        create_response = await authenticated_owner_client.post(
            "/api/v1/categories",
            json=category_data
        )
        
        if create_response.status_code == 201:
            category_id = create_response.json()["id"]
            
            # Delete the category
            response = await authenticated_owner_client.delete(
                f"/api/v1/categories/{category_id}"
            )
            
            assert response.status_code in [204, 400, 403, 404]


class TestCategoryHierarchy:
    """Test category hierarchy functionality."""

    @pytest.mark.asyncio
    async def test_get_category_hierarchy(self, authenticated_owner_client: AsyncClient):
        """Test retrieving category hierarchy."""
        response = await authenticated_owner_client.get("/api/v1/categories/hierarchy")
        
        assert response.status_code == 200
        data = response.json()
        assert "categories" in data or "hierarchy" in data or isinstance(data, list)

    @pytest.mark.asyncio
    async def test_setup_standard_hierarchy(self, authenticated_owner_client: AsyncClient):
        """Test setting up standard category hierarchy."""
        response = await authenticated_owner_client.post("/api/v1/categories/setup-standard-hierarchy")
        
        # May succeed or fail depending on existing data and permissions
        assert response.status_code in [200, 400, 403, 409]

    @pytest.mark.asyncio
    async def test_create_multilevel_hierarchies(self, authenticated_owner_client: AsyncClient):
        """Test creating multilevel category hierarchies."""
        response = await authenticated_owner_client.post("/api/v1/categories/create-multilevel-hierarchies")
        
        # May succeed or fail depending on existing data and permissions
        assert response.status_code in [200, 400, 403, 409]

    @pytest.mark.asyncio
    async def test_fix_hierarchy_integrity(self, authenticated_owner_client: AsyncClient):
        """Test fixing category hierarchy integrity."""
        response = await authenticated_owner_client.post("/api/v1/categories/fix-hierarchy-integrity")
        
        # May succeed or fail depending on existing data and permissions
        assert response.status_code in [200, 400, 403]


class TestCategoryGLCodes:
    """Test GL code related functionality."""

    @pytest.mark.asyncio
    async def test_validate_gl_code(self, authenticated_owner_client: AsyncClient):
        """Test GL code validation."""
        request_data = {
            "gl_code": "5001",
            "account_type": "Expense"
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/categories/validate-gl-code",
            json=request_data
        )
        
        assert response.status_code in [200, 400, 422]

    @pytest.mark.asyncio
    async def test_suggest_gl_codes(self, authenticated_owner_client: AsyncClient):
        """Test GL code suggestions."""
        request_data = {
            "category_name": "Office Supplies",
            "account_type": "Expense"
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/categories/suggest-gl-codes",
            json=request_data
        )
        
        assert response.status_code in [200, 400, 422]

    @pytest.mark.asyncio
    async def test_export_gl_mappings(self, authenticated_owner_client: AsyncClient):
        """Test exporting GL mappings."""
        response = await authenticated_owner_client.get("/api/v1/categories/gl-mappings/export")
        
        assert response.status_code in [200, 404]

    @pytest.mark.asyncio
    async def test_bulk_gl_update(self, authenticated_owner_client: AsyncClient):
        """Test bulk GL code updates."""
        # First get some categories to update
        list_response = await authenticated_owner_client.get("/api/v1/categories")
        assert list_response.status_code == 200
        categories = list_response.json()
        
        if categories:
            bulk_data = {
                "mappings": [
                    {
                        "category_id": categories[0]["id"],
                        "gl_code": "5002",
                        "gl_account_name": "Updated Account"
                    }
                ]
            }
            
            response = await authenticated_owner_client.post(
                "/api/v1/categories/bulk-gl-update",
                json=bulk_data
            )
            
            assert response.status_code in [200, 400, 422]


class TestCategoryValidation:
    """Test category validation functionality."""

    @pytest.mark.asyncio
    async def test_quick_mis_validation(self, authenticated_owner_client: AsyncClient):
        """Test quick MIS validation."""
        response = await authenticated_owner_client.get("/api/v1/categories/validate/quick")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_comprehensive_mis_validation(self, authenticated_owner_client: AsyncClient):
        """Test comprehensive MIS validation."""
        response = await authenticated_owner_client.get("/api/v1/categories/validate/comprehensive")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_structure_health_metrics(self, authenticated_owner_client: AsyncClient):
        """Test category structure health metrics."""
        response = await authenticated_owner_client.get("/api/v1/categories/validate/structure-health")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)


class TestCategoryMetrics:
    """Test category metrics and analytics."""

    @pytest.mark.asyncio
    async def test_categorization_metrics(self, authenticated_owner_client: AsyncClient):
        """Test categorization metrics."""
        response = await authenticated_owner_client.get("/api/v1/categories/metrics/categorization")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)


class TestCategoryVendorFunctionality:
    """Test vendor-related category functionality."""

    @pytest.mark.asyncio
    async def test_get_vendor_groupings(self, authenticated_owner_client: AsyncClient):
        """Test getting vendor groupings."""
        response = await authenticated_owner_client.get("/api/v1/categories/vendors/groupings")
        
        assert response.status_code in [200, 404]

    @pytest.mark.asyncio
    async def test_get_user_vendor_mappings(self, authenticated_owner_client: AsyncClient):
        """Test getting user vendor mappings."""
        response = await authenticated_owner_client.get("/api/v1/categories/vendors/user-mappings")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    @pytest.mark.asyncio
    async def test_preview_vendor_lookup(self, authenticated_owner_client: AsyncClient):
        """Test vendor lookup preview."""
        response = await authenticated_owner_client.get(
            "/api/v1/categories/vendors/lookup-preview?vendor_name=TestVendor"
        )
        
        assert response.status_code in [200, 404]


class TestCategoryErrorHandling:
    """Test category error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_get_nonexistent_category(self, authenticated_owner_client: AsyncClient):
        """Test retrieving a non-existent category."""
        response = await authenticated_owner_client.get("/api/v1/categories/999999")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_unauthorized_access(self, async_client: AsyncClient):
        """Test accessing category endpoints without authentication."""
        response = await async_client.get("/api/v1/categories")
        
        assert response.status_code == 401
        data = response.json()
        assert data["detail"] == "Not authenticated"

    @pytest.mark.asyncio
    async def test_create_category_invalid_data(self, authenticated_owner_client: AsyncClient):
        """Test creating category with invalid data."""
        invalid_data = {
            "name": "",  # Empty name
            "color": "invalid-color",  # Invalid color format
            "gl_code": "invalid-gl-code-too-long"  # Invalid GL code
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/categories",
            json=invalid_data
        )
        
        assert response.status_code in [400, 422]
        data = response.json()
        assert "detail" in data


class TestCategoryLearning:
    """Test category learning functionality."""

    @pytest.mark.asyncio
    async def test_learn_from_onboarding(self, authenticated_owner_client: AsyncClient):
        """Test learning categories from onboarding data."""
        transactions_data = [
            {
                "description": "Office Depot Purchase",
                "amount": 45.99,
                "suggested_category": "Office Supplies"
            },
            {
                "description": "Starbucks Coffee",
                "amount": 5.50,
                "suggested_category": "Meals & Entertainment"
            }
        ]
        
        response = await authenticated_owner_client.post(
            "/api/v1/categories/learn-from-onboarding",
            json=transactions_data
        )
        
        assert response.status_code in [200, 400, 422]