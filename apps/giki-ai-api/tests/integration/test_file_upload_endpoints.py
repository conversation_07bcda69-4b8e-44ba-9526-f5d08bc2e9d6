"""
Integration tests for file upload endpoints.

Tests file upload, processing, schema interpretation, and export functionality.
"""

import io
import pytest
from httpx import AsyncClient
import tempfile
import os


class TestFileUploadBasic:
    """Test basic file upload functionality."""

    @pytest.mark.asyncio
    async def test_get_uploads_list(self, authenticated_owner_client: AsyncClient):
        """Test retrieving list of uploads."""
        response = await authenticated_owner_client.get("/api/v1/files")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    @pytest.mark.asyncio
    async def test_get_upload_by_id(self, authenticated_owner_client: AsyncClient):
        """Test retrieving specific upload by ID."""
        # First get the list to find a valid upload ID
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            response = await authenticated_owner_client.get(f"/api/v1/files/{upload_id}")
            
            assert response.status_code in [200, 404]
            if response.status_code == 200:
                data = response.json()
                assert data["id"] == upload_id
                assert "filename" in data

    @pytest.mark.asyncio
    async def test_upload_csv_file(self, authenticated_owner_client: AsyncClient):
        """Test uploading a CSV file."""
        # Create test CSV content
        csv_content = """Date,Description,Amount
2024-01-01,Office Supplies,50.00
2024-01-02,Coffee Shop,15.50
2024-01-03,Gas Station,75.25"""
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(csv_content)
            temp_path = f.name
        
        try:
            with open(temp_path, 'rb') as f:
                files = {"file": ("test_transactions.csv", f, "text/csv")}
                
                response = await authenticated_owner_client.post(
                    "/api/v1/files/upload",
                    files=files
                )
            
            # May succeed or fail depending on validation/processing
            assert response.status_code in [200, 201, 400, 422, 500]
            
            if response.status_code in [200, 201]:
                data = response.json()
                assert "upload_id" in data or "id" in data
                assert "filename" in data
        
        finally:
            # Clean up temporary file
            os.unlink(temp_path)

    @pytest.mark.asyncio
    async def test_upload_excel_file(self, authenticated_owner_client: AsyncClient):
        """Test uploading an Excel file."""
        # Create test Excel content using pandas
        import pandas as pd
        
        data = {
            'Date': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'Description': ['Office Supplies', 'Coffee Shop', 'Gas Station'],
            'Amount': [50.00, 15.50, 75.25]
        }
        df = pd.DataFrame(data)
        
        # Create temporary Excel file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
            df.to_excel(f.name, index=False)
            temp_path = f.name
        
        try:
            with open(temp_path, 'rb') as f:
                files = {"file": ("test_transactions.xlsx", f, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}
                
                response = await authenticated_owner_client.post(
                    "/api/v1/files/upload",
                    files=files
                )
            
            # May succeed or fail depending on validation/processing
            assert response.status_code in [200, 201, 400, 422, 500]
            
            if response.status_code in [200, 201]:
                data = response.json()
                assert "upload_id" in data or "id" in data
                assert "filename" in data
        
        finally:
            # Clean up temporary file
            os.unlink(temp_path)

    @pytest.mark.asyncio
    async def test_upload_invalid_file_type(self, authenticated_owner_client: AsyncClient):
        """Test uploading an invalid file type."""
        # Create text file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is not a valid financial file")
            temp_path = f.name
        
        try:
            with open(temp_path, 'rb') as f:
                files = {"file": ("invalid.txt", f, "text/plain")}
                
                response = await authenticated_owner_client.post(
                    "/api/v1/files/upload",
                    files=files
                )
            
            # Should fail with invalid file type
            assert response.status_code in [400, 422]
            
            data = response.json()
            assert "detail" in data
        
        finally:
            # Clean up temporary file
            os.unlink(temp_path)


class TestFileProcessing:
    """Test file processing functionality."""

    @pytest.mark.asyncio
    async def test_get_processing_status(self, authenticated_owner_client: AsyncClient):
        """Test retrieving processing status."""
        response = await authenticated_owner_client.get("/api/v1/files/processing-status")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_get_upload_status(self, authenticated_owner_client: AsyncClient):
        """Test retrieving upload status by ID."""
        # First get uploads list
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            response = await authenticated_owner_client.get(f"/api/v1/files/{upload_id}/status")
            
            assert response.status_code in [200, 404]
            if response.status_code == 200:
                data = response.json()
                assert "status" in data

    @pytest.mark.asyncio
    async def test_get_processing_details(self, authenticated_owner_client: AsyncClient):
        """Test retrieving processing details."""
        # First get uploads list
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            response = await authenticated_owner_client.get(f"/api/v1/files/{upload_id}/processing-details")
            
            assert response.status_code in [200, 404]
            if response.status_code == 200:
                data = response.json()
                assert isinstance(data, dict)


class TestSchemaInterpretation:
    """Test schema interpretation functionality."""

    @pytest.mark.asyncio
    async def test_get_schema_interpretation(self, authenticated_owner_client: AsyncClient):
        """Test retrieving schema interpretation."""
        # First get uploads list
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            response = await authenticated_owner_client.get(f"/api/v1/files/{upload_id}/schema")
            
            assert response.status_code in [200, 404, 422]
            if response.status_code == 200:
                data = response.json()
                assert "columns" in data or "interpretation" in data

    @pytest.mark.asyncio
    async def test_get_interpretation_storage(self, authenticated_owner_client: AsyncClient):
        """Test retrieving stored interpretation."""
        # First get uploads list
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            response = await authenticated_owner_client.get(f"/api/v1/files/{upload_id}/interpretation")
            
            assert response.status_code in [200, 404]
            if response.status_code == 200:
                data = response.json()
                assert isinstance(data, dict)

    @pytest.mark.asyncio
    async def test_confirm_interpretation(self, authenticated_owner_client: AsyncClient):
        """Test confirming schema interpretation."""
        # First get uploads list
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            # Test with sample interpretation data
            interpretation_data = {
                "column_mappings": {
                    "Date": "date",
                    "Description": "description", 
                    "Amount": "amount"
                },
                "user_confirmed": True
            }
            
            response = await authenticated_owner_client.post(
                f"/api/v1/files/{upload_id}/confirm-interpretation",
                json=interpretation_data
            )
            
            assert response.status_code in [200, 400, 404, 422]
            if response.status_code == 200:
                data = response.json()
                assert "success" in data or "confirmed" in data


class TestColumnMapping:
    """Test column mapping functionality."""

    @pytest.mark.asyncio
    async def test_get_columns_list(self, authenticated_owner_client: AsyncClient):
        """Test retrieving available columns."""
        # First get uploads list
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            response = await authenticated_owner_client.get(f"/api/v1/files/{upload_id}/columns")
            
            assert response.status_code in [200, 404]
            if response.status_code == 200:
                data = response.json()
                assert "columns" in data or isinstance(data, list)

    @pytest.mark.asyncio
    async def test_map_columns(self, authenticated_owner_client: AsyncClient):
        """Test column mapping functionality."""
        # First get uploads list
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            # Test with sample mapping data
            mapping_data = {
                "column_mappings": [
                    {"source_column": "Date", "target_field": "date"},
                    {"source_column": "Description", "target_field": "description"},
                    {"source_column": "Amount", "target_field": "amount"}
                ]
            }
            
            response = await authenticated_owner_client.post(
                f"/api/v1/files/{upload_id}/map",
                json=mapping_data
            )
            
            assert response.status_code in [200, 400, 404, 422]
            if response.status_code == 200:
                data = response.json()
                assert "processed" in data or "success" in data


class TestTransactionRetrieval:
    """Test transaction retrieval from uploads."""

    @pytest.mark.asyncio
    async def test_get_upload_transactions(self, authenticated_owner_client: AsyncClient):
        """Test retrieving transactions from upload."""
        # First get uploads list
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            response = await authenticated_owner_client.get(f"/api/v1/files/{upload_id}/transactions")
            
            assert response.status_code in [200, 404]
            if response.status_code == 200:
                data = response.json()
                assert "items" in data or "transactions" in data or isinstance(data, list)


class TestExportFunctionality:
    """Test export functionality."""

    @pytest.mark.asyncio
    async def test_export_transactions(self, authenticated_owner_client: AsyncClient):
        """Test exporting transactions."""
        response = await authenticated_owner_client.get("/api/v1/files/export-transactions")
        
        assert response.status_code in [200, 404, 422]
        if response.status_code == 200:
            # Should return either JSON data or file content
            assert response.headers.get("content-type") in [
                "application/json",
                "text/csv",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ]


class TestProductionDataUpload:
    """Test production data upload functionality."""

    @pytest.mark.asyncio
    async def test_upload_production_data(self, authenticated_owner_client: AsyncClient):
        """Test uploading production data."""
        # Create test CSV for production upload
        csv_content = """Date,Description,Amount,Account
2024-01-01,Office Supplies,50.00,Expenses
2024-01-02,Client Payment,1000.00,Revenue
2024-01-03,Office Rent,500.00,Expenses"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(csv_content)
            temp_path = f.name
        
        try:
            with open(temp_path, 'rb') as f:
                files = {"file": ("production_data.csv", f, "text/csv")}
                
                response = await authenticated_owner_client.post(
                    "/api/v1/files/upload-production-data",
                    files=files
                )
            
            # May succeed or fail depending on validation/processing
            assert response.status_code in [200, 201, 400, 403, 422, 500]
            
            if response.status_code in [200, 201]:
                data = response.json()
                assert "uploads" in data or "success" in data
        
        finally:
            # Clean up temporary file
            os.unlink(temp_path)


class TestErrorHandling:
    """Test error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_get_nonexistent_upload(self, authenticated_owner_client: AsyncClient):
        """Test retrieving a non-existent upload."""
        response = await authenticated_owner_client.get("/api/v1/files/nonexistent-id")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_unauthorized_access(self, async_client: AsyncClient):
        """Test accessing file endpoints without authentication."""
        response = await async_client.get("/api/v1/files")
        
        assert response.status_code == 401
        data = response.json()
        assert data["detail"] == "Not authenticated"

    @pytest.mark.asyncio
    async def test_upload_without_file(self, authenticated_owner_client: AsyncClient):
        """Test upload endpoint without file."""
        response = await authenticated_owner_client.post("/api/v1/files/upload")
        
        assert response.status_code in [400, 422]
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_upload_empty_file(self, authenticated_owner_client: AsyncClient):
        """Test uploading an empty file."""
        # Create empty file
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as f:
            temp_path = f.name
        
        try:
            with open(temp_path, 'rb') as f:
                files = {"file": ("empty.csv", f, "text/csv")}
                
                response = await authenticated_owner_client.post(
                    "/api/v1/files/upload",
                    files=files
                )
            
            # Should fail with empty file
            assert response.status_code in [400, 422]
            data = response.json()
            assert "detail" in data
        
        finally:
            # Clean up temporary file
            os.unlink(temp_path)

    @pytest.mark.asyncio
    async def test_map_columns_nonexistent_upload(self, authenticated_owner_client: AsyncClient):
        """Test column mapping with non-existent upload."""
        mapping_data = {
            "column_mappings": [
                {"source_column": "Date", "target_field": "date"}
            ]
        }
        
        response = await authenticated_owner_client.post(
            "/api/v1/files/nonexistent-id/map",
            json=mapping_data
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data

    @pytest.mark.asyncio
    async def test_map_columns_invalid_data(self, authenticated_owner_client: AsyncClient):
        """Test column mapping with invalid data."""
        # First get a valid upload ID
        list_response = await authenticated_owner_client.get("/api/v1/files")
        assert list_response.status_code == 200
        uploads = list_response.json()
        
        if uploads:
            upload_id = uploads[0]["id"]
            
            # Send invalid mapping data
            invalid_data = {
                "invalid_field": "invalid_value"
            }
            
            response = await authenticated_owner_client.post(
                f"/api/v1/files/{upload_id}/map",
                json=invalid_data
            )
            
            assert response.status_code in [400, 422]
            data = response.json()
            assert "detail" in data


class TestFileValidation:
    """Test file validation functionality."""

    @pytest.mark.asyncio
    async def test_upload_oversized_file(self, authenticated_owner_client: AsyncClient):
        """Test uploading an oversized file."""
        # Create large CSV content (this is a simplified test)
        large_content = "Date,Description,Amount\n" + "2024-01-01,Test,10.00\n" * 10000
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(large_content)
            temp_path = f.name
        
        try:
            with open(temp_path, 'rb') as f:
                files = {"file": ("large_file.csv", f, "text/csv")}
                
                response = await authenticated_owner_client.post(
                    "/api/v1/files/upload",
                    files=files
                )
            
            # May succeed or fail depending on size limits
            assert response.status_code in [200, 201, 400, 413, 422, 500]
            
            if response.status_code in [400, 413, 422]:
                data = response.json()
                assert "detail" in data
        
        finally:
            # Clean up temporary file
            os.unlink(temp_path)

    @pytest.mark.asyncio
    async def test_upload_corrupted_file(self, authenticated_owner_client: AsyncClient):
        """Test uploading a corrupted file."""
        # Create corrupted CSV content
        corrupted_content = b"Date,Description,Amount\n2024-01-01,Test\xFF\xFE,10.00"
        
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as f:
            f.write(corrupted_content)
            temp_path = f.name
        
        try:
            with open(temp_path, 'rb') as f:
                files = {"file": ("corrupted.csv", f, "text/csv")}
                
                response = await authenticated_owner_client.post(
                    "/api/v1/files/upload",
                    files=files
                )
            
            # Should handle gracefully
            assert response.status_code in [200, 201, 400, 422, 500]
            
            if response.status_code in [400, 422]:
                data = response.json()
                assert "detail" in data
        
        finally:
            # Clean up temporary file
            os.unlink(temp_path)