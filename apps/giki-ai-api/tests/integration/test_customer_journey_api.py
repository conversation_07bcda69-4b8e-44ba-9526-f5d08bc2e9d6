"""
Customer Journey API Integration Tests
Comprehensive testing of the complete customer journey through API endpoints
"""

import pytest
import asyncio
import json
from pathlib import Path
from typing import Dict, Any, List
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from giki_ai_api.domains.auth.models import User
from giki_ai_api.domains.transactions.models import Transaction
from giki_ai_api.domains.files.models import FileUpload
from giki_ai_api.domains.categories.models import Category


class CustomerJourneyAPIHelper:
    """Helper class for customer journey API testing"""
    
    def __init__(self, client: AsyncClient, db: AsyncSession):
        self.client = client
        self.db = db
        self.auth_headers = {}
        self.tenant_id = None
        self.user_id = None
    
    async def login_user(self, email: str, password: str) -> Dict[str, Any]:
        """Login user and set auth headers"""
        response = await self.client.post("/auth/token", data={
            "username": email,
            "password": password
        })
        assert response.status_code == 200
        
        token_data = response.json()
        self.auth_headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        
        # Get user info
        user_response = await self.client.get("/auth/me", headers=self.auth_headers)
        assert user_response.status_code == 200
        
        user_data = user_response.json()
        self.tenant_id = user_data["tenant_id"]
        self.user_id = user_data["id"]
        
        return token_data
    
    async def upload_file(self, file_path: str, file_type: str = "bank_statement") -> Dict[str, Any]:
        """Upload file for processing"""
        test_file_path = Path(__file__).parent.parent.parent / "libs" / "test-data" / file_path
        
        with open(test_file_path, "rb") as f:
            files = {"file": (test_file_path.name, f, "text/csv")}
            data = {"file_type": file_type}
            
            response = await self.client.post(
                "/files/upload", 
                files=files, 
                data=data,
                headers=self.auth_headers
            )
        
        assert response.status_code == 200
        return response.json()
    
    async def wait_for_processing(self, file_id: str, timeout: int = 60) -> Dict[str, Any]:
        """Wait for file processing to complete"""
        for _ in range(timeout):
            response = await self.client.get(
                f"/files/{file_id}/status",
                headers=self.auth_headers
            )
            assert response.status_code == 200
            
            status_data = response.json()
            if status_data["status"] in ["completed", "failed"]:
                return status_data
            
            await asyncio.sleep(1)
        
        raise TimeoutError(f"File processing did not complete within {timeout} seconds")
    
    async def get_dashboard_metrics(self) -> Dict[str, Any]:
        """Get dashboard metrics"""
        response = await self.client.get("/dashboard/metrics", headers=self.auth_headers)
        assert response.status_code == 200
        return response.json()
    
    async def get_transactions(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get transactions for review"""
        response = await self.client.get(
            f"/transactions?limit={limit}",
            headers=self.auth_headers
        )
        assert response.status_code == 200
        return response.json()["transactions"]
    
    async def export_transactions(self, format_type: str) -> bytes:
        """Export transactions in specified format"""
        response = await self.client.post(
            "/transactions/export",
            json={"format": format_type, "include_all": True},
            headers=self.auth_headers
        )
        assert response.status_code == 200
        return response.content


@pytest.mark.asyncio
class TestCustomerJourneyMISSetup:
    """Test complete MIS setup journey in 5 minutes"""
    
    async def test_owner_complete_mis_setup(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test owner completes full MIS setup journey"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        # Step 1: Login as owner
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Step 2: Verify initial empty dashboard
        initial_metrics = await helper.get_dashboard_metrics()
        assert initial_metrics["total_transactions"] == 0
        assert initial_metrics["categorization_rate"] == 0.0
        
        # Step 3: Upload bank statement
        upload_response = await helper.upload_file("synthetic-bank-data/hdfc_bank_transactions.csv")
        file_id = upload_response["file_id"]
        
        # Step 4: Wait for processing completion
        processing_status = await helper.wait_for_processing(file_id)
        assert processing_status["status"] == "completed"
        assert processing_status["transactions_processed"] > 0
        
        # Step 5: Verify categorization achieved baseline accuracy (87%+)
        final_metrics = await helper.get_dashboard_metrics()
        assert final_metrics["total_transactions"] > 0
        assert final_metrics["categorization_rate"] >= 87.0
        assert final_metrics["export_readiness"] >= 70
        
        # Step 6: Verify transactions are properly categorized
        transactions = await helper.get_transactions()
        assert len(transactions) > 0
        
        categorized_count = sum(1 for t in transactions if t["category"] is not None)
        categorization_rate = (categorized_count / len(transactions)) * 100
        assert categorization_rate >= 87.0
        
        # Step 7: Export to QuickBooks format
        export_data = await helper.export_transactions("quickbooks")
        assert len(export_data) > 0
        
        # Verify QBO format structure
        export_content = export_data.decode('utf-8')
        assert "!Type:Bank" in export_content or "OFXHEADER" in export_content
    
    async def test_progressive_enhancement_detection(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test detection and application of progressive enhancements"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Upload initial file
        upload1 = await helper.upload_file("synthetic-bank-data/hdfc_bank_transactions.csv")
        await helper.wait_for_processing(upload1["file_id"])
        
        baseline_metrics = await helper.get_dashboard_metrics()
        baseline_accuracy = baseline_metrics["categorization_rate"]
        
        # Upload additional historical data
        upload2 = await helper.upload_file("synthetic-bank-data/axis_bank_credit_card.csv")
        await helper.wait_for_processing(upload2["file_id"])
        
        # Check for enhancement opportunities
        response = await helper.client.get("/enhancements/opportunities", headers=helper.auth_headers)
        assert response.status_code == 200
        
        opportunities = response.json()
        assert len(opportunities) > 0
        
        # Should detect historical enhancement
        historical_enhancement = next(
            (opp for opp in opportunities if opp["type"] == "historical"), 
            None
        )
        assert historical_enhancement is not None
        assert historical_enhancement["potential_improvement"] >= 15
        
        # Apply enhancement
        apply_response = await helper.client.post(
            f"/enhancements/{historical_enhancement['id']}/apply",
            headers=helper.auth_headers
        )
        assert apply_response.status_code == 200
        
        # Wait for enhancement processing
        enhancement_id = apply_response.json()["enhancement_id"]
        for _ in range(60):  # 60 second timeout
            status_response = await helper.client.get(
                f"/enhancements/{enhancement_id}/status",
                headers=helper.auth_headers
            )
            status = status_response.json()
            if status["status"] == "completed":
                break
            await asyncio.sleep(1)
        
        # Verify improved accuracy
        enhanced_metrics = await helper.get_dashboard_metrics()
        improvement = enhanced_metrics["categorization_rate"] - baseline_accuracy
        assert improvement >= 10.0  # At least 10% improvement


@pytest.mark.asyncio
class TestRoleBasedAccess:
    """Test role-based access control through API"""
    
    async def test_manager_permissions(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test manager role has appropriate API access"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Should access dashboard
        metrics = await helper.get_dashboard_metrics()
        assert "total_transactions" in metrics
        
        # Should access transactions
        transactions = await helper.get_transactions()
        assert isinstance(transactions, list)
        
        # Should be able to export
        export_data = await helper.export_transactions("csv")
        assert len(export_data) > 0
        
        # Should NOT access admin endpoints
        admin_response = await helper.client.get("/admin/tenants", headers=helper.auth_headers)
        assert admin_response.status_code == 403
    
    async def test_employee_permissions(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test employee role has limited API access"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Should access dashboard (read-only)
        metrics = await helper.get_dashboard_metrics()
        assert "total_transactions" in metrics
        
        # Should NOT upload files
        upload_response = await helper.client.post(
            "/files/upload",
            files={"file": ("test.csv", b"data", "text/csv")},
            headers=helper.auth_headers
        )
        assert upload_response.status_code == 403
        
        # Should NOT export transactions
        export_response = await helper.client.post(
            "/transactions/export",
            json={"format": "csv"},
            headers=helper.auth_headers
        )
        assert export_response.status_code == 403


@pytest.mark.asyncio
class TestDataQualityAndExportValidation:
    """Test data quality and export functionality"""
    
    async def test_export_format_integrity(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test all export formats maintain data integrity"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Upload and process data
        upload_response = await helper.upload_file("synthetic-bank-data/hdfc_bank_transactions.csv")
        await helper.wait_for_processing(upload_response["file_id"])
        
        # Test each export format
        formats = ["quickbooks", "xero", "csv", "tally", "excel"]
        
        for format_type in formats:
            export_data = await helper.export_transactions(format_type)
            assert len(export_data) > 0
            
            # Verify format-specific headers
            content = export_data.decode('utf-8', errors='ignore')
            
            if format_type == "csv":
                assert "Date" in content and "Amount" in content
            elif format_type == "quickbooks":
                assert "!Type:" in content or "OFXHEADER" in content
            elif format_type == "xero":
                assert "Date" in content and "Reference" in content
    
    async def test_categorization_accuracy_standards(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test categorization meets MIS accuracy standards"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Upload test data
        upload_response = await helper.upload_file("synthetic-bank-data/hdfc_bank_transactions.csv")
        await helper.wait_for_processing(upload_response["file_id"])
        
        # Verify accuracy metrics
        metrics = await helper.get_dashboard_metrics()
        assert metrics["categorization_rate"] >= 87.0
        
        # Verify transaction-level accuracy
        transactions = await helper.get_transactions()
        
        # Check AI confidence scores
        high_confidence_count = sum(
            1 for t in transactions 
            if t.get("ai_confidence", 0) >= 0.8
        )
        high_confidence_rate = (high_confidence_count / len(transactions)) * 100
        assert high_confidence_rate >= 80.0  # 80% high confidence
        
        # Verify GL code assignments
        transactions_with_gl = sum(
            1 for t in transactions 
            if t.get("gl_code") is not None
        )
        gl_assignment_rate = (transactions_with_gl / len(transactions)) * 100
        assert gl_assignment_rate >= 95.0  # 95% GL code assignment


@pytest.mark.asyncio
class TestRealtimeUpdatesAndWebSocket:
    """Test real-time features and WebSocket integration"""
    
    async def test_file_processing_progress(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test file processing progress updates"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Start file upload
        upload_response = await helper.upload_file("synthetic-bank-data/hdfc_bank_transactions.csv")
        file_id = upload_response["file_id"]
        
        # Monitor processing progress
        progress_updates = []
        start_time = asyncio.get_event_loop().time()
        
        while True:
            response = await helper.client.get(
                f"/files/{file_id}/status",
                headers=helper.auth_headers
            )
            status_data = response.json()
            progress_updates.append(status_data)
            
            if status_data["status"] in ["completed", "failed"]:
                break
                
            # Timeout after 60 seconds
            if asyncio.get_event_loop().time() - start_time > 60:
                break
                
            await asyncio.sleep(1)
        
        # Verify progress was tracked
        assert len(progress_updates) > 1
        assert progress_updates[0]["status"] == "processing"
        assert progress_updates[-1]["status"] == "completed"
        
        # Verify progress metrics
        final_status = progress_updates[-1]
        assert final_status["transactions_processed"] > 0
        assert final_status["accuracy_achieved"] >= 85.0
    
    async def test_enhancement_detection_notifications(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test enhancement opportunity detection"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Upload first file
        upload1 = await helper.upload_file("synthetic-bank-data/hdfc_bank_transactions.csv")
        await helper.wait_for_processing(upload1["file_id"])
        
        # Upload second file (should trigger enhancement detection)
        upload2 = await helper.upload_file("synthetic-bank-data/axis_bank_credit_card.csv")
        await helper.wait_for_processing(upload2["file_id"])
        
        # Check for enhancement notifications
        response = await helper.client.get("/enhancements/opportunities", headers=helper.auth_headers)
        opportunities = response.json()
        
        assert len(opportunities) > 0
        
        # Verify enhancement types
        enhancement_types = [opp["type"] for opp in opportunities]
        assert "historical" in enhancement_types
        
        # Verify potential improvements
        for opp in opportunities:
            assert opp["potential_improvement"] > 0
            assert "description" in opp


@pytest.mark.asyncio
class TestPerformanceAndScaling:
    """Test performance and scaling capabilities"""
    
    async def test_large_file_processing(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test processing of large transaction files"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        start_time = asyncio.get_event_loop().time()
        
        # Upload large file (>1000 transactions)
        upload_response = await helper.upload_file("synthetic-bank-data/hdfc_bank_transactions.csv")
        processing_status = await helper.wait_for_processing(upload_response["file_id"], timeout=120)
        
        processing_time = asyncio.get_event_loop().time() - start_time
        
        # Should complete within 2 minutes
        assert processing_time < 120
        assert processing_status["status"] == "completed"
        assert processing_status["transactions_processed"] > 100
        
        # Verify quality maintained under load
        metrics = await helper.get_dashboard_metrics()
        assert metrics["categorization_rate"] >= 85.0
    
    async def test_concurrent_user_processing(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test concurrent file processing by multiple users"""
        # This test would require multiple tenant setup
        # Simplified version testing single tenant concurrent operations
        
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Simulate multiple concurrent requests
        tasks = []
        for i in range(3):
            task = helper.get_dashboard_metrics()
            tasks.append(task)
        
        # All requests should complete successfully
        results = await asyncio.gather(*tasks)
        assert len(results) == 3
        
        for result in results:
            assert "total_transactions" in result


@pytest.mark.asyncio
class TestErrorHandlingAndRecovery:
    """Test error handling and recovery mechanisms"""
    
    async def test_invalid_file_format_handling(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test handling of invalid file formats"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Try to upload invalid file format
        invalid_files = [
            ("test.pdf", b"PDF content", "application/pdf"),
            ("test.txt", b"Plain text", "text/plain"),
            ("test.jpg", b"Image data", "image/jpeg"),
        ]
        
        for filename, content, mime_type in invalid_files:
            files = {"file": (filename, content, mime_type)}
            
            response = await helper.client.post(
                "/files/upload",
                files=files,
                headers=helper.auth_headers
            )
            
            assert response.status_code == 400
            error_data = response.json()
            assert "format" in error_data["detail"].lower()
    
    async def test_authentication_error_handling(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test authentication error scenarios"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        # Test invalid credentials
        response = await helper.client.post("/auth/token", data={
            "username": "<EMAIL>",
            "password": "wrongpassword"
        })
        assert response.status_code == 401
        
        # Test expired token (simulate with invalid token)
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        
        response = await helper.client.get("/dashboard/metrics", headers=invalid_headers)
        assert response.status_code == 401
    
    async def test_data_validation_and_cleanup(self, async_client: AsyncClient, db_session: AsyncSession):
        """Test data validation and cleanup processes"""
        helper = CustomerJourneyAPIHelper(async_client, db_session)
        
        await helper.login_user("<EMAIL>", "GikiTest2025Secure")
        
        # Upload file with some invalid data
        upload_response = await helper.upload_file("test-data/mis-testing/quick-setup-50-transactions.csv")
        processing_status = await helper.wait_for_processing(upload_response["file_id"])
        
        # Should complete with warnings about data issues
        assert processing_status["status"] == "completed"
        
        # Check for data validation warnings
        if "warnings" in processing_status:
            assert isinstance(processing_status["warnings"], list)
        
        # Verify clean data in final results
        transactions = await helper.get_transactions()
        for transaction in transactions:
            assert transaction["amount"] is not None
            assert transaction["date"] is not None
            assert len(transaction["description"]) > 0