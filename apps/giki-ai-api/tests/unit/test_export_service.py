"""
Unit tests for ExportService.

Tests transaction export functionality, format generation, and data transformation.
"""

import io
import pytest
from datetime import datetime, date
from unittest.mock import AsyncMock, Mock, patch
from asyncpg import Connection
import pandas as pd

from giki_ai_api.domains.exports.service import ExportService
from giki_ai_api.domains.exports.export_formats import ExportFormat, ExportFormatSpec
from giki_ai_api.shared.exceptions import ServiceError, ValidationError


# Shared fixtures
@pytest.fixture
def mock_connection():
    """Create a mock database connection."""
    return Mock(spec=Connection)

@pytest.fixture
def export_service():
    """Create ExportService instance."""
    return ExportService()

@pytest.fixture
def sample_transactions():
    """Sample transaction data for testing."""
    return [
        {
            "id": 1,
            "date": date(2024, 1, 1),
            "description": "Office Supplies",
            "amount": 50.00,
            "vendor": "Office Depot",
            "vendor_name": "Office Depot Inc",
            "transaction_type": "debit",
            "ai_category": "Office Expenses",
            "ai_confidence": 0.95,
            "category_name": "Office Expenses",
            "category_type": "Expense",
            "category_gl_code": "5001",
            "debit_amount": 50.00,
            "credit_amount": 0,
            "reference_number": "REF001",
            "memo": "Office supplies purchase",
            "payment_method": "Credit Card",
            "gl_code": "5001",
            "account_name": "Business Account",
            "account_number": "123456"
        },
        {
            "id": 2,
            "date": date(2024, 1, 2),
            "description": "Client Payment",
            "amount": 1000.00,
            "vendor": "ABC Corp",
            "vendor_name": "ABC Corporation",
            "transaction_type": "credit",
            "ai_category": "Revenue",
            "ai_confidence": 0.98,
            "category_name": "Service Revenue",
            "category_type": "Income",
            "category_gl_code": "4001",
            "debit_amount": 0,
            "credit_amount": 1000.00,
            "reference_number": "PAY002",
            "memo": "Service payment received",
            "payment_method": "Bank Transfer",
            "gl_code": "4001",
            "account_name": "Business Account",
            "account_number": "123456"
        }
    ]

@pytest.fixture
def sample_format_spec():
    """Sample export format specification."""
    return ExportFormatSpec(
        name="Test Format",
        file_extension="csv",
        delimiter=",",
        required_fields=["date", "description", "amount"],
        field_mappings={
            "date": "Date",
            "description": "Description", 
            "amount": "Amount",
            "category_name": "Category"
        },
        column_order=["Date", "Description", "Amount", "Category"],
        date_format="%Y-%m-%d",
        amount_format="0.00",
        has_header=True
    )

@pytest.fixture
def sample_filters():
    """Sample export filters."""
    return {
        "date_from": date(2024, 1, 1),
        "date_to": date(2024, 1, 31),
        "include_uncategorized": True
    }


class TestExportService:
    """Test ExportService class."""
    pass


class TestTransactionExport:
    """Test main transaction export functionality."""

    @pytest.mark.asyncio
    async def test_export_transactions_success(self, export_service, mock_connection, sample_transactions, sample_format_spec):
        """Test successful transaction export."""
        tenant_id = 1
        format_id = ExportFormat.QUICKBOOKS_CSV
        
        # Mock dependencies
        with patch.multiple(
            export_service,
            _fetch_transactions=AsyncMock(return_value=sample_transactions),
            _transform_transactions=Mock(return_value=sample_transactions),
            _generate_csv=Mock(return_value=b"test,csv,content"),
            _get_content_type=Mock(return_value="text/csv")
        ):
            # Mock format registry
            with patch('giki_ai_api.domains.exports.service.export_format_registry') as mock_registry:
                mock_registry.get_format.return_value = sample_format_spec
                
                content, filename, content_type = await export_service.export_transactions(
                    mock_connection, tenant_id, format_id
                )
                
                assert content == b"test,csv,content"
                assert filename.startswith("giki_export_test_format_")
                assert filename.endswith(".csv")
                assert content_type == "text/csv"

    @pytest.mark.asyncio
    async def test_export_transactions_unsupported_format(self, export_service, mock_connection):
        """Test export with unsupported format."""
        tenant_id = 1
        format_id = "unsupported_format"
        
        # Mock format registry to return None
        with patch('giki_ai_api.domains.exports.service.export_format_registry') as mock_registry:
            mock_registry.get_format.return_value = None
            
            with pytest.raises(ServiceError) as exc_info:
                await export_service.export_transactions(mock_connection, tenant_id, format_id)
            
            assert "Unsupported export format" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_export_transactions_no_data(self, export_service, mock_connection, sample_format_spec):
        """Test export when no transactions found."""
        tenant_id = 1
        format_id = ExportFormat.QUICKBOOKS_CSV
        
        # Mock empty transaction fetch
        with patch.object(export_service, '_fetch_transactions', AsyncMock(return_value=[])):
            with patch('giki_ai_api.domains.exports.service.export_format_registry') as mock_registry:
                mock_registry.get_format.return_value = sample_format_spec
                
                with pytest.raises(ServiceError) as exc_info:
                    await export_service.export_transactions(mock_connection, tenant_id, format_id)
                
                assert "No transactions found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_export_transactions_with_filters(self, export_service, mock_connection, sample_transactions, sample_format_spec, sample_filters):
        """Test export with filters applied."""
        tenant_id = 1
        format_id = ExportFormat.QUICKBOOKS_CSV
        
        # Mock dependencies directly on the service instance
        export_service._fetch_transactions = AsyncMock(return_value=sample_transactions)
        export_service._transform_transactions = Mock(return_value=sample_transactions)
        export_service._generate_csv = Mock(return_value=b"filtered,content")
        export_service._get_content_type = Mock(return_value="text/csv")
        
        mocks = {
            '_fetch_transactions': export_service._fetch_transactions,
            '_transform_transactions': export_service._transform_transactions,
            '_generate_csv': export_service._generate_csv,
            '_get_content_type': export_service._get_content_type
        }
        
        with patch('giki_ai_api.domains.exports.service.export_format_registry') as mock_registry:
            mock_registry.get_format.return_value = sample_format_spec
            
            await export_service.export_transactions(
                mock_connection, tenant_id, format_id, filters=sample_filters
            )
            
            # Verify filters were passed to fetch
            mocks['_fetch_transactions'].assert_called_once_with(
                mock_connection, tenant_id, sample_filters
            )


class TestTransactionFetching:
    """Test transaction fetching from database."""

    @pytest.mark.asyncio
    async def test_fetch_transactions_success(self, export_service, mock_connection):
        """Test successful transaction fetching."""
        tenant_id = 1
        
        # Mock database response
        mock_rows = [
            {
                "id": 1,
                "date": date(2024, 1, 1),
                "description": "Test Transaction",
                "amount": 100.00,
                "vendor": "Test Vendor",
                "transaction_type": "debit",
                "ai_category": "Test Category",
                "category_name": "Test Category",
                "category_gl_code": "5001"
            }
        ]
        mock_connection.fetch = AsyncMock(return_value=mock_rows)
        
        result = await export_service._fetch_transactions(mock_connection, tenant_id)
        
        assert len(result) == 1
        assert result[0]["id"] == 1
        assert result[0]["debit_amount"] == 100.00
        assert result[0]["credit_amount"] == 0

    @pytest.mark.asyncio
    async def test_fetch_transactions_with_filters(self, export_service, mock_connection, sample_filters):
        """Test transaction fetching with filters."""
        tenant_id = 1
        mock_connection.fetch = AsyncMock(return_value=[])
        
        await export_service._fetch_transactions(mock_connection, tenant_id, sample_filters)
        
        # Verify query was called with parameters
        mock_connection.fetch.assert_called_once()
        call_args = mock_connection.fetch.call_args[0]
        assert tenant_id in call_args
        assert "AND t.date >=" in call_args[0]
        assert "AND t.date <=" in call_args[0]

    @pytest.mark.asyncio
    async def test_fetch_transactions_credit_calculation(self, export_service, mock_connection):
        """Test debit/credit calculation for different transaction types."""
        tenant_id = 1
        
        # Mock transactions with different types
        mock_rows = [
            {
                "id": 1,
                "amount": 100.00,
                "transaction_type": "debit",
                "date": date(2024, 1, 1),
                "description": "Debit Transaction",
                "category_name": "Expense"
            },
            {
                "id": 2,
                "amount": 200.00,
                "transaction_type": "credit",
                "date": date(2024, 1, 1),
                "description": "Credit Transaction",
                "category_name": "Income"
            },
            {
                "id": 3,
                "amount": -50.00,
                "transaction_type": "debit",
                "date": date(2024, 1, 1),
                "description": "Negative Amount",
                "category_name": "Expense"
            }
        ]
        mock_connection.fetch = AsyncMock(return_value=mock_rows)
        
        result = await export_service._fetch_transactions(mock_connection, tenant_id)
        
        # Check debit transaction
        assert result[0]["debit_amount"] == 100.00
        assert result[0]["credit_amount"] == 0
        
        # Check credit transaction
        assert result[1]["debit_amount"] == 0
        assert result[1]["credit_amount"] == 200.00
        
        # Check negative amount (treated as debit)
        assert result[2]["debit_amount"] == 50.00
        assert result[2]["credit_amount"] == 0

    @pytest.mark.asyncio
    async def test_fetch_transactions_category_hierarchy(self, export_service, mock_connection):
        """Test category name resolution hierarchy."""
        tenant_id = 1
        
        # Mock transaction with various category fields
        mock_rows = [
            {
                "id": 1,
                "date": date(2024, 1, 1),
                "description": "Test",
                "amount": 100.00,
                "transaction_type": "debit",
                "category_name": None,
                "ai_category": "AI Category",
                "ai_suggested_category": "Suggested Category",
                "original_category": "Original Category"
            }
        ]
        mock_connection.fetch = AsyncMock(return_value=mock_rows)
        
        result = await export_service._fetch_transactions(mock_connection, tenant_id)
        
        # Should use AI category when category_name is None
        assert result[0]["category_name"] == "AI Category"


class TestDataTransformation:
    """Test data transformation for different export formats."""

    def test_transform_transactions_basic(self, export_service, sample_transactions, sample_format_spec):
        """Test basic transaction transformation."""
        result = export_service._transform_transactions(sample_transactions, sample_format_spec)
        
        assert len(result) == 2
        assert all("Date" in row for row in result)
        assert all("Description" in row for row in result)
        assert all("Amount" in row for row in result)

    def test_transform_transactions_field_mapping(self, export_service, sample_transactions):
        """Test field mapping during transformation."""
        format_spec = ExportFormatSpec(
            name="Custom Format",
            file_extension="csv",
            field_mappings={
                "description": "Transaction Description",
                "amount": "Transaction Amount",
                "vendor_name": "Vendor"
            },
            has_header=True
        )
        
        result = export_service._transform_transactions(sample_transactions, format_spec)
        
        assert "Transaction Description" in result[0]
        assert "Transaction Amount" in result[0]
        assert "Vendor" in result[0]
        assert result[0]["Transaction Description"] == "Office Supplies"

    def test_transform_transactions_with_options(self, export_service, sample_transactions, sample_format_spec):
        """Test transformation with export options."""
        options = {
            "include_memo": True,
            "split_amounts": True
        }
        
        result = export_service._transform_transactions(sample_transactions, sample_format_spec, options)
        
        assert len(result) == 2
        # Verify options were considered in transformation
        assert all(isinstance(row, dict) for row in result)


class TestFileGeneration:
    """Test file content generation for different formats."""

    def test_generate_csv_basic(self, export_service, sample_format_spec):
        """Test basic CSV generation."""
        export_data = [
            {"Date": "2024-01-01", "Description": "Test", "Amount": "100.00"},
            {"Date": "2024-01-02", "Description": "Test2", "Amount": "200.00"}
        ]
        
        result = export_service._generate_csv(export_data, sample_format_spec)
        
        assert isinstance(result, bytes)
        content = result.decode('utf-8')
        assert "Date,Description,Amount" in content
        assert "2024-01-01,Test,100.00" in content

    def test_generate_csv_custom_delimiter(self, export_service):
        """Test CSV generation with custom delimiter."""
        format_spec = ExportFormatSpec(
            name="Tab Delimited",
            file_extension="csv",
            delimiter="\t",
            column_order=["Date", "Description", "Amount"],
            has_header=True
        )
        
        export_data = [
            {"Date": "2024-01-01", "Description": "Test", "Amount": "100.00"}
        ]
        
        result = export_service._generate_csv(export_data, format_spec)
        content = result.decode('utf-8')
        
        assert "\t" in content
        assert "Date\tDescription\tAmount" in content

    def test_generate_excel_basic(self, export_service, sample_format_spec):
        """Test basic Excel generation."""
        export_data = [
            {"Date": "2024-01-01", "Description": "Test", "Amount": "100.00"}
        ]
        
        # Mock pandas to_excel
        with patch('pandas.DataFrame.to_excel') as mock_to_excel:
            mock_buffer = io.BytesIO(b"excel content")
            mock_to_excel.return_value = None
            
            with patch('io.BytesIO') as mock_io:
                mock_io.return_value = mock_buffer
                mock_buffer.getvalue = Mock(return_value=b"excel content")
                
                result = export_service._generate_excel(export_data, sample_format_spec)
                
                assert result == b"excel content"

    def test_generate_iif_basic(self, export_service):
        """Test basic IIF (QuickBooks) generation."""
        format_spec = ExportFormatSpec(
            name="QuickBooks IIF",
            file_extension="iif"
        )
        
        export_data = [
            {
                "date": "2024-01-01",
                "description": "Test Transaction",
                "debit_amount": 100.00,
                "credit_amount": 0,
                "account_name": "Checking",
                "category_name": "Office Expenses"
            }
        ]
        
        result = export_service._generate_iif(export_data, format_spec)
        content = result.decode('utf-8')
        
        assert "!HDR\tPROD\tVER\tREL\tIIFVER\tDATE\tTIME\tACCNT" in content
        assert "!TRNS\tTRNSTYPE\tDATE\tACCNT" in content

    def test_generate_xml_basic(self, export_service):
        """Test basic XML generation."""
        format_spec = ExportFormatSpec(
            name="XML Export",
            file_extension="xml"
        )
        
        export_data = [
            {"date": "2024-01-01", "description": "Test", "amount": "100.00"}
        ]
        
        result = export_service._generate_xml(export_data, format_spec)
        content = result.decode('utf-8')
        
        assert "<?xml version=" in content
        assert "<transactions>" in content
        assert "<transaction>" in content
        assert "<date>2024-01-01</date>" in content


class TestHelperMethods:
    """Test helper methods."""

    def test_get_content_type_csv(self, export_service):
        """Test content type for CSV files."""
        result = export_service._get_content_type("csv")
        assert result == "text/csv"

    def test_get_content_type_excel(self, export_service):
        """Test content type for Excel files."""
        result = export_service._get_content_type("xlsx")
        assert result == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

    def test_get_content_type_xml(self, export_service):
        """Test content type for XML files."""
        result = export_service._get_content_type("xml")
        assert result == "application/xml"

    def test_get_content_type_unknown(self, export_service):
        """Test content type for unknown extensions."""
        result = export_service._get_content_type("unknown")
        assert result == "application/octet-stream"


class TestErrorHandling:
    """Test error handling scenarios."""

    @pytest.mark.asyncio
    async def test_export_transactions_database_error(self, export_service, mock_connection, sample_format_spec):
        """Test handling of database errors during export."""
        tenant_id = 1
        format_id = ExportFormat.QUICKBOOKS_CSV
        
        # Mock database error
        with patch.object(export_service, '_fetch_transactions', AsyncMock(side_effect=Exception("Database error"))):
            with patch('giki_ai_api.domains.exports.service.export_format_registry') as mock_registry:
                mock_registry.get_format.return_value = sample_format_spec
                
                with pytest.raises(ServiceError) as exc_info:
                    await export_service.export_transactions(mock_connection, tenant_id, format_id)
                
                assert "Export failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_fetch_transactions_sql_error(self, export_service, mock_connection):
        """Test handling of SQL errors during fetch."""
        tenant_id = 1
        
        # Mock SQL error
        mock_connection.fetch = AsyncMock(side_effect=Exception("SQL error"))
        
        with pytest.raises(Exception):
            await export_service._fetch_transactions(mock_connection, tenant_id)

    def test_transform_transactions_invalid_data(self, export_service, sample_format_spec):
        """Test transformation with invalid transaction data."""
        invalid_transactions = [
            {"id": 1},  # Missing required fields
            {"description": "Test"}  # Missing other required fields
        ]
        
        # Should handle gracefully by using default values
        result = export_service._transform_transactions(invalid_transactions, sample_format_spec)
        
        assert len(result) == 2
        assert all(isinstance(row, dict) for row in result)

    def test_generate_csv_empty_data(self, export_service, sample_format_spec):
        """Test CSV generation with empty data."""
        result = export_service._generate_csv([], sample_format_spec)
        
        assert isinstance(result, bytes)
        content = result.decode('utf-8')
        
        # Should still have header if format specifies it
        if sample_format_spec.has_header:
            assert len(content.strip()) > 0


class TestFormatSpecificBehavior:
    """Test behavior specific to different export formats."""

    def test_quickbooks_specific_formatting(self, export_service, sample_transactions):
        """Test QuickBooks-specific formatting requirements."""
        format_spec = ExportFormatSpec(
            name="QuickBooks CSV",
            file_extension="csv",
            field_mappings={
                "date": "Date",
                "description": "Description",
                "debit_amount": "Debit",
                "credit_amount": "Credit",
                "category_name": "Account"
            },
            date_format="%m/%d/%Y",
            amount_format="0.00"
        )
        
        result = export_service._transform_transactions(sample_transactions, format_spec)
        
        assert len(result) == 2
        assert "Date" in result[0]
        assert "Debit" in result[0]
        assert "Credit" in result[0]

    def test_sage_specific_formatting(self, export_service, sample_transactions):
        """Test Sage-specific formatting requirements."""
        format_spec = ExportFormatSpec(
            name="Sage CSV",
            file_extension="csv",
            field_mappings={
                "date": "Transaction Date",
                "reference_number": "Reference",
                "description": "Details",
                "debit_amount": "Debit Amount",
                "credit_amount": "Credit Amount"
            },
            date_format="%d/%m/%Y"
        )
        
        result = export_service._transform_transactions(sample_transactions, format_spec)
        
        assert "Transaction Date" in result[0]
        assert "Reference" in result[0]
        assert "Details" in result[0]

    def test_xero_specific_formatting(self, export_service, sample_transactions):
        """Test Xero-specific formatting requirements."""
        format_spec = ExportFormatSpec(
            name="Xero CSV",
            file_extension="csv",
            field_mappings={
                "date": "*Date",
                "description": "*Description",
                "amount": "*Amount",
                "category_name": "*Account",
                "reference_number": "Reference"
            },
            required_fields=["*Date", "*Description", "*Amount", "*Account"]
        )
        
        result = export_service._transform_transactions(sample_transactions, format_spec)
        
        assert "*Date" in result[0]
        assert "*Description" in result[0]
        assert "*Amount" in result[0]
        assert "*Account" in result[0]