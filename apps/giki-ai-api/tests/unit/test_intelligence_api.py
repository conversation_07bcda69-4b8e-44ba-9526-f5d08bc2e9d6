"""
Comprehensive unit tests for Intelligence API - AI/ML transaction processing.

Tests the complete AI intelligence workflow including entity extraction,
amount processing, description normalization, and batch analysis.
"""

import json
from datetime import datetime, timezone
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from giki_ai_api.core.main import app
from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session
from giki_ai_api.domains.auth.models import User
from giki_ai_api.domains.auth.secure_auth import get_current_active_user


@pytest.fixture
def mock_db_connection():
    """Mock database connection."""
    return AsyncMock()


@pytest.fixture
def mock_current_user():
    """Mock authenticated user."""
    user = Mock(spec=User)
    user.id = 1
    user.email = "<EMAIL>"
    user.is_active = True
    return user


@pytest.fixture
def mock_intelligence_service():
    """Mock intelligence service."""
    return AsyncMock()

@pytest.fixture
def client(mock_current_user, mock_db_connection, mock_intelligence_service):
    """Create test client with overridden dependencies."""
    from giki_ai_api.core.dependencies import get_intelligence_service
    
    app.dependency_overrides[get_current_active_user] = lambda: mock_current_user
    app.dependency_overrides[get_current_tenant_id] = lambda: 1
    app.dependency_overrides[get_db_session] = lambda: mock_db_connection
    app.dependency_overrides[get_intelligence_service] = lambda: mock_intelligence_service
    
    test_client = TestClient(app)
    yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def sample_transaction_descriptions():
    """Sample transaction descriptions for testing."""
    return [
        "AMAZON WEB SERVICES AWS COMPUTE",
        "OFFICE DEPOT #1234 SUPPLIES",
        "STARBUCKS #567 COFFEE MEETING",
        "UBER TRIP 12/15 BUSINESS TRAVEL",
        "MICROSOFT OFFICE 365 SUBSCRIPTION"
    ]


class TestAccountingSystemDetection:
    """Test accounting system detection from file headers."""

    def test_detect_accounting_system_quickbooks(
        self, client: TestClient, mock_db_connection: AsyncMock, mock_intelligence_service: AsyncMock, auth_headers: dict
    ):
        """Test detecting QuickBooks format from headers."""
        mock_intelligence_service.detect_accounting_system.return_value = {
                "system_type": "quickbooks",
                "cultural_context": "us",
                "confidence": 0.95,
                "features": {
                    "columns": ["Date", "Num", "Description", "Account", "Amount"],
                    "format_type": "quickbooks_csv",
                    "date_format": "MM/dd/yyyy"
                },
                "reasoning": "Standard QuickBooks export format detected with characteristic columns",
                "institution_info": {
                    "name": "QuickBooks",
                    "type": "accounting_software"
                }
        }
        
        detection_request = {
            "columns": ["Date", "Num", "Description", "Account", "Amount"],
            "sample_data": {
                "Date": "01/15/2024", "Description": "AMAZON WEB SERVICES", "Amount": "-250.50"
            },
            "filename": "quickbooks_export.csv"
        }
        
        response = client.post(
            "/api/v1/intelligence/detect-accounting-system",
            json=detection_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["system_type"] == "quickbooks"
        assert data["cultural_context"] == "us"
        assert data["confidence"] == 0.95
        assert "features" in data
        assert "reasoning" in data
        assert "institution_info" in data

    def test_detect_accounting_system_unknown(
        self, client: TestClient, mock_db_connection: AsyncMock, mock_intelligence_service: AsyncMock, auth_headers: dict
    ):
        """Test handling unknown accounting system format."""
        mock_intelligence_service.detect_accounting_system.return_value = {
                "system_type": "unknown",
                "cultural_context": "global",
                "confidence": 0.2,
                "features": {
                    "columns": ["Col1", "Col2", "Col3"],
                    "format_type": "unknown"
                },
                "reasoning": "Cannot identify accounting system from provided columns",
                "institution_info": {
                    "name": "Unknown",
                    "type": "unidentified"
                }
        }
        
        detection_request = {
            "columns": ["Col1", "Col2", "Col3"],
            "sample_data": {"Col1": "data1", "Col2": "data2", "Col3": "data3"},
            "filename": "unknown_format.csv"
        }
        
        response = client.post(
            "/api/v1/intelligence/detect-accounting-system",
            json=detection_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["system_type"] == "unknown"
        assert data["cultural_context"] == "global"
        assert data["confidence"] < 0.5
        assert "features" in data
        assert "reasoning" in data


class TestEntityExtraction:
    """Test entity extraction from transaction descriptions."""

    def test_extract_entities_vendor_detection(
        self, client: TestClient, mock_db_connection: AsyncMock, mock_intelligence_service: AsyncMock, auth_headers: dict
    ):
        """Test extracting vendor entities from descriptions."""
        # Mock the extract_entities method to return entities in the expected format
        mock_intelligence_service.extract_entities.return_value = {
            "merchants": ["AMAZON WEB SERVICES", "AWS"],
            "account_numbers": [],
            "amounts": []
        }
        
        extraction_request = {
            "descriptions": ["AMAZON WEB SERVICES AWS COMPUTE"],
            "cultural_context": "global"
        }
        
        response = client.post(
            "/api/v1/intelligence/extract-entities",
            json=extraction_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "entities" in data
        assert "total_processed" in data
        assert data["total_processed"] == 1
        assert len(data["entities"]) == 3  # merchants, account_numbers, amounts
        assert "merchants" in data["entities"]
        assert "AMAZON WEB SERVICES" in data["entities"]["merchants"]

    def test_extract_entities_location_detection(
        self, client: TestClient, mock_db_connection: AsyncMock, mock_intelligence_service: AsyncMock, auth_headers: dict
    ):
        """Test extracting location entities from descriptions."""
        # Mock the extract_entities method to return entities in the expected format
        mock_intelligence_service.extract_entities.return_value = {
            "merchants": ["STARBUCKS"],
            "account_numbers": [],
            "amounts": []
        }
        
        extraction_request = {
            "descriptions": ["STARBUCKS #567 COFFEE MEETING"],
            "cultural_context": "global"
        }
        
        response = client.post(
            "/api/v1/intelligence/extract-entities",
            json=extraction_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "entities" in data
        assert "total_processed" in data
        assert data["total_processed"] == 1
        assert len(data["entities"]) == 3  # merchants, account_numbers, amounts
        assert "merchants" in data["entities"]
        assert "STARBUCKS" in data["entities"]["merchants"]


class TestAmountProcessing:
    """Test amount field processing and normalization."""

    def test_process_amount_currency_detection(
        self, client: TestClient, mock_db_connection: AsyncMock, mock_intelligence_service: AsyncMock, auth_headers: dict
    ):
        """Test processing amounts with currency detection."""
        # Mock the process_amount_fields method to return the expected response format
        mock_intelligence_service.process_amount_fields.return_value = {
            "amount": 250.50,
            "transaction_type": "debit",
            "currency": "USD",
            "cash_flow_direction": "outflow"
        }
        
        processing_request = {
            "transaction_data": {
                "amount": "$250.50",
                "description": "AMAZON WEB SERVICES",
                "date": "2024-01-15"
            },
            "column_mapping": {
                "amount": "Amount",
                "description": "Description",
                "date": "Date"
            },
            "accounting_system": {
                "system_type": "quickbooks",
                "cultural_context": "us"
            }
        }
        
        response = client.post(
            "/api/v1/intelligence/process-amount",
            json=processing_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["amount"] == 250.50
        assert data["transaction_type"] == "debit"
        assert data["currency"] == "USD"
        assert data["cash_flow_direction"] == "outflow"

    def test_process_amount_debit_credit_inference(
        self, client: TestClient, mock_db_connection: AsyncMock, mock_intelligence_service: AsyncMock, auth_headers: dict
    ):
        """Test inferring debit/credit from amount patterns."""
        # Mock the process_amount_fields method to return the expected response format
        mock_intelligence_service.process_amount_fields.return_value = {
            "amount": 250.50,
            "transaction_type": "expense",
            "currency": "USD",
            "cash_flow_direction": "outflow"
        }
        
        processing_request = {
            "transaction_data": {
                "amount": "250.50",
                "description": "OFFICE SUPPLIES",
                "date": "2024-01-15"
            },
            "column_mapping": {
                "amount": "Amount",
                "description": "Description",
                "date": "Date"
            },
            "accounting_system": {
                "system_type": "quickbooks",
                "cultural_context": "us"
            }
        }
        
        response = client.post(
            "/api/v1/intelligence/process-amount",
            json=processing_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["amount"] == 250.50
        assert data["transaction_type"] == "expense"
        assert data["currency"] == "USD"
        assert data["cash_flow_direction"] == "outflow"


class TestDescriptionNormalization:
    """Test description normalization and standardization."""

    def test_normalize_descriptions_vendor_standardization(
        self, client: TestClient, mock_db_connection: AsyncMock, 
        mock_intelligence_service: AsyncMock, auth_headers: dict, sample_transaction_descriptions
    ):
        """Test normalizing vendor names in descriptions."""
        # Mock both expected return values separately
        mock_normalize_responses = [
            "Amazon Web Services - AWS Compute",
            "Office Depot - Supplies"
        ]
        
        # Set up the mock to return different values for each call
        mock_intelligence_service.normalize_description.side_effect = mock_normalize_responses
        
        normalization_request = {
            "descriptions": sample_transaction_descriptions[:2],
            "normalize_vendors": True,
            "extract_locations": True,
            "standardize_format": True
        }
        
        response = client.post(
            "/api/v1/intelligence/normalize-descriptions",
            json=normalization_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["normalized_descriptions"]) == 2
        assert data["normalized_descriptions"][0] == "Amazon Web Services - AWS Compute"
        assert data["normalized_descriptions"][1] == "Office Depot - Supplies"

    def test_normalize_descriptions_category_hints(
        self, client: TestClient, mock_db_connection: AsyncMock, 
        mock_intelligence_service: AsyncMock, auth_headers: dict
    ):
        """Test getting category hints from normalized descriptions."""
        # Mock normalize_description to return a normalized string
        mock_intelligence_service.normalize_description.return_value = "Starbucks - Coffee Meeting"
        
        normalization_request = {
            "descriptions": ["STARBUCKS #567 COFFEE MEETING"],
            "generate_category_hints": True,
            "business_context": True
        }
        
        response = client.post(
            "/api/v1/intelligence/normalize-descriptions",
            json=normalization_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["normalized_descriptions"]) == 1
        assert data["normalized_descriptions"][0] == "Starbucks - Coffee Meeting"
        assert data["total_processed"] == 1


class TestBatchAnalysis:
    """Test batch transaction analysis."""

    def test_analyze_batch_pattern_detection(
        self, client: TestClient, mock_db_connection: AsyncMock, 
        mock_intelligence_service: AsyncMock, auth_headers: dict
    ):
        """Test analyzing batch of transactions for patterns."""
        mock_intelligence_service.analyze_transaction_batch.return_value = {
            "batch_size": 3,
            "accounting_system": {
                "system_type": "quickbooks",
                "cultural_context": "us",
                "confidence": 0.9
            },
            "processing_summary": {
                "total_processed": 3,
                "processing_errors": 0,
                "success_rate": 1.0
            },
            "quality_metrics": {
                "total_transactions": 3,
                "transaction_types": {"debit": 3, "credit": 0},
                "average_amount": 128.50,
                "detected_system_confidence": 0.9
            },
            "insights": {
                "data_quality": "excellent",
                "completeness": 1.0,
                "patterns": ["recurring_subscriptions", "business_travel"],
                "anomalies": [],
                "recommendations": ["Proceed with standard processing"]
            },
            "recommendations": ["Proceed with standard processing"]
        }
        
        batch_request = {
            "transactions": [
                {"description": "AMAZON WEB SERVICES", "amount": -250.50},
                {"description": "OFFICE DEPOT SUPPLIES", "amount": -89.99},
                {"description": "UBER BUSINESS TRIP", "amount": -45.00}
            ],
            "detect_patterns": True,
            "find_anomalies": True,
            "suggest_categories": True
        }
        
        response = client.post(
            "/api/v1/intelligence/analyze-batch",
            json=batch_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["batch_size"] == 3
        assert data["accounting_system"]["system_type"] == "quickbooks"
        assert data["quality_metrics"]["total_transactions"] == 3


# TODO: Re-enable these tests after implementing proper agent mocking
# class TestAgentIntegration:
#     """Test AI agent integration endpoints."""
#
#     def test_agent_customer_query(
#         self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
#     ):
#         """Test customer query processing through AI agent."""
#         # These tests need to be rewritten to properly mock the agent classes
#         pass
#
#     def test_agent_data_processing(
#         self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
#     ):
#         """Test data processing through AI agent."""
#         # These tests need to be rewritten to properly mock the agent classes
#         pass


class TestAccuracyMetrics:
    """Test accuracy metrics endpoint."""

    def test_get_accuracy_metrics(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving AI accuracy metrics."""
        # This endpoint loads from a JSON file, not from IntelligenceService
        response = client.get("/api/v1/intelligence/accuracy-metrics", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        # The response will either have metrics from the JSON file or empty metrics
        assert "metrics" in data


class TestHealthCheck:
    """Test intelligence service health check."""

    def test_health_check(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test intelligence service health endpoint."""
        response = client.get("/api/v1/intelligence/health", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "status" in data
        # The response has 'service' not 'services'
        assert "service" in data
        assert data["status"] == "healthy"


class TestErrorHandling:
    """Test error handling scenarios."""

    def test_invalid_detection_request(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test invalid accounting system detection request."""
        invalid_request = {
            "headers": [],  # Empty headers
            "sample_data": []
        }
        
        response = client.post(
            "/api/v1/intelligence/detect-accounting-system",
            json=invalid_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_service_unavailable(
        self, client: TestClient, mock_db_connection: AsyncMock, mock_intelligence_service: AsyncMock, auth_headers: dict
    ):
        """Test handling when intelligence service is unavailable."""
        # Mock the service to raise an exception
        mock_intelligence_service.detect_accounting_system.side_effect = Exception("Intelligence service unavailable")
        
        detection_request = {
            "columns": ["Date", "Amount", "Description"],
            "sample_data": {"Date": "2024-01-01", "Amount": "100.00"},
            "filename": "test.csv"
        }
        
        response = client.post(
            "/api/v1/intelligence/detect-accounting-system",
            json=detection_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR