"""
Comprehensive unit tests for Dashboard API endpoints.

Tests all dashboard endpoints with realistic data and proper mocking.
"""

from datetime import date, datetime, timedelta
from unittest.mock import AsyncMock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from giki_ai_api.core.main import app
from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session
from giki_ai_api.domains.auth.models import User
from giki_ai_api.domains.auth.secure_auth import get_current_active_user


@pytest.fixture
def mock_current_user():
    """Mock authenticated user."""
    return User(
        id=1,
        email="<EMAIL>",
        username="<EMAIL>",
        is_active=True,
        is_verified=True,
        is_superuser=False,
        tenant_id=1,
    )


@pytest.fixture
def mock_db_connection():
    """Mock database connection."""
    return AsyncMock()


@pytest.fixture
def client(mock_current_user, mock_db_connection):
    """Create test client with overridden dependencies."""
    # Override FastAPI dependencies
    app.dependency_overrides[get_current_active_user] = lambda: mock_current_user
    app.dependency_overrides[get_current_tenant_id] = lambda: 1
    app.dependency_overrides[get_db_session] = lambda: mock_db_connection
    
    # Create test client
    test_client = TestClient(app)
    
    yield test_client
    
    # Clean up overrides
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test-token"}


class TestDashboardMetrics:
    """Test dashboard metrics endpoint."""

    def test_get_dashboard_metrics_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test successful retrieval of dashboard metrics."""
        # Mock database response
        mock_row = {
            "total_count": 25,
            "total_income": 5000.00,
            "total_expenses": 2415.50,
            "categorized_count": 20,
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_row)

        response = client.get(
            "/api/v1/dashboard/metrics",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify metrics calculation
        assert data["total_transactions"] == 25
        assert data["total_income"] == 5000.00
        assert data["total_expenses"] == 2415.50
        assert data["net_income"] == 2584.50
        assert data["categorized_transactions"] == 20
        assert data["uncategorized_transactions"] == 5
        assert data["categorization_rate"] == 80.0
        assert "date_range" in data

    def test_get_dashboard_metrics_with_date_range(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test dashboard metrics with custom date range."""
        mock_row = {
            "total_count": 10,
            "total_income": 2000.00,
            "total_expenses": 800.00,
            "categorized_count": 8,
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_row)

        response = client.get(
            "/api/v1/dashboard/metrics",
            params={
                "start_date": "2024-01-01",
                "end_date": "2024-01-31",
            },
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["date_range"]["start"] == "2024-01-01"
        assert data["date_range"]["end"] == "2024-01-31"

    def test_get_dashboard_metrics_no_transactions(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test dashboard metrics with no transactions."""
        mock_row = {
            "total_count": 0,
            "total_income": None,
            "total_expenses": None,
            "categorized_count": 0,
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_row)

        response = client.get(
            "/api/v1/dashboard/metrics",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total_transactions"] == 0
        assert data["total_income"] == 0.0
        assert data["total_expenses"] == 0.0
        assert data["net_income"] == 0.0
        assert data["categorization_rate"] == 0


class TestRecentTransactions:
    """Test recent transactions endpoint."""

    def test_get_recent_transactions_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test successful retrieval of recent transactions."""
        # Mock database response
        mock_rows = [
            {
                "id": 1,
                "date": date(2024, 1, 15),
                "description": "Amazon Web Services",
                "amount": -250.00,
                "original_category": "Technology/Cloud Services",
                "ai_category": None,
            },
            {
                "id": 2,
                "date": date(2024, 1, 14),
                "description": "Salary Deposit",
                "amount": 5000.00,
                "original_category": None,
                "ai_category": "Income/Salary",
            },
            {
                "id": 3,
                "date": date(2024, 1, 13),
                "description": "Starbucks Coffee",
                "amount": -15.50,
                "original_category": None,
                "ai_category": None,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/dashboard/recent-transactions",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert len(data["items"]) == 3
        assert data["total"] == 3
        
        # Verify first transaction
        first_item = data["items"][0]
        assert first_item["id"] == 1
        assert first_item["date"] == "2024-01-15"
        assert first_item["description"] == "Amazon Web Services"
        assert first_item["amount"] == -250.00
        assert first_item["category"] == "Technology/Cloud Services"
        assert first_item["is_categorized"] is True
        
        # Verify uncategorized transaction
        third_item = data["items"][2]
        assert third_item["is_categorized"] is False
        assert third_item["category"] is None

    def test_get_recent_transactions_with_limit(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test recent transactions with custom limit."""
        mock_rows = [
            {
                "id": 1,
                "date": date(2024, 1, 15),
                "description": "Transaction 1",
                "amount": -100.00,
                "original_category": "Test Category",
                "ai_category": None,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/dashboard/recent-transactions",
            params={"limit": 5},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 1
        
        # Verify the database was called with correct limit
        mock_db_connection.fetch.assert_called_once()
        call_args = mock_db_connection.fetch.call_args[0]
        assert call_args[2] == 5  # limit parameter

    def test_get_recent_transactions_empty(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test recent transactions with no data."""
        mock_db_connection.fetch = AsyncMock(return_value=[])

        response = client.get(
            "/api/v1/dashboard/recent-transactions",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["items"] == []
        assert data["total"] == 0


class TestCategoryBreakdown:
    """Test category breakdown endpoint."""

    def test_get_category_breakdown_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test successful retrieval of category breakdown."""
        # Mock database response
        mock_rows = [
            {
                "category": "Expenses/Rent",
                "total_amount": 2000.00,
                "transaction_count": 1,
            },
            {
                "category": "Technology/Cloud Services",
                "total_amount": 250.00,
                "transaction_count": 1,
            },
            {
                "category": "Expenses/Meals & Entertainment",
                "total_amount": 165.50,
                "transaction_count": 3,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/dashboard/category-breakdown",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert len(data["items"]) == 3
        assert data["total_categories"] == 3
        assert data["total_spending"] == 2415.50
        
        # Verify percentage calculations
        first_item = data["items"][0]
        assert first_item["category"] == "Expenses/Rent"
        assert first_item["amount"] == 2000.00
        assert first_item["transaction_count"] == 1
        assert abs(first_item["percentage"] - 82.80) < 0.02  # ~82.80%

    def test_get_category_breakdown_with_date_range(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test category breakdown with custom date range."""
        mock_rows = [
            {
                "category": "Technology/Software",
                "total_amount": 150.00,
                "transaction_count": 1,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/dashboard/category-breakdown",
            params={
                "start_date": "2024-01-01",
                "end_date": "2024-01-31",
            },
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 1
        assert data["total_spending"] == 150.00

    def test_get_category_breakdown_no_expenses(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test category breakdown with no expense transactions."""
        mock_db_connection.fetch = AsyncMock(return_value=[])

        response = client.get(
            "/api/v1/dashboard/category-breakdown",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["items"] == []
        assert data["total_categories"] == 0
        assert data["total_spending"] == 0


class TestErrorHandling:
    """Test error handling in dashboard endpoints."""

    def test_database_error_in_metrics(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test that database errors are properly raised."""
        mock_db_connection.fetchrow.side_effect = Exception("Database connection failed")

        # Database errors are not handled in the dashboard API, so they propagate
        with pytest.raises(Exception):
            response = client.get(
                "/api/v1/dashboard/metrics",
                headers=auth_headers,
            )

    def test_invalid_date_format(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test that invalid date formats raise ValueError."""
        # Invalid date format causes ValueError to be raised
        with pytest.raises(ValueError):
            response = client.get(
                "/api/v1/dashboard/metrics",
                params={"start_date": "invalid-date"},
                headers=auth_headers,
            )

    def test_invalid_limit_parameter(
        self, client: TestClient, auth_headers: dict
    ):
        """Test handling of invalid limit parameter."""
        response = client.get(
            "/api/v1/dashboard/recent-transactions",
            params={"limit": 100},  # Exceeds maximum of 50
            headers=auth_headers,
        )

        # Should return 422 validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestPerformance:
    """Test performance aspects of dashboard endpoints."""

    def test_metrics_query_efficiency(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test that metrics endpoint uses a single efficient query."""
        mock_row = {
            "total_count": 100,
            "total_income": 10000.00,
            "total_expenses": 5000.00,
            "categorized_count": 80,
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_row)

        response = client.get(
            "/api/v1/dashboard/metrics",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        
        # Verify only one database call was made
        assert mock_db_connection.fetchrow.call_count == 1
        
        # Verify the query includes all required metrics
        call_args = mock_db_connection.fetchrow.call_args[0]
        query = call_args[0]
        assert "COUNT(*)" in query
        assert "SUM(CASE WHEN amount > 0" in query
        assert "SUM(CASE WHEN amount < 0" in query
        assert "COUNT(CASE WHEN original_category IS NOT NULL" in query