"""
Comprehensive unit tests for Categories API endpoints.

Tests the core MIS categorization functionality with realistic data.
"""

import json
from datetime import datetime, timezone
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from giki_ai_api.core.main import app
from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session, get_current_user_with_tenant
from giki_ai_api.domains.auth.models import User, Tenant
from giki_ai_api.domains.auth.secure_auth import get_current_active_user


@pytest.fixture
def mock_user():
    """Mock authenticated user."""
    return User(
        id=1,
        email="<EMAIL>",
        username="<EMAIL>",
        is_active=True,
        is_verified=True,
        is_superuser=False,
        tenant_id=1,
    )


@pytest.fixture
def mock_tenant():
    """Mock tenant."""
    return Tenant(
        id=1,
        name="Test Company",
        domain="test.com",
        subscription_status="active",
        subscription_plan="free",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
    )


@pytest.fixture
def mock_db_connection():
    """Mock database connection."""
    return AsyncMock()


@pytest.fixture
def mock_user_tenant(mock_user, mock_tenant):
    """Mock user-tenant tuple."""
    return (mock_user, mock_tenant)


@pytest.fixture
def mock_vertex_client():
    """Mock Vertex AI client."""
    return AsyncMock()


@pytest.fixture
def client(mock_user, mock_tenant, mock_db_connection, mock_user_tenant, mock_vertex_client):
    """Create test client with overridden dependencies."""
    # Override FastAPI dependencies
    app.dependency_overrides[get_current_active_user] = lambda: mock_user
    app.dependency_overrides[get_current_tenant_id] = lambda: mock_tenant.id
    app.dependency_overrides[get_db_session] = lambda: mock_db_connection
    app.dependency_overrides[get_current_user_with_tenant] = lambda: mock_user_tenant
    from giki_ai_api.core.dependencies import get_vertex_ai_client
    app.dependency_overrides[get_vertex_ai_client] = lambda: mock_vertex_client
    
    # Create test client
    test_client = TestClient(app)
    
    yield test_client
    
    # Clean up overrides
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test-token"}


class TestCategoryManagement:
    """Test category CRUD operations."""

    def test_get_categories_list(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving list of categories."""
        # Mock database response
        mock_categories = [
            {
                "id": 1,
                "name": "Office Supplies",
                "parent_id": None,
                "level": 1,
                "color": "#295343",
                "icon": "□",
                "transaction_count": 125,
                "total_amount": 15420.50,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
            },
            {
                "id": 2,
                "name": "Software",
                "parent_id": 1,
                "level": 2,
                "color": "#295343",
                "icon": "□",
                "transaction_count": 45,
                "total_amount": 8920.00,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_categories)

        response = client.get("/api/v1/categories", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 2
        assert data[0]["name"] == "Office Supplies"
        assert data[0]["transaction_count"] == 125

    def test_get_category_by_id(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving a specific category by ID."""
        category_id = 1
        mock_category = {
            "id": category_id,
            "name": "Office Supplies",
            "description": "General office supplies and equipment",
            "parent_id": None,
            "level": 1,
            "color": "#295343",
            "icon": "□",
            "transaction_count": 125,
            "total_amount": 15420.50,
            "accuracy_score": 0.92,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_category)

        response = client.get(f"/api/v1/categories/{category_id}", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == category_id
        assert data["name"] == "Office Supplies"
        assert data["accuracy_score"] == 0.92

    def test_create_category(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test creating a new category."""
        new_category = {
            "name": "Travel Expenses",
            "description": "Business travel and accommodation",
            "parent_id": None,
            "color": "#295343",
            "icon": "□",
        }
        
        # Mock database operations
        mock_db_connection.fetchval = AsyncMock(return_value=3)  # new category ID
        mock_db_connection.fetchrow = AsyncMock(return_value={
            "id": 3,
            "name": "Travel Expenses",
            "description": "Business travel and accommodation",
            "parent_id": None,
            "level": 1,
            "color": "#295343",
            "icon": "□",
            "transaction_count": 0,
            "total_amount": 0.0,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        })

        response = client.post("/api/v1/categories", json=new_category, headers=auth_headers)

        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["name"] == "Travel Expenses"
        assert data["id"] == 3

    def test_update_category(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test updating an existing category."""
        category_id = 1
        update_data = {
            "name": "Office Supplies & Equipment",
            "description": "Updated description for office supplies",
        }
        
        # Mock database operations
        mock_db_connection.execute = AsyncMock()
        mock_db_connection.fetchrow = AsyncMock(return_value={
            "id": category_id,
            "name": "Office Supplies & Equipment",
            "description": "Updated description for office supplies",
            "parent_id": None,
            "level": 1,
            "color": "#295343",
            "icon": "□",
            "transaction_count": 125,
            "total_amount": 15420.50,
            "updated_at": datetime.now(timezone.utc),
        })

        response = client.put(f"/api/v1/categories/{category_id}", json=update_data, headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == "Office Supplies & Equipment"
        assert data["description"] == "Updated description for office supplies"


class TestCategoryHierarchy:
    """Test category hierarchy operations."""

    def test_get_category_hierarchy(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving category hierarchy."""
        mock_hierarchy = [
            {
                "id": 1,
                "name": "Expenses",
                "level": 1,
                "children": [
                    {
                        "id": 2,
                        "name": "Office Supplies",
                        "level": 2,
                        "parent_id": 1,
                        "children": [
                            {
                                "id": 3,
                                "name": "Software",
                                "level": 3,
                                "parent_id": 2,
                                "children": [],
                            }
                        ],
                    }
                ],
            }
        ]
        
        with patch("giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.get_category_hierarchy = AsyncMock(return_value=mock_hierarchy)

            response = client.get("/api/v1/categories/hierarchy", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert isinstance(data, list)
            assert len(data) >= 1
            assert data[0]["name"] == "Expenses"
            assert data[0]["level"] == 1
            assert len(data[0]["children"]) >= 1

    def test_setup_standard_hierarchy(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test setting up standard MIS category hierarchy."""
        setup_request = {
            "industry": "technology",
            "business_size": "medium",
            "include_gl_codes": True,
        }
        
        # Mock database operations
        mock_db_connection.execute = AsyncMock()
        mock_db_connection.executemany = AsyncMock()
        
        with patch("giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.setup_standard_hierarchy = AsyncMock(return_value={
                "categories_created": 45,
                "hierarchy_levels": 3,
                "industry_template": "technology",
                "gl_codes_included": True,
            })

            response = client.post("/api/v1/categories/setup-hierarchy", json=setup_request, headers=auth_headers)

            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["categories_created"] == 45
            assert data["industry_template"] == "technology"
            assert data["gl_codes_included"] is True


class TestCategoryMISCategorization:
    """Test MIS categorization functionality."""

    def test_categorize_transaction(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test AI categorization of a transaction."""
        categorization_request = {
            "description": "Microsoft Office 365 Business Premium",
            "amount": 299.99,
            "vendor": "Microsoft Corporation",
            "date": "2024-01-15",
        }
        
        with patch("giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.categorize_transaction = AsyncMock(return_value={
                "category_id": 15,
                "category_name": "Software Subscriptions",
                "confidence_score": 0.94,
                "reasoning": "Microsoft Office 365 is clearly a software subscription service",
                "suggested_gl_code": "6100-010",
                "alternative_categories": [
                    {"id": 16, "name": "IT Services", "confidence": 0.78},
                    {"id": 12, "name": "Office Supplies", "confidence": 0.23},
                ],
            })

            response = client.post("/api/v1/categories/categorize", json=categorization_request, headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["category_name"] == "Software Subscriptions"
            assert data["confidence_score"] == 0.94
            assert data["suggested_gl_code"] == "6100-010"
            assert len(data["alternative_categories"]) >= 2

    def test_batch_categorize_transactions(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test batch categorization of multiple transactions."""
        batch_request = {
            "transactions": [
                {
                    "id": 1,
                    "description": "Amazon Web Services",
                    "amount": 156.78,
                    "vendor": "AWS",
                },
                {
                    "id": 2,
                    "description": "Staples Office Supplies",
                    "amount": 45.20,
                    "vendor": "Staples",
                },
            ]
        }
        
        with patch("giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.batch_categorize = AsyncMock(return_value={
                "total_processed": 2,
                "successful_categorizations": 2,
                "failed_categorizations": 0,
                "results": [
                    {
                        "transaction_id": 1,
                        "category_id": 18,
                        "category_name": "Cloud Services",
                        "confidence_score": 0.96,
                    },
                    {
                        "transaction_id": 2,
                        "category_id": 12,
                        "category_name": "Office Supplies",
                        "confidence_score": 0.89,
                    },
                ],
            })

            response = client.post("/api/v1/categories/batch-categorize", json=batch_request, headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total_processed"] == 2
            assert data["successful_categorizations"] == 2
            assert len(data["results"]) == 2

    def test_get_mis_accuracy_metrics(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving MIS categorization accuracy metrics."""
        
        with patch("giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.calculate_mis_accuracy = AsyncMock(return_value={
                "baseline_accuracy": 0.72,
                "current_accuracy": 0.91,
                "accuracy_gain": 0.19,
                "total_transactions": 2580,
                "correctly_categorized": 2348,
                "needs_review": 232,
                "category_breakdown": {
                    "Office Supplies": {"accuracy": 0.94, "count": 340},
                    "Software": {"accuracy": 0.89, "count": 180},
                    "Travel": {"accuracy": 0.76, "count": 95},
                },
                "improvement_suggestions": [
                    "Add more training data for Travel category",
                    "Review ambiguous Software vs IT Services categorizations",
                ],
            })

            response = client.get("/api/v1/categories/mis-accuracy", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["current_accuracy"] == 0.91
            assert data["accuracy_gain"] == 0.19
            assert data["total_transactions"] == 2580
            assert len(data["category_breakdown"]) >= 3
            assert len(data["improvement_suggestions"]) >= 1


class TestErrorHandling:
    """Test error handling scenarios."""

    def test_get_nonexistent_category(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving a non-existent category."""
        mock_db_connection.fetchrow = AsyncMock(return_value=None)

        response = client.get("/api/v1/categories/999", headers=auth_headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_create_category_validation_error(
        self, client: TestClient, auth_headers: dict
    ):
        """Test creating category with invalid data."""
        invalid_category = {
            "name": "",  # Empty name should fail validation
            "color": "invalid-color",  # Invalid color format
        }

        response = client.post("/api/v1/categories", json=invalid_category, headers=auth_headers)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_categorization_service_error(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test categorization when AI service fails."""
        categorization_request = {
            "description": "Test transaction",
            "amount": 100.0,
        }
        
        with patch("giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.categorize_transaction = AsyncMock(side_effect=Exception("AI service unavailable"))

            response = client.post("/api/v1/categories/categorize", json=categorization_request, headers=auth_headers)

            # Should handle gracefully and return fallback response
            assert response.status_code in [status.HTTP_500_INTERNAL_SERVER_ERROR, status.HTTP_503_SERVICE_UNAVAILABLE]