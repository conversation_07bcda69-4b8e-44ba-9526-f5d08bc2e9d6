"""
Core unit tests for Reports API endpoints.

Tests critical report endpoints with realistic data.
"""

from datetime import date, datetime
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi.testclient import TestClient

from giki_ai_api.core.main import app
from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session
from giki_ai_api.domains.auth.models import User
from giki_ai_api.domains.auth.secure_auth import get_current_active_user


@pytest.fixture
def mock_user():
    """Mock authenticated user."""
    return User(
        id=1,
        email="<EMAIL>",
        username="<EMAIL>",
        is_active=True,
        is_verified=True,
        is_superuser=False,
        tenant_id=1,
    )


@pytest.fixture
def mock_db():
    """Mock database connection."""
    return AsyncMock()


@pytest.fixture
def client(mock_user, mock_db):
    """Create test client with overridden dependencies."""
    # Override FastAPI dependencies
    app.dependency_overrides[get_current_active_user] = lambda: mock_user
    app.dependency_overrides[get_current_tenant_id] = lambda: 1
    app.dependency_overrides[get_db_session] = lambda: mock_db
    
    # Create test client
    test_client = TestClient(app)
    
    yield test_client
    
    # Clean up overrides
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Auth headers for requests."""
    return {"Authorization": "Bearer test-token"}


class TestReportsAPI:
    """Test core reports endpoints."""
    
    def test_spending_by_category(self, client, mock_db, auth_headers):
        """Test spending by category endpoint."""
        # Mock data
        mock_rows = [
            {
                "category_path": "Technology/Cloud Services",
                "total_amount": -250.00,
                "transaction_count": 1,
            },
            {
                "category_path": "Expenses/Rent",
                "total_amount": -2000.00,
                "transaction_count": 1,
            },
        ]
        mock_db.fetch = AsyncMock(return_value=mock_rows)
        
        # Mock cache as None (cache miss)
        with patch("giki_ai_api.shared.cache.cache.api_cache.get", AsyncMock(return_value=None)), \
             patch("giki_ai_api.shared.cache.cache.api_cache.set", AsyncMock()):
            
            response = client.get("/api/v1/reports/spending-by-category", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert len(data["items"]) == 2
        assert data["items"][0]["category_path"] == "Technology/Cloud Services"
        assert data["items"][0]["total_amount"] == -250.00
    
    def test_income_expense_summary(self, client, mock_db, auth_headers):
        """Test income/expense summary endpoint."""
        # Mock data
        mock_rows = [
            {"transaction_type": "income", "total_amount": 5000.00},
            {"transaction_type": "expense", "total_amount": -2415.50},
        ]
        mock_db.fetch = AsyncMock(return_value=mock_rows)
        
        response = client.get("/api/v1/reports/income-expense-summary", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_income"] == 5000.00
        assert data["total_expenses"] == 2415.50
        assert data["net_income_loss"] == 2584.50
    
    def test_financial_summary(self, client, mock_db, auth_headers):
        """Test financial summary endpoint."""
        # Mock data
        mock_row = {
            "transaction_count": 5,
            "total_income": 5000.00,
            "total_expenses": 2415.50,
        }
        mock_db.fetchrow = AsyncMock(return_value=mock_row)
        
        response = client.get("/api/v1/reports/summary", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_income"] == 5000.00
        assert data["total_expenses"] == 2415.50
        assert data["net_income"] == 2584.50
        assert data["transaction_count"] == 5
    
    def test_monthly_trends(self, client, mock_db, auth_headers):
        """Test monthly trends endpoint."""
        # Mock data
        mock_rows = [
            {
                "month": "2024-01",
                "transaction_type": "income",
                "total_amount": 5000.00,
                "transaction_count": 1,
            },
            {
                "month": "2024-01",
                "transaction_type": "expense",
                "total_amount": -2250.00,
                "transaction_count": 2,
            },
        ]
        mock_db.fetch = AsyncMock(return_value=mock_rows)
        
        response = client.get("/api/v1/reports/monthly-trends", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert len(data["items"]) == 1
        assert data["items"][0]["month"] == "2024-01"
        assert data["items"][0]["total_income"] == 5000.00
        assert data["items"][0]["total_expenses"] == 2250.00
    
    def test_category_breakdown(self, client, mock_db, auth_headers):
        """Test category breakdown endpoint."""
        # Mock data
        mock_rows = [
            {
                "category_id": 301,
                "category_name": "Expenses/Rent",
                "gl_code": None,
                "category_path": "Expenses/Rent",
                "total_amount": 2000.00,
                "transaction_count": 1,
            },
        ]
        mock_db.fetch = AsyncMock(return_value=mock_rows)
        
        response = client.get("/api/v1/reports/category-breakdown?type=expense", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["type"] == "expense"
        assert len(data["categories"]) == 1
        assert data["categories"][0]["percentage"] == 100.0