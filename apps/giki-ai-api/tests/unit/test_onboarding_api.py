"""
Comprehensive unit tests for Onboarding API endpoints.

Tests the core MIS setup journey and key onboarding endpoints with realistic data.
"""

import json
import tempfile
from datetime import datetime, timezone
from io import BytesIO
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from giki_ai_api.core.main import app
from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session, get_current_user_with_tenant
from giki_ai_api.domains.auth.models import User, Tenant
from giki_ai_api.domains.auth.secure_auth import get_current_active_user


@pytest.fixture
def mock_user():
    """Mock authenticated user."""
    return User(
        id=1,
        email="<EMAIL>",
        username="<EMAIL>",
        is_active=True,
        is_verified=True,
        is_superuser=False,
        tenant_id=1,
    )


@pytest.fixture
def mock_tenant():
    """Mock tenant."""
    return Tenant(
        id=1,
        name="Test Company",
        domain="test.com",
        subscription_status="active",
        subscription_plan="free",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
    )


@pytest.fixture
def mock_db_connection():
    """Mock database connection."""
    return AsyncMock()


@pytest.fixture
def mock_user_tenant(mock_user, mock_tenant):
    """Mock user-tenant tuple."""
    return (mock_user, mock_tenant)


@pytest.fixture
def mock_vertex_client():
    """Mock Vertex AI client."""
    return AsyncMock()


@pytest.fixture
def client(mock_user, mock_tenant, mock_db_connection, mock_user_tenant, mock_vertex_client):
    """Create test client with overridden dependencies."""
    # Override FastAPI dependencies
    app.dependency_overrides[get_current_active_user] = lambda: mock_user
    app.dependency_overrides[get_current_tenant_id] = lambda: mock_tenant.id
    app.dependency_overrides[get_db_session] = lambda: mock_db_connection
    app.dependency_overrides[get_current_user_with_tenant] = lambda: mock_user_tenant
    from giki_ai_api.core.dependencies import get_vertex_ai_client
    app.dependency_overrides[get_vertex_ai_client] = lambda: mock_vertex_client
    
    # Create test client
    test_client = TestClient(app)
    
    yield test_client
    
    # Clean up overrides
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test-token"}


class TestOnboardingStatus:
    """Test onboarding status endpoints."""

    def test_get_onboarding_status(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting onboarding status."""
        # Mock OnboardingService
        with patch("giki_ai_api.domains.onboarding.router.OnboardingService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.get_onboarding_status = AsyncMock(return_value={
                "tenant_id": 1,
                "onboarding_type": "zero_onboarding",
                "onboarding_stage": "completed",
                "approved_for_production": True,
                "total_transactions": 1250,
                "uploaded_files": ["bank_data.xlsx"],
                "latest_validation": None,
            })

            response = client.get("/api/v1/onboarding/status", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["tenant_id"] == 1
            assert data["onboarding_type"] == "zero_onboarding"
            assert data["approved_for_production"] is True


class TestOnboardingProgress:
    """Test onboarding progress endpoints."""

    def test_get_onboarding_progress_completed(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting progress for completed onboarding."""
        onboarding_id = "1-zero_onboarding"
        
        with patch("giki_ai_api.domains.onboarding.router.OnboardingService") as mock_service:
            mock_instance = mock_service.return_value
            # Create a mock object with attributes instead of a dict
            mock_status = Mock()
            mock_status.tenant_id = 1
            mock_status.onboarding_type = "zero_onboarding"
            mock_status.onboarding_stage = "completed"
            mock_status.approved_for_production = True
            mock_status.total_transactions = 1250
            mock_status.uploaded_files = ["bank_data.xlsx"]
            # Create a nested mock for latest_validation
            mock_validation = Mock()
            mock_validation.average_accuracy = 92.5
            mock_status.latest_validation = mock_validation
            mock_instance.get_onboarding_status = AsyncMock(return_value=mock_status)

            response = client.get(f"/api/v1/onboarding/{onboarding_id}/progress", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["onboarding_id"] == onboarding_id
            assert data["type"] == "zero_onboarding"
            assert data["status"] == "completed"
            assert data["progress"] == 100.0
            assert data["current_step"] == "Setup complete"


class TestMISSetup:
    """Test unified MIS setup endpoints."""

    def test_unified_mis_setup_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test successful MIS setup."""
        request_data = {
            "company_info": {
                "name": "Test Company Inc.",
                "industry": "Technology",
                "size": "50-200",
                "fiscal_year_end": "December",
                "default_currency": "USD"
            },
            "uploaded_files": []
        }

        # Mock database operations
        mock_db_connection.fetchrow = AsyncMock(return_value={"id": 123})
        
        # Mock MIS categorization service
        with patch("giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationService") as mock_mis_service:
            mock_mis_instance = mock_mis_service.return_value
            mock_mis_instance.apply_industry_template = AsyncMock()
            mock_mis_instance.update_business_context = AsyncMock()
            mock_mis_instance.calculate_mis_accuracy = AsyncMock(return_value={
                "baseline_accuracy": 0.87,
                "current_accuracy": 0.87,
                "accuracy_gain": 0.0,
                "enhancements_applied": []
            })

            response = client.post(
                "/api/v1/onboarding/mis/setup",
                json=request_data,
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["setupId"] == "123"
            assert data["status"] == "active"
            assert data["baselineAccuracy"] == 0.87
            assert isinstance(data["enhancementOpportunities"], list)

    def test_get_mis_setup_status(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting MIS setup status."""
        setup_id = "123"
        
        # Mock database response
        mock_db_connection.fetchrow = AsyncMock(return_value={
            "id": 123,
            "tenant_id": 1,
            "stage": "completed",
            "onboarding_type": "zero_onboarding",
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        })
        mock_db_connection.fetchval = AsyncMock(return_value=45)  # category count
        
        # Mock MIS service
        with patch("giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationService") as mock_mis_service:
            mock_mis_instance = mock_mis_service.return_value
            mock_mis_instance.calculate_mis_accuracy = AsyncMock(return_value={
                "baseline_accuracy": 0.87,
                "current_accuracy": 0.92,
                "accuracy_gain": 0.05,
                "enhancements_applied": ["industry_template"]
            })

            response = client.get(f"/api/v1/onboarding/mis/setup/{setup_id}", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["setup_id"] == setup_id
            assert data["status"] == "active"
            assert data["category_count"] == 45
            assert data["baseline_accuracy"] == 0.87
            assert data["current_accuracy"] == 0.92

    def test_complete_mis_setup(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test completing MIS setup."""
        setup_id = "123"
        
        # Mock database operations
        mock_db_connection.fetchrow = AsyncMock(return_value={
            "id": 123,
            "tenant_id": 1,
            "stage": "in_progress"
        })
        mock_db_connection.execute = AsyncMock()

        response = client.post(
            f"/api/v1/onboarding/mis/setup/{setup_id}/complete",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "completed"
        assert data["setup_id"] == setup_id
        assert data["tenant_id"] == 1


class TestFileUpload:
    """Test file upload endpoints."""

    def test_upload_historical_data(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test uploading historical data file."""
        # Create a test file
        test_file_content = b"Date,Description,Amount,Category\n2024-01-15,Amazon Web Services,-250.00,Technology/Cloud Services"
        
        # Mock settings and path operations
        with patch("giki_ai_api.domains.onboarding.router.settings") as mock_settings:
            mock_settings.get_upload_directory.return_value = Path("/tmp/uploads/1")
            
            # Mock OnboardingService
            with patch("giki_ai_api.domains.onboarding.router.OnboardingService") as mock_service:
                mock_instance = mock_service.return_value
                # Create a mock column mapping object with model_dump method
                mock_column_mapping = Mock()
                mock_column_mapping.model_dump = Mock(return_value={
                    "date_column": "Date",
                    "description_column": "Description", 
                    "amount_column": "Amount",
                    "category_column": "Category",
                })
                mock_instance.interpret_file_columns = AsyncMock(return_value=mock_column_mapping)
                
                # Mock database operations
                mock_db_connection.execute = AsyncMock()
                mock_db_connection.fetchval = AsyncMock(side_effect=["upload-123", "report-456"])  # upload_id, report_id
                
                # Create a proper mock for file operations
                mock_file = Mock()
                mock_file.__enter__ = Mock(return_value=mock_file)
                mock_file.__exit__ = Mock(return_value=None)
                mock_file.write = Mock()
                
                # Mock the background task to prevent actual execution
                with patch("giki_ai_api.domains.onboarding.router._process_file_background") as mock_bg_task:
                    # Mock file operations with context manager support
                    with patch("giki_ai_api.domains.onboarding.router.open", create=True, return_value=mock_file):
                        with patch("pathlib.Path.stat") as mock_stat:
                            mock_stat.return_value.st_size = len(test_file_content)
                            with patch("pathlib.Path.exists", return_value=True):
                                response = client.post(
                                "/api/v1/onboarding/upload-historical-data",
                                files={"file": ("test_data.csv", test_file_content, "text/csv")},
                                data={"year": 2024, "has_category_labels": True},
                                headers=auth_headers,
                                )

                                assert response.status_code == status.HTTP_200_OK
                                data = response.json()
                                assert data["status"] == "processing"
                                assert "upload_id" in data
                                assert "processing_report_id" in data
                                assert data["transactions_imported"] == 0  # Will be updated after processing

    def test_get_upload_status(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting upload status."""
        upload_id = "test-upload-123"
        
        # Mock database responses
        upload_row = {
            "id": upload_id,
            "filename": "test_data.csv",
            "status": "completed",
            "processing_status": "completed",
            "size": 100,
            "error_message": None,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        }
        
        report_row = {
            "id": "report-123",
            "status": "completed",
            "total_rows": 100,
            "successful_rows": 98,
            "failed_rows": 2,
            "skipped_rows": 0,
            "processing_duration_seconds": 15.5,
            "error_message": None,
        }
        
        mock_db_connection.fetchrow = AsyncMock(side_effect=[upload_row, report_row])

        response = client.get(f"/api/v1/onboarding/upload-status/{upload_id}", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["upload_id"] == upload_id
        assert data["filename"] == "test_data.csv"
        assert data["status"] == "completed"
        assert data["transactions_imported"] == 100
        assert "processing_report" in data
        assert data["processing_report"]["successful_rows"] == 98


class TestEnhancementDetection:
    """Test enhancement detection endpoints."""

    def test_detect_enhancements_historical_data(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test detecting historical data enhancement opportunities."""
        # Create test Excel content
        excel_content = BytesIO()
        
        # Mock settings and pandas
        with patch("giki_ai_api.domains.onboarding.router.settings") as mock_settings:
            mock_settings.get_upload_directory.return_value = Path("/tmp/uploads/1")
            
            with patch("pandas.read_excel") as mock_read_excel:
                # Mock DataFrame with historical transaction columns
                mock_df = Mock()
                mock_df.columns = ["Date", "Description", "Amount", "Category", "GL Code"]
                mock_df.__len__ = Mock(return_value=250)
                mock_read_excel.return_value = mock_df
                
                # Mock file operations with context manager support
                mock_file = Mock()
                mock_file.__enter__ = Mock(return_value=mock_file)
                mock_file.__exit__ = Mock(return_value=None)
                mock_file.__len__ = Mock(return_value=0)
                mock_file.write = Mock()
                mock_file.read = Mock(return_value=b'')
                
                with patch("builtins.open", return_value=mock_file):
                    with patch("pathlib.Path.unlink"):
                        response = client.post(
                            "/api/v1/onboarding/mis/detect-enhancements",
                            files={"file": ("bank_data.xlsx", excel_content.getvalue(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")},
                            headers=auth_headers,
                        )

                        assert response.status_code == status.HTTP_200_OK
                        data = response.json()
                        assert "enhancements" in data
                        
                        # Should detect both historical and schema enhancements
                        enhancements = data["enhancements"]
                        assert len(enhancements) >= 1
                        
                        # Check for historical enhancement
                        historical_enhancement = next((e for e in enhancements if e["type"] == "historical"), None)
                        assert historical_enhancement is not None
                        assert historical_enhancement["accuracy_gain"] == "+15-20%"
                        assert historical_enhancement["priority"] == "recommended"


class TestBusinessContext:
    """Test business context endpoints."""

    def test_save_business_context(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test saving business context."""
        business_context = {
            "company_name": "Test Company Inc.",
            "industry": "Technology",
            "company_size": "50-200",
            "annual_revenue": "$10M-$50M",
            "primary_business": "Software Development",
            "key_expense_categories": ["Technology", "Marketing", "Operations"]
        }
        
        # Mock OnboardingService
        with patch("giki_ai_api.domains.onboarding.router.OnboardingService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.update_onboarding_with_business_context = AsyncMock(return_value={
                "business_context_saved": True,
                "onboarding_status": "context_updated"
            })

            response = client.post(
                "/api/v1/onboarding/business-context",
                json=business_context,
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is True
            assert data["business_context_saved"] is True

    def test_get_business_context(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting business context."""
        expected_context = {
            "company_name": "Test Company Inc.",
            "industry": "Technology",
            "company_size": "50-200",
            "fiscal_year_end": "December"
        }
        
        # Mock OnboardingService
        with patch("giki_ai_api.domains.onboarding.router.OnboardingService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.get_business_context = AsyncMock(return_value=expected_context)

            response = client.get("/api/v1/onboarding/business-context", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is True
            assert data["business_context"]["company_name"] == "Test Company Inc."


class TestZeroOnboarding:
    """Test zero onboarding endpoints."""

    def test_start_zero_onboarding(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test starting zero onboarding process."""
        request_data = {
            "tenant_id": 1,
            "tenant_name": "Test Company",
            "industry": "Technology",
            "company_size": "50-200",
            "business_type": "Software Development",
            "company_website": "https://test.com",
            "contact_email": "<EMAIL>"
        }
        
        # Mock OnboardingService
        with patch("giki_ai_api.domains.onboarding.router.OnboardingService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.store_business_context = AsyncMock()
            mock_instance.initialize_zero_onboarding = AsyncMock()
            mock_instance.apply_mis_template_for_tenant = AsyncMock(return_value={
                "success": True,
                "industry": "Technology",
                "confidence": 0.95
            })
            mock_instance.get_onboarding_status = AsyncMock(return_value={
                "tenant_id": 1,
                "onboarding_type": "zero_onboarding",
                "onboarding_stage": "zero_ready",
                "approved_for_production": True,
            })

            response = client.post(
                "/api/v1/onboarding/start-zero",
                json=request_data,
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["tenant_id"] == 1
            assert data["onboarding_type"] == "zero_onboarding"
            assert data["approved_for_production"] is True


class TestSchemaImport:
    """Test schema import endpoints."""

    def test_import_category_schema_json(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test importing category schema from JSON file."""
        # Create test JSON schema
        schema_data = {
            "categories": [
                {"name": "Technology", "level": 0, "gl_code": "6100", "account_type": "Expense"},
                {"name": "Technology/Software", "level": 1, "gl_code": "6110", "parent": "Technology", "account_type": "Expense"},
                {"name": "Marketing", "level": 0, "gl_code": "6200", "account_type": "Expense"}
            ]
        }
        schema_json = json.dumps(schema_data).encode()
        
        # Mock OnboardingService
        with patch("giki_ai_api.domains.onboarding.router.OnboardingService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.import_category_schema = AsyncMock()
            
            # Mock helper functions
            with patch("giki_ai_api.domains.onboarding.router._parse_category_schema_file") as mock_parse:
                mock_parse.return_value = schema_data
                with patch("giki_ai_api.domains.onboarding.router._store_category_schema") as mock_store:
                    mock_store.return_value = "schema-123"
                    with patch("giki_ai_api.domains.onboarding.router._create_categories_from_schema") as mock_create:
                        mock_create.return_value = 3

                        response = client.post(
                            "/api/v1/onboarding/import-schema",
                            files={"file": ("schema.json", schema_json, "application/json")},
                            headers=auth_headers,
                        )

                        assert response.status_code == status.HTTP_200_OK
                        data = response.json()
                        assert data["schema_id"] == "schema-123"
                        assert data["categories_created"] == 3
                        assert data["details"]["total_categories"] == 3


class TestErrorHandling:
    """Test error handling in onboarding endpoints."""

    def test_upload_invalid_file_format(
        self, client: TestClient, auth_headers: dict
    ):
        """Test uploading file with invalid format."""
        test_file_content = b"Some text content"
        
        response = client.post(
            "/api/v1/onboarding/upload-historical-data",
            files={"file": ("test.txt", test_file_content, "text/plain")},
            data={"year": 2024, "has_category_labels": True},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid file format" in response.json()["detail"]

    def test_mis_setup_database_error(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test MIS setup with database error."""
        request_data = {
            "company_info": {
                "name": "Test Company",
                "industry": "Technology",
                "size": "50-200",
                "fiscal_year_end": "December",
                "default_currency": "USD"
            }
        }
        
        # Mock database error
        mock_db_connection.fetchrow.side_effect = Exception("Database connection failed")

        response = client.post(
            "/api/v1/onboarding/mis/setup",
            json=request_data,
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to initialize MIS setup" in response.json()["detail"]

    def test_upload_status_not_found(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting status for non-existent upload."""
        upload_id = "non-existent-upload"
        
        # Mock database returning None
        mock_db_connection.fetchrow = AsyncMock(return_value=None)

        response = client.get(f"/api/v1/onboarding/upload-status/{upload_id}", headers=auth_headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert f"Upload {upload_id} not found" in response.json()["detail"]


class TestPerformance:
    """Test performance aspects of onboarding endpoints."""

    def test_batch_upload_performance(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test batch upload with multiple files."""
        # Create multiple test files
        files = []
        for i in range(3):
            content = f"Date,Description,Amount\n2024-01-{i+1:02d},Test Transaction {i+1},-{(i+1)*100}.00".encode()
            files.append(("files", (f"test_{i+1}.csv", content, "text/csv")))
        
        # Mock OnboardingService
        with patch("giki_ai_api.domains.onboarding.router.OnboardingService") as mock_service:
            mock_instance = mock_service.return_value
            
            # Create a mock column mapping object with model_dump method
            mock_column_mapping = Mock()
            mock_column_mapping.model_dump = Mock(return_value={
                "date_column": "Date",
                "description_column": "Description",
                "amount_column": "Amount",
            })
            
            mock_instance.interpret_file_columns = AsyncMock(return_value=mock_column_mapping)
            mock_instance.process_uploaded_file_with_reporting = AsyncMock(
                side_effect=[(50, "report-1", "upload-1"), (75, "report-2", "upload-2"), (60, "report-3", "upload-3")]
            )
            mock_instance.build_rag_corpus = AsyncMock(return_value=Mock(total_patterns=500))
            
            # Mock settings and Path operations
            with patch("giki_ai_api.domains.onboarding.router.settings") as mock_settings:
                mock_settings.get_upload_directory.return_value = Path("/tmp/uploads/1")
                
                # Mock Path operations more specifically to avoid pytest conflict
                with patch("pathlib.Path.mkdir"):
                    with patch("pathlib.Path.stat") as mock_stat:
                        mock_stat.return_value.st_size = 1000  # File size
                        with patch("pathlib.Path.exists", return_value=True):
                            # Create proper file mock with context manager
                            mock_file = Mock()
                            mock_file.__enter__ = Mock(return_value=mock_file)
                            mock_file.__exit__ = Mock(return_value=None)
                            mock_file.write = Mock()
                            
                            with patch("giki_ai_api.domains.onboarding.router.open", create=True, return_value=mock_file):
                                response = client.post(
                                    "/api/v1/onboarding/batch-upload-files",
                                    files=files,
                                    data={"year": "2024", "has_category_labels": True},
                                    headers=auth_headers,
                                )

                                assert response.status_code == status.HTTP_200_OK
                                data = response.json()
                                assert data["status"] == "completed"
                                assert data["summary"]["total_files_processed"] == 3
                                assert data["summary"]["successful_uploads"] == 3
                                assert data["summary"]["total_transactions_imported"] == 185
                                assert data["summary"]["rag_corpus_built"] is True