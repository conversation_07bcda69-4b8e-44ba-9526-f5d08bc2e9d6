"""
Comprehensive unit tests for Reports API endpoints.

Tests all 14 report endpoints with realistic data and minimal mocking.
Focuses on core product journey: data aggregation, analytics, and export functionality.
"""

import json
from datetime import date, datetime
from typing import Any, Dict, List
from unittest.mock import AsyncMock, Mock, patch

import pytest
from asyncpg import Connection
from fastapi import status
from fastapi.testclient import TestClient
from httpx import AsyncClient

from giki_ai_api.core.main import app
from giki_ai_api.domains.auth.models import User
from giki_ai_api.domains.reports.schemas import (
    CustomReportGenerateRequest,
    CustomReportSaveRequest,
    ReportDateRangeQueryFilters,
)


# Mock authenticated user
@pytest.fixture
def mock_current_user():
    """Mock authenticated user."""
    return User(
        id=1,
        email="<EMAIL>",
        username="<EMAIL>",
        is_active=True,
        is_verified=True,
        is_superuser=False,
        tenant_id=1,
    )


# Mock database connection
@pytest.fixture
def mock_db_connection():
    """Mock database connection."""
    return AsyncMock()


# Test client fixture with dependency overrides
@pytest.fixture
def client(mock_current_user, mock_db_connection):
    """Create test client with overridden dependencies."""
    from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session
    from giki_ai_api.domains.auth.secure_auth import get_current_active_user
    
    # Override FastAPI dependencies
    app.dependency_overrides[get_current_active_user] = lambda: mock_current_user
    app.dependency_overrides[get_current_tenant_id] = lambda: 1
    app.dependency_overrides[get_db_session] = lambda: mock_db_connection
    
    # Create test client
    test_client = TestClient(app)
    
    yield test_client
    
    # Clean up overrides
    app.dependency_overrides.clear()


# Test fixtures with realistic data
@pytest.fixture
def sample_transactions():
    """Realistic transaction data for testing reports."""
    return [
        {
            "id": 1,
            "tenant_id": 1,
            "date": date(2024, 1, 15),
            "description": "Amazon Web Services",
            "amount": -250.00,
            "ai_category": "Technology/Cloud Services",
            "original_category": None,
            "ai_suggested_category": 101,
            "entity_id": 1,
        },
        {
            "id": 2,
            "tenant_id": 1,
            "date": date(2024, 1, 16),
            "description": "Client Payment - ABC Corp",
            "amount": 5000.00,
            "ai_category": "Income/Client Payments",
            "original_category": "Income/Client Payments",
            "ai_suggested_category": 201,
            "entity_id": 2,
        },
        {
            "id": 3,
            "tenant_id": 1,
            "date": date(2024, 1, 20),
            "description": "Office Rent - January",
            "amount": -2000.00,
            "ai_category": "Expenses/Rent",
            "original_category": "Expenses/Rent",
            "ai_suggested_category": 301,
            "entity_id": 3,
        },
        {
            "id": 4,
            "tenant_id": 1,
            "date": date(2024, 2, 5),
            "description": "Starbucks Coffee",
            "amount": -15.50,
            "ai_category": "Expenses/Meals & Entertainment",
            "original_category": None,
            "ai_suggested_category": 401,
            "entity_id": 4,
        },
        {
            "id": 5,
            "tenant_id": 1,
            "date": date(2024, 2, 10),
            "description": "Software License - Zoom",
            "amount": -150.00,
            "ai_category": "Technology/Software",
            "original_category": "Technology/Software",
            "ai_suggested_category": 501,
            "entity_id": 5,
        },
    ]


@pytest.fixture
def sample_entities():
    """Realistic entity data for vendor analysis."""
    return [
        {"id": 1, "name": "Amazon Web Services", "tenant_id": 1},
        {"id": 2, "name": "ABC Corp", "tenant_id": 1},
        {"id": 3, "name": "Property Management LLC", "tenant_id": 1},
        {"id": 4, "name": "Starbucks", "tenant_id": 1},
        {"id": 5, "name": "Zoom", "tenant_id": 1},
    ]


@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test-token"}


class TestSpendingByCategory:
    """Test spending by category report endpoint."""

    def test_get_spending_by_category_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test successful retrieval of spending by category."""
        # Mock database response
        mock_rows = [
            {
                "category_path": "Technology/Cloud Services",
                "total_amount": -250.00,
                "transaction_count": 1,
            },
            {
                "category_path": "Expenses/Rent",
                "total_amount": -2000.00,
                "transaction_count": 1,
            },
            {
                "category_path": "Expenses/Meals & Entertainment",
                "total_amount": -15.50,
                "transaction_count": 1,
            },
            {
                "category_path": "Technology/Software",
                "total_amount": -150.00,
                "transaction_count": 1,
            },
        ]
        
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)
        
        # Mock cache to return None (cache miss)
        with patch("giki_ai_api.shared.cache.cache.api_cache.get", AsyncMock(return_value=None)):
            with patch("giki_ai_api.shared.cache.cache.api_cache.set", AsyncMock()):
                response = client.get(
                    "/api/v1/reports/spending-by-category",
                    headers=auth_headers,
                )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert len(data["items"]) == 4
        assert data["total_records"] == 4
        
        # Verify data structure
        first_item = data["items"][0]
        assert "category_path" in first_item
        assert "total_amount" in first_item
        assert "transaction_count" in first_item

    def test_get_spending_by_category_with_date_filter(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test spending by category with date range filter."""
        mock_rows = [
            {
                "category_path": "Technology/Cloud Services",
                "total_amount": -250.00,
                "transaction_count": 1,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/reports/spending-by-category",
            params={"start_date": "2024-01-01", "end_date": "2024-01-31"},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 1

    def test_get_spending_by_category_cached(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test that spending by category uses caching."""
        mock_rows = [
            {
                "category_path": "Technology/Cloud Services",
                "total_amount": -250.00,
                "transaction_count": 1,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)
        
        mock_cache_get = AsyncMock(side_effect=[None, {"items": mock_rows, "total_records": 1}])
        mock_cache_set = AsyncMock()
        
        with patch("giki_ai_api.shared.cache.cache.api_cache.get", mock_cache_get):
            with patch("giki_ai_api.shared.cache.cache.api_cache.set", mock_cache_set):
                # First request - cache miss
                response1 = client.get(
                    "/api/v1/reports/spending-by-category",
                    headers=auth_headers,
                )
                assert response1.status_code == status.HTTP_200_OK
                
                # Second request - cache hit
                response2 = client.get(
                    "/api/v1/reports/spending-by-category", 
                    headers=auth_headers,
                )
                assert response2.status_code == status.HTTP_200_OK
        
        # Database should only be called once due to caching
        assert mock_db_connection.fetch.call_count == 1
        # Cache should be set once
        assert mock_cache_set.call_count == 1


class TestSpendingByEntity:
    """Test spending by entity/vendor report endpoint."""

    def test_get_spending_by_entity_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test successful retrieval of spending by entity."""
        mock_rows = [
            {
                "entity_name": "Property Management LLC",
                "total_amount": 2000.00,
                "transaction_count": 1,
            },
            {
                "entity_name": "Amazon Web Services",
                "total_amount": 250.00,
                "transaction_count": 1,
            },
            {
                "entity_name": "Zoom",
                "total_amount": 150.00,
                "transaction_count": 1,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/reports/spending-by-entity",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert len(data["items"]) == 3
        assert data["total_records"] == 3

    def test_get_spending_by_entity_with_filters(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test spending by entity with date filters."""
        mock_rows = []
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/reports/spending-by-entity",
            params={"start_date": "2024-03-01", "end_date": "2024-03-31"},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 0


class TestIncomeExpenseSummary:
    """Test income/expense summary endpoint."""

    def test_get_income_expense_summary_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test successful retrieval of income/expense summary."""
        mock_rows = [
            {"transaction_type": "income", "total_amount": 5000.00},
            {"transaction_type": "expense", "total_amount": -2415.50},
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/reports/income-expense-summary",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total_income"] == 5000.00
        assert data["total_expenses"] == 2415.50  # Absolute value
        assert data["net_income_loss"] == 2584.50


class TestFinancialSummary:
    """Test financial summary endpoint."""

    def test_get_financial_summary_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test successful retrieval of financial summary."""
        mock_row = {
            "transaction_count": 5,
            "total_income": 5000.00,
            "total_expenses": 2415.50,
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_row)

        response = client.get(
            "/api/v1/reports/summary",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total_income"] == 5000.00
        assert data["total_expenses"] == 2415.50
        assert data["net_income"] == 2584.50
        assert data["transaction_count"] == 5
        assert "date_range" in data

    def test_get_financial_summary_with_date_range(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test financial summary with date range."""
        mock_row = {
            "transaction_count": 3,
            "total_income": 5000.00,
            "total_expenses": 2250.00,
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_row)

        response = client.get(
            "/api/v1/reports/summary",
            params={"start_date": "2024-01-01", "end_date": "2024-01-31"},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["date_range"]["start_date"] == "2024-01-01"
        assert data["date_range"]["end_date"] == "2024-01-31"

    def test_get_financial_summary_invalid_date_format(
        self, client: TestClient, auth_headers: dict
    ):
        """Test financial summary with invalid date format."""
        response = client.get(
            "/api/v1/reports/summary",
            params={"start_date": "invalid-date"},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid start_date format" in response.json()["detail"]


class TestMonthlyTrends:
    """Test monthly trends report endpoint."""

    def test_get_monthly_trends_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test successful retrieval of monthly trends."""
        mock_rows = [
            {
                "month": "2024-01",
                "transaction_type": "income",
                "total_amount": 5000.00,
                "transaction_count": 1,
            },
            {
                "month": "2024-01",
                "transaction_type": "expense",
                "total_amount": -2250.00,
                "transaction_count": 2,
            },
            {
                "month": "2024-02",
                "transaction_type": "expense",
                "total_amount": -165.50,
                "transaction_count": 2,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/reports/monthly-trends",
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert len(data["items"]) == 2  # Two months
        assert data["total_months"] == 2
        
        # Check January data
        jan_data = next(item for item in data["items"] if item["month"] == "2024-01")
        assert jan_data["total_income"] == 5000.00
        assert jan_data["total_expenses"] == 2250.00
        assert jan_data["net_amount"] == 2750.00
        assert jan_data["transaction_count"] == 3


class TestCategoryBreakdown:
    """Test category breakdown endpoint."""

    def test_get_category_breakdown_expenses(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test category breakdown for expenses."""
        mock_rows = [
            {
                "category_id": 301,
                "category_name": "Expenses/Rent",
                "gl_code": None,
                "category_path": "Expenses/Rent",
                "total_amount": 2000.00,
                "transaction_count": 1,
            },
            {
                "category_id": 101,
                "category_name": "Technology/Cloud Services",
                "gl_code": None,
                "category_path": "Technology/Cloud Services",
                "total_amount": 250.00,
                "transaction_count": 1,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/reports/category-breakdown",
            params={"type": "expense"},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["type"] == "expense"
        assert len(data["categories"]) == 2
        assert data["total_amount"] == 2250.00
        
        # Check percentage calculation
        assert data["categories"][0]["percentage"] == pytest.approx(88.89, 0.01)
        assert data["categories"][1]["percentage"] == pytest.approx(11.11, 0.01)

    def test_get_category_breakdown_income(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test category breakdown for income."""
        mock_rows = [
            {
                "category_id": 201,
                "category_name": "Income/Client Payments",
                "gl_code": None,
                "category_path": "Income/Client Payments",
                "total_amount": 5000.00,
                "transaction_count": 1,
            },
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_rows)

        response = client.get(
            "/api/v1/reports/category-breakdown",
            params={"type": "income"},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["type"] == "income"
        assert len(data["categories"]) == 1
        assert data["categories"][0]["percentage"] == 100.0


class TestCustomReports:
    """Test custom report endpoints."""

    def test_generate_custom_report(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test custom report generation."""
        request_data = {
            "report_type": "transaction_summary",
            "filters": {
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "categories": ["Technology/Cloud Services"],
            },
            "group_by": "category",
            "include_charts": True,
        }

        # Mock custom report service response
        with patch(
            "giki_ai_api.domains.reports.router.CustomReportService"
        ) as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.generate_custom_report = AsyncMock(return_value={
                "report_id": None,
                "report_type": "transaction_summary",
                "filters": {
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31",
                    "categories": ["Technology/Cloud Services"],
                },
                "date_range": {
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31",
                },
                "data": {
                    "items": [
                        {
                            "category": "Technology/Cloud Services",
                            "total_amount": 250.00,
                            "transaction_count": 1,
                        }
                    ],
                    "summary": {
                        "total_amount": 250.00,
                        "total_transactions": 1,
                    },
                },
                "metadata": {},
                "generated_at": datetime.now().isoformat(),
            })

            response = client.post(
                "/api/v1/reports/custom/generate",
                json=request_data,
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["report_type"] == "transaction_summary"
            assert "filters" in data
            assert "data" in data
            assert "metadata" in data

    def test_save_custom_report(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test saving custom report configuration."""
        request_data = {
            "name": "Monthly Technology Expenses",
            "description": "Track monthly spending on technology services",
            "report_type": "expense_tracking",
            "configuration": {
                "filters": {"categories": ["Technology"]},
                "group_by": "month",
            },
        }

        with patch(
            "giki_ai_api.domains.reports.router.CustomReportService"
        ) as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.save_custom_report = AsyncMock(return_value={
                "id": 1,
                "name": "Monthly Technology Expenses",
                "description": "Track monthly spending on technology services",
                "report_type": "expense_tracking",
                "configuration": request_data["configuration"],
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
            })

            response = client.post(
                "/api/v1/reports/custom/save",
                json=request_data,
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == 1
            assert data["name"] == "Monthly Technology Expenses"

    def test_list_custom_reports(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test listing saved custom reports."""
        with patch(
            "giki_ai_api.domains.reports.router.CustomReportService"
        ) as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.list_custom_reports = AsyncMock(return_value=[
                {
                    "id": 1,
                    "name": "Monthly Technology Expenses",
                    "description": "Track monthly technology expenses",
                    "report_type": "expense_tracking",
                    "configuration": {"filters": {"categories": ["Technology"]}},
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                },
                {
                    "id": 2,
                    "name": "Quarterly Income Analysis",
                    "description": "Analyze quarterly income trends",
                    "report_type": "income_analysis",
                    "configuration": {"filters": {"categories": ["Income"]}},
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                },
            ])

            response = client.get(
                "/api/v1/reports/custom/list",
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data["reports"]) == 2
            assert data["total"] == 2

    def test_delete_custom_report(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test deleting a custom report."""
        with patch(
            "giki_ai_api.domains.reports.router.CustomReportService"
        ) as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.delete_custom_report = AsyncMock(return_value=True)

            response = client.delete(
                "/api/v1/reports/custom/1",
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is True


class TestExportEndpoints:
    """Test export functionality endpoints."""

    def test_export_to_excel(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test Excel export endpoint."""
        with patch(
            "giki_ai_api.domains.reports.router.ProfessionalExcelExportService"
        ) as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.export_transactions = AsyncMock(return_value=b"excel-file-content")

            response = client.post(
                "/api/v1/reports/export/excel",
                params={
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31",
                    "include_charts": True,
                    "include_summary": True,
                },
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            assert "attachment" in response.headers["content-disposition"]

    def test_export_to_pdf(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test PDF export endpoint."""
        from io import BytesIO
        
        with patch(
            "giki_ai_api.domains.reports.router.ProfessionalPDFExportService"
        ) as mock_service:
            mock_instance = mock_service.return_value
            pdf_buffer = BytesIO(b"pdf-file-content")
            mock_instance.generate_financial_report_pdf = AsyncMock(return_value=pdf_buffer)

            response = client.post(
                "/api/v1/reports/export/pdf",
                params={
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31",
                    "include_charts": True,
                },
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            assert response.headers["content-type"] == "application/pdf"
            assert "attachment" in response.headers["content-disposition"]

    def test_export_to_csv(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test CSV export endpoint."""
        from io import StringIO
        
        with patch(
            "giki_ai_api.domains.reports.router.ProfessionalCSVExportService"
        ) as mock_service:
            mock_instance = mock_service.return_value
            csv_buffer = StringIO("Date,Description,Amount\n2024-01-15,Amazon Web Services,-250.00")
            mock_instance.generate_transactions_csv = AsyncMock(return_value=csv_buffer)

            response = client.post(
                "/api/v1/reports/export/csv",
                params={
                    "export_type": "transactions",
                    "format_style": "standard",
                    "include_metadata": True,
                },
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            assert "text/csv" in response.headers["content-type"]
            assert "attachment" in response.headers["content-disposition"]

    def test_export_csv_invalid_type(
        self, client: TestClient, auth_headers: dict
    ):
        """Test CSV export with invalid export type."""
        response = client.post(
            "/api/v1/reports/export/csv",
            params={"export_type": "invalid"},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to generate CSV export" in response.json()["detail"]


# Database error handling tests
class TestErrorHandling:
    """Test error handling in report endpoints."""

    def test_database_error_handling(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test handling of database errors."""
        # Mock database error
        mock_db_connection.fetch.side_effect = Exception("Database connection failed")
        
        # Ensure cache returns None so we hit the database
        with patch("giki_ai_api.shared.cache.cache.api_cache.get", AsyncMock(return_value=None)):
            response = client.get(
                "/api/v1/reports/spending-by-category",
                headers=auth_headers,
            )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Error fetching report data" in response.json()["detail"]


# Performance tests
class TestPerformance:
    """Test performance aspects of report endpoints."""

    def test_slow_query_logging(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test that slow queries are logged appropriately."""
        # Mock slow database response with empty result
        mock_db_connection.fetch = AsyncMock(return_value=[])
        
        # Ensure cache returns None so we hit the database
        with patch("giki_ai_api.shared.cache.cache.api_cache.get", AsyncMock(return_value=None)):
            with patch("giki_ai_api.shared.cache.cache.api_cache.set", AsyncMock()):
                response = client.get(
                    "/api/v1/reports/spending-by-category",
                    headers=auth_headers,
                )

        assert response.status_code == status.HTTP_200_OK
        # In real implementation, we would check logs for warning