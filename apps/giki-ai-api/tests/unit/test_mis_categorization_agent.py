"""
Unit tests for MISCategorizationAgent - The AI Engine.

This agent is the core AI categorization engine that uses Vertex AI
with business context and vendor intelligence to categorize transactions
into proper MIS (Management Information System) hierarchies.
"""

import json
import pytest
from unittest.mock import AsyncMock, Mock, patch
from asyncpg import Connection

from giki_ai_api.domains.categories.mis_categorization_agent import (
    MISCategorizationAgent,
    MISCategoryResult,
    HierarchicalMISResult
)


# Shared fixtures
@pytest.fixture
def mock_db_connection():
    """Create a mock database connection."""
    return Mock(spec=Connection)

@pytest.fixture
def categorization_agent():
    """Create MISCategorizationAgent instance."""
    return MISCategorizationAgent(tenant_id=1)

@pytest.fixture
def sample_tenant_context():
    """Sample tenant business context."""
    return {
        "tenant_name": "Test Company",
        "business_context": {
            "industry": "Technology",
            "company_size": "Medium",
            "location": "United States"
        },
        "income_categories": [
            {"name": "Software Revenue", "description": "Software sales", "gl_code": "4100"},
            {"name": "Consulting Revenue", "description": "Consulting services", "gl_code": "4200"}
        ],
        "expense_categories": [
            {"name": "Office Supplies", "description": "Office supplies and equipment", "gl_code": "5001"},
            {"name": "Software Licenses", "description": "Software licensing costs", "gl_code": "5100"},
            {"name": "Marketing Expenses", "description": "Marketing and advertising", "gl_code": "5200"}
        ]
    }

@pytest.fixture
def sample_ai_response():
    """Sample AI categorization response."""
    return {
        "category_name": "Office Supplies",
        "parent_category": "Expenses",
        "confidence": 0.95,
        "reasoning": "Transaction description indicates office supplies purchase from known vendor",
        "gl_code": "5001"
    }


class TestMISCategorizationAgent:
    """Test MISCategorizationAgent initialization and basic functionality."""

    def test_agent_initialization(self):
        """Test agent initializes with correct tenant ID."""
        tenant_id = 42
        agent = MISCategorizationAgent(tenant_id)
        
        assert agent.tenant_id == tenant_id
        assert agent.name == "giki_ai_mis_categorization"
        assert "MIS-focused transaction categorization" in agent.description

    def test_agent_inherits_from_standard_agent(self, categorization_agent):
        """Test agent inherits from StandardGikiAgent."""
        # Check it has standard agent functionality
        assert hasattr(categorization_agent, '_call_llm')
        assert hasattr(categorization_agent, 'get_tenant_context')
        assert hasattr(categorization_agent, 'search_vendor_info')


class TestTenantContextRetrieval:
    """Test business context and MIS categories retrieval."""

    @pytest.mark.asyncio
    async def test_get_tenant_context_success(self, categorization_agent, mock_db_connection):
        """Test successful tenant context retrieval."""
        # Mock tenant data
        mock_db_connection.fetchrow.return_value = {
            "name": "Tech Company Inc",
            "business_context": '{"industry": "Technology", "company_size": "Medium"}'
        }
        
        # Mock categories data
        mock_db_connection.fetch.return_value = [
            {
                "name": "Software Revenue",
                "description": "Software sales",
                "gl_code": "4100",
                "parent_name": "Income"
            },
            {
                "name": "Office Supplies", 
                "description": "Office supplies",
                "gl_code": "5001",
                "parent_name": "Expenses"
            },
            {
                "name": "Marketing Expenses",
                "description": "Marketing costs", 
                "gl_code": "5200",
                "parent_name": "Expenses"
            }
        ]
        
        context = await categorization_agent.get_tenant_context(mock_db_connection)
        
        assert context["tenant_name"] == "Tech Company Inc"
        assert context["business_context"]["industry"] == "Technology"
        assert context["business_context"]["company_size"] == "Medium"
        
        # Verify income categories
        assert len(context["income_categories"]) == 1
        assert context["income_categories"][0]["name"] == "Software Revenue"
        assert context["income_categories"][0]["gl_code"] == "4100"
        
        # Verify expense categories
        assert len(context["expense_categories"]) == 2
        expense_names = [cat["name"] for cat in context["expense_categories"]]
        assert "Office Supplies" in expense_names
        assert "Marketing Expenses" in expense_names

    @pytest.mark.asyncio
    async def test_get_tenant_context_no_data(self, categorization_agent, mock_db_connection):
        """Test tenant context when no data exists."""
        # Mock no tenant data
        mock_db_connection.fetchrow.return_value = None
        mock_db_connection.fetch.return_value = []
        
        context = await categorization_agent.get_tenant_context(mock_db_connection)
        
        assert context["tenant_name"] == "Unknown"
        assert context["business_context"] == {}
        assert context["income_categories"] == []
        assert context["expense_categories"] == []

    @pytest.mark.asyncio
    async def test_get_tenant_context_only_level_2_categories(self, categorization_agent, mock_db_connection):
        """Test that only level 2 categories are retrieved for AI context."""
        mock_db_connection.fetchrow.return_value = {
            "name": "Test Company",
            "business_context": '{}'
        }
        
        # Should only get level 2 categories
        mock_db_connection.fetch.return_value = [
            {"name": "Level 2 Category", "parent_name": "Expenses", "gl_code": "5001", "description": "Test"}
        ]
        
        context = await categorization_agent.get_tenant_context(mock_db_connection)
        
        # Verify SQL query targets level 2 categories
        mock_db_connection.fetch.assert_called_once()
        call_args = mock_db_connection.fetch.call_args[0]
        assert "level = 2" in call_args[0]  # SQL query should filter for level 2


class TestVendorIntelligence:
    """Test vendor intelligence and pattern recognition."""

    @pytest.mark.asyncio
    async def test_vendor_pattern_matching_office_supplies(self, categorization_agent):
        """Test vendor pattern matching for office supplies."""
        vendor_info = await categorization_agent.search_vendor_info("Office Depot")
        
        assert vendor_info is not None
        assert vendor_info["vendor_name"] == "Office Depot"
        assert vendor_info["category_hint"] == "office"
        assert vendor_info["pattern_match"] is True

    @pytest.mark.asyncio
    async def test_vendor_pattern_matching_technology(self, categorization_agent):
        """Test vendor pattern matching for technology vendors."""
        vendor_info = await categorization_agent.search_vendor_info("Microsoft Software Services")
        
        assert vendor_info is not None
        assert vendor_info["category_hint"] == "technology"
        assert vendor_info["pattern_match"] is True

    @pytest.mark.asyncio
    async def test_vendor_pattern_matching_utilities(self, categorization_agent):
        """Test vendor pattern matching for utility companies."""
        vendor_info = await categorization_agent.search_vendor_info("Pacific Gas & Electric")
        
        assert vendor_info is not None
        assert vendor_info["category_hint"] == "utilities"
        assert vendor_info["pattern_match"] is True

    @pytest.mark.asyncio
    async def test_vendor_pattern_matching_professional_services(self, categorization_agent):
        """Test vendor pattern matching for professional services."""
        vendor_info = await categorization_agent.search_vendor_info("Smith & Associates Legal")
        
        assert vendor_info is not None
        assert vendor_info["category_hint"] == "professional"
        assert vendor_info["pattern_match"] is True

    @pytest.mark.asyncio
    async def test_vendor_pattern_matching_no_match(self, categorization_agent):
        """Test vendor pattern matching when no pattern matches."""
        vendor_info = await categorization_agent.search_vendor_info("Unknown Vendor Corp")
        
        assert vendor_info is not None
        assert vendor_info["vendor_name"] == "Unknown Vendor Corp"
        assert vendor_info["category_hint"] == "general"
        assert vendor_info["pattern_match"] is False

    @pytest.mark.asyncio
    async def test_vendor_pattern_matching_case_insensitive(self, categorization_agent):
        """Test vendor pattern matching is case insensitive."""
        vendor_info = await categorization_agent.search_vendor_info("OFFICE DEPOT STORE")
        
        assert vendor_info is not None
        assert vendor_info["category_hint"] == "office"
        assert vendor_info["pattern_match"] is True

    @pytest.mark.asyncio
    async def test_vendor_pattern_coverage(self, categorization_agent):
        """Test vendor pattern coverage for common business categories."""
        test_cases = [
            ("State Farm Insurance", "insurance"),
            ("Johnson Electric Company", "utilities"), 
            ("ABC Marketing Agency", "marketing"),
            ("DataTech Software Solutions", "technology"),
            ("Global Travel Services", "travel"),
            ("Maintenance & Cleaning Co", "facilities"),
            ("HR Payroll Solutions", "payroll")
        ]
        
        for vendor_name, expected_category in test_cases:
            vendor_info = await categorization_agent.search_vendor_info(vendor_name)
            assert vendor_info["category_hint"] == expected_category, f"Failed for {vendor_name}"


class TestCoreCategorization:
    """Test core AI categorization functionality."""

    @pytest.mark.asyncio
    async def test_categorize_transaction_expense_success(self, categorization_agent, mock_db_connection, sample_tenant_context, sample_ai_response):
        """Test successful expense transaction categorization."""
        description = "Office Depot Purchase - Office Supplies"
        amount = -150.00  # Negative for expense
        
        # Mock dependencies using module-level patching for Google ADK agent compatibility
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.search_vendor_info') as mock_search_vendor:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                    with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                        
                        mock_get_context.return_value = sample_tenant_context
                        mock_extract_vendor.return_value = "Office Depot"
                        mock_search_vendor.return_value = {
                            "vendor_name": "Office Depot",
                            "category_hint": "office",
                            "pattern_match": True
                        }
                        mock_call_llm.return_value = json.dumps(sample_ai_response)
                        
                        result = await categorization_agent.categorize_transaction(
                            description=description,
                            amount=amount,
                            db_conn=mock_db_connection
                        )
                        
                        assert isinstance(result, MISCategoryResult)
                        assert result.category_name == "Office Supplies"
                        assert result.parent_category == "Expenses"
                        assert result.confidence == 0.95
                        assert result.gl_code == "5001"
                        assert result.vendor_info is not None
                        assert result.vendor_info["category_hint"] == "office"
                        
                        # Verify proper method calls
                        mock_get_context.assert_called_once_with(mock_db_connection)
                        mock_extract_vendor.assert_called_once_with(description)
                        mock_search_vendor.assert_called_once_with("Office Depot")

    @pytest.mark.asyncio
    async def test_categorize_transaction_income_success(self, categorization_agent, mock_db_connection, sample_tenant_context):
        """Test successful income transaction categorization."""
        description = "Client Payment - Software Services"
        amount = 5000.00  # Positive for income
        
        income_response = {
            "category_name": "Software Revenue",
            "parent_category": "Income",
            "confidence": 0.98,
            "reasoning": "Client payment for software services",
            "gl_code": "4100"
        }
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.search_vendor_info') as mock_search_vendor:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                    with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                        
                        mock_get_context.return_value = sample_tenant_context
                        mock_extract_vendor.return_value = None  # No vendor extracted
                        mock_search_vendor.return_value = None
                        mock_call_llm.return_value = json.dumps(income_response)
                        
                        result = await categorization_agent.categorize_transaction(
                            description=description,
                            amount=amount,
                            db_conn=mock_db_connection
                        )
                        
                        assert result.category_name == "Software Revenue"
                        assert result.parent_category == "Income"
                        assert result.confidence == 0.98
                        assert result.gl_code == "4100"

    @pytest.mark.asyncio
    async def test_categorize_transaction_mis_structure_enforcement(self, categorization_agent, mock_db_connection, sample_tenant_context):
        """Test MIS structure is enforced when AI returns invalid parent category."""
        description = "Random Transaction"
        amount = -100.00
        
        # AI returns invalid parent category
        invalid_response = {
            "category_name": "Some Category",
            "parent_category": "Invalid Parent",  # Not Income or Expenses
            "confidence": 0.80,
            "reasoning": "Some reasoning"
        }
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                    
                    mock_get_context.return_value = sample_tenant_context
                    mock_extract_vendor.return_value = None
                    mock_call_llm.return_value = json.dumps(invalid_response)
                    
                    result = await categorization_agent.categorize_transaction(
                        description=description,
                        amount=amount,
                        db_conn=mock_db_connection
                    )
                    
                    # Should force into Expenses since amount is negative
                    assert result.parent_category == "Expenses"
                    assert result.category_name == "Some Category"

    @pytest.mark.asyncio
    async def test_categorize_transaction_ai_failure(self, categorization_agent, mock_db_connection, sample_tenant_context):
        """Test proper error handling when AI fails."""
        description = "Test Transaction"
        amount = -100.00
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                    
                    mock_get_context.return_value = sample_tenant_context
                    mock_extract_vendor.return_value = None
                    mock_call_llm.side_effect = Exception("Vertex AI service unavailable")
                    
                    # Should re-raise the exception, not return fake data
                    with pytest.raises(Exception) as exc_info:
                        await categorization_agent.categorize_transaction(
                            description=description,
                            amount=amount,
                            db_conn=mock_db_connection
                        )
                    
                    assert "Vertex AI service unavailable" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_categorize_transaction_invalid_json_response(self, categorization_agent, mock_db_connection, sample_tenant_context):
        """Test handling of invalid JSON response from AI."""
        description = "Test Transaction"
        amount = -100.00
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                    
                    mock_get_context.return_value = sample_tenant_context
                    mock_extract_vendor.return_value = None
                    mock_call_llm.return_value = "Invalid JSON response"
                    
                    with pytest.raises(Exception):
                        await categorization_agent.categorize_transaction(
                            description=description,
                            amount=amount,
                            db_conn=mock_db_connection
                        )

    @pytest.mark.asyncio
    async def test_categorize_transaction_with_remarks(self, categorization_agent, mock_db_connection, sample_tenant_context, sample_ai_response):
        """Test categorization with remarks field (M1 Nuvie support)."""
        description = "Transaction Description"
        amount = -75.00
        remarks = "Office supplies for Q1 budget"
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                    with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._prepare_mis_prompt_data') as mock_prepare_prompt:
                        
                        mock_get_context.return_value = sample_tenant_context
                        mock_extract_vendor.return_value = None
                        mock_call_llm.return_value = json.dumps(sample_ai_response)
                        mock_prepare_prompt.return_value = {
                            "description": description,
                            "business_type": "Technology",
                            "vendor_context": "No vendor",
                            "categories_str": "Test categories",
                            "hint_context": "No hints",
                            "transaction_type": "expense",
                            "amount": abs(amount),
                            "parent_category": "Expenses",
                            "industry": "Technology",
                            "bank_context": "Standard",
                            "remarks": remarks
                        }
                        
                        result = await categorization_agent.categorize_transaction(
                            description=description,
                            amount=amount,
                            db_conn=mock_db_connection,
                            remarks=remarks
                        )
                        
                        # Verify remarks are passed to prompt preparation
                        mock_prepare_prompt.assert_called_once()
                        call_args = mock_prepare_prompt.call_args[1]  # kwargs
                        assert call_args["remarks"] == remarks

    @pytest.mark.asyncio
    async def test_categorize_transaction_list_response_handling(self, categorization_agent, mock_db_connection, sample_tenant_context):
        """Test handling when AI returns a list instead of dict."""
        description = "Test Transaction"
        amount = -100.00
        
        # AI returns list of results instead of single dict
        list_response = [
            {
                "category_name": "Office Supplies",
                "parent_category": "Expenses",
                "confidence": 0.95,
                "reasoning": "First result"
            },
            {
                "category_name": "Alternative Category",
                "parent_category": "Expenses", 
                "confidence": 0.85,
                "reasoning": "Second result"
            }
        ]
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                    
                    mock_get_context.return_value = sample_tenant_context
                    mock_extract_vendor.return_value = None
                    mock_call_llm.return_value = json.dumps(list_response)
                    
                    result = await categorization_agent.categorize_transaction(
                        description=description,
                        amount=amount,
                        db_conn=mock_db_connection
                    )
                    
                    # Should take the first result from the list
                    assert result.category_name == "Office Supplies"
                    assert result.confidence == 0.95


class TestOptimizedCategorization:
    """Test optimized categorization functionality for batch processing."""

    @pytest.mark.asyncio
    async def test_categorize_transaction_optimized_with_precomputed_data(self, categorization_agent, mock_db_connection, sample_tenant_context, sample_ai_response):
        """Test optimized categorization with pre-computed vendor and context data."""
        description = "Office supplies purchase"
        amount = -100.00
        
        # Pre-computed data to avoid database calls
        vendor_info = {
            "vendor_name": "Office Depot",
            "category_hint": "office",
            "pattern_match": True
        }
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
                    with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.search_vendor_info') as mock_search_vendor:
                        
                        mock_call_llm.return_value = json.dumps(sample_ai_response)
                        mock_extract_vendor.return_value = "Office Depot"
                        
                        result = await categorization_agent.categorize_transaction_optimized(
                            description=description,
                            amount=amount,
                            db_conn=mock_db_connection,
                            vendor_info=vendor_info,
                            tenant_context=sample_tenant_context
                        )
                        
                        assert result.category_name == "Office Supplies"
                        assert result.vendor_info == vendor_info
                        
                        # Should not call these methods since data is pre-computed
                        mock_get_context.assert_not_called()
                        mock_search_vendor.assert_not_called()

    @pytest.mark.asyncio
    async def test_categorize_transaction_optimized_fallback_to_regular(self, categorization_agent, mock_db_connection, sample_tenant_context, sample_ai_response):
        """Test optimized categorization falls back to regular methods when data not provided."""
        description = "Test transaction"
        amount = -50.00
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.search_vendor_info') as mock_search_vendor:
                    with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                        
                        mock_call_llm.return_value = json.dumps(sample_ai_response)
                        mock_get_context.return_value = sample_tenant_context
                        mock_extract_vendor.return_value = None
                        mock_search_vendor.return_value = None
                        
                        result = await categorization_agent.categorize_transaction_optimized(
                            description=description,
                            amount=amount,
                            db_conn=mock_db_connection
                            # No pre-computed data provided
                        )
                        
                        assert result.category_name == "Office Supplies"
                        
                        # Should call regular methods since no pre-computed data
                        mock_get_context.assert_called_once()


class TestPromptIntegration:
    """Test prompt registry integration and prompt data preparation."""

    @pytest.mark.asyncio
    async def test_prompt_registry_integration(self, categorization_agent, mock_db_connection, sample_tenant_context, sample_ai_response):
        """Test integration with centralized prompt registry."""
        description = "Test transaction"
        amount = -100.00
        
        with patch('giki_ai_api.shared.ai.prompt_registry.get_prompt_registry') as mock_get_registry:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                    with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._prepare_mis_prompt_data') as mock_prepare_prompt:
                            
                            # Mock prompt registry
                            mock_prompt = Mock()
                            mock_prompt.format.return_value = "Formatted prompt"
                            mock_prompt.model_config = {"temperature": 0.1}
                            
                            mock_registry = Mock()
                            mock_registry.get.return_value = mock_prompt
                            mock_get_registry.return_value = mock_registry
                            
                            # Mock other dependencies
                            mock_get_context.return_value = sample_tenant_context
                            mock_extract_vendor.return_value = None
                            mock_prepare_prompt.return_value = {"test": "data"}
                            mock_call_llm.return_value = json.dumps(sample_ai_response)
                            
                            await categorization_agent.categorize_transaction(
                                description=description,
                                amount=amount,
                                db_conn=mock_db_connection
                            )
                            
                            # Verify prompt registry usage
                            mock_get_registry.assert_called_once()
                            mock_registry.get.assert_called_once_with("mis_categorization_main")
                            mock_prompt.format.assert_called_once_with(test="data")
                            
                            # Verify AI call with model config
                            mock_call_llm.assert_called_once_with("Formatted prompt", {"temperature": 0.1})

    def test_vendor_name_extraction(self, categorization_agent):
        """Test vendor name extraction from transaction descriptions."""
        test_cases = [
            ("OFFICE DEPOT #123 PURCHASE", "OFFICE DEPOT"),
            ("ACH TRANSFER FROM BANK OF AMERICA", "BANK OF AMERICA"),
            ("STARBUCKS STORE #456", "STARBUCKS"),
            ("PAYMENT TO MICROSOFT CORP", "MICROSOFT CORP"),
            ("Simple Description", None)
        ]
        
        for description, expected_vendor in test_cases:
            # This tests the _extract_vendor_name method which should exist
            if hasattr(categorization_agent, '_extract_vendor_name'):
                result = categorization_agent._extract_vendor_name(description)
                if expected_vendor:
                    assert expected_vendor.lower() in result.lower() if result else False
                else:
                    assert result is None or result == ""


class TestConcurrencyAndSafety:
    """Test concurrency control and safety measures."""

    @pytest.mark.asyncio
    async def test_vertex_ai_concurrency_control(self, categorization_agent):
        """Test Vertex AI concurrency control using safe_vertex_ai_call."""
        prompt = "Test prompt"
        config = {"temperature": 0.1}
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.safe_vertex_ai_call') as mock_safe_call:
            mock_safe_call.return_value = "AI response"
            
            result = await categorization_agent._call_llm(prompt, config)
            
            assert result == "AI response"
            mock_safe_call.assert_called_once_with(prompt, config)

    @pytest.mark.asyncio
    async def test_vertex_ai_failure_propagation(self, categorization_agent):
        """Test that Vertex AI failures are properly propagated."""
        prompt = "Test prompt"
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.safe_vertex_ai_call') as mock_safe_call:
            mock_safe_call.side_effect = Exception("Vertex AI quota exceeded")
            
            with pytest.raises(Exception) as exc_info:
                await categorization_agent._call_llm(prompt)
            
            assert "Vertex AI quota exceeded" in str(exc_info.value)


class TestBusinessContextAwareness:
    """Test business context influences categorization decisions."""

    @pytest.mark.asyncio
    async def test_industry_specific_categorization(self, categorization_agent, mock_db_connection):
        """Test that industry context influences categorization."""
        # Technology company context
        tech_context = {
            "tenant_name": "Tech Corp",
            "business_context": {"industry": "Technology", "company_size": "Large"},
            "expense_categories": [
                {"name": "Software Licenses", "gl_code": "5100", "description": "Software licensing costs"},
                {"name": "Cloud Services", "gl_code": "5101", "description": "Cloud infrastructure costs"},
                {"name": "Development Tools", "gl_code": "5102", "description": "Development tool subscriptions"}
            ],
            "income_categories": []
        }
        
        tech_response = {
            "category_name": "Cloud Services",
            "parent_category": "Expenses",
            "confidence": 0.92,
            "reasoning": "AWS cloud service expense for technology company"
        }
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                    
                    mock_get_context.return_value = tech_context
                    mock_extract_vendor.return_value = "AWS"
                    mock_call_llm.return_value = json.dumps(tech_response)
                    
                    result = await categorization_agent.categorize_transaction(
                        description="AWS Cloud Services Monthly",
                        amount=-500.00,
                        db_conn=mock_db_connection
                    )
                    
                    # Should use industry-specific category
                    assert result.category_name == "Cloud Services"
                    assert "technology company" in result.reasoning.lower()

    @pytest.mark.asyncio
    async def test_company_size_context_usage(self, categorization_agent, mock_db_connection):
        """Test that company size influences categorization decisions."""
        large_company_context = {
            "tenant_name": "Enterprise Corp",
            "business_context": {"industry": "Manufacturing", "company_size": "Large"},
            "expense_categories": [
                {"name": "Enterprise Software", "gl_code": "5200"},
                {"name": "Legal & Compliance", "gl_code": "5300"}
            ],
            "income_categories": []
        }
        
        with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent.get_tenant_context') as mock_get_context:
            with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._prepare_mis_prompt_data') as mock_prepare_prompt:
                with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._call_llm') as mock_call_llm:
                    with patch('giki_ai_api.domains.categories.mis_categorization_agent.MISCategorizationAgent._extract_vendor_name') as mock_extract_vendor:
                        
                        mock_get_context.return_value = large_company_context
                        mock_extract_vendor.return_value = None
                        mock_prepare_prompt.return_value = {
                            "description": "Legal consultation fees",
                            "business_type": "Manufacturing",
                            "vendor_context": "No vendor",
                            "categories_str": "Test categories",
                            "hint_context": "No hints",
                            "transaction_type": "expense",
                            "amount": 1000.00,
                            "parent_category": "Expenses",
                            "industry": "Manufacturing",
                            "bank_context": "Standard"
                        }
                        mock_call_llm.return_value = json.dumps({
                            "category_name": "Test", 
                            "parent_category": "Expenses", 
                            "confidence": 0.8
                        })
                        
                        await categorization_agent.categorize_transaction(
                            description="Legal consultation fees",
                            amount=-1000.00,
                            db_conn=mock_db_connection
                        )
                        
                        # Verify business context is passed to prompt preparation
                        mock_prepare_prompt.assert_called_once()
                        call_args = mock_prepare_prompt.call_args[1]
                        context = call_args["context"]
                        assert context["business_context"]["company_size"] == "Large"
                        assert context["business_context"]["industry"] == "Manufacturing"


if __name__ == "__main__":
    """Run MIS categorization agent tests directly."""
    pytest.main([__file__, "-v", "-s", "--tb=short"])