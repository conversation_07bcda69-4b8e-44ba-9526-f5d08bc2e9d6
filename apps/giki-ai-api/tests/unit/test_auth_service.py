"""
Unit tests for authentication service methods.

Tests cover JWT token generation, password hashing, user authentication,
and token validation without external dependencies.
"""

import asyncio
from datetime import datetime, timedelta, timezone
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from asyncpg import Record
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from jose import jwt

from giki_ai_api.domains.auth import secure_auth
from giki_ai_api.domains.auth.models import UserDB


class TestPasswordHashing:
    """Test password hashing and verification functions."""
    
    def test_get_password_hash(self):
        """Test password hashing creates valid bcrypt hash."""
        password = "test_password_123"
        hashed = secure_auth.get_password_hash(password)
        
        assert hashed is not None
        assert hashed != password
        assert hashed.startswith("$2b$")  # bcrypt prefix
        assert len(hashed) == 60  # bcrypt hash length
    
    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        password = "test_password_123"
        hashed = secure_auth.get_password_hash(password)
        
        assert secure_auth.verify_password(password, hashed) is True
    
    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        password = "test_password_123"
        wrong_password = "wrong_password_456"
        hashed = secure_auth.get_password_hash(password)
        
        assert secure_auth.verify_password(wrong_password, hashed) is False
    
    def test_verify_password_invalid_hash(self):
        """Test password verification with invalid hash format."""
        password = "test_password_123"
        invalid_hash = "not_a_valid_hash"
        
        assert secure_auth.verify_password(password, invalid_hash) is False
    
    @pytest.mark.asyncio
    async def test_verify_password_fast(self):
        """Test async password verification."""
        password = "test_password_123"
        hashed = secure_auth.get_password_hash(password)
        
        result = await secure_auth.verify_password_fast(password, hashed)
        assert result is True
        
        wrong_result = await secure_auth.verify_password_fast("wrong", hashed)
        assert wrong_result is False


class TestJWTTokenGeneration:
    """Test JWT token generation and decoding."""
    
    @patch('giki_ai_api.domains.auth.secure_auth._get_private_key')
    @patch('giki_ai_api.domains.auth.secure_auth._get_public_key')
    def test_create_access_token(self, mock_public_key, mock_private_key, mock_rsa_keys, sample_user):
        """Test access token creation with RS256."""
        mock_private_key.return_value = mock_rsa_keys["private"]
        mock_public_key.return_value = mock_rsa_keys["public"]
        
        token = secure_auth.create_access_token(sample_user)
        
        assert token is not None
        assert isinstance(token, str)
        
        # Decode and verify token
        payload = jwt.decode(token, mock_rsa_keys["public"], algorithms=["RS256"])
        assert payload["sub"] == f"{sample_user.id}:{sample_user.tenant_id}"
        assert payload["type"] == "access"
        assert "exp" in payload
    
    @patch('giki_ai_api.domains.auth.secure_auth._get_private_key')
    @patch('giki_ai_api.domains.auth.secure_auth._get_public_key')
    def test_create_access_token_with_expiry(self, mock_public_key, mock_private_key, mock_rsa_keys, sample_user):
        """Test access token creation with custom expiry."""
        mock_private_key.return_value = mock_rsa_keys["private"]
        mock_public_key.return_value = mock_rsa_keys["public"]
        
        expires_delta = timedelta(minutes=60)
        token = secure_auth.create_access_token(sample_user, expires_delta)
        
        payload = jwt.decode(token, mock_rsa_keys["public"], algorithms=["RS256"])
        exp_time = datetime.fromtimestamp(payload["exp"], tz=timezone.utc)
        
        # Check expiry is approximately 60 minutes from now
        expected_exp = datetime.now(timezone.utc) + expires_delta
        assert abs((exp_time - expected_exp).total_seconds()) < 5  # 5 second tolerance
    
    @patch('giki_ai_api.domains.auth.secure_auth._get_private_key')
    @patch('giki_ai_api.domains.auth.secure_auth._get_public_key')
    def test_create_refresh_token(self, mock_public_key, mock_private_key, mock_rsa_keys, sample_user):
        """Test refresh token creation."""
        mock_private_key.return_value = mock_rsa_keys["private"]
        mock_public_key.return_value = mock_rsa_keys["public"]
        
        token = secure_auth.create_refresh_token(sample_user)
        
        assert token is not None
        assert isinstance(token, str)
        
        # Decode and verify token
        payload = jwt.decode(token, mock_rsa_keys["public"], algorithms=["RS256"])
        assert payload["sub"] == f"{sample_user.id}:{sample_user.tenant_id}"
        assert payload["type"] == "refresh"
        assert "exp" in payload
    
    @patch('giki_ai_api.domains.auth.secure_auth._get_private_key')
    @patch('giki_ai_api.domains.auth.secure_auth._get_public_key')
    def test_create_access_token_fast(self, mock_public_key, mock_private_key, mock_rsa_keys, sample_user):
        """Test optimized access token creation."""
        mock_private_key.return_value = mock_rsa_keys["private"]
        mock_public_key.return_value = mock_rsa_keys["public"]
        
        # Mock the _ALGORITHM variable
        with patch('giki_ai_api.domains.auth.secure_auth._ALGORITHM', 'RS256'):
            token = secure_auth.create_access_token_fast(sample_user)
        
        assert token is not None
        assert isinstance(token, str)
        
        # Verify it's a valid JWT
        payload = jwt.decode(token, mock_rsa_keys["public"], algorithms=["RS256"])
        assert payload["sub"] == f"{sample_user.id}:{sample_user.tenant_id}"
        assert payload["type"] == "access"
        assert "iat" in payload  # Fast version includes issued at time


class TestJWTTokenDecoding:
    """Test JWT token decoding and validation."""
    
    @patch('giki_ai_api.domains.auth.secure_auth._get_private_key')
    @patch('giki_ai_api.domains.auth.secure_auth._get_public_key')
    def test_decode_access_token_valid(self, mock_public_key, mock_private_key, mock_rsa_keys, sample_user):
        """Test decoding valid access token."""
        mock_private_key.return_value = mock_rsa_keys["private"]
        mock_public_key.return_value = mock_rsa_keys["public"]
        
        token = secure_auth.create_access_token(sample_user)
        user_id, tenant_id = secure_auth.decode_access_token(token)
        
        assert user_id == sample_user.id
        assert tenant_id == sample_user.tenant_id
    
    @patch('giki_ai_api.domains.auth.secure_auth._get_public_key')
    def test_decode_access_token_invalid(self, mock_public_key, mock_rsa_keys):
        """Test decoding invalid access token."""
        mock_public_key.return_value = mock_rsa_keys["public"]
        
        with pytest.raises(HTTPException) as exc_info:
            secure_auth.decode_access_token("invalid_token")
        
        assert exc_info.value.status_code == 401
        assert exc_info.value.detail == "Could not validate credentials"
    
    @patch('giki_ai_api.domains.auth.secure_auth._get_private_key')
    @patch('giki_ai_api.domains.auth.secure_auth._get_public_key')
    def test_decode_access_token_wrong_type(self, mock_public_key, mock_private_key, mock_rsa_keys, sample_user):
        """Test decoding refresh token as access token."""
        mock_private_key.return_value = mock_rsa_keys["private"]
        mock_public_key.return_value = mock_rsa_keys["public"]
        
        refresh_token = secure_auth.create_refresh_token(sample_user)
        
        with pytest.raises(HTTPException) as exc_info:
            secure_auth.decode_access_token(refresh_token)
        
        assert exc_info.value.status_code == 401
    
    @patch('giki_ai_api.domains.auth.secure_auth._get_private_key')
    @patch('giki_ai_api.domains.auth.secure_auth._get_public_key')
    def test_decode_refresh_token_valid(self, mock_public_key, mock_private_key, mock_rsa_keys, sample_user):
        """Test decoding valid refresh token."""
        mock_private_key.return_value = mock_rsa_keys["private"]
        mock_public_key.return_value = mock_rsa_keys["public"]
        
        token = secure_auth.create_refresh_token(sample_user)
        user_id, tenant_id = secure_auth.decode_refresh_token(token)
        
        assert user_id == sample_user.id
        assert tenant_id == sample_user.tenant_id
    
    @patch('giki_ai_api.domains.auth.secure_auth._get_private_key')
    @patch('giki_ai_api.domains.auth.secure_auth._get_public_key')
    def test_decode_token_with_none_tenant(self, mock_public_key, mock_private_key, mock_rsa_keys):
        """Test decoding token with None tenant_id."""
        mock_private_key.return_value = mock_rsa_keys["private"]
        mock_public_key.return_value = mock_rsa_keys["public"]
        
        # Manually create a token with None tenant_id
        from datetime import datetime, timezone, timedelta
        payload = {
            "exp": datetime.now(timezone.utc) + timedelta(minutes=30),
            "sub": "1:None",  # user_id:None format
            "type": "access",
        }
        token = jwt.encode(payload, mock_rsa_keys["private"], algorithm="RS256")
        
        user_id, tenant_id = secure_auth.decode_access_token(token)
        
        assert user_id == 1
        assert tenant_id is None


class TestUserAuthentication:
    """Test user authentication against database."""
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, mock_db_connection, sample_user_data, password_context):
        """Test successful user authentication."""
        # Hash a known password
        plain_password = "correct_password"
        hashed_password = password_context.hash(plain_password)
        
        # Update sample data with hashed password
        user_data = sample_user_data.copy()
        user_data["hashed_password"] = hashed_password
        
        # Mock database response with all required fields
        mock_record = {}
        for key, value in user_data.items():
            mock_record[key] = value
        # Add any missing fields that the query expects
        mock_record["username"] = user_data.get("username", user_data["email"])
        mock_record["is_verified"] = user_data.get("is_verified", False)
        mock_record["last_login"] = user_data.get("last_login")
        mock_record["login_count"] = user_data.get("login_count", 0)
        
        mock_db_connection.fetchrow.return_value = mock_record
        
        # Test authentication
        with patch('giki_ai_api.domains.auth.secure_auth.verify_password_fast', 
                   new_callable=AsyncMock) as mock_verify:
            mock_verify.return_value = True
            
            result = await secure_auth.authenticate_user_with_db(
                mock_db_connection, user_data["email"], plain_password
            )
        
        assert result is not None
        assert result.email == user_data["email"]
        assert result.id == user_data["id"]
        assert result.is_active is True
    
    @pytest.mark.asyncio
    async def test_authenticate_user_not_found(self, mock_db_connection):
        """Test authentication with non-existent user."""
        mock_db_connection.fetchrow.return_value = None
        
        result = await secure_auth.authenticate_user_with_db(
            mock_db_connection, "<EMAIL>", "password"
        )
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_wrong_password(self, mock_db_connection, sample_user_data):
        """Test authentication with wrong password."""
        # Mock database response
        mock_record = MagicMock(spec=Record)
        mock_record.__getitem__.side_effect = lambda key: sample_user_data.get(key)
        mock_record.get.side_effect = lambda key, default=None: sample_user_data.get(key, default)
        
        mock_db_connection.fetchrow.return_value = mock_record
        
        # Test authentication with wrong password
        with patch('giki_ai_api.domains.auth.secure_auth.verify_password_fast', 
                   new_callable=AsyncMock) as mock_verify:
            mock_verify.return_value = False
            
            result = await secure_auth.authenticate_user_with_db(
                mock_db_connection, sample_user_data["email"], "wrong_password"
            )
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_inactive(self, mock_db_connection, sample_user_data, password_context):
        """Test authentication with inactive user."""
        # Create inactive user data
        inactive_user_data = sample_user_data.copy()
        inactive_user_data["is_active"] = False
        inactive_user_data["hashed_password"] = password_context.hash("password")
        
        # Mock database response
        mock_record = MagicMock(spec=Record)
        mock_record.__getitem__.side_effect = lambda key: inactive_user_data.get(key)
        mock_record.get.side_effect = lambda key, default=None: inactive_user_data.get(key, default)
        
        mock_db_connection.fetchrow.return_value = mock_record
        
        # Test authentication
        with patch('giki_ai_api.domains.auth.secure_auth.verify_password_fast', 
                   new_callable=AsyncMock) as mock_verify:
            mock_verify.return_value = True
            
            result = await secure_auth.authenticate_user_with_db(
                mock_db_connection, inactive_user_data["email"], "password"
            )
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_database_error(self, mock_db_connection):
        """Test authentication with database error."""
        mock_db_connection.fetchrow.side_effect = Exception("Database connection error")
        
        result = await secure_auth.authenticate_user_with_db(
            mock_db_connection, "<EMAIL>", "password"
        )
        
        assert result is None


class TestGetCurrentUser:
    """Test getting current user from token."""
    
    @pytest.mark.asyncio
    @patch('giki_ai_api.domains.auth.secure_auth.decode_access_token')
    async def test_get_current_user_valid(self, mock_decode, mock_db_connection, sample_user_data):
        """Test getting current user with valid token."""
        mock_decode.return_value = (sample_user_data["id"], sample_user_data["tenant_id"])
        
        # Mock database response as a dict
        mock_db_connection.fetchrow.return_value = sample_user_data
        
        result = await secure_auth.get_current_user("valid_token", mock_db_connection)
        
        assert result is not None
        assert result.email == sample_user_data["email"]
        assert result.id == sample_user_data["id"]
    
    @pytest.mark.asyncio
    @patch('giki_ai_api.domains.auth.secure_auth.decode_access_token')
    async def test_get_current_user_not_found(self, mock_decode, mock_db_connection):
        """Test getting current user when user not found in database."""
        mock_decode.return_value = (999, 1)
        mock_db_connection.fetchrow.return_value = None
        
        with pytest.raises(HTTPException) as exc_info:
            await secure_auth.get_current_user("valid_token", mock_db_connection)
        
        assert exc_info.value.status_code == 401
        assert exc_info.value.detail == "Could not validate credentials"
    
    @pytest.mark.asyncio
    @patch('giki_ai_api.domains.auth.secure_auth.decode_access_token')
    async def test_get_current_user_inactive(self, mock_decode, mock_db_connection, sample_user_data):
        """Test getting current user when user is inactive."""
        mock_decode.return_value = (sample_user_data["id"], sample_user_data["tenant_id"])
        
        # Create inactive user data
        inactive_user_data = sample_user_data.copy()
        inactive_user_data["is_active"] = False
        
        # Mock database response as a dict
        mock_db_connection.fetchrow.return_value = inactive_user_data
        
        with pytest.raises(HTTPException) as exc_info:
            await secure_auth.get_current_user("valid_token", mock_db_connection)
        
        assert exc_info.value.status_code == 403
        assert exc_info.value.detail == "User account is deactivated"
    
    @pytest.mark.asyncio
    @patch('giki_ai_api.domains.auth.secure_auth.decode_access_token')
    async def test_get_current_user_none_tenant(self, mock_decode, mock_db_connection, sample_user_data):
        """Test getting current user with None tenant_id."""
        # Note: UserDB requires tenant_id to be an int, so we can't test None tenant_id
        # This is a limitation of the current model design
        # For now, we'll test with tenant_id = 0 which might represent "no tenant" 
        user_data = sample_user_data.copy()
        user_data["tenant_id"] = 0
        
        mock_decode.return_value = (user_data["id"], 0)
        
        # Mock database response as a dict
        mock_db_connection.fetchrow.return_value = user_data
        
        result = await secure_auth.get_current_user("valid_token", mock_db_connection)
        
        assert result is not None
        assert result.tenant_id == 0


class TestWebSocketAuthentication:
    """Test WebSocket-specific authentication."""
    
    @pytest.mark.asyncio
    @patch('giki_ai_api.domains.auth.secure_auth.decode_access_token')
    async def test_get_current_active_user_ws_valid(self, mock_decode, mock_db_connection, sample_user_data):
        """Test WebSocket authentication with valid token."""
        mock_decode.return_value = (sample_user_data["id"], sample_user_data["tenant_id"])
        
        # Mock database response as a dict
        mock_db_connection.fetchrow.return_value = sample_user_data
        
        result = await secure_auth.get_current_active_user_ws("valid_token", mock_db_connection)
        
        assert result is not None
        assert result.email == sample_user_data["email"]
    
    @pytest.mark.asyncio
    @patch('giki_ai_api.domains.auth.secure_auth.decode_access_token')
    async def test_get_current_active_user_ws_invalid(self, mock_decode, mock_db_connection):
        """Test WebSocket authentication with invalid token."""
        mock_decode.side_effect = HTTPException(status_code=401, detail="Invalid token")
        
        result = await secure_auth.get_current_active_user_ws("invalid_token", mock_db_connection)
        
        assert result is None


class TestAdminRequirement:
    """Test admin user requirement dependency."""
    
    @pytest.mark.asyncio
    async def test_require_admin_user_success(self, sample_admin):
        """Test admin requirement with admin user."""
        result = await secure_auth.require_admin_user(sample_admin)
        assert result == sample_admin
    
    @pytest.mark.asyncio
    async def test_require_admin_user_failure(self, sample_user):
        """Test admin requirement with non-admin user."""
        with pytest.raises(HTTPException) as exc_info:
            await secure_auth.require_admin_user(sample_user)
        
        assert exc_info.value.status_code == 403
        assert exc_info.value.detail == "Admin privileges required"