"""
Unit tests for EnhancedProcessingService.

Tests file processing, validation, categorization, and progress tracking.
"""

import io
import pytest
from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch
from asyncpg import Connection
import pandas as pd

from giki_ai_api.domains.files.enhanced_processing_service import EnhancedProcessingService
from giki_ai_api.domains.auth.models import User
from giki_ai_api.shared.exceptions import ValidationError


# Shared fixtures
@pytest.fixture
def mock_connection():
    """Create a mock database connection."""
    return Mock(spec=Connection)

@pytest.fixture
def enhanced_service(mock_connection):
    """Create EnhancedProcessingService instance with mock connection."""
    return EnhancedProcessingService(mock_connection)

@pytest.fixture
def sample_user():
    """Sample user for testing."""
    return User(
        id=1,
        email="<EMAIL>",
        name="Test User",
        tenant_id=1,
        role="owner"
    )

@pytest.fixture
def sample_upload_data():
    """Sample upload data for testing."""
    return {
        "id": "upload-123",
        "filename": "test_transactions.csv",
        "file_size": 1024,
        "status": "uploaded",
        "metadata": {}
    }

@pytest.fixture
def sample_csv_content():
    """Sample CSV content as bytes."""
    csv_data = """Date,Description,Amount
2024-01-01,Office Supplies,50.00
2024-01-02,Coffee Shop,15.50
2024-01-03,Gas Station,75.25"""
    return csv_data.encode('utf-8')

@pytest.fixture
def sample_file_data():
    """Sample file validation result."""
    return {
        "format": "csv",
        "size": 1024,
        "rows": 3,
        "columns": 3,
        "filename": "test_transactions.csv",
        "security_validated": True,
        "encoding": "utf-8"
    }


class TestEnhancedProcessingService:
    """Test EnhancedProcessingService class."""
    pass


class TestProcessingInitialization:
    """Test processing initialization and setup."""

    @pytest.mark.asyncio
    async def test_start_enhanced_processing_initialization(self, enhanced_service, sample_user, sample_upload_data):
        """Test processing state initialization."""
        upload_id = sample_upload_data["id"]
        
        # Mock all the processing steps to focus on initialization
        with patch.multiple(
            enhanced_service,
            _validate_and_extract_file=AsyncMock(return_value={"format": "csv", "rows": 10}),
            _analyze_schema=AsyncMock(return_value={"columns": []}),
            _parse_transactions=AsyncMock(return_value=[]),
            _categorize_transactions_enhanced=AsyncMock(return_value=[]),
            _generate_enhanced_results=AsyncMock(return_value={}),
            _update_progress=AsyncMock()
        ):
            result = await enhanced_service.start_enhanced_processing(upload_id, sample_user)
            
            # Check that processing state was created
            assert upload_id in enhanced_service.processing_cache
            state = enhanced_service.processing_cache[upload_id]
            
            assert state["upload_id"] == upload_id
            assert state["user_id"] == sample_user.id
            assert state["status"] == "completed"
            assert state["progress"] == 100
            assert state["total_steps"] == 5

    def test_processing_cache_management(self, enhanced_service):
        """Test processing cache initialization."""
        assert isinstance(enhanced_service.processing_cache, dict)
        assert len(enhanced_service.processing_cache) == 0


class TestFileValidation:
    """Test file validation and extraction functionality."""

    @pytest.mark.asyncio
    async def test_validate_and_extract_csv_success(self, enhanced_service, mock_connection, sample_csv_content):
        """Test successful CSV file validation."""
        upload_id = "test-upload"
        
        # Mock database response
        mock_connection.fetchrow = AsyncMock(return_value={
            "filename": "test.csv",
            "file_size": len(sample_csv_content),
            "status": "uploaded",
            "file_data": sample_csv_content,
            "metadata": {}
        })
        
        result = await enhanced_service._validate_and_extract_file(upload_id)
        
        assert result["format"] == "csv"
        assert result["rows"] == 3
        assert result["columns"] == 3
        assert result["security_validated"] is True
        assert result["filename"] == "test.csv"

    @pytest.mark.asyncio
    async def test_validate_file_not_found(self, enhanced_service, mock_connection):
        """Test validation when file is not found."""
        mock_connection.fetchrow = AsyncMock(return_value=None)
        
        with pytest.raises(ValidationError) as exc_info:
            await enhanced_service._validate_and_extract_file("nonexistent")
        
        assert "not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_unsupported_format(self, enhanced_service, mock_connection):
        """Test validation with unsupported file format."""
        mock_connection.fetchrow = AsyncMock(return_value={
            "filename": "test.txt",
            "file_size": 100,
            "status": "uploaded",
            "file_data": b"test content",
            "metadata": {}
        })
        
        with pytest.raises(ValidationError) as exc_info:
            await enhanced_service._validate_and_extract_file("test-upload")
        
        assert "Unsupported file format" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_file_too_large(self, enhanced_service, mock_connection):
        """Test validation with oversized file."""
        large_data = b"x" * (51 * 1024 * 1024)  # 51MB
        
        mock_connection.fetchrow = AsyncMock(return_value={
            "filename": "large.csv",
            "file_size": len(large_data),
            "status": "uploaded",
            "file_data": large_data,
            "metadata": {}
        })
        
        with pytest.raises(ValidationError) as exc_info:
            await enhanced_service._validate_and_extract_file("test-upload")
        
        assert "File too large" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_invalid_csv_format(self, enhanced_service, mock_connection):
        """Test validation with invalid CSV content."""
        invalid_csv = b"invalid,csv\nwith,unclosed,quote\""
        
        mock_connection.fetchrow = AsyncMock(return_value={
            "filename": "invalid.csv",
            "file_size": len(invalid_csv),
            "status": "uploaded",
            "file_data": invalid_csv,
            "metadata": {}
        })
        
        with pytest.raises(ValidationError) as exc_info:
            await enhanced_service._validate_and_extract_file("test-upload")
        
        assert "Invalid CSV format" in str(exc_info.value)


class TestSchemaAnalysis:
    """Test schema analysis functionality."""

    @pytest.mark.asyncio
    async def test_analyze_schema_success(self, enhanced_service, sample_file_data):
        """Test successful schema analysis."""
        upload_id = "test-upload"
        
        # Mock file service and dependencies
        mock_file_result = Mock()
        mock_file_result.sheets = [Mock()]
        mock_file_result.sheets[0].columns = ["Date", "Description", "Amount"]
        
        with patch.multiple(
            enhanced_service,
            _get_file_content=AsyncMock(return_value=b"mock content"),
            _get_tenant_id=AsyncMock(return_value=1)
        ):
            enhanced_service.file_service.process_file = AsyncMock(return_value=mock_file_result)
            
            result = await enhanced_service._analyze_schema(upload_id, sample_file_data)
            
            assert "detected_columns" in result
            assert "confidence_score" in result
            assert result["total_columns"] == 3

    @pytest.mark.asyncio
    async def test_analyze_schema_no_sheets(self, enhanced_service, sample_file_data):
        """Test schema analysis with no data sheets."""
        upload_id = "test-upload"
        
        # Mock file service with no sheets
        mock_file_result = Mock()
        mock_file_result.sheets = []
        
        with patch.multiple(
            enhanced_service,
            _get_file_content=AsyncMock(return_value=b"mock content"),
            _get_tenant_id=AsyncMock(return_value=1)
        ):
            enhanced_service.file_service.process_file = AsyncMock(return_value=mock_file_result)
            
            with pytest.raises(ValidationError) as exc_info:
                await enhanced_service._analyze_schema(upload_id, sample_file_data)
            
            assert "No data sheets found" in str(exc_info.value)


class TestTransactionParsing:
    """Test transaction parsing functionality."""

    @pytest.mark.asyncio
    async def test_parse_transactions_success(self, enhanced_service):
        """Test successful transaction parsing."""
        upload_id = "test-upload"
        file_data = {"format": "csv", "rows": 3}
        schema_analysis = {
            "detected_columns": {
                "date": {"column_index": 0, "confidence": 0.95},
                "description": {"column_index": 1, "confidence": 0.90},
                "amount": {"column_index": 2, "confidence": 0.98}
            }
        }
        
        # Mock dependencies
        with patch.multiple(
            enhanced_service,
            _get_file_content=AsyncMock(return_value=b"mock content"),
            _get_tenant_id=AsyncMock(return_value=1)
        ):
            mock_file_result = Mock()
            mock_sheet = Mock()
            mock_sheet.data = [
                ["2024-01-01", "Office Supplies", "50.00"],
                ["2024-01-02", "Coffee Shop", "15.50"],
                ["2024-01-03", "Gas Station", "75.25"]
            ]
            mock_file_result.sheets = [mock_sheet]
            enhanced_service.file_service.process_file = AsyncMock(return_value=mock_file_result)
            
            result = await enhanced_service._parse_transactions(upload_id, file_data, schema_analysis)
            
            assert len(result) == 3
            assert all("date" in txn for txn in result)
            assert all("description" in txn for txn in result)
            assert all("amount" in txn for txn in result)

    @pytest.mark.asyncio
    async def test_parse_transactions_validation_errors(self, enhanced_service):
        """Test transaction parsing with validation errors."""
        upload_id = "test-upload"
        file_data = {"format": "csv", "rows": 2}
        schema_analysis = {
            "detected_columns": {
                "date": {"column_index": 0, "confidence": 0.95},
                "amount": {"column_index": 2, "confidence": 0.98}
            }
        }
        
        # Mock file with invalid data
        with patch.multiple(
            enhanced_service,
            _get_file_content=AsyncMock(return_value=b"mock content"),
            _get_tenant_id=AsyncMock(return_value=1)
        ):
            mock_file_result = Mock()
            mock_sheet = Mock()
            mock_sheet.data = [
                ["invalid-date", "Office Supplies", "not-a-number"],
                ["2024-01-02", "Coffee Shop", "15.50"]
            ]
            mock_file_result.sheets = [mock_sheet]
            enhanced_service.file_service.process_file = AsyncMock(return_value=mock_file_result)
            
            result = await enhanced_service._parse_transactions(upload_id, file_data, schema_analysis)
            
            # Should still return valid transactions, skipping invalid ones
            assert len(result) == 1
            assert result[0]["description"] == "Coffee Shop"


class TestProgressTracking:
    """Test progress tracking functionality."""

    @pytest.mark.asyncio
    async def test_update_progress_success(self, enhanced_service):
        """Test successful progress update."""
        upload_id = "test-upload"
        
        # Initialize processing state
        enhanced_service.processing_cache[upload_id] = {
            "status": "processing",
            "progress": 0
        }
        
        callback = AsyncMock()
        
        await enhanced_service._update_progress(upload_id, 50, "Test step", callback)
        
        state = enhanced_service.processing_cache[upload_id]
        assert state["progress"] == 50
        assert state["current_step"] == "Test step"
        
        # Verify callback was called with individual arguments
        callback.assert_called_once_with(upload_id, 50, "Test step")

    @pytest.mark.asyncio
    async def test_update_progress_no_callback(self, enhanced_service):
        """Test progress update without callback."""
        upload_id = "test-upload"
        
        enhanced_service.processing_cache[upload_id] = {
            "status": "processing",
            "progress": 0
        }
        
        # Should not raise exception with None callback
        await enhanced_service._update_progress(upload_id, 75, "Another step", None)
        
        state = enhanced_service.processing_cache[upload_id]
        assert state["progress"] == 75


class TestErrorHandling:
    """Test error handling scenarios."""

    @pytest.mark.asyncio
    async def test_handle_processing_error(self, enhanced_service):
        """Test processing error handling."""
        upload_id = "test-upload"
        error_message = "Test error"
        
        # Initialize processing state
        enhanced_service.processing_cache[upload_id] = {
            "status": "processing",
            "progress": 50
        }
        
        callback = AsyncMock()
        
        await enhanced_service._handle_processing_error(upload_id, error_message, callback)
        
        state = enhanced_service.processing_cache[upload_id]
        assert state["status"] == "failed"
        assert state["error_message"] == error_message
        
        # Verify callback was called with error
        callback.assert_called_once_with(upload_id, -1, f"Error: {error_message}")

    @pytest.mark.asyncio
    async def test_processing_exception_handling(self, enhanced_service, sample_user):
        """Test exception handling in main processing flow."""
        upload_id = "test-upload"
        
        # Mock file validation to raise exception
        enhanced_service._validate_and_extract_file = AsyncMock(side_effect=Exception("Test exception"))
        enhanced_service._handle_processing_error = AsyncMock()
        
        with pytest.raises(Exception):
            await enhanced_service.start_enhanced_processing(upload_id, sample_user)
        
        # Verify error handler was called
        enhanced_service._handle_processing_error.assert_called_once_with(
            upload_id, "Test exception", None
        )


class TestHelperMethods:
    """Test helper methods."""

    @pytest.mark.asyncio
    async def test_get_file_content(self, enhanced_service, mock_connection):
        """Test file content retrieval."""
        upload_id = "test-upload"
        expected_content = b"test file content"
        
        mock_connection.fetchval = AsyncMock(return_value=expected_content)
        
        result = await enhanced_service._get_file_content(upload_id)
        
        assert result == expected_content
        mock_connection.fetchval.assert_called_once_with(
            "SELECT file_data FROM uploads WHERE id = $1", upload_id
        )

    @pytest.mark.asyncio
    async def test_get_tenant_id(self, enhanced_service, mock_connection):
        """Test tenant ID retrieval."""
        upload_id = "test-upload"
        expected_tenant_id = 42
        
        mock_connection.fetchval = AsyncMock(return_value=expected_tenant_id)
        
        result = await enhanced_service._get_tenant_id(upload_id)
        
        assert result == expected_tenant_id
        mock_connection.fetchval.assert_called_once_with(
            "SELECT tenant_id FROM uploads WHERE id = $1", upload_id
        )

    @pytest.mark.asyncio
    async def test_get_file_content_not_found(self, enhanced_service, mock_connection):
        """Test file content retrieval when file not found."""
        upload_id = "nonexistent"
        
        mock_connection.fetchval = AsyncMock(return_value=None)
        
        with pytest.raises(ValidationError) as exc_info:
            await enhanced_service._get_file_content(upload_id)
        
        assert "not found" in str(exc_info.value)


class TestCategorization:
    """Test categorization functionality."""

    @pytest.mark.asyncio
    async def test_categorize_transactions_enhanced_success(self, enhanced_service, sample_user):
        """Test successful transaction categorization."""
        upload_id = "test-upload"
        transactions = [
            {"id": 1, "description": "Office Supplies", "amount": 50.00},
            {"id": 2, "description": "Coffee Shop", "amount": 15.50}
        ]
        
        # Mock categorization service
        mock_categorized = [
            {**transactions[0], "category": "Office Expenses", "confidence": 0.95},
            {**transactions[1], "category": "Meals & Entertainment", "confidence": 0.88}
        ]
        
        enhanced_service.categorization_service.categorize_transactions_batch = AsyncMock(
            return_value=mock_categorized
        )
        enhanced_service._update_progress = AsyncMock()
        
        result = await enhanced_service._categorize_transactions_enhanced(
            upload_id, transactions, sample_user, None
        )
        
        assert len(result) == 2
        assert all("category" in txn for txn in result)
        assert all("confidence" in txn for txn in result)

    @pytest.mark.asyncio
    async def test_categorize_transactions_with_progress_updates(self, enhanced_service, sample_user):
        """Test categorization with progress updates."""
        upload_id = "test-upload"
        transactions = [{"id": i, "description": f"Transaction {i}"} for i in range(100)]
        
        enhanced_service.categorization_service.categorize_transactions_batch = AsyncMock(
            return_value=transactions
        )
        enhanced_service._update_progress = AsyncMock()
        
        callback = AsyncMock()
        
        await enhanced_service._categorize_transactions_enhanced(
            upload_id, transactions, sample_user, callback
        )
        
        # Verify progress updates were called
        assert enhanced_service._update_progress.call_count > 1


class TestResultsGeneration:
    """Test results generation functionality."""

    @pytest.mark.asyncio
    async def test_generate_enhanced_results_success(self, enhanced_service):
        """Test successful results generation."""
        upload_id = "test-upload"
        categorized_transactions = [
            {"category": "Office Expenses", "amount": 50.00, "confidence": 0.95},
            {"category": "Travel", "amount": 100.00, "confidence": 0.88},
            {"category": "Office Expenses", "amount": 25.00, "confidence": 0.92}
        ]
        processing_state = {
            "transaction_count": 3,
            "start_time": datetime.utcnow(),
            "user_id": 1
        }
        
        result = await enhanced_service._generate_enhanced_results(
            upload_id, categorized_transactions, processing_state
        )
        
        assert result["upload_id"] == upload_id
        assert result["total_transactions"] == 3
        assert result["categorized_count"] == 3
        assert "accuracy_metrics" in result
        assert "category_breakdown" in result
        assert "processing_summary" in result

    @pytest.mark.asyncio
    async def test_generate_results_accuracy_calculation(self, enhanced_service):
        """Test accuracy calculation in results."""
        upload_id = "test-upload"
        categorized_transactions = [
            {"confidence": 0.95},
            {"confidence": 0.88},
            {"confidence": 0.92},
            {"confidence": 0.75}
        ]
        processing_state = {"transaction_count": 4}
        
        result = await enhanced_service._generate_enhanced_results(
            upload_id, categorized_transactions, processing_state
        )
        
        accuracy = result["accuracy_metrics"]
        assert accuracy["average_confidence"] == 0.875
        assert accuracy["high_confidence_count"] == 3  # >= 0.8
        assert accuracy["low_confidence_count"] == 1   # < 0.8