"""
Comprehensive unit tests for Transactions API endpoints.

Tests the core transaction review, categorization, and improvement functionality.
"""

import json
from datetime import datetime, timezone, date
from decimal import Decimal
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from giki_ai_api.core.main import app
from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session, get_current_user_with_tenant
from giki_ai_api.domains.auth.models import User, Tenant
from giki_ai_api.domains.auth.secure_auth import get_current_active_user


@pytest.fixture
def mock_user():
    """Mock authenticated user."""
    return User(
        id=1,
        email="<EMAIL>",
        username="<EMAIL>",
        is_active=True,
        is_verified=True,
        is_superuser=False,
        tenant_id=1,
    )


@pytest.fixture
def mock_tenant():
    """Mock tenant."""
    return Tenant(
        id=1,
        name="Test Company",
        domain="test.com",
        subscription_status="active",
        subscription_plan="free",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
    )


@pytest.fixture
def mock_db_connection():
    """Mock database connection."""
    return AsyncMock()


@pytest.fixture
def mock_user_tenant(mock_user, mock_tenant):
    """Mock user-tenant tuple."""
    return (mock_user, mock_tenant)


@pytest.fixture
def client(mock_user, mock_tenant, mock_db_connection, mock_user_tenant):
    """Create test client with overridden dependencies."""
    # Override FastAPI dependencies
    app.dependency_overrides[get_current_active_user] = lambda: mock_user
    app.dependency_overrides[get_current_tenant_id] = lambda: mock_tenant.id
    app.dependency_overrides[get_db_session] = lambda: mock_db_connection
    app.dependency_overrides[get_current_user_with_tenant] = lambda: mock_user_tenant
    
    # Create test client
    test_client = TestClient(app)
    
    yield test_client
    
    # Clean up overrides
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def sample_transactions():
    """Sample transaction data for testing."""
    return [
        {
            "id": "123e4567-e89b-12d3-a456-************",
            "date": date(2024, 1, 15),
            "description": "AMAZON WEB SERVICES",
            "amount": -250.50,
            "account": "Business Checking",
            "transaction_type": "debit",
            "category_id": 15,
            "category_name": "Technology/Cloud Services",
            "original_category": "Technology/Cloud Services",
            "vendor": "Amazon",
            "vendor_name": "Amazon",
            "confidence_score": 0.95,
            "needs_review": False,
            "ai_suggested_category_id": 15,
            "ai_category": "Technology/Cloud Services",
            "ai_confidence": 0.95,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        },
        {
            "id": "223e4567-e89b-12d3-a456-************",
            "date": date(2024, 1, 20),
            "description": "OFFICE DEPOT #1234",
            "amount": -89.99,
            "account": "Business Checking",
            "transaction_type": "debit",
            "category_id": None,
            "category_name": None,
            "original_category": None,
            "vendor": "Office Depot",
            "vendor_name": "Office Depot",
            "confidence_score": 0.45,
            "needs_review": True,
            "ai_suggested_category_id": 8,
            "ai_category": None,
            "ai_confidence": 0.45,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        },
    ]


class TestTransactionRetrieval:
    """Test transaction retrieval endpoints."""

    def test_get_transactions_list(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict, sample_transactions
    ):
        """Test retrieving paginated list of transactions."""
        # Mock database response
        mock_db_connection.fetch = AsyncMock(return_value=sample_transactions)
        mock_db_connection.fetchval = AsyncMock(return_value=250)  # total count

        response = client.get("/api/v1/transactions", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data
        assert len(data["items"]) == 2
        assert data["total_count"] == 250

    def test_get_transaction_by_id(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict, sample_transactions
    ):
        """Test retrieving a specific transaction by ID."""
        transaction_id = "123e4567-e89b-12d3-a456-************"
        mock_db_connection.fetchrow = AsyncMock(return_value=sample_transactions[0])

        response = client.get(f"/api/v1/transactions/{transaction_id}", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == transaction_id
        assert data["description"] == "AMAZON WEB SERVICES"
        assert data["amount"] == -250.50

    def test_get_transactions_for_review(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict, sample_transactions
    ):
        """Test retrieving transactions that need review."""
        # Only return transactions with needs_review=True
        review_transactions = [t for t in sample_transactions if t.get("needs_review", False)]
        mock_db_connection.fetch = AsyncMock(return_value=review_transactions)
        mock_db_connection.fetchval = AsyncMock(return_value=45)  # count of review items

        response = client.get("/api/v1/transactions/review-queue", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "review_queue" in data
        assert len(data["review_queue"]) == 1
        assert data["queue_size"] == 1
        assert "average_confidence" in data


class TestTransactionCategorization:
    """Test transaction categorization and review endpoints."""

    def test_update_transaction_category(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test updating a transaction's category."""
        transaction_id = "123e4567-e89b-12d3-a456-************"
        update_data = {
            "category_id": 18,
            "user_confirmed": True,
            "notes": "Correctly categorized as cloud services",
        }
        
        # Mock database operations
        mock_db_connection.fetchval = AsyncMock()
        mock_db_connection.fetchval.side_effect = [
            transaction_id,  # First call: transaction exists check
            18,  # Second call: category exists check
            transaction_id,  # Third call: update query RETURNING id
        ]
        mock_db_connection.execute = AsyncMock()

        response = client.put(
            f"/api/v1/transactions/{transaction_id}/category?category_id={update_data['category_id']}",
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["transaction_id"] == transaction_id

    def test_batch_categorize_transactions(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test batch categorization of multiple transactions."""
        batch_data = {
            "transaction_ids": [
                "123e4567-e89b-12d3-a456-************",
                "223e4567-e89b-12d3-a456-************",
            ],
            "category_id": 15,
            "apply_to_similar": True,
        }
        
        # Mock database operations
        mock_db_connection.execute = AsyncMock(return_value="UPDATE 12")  # PostgreSQL command result
        mock_db_connection.fetchval = AsyncMock(return_value=12)  # number of updated

        response = client.put(
            "/api/v1/transactions/batch/category",
            json=batch_data,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["updated_count"] == 12
        assert data["success"] is True

    def test_approve_ai_suggestions(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test approving AI categorization suggestions."""
        approval_data = ["223e4567-e89b-12d3-a456-************"]
        
        # Mock database operations  
        mock_db_connection.fetch = AsyncMock(return_value=[{
            "id": "223e4567-e89b-12d3-a456-************",
            "ai_suggested_category_id": 8,
            "confidence_score": 0.89,
        }])
        mock_db_connection.execute = AsyncMock(return_value="UPDATE 1")

        response = client.post(
            "/api/v1/transactions/bulk-approve",
            json=approval_data,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["approved_count"] == 1
        assert data["success"] is True


class TestTransactionImprovement:
    """Test transaction improvement and learning endpoints."""

    def test_learn_from_corrections(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test learning from user corrections to improve categorization."""
        learning_data = {
            "transaction_ids": ["1", "2"],
            "improvement_type": "accuracy",
            "feedback": "Learn from recent user corrections"
        }
        
        # Mock database operations for the improvement endpoint
        mock_db_connection.fetch = AsyncMock(return_value=[
            {
                "id": "1", 
                "description": "AWS SERVICES",
                "ai_confidence": 0.85,
                "ai_category": "Technology/Cloud Services",
                "ai_suggested_category_id": 15,
                "category_id": 15,
                "amount": -250.50
            },
            {
                "id": "2", 
                "description": "OFFICE DEPOT",
                "ai_confidence": 0.65,
                "ai_category": "Office Supplies",
                "ai_suggested_category_id": 8,
                "category_id": None,
                "amount": -89.99
            }
        ])

        response = client.post(
            "/api/v1/transactions/improve-categorization",
            json=learning_data,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "improvement_id" in data
        assert "processing_status" in data
        assert "improvements_suggested" in data

    def test_get_categorization_insights(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving categorization insights and patterns."""
        # Mock database aggregation results
        mock_db_connection.fetch = AsyncMock(return_value=[
            {
                "category_name": "Office Supplies",
                "total_transactions": 145,
                "ai_accuracy": 0.89,
                "user_corrections": 16,
                "common_vendors": ["Staples", "Office Depot", "Amazon"],
            },
            {
                "category_name": "Software",
                "total_transactions": 89,
                "ai_accuracy": 0.94,
                "user_corrections": 5,
                "common_vendors": ["Microsoft", "Adobe", "Salesforce"],
            },
        ])

        response = client.get("/api/v1/transactions/categorization-insights", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "insights" in data
        assert len(data["insights"]) >= 2
        assert data["insights"][0]["category_name"] == "Office Supplies"
        assert data["insights"][0]["ai_accuracy"] == 0.89


class TestTransactionSearch:
    """Test transaction search and filtering."""

    def test_search_transactions(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict, sample_transactions
    ):
        """Test searching transactions by description."""
        search_params = {
            "q": "amazon",
            "date_from": "2024-01-01",
            "date_to": "2024-01-31",
        }
        
        # Mock search results
        mock_db_connection.fetch = AsyncMock(return_value=[sample_transactions[0]])
        mock_db_connection.fetchval = AsyncMock(return_value=1)

        response = client.get("/api/v1/transactions", params={"search_term": search_params["q"], "start_date": search_params["date_from"], "end_date": search_params["date_to"]}, headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 1
        assert "AMAZON" in data["items"][0]["description"].upper()

    def test_filter_by_category(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test filtering transactions by category."""
        category_id = 15
        
        # Mock filtered results
        mock_db_connection.fetch = AsyncMock(return_value=[{
            "id": "123e4567-e89b-12d3-a456-************",
            "date": date(2024, 1, 15),
            "description": "AWS MONTHLY",
            "amount": -156.78,
            "category_id": category_id,
            "category_path": "Business > Technology > Cloud Services",
            "ai_suggested_category": None,
            "ai_suggested_category_path": None,
            "ai_category_confidence": None,
            "is_categorized": True,
            "is_user_modified": False,
            "user_corrected": False,
            "entity_id": None,
            "upload_id": None,
            "status": "categorized",
            "vendor_name": "Amazon Web Services",
            "original_category": "Technology/Cloud Services",
            "ai_category": None,
            "account": None,
            "transaction_type": None
        }])
        mock_db_connection.fetchval = AsyncMock(return_value=23)

        response = client.get(f"/api/v1/transactions?category_id={category_id}", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert all(item["category_id"] == category_id for item in data["items"])


class TestTransactionExport:
    """Test transaction export functionality."""

    def test_export_transactions_csv(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict, sample_transactions
    ):
        """Test exporting transactions to CSV format."""
        export_params = {
            "format": "csv",
            "date_from": "2024-01-01",
            "date_to": "2024-01-31",
            "include_categories": True,
        }
        
        # Mock export data
        mock_db_connection.fetch = AsyncMock(return_value=sample_transactions)

        response = client.get("/api/v1/transactions/export", params=export_params, headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "text/csv; charset=utf-8"
        assert "attachment" in response.headers.get("content-disposition", "")


class TestErrorHandling:
    """Test error handling scenarios."""

    def test_get_nonexistent_transaction(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving a non-existent transaction."""
        mock_db_connection.fetchrow = AsyncMock(return_value=None)

        response = client.get("/api/v1/transactions/999", headers=auth_headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_update_transaction_validation_error(
        self, client: TestClient, auth_headers: dict
    ):
        """Test updating transaction with invalid data."""
        invalid_update = {
            "category_id": "invalid",  # Should be integer
            "amount": "not-a-number",
        }

        response = client.put("/api/v1/transactions/123/category?category_id=invalid", headers=auth_headers)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_batch_operation_partial_failure(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test batch operation with partial failures."""
        batch_data = {
            "transaction_ids": ["valid-id", "invalid-id"],
            "category_id": 15,
        }
        
        # Mock partial success - the batch endpoint does validation and returns a result
        mock_db_connection.fetch = AsyncMock(return_value=[{"id": "valid-id"}])  # Only valid ID found
        mock_db_connection.execute = AsyncMock(return_value="UPDATE 1")  # Only 1 updated

        response = client.put("/api/v1/transactions/batch/category", json=batch_data, headers=auth_headers)

        # Should still return success but indicate partial failure
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_207_MULTI_STATUS]