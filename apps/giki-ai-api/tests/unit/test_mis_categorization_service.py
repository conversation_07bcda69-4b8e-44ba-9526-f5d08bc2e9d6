"""
Unit tests for MISCategorizationService - Core Business Logic.

This service is the heart of giki.ai's value proposition: ensuring every
customer gets a complete Management Information System with proper
Income/Expense hierarchy and industry-specific categorization.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch
from asyncpg import Connection

from giki_ai_api.domains.categories.mis_categorization_service import (
    MISCategorizationService,
    MISCategorizationResult,
    BusinessContext
)
from giki_ai_api.domains.categories.mis_categorization_agent import (
    MISCategoryResult,
    HierarchicalMISResult
)
from giki_ai_api.shared.exceptions import ServiceError


# Shared fixtures
@pytest.fixture
def mock_db_connection():
    """Create a mock database connection."""
    return Mock(spec=Connection)

@pytest.fixture
def mis_service(mock_db_connection):
    """Create MISCategorizationService instance."""
    return MISCategorizationService(mock_db_connection)

@pytest.fixture
def sample_business_context():
    """Sample business context for testing."""
    return BusinessContext(
        tenant_id=1,
        industry="Technology",
        company_size="Small",
        company_website="https://example.com",
        fiscal_year_start="April",
        reporting_frequency="Monthly",
        existing_gl_structure=None,
        categorization_hints={"office supplies": "Operating Expenses"}
    )

@pytest.fixture
def sample_transaction():
    """Sample transaction data for testing."""
    return {
        "id": "txn-123",
        "description": "Office Depot Purchase",
        "amount": 150.00,
        "date": "2024-01-15",
        "vendor": "Office Depot",
        "remarks": "Monthly office supplies"
    }

@pytest.fixture
def sample_ai_result():
    """Sample AI categorization result."""
    return MISCategoryResult(
        category_name="Office Supplies",
        parent_category="Operating Expenses",
        confidence=0.95,
        reasoning="Clear office supplies purchase from known vendor",
        vendor_info={"name": "Office Depot", "category": "Office Supplies"},
        gl_code="5001"
    )

@pytest.fixture
def sample_hierarchical_result():
    """Sample hierarchical AI result."""
    return HierarchicalMISResult(
        level1_category="Expenses",
        level2_category="Operating Expenses", 
        level3_category="Office Supplies",
        confidence=0.95,
        reasoning="Office supplies purchase from Office Depot",
        vendor_info={"name": "Office Depot", "type": "retail"},
        gl_code="5001"
    )


class TestMISCategorizationService:
    """Test MISCategorizationService class initialization."""
    
    def test_service_initialization(self, mock_db_connection):
        """Test service initializes correctly."""
        service = MISCategorizationService(mock_db_connection)
        
        assert service.db == mock_db_connection
        assert hasattr(service, 'category_service')
        assert hasattr(service, '_agent_cache')
        assert isinstance(service._agent_cache, dict)


class TestCoreCategorization:
    """Test core categorization functionality - the heart of giki.ai."""

    @pytest.mark.asyncio
    async def test_categorize_transaction_success(self, mis_service, sample_transaction, sample_ai_result):
        """Test successful transaction categorization with MIS structure."""
        tenant_id = 1
        
        # Mock all dependencies using patch.object
        with patch.object(mis_service, '_get_business_context', return_value={"industry": "Technology"}) as mock_get_context:
            with patch.object(mis_service, '_get_or_create_agent') as mock_get_agent:
                with patch.object(mis_service, '_ensure_category_exists', return_value={
                    "category_id": "cat-123",
                    "full_path": "Expenses > Operating Expenses > Office Supplies",
                    "gl_code": "5001",
                    "created_new": False
                }) as mock_ensure_category:
                    
                    # Mock agent categorization
                    mock_agent = Mock()
                    mock_agent.categorize_transaction = AsyncMock(return_value=sample_ai_result)
                    mock_get_agent.return_value = mock_agent
                    
                    result = await mis_service.categorize_transaction(
                        tenant_id=tenant_id,
                        transaction_id=sample_transaction["id"],
                        description=sample_transaction["description"],
                        amount=sample_transaction["amount"],
                        transaction_date=sample_transaction["date"],
                        vendor=sample_transaction["vendor"],
                        remarks=sample_transaction["remarks"]
                    )
                    
                    # Validate MIS structure is enforced
                    assert isinstance(result, MISCategorizationResult)
                    assert result.transaction_id == sample_transaction["id"]
                    assert result.category_name == "Office Supplies"
                    assert result.parent_category == "Operating Expenses"
                    assert result.full_path == "Expenses > Operating Expenses > Office Supplies"
                    assert result.gl_code == "5001"
                    assert result.confidence == 0.95
                    assert result.category_id == "cat-123"
                    
                    # Verify proper method calls
                    mock_get_context.assert_called_once_with(tenant_id)
                    mock_get_agent.assert_called_once_with(tenant_id)
                    mock_agent.categorize_transaction.assert_called_once()

    @pytest.mark.asyncio
    async def test_categorize_transaction_ai_failure(self, mis_service, sample_transaction):
        """Test proper error handling when AI categorization fails."""
        tenant_id = 1
        
        with patch.object(mis_service, '_get_business_context', return_value={"industry": "Technology"}):
            with patch.object(mis_service, '_get_or_create_agent') as mock_get_agent:
                # Mock agent to raise exception
                mock_agent = Mock()
                mock_agent.categorize_transaction = AsyncMock(side_effect=Exception("AI service unavailable"))
                mock_get_agent.return_value = mock_agent
                
                with pytest.raises(ServiceError) as exc_info:
                    await mis_service.categorize_transaction(
                        tenant_id=tenant_id,
                        transaction_id=sample_transaction["id"],
                        description=sample_transaction["description"],
                        amount=sample_transaction["amount"]
                    )
                
                assert "AI categorization failed" in str(exc_info.value)
                assert exc_info.value.service_name == "MISCategorizationService"
                assert exc_info.value.operation == "categorize_transaction"

    @pytest.mark.asyncio
    async def test_categorize_transaction_creates_new_category(self, mis_service, sample_transaction, sample_ai_result):
        """Test that new categories are created with proper MIS hierarchy."""
        tenant_id = 1
        
        with patch.object(mis_service, '_get_business_context', return_value={"industry": "Technology"}):
            with patch.object(mis_service, '_get_or_create_agent') as mock_get_agent:
                with patch.object(mis_service, '_ensure_category_exists', return_value={
                    "category_id": "cat-new-123",
                    "full_path": "Expenses > Operating Expenses > New Category",
                    "gl_code": "5099",
                    "created_new": True  # New category was created
                }) as mock_ensure_category:
                    
                    mock_agent = Mock()
                    mock_agent.categorize_transaction = AsyncMock(return_value=sample_ai_result)
                    mock_get_agent.return_value = mock_agent
                    
                    result = await mis_service.categorize_transaction(
                        tenant_id=tenant_id,
                        transaction_id=sample_transaction["id"],
                        description=sample_transaction["description"],
                        amount=sample_transaction["amount"]
                    )
                    
                    assert result.created_new is True
                    assert result.category_id == "cat-new-123"
                    
                    # Verify category creation was called with proper hierarchy
                    mock_ensure_category.assert_called_once_with(
                        tenant_id=tenant_id,
                        category_name=sample_ai_result.category_name,
                        parent_category=sample_ai_result.parent_category,
                        gl_code=sample_ai_result.gl_code,
                        confidence=sample_ai_result.confidence
                    )


class TestBatchCategorization:
    """Test batch categorization functionality for efficient processing."""

    @pytest.mark.asyncio
    async def test_categorize_batch_success(self, mis_service):
        """Test successful batch categorization with proper MIS structure."""
        tenant_id = 1
        transactions = [
            {"id": "txn-1", "description": "Office Supplies", "amount": 100.00},
            {"id": "txn-2", "description": "Software License", "amount": 500.00},
            {"id": "txn-3", "description": "Client Payment", "amount": 2000.00}
        ]
        
        # Mock batch processing
        expected_results = [
            MISCategorizationResult(
                transaction_id="txn-1",
                category_name="Office Supplies",
                parent_category="Operating Expenses",
                full_path="Expenses > Operating Expenses > Office Supplies",
                gl_code="5001",
                confidence=0.95,
                reasoning="Office supplies purchase",
                vendor_info=None,
                category_id="cat-1",
                created_new=False
            ),
            MISCategorizationResult(
                transaction_id="txn-2",
                category_name="Software",
                parent_category="Technology Expenses",
                full_path="Expenses > Technology Expenses > Software",
                gl_code="5100",
                confidence=0.92,
                reasoning="Software license",
                vendor_info=None,
                category_id="cat-2",
                created_new=False
            ),
            MISCategorizationResult(
                transaction_id="txn-3",
                category_name="Service Revenue",
                parent_category="Revenue",
                full_path="Income > Revenue > Service Revenue",
                gl_code="4100",
                confidence=0.98,
                reasoning="Client payment received",
                vendor_info=None,
                category_id="cat-3",
                created_new=False
            )
        ]
        
        with patch.object(mis_service, '_get_or_create_agent') as mock_get_agent:
            with patch.object(mis_service, '_categorize_single_transaction_optimized') as mock_categorize:
                mock_categorize.side_effect = expected_results
                
                results = await mis_service.categorize_batch(
                    tenant_id=tenant_id,
                    transactions=transactions,
                    batch_size=3
                )
                
                assert len(results) == 3
                
                # Verify Income/Expense classification
                expense_transactions = [r for r in results if r.full_path.startswith("Expenses")]
                income_transactions = [r for r in results if r.full_path.startswith("Income")]
                
                assert len(expense_transactions) == 2  # Office supplies and software
                assert len(income_transactions) == 1   # Client payment
                
                # Verify proper GL codes are assigned
                assert all(r.gl_code is not None for r in results)
                
                # Verify proper hierarchy structure
                for result in results:
                    assert " > " in result.full_path  # Hierarchical structure
                    assert result.confidence > 0.8   # High confidence

    @pytest.mark.asyncio
    async def test_categorize_batch_with_vendor_caching(self, mis_service):
        """Test batch categorization with vendor caching optimization."""
        tenant_id = 1
        transactions = [
            {"id": "txn-1", "description": "Office Depot Purchase 1", "amount": 100.00, "vendor": "Office Depot"},
            {"id": "txn-2", "description": "Office Depot Purchase 2", "amount": 150.00, "vendor": "Office Depot"},
            {"id": "txn-3", "description": "Starbucks Coffee", "amount": 15.00, "vendor": "Starbucks"}
        ]
        
        with patch.object(mis_service, '_get_or_create_agent') as mock_get_agent:
            with patch.object(mis_service, '_categorize_single_transaction_optimized') as mock_categorize:
                # Mock return values
                mock_categorize.side_effect = [
                    MISCategorizationResult(
                        transaction_id="txn-1", category_name="Office Supplies",
                        parent_category="Operating Expenses", full_path="Expenses > Operating Expenses > Office Supplies",
                        gl_code="5001", confidence=0.95, reasoning="Office supplies", vendor_info=None,
                        category_id="cat-1", created_new=False
                    ),
                    MISCategorizationResult(
                        transaction_id="txn-2", category_name="Office Supplies",
                        parent_category="Operating Expenses", full_path="Expenses > Operating Expenses > Office Supplies",
                        gl_code="5001", confidence=0.95, reasoning="Office supplies", vendor_info=None,
                        category_id="cat-1", created_new=False
                    ),
                    MISCategorizationResult(
                        transaction_id="txn-3", category_name="Meals & Entertainment",
                        parent_category="Operating Expenses", full_path="Expenses > Operating Expenses > Meals & Entertainment",
                        gl_code="5200", confidence=0.90, reasoning="Coffee purchase", vendor_info=None,
                        category_id="cat-2", created_new=False
                    )
                ]
                
                results = await mis_service.categorize_batch(
                    tenant_id=tenant_id,
                    transactions=transactions,
                    vendor_search_limit=100
                )
                
                assert len(results) == 3
                
                # Verify same vendor gets same category (vendor caching effect)
                office_depot_results = [r for r in results if "Office Depot" in str(r.reasoning)]
                if len(office_depot_results) >= 2:
                    assert office_depot_results[0].category_name == office_depot_results[1].category_name

    @pytest.mark.asyncio
    async def test_categorize_batch_empty_list(self, mis_service):
        """Test batch categorization with empty transaction list."""
        tenant_id = 1
        transactions = []
        
        results = await mis_service.categorize_batch(tenant_id=tenant_id, transactions=transactions)
        
        assert results == []

    @pytest.mark.asyncio
    async def test_categorize_batch_custom_batch_size(self, mis_service):
        """Test batch categorization with custom batch size."""
        tenant_id = 1
        transactions = [{"id": f"txn-{i}", "description": f"Transaction {i}", "amount": 100.0} for i in range(10)]
        
        with patch.object(mis_service, '_get_or_create_agent'):
            with patch.object(mis_service, '_categorize_single_transaction_optimized') as mock_categorize:
                # Mock return values
                mock_categorize.side_effect = [
                    MISCategorizationResult(
                        transaction_id=f"txn-{i}", category_name="Test Category",
                        parent_category="Operating Expenses", full_path="Expenses > Operating Expenses > Test Category",
                        gl_code="5001", confidence=0.95, reasoning="Test", vendor_info=None,
                        category_id="cat-1", created_new=False
                    ) for i in range(10)
                ]
                
                results = await mis_service.categorize_batch(
                    tenant_id=tenant_id,
                    transactions=transactions,
                    batch_size=3  # Process in batches of 3
                )
                
                assert len(results) == 10
                assert all(r.category_name == "Test Category" for r in results)


class TestIndustryTemplateApplication:
    """Test industry template application - core MIS setup functionality."""

    @pytest.mark.asyncio
    async def test_apply_industry_template_technology(self, mis_service):
        """Test applying technology industry template."""
        tenant_id = 1
        industry = "Technology"
        
        # Mock template creation
        with patch('giki_ai_api.domains.categories.mis_templates.MISTemplates.get_industry_template') as mock_get_template:
            with patch('giki_ai_api.domains.categories.mis_templates.MISTemplates.create_categories_from_template') as mock_create_from_template:
                with patch.object(mis_service, '_create_category') as mock_create_category:
                    
                    # Mock template data
                    mock_get_template.return_value = {
                        "industry": "Technology",
                        "income": {"name": "Income", "gl_code": "4000"},
                        "expenses": {"name": "Expenses", "gl_code": "5000"}
                    }
                    
                    # Mock category creation data
                    mock_create_from_template.return_value = [
                        {"name": "Income", "level": 0, "gl_code": "4000", "parent_name": None},
                        {"name": "Expenses", "level": 0, "gl_code": "5000", "parent_name": None},
                        {"name": "Software Revenue", "level": 1, "gl_code": "4100", "parent_name": "Income"},
                        {"name": "Operating Expenses", "level": 1, "gl_code": "5100", "parent_name": "Expenses"}
                    ]
                    
                    # Mock created categories
                    mock_create_category.side_effect = [
                        {"id": "cat-income", "name": "Income", "level": 0},
                        {"id": "cat-expenses", "name": "Expenses", "level": 0},
                        {"id": "cat-software", "name": "Software Revenue", "level": 1},
                        {"id": "cat-operating", "name": "Operating Expenses", "level": 1}
                    ]
                    
                    result = await mis_service.apply_industry_template(tenant_id, industry)
                    
                    assert result["success"] is True
                    assert result["industry"] == "Technology"
                    assert result["categories_created"] == 4
                    assert result["template_applied"] == "Technology"
                    
                    # Verify root categories exist
                    root_categories = result["root_categories"]
                    assert len(root_categories) == 2
                    root_names = [cat["name"] for cat in root_categories]
                    assert "Income" in root_names
                    assert "Expenses" in root_names
                    
                    # Verify proper method calls
                    mock_get_template.assert_called_once_with(industry)
                    mock_create_from_template.assert_called_once()
                    assert mock_create_category.call_count == 4

    @pytest.mark.asyncio
    async def test_apply_industry_template_failure(self, mis_service):
        """Test industry template application failure handling."""
        tenant_id = 1
        industry = "Technology"
        
        with patch('giki_ai_api.domains.categories.mis_templates.MISTemplates.get_industry_template') as mock_get_template:
            mock_get_template.side_effect = Exception("Template service unavailable")
            
            with pytest.raises(ServiceError) as exc_info:
                await mis_service.apply_industry_template(tenant_id, industry)
            
            assert "Template application failed" in str(exc_info.value)
            assert exc_info.value.service_name == "MISCategorizationService"
            assert exc_info.value.operation == "apply_industry_template"

    @pytest.mark.asyncio
    async def test_apply_industry_template_hierarchy_creation(self, mis_service):
        """Test proper hierarchy creation order (level 0, then 1, then 2)."""
        tenant_id = 1
        industry = "Retail"
        
        with patch('giki_ai_api.domains.categories.mis_templates.MISTemplates.get_industry_template') as mock_get_template:
            with patch('giki_ai_api.domains.categories.mis_templates.MISTemplates.create_categories_from_template') as mock_create_from_template:
                with patch.object(mis_service, '_create_category') as mock_create_category:
                    
                    mock_get_template.return_value = {"industry": "Retail"}
                    
                    # Mock 3-level hierarchy
                    mock_create_from_template.return_value = [
                        {"name": "Income", "level": 0, "parent_name": None},
                        {"name": "Product Sales", "level": 1, "parent_name": "Income"},
                        {"name": "Electronics Sales", "level": 2, "parent_name": "Product Sales"},
                        {"name": "Expenses", "level": 0, "parent_name": None},
                        {"name": "Cost of Goods", "level": 1, "parent_name": "Expenses"},
                        {"name": "Inventory Costs", "level": 2, "parent_name": "Cost of Goods"}
                    ]
                    
                    mock_create_category.return_value = {"id": "test-id", "level": 0}
                    
                    result = await mis_service.apply_industry_template(tenant_id, industry)
                    
                    assert result["success"] is True
                    assert result["categories_created"] == 6
                    
                    # Verify creation order: level 0 first, then 1, then 2
                    call_args_list = mock_create_category.call_args_list
                    assert len(call_args_list) == 6
                    
                    # Level 0 categories should be created first
                    level_0_calls = [call for call in call_args_list[:2]]
                    for call in level_0_calls:
                        cat_data = call[0][1]  # Second argument (cat_data)
                        assert cat_data["level"] == 0


class TestMISStructureValidation:
    """Test MIS structure validation functionality."""

    @pytest.mark.asyncio
    async def test_validate_mis_structure_success(self, mis_service, mock_db_connection):
        """Test successful MIS structure validation."""
        tenant_id = 1
        
        # Mock database queries for validation
        mock_db_connection.fetch.side_effect = [
            # Root categories query
            [
                {"name": "Income", "id": 1, "gl_code": "4000"},
                {"name": "Expenses", "id": 2, "gl_code": "5000"}
            ],
            # Orphaned categories query
            [],
            # Categories without GL codes query
            []
        ]
        
        mock_db_connection.fetchval.return_value = 0  # No issues found
        
        result = await mis_service.validate_mis_structure(tenant_id)
        
        assert result["valid"] is True
        assert len(result["issues"]) == 0
        assert result["root_categories_found"] == 2
        assert "Income" in [cat["name"] for cat in result["root_categories"]]
        assert "Expenses" in [cat["name"] for cat in result["root_categories"]]

    @pytest.mark.asyncio
    async def test_validate_mis_structure_missing_roots(self, mis_service, mock_db_connection):
        """Test MIS validation with missing root categories."""
        tenant_id = 1
        
        # Mock missing root categories
        mock_db_connection.fetch.side_effect = [
            # Only Income root, missing Expenses
            [{"name": "Income", "id": 1, "gl_code": "4000"}],
            [],  # No orphaned
            []   # No missing GL codes
        ]
        mock_db_connection.fetchval.return_value = 0  # No categories without GL codes
        mock_db_connection.fetchrow.return_value = {
            "total_categories": 1,
            "parent_count": 0,
            "max_depth": 0
        }
        
        result = await mis_service.validate_mis_structure(tenant_id)
        
        assert result["valid"] is False
        assert len(result["issues"]) > 0
        assert any("Missing 'Expenses' root category" in issue for issue in result["issues"])

    @pytest.mark.asyncio
    async def test_validate_mis_structure_orphaned_categories(self, mis_service, mock_db_connection):
        """Test MIS validation with orphaned categories."""
        tenant_id = 1
        
        mock_db_connection.fetch.side_effect = [
            # Root categories OK
            [
                {"name": "Income", "id": 1, "gl_code": "4000"},
                {"name": "Expenses", "id": 2, "gl_code": "5000"}
            ],
            # Orphaned categories found
            [
                {"name": "Orphaned Category", "id": 99, "parent_id": 999}
            ],
            # No missing GL codes
            []
        ]
        mock_db_connection.fetchval.return_value = 0  # No categories without GL codes
        mock_db_connection.fetchrow.return_value = {
            "total_categories": 3,
            "parent_count": 2,
            "max_depth": 1
        }
        
        result = await mis_service.validate_mis_structure(tenant_id)
        
        assert result["valid"] is False
        assert any("orphaned" in issue.lower() for issue in result["issues"])
        assert result["orphaned_categories"] == 1

    @pytest.mark.asyncio
    async def test_validate_mis_structure_missing_gl_codes(self, mis_service, mock_db_connection):
        """Test MIS validation with missing GL codes."""
        tenant_id = 1
        
        mock_db_connection.fetch.side_effect = [
            # Root categories OK
            [
                {"name": "Income", "id": 1, "gl_code": "4000"},
                {"name": "Expenses", "id": 2, "gl_code": "5000"}
            ],
            # No orphaned categories
            [],
            # Categories without GL codes
            [
                {"name": "No GL Code Category", "id": 10}
            ]
        ]
        mock_db_connection.fetchval.return_value = 1  # 1 category without GL codes
        mock_db_connection.fetchrow.return_value = {
            "total_categories": 3,
            "parent_count": 2,
            "max_depth": 1
        }
        
        result = await mis_service.validate_mis_structure(tenant_id)
        
        assert result["valid"] is False
        assert any("GL code" in issue for issue in result["issues"])
        assert result["categories_without_gl_codes"] == 1


class TestBusinessContextManagement:
    """Test business context management functionality."""

    @pytest.mark.asyncio
    async def test_get_business_context_success(self, mis_service, mock_db_connection):
        """Test successful business context retrieval."""
        tenant_id = 1
        
        # Mock business context data
        mock_db_connection.fetchrow.return_value = {
            "name": "Test Company",
            "id": tenant_id,
            "business_context": {
                "industry": "Technology",
                "company_size": "Medium",
                "company_website": "https://example.com",
                "fiscal_year_start": "January",
                "reporting_frequency": "Monthly"
            }
        }
        
        context = await mis_service._get_business_context(tenant_id)
        
        assert context["industry"] == "Technology"
        assert context["company_size"] == "Medium"
        assert context["tenant_id"] == tenant_id

    @pytest.mark.asyncio
    async def test_get_business_context_not_found(self, mis_service, mock_db_connection):
        """Test business context when not found (new tenant)."""
        tenant_id = 1
        
        mock_db_connection.fetchrow.return_value = None
        
        context = await mis_service._get_business_context(tenant_id)
        
        # Should return default context
        assert context["industry"] == "General Business"
        assert context["company_size"] == "Small"
        assert context["tenant_id"] == tenant_id


class TestAgentCaching:
    """Test MIS agent caching functionality for performance."""

    @pytest.mark.asyncio
    async def test_agent_caching_creates_new_agent(self, mis_service):
        """Test agent is created and cached for new tenant."""
        tenant_id = 1
        
        with patch('giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationAgent') as mock_agent_class:
            with patch.object(mis_service, '_get_business_context') as mock_get_context:
                mock_get_context.return_value = {"industry": "Technology"}
                mock_agent_instance = Mock()
                mock_agent_class.return_value = mock_agent_instance
                
                agent = await mis_service._get_or_create_agent(tenant_id)
                
                assert agent == mock_agent_instance
                assert tenant_id in mis_service._agent_cache
                assert mis_service._agent_cache[tenant_id] == mock_agent_instance
                
                # Verify agent was created with business context
                mock_agent_class.assert_called_once_with(tenant_id=tenant_id)

    @pytest.mark.asyncio
    async def test_agent_caching_returns_cached_agent(self, mis_service):
        """Test cached agent is returned for existing tenant."""
        tenant_id = 1
        cached_agent = Mock()
        
        # Pre-populate cache
        mis_service._agent_cache[tenant_id] = cached_agent
        
        with patch('giki_ai_api.domains.categories.mis_categorization_service.MISCategorizationAgent') as mock_agent_class:
            agent = await mis_service._get_or_create_agent(tenant_id)
            
            assert agent == cached_agent
            # Should not create new agent
            mock_agent_class.assert_not_called()


class TestCategoryManagement:
    """Test category creation and management within MIS structure."""

    @pytest.mark.asyncio
    async def test_ensure_category_exists_new_category(self, mis_service, mock_db_connection):
        """Test creating new category with proper MIS hierarchy."""
        tenant_id = 1
        category_name = "New Software Category"
        parent_category = "Technology Expenses"
        gl_code = "5150"
        confidence = 0.95
        
        # Mock database calls based on the SQL query
        call_count = 0
        def mock_fetchrow_side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            sql = args[0]
            
            if call_count == 1:
                # First call: Category existence check - return None (doesn't exist)
                return None
            elif call_count == 2:
                # Second call: Parent existence check - return None (doesn't exist)
                return None
            elif call_count == 3:
                # Third call: Parent creation - return created parent data
                return {
                    "id": "parent-123",
                    "name": parent_category,
                    "level": 0,
                    "path": parent_category,
                    "gl_code": "5000",
                    "tenant_id": tenant_id
                }
            elif call_count == 4:
                # Fourth call: Category creation - return created category data
                return {
                    "id": "new-cat-123",
                    "name": category_name,
                    "level": 1,
                    "path": f"{parent_category} > {category_name}",
                    "gl_code": gl_code,
                    "tenant_id": tenant_id
                }
            # Any other calls: return None (for safety)
            return None
        
        mock_db_connection.fetchrow.side_effect = mock_fetchrow_side_effect
        
        result = await mis_service._ensure_category_exists(
            tenant_id=tenant_id,
            category_name=category_name,
            parent_category=parent_category,
            gl_code=gl_code,
            confidence=confidence
        )
        
        assert result["created_new"] is True
        assert result["category_id"] == "new-cat-123"
        assert result["gl_code"] == gl_code

    @pytest.mark.asyncio
    async def test_ensure_category_exists_existing_category(self, mis_service, mock_db_connection):
        """Test using existing category."""
        tenant_id = 1
        category_name = "Office Supplies"
        
        # Mock category exists
        mock_db_connection.fetchrow.return_value = {
            "id": "existing-cat-123",
            "name": category_name,
            "path": "Expenses > Operating Expenses > Office Supplies",
            "gl_code": "5001"
        }
        
        result = await mis_service._ensure_category_exists(
            tenant_id=tenant_id,
            category_name=category_name,
            parent_category="Operating Expenses",
            gl_code="5001",
            confidence=0.95
        )
        
        assert result["created_new"] is False
        assert result["category_id"] == "existing-cat-123"
        assert result["full_path"] == "Expenses > Operating Expenses > Office Supplies"


class TestPerformanceOptimizations:
    """Test performance optimizations in MIS categorization."""

    @pytest.mark.asyncio
    async def test_vendor_caching_reduces_api_calls(self, mis_service):
        """Test vendor caching reduces Google Search API calls."""
        tenant_id = 1
        transactions = [
            {"description": "Office Depot Purchase 1", "vendor": "Office Depot", "amount": 100},
            {"description": "Office Depot Purchase 2", "vendor": "Office Depot", "amount": 150},
            {"description": "Different Vendor", "vendor": "Different Corp", "amount": 200}
        ]
        
        vendor_cache = {}
        
        # This would be tested in the actual batch processing
        # to ensure the same vendor info is reused
        with patch.object(mis_service, '_get_or_create_agent'):
            # Simulate vendor caching behavior
            for txn in transactions:
                vendor = txn.get("vendor")
                if vendor not in vendor_cache:
                    vendor_cache[vendor] = {"name": vendor, "category": "test"}
            
            # Should only have 2 unique vendors cached
            assert len(vendor_cache) == 2
            assert "Office Depot" in vendor_cache
            assert "Different Corp" in vendor_cache

    @pytest.mark.asyncio 
    async def test_business_context_caching(self, mis_service):
        """Test business context is cached across multiple calls."""
        tenant_id = 1
        
        with patch.object(mis_service, '_get_business_context') as mock_get_context:
            mock_get_context.return_value = {"industry": "Technology"}
            
            # Multiple calls should use cached context
            await mis_service._get_or_create_agent(tenant_id)
            await mis_service._get_or_create_agent(tenant_id)
            
            # Business context should only be fetched once due to agent caching
            assert mock_get_context.call_count <= 1


if __name__ == "__main__":
    """Run MIS categorization tests directly."""
    pytest.main([__file__, "-v", "-s", "--tb=short"])