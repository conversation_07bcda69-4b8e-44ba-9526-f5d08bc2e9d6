"""
Unit tests for duplicate detection service.

Tests cover duplicate detection logic, fuzzy matching,
and audit trail functionality without external dependencies.
"""

import asyncio
from datetime import date, datetime, timedelta
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from asyncpg import Connection

from giki_ai_api.domains.transactions.duplicate_detection_service import (
    DuplicateDetectionService,
    DeduplicationResult,
    DuplicateMatch,
)
from giki_ai_api.domains.transactions.models import Transaction


class TestDuplicateDetectionService:
    """Test duplicate detection service functionality."""

    @pytest.fixture
    def duplicate_service(self, mock_db_connection):
        """Create duplicate detection service instance."""
        return DuplicateDetectionService(mock_db_connection)

    @pytest.fixture
    def sample_transaction(self):
        """Sample transaction for testing."""
        return Transaction(
            id="test-123",
            date=date.today(),
            description="Starbucks Coffee Shop",
            amount=5.50,
            account="Business Checking",
            tenant_id=1,
            upload_id="upload-123",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

    @pytest.fixture
    def sample_transactions_batch(self):
        """Sample batch of transactions for testing."""
        base_date = date.today()
        return [
            Transaction(
                id=f"tx-{i}",
                date=base_date + timedelta(days=i),
                description=f"Transaction {i}",
                amount=100.0 * (i + 1),
                account="Business Checking",
                tenant_id=1,
                upload_id="batch-upload",
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            for i in range(5)
        ]


class TestDuplicateDetection(TestDuplicateDetectionService):
    """Test duplicate detection functionality."""

    @pytest.mark.asyncio
    async def test_detect_duplicates_batch_no_duplicates(
        self, duplicate_service, mock_db_connection, sample_transactions_batch
    ):
        """Test batch detection with no duplicates."""
        # Mock no existing transactions
        mock_db_connection.fetch.return_value = []
        
        results = await duplicate_service.detect_duplicates_batch(
            sample_transactions_batch, check_existing=True
        )
        
        assert len(results) == len(sample_transactions_batch)
        for result in results:
            assert result.is_duplicate is False
            assert result.duplicate_type == DuplicateType.NONE
            assert result.matching_transaction_ids == []

    @pytest.mark.asyncio
    async def test_detect_duplicates_batch_with_exact_match(
        self, duplicate_service, mock_db_connection, sample_transactions_batch
    ):
        """Test batch detection with exact duplicate."""
        # Create an exact duplicate in the batch
        duplicate = sample_transactions_batch[0]
        sample_transactions_batch.append(
            Transaction(
                id="duplicate-tx",
                date=duplicate.date,
                description=duplicate.description,
                amount=duplicate.amount,
                account=duplicate.account,
                tenant_id=duplicate.tenant_id,
                upload_id=duplicate.upload_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
        )
        
        # Mock no existing transactions in database
        mock_db_connection.fetch.return_value = []
        
        results = await duplicate_service.detect_duplicates_batch(
            sample_transactions_batch, check_existing=False
        )
        
        # Find the duplicate results
        duplicate_results = [r for r in results if r.is_duplicate]
        assert len(duplicate_results) == 2  # Both transactions should be marked as duplicates
        
        for result in duplicate_results:
            assert result.duplicate_type == DuplicateType.EXACT
            assert len(result.matching_transaction_ids) == 1

    @pytest.mark.asyncio
    async def test_find_exact_matches(
        self, duplicate_service, mock_db_connection, sample_transaction
    ):
        """Test exact match detection against existing transactions."""
        # Mock existing transaction that matches exactly
        existing = {
            "id": "existing-123",
            "date": sample_transaction.date,
            "description": sample_transaction.description,
            "amount": sample_transaction.amount,
            "account": sample_transaction.account,
        }
        
        mock_db_connection.fetch.return_value = [existing]
        
        result = await duplicate_service._find_exact_matches(
            sample_transaction, tenant_id=1
        )
        
        assert result.is_duplicate is True
        assert result.duplicate_type == DuplicateType.EXACT
        assert "existing-123" in result.matching_transaction_ids
        assert result.confidence_score == 1.0

    @pytest.mark.asyncio
    async def test_find_date_amount_matches(
        self, duplicate_service, mock_db_connection, sample_transaction
    ):
        """Test date/amount based matching."""
        # Mock existing transaction with same date and amount but different description
        existing = {
            "id": "similar-123",
            "date": sample_transaction.date,
            "description": "Different Coffee Shop",
            "amount": sample_transaction.amount,
            "account": sample_transaction.account,
            "ai_confidence": 0.85,
        }
        
        mock_db_connection.fetch.return_value = [existing]
        
        result = await duplicate_service._find_date_amount_matches(
            sample_transaction, tenant_id=1
        )
        
        assert result.is_duplicate is True
        assert result.duplicate_type == DuplicateType.DATE_AMOUNT
        assert "similar-123" in result.matching_transaction_ids
        assert 0.7 <= result.confidence_score <= 0.9

    @pytest.mark.asyncio
    async def test_find_fuzzy_description_matches(
        self, duplicate_service, mock_db_connection, sample_transaction
    ):
        """Test fuzzy description matching."""
        # Mock existing transactions with similar descriptions
        existing = [
            {
                "id": "fuzzy-1",
                "date": sample_transaction.date - timedelta(days=1),
                "description": "Starbucks Coffee",  # Similar but not exact
                "amount": sample_transaction.amount,
            },
            {
                "id": "fuzzy-2",
                "date": sample_transaction.date - timedelta(days=2),
                "description": "STARBUCKS COFFEE SHOP #123",  # More similar
                "amount": sample_transaction.amount + 0.50,  # Slightly different amount
            },
        ]
        
        mock_db_connection.fetch.return_value = existing
        
        result = await duplicate_service._find_fuzzy_description_matches(
            sample_transaction, tenant_id=1, days_window=7
        )
        
        assert result.is_duplicate is True
        assert result.duplicate_type == DuplicateType.FUZZY
        assert len(result.matching_transaction_ids) > 0
        assert result.confidence_score > 0.5


class TestDuplicateMetrics(TestDuplicateDetectionService):
    """Test duplicate detection metrics and statistics."""

    @pytest.mark.asyncio
    async def test_get_duplicate_statistics(
        self, duplicate_service, mock_db_connection
    ):
        """Test getting duplicate statistics."""
        # Mock statistics query results
        mock_stats = {
            "total_transactions": 1000,
            "duplicate_count": 150,
            "exact_duplicates": 50,
            "date_amount_duplicates": 60,
            "fuzzy_duplicates": 40,
        }
        
        mock_db_connection.fetchrow.return_value = mock_stats
        
        result = await duplicate_service.get_duplicate_statistics(
            tenant_id=1, start_date=date.today() - timedelta(days=30)
        )
        
        assert result["total_transactions"] == 1000
        assert result["duplicate_count"] == 150
        assert result["duplicate_rate"] == 0.15
        assert result["exact_duplicates"] == 50
        assert result["date_amount_duplicates"] == 60
        assert result["fuzzy_duplicates"] == 40

    @pytest.mark.asyncio
    async def test_log_duplicate_decision(
        self, duplicate_service, mock_db_connection
    ):
        """Test logging duplicate detection decisions."""
        # Test logging a keep decision
        mock_db_connection.execute.return_value = None
        
        await duplicate_service.log_duplicate_decision(
            transaction_id="tx-123",
            duplicate_ids=["dup-1", "dup-2"],
            decision="keep",
            confidence=0.95,
            user_id=1,
        )
        
        # Verify SQL was executed
        mock_db_connection.execute.assert_called_once()
        call_args = mock_db_connection.execute.call_args[0]
        assert "INSERT INTO duplicate_detection_log" in call_args[0]


class TestDuplicateDetectionEdgeCases(TestDuplicateDetectionService):
    """Test edge cases in duplicate detection."""

    @pytest.mark.asyncio
    async def test_detect_duplicates_empty_batch(
        self, duplicate_service, mock_db_connection
    ):
        """Test detection with empty batch."""
        results = await duplicate_service.detect_duplicates_batch([])
        assert results == []

    @pytest.mark.asyncio
    async def test_detect_duplicates_single_transaction(
        self, duplicate_service, mock_db_connection, sample_transaction
    ):
        """Test detection with single transaction."""
        mock_db_connection.fetch.return_value = []
        
        results = await duplicate_service.detect_duplicates_batch(
            [sample_transaction], check_existing=True
        )
        
        assert len(results) == 1
        assert results[0].is_duplicate is False

    @pytest.mark.asyncio
    async def test_fuzzy_matching_with_special_characters(
        self, duplicate_service, mock_db_connection
    ):
        """Test fuzzy matching with special characters in description."""
        transaction = Transaction(
            id="special-tx",
            date=date.today(),
            description="PAYMENT #12345 - INVOICE: ABC-789 (PROCESSED)",
            amount=500.00,
            account="Business Checking",
            tenant_id=1,
            upload_id="upload-123",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        
        # Mock similar transaction with different special characters
        existing = [{
            "id": "existing-special",
            "date": transaction.date,
            "description": "PAYMENT 12345 INVOICE ABC789 PROCESSED",
            "amount": 500.00,
        }]
        
        mock_db_connection.fetch.return_value = existing
        
        result = await duplicate_service._find_fuzzy_description_matches(
            transaction, tenant_id=1, days_window=1
        )
        
        # Should still find match despite special character differences
        assert result.is_duplicate is True
        assert "existing-special" in result.matching_transaction_ids

    @pytest.mark.asyncio
    async def test_amount_tolerance_matching(
        self, duplicate_service, mock_db_connection, sample_transaction
    ):
        """Test matching with small amount differences."""
        # Test transactions with amounts within tolerance
        existing = [
            {
                "id": "close-amount-1",
                "date": sample_transaction.date,
                "description": sample_transaction.description,
                "amount": sample_transaction.amount + 0.01,  # 1 cent difference
            },
            {
                "id": "close-amount-2",
                "date": sample_transaction.date,
                "description": sample_transaction.description,
                "amount": sample_transaction.amount + 0.10,  # 10 cent difference
            },
        ]
        
        mock_db_connection.fetch.return_value = existing
        
        # Should match within tolerance
        result = await duplicate_service._find_exact_matches(
            sample_transaction, tenant_id=1
        )
        
        # Depending on implementation, may or may not consider these exact matches
        # This tests the behavior of amount tolerance
        if result.is_duplicate:
            assert len(result.matching_transaction_ids) >= 1