"""
Unit test configuration and fixtures for giki.ai API.

Provides isolated test fixtures for unit testing without external dependencies.
"""

import asyncio
from datetime import datetime, timezone
from unittest.mock import AsyncMock, Mock

import asyncpg
import pytest
from passlib.context import CryptContext

from giki_ai_api.domains.auth.models import UserDB


# Removed custom event_loop fixture to avoid deprecation warning and conflicts with pytest-asyncio


@pytest.fixture
def mock_db_connection():
    """Mock database connection for unit tests."""
    conn = AsyncMock(spec=asyncpg.Connection)
    return conn


@pytest.fixture
def password_context():
    """Password hashing context for testing."""
    return CryptContext(schemes=["bcrypt"], deprecated="auto", bcrypt__rounds=4)  # Lower rounds for faster tests


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "id": 1,
        "email": "<EMAIL>",
        "username": "<EMAIL>",
        "hashed_password": "$2b$12$abcdefghijklmnopqrstuvwxyz1234567890",  # Mock hash
        "is_active": True,
        "is_verified": False,
        "is_superuser": False,
        "tenant_id": 1,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
        "last_login": None,
        "login_count": 0,
    }


@pytest.fixture
def sample_user(sample_user_data):
    """Sample UserDB instance for testing."""
    return UserDB(**sample_user_data)


@pytest.fixture
def sample_admin_data(sample_user_data):
    """Sample admin user data for testing."""
    admin_data = sample_user_data.copy()
    admin_data.update({
        "id": 2,
        "email": "<EMAIL>",
        "username": "<EMAIL>",
        "is_superuser": True,
    })
    return admin_data


@pytest.fixture
def sample_admin(sample_admin_data):
    """Sample admin UserDB instance for testing."""
    return UserDB(**sample_admin_data)


@pytest.fixture
def mock_jwt_settings():
    """Mock JWT settings for testing."""
    return {
        "SECRET_KEY": "test-secret-key",
        "ALGORITHM": "RS256",
        "ACCESS_TOKEN_EXPIRE_MINUTES": 30,
        "REFRESH_TOKEN_EXPIRE_DAYS": 7,
    }


@pytest.fixture
def mock_rsa_keys():
    """Mock RSA key pair for JWT testing."""
    # These are test keys generated for unit testing only
    private_key = """******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    
    public_key = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzpsaxpMHk8fkNmOpXXN3
wY4z9HgzB8DKpnL0/a+SWE0Fu6TVCMSAd6O2+U2Zv3/kd5vjZjkXDrKvlH+SwVG7
wmWeWaZm2uvIra+PMYemAGitag0k/vvv03i3N/9qxLqLRXpxibU9WY+KaZ5TiqTT
0gsgNx9I4eRqXXIOZVqHBrmyDWYkMmTJNtE6Ze3b0s2Djx8QHQjEkLd3UnTYqmWh
vxsodFG86j56FReOcer13gSyiKclyTRLNBaudHrbGQrSf69Rwe+x3C0+yNugGVRv
m0uX23fKjy51Ere2YoK/y+7VScsSKVmidgpgPIer8BQKnd4495ZU+O0FNR/yCISF
bwIDAQAB
-----END PUBLIC KEY-----"""
    
    return {"private": private_key, "public": public_key}