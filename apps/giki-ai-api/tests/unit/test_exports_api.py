"""
Comprehensive unit tests for Exports API - critical for customer value.

Tests the complete export workflow for categorized transactions to various 
accounting software formats (QuickBooks, Xero, CSV, etc.).
"""

import json
from datetime import date, datetime, timezone
from io import BytesIO
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from giki_ai_api.core.main import app
from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session
from giki_ai_api.domains.auth.models import User
from giki_ai_api.domains.auth.secure_auth import get_current_active_user


@pytest.fixture
def mock_db_connection():
    """Mock database connection."""
    return AsyncMock()


@pytest.fixture
def mock_current_user():
    """Mock authenticated user."""
    user = Mock(spec=User)
    user.id = 1
    user.email = "<EMAIL>"
    user.is_active = True
    return user


@pytest.fixture
def client(mock_current_user, mock_db_connection):
    """Create test client with overridden dependencies."""
    app.dependency_overrides[get_current_active_user] = lambda: mock_current_user
    app.dependency_overrides[get_current_tenant_id] = lambda: 1
    app.dependency_overrides[get_db_session] = lambda: mock_db_connection
    
    test_client = TestClient(app)
    yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def sample_transactions():
    """Sample transaction data for export testing."""
    return [
        {
            "id": "123e4567-e89b-12d3-a456-************",
            "date": date(2024, 1, 15),
            "description": "AMAZON WEB SERVICES",
            "amount": -250.50,
            "category_id": 15,
            "category_name": "Technology/Cloud Services",
            "account": "Business Credit Card",
            "ai_category": "Technology/Cloud Services",
            "ai_confidence": 0.95,
            "gl_code": "6200",
            "created_at": datetime.now(timezone.utc),
        },
        {
            "id": "223e4567-e89b-12d3-a456-************",
            "date": date(2024, 1, 20),
            "description": "OFFICE SUPPLIES INC",
            "amount": -89.99,
            "category_id": 8,
            "category_name": "Office Supplies",
            "account": "Business Checking",
            "ai_category": "Office Supplies",
            "ai_confidence": 0.88,
            "gl_code": "6300",
            "created_at": datetime.now(timezone.utc),
        }
    ]


class TestExportFormats:
    """Test export format management."""

    def test_get_available_formats(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving available export formats."""
        response = client.get("/api/v1/exports/formats", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Check format structure
        format_info = data[0]
        assert "id" in format_info
        assert "name" in format_info
        assert "description" in format_info
        assert "file_extension" in format_info
        assert "supports_multi_currency" in format_info
        assert "requires_account_codes" in format_info

    def test_get_format_details(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving specific format details."""
        format_id = "quickbooks_iif"
        
        response = client.get(f"/api/v1/exports/formats/{format_id}", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == format_id
        assert "required_fields" in data
        assert "supports_multi_currency" in data


class TestTransactionExport:
    """Test transaction export functionality."""

    def test_export_transactions_csv(
        self, client: TestClient, mock_db_connection: AsyncMock, 
        auth_headers: dict, sample_transactions
    ):
        """Test exporting transactions to CSV format."""
        # Mock export service
        with patch("giki_ai_api.domains.exports.router.export_service") as mock_service:
            mock_service.export_transactions.return_value = {
                "content": "Date,Description,Amount,Category,Account\n2024-01-15,AMAZON WEB SERVICES,-250.50,Technology/Cloud Services,Business Credit Card",
                "filename": "transactions_export_2024-01-15.csv",
                "content_type": "text/csv"
            }
            
            export_request = {
                "format_id": "csv",
                "date_from": "2024-01-01",
                "date_to": "2024-01-31",
                "include_uncategorized": True
            }
            
            response = client.post(
                "/api/v1/exports/transactions", 
                json=export_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            assert response.headers["content-type"].startswith("text/csv")
            assert "attachment" in response.headers.get("content-disposition", "")

    def test_export_transactions_quickbooks(
        self, client: TestClient, mock_db_connection: AsyncMock, 
        auth_headers: dict, sample_transactions
    ):
        """Test exporting transactions to QuickBooks IIF format."""
        # Mock export service
        with patch("giki_ai_api.domains.exports.router.export_service") as mock_service:
            mock_service.export_transactions.return_value = {
                "content": "!TRNS\tTRNSTYPE\tDATE\tACCNT\tNAME\tCLASS\tAMOUNT\nTRNS\tEXPENSE\t1/15/2024\tTechnology/Cloud Services\tAMAZON WEB SERVICES\t\t-250.50",
                "filename": "transactions_export_2024-01-15.iif", 
                "content_type": "application/octet-stream"
            }
            
            export_request = {
                "format_id": "quickbooks_iif",
                "date_from": "2024-01-01",
                "date_to": "2024-01-31",
                "category_ids": [15, 8],
                "include_uncategorized": False
            }
            
            response = client.post(
                "/api/v1/exports/transactions",
                json=export_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            assert response.headers["content-type"] == "application/octet-stream"

    def test_export_with_date_filters(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test exporting transactions with date range filters."""
        with patch("giki_ai_api.domains.exports.router.export_service") as mock_service:
            mock_service.export_transactions.return_value = {
                "content": "Date,Description,Amount\n2024-01-15,AMAZON WEB SERVICES,-250.50",
                "filename": "filtered_export.csv",
                "content_type": "text/csv"
            }
            
            export_request = {
                "format_id": "csv",
                "date_from": "2024-01-15",
                "date_to": "2024-01-15",
                "category_ids": [15]
            }
            
            response = client.post(
                "/api/v1/exports/transactions",
                json=export_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            # Verify service was called with correct filters
            mock_service.export_transactions.assert_called_once()

    def test_export_by_categories(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test exporting transactions filtered by categories."""
        with patch("giki_ai_api.domains.exports.router.export_service") as mock_service:
            mock_service.export_transactions.return_value = {
                "content": "Date,Description,Amount,Category\n2024-01-15,AMAZON WEB SERVICES,-250.50,Technology",
                "filename": "category_export.csv",
                "content_type": "text/csv"
            }
            
            export_request = {
                "format_id": "csv",
                "category_ids": [15],
                "include_uncategorized": False
            }
            
            response = client.post(
                "/api/v1/exports/transactions",
                json=export_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK


class TestMISExports:
    """Test MIS-specific export functionality."""

    def test_export_mis_summary(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test exporting MIS categorization summary."""
        with patch("giki_ai_api.domains.exports.router.export_service") as mock_service:
            mock_service.export_mis_summary.return_value = {
                "content": "Category,Count,Total Amount,Avg Confidence\nTechnology,25,5250.00,0.94",
                "filename": "mis_summary_2024-01.csv",
                "content_type": "text/csv"
            }
            
            export_request = {
                "date_from": "2024-01-01",
                "date_to": "2024-01-31",
                "include_confidence_metrics": True
            }
            
            response = client.post(
                "/api/v1/exports/mis-summary",
                json=export_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            assert response.headers["content-type"].startswith("text/csv")

    def test_export_accuracy_report(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test exporting MIS accuracy analysis report."""
        with patch("giki_ai_api.domains.exports.router.export_service") as mock_service:
            mock_service.export_accuracy_report.return_value = {
                "content": "Upload,Total Transactions,Categorized,Accuracy %,Avg Confidence\nupload-123,100,98,98.0,0.89",
                "filename": "accuracy_report_2024-01.csv",
                "content_type": "text/csv"
            }
            
            export_request = {
                "upload_ids": ["upload-123", "upload-456"],
                "include_transaction_details": True
            }
            
            response = client.post(
                "/api/v1/exports/accuracy-report",
                json=export_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK


class TestExportValidation:
    """Test export validation and error handling."""

    def test_export_invalid_format(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test exporting with invalid format ID."""
        export_request = {
            "format_id": "invalid_format",
            "date_from": "2024-01-01",
            "date_to": "2024-01-31"
        }
        
        response = client.post(
            "/api/v1/exports/transactions",
            json=export_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "format" in data["detail"].lower()

    def test_export_invalid_date_range(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test exporting with invalid date range."""
        export_request = {
            "format_id": "csv",
            "date_from": "2024-01-31",
            "date_to": "2024-01-01"  # End before start
        }
        
        response = client.post(
            "/api/v1/exports/transactions",
            json=export_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_export_no_transactions(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test exporting when no transactions match filters."""
        with patch("giki_ai_api.domains.exports.router.export_service") as mock_service:
            mock_service.export_transactions.side_effect = ValidationError("No transactions found matching the specified criteria")
            
            export_request = {
                "format_id": "csv",
                "date_from": "2030-01-01",
                "date_to": "2030-01-31"
            }
            
            response = client.post(
                "/api/v1/exports/transactions",
                json=export_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_400_BAD_REQUEST


class TestExportPerformance:
    """Test export performance and optimization."""

    def test_large_export_streaming(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test streaming large export files."""
        with patch("giki_ai_api.domains.exports.router.export_service") as mock_service:
            # Simulate large export
            large_content = "Date,Description,Amount\n" + "\n".join([
                f"2024-01-{i:02d},Transaction {i},-{i}.00" for i in range(1, 1001)
            ])
            
            mock_service.export_transactions.return_value = {
                "content": large_content,
                "filename": "large_export.csv",
                "content_type": "text/csv"
            }
            
            export_request = {
                "format_id": "csv",
                "date_from": "2024-01-01",
                "date_to": "2024-12-31"
            }
            
            response = client.post(
                "/api/v1/exports/transactions",
                json=export_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            assert len(response.content) > 10000  # Large file


class TestExportAnalytics:
    """Test export analytics and tracking."""

    def test_track_export_usage(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test that export usage is tracked for analytics."""
        with patch("giki_ai_api.domains.exports.router.export_service") as mock_service:
            mock_service.export_transactions.return_value = {
                "content": "Date,Description,Amount\n2024-01-15,Test,-100.00",
                "filename": "test_export.csv",
                "content_type": "text/csv"
            }
            
            export_request = {
                "format_id": "csv",
                "date_from": "2024-01-01",
                "date_to": "2024-01-31"
            }
            
            response = client.post(
                "/api/v1/exports/transactions",
                json=export_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            # Verify tracking was called
            mock_service.export_transactions.assert_called_once()