"""
Comprehensive unit tests for Accuracy API - core MIS functionality.

Tests the complete accuracy measurement workflow for MIS categorization,
including test creation, execution, metrics collection, and reporting.
"""

import json
from datetime import datetime, timezone
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from giki_ai_api.core.main import app
from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session
from giki_ai_api.domains.auth.models import User
from giki_ai_api.domains.auth.secure_auth import get_current_active_user


@pytest.fixture
def mock_db_connection():
    """Mock database connection."""
    return AsyncMock()


@pytest.fixture
def mock_current_user():
    """Mock authenticated user."""
    user = Mock(spec=User)
    user.id = 1
    user.email = "<EMAIL>"
    user.is_active = True
    return user


@pytest.fixture
def client(mock_current_user, mock_db_connection):
    """Create test client with overridden dependencies."""
    app.dependency_overrides[get_current_active_user] = lambda: mock_current_user
    app.dependency_overrides[get_current_tenant_id] = lambda: 1
    app.dependency_overrides[get_db_session] = lambda: mock_db_connection
    
    test_client = TestClient(app)
    yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def sample_accuracy_test():
    """Sample accuracy test data."""
    return {
        "id": 123,  # Use integer ID
        "tenant_id": 1,
        "name": "MIS Categorization Accuracy Test",
        "description": "Test AI categorization accuracy against known data",
        "scenario": "historical_data",  # Required field
        "test_data_source": "libs/test-data/synthetic/hdfc_bank_transactions.csv",  # Required field
        "sample_size": 100,
        "status": "completed",
        "created_at": datetime.now(timezone.utc),
        "started_at": datetime.now(timezone.utc),
        "completed_at": datetime.now(timezone.utc),
        "total_transactions": 1000,
        "successful_categorizations": 940,
        "ai_judge_correct": 940,
        "ai_judge_incorrect": 60,
        "ai_judge_partially_correct": 0,
        "precision": 0.94,
        "recall": 0.92,
        "f1_score": 0.93,
        "accuracy_percentage": 94.0
    }


@pytest.fixture
def sample_accuracy_metrics():
    """Sample accuracy metrics data."""
    return [
        {
            "id": 1,  # Use integer ID
            "test_id": 123,  # Use integer ID
            "metric_type": "overall_accuracy",
            "value": 0.94,
            "category": "All Categories",
            "confidence_threshold": 0.8
        },
        {
            "id": 2,  # Use integer ID 
            "test_id": 123,  # Use integer ID
            "metric_type": "category_accuracy",
            "value": 0.98,
            "category": "Technology/Cloud Services",
            "confidence_threshold": 0.8
        }
    ]


class TestAccuracyTestManagement:
    """Test accuracy test creation and management."""

    def test_create_accuracy_test(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test creating a new accuracy test."""
        # Mock AccuracyMeasurementService
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Mock the service methods
            test_id = 123
            mock_service.create_accuracy_test.return_value = test_id
            
            # Mock the get_accuracy_test to return a proper test object
            from giki_ai_api.domains.accuracy.models import AccuracyTest, AccuracyTestScenario, AccuracyTestStatus
            mock_test = AccuracyTest(
                id=test_id,
                tenant_id=1,
                name="MIS Categorization Test",
                description="Test AI categorization accuracy",
                scenario=AccuracyTestScenario.HISTORICAL_DATA,
                test_data_source="libs/test-data/synthetic/hdfc_bank_transactions.csv",
                sample_size=100,
                status=AccuracyTestStatus.PENDING,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            mock_service.get_accuracy_test.return_value = mock_test
            
            test_request = {
                "name": "MIS Categorization Test",
                "description": "Test AI categorization accuracy",
                "scenario": "historical_data",
                "test_data_source": "libs/test-data/synthetic/hdfc_bank_transactions.csv",
                "sample_size": 100
            }
            
            response = client.post(
                "/api/v1/accuracy/tests",
                json=test_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["id"] == 123
            assert data["name"] == "MIS Categorization Test"
            assert data["scenario"] == "historical_data"
            assert data["status"] == "pending"

    def test_get_accuracy_tests_list(
        self, client: TestClient, mock_db_connection: AsyncMock, 
        auth_headers: dict, sample_accuracy_test
    ):
        """Test retrieving list of accuracy tests."""
        # Create proper AccuracyTestSummary objects for the response
        from giki_ai_api.domains.accuracy.models import AccuracyTestSummary, AccuracyTestScenario, AccuracyTestStatus
        mock_tests = [
            AccuracyTestSummary(
                test_id=123,
                test_name="MIS Categorization Accuracy Test",
                scenario=AccuracyTestScenario.HISTORICAL_DATA,
                status=AccuracyTestStatus.COMPLETED,
                overall_accuracy=94.0,
                precision=0.94,
                recall=0.92,
                f1_score=0.93,
                total_transactions=1000,
                successful_categorizations=940,
                success_rate=94.0,
                ai_judge_accuracy=95.0,
                ai_judge_confidence_avg=0.88
            )
        ]
        
        # Override the dependency directly in the app for this test
        from giki_ai_api.core.dependencies import get_accuracy_service
        from giki_ai_api.core.main import app
        
        mock_service = AsyncMock()
        mock_service.list_accuracy_tests.return_value = mock_tests
        
        app.dependency_overrides[get_accuracy_service] = lambda: mock_service
        
        try:
            response = client.get("/api/v1/accuracy/tests", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "tests" in data
            assert len(data["tests"]) == 1
            assert data["tests"][0]["test_name"] == "MIS Categorization Accuracy Test"
            assert data["tests"][0]["scenario"] == "historical_data"
        finally:
            # Clean up the override
            if get_accuracy_service in app.dependency_overrides:
                del app.dependency_overrides[get_accuracy_service]

    def test_get_accuracy_test_by_id(
        self, client: TestClient, mock_db_connection: AsyncMock,
        auth_headers: dict, sample_accuracy_test
    ):
        """Test retrieving specific accuracy test details."""
        test_id = 123  # Use integer ID
        
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Create proper AccuracyTest object for the response
            from giki_ai_api.domains.accuracy.models import AccuracyTest, AccuracyTestScenario, AccuracyTestStatus
            mock_test = AccuracyTest(
                id=test_id,
                tenant_id=1,
                name="MIS Categorization Accuracy Test",
                description="Test AI categorization accuracy against known data",
                scenario=AccuracyTestScenario.HISTORICAL_DATA,
                test_data_source="libs/test-data/synthetic/hdfc_bank_transactions.csv",
                sample_size=100,
                status=AccuracyTestStatus.COMPLETED,
                created_at=datetime.now(),
                started_at=datetime.now(),
                completed_at=datetime.now(),
                total_transactions=1000,
                successful_categorizations=940,
                ai_judge_correct=940,
                ai_judge_incorrect=60,
                ai_judge_partially_correct=0,
                precision=0.94,
                recall=0.92,
                f1_score=0.93,
                accuracy_percentage=94.0
            )
            mock_service.get_accuracy_test.return_value = mock_test
        
            response = client.get(f"/api/v1/accuracy/tests/{test_id}", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == test_id
            assert data["status"] == "completed"
            assert data["total_transactions"] == 1000
            assert data["successful_categorizations"] == 940


class TestAccuracyTestExecution:
    """Test accuracy test execution workflow."""

    def test_run_accuracy_test(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test executing an accuracy test."""
        test_id = 123  # Use integer ID
        
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Create proper AccuracyTest object for get_accuracy_test call
            from giki_ai_api.domains.accuracy.models import AccuracyTest, AccuracyTestScenario, AccuracyTestStatus
            mock_test = AccuracyTest(
                id=test_id,
                tenant_id=1,
                name="MIS Categorization Accuracy Test",
                description="Test AI categorization accuracy",
                scenario=AccuracyTestScenario.HISTORICAL_DATA,
                test_data_source="libs/test-data/synthetic/hdfc_bank_transactions.csv",
                sample_size=100,
                status=AccuracyTestStatus.PENDING,  # Must be PENDING to run
                created_at=datetime.now(),
                total_transactions=0,
                successful_categorizations=0,
                ai_judge_correct=0,
                ai_judge_incorrect=0,
                ai_judge_partially_correct=0
            )
            mock_service.get_accuracy_test.return_value = mock_test
            
            # Mock run_accuracy_test method response
            mock_service.run_accuracy_test.return_value = {
                "test_id": test_id,
                "status": "completed",
                "results": {
                    "total_transactions": 100,
                    "successful_categorizations": 94,
                    "ai_judge_correct": 88,
                    "ai_judge_incorrect": 12
                },
                "metrics": {
                    "overall_accuracy": 94.0,
                    "overall_precision": 0.88,
                    "overall_recall": 0.88,
                    "overall_f1_score": 0.88
                },
                "summary": {
                    "total_transactions": 100,
                    "success_rate": 94.0,
                    "ai_judge_accuracy": 88.0
                }
            }
            
            response = client.post(f"/api/v1/accuracy/tests/{test_id}/run", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["test_id"] == test_id
            assert data["status"] == "completed"
            assert data["message"] == "Accuracy test completed successfully"
            assert "results" in data
            assert "metrics" in data

    def test_get_test_metrics(
        self, client: TestClient, mock_db_connection: AsyncMock,
        auth_headers: dict, sample_accuracy_metrics
    ):
        """Test retrieving accuracy test metrics."""
        test_id = 123
        
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Create proper AccuracyTest object for get_accuracy_test call
            from giki_ai_api.domains.accuracy.models import AccuracyTest, AccuracyTestScenario, AccuracyTestStatus, AccuracyMetric
            mock_test = AccuracyTest(
                id=test_id,
                tenant_id=1,
                name="MIS Categorization Accuracy Test",
                description="Test AI categorization accuracy",
                scenario=AccuracyTestScenario.HISTORICAL_DATA,
                test_data_source="libs/test-data/synthetic/hdfc_bank_transactions.csv",
                sample_size=100,
                status=AccuracyTestStatus.COMPLETED,
                created_at=datetime.now(),
                total_transactions=1000,
                successful_categorizations=940,
                ai_judge_correct=940,
                ai_judge_incorrect=60,
                ai_judge_partially_correct=0
            )
            mock_service.get_accuracy_test.return_value = mock_test
            
            # Create proper AccuracyMetric objects for the response
            mock_metrics = [
                AccuracyMetric(
                    id=1,
                    test_id=test_id,
                    metric_name="overall",
                    metric_type="overall_accuracy",
                    value=0.94,
                    sample_count=1000,
                    true_positives=940,
                    false_positives=60,
                    calculation_method="ai_judge_evaluation",
                    calculated_at=datetime.now()
                ),
                AccuracyMetric(
                    id=2,
                    test_id=test_id,
                    metric_name="Technology/Cloud Services",
                    metric_type="category_accuracy",
                    value=0.98,
                    sample_count=250,
                    true_positives=245,
                    false_positives=5,
                    category_filter="Technology/Cloud Services",
                    calculation_method="ai_judge_evaluation",
                    calculated_at=datetime.now()
                )
            ]
            mock_service.repository.get_accuracy_metrics_for_test.return_value = mock_metrics
        
            response = client.get(f"/api/v1/accuracy/tests/{test_id}/metrics", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "metrics" in data
            assert len(data["metrics"]) == 2
            assert data["metrics"][0]["metric_type"] == "overall_accuracy"
            assert data["metrics"][0]["value"] == 0.94
            assert data["metrics"][1]["metric_name"] == "Technology/Cloud Services"

    def test_get_ai_judgments(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving AI judgment details."""
        test_id = 123
        
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Create proper AccuracyTest object for get_accuracy_test call
            from giki_ai_api.domains.accuracy.models import AccuracyTest, AccuracyTestScenario, AccuracyTestStatus, AIJudgment, AIJudgmentResult
            mock_test = AccuracyTest(
                id=test_id,
                tenant_id=1,
                name="MIS Categorization Accuracy Test",
                description="Test AI categorization accuracy",
                scenario=AccuracyTestScenario.HISTORICAL_DATA,
                test_data_source="libs/test-data/synthetic/hdfc_bank_transactions.csv",
                sample_size=100,
                status=AccuracyTestStatus.COMPLETED,
                created_at=datetime.now(),
                total_transactions=1000,
                successful_categorizations=940,
                ai_judge_correct=940,
                ai_judge_incorrect=60,
                ai_judge_partially_correct=0
            )
            mock_service.get_accuracy_test.return_value = mock_test
            
            # Create proper AIJudgment objects for the response
            sample_judgments = [
                AIJudgment(
                    id=1,
                    test_id=test_id,
                    transaction_id="txn-123",
                    original_category="Technology/Cloud Services",
                    ai_category="Technology/Cloud Services",
                    ai_full_hierarchy="Business > Technology > Cloud Services",
                    ai_confidence=0.95,
                    judgment_result=AIJudgmentResult.CORRECT,
                    judgment_confidence=0.95,
                    judgment_reasoning="High confidence match for AWS expense",
                    transaction_description="AWS EC2 Instance charges",
                    transaction_amount=125.50,
                    transaction_type="debit",
                    judged_at=datetime.now(),
                    judge_model="gemini-2.0-flash-001",
                    judge_version="2.0"
                ),
                AIJudgment(
                    id=2,
                    test_id=test_id,
                    transaction_id="txn-124",
                    original_category="Marketing",
                    ai_category="Office Supplies",
                    ai_full_hierarchy="Business > Office > Supplies",
                    ai_confidence=0.67,
                    judgment_result=AIJudgmentResult.INCORRECT,
                    judgment_confidence=0.67,
                    judgment_reasoning="Misclassified marketing materials as office supplies",
                    transaction_description="Marketing brochures printing",
                    transaction_amount=89.99,
                    transaction_type="debit",
                    judged_at=datetime.now(),
                    judge_model="gemini-2.0-flash-001",
                    judge_version="2.0"
                )
            ]
            
            mock_service.repository.get_ai_judgments_for_test.return_value = sample_judgments
        
            response = client.get(f"/api/v1/accuracy/tests/{test_id}/judgments", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "judgments" in data
            assert len(data["judgments"]) == 2
            assert data["judgments"][0]["judgment_result"] == "correct"
            assert data["judgments"][1]["judgment_result"] == "incorrect"


class TestCategorySchemaManagement:
    """Test category schema management for accuracy testing."""

    def test_create_category_schema(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test creating a category schema for testing."""
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Mock repository method response
            schema_id = 123
            mock_service.repository.create_category_schema.return_value = schema_id
            
            # Create proper CategorySchema object for get_category_schema call
            from giki_ai_api.domains.accuracy.models import CategorySchema
            mock_schema = CategorySchema(
                id=schema_id,
                tenant_id=1,
                name="Standard Business Categories",
                description="Common business expense categories",
                schema_format="json",
                schema_data={
                    "categories": [
                        {"name": "Technology/Cloud Services", "code": "TECH"},
                        {"name": "Office Supplies", "code": "OFFICE"},
                        {"name": "Marketing", "code": "MARKETING"}
                    ]
                },
                category_count=3,
                max_hierarchy_depth=1,
                has_gl_codes=False,
                imported_from="test_data.json",
                imported_at=datetime.now(),
                is_active=True,
                usage_count=0
            )
            mock_service.repository.get_category_schema.return_value = mock_schema
            
            schema_request = {
                "name": "Standard Business Categories",
                "description": "Common business expense categories",
                "schema_format": "json",  # Required field
                "schema_data": {          # Required field
                    "categories": [
                        {"name": "Technology/Cloud Services", "code": "TECH"},
                        {"name": "Office Supplies", "code": "OFFICE"},
                        {"name": "Marketing", "code": "MARKETING"}
                    ]
                },
                "imported_from": "test_data.json"  # Required field
            }
            
            response = client.post(
                "/api/v1/accuracy/schemas",
                json=schema_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["id"] == 123
            assert data["name"] == "Standard Business Categories"
            assert data["category_count"] == 3

    def test_import_category_schema(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test importing category schema from existing upload."""
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            # The endpoint doesn't use repository method, it directly processes the data
            # We need to mock the connection.fetchval call that returns the schema_id
            mock_db_connection.fetchval = AsyncMock(return_value=456)
            mock_db_connection.execute = AsyncMock()
            
            import_request = {
                "name": "Imported from Upload 123",  # Required field
                "description": "Schema imported from file upload",
                "file_format": "json",  # Required field
                "file_content": '{"categories": [{"name": "Technology", "code": "TECH"}, {"name": "Office", "code": "OFF"}]}',  # Required field
                "detect_hierarchy": True,
                "generate_gl_codes": True
            }
            
            response = client.post(
                "/api/v1/accuracy/schemas/import",
                json=import_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert "schema_id" in data  # Accept any valid schema_id since this endpoint does complex processing
            assert data["categories_imported"] == 2  # Based on actual test data with 2 categories

    def test_get_category_schemas(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving available category schemas."""
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Create proper CategorySchema objects for the response
            from giki_ai_api.domains.accuracy.models import CategorySchema
            sample_schemas = [
                CategorySchema(
                    id=123,
                    tenant_id=1,
                    name="Standard Business Categories",
                    description="Common business expense categories",
                    schema_format="json",
                    schema_data={"categories": []},
                    category_count=25,
                    max_hierarchy_depth=2,
                    has_gl_codes=False,
                    imported_from="test_file.json",
                    imported_at=datetime.now(),
                    is_active=True,
                    usage_count=0
                )
            ]
            
            mock_service.repository.list_category_schemas.return_value = sample_schemas
        
            response = client.get("/api/v1/accuracy/schemas", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "schemas" in data
            assert len(data["schemas"]) == 1
            assert data["schemas"][0]["name"] == "Standard Business Categories"


class TestAccuracyReporting:
    """Test accuracy reporting and analytics."""

    def test_get_accuracy_report(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test generating comprehensive accuracy report."""
        test_id = 123
        
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Create proper AccuracyTest object for get_accuracy_test call
            from giki_ai_api.domains.accuracy.models import AccuracyTest, AccuracyTestScenario, AccuracyTestStatus
            mock_test = AccuracyTest(
                id=test_id,
                tenant_id=1,
                name="MIS Categorization Accuracy Test",
                description="Test AI categorization accuracy",
                scenario=AccuracyTestScenario.HISTORICAL_DATA,
                test_data_source="libs/test-data/synthetic/hdfc_bank_transactions.csv",
                sample_size=100,
                status=AccuracyTestStatus.COMPLETED,
                created_at=datetime.now(),
                total_transactions=1000,
                successful_categorizations=940,
                ai_judge_correct=940,
                ai_judge_incorrect=60,
                ai_judge_partially_correct=0
            )
            mock_service.get_accuracy_test.return_value = mock_test
            
            # Mock repository methods
            mock_service.repository.get_accuracy_metrics_for_test.return_value = []
            mock_service.repository.get_ai_judgments_for_test.return_value = []
            
            response = client.get(f"/api/v1/accuracy/tests/{test_id}/report", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["test_id"] == test_id
            assert data["test_name"] == "MIS Categorization Accuracy Test"
            assert "overall_metrics" in data
            assert "ai_judge_summary" in data


class TestTemporalValidation:
    """Test temporal validation features."""

    def test_temporal_validation(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test temporal accuracy validation."""
        tenant_id = 1
        
        # Mock the temporal service that the endpoint imports
        with patch("giki_ai_api.domains.accuracy.temporal_accuracy_service_simple.SimpleTemporalAccuracyService") as mock_temporal_service_class:
            mock_temporal_service = AsyncMock()
            mock_temporal_service_class.return_value = mock_temporal_service
            
            # Mock the service methods
            mock_temporal_service.get_temporal_validation_status.return_value = {
                "ready_for_validation": True,
                "data_statistics": {
                    "with_original_categories": 100,
                    "training_period_transactions": 500,
                    "testing_period_transactions": 200
                }
            }
            
            mock_temporal_service.run_progressive_monthly_validation.return_value = {
                "overall_statistics": {
                    "average_accuracy": 0.92,
                    "overall_meets_target": True,
                    "months_tested": ["2024-07", "2024-08", "2024-09"],
                    "temporal_consistency": 0.88,
                    "improvement_trend": "stable",
                    "total_transactions_tested": 1500
                },
                "monthly_results": []
            }
            
            mock_temporal_service.create_temporal_data_split.return_value = {
                "training_period": "2024-01-01 to 2024-06-30",
                "testing_period": "2024-07-01 to 2024-12-31"
            }
            
            response = client.post(f"/api/v1/accuracy/temporal/validate/{tenant_id}", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["tenant_id"] == tenant_id
            assert data["status"] == "completed"
            assert data["validation_type"] == "temporal_accuracy"

    def test_get_temporal_status(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving temporal validation status."""
        tenant_id = 1
        
        # Mock the temporal service that the endpoint imports
        with patch("giki_ai_api.domains.accuracy.temporal_accuracy_service_simple.SimpleTemporalAccuracyService") as mock_temporal_service_class:
            mock_temporal_service = AsyncMock()
            mock_temporal_service_class.return_value = mock_temporal_service
            
            # Mock the service method
            mock_temporal_service.get_temporal_validation_status.return_value = {
                "ready_for_validation": True,
                "data_statistics": {
                    "with_original_categories": 100,
                    "training_period_transactions": 500,
                    "testing_period_transactions": 200
                },
                "last_validation": datetime.now(timezone.utc),
                "accuracy_trend": "improving",
                "drift_detected": False
            }
            
            response = client.get(f"/api/v1/accuracy/temporal/status/{tenant_id}", headers=auth_headers)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["tenant_id"] == tenant_id
            assert data["validation_type"] == "temporal_accuracy"
            assert data["requirements_met"] == True


class TestErrorHandling:
    """Test error handling scenarios."""

    def test_get_nonexistent_test(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test retrieving non-existent accuracy test."""
        test_id = 999
        
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Mock the get_accuracy_test to return None (not found)
            # The router logic checks if test is None and raises HTTPException 404
            mock_service.get_accuracy_test.return_value = None
            
            response = client.get(f"/api/v1/accuracy/tests/{test_id}", headers=auth_headers)

            # Router checks if test is None and raises HTTPException(404) directly
            assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_create_test_validation_error(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test creating accuracy test with invalid data."""
        invalid_request = {
            "name": "",  # Empty name
            "test_type": "invalid_type",
            "upload_ids": []  # Empty upload list
        }
        
        response = client.post(
            "/api/v1/accuracy/tests",
            json=invalid_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_run_test_already_running(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test running accuracy test that's already running."""
        test_id = 123
        
        # Mock AccuracyMeasurementService since endpoint creates it directly
        with patch("giki_ai_api.domains.accuracy.router.AccuracyMeasurementService") as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Mock the run_accuracy_test method to raise AccuracyMeasurementError for already running test
            from giki_ai_api.domains.accuracy.service import AccuracyMeasurementError
            mock_service.run_accuracy_test.side_effect = AccuracyMeasurementError(
                f"Test {test_id} is not in pending status (current: running)"
            )
            
            response = client.post(f"/api/v1/accuracy/tests/{test_id}/run", headers=auth_headers)

            # The service raises AccuracyMeasurementError which router converts to 400
            assert response.status_code == status.HTTP_400_BAD_REQUEST