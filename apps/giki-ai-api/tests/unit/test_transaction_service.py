"""
Unit tests for transaction service.

Tests cover transaction CRUD operations, batch processing,
amount processing, and analysis without external dependencies.
"""

import asyncio
import uuid
from datetime import date, datetime
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch, Mock

import pytest
from asyncpg import Connection

from giki_ai_api.domains.transactions.models import Transaction
from giki_ai_api.domains.transactions.schemas import TransactionCreate
from giki_ai_api.domains.transactions.service import (
    AnalysisResult,
    BatchResult,
    ProcessingOptions,
    TransactionService,
)
from giki_ai_api.shared.exceptions import TransactionProcessingError, ValidationError


class TestTransactionService:
    """Test transaction service core functionality."""

    @pytest.fixture
    def mock_ai_service(self):
        """Mock AI service for testing."""
        return AsyncMock()

    @pytest.fixture
    def mock_ws_service(self):
        """Mock WebSocket service for testing."""
        return AsyncMock()

    @pytest.fixture
    def transaction_service(self, mock_db_connection, mock_ai_service):
        """Create transaction service instance for testing."""
        with patch('giki_ai_api.domains.transactions.service.WebSocketService') as mock_ws:
            mock_ws.return_value = AsyncMock()
            service = TransactionService(mock_db_connection, mock_ai_service)
            return service

    @pytest.fixture
    def sample_transaction_data(self):
        """Sample transaction data for testing."""
        return {
            "date": date.today(),
            "description": "Office Supplies Purchase",
            "amount": 125.50,
            "account": "Business Checking",
            "transaction_type": "debit",
        }

    @pytest.fixture
    def sample_transaction_create(self, sample_transaction_data):
        """Sample TransactionCreate object."""
        return TransactionCreate(**sample_transaction_data)


class TestTransactionCreation(TestTransactionService):
    """Test transaction creation functionality."""

    @pytest.mark.asyncio
    async def test_create_transaction_from_dict(
        self, transaction_service, mock_db_connection, sample_transaction_data
    ):
        """Test creating transaction from dictionary data."""
        # Mock UUID generation
        test_uuid = "test-uuid-123"
        with patch('uuid.uuid4', return_value=test_uuid):
            # Mock execute to return success
            mock_db_connection.execute.return_value = None
            
            # Mock _process_amount and _process_description
            with patch.object(transaction_service, '_process_amount', return_value=sample_transaction_data["amount"]):
                with patch.object(transaction_service, '_process_description', return_value=sample_transaction_data["description"]):
                    # Patch Transaction creation to include timestamps
                    with patch('giki_ai_api.domains.transactions.service.Transaction') as MockTransaction:
                        mock_transaction = Transaction(
                            id=test_uuid,
                            tenant_id=1,
                            upload_id="test-upload",
                            description=sample_transaction_data["description"],
                            amount=sample_transaction_data["amount"],
                            date=sample_transaction_data["date"],
                            account=sample_transaction_data["account"],
                            transaction_type=sample_transaction_data["transaction_type"],
                            created_at=datetime.now(),
                            updated_at=datetime.now(),
                        )
                        MockTransaction.return_value = mock_transaction
                        
                        result = await transaction_service.create_transaction(
                            sample_transaction_data, tenant_id=1, upload_id="test-upload"
                        )
            
            assert result.id == test_uuid
            assert result.description == sample_transaction_data["description"]
            assert result.amount == sample_transaction_data["amount"]
            assert result.date == sample_transaction_data["date"]
            assert result.tenant_id == 1
            
            # Verify SQL was executed
            mock_db_connection.execute.assert_called_once()
            call_args = mock_db_connection.execute.call_args[0]
            assert "INSERT INTO transactions" in call_args[0]

    @pytest.mark.asyncio
    async def test_create_transaction_from_schema(
        self, transaction_service, mock_db_connection, sample_transaction_create
    ):
        """Test creating transaction from TransactionCreate schema."""
        test_uuid = "test-uuid-456"
        with patch('uuid.uuid4', return_value=test_uuid):
            mock_db_connection.execute.return_value = None
            
            result = await transaction_service.create_transaction(
                sample_transaction_create, tenant_id=2
            )
            
            assert result.id == test_uuid
            assert result.tenant_id == 2
            assert result.description == sample_transaction_create.description

    @pytest.mark.asyncio
    async def test_create_transaction_processes_amount(
        self, transaction_service, mock_db_connection, sample_transaction_data
    ):
        """Test that amount is processed correctly."""
        mock_db_connection.execute.return_value = None
        
        # Test with string amount
        data = sample_transaction_data.copy()
        data["amount"] = "1,234.56"
        
        with patch.object(
            transaction_service, '_process_amount', 
            new_callable=AsyncMock
        ) as mock_process:
            mock_process.return_value = Decimal("1234.56")
            
            result = await transaction_service.create_transaction(data)
            
            mock_process.assert_called_once_with("1,234.56")
            assert result.amount == Decimal("1234.56")

    @pytest.mark.asyncio
    async def test_create_transaction_processes_description(
        self, transaction_service, mock_db_connection, sample_transaction_data
    ):
        """Test that description is processed correctly."""
        mock_db_connection.execute.return_value = None
        
        # Test with messy description
        data = sample_transaction_data.copy()
        data["description"] = "  PURCHASE AT   STORE #123  "
        
        with patch.object(
            transaction_service, '_process_description',
            new_callable=AsyncMock
        ) as mock_process:
            mock_process.return_value = "Purchase at Store #123"
            
            result = await transaction_service.create_transaction(data)
            
            mock_process.assert_called_once_with("  PURCHASE AT   STORE #123  ")
            assert result.description == "Purchase at Store #123"

    @pytest.mark.asyncio
    async def test_create_transaction_database_error(
        self, transaction_service, mock_db_connection, sample_transaction_data
    ):
        """Test transaction creation with database error."""
        mock_db_connection.execute.side_effect = Exception("Database connection lost")
        
        with pytest.raises(TransactionProcessingError) as exc_info:
            await transaction_service.create_transaction(sample_transaction_data)
        
        assert "Transaction creation failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_transaction_date_handling(
        self, transaction_service, mock_db_connection
    ):
        """Test various date format handling."""
        mock_db_connection.execute.return_value = None
        
        # Test with string date
        data = {"description": "Test", "amount": 100, "date": "2024-01-15"}
        result = await transaction_service.create_transaction(data)
        assert result.date == date(2024, 1, 15)
        
        # Test with datetime
        data["date"] = datetime(2024, 1, 16, 10, 30)
        result = await transaction_service.create_transaction(data)
        assert result.date == date(2024, 1, 16)
        
        # Test with no date (should use today)
        data.pop("date")
        result = await transaction_service.create_transaction(data)
        assert result.date == date.today()


class TestBatchProcessing(TestTransactionService):
    """Test batch transaction processing."""

    @pytest.mark.asyncio
    async def test_process_transaction_batch_success(
        self, transaction_service, mock_db_connection
    ):
        """Test successful batch processing."""
        # Create test data
        transactions_data = [
            TransactionCreate(
                description=f"Transaction {i}",
                amount=100.0 * i,
                date=date.today(),
            )
            for i in range(1, 4)
        ]
        
        # Mock successful creation
        async def mock_create_success(tx_data, **kwargs):
            return Transaction(
                id=str(uuid.uuid4()),
                description=tx_data.description,
                amount=tx_data.amount,
                date=tx_data.date,
                tenant_id=kwargs.get("tenant_id", 1),
                upload_id=kwargs.get("upload_id", "default"),
            )
        
        with patch.object(
            transaction_service, 'create_transaction',
            side_effect=mock_create_success
        ):
            result = await transaction_service.process_transaction_batch(
                transactions_data, user_id=1
            )
        
        assert isinstance(result, BatchResult)
        assert result.successful == 3
        assert result.failed == 0
        assert len(result.errors) == 0
        assert len(result.processed_transactions) == 3

    @pytest.mark.asyncio
    async def test_process_transaction_batch_partial_failure(
        self, transaction_service, mock_db_connection
    ):
        """Test batch processing with some failures."""
        transactions_data = [
            TransactionCreate(description=f"Transaction {i}", amount=100.0 * i, date=date.today())
            for i in range(1, 4)
        ]
        
        # Mock with one failure
        async def mock_create_mixed(tx_data, **kwargs):
            if "2" in tx_data.description:
                raise TransactionProcessingError("Processing failed")
            return Transaction(
                id=str(uuid.uuid4()),
                description=tx_data.description,
                amount=tx_data.amount,
                date=tx_data.date,
                tenant_id=1,
                upload_id="default",
            )
        
        with patch.object(
            transaction_service, 'create_transaction',
            side_effect=mock_create_mixed
        ):
            result = await transaction_service.process_transaction_batch(
                transactions_data, user_id=1
            )
        
        assert result.successful == 2
        assert result.failed == 1
        assert len(result.errors) == 1
        assert result.errors[0]["error"] == "Processing failed"

    @pytest.mark.asyncio
    async def test_process_transaction_batch_with_options(
        self, transaction_service, mock_db_connection
    ):
        """Test batch processing with custom options."""
        transactions_data = [
            TransactionCreate(description="Test", amount=100, date=date.today())
        ]
        
        options = ProcessingOptions(
            auto_categorize=False,
            extract_entities=False,
            validate_amounts=False,
            detect_duplicates=False,
            batch_size=1,
        )
        
        with patch.object(
            transaction_service, 'create_transaction',
            new_callable=AsyncMock
        ) as mock_create:
            mock_create.return_value = Transaction(
                id="test-id",
                description="Test",
                amount=100,
                date=date.today(),
                tenant_id=1,
                upload_id="default",
            )
            
            result = await transaction_service.process_transaction_batch(
                transactions_data, user_id=1, options=options
            )
            
            # Verify options were respected
            assert result.successful == 1
            # In a real implementation, we'd verify that options affected processing


class TestAmountProcessing(TestTransactionService):
    """Test amount processing functionality."""

    @pytest.mark.asyncio
    async def test_process_amount_numeric(self, transaction_service):
        """Test processing numeric amounts."""
        # Test integer
        result = await transaction_service._process_amount(100)
        assert result == Decimal("100")
        
        # Test float
        result = await transaction_service._process_amount(123.45)
        assert result == Decimal("123.45")
        
        # Test Decimal
        result = await transaction_service._process_amount(Decimal("999.99"))
        assert result == Decimal("999.99")

    @pytest.mark.asyncio
    async def test_process_amount_string(self, transaction_service):
        """Test processing string amounts."""
        # Test simple string
        result = await transaction_service._process_amount("100.50")
        assert result == Decimal("100.50")
        
        # Test with commas
        result = await transaction_service._process_amount("1,234.56")
        assert result == Decimal("1234.56")
        
        # Test with currency symbol
        result = await transaction_service._process_amount("$1,234.56")
        assert result == Decimal("1234.56")
        
        # Test negative with parentheses
        result = await transaction_service._process_amount("(100.00)")
        assert result == Decimal("-100.00")

    @pytest.mark.asyncio
    async def test_process_amount_invalid(self, transaction_service):
        """Test processing invalid amounts."""
        # Test empty string
        result = await transaction_service._process_amount("")
        assert result == Decimal("0")
        
        # Test None
        result = await transaction_service._process_amount(None)
        assert result == Decimal("0")
        
        # Test invalid string
        with pytest.raises(ValidationError):
            await transaction_service._process_amount("not a number")


class TestDescriptionProcessing(TestTransactionService):
    """Test description processing functionality."""

    @pytest.mark.asyncio
    async def test_process_description_basic(self, transaction_service):
        """Test basic description processing."""
        # Test normal description
        result = await transaction_service._process_description("Office Supplies")
        assert result == "Office Supplies"
        
        # Test with extra spaces
        result = await transaction_service._process_description("  Office   Supplies  ")
        assert result == "Office Supplies"
        
        # Test all caps
        result = await transaction_service._process_description("OFFICE SUPPLIES")
        assert result == "Office Supplies"

    @pytest.mark.asyncio
    async def test_process_description_complex(self, transaction_service):
        """Test complex description processing."""
        # Test with transaction codes
        result = await transaction_service._process_description(
            "POS PURCHASE #1234 AT STORE 5678"
        )
        assert "Store" in result
        
        # Test with dates
        result = await transaction_service._process_description(
            "PAYMENT 01/15/24 INVOICE 12345"
        )
        assert "Payment" in result
        assert "Invoice" in result

    @pytest.mark.asyncio
    async def test_process_description_empty(self, transaction_service):
        """Test empty description handling."""
        result = await transaction_service._process_description("")
        assert result == "No Description"
        
        result = await transaction_service._process_description(None)
        assert result == "No Description"
        
        result = await transaction_service._process_description("   ")
        assert result == "No Description"


class TestTransactionAnalysis(TestTransactionService):
    """Test transaction analysis functionality."""

    @pytest.mark.asyncio
    async def test_analyze_transaction_patterns(
        self, transaction_service, mock_db_connection
    ):
        """Test pattern analysis."""
        # Mock database response with transaction data
        mock_transactions = [
            {
                "description": "Starbucks",
                "amount": 5.50,
                "date": date.today(),
                "category": "Food & Dining",
            },
            {
                "description": "Starbucks",
                "amount": 6.25,
                "date": date.today(),
                "category": "Food & Dining",
            },
            {
                "description": "Office Depot",
                "amount": 125.00,
                "date": date.today(),
                "category": "Office Supplies",
            },
        ]
        
        mock_db_connection.fetch.return_value = mock_transactions
        
        result = await transaction_service.analyze_transaction_patterns(
            tenant_id=1, start_date=date.today(), end_date=date.today()
        )
        
        assert isinstance(result, AnalysisResult)
        assert "patterns" in result.__dict__
        assert "insights" in result.__dict__
        assert "statistics" in result.__dict__
        assert "recommendations" in result.__dict__

    @pytest.mark.asyncio
    async def test_get_transaction_statistics(
        self, transaction_service, mock_db_connection
    ):
        """Test getting transaction statistics."""
        # Mock statistics query response
        mock_stats = {
            "total_count": 100,
            "total_amount": 15000.00,
            "average_amount": 150.00,
            "categorized_count": 85,
            "needs_review_count": 15,
        }
        
        mock_db_connection.fetchrow.return_value = mock_stats
        
        result = await transaction_service.get_transaction_statistics(
            tenant_id=1, start_date=date.today(), end_date=date.today()
        )
        
        assert result["total_count"] == 100
        assert result["total_amount"] == 15000.00
        assert result["categorization_rate"] == 0.85