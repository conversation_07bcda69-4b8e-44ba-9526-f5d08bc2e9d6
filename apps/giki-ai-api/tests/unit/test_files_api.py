"""
Comprehensive unit tests for Files API upload/processing journey.

Tests the complete file upload, schema interpretation, and processing workflow.
"""

import json
from datetime import datetime, timezone
from io import BytesIO
from unittest.mock import AsyncMock, Mock, patch, mock_open

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from giki_ai_api.core.main import app
from giki_ai_api.core.dependencies import get_current_tenant_id, get_db_session
from giki_ai_api.domains.auth.models import User
from giki_ai_api.domains.auth.secure_auth import get_current_active_user


@pytest.fixture
def mock_user():
    """Mock authenticated user."""
    return User(
        id=1,
        email="<EMAIL>", 
        username="<EMAIL>",
        is_active=True,
        is_verified=True,
        is_superuser=False,
        tenant_id=1,
    )


@pytest.fixture
def mock_db_connection():
    """Mock database connection."""
    return AsyncMock()


@pytest.fixture
def client(mock_user, mock_db_connection):
    """Create test client with overridden dependencies."""
    # Override FastAPI dependencies
    app.dependency_overrides[get_current_active_user] = lambda: mock_user
    app.dependency_overrides[get_current_tenant_id] = lambda: 1
    app.dependency_overrides[get_db_session] = lambda: mock_db_connection
    
    # Create test client
    test_client = TestClient(app)
    
    yield test_client
    
    # Clean up overrides
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Authentication headers for API requests."""
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def sample_csv_content():
    """Sample CSV file content for testing."""
    return b"""Date,Description,Amount,Category
2024-01-15,Amazon Web Services,-250.00,Technology/Cloud Services
2024-01-14,Salary Deposit,5000.00,Income/Salary
2024-01-13,Starbucks Coffee,-15.50,Expenses/Meals & Entertainment
2024-01-12,Office Rent,-2000.00,Expenses/Rent
2024-01-11,Microsoft Office,-150.00,Technology/Software"""


class TestFileUpload:
    """Test file upload endpoints."""

    def test_upload_single_file_success(
        self, client: TestClient, mock_db_connection: AsyncMock, 
        auth_headers: dict, sample_csv_content: bytes
    ):
        """Test successful single file upload."""
        upload_id = "upload-123"
        
        # Mock database operations
        mock_db_connection.execute = AsyncMock()
        mock_db_connection.fetchrow = AsyncMock(return_value={"id": upload_id})
        
        # Mock settings and file operations
        with patch("giki_ai_api.domains.files.router.settings") as mock_settings:
            mock_settings.get_upload_directory.return_value = "/tmp/uploads/1"
            
            # Create a proper aiofiles mock with async context manager
            mock_file = AsyncMock()
            mock_file.__aenter__ = AsyncMock(return_value=mock_file)
            mock_file.__aexit__ = AsyncMock(return_value=None)
            mock_file.write = AsyncMock()
            
            with patch("aiofiles.open", return_value=mock_file):
                with patch("giki_ai_api.domains.files.router.check_upload_duplicates") as mock_check:
                    # Create a proper mock FileDeduplicationResult
                    from unittest.mock import Mock
                    mock_result = Mock()
                    mock_result.recommended_action = "proceed"
                    mock_result.customer_message = "No duplicates detected"
                    mock_result.is_duplicate_file = False
                    mock_result.duplicate_transactions_count = 0
                    mock_result.unique_transactions_count = 1
                    # Make the mock async function return the result
                    mock_check.return_value = mock_result
                    
                    response = client.post(
                        "/api/v1/files/upload",
                        files={"files": ("transactions.csv", sample_csv_content, "text/csv")},
                        headers=auth_headers,
                    )

                    assert response.status_code == status.HTTP_200_OK
                    data = response.json()
                    assert "uploads" in data
                    assert data["successful"] >= 1
                    assert data["failed"] == 0
                    assert data["total_files"] >= 1

    def test_upload_multiple_files(
        self, client: TestClient, mock_db_connection: AsyncMock, 
        auth_headers: dict, sample_csv_content: bytes
    ):
        """Test uploading multiple files."""
        # Create multiple test files
        files = [
            ("files", ("bank1.csv", sample_csv_content, "text/csv")),
            ("files", ("bank2.csv", sample_csv_content, "text/csv")),
            ("files", ("credit_card.csv", sample_csv_content, "text/csv")),
        ]
        
        # Mock database operations
        mock_db_connection.execute = AsyncMock()
        mock_db_connection.fetchrow = AsyncMock(side_effect=[
            {"id": "upload-1"},
            {"id": "upload-2"}, 
            {"id": "upload-3"}
        ])
        
        with patch("giki_ai_api.domains.files.router.settings") as mock_settings:
            mock_settings.get_upload_directory.return_value = "/tmp/uploads/1"
            
            # Create a proper aiofiles mock with async context manager
            mock_file = AsyncMock()
            mock_file.__aenter__ = AsyncMock(return_value=mock_file)
            mock_file.__aexit__ = AsyncMock(return_value=None)
            mock_file.write = AsyncMock()
            
            with patch("aiofiles.open", return_value=mock_file):
                with patch("giki_ai_api.domains.files.router.check_upload_duplicates") as mock_check:
                    # Create proper mock FileDeduplicationResult
                    from unittest.mock import Mock
                    mock_result = Mock()
                    mock_result.recommended_action = "proceed"
                    mock_result.customer_message = "No duplicates detected"
                    mock_result.is_duplicate_file = False
                    mock_result.duplicate_transactions_count = 0
                    mock_result.unique_transactions_count = 3
                    mock_check.return_value = mock_result
                    response = client.post(
                        "/api/v1/files/upload",
                        files=files,
                        headers=auth_headers,
                    )

                    assert response.status_code == status.HTTP_200_OK
                    data = response.json()
                    assert data["successful"] == 3
                    assert data["failed"] == 0
                    assert len(data["uploads"]) == 3

    def test_upload_duplicate_detection(
        self, client: TestClient, mock_db_connection: AsyncMock,
        auth_headers: dict, sample_csv_content: bytes
    ):
        """Test duplicate file detection."""
        # Mock duplicate detection
        with patch("giki_ai_api.domains.files.router.check_upload_duplicates") as mock_check:
            # Create mock FileDeduplicationResult that indicates duplicate
            from unittest.mock import Mock
            mock_result = Mock()
            mock_result.recommended_action = "reject"
            mock_result.customer_message = "DUPLICATE FILE: This exact file was already uploaded. Upload rejected."
            mock_result.is_duplicate_file = True
            mock_result.duplicate_upload_id = "existing-123"
            mock_result.duplicate_transactions_count = 0
            mock_result.unique_transactions_count = 0
            mock_result.duplicate_details = {
                "duplicate_file": {
                    "id": "existing-123",
                    "filename": "transactions.csv"
                }
            }
            mock_check.return_value = mock_result
            
            response = client.post(
                "/api/v1/files/upload",
                files={"files": ("transactions.csv", sample_csv_content, "text/csv")},
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["successful"] == 0
            assert data["failed"] == 1
            assert "uploads" in data

    def test_upload_invalid_file_format(
        self, client: TestClient, auth_headers: dict
    ):
        """Test uploading unsupported file format."""
        invalid_content = b"This is just text content"
        
        response = client.post(
            "/api/v1/files/upload",
            files={"files": ("document.txt", invalid_content, "text/plain")},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["successful"] == 0
        assert data["failed"] == 1


class TestFileRetrieval:
    """Test file retrieval endpoints."""

    def test_get_uploads_list(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting list of uploads for a tenant."""
        # Mock database response matching SQL query columns
        mock_uploads = [
            {
                "id": "upload-1",
                "filename": "bank_data.csv",
                "content_type": "text/csv",
                "size": 1024,
                "status": "completed",
                "headers": None,
                "created_at": datetime.now(timezone.utc),
                "tenant_id": 1,
                "updated_at": datetime.now(timezone.utc),
            },
            {
                "id": "upload-2", 
                "filename": "credit_card.csv",
                "content_type": "text/csv",
                "size": 2048,
                "status": "processing",
                "headers": None,
                "created_at": datetime.now(timezone.utc),
                "tenant_id": 1,
                "updated_at": datetime.now(timezone.utc),
            }
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_uploads)

        response = client.get("/api/v1/files", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 2
        assert data[0]["upload_id"] == "upload-1"
        assert data[0]["status"] == "completed"

    def test_get_single_upload(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting details of a single upload."""
        upload_id = "upload-123"
        
        # Mock database response matching actual query
        mock_upload = {
            "id": upload_id,
            "filename": "transactions.csv",
            "content_type": "text/csv",
            "size": 1500,
            "status": "completed",
            "headers": None,
            "created_at": datetime.now(timezone.utc),
            "tenant_id": 1,
            "updated_at": datetime.now(timezone.utc),
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_upload)

        response = client.get(f"/api/v1/files/{upload_id}", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["upload_id"] == upload_id
        assert data["filename"] == "transactions.csv"
        assert data["status"] == "completed"

    def test_get_upload_not_found(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting non-existent upload."""
        upload_id = "non-existent"
        
        # Mock database returning None
        mock_db_connection.fetchrow = AsyncMock(return_value=None)

        response = client.get(f"/api/v1/files/{upload_id}", headers=auth_headers)

        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestSchemaInterpretation:
    """Test schema interpretation endpoints."""

    def test_get_file_schema(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting file schema interpretation."""
        upload_id = "upload-123"
        
        # Mock schema interpretation from database
        mock_interpretation = {
            "id": 1,
            "upload_id": upload_id,
            "tenant_id": 1,
            "filename": "transactions.csv",
            "column_mappings": json.dumps([
                {
                    "original_name": "Date",
                    "mapped_field": "date",
                    "confidence": 0.95,
                    "reasoning": "Date format detected"
                },
                {
                    "original_name": "Description",
                    "mapped_field": "description",
                    "confidence": 0.98,
                    "reasoning": "Text field for transaction description"
                },
                {
                    "original_name": "Amount",
                    "mapped_field": "amount",
                    "confidence": 0.99,
                    "reasoning": "Numeric currency values detected"
                }
            ]),
            "overall_confidence": 0.97,
            "required_fields_mapped": json.dumps({
                "date": True,
                "description": True,
                "amount": True
            }),
            "interpretation_summary": "Successfully mapped all required fields",
            "debit_credit_inference": json.dumps({}),
            "regional_variations": json.dumps([]),
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_interpretation)
        
        response = client.get(f"/api/v1/files/{upload_id}/schema", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["upload_id"] == upload_id
        assert "column_mappings" in data
        assert len(data["column_mappings"]) == 3
        assert data["overall_confidence"] == 0.97

    def test_get_upload_columns(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting upload columns."""
        upload_id = "upload-123"
        
        # Mock database response - match SQL query and normalize_headers function
        mock_upload = {
            "id": upload_id,
            "tenant_id": 1,
            "headers": json.dumps(["Date", "Description", "Amount", "Category", "Reference"]),
            "filename": "bank_data.csv",
            "status": "completed"
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_upload)

        response = client.get(f"/api/v1/files/{upload_id}/columns", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "columns" in data
        assert len(data["columns"]) == 5
        assert "Date" in data["columns"]
        assert "Amount" in data["columns"]

    def test_get_schema_interpretation_cached(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting cached schema interpretation."""
        upload_id = "upload-123"
        
        # Mock database row for upload data
        upload_row = {
            "id": upload_id,
            "tenant_id": 1,
            "file_path": "/tmp/uploads/test.csv",
            "headers": ["Date", "Description", "Amount", "Category"],
            "filename": "test.csv",
            "status": "completed",
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "content_type": "text/csv",
            "size": 1024,
            "user_id": 1,
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=upload_row)
        
        # Mock cached interpretation with all required fields
        cached_interpretation = {
            "upload_id": upload_id,
            "filename": "test.csv",
            "column_mappings": [
                {
                    "original_name": "Date",
                    "mapped_field": "date",
                    "confidence": 0.98,
                    "reasoning": "Clear date column"
                },
                {
                    "original_name": "Description",
                    "mapped_field": "description", 
                    "confidence": 0.95,
                    "reasoning": "Transaction description"
                },
                {
                    "original_name": "Amount",
                    "mapped_field": "amount",
                    "confidence": 0.99,
                    "reasoning": "Transaction amount"
                },
                {
                    "original_name": "Category",
                    "mapped_field": "category",
                    "confidence": 0.90,
                    "reasoning": "Transaction category"
                }
            ],
            "overall_confidence": 0.95,
            "required_fields_mapped": {
                "date": True,
                "description": True,
                "amount": True,
                "category": True
            },
            "interpretation_summary": "Successfully mapped all required fields with high confidence",
            "debit_credit_inference": {},
            "regional_variations": []
        }
        
        # Mock the module-level cache directly
        with patch.dict("giki_ai_api.domains.files.router._schema_interpretation_cache", 
                       {f"{upload_id}_1": cached_interpretation}):
            
            # Mock file existence check (shouldn't be needed due to cache, but just in case)
            with patch("os.path.exists", return_value=True):
                # Mock pandas read operation
                with patch("pandas.read_csv") as mock_read_csv:
                    # Create mock dataframe that simulates the full chain
                    mock_df = Mock()
                    # Mock columns with tolist method
                    mock_columns = Mock()
                    mock_columns.tolist = Mock(return_value=["Date", "Description", "Amount", "Category"])
                    mock_df.columns = mock_columns
                    
                    # Create the chain: df.head(5).fillna("").astype(str).values.tolist()
                    mock_head = Mock()
                    mock_fillna = Mock() 
                    mock_astype = Mock()
                    mock_values = Mock()
                    
                    # Sample data as list of lists (rows)
                    sample_data = [
                        ["2024-01-01", "Test Transaction 1", "100.00", "Category 1"],
                        ["2024-01-02", "Test Transaction 2", "200.00", "Category 2"],
                    ]
                    
                    mock_values.tolist = Mock(return_value=sample_data)
                    mock_astype.values = mock_values
                    mock_fillna.astype = Mock(return_value=mock_astype)
                    mock_head.fillna = Mock(return_value=mock_fillna)
                    mock_df.head = Mock(return_value=mock_head)
                    
                    mock_df.to_dict = Mock(return_value={
                        "Date": ["2024-01-01", "2024-01-02"],
                        "Description": ["Test 1", "Test 2"],
                        "Amount": [100, 200],
                        "Category": ["Cat 1", "Cat 2"]
                    })
                    mock_read_csv.return_value = mock_df
                    
                    response = client.get(f"/api/v1/files/{upload_id}/schema-interpretation", headers=auth_headers)

                    assert response.status_code == status.HTTP_200_OK
                    data = response.json()
                    assert data["upload_id"] == upload_id
                    assert data["overall_confidence"] == 0.95
                    assert len(data["column_mappings"]) == 4
                    assert data["filename"] == "test.csv"


class TestFileProcessing:
    """Test file processing endpoints."""

    def test_process_mapped_file(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test processing file with column mapping."""
        upload_id = "upload-123"
        
        mapping_payload = {
            "mapping": {
                "Date": "date",
                "Description": "description", 
                "Amount": "amount",
                "Category": "category",
            },
            "skip_header": True,
            "date_format": "YYYY-MM-DD"
        }
        
        # Mock upload data
        mock_upload = {
            "id": upload_id,
            "filename": "transactions.csv",
            "file_path": "/tmp/uploads/1/transactions.csv",
            "tenant_id": 1,
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_upload)
        mock_db_connection.execute = AsyncMock()
        mock_db_connection.fetch = AsyncMock(return_value=[])  # Existing transactions
        
        # Mock file system operations
        with patch("os.path.exists", return_value=True):
            with patch("os.listdir", return_value=["transactions.csv"]):
                # Mock file open operation
                mock_file_data = b"Date,Description,Amount,Category\n2024-01-01,Test,100.00,Test Category"
                with patch("builtins.open", mock_open(read_data=mock_file_data)):
                    # Mock file processing services 
                    with patch("giki_ai_api.domains.files.router.IntelligentDataInterpretationService") as mock_service:
                        mock_instance = mock_service.return_value
                        mock_instance.process_confirmed_interpretation = AsyncMock(return_value={
                            "success": True,
                            "transactions_created": 5,
                            "processing_time": 2.5,
                            "errors": [],
                        })
                        
                        # Mock the FileService import at the point where it's used
                        with patch("giki_ai_api.domains.files.service.FileService") as mock_file_service:
                            mock_file_instance = mock_file_service.return_value
                            # Create a mock object that has the expected attributes
                            mock_processing_result = Mock()
                            mock_processing_result.success = True
                            
                            # Create mock sheet with sample_data attribute
                            mock_sheet = Mock()
                            mock_sheet.name = "Sheet1"
                            mock_sheet.columns = ["Date", "Description", "Amount", "Category"]
                            mock_sheet.row_count = 15
                            mock_sheet.sample_data = [
                                ["Date", "Description", "Amount", "Category"],
                                ["2024-01-01", "Test Transaction 1", "100.00", "Test Category 1"],
                                ["2024-01-02", "Test Transaction 2", "200.00", "Test Category 2"]
                            ]
                            
                            mock_processing_result.sheets = [mock_sheet]
                            mock_file_instance.process_file = AsyncMock(return_value=mock_processing_result)
                            
                            response = client.post(
                                f"/api/v1/files/{upload_id}/map",
                                json=mapping_payload,
                                headers=auth_headers,
                            )

                            assert response.status_code == status.HTTP_200_OK
                            data = response.json()
                            assert data["upload_id"] == upload_id
                            assert "message" in data
                            assert data["records_processed"] >= 0
                            assert "errors" in data

    def test_get_transactions_for_upload(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting transactions for a specific upload."""
        upload_id = "upload-123"
        
        # Mock upload row (required for validation)
        upload_row = {
            "id": upload_id,
            "tenant_id": 1,
            "file_path": "/tmp/uploads/test.csv",
            "filename": "test.csv",
            "status": "completed",
            "user_id": 1,
        }
        
        # Mock transactions data
        mock_transactions = [
            {
                "id": "1",  # String ID for Pydantic validation
                "date": "2024-01-15",
                "description": "Amazon Web Services",
                "amount": -250.00,
                "original_category": "Technology/Cloud Services",
                "ai_category": None,
                "upload_id": upload_id,
                "tenant_id": 1,
            },
            {
                "id": "2",  # String ID for Pydantic validation
                "date": "2024-01-14", 
                "description": "Salary Deposit",
                "amount": 5000.00,
                "original_category": "Income/Salary",
                "ai_category": None,
                "upload_id": upload_id,
                "tenant_id": 1,
            }
        ]
        
        # Mock database operations
        mock_db_connection.fetchrow = AsyncMock(return_value=upload_row)  # Upload validation
        mock_db_connection.fetch = AsyncMock(return_value=mock_transactions)
        mock_db_connection.fetchval = AsyncMock(return_value=2)  # Total count

        response = client.get(f"/api/v1/files/{upload_id}/transactions", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert len(data["items"]) == 2
        assert data["total"] == 2
        assert data["items"][0]["description"] == "Amazon Web Services"

    def test_get_upload_processing_status(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting upload processing status."""
        upload_id = "upload-123"
        
        # Mock upload status
        mock_status = {
            "id": upload_id,
            "filename": "transactions.csv",
            "size": 1024,
            "status": "processing",
            "error_message": None,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "total_transactions": 150,  # From COUNT query
            "categorized_transactions": 100,  # From COUNT query  
            "has_schema_interpretation": 1,  # From COUNT query
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_status)

        response = client.get(f"/api/v1/files/{upload_id}/status", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["upload_id"] == upload_id
        assert data["status"] == "processing"
        assert data["progress"] >= 0
        assert data["total_transactions"] == 150
        assert data["categorized_transactions"] == 100


class TestProductionDataUpload:
    """Test production data upload (without categories)."""

    def test_upload_production_files(
        self, client: TestClient, mock_db_connection: AsyncMock,
        auth_headers: dict, sample_csv_content: bytes
    ):
        """Test uploading production files without category labels."""
        # Prepare files without category column
        production_csv = b"""Date,Description,Amount
2024-01-15,Amazon Web Services,-250.00
2024-01-14,Salary Deposit,5000.00
2024-01-13,Starbucks Coffee,-15.50"""
        
        files = [
            ("files", ("production_bank.csv", production_csv, "text/csv")),
            ("files", ("production_card.csv", production_csv, "text/csv")),
        ]
        
        # Mock database operations
        mock_db_connection.execute = AsyncMock()
        
        # First call: onboarding status check, then upload inserts
        onboarding_status_row = {
            "id": 1,
            "tenant_id": 1,
            "onboarding_type": "zero_onboarding", 
            "stage": "completed",
            "approved_for_production": True,
            "approved_at": datetime.now(timezone.utc),
            "approved_by_user_id": 1,
            "approval_notes": "Test approved",
            "last_validation_id": None,
            "last_validation_accuracy": None,
            "total_transactions": 0,
            "transactions_with_labels": 0,
            "date_range_start": None,
            "date_range_end": None,
            "last_activity": datetime.now(timezone.utc),
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        upload_rows = [
            {
                "id": 1,  # Integer ID 
                "tenant_id": 1,
                "filename": "production_bank.csv",
                "content_type": "text/csv",
                "size": 1024,
                "status": "completed",
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "file_path": "/tmp/uploads/1/production_bank.csv",
                "user_id": 1,
                "headers": None
            },
            {
                "id": 2,  # Integer ID
                "tenant_id": 1, 
                "filename": "production_card.csv",
                "content_type": "text/csv", 
                "size": 1024,
                "status": "completed",
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "file_path": "/tmp/uploads/1/production_card.csv",
                "user_id": 1,
                "headers": None
            }
        ]
        
        mock_db_connection.fetchrow = AsyncMock(side_effect=[onboarding_status_row] + upload_rows)
        
        with patch("giki_ai_api.domains.files.router.settings") as mock_settings:
            mock_settings.get_upload_directory.return_value = "/tmp/uploads/1"
            
            # Create a proper aiofiles mock with async context manager
            mock_file = AsyncMock()
            mock_file.__aenter__ = AsyncMock(return_value=mock_file)
            mock_file.__aexit__ = AsyncMock(return_value=None)
            mock_file.write = AsyncMock()
            
            with patch("aiofiles.open", return_value=mock_file):
                with patch("giki_ai_api.domains.files.router.check_upload_duplicates") as mock_check:
                    # Create proper mock FileDeduplicationResult
                    from unittest.mock import Mock
                    mock_result = Mock()
                    mock_result.recommended_action = "proceed"
                    mock_result.customer_message = "No duplicates detected"
                    mock_result.is_duplicate_file = False
                    mock_result.duplicate_transactions_count = 0
                    mock_result.unique_transactions_count = 3
                    mock_check.return_value = mock_result
                    response = client.post(
                        "/api/v1/files/upload-production-data",
                        files=files,
                        headers=auth_headers,
                    )

                    assert response.status_code == status.HTTP_200_OK
                    data = response.json()
                    assert data["successful"] == 2
                    assert data["failed"] == 0


class TestInterpretationConfirmation:
    """Test interpretation confirmation workflow."""

    def test_confirm_interpretation_success(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test confirming schema interpretation."""
        upload_id = "upload-123"
        
        confirmation_data = {
            "upload_id": upload_id,
            "confirmed_mappings": {
                "date_column": "Date",
                "description_column": "Description",
                "amount_column": "Amount",
                "category_column": "Category"
            },
            "processing_options": {
                "skip_header": True,
                "date_format": "YYYY-MM-DD",
                "apply_ai_categorization": True
            }
        }
        
        # Mock upload data
        mock_upload = {
            "id": 123,  # Integer ID
            "file_path": "/tmp/uploads/1/transactions.csv",
            "tenant_id": 1,
            "filename": "transactions.csv",
            "content_type": "text/csv",
            "size": 1024,
            "status": "completed", 
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "user_id": 1,
            "headers": None
        }
        
        # Mock multiple database calls: upload, then possibly user lookup
        mock_user = {
            "id": 1,
            "email": "<EMAIL>",
            "username": "<EMAIL>",
            "is_active": True,
            "is_verified": True,
            "is_superuser": False,
            "tenant_id": 1,
        }
        
        mock_db_connection.fetchrow = AsyncMock(side_effect=[mock_upload, mock_user])
        mock_db_connection.execute = AsyncMock()
        
        # Mock processing service
        with patch("giki_ai_api.domains.files.router.IntelligentDataInterpretationService") as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.process_confirmed_interpretation = AsyncMock()
            
            response = client.post(
                f"/api/v1/files/{upload_id}/interpretation/confirm",
                json=confirmation_data,
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_202_ACCEPTED
            data = response.json()
            assert data["upload_id"] == upload_id
            assert data["status"] == "processing"
            assert "confirmation_id" in data

    def test_get_interpretation_results(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test getting stored interpretation results."""
        upload_id = "upload-123"
        
        # Mock interpretation storage
        mock_interpretation = {
            "upload_id": upload_id,
            "interpretation_data": {
                "confirmed_mapping": {
                    "date_column": "Date",
                    "amount_column": "Amount"
                },
                "confidence_score": 0.92
            },
            "status": "confirmed",
            "created_at": datetime.now(timezone.utc),
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_interpretation)

        response = client.get(f"/api/v1/files/{upload_id}/interpretation", headers=auth_headers)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["upload_id"] == upload_id
        assert data["status"] == "confirmed"
        assert "interpretation_data" in data


class TestErrorHandling:
    """Test error handling in file processing."""

    def test_upload_file_system_error(
        self, client: TestClient, mock_db_connection: AsyncMock, 
        auth_headers: dict, sample_csv_content: bytes
    ):
        """Test handling file system errors during upload."""
        # Mock file system error
        with patch("aiofiles.open", side_effect=OSError("Disk full")):
            response = client.post(
                "/api/v1/files/upload",
                files={"files": ("transactions.csv", sample_csv_content, "text/csv")},
                headers=auth_headers,
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["successful"] == 0
            assert data["failed"] == 1

    def test_process_file_with_invalid_mapping(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test processing file with invalid column mapping."""
        upload_id = "upload-123"
        
        # Mock upload data
        mock_upload = {
            "id": upload_id,  # String ID
            "filename": "test.csv",
            "file_path": "/tmp/uploads/1/test.csv", 
            "tenant_id": 1,
            "status": "completed",
            "user_id": 1,
            "content_type": "text/csv",
            "size": 1024,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "headers": None
        }
        mock_db_connection.fetchrow = AsyncMock(return_value=mock_upload)
        
        # Invalid mapping (missing required fields)
        invalid_mapping = {
            "mapping": {
                "date_column": "Date",
                # Missing description_column and amount_column
            }
        }
        
        response = client.post(
            f"/api/v1/files/{upload_id}/map",
            json=invalid_mapping,
            headers=auth_headers,
        )

        # Should return validation error - the business logic throws 400, but global error handler converts to 500
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    def test_get_processing_details_database_error(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test handling database errors when getting processing details."""
        upload_id = "upload-123"
        
        # Mock database error
        mock_db_connection.fetchrow.side_effect = Exception("Database connection failed")

        response = client.get(f"/api/v1/files/{upload_id}/processing-details", headers=auth_headers)

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


class TestExportFunctionality:
    """Test export functionality."""

    def test_export_transactions_to_excel(
        self, client: TestClient, mock_db_connection: AsyncMock, auth_headers: dict
    ):
        """Test exporting transactions to Excel format."""
        # Mock transactions for export
        mock_transactions = [
            {
                "date": "2024-01-15",
                "description": "Amazon Web Services", 
                "amount": -250.00,
                "category": "Technology/Cloud Services",
            },
            {
                "date": "2024-01-14",
                "description": "Salary Deposit",
                "amount": 5000.00,
                "category": "Income/Salary",
            }
        ]
        mock_db_connection.fetch = AsyncMock(return_value=mock_transactions)
        
        response = client.get(
            "/api/v1/files/export-transactions",
            params={"format": "excel", "export_type": "m1_verification"},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"