#!/bin/bash
set -e

echo "🏗️ Building giki-ai-api using uv and hatchling..."

# Ensure we're in the workspace root
cd "$(dirname "$0")/../.."

# Build the API package using uv build
echo "📦 Building wheel using uv build..."
cd apps/giki-ai-api
uv build --wheel

# Install in development mode
echo "🔧 Installing in development mode..."
uv pip install -e .

echo "✅ Build complete! Package built with uv + hatchling and installed in dev mode."
