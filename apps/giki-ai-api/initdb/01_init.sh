#!/bin/bash
set -e

DB_NAME="giki-ai_test"
OWNER_USER="$POSTGRES_USER" # This will be 'user' from docker-compose env

echo "Executing custom init script: 01_init.sh"
echo "Ensuring database '$DB_NAME' exists and is owned by '$OWNER_USER'"

# Connect to the default 'postgres' database to perform database creation
# The POSTGRES_USER will have superuser privileges if POSTGRES_DB is not set in env.
# Check if database exists
DB_EXISTS=$(psql -v ON_ERROR_STOP=0 --username "$OWNER_USER" --dbname "postgres" -tAc "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME'")

if [ "$DB_EXISTS" = "1" ]; then
    echo "Database '$DB_NAME' already exists."
    # Optionally, ensure correct owner if it exists but has wrong owner
    # psql -v ON_ERROR_STOP=1 --username "$OWNER_USER" --dbname "postgres" -c "ALTER DATABASE \"$DB_NAME\" OWNER TO \"$OWNER_USER\";"
else
    echo "Database '$DB_NAME' does not exist. Creating..."
    psql -v ON_ERROR_STOP=1 --username "$OWNER_USER" --dbname "postgres" -c "CREATE DATABASE \"$DB_NAME\" OWNER \"$OWNER_USER\";"
    echo "Database '$DB_NAME' created and owned by '$OWNER_USER'."
fi

echo "Database '$DB_NAME' setup complete. Granting privileges..."

# Grant all privileges on this new database to the user.
# While owner usually has them, this is explicit.
psql -v ON_ERROR_STOP=1 --username "$OWNER_USER" --dbname "$DB_NAME" <<-EOSQL
    GRANT ALL PRIVILEGES ON DATABASE "$DB_NAME" TO "$OWNER_USER";
    -- Ensure the user can create tables, etc., in the public schema of the new database
    GRANT ALL ON SCHEMA public TO "$OWNER_USER";
EOSQL

echo "Privileges granted for database '$DB_NAME' to user '$OWNER_USER'."
echo "Database '$DB_NAME' initialization (creation and privileges) complete."
echo "Alembic migrations will be handled by the API service for this database."
echo "Custom init script 01_init.sh finished."