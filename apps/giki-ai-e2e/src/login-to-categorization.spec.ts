import { test, expect } from '@playwright/test';
import path from 'path';

/**
 * E2E Test: Journey from Login to Schema Interpretation
 * 
 * This test validates the critical user journey from authentication
 * through file upload to successful AI schema interpretation.
 * 
 * Key checkpoints:
 * 1. Successful authentication
 * 2. Navigate to upload page
 * 3. File upload completion
 * 4. AI schema interpretation
 * 5. Confidence scores returned
 * 
 * Note: Processing page navigation and full categorization are tested separately
 * as they depend on the enhanced upload workflow orchestration.
 */

const TEST_USER = {
  email: '<EMAIL>',
  password: 'GikiTest2025Secure'
};

const TEST_FILE = path.join(
  __dirname,
  '../../../libs/test-data/mis-testing/quick-setup/test_transactions_uncategorized.xlsx'
);

test.describe('Login to Schema Interpretation Journey', () => {
  test.beforeEach(async ({ page }) => {
    // Set up request interceptors for debugging
    page.on('response', response => {
      if (response.status() >= 400) {
        console.error(`❌ API Error: ${response.status()} - ${response.url()}`);
      }
    });

    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.error(`❌ Browser Error: ${msg.text()}`);
      }
    });
  });

  test('Complete journey with performance metrics', async ({ page }) => {
    const journey = {
      startTime: Date.now(),
      checkpoints: {} as Record<string, number>
    };

    // Checkpoint 1: Navigate to login
    console.log('🚀 Starting journey: Login to First Categorization');
    await page.goto('http://localhost:4200/login');
    journey.checkpoints.pageLoad = Date.now() - journey.startTime;

    // Verify login page loaded
    await expect(page).toHaveTitle(/giki\.ai|Login/);
    const loginForm = page.locator('form').filter({ hasText: /sign in|log in/i });
    await expect(loginForm).toBeVisible();

    // Checkpoint 2: Perform authentication
    console.log('🔐 Authenticating user...');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    
    // Click login and wait for navigation
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle' }),
      page.click('button[type="submit"]')
    ]);
    journey.checkpoints.authenticated = Date.now() - journey.startTime;

    // Verify successful login
    await expect(page).not.toHaveURL(/login/);
    console.log(`✅ Authentication successful (${journey.checkpoints.authenticated}ms)`);

    // Checkpoint 3: Navigate to upload
    console.log('📤 Navigating to upload...');
    
    // Check if we're already on upload page, otherwise navigate
    if (!page.url().includes('/upload')) {
      // Look for upload link/button
      const uploadLink = page.locator('a, button').filter({ hasText: /upload|import/i }).first();
      if (await uploadLink.isVisible()) {
        await uploadLink.click();
      } else {
        await page.goto('http://localhost:4200/upload');
      }
    }

    await page.waitForLoadState('networkidle');
    journey.checkpoints.uploadPageReady = Date.now() - journey.startTime;

    // Verify upload interface
    const uploadArea = page.locator('.upload-area, .dropzone, [data-testid="upload-dropzone"]').first();
    await expect(uploadArea).toBeVisible({ timeout: 10000 });
    console.log(`✅ Upload page ready (${journey.checkpoints.uploadPageReady}ms)`);

    // Checkpoint 4: Upload file
    console.log('📁 Uploading test file...');
    const fileInput = page.locator('input[type="file"]');
    
    // Set up response listener for upload
    const uploadResponse = page.waitForResponse(
      response => response.url().includes('/upload') && response.status() === 200,
      { timeout: 60000 }
    );

    await fileInput.setInputFiles(TEST_FILE);
    await uploadResponse;
    journey.checkpoints.fileUploaded = Date.now() - journey.startTime;
    console.log(`✅ File uploaded successfully (${journey.checkpoints.fileUploaded}ms)`);

    // Checkpoint 5: Verify schema interpretation successful
    console.log('🤖 Verifying AI schema interpretation...');
    
    // Check if schema endpoint was called successfully
    const schemaResponse = await page.waitForResponse(
      response => response.url().includes('/schema') && response.status() === 200,
      { timeout: 15000 }
    );
    
    if (schemaResponse) {
      console.log('✅ Schema interpretation successful');
      journey.checkpoints.schemaInterpreted = Date.now() - journey.startTime;
      
      // Verify the response contains expected data
      const schemaData = await schemaResponse.json();
      
      // The response has a nested structure with the data in a 'data' field
      const interpretationData = schemaData.data || schemaData;
      
      // Verify the actual response structure
      expect(interpretationData).toHaveProperty('column_mappings');
      expect(interpretationData).toHaveProperty('overall_confidence');
      expect(interpretationData).toHaveProperty('interpretation_summary');
      expect(interpretationData).toHaveProperty('upload_id');
      
      console.log('📊 Schema detection confidence:', interpretationData.overall_confidence);
      console.log('📝 Interpretation summary:', interpretationData.interpretation_summary);
      console.log('✅ Integration test successful - Upload and schema interpretation completed');
    }

    journey.checkpoints.complete = Date.now() - journey.startTime;

    // Summary
    console.log('\n📊 Journey Summary:');
    console.log(`  Total time: ${journey.checkpoints.complete}ms`);
    console.log('  Checkpoints:');
    Object.entries(journey.checkpoints).forEach(([checkpoint, time]) => {
      console.log(`    - ${checkpoint}: ${time}ms`);
    });

    // Performance assertions
    expect(journey.checkpoints.complete).toBeLessThan(180000); // Under 3 minutes
    expect(journey.checkpoints.authenticated).toBeLessThan(5000); // Auth under 5s
    expect(journey.checkpoints.schemaInterpreted).toBeLessThan(30000); // Schema interpretation under 30s
  });

  test('Verify real-time updates during categorization', async ({ page }) => {
    // Quick login
    await page.goto('http://localhost:4200/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await Promise.all([
      page.waitForNavigation(),
      page.click('button[type="submit"]')
    ]);

    // Navigate to upload
    await page.goto('http://localhost:4200/upload');
    
    // Upload file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(TEST_FILE);

    // Monitor real-time updates
    console.log('📡 Monitoring real-time updates...');
    
    const updates: string[] = [];
    
    // Set up mutation observer to track UI changes
    await page.evaluate(() => {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' || mutation.type === 'characterData') {
            const target = mutation.target as HTMLElement;
            if (target.textContent?.includes('%') || 
                target.textContent?.includes('Processing') ||
                target.textContent?.includes('Categorizing')) {
              (window as any).updates = (window as any).updates || [];
              (window as any).updates.push({
                time: Date.now(),
                text: target.textContent
              });
            }
          }
        });
      });

      observer.observe(document.body, {
        childList: true,
        characterData: true,
        subtree: true
      });
    });

    // Wait for processing to complete
    await page.waitForSelector('.categorization-complete, .results-ready', { 
      timeout: 120000 
    });

    // Retrieve captured updates
    const capturedUpdates = await page.evaluate(() => (window as any).updates || []);
    
    console.log(`📊 Captured ${capturedUpdates.length} real-time updates`);
    expect(capturedUpdates.length).toBeGreaterThan(0);
    
    // Verify updates show progression
    const progressUpdates = capturedUpdates.filter((u: any) => 
      u.text.match(/\d+%/) || u.text.includes('Processing')
    );
    
    expect(progressUpdates.length).toBeGreaterThan(0);
    console.log('✅ Real-time updates verified');
  });

  test('Verify error recovery during journey', async ({ page }) => {
    // Test network interruption recovery
    console.log('🛡️ Testing error recovery...');

    // Login
    await page.goto('http://localhost:4200/login');
    await page.fill('input[type="email"]', TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await Promise.all([
      page.waitForNavigation(),
      page.click('button[type="submit"]')
    ]);

    // Navigate to upload
    await page.goto('http://localhost:4200/upload');

    // Simulate network failure during upload
    await page.route('**/api/v1/files/upload', route => {
      route.abort('failed');
    });

    // Try to upload
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(TEST_FILE);

    // Verify error message appears
    const errorMessage = page.locator('.error, .alert-error, [role="alert"]');
    await expect(errorMessage).toBeVisible({ timeout: 10000 });
    
    console.log('✅ Error message displayed correctly');

    // Restore network and verify retry works
    await page.unroute('**/api/v1/files/upload');
    
    // Look for retry button or re-upload
    const retryButton = page.locator('button').filter({ hasText: /retry|try again/i });
    if (await retryButton.isVisible()) {
      await retryButton.click();
    } else {
      // Re-upload file
      await fileInput.setInputFiles(TEST_FILE);
    }

    // Verify successful upload after retry
    await page.waitForResponse(
      response => response.url().includes('/upload') && response.status() === 200,
      { timeout: 30000 }
    );

    console.log('✅ Error recovery successful');
  });
});

// Accessibility test for the journey
test.describe('Accessibility', () => {
  test('Journey is keyboard navigable', async ({ page }) => {
    console.log('⌨️ Testing keyboard navigation...');

    await page.goto('http://localhost:4200/login');

    // Tab through login form
    await page.keyboard.press('Tab'); // Focus email
    await page.keyboard.type(TEST_USER.email);
    await page.keyboard.press('Tab'); // Focus password
    await page.keyboard.type(TEST_USER.password);
    await page.keyboard.press('Tab'); // Focus submit
    await page.keyboard.press('Enter'); // Submit

    await page.waitForNavigation();
    console.log('✅ Login via keyboard successful');

    // Navigate to upload via keyboard
    await page.keyboard.press('Tab'); // Start tabbing through navigation
    
    // Find upload link by tabbing
    let uploadFocused = false;
    for (let i = 0; i < 20; i++) {
      await page.keyboard.press('Tab');
      const focused = await page.evaluate(() => {
        const el = document.activeElement;
        return el?.textContent?.toLowerCase().includes('upload');
      });
      if (focused) {
        uploadFocused = true;
        break;
      }
    }

    expect(uploadFocused).toBeTruthy();
    await page.keyboard.press('Enter');
    
    console.log('✅ Navigation via keyboard successful');
  });
});