import { test, expect } from '@playwright/test';
import path from 'path';

/**
 * Comprehensive Integration Tests for All Key Workflows
 * 
 * This test suite validates the integration between all major components:
 * 1. Authentication & Authorization
 * 2. File Upload & Processing
 * 3. AI Categorization & Learning
 * 4. Review & Correction Workflow
 * 5. Report Generation
 * 6. Export Functionality
 * 7. Agent Panel Integration
 * 8. Real-time Updates
 * 9. Progressive Enhancement
 * 10. Multi-user Scenarios
 */

// Test configuration
const BASE_URL = process.env.APP_BASE_URL || 'http://localhost:4200';
const API_URL = process.env.API_BASE_URL || 'http://localhost:8000';

// Test users for different roles
const TEST_USERS = {
  owner: {
    email: '<EMAIL>',
    password: 'GikiTest2025Secure',
    role: 'owner'
  },
  accountant: {
    email: '<EMAIL>',
    password: 'GikiTest2025Secure',
    role: 'accountant'
  },
  bookkeeper: {
    email: '<EMAIL>',
    password: 'GikiTest2025Secure',
    role: 'bookkeeper'
  },
  viewer: {
    email: '<EMAIL>',
    password: 'GikiTest2025Secure',
    role: 'viewer'
  }
};

// Test data files
const TEST_FILES = {
  quickSetup: path.join(__dirname, '../../../libs/test-data/mis-testing/quick-setup/test_transactions_uncategorized.xlsx'),
  historical: path.join(__dirname, '../../../libs/test-data/mis-testing/historical-enhancement/historical_transactions_with_patterns.xlsx'),
  schema: path.join(__dirname, '../../../libs/test-data/mis-testing/schema-enhancement/gl_code_reference.xlsx')
};

// Helper functions
async function login(page: any, user: typeof TEST_USERS.owner) {
  await page.goto(`${BASE_URL}/login`);
  await page.fill('input[type="email"]', user.email);
  await page.fill('input[type="password"]', user.password);
  await Promise.all([
    page.waitForNavigation(),
    page.click('button[type="submit"]')
  ]);
}

async function uploadFile(page: any, filePath: string) {
  const fileInput = page.locator('input[type="file"]');
  await fileInput.setInputFiles(filePath);
  await page.waitForResponse(
    (response: any) => response.url().includes('/upload') && response.status() === 200,
    { timeout: 60000 }
  );
}

test.describe('Authentication & Authorization Workflow', () => {
  test('Role-based access control', async ({ page }) => {
    console.log('🔐 Testing role-based access control...');

    // Test each user role
    for (const [roleName, user] of Object.entries(TEST_USERS)) {
      console.log(`\n👤 Testing ${roleName} role...`);
      
      // Login
      await login(page, user);
      
      // Verify dashboard access
      await expect(page).toHaveURL(/dashboard|home/);
      
      // Check role-specific features
      if (roleName === 'owner' || roleName === 'accountant') {
        // Should see reports menu
        const reportsMenu = page.locator('a, button').filter({ hasText: /reports/i });
        await expect(reportsMenu).toBeVisible();
      }
      
      if (roleName === 'viewer') {
        // Should NOT see upload option
        const uploadButton = page.locator('a, button').filter({ hasText: /upload/i });
        await expect(uploadButton).not.toBeVisible();
      }
      
      // Logout
      await page.click('[data-testid="user-menu"], .user-menu');
      await page.click('button:has-text("Logout"), a:has-text("Logout")');
      
      console.log(`✅ ${roleName} role verified`);
    }
  });

  test('Session persistence and timeout', async ({ page }) => {
    console.log('⏱️ Testing session persistence...');
    
    // Login
    await login(page, TEST_USERS.owner);
    
    // Store auth token
    const authToken = await page.evaluate(() => {
      return localStorage.getItem('accessToken') || sessionStorage.getItem('accessToken');
    });
    
    expect(authToken).toBeTruthy();
    
    // Navigate away and back
    await page.goto('https://google.com');
    await page.goto(BASE_URL);
    
    // Should still be logged in
    await expect(page).not.toHaveURL(/login/);
    console.log('✅ Session persisted across navigation');
  });
});

test.describe('File Upload & Processing Workflow', () => {
  test('Multiple file format support', async ({ page }) => {
    console.log('📁 Testing file format support...');
    
    await login(page, TEST_USERS.owner);
    await page.goto(`${BASE_URL}/upload`);
    
    // Test Excel file
    await uploadFile(page, TEST_FILES.quickSetup);
    console.log('✅ Excel file uploaded successfully');
    
    // Wait for processing to complete
    await page.waitForSelector('.processing-complete, .results', { timeout: 60000 });
  });

  test('Large file handling', async ({ page }) => {
    console.log('📊 Testing large file handling...');
    
    await login(page, TEST_USERS.owner);
    await page.goto(`${BASE_URL}/upload`);
    
    // Upload historical data (larger file)
    await uploadFile(page, TEST_FILES.historical);
    
    // Monitor progress updates
    const progressUpdates: number[] = [];
    
    // Poll for progress
    let complete = false;
    while (!complete) {
      const progressText = await page.textContent('.progress, .processing-status');
      if (progressText) {
        const match = progressText.match(/(\d+)%/);
        if (match) {
          progressUpdates.push(parseInt(match[1]));
        }
        complete = progressText.includes('100%') || progressText.includes('Complete');
      }
      if (!complete) {
        await page.waitForTimeout(1000);
      }
    }
    
    // Verify progress updates were incremental
    expect(progressUpdates.length).toBeGreaterThan(0);
    console.log(`✅ Received ${progressUpdates.length} progress updates`);
  });
});

test.describe('AI Categorization & Learning Workflow', () => {
  test('Initial categorization accuracy', async ({ page }) => {
    console.log('🤖 Testing AI categorization...');
    
    await login(page, TEST_USERS.owner);
    await page.goto(`${BASE_URL}/upload`);
    await uploadFile(page, TEST_FILES.quickSetup);
    
    // Wait for categorization
    await page.waitForSelector('.categorization-results, .transaction-list', { 
      timeout: 120000 
    });
    
    // Check accuracy metric
    const accuracyText = await page.textContent('.accuracy-score, .accuracy-metric');
    const accuracy = parseFloat(accuracyText?.match(/(\d+(?:\.\d+)?)/)?.[1] || '0');
    
    expect(accuracy).toBeGreaterThanOrEqual(87);
    console.log(`✅ Initial accuracy: ${accuracy}%`);
  });

  test('User correction learning', async ({ page }) => {
    console.log('🎯 Testing learning from corrections...');
    
    await login(page, TEST_USERS.owner);
    
    // Navigate to transactions
    await page.goto(`${BASE_URL}/transactions`);
    await page.waitForSelector('.transaction-row, .transaction-item');
    
    // Find a transaction to correct
    const firstTransaction = page.locator('.transaction-row, .transaction-item').first();
    await firstTransaction.click();
    
    // Change category
    const categoryDropdown = page.locator('select.category-select, [data-testid="category-dropdown"]');
    if (await categoryDropdown.isVisible()) {
      const currentCategory = await categoryDropdown.inputValue();
      
      // Select a different category
      const options = await categoryDropdown.locator('option').all();
      for (const option of options) {
        const value = await option.getAttribute('value');
        if (value && value !== currentCategory) {
          await categoryDropdown.selectOption(value);
          break;
        }
      }
      
      // Save correction
      await page.click('button:has-text("Save"), button:has-text("Update")');
      
      // Verify correction was saved
      await page.waitForResponse(
        response => response.url().includes('/transaction') && response.status() === 200
      );
      
      console.log('✅ Correction saved and AI learning triggered');
    }
  });
});

test.describe('Review & Correction Workflow', () => {
  test('Bulk review interface', async ({ page }) => {
    console.log('📝 Testing bulk review...');
    
    await login(page, TEST_USERS.accountant);
    await page.goto(`${BASE_URL}/review`);
    
    // Check for review queue
    const reviewItems = await page.locator('.review-item, .transaction-review').count();
    console.log(`📊 Found ${reviewItems} items for review`);
    
    if (reviewItems > 0) {
      // Test bulk selection
      const selectAll = page.locator('input[type="checkbox"]').first();
      await selectAll.check();
      
      // Verify all items selected
      const checkedBoxes = await page.locator('input[type="checkbox"]:checked').count();
      expect(checkedBoxes).toBeGreaterThan(1);
      
      console.log('✅ Bulk selection working');
    }
  });

  test('Approval workflow', async ({ page }) => {
    console.log('✅ Testing approval workflow...');
    
    await login(page, TEST_USERS.accountant);
    await page.goto(`${BASE_URL}/review`);
    
    const firstReviewItem = page.locator('.review-item, .transaction-review').first();
    if (await firstReviewItem.isVisible()) {
      // Approve first item
      await firstReviewItem.locator('button:has-text("Approve")').click();
      
      // Verify approval
      await page.waitForResponse(
        response => response.url().includes('/approve') && response.status() === 200
      );
      
      console.log('✅ Approval workflow completed');
    }
  });
});

test.describe('Report Generation Workflow', () => {
  test('Generate standard reports', async ({ page }) => {
    console.log('📊 Testing report generation...');
    
    await login(page, TEST_USERS.owner);
    await page.goto(`${BASE_URL}/reports`);
    
    // Test each report type
    const reportTypes = ['Income Statement', 'Expense Summary', 'Category Breakdown'];
    
    for (const reportType of reportTypes) {
      const reportButton = page.locator('button, a').filter({ hasText: reportType });
      if (await reportButton.isVisible()) {
        await reportButton.click();
        
        // Wait for report to generate
        await page.waitForSelector('.report-content, .report-data, canvas', {
          timeout: 30000
        });
        
        console.log(`✅ ${reportType} generated successfully`);
        
        // Go back to reports list
        await page.goto(`${BASE_URL}/reports`);
      }
    }
  });

  test('Custom report builder', async ({ page }) => {
    console.log('🔧 Testing custom report builder...');
    
    await login(page, TEST_USERS.accountant);
    await page.goto(`${BASE_URL}/reports/custom`);
    
    // If custom report builder exists
    if (page.url().includes('custom')) {
      // Select date range
      const dateFrom = page.locator('input[type="date"]').first();
      const dateTo = page.locator('input[type="date"]').last();
      
      if (await dateFrom.isVisible()) {
        await dateFrom.fill('2024-01-01');
        await dateTo.fill('2024-12-31');
      }
      
      // Generate report
      await page.click('button:has-text("Generate")');
      
      // Wait for results
      await page.waitForSelector('.report-results, .custom-report', {
        timeout: 30000
      });
      
      console.log('✅ Custom report generated');
    }
  });
});

test.describe('Export Functionality Workflow', () => {
  test('Export to multiple formats', async ({ page }) => {
    console.log('💾 Testing export functionality...');
    
    await login(page, TEST_USERS.owner);
    await page.goto(`${BASE_URL}/transactions`);
    
    // Open export dialog
    const exportButton = page.locator('button:has-text("Export")');
    await exportButton.click();
    
    // Verify export formats
    const formats = ['Excel', 'CSV', 'QuickBooks', 'PDF'];
    for (const format of formats) {
      const formatOption = page.locator(`button:has-text("${format}"), label:has-text("${format}")`);
      await expect(formatOption).toBeVisible();
    }
    
    console.log('✅ All export formats available');
    
    // Test Excel export
    const downloadPromise = page.waitForEvent('download');
    await page.click('button:has-text("Excel")');
    const download = await downloadPromise;
    
    expect(download.suggestedFilename()).toContain('.xlsx');
    console.log('✅ Excel export successful');
  });
});

test.describe('Agent Panel Integration Workflow', () => {
  test('Natural language queries', async ({ page }) => {
    console.log('💬 Testing agent panel...');
    
    await login(page, TEST_USERS.owner);
    
    // Open agent panel
    await page.click('button[aria-label*="Assistant"], button:has-text("AI"), .agent-toggle');
    await page.waitForSelector('.agent-panel, .chat-panel');
    
    // Test queries
    const queries = [
      "What's my total spending this month?",
      "Show me transactions over $1000",
      "Generate expense report"
    ];
    
    for (const query of queries) {
      const input = page.locator('.chat-input input, input[placeholder*="Type"]');
      await input.fill(query);
      await input.press('Enter');
      
      // Wait for response
      await page.waitForSelector('.agent-response, .chat-message.agent', {
        timeout: 30000
      });
      
      console.log(`✅ Query processed: "${query}"`);
      
      // Small delay between queries
      await page.waitForTimeout(1000);
    }
  });

  test('Context-aware assistance', async ({ page }) => {
    console.log('🧠 Testing context awareness...');
    
    await login(page, TEST_USERS.owner);
    
    // Go to specific page
    await page.goto(`${BASE_URL}/upload`);
    
    // Open agent
    await page.click('.agent-toggle, button:has-text("AI")');
    
    // Ask context-specific question
    const input = page.locator('.chat-input input');
    await input.fill("Help me with uploading");
    await input.press('Enter');
    
    // Verify response is context-aware
    const response = await page.waitForSelector('.agent-response:last-child');
    const responseText = await response.textContent();
    
    expect(responseText?.toLowerCase()).toContain('upload');
    console.log('✅ Agent provides context-aware help');
  });
});

test.describe('Real-time Updates Workflow', () => {
  test('WebSocket connection for live updates', async ({ page }) => {
    console.log('🔄 Testing real-time updates...');
    
    await login(page, TEST_USERS.owner);
    
    // Monitor WebSocket connections
    const wsConnected = await page.evaluate(() => {
      return new Promise((resolve) => {
        const originalWS = window.WebSocket;
        window.WebSocket = new Proxy(originalWS, {
          construct(target, args) {
            const ws = new target(...args);
            ws.addEventListener('open', () => resolve(true));
            return ws;
          }
        });
        
        // Timeout after 5 seconds
        setTimeout(() => resolve(false), 5000);
      });
    });
    
    if (wsConnected) {
      console.log('✅ WebSocket connection established');
    } else {
      console.log('⚠️ No WebSocket connection detected (may be using polling)');
    }
  });
});

test.describe('Progressive Enhancement Workflow', () => {
  test('Apply historical enhancement', async ({ page }) => {
    console.log('📈 Testing progressive enhancement...');
    
    await login(page, TEST_USERS.owner);
    
    // Check current accuracy
    await page.goto(`${BASE_URL}/dashboard`);
    const initialAccuracy = await page.textContent('.accuracy-metric');
    const initialValue = parseFloat(initialAccuracy?.match(/(\d+)/)?.[1] || '0');
    
    // Go to enhancements
    await page.goto(`${BASE_URL}/settings/enhancements`);
    
    // Upload historical data
    const enhanceButton = page.locator('button:has-text("Add Historical Data")');
    if (await enhanceButton.isVisible()) {
      await enhanceButton.click();
      await uploadFile(page, TEST_FILES.historical);
      
      // Wait for enhancement to process
      await page.waitForSelector('.enhancement-complete', { timeout: 120000 });
      
      // Check new accuracy
      const newAccuracy = await page.textContent('.accuracy-metric');
      const newValue = parseFloat(newAccuracy?.match(/(\d+)/)?.[1] || '0');
      
      expect(newValue).toBeGreaterThan(initialValue);
      console.log(`✅ Accuracy improved from ${initialValue}% to ${newValue}%`);
    }
  });
});

test.describe('Multi-user Scenarios', () => {
  test('Concurrent user actions', async ({ browser }) => {
    console.log('👥 Testing multi-user scenarios...');
    
    // Create two browser contexts
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    // Login as different users
    await login(page1, TEST_USERS.owner);
    await login(page2, TEST_USERS.accountant);
    
    // Perform concurrent actions
    await Promise.all([
      page1.goto(`${BASE_URL}/transactions`),
      page2.goto(`${BASE_URL}/review`)
    ]);
    
    // Verify both users can work simultaneously
    await expect(page1).toHaveURL(/transactions/);
    await expect(page2).toHaveURL(/review/);
    
    console.log('✅ Multi-user concurrent access verified');
    
    // Cleanup
    await context1.close();
    await context2.close();
  });
});

// Performance benchmark for integrated workflows
test.describe('Performance Benchmarks', () => {
  test('End-to-end workflow performance', async ({ page }) => {
    console.log('⚡ Testing integrated workflow performance...');
    
    const metrics = {
      login: 0,
      upload: 0,
      categorization: 0,
      total: 0
    };
    
    const startTime = Date.now();
    
    // Login
    const loginStart = Date.now();
    await login(page, TEST_USERS.owner);
    metrics.login = Date.now() - loginStart;
    
    // Upload
    const uploadStart = Date.now();
    await page.goto(`${BASE_URL}/upload`);
    await uploadFile(page, TEST_FILES.quickSetup);
    metrics.upload = Date.now() - uploadStart;
    
    // Wait for categorization
    const categorizationStart = Date.now();
    await page.waitForSelector('.categorization-complete', { timeout: 120000 });
    metrics.categorization = Date.now() - categorizationStart;
    
    metrics.total = Date.now() - startTime;
    
    console.log('📊 Performance metrics:');
    console.log(`  - Login: ${metrics.login}ms`);
    console.log(`  - Upload: ${metrics.upload}ms`);
    console.log(`  - Categorization: ${metrics.categorization}ms`);
    console.log(`  - Total: ${metrics.total}ms`);
    
    // Assert performance targets
    expect(metrics.total).toBeLessThan(180000); // Under 3 minutes
  });
});