{"name": "giki-ai-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/giki-ai-app", "projectType": "application", "tags": [], "implicitDependencies": [], "targets": {"serve": {"executor": "nx:run-commands", "options": {"commands": ["./scripts/nx/ensure-postgres.sh", "./scripts/nx/ensure-redis.sh", "./scripts/nx/clear-ports.sh clear-frontend", "cd apps/giki-ai-app && nohup env VITE_API_BASE_URL=http://localhost:8000/api/v1 pnpm vite dev --port 4200 --host 0.0.0.0 --strictPort > ../../logs/frontend.log 2>&1 &"], "parallel": false, "cwd": "{workspaceRoot}"}}, "serve:stable": {"executor": "nx:run-commands", "options": {"commands": ["./scripts/nx/clear-ports.sh clear-frontend", "cd apps/giki-ai-app && pnpm vite build && pnpm vite preview --port 4200 --host 0.0.0.0 --strictPort"], "parallel": false, "cwd": "{workspaceRoot}"}}, "build": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-app && pnpm vite build", "cwd": "{workspaceRoot}"}, "configurations": {"production": {"command": "cd apps/giki-ai-app && pnpm vite build", "cwd": "{workspaceRoot}"}}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/giki-ai-app/**/*.{ts,tsx,js,jsx}"], "maxWarnings": -1, "fix": true}}, "test": {"executor": "nx:run-commands", "options": {"command": "cd apps/giki-ai-app && vitest run", "cwd": "{workspaceRoot}"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "./scripts/nx/deploy-frontend.sh", "cwd": "{workspaceRoot}"}}}}