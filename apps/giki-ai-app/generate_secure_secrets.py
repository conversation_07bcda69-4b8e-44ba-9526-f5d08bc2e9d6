#!/usr/bin/env python3
"""Generate cryptographically secure secrets for JWT and other sensitive configurations."""

import secrets
import string
import base64

def generate_secure_secret(length=64):
    """Generate a cryptographically secure random secret."""
    # Use secrets module for cryptographic security
    alphabet = string.ascii_letters + string.digits + string.punctuation
    # Remove problematic characters for shell/env usage
    safe_alphabet = alphabet.replace('"', '').replace("'", '').replace('\\', '').replace('`', '')
    return ''.join(secrets.choice(safe_alphabet) for _ in range(length))

def generate_base64_secret(bytes_length=32):
    """Generate a base64-encoded cryptographically secure secret."""
    random_bytes = secrets.token_bytes(bytes_length)
    return base64.urlsafe_b64encode(random_bytes).decode('utf-8')

def main():
    print("=== Secure Secrets Generator for giki.ai ===\n")
    
    # Generate JWT secrets
    jwt_secret_dev = generate_secure_secret(64)
    jwt_secret_prod = generate_secure_secret(64)
    
    # Generate refresh token secrets
    refresh_secret_dev = generate_secure_secret(64)
    refresh_secret_prod = generate_secure_secret(64)
    
    # Generate API keys
    api_key_dev = generate_base64_secret(32)
    api_key_prod = generate_base64_secret(32)
    
    # Generate encryption keys
    encryption_key_dev = generate_base64_secret(32)
    encryption_key_prod = generate_base64_secret(32)
    
    print("### Development Environment Secrets ###")
    print(f"JWT_SECRET_KEY={jwt_secret_dev}")
    print(f"JWT_REFRESH_SECRET_KEY={refresh_secret_dev}")
    print(f"API_SECRET_KEY={api_key_dev}")
    print(f"ENCRYPTION_KEY={encryption_key_dev}")
    
    print("\n### Production Environment Secrets ###")
    print(f"JWT_SECRET_KEY={jwt_secret_prod}")
    print(f"JWT_REFRESH_SECRET_KEY={refresh_secret_prod}")
    print(f"API_SECRET_KEY={api_key_prod}")
    print(f"ENCRYPTION_KEY={encryption_key_prod}")
    
    print("\n### Additional Security Recommendations ###")
    print("1. Store these secrets in a secure password manager")
    print("2. Use environment-specific secret management (e.g., Google Secret Manager)")
    print("3. Rotate secrets regularly (recommended: every 90 days)")
    print("4. Never commit actual secrets to version control")
    print("5. Use .env.example files with placeholder values")

if __name__ == "__main__":
    main()