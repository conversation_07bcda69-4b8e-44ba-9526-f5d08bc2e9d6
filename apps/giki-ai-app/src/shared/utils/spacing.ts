/**
 * Spacing System - Standardized spacing definitions matching mockup design
 * Use these consistent spacing values throughout the application
 */

export const spacing = {
  1: '0.25rem',   // 4px
  2: '0.5rem',    // 8px
  3: '0.75rem',   // 12px
  4: '1rem',      // 16px
  5: '1.25rem',   // 20px
  6: '1.5rem',    // 24px
  8: '2rem',      // 32px
  10: '2.5rem',   // 40px
  12: '3rem',     // 48px
  16: '4rem',     // 64px
  20: '5rem',     // 80px
} as const;

// Common spacing patterns for consistent usage
export const spacingClasses = {
  // Padding
  p1: 'p-1',     // 0.25rem
  p2: 'p-2',     // 0.5rem
  p3: 'p-3',     // 0.75rem
  p4: 'p-4',     // 1rem
  p5: 'p-5',     // 1.25rem
  p6: 'p-6',     // 1.5rem
  p8: 'p-8',     // 2rem
  p10: 'p-10',   // 2.5rem
  p12: 'p-12',   // 3rem
  p16: 'p-16',   // 4rem
  p20: 'p-20',   // 5rem

  // Margin
  m1: 'm-1',     // 0.25rem
  m2: 'm-2',     // 0.5rem
  m3: 'm-3',     // 0.75rem
  m4: 'm-4',     // 1rem
  m5: 'm-5',     // 1.25rem
  m6: 'm-6',     // 1.5rem
  m8: 'm-8',     // 2rem
  m10: 'm-10',   // 2.5rem
  m12: 'm-12',   // 3rem
  m16: 'm-16',   // 4rem
  m20: 'm-20',   // 5rem

  // Gap
  gap1: 'gap-1',   // 0.25rem
  gap2: 'gap-2',   // 0.5rem
  gap3: 'gap-3',   // 0.75rem
  gap4: 'gap-4',   // 1rem
  gap5: 'gap-5',   // 1.25rem
  gap6: 'gap-6',   // 1.5rem
  gap8: 'gap-8',   // 2rem
  gap10: 'gap-10', // 2.5rem
  gap12: 'gap-12', // 3rem
  gap16: 'gap-16', // 4rem
  gap20: 'gap-20', // 5rem
} as const;

// Helper functions for consistent spacing
export const getSpacing = (size: keyof typeof spacing) => spacing[size];

export const getSpacingStyle = (property: 'padding' | 'margin' | 'gap', size: keyof typeof spacing) => ({
  [property]: spacing[size],
});

// Common layout spacing patterns from mockups
export const layoutSpacing = {
  // Container padding
  containerPadding: spacing[6],      // 1.5rem / 24px
  containerPaddingLarge: spacing[8], // 2rem / 32px
  
  // Section spacing
  sectionGap: spacing[8],            // 2rem / 32px
  sectionGapLarge: spacing[16],      // 4rem / 64px
  
  // Component spacing
  componentGap: spacing[4],          // 1rem / 16px
  componentGapSmall: spacing[2],     // 0.5rem / 8px
  
  // Navigation spacing
  navPadding: spacing[4],            // 1rem / 16px
  navGap: spacing[3],                // 0.75rem / 12px
  
  // Form spacing
  formGap: spacing[4],               // 1rem / 16px
  formPadding: spacing[5],           // 1.25rem / 20px
} as const;