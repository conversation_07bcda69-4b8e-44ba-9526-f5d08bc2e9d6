import { useEffect, useRef } from 'react';

/**
 * Accessibility utility functions and hooks for giki.ai
 * Ensures WCAG 2.1 AA compliance across the application
 */

/**
 * Announce content to screen readers using ARIA live regions
 */
export const announce = (
  message: string,
  priority: 'polite' | 'assertive' = 'polite',
) => {
  const announcement = document.createElement('div');
  announcement.setAttribute('role', 'status');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.style.position = 'absolute';
  announcement.style.left = '-10000px';
  announcement.style.width = '1px';
  announcement.style.height = '1px';
  announcement.style.overflow = 'hidden';

  announcement.textContent = message;
  document.body.appendChild(announcement);

  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

/**
 * Focus trap hook for modals, dialogs, and overlays
 */
export const useFocusTrap = (isActive: boolean) => {
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!isActive || !elementRef.current) return;

    const element = elementRef.current;
    const focusableElements = element.querySelectorAll(
      'a[href], button:not([disabled]), textarea:not([disabled]), input:not([disabled]), select:not([disabled]), [tabindex]:not([tabindex="-1"])',
    );

    const firstFocusable = focusableElements[0] as HTMLElement;
    const lastFocusable = focusableElements[
      focusableElements.length - 1
    ] as HTMLElement;

    // Focus first element
    firstFocusable?.focus();

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstFocusable) {
          e.preventDefault();
          lastFocusable?.focus();
        }
      } else {
        if (document.activeElement === lastFocusable) {
          e.preventDefault();
          firstFocusable?.focus();
        }
      }
    };

    element.addEventListener('keydown', handleKeyDown);

    return () => {
      element.removeEventListener('keydown', handleKeyDown);
    };
  }, [isActive]);

  return elementRef;
};

/**
 * Generate unique IDs for form elements and their labels
 */
export const useId = (prefix: string = 'giki') => {
  const ref = useRef<string>();

  if (!ref.current) {
    ref.current = `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  }

  return ref.current;
};

/**
 * Format currency for screen readers
 */
export const formatCurrencyForScreenReader = (
  amount: number,
  currency: string = 'USD',
) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  });

  const formatted = formatter.format(amount);
  const isNegative = amount < 0;

  return {
    visual: formatted,
    screenReader: `${isNegative ? 'negative ' : ''}${Math.abs(amount)} ${currency === 'USD' ? 'dollars' : currency}`,
  };
};

/**
 * Skip to main content link helper
 */
export const skipToMain = () => {
  const main = document.querySelector('main');
  if (main) {
    main.tabIndex = -1;
    main.focus();
    main.removeAttribute('tabindex');
  }
};

/**
 * Keyboard navigation hook for custom components
 */
export const useKeyboardNavigation = (
  items: HTMLElement[],
  options: {
    orientation?: 'horizontal' | 'vertical' | 'both';
    loop?: boolean;
    onSelect?: (index: number) => void;
  } = {},
) => {
  const { orientation = 'vertical', loop = true, onSelect } = options;

  const handleKeyDown = (e: KeyboardEvent, currentIndex: number) => {
    let nextIndex = currentIndex;

    switch (e.key) {
      case 'ArrowDown':
        if (orientation === 'vertical' || orientation === 'both') {
          e.preventDefault();
          nextIndex = currentIndex + 1;
        }
        break;
      case 'ArrowUp':
        if (orientation === 'vertical' || orientation === 'both') {
          e.preventDefault();
          nextIndex = currentIndex - 1;
        }
        break;
      case 'ArrowRight':
        if (orientation === 'horizontal' || orientation === 'both') {
          e.preventDefault();
          nextIndex = currentIndex + 1;
        }
        break;
      case 'ArrowLeft':
        if (orientation === 'horizontal' || orientation === 'both') {
          e.preventDefault();
          nextIndex = currentIndex - 1;
        }
        break;
      case 'Home':
        e.preventDefault();
        nextIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        nextIndex = items.length - 1;
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (onSelect) onSelect(currentIndex);
        return;
      default:
        return;
    }

    // Handle wrapping
    if (loop) {
      if (nextIndex < 0) nextIndex = items.length - 1;
      if (nextIndex >= items.length) nextIndex = 0;
    } else {
      nextIndex = Math.max(0, Math.min(items.length - 1, nextIndex));
    }

    // Focus next item
    items[nextIndex]?.focus();
  };

  return handleKeyDown;
};

/**
 * High contrast mode detection
 */
export const useHighContrastMode = () => {
  const mediaQuery = window.matchMedia('(prefers-contrast: high)');
  const isHighContrast = useRef(mediaQuery.matches);

  useEffect(() => {
    const handleChange = (e: MediaQueryListEvent) => {
      isHighContrast.current = e.matches;
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return isHighContrast.current;
};

/**
 * Reduced motion preference detection
 */
export const useReducedMotion = () => {
  const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
  const prefersReducedMotion = useRef(mediaQuery.matches);

  useEffect(() => {
    const handleChange = (e: MediaQueryListEvent) => {
      prefersReducedMotion.current = e.matches;
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion.current;
};

/**
 * ARIA describedby helper for form validation
 */
export const getAriaDescribedBy = (
  fieldId: string,
  hasError: boolean,
  hasHelperText: boolean,
) => {
  const parts = [];
  if (hasHelperText) parts.push(`${fieldId}-helper`);
  if (hasError) parts.push(`${fieldId}-error`);
  return parts.length > 0 ? parts.join(' ') : undefined;
};

/**
 * Focus visible class utility
 */
export const focusClasses =
  'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background';

/**
 * Screen reader only class
 */
export const srOnly =
  'absolute left-[-10000px] top-auto w-[1px] h-[1px] overflow-hidden';
