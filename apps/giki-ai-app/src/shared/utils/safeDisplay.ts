/**
 * Safe Display Utilities for preventing NaN display issues
 * Simple, lightweight utilities for common formatting operations
 */

/**
 * Safely format a percentage value to prevent NaN display
 */
export const safePercentage = (
  value: number | null | undefined,
  decimals: number = 1,
  fallback: string = '0.0',
): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return fallback;
  }
  return value.toFixed(decimals);
};

/**
 * Safely format a number to prevent NaN display
 */
export const safeNumber = (
  value: number | null | undefined,
  decimals: number = 0,
  fallback: string = '0',
): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return fallback;
  }
  return value.toFixed(decimals);
};

/**
 * Safe division that prevents NaN results
 */
export const safeDivide = (
  numerator: number,
  denominator: number,
  fallback: number = 0,
): number => {
  if (denominator === 0 || isNaN(numerator) || isNaN(denominator)) {
    return fallback;
  }
  return numerator / denominator;
};

/**
 * Safe percentage calculation with NaN protection
 */
export const safePercentageCalc = (
  part: number,
  total: number,
  fallback: number = 0,
): number => {
  return safeDivide(part, total, fallback) * 100;
};
