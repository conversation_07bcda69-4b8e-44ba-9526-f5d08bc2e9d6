/**
 * Financial number formatting utilities for giki.ai
 * Follows enterprise financial software standards
 *
 * Enterprise Standards:
 * - Currency symbol ($) before all amounts
 * - Thousands separators (commas) for readability
 * - Consistent .XX decimal display (even for whole numbers)
 * - Right-aligned in table columns for vertical alignment
 * - Monospace/tabular number fonts for perfect alignment
 * - Proper negative number handling for financial data
 */

export interface CurrencyFormatOptions {
  currency?: string;
  locale?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  showCurrency?: boolean;
  showSign?: boolean;
  forceDecimals?: boolean; // Force .00 even for whole numbers
  compact?: boolean; // For large numbers (K, M, B)
}

/**
 * Format a number as currency following enterprise financial standards
 * - Currency symbol ($) always displayed before amounts
 * - Thousands separators (commas) for all amounts >= 1,000
 * - Consistent decimal places (.XX format, even for whole numbers)
 * - Proper negative number handling for financial data
 * - Right-aligned for tables (handled via CSS classes)
 */
export const formatCurrency = (
  amount: number | string | null | undefined,
  options: CurrencyFormatOptions = {},
): string => {
  const {
    currency = 'USD',
    locale = 'en-US',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    showCurrency = true,
    showSign = false,
    forceDecimals = true,
    compact = false,
  } = options;

  // Handle null/undefined values - always show enterprise format
  if (amount === null || amount === undefined) {
    return showCurrency ? '$0.00' : '0.00';
  }

  // Convert to number
  const numericAmount =
    typeof amount === 'string' ? parseFloat(amount) : amount;

  // Handle invalid numbers - always show enterprise format
  if (isNaN(numericAmount)) {
    return showCurrency ? '$0.00' : '0.00';
  }

  // Handle compact format for large numbers
  if (compact && Math.abs(numericAmount) >= 1000) {
    return formatCompactCurrency(numericAmount, { ...options, compact: false });
  }

  // Enterprise standard: Always show 2 decimal places
  const effectiveMinDecimals = forceDecimals ? 2 : minimumFractionDigits;
  const effectiveMaxDecimals = forceDecimals ? 2 : maximumFractionDigits;

  // Format using Intl.NumberFormat for proper localization
  const formatter = new Intl.NumberFormat(locale, {
    style: showCurrency ? 'currency' : 'decimal',
    currency: currency,
    minimumFractionDigits: effectiveMinDecimals,
    maximumFractionDigits: effectiveMaxDecimals,
  });

  // Always format the absolute value first
  let formatted = formatter.format(Math.abs(numericAmount));

  // Handle sign display for enterprise financial data
  if (numericAmount < 0) {
    // Negative amounts: Standard accounting format with minus sign before currency
    formatted = `-${formatted}`;
  } else if (showSign && numericAmount > 0) {
    // Positive amounts with explicit sign (for variance reporting)
    formatted = `+${formatted}`;
  }

  return formatted;
};

/**
 * Format currency in compact notation for large numbers (K, M, B)
 * Used for dashboard metrics and summary displays
 */
export const formatCompactCurrency = (
  amount: number | string | null | undefined,
  options: CurrencyFormatOptions = {},
): string => {
  const { currency = 'USD', locale = 'en-US', showCurrency = true } = options;

  if (amount === null || amount === undefined) {
    return showCurrency ? '$0' : '0';
  }

  const numericAmount =
    typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numericAmount)) {
    return showCurrency ? '$0' : '0';
  }

  const formatter = new Intl.NumberFormat(locale, {
    style: showCurrency ? 'currency' : 'decimal',
    currency: currency,
    notation: 'compact',
    maximumFractionDigits: 1,
  });

  return formatter.format(numericAmount);
};

/**
 * Format currency for data tables (enterprise financial standard)
 * - Always shows currency symbol ($)
 * - Always shows 2 decimal places (.XX)
 * - Right-aligned with monospace font (via CSS classes)
 * - Consistent width for perfect column alignment
 */
export const formatCurrencyForTable = (
  amount: number | string | null | undefined,
  options: CurrencyFormatOptions = {},
): string => {
  return formatCurrency(amount, {
    ...options,
    showCurrency: true,
    forceDecimals: true,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

/**
 * Format currency for dashboard metrics (with optional compact notation)
 * Used for metric cards where space is limited
 */
export const formatCurrencyForMetrics = (
  amount: number | string | null | undefined,
  options: CurrencyFormatOptions = {},
): string => {
  const numericAmount =
    typeof amount === 'string' ? parseFloat(amount) : amount;

  // Use compact format for large numbers (>= 100K)
  if (
    numericAmount !== null &&
    numericAmount !== undefined &&
    !isNaN(numericAmount)
  ) {
    if (Math.abs(numericAmount) >= 100000) {
      return formatCompactCurrency(amount, options);
    }
  }

  return formatCurrency(amount, {
    ...options,
    showCurrency: true,
    forceDecimals: true,
  });
};

/**
 * Format percentage for financial displays
 */
export const formatPercentage = (
  value: number | string | null | undefined,
  decimals: number = 1,
): string => {
  if (value === null || value === undefined) return '0.0%';

  const numericValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numericValue)) return '0.0%';

  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(numericValue / 100);
};

/**
 * Determine color class for financial amounts
 * Green for positive/income, red for negative/expenses
 */
export const getAmountColorClass = (
  amount: number | string | null | undefined,
  type?: 'income' | 'expense' | 'auto',
): string => {
  if (amount === null || amount === undefined) return 'text-muted-foreground';

  const numericAmount =
    typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numericAmount)) return 'text-muted-foreground';

  if (type === 'income') {
    return 'text-green-600 dark:text-green-400';
  } else if (type === 'expense') {
    return 'text-red-600 dark:text-red-400';
  } else {
    // Auto-detect based on sign
    return numericAmount >= 0
      ? 'text-green-600 dark:text-green-400'
      : 'text-red-600 dark:text-red-400';
  }
};

/**
 * CSS classes for enterprise financial number alignment and display
 * Ensures perfect vertical alignment in tables and consistent formatting
 */
export const FINANCIAL_TABLE_CLASSES = {
  // Main currency amounts - enterprise standard
  currency: 'text-right tabular-nums font-mono text-financial font-semibold',

  // Standard amounts without currency symbol
  amount: 'text-right tabular-nums font-mono text-financial',

  // Percentage values
  percentage: 'text-right tabular-nums font-mono text-financial',

  // Large metric displays (dashboard cards)
  currencyMetric:
    'text-right tabular-nums font-mono text-financial font-bold text-lg',

  // Small financial numbers (subtotals, etc.)
  currencySmall: 'text-right tabular-nums font-mono text-financial text-sm',

  // Financial table headers
  currencyHeader: 'text-right font-semibold text-muted-foreground text-sm',
} as const;

/**
 * Helper function to get appropriate CSS classes for financial display
 */
export const getFinancialDisplayClass = (
  type:
    | 'currency'
    | 'amount'
    | 'percentage'
    | 'metric'
    | 'small'
    | 'header' = 'currency',
): string => {
  switch (type) {
    case 'metric':
      return FINANCIAL_TABLE_CLASSES.currencyMetric;
    case 'small':
      return FINANCIAL_TABLE_CLASSES.currencySmall;
    case 'header':
      return FINANCIAL_TABLE_CLASSES.currencyHeader;
    case 'amount':
      return FINANCIAL_TABLE_CLASSES.amount;
    case 'percentage':
      return FINANCIAL_TABLE_CLASSES.percentage;
    case 'currency':
    default:
      return FINANCIAL_TABLE_CLASSES.currency;
  }
};
