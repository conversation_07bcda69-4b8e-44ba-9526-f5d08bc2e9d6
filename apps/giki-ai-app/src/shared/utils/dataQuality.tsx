/**
 * Data Quality Utilities for giki.ai
 *
 * Provides safe number formatting and data validation to prevent NaN display issues
 * and ensure professional data presentation throughout the application.
 */

import React from 'react';

export interface DataQualityOptions {
  fallbackValue?: string;
  showDataIssueLabel?: boolean;
  allowNegative?: boolean;
  decimalPlaces?: number;
}

/**
 * Safely convert any value to a valid number, preventing NaN display
 */
export const safeNumber = (value: unknown, fallback: number = 0): number => {
  if (value === null || value === undefined) return fallback;

  const num = typeof value === 'string' ? parseFloat(value) : Number(value);
  return isNaN(num) ? fallback : num;
};

/**
 * Safely format a percentage, preventing NaN% display
 */
export const safePercentage = (
  value: unknown,
  options: DataQualityOptions = {},
): string => {
  const {
    fallbackValue = '0.0%',
    showDataIssueLabel = false,
    decimalPlaces = 1,
  } = options;

  if (value === null || value === undefined) {
    return showDataIssueLabel ? 'No Data' : fallbackValue;
  }

  const num = safeNumber(value, 0);

  if (isNaN(Number(value))) {
    return showDataIssueLabel ? 'Data Issue' : fallbackValue;
  }

  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces,
  }).format(num / 100);
};

/**
 * Safely format a number with specified decimal places
 */
export const safeNumberFormat = (
  value: unknown,
  options: DataQualityOptions = {},
): string => {
  const {
    fallbackValue = '0',
    showDataIssueLabel = false,
    decimalPlaces = 0,
  } = options;

  if (value === null || value === undefined) {
    return showDataIssueLabel ? 'No Data' : fallbackValue;
  }

  const num = safeNumber(value, 0);

  if (isNaN(Number(value))) {
    return showDataIssueLabel ? 'Data Issue' : fallbackValue;
  }

  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces,
  }).format(num);
};

/**
 * Validate and safely display financial metrics
 */
export const safeMetricDisplay = (
  value: unknown,
  type: 'currency' | 'percentage' | 'number' = 'number',
  options: DataQualityOptions = {},
): string => {
  const { showDataIssueLabel = true } = options;

  // Check for truly invalid data
  if (value === null || value === undefined) {
    return showDataIssueLabel ? 'No Data' : '—';
  }

  const originalValue = value;
  const num = safeNumber(value, 0);

  // If the original value was invalid (not just null/undefined)
  if (
    isNaN(Number(originalValue)) &&
    originalValue !== null &&
    originalValue !== undefined
  ) {
    return showDataIssueLabel ? 'Data Issue' : '—';
  }

  switch (type) {
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(num);

    case 'percentage':
      return safePercentage(originalValue, options);

    case 'number':
    default:
      return safeNumberFormat(originalValue, options);
  }
};

/**
 * Check if a value represents valid numerical data
 */
export const isValidNumericalData = (value: unknown): boolean => {
  if (value === null || value === undefined) return false;

  const num = typeof value === 'string' ? parseFloat(value) : Number(value);
  return !isNaN(num) && isFinite(num);
};

/**
 * Safe division operation that prevents NaN results
 */
export const safeDivision = (
  numerator: unknown,
  denominator: unknown,
  fallback: number = 0,
): number => {
  const num = safeNumber(numerator, 0);
  const denom = safeNumber(denominator, 1);

  if (denom === 0) return fallback;

  const result = num / denom;
  return isNaN(result) ? fallback : result;
};

/**
 * Safe calculation of percentage from two values
 */
export const safePercentageCalculation = (
  part: unknown,
  total: unknown,
  fallback: number = 0,
): number => {
  const partNum = safeNumber(part, 0);
  const totalNum = safeNumber(total, 1);

  if (totalNum === 0) return fallback;

  const percentage = (partNum / totalNum) * 100;
  return isNaN(percentage) ? fallback : percentage;
};

/**
 * React component wrapper for safe data display
 */
export const SafeDataDisplay: React.FC<{
  value: unknown;
  type?: 'currency' | 'percentage' | 'number';
  fallback?: string;
  className?: string;
  showIssueLabel?: boolean;
}> = ({
  value,
  type = 'number',
  fallback = '—',
  className = '',
  showIssueLabel = true,
}) => {
  const isValid = isValidNumericalData(value);

  if (!isValid) {
    return (
      <span
        className={`text-orange-600 text-xs ${className}`}
        title="Invalid or missing data"
      >
        {showIssueLabel ? 'Data Issue' : fallback}
      </span>
    );
  }

  const displayValue = safeMetricDisplay(value, type, {
    showDataIssueLabel: showIssueLabel,
  });

  return <span className={className}>{displayValue}</span>;
};
