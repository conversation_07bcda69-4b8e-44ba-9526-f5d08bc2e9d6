/**
 * Brand Gradients - Standardized gradient definitions matching mockup design
 * Use these instead of flat brand colors throughout the application
 */

export const brandGradients = {
  primary: 'linear-gradient(135deg, #295343 0%, #1A3F5F 100%)',
  primaryHover: 'linear-gradient(135deg, #1D372E 0%, #163651 100%)',
  
  // Additional gradient variations for different use cases
  subtle: 'linear-gradient(135deg, #295343 0%, #1A3F5F 50%, #3F1A5F 100%)',
  light: 'linear-gradient(135deg, rgba(41, 83, 67, 0.1) 0%, rgba(26, 63, 95, 0.1) 100%)',
  
  // Box shadow for buttons and elements using the gradient
  boxShadow: '0 4px 16px rgba(41, 83, 67, 0.2)',
  boxShadowHover: '0 6px 20px rgba(41, 83, 67, 0.3)',
} as const;

export const applyGradientHover = (element: HTMLElement, isHover: boolean) => {
  element.style.background = isHover ? brandGradients.primaryHover : brandGradients.primary;
  element.style.boxShadow = isHover ? brandGradients.boxShadowHover : brandGradients.boxShadow;
};

export const gradientButtonStyle = {
  background: brandGradients.primary,
  boxShadow: brandGradients.boxShadow,
  transition: 'all 0.2s ease',
};

export const gradientButtonHoverStyle = {
  background: brandGradients.primaryHover,
  boxShadow: brandGradients.boxShadowHover,
  transform: 'translateY(-1px)',
};