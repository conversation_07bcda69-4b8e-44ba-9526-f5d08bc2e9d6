// Performance monitoring utilities for production deployment
import React from 'react';
import { environment } from '../../environments/environment';

export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  bundleSize: number;
  errorCount: number;
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private startTime = performance.now();

  constructor() {
    if (environment.enablePerformanceMonitoring) {
      this.initializeMonitoring();
    }
  }

  private initializeMonitoring() {
    // Monitor page load performance
    window.addEventListener('load', () => {
      this.recordLoadTime();
    });

    // Monitor React render performance
    this.observeRenderPerformance();

    // Monitor user interactions
    this.observeUserInteractions();

    // Monitor errors
    this.observeErrors();
  }

  private recordLoadTime() {
    const loadTime = performance.now() - this.startTime;
    this.metrics.loadTime = loadTime;

    if (environment.enableAnalytics) {
     (`Page load time: ${loadTime.toFixed(2)}ms`);
    }
  }

  private observeRenderPerformance() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        for (const entry of entries) {
          if (entry.entryType === 'measure') {
            this.metrics.renderTime = entry.duration;
          }
        }
      });

      observer.observe({ entryTypes: ['measure'] });
    }
  }

  private observeUserInteractions() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        for (const entry of entries) {
          if (entry.entryType === 'event') {
            this.metrics.interactionTime = entry.duration;
          }
        }
      });

      observer.observe({ entryTypes: ['event'] });
    }
  }

  private observeErrors() {
    window.addEventListener('error', (error) => {
      this.metrics.errorCount = (this.metrics.errorCount || 0) + 1;

      if (environment.enableAnalytics) {
        console.error('Runtime error:', error.message);
      }
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.metrics.errorCount = (this.metrics.errorCount || 0) + 1;

      if (environment.enableAnalytics) {
        console.error('Unhandled promise rejection:', event.reason);
      }
    });
  }

  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  public markComponentRender(componentName: string) {
    if (environment.enablePerformanceMonitoring) {
      performance.mark(`${componentName}-render-start`);

      // Use setTimeout to mark end of render in next tick
      setTimeout(() => {
        performance.mark(`${componentName}-render-end`);
        performance.measure(
          `${componentName}-render-time`,
          `${componentName}-render-start`,
          `${componentName}-render-end`,
        );
      }, 0);
    }
  }

  public reportToAnalytics() {
    if (environment.enableAnalytics && this.metrics.loadTime) {
      // In a real application, you would send these to your analytics service
     ('Performance Report:', this.metrics);
    }
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Higher-order component for React performance monitoring

export function withPerformanceMonitoring<T extends {}>(
  WrappedComponent: React.ComponentType<T>,
  componentName?: string,
) {
  const displayName =
    componentName || WrappedComponent.displayName || WrappedComponent.name;

  return function MonitoredComponent(props: T) {
    React.useEffect(() => {
      performanceMonitor.markComponentRender(displayName);
    });

    return React.createElement(WrappedComponent, props);
  };
}

// React hook for performance monitoring
export function usePerformanceMonitoring(componentName: string) {
  React.useEffect(() => {
    performanceMonitor.markComponentRender(componentName);
  }, [componentName]);

  return performanceMonitor.getMetrics();
}
