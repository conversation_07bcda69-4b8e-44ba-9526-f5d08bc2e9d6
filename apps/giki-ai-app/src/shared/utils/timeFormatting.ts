/**
 * Time Formatting Utilities
 *
 * Consistent formatting for processing times, durations, and timestamps
 * across the giki.ai application.
 */

/**
 * Format processing time with automatic unit detection
 * Handles both milliseconds and seconds input gracefully
 */
export const formatProcessingTime = (
  timeValue: number | null | undefined,
  options: {
    showMs?: boolean;
    precision?: number;
  } = {},
): string => {
  const { showMs = false, precision = 1 } = options;

  if (timeValue === null || timeValue === undefined || isNaN(timeValue)) {
    return '0s';
  }

  // Auto-detect units: if value > 1000, likely milliseconds
  let timeInSeconds: number;

  if (timeValue > 1000 && !showMs) {
    // Convert from milliseconds to seconds
    timeInSeconds = timeValue / 1000;
  } else if (showMs) {
    // Keep as milliseconds
    return `${Math.round(timeValue)}ms`;
  } else {
    // Already in seconds
    timeInSeconds = timeValue;
  }

  // Format based on magnitude
  if (timeInSeconds < 60) {
    return `${timeInSeconds.toFixed(precision)}s`;
  } else if (timeInSeconds < 3600) {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = timeInSeconds % 60;
    if (seconds === 0) {
      return `${minutes}m`;
    }
    return `${minutes}m ${seconds.toFixed(0)}s`;
  } else if (timeInSeconds < 86400) {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    if (minutes === 0) {
      return `${hours}h`;
    }
    return `${hours}h ${minutes}m`;
  } else {
    const days = Math.floor(timeInSeconds / 86400);
    const hours = Math.floor((timeInSeconds % 86400) / 3600);
    if (hours === 0) {
      return `${days}d`;
    }
    return `${days}d ${hours}h`;
  }
};

/**
 * Format timestamp relative to current time (e.g., "2m ago", "3h ago")
 */
export const formatRelativeTime = (
  timestamp: string | number | Date,
  options: {
    maxDays?: number;
    showSeconds?: boolean;
  } = {},
): string => {
  const { maxDays = 365, showSeconds = false } = options;

  const date = new Date(timestamp);

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    return 'Invalid date';
  }

  const now = new Date();
  const diffMs = now.getTime() - date.getTime();

  // Check for future dates or invalid time differences
  if (diffMs < 0) {
    return 'Recently';
  }

  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (showSeconds && diffSeconds < 60) {
    if (diffSeconds < 5) return 'Just now';
    return `${diffSeconds}s ago`;
  }

  if (diffMinutes < 1) return 'Just now';
  if (diffMinutes < 60) return `${diffMinutes}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays <= maxDays) return `${diffDays}d ago`;

  // For very old dates, show the actual date
  return date.toLocaleDateString();
};

/**
 * Format duration in a human-readable way
 */
export const formatDuration = (
  durationMs: number,
  options: {
    compact?: boolean;
    maxUnits?: number;
  } = {},
): string => {
  const { compact = false, maxUnits = 2 } = options;

  if (durationMs < 0) return '0s';

  const units = [
    {
      name: compact ? 'd' : ' day',
      value: 86400000,
      plural: compact ? 'd' : ' days',
    },
    {
      name: compact ? 'h' : ' hour',
      value: 3600000,
      plural: compact ? 'h' : ' hours',
    },
    {
      name: compact ? 'm' : ' minute',
      value: 60000,
      plural: compact ? 'm' : ' minutes',
    },
    {
      name: compact ? 's' : ' second',
      value: 1000,
      plural: compact ? 's' : ' seconds',
    },
  ];

  const parts: string[] = [];
  let remaining = durationMs;

  for (const unit of units) {
    if (remaining >= unit.value && parts.length < maxUnits) {
      const count = Math.floor(remaining / unit.value);
      remaining %= unit.value;

      if (compact) {
        parts.push(`${count}${unit.name}`);
      } else {
        parts.push(`${count}${count === 1 ? unit.name : unit.plural}`);
      }
    }
  }

  if (parts.length === 0) {
    return compact ? '0s' : '0 seconds';
  }

  return parts.join(compact ? ' ' : ', ');
};

/**
 * Get estimated completion time based on current progress
 */
export const getEstimatedCompletion = (
  startTime: Date,
  currentProgress: number,
  options: {
    format?: 'relative' | 'absolute';
    precision?: 'rough' | 'precise';
  } = {},
): string => {
  const { format = 'relative', precision = 'rough' } = options;

  if (currentProgress <= 0 || currentProgress >= 1) {
    return format === 'relative' ? 'Unknown' : 'N/A';
  }

  const elapsedMs = Date.now() - startTime.getTime();
  const totalEstimatedMs = elapsedMs / currentProgress;
  const remainingMs = totalEstimatedMs - elapsedMs;

  if (remainingMs <= 0) {
    return 'Almost done';
  }

  if (format === 'absolute') {
    const completionTime = new Date(Date.now() + remainingMs);
    return completionTime.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  // Relative format
  if (precision === 'rough') {
    // Round to reasonable intervals
    if (remainingMs < 30000) return 'Less than 30s';
    if (remainingMs < 60000) return 'About 1m';
    if (remainingMs < 120000) return 'About 2m';
    if (remainingMs < 300000) return 'About 5m';
    if (remainingMs < 600000) return 'About 10m';
    if (remainingMs < 1800000) return 'About 30m';
    if (remainingMs < 3600000) return 'About 1h';
    return 'More than 1h';
  }

  // Precise format
  return formatDuration(remainingMs, { compact: true, maxUnits: 2 });
};
