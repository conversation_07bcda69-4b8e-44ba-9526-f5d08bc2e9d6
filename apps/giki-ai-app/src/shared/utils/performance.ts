import { logger } from './errorHandling';

interface PerformanceMark {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

class PerformanceMonitor {
  private marks: Map<string, PerformanceMark> = new Map();

  start(markName: string): void {
    this.marks.set(markName, {
      name: markName,
      startTime: performance.now(),
    });
  }

  end(markName: string): number | null {
    const mark = this.marks.get(markName);
    if (!mark) {
      logger.warn(
        `Performance mark '${markName}' not found`,
        'PerformanceMonitor',
      );
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - mark.startTime;

    mark.endTime = endTime;
    mark.duration = duration;

    logger.debug(
      `Performance: ${markName} took ${duration.toFixed(2)}ms`,
      'PerformanceMonitor',
      { markName, duration },
    );

    return duration;
  }

  measure(markName: string): number | null {
    const mark = this.marks.get(markName);
    if (!mark || !mark.duration) {
      return null;
    }
    return mark.duration;
  }

  clear(markName?: string): void {
    if (markName) {
      this.marks.delete(markName);
    } else {
      this.marks.clear();
    }
  }

  getAllMarks(): PerformanceMark[] {
    return Array.from(this.marks.values());
  }
}

export const performanceMonitor = new PerformanceMonitor();
