/**
 * Financial Formatting Utilities
 *
 * Professional financial data formatting for enterprise-grade applications.
 * Handles currency, percentages, and financial numbers with proper alignment.
 */

export interface CurrencyConfig {
  code: string;
  symbol: string;
  locale: string;
  position: 'prefix' | 'suffix';
  decimalPlaces: number;
}

// Default currency configurations
export const CURRENCY_CONFIGS: Record<string, CurrencyConfig> = {
  USD: {
    code: 'USD',
    symbol: '$',
    locale: 'en-US',
    position: 'prefix',
    decimalPlaces: 2,
  },
  INR: {
    code: 'INR',
    symbol: '₹',
    locale: 'en-IN',
    position: 'prefix',
    decimalPlaces: 2,
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    locale: 'de-DE',
    position: 'prefix',
    decimalPlaces: 2,
  },
  GBP: {
    code: 'GBP',
    symbol: '£',
    locale: 'en-GB',
    position: 'prefix',
    decimalPlaces: 2,
  },
};

// Get user's preferred currency from localStorage or default
export function getUserCurrency(): CurrencyConfig {
  const stored = localStorage.getItem('userCurrency');
  if (stored && CURRENCY_CONFIGS[stored]) {
    return CURRENCY_CONFIGS[stored];
  }
  // Default to USD for now
  return CURRENCY_CONFIGS.USD;
}

/**
 * Format currency with proper alignment and styling
 * @param amount - The numeric amount
 * @param options - Formatting options
 */
export function formatCurrency(
  amount: number,
  options: {
    currency?: string;
    showDecimals?: boolean;
    forceSign?: boolean;
    compact?: boolean;
  } = {},
): string {
  const config = options.currency
    ? CURRENCY_CONFIGS[options.currency] || CURRENCY_CONFIGS.USD
    : getUserCurrency();

  const formatter = new Intl.NumberFormat(config.locale, {
    style: 'currency',
    currency: config.code,
    minimumFractionDigits:
      options.showDecimals !== false ? config.decimalPlaces : 0,
    maximumFractionDigits:
      options.showDecimals !== false ? config.decimalPlaces : 0,
    notation: options.compact ? 'compact' : 'standard',
    signDisplay: options.forceSign ? 'always' : 'auto',
  });

  return formatter.format(amount);
}

/**
 * Format currency for display in tables with proper alignment
 * Returns object with formatted value and CSS classes
 */
export function formatCurrencyForTable(amount: number): {
  display: string;
  className: string;
  isNegative: boolean;
} {
  const isNegative = amount < 0;
  const formatted = formatCurrency(Math.abs(amount));

  return {
    display: isNegative ? `(${formatted})` : formatted,
    className: isNegative ? 'text-red-600' : 'text-gray-900',
    isNegative,
  };
}

/**
 * Format percentage with proper precision
 */
export function formatPercentage(
  value: number,
  options: {
    decimals?: number;
    showSign?: boolean;
  } = {},
): string {
  const decimals = options.decimals ?? 1;
  const formatted = value.toFixed(decimals);
  const sign = options.showSign && value > 0 ? '+' : '';
  return `${sign}${formatted}%`;
}

/**
 * Format large numbers with abbreviations (K, M, B)
 */
export function formatCompactNumber(value: number): string {
  const formatter = new Intl.NumberFormat('en-US', {
    notation: 'compact',
    compactDisplay: 'short',
    maximumFractionDigits: 1,
  });
  return formatter.format(value);
}

/**
 * Get debit/credit designation for accounting
 */
export function getDebitCreditDesignation(
  amount: number,
  accountType: 'asset' | 'liability' | 'expense' | 'revenue' | 'equity',
): 'DR' | 'CR' {
  // Normal balances:
  // Assets and Expenses: Debit (positive increases)
  // Liabilities, Revenue, Equity: Credit (positive increases)

  if (accountType === 'asset' || accountType === 'expense') {
    return amount >= 0 ? 'DR' : 'CR';
  } else {
    return amount >= 0 ? 'CR' : 'DR';
  }
}

/**
 * Format amount with accounting notation
 * Negative numbers in parentheses, aligned decimals
 */
export function formatAccountingNumber(
  amount: number,
  options: {
    showCurrency?: boolean;
    decimals?: number;
  } = {},
): string {
  const decimals = options.decimals ?? 2;
  const absAmount = Math.abs(amount);

  if (options.showCurrency) {
    const formatted = formatCurrency(absAmount, { showDecimals: decimals > 0 });
    return amount < 0 ? `(${formatted})` : formatted;
  }

  const formatted = absAmount.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });

  return amount < 0 ? `(${formatted})` : formatted;
}

/**
 * Parse currency string back to number
 * Handles various formats: $1,234.56, (1,234.56), etc.
 */
export function parseCurrencyString(value: string): number {
  // Remove currency symbols, spaces, and commas
  const cleaned = value.replace(/[^0-9.-]/g, '').replace(/,/g, '');

  // Check if negative (parentheses)
  const isNegative = value.includes('(') && value.includes(')');

  const parsed = parseFloat(cleaned);
  return isNegative ? -Math.abs(parsed) : parsed;
}

/**
 * Format date for financial reports
 */
export function formatFinancialDate(date: string | Date): string {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
  });
}

/**
 * Format date range for reports
 */
export function formatDateRange(startDate: Date, endDate: Date): string {
  const start = formatFinancialDate(startDate);
  const end = formatFinancialDate(endDate);
  return `${start} - ${end}`;
}

/**
 * Calculate and format variance
 */
export function formatVariance(
  actual: number,
  budget: number,
  options: {
    showPercentage?: boolean;
    showAmount?: boolean;
  } = { showPercentage: true, showAmount: true },
): string {
  const variance = actual - budget;
  const variancePercent = budget !== 0 ? (variance / budget) * 100 : 0;

  const parts: string[] = [];

  if (options.showAmount) {
    parts.push(formatCurrency(variance, { forceSign: true }));
  }

  if (options.showPercentage) {
    parts.push(`(${formatPercentage(variancePercent, { showSign: true })})`);
  }

  return parts.join(' ');
}
