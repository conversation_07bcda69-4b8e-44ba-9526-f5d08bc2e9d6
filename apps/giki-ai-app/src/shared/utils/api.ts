/**
 * API Utilities - Centralized API helpers
 * 
 * Consolidates API URL handling and common request patterns.
 */

import { API_CONFIG } from '../constants/brand';

/**
 * Get the base API URL from environment or fallback
 */
export const getApiBaseUrl = (): string => {
  return API_CONFIG.baseUrl;
};

/**
 * Get a full API endpoint URL
 */
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  const version = API_CONFIG.version;
  
  // Ensure endpoint starts with /
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  return `${baseUrl}${version}${normalizedEndpoint}`;
};

/**
 * Get WebSocket URL for real-time features
 */
export const getWebSocketUrl = (path: string): string => {
  const baseUrl = getApiBaseUrl();
  const wsUrl = baseUrl.replace(/^http/, 'ws');
  
  // Ensure path starts with /
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  return `${wsUrl}/ws${normalizedPath}`;
};

/**
 * Common API headers
 */
export const getApiHeaders = (includeAuth = true): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    const token = localStorage.getItem('authToken');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }
  
  return headers;
};

/**
 * Build query string from parameters
 */
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  });
  
  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
};

/**
 * Handle API errors consistently
 */
export const handleApiError = (error: any): string => {
  if (error.response?.data?.detail) {
    return error.response.data.detail;
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};