/**
 * Brand Constants - Centralized brand values
 * 
 * Single source of truth for all brand-related constants.
 * Use these instead of hardcoded values throughout the application.
 */

// Brand Colors (Design System Compliant)
export const BRAND_COLORS = {
  primary: '#295343',
  primaryHover: '#1D372E',
  primaryActive: '#295343',
  primaryLight: '#E8F5E8',
  primaryAlpha: 'rgba(41, 83, 67, 0.1)',
} as const;

// CSS Class Names for Brand Colors
export const BRAND_CLASSES = {
  primary: 'bg-[#295343]',
  primaryHover: 'hover:bg-[#1D372E]',
  primaryText: 'text-[#295343]',
  primaryBorder: 'border-[#295343]',
  primaryFocus: 'focus:ring-[#295343]',
  primaryButton: 'bg-[#295343] hover:bg-[#1D372E] text-white',
  primaryOutline: 'border-[#295343] text-[#295343] hover:bg-[#295343] hover:text-white',
} as const;

// API Configuration
export const API_CONFIG = {
  baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  version: '/api/v1',
  timeout: 30000,
} as const;

// Layout Constants
export const LAYOUT = {
  navExpanded: '240px',
  navCollapsed: '64px',
  agentPanel: '400px',
  agentMinWidth: '320px',
  agentMaxWidth: '480px',
  contentMaxWidth: '1200px',
} as const;

// Geometric Icons (Brand Compliant)
export const BRAND_ICONS = {
  square: '□',
  squares: '⊞',
  menu: '∷',
  circle: '⚬',
  arrow: '↑',
} as const;

// Common Spacing
export const SPACING = {
  page: 'p-page',
  section: 'space-section',
  element: 'gap-element',
  card: 'p-6',
  button: 'px-6 py-3',
} as const;

// Animation Durations
export const ANIMATIONS = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
} as const;

// Z-Index Layers
export const Z_INDEX = {
  dropdown: 1000,
  modal: 1500,
  tooltip: 2000,
  notification: 2500,
} as const;

// Breakpoints (matching Tailwind)
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Professional Status Colors
export const STATUS_COLORS = {
  success: '#059669',
  warning: '#D97706',
  error: '#DC2626',
  info: '#2563EB',
  neutral: '#6B7280',
} as const;

// Export helper functions
export const getBrandColor = (variant: keyof typeof BRAND_COLORS) => BRAND_COLORS[variant];
export const getBrandClass = (variant: keyof typeof BRAND_CLASSES) => BRAND_CLASSES[variant];
export const getApiUrl = (endpoint: string) => `${API_CONFIG.baseUrl}${API_CONFIG.version}${endpoint}`;