/**
 * Centralized logging system for the application
 *
 * Features:
 * - Environment-aware logging (verbose in dev, minimal in prod)
 * - Structured logging with context
 * - Performance tracking
 * - Error aggregation for monitoring
 * - Remote error reporting capability
 * - Session tracking and correlation
 * - User action tracking
 * - Network request logging
 * - Performance metrics collection
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogContext {
  [key: string]: unknown;
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  context?: LogContext;
  error?: Error;
  sessionId?: string;
  userId?: string;
  correlationId?: string;
  duration?: number;
}

export interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  metadata?: Record<string, unknown>;
}

export interface NetworkLogEntry {
  url: string;
  method: string;
  status?: number;
  duration: number;
  timestamp: Date;
  error?: string;
  requestSize?: number;
  responseSize?: number;
}

export interface UserAction {
  action: string;
  component: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

export interface ErrorReport {
  id: string;
  entries: LogEntry[];
  performanceMetrics: PerformanceMetric[];
  networkLogs: NetworkLogEntry[];
  userActions: UserAction[];
  environment: {
    url: string;
    userAgent: string;
    timestamp: string;
    sessionId: string;
    userId?: string;
  };
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';
  private logBuffer: LogEntry[] = [];
  private performanceMetrics: PerformanceMetric[] = [];
  private networkLogs: NetworkLogEntry[] = [];
  private userActions: UserAction[] = [];
  private maxBufferSize = 100;
  private maxMetricsSize = 200;
  private sessionId: string;
  private userId?: string;
  private correlationCounter = 0;
  private performanceTimers = new Map<string, number>();
  private errorReportingCallback?: (report: ErrorReport) => Promise<void>;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.setupNetworkInterceptor();
    this.setupPerformanceObserver();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCorrelationId(): string {
    return `corr_${Date.now()}_${++this.correlationCounter}`;
  }

  private formatMessage(
    level: LogLevel,
    message: string,
    context?: LogContext,
  ): string {
    const timestamp = new Date().toISOString();
    const contextStr = context ? JSON.stringify(context, null, 2) : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${contextStr ? '\n' + contextStr : ''}`;
  }

  private setupNetworkInterceptor() {
    if (typeof window === 'undefined') return;

    // Intercept fetch
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = Date.now();
      const [resource, config] = args;
      const url =
        resource instanceof Request ? resource.url : resource.toString();
      const method = (config?.method || 'GET').toUpperCase();

      try {
        const response = await originalFetch(...args);
        const duration = Date.now() - startTime;

        this.logNetworkRequest({
          url,
          method,
          status: response.status,
          duration,
          timestamp: new Date(),
        });

        return response;
      } catch (error) {
        const duration = Date.now() - startTime;

        this.logNetworkRequest({
          url,
          method,
          duration,
          timestamp: new Date(),
          error: error instanceof Error ? error.message : 'Network error',
        });

        throw error;
      }
    };
  }

  private setupPerformanceObserver() {
    if (typeof window === 'undefined' || !window.PerformanceObserver) return;

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (
            entry.entryType === 'navigation' ||
            entry.entryType === 'resource'
          ) {
            this.trackPerformanceEntry(entry);
          }
        }
      });

      observer.observe({ entryTypes: ['navigation', 'resource'] });
    } catch (e) {
      // Performance observer not supported
    }
  }

  private trackPerformanceEntry(entry: PerformanceEntry) {
    if (entry instanceof PerformanceNavigationTiming) {
      this.info('Page navigation performance', {
        domContentLoaded:
          entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
        loadComplete: entry.loadEventEnd - entry.loadEventStart,
        totalDuration: entry.duration,
      });
    }
  }

  private log(
    level: LogLevel,
    message: string,
    context?: LogContext,
    error?: Error,
  ) {
    const correlationId = this.generateCorrelationId();

    // Always store in buffer for error reporting
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context,
      error,
      sessionId: this.sessionId,
      userId: this.userId,
      correlationId,
    };

    this.logBuffer.push(entry);
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer.shift();
    }

    // Console output based on environment
    const formattedMessage = this.formatMessage(level, message, context);

    switch (level) {
      case 'debug':
        if (this.isDevelopment) {
          console.debug(formattedMessage);
        }
        break;
      case 'info':
        console.info(formattedMessage);
        break;
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'error':
        console.error(formattedMessage);
        if (error?.stack) {
          console.error(error.stack);
        }

        // In production, send to error monitoring service
        if (!this.isDevelopment && this.shouldReportError(error)) {
          void this.reportToMonitoring(entry);
        }
        break;
    }
  }

  private shouldReportError(error?: Error): boolean {
    if (!error) return true;

    // Don't report certain expected errors
    const ignoredErrors = [
      'Network request failed',
      'Failed to fetch',
      'Load failed',
      'The user aborted a request',
    ];

    return !ignoredErrors.some((ignored) =>
      error.message.toLowerCase().includes(ignored.toLowerCase()),
    );
  }

  private async reportToMonitoring(entry: LogEntry) {
    // Create comprehensive error report
    const report: ErrorReport = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      entries: this.getRecentLogs(20), // Get last 20 log entries
      performanceMetrics: this.performanceMetrics.slice(-50), // Last 50 performance metrics
      networkLogs: this.networkLogs.slice(-30), // Last 30 network requests
      userActions: this.userActions.slice(-20), // Last 20 user actions
      environment: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId,
        userId: this.userId,
      },
    };

    // Call external reporting service if configured
    if (this.errorReportingCallback) {
      try {
        await this.errorReportingCallback(report);
      } catch (e) {
        console.error('Failed to report error to monitoring service:', e);
      }
    }

    // Always store locally for debugging
    try {
      const errorReports = JSON.parse(
        localStorage.getItem('giki_error_reports') || '[]',
      );
      errorReports.push(report);

      // Keep only last 10 comprehensive reports (they're larger now)
      if (errorReports.length > 10) {
        errorReports.splice(0, errorReports.length - 10);
      }

      localStorage.setItem('giki_error_reports', JSON.stringify(errorReports));
    } catch (e) {
      // Fail silently if localStorage is full or unavailable
    }
  }

  private logNetworkRequest(entry: NetworkLogEntry) {
    this.networkLogs.push(entry);
    if (this.networkLogs.length > this.maxMetricsSize) {
      this.networkLogs.shift();
    }

    // Log slow requests
    if (entry.duration > 3000) {
      this.warn('Slow network request detected', {
        url: entry.url,
        method: entry.method,
        duration: entry.duration,
        status: entry.status,
      });
    }

    // Log failed requests
    if (entry.error || (entry.status && entry.status >= 400)) {
      this.error('Network request failed', {
        url: entry.url,
        method: entry.method,
        status: entry.status,
        error: entry.error,
      });
    }
  }

  debug(message: string, context?: LogContext) {
    this.log('debug', message, context);
  }

  info(message: string, context?: LogContext) {
    this.log('info', message, context);
  }

  warn(message: string, context?: LogContext) {
    this.log('warn', message, context);
  }

  error(message: string, context?: LogContext & { error?: unknown }) {
    const error =
      context?.error instanceof Error
        ? context.error
        : context?.error
          ? new Error(
              typeof context.error === 'string'
                ? context.error
                : JSON.stringify(context.error),
            )
          : undefined;

    const cleanContext = { ...context };
    delete cleanContext.error;

    this.log('error', message, cleanContext, error);
  }

  // Performance tracking
  time(label: string) {
    this.performanceTimers.set(label, Date.now());
    if (this.isDevelopment) {
      console.time(label);
    }
  }

  timeEnd(label: string) {
    const startTime = this.performanceTimers.get(label);
    if (startTime) {
      const duration = Date.now() - startTime;
      this.performanceTimers.delete(label);

      // Store performance metric
      const metric: PerformanceMetric = {
        operation: label,
        duration,
        timestamp: new Date(),
        success: true,
      };

      this.performanceMetrics.push(metric);
      if (this.performanceMetrics.length > this.maxMetricsSize) {
        this.performanceMetrics.shift();
      }

      // Log slow operations
      if (duration > 1000) {
        this.warn(`Slow operation detected: ${label}`, { duration });
      }
    }

    if (this.isDevelopment) {
      console.timeEnd(label);
    }
  }

  // Track user actions
  trackUserAction(
    action: string,
    component: string,
    metadata?: Record<string, unknown>,
  ) {
    const userAction: UserAction = {
      action,
      component,
      timestamp: new Date(),
      metadata,
    };

    this.userActions.push(userAction);
    if (this.userActions.length > this.maxBufferSize) {
      this.userActions.shift();
    }

    this.debug(`User action: ${action}`, { component, ...metadata });
  }

  // Track performance metrics
  trackMetric(
    operation: string,
    duration: number,
    success: boolean,
    metadata?: Record<string, unknown>,
  ) {
    const metric: PerformanceMetric = {
      operation,
      duration,
      timestamp: new Date(),
      success,
      metadata,
    };

    this.performanceMetrics.push(metric);
    if (this.performanceMetrics.length > this.maxMetricsSize) {
      this.performanceMetrics.shift();
    }
  }

  // Set user context
  setUser(userId: string) {
    this.userId = userId;
    this.info('User context updated', { userId });
  }

  // Clear user context
  clearUser() {
    this.userId = undefined;
    this.info('User context cleared');
  }

  // Configure external error reporting
  configureErrorReporting(callback: (report: ErrorReport) => Promise<void>) {
    this.errorReportingCallback = callback;
  }

  // Get recent logs for debugging
  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logBuffer.slice(-count);
  }

  // Get performance metrics
  getPerformanceMetrics(): PerformanceMetric[] {
    return [...this.performanceMetrics];
  }

  // Get network logs
  getNetworkLogs(): NetworkLogEntry[] {
    return [...this.networkLogs];
  }

  // Get user actions
  getUserActions(): UserAction[] {
    return [...this.userActions];
  }

  // Clear error reports (useful after they've been sent to server)
  clearErrorReports() {
    try {
      localStorage.removeItem('giki_error_reports');
    } catch (e) {
      // Fail silently
    }
  }

  // Get stored error reports
  getErrorReports(): ErrorReport[] {
    try {
      return JSON.parse(localStorage.getItem('giki_error_reports') || '[]');
    } catch (e) {
      return [];
    }
  }

  // Generate debug report
  generateDebugReport(): ErrorReport {
    return {
      id: `debug_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      entries: this.getRecentLogs(),
      performanceMetrics: this.getPerformanceMetrics(),
      networkLogs: this.getNetworkLogs(),
      userActions: this.getUserActions(),
      environment: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId,
        userId: this.userId,
      },
    };
  }
}

export const logger = new Logger();

// Export convenience functions
export const logPerformance = (operation: string, fn: () => void) => {
  logger.time(operation);
  try {
    fn();
    logger.trackMetric(operation, 0, true); // Duration will be tracked by timeEnd
  } catch (error) {
    logger.trackMetric(operation, 0, false, {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  } finally {
    logger.timeEnd(operation);
  }
};

export const logAsyncPerformance = async <T>(
  operation: string,
  fn: () => Promise<T>,
): Promise<T> => {
  const startTime = Date.now();
  logger.time(operation);
  try {
    const result = await fn();
    const duration = Date.now() - startTime;
    logger.trackMetric(operation, duration, true);
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.trackMetric(operation, duration, false, {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  } finally {
    logger.timeEnd(operation);
  }
};

// Track user interactions
export const trackUserAction = (
  action: string,
  component: string,
  metadata?: Record<string, unknown>,
) => {
  logger.trackUserAction(action, component, metadata);
};

// Track custom metrics
export const trackMetric = (
  operation: string,
  duration: number,
  success: boolean,
  metadata?: Record<string, unknown>,
) => {
  logger.trackMetric(operation, duration, success, metadata);
};

// Debug helpers
export const generateDebugReport = () => logger.generateDebugReport();
export const getErrorReports = () => logger.getErrorReports();
export const clearErrorReports = () => logger.clearErrorReports();

// Configure error reporting service
export const configureErrorReporting = (
  callback: (report: ErrorReport) => Promise<void>,
) => {
  logger.configureErrorReporting(callback);
};

// User context management
export const setLoggerUser = (userId: string) => logger.setUser(userId);
export const clearLoggerUser = () => logger.clearUser();
