// apps/giki-ai-app/src/services/apiOptimization.ts
import { apiClient } from './apiClient';

interface BatchRequest {
  id: string;
  endpoint: string;
  payload: unknown;
  resolve: (value: unknown) => void;
  reject: (error: unknown) => void;
  timestamp: number;
}

interface DebouncedRequest {
  key: string;
  request: () => Promise<unknown>;
  resolve: (value: unknown) => void;
  reject: (error: unknown) => void;
  timeout: NodeJS.Timeout;
}

class ApiOptimizationService {
  private batchQueue: BatchRequest[] = [];
  private debouncedRequests = new Map<string, DebouncedRequest>();
  private batchTimeout: NodeJS.Timeout | null = null;
  private readonly BATCH_SIZE = 5;
  private readonly BATCH_DELAY = 100; // 100ms
  private readonly DEBOUNCE_DELAY = 300; // 300ms

  /**
   * Batch multiple API requests together
   */
  batchRequest<T>(endpoint: string, payload: unknown): Promise<T> {
    return new Promise((resolve, reject) => {
      const request: BatchRequest = {
        id: `batch-${Date.now()}-${Math.random()}`,
        endpoint,
        payload,
        resolve,
        reject,
        timestamp: Date.now(),
      };

      this.batchQueue.push(request);

      // Process batch if we hit the size limit
      if (this.batchQueue.length >= this.BATCH_SIZE) {
        void this.processBatch();
      } else {
        // Set timeout to process batch after delay
        if (this.batchTimeout) {
          clearTimeout(this.batchTimeout);
        }
        this.batchTimeout = setTimeout(() => {
          void this.processBatch();
        }, this.BATCH_DELAY);
      }
    });
  }

  /**
   * Debounce API requests to prevent rapid successive calls
   */
  debounceRequest<T>(key: string, request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      // Cancel existing debounced request for this key
      const existing = this.debouncedRequests.get(key);
      if (existing) {
        clearTimeout(existing.timeout);
        existing.reject(new Error('Request cancelled by newer request'));
      }

      // Create new debounced request
      const timeout = setTimeout(async () => {
        this.debouncedRequests.delete(key);
        try {
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, this.DEBOUNCE_DELAY);

      this.debouncedRequests.set(key, {
        key,
        request,
        resolve,
        reject,
        timeout,
      });
    });
  }

  /**
   * Process batched requests
   */
  private async processBatch() {
    if (this.batchQueue.length === 0) return;

    const batch = [...this.batchQueue];
    this.batchQueue = [];

    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }

    // Group requests by endpoint
    const groupedRequests = batch.reduce(
      (groups, request) => {
        if (!groups[request.endpoint]) {
          groups[request.endpoint] = [];
        }
        groups[request.endpoint].push(request);
        return groups;
      },
      {} as Record<string, BatchRequest[]>,
    );

    // Process each group
    for (const [endpoint, requests] of Object.entries(groupedRequests)) {
      try {
        if (requests.length === 1) {
          // Single request - process normally
          const request = requests[0];
          const response = await apiClient.post(endpoint, request.payload);
          request.resolve(response.data);
        } else {
          // Multiple requests - batch them
          const batchPayload = {
            batch: true,
            requests: requests.map((req) => ({
              id: req.id,
              payload: req.payload,
            })),
          };

          const response = await apiClient.post(
            `${endpoint}/batch`,
            batchPayload,
          );

          // Distribute responses back to individual requests
          if (
            response.data &&
            typeof response.data === 'object' &&
            'results' in response.data
          ) {
            (
              response.data as {
                results: Array<{ id: string; error?: string; data?: unknown }>;
              }
            ).results.forEach((result) => {
              const request = requests.find((req) => req.id === result.id);
              if (request) {
                if (result.error) {
                  request.reject(new Error(result.error));
                } else {
                  request.resolve(result.data);
                }
              }
            });
          } else {
            // Fallback: process individually if batch endpoint doesn't exist
            await Promise.all(
              requests.map(async (request) => {
                try {
                  const response = await apiClient.post(
                    endpoint,
                    request.payload,
                  );
                  request.resolve(response.data);
                } catch (error) {
                  request.reject(error);
                }
              }),
            );
          }
        }
      } catch (error) {
        // Reject all requests in this group
        requests.forEach((request) => request.reject(error));
      }
    }
  }

  /**
   * Optimize agent query with intelligent caching and batching
   */
  async optimizedAgentQuery(
    query: string,
    queryType: string = 'general',
    parameters: Record<string, unknown> = {},
  ) {
    const cacheKey = `${queryType}-${JSON.stringify({ query, parameters })}`;

    // Use debouncing for rapid successive queries
    return this.debounceRequest(cacheKey, async () => {
      // Use batching for multiple simultaneous queries
      return this.batchRequest('/agent/customer/query', {
        query,
        query_type: queryType,
        parameters,
      });
    });
  }

  /**
   * Preload critical data with optimized requests
   */
  async preloadCriticalData() {
    const criticalQueries = [
      { query: 'Get recent transactions', type: 'transactions' },
      { query: 'Generate spending insights', type: 'insights' },
      { query: 'Get accuracy metrics', type: 'accuracy' },
    ];

    // Batch all critical queries together
    const promises = criticalQueries.map(({ query, type }) =>
      this.batchRequest('/agent/customer/query', {
        query,
        query_type: type,
        parameters: { preload: true },
      }).catch((error) => {
        console.warn(`Failed to preload ${type}:`, error);
        return null;
      }),
    );

    const results = await Promise.allSettled(promises);
    return results.filter((result) => result.status === 'fulfilled');
  }

  /**
   * Optimize file upload with chunking and progress tracking
   */
  async optimizedFileUpload(
    file: File,
    onProgress?: (progress: number) => void,
  ) {
    const CHUNK_SIZE = 1024 * 1024; // 1MB chunks
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);

    if (totalChunks === 1) {
      // Small file - upload directly
      const formData = new FormData();
      formData.append('file', file);

      return apiClient.post('/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
    }

    // Large file - use chunked upload
    const uploadId = `upload-${Date.now()}-${Math.random()}`;
    const chunks: Promise<unknown>[] = [];

    for (let i = 0; i < totalChunks; i++) {
      const start = i * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE, file.size);
      const chunk = file.slice(start, end);

      const formData = new FormData();
      formData.append('chunk', chunk);
      formData.append('uploadId', uploadId);
      formData.append('chunkIndex', i.toString());
      formData.append('totalChunks', totalChunks.toString());
      formData.append('fileName', file.name);

      const chunkPromise = this.batchRequest('/upload/chunk', formData).then(
        (result) => {
          if (onProgress) {
            const progress = ((i + 1) / totalChunks) * 100;
            onProgress(progress);
          }
          return result;
        },
      );

      chunks.push(chunkPromise);
    }

    // Wait for all chunks to upload
    await Promise.all(chunks);

    // Finalize upload
    return this.batchRequest('/upload/finalize', {
      uploadId,
      fileName: file.name,
      totalSize: file.size,
    });
  }

  /**
   * Clear all pending requests
   */
  clearPendingRequests() {
    // Clear batch queue
    this.batchQueue.forEach((request) => {
      request.reject(new Error('Request cancelled'));
    });
    this.batchQueue = [];

    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }

    // Clear debounced requests
    this.debouncedRequests.forEach((request) => {
      clearTimeout(request.timeout);
      request.reject(new Error('Request cancelled'));
    });
    this.debouncedRequests.clear();
  }

  /**
   * Get optimization statistics
   */
  getOptimizationStats() {
    return {
      pendingBatchRequests: this.batchQueue.length,
      pendingDebouncedRequests: this.debouncedRequests.size,
      batchSize: this.BATCH_SIZE,
      batchDelay: this.BATCH_DELAY,
      debounceDelay: this.DEBOUNCE_DELAY,
    };
  }
}

// Global optimization service instance
export const apiOptimization = new ApiOptimizationService();
