/**
 * Core API Client for giki-ai-app
 *
 * This module provides a centralized client for all API communications with the giki-ai-api backend.
 * It handles authentication, error handling, request/response interceptors, and provides
 * a consistent interface for making API requests.
 */

import {
  getAccessToken,
  getRefreshToken,
  setAccessToken,
  setRefreshToken,
  clearTokens,
} from '@/features/auth/services/auth';
import { refreshToken as refreshAuthToken } from '@/features/auth/services/authService';
import {
  ErrorType,
  createApiError,
  createAuthenticationError,
} from '@/shared/types/errors';
import {
  logger,
  circuitBreakers,
  defaultR<PERSON>ryHandler,
  RetryHandler,
  RetryConfig,
} from '@/shared/utils/errorHandling';

// Import environment configuration
import environment from '@/environments/environment';

// API Configuration
const getApiBaseUrl = (): string => {
  // First check for environment variable override
  if (import.meta.env && import.meta.env.VITE_API_BASE_URL) {
    const baseUrl = import.meta.env.VITE_API_BASE_URL as string;
    // Using VITE_API_BASE_URL from environment
    return baseUrl !== '/' && baseUrl.endsWith('/')
      ? baseUrl.slice(0, -1)
      : baseUrl;
  }

  // Fallback to environment config
  const envApiUrl = environment.apiUrl;
  // Using environment.apiUrl configuration
  return envApiUrl && envApiUrl !== '/' && envApiUrl.endsWith('/')
    ? envApiUrl.slice(0, -1)
    : envApiUrl || '';
};

// Request options type
export interface ApiRequestOptions extends RequestInit {
  requiresAuth?: boolean;
  skipRefreshToken?: boolean;
  params?: Record<string, string | number | boolean | undefined | null>;
  onUploadProgress?: (progressEvent: unknown) => void;
  responseType?: 'json' | 'blob' | 'text' | 'arraybuffer';
  retry?: boolean | RetryConfig;
  useCircuitBreaker?: boolean;
  fallback?: () => Promise<unknown>;
}

// Response type
export interface ApiResponse<T> {
  data: T;
  status: number;
  headers: Headers;
}

// Error response type
export interface ApiErrorResponse {
  detail?: string;
  message?: string;
  errors?: Record<string, string[]>;
  status_code?: number;
  [key: string]: unknown;
}

// Performance tracking
interface PerformanceMetrics {
  totalRequests: number;
  failedRequests: number;
  totalResponseTime: number;
  lastResponseTime: number;
}

const performanceMetrics: PerformanceMetrics = {
  totalRequests: 0,
  failedRequests: 0,
  totalResponseTime: 0,
  lastResponseTime: 0,
};

/**
 * Core API client class
 */
class ApiClient {
  private baseUrl: string;
  private refreshPromise: Promise<string> | null = null;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || getApiBaseUrl();
  }

  /**
   * Emit performance metrics
   */
  private emitPerformanceMetrics(responseTime: number, success: boolean): void {
    performanceMetrics.totalRequests++;
    if (!success) performanceMetrics.failedRequests++;
    performanceMetrics.totalResponseTime += responseTime;
    performanceMetrics.lastResponseTime = responseTime;

    const avgResponseTime =
      performanceMetrics.totalResponseTime / performanceMetrics.totalRequests;
    const successRate =
      ((performanceMetrics.totalRequests - performanceMetrics.failedRequests) /
        performanceMetrics.totalRequests) *
      100;

    // Emit custom event for performance monitor
    if (typeof window !== 'undefined') {
      window.dispatchEvent(
        new CustomEvent('api-performance', {
          detail: {
            avgResponseTime,
            lastResponseTime: responseTime,
            totalRequests: performanceMetrics.totalRequests,
            failedRequests: performanceMetrics.failedRequests,
            successRate,
          },
        }),
      );
    }
  }

  /**
   * Set the base URL for API requests
   */
  setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
  }

  /**
   * Get the current base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * Build a URL with query parameters
   */
  private buildUrl(
    endpoint: string,
    params?: Record<string, string | number | boolean | undefined | null>,
  ): string {
    // Building URL with base, endpoint, and params

    // For relative base URLs (like /api/v1), just concatenate with endpoint
    let fullUrl: string;
    if (this?.baseUrl?.startsWith('http')) {
      // Absolute URL
      fullUrl = `${this.baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    } else {
      // Relative URL - just concatenate (Vite proxy will handle routing)
      const basePath = this?.baseUrl?.endsWith('/')
        ? this?.baseUrl?.slice(0, -1)
        : this.baseUrl;
      const endpointPath = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
      fullUrl = `${basePath}${endpointPath}`;
    }

    // URL built successfully

    // Handle query parameters
    if (!params || Object.keys(params).length === 0) {
      return fullUrl;
    }

    // For relative URLs, manually build query string to avoid URL constructor issues
    const queryString = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(
        ([key, value]) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`,
      )
      .join('&');

    return queryString ? `${fullUrl}?${queryString}` : fullUrl;
  }

  /**
   * Add authentication headers to request options
   */
  private async addAuthHeaders(
    options: RequestInit,
    skipRefreshToken = false,
  ): Promise<RequestInit> {
    const token = getAccessToken();

    // Adding authentication headers to request

    if (!token) {
      // No token found, checking if refresh should be skipped
      if (skipRefreshToken) {
        return options;
      }

      // Try to refresh the token
      try {
        // Attempting to refresh access token
        const newToken = await this.refreshAccessToken();
        // Token refresh successful

        return {
          ...options,
          headers: {
            ...options.headers,
            Authorization: `Bearer ${newToken}`,
          },
        };
      } catch (error) {
        // Token refresh failed - logging for debugging
        logger.error(
          'Failed to refresh token for request',
          'ApiClient.addAuthHeaders',
          error instanceof Error ? error : new Error(String(error)),
        );
        throw createAuthenticationError(
          'Authentication required. Please log in.',
          401,
        );
      }
    }

    // Final validation before adding to headers - ensure token is valid JWT format
    if (typeof token !== 'string' || token.split('.').length !== 3) {
      logger.error('Stored token has invalid format', 'ApiClient.addAuthHeaders', {
        tokenType: typeof token,
        tokenLength: token?.length || 0,
        segments: token?.split?.('.').length || 0,
      });
      // Clear the invalid token and try refresh if not skipped
      clearTokens();
      if (!skipRefreshToken) {
        try {
          const newToken = await this.refreshAccessToken();
          if (!newToken || typeof newToken !== 'string' || newToken.split('.').length !== 3) {
            throw createAuthenticationError('Authentication required. Please log in.', 401);
          }
          return {
            ...options,
            headers: {
              ...options.headers,
              Authorization: `Bearer ${newToken}`,
            },
          };
        } catch {
          throw createAuthenticationError('Authentication required. Please log in.', 401);
        }
      }
      throw createAuthenticationError('Authentication required. Please log in.', 401);
    }

    // Adding authorization header with bearer token
    return {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`,
      },
    };
  }

  /**
   * Refresh the access token
   */
  private async refreshAccessToken(): Promise<string> {
    // If there's already a refresh in progress, return that promise
    if (this.refreshPromise !== null) {
      return this.refreshPromise;
    }

    const refreshTokenValue = getRefreshToken();

    if (!refreshTokenValue) {
      throw createAuthenticationError('No refresh token available', 401);
    }

    // Create a new refresh promise
    this.refreshPromise = new Promise<string>((resolve, reject) => {
      refreshAuthToken(refreshTokenValue)
        .then((tokens) => {
          // Update tokens in storage
          setAccessToken(tokens.accessToken);
          if (tokens.refreshToken) {
            setRefreshToken(tokens.refreshToken);
          }
          resolve(tokens.accessToken);
        })
        .catch((error) => {
          // Clear tokens on refresh failure
          clearTokens();
          reject(error);
        })
        .finally(() => {
          // Clear the refresh promise
          this.refreshPromise = null;
        });
    });

    return this.refreshPromise;
  }

  /**
   * Process the API response
   */
  private async processResponse<T>(
    response: Response,
    responseType: 'json' | 'blob' | 'text' | 'arraybuffer' = 'json',
  ): Promise<ApiResponse<T>> {
    if (!response.ok) {
      await this.handleErrorResponse(response);
    }

    // For 204 No Content responses, return null data
    if (response.status === 204) {
      return {
        data: null as unknown as T,
        status: response.status,
        headers: response.headers,
      };
    }

    // Parse response based on responseType
    let data: T;

    switch (responseType) {
      case 'blob':
        data = (await response.blob()) as unknown as T;
        break;
      case 'text':
        data = (await response.text()) as unknown as T;
        break;
      case 'arraybuffer':
        data = (await response.arrayBuffer()) as unknown as T;
        break;
      case 'json':
      default: {
        // Check content type for JSON
        const contentType = response?.headers?.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          data = await response.json();
        } else {
          // For non-JSON responses, return the raw text when expecting JSON
          const text = await response.text();
          data = text as unknown as T;
        }
        break;
      }
    }

    return {
      data,
      status: response.status,
      headers: response.headers,
    };
  }

  /**
   * Handle error responses
   */
  private async handleErrorResponse(response: Response): Promise<never> {
    let errorData: ApiErrorResponse = {};
    let errorMessage = 'An error occurred while communicating with the server';

    try {
      errorData = await response.json();
      errorMessage = errorData.detail || errorData.message || errorMessage;
    } catch (e) {
      logger.warn(
        'Could not parse error response',
        'ApiClient.handleErrorResponse',
        e,
      );
    }

    // Handle different error types based on status code
    switch (response.status) {
      case 400:
        throw createApiError({
          type: ErrorType.VALIDATION,
          message: errorMessage,
          statusCode: response.status,
          details: errorData.errors || undefined,
        });
      case 401:
        // Dispatch auth-error event for session recovery
        if (typeof window !== 'undefined') {
          window.dispatchEvent(
            new CustomEvent('auth-error', {
              detail: {
                statusCode: response.status,
                message: 'Authentication failed',
              },
            }),
          );
        }
        throw createAuthenticationError(
          'Authentication failed. Please log in again.',
          response.status,
        );
      case 403:
        throw createApiError({
          type: ErrorType.AUTHORIZATION,
          message: 'You do not have permission to perform this action',
          statusCode: response.status,
          details: errorData.errors || undefined,
        });
      case 404:
        throw createApiError({
          type: ErrorType.NOT_FOUND,
          message: 'The requested resource was not found',
          statusCode: response.status,
          details: errorData.errors || undefined,
        });
      case 422:
        throw createApiError({
          type: ErrorType.VALIDATION,
          message: errorMessage,
          statusCode: response.status,
          details: errorData.errors || undefined,
        });
      case 500:
      case 502:
      case 503:
      case 504:
        throw createApiError({
          type: ErrorType.SERVER,
          message: 'A server error occurred. Please try again later.',
          statusCode: response.status,
          details: errorData.errors || undefined,
        });
      default:
        throw createApiError({
          type: ErrorType.UNKNOWN,
          message: errorMessage,
          statusCode: response.status,
          details: errorData.errors || undefined,
        });
    }
  }

  /**
   * Make an API request
   */
  private async request<T>(
    method: string,
    endpoint: string,
    options: ApiRequestOptions = {},
  ): Promise<ApiResponse<T>> {
    const {
      requiresAuth = true,
      skipRefreshToken = false,
      params,
      responseType = 'json',
      retry = true,
      useCircuitBreaker = true,
      fallback,
      ...requestOptions
    } = options;

    // Build the URL with query parameters
    const url = this.buildUrl(endpoint, params);

    // Set default timeout based on endpoint type
    const defaultTimeout = endpoint.includes('/schema-interpretation')
      ? 60000
      : endpoint.includes('/upload') || endpoint.includes('formData')
        ? 300000 // 5 minutes for file uploads
        : 30000;
    const timeoutMs =
      (requestOptions as ApiRequestOptions & { timeout?: number })?.timeout ||
      defaultTimeout;

    // Prepare request options with AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    let fetchOptions: RequestInit = {
      method,
      credentials: 'include', // Include cookies for CORS requests
      signal: controller.signal,
      headers: {
        Accept: 'application/json',
        ...(requestOptions.headers || {}),
      },
      ...requestOptions,
    };

    // Only set Content-Type if body is not FormData or URLSearchParams
    // FormData requires the browser to set the Content-Type with boundary
    // URLSearchParams needs application/x-www-form-urlencoded
    const isFormData = requestOptions.body instanceof FormData;
    const isUrlEncoded = requestOptions.body instanceof URLSearchParams;
    if (!isFormData && !isUrlEncoded && requestOptions.body) {
      fetchOptions.headers = {
        'Content-Type': 'application/json',
        ...fetchOptions.headers,
      };
    }

    // Add authentication headers if required
    if (requiresAuth) {
      fetchOptions = await this.addAuthHeaders(fetchOptions, skipRefreshToken);
    }

    // Start performance tracking
    const startTime = performance.now();

    // Core request function
    const executeRequest = async (): Promise<ApiResponse<T>> => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

      try {
        const response = await fetch(url, {
          ...fetchOptions,
          signal: controller.signal,
        });

        // Clear the timeout since request completed
        clearTimeout(timeoutId);

        // Handle 401 Unauthorized error by attempting to refresh the token
        if (response.status === 401 && requiresAuth && !skipRefreshToken) {
          try {
            // Refresh the token
            const newToken = await this.refreshAccessToken();

            // Create new timeout for retry
            const retryController = new AbortController();
            const retryTimeoutId = setTimeout(
              () => retryController.abort(),
              timeoutMs,
            );

            // Retry the request with the new token
            const retryOptions: RequestInit = {
              ...fetchOptions,
              signal: retryController.signal,
              headers: {
                ...fetchOptions.headers,
                Authorization: `Bearer ${newToken}`,
              },
            };

            const retryResponse = await fetch(url, retryOptions);
            clearTimeout(retryTimeoutId);
            return this.processResponse<T>(retryResponse, responseType);
          } catch (_refreshError) {
            // If token refresh fails, clear tokens and then process the original 401 error
            clearTokens();
            return this.processResponse<T>(response, responseType);
          }
        }

        return this.processResponse<T>(response, responseType);
      } catch (error) {
        // Clear the timeout on error
        clearTimeout(timeoutId);

        // Handle timeout errors
        if (error instanceof DOMException && error.name === 'AbortError') {
          throw createApiError({
            type: ErrorType.NETWORK,
            message: `Request timed out after ${timeoutMs}ms. Please try again.`,
            originalError: error,
          });
        }

        // Handle network errors
        if (
          error instanceof TypeError &&
          error?.message?.includes('Failed to fetch')
        ) {
          // Dispatch network-error event for session recovery
          if (typeof window !== 'undefined') {
            window.dispatchEvent(
              new CustomEvent('network-error', {
                detail: {
                  message: 'Network error occurred',
                },
              }),
            );
          }
          throw createApiError({
            type: ErrorType.NETWORK,
            message: 'Network error. Please check your internet connection.',
            originalError: error,
          });
        }

        // Re-throw other errors (including those from processResponse)
        throw error;
      }
    };

    try {
      let result: ApiResponse<T>;

      // Apply circuit breaker if enabled
      if (useCircuitBreaker) {
        const circuitBreaker = endpoint.includes('/upload')
          ? circuitBreakers.fileUpload
          : circuitBreakers.api;

        result = await circuitBreaker.execute(
          async () => {
            // Apply retry logic if enabled
            if (retry) {
              const retryHandler =
                retry === true ? defaultRetryHandler : new RetryHandler(retry);

              return await retryHandler.execute(
                executeRequest,
                `${method} ${endpoint}`,
              );
            } else {
              return await executeRequest();
            }
          },
          fallback,
          `${method} ${endpoint}`,
        );
      } else {
        // No circuit breaker, just apply retry logic if enabled
        if (retry) {
          const retryHandler =
            retry === true ? defaultRetryHandler : new RetryHandler(retry);

          result = await retryHandler.execute(
            executeRequest,
            `${method} ${endpoint}`,
          );
        } else {
          result = await executeRequest();
        }
      }

      // Track successful request
      const endTime = performance.now();
      this.emitPerformanceMetrics(endTime - startTime, true);

      return result;
    } catch (error) {
      // Track failed request
      const endTime = performance.now();
      this.emitPerformanceMetrics(endTime - startTime, false);

      throw error;
    }
  }

  /**
   * Make a GET request
   */
  async get<T>(
    endpoint: string,
    options: ApiRequestOptions = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>('GET', endpoint, options);
  }

  /**
   * Make a POST request
   */
  async post<T>(
    endpoint: string,
    data?: unknown,
    options: ApiRequestOptions = {},
  ): Promise<ApiResponse<T>> {
    // Check if data is FormData or URLSearchParams - pass directly
    const isFormData = data instanceof FormData;
    const isUrlEncoded = data instanceof URLSearchParams;

    return this.request<T>('POST', endpoint, {
      ...options,
      body:
        isFormData || isUrlEncoded
          ? data
          : data
            ? JSON.stringify(data)
            : undefined,
    });
  }

  /**
   * Make a PUT request
   */
  async put<T>(
    endpoint: string,
    data?: unknown,
    options: ApiRequestOptions = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>('PUT', endpoint, {
      ...options,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PATCH request
   */
  async patch<T>(
    endpoint: string,
    data?: unknown,
    options: ApiRequestOptions = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>('PATCH', endpoint, {
      ...options,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a DELETE request
   */
  async delete<T>(
    endpoint: string,
    options: ApiRequestOptions = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', endpoint, options);
  }

  /**
   * Make a form data POST request (for file uploads)
   */
  async postFormData<T>(
    endpoint: string,
    formData: FormData,
    options: ApiRequestOptions = {},
  ): Promise<ApiResponse<T>> {
    // No need to manipulate headers - the request method will handle FormData correctly
    return this.request<T>('POST', endpoint, {
      ...options,
      body: formData,
    });
  }

  /**
   * Make a form-urlencoded POST request (for OAuth2 token requests)
   */
  async postFormUrlEncoded<T>(
    endpoint: string,
    data: Record<string, string>,
    options: ApiRequestOptions = {},
  ): Promise<ApiResponse<T>> {
    // Use manual string building for consistent cross-browser behavior
    const formBody = Object.entries(data)
      .filter(([_, value]) => value !== null && value !== undefined)
      .map(
        ([key, value]) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
      )
      .join('&');

    return this.request<T>('POST', endpoint, {
      ...options,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
        ...(options.headers || {}),
      },
      body: formBody,
    });
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();

// Export the class for testing and custom instances
export default ApiClient;
