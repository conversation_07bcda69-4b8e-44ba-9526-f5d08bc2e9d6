/**
 * Agents API Service - Real backend integration for UnifiedAgentPanel
 */

import { apiClient } from './api/apiClient';

export interface ChatMessage {
  message: string;
  command?: string;
  parameters?: Record<string, unknown>;
  context?: Record<string, unknown>;
}

export interface ChatResponse {
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
  suggestions?: string[];
  actions?: Array<{
    label: string;
    action: string;
    variant?: 'default' | 'outline';
  }>;
  processing_time_ms?: number;
  error?: string;
}

export interface AgentCommand {
  command: string;
  description: string;
  parameters: string[];
  example: string;
}

export interface AgentCommandsResponse {
  commands: AgentCommand[];
  natural_language_support: boolean;
  context_aware: boolean;
}

export interface AgentStatus {
  status: string;
  agent_version: string;
  commands_available: number;
  database_connected: boolean;
  tenant_id: number;
  user_id: number;
  capabilities: string[];
}

export interface SuggestionsResponse {
  suggestions: string[];
  partial_message: string;
}

class AgentsApiService {
  private baseUrl = '/agents';

  /**
   * Send a chat message to the conversational agent
   */
  async sendMessage(message: ChatMessage): Promise<ChatResponse> {
    const response = await apiClient.post<ChatResponse>(
      `${this.baseUrl}/chat`,
      message,
    );
    return response.data;
  }

  /**
   * Get available agent commands
   */
  async getCommands(): Promise<AgentCommandsResponse> {
    const response = await apiClient.get<AgentCommandsResponse>(
      `${this.baseUrl}/commands`,
    );
    return response.data;
  }

  /**
   * Get agent status and health
   */
  async getStatus(): Promise<AgentStatus> {
    const response = await apiClient.get<AgentStatus>(`${this.baseUrl}/status`);
    return response.data;
  }

  /**
   * Get command suggestions based on partial input
   */
  async getSuggestions(partialMessage: string): Promise<SuggestionsResponse> {
    const response = await apiClient.post<SuggestionsResponse>(
      `${this.baseUrl}/suggestions`,
      { partial_message: partialMessage },
    );
    return response.data;
  }

  /**
   * Execute a specific command
   */
  async executeCommand(
    command: string,
    parameters?: Record<string, any>,
    context?: Record<string, any>,
  ): Promise<ChatResponse> {
    return this.sendMessage({
      message: `Execute command: ${command}`,
      command,
      parameters,
      context,
    });
  }

  /**
   * Send natural language message without specific command
   */
  async sendNaturalLanguage(
    message: string,
    context?: Record<string, any>,
  ): Promise<ChatResponse> {
    return this.sendMessage({
      message,
      context,
    });
  }
}

export const agentsApi = new AgentsApiService();
