/**
 * Real-time synchronization service for coordinating updates between
 * traditional UI components and the AI agent panel
 */

type EventType =
  | 'transaction.categorized'
  | 'transaction.selected'
  | 'category.created'
  | 'category.updated'
  | 'file.uploaded'
  | 'file.processed'
  | 'batch?.categorization?.started'
  | 'batch?.categorization?.completed'
  | 'report.generate';

interface SyncEvent<T = unknown> {
  type: EventType;
  timestamp: number;
  data: T;
  source: 'ui' | 'agent';
}

type EventHandler<T = unknown> = (event: SyncEvent<T>) => void;

class RealtimeSyncService {
  private static instance: RealtimeSyncService;
  private eventHandlers: Map<EventType, Set<EventHandler>> = new Map();
  private eventHistory: SyncEvent[] = [];
  private maxHistorySize = 100;

  private constructor() {
    // Initialize with empty handlers for all event types
    const eventTypes: EventType[] = [
      'transaction.categorized',
      'transaction.selected',
      'category.created',
      'category.updated',
      'file.uploaded',
      'file.processed',
      'batch?.categorization?.started',
      'batch?.categorization?.completed',
      'report.generate',
    ];

    eventTypes.forEach((type) => {
      this?.eventHandlers?.set(type, new Set());
    });
  }

  static getInstance(): RealtimeSyncService {
    if (!RealtimeSyncService.instance) {
      RealtimeSyncService.instance = new RealtimeSyncService();
    }
    return RealtimeSyncService.instance;
  }

  /**
   * Subscribe to a specific event type
   */
  subscribe<T = unknown>(
    eventType: EventType,
    handler: EventHandler<T>,
  ): () => void {
    const handlers = this?.eventHandlers?.get(eventType);
    if (handlers) {
      handlers.add(handler);
    }

    // Return unsubscribe function
    return () => {
      const handlers = this?.eventHandlers?.get(eventType);
      if (handlers) {
        handlers.delete(handler);
      }
    };
  }

  /**
   * Emit an event to all subscribers
   */
  emit<T = unknown>(
    eventType: EventType,
    data: T,
    source: 'ui' | 'agent' = 'ui',
  ): void {
    const event: SyncEvent<T> = {
      type: eventType,
      timestamp: Date.now(),
      data,
      source,
    };

    // Add to history
    this?.eventHistory?.push(event);
    if (this?.eventHistory?.length > this.maxHistorySize) {
      this?.eventHistory?.shift();
    }

    // Notify all subscribers
    const handlers = this?.eventHandlers?.get(eventType);
    if (handlers) {
      handlers.forEach((handler) => {
        try {
          handler(event);
        } catch (error) {
          console.error(`Error in event handler for ${eventType}:`, error);
        }
      });
    }

    // Log event for debugging
    console.debug(`[RealtimeSync] Event emitted:`, {
      type: eventType,
      source,
      data,
    });
  }

  /**
   * Get recent events of a specific type
   */
  getRecentEvents(eventType?: EventType, limit: number = 10): SyncEvent[] {
    let events = this.eventHistory;

    if (eventType) {
      events = events.filter((e) => e.type === eventType);
    }

    return events.slice(-limit);
  }

  /**
   * Clear event history
   */
  clearHistory(): void {
    this.eventHistory = [];
  }

  /**
   * Get all active subscriptions count
   */
  getSubscriptionCount(): Map<EventType, number> {
    const counts = new Map<EventType, number>();
    this?.eventHandlers?.forEach((handlers, type) => {
      counts.set(type, handlers.size);
    });
    return counts;
  }
}

// Export singleton instance
export const realtimeSync = RealtimeSyncService.getInstance();

// Export types for external use
export type { EventType, SyncEvent, EventHandler };

// Helper hooks for React components
import { useEffect, useCallback } from 'react';

/**
 * React hook for subscribing to realtime sync events
 */
export function useRealtimeSync<T = unknown>(
  eventType: EventType,
  handler: (data: T) => void,
  deps: React.DependencyList = [],
): void {
  const memoizedHandler = useCallback(
    (event: SyncEvent<T>) => {
      handler(event.data);
    },
    [handler, ...deps],
  );

  useEffect(() => {
    const unsubscribe = realtimeSync.subscribe(eventType, memoizedHandler);
    return unsubscribe;
  }, [eventType, memoizedHandler]);
}

/**
 * React hook for emitting realtime sync events
 */
export function useRealtimeEmit(): {
  emit: <T = unknown>(
    eventType: EventType,
    data: T,
    source?: 'ui' | 'agent',
  ) => void;
} {
  const emit = useCallback(
    <T = unknown>(
      eventType: EventType,
      data: T,
      source: 'ui' | 'agent' = 'ui',
    ) => {
      realtimeSync.emit(eventType, data, source);
    },
    [],
  );

  return { emit };
}
