/**
 * Agent WebSocket Service
 * Real-time communication service for agent integration
 * Handles authentication, message routing, and connection management
 */
import React from 'react';

interface WebSocketMessage {
  type: 'message' | 'file' | 'action' | 'status' | 'error' | 'system';
  id: string;
  content: string;
  sender: 'user' | 'agent' | 'system';
  timestamp: string;
  metadata?: {
    confidence?: number;
    actionType?: string;
    fileUrl?: string;
    fileName?: string;
    agentType?: 'categorization' | 'reports' | 'coordinator' | 'gl_code';
    sessionId?: string;
  };
}

interface AgentStatus {
  status: 'online' | 'busy' | 'typing' | 'offline';
  lastSeen: string;
  currentTask?: string;
  capabilities: string[];
}

interface WebSocketConfig {
  url: string;
  reconnectAttempts: number;
  reconnectDelay: number;
  heartbeatInterval: number;
  authToken?: string;
}

type ConnectionState = 'connecting' | 'connected' | 'disconnected' | 'error';

type MessageHandler = (message: WebSocketMessage) => void;
type StatusHandler = (status: AgentStatus) => void;
type ConnectionHandler = (state: ConnectionState) => void;
type ErrorHandler = (error: Error) => void;

export class AgentWebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private connectionState: ConnectionState = 'disconnected';
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private sessionId: string;

  // Event handlers
  private messageHandlers: MessageHandler[] = [];
  private statusHandlers: StatusHandler[] = [];
  private connectionHandlers: ConnectionHandler[] = [];
  private errorHandlers: ErrorHandler[] = [];

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: config.url || this.getWebSocketUrl(),
      reconnectAttempts: config.reconnectAttempts || 5,
      reconnectDelay: config.reconnectDelay || 3000,
      heartbeatInterval: config.heartbeatInterval || 30000,
      authToken: config.authToken,
    };

    this.sessionId = this.generateSessionId();
  }

  private getWebSocketUrl(): string {
    // Determine WebSocket URL based on environment
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;

    if (process.env.NODE_ENV === 'development') {
      return `ws://localhost:8000/ws/agent`;
    }

    return `${protocol}//${host}/ws/agent`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Connect to the WebSocket server
   */
  public connect(): void {
    if (
      this.connectionState === 'connected' ||
      this.connectionState === 'connecting'
    ) {
      return;
    }

    this.setConnectionState('connecting');

    try {
      const wsUrl = new URL(this.config.url);

      // Add authentication token if available
      if (this.config.authToken) {
        wsUrl.searchParams.set('token', this.config.authToken);
      }

      wsUrl.searchParams.set('session_id', this.sessionId);

      this.ws = new WebSocket(wsUrl.toString());

      this.ws.onopen = this.handleOpen.bind(this) as (event: Event) => void;
      this.ws.onmessage = this.handleMessage.bind(this) as (
        event: MessageEvent,
      ) => void;
      this.ws.onclose = this.handleClose.bind(this) as (
        event: CloseEvent,
      ) => void;
      this.ws.onerror = this.handleError.bind(this) as (event: Event) => void;
    } catch (error) {
      this.handleConnectionError(
        new Error(`Failed to create WebSocket connection: ${error}`),
      );
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    this.reconnectAttempts = this.config.reconnectAttempts; // Prevent reconnection

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.close(1000, 'Client disconnect');
    }

    this.ws = null;
    this.setConnectionState('disconnected');
  }

  /**
   * Send a message to the agent
   */
  public sendMessage(
    content: string,
    type: WebSocketMessage['type'] = 'message',
    metadata?: WebSocketMessage['metadata'],
  ): void {
    if (this.connectionState !== 'connected' || !this.ws) {
      throw new Error('WebSocket not connected');
    }

    const message: WebSocketMessage = {
      type,
      id: this.generateMessageId(),
      content,
      sender: 'user',
      timestamp: new Date().toISOString(),
      metadata: {
        ...metadata,
        sessionId: this.sessionId,
      },
    };

    this.ws.send(JSON.stringify(message));
  }

  /**
   * Send a file to the agent
   */
  public sendFile(file: File, description?: string): void {
    const fileMessage: WebSocketMessage = {
      type: 'file',
      id: this.generateMessageId(),
      content: description || `Uploaded file: ${file.name}`,
      sender: 'user',
      timestamp: new Date().toISOString(),
      metadata: {
        fileName: file.name,
        fileUrl: URL.createObjectURL(file),
        sessionId: this.sessionId,
      },
    };

    // In a real implementation, you would upload the file to a server
    // and send the URL to the agent
    this.sendMessage(fileMessage.content, 'file', fileMessage.metadata);
  }

  /**
   * Request specific agent action
   */
  public requestAction(
    actionType: string,
    parameters?: Record<string, unknown>,
  ): void {
    const actionMessage: WebSocketMessage = {
      type: 'action',
      id: this.generateMessageId(),
      content: `Action requested: ${actionType}`,
      sender: 'user',
      timestamp: new Date().toISOString(),
      metadata: {
        actionType,
        ...parameters,
        sessionId: this.sessionId,
      },
    };

    this.sendMessage(actionMessage.content, 'action', actionMessage.metadata);
  }

  // Event handler registration methods
  public onMessage(handler: MessageHandler): () => void {
    this.messageHandlers.push(handler);
    return () => {
      const index = this.messageHandlers.indexOf(handler);
      if (index > -1) {
        this.messageHandlers.splice(index, 1);
      }
    };
  }

  public onStatusChange(handler: StatusHandler): () => void {
    this.statusHandlers.push(handler);
    return () => {
      const index = this.statusHandlers.indexOf(handler);
      if (index > -1) {
        this.statusHandlers.splice(index, 1);
      }
    };
  }

  public onConnectionChange(handler: ConnectionHandler): () => void {
    this.connectionHandlers.push(handler);
    return () => {
      const index = this.connectionHandlers.indexOf(handler);
      if (index > -1) {
        this.connectionHandlers.splice(index, 1);
      }
    };
  }

  public onError(handler: ErrorHandler): () => void {
    this.errorHandlers.push(handler);
    return () => {
      const index = this.errorHandlers.indexOf(handler);
      if (index > -1) {
        this.errorHandlers.splice(index, 1);
      }
    };
  }

  // Getters
  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public isConnected(): boolean {
    return this.connectionState === 'connected';
  }

  // Private methods
  private handleOpen(): void {
    // WebSocket connection established
    this.setConnectionState('connected');
    this.reconnectAttempts = 0;
    this.startHeartbeat();

    // Send initial connection message
    this.sendSystemMessage('connection_established', {
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
    });
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data as string) as WebSocketMessage;

      // Handle system messages
      if (message.type === 'system') {
        this.handleSystemMessage(message);
        return;
      }

      // Handle status updates
      if (message.type === 'status') {
        this.handleStatusUpdate(message);
        return;
      }

      // Notify message handlers
      this.messageHandlers.forEach((handler) => handler(message));
    } catch (error) {
      // Failed to parse WebSocket message - notifying error handlers
      this.notifyErrorHandlers(new Error('Invalid message format'));
    }
  }

  private handleClose(event: CloseEvent): void {
    // WebSocket disconnected - code and reason available in event
    this.setConnectionState('disconnected');

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    // Attempt to reconnect if not a normal closure
    if (
      event.code !== 1000 &&
      this.reconnectAttempts < this.config.reconnectAttempts
    ) {
      this.scheduleReconnect();
    }
  }

  private handleError(event: Event): void {
    // WebSocket error occurred - notifying error handlers
    this.handleConnectionError(new Error('WebSocket connection error'));
  }

  private handleSystemMessage(message: WebSocketMessage): void {
    // System message received from server

    // Handle different system message types
    if (message.content === 'heartbeat_response') {
      // Heartbeat acknowledged
      return;
    }

    if (message.content === 'agent_status_update') {
      // Agent status update
      return;
    }
  }

  private handleStatusUpdate(message: WebSocketMessage): void {
    try {
      const status: AgentStatus = JSON.parse(message.content) as AgentStatus;
      this.statusHandlers.forEach((handler) => handler(status));
    } catch (error) {
      // Failed to parse status update - invalid format
    }
  }

  private handleConnectionError(error: Error): void {
    this.setConnectionState('error');
    this.notifyErrorHandlers(error);
  }

  private setConnectionState(state: ConnectionState): void {
    if (this.connectionState !== state) {
      this.connectionState = state;
      this.connectionHandlers.forEach((handler) => handler(state));
    }
  }

  private notifyErrorHandlers(error: Error): void {
    this.errorHandlers.forEach((handler) => handler(error));
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectAttempts++;
    const delay =
      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    // Reconnecting with exponential backoff

    this.reconnectTimer = setTimeout(() => {
      void this.connect();
    }, delay);
  }

  private startHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendSystemMessage('heartbeat');
      }
    }, this.config.heartbeatInterval);
  }

  private sendSystemMessage(
    type: string,
    data?: Record<string, unknown>,
  ): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type: 'system',
        id: this.generateMessageId(),
        content: type,
        sender: 'user',
        timestamp: new Date().toISOString(),
        metadata: {
          sessionId: this.sessionId,
          ...data,
        },
      };

      this.ws.send(JSON.stringify(message));
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Singleton instance for global access
export const agentWebSocketService = new AgentWebSocketService();

// React hook for easy integration
export function useAgentWebSocket() {
  const [connectionState, setConnectionState] = React.useState<ConnectionState>(
    agentWebSocketService.getConnectionState(),
  );
  const [messages, setMessages] = React.useState<WebSocketMessage[]>([]);
  const [agentStatus, setAgentStatus] = React.useState<AgentStatus | null>(
    null,
  );

  React.useEffect(() => {
    // Subscribe to connection changes
    const unsubscribeConnection =
      agentWebSocketService.onConnectionChange(setConnectionState);

    // Subscribe to messages
    const unsubscribeMessages = agentWebSocketService.onMessage((message) => {
      setMessages((prev) => [...prev, message]);
    });

    // Subscribe to status updates
    const unsubscribeStatus =
      agentWebSocketService.onStatusChange(setAgentStatus);

    // Auto-connect
    if (!agentWebSocketService.isConnected()) {
      try {
        agentWebSocketService.connect();
      } catch (error) {
        // Error in reconnect attempt - will retry
      }
    }

    return () => {
      unsubscribeConnection();
      unsubscribeMessages();
      unsubscribeStatus();
    };
  }, []);

  return {
    connectionState,
    messages,
    agentStatus,
    sendMessage: agentWebSocketService.sendMessage.bind(
      agentWebSocketService,
    ) as typeof agentWebSocketService.sendMessage,
    sendFile: agentWebSocketService.sendFile.bind(
      agentWebSocketService,
    ) as typeof agentWebSocketService.sendFile,
    requestAction: agentWebSocketService.requestAction.bind(
      agentWebSocketService,
    ) as typeof agentWebSocketService.requestAction,
    connect: agentWebSocketService.connect.bind(
      agentWebSocketService,
    ) as typeof agentWebSocketService.connect,
    disconnect: agentWebSocketService.disconnect.bind(
      agentWebSocketService,
    ) as typeof agentWebSocketService.disconnect,
    isConnected: agentWebSocketService.isConnected.bind(
      agentWebSocketService,
    ) as typeof agentWebSocketService.isConnected,
  };
}

export default AgentWebSocketService;
