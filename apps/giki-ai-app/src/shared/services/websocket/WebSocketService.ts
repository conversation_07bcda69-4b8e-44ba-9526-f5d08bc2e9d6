/**
 * WebSocket Service - Unified real-time communication for giki.ai
 * 
 * Replaces realtimeSync.emit with proper WebSocket implementation
 * for consistent real-time updates across all features.
 * 
 * Features:
 * - Auto-reconnection with exponential backoff
 * - Message queuing during disconnections
 * - Event-based subscription system
 * - Error handling and recovery
 * - Backward compatibility with realtimeSync events
 */

// Custom EventEmitter implementation for browser compatibility
class EventEmitter {
  private events: { [key: string]: Function[] } = {};

  on(event: string, listener: Function): void {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  }

  off(event: string, listener: Function): void {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(l => l !== listener);
  }

  emit(event: string, ...args: any[]): void {
    if (!this.events[event]) return;
    this.events[event].forEach(listener => listener(...args));
  }
}

// Event types (compatible with realtimeSync)
export type WebSocketEventType =
  | 'transaction.categorized'
  | 'transaction.selected'
  | 'transaction.updated'
  | 'category.created'
  | 'category.updated'
  | 'file.uploaded'
  | 'file.processed'
  | 'processing.started'
  | 'processing.progress'
  | 'processing.completed'
  | 'batch.categorization.started'
  | 'batch.categorization.completed'
  | 'report.generate'
  | 'report.ready'
  | 'accuracy.updated'
  | 'enhancement.progress'
  | 'agent.message'
  | 'system.notification';

export interface WebSocketMessage<T = unknown> {
  type: WebSocketEventType;
  payload: T;
  timestamp: number;
  id: string;
  tenant_id?: number;
  user_id?: number;
}

export interface WebSocketConfig {
  url?: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  messageQueueSize?: number;
}

export type WebSocketEventHandler<T = unknown> = (message: WebSocketMessage<T>) => void;

export interface ConnectionStatus {
  connected: boolean;
  reconnectAttempts: number;
  lastError?: string;
  lastConnected?: Date;
  lastDisconnected?: Date;
}

class WebSocketService extends EventEmitter {
  private static instance: WebSocketService;
  private ws: WebSocket | null = null;
  private config: Required<WebSocketConfig>;
  private messageQueue: WebSocketMessage[] = [];
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private heartbeatTimeout: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private connectionStatus: ConnectionStatus = {
    connected: false,
    reconnectAttempts: 0
  };

  private constructor(config: WebSocketConfig = {}) {
    super();
    this.config = {
      url: config.url || this.getWebSocketUrl(),
      reconnectInterval: config.reconnectInterval || 3000,
      maxReconnectAttempts: config.maxReconnectAttempts || 10,
      heartbeatInterval: config.heartbeatInterval || 30000,
      messageQueueSize: config.messageQueueSize || 100
    };
    
    this.connect();
  }

  public static getInstance(config?: WebSocketConfig): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService(config);
    }
    return WebSocketService.instance;
  }

  /**
   * Connect to WebSocket server
   */
  private connect(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      this.ws = new WebSocket(this.config.url);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket connection open
   */
  private handleOpen(): void {
    this.connectionStatus = {
      connected: true,
      reconnectAttempts: this.reconnectAttempts,
      lastConnected: new Date()
    };
    
    this.reconnectAttempts = 0;
    this.emit('connection.opened');
    
    // Send queued messages
    this.flushMessageQueue();
    
    // Start heartbeat
    this.startHeartbeat();
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      // Validate message structure
      if (!message.type || !message.payload || !message.id) {
        console.warn('Invalid WebSocket message received:', message);
        return;
      }
      
      // Emit typed event
      this.emit('message', message);
      this.emit(`message.${message.type}`, message);
      
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  /**
   * Handle WebSocket connection close
   */
  private handleClose(event: CloseEvent): void {
    this.connectionStatus = {
      connected: false,
      reconnectAttempts: this.reconnectAttempts,
      lastDisconnected: new Date()
    };
    
    this.stopHeartbeat();
    this.emit('connection.closed', event);
    
    // Schedule reconnection if not a clean close
    if (event.code !== 1000) {
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket errors
   */
  private handleError(error: Event): void {
    console.error('WebSocket error:', error);
    this.connectionStatus.lastError = 'Connection error';
    this.emit('connection.error', error);
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.emit('connection.failed');
      return;
    }

    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts),
      30000 // Max 30 seconds
    );
    
    this.reconnectAttempts++;
    
    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatTimeout = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'heartbeat', timestamp: Date.now() }));
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimeout) {
      clearInterval(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }

  /**
   * Send queued messages
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()!;
      this.send(message.type, message.payload);
    }
  }

  /**
   * Generate WebSocket URL based on current location
   */
  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/api/v1/ws`;
  }

  /**
   * Send message via WebSocket
   */
  public send<T = unknown>(type: WebSocketEventType, payload: T): void {
    const message: WebSocketMessage<T> = {
      type,
      payload,
      timestamp: Date.now(),
      id: this.generateMessageId()
    };

    if (this.isConnected()) {
      try {
        this.ws!.send(JSON.stringify(message));
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        this.queueMessage(message);
      }
    } else {
      this.queueMessage(message);
    }
  }

  /**
   * Queue message for later delivery
   */
  private queueMessage(message: WebSocketMessage): void {
    if (this.messageQueue.length >= this.config.messageQueueSize) {
      this.messageQueue.shift(); // Remove oldest message
    }
    this.messageQueue.push(message);
  }

  /**
   * Subscribe to specific event type
   */
  public subscribe<T = unknown>(
    eventType: WebSocketEventType,
    handler: WebSocketEventHandler<T>
  ): () => void {
    this.on(`message.${eventType}`, handler);
    
    return () => {
      this.off(`message.${eventType}`, handler);
    };
  }

  /**
   * Subscribe to all messages
   */
  public subscribeAll(handler: WebSocketEventHandler): () => void {
    this.on('message', handler);
    
    return () => {
      this.off('message', handler);
    };
  }

  /**
   * Check if WebSocket is connected
   */
  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  /**
   * Manually reconnect
   */
  public reconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }
    
    this.disconnect();
    this.reconnectAttempts = 0;
    this.connect();
  }

  /**
   * Disconnect WebSocket
   */
  public disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.connectionStatus.connected = false;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clear message queue
   */
  public clearQueue(): void {
    this.messageQueue = [];
  }

  /**
   * Get queued message count
   */
  public getQueuedMessageCount(): number {
    return this.messageQueue.length;
  }
}

// Export singleton instance
export const webSocketService = WebSocketService.getInstance();

// React hook for WebSocket events
import React, { useEffect, useCallback, useState } from 'react';

export function useWebSocket<T = unknown>(
  eventType: WebSocketEventType,
  handler: (payload: T) => void,
  deps: React.DependencyList = []
): void {
  const memoizedHandler = useCallback(
    (message: WebSocketMessage<T>) => {
      handler(message.payload);
    },
    [handler, ...deps]
  );

  useEffect(() => {
    const unsubscribe = webSocketService.subscribe(eventType, memoizedHandler);
    return unsubscribe;
  }, [eventType, memoizedHandler]);
}

// React hook for WebSocket connection status
export function useWebSocketStatus(): {
  connected: boolean;
  reconnectAttempts: number;
  lastError?: string;
  reconnect: () => void;
} {
  const [status, setStatus] = useState(webSocketService.getConnectionStatus());

  useEffect(() => {
    const updateStatus = () => {
      setStatus(webSocketService.getConnectionStatus());
    };

    webSocketService.on('connection.opened', updateStatus);
    webSocketService.on('connection.closed', updateStatus);
    webSocketService.on('connection.error', updateStatus);

    return () => {
      webSocketService.off('connection.opened', updateStatus);
      webSocketService.off('connection.closed', updateStatus);
      webSocketService.off('connection.error', updateStatus);
    };
  }, []);

  return {
    connected: status.connected,
    reconnectAttempts: status.reconnectAttempts,
    lastError: status.lastError,
    reconnect: () => webSocketService.reconnect()
  };
}

// Backward compatibility - emit function similar to realtimeSync
export const emit = <T = unknown>(
  eventType: WebSocketEventType,
  payload: T,
  source: 'ui' | 'agent' = 'ui'
): void => {
  webSocketService.send(eventType, { ...payload, source });
};

// Export types
export type {
  WebSocketEventType,
  WebSocketMessage,
  WebSocketConfig,
  WebSocketEventHandler,
  ConnectionStatus
};