/**
 * UnifiedAIService - Consolidated AI operations for the giki.ai platform
 * 
 * This service consolidates AI functionality from multiple sources:
 * - Report generation and natural language parsing
 * - Categorization intelligence
 * - Conversational agent capabilities
 * - Performance optimization for direct operations
 * 
 * Replaces multiple services with a single, unified interface while maintaining
 * backward compatibility and performance optimizations.
 */

import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError } from '@/shared/utils/errorHandling';

// Core AI Request/Response Types
export interface AIRequest {
  query: string;
  context?: {
    operation: 'report_generation' | 'categorization' | 'conversation' | 'analysis';
    user_id?: number;
    tenant_id?: number;
    additional_context?: Record<string, unknown>;
  };
  options?: {
    temperature?: number;
    max_tokens?: number;
    stream?: boolean;
  };
}

export interface AIResponse {
  success: boolean;
  result: {
    response: string;
    structured_data?: Record<string, unknown>;
    confidence?: number;
    reasoning?: string;
    actions?: Array<{
      label: string;
      command: string;
      parameters?: Record<string, unknown>;
    }>;
  };
  processing_time_ms: number;
  operation_type: string;
  cached?: boolean;
}

// Report Generation Types (absorbed from reportGenerationTool.ts)
export interface ReportRequest {
  type: 'spending_by_category' | 'income_vs_expense' | 'monthly_trends' | 'custom' | 'pivot';
  parameters?: {
    dateRange?: { from: Date; to: Date };
    metrics?: string[];
    dimensions?: string[];
    filters?: unknown[];
    groupBy?: string;
    chartType?: string;
  };
  naturalLanguageQuery: string;
}

export interface ReportGenerationResult {
  success: boolean;
  reportId: string;
  reportType: string;
  message: string;
  data?: ReportRequest;
  export_ready?: boolean;
}

// Categorization Types
export interface CategorizationRequest {
  description: string;
  amount?: number;
  date?: string;
  context?: {
    tenant_id?: number;
    historical_data?: boolean;
    confidence_threshold?: number;
  };
}

export interface CategorizationResult {
  success: boolean;
  category: string;
  confidence: number;
  reasoning: string;
  alternatives?: Array<{
    category: string;
    confidence: number;
  }>;
  gl_code?: string;
  hierarchy_path?: string;
}

// Conversation Types
export interface ConversationRequest {
  message: string;
  session_id?: string;
  context?: {
    current_page?: string;
    user_role?: string;
    available_actions?: string[];
    current_data?: Record<string, unknown>;
  };
}

export interface ConversationResponse {
  success: boolean;
  message: string;
  session_id: string;
  actions?: Array<{
    label: string;
    command: string;
    variant?: 'default' | 'outline';
  }>;
  suggestions?: string[];
  data?: Record<string, unknown>;
}

class UnifiedAIService {
  private baseUrl = '/api/v1/ai/unified';
  private reportCache = new Map<string, ReportGenerationResult>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  /**
   * Parse natural language report query using AI
   * (Performance optimization: direct report generation for simple queries)
   */
  async parseReportQuery(query: string): Promise<ReportRequest> {
    try {
      // Check for simple patterns first (performance optimization)
      const simplePatterns = this.detectSimpleReportPatterns(query);
      if (simplePatterns) {
        return {
          type: simplePatterns.type,
          parameters: simplePatterns.parameters,
          naturalLanguageQuery: query
        };
      }

      // Use AI for complex queries
      const response = await apiClient.post<ReportRequest>(`${this.baseUrl}/parse-report`, {
        query: query.trim(),
        context: 'financial_report_generation'
      });

      return response.data;
    } catch (error) {
      throw handleApiError(error, {
        context: 'parseReportQuery',
        defaultMessage: 'Failed to parse report query'
      });
    }
  }

  /**
   * Generate report from natural language query
   * (Maintains performance optimization from reportGenerationTool.ts)
   */
  async generateReportFromQuery(
    query: string,
    userId: string,
    options?: { useCache?: boolean }
  ): Promise<ReportGenerationResult> {
    try {
      const cacheKey = `${userId}:${query}`;
      
      // Check cache for performance
      if (options?.useCache && this.reportCache.has(cacheKey)) {
        const cached = this.reportCache.get(cacheKey)!;
        return { ...cached, cached: true };
      }

      const reportRequest = await this.parseReportQuery(query);
      const reportId = `report_${Date.now()}`;

      // Generate report message based on type
      const message = this.generateReportMessage(reportRequest.type);

      const result: ReportGenerationResult = {
        success: true,
        reportId,
        reportType: reportRequest.type,
        message,
        data: reportRequest,
        export_ready: true
      };

      // Cache result
      if (options?.useCache) {
        this.reportCache.set(cacheKey, result);
        setTimeout(() => this.reportCache.delete(cacheKey), this.cacheTimeout);
      }

      return result;
    } catch (error) {
      throw handleApiError(error, {
        context: 'generateReportFromQuery',
        defaultMessage: 'Failed to generate report'
      });
    }
  }

  /**
   * Categorize transaction using AI
   * (Unified categorization with confidence scoring)
   */
  async categorizeTransaction(request: CategorizationRequest): Promise<CategorizationResult> {
    try {
      const response = await apiClient.post<CategorizationResult>(`${this.baseUrl}/categorize`, {
        description: request.description,
        amount: request.amount,
        date: request.date,
        context: request.context
      });

      return response.data;
    } catch (error) {
      throw handleApiError(error, {
        context: 'categorizeTransaction',
        defaultMessage: 'Failed to categorize transaction'
      });
    }
  }

  /**
   * Batch categorize transactions
   * (Performance optimization for bulk operations)
   */
  async categorizeBatch(
    transactions: Array<{
      id: string;
      description: string;
      amount?: number;
      date?: string;
    }>,
    context?: CategorizationRequest['context']
  ): Promise<Record<string, CategorizationResult>> {
    try {
      const response = await apiClient.post<Record<string, CategorizationResult>>(
        `${this.baseUrl}/categorize/batch`,
        {
          transactions,
          context
        }
      );

      return response.data;
    } catch (error) {
      throw handleApiError(error, {
        context: 'categorizeBatch',
        defaultMessage: 'Failed to categorize transactions in batch'
      });
    }
  }

  /**
   * Process conversational query
   * (Unified conversation handling with context awareness)
   */
  async processConversation(request: ConversationRequest): Promise<ConversationResponse> {
    try {
      const response = await apiClient.post<ConversationResponse>(`${this.baseUrl}/conversation`, {
        message: request.message,
        session_id: request.session_id,
        context: request.context
      });

      return response.data;
    } catch (error) {
      throw handleApiError(error, {
        context: 'processConversation',
        defaultMessage: 'Failed to process conversation'
      });
    }
  }

  /**
   * General AI query processing
   * (Unified entry point for all AI operations)
   */
  async processQuery(request: AIRequest): Promise<AIResponse> {
    try {
      const response = await apiClient.post<AIResponse>(`${this.baseUrl}/query`, request);
      return response.data;
    } catch (error) {
      throw handleApiError(error, {
        context: 'processQuery',
        defaultMessage: 'Failed to process AI query'
      });
    }
  }

  /**
   * Get AI service capabilities and status
   */
  async getCapabilities(): Promise<{
    available_operations: string[];
    model_info: Record<string, unknown>;
    performance_metrics: Record<string, unknown>;
  }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/capabilities`);
      return response.data;
    } catch (error) {
      throw handleApiError(error, {
        context: 'getCapabilities',
        defaultMessage: 'Failed to get AI capabilities'
      });
    }
  }

  // Private helper methods

  /**
   * Detect simple report patterns for performance optimization
   */
  private detectSimpleReportPatterns(query: string): { type: ReportRequest['type']; parameters: ReportRequest['parameters'] } | null {
    const queryLower = query.toLowerCase();

    // Simple spending by category patterns
    if (queryLower.includes('spending by category') || queryLower.includes('expenses by category')) {
      return {
        type: 'spending_by_category',
        parameters: {
          chartType: 'pie',
          metrics: ['amount'],
          dimensions: ['category']
        }
      };
    }

    // Simple income vs expense patterns
    if (queryLower.includes('income vs expense') || queryLower.includes('income and expense')) {
      return {
        type: 'income_vs_expense',
        parameters: {
          chartType: 'bar',
          metrics: ['amount'],
          dimensions: ['type']
        }
      };
    }

    // Simple monthly trends patterns
    if (queryLower.includes('monthly trends') || queryLower.includes('trends by month')) {
      return {
        type: 'monthly_trends',
        parameters: {
          chartType: 'line',
          metrics: ['amount'],
          dimensions: ['month'],
          groupBy: 'month'
        }
      };
    }

    return null;
  }

  /**
   * Generate contextual report message
   */
  private generateReportMessage(reportType: ReportRequest['type']): string {
    switch (reportType) {
      case 'spending_by_category':
        return "I'm generating a spending by category report for you. It will show your expenses broken down by each category.";
      case 'income_vs_expense':
        return "I'm creating an income vs expense comparison report. This will help you see your financial balance.";
      case 'monthly_trends':
        return "I'm preparing a monthly trends report to show how your finances change over time.";
      case 'pivot':
        return "I'm setting up a pivot table for you. You can drag and drop fields to analyze your data from different angles.";
      case 'custom':
        return "I'm creating a custom report based on your specifications.";
      default:
        return "I'm generating your requested report.";
    }
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.reportCache.clear();
  }
}

// Export singleton instance
export const unifiedAIService = new UnifiedAIService();

// Export types for external use
export type {
  AIRequest,
  AIResponse,
  ReportRequest,
  ReportGenerationResult,
  CategorizationRequest,
  CategorizationResult,
  ConversationRequest,
  ConversationResponse
};

// Example usage patterns
export const AI_EXAMPLES = {
  reportGeneration: [
    'Show me my spending by category',
    'Generate a monthly trends report',
    'Compare my income vs expenses',
    'Create a pie chart of my expenses',
    'Show spending by category for last 3 months'
  ],
  categorization: [
    'Categorize: "Amazon purchase $45.67"',
    'Categorize: "Starbucks coffee $4.50"',
    'Categorize: "Gas station fill-up $62.34"'
  ],
  conversation: [
    'How can I improve my expense tracking?',
    'What are my highest spending categories?',
    'Help me set up automatic categorization'
  ]
};