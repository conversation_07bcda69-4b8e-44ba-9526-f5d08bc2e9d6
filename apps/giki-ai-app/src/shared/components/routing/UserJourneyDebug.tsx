/**
 * User Journey Debug Component - For testing intelligent routing
 * Shows current user status and where they would be routed
 */
import React from 'react';
import { useUserJourneyStatus } from '@/shared/hooks/useUserJourneyStatus';

export const UserJourneyDebug: React.FC = () => {
  const status = useUserJourneyStatus();

  if (status.loading) {
    return (
      <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm">
        <div className="text-sm text-gray-600">Checking user journey status...</div>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm">
      <div className="text-sm font-semibold text-gray-800 mb-2">Journey Status</div>
      <div className="space-y-1 text-xs">
        <div>Total Transactions: {status.totalTransactions}</div>
        <div>Has Transactions: {status.hasTransactions ? '□' : '⚬'}</div>
        <div>Has Categorized: {status.hasCategorizedTransactions ? '□' : '⚬'}</div>
        <div>Categorized %: {status.categorizedPercentage.toFixed(1)}%</div>
        <div className="border-t pt-2 mt-2">
          <div className="font-medium">
            Should go to: {status.shouldGoToDashboard ? '∷ Dashboard' : '↑ Upload'}
          </div>
        </div>
        {status.error && (
          <div className="text-error text-xs">Error - defaulting to upload</div>
        )}
      </div>
    </div>
  );
};