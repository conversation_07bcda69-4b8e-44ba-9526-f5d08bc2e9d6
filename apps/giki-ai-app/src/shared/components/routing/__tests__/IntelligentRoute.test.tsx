/**
 * IntelligentRoute Component Tests
 * 
 * Tests the intelligent routing system that determines correct user destinations
 * based on their transaction categorization status and journey progress.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render } from '@testing-library/react';
import { MemoryRouter, useLocation } from 'react-router-dom';
import { IntelligentRoute } from '../IntelligentRoute';
import * as journeyHook from '@/shared/hooks/useUserJourneyStatus';

// Mock the hook
const mockUseUserJourneyStatus = vi.spyOn(journeyHook, 'useUserJourneyStatus');

// Test component to capture navigation
const LocationTracker = () => {
  const location = useLocation();
  return <div data-testid="location">{location.pathname}</div>;
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MemoryRouter initialEntries={['/']}>
    {children}
    <LocationTracker />
  </MemoryRouter>
);

describe('IntelligentRoute', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when user has categorized transactions', () => {
    beforeEach(() => {
      mockUseUserJourneyStatus.mockReturnValue({
        loading: false,
        error: false,
        shouldGoToDashboard: true,
        shouldGoToUpload: false,
        hasTransactions: true,
        hasCategorizedTransactions: true,
        categorizedPercentage: 100,
        totalTransactions: 150,
      });
    });

    it('should navigate to dashboard when user has categorized transactions', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      expect(getByTestId('location')).toHaveTextContent('/dashboard');
    });

    it('should call useUserJourneyStatus hook', () => {
      render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      expect(mockUseUserJourneyStatus).toHaveBeenCalled();
    });
  });

  describe('when user has uncategorized transactions', () => {
    beforeEach(() => {
      mockUseUserJourneyStatus.mockReturnValue({
        loading: false,
        error: false,
        shouldGoToDashboard: false,
        shouldGoToUpload: true,
        hasTransactions: true,
        hasCategorizedTransactions: false,
        categorizedPercentage: 0,
        totalTransactions: 100,
      });
    });

    it('should navigate to upload page when user has only uncategorized transactions', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      expect(getByTestId('location')).toHaveTextContent('/upload');
    });
  });

  describe('when user has no transactions', () => {
    beforeEach(() => {
      mockUseUserJourneyStatus.mockReturnValue({
        loading: false,
        error: false,
        shouldGoToDashboard: false,
        shouldGoToUpload: true,
        hasTransactions: false,
        hasCategorizedTransactions: false,
        categorizedPercentage: 0,
        totalTransactions: 0,
      });
    });

    it('should navigate to upload page when user has no transactions', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      expect(getByTestId('location')).toHaveTextContent('/upload');
    });
  });

  describe('when loading user journey status', () => {
    beforeEach(() => {
      mockUseUserJourneyStatus.mockReturnValue({
        loading: true,
        error: false,
        shouldGoToDashboard: false,
        shouldGoToUpload: false,
        hasTransactions: false,
        hasCategorizedTransactions: false,
        categorizedPercentage: 0,
        totalTransactions: 0,
      });
    });

    it('should show loading state while determining user journey', () => {
      const { getByText } = render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      expect(getByText(/determining your next step/i)).toBeInTheDocument();
    });
  });

  describe('when there is an error', () => {
    beforeEach(() => {
      mockUseUserJourneyStatus.mockReturnValue({
        loading: false,
        error: true,
        shouldGoToDashboard: false,
        shouldGoToUpload: true,
        hasTransactions: false,
        hasCategorizedTransactions: false,
        categorizedPercentage: 0,
        totalTransactions: 0,
      });
    });

    it('should navigate to upload page as fallback when there is an error', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      expect(getByTestId('location')).toHaveTextContent('/upload');
    });
  });

  describe('edge cases', () => {
    it('should handle partial categorization correctly (mixed state)', () => {
      mockUseUserJourneyStatus.mockReturnValue({
        loading: false,
        error: false,
        shouldGoToDashboard: true,
        shouldGoToUpload: false,
        hasTransactions: true,
        hasCategorizedTransactions: true,
        categorizedPercentage: 75,
        totalTransactions: 200,
      });

      const { getByTestId } = render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      expect(getByTestId('location')).toHaveTextContent('/dashboard');
    });

    it('should handle backend inconsistency gracefully', () => {
      // Simulate the backend bug we fixed - all transactions marked as "Uncategorized"
      mockUseUserJourneyStatus.mockReturnValue({
        loading: false,
        error: false,
        shouldGoToDashboard: false, // Fixed by our validation logic
        shouldGoToUpload: true,
        hasTransactions: true,
        hasCategorizedTransactions: false, // Corrected by validation
        categorizedPercentage: 0, // Corrected from backend claiming 100%
        totalTransactions: 100,
      });

      const { getByTestId } = render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      expect(getByTestId('location')).toHaveTextContent('/upload');
    });
  });

  describe('routing logic validation', () => {
    it('should prioritize dashboard when shouldGoToDashboard is true', () => {
      mockUseUserJourneyStatus.mockReturnValue({
        loading: false,
        error: false,
        shouldGoToDashboard: true,
        shouldGoToUpload: false,
        hasTransactions: true,
        hasCategorizedTransactions: true,
        categorizedPercentage: 100,
        totalTransactions: 100,
      });

      const { getByTestId } = render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      expect(getByTestId('location')).toHaveTextContent('/dashboard');
    });

    it('should default to upload when neither dashboard nor upload is recommended', () => {
      mockUseUserJourneyStatus.mockReturnValue({
        loading: false,
        error: false,
        shouldGoToDashboard: false,
        shouldGoToUpload: false, // Neither recommended
        hasTransactions: false,
        hasCategorizedTransactions: false,
        categorizedPercentage: 0,
        totalTransactions: 0,
      });

      const { getByTestId } = render(
        <TestWrapper>
          <IntelligentRoute />
        </TestWrapper>
      );

      // Should fallback to upload as default
      expect(getByTestId('location')).toHaveTextContent('/upload');
    });

    it('should use custom paths when provided', () => {
      mockUseUserJourneyStatus.mockReturnValue({
        loading: false,
        error: false,
        shouldGoToDashboard: true,
        shouldGoToUpload: false,
        hasTransactions: true,
        hasCategorizedTransactions: true,
        categorizedPercentage: 100,
        totalTransactions: 100,
      });

      const { getByTestId } = render(
        <TestWrapper>
          <IntelligentRoute dashboardPath="/custom-dashboard" uploadPath="/custom-upload" />
        </TestWrapper>
      );

      expect(getByTestId('location')).toHaveTextContent('/custom-dashboard');
    });
  });
});