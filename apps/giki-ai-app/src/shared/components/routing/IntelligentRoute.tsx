/**
 * Intelligent Route Component - Routes users based on their journey status
 * 
 * Journey Logic:
 * - New users (no transactions) → Upload
 * - Users with uncategorized transactions → Upload/Processing
 * - Users with categorized transactions → Dashboard
 */
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useUserJourneyStatus } from '@/shared/hooks/useUserJourneyStatus';

interface IntelligentRouteProps {
  dashboardPath?: string;
  uploadPath?: string;
  loadingComponent?: React.ReactNode;
}

const IntelligentRouteComponent: React.FC<IntelligentRouteProps> = ({
  dashboardPath = '/dashboard',
  uploadPath = '/upload',
  loadingComponent = (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <div
          className="w-8 h-8 border-4 border-t-transparent rounded-full animate-spin mx-auto mb-4"
          style={{ 
            borderColor: 'linear-gradient(135deg, #295343 0%, #1A3F5F 100%)', 
            borderTopColor: 'transparent' 
          }}
        />
        <p className="text-gray-600">Determining your next step...</p>
      </div>
    </div>
  ),
}) => {
  const journeyStatus = useUserJourneyStatus();

  // Show loading state while determining user status
  if (journeyStatus.loading) {
    return <>{loadingComponent}</>;
  }

  // On error, default to upload (safer for new users)
  if (journeyStatus.error) {
    return <Navigate to={uploadPath} replace />;
  }

  // Route based on journey status
  if (journeyStatus.shouldGoToDashboard) {
    return <Navigate to={dashboardPath} replace />;
  }

  // Default to upload flow
  return <Navigate to={uploadPath} replace />;
};

export const IntelligentRoute = React.memo(IntelligentRouteComponent);
IntelligentRoute.displayName = 'IntelligentRoute';