import React from 'react';
import { cn } from '@/shared/utils/utils';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface FinancialBadgeProps {
  value: number;
  type?: 'currency' | 'percentage' | 'number';
  trend?: 'up' | 'down' | 'neutral';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
  showIcon?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const FinancialBadge: React.FC<FinancialBadgeProps> = React.memo(({
  value,
  type = 'currency',
  trend,
  size = 'md',
  variant = 'default',
  showIcon = true,
  className,
  children,
}) => {
  // Determine variant based on value if not explicitly set
  const effectiveVariant =
    variant === 'default' && value !== undefined
      ? value > 0
        ? 'success'
        : value < 0
          ? 'error'
          : 'default'
      : variant;

  // Determine trend based on value if not explicitly set
  const effectiveTrend =
    trend || (value > 0 ? 'up' : value < 0 ? 'down' : 'neutral');

  const variantClasses = {
    default: 'bg-gray-100 text-gray-700 border-gray-200',
    success: 'bg-green-50 text-green-700 border-green-200',
    warning: 'bg-amber-50 text-amber-700 border-amber-200',
    error: 'bg-red-50 text-red-700 border-red-200',
  };

  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-1',
    lg: 'text-base px-3 py-1.5',
  };

  const iconSizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  const TrendIcon =
    effectiveTrend === 'up'
      ? TrendingUp
      : effectiveTrend === 'down'
        ? TrendingDown
        : Minus;

  const formatValue = () => {
    if (type === 'currency') {
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(Math.abs(value));
      return value < 0 ? `(${formatted})` : formatted;
    }

    if (type === 'percentage') {
      const sign = value > 0 ? '+' : '';
      return `${sign}${value.toFixed(1)}%`;
    }

    return value.toLocaleString();
  };

  return (
    <span
      className={cn(
        'inline-flex items-center gap-1 rounded-md border font-medium',
        variantClasses[effectiveVariant],
        sizeClasses[size],
        className,
      )}
    >
      {showIcon && <TrendIcon className={iconSizeClasses[size]} />}
      <span className="text-sm">{children || formatValue()}</span>
    </span>
  );
});

FinancialBadge.displayName = 'FinancialBadge';

// Specialized components for common use cases
export const CurrencyBadge: React.FC<Omit<FinancialBadgeProps, 'type'>> = React.memo((
  props,
) => <FinancialBadge {...props} type="currency" />);

CurrencyBadge.displayName = 'CurrencyBadge';

export const PercentageBadge: React.FC<Omit<FinancialBadgeProps, 'type'>> = React.memo((
  props,
) => <FinancialBadge {...props} type="percentage" />);

PercentageBadge.displayName = 'PercentageBadge';

export const VarianceBadge: React.FC<{
  actual: number;
  budget: number;
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = React.memo(({ actual, budget, showPercentage = true, size = 'md', className }) => {
  const variance = actual - budget;
  const variancePercent = budget !== 0 ? (variance / budget) * 100 : 0;

  return (
    <div className={cn('inline-flex items-center gap-2', className)}>
      <CurrencyBadge value={variance} size={size} />
      {showPercentage && (
        <PercentageBadge value={variancePercent} size={size} />
      )}
    </div>
  );
});

VarianceBadge.displayName = 'VarianceBadge';

export default FinancialBadge;
