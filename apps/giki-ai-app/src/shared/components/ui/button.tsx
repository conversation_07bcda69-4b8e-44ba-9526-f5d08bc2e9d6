import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/shared/utils/utils';
import { focusClasses, useReducedMotion } from '@/shared/utils/accessibility';

const buttonVariants = cva(
  `inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-button-base transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden ${focusClasses}`,
  {
    variants: {
      variant: {
        // Primary - Main action button with gradient
        default:
          'bg-gradient-to-r from-primary to-primary-hover text-primary-foreground hover:from-primary-hover hover:to-primary shadow-md hover:shadow-lg border border-primary/20',

        // Secondary - Alternative action
        secondary:
          'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm hover:shadow-md border border-border',

        // Outline - Secondary emphasis
        outline:
          'border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-accent',

        // Ghost - Minimal emphasis
        ghost: 'hover:bg-accent hover:text-accent-foreground',

        // Brand gradient - Full spectrum
        gradient:
          'bg-gradient-to-r from-[hsl(var(--giki-brand-green-dark))] via-[hsl(var(--giki-brand-blue-dark))] to-[hsl(var(--giki-brand-purple-dark))] text-white !font-semibold shadow-lg hover:shadow-xl border border-white/20',

        // Glass morphism
        glass:
          'bg-background/70 backdrop-blur-md border border-border/50 hover:bg-background/80 shadow-sm hover:shadow-md',

        // Success variant
        success:
          'bg-success text-success-foreground hover:bg-success/90 shadow-md hover:shadow-lg',

        // Destructive variant with better styling
        destructive:
          'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-md hover:shadow-lg',
      },
      size: {
        default: 'h-11 px-[var(--giki-space-4)] py-[var(--giki-space-2)]', // 44px minimum height
        sm: 'h-11 px-[var(--giki-space-3)] text-button-sm', // 44px minimum for accessibility
        lg: 'h-12 px-[var(--giki-space-8)] text-button-lg',
        icon: 'h-11 w-11', // 44px minimum square touch target
        xs: 'h-11 px-[var(--giki-space-2)] text-button-sm', // 44px minimum for accessibility
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  ripple?: boolean;
  loadingText?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading = false,
      ripple = true,
      loadingText,
      children,
      disabled,
      'aria-label': ariaLabel,
      'aria-pressed': ariaPressed,
      'aria-expanded': ariaExpanded,
      ...props
    },
    ref,
  ) => {
    const Comp = asChild ? Slot : 'button';
    const [ripples, setRipples] = React.useState<
      Array<{ x: number; y: number; id: number }>
    >([]);
    const prefersReducedMotion = useReducedMotion();

    // Handle ripple effect with reduced motion support
    const handleRipple = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (!ripple || disabled || loading || prefersReducedMotion) return;

      const button = event.currentTarget;
      const rect = button.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      const id = Date.now();

      setRipples((prev) => [...prev, { x, y, id }]);
      setTimeout(() => {
        setRipples((prev) => prev.filter((r) => r.id !== id));
      }, 600);
    };

    // Loading spinner component with better animation and accessibility
    const LoadingSpinner = () => (
      <div
        className="flex flex-wrap space-x-1 -ml-1 mr-2"
        role="status"
        aria-live="polite"
      >
        <span className="sr-only">{loadingText || 'Loading...'}</span>
        <div
          className={cn(
            'w-1 h-1 bg-current rounded-full',
            !prefersReducedMotion && 'animate-bounce',
          )}
          style={{ animationDelay: '0ms' }}
          aria-hidden="true"
        />
        <div
          className={cn(
            'w-1 h-1 bg-current rounded-full',
            !prefersReducedMotion && 'animate-bounce',
          )}
          style={{ animationDelay: '150ms' }}
          aria-hidden="true"
        />
        <div
          className={cn(
            'w-1 h-1 bg-current rounded-full',
            !prefersReducedMotion && 'animate-bounce',
          )}
          style={{ animationDelay: '300ms' }}
          aria-hidden="true"
        />
      </div>
    );

    // Content with loading state
    const content = (
      <>
        {loading && <LoadingSpinner />}
        {children}
      </>
    );

    // When asChild is true, we need to ensure only one React element child
    if (asChild) {
      return (
        <Slot
          className={cn(buttonVariants({ variant, size, className }))}
          ref={ref}
          {...props}
        >
          {React.isValidElement(children)
            ? React.cloneElement(
                children as React.ReactElement<
                  React.HTMLAttributes<HTMLElement>
                >,
                {
                  disabled: disabled || loading,
                  onMouseDown: handleRipple,
                  'aria-label': ariaLabel,
                  'aria-pressed': ariaPressed,
                  'aria-expanded': ariaExpanded,
                  'aria-busy': loading,
                  'aria-disabled': disabled || loading,
                },
              )
            : children}
        </Slot>
      );
    }

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={disabled || loading}
        type="button"
        onMouseDown={handleRipple}
        aria-label={ariaLabel}
        aria-pressed={ariaPressed}
        aria-expanded={ariaExpanded}
        aria-busy={loading}
        aria-disabled={disabled || loading}
        {...props}
      >
        {/* Ripple effects with reduced motion support */}
        {!prefersReducedMotion &&
          ripples.map(({ x, y, id }) => (
            <span
              key={id}
              className="absolute bg-white/30 rounded-full pointer-events-none animate-ripple"
              style={{
                left: x - 20,
                top: y - 20,
                width: 40,
                height: 40,
              }}
              aria-hidden="true"
            />
          ))}

        {/* Gradient overlay for hover effect on gradient variant */}
        {variant === 'gradient' && (
          <span
            className="absolute inset-0 bg-white/0 hover:bg-white/10 transition-colors duration-300 rounded-lg"
            aria-hidden="true"
          />
        )}

        {content}
      </Comp>
    );
  },
);
Button.displayName = 'Button';

export { Button, buttonVariants };
