/**
 * AI Accuracy Tooltip Component
 *
 * CEO-friendly explanations for AI accuracy metrics and categorization confidence scores.
 * Designed to help business users understand what the percentages mean in practical terms.
 */
import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/ui/tooltip';
import { Badge } from '@/shared/components/ui/badge';
import {
  HelpCircle,
  Target,
  CheckCircle,
  Brain,
  Award,
  TrendingUp,
} from 'lucide-react';
import { cn } from '@/shared/utils/utils';

interface AccuracyTooltipProps {
  children: React.ReactNode;
  type:
    | 'categorization'
    | 'confidence'
    | 'processing'
    | 'validation'
    | 'overall';
  value?: number;
  className?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
}

const getAccuracyExplanation = (
  type: AccuracyTooltipProps['type'],
  value?: number,
) => {
  const percentage = value ? Math.round(value * 100) : value;

  switch (type) {
    case 'categorization':
      return {
        title: 'AI Categorization Accuracy',
        icon: Brain,
        explanation: `${percentage}% of transactions were categorized correctly by AI. ${getCategorizationGuidance(percentage)}`,
        businessImpact: getCategorizationBusinessImpact(percentage),
        benchmark: getCategorizationBenchmark(percentage),
      };

    case 'confidence':
      return {
        title: 'AI Confidence Score',
        icon: Target,
        explanation: `The AI is ${percentage}% confident in this categorization. ${getConfidenceGuidance(percentage)}`,
        businessImpact: getConfidenceBusinessImpact(percentage),
        benchmark: getConfidenceBenchmark(percentage),
      };

    case 'processing':
      return {
        title: 'Processing Accuracy',
        icon: CheckCircle,
        explanation: `${percentage}% of your transactions have been successfully processed and categorized by AI.`,
        businessImpact:
          'Higher processing rates mean faster month-end close and reduced manual work.',
        benchmark: getBenchmarkText(percentage, 95, 85, 'processing'),
      };

    case 'validation':
      return {
        title: 'Validation Accuracy',
        icon: Award,
        explanation: `${percentage}% accuracy validated against your historical patterns and business rules.`,
        businessImpact:
          'Higher validation scores mean the AI understands your specific business patterns.',
        benchmark: getBenchmarkText(percentage, 90, 80, 'validation'),
      };

    case 'overall':
      return {
        title: 'Overall System Accuracy',
        icon: TrendingUp,
        explanation: `${percentage}% overall accuracy across categorization, validation, and processing.`,
        businessImpact:
          'This represents your complete AI-powered financial automation effectiveness.',
        benchmark: getBenchmarkText(percentage, 92, 85, 'overall'),
      };

    default:
      return {
        title: 'AI Accuracy Metric',
        icon: HelpCircle,
        explanation:
          'This metric shows how accurately the AI is performing its tasks.',
        businessImpact:
          'Higher accuracy means more reliable automation and less manual review needed.',
        benchmark: 'Aim for 85%+ for production use.',
      };
  }
};

const getCategorizationGuidance = (percentage?: number): string => {
  if (!percentage) return 'Review AI suggestions to improve accuracy.';
  if (percentage >= 95)
    return 'Excellent! Your AI is performing at enterprise level.';
  if (percentage >= 90)
    return 'Great performance for most business operations.';
  if (percentage >= 85)
    return 'Good accuracy, suitable for regular business use.';
  if (percentage >= 80)
    return 'Acceptable for initial setup, room for improvement.';
  return 'Review AI suggestions and provide feedback to improve accuracy.';
};

const getCategorizationBusinessImpact = (percentage?: number): string => {
  if (!percentage)
    return 'Accuracy improvements reduce manual categorization work.';
  if (percentage >= 95)
    return 'Saves 95%+ of manual categorization time. Ready for full automation.';
  if (percentage >= 90)
    return 'Saves 90%+ of manual work. Minimal review needed.';
  if (percentage >= 85)
    return 'Saves 85%+ of manual work. Standard business quality.';
  if (percentage >= 80)
    return 'Saves 80%+ of manual work. Some manual review recommended.';
  return 'Significant manual review required. Consider additional training data.';
};

const getCategorizationBenchmark = (percentage?: number): string => {
  if (!percentage)
    return 'Target: 85%+ for production use, 90%+ for enterprise.';
  if (percentage >= 95) return '⚬ Enterprise-grade performance (95%+)';
  if (percentage >= 90) return '□ Production-ready (90-94%)';
  if (percentage >= 85) return '□ Business-ready (85-89%)';
  if (percentage >= 80) return '∷ Acceptable for testing (80-84%)';
  return '↑ Needs improvement (<80%)';
};

const getConfidenceGuidance = (percentage?: number): string => {
  if (!percentage) return 'Review this categorization manually.';
  if (percentage >= 95) return 'Very high confidence - likely correct.';
  if (percentage >= 85) return 'High confidence - probably correct.';
  if (percentage >= 70) return 'Medium confidence - worth reviewing.';
  if (percentage >= 50) return 'Low confidence - should review manually.';
  return 'Very low confidence - definitely review manually.';
};

const getConfidenceBusinessImpact = (percentage?: number): string => {
  if (!percentage) return 'Low confidence suggestions may need manual review.';
  if (percentage >= 95) return 'Auto-approve recommended - very reliable.';
  if (percentage >= 85)
    return 'Batch approval suitable for high-volume processing.';
  if (percentage >= 70) return 'Individual review recommended for accuracy.';
  if (percentage >= 50) return 'Manual review required before approval.';
  return 'Requires immediate manual review and possibly additional training.';
};

const getConfidenceBenchmark = (percentage?: number): string => {
  if (!percentage) return 'Aim for 85%+ confidence for auto-approval.';
  if (percentage >= 95) return '⚬ Auto-approve level (95%+)';
  if (percentage >= 85) return '□ High confidence (85-94%)';
  if (percentage >= 70) return '∷ Medium confidence (70-84%)';
  if (percentage >= 50) return '∷ Low confidence (50-69%)';
  return '↑ Very low confidence (<50%)';
};

const getBenchmarkText = (
  percentage?: number,
  excellent = 95,
  good = 85,
  type = 'general',
): string => {
  if (!percentage)
    return `Target: ${good}%+ for production use, ${excellent}%+ for enterprise.`;
  if (percentage >= excellent)
    return `⚬ Excellent ${type} performance (${excellent}%+)`;
  if (percentage >= good)
    return `□ Good ${type} performance (${good}-${excellent - 1}%)`;
  if (percentage >= good - 10)
    return `∷ Acceptable ${type} performance (${good - 10}-${good - 1}%)`;
  return `↑ ${type} performance needs improvement (<${good - 10}%)`;
};

// Removed unused getAccuracyColor function

const getAccuracyBadgeVariant = (
  value?: number,
): 'default' | 'secondary' | 'destructive' | 'outline' => {
  if (!value) return 'outline';
  const percentage = value > 1 ? value : value * 100;
  if (percentage >= 90) return 'default'; // Green
  if (percentage >= 85) return 'secondary'; // Blue
  if (percentage >= 80) return 'outline'; // Yellow
  return 'destructive'; // Red
};

export const AccuracyTooltip: React.FC<AccuracyTooltipProps> = React.memo(({
  children,
  type,
  value,
  className,
  placement = 'top',
}) => {
  const explanation = getAccuracyExplanation(type, value);
  const IconComponent = explanation.icon;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'cursor-help inline-flex items-center gap-1',
              className,
            )}
          >
            {children}
            <HelpCircle className="h-3 w-3 text-muted-foreground hover:text-foreground transition-colors" />
          </div>
        </TooltipTrigger>
        <TooltipContent
          side={placement}
          className="max-w-sm p-4 space-y-3"
          sideOffset={5}
        >
          <div className="flex items-center gap-2">
            <IconComponent className="h-4 w-4 text-primary" />
            <span className="font-semibold text-sm">{explanation.title}</span>
          </div>

          <div className="space-y-2 text-xs">
            <p className="text-foreground">{explanation.explanation}</p>

            <div className="space-y-1">
              <p className="text-muted-foreground">
                <span className="font-medium">Business Impact:</span>{' '}
                {explanation.businessImpact}
              </p>

              <div className="flex items-center gap-2">
                <span className="font-medium text-muted-foreground">
                  Benchmark:
                </span>
                <Badge
                  variant={getAccuracyBadgeVariant(value)}
                  className="text-xs px-2 py-0.5"
                >
                  {explanation.benchmark}
                </Badge>
              </div>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
});

AccuracyTooltip.displayName = 'AccuracyTooltip';

// Convenience components for common use cases
export const CategorizationAccuracyTooltip: React.FC<{
  children: React.ReactNode;
  accuracy: number;
  className?: string;
}> = React.memo(({ children, accuracy, className }) => (
  <AccuracyTooltip type="categorization" value={accuracy} className={className}>
    {children}
  </AccuracyTooltip>
));

CategorizationAccuracyTooltip.displayName = 'CategorizationAccuracyTooltip';

export const ConfidenceScoreTooltip: React.FC<{
  children: React.ReactNode;
  confidence: number;
  className?: string;
}> = React.memo(({ children, confidence, className }) => (
  <AccuracyTooltip type="confidence" value={confidence} className={className}>
    {children}
  </AccuracyTooltip>
));

ConfidenceScoreTooltip.displayName = 'ConfidenceScoreTooltip';

export const ProcessingAccuracyTooltip: React.FC<{
  children: React.ReactNode;
  accuracy: number;
  className?: string;
}> = React.memo(({ children, accuracy, className }) => (
  <AccuracyTooltip type="processing" value={accuracy} className={className}>
    {children}
  </AccuracyTooltip>
));

ProcessingAccuracyTooltip.displayName = 'ProcessingAccuracyTooltip';

export const OverallAccuracyTooltip: React.FC<{
  children: React.ReactNode;
  accuracy: number;
  className?: string;
}> = React.memo(({ children, accuracy, className }) => (
  <AccuracyTooltip type="overall" value={accuracy} className={className}>
    {children}
  </AccuracyTooltip>
));

OverallAccuracyTooltip.displayName = 'OverallAccuracyTooltip';

export default AccuracyTooltip;
