import React from 'react';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { Button } from '@/shared/components/ui/button';
// Removed Lucide imports - replaced with geometric icons
// RefreshCw → ⟲, Home → □, ArrowLeft → ←, Bug → ⚠
// Wifi → ◈, Server → ▦, Shield → △, Clock → ◷, AlertTriangle → ⚠
import { extractErrorMessage } from '@/shared/utils/errorHandling';
import { cn } from '@/shared/utils/utils';

export interface UnifiedErrorProps {
  error?: unknown;
  title?: string;
  description?: string;
  variant?: 'error' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  showRetry?: boolean;
  showGoHome?: boolean;
  showGoBack?: boolean;
  onRetry?: () => void;
  onGoHome?: () => void;
  onGoBack?: () => void;
  className?: string;
  // Layout options
  layout?: 'inline' | 'centered' | 'full-page';
  // Icon customization
  showIcon?: boolean;
  customIcon?: React.ReactNode;
}

export const UnifiedError: React.FC<UnifiedErrorProps> = ({
  error,
  title,
  description,
  variant = 'error',
  size = 'md',
  showRetry = false,
  showGoHome = false,
  showGoBack = false,
  onRetry,
  onGoHome,
  onGoBack,
  className = '',
  layout = 'inline',
  showIcon = true,
  customIcon,
}) => {
  // Extract error message if error object is provided
  const errorMessage = error
    ? extractErrorMessage(error, 'An unexpected error occurred')
    : description || 'An unexpected error occurred';

  const finalTitle =
    title ||
    (variant === 'error'
      ? 'Something went wrong'
      : variant === 'warning'
        ? 'Warning'
        : 'Information');

  // Determine error type and styling based on error message
  const getErrorType = (message: string) => {
    if (
      message.toLowerCase().includes('network') ||
      message.toLowerCase().includes('fetch')
    ) {
      return {
        type: 'network',
        icon: <span className="h-6 w-6 text-2xl font-bold flex items-center justify-center">◈</span>,
        color: 'text-warning',
        bgColor: 'bg-warning/10',
        borderColor: 'border-warning/20',
      };
    }
    if (
      message.toLowerCase().includes('server') ||
      message.toLowerCase().includes('500')
    ) {
      return {
        type: 'server',
        icon: <span className="h-6 w-6 text-2xl font-bold flex items-center justify-center">▦</span>,
        color: 'text-destructive',
        bgColor: 'bg-destructive/10',
        borderColor: 'border-destructive/20',
      };
    }
    if (
      message.toLowerCase().includes('unauthorized') ||
      message.toLowerCase().includes('401')
    ) {
      return {
        type: 'auth',
        icon: <span className="h-6 w-6 text-2xl font-bold flex items-center justify-center">△</span>,
        color: 'text-warning',
        bgColor: 'bg-warning/10',
        borderColor: 'border-warning/20',
      };
    }
    if (message.toLowerCase().includes('timeout')) {
      return {
        type: 'timeout',
        icon: <span className="h-6 w-6 text-2xl font-bold flex items-center justify-center">◷</span>,
        color: 'text-warning',
        bgColor: 'bg-warning/10',
        borderColor: 'border-warning/20',
      };
    }
    return {
      type: 'generic',
      icon:
        variant === 'warning' ? (
          <span className="h-6 w-6 text-2xl font-bold flex items-center justify-center">⚠</span>
        ) : (
          <span className="h-6 w-6 text-2xl font-bold flex items-center justify-center">⚠</span>
        ),
      color:
        variant === 'error'
          ? 'text-destructive'
          : variant === 'warning'
            ? 'text-warning'
            : 'text-info',
      bgColor:
        variant === 'error'
          ? 'bg-destructive/10'
          : variant === 'warning'
            ? 'bg-warning/10'
            : 'bg-info/10',
      borderColor:
        variant === 'error'
          ? 'border-destructive/20'
          : variant === 'warning'
            ? 'border-warning/20'
            : 'border-info/20',
    };
  };

  const errorType = getErrorType(errorMessage);
  const displayIcon = customIcon || errorType.icon;

  // Size variants
  const sizeClasses = {
    sm: {
      container: 'p-4',
      icon: 'w-8 h-8',
      iconContainer: 'w-10 h-10',
      title: 'text-lg font-semibold',
      description: 'text-sm',
      button: 'text-sm px-3 py-1.5',
    },
    md: {
      container: 'p-6',
      icon: 'w-12 h-12',
      iconContainer: 'w-12 h-12',
      title: 'text-xl font-semibold',
      description: 'text-base',
      button: 'text-sm px-4 py-2',
    },
    lg: {
      container: 'p-8',
      icon: 'w-16 sm:w-16 h-16',
      iconContainer: 'w-16 sm:w-16 h-16',
      title: 'text-2xl font-bold',
      description: 'text-lg',
      button: 'text-base px-6 py-3',
    },
  };

  const sizes = sizeClasses[size];

  // Layout variants
  const layoutClasses = {
    inline: '',
    centered: 'flex flex-col items-center justify-center text-center',
    'full-page':
      'flex flex-col items-center justify-center min-h-[400px] overflow-y-auto text-center',
  };

  // Action handlers
  const handleGoHome = () => {
    if (onGoHome) {
      onGoHome();
    } else {
      window.location.href = '/';
    }
  };

  const handleGoBack = () => {
    if (onGoBack) {
      onGoBack();
    } else {
      window?.history?.back();
    }
  };

  // Render buttons
  const renderButtons = () => {
    const buttons = [];

    if (showRetry && onRetry) {
      buttons.push(
        <Button
          className={`max-w-full ${sizes.button}`}
          key="retry"
          onClick={() => void onRetry()}
          variant="default"
          size={size === 'lg' ? 'lg' : 'default'}
        >
          <span className="mr-2 h-4 w-4 text-lg font-bold flex items-center justify-center">⟲</span>
          Try Again
        </Button>,
      );
    }

    if (showGoBack) {
      buttons.push(
        <Button
          className={`max-w-full ${sizes.button}`}
          key="back"
          onClick={() => void handleGoBack()}
          variant="outline"
          size={size === 'lg' ? 'lg' : 'default'}
        >
          <span className="mr-2 h-4 w-4 text-lg font-bold flex items-center justify-center">←</span>
          Go Back
        </Button>,
      );
    }

    if (showGoHome) {
      buttons.push(
        <Button
          className={`max-w-full ${sizes.button}`}
          key="home"
          onClick={() => void handleGoHome()}
          variant="outline"
          size={size === 'lg' ? 'lg' : 'default'}
        >
          <span className="mr-2 h-4 w-4 text-lg font-bold flex items-center justify-center">□</span>
          Go Home
        </Button>,
      );
    }

    return buttons.length > 0 ? (
      <div className="flex flex-wrap flex-wrap gap-3 justify-center">
        {buttons}
      </div>
    ) : null;
  };

  return (
    <Alert
      className={cn(
        `${errorType.bgColor} ${errorType.borderColor} shadow-lg animate-slide-in`,
        layoutClasses[layout],
        sizes.container,
        className,
      )}
    >
      <div
        className={cn(
          'flex gap-4',
          layout === 'centered' || layout === 'full-page'
            ? 'flex-col items-center'
            : 'items-start',
        )}
      >
        {showIcon && (
          <div
            className={cn(
              `flex-shrink-0 ${errorType.bgColor} rounded-full flex items-center justify-center`,
              sizes.iconContainer,
              layout === 'centered' || layout === 'full-page' ? 'mb-4' : '',
            )}
          >
            <div className={cn(errorType.color, sizes.icon)}>{displayIcon}</div>
          </div>
        )}

        <div
          className={cn(
            'flex-1 space-y-4',
            layout === 'centered' || layout === 'full-page'
              ? 'text-center'
              : '',
          )}
        >
          <div>
            <AlertTitle className={cn(`${errorType.color} mb-2`, sizes.title)}>
              {finalTitle}
            </AlertTitle>
            <AlertDescription
              className={cn(
                'leading-relaxed text-text-secondary font-medium',
                sizes.description,
              )}
            >
              {errorMessage}
            </AlertDescription>
          </div>

          {renderButtons()}
        </div>
      </div>
    </Alert>
  );
};

// Specialized error components for common use cases
export const APIError: React.FC<{
  onRetry?: () => void;
  className?: string;
}> = ({ onRetry, className }) => (
  <UnifiedError
    title="Connection Error"
    description="Unable to connect to our servers. Please check your internet connection and try again."
    variant="error"
    showRetry={!!onRetry}
    onRetry={onRetry}
    showGoBack
    layout="centered"
    className={className}
  />
);

export const NotFoundError: React.FC<{ className?: string }> = ({
  className,
}) => (
  <UnifiedError
    title="Page Not Found"
    description="The page you're looking for doesn't exist or has been moved."
    variant="warning"
    showGoHome
    layout="full-page"
    className={className}
  />
);

export const PermissionError: React.FC<{ className?: string }> = ({
  className,
}) => (
  <UnifiedError
    title="Access Denied"
    description="You don't have permission to access this resource. Please contact your administrator."
    variant="warning"
    showGoHome
    showGoBack
    layout="centered"
    className={className}
  />
);

export const LoadingError: React.FC<{
  onRetry?: () => void;
  className?: string;
}> = ({ onRetry, className }) => (
  <UnifiedError
    title="Failed to Load Data"
    description="We couldn't load the requested data. This might be a temporary issue."
    variant="error"
    showRetry={!!onRetry}
    onRetry={onRetry}
    showGoBack
    layout="centered"
    className={className}
  />
);

export default UnifiedError;
