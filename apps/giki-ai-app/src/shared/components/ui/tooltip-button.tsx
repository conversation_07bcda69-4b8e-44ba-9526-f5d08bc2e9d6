import * as React from 'react';
import { Button, ButtonProps } from './button';
import { Tooltip, TooltipContent, TooltipTrigger } from './tooltip';

export interface TooltipButtonProps extends ButtonProps {
  tooltip?: string;
  tooltipSide?: 'top' | 'right' | 'bottom' | 'left';
  tooltipAlign?: 'start' | 'center' | 'end';
  delayDuration?: number;
}

/**
 * A Button component with built-in tooltip support.
 * Combines the Button and Tooltip components for consistent tooltip behavior.
 */
export const TooltipButton = React.forwardRef<
  HTMLButtonElement,
  TooltipButtonProps
>(
  (
    {
      tooltip,
      tooltipSide = 'top',
      tooltipAlign = 'center',
      delayDuration = 200,
      ...buttonProps
    },
    ref,
  ) => {
    // If no tooltip is provided, render a regular button
    if (!tooltip) {
      return <Button ref={ref} {...buttonProps} />;
    }

    return (
      <Tooltip delayDuration={delayDuration}>
        <TooltipTrigger asChild>
          <Button ref={ref} {...buttonProps} />
        </TooltipTrigger>
        <TooltipContent side={tooltipSide} align={tooltipAlign}>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    );
  },
);

TooltipButton.displayName = 'TooltipButton';
