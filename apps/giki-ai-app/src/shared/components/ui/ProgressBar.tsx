/**
 * ProgressBar Component
 *
 * A reusable progress bar component with customizable styling.
 */

import React from 'react';

interface ProgressBarProps {
  value: number; // 0-100
  className?: string;
  barClassName?: string;
  showLabel?: boolean;
}

export const ProgressBar: React.FC<ProgressBarProps> = React.memo(({
  value,
  className = '',
  barClassName = '',
  showLabel = false,
}) => {
  const clampedValue = Math.max(0, Math.min(100, value));

  return (
    <div className={`relative ${className}`} data-testid="progress-bar" data-value={clampedValue}>
      <div className="bg-[hsl(var(--giki-neutral-200))] rounded-full overflow-hidden">
        <div
          className={`bg-brand-primary h-full transition-all duration-300 ${barClassName}`}
          style={{ width: `${clampedValue}%` }}
        />
      </div>
      {showLabel && (
        <span className="absolute inset-0 flex flex-wrap items-center justify-center truncate text-caption font-medium">
          {clampedValue}%
        </span>
      )}
    </div>
  );
});

ProgressBar.displayName = 'ProgressBar';
