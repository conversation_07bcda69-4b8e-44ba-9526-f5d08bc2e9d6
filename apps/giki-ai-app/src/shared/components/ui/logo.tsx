import React from 'react';
import { cn } from '@/shared/utils/utils';

interface LogoProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'xxs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'monochrome' | 'text-only';
  showText?: boolean;
  isDarkBackground?: boolean;
}

const Logo: React.FC<LogoProps> = ({
  className,
  size = 'md',
  variant = 'default',
  showText = true,
  isDarkBackground = false,
  ...props
}) => {
  // Adjusted size classes with proper aspect ratio to prevent clipping
  const sizeClasses = {
    xxs: 'h-5 w-4', // Maintain 5:4 aspect ratio
    xs: 'h-6 w-5',
    sm: 'h-8 w-6',
    md: 'h-10 w-8',
    lg: 'h-12 w-10',
    xl: 'h-16 w-12', // Fixed: w-13 is not a valid Tailwind class
  };

  const textSizeClasses = {
    xxs: 'text-xs',
    xs: 'text-sm',
    sm: 'text-base',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl',
  };

  const getColorClasses = () => {
    if (variant === 'monochrome') {
      return {
        primary: isDarkBackground ? 'text-white' : 'text-foreground',
        secondary: isDarkBackground ? 'text-white' : 'text-foreground',
      };
    }

    if (isDarkBackground) {
      return {
        primary: 'text-white', // White "giki" on dark background
        secondary: 'text-[hsl(var(--giki-brand-green))]', // Light green ".ai" for accent
      };
    }

    return {
      primary: 'text-primary', // Dark green #0D4F12 for "giki"
      secondary: 'text-foreground', // Black text for ".ai" for proper contrast
    };
  };

  const colors = getColorClasses();
  // Use SVG as primary, PNG as fallback
  const logoSrc = '/images/giki-logo.svg';
  const fallbackLogoSrc = '/images/giki-logo.png';

  return (
    <div className={cn('flex items-center gap-2', className)} {...props}>
      {variant !== 'text-only' && (
        <div className="flex flex-wrap items-center justify-center p-0 overflow-visible">
          <img
            src={logoSrc}
            alt="Giki.ai Logo"
            className={cn(sizeClasses[size], 'object-contain object-center')}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = fallbackLogoSrc;
              target.onerror = () => {
                const logoContainer = target.parentElement;
                if (logoContainer) {
                  logoContainer.style.display = 'none';
                }
              };
            }}
          />
        </div>
      )}
      {showText && (
        <div
          className={cn('font-bold', textSizeClasses[size])}
          style={{ fontFamily: 'var(--giki-font-family-display)' }}
        >
          <span className={colors.primary}>giki</span>
          <span className={colors.secondary}>.ai</span>
        </div>
      )}
    </div>
  );
};

export default Logo;
