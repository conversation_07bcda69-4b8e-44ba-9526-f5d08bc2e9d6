/**
 * Professional Empty States Component System
 * 
 * Provides consistent empty state experiences across the application
 * with brand-compliant geometric icons and professional messaging
 */
import React from 'react';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { cn } from '@/shared/utils/utils';

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline';
  };
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  layout?: 'centered' | 'inline';
}

export function EmptyState({
  title,
  description,
  icon = '□',
  action,
  className,
  size = 'md',
  layout = 'centered',
}: EmptyStateProps) {
  const sizeClasses = {
    sm: {
      container: 'py-8 px-4',
      icon: 'text-4xl mb-4',
      title: 'text-lg font-semibold',
      description: 'text-sm',
      button: 'text-sm px-4 py-2',
    },
    md: {
      container: 'py-12 px-6',
      icon: 'text-6xl mb-6',
      title: 'text-xl font-semibold',
      description: 'text-base',
      button: 'text-base px-6 py-3',
    },
    lg: {
      container: 'py-16 px-8',
      icon: 'text-8xl mb-8',
      title: 'text-2xl font-bold',
      description: 'text-lg',
      button: 'text-lg px-8 py-4',
    },
  };

  const sizes = sizeClasses[size];

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center text-center',
        layout === 'centered' ? 'min-h-[400px]' : '',
        sizes.container,
        className,
      )}
    >
      <div className={cn('text-muted-foreground mb-4', sizes.icon)}>
        {icon}
      </div>
      <h3 className={cn('text-primary-foreground mb-2', sizes.title)}>
        {title}
      </h3>
      <p className={cn('text-muted-foreground max-w-md leading-relaxed mb-6', sizes.description)}>
        {description}
      </p>
      {action && (
        <Button
          onClick={action.onClick}
          variant={action.variant || 'default'}
          className={cn('transition-all duration-200', sizes.button)}
        >
          {action.label}
        </Button>
      )}
    </div>
  );
}

// Specialized empty state components for common scenarios
export const EmptyTransactions = ({ onUpload }: { onUpload?: () => void }) => (
  <EmptyState
    icon="□"
    title="No Transactions Found"
    description="Upload your first transaction file to get started with AI-powered categorization and insights."
    action={onUpload ? {
      label: "Upload Transactions",
      onClick: onUpload,
    } : undefined}
    size="lg"
  />
);

export const EmptyCategories = ({ onCreateCategory }: { onCreateCategory?: () => void }) => (
  <EmptyState
    icon="⚬"
    title="No Categories Configured"
    description="Create your first category to start organizing your transactions with professional hierarchy and rules."
    action={onCreateCategory ? {
      label: "Create Category",
      onClick: onCreateCategory,
    } : undefined}
    size="md"
  />
);

export const EmptyReports = ({ onGenerateReport }: { onGenerateReport?: () => void }) => (
  <EmptyState
    icon="∷"
    title="No Reports Generated"
    description="Generate your first financial report to gain insights into your spending patterns and business performance."
    action={onGenerateReport ? {
      label: "Generate Report",
      onClick: onGenerateReport,
    } : undefined}
    size="md"
  />
);

export const EmptySearchResults = ({ searchQuery, onClearSearch }: { 
  searchQuery?: string; 
  onClearSearch?: () => void;
}) => (
  <EmptyState
    icon="⌕"
    title="No Results Found"
    description={
      searchQuery 
        ? `No results found for "${searchQuery}". Try adjusting your search terms or filters.`
        : "Your search didn't return any results. Try different keywords or filters."
    }
    action={onClearSearch ? {
      label: "Clear Search",
      onClick: onClearSearch,
      variant: "outline",
    } : undefined}
    size="sm"
    layout="inline"
  />
);

export const EmptyData = ({ 
  dataType = "data",
  onRefresh 
}: { 
  dataType?: string;
  onRefresh?: () => void;
}) => (
  <EmptyState
    icon="◯"
    title={`No ${dataType} Available`}
    description={`There's no ${dataType} to display at the moment. This could be temporary or you may need to add some ${dataType} first.`}
    action={onRefresh ? {
      label: "Refresh",
      onClick: onRefresh,
      variant: "outline",
    } : undefined}
    size="sm"
  />
);

export const EmptyFiltered = ({ 
  onClearFilters,
  filterCount = 0
}: { 
  onClearFilters?: () => void;
  filterCount?: number;
}) => (
  <EmptyState
    icon="∷"
    title="No Matching Results"
    description={`Your current filters (${filterCount} active) don't match any items. Try adjusting or removing some filters.`}
    action={onClearFilters ? {
      label: "Clear All Filters",
      onClick: onClearFilters,
      variant: "outline",
    } : undefined}
    size="sm"
    layout="inline"
  />
);

// Card-based empty states for inline use
export const EmptyCard = ({ 
  title, 
  description, 
  icon = "□",
  action,
  className 
}: Omit<EmptyStateProps, 'size' | 'layout'>) => (
  <Card className={cn('', className)}>
    <CardContent className="p-8">
      <EmptyState
        title={title}
        description={description}
        icon={icon}
        action={action}
        size="sm"
        layout="inline"
      />
    </CardContent>
  </Card>
);

export default EmptyState;