import * as React from 'react';
import * as ProgressPrimitive from '@radix-ui/react-progress';

import { cn } from '@/shared/utils/utils';
import { announce } from '@/shared/utils/accessibility';

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & {
    variant?: 'default' | 'success' | 'warning' | 'destructive' | 'accent';
    size?: 'sm' | 'default' | 'lg';
    label?: string;
    showValue?: boolean;
    announceProgress?: boolean;
  }
>(
  (
    {
      className,
      value,
      variant = 'default',
      size = 'default',
      label,
      showValue = false,
      announceProgress = true,
      ...props
    },
    ref,
  ) => {
    const sizeClasses = {
      sm: 'h-2',
      default: 'h-3',
      lg: 'h-4',
    };

    const variantClasses = {
      default: 'bg-primary',
      success: 'bg-success',
      warning: 'bg-warning',
      destructive: 'bg-destructive',
      accent: 'bg-accent',
    };

    // Announce progress changes to screen readers
    React.useEffect(() => {
      if (announceProgress && value !== undefined) {
        const message = label
          ? `${label}: ${value}% complete`
          : `Progress: ${value}% complete`;
        announce(message, 'polite');
      }
    }, [value, label, announceProgress]);

    const progressValue = value || 0;

    return (
      <div className="w-full">
        {(label || showValue) && (
          <div className="flex justify-between mb-1">
            {label && (
              <span className="text-sm font-medium text-foreground">
                {label}
              </span>
            )}
            {showValue && (
              <span className="text-sm text-muted-foreground">
                {progressValue}%
              </span>
            )}
          </div>
        )}
        <ProgressPrimitive.Root
          ref={ref}
          className={cn(
            'relative overflow-hidden rounded-full bg-muted shadow-inner',
            sizeClasses[size],
            className,
          )}
          aria-valuemin={0}
          aria-valuemax={100}
          aria-valuenow={progressValue}
          aria-label={label || 'Progress'}
          role="progressbar"
          {...props}
        >
          <ProgressPrimitive.Indicator
            className={cn(
              'h-full w-full flex-1 transition-all duration-500 ease-out rounded-full shadow-sm',
              variantClasses[variant],
            )}
            style={{ transform: `translateX(-${100 - progressValue}%)` }}
          />
        </ProgressPrimitive.Root>
      </div>
    );
  },
);
Progress.displayName = ProgressPrimitive?.Root?.displayName;

export { Progress };
