import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON>w, Home, ArrowLeft } from 'lucide-react';
import { Button } from './button';
import { Alert, AlertDescription, AlertTitle } from './alert';

interface ErrorStateProps {
  title?: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  showHomeButton?: boolean;
  showBackButton?: boolean;
  variant?: 'error' | 'warning' | 'info';
  className?: string;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'Something went wrong',
  description = 'We encountered an unexpected error. Please try again or contact support if the problem persists.',
  actionLabel = 'Try Again',
  onAction,
  showHomeButton = false,
  showBackButton = false,
  variant = 'error',
  className = '',
}) => {
  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleGoBack = () => {
    window.history.back();
  };

  const alertVariant =
    variant === 'error'
      ? 'destructive'
      : variant === 'warning'
        ? 'warning'
        : 'info';

  return (
    <div
      className={`flex flex-col items-center justify-center min-h-[400px] overflow-y-auto p-8 text-center space-y-6 ${className}`}
    >
      <div className="w-16 sm:w-16 h-16 rounded-full bg-destructive/10 flex flex-wrap items-center justify-center mb-4">
        <AlertTriangle className="w-8 h-8 text-destructive" />
      </div>

      <div className="space-y-2 max-w-md">
        <h2 className="text-2xl font-bold text-foreground">{title}</h2>
        <p className="truncate[hsl(var(--giki-text-primary))] leading-relaxed">
          {description}
        </p>
      </div>

      <Alert variant={alertVariant} className="max-w-md">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error Details</AlertTitle>
        <AlertDescription>
          If this error persists, please check your internet connection or
          contact our support team for assistance.
        </AlertDescription>
      </Alert>

      <div className="flex flex-wrap flex-col sm:flex-row gap-3 pt-4">
        {onAction && (
          <Button className="max-w-full gap-2" onClick={() => void onAction()}>
            <RefreshCw className="w-4 h-4" />
            {actionLabel}
          </Button>
        )}

        {showBackButton && (
          <Button
            className="max-w-full gap-2"
            variant="outline"
            onClick={() => void handleGoBack()}
          >
            <ArrowLeft className="w-4 h-4" />
            Go Back
          </Button>
        )}

        {showHomeButton && (
          <Button
            className="max-w-full gap-2"
            variant="outline"
            onClick={() => void handleGoHome()}
          >
            <Home className="w-4 h-4" />
            Go Home
          </Button>
        )}
      </div>
    </div>
  );
};

// Specialized error components for common use cases
export const APIErrorState: React.FC<{ onRetry?: () => void }> = ({
  onRetry,
}) => (
  <ErrorState
    title="Connection Error"
    description="Unable to connect to our servers. Please check your internet connection and try again."
    actionLabel="Retry Connection"
    onAction={onRetry}
    showBackButton
    variant="error"
  />
);

export const NotFoundErrorState: React.FC = () => (
  <ErrorState
    title="Page Not Found"
    description="The page you're looking for doesn't exist or has been moved."
    actionLabel="Go to Dashboard"
    onAction={() => (window.location.href = '/')}
    showHomeButton
    variant="warning"
  />
);

export const PermissionErrorState: React.FC = () => (
  <ErrorState
    title="Access Denied"
    description="You don't have permission to access this resource. Please contact your administrator."
    showHomeButton
    showBackButton
    variant="warning"
  />
);

export const LoadingErrorState: React.FC<{ onRetry?: () => void }> = ({
  onRetry,
}) => (
  <ErrorState
    title="Failed to Load Data"
    description="We couldn't load the requested data. This might be a temporary issue."
    actionLabel="Reload Data"
    onAction={onRetry}
    showBackButton
    variant="error"
  />
);
