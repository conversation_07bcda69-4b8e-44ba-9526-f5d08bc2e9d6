/**
 * Text with Ellipsis Component
 *
 * Handles text overflow with proper ellipsis and optional tooltip
 * for showing full text on hover.
 */

import React, { useRef, useEffect, useState } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/shared/components/ui/tooltip';

interface TextWithEllipsisProps {
  children: React.ReactNode;
  className?: string;
  lines?: 1 | 2 | 3;
  showTooltip?: boolean;
  tooltipContent?: string;
  maxWidth?: string;
}

export const TextWithEllipsis: React.FC<TextWithEllipsisProps> = React.memo(({
  children,
  className = '',
  lines = 1,
  showTooltip = true,
  tooltipContent,
  maxWidth = '100%',
}) => {
  const textRef = useRef<HTMLDivElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);

  // Check if text is overflowing
  useEffect(() => {
    const checkOverflow = () => {
      if (textRef.current) {
        const { scrollHeight, clientHeight, scrollWidth, clientWidth } = textRef.current;
        const hasOverflow = lines === 1 
          ? scrollWidth > clientWidth 
          : scrollHeight > clientHeight;
        setIsOverflowing(hasOverflow);
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    return () => window.removeEventListener('resize', checkOverflow);
  }, [children, lines]);

  const ellipsisClass = lines === 1 
    ? 'text-ellipsis' 
    : `text-ellipsis-${lines}`;

  const textElement = (
    <div
      ref={textRef}
      className={`${ellipsisClass} ${className}`}
      style={{ maxWidth }}
      title={!showTooltip && isOverflowing ? String(children) : undefined}
    >
      {children}
    </div>
  );

  // Show tooltip only if text is overflowing and tooltip is enabled
  if (showTooltip && isOverflowing) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {textElement}
        </TooltipTrigger>
        <TooltipContent side="top" align="start" className="max-w-sm">
          <p className="whitespace-pre-wrap break-words">
            {tooltipContent || children}
          </p>
        </TooltipContent>
      </Tooltip>
    );
  }

  return textElement;
});

TextWithEllipsis.displayName = 'TextWithEllipsis';

export default TextWithEllipsis;