import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/shared/utils/utils';

const badgeVariants = cva(
  'inline-flex items-center rounded-lg border px-3 py-1 text-status-badge transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary text-primary-foreground shadow-sm hover:bg-primary/90',
        secondary:
          'border-transparent bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/90',
        destructive:
          'border-transparent bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',
        success:
          'border-transparent bg-success text-success-foreground shadow-sm hover:bg-success/90',
        warning:
          'border-transparent bg-warning text-warning-foreground shadow-sm hover:bg-warning/90',
        outline: 'border-border text-foreground bg-transparent hover:bg-muted',
        accent:
          'border-transparent bg-accent text-accent-foreground shadow-sm hover:bg-accent/90',
        muted: 'border-transparent bg-muted text-foreground hover:bg-muted/80',
      },
      size: {
        default: 'px-3 py-1',
        sm: 'px-2 py-0.5 rounded-md',
        lg: 'px-4 py-1.5 rounded-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div
      className={cn(badgeVariants({ variant, size }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
