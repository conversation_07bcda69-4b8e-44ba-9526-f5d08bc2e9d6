/**
 * Excel-Inspired Layout Component
 *
 * Professional layout system mimicking Excel's worksheet structure with
 * header bars, formula bars, and grid-based content organization.
 */
import React from 'react';
import { cn } from '@/shared/utils/utils';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import {
  Calculator,
  Download,
  Filter,
  RefreshCw,
  Settings,
  Grid3x3,
  BarChart3,
  PieChart,
} from 'lucide-react';

interface ExcelLayoutProps {
  title: string;
  subtitle?: string;
  showFormulaBar?: boolean;
  showToolbar?: boolean;
  actions?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

interface ExcelToolbarProps {
  onRefresh?: () => void;
  onFilter?: () => void;
  onExport?: () => void;
  onSettings?: () => void;
  customActions?: React.ReactNode;
}

interface ExcelFormulaBarProps {
  formula?: string;
  onFormulaChange?: (formula: string) => void;
  readOnly?: boolean;
}

const ExcelToolbar: React.FC<ExcelToolbarProps> = ({
  onRefresh,
  onFilter,
  onExport,
  onSettings,
  customActions,
}) => (
  <div className="excel-toolbar flex flex-wrap items-center justify-between bg-[#f8f9fa] border-b border-[#dee2e6] px-4 py-2">
    <div className="flex flex-wrap items-center gap-1">
      {/* File Operations */}
      <div className="flex flex-wrap items-center gap-1 pr-3 border-r border-[#dee2e6]">
        <Button
          className="max-w-full h-8 px-2 truncate text-caption hover:bg-[#e9ecef]"
          variant="ghost"
          size="sm"
          onClick={() => void onRefresh()}
          title="Refresh Data"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
        <Button
          className="max-w-full h-8 px-2 truncate text-caption hover:bg-[#e9ecef]"
          variant="ghost"
          size="sm"
          onClick={() => void onExport()}
          title="Export Data"
        >
          <Download className="h-3 w-3" />
        </Button>
      </div>

      {/* Data Operations */}
      <div className="flex flex-wrap items-center gap-1 pr-3 border-r border-[#dee2e6]">
        <Button
          className="max-w-full h-8 px-2 truncate text-caption hover:bg-[#e9ecef]"
          variant="ghost"
          size="sm"
          onClick={() => void onFilter()}
          title="Filter Data"
        >
          <Filter className="h-3 w-3" />
        </Button>
        <Button
          className="max-w-full h-8 px-2 truncate text-caption hover:bg-[#e9ecef]"
          variant="ghost"
          size="sm"
          title="Calculations"
        >
          <Calculator className="h-3 w-3" />
        </Button>
      </div>

      {/* View Operations */}
      <div className="flex flex-wrap items-center gap-1 pr-3 border-r border-[#dee2e6]">
        <Button
          className="max-w-full h-8 px-2 truncate text-caption hover:bg-[#e9ecef]"
          variant="ghost"
          size="sm"
          title="Grid View"
        >
          <Grid3x3 className="h-3 w-3" />
        </Button>
        <Button
          className="max-w-full h-8 px-2 truncate text-caption hover:bg-[#e9ecef]"
          variant="ghost"
          size="sm"
          title="Chart View"
        >
          <BarChart3 className="h-3 w-3" />
        </Button>
        <Button
          className="max-w-full h-8 px-2 truncate text-caption hover:bg-[#e9ecef]"
          variant="ghost"
          size="sm"
          title="Analytics View"
        >
          <PieChart className="h-3 w-3" />
        </Button>
      </div>

      {customActions}
    </div>

    <div className="flex flex-wrap items-center gap-2">
      <Button
        className="max-w-full h-8 px-2 truncate text-caption hover:bg-[#e9ecef]"
        variant="ghost"
        size="sm"
        onClick={() => void onSettings()}
        title="Settings"
      >
        <Settings className="h-3 w-3" />
      </Button>
    </div>
  </div>
);

const ExcelFormulaBar: React.FC<ExcelFormulaBarProps> = ({
  formula = '',
  onFormulaChange,
  readOnly = false,
}) => (
  <div className="excel-formula-bar flex flex-wrap items-center bg-white border-b border-[#dee2e6] px-4 py-2">
    <div className="flex flex-wrap items-center gap-3 w-full">
      <div className="flex flex-wrap items-center gap-2">
        <span className="truncate text-caption font-medium text-[#495057] min-w-0">
          fx
        </span>
        <div className="h-4 w-px bg-[#dee2e6]"></div>
      </div>

      <div className="flex flex-wrap-1">
        <Input
          value={formula}
          onChange={(e) => onFormulaChange?.(e?.target?.value)}
          placeholder="Enter formula or search..."
          className="h-7 truncate text-caption border-none shadow-none text-financial"
          readOnly={readOnly}
        />
      </div>
    </div>
  </div>
);

const ExcelLayout: React.FC<ExcelLayoutProps> = ({
  title,
  subtitle,
  showFormulaBar = false,
  showToolbar = true,
  actions,
  children,
  className,
}) => {
  return (
    <div
      className={cn('excel-layout flex flex-col h-full bg-white', className)}
    >
      {/* Title Bar */}
      <div className="excel-title-bar bg-[hsl(var(--giki-primary))] truncatewhite px-4 py-3">
        <div className="flex flex-wrap items-center justify-between">
          <div>
            <h1 className="text-card-foreground-title m-0">{title}</h1>
            {subtitle && (
              <p className="text-sm opacity-90 mt-1 m-0">{subtitle}</p>
            )}
          </div>

          {actions && (
            <div className="flex flex-wrap items-center gap-2">{actions}</div>
          )}
        </div>
      </div>

      {/* Toolbar */}
      {showToolbar && <ExcelToolbar />}

      {/* Formula Bar */}
      {showFormulaBar && <ExcelFormulaBar />}

      {/* Column Headers */}
      <div className="excel-column-headers bg-[#f8f9fa] border-b border-[#dee2e6] px-4 py-2">
        <div className="flex flex-wrap items-center gap-4 truncate text-caption text-[#6c757d]">
          <div className="flex flex-wrap items-center gap-2">
            <span>Ready</span>
            <div className="h-3 w-px bg-[#dee2e6]"></div>
          </div>
          <div className="flex flex-wrap items-center gap-4">
            <span>NUM</span>
            <span>CAPS</span>
            <span>SCRL</span>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="excel-content flex flex-wrap-1 overflow-auto bg-white">
        <div className="excel-worksheet p-0">{children}</div>
      </div>

      <style>{`
        .excel-layout {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          border: 1px solid #dee2e6;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: var(--giki-shadow-lg);
        }
        
        .excel-title-bar {
          background: linear-gradient(135deg, hsl(var(--giki-primary)) 0%, hsl(var(--giki-primary-hover)) 100%);
        }
        
        .excel-worksheet {
          background-image: 
            linear-gradient(hsla(var(--giki-neutral-300), 0.3) 1px, transparent 1px),
            linear-gradient(90deg, hsla(var(--giki-neutral-300), 0.3) 1px, transparent 1px);
          background-size: 50px 50px;
          min-height: 100%;
        }
        
        .excel-content {
          position: relative;
        }
        
        .excel-content::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 40px;
          height: 100%;
          background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
          border-right: 1px solid #dee2e6;
          z-index: 10;
        }
        
        /* Row numbers */
        .excel-content::after {
          content: '1\\A 2\\A 3\\A 4\\A 5\\A 6\\A 7\\A 8\\A 9\\A 10\\A 11\\A 12\\A 13\\A 14\\A 15\\A 16\\A 17\\A 18\\A 19\\A 20';
          position: absolute;
          top: 0;
          left: 0;
          width: 40px;
          padding: 50px 8px 0 8px;
          font-size: 11px;
          line-height: 50px;
          color: #6c757d;
          text-align: center;
          white-space: pre;
          z-index: 20;
          pointer-events: none;
        }
      `}</style>
    </div>
  );
};

export { ExcelLayout, ExcelToolbar, ExcelFormulaBar };
export default ExcelLayout;
