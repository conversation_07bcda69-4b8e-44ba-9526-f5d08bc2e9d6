/**
 * Error Boundary Component
 *
 * Provides standardized error handling and fallback UI for React components.
 * Implements proper error isolation to prevent cascading failures.
 * Enhanced with:
 * - Comprehensive error logging
 * - Automatic error recovery attempts
 * - User-friendly error messages
 * - Performance impact tracking
 */
import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { AlertTriangle, RefreshCw, Home, Mail } from 'lucide-react';
import { logger } from '@/shared/lib/logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  componentName?: string;
  enableAutoRecovery?: boolean;
  maxRetries?: number;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  retryCount: number;
  isRecovering: boolean;
}

export type ErrorType =
  | 'CHUNK_LOAD'
  | 'NETWORK'
  | 'PERMISSION'
  | 'VALIDATION'
  | 'UNKNOWN';

export interface ErrorContext {
  type: ErrorType;
  componentName?: string;
  timestamp: Date;
  userAgent: string;
  url: string;
  stackTrace?: string;
}

export class ErrorBoundary extends Component<Props, State> {
  private autoRecoveryTimer?: NodeJS.Timeout;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      retryCount: 0,
      isRecovering: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorType = this.classifyError(error);
    const errorContext: ErrorContext = {
      type: errorType,
      componentName: this.props.componentName,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      stackTrace: errorInfo.componentStack,
    };

    // Log error with context
    logger.error('Component error boundary caught error', {
      error: error.message,
      errorType,
      componentName: this.props.componentName,
      stackTrace: error.stack,
      componentStack: errorInfo.componentStack,
      ...errorContext,
    });

    // Store error info for display
    this.setState({ errorInfo });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Attempt auto-recovery for certain error types
    if (
      this.props.enableAutoRecovery &&
      this.shouldAttemptAutoRecovery(errorType)
    ) {
      this.attemptAutoRecovery();
    }
  }

  componentWillUnmount() {
    if (this.autoRecoveryTimer) {
      clearTimeout(this.autoRecoveryTimer);
    }
  }

  private classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase();

    if (message.includes('chunk') || message.includes('loading css chunk')) {
      return 'CHUNK_LOAD';
    }
    if (message.includes('network') || message.includes('fetch')) {
      return 'NETWORK';
    }
    if (message.includes('permission') || message.includes('unauthorized')) {
      return 'PERMISSION';
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return 'VALIDATION';
    }

    return 'UNKNOWN';
  }

  private shouldAttemptAutoRecovery(errorType: ErrorType): boolean {
    const maxRetries = this.props.maxRetries ?? 3;
    return (
      this.state.retryCount < maxRetries &&
      (errorType === 'CHUNK_LOAD' || errorType === 'NETWORK')
    );
  }

  private attemptAutoRecovery = () => {
    this.setState({ isRecovering: true });

    this.autoRecoveryTimer = setTimeout(() => {
      logger.info('Attempting automatic error recovery', {
        componentName: this.props.componentName,
        retryCount: this.state.retryCount + 1,
      });

      this.handleRetry();
    }, 2000); // Wait 2 seconds before retry
  };

  handleRetry = () => {
    this.setState((prevState) => ({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: prevState.retryCount + 1,
      isRecovering: false,
    }));
  };

  handleNavigateHome = () => {
    window.location.href = '/';
  };

  private getErrorMessage(): string {
    const errorType = this.state.error
      ? this.classifyError(this.state.error)
      : 'UNKNOWN';

    switch (errorType) {
      case 'CHUNK_LOAD':
        return 'The application needs to be refreshed to load the latest version.';
      case 'NETWORK':
        return 'Network connection issue. Please check your internet connection.';
      case 'PERMISSION':
        return "You don't have permission to access this feature.";
      case 'VALIDATION':
        return 'Invalid data detected. Please check your input and try again.';
      default:
        return 'We encountered an unexpected error. Please try again.';
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const errorType = this.state.error
        ? this.classifyError(this.state.error)
        : 'UNKNOWN';
      const isDevelopment = process.env.NODE_ENV === 'development';

      return (
        <Card className="max-w-md mx-auto mt-8">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle className="text-destructive">
              {errorType === 'CHUNK_LOAD'
                ? 'Update Required'
                : 'Something went wrong'}
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground text-sm">
              {this.getErrorMessage()}
            </p>

            {isDevelopment && this.state.error && (
              <details className="text-left text-xs">
                <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                  Error details
                </summary>
                <pre className="mt-2 p-2 bg-muted rounded overflow-auto max-h-40">
                  {this.state.error.stack}
                </pre>
              </details>
            )}

            {this.state.isRecovering && (
              <p className="text-sm text-muted-foreground animate-pulse">
                Attempting automatic recovery...
              </p>
            )}

            <div className="flex flex-col gap-2">
              <Button
                className="w-full"
                onClick={this.handleRetry}
                variant="outline"
                disabled={this.state.isRecovering}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                {errorType === 'CHUNK_LOAD'
                  ? 'Refresh Application'
                  : 'Try Again'}
              </Button>

              <Button
                className="w-full"
                onClick={this.handleNavigateHome}
                variant="ghost"
              >
                <Home className="mr-2 h-4 w-4" />
                Go to Dashboard
              </Button>
            </div>

            {this.state.retryCount > 2 && (
              <p className="text-xs text-muted-foreground">
                Still having issues?{' '}
                <a href="mailto:<EMAIL>" className="underline">
                  <Mail className="inline h-3 w-3 mr-1" />
                  Contact support
                </a>
              </p>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Hook-based error boundary for functional components
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);
  const [isRecovering, setIsRecovering] = React.useState(false);

  const resetError = React.useCallback(() => {
    setError(null);
    setIsRecovering(false);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    logger.error('Error caught by useErrorHandler', {
      error: error.message,
      stack: error.stack,
      url: window.location.href,
    });
    setError(error);
  }, []);

  const recoverWithFallback = React.useCallback(
    async (fallbackAction: () => Promise<void>) => {
      setIsRecovering(true);
      try {
        await fallbackAction();
        resetError();
      } catch (fallbackError) {
        logger.error('Fallback recovery failed', { error: fallbackError });
        setIsRecovering(false);
      }
    },
    [resetError],
  );

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { handleError, resetError, recoverWithFallback, isRecovering };
}

// Async error boundary wrapper
export function AsyncErrorBoundary({
  children,
  fallback,
  onError,
}: {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error) => void;
}) {
  const { handleError } = useErrorHandler();

  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const errorMessage =
        typeof event.reason === 'object' &&
        event.reason !== null &&
        'message' in event.reason
          ? String(event.reason.message)
          : 'Unhandled promise rejection';
      const error = new Error(errorMessage);
      if (
        typeof event.reason === 'object' &&
        event.reason !== null &&
        'stack' in event.reason
      ) {
        error.stack = String(event.reason.stack);
      }

      logger.error('Unhandled promise rejection caught', {
        reason: event.reason,
        promise: event.promise,
      });

      onError?.(error);
      handleError(error);
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    return () => {
      window.removeEventListener(
        'unhandledrejection',
        handleUnhandledRejection,
      );
    };
  }, [handleError, onError]);

  return <ErrorBoundary fallback={fallback}>{children}</ErrorBoundary>;
}

export default ErrorBoundary;
