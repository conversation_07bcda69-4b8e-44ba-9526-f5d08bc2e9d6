/**
 * Modal Component - Professional B2B modal system
 * Built on shadcn/ui Dialog with professional styling and advanced features
 */

import React, { ReactNode } from 'react';
import { X, AlertTriangle, CheckCircle, Info, XCircle } from 'lucide-react';
import { cn } from '@/shared/utils/utils';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';

export type ModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';
export type ModalVariant =
  | 'default'
  | 'confirmation'
  | 'warning'
  | 'error'
  | 'success';
export type ModalAnimation = 'fade' | 'slide' | 'scale' | 'none';

interface ModalProps {
  children?: ReactNode;
  trigger?: ReactNode;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  title?: string;
  description?: string;
  size?: ModalSize;
  variant?: ModalVariant;
  animation?: ModalAnimation;
  showCloseButton?: boolean;
  closeOnEscape?: boolean;
  closeOnOutsideClick?: boolean;
  persistent?: boolean;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  footerClassName?: string;

  // Action buttons
  primaryAction?: {
    label: string;
    onClick: () => void;
    loading?: boolean;
    disabled?: boolean;
    variant?: 'default' | 'destructive' | 'outline' | 'secondary';
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    loading?: boolean;
    disabled?: boolean;
    variant?: 'default' | 'destructive' | 'outline' | 'secondary';
  };

  // Advanced features
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  icon?: ReactNode;
  maxHeight?: string;
  scrollable?: boolean;

  // Callbacks
  onOpen?: () => void;
  onClose?: () => void;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const sizeClasses: Record<ModalSize, string> = {
  sm: 'max-w-md',
  md: 'max-w-lg',
  lg: 'max-w-2xl',
  xl: 'max-w-4xl',
  full: 'max-w-7xl',
};

const variantIcons: Record<ModalVariant, ReactNode> = {
  default: null,
  confirmation: <Info className="h-6 w-6 text-blue-600" />,
  warning: <AlertTriangle className="h-6 w-6 text-yellow-600" />,
  error: <XCircle className="h-6 w-6 text-error" />,
  success: <CheckCircle className="h-6 w-6 text-success" />,
};

const variantStyles: Record<ModalVariant, string> = {
  default: '',
  confirmation: 'border-blue-200',
  warning: 'border-yellow-200',
  error: 'border-red-200',
  success: 'border-green-200',
};

export function Modal({
  children,
  trigger,
  isOpen,
  onOpenChange,
  title,
  description,
  size = 'md',
  variant = 'default',
  animation: _animation = 'fade',
  showCloseButton = true,
  closeOnEscape = true,
  closeOnOutsideClick = true,
  persistent = false,
  className,
  headerClassName,
  contentClassName,
  footerClassName,
  primaryAction,
  secondaryAction,
  badge,
  icon,
  maxHeight,
  scrollable = true,
  onOpen,
  onClose,
  onConfirm,
  onCancel,
}: ModalProps) {
  const [internalOpen, setInternalOpen] = React.useState(false);

  const open = isOpen !== undefined ? isOpen : internalOpen;
  const setOpen = React.useCallback(
    (newOpen: boolean) => {
      if (persistent && !newOpen) return;

      if (onOpenChange) {
        onOpenChange(newOpen);
      } else {
        setInternalOpen(newOpen);
      }

      if (newOpen && onOpen) {
        onOpen();
      } else if (!newOpen && onClose) {
        onClose();
      }
    },
    [onOpenChange, onOpen, onClose, persistent],
  );

  const handlePrimaryAction = React.useCallback(() => {
    if (primaryAction?.onClick) {
      primaryAction.onClick();
    }
    if (onConfirm) {
      onConfirm();
    }
  }, [primaryAction, onConfirm]);

  const handleSecondaryAction = React.useCallback(() => {
    if (secondaryAction?.onClick) {
      secondaryAction.onClick();
    }
    if (onCancel) {
      onCancel();
    }
  }, [secondaryAction, onCancel]);

  const displayIcon = icon || variantIcons[variant];

  return (
    <Dialog open={open} onOpenChange={setOpen} modal={true}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}

      <DialogContent
        className={cn(
          'sm:max-w-[425px]',
          sizeClasses[size],
          variantStyles[variant],
          'bg-white border-gray-200 shadow-2xl',
          scrollable && maxHeight && 'overflow-hidden',
          className,
        )}
        style={maxHeight ? { maxHeight } : undefined}
        onEscapeKeyDown={closeOnEscape ? undefined : (e) => e.preventDefault()}
        onPointerDownOutside={
          closeOnOutsideClick ? undefined : (e) => e.preventDefault()
        }
        onInteractOutside={
          closeOnOutsideClick ? undefined : (e) => e.preventDefault()
        }
      >
        {/* Enhanced Header */}
        {(title || description || badge || displayIcon || showCloseButton) && (
          <DialogHeader className={cn('space-y-3', headerClassName)}>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1">
                {displayIcon && (
                  <div className="flex-shrink-0 mt-1">{displayIcon}</div>
                )}

                <div className="flex-1 space-y-2">
                  {badge && (
                    <Badge
                      variant={badge.variant || 'default'}
                      className="w-fit"
                    >
                      {badge.text}
                    </Badge>
                  )}

                  {title && (
                    <DialogTitle className="text-xl font-semibold text-brand-primary leading-tight">
                      {title}
                    </DialogTitle>
                  )}

                  {description && (
                    <DialogDescription className="text-gray-600 leading-relaxed">
                      {description}
                    </DialogDescription>
                  )}
                </div>
              </div>

              {showCloseButton && !persistent && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
                  onClick={() => setOpen(false)}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </Button>
              )}
            </div>
          </DialogHeader>
        )}

        {/* Enhanced Content */}
        <div
          className={cn(
            'flex-1',
            scrollable && maxHeight && 'overflow-y-auto',
            contentClassName,
          )}
        >
          {children}
        </div>

        {/* Enhanced Footer */}
        {(primaryAction || secondaryAction) && (
          <DialogFooter
            className={cn('flex-row gap-2 sm:gap-2', footerClassName)}
          >
            {secondaryAction && (
              <Button
                variant={secondaryAction.variant || 'outline'}
                onClick={handleSecondaryAction}
                disabled={secondaryAction.disabled || secondaryAction.loading}
                className="flex-1 sm:flex-none"
              >
                {secondaryAction.loading && (
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                )}
                {secondaryAction.label}
              </Button>
            )}

            {primaryAction && (
              <Button
                variant={primaryAction.variant || 'default'}
                onClick={handlePrimaryAction}
                disabled={primaryAction.disabled || primaryAction.loading}
                className="flex-1 sm:flex-none bg-brand-primary hover:bg-brand-primary-hover text-white"
              >
                {primaryAction.loading && (
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-gray-300" />
                )}
                {primaryAction.label}
              </Button>
            )}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}

// Preset modal configurations for common use cases
export function ConfirmationModal({
  title = 'Confirm Action',
  description = 'Are you sure you want to proceed?',
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  onConfirm,
  onCancel,
  ...props
}: Omit<ModalProps, 'variant' | 'primaryAction' | 'secondaryAction'> & {
  confirmLabel?: string;
  cancelLabel?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}) {
  return (
    <Modal
      {...props}
      variant="confirmation"
      title={title}
      description={description}
      primaryAction={{
        label: confirmLabel,
        onClick: onConfirm || (() => {}),
        variant: 'default',
      }}
      secondaryAction={{
        label: cancelLabel,
        onClick: onCancel || (() => {}),
        variant: 'outline',
      }}
    />
  );
}

export function DestructiveModal({
  title = 'Confirm Deletion',
  description = 'This action cannot be undone.',
  confirmLabel = 'Delete',
  cancelLabel = 'Cancel',
  onConfirm,
  onCancel,
  ...props
}: Omit<ModalProps, 'variant' | 'primaryAction' | 'secondaryAction'> & {
  confirmLabel?: string;
  cancelLabel?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}) {
  return (
    <Modal
      {...props}
      variant="error"
      title={title}
      description={description}
      primaryAction={{
        label: confirmLabel,
        onClick: onConfirm || (() => {}),
        variant: 'destructive',
      }}
      secondaryAction={{
        label: cancelLabel,
        onClick: onCancel || (() => {}),
        variant: 'outline',
      }}
    />
  );
}

export function SuccessModal({
  title = 'Success',
  description = 'The operation completed successfully.',
  actionLabel = 'Continue',
  onAction,
  ...props
}: Omit<ModalProps, 'variant' | 'primaryAction'> & {
  actionLabel?: string;
  onAction?: () => void;
}) {
  return (
    <Modal
      {...props}
      variant="success"
      title={title}
      description={description}
      primaryAction={{
        label: actionLabel,
        onClick: onAction || (() => {}),
        variant: 'default',
      }}
    />
  );
}

export function ErrorModal({
  title = 'Error',
  description = 'An error occurred. Please try again.',
  actionLabel = 'OK',
  onAction,
  ...props
}: Omit<ModalProps, 'variant' | 'primaryAction'> & {
  actionLabel?: string;
  onAction?: () => void;
}) {
  return (
    <Modal
      {...props}
      variant="error"
      title={title}
      description={description}
      primaryAction={{
        label: actionLabel,
        onClick: onAction || (() => {}),
        variant: 'default',
      }}
    />
  );
}

export function LoadingModal({
  title = 'Processing',
  description = 'Please wait while we process your request...',
  ...props
}: Omit<ModalProps, 'variant' | 'persistent' | 'showCloseButton'>) {
  return (
    <Modal
      {...props}
      variant="default"
      title={title}
      description={description}
      persistent={true}
      showCloseButton={false}
      icon={
        <div className="h-6 w-6 animate-spin rounded-full border-2 border-brand-primary border-t-gray-300" />
      }
    />
  );
}
