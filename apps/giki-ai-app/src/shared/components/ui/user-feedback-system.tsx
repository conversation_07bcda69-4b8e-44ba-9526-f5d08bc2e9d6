/**
 * User Feedback System
 *
 * Provides contextual feedback, guidance, and transparency during operations.
 * Helps users understand system behavior and manage expectations during slow performance.
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
// Removed Lucide imports - replaced with geometric icons
// Clock → ◷, AlertTriangle → ⚠, Info → ⓘ, CheckCircle → ✓
// Coffee → ☉, TrendingUp → ↗, Wifi → ◈, RefreshCw → ⟲, Lightbulb → ☀, X → ✕
import { cn } from '@/shared/utils/utils';

interface FeedbackMessage {
  id: string;
  type: 'info' | 'warning' | 'success' | 'loading' | 'tip';
  title: string;
  message: string;
  duration?: number; // Auto-dismiss after this time (ms)
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
  progress?: number; // 0-100 for progress indicators
}

interface UserFeedbackProps {
  className?: string;
  position?: 'top' | 'bottom' | 'center';
  maxMessages?: number;
}

interface FeedbackContextType {
  addMessage: (message: Omit<FeedbackMessage, 'id'>) => string;
  updateMessage: (id: string, updates: Partial<FeedbackMessage>) => void;
  removeMessage: (id: string) => void;
  clearAll: () => void;
}

// Performance guidance messages
const PERFORMANCE_TIPS = [
  {
    title: 'System Processing',
    message:
      'Our AI is analyzing your financial data thoroughly. This typically takes 3-7 seconds for comprehensive results.',
    icon: '☉',
  },
  {
    title: 'Database Optimization',
    message:
      "We're working on faster response times. Thank you for your patience as we process your request.",
    icon: '↗',
  },
  {
    title: 'Large Dataset',
    message:
      'Processing a large number of transactions. Consider filtering by date range for faster results.',
    icon: 'ⓘ',
  },
  {
    title: 'Background Processing',
    message:
      'Your request is being processed. You can continue using other features while we work.',
    icon: '⟲',
  },
];

const CONTEXTUAL_TIPS = [
  {
    operation: 'login',
    message:
      'First-time authentication may take longer as we set up your secure session.',
    icon: '◈',
  },
  {
    operation: 'upload',
    message:
      "Large files are processed in the background. You'll be notified when complete.",
    icon: '☉',
  },
  {
    operation: 'categorization',
    message:
      'Our AI is learning your spending patterns to provide better categorization.',
    icon: '☀',
  },
  {
    operation: 'report',
    message:
      'Complex reports require detailed analysis. Grab a coffee while we crunch the numbers!',
    icon: '☉',
  },
];

export const UserFeedbackSystem: React.FC<UserFeedbackProps> = ({
  className,
  position = 'top',
  maxMessages = 3,
}) => {
  const [messages, setMessages] = useState<FeedbackMessage[]>([]);

  const _addMessage = (message: Omit<FeedbackMessage, 'id'>): string => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const newMessage: FeedbackMessage = { ...message, id };

    setMessages((prev) => {
      const updated = [...prev, newMessage];
      // Keep only the last maxMessages
      return updated.slice(-maxMessages);
    });

    // Auto-dismiss if duration is specified
    if (message.duration && !message.persistent) {
      setTimeout(() => {
        removeMessage(id);
      }, message.duration);
    }

    return id;
  };

  const _updateMessage = (id: string, updates: Partial<FeedbackMessage>) => {
    setMessages((prev) =>
      prev.map((msg) => (msg.id === id ? { ...msg, ...updates } : msg)),
    );
  };

  const removeMessage = (id: string) => {
    setMessages((prev) => prev.filter((msg) => msg.id !== id));
  };

  const _clearAll = () => {
    setMessages([]);
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'warning':
        return '⚠';
      case 'success':
        return '✓';
      case 'loading':
        return '⟲';
      case 'tip':
        return '☀';
      default:
        return 'ⓘ';
    }
  };

  const getMessageColors = (type: string) => {
    switch (type) {
      case 'warning':
        return 'border-warning/20 bg-warning/10 text-warning';
      case 'success':
        return 'border-success/20 bg-success/10 text-success';
      case 'loading':
        return 'border-info/20 bg-info/10 text-info';
      case 'tip':
        return 'border-brand-primary/20 bg-brand-primary/10 text-brand-primary';
      default:
        return 'border-border bg-muted text-primary-foreground';
    }
  };

  const getPositionClasses = (pos: string) => {
    switch (pos) {
      case 'bottom':
        return 'fixed bottom-4 left-1/2 improve -translate-x-1/2 z-modal';
      case 'center':
        return 'fixed top-1/2 left-1/2 improve -translate-x-1/2 -translate-y-1/2 z-modal';
      default:
        return 'fixed top-4 left-1/2 improve -translate-x-1/2 z-modal';
    }
  };

  if (messages.length === 0) return null;

  return (
    <div className={cn(getPositionClasses(position), className)}>
      <div className="space-y-2 w-96 max-w-sm">
        {messages.map((message) => {
          const icon = getMessageIcon(message.type);

          return (
            <Card
              key={message.id}
              className={cn(
                'shadow-lg border animate-in slide-in-from-top-2 duration-200',
                getMessageColors(message.type),
              )}
            >
              <CardContent className="p-4 overflow-hidden">
                <div className="flex flex-wrap items-start justify-between">
                  <div className="flex flex-wrap items-start gap-3 flex-1">
                    <span
                      className={cn(
                        'h-5 w-5 mt-0.5 flex-shrink-0 text-xl font-bold flex items-center justify-center',
                        message.type === 'loading' && 'animate-spin',
                      )}
                    >
                      {icon}
                    </span>

                    <div className="flex flex-wrap-1 min-w-0">
                      <h4 className="font-medium text-sm mb-1">
                        {message.title}
                      </h4>
                      <p className="text-sm opacity-90 leading-relaxed">
                        {message.message}
                      </p>

                      {/* Progress bar for loading states */}
                      {message.type === 'loading' &&
                        message.progress !== undefined && (
                          <div className="mt-3">
                            <div className="flex flex-wrap justify-between truncate text-caption mb-1">
                              <span>Progress</span>
                              <span>{message.progress}%</span>
                            </div>
                            <Progress
                              value={message.progress}
                              className="h-1"
                            />
                          </div>
                        )}

                      {/* Action button */}
                      {message.action && (
                        <Button
                          className="max-w-full mt-3 h-7 px-3 truncate text-caption border-current hover:bg-current/10"
                          variant="outline"
                          size="sm"
                          onClick={message?.action?.onClick}
                        >
                          {message?.action?.label}
                        </Button>
                      )}
                    </div>
                  </div>

                  {!message.persistent && (
                    <Button
                      className="max-w-full h-6 w-6 p-0 hover:bg-current/10"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMessage(message.id)}
                    >
                      <span className="h-3 w-3 text-sm font-bold flex items-center justify-center">✕</span>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

// Hook for easy feedback management
export const useUserFeedback = () => {
  const [feedbackSystem, setFeedbackSystem] =
    useState<FeedbackContextType | null>(null);

  const showPerformanceTip = (operation?: string) => {
    if (!feedbackSystem) return;

    // Show contextual tip if available
    const contextualTip = CONTEXTUAL_TIPS.find(
      (tip) => tip.operation === operation,
    );
    if (contextualTip) {
      const _Icon = contextualTip.icon;
      return feedbackSystem.addMessage({
        type: 'tip',
        title:
          contextualTip?.operation?.charAt(0).toUpperCase() +
          contextualTip?.operation?.slice(1),
        message: contextualTip.message,
        duration: 8000,
      });
    }

    // Show random performance tip
    const randomTip =
      PERFORMANCE_TIPS[Math.floor(Math.random() * PERFORMANCE_TIPS.length)];
    return feedbackSystem.addMessage({
      type: 'info',
      title: randomTip.title,
      message: randomTip.message,
      duration: 6000,
    });
  };

  const showSlowOperationWarning = (
    operation: string,
    expectedTime: number,
  ) => {
    if (!feedbackSystem) return;

    return feedbackSystem.addMessage({
      type: 'warning',
      title: 'Slower Than Expected',
      message: `${operation} is taking longer than usual. This typically completes in ${expectedTime}ms, but current performance may vary. Please wait...`,
      duration: 10000,
      action: {
        label: 'Learn More',
        onClick: () => {
          feedbackSystem.addMessage({
            type: 'info',
            title: 'Performance Information',
            message:
              'We are continuously optimizing our systems. Current slower response times are due to comprehensive data processing and will improve in upcoming updates.',
            duration: 12000,
          });
        },
      },
    });
  };

  const showLoadingFeedback = (operation: string, progress?: number) => {
    if (!feedbackSystem) return;

    return feedbackSystem.addMessage({
      type: 'loading',
      title: `Processing ${operation}`,
      message: 'Please wait while we handle your request...',
      progress,
      persistent: true,
    });
  };

  const showSuccessFeedback = (operation: string, details?: string) => {
    if (!feedbackSystem) return;

    return feedbackSystem.addMessage({
      type: 'success',
      title: `${operation} Complete`,
      message: details || 'Operation completed successfully!',
      duration: 4000,
    });
  };

  const showSystemStatus = (avgResponseTime: number, target: number = 200) => {
    if (!feedbackSystem) return;

    if (avgResponseTime > target * 3) {
      return feedbackSystem.addMessage({
        type: 'warning',
        title: 'System Performance Notice',
        message: `Current response times are ${Math.round(avgResponseTime / target)}x slower than target. Our team is working on optimizations.`,
        duration: 15000,
        action: {
          label: 'Status Updates',
          onClick: () => window.open('/status', '_blank'),
        },
      });
    }
  };

  return {
    showPerformanceTip,
    showSlowOperationWarning,
    showLoadingFeedback,
    showSuccessFeedback,
    showSystemStatus,
    setFeedbackSystem,
  };
};

// Performance-aware loading component
interface SmartLoadingProps {
  operation: string;
  startTime: number;
  target?: number;
  children?: React.ReactNode;
}

export const SmartLoading: React.FC<SmartLoadingProps> = ({
  operation,
  startTime,
  target = 200,
  children,
}) => {
  const [elapsed, setElapsed] = useState(0);
  const { showPerformanceTip, showSlowOperationWarning } = useUserFeedback();

  useEffect(() => {
    const interval = setInterval(() => {
      const current = Date.now() - startTime;
      setElapsed(current);

      // Show tip after 2x target time
      if (current > target * 2 && current < target * 2.5) {
        showPerformanceTip(operation.toLowerCase());
      }

      // Show warning after 5x target time
      if (current > target * 5 && current < target * 5.5) {
        showSlowOperationWarning(operation, target);
      }
    }, 500);

    return () => clearInterval(interval);
  }, [
    startTime,
    target,
    operation,
    showPerformanceTip,
    showSlowOperationWarning,
  ]);

  const getPhase = () => {
    if (elapsed < target) return 'fast';
    if (elapsed < target * 2) return 'normal';
    if (elapsed < target * 5) return 'slow';
    return 'very-slow';
  };

  const phase = getPhase();

  return (
    <div className="smart-loading flex flex-wrap flex-col items-center justify-center p-6">
      <div className="relative">
        <span
          className={cn(
            'h-8 w-8 animate-spin text-3xl font-bold flex items-center justify-center',
            phase === 'fast' && 'text-success',
            phase === 'normal' && 'text-info',
            phase === 'slow' && 'text-warning',
            phase === 'very-slow' && 'text-destructive',
          )}
        >
          ⟲
        </span>

        {phase !== 'fast' && (
          <div className="absolute -bottom-1 -right-1">
            <span className="h-3 w-3 text-warning text-sm font-bold flex items-center justify-center">◷</span>
          </div>
        )}
      </div>

      <div className="mt-4 text-center">
        <h3 className="font-medium text-sm">{operation}</h3>
        <div className="flex flex-wrap items-center gap-2 mt-1 truncate text-caption text-muted-foreground">
          <span>{(elapsed / 1000).toFixed(1)}s elapsed</span>
          {elapsed > target && (
            <Badge
              variant="outline"
              className="truncate text-caption max-w-[150px] truncate"
            >
              {Math.round(elapsed / target)}x target
            </Badge>
          )}
        </div>
      </div>

      {children}
    </div>
  );
};

export default UserFeedbackSystem;
