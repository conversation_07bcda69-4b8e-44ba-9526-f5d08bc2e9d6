import * as React from 'react';

import { cn } from '@/shared/utils/utils';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
  success?: boolean;
  variant?: 'default' | 'financial' | 'glass';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      error,
      success,
      variant = 'default',
      leftIcon,
      rightIcon,
      ...props
    },
    ref,
  ) => {
    // Variant styles
    const variantStyles = {
      default: 'bg-background border-input',
      financial: 'bg-background border-input font-mono tabular-nums text-right',
      glass: 'bg-background/70 backdrop-blur-sm border-input/50',
    };

    const Component = leftIcon || rightIcon ? 'div' : React.Fragment;
    const wrapperProps =
      leftIcon || rightIcon
        ? {
            className: 'relative flex items-center w-full',
          }
        : {};

    const inputElement = (
      <input
        type={type}
        className={cn(
          // Base styling with design tokens and enhanced professional appearance
          'flex h-11 w-full rounded-lg border text-sm font-medium transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-70 disabled:bg-muted disabled:text-muted-foreground shadow-sm hover:shadow-md focus:shadow-lg',
          // Using design token for padding
          'px-[var(--giki-space-4)] py-[var(--giki-space-3)]',

          // Variant-specific styles
          variantStyles[variant],

          // Interactive states
          'hover:border-border/80',

          // Placeholder styling using design tokens
          'placeholder:text-muted-foreground placeholder:font-normal',

          // Focus states with enhanced glow
          'focus-visible:ring-primary/20 focus-visible:border-primary focus-visible:bg-background',

          // Error state with enhanced styling
          error &&
            'border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive bg-destructive/5',

          // Success state with enhanced styling
          success &&
            'border-success focus-visible:ring-success/20 focus-visible:border-success bg-success/5',

          // Icon padding adjustments
          leftIcon && 'pl-12',
          rightIcon && 'pr-12',

          className,
        )}
        ref={ref}
        {...props}
      />
    );

    if (!leftIcon && !rightIcon) {
      return inputElement;
    }

    return (
      <Component {...wrapperProps}>
        {leftIcon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground pointer-events-none z-10">
            {leftIcon}
          </div>
        )}
        {inputElement}
        {rightIcon && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground pointer-events-none z-10">
            {rightIcon}
          </div>
        )}
      </Component>
    );
  },
);
Input.displayName = 'Input';

export { Input };
