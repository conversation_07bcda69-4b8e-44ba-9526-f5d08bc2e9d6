import * as React from 'react';
import { LucideIcon } from 'lucide-react';

import { cn } from '@/shared/utils/utils';

// Enhanced Card Props Interface - Consolidated from all card systems
interface CardProps extends React.ComponentProps<'div'> {
  // Visual variants for different use cases (consolidated from all card systems)
  variant?: 'default' | 'branded' | 'professional' | 'metric' | 'success' | 'warning' | 'error' | 'info' | 'glass' | 'elevated' | 'excel';
  
  // Header props for metric and professional cards
  title?: string;
  subtitle?: string;
  icon?: LucideIcon;
  iconColor?: string;
  action?: React.ReactNode;
  
  // Trend indicator for metric cards
  trend?: {
    value: number;
    label?: string;
    direction: 'up' | 'down' | 'neutral';
  };
  
  // Visual enhancements (consolidated from GikiCard and ExcelCard)
  hover?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  interactive?: boolean;
  size?: 'sm' | 'md' | 'lg'; // From ExcelCard
  gradient?: 'brand' | 'green' | 'blue' | 'purple' | 'pink' | 'none'; // From GikiCard
  
  // Callback for interactive cards
  onClick?: () => void;
}

function Card({ 
  className, 
  variant = 'default',
  title,
  subtitle,
  icon: Icon,
  iconColor,
  action,
  trend,
  hover = true,
  padding = 'md',
  interactive = false,
  size = 'md',
  gradient = 'none',
  onClick,
  children,
  ...props 
}: CardProps) {
  // Base styles with enhanced variants and accessibility
  const baseStyles = cn(
    'bg-card text-card-foreground flex flex-col rounded-xl border transition-all duration-300',
    hover && 'hover:shadow-xl',
    interactive && 'cursor-pointer hover:-translate-y-0.5',
    onClick && 'cursor-pointer focus:outline-none focus:ring-2 focus:ring-[var(--giki-primary)] focus:ring-offset-2'
  );

  // Variant styles combining best of all card systems (Card, ExcelCard, GikiCard)
  const variantStyles = {
    default: 'border-border shadow-lg component-gap-lg',
    branded: 'border-brand-primary/20 shadow-lg component-gap-lg bg-gradient-to-br from-brand-primary/5 via-transparent to-brand-primary/5',
    professional: 'border-l-4 border-l-[var(--giki-primary)] border-border shadow-md component-gap',
    metric: 'border-border shadow-sm hover:border-brand-primary/20 transition-colors component-gap-sm',
    success: 'border-l-4 border-l-green-500 bg-green-50/50 border-border shadow-md component-gap',
    warning: 'border-l-4 border-l-amber-500 bg-amber-50/50 border-border shadow-md component-gap',
    error: 'border-l-4 border-l-red-500 bg-red-50/50 border-border shadow-md component-gap',
    info: 'border-l-4 border-l-blue-500 bg-blue-50/50 border-border shadow-md component-gap',
    // New variants from GikiCard
    glass: 'border-white/20 shadow-lg backdrop-blur-sm bg-white/10 component-gap',
    elevated: 'border-border shadow-2xl hover:shadow-3xl bg-white component-gap-lg',
    // New variant from ExcelCard 
    excel: 'border-gray-300 shadow-sm bg-white hover:shadow-md component-gap-sm font-mono',
  };

  // Size styles from ExcelCard
  const sizeStyles = {
    sm: 'text-sm',
    md: 'text-base', 
    lg: 'text-lg',
  };

  // Gradient styles from GikiCard
  const gradientStyles = {
    none: '',
    brand: 'bg-gradient-to-br from-[var(--giki-primary)] via-[var(--giki-primary-hover)] to-[var(--giki-primary)]',
    green: 'bg-gradient-to-br from-[var(--giki-dark-green)] via-[var(--giki-primary)] to-[var(--giki-dark-green)]',
    blue: 'bg-gradient-to-br from-[var(--giki-dark-blue)] via-[var(--giki-dark-blue)] to-[var(--giki-dark-blue)]',
    purple: 'bg-gradient-to-br from-[var(--giki-dark-purple)] via-[var(--giki-dark-purple)] to-[var(--giki-dark-purple)]',
    pink: 'bg-gradient-to-br from-[var(--giki-dark-red)] via-[var(--giki-dark-red)] to-[var(--giki-dark-red)]',
  };

  // Padding styles using design tokens
  const paddingStyles = {
    none: '',
    sm: 'card-padding-sm',
    md: 'card-padding',
    lg: 'card-padding-lg',
  };

  // Helper function for trend colors
  const getTrendColor = (direction: string) => {
    switch (direction) {
      case 'up': return 'text-success';
      case 'down': return 'text-error';
      default: return 'text-gray-500';
    }
  };

  // Helper function for trend arrows
  const getTrendArrow = (direction: string) => {
    switch (direction) {
      case 'up': return '↗';
      case 'down': return '↘';
      default: return '→';
    }
  };

  // Handle keyboard events for accessibility
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (onClick && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      onClick();
    }
  };

  return (
    <div
      data-slot="card"
      className={cn(
        baseStyles,
        variantStyles[variant],
        paddingStyles[padding],
        sizeStyles[size],
        gradient !== 'none' && gradientStyles[gradient],
        gradient !== 'none' && 'text-white', // White text for gradient backgrounds
        className,
      )}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      tabIndex={onClick ? 0 : undefined}
      role={onClick ? 'button' : undefined}
      aria-pressed={onClick ? false : undefined}
      {...props}
    >
      {/* Enhanced Header Section for professional and metric variants */}
      {(title || subtitle || Icon || action || trend) && (
        <div className="flex items-start justify-between component-gap">
          <div className="flex items-start component-gap-sm flex-1 min-w-0">
            {Icon && (
              <div
                className={cn(
                  'p-[var(--giki-space-2.5)] rounded-lg flex items-center justify-center flex-shrink-0',
                  iconColor || 'bg-[var(--giki-primary)]/20 text-[var(--giki-primary)]'
                )}
              >
                <Icon className="w-5 h-5" strokeWidth={2} />
              </div>
            )}
            
            <div className="flex-1 min-w-0">
              {title && (
                <h3 className={cn(
                  'font-semibold leading-tight',
                  variant === 'metric' ? 'text-table-header uppercase tracking-wide mb-1' : 'text-card-title'
                )}>
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className={cn(
                  'text-muted-foreground',
                  variant === 'metric' ? 'text-caption mt-1' : 'text-card-subtitle mt-1'
                )}>
                  {subtitle}
                </p>
              )}
            </div>
          </div>

          {/* Action or Trend Indicator */}
          {action && <div className="flex-shrink-0">{action}</div>}
          {trend && (
            <div className={cn(
              'flex items-center component-gap-sm text-xs font-medium flex-shrink-0',
              getTrendColor(trend.direction)
            )}>
              <span className="font-bold text-sm">{getTrendArrow(trend.direction)}</span>
              <span>
                {trend.direction === 'up' ? '+' : ''}
                {trend.value}%{trend.label ? ` ${trend.label}` : ''}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Main content */}
      {children}
    </div>
  );
}

function CardHeader({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot="card-header"
      className={cn(
        'flex items-center justify-between card-padding-sm [.border-b]:pb-6',
        className,
      )}
      {...props}
    />
  );
}

function CardTitle({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot="card-title"
      className={cn(
        'text-card-title',
        className,
      )}
      {...props}
    />
  );
}

function CardDescription({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot="card-description"
      className={cn(
        'text-card-subtitle',
        className,
      )}
      {...props}
    />
  );
}

function CardAction({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot="card-action"
      className={cn(
        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',
        className,
      )}
      {...props}
    />
  );
}

function CardContent({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot="card-content"
      className={cn('card-padding-sm component-gap', className)}
      {...props}
    />
  );
}

function CardFooter({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot="card-footer"
      className={cn(
        'flex items-center justify-between card-padding-sm [.border-t]:pt-6 border-t border-border/30',
        className,
      )}
      {...props}
    />
  );
}

// Enhanced Metric Card Component (replaces ExcelCard and GikiCard metric variants)
interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    label?: string;
    direction: 'up' | 'down' | 'neutral';
  };
  icon?: LucideIcon;
  className?: string;
  onClick?: () => void;
}

function MetricCard({
  title,
  value,
  subtitle,
  trend,
  icon,
  className,
  onClick,
}: MetricCardProps) {
  return (
    <Card
      variant="metric"
      title={title}
      subtitle={subtitle}
      icon={icon}
      trend={trend}
      className={className}
      onClick={onClick}
      interactive={!!onClick}
      padding="md"
    >
      <div className={cn(
        'text-3xl font-bold text-brand-primary font-mono tracking-tight',
        'tabular-nums'
      )}>
        {value}
      </div>
    </Card>
  );
}

// Professional Card Component (replaces variants of GikiCard)
interface ProfessionalCardProps {
  title?: string;
  subtitle?: string;
  icon?: LucideIcon;
  action?: React.ReactNode;
  variant?: 'success' | 'warning' | 'error' | 'info' | 'default';
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

function ProfessionalCard({
  title,
  subtitle,
  icon,
  action,
  variant = 'default',
  children,
  className,
  onClick,
}: ProfessionalCardProps) {
  return (
    <Card
      variant={variant === 'default' ? 'professional' : variant}
      title={title}
      subtitle={subtitle}
      icon={icon}
      action={action}
      className={className}
      onClick={onClick}
      interactive={!!onClick}
      padding="md"
    >
      {children}
    </Card>
  );
}

// ExcelCard compatibility component (replaces excel-card.tsx)
interface ExcelCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    label: string;
    direction: 'up' | 'down' | 'neutral';
  };
  icon?: LucideIcon;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

function ExcelCard({
  title,
  value,
  subtitle,
  trend,
  icon,
  variant = 'default',
  size = 'md',
  className,
  children,
}: ExcelCardProps) {
  return (
    <Card
      variant="excel"
      title={title}
      subtitle={subtitle}
      icon={icon}
      trend={trend}
      size={size}
      className={className}
      padding="md"
    >
      <div className={cn(
        'font-bold text-brand-primary font-mono tabular-nums',
        size === 'sm' ? 'text-lg' : size === 'lg' ? 'text-4xl' : 'text-2xl'
      )}>
        {value}
      </div>
      {children}
    </Card>
  );
}

// GikiCard compatibility component (replaces giki-card.tsx)  
interface GikiCardProps {
  children?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'branded' | 'glass' | 'elevated' | 'success' | 'warning' | 'error';
  title?: string;
  subtitle?: string;
  icon?: LucideIcon;
  iconColor?: string;
  action?: React.ReactNode;
  gradient?: 'brand' | 'green' | 'blue' | 'purple' | 'pink' | 'none';
  hover?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  onClick?: () => void;
  interactive?: boolean;
}

function GikiCard({
  children,
  className,
  variant = 'default',
  title,
  subtitle,
  icon,
  iconColor,
  action,
  gradient = 'none',
  hover = true,
  padding = 'md',
  onClick,
  interactive = false,
}: GikiCardProps) {
  return (
    <Card
      variant={variant}
      title={title}
      subtitle={subtitle}
      icon={icon}
      iconColor={iconColor}
      action={action}
      gradient={gradient}
      hover={hover}
      padding={padding}
      onClick={onClick}
      interactive={interactive}
      className={className}
    >
      {children}
    </Card>
  );
}

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardAction,
  CardDescription,
  CardContent,
  MetricCard,
  ProfessionalCard,
  // Compatibility components for seamless migration
  ExcelCard,
  GikiCard,
  // Type exports
  type CardProps,
  type MetricCardProps,
  type ProfessionalCardProps,
  type ExcelCardProps,
  type GikiCardProps,
};
