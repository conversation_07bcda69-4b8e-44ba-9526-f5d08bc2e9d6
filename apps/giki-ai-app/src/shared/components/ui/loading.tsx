import React, { useEffect, useState } from 'react';
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { cn } from '@/shared/utils/utils';
import { Button } from './button';
import { Progress } from './progress';
import { Card } from './card';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullPage?: boolean;
  className?: string;
  showProgress?: boolean;
  progress?: number;
  estimatedTime?: number;
  showCancel?: boolean;
  onCancel?: () => void;
  delay?: number;
  timeout?: number;
  onTimeout?: () => void;
  showElapsedTime?: boolean;
}

export function Loading({
  size = 'md',
  text = 'Loading...',
  fullPage = false,
  className,
  showProgress = false,
  progress = 0,
  estimatedTime,
  showCancel = false,
  onCancel,
  delay = 0,
  timeout,
  onTimeout,
  showElapsedTime = false,
}: LoadingProps) {
  const [isVisible, setIsVisible] = useState(delay === 0);
  const [hasTimedOut, setHasTimedOut] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);

  const sizeMap = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  // Handle delay
  useEffect(() => {
    if (delay > 0) {
      const timer = setTimeout(() => setIsVisible(true), delay);
      return () => clearTimeout(timer);
    }
  }, [delay]);

  // Handle timeout
  useEffect(() => {
    if (timeout && timeout > 0) {
      const timer = setTimeout(() => {
        setHasTimedOut(true);
        onTimeout?.();
      }, timeout);
      return () => clearTimeout(timer);
    }
  }, [timeout, onTimeout]);

  // Track elapsed time
  useEffect(() => {
    if (showElapsedTime) {
      const interval = setInterval(() => {
        setElapsedTime((prev) => prev + 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [showElapsedTime]);

  if (!isVisible) return null;

  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const content = (
    <div
      className={cn(
        'flex flex-col items-center justify-center',
        fullPage ? 'min-h-[50vh]' : 'py-8',
        className,
      )}
    >
      {hasTimedOut ? (
        <div className="text-center space-y-4 max-w-sm">
          <AlertCircle className="w-12 h-12 text-destructive mx-auto" />
          <div className="space-y-2">
            <p className="text-destructive font-medium">Operation timed out</p>
            <p className="text-sm text-muted-foreground">
              This is taking longer than expected. Please try again.
            </p>
          </div>
          {onCancel && (
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              className="mt-2"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
          )}
        </div>
      ) : (
        <>
          <Loader2 className={cn('animate-spin text-primary', sizeMap[size])} />
          {text && (
            <p className="mt-4 text-sm text-foreground font-medium animate-pulse">
              {text}
            </p>
          )}

          {showProgress && (
            <div className="w-full max-w-xs mt-4 space-y-2">
              <Progress value={progress} className="h-2" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{Math.round(progress)}%</span>
                {estimatedTime && (
                  <span>~{formatTime(estimatedTime)} remaining</span>
                )}
              </div>
            </div>
          )}

          {showElapsedTime && elapsedTime > 5 && (
            <p className="text-xs text-muted-foreground mt-2 animate-pulse">
              Elapsed: {formatTime(elapsedTime)}
            </p>
          )}

          {showCancel && onCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="mt-4"
            >
              Cancel
            </Button>
          )}
        </>
      )}
    </div>
  );

  if (fullPage) {
    return (
      <div className="flex flex-wrap h-full w-full items-center justify-center">
        {content}
      </div>
    );
  }

  return content;
}

// Skeleton loading components for better perceived performance
export function LoadingSkeleton({
  className,
  style,
}: {
  className?: string;
  style?: React.CSSProperties;
}) {
  return (
    <div
      className={cn('animate-pulse bg-muted rounded', className)}
      style={style}
    />
  );
}

export function LoadingText({
  lines = 3,
  className,
}: {
  lines?: number;
  className?: string;
}) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className="h-4 bg-muted rounded animate-pulse"
          style={{ width: `${Math.random() * 40 + 60}%` }}
        />
      ))}
    </div>
  );
}

export function LoadingCard({ className }: { className?: string }) {
  return (
    <Card className={cn('p-6', className)}>
      <div className="space-y-4">
        <LoadingSkeleton className="h-6 w-1/3" />
        <LoadingText lines={2} />
        <div className="flex gap-2">
          <LoadingSkeleton className="h-9 w-20" />
          <LoadingSkeleton className="h-9 w-20" />
        </div>
      </div>
    </Card>
  );
}

export function LoadingTable({ rows = 5 }: { rows?: number }) {
  return (
    <div className="w-full">
      <div className="border rounded-lg">
        {/* Header */}
        <div className="border-b bg-muted/50 p-4">
          <div className="flex gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <LoadingSkeleton key={i} className="h-4 flex-1" />
            ))}
          </div>
        </div>
        {/* Rows */}
        {Array.from({ length: rows }).map((_, i) => (
          <div key={i} className="border-b p-4 last:border-b-0">
            <div className="flex gap-4">
              {Array.from({ length: 4 }).map((_, j) => (
                <LoadingSkeleton
                  key={j}
                  className="h-4 flex-1"
                  style={{ width: `${Math.random() * 30 + 70}%` }}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
