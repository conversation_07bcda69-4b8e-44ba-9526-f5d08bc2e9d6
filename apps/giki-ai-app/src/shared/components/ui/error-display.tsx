import React from 'react';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { Button } from '@/shared/components/ui/button';
import {
  RefreshCw,
  Home,
  ArrowLeft,
  Bug,
  Wifi,
  Server,
  Shield,
  Clock,
} from 'lucide-react';
import { extractErrorMessage } from '@/shared/utils/errorHandling';

interface ErrorDisplayProps {
  error: unknown;
  title?: string;
  showRetry?: boolean;
  showGoHome?: boolean;
  showGoBack?: boolean;
  onRetry?: () => void;
  onGoHome?: () => void;
  onGoBack?: () => void;
  className?: string;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = React.memo(({
  error,
  title = 'Something went wrong',
  showRetry = true,
  showGoHome = false,
  showGoBack = false,
  onRetry,
  onGoHome,
  onGoBack,
  className = '',
}) => {
  const errorMessage = extractErrorMessage(
    error,
    'An unexpected error occurred',
  );

  // Determine error type and appropriate icon/styling
  const getErrorType = (message: string) => {
    if (
      message.toLowerCase().includes('network') ||
      message.toLowerCase().includes('connection')
    ) {
      return {
        type: 'network',
        icon: <Wifi className="h-6 w-6" />,
        color: 'text-warning',
        bgColor: 'bg-warning/10',
        borderColor: 'border-warning/20',
      };
    }
    if (
      message.toLowerCase().includes('server') ||
      message.toLowerCase().includes('500')
    ) {
      return {
        type: 'server',
        icon: <Server className="h-6 w-6" />,
        color: 'text-destructive',
        bgColor: 'bg-destructive/10',
        borderColor: 'border-destructive/20',
      };
    }
    if (
      message.toLowerCase().includes('unauthorized') ||
      message.toLowerCase().includes('401')
    ) {
      return {
        type: 'auth',
        icon: <Shield className="h-6 w-6" />,
        color: 'text-warning',
        bgColor: 'bg-warning/10',
        borderColor: 'border-warning/20',
      };
    }
    if (message.toLowerCase().includes('timeout')) {
      return {
        type: 'timeout',
        icon: <Clock className="h-6 w-6" />,
        color: 'text-warning',
        bgColor: 'bg-warning/10',
        borderColor: 'border-warning/20',
      };
    }
    return {
      type: 'generic',
      icon: <Bug className="h-6 w-6" />,
      color: 'text-destructive',
      bgColor: 'bg-destructive/10',
      borderColor: 'border-destructive/20',
    };
  };

  const errorType = getErrorType(errorMessage);

  // Get user-friendly suggestions based on error type
  const getSuggestions = (type: string) => {
    switch (type) {
      case 'network':
        return [
          'Check your internet connection',
          'Try refreshing the page',
          'Contact support if the issue persists',
        ];
      case 'server':
        return [
          'Our servers are experiencing issues',
          'Please try again in a few minutes',
          'Contact support if the problem continues',
        ];
      case 'auth':
        return [
          'Please log in again',
          'Your session may have expired',
          'Contact support if you continue having access issues',
        ];
      case 'timeout':
        return [
          'The request took too long to complete',
          'Try again with a smaller file or dataset',
          'Check your internet connection',
        ];
      default:
        return [
          'Try refreshing the page',
          'Clear your browser cache',
          'Contact support if the issue persists',
        ];
    }
  };

  const suggestions = getSuggestions(errorType.type);

  return (
    <Alert
      className={`${errorType.bgColor} ${errorType.borderColor} shadow-lg animate-slide-in ${className}`}
    >
      <div className="flex flex-wrap items-start gap-4">
        <div
          className={`flex-shrink-0 w-12 h-12 ${errorType.bgColor} rounded-full flex items-center justify-center`}
        >
          <div className={errorType.color}>{errorType.icon}</div>
        </div>
        <div className="flex flex-wrap-1 space-y-4">
          <div>
            <AlertTitle
              className={`text-xl font-semibold ${errorType.color} mb-2`}
            >
              {title}
            </AlertTitle>
            <AlertDescription className="text-base leading-relaxed text-text-secondary font-medium">
              {errorMessage}
            </AlertDescription>
          </div>

          {suggestions.length > 0 && (
            <div className="space-y-2">
              <p className="text-label text-foreground">What you can try:</p>
              <ul className="text-sm text-[hsl(var(--giki-text-muted))] space-y-1">
                {suggestions.map((suggestion, index) => (
                  <li key={index} className="flex flex-wrap items-start gap-2">
                    <span className="text-primary mt-1">•</span>
                    <span>{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="flex flex-wrap flex-col sm:flex-row gap-3 pt-2">
            {showRetry && onRetry && (
              <Button
                className={`max-w-full ${errorType.borderColor} ${errorType.color} hover:${errorType.bgColor} hover:${errorType.borderColor}/50 transition-all duration-200`}
                variant="outline"
                size="sm"
                onClick={() => void onRetry()}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            )}
            {showGoBack && onGoBack && (
              <Button
                className="max-w-full truncate text-secondary hover:text-foreground hover:bg-muted/50"
                variant="ghost"
                size="sm"
                onClick={() => void onGoBack()}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back
              </Button>
            )}
            {showGoHome && onGoHome && (
              <Button
                className="max-w-full truncate text-muted hover:text-foreground hover:bg-muted/50"
                variant="ghost"
                size="sm"
                onClick={() => void onGoHome()}
              >
                <Home className="w-4 h-4 mr-2" />
                Go to Dashboard
              </Button>
            )}
          </div>
        </div>
      </div>
    </Alert>
  );
});

ErrorDisplay.displayName = 'ErrorDisplay';

export default ErrorDisplay;
