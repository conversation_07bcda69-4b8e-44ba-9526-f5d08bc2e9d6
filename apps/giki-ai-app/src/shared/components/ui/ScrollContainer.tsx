import React, { useRef, useEffect, useState } from 'react';
import { cn } from '@/shared/utils/utils';
import { ChevronUp } from 'lucide-react';
import { useScrollPosition } from '@/shared/hooks/useScrollPosition';

interface ScrollContainerProps {
  children: React.ReactNode;
  className?: string;
  showScrollToTop?: boolean;
  scrollToTopThreshold?: number;
  showScrollIndicators?: boolean;
  preserveScrollPosition?: boolean;
  maxHeight?: string;
  orientation?: 'vertical' | 'horizontal' | 'both';
  scrollbarStyle?: 'default' | 'thin' | 'hidden';
}

export const ScrollContainer: React.FC<ScrollContainerProps> = ({
  children,
  className,
  showScrollToTop = true,
  scrollToTopThreshold = 300,
  showScrollIndicators = true,
  preserveScrollPosition = true,
  maxHeight,
  orientation = 'vertical',
  scrollbarStyle = 'thin',
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [showTopButton, setShowTopButton] = useState(false);
  const [showBottomIndicator, setShowBottomIndicator] = useState(false);
  const [showTopIndicator, setShowTopIndicator] = useState(false);

  // Always call the hook to satisfy React's rules
  const { scrollToTop: hookScrollToTop } = useScrollPosition(containerRef);

  // Use the hook's function or a fallback based on preserveScrollPosition
  const scrollToTop = preserveScrollPosition
    ? hookScrollToTop
    : () => {
        containerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
      };

  // Handle scroll events to show/hide indicators
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;

      // Show scroll to top button
      setShowTopButton(scrollTop > scrollToTopThreshold);

      // Show scroll indicators
      if (showScrollIndicators) {
        setShowTopIndicator(scrollTop > 10);
        setShowBottomIndicator(scrollTop < scrollHeight - clientHeight - 10);
      }
    };

    container.addEventListener('scroll', handleScroll, { passive: true });

    // Initial check
    handleScroll();

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [scrollToTopThreshold, showScrollIndicators]);

  const scrollbarClasses = {
    default: '',
    thin: 'scrollbar-thin scrollbar-thumb-rounded',
    hidden: 'scrollbar-hide',
  };

  const overflowClasses = {
    vertical: 'overflow-y-auto overflow-x-hidden',
    horizontal: 'overflow-x-auto overflow-y-hidden',
    both: 'overflow-auto',
  };

  return (
    <div className="relative">
      {/* Top scroll indicator */}
      {showScrollIndicators && showTopIndicator && (
        <div className="absolute top-0 left-0 right-0 h-8 bg-gradient-to-b from-background to-transparent pointer-events-none z-10" />
      )}

      {/* Scrollable container */}
      <div
        ref={containerRef}
        className={cn(
          'relative',
          overflowClasses[orientation],
          scrollbarClasses[scrollbarStyle],
          className,
        )}
        style={{ maxHeight }}
      >
        {children}
      </div>

      {/* Bottom scroll indicator */}
      {showScrollIndicators && showBottomIndicator && (
        <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-background to-transparent pointer-events-none z-10" />
      )}

      {/* Scroll to top button */}
      {showScrollToTop && showTopButton && (
        <button
          onClick={() => scrollToTop(true)}
          className={cn(
            'fixed bottom-6 right-6 p-3 rounded-full',
            'bg-primary text-primary-foreground shadow-lg',
            'hover:bg-primary/90 transition-all duration-200',
            'transform translate-y-0 opacity-100',
            'hover:scale-110 active:scale-95',
            'z-50',
          )}
          aria-label="Scroll to top"
        >
          <ChevronUp className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};

// Specialized scroll container for tables
export const TableScrollContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
  maxHeight?: string;
}> = ({ children, className, maxHeight = '600px' }) => {
  return (
    <ScrollContainer
      className={cn('border border-border rounded-lg', className)}
      maxHeight={maxHeight}
      orientation="both"
      showScrollIndicators={true}
      scrollbarStyle="thin"
      showScrollToTop={false}
    >
      {children}
    </ScrollContainer>
  );
};

// Specialized scroll container for chat/messages
export const ChatScrollContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
  autoScrollToBottom?: boolean;
}> = ({ children, className, autoScrollToBottom = true }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (autoScrollToBottom && containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [children, autoScrollToBottom]);

  return (
    <div
      ref={containerRef}
      className={cn(
        'overflow-y-auto scrollbar-thin',
        'flex flex-col',
        className,
      )}
    >
      {children}
    </div>
  );
};
