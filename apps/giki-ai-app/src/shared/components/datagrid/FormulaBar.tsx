import React from 'react';
import { Input } from '@/shared/components/ui/input';
// import { Table } from '@tanstack/react-table'; // Import Table type if needed - Unused

interface FormulaBarProps {
  // We'll refine this based on how we access the table state
  // Option 1: Pass the whole table instance
  // table: Table<unknown>;
  // Option 2: Pass only the selected cell value
  selectedValue: string | number | null;
}

const FormulaBar: React.FC<FormulaBarProps> = ({ selectedValue }) => {
  // Logic to get the value from the table instance if Option 1 is chosen
  // const selectedCell = table.getState().currentCell;
  // const displayValue = selectedCell ? table.getCell(selectedCell.rowId, selectedCell.columnId).getValue() : '';

  // Using Option 2 directly
  const displayValue = selectedValue ?? '';

  return (
    <div className="flex flex-wrap items-center p-1 border-b bg-muted/40">
      <span className="px-2 truncate text-caption font-semibold text-[hsl(var(--giki-text-muted))]">
        fx
      </span>
      <Input
        readOnly
        value={String(displayValue)} // Ensure value is string
        className="flex flex-wrap-grow h-7 rounded-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
        placeholder="Selected cell value..."
      />
    </div>
  );
};

export default FormulaBar;
