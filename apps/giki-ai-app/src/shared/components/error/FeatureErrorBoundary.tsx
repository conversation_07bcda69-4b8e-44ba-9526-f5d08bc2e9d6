/**
 * Feature-Level Error Boundaries
 * 
 * Specialized error boundaries for different feature areas with
 * custom fallback UI and recovery strategies for production reliability.
 */
import React, { ReactNode } from 'react';
import { Upload, BarChart3, FileText, Cpu, RefreshCw, Home } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { ErrorBoundary } from '@/shared/components/ui/error-boundary';

// Feature-specific fallback components
interface FallbackProps {
  error?: Error;
  resetError: () => void;
  componentName: string;
}

const FileUploadFallback: React.FC<FallbackProps> = ({ resetError, componentName }) => (
  <Card className="max-w-md mx-auto mt-8 border-destructive/20">
    <CardHeader className="text-center pb-3">
      <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-3">
        <Upload className="h-6 w-6 text-destructive" />
      </div>
      <CardTitle className="text-destructive text-lg">Upload Temporarily Unavailable</CardTitle>
      <Badge variant="destructive" className="text-xs">
        {componentName} Error
      </Badge>
    </CardHeader>
    <CardContent className="text-center space-y-4">
      <p className="text-muted-foreground text-sm">
        File upload is temporarily experiencing issues. Your data is safe.
      </p>
      <div className="space-y-2">
        <Button 
          onClick={resetError}
          className="w-full"
          variant="outline"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Upload Again
        </Button>
        <Button 
          onClick={() => window.location.href = '/dashboard'}
          variant="ghost"
          className="w-full"
        >
          <Home className="mr-2 h-4 w-4" />
          Return to Dashboard
        </Button>
      </div>
      <p className="text-xs text-muted-foreground">
        If issues persist, try refreshing the page or contact support.
      </p>
    </CardContent>
  </Card>
);

const DashboardFallback: React.FC<FallbackProps> = ({ resetError, componentName }) => (
  <Card className="max-w-md mx-auto mt-8 border-warning/20">
    <CardHeader className="text-center pb-3">
      <div className="mx-auto w-12 h-12 bg-warning/10 rounded-full flex items-center justify-center mb-3">
        <BarChart3 className="h-6 w-6 text-warning" />
      </div>
      <CardTitle className="text-warning text-lg">Dashboard Component Unavailable</CardTitle>
      <Badge variant="secondary" className="text-xs">
        {componentName} Failure
      </Badge>
    </CardHeader>
    <CardContent className="text-center space-y-4">
      <p className="text-muted-foreground text-sm">
        Dashboard metrics are temporarily unavailable. Other features remain functional.
      </p>
      <div className="space-y-2">
        <Button 
          onClick={resetError}
          className="w-full"
          variant="outline"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Reload Dashboard
        </Button>
        <div className="grid grid-cols-2 gap-2">
          <Button 
            onClick={() => window.location.href = '/transactions'}
            variant="ghost"
            size="sm"
          >
            <FileText className="mr-1 h-3 w-3" />
            Transactions
          </Button>
          <Button 
            onClick={() => window.location.href = '/reports'}
            variant="ghost"
            size="sm"
          >
            <BarChart3 className="mr-1 h-3 w-3" />
            Reports
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
);

const TransactionFallback: React.FC<FallbackProps> = ({ resetError, componentName }) => (
  <Card className="max-w-md mx-auto mt-8 border-info/20">
    <CardHeader className="text-center pb-3">
      <div className="mx-auto w-12 h-12 bg-info/10 rounded-full flex items-center justify-center mb-3">
        <FileText className="h-6 w-6 text-info" />
      </div>
      <CardTitle className="text-info text-lg">Transaction Component Error</CardTitle>
      <Badge variant="outline" className="text-xs">
        {componentName} Error
      </Badge>
    </CardHeader>
    <CardContent className="text-center space-y-4">
      <p className="text-muted-foreground text-sm">
        Transaction processing component failed. Your data is secure.
      </p>
      <div className="space-y-2">
        <Button 
          onClick={resetError}
          className="w-full"
          variant="outline"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Reload Transactions
        </Button>
        <Button 
          onClick={() => window.location.href = '/dashboard'}
          variant="ghost"
          className="w-full"
        >
          <Home className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Button>
      </div>
      <p className="text-xs text-muted-foreground">
        Try refreshing or check back in a few minutes.
      </p>
    </CardContent>
  </Card>
);

const AgentPanelFallback: React.FC<FallbackProps> = ({ resetError, componentName }) => (
  <Card className="max-w-sm border-muted">
    <CardHeader className="pb-3">
      <div className="flex items-center gap-2">
        <div className="w-8 h-8 bg-muted rounded-lg flex items-center justify-center">
          <Cpu className="h-4 w-4 text-muted-foreground" />
        </div>
        <div>
          <CardTitle className="text-sm">AI Assistant Unavailable</CardTitle>
          <Badge variant="secondary" className="text-xs mt-1">
            {componentName} Error
          </Badge>
        </div>
      </div>
    </CardHeader>
    <CardContent className="pt-0 space-y-3">
      <p className="text-xs text-muted-foreground">
        AI assistant is temporarily unavailable. Other features remain functional.
      </p>
      <Button 
        onClick={resetError}
        variant="outline"
        size="sm"
        className="w-full"
      >
        <RefreshCw className="mr-2 h-3 w-3" />
        Reconnect Assistant
      </Button>
    </CardContent>
  </Card>
);

// Feature-specific error boundary components
interface FeatureErrorBoundaryProps {
  children: ReactNode;
  featureType: 'upload' | 'dashboard' | 'transactions' | 'agent' | 'reports';
  componentName?: string;
  onError?: (error: Error) => void;
  enableAutoRecovery?: boolean;
}

export const FeatureErrorBoundary: React.FC<FeatureErrorBoundaryProps> = ({
  children,
  featureType,
  componentName = 'Component',
  onError,
  enableAutoRecovery = true,
}) => {
  const getFallbackComponent = () => {
    // Use a functional component to access the reset functionality
    const FallbackWithReset = () => {
      const resetError = () => {
        // Force re-render by reloading the current route
        window.location.reload();
      };

      const fallbackProps = { resetError, componentName };
      
      switch (featureType) {
        case 'upload':
          return <FileUploadFallback {...fallbackProps} />;
        case 'dashboard':
          return <DashboardFallback {...fallbackProps} />;
        case 'transactions':
          return <TransactionFallback {...fallbackProps} />;
        case 'agent':
          return <AgentPanelFallback {...fallbackProps} />;
        default:
          return <DashboardFallback {...fallbackProps} />;
      }
    };

    return <FallbackWithReset />;
  };

  return (
    <ErrorBoundary
      componentName={componentName}
      enableAutoRecovery={enableAutoRecovery}
      maxRetries={2}
      onError={onError}
      fallback={getFallbackComponent()}
    >
      {children}
    </ErrorBoundary>
  );
};

// Convenience exports for specific features
export const FileUploadErrorBoundary: React.FC<{ children: ReactNode; componentName?: string }> = ({ 
  children, 
  componentName = 'FileUpload' 
}) => (
  <FeatureErrorBoundary featureType="upload" componentName={componentName}>
    {children}
  </FeatureErrorBoundary>
);

export const DashboardErrorBoundary: React.FC<{ children: ReactNode; componentName?: string }> = ({ 
  children, 
  componentName = 'Dashboard' 
}) => (
  <FeatureErrorBoundary featureType="dashboard" componentName={componentName}>
    {children}
  </FeatureErrorBoundary>
);

export const TransactionErrorBoundary: React.FC<{ children: ReactNode; componentName?: string }> = ({ 
  children, 
  componentName = 'Transaction' 
}) => (
  <FeatureErrorBoundary featureType="transactions" componentName={componentName}>
    {children}
  </FeatureErrorBoundary>
);

export const AgentErrorBoundary: React.FC<{ children: ReactNode; componentName?: string }> = ({ 
  children, 
  componentName = 'AgentPanel' 
}) => (
  <FeatureErrorBoundary featureType="agent" componentName={componentName}>
    {children}
  </FeatureErrorBoundary>
);

export default FeatureErrorBoundary;