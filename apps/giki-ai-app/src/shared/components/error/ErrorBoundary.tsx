import { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle, RefreshCw, Bug, Home, RotateCcw } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Separator } from '@/shared/components/ui/separator';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/shared/components/ui/card';
import { logger } from '@/shared/utils/errorHandling';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error using structured logger
    logger.error('Error caught by ErrorBoundary', 'ErrorBoundary', error, {
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
    });

    this.setState({
      error,
      errorInfo,
    });
  }

  handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render(): ReactNode {
    if (this?.state?.hasError) {
      // If a custom fallback is provided, use it
      if (this?.props?.fallback) {
        return this?.props?.fallback;
      }

      // Professional error UI with design system
      return (
        <div className="min-h-screen bg-secondary flex flex-wrap items-center justify-center p-6">
          <Card className="mx-auto max-w-2xl shadow-premium border-border">
            <CardHeader className="bg-gradient-to-r from-destructive/10 to-destructive/5 border-b border-border">
              <div className="flex flex-wrap items-center space-x-3">
                <div className="p-2 bg-destructive/10 rounded-lg">
                  <AlertCircle className="h-6 w-6 text-destructive" />
                </div>
                <div>
                  <CardTitle className="truncateh3 text-foreground">
                    Application Error
                  </CardTitle>
                  <CardDescription className="text-body-small text-muted-foreground">
                    Something unexpected happened. We&apos;re working to fix
                    this issue.
                  </CardDescription>
                </div>
              </div>
              <div className="flex flex-wrap items-center space-x-2 mt-4">
                <Badge
                  variant="destructive"
                  className="bg-destructive/20 text-destructive border-destructive max-w-[150px] truncate"
                >
                  <Bug className="h-3 w-3 mr-1" />
                  Runtime Error
                </Badge>
                <Badge
                  variant="outline"
                  className="text-muted-foreground border-border max-w-[150px] truncate"
                >
                  Error ID: {Date.now().toString(36)}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="pt-6 space-y-6 overflow-hidden">
              <div className="space-y-4">
                <div>
                  <h3 className="truncateh5 text-foreground mb-2">
                    Error Details
                  </h3>
                  <div className="p-4 bg-muted rounded-lg border border-border">
                    <p className="truncatebody text-foreground text-financial">
                      {this?.state?.error?.message ||
                        'An unknown error occurred'}
                    </p>
                  </div>
                </div>

                {this?.state?.errorInfo && (
                  <div>
                    <h3 className="truncateh5 text-foreground mb-2">
                      Technical Information
                    </h3>
                    <details className="group">
                      <summary className="cursor-pointer text-body-small text-muted-foreground hover:text-foreground transition-colors flex flex-wrap items-center space-x-2">
                        <span>View Stack Trace</span>
                        <span className="truncate text-caption">
                          (Click to expand)
                        </span>
                      </summary>
                      <div className="mt-3 p-4 bg-background rounded-lg border border-border max-h-64 overflow-auto">
                        <pre className="truncate text-caption text-muted-foreground text-financial whitespace-pre-wrap">
                          {this?.state?.errorInfo.componentStack}
                        </pre>
                      </div>
                    </details>
                  </div>
                )}

                <Separator className="my-6" />

                <div className="space-y-3">
                  <h3 className="truncateh5 text-foreground">
                    What can you do?
                  </h3>
                  <ul className="space-y-2 text-body-small text-muted-foreground">
                    <li className="flex flex-wrap items-start space-x-2">
                      <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex flex-wrap-shrink-0" />
                      <span>
                        Try refreshing the page to see if the issue resolves
                      </span>
                    </li>
                    <li className="flex flex-wrap items-start space-x-2">
                      <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex flex-wrap-shrink-0" />
                      <span>Check your internet connection and try again</span>
                    </li>
                    <li className="flex flex-wrap items-start space-x-2">
                      <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex flex-wrap-shrink-0" />
                      <span>Contact support if the problem persists</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>

            <CardFooter className="bg-muted/30 border-t border-border">
              <div className="flex flex-wrap items-center justify-between w-full">
                <div className="flex flex-wrap items-center space-x-3">
                  <Button
                    className="max-w-full text-foreground border-border hover:bg-muted"
                    variant="outline"
                    onClick={() => (window.location.href = '/')}
                  >
                    <Home className="mr-2 h-4 w-4" />
                    Go Home
                  </Button>
                  <Button
                    className="max-w-full text-foreground border-border hover:bg-muted"
                    variant="outline"
                    onClick={() => window.location.reload()}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reload Page
                  </Button>
                </div>
                <Button
                  className="max-w-full bg-primary text-primary-foreground hover:bg-primary/90"
                  onClick={this.handleReset}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      );
    }

    return this?.props?.children;
  }
}

export default ErrorBoundary;
