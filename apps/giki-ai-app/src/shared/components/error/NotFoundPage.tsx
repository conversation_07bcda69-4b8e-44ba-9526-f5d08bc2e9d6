import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import Logo from '@/shared/components/ui/logo';
import { cn } from '@/shared/utils/utils';

interface NotFoundPageProps {
  attemptedPath?: string;
  message?: string;
  className?: string;
}

const NotFoundPage: React.FC<NotFoundPageProps> = ({
  attemptedPath,
  message,
  className,
}) => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoDashboard = () => {
    navigate('/dashboard');
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  const handleContactSupport = () => {
    window.open('mailto:<EMAIL>', '_blank');
  };

  return (
    <div
      className={cn(
        'min-h-screen flex flex-wrap items-center justify-center bg-background',
        'px-4 py-8',
        className,
      )}
    >
      {/* Skip to main content link for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md"
      >
        Skip to main content
      </a>

      <div id="main-content" className="w-full max-w-2xl mx-auto text-center">
        {/* Header with Logo */}
        <div className="mb-8">
          <div className="flex justify-center mb-6">
            <Logo
              size="xl"
              variant="default"
              showText={true}
              className="text-center"
            />
          </div>

          {/* Professional header bar with brand color */}
          <div
            className="h-1 w-24 mx-auto rounded-full mb-8"
            style={{ backgroundColor: 'var(--giki-primary)' }}
          />
        </div>

        {/* Main error content in Card */}
        <Card className="mx-auto max-w-lg shadow-lg border-2">
          <CardContent className="p-8">
            {/* 404 with brand color accent */}
            <div className="mb-6">
              <h1
                className="text-6xl font-bold mb-2"
                style={{ color: 'var(--giki-primary)' }}
                role="heading"
                aria-level={1}
              >
                404
              </h1>
              <div className="flex items-center justify-center gap-2 mb-4">
                <span className="text-lg">□</span>
                <h2 className="text-xl font-semibold text-foreground">
                  Page Not Found
                </h2>
                <span className="text-lg">□</span>
              </div>
            </div>

            {/* Enhanced error message */}
            <div className="mb-8 space-y-3">
              <p className="text-muted-foreground text-base leading-relaxed">
                {message ||
                  "We couldn't find the page you're looking for. It might have been moved, deleted, or the URL was entered incorrectly."}
              </p>

              {attemptedPath && (
                <p className="text-sm text-muted-foreground">
                  <span className="font-medium">Requested path:</span>{' '}
                  {attemptedPath}
                </p>
              )}

              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mt-4">
                <span>⊞</span>
                <span>Your financial data remains secure and unaffected</span>
                <span>⊞</span>
              </div>
            </div>

            {/* Multiple navigation options */}
            <div className="space-y-4">
              {/* Primary actions */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={handleGoDashboard}
                  className="bg-brand-primary hover:bg-brand-primary/90 text-white px-6 py-2"
                  size="lg"
                >
                  <span className="mr-2">∷</span>
                  Go to Dashboard
                </Button>

                <Button
                  onClick={handleGoHome}
                  variant="outline"
                  size="lg"
                  className="border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white px-6 py-2"
                >
                  <span className="mr-2">□</span>
                  Go Home
                </Button>
              </div>

              {/* Secondary actions */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={handleGoBack}
                  variant="ghost"
                  size="sm"
                  className="text-muted-foreground hover:text-brand-primary"
                >
                  <span className="mr-2">↑</span>
                  Go Back
                </Button>

                <Button
                  onClick={handleContactSupport}
                  variant="ghost"
                  size="sm"
                  className="text-muted-foreground hover:text-brand-primary"
                >
                  <span className="mr-2">⚬</span>
                  Contact Support
                </Button>
              </div>
            </div>

            {/* Professional help text */}
            <div className="mt-8 pt-6 border-t border-border">
              <p className="text-xs text-muted-foreground leading-relaxed">
                Need immediate assistance? Contact our support team at{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="text-brand-primary hover:underline font-medium"
                >
                  <EMAIL>
                </a>{' '}
                or visit our help center for troubleshooting guides.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer with additional context */}
        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground">
            Professional automated MIS preparation platform
          </p>
          <div className="flex justify-center items-center gap-4 mt-4 text-xs text-muted-foreground">
            <span>□ Complete MIS Automation</span>
            <span>⊞ Custom Report Generation</span>
            <span>∷ AI Financial Intelligence</span>
          </div>
        </div>
      </div>

      {/* Notifications region for screen readers */}
      <div
        role="region"
        aria-label="Notifications"
        className="sr-only"
        aria-live="polite"
      />
    </div>
  );
};

export default NotFoundPage;
