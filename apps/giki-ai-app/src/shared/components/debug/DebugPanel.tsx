import React, { useState, useEffect } from 'react';
import { X, Download, RefreshCw, Trash2, Bug } from 'lucide-react';
import {
  logger,
  generateDebugReport,
  getErrorReports,
  clearErrorReports,
  type LogEntry,
  type PerformanceMetric,
  type NetworkLogEntry,
  type UserAction,
} from '@/shared/lib/logger';
import { Button } from '@/shared/components/ui/button';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import { Card } from '@/shared/components/ui/card';
import { cn } from '@/shared/utils/utils';

interface DebugPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export function DebugPanel({ isOpen, onClose }: DebugPanelProps) {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [networkLogs, setNetworkLogs] = useState<NetworkLogEntry[]>([]);
  const [userActions, setUserActions] = useState<UserAction[]>([]);
  const [errorReports, setErrorReports] = useState(getErrorReports());
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (!isOpen || !autoRefresh) return;

    const refreshData = () => {
      setLogs(logger.getRecentLogs());
      setMetrics(logger.getPerformanceMetrics());
      setNetworkLogs(logger.getNetworkLogs());
      setUserActions(logger.getUserActions());
      setErrorReports(getErrorReports());
    };

    refreshData();
    const interval = setInterval(refreshData, 2000);

    return () => clearInterval(interval);
  }, [isOpen, autoRefresh]);

  const downloadDebugReport = () => {
    const report = generateDebugReport();
    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `giki-debug-report-${new Date().toISOString()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const clearAllReports = () => {
    clearErrorReports();
    setErrorReports([]);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
      <div className="fixed right-0 top-0 bottom-0 w-[600px] bg-background border-l shadow-lg overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Bug className="w-5 h-5 text-primary" />
            <h2 className="text-xl font-semibold">Debug Panel</h2>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={cn(autoRefresh && 'text-primary')}
            >
              <RefreshCw
                className={cn('w-4 h-4', autoRefresh && 'animate-spin')}
              />
            </Button>
            <Button variant="ghost" size="sm" onClick={downloadDebugReport}>
              <Download className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="logs" className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-5 p-4">
              <TabsTrigger value="logs">Logs</TabsTrigger>
              <TabsTrigger value="network">Network</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="actions">Actions</TabsTrigger>
              <TabsTrigger value="errors">Errors</TabsTrigger>
            </TabsList>

            {/* Logs Tab */}
            <TabsContent value="logs" className="flex-1 overflow-auto p-4">
              <div className="space-y-2">
                {logs.map((log, index) => (
                  <Card
                    key={index}
                    className={cn(
                      'p-3 text-sm font-mono',
                      log.level === 'error' &&
                        'border-destructive bg-destructive/10',
                      log.level === 'warn' && 'border-warning bg-warning/10',
                    )}
                  >
                    <div className="flex items-start gap-2">
                      <span
                        className={cn(
                          'text-xs font-medium uppercase',
                          log.level === 'error' && 'text-destructive',
                          log.level === 'warn' && 'text-warning',
                          log.level === 'info' && 'text-info',
                          log.level === 'debug' && 'text-muted-foreground',
                        )}
                      >
                        [{log.level}]
                      </span>
                      <div className="flex-1">
                        <div>{log.message}</div>
                        {log.context && (
                          <pre className="mt-1 text-xs text-muted-foreground">
                            {JSON.stringify(log.context, null, 2)}
                          </pre>
                        )}
                        {log.error && (
                          <pre className="mt-1 text-xs text-destructive">
                            {log.error.stack || log.error.message}
                          </pre>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Network Tab */}
            <TabsContent value="network" className="flex-1 overflow-auto p-4">
              <div className="space-y-2">
                {networkLogs.map((request, index) => (
                  <Card
                    key={index}
                    className={cn(
                      'p-3 text-sm',
                      request.error && 'border-destructive bg-destructive/10',
                      request.duration > 3000 && 'border-warning bg-warning/10',
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{request.method}</span>
                        <span className="text-xs text-muted-foreground truncate max-w-[300px]">
                          {request.url}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        {request.status && (
                          <span
                            className={cn(
                              'text-xs font-medium',
                              request.status < 400
                                ? 'text-success'
                                : 'text-destructive',
                            )}
                          >
                            {request.status}
                          </span>
                        )}
                        <span className="text-xs text-muted-foreground">
                          {request.duration}ms
                        </span>
                      </div>
                    </div>
                    {request.error && (
                      <div className="mt-1 text-xs text-destructive">
                        {request.error}
                      </div>
                    )}
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Performance Tab */}
            <TabsContent
              value="performance"
              className="flex-1 overflow-auto p-4"
            >
              <div className="space-y-2">
                {metrics.map((metric, index) => (
                  <Card
                    key={index}
                    className={cn(
                      'p-3 text-sm',
                      !metric.success && 'border-destructive bg-destructive/10',
                      metric.duration > 1000 && 'border-warning bg-warning/10',
                    )}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{metric.operation}</span>
                      <div className="flex items-center gap-2">
                        <span
                          className={cn(
                            'text-xs',
                            metric.success
                              ? 'text-success'
                              : 'text-destructive',
                          )}
                        >
                          {metric.success ? '□' : '⚬'}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {metric.duration}ms
                        </span>
                      </div>
                    </div>
                    {metric.metadata && (
                      <pre className="mt-1 text-xs text-muted-foreground">
                        {JSON.stringify(metric.metadata, null, 2)}
                      </pre>
                    )}
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* User Actions Tab */}
            <TabsContent value="actions" className="flex-1 overflow-auto p-4">
              <div className="space-y-2">
                {userActions.map((action, index) => (
                  <Card key={index} className="p-3 text-sm">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-medium">{action.action}</span>
                        <span className="text-xs text-muted-foreground ml-2">
                          in {action.component}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(action.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    {action.metadata && (
                      <pre className="mt-1 text-xs text-muted-foreground">
                        {JSON.stringify(action.metadata, null, 2)}
                      </pre>
                    )}
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Error Reports Tab */}
            <TabsContent value="errors" className="flex-1 overflow-auto p-4">
              <div className="space-y-4">
                <div className="flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAllReports}
                    disabled={errorReports.length === 0}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear All
                  </Button>
                </div>
                {errorReports.map((report, index) => (
                  <Card key={index} className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm">{report.id}</span>
                        <span className="text-xs text-muted-foreground">
                          {report.environment.timestamp}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        <div>URL: {report.environment.url}</div>
                        <div>Session: {report.environment.sessionId}</div>
                        {report.environment.userId && (
                          <div>User: {report.environment.userId}</div>
                        )}
                      </div>
                      <div className="text-xs">
                        <div>Logs: {report.entries.length}</div>
                        <div>Network: {report.networkLogs.length}</div>
                        <div>Metrics: {report.performanceMetrics.length}</div>
                        <div>Actions: {report.userActions.length}</div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const blob = new Blob(
                            [JSON.stringify(report, null, 2)],
                            {
                              type: 'application/json',
                            },
                          );
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = `error-report-${report.id}.json`;
                          a.click();
                          URL.revokeObjectURL(url);
                        }}
                      >
                        <Download className="w-3 h-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

// Debug toggle button for development
export function DebugToggle() {
  const [isDebugOpen, setIsDebugOpen] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      <button
        onClick={() => setIsDebugOpen(true)}
        className="fixed bottom-4 right-4 z-40 p-3 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-shadow"
        title="Open Debug Panel"
      >
        <Bug className="w-5 h-5" />
      </button>
      <DebugPanel isOpen={isDebugOpen} onClose={() => setIsDebugOpen(false)} />
    </>
  );
}
