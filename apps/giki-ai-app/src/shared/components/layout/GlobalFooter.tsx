/**
 * Global Footer Component - Professional B2B Footer
 *
 * Provides consistent footer across all pages with:
 * - Quick navigation links
 * - System status and version information
 * - Help and support links
 * - Professional copyright notice
 * - Brand compliance with design system
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { ExternalLink, HelpCircle, Shield, FileText } from 'lucide-react';

interface GlobalFooterProps {
  className?: string;
}

// Footer navigation links
const FOOTER_LINKS = {
  product: [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Upload Files', href: '/upload' },
    { label: 'Reports', href: '/reports' },
    { label: 'Categories', href: '/categories' },
  ],
  support: [
    { label: 'Help Center', href: '/help', external: true },
    { label: 'Contact Support', href: '/support', external: true },
    { label: 'Documentation', href: '/docs', external: true },
    { label: 'API Reference', href: '/api-docs', external: true },
  ],
  legal: [
    { label: 'Privacy Policy', href: '/privacy', external: true },
    { label: 'Terms of Service', href: '/terms', external: true },
    { label: 'Security', href: '/security', external: true },
    { label: 'Compliance', href: '/compliance', external: true },
  ],
};

// System information - would typically come from environment or API
const SYSTEM_INFO = {
  version: '2.1.0',
  buildDate: '2025-01-11',
  environment: process.env.NODE_ENV || 'development',
};

const GlobalFooterComponent: React.FC<GlobalFooterProps> = ({ className = '' }) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer 
      className={`
        bg-white border-t border-gray-200 
        mt-auto pt-8 pb-6 px-6
        ${className}
      `}
    >
      <div className="max-w-7xl mx-auto">
        {/* Main footer content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Brand and description */}
          <div className="md:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-brand-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">G</span>
              </div>
              <span className="text-lg font-semibold text-brand-primary">giki.ai</span>
            </div>
            <p className="text-sm text-gray-600 mb-4 leading-relaxed">
              AI-powered financial transaction categorization and management 
              platform for modern businesses.
            </p>
            <div className="text-xs text-gray-500 space-y-1">
              <div>Version {SYSTEM_INFO.version}</div>
              <div>Built on {SYSTEM_INFO.buildDate}</div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>System Operational</span>
              </div>
            </div>
          </div>

          {/* Product links */}
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Product</h3>
            <ul className="space-y-3">
              {FOOTER_LINKS.product.map((link) => (
                <li key={link.href}>
                  <Link
                    to={link.href}
                    className="text-sm text-gray-600 hover:text-brand-primary transition-colors duration-200"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support links */}
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-1">
              <HelpCircle className="h-4 w-4" />
              <span>Support</span>
            </h3>
            <ul className="space-y-3">
              {FOOTER_LINKS.support.map((link) => (
                <li key={link.href}>
                  {link.external ? (
                    <a
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-gray-600 hover:text-brand-primary transition-colors duration-200 flex items-center space-x-1"
                    >
                      <span>{link.label}</span>
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  ) : (
                    <Link
                      to={link.href}
                      className="text-sm text-gray-600 hover:text-brand-primary transition-colors duration-200"
                    >
                      {link.label}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>

          {/* Legal links */}
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-1">
              <Shield className="h-4 w-4" />
              <span>Legal</span>
            </h3>
            <ul className="space-y-3">
              {FOOTER_LINKS.legal.map((link) => (
                <li key={link.href}>
                  {link.external ? (
                    <a
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-gray-600 hover:text-brand-primary transition-colors duration-200 flex items-center space-x-1"
                    >
                      <span>{link.label}</span>
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  ) : (
                    <Link
                      to={link.href}
                      className="text-sm text-gray-600 hover:text-brand-primary transition-colors duration-200"
                    >
                      {link.label}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="flex flex-col md:flex-row justify-between items-center pt-6 border-t border-gray-100">
          <div className="text-sm text-gray-500 mb-4 md:mb-0">
            © {currentYear} giki.ai. All rights reserved.
          </div>
          
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <FileText className="h-4 w-4" />
              <span>Enterprise Ready</span>
            </div>
            <div className="flex items-center space-x-1">
              <Shield className="h-4 w-4" />
              <span>SOC 2 Compliant</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-brand-primary rounded-full"></div>
              <span>Secure & Encrypted</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export const GlobalFooter = React.memo(GlobalFooterComponent);
GlobalFooter.displayName = 'GlobalFooter';

export default GlobalFooter;