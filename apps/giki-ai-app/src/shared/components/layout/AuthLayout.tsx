import React from 'react';
import Logo from '@/shared/components/ui/logo';
import ThemeToggle from '@/shared/components/ui/ThemeToggle';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle: string;
  showLogo?: boolean;
}

const AuthLayoutComponent: React.FC<AuthLayoutProps> = ({
  children,
  title,
  subtitle,
  showLogo = true,
}) => {
  return (
    <div className="min-h-screen bg-background text-foreground relative overflow-hidden">
      {/* Theme Toggle - consistent with main app */}
      <div className="absolute top-4 right-4 z-modal">
        <ThemeToggle />
      </div>

      {/* Background Pattern - consistent with MainContent */}
      <div className="absolute inset-0 bg-grid-pattern opacity-3 pointer-events-none" />
      <div className="absolute inset-0 bg-gradient-to-br from-primary/2 via-transparent to-accent/2 pointer-events-none" />

      {/* Main Content Container */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-sm">
          {/* Auth Card - Clean, properly sized design */}
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
            {/* Header Section */}
            <div className="px-6 pt-6 pb-4 border-b border-gray-100">
              {showLogo && (
                <div className="flex justify-center items-center mb-4">
                  <Logo size="md" showText={true} />
                </div>
              )}
              <div className="text-center">
                <h1 className="text-3xl font-semibold text-gray-900 mb-1">
                  {title}
                </h1>
                <p className="text-sm text-gray-600">{subtitle}</p>
              </div>
            </div>

            {/* Form Content */}
            <div className="px-6 py-6">{children}</div>
          </div>

          {/* Footer - Professional branding */}
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              © 2024 Giki AI. Professional financial data management platform.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export const AuthLayout = React.memo(AuthLayoutComponent);
AuthLayout.displayName = 'AuthLayout';

export default AuthLayout;
