import React from 'react';
import Logo from '@/shared/components/ui/logo';

interface ChatInterfaceToggleProps {
  onToggleExpand: () => void;
  isExpanded: boolean;
}

export const ChatInterfaceToggle: React.FC<ChatInterfaceToggleProps> = ({
  onToggleExpand,
  isExpanded,
}) => {
  return (
    <div
      className={`fixed top-40 z-overlay transition-all duration-500`}
      style={{
        right: isExpanded ? '400px' : '0px',
      }}
    >
      {/* Modern AI-style handle attached to panel border */}
      <div
        onClick={() => void onToggleExpand()}
        className={`
          relative cursor-pointer group overflow-hidden
          bg-gradient-to-br from-background via-background to-background
          border border-border border-r-0
          hover:from-primary hover:via-primary hover:to-primary
          shadow-lg hover:shadow-xl hover:shadow-primary/20
          transition-all duration-300 ease-out
          rounded-l-lg
          h-6 w-4
          flex items-center justify-center
          backdrop-blur-sm
          ${isExpanded ? 'hover:-translate-x-1' : 'hover:-translate-x-1'}
        `}
        title={isExpanded ? 'Close AI Assistant' : 'Open AI Assistant'}
      >
        {/* Subtle background pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-foreground/10 to-transparent opacity-50 pointer-events-none" />

        {/* Modern AI Logo */}
        <div className="relative flex flex-wrap items-center justify-center improve group-hover:scale-105 transition-improve duration-200">
          <Logo
            size="sm"
            variant="default"
            showText={false}
            className="text-foreground group-hover:text-primary-foreground"
          />
        </div>

        {/* AI pulse indicator when collapsed */}
        {!isExpanded && (
          <div className="absolute -top-0.5 -left-0.5 flex flex-wrap items-center justify-center">
            <div className="w-2 h-2 bg-gradient-to-br from-primary to-primary rounded-full border border-border shadow-sm animate-pulse" />
            <div className="absolute w-2 h-2 bg-primary/20 rounded-full animate-ping" />
          </div>
        )}

        {/* Modern AI glow effect */}
        <div className="absolute inset-0 rounded-l-xl bg-gradient-to-br from-emerald-500/0 via-cyan-500/0 to-teal-500/0 group-hover:from-emerald-500/10 group-hover:via-cyan-500/5 group-hover:to-teal-500/10 transition-all duration-300 pointer-events-none" />
      </div>
    </div>
  );
};

export default ChatInterfaceToggle;
