import React from 'react';

interface QuickActionsProps {
  actions: string[];
  onActionClick: (action: string) => void;
}

export const QuickActions: React.FC<QuickActionsProps> = React.memo(({
  actions,
  onActionClick,
}) => {
  return (
    <div className="space-y-1">
      <p className="text-xs font-medium text-muted-foreground mb-2">
        Quick actions
      </p>
      <div className="flex flex-wrap gap-2">
        {actions.map((action, index) => (
          <button
            key={action}
            onClick={() => onActionClick(action)}
            className="px-3 py-1.5 text-xs bg-secondary/50 hover:bg-secondary text-secondary-foreground rounded-md transition-all duration-200 hover:shadow-sm animate-fade-in"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {action}
          </button>
        ))}
      </div>
    </div>
  );
});

QuickActions.displayName = 'QuickActions';
