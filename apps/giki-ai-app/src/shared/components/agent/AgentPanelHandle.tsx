import React from 'react';

interface AgentPanelHandleProps {
  onToggleExpand: () => void;
  isExpanded: boolean;
  isVisible?: boolean;
}

export const AgentPanelHandle: React.FC<AgentPanelHandleProps> = ({
  onToggleExpand,
  isExpanded,
  isVisible = true,
}) => {
  if (!isVisible) return null;

  return (
    <div
      className={`fixed top-1/2 transform -translate-y-1/2 z-[100] transition-all duration-300 ease-in-out`}
      style={{
        right: isExpanded ? '400px' : '0px',
      }}
    >
      {/* Agent Activation Badge - matches mockup design */}
      <div
        onClick={onToggleExpand}
        className={`
          w-12 h-12 bg-brand-primary hover:bg-brand-primary-hover
          rounded-l-xl flex items-center justify-center
          cursor-pointer transition-all duration-300 ease-in-out
          shadow-lg hover:shadow-xl
          ${!isExpanded ? 'hover:translate-x-[-4px]' : ''}
          relative overflow-hidden
        `}
        title={isExpanded ? 'Close AI Assistant (Cmd+K)' : 'Open AI Assistant (Cmd+K)'}
      >
        {/* Giki Logo */}
        <img 
          src="/images/giki-logo.svg" 
          alt="AI Assistant" 
          className="w-6 h-6 filter brightness-0 invert"
        />
        
        {/* Pulse Animation Dot - only when collapsed */}
        {!isExpanded && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse-dot" />
          </div>
        )}
      </div>
      
      {/* Custom pulse animation styles */}
      <style jsx>{`
        @keyframes pulse-dot {
          0%, 100% { 
            opacity: 1; 
            transform: translate(-50%, -50%) scale(1); 
          }
          50% { 
            opacity: 0.5; 
            transform: translate(-50%, -50%) scale(1.2); 
          }
        }
        .animate-pulse-dot {
          animation: pulse-dot 2s infinite;
        }
      `}</style>
    </div>
  );
};

export default AgentPanelHandle;