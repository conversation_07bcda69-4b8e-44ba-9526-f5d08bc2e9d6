/**
 * ADK Agent Client - Sophisticated Agent Collaboration Interface
 *
 * Frontend integration with backend ADK (Agent Development Kit) capabilities.
 * Provides agent discovery, coordination, memory management, and complex workflows.
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  Bot,
  Network,
  Database,
  Zap,
  GitBranch,
  Activity,
  Users,
  CheckCircle,
  AlertCircle,
  RotateCcw,
} from 'lucide-react';
import { cn } from '@/shared/utils/utils';

interface ADKAgent {
  id: string;
  name: string;
  type:
    | 'gl_code'
    | 'debit_credit'
    | 'reports'
    | 'categorization'
    | 'coordinator'
    | 'customer';
  status: 'available' | 'busy' | 'offline' | 'error';
  capabilities: string[];
  currentTask?: string;
  lastActivity: string;
  memorySize: number;
  toolsAvailable: string[];
}

interface AgentSession {
  id: string;
  agentId: string;
  conversationId: string;
  memoryContext: unknown;
  startTime: string;
  lastInteraction: string;
  isActive: boolean;
}

interface AgentTransfer {
  fromAgent: string;
  toAgent: string;
  context: unknown;
  reason: string;
  timestamp: string;
}

interface ADKAgentClientProps {
  className?: string;
  enableAgentDiscovery?: boolean;
  enableDatabaseManagement?: boolean;
  enableA2AProtocol?: boolean;
}

const ADKAgentClient: React.FC<ADKAgentClientProps> = ({
  className,
  enableAgentDiscovery = true,
  enableDatabaseManagement = true,
  enableA2AProtocol = true,
}) => {
  const [agents, setAgents] = useState<ADKAgent[]>([]);
  const [_sessions, setSessions] = useState<AgentSession[]>([]);
  const [transfers, setTransfers] = useState<AgentTransfer[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<ADKAgent | null>(null);
  const [currentSession, setCurrentSession] = useState<AgentSession | null>(
    null,
  );
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [agentNetworkStatus, setAgentNetworkStatus] = useState<
    'connected' | 'discovering' | 'offline'
  >('offline');

  // Simulate agent discovery (in real implementation, this would call backend ADK APIs)
  const discoverAgents = useCallback(async () => {
    setIsDiscovering(true);
    setAgentNetworkStatus('discovering');

    try {
      // Simulate API call to backend ADK agent discovery
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const discoveredAgents: ADKAgent[] = [
        {
          id: 'gl-code-agent',
          name: 'GL Code Agent',
          type: 'gl_code',
          status: 'available',
          capabilities: [
            'assign_gl_codes',
            'create_category_hierarchy',
            'map_to_23_toplevel_structure',
          ],
          lastActivity: new Date().toISOString(),
          memorySize: 1024,
          toolsAvailable: [
            'load_memory',
            'preload_memory',
            'transfer_to_agent',
          ],
        },
        {
          id: 'debit-credit-agent',
          name: 'Debit Credit Agent',
          type: 'debit_credit',
          status: 'available',
          capabilities: [
            'infer_transaction_type',
            'validate_accounting_rules',
            'detect_anomalies',
          ],
          lastActivity: new Date().toISOString(),
          memorySize: 512,
          toolsAvailable: [
            'load_artifacts',
            'openapi_tool',
            'vertex_ai_search_tool',
          ],
        },
        {
          id: 'reports-agent',
          name: 'Reports Agent',
          type: 'reports',
          status: 'busy',
          capabilities: [
            'generate_reports',
            'financial_analysis',
            'export_data',
          ],
          currentTask: 'Generating Q4 financial report',
          lastActivity: new Date().toISOString(),
          memorySize: 2048,
          toolsAvailable: [
            'apihub_tool',
            'google_search_tool',
            'LongRunningFunctionTool',
          ],
        },
        {
          id: 'coordinator-agent',
          name: 'Coordinator Agent',
          type: 'coordinator',
          status: 'available',
          capabilities: [
            'orchestrate_workflows',
            'manage_agent_handoffs',
            'optimize_resources',
          ],
          lastActivity: new Date().toISOString(),
          memorySize: 4096,
          toolsAvailable: [
            'transfer_to_agent',
            'exit_loop_tool',
            'get_user_choice',
          ],
        },
        {
          id: 'customer-agent',
          name: 'Customer Agent',
          type: 'customer',
          status: 'available',
          capabilities: [
            'ui_equivalence',
            'conversation_handling',
            'user_assistance',
          ],
          lastActivity: new Date().toISOString(),
          memorySize: 1536,
          toolsAvailable: [
            'load_memory',
            'transfer_to_agent',
            'get_user_choice_tool',
          ],
        },
      ];

      setAgents(discoveredAgents);
      setAgentNetworkStatus('connected');
    } catch {
      setAgentNetworkStatus('offline');
    } finally {
      setIsDiscovering(false);
    }
  }, []);

  // Initialize agent session with memory preloading
  const startAgentSession = useCallback(
    (agent: ADKAgent) => {
      const session: AgentSession = {
        id: `session-${Date.now()}`,
        agentId: agent.id,
        conversationId: `conv-${Date.now()}`,
        memoryContext: {},
        startTime: new Date().toISOString(),
        lastInteraction: new Date().toISOString(),
        isActive: true,
      };

      setSessions((prev) => [...prev, session]);
      setCurrentSession(session);
      setSelectedAgent(agent);

      // Simulate memory preloading
      if (enableDatabaseManagement) {
        // This would call backend API: POST /api/v1/adk/agents/{agentId}/preload-memory
        // Preloading memory for agent with context size
      }
    },
    [enableDatabaseManagement],
  );

  // Transfer conversation to another agent
  const transferToAgent = useCallback(
    (targetAgentId: string, context: unknown, reason: string) => {
      if (!selectedAgent) return;

      const transfer: AgentTransfer = {
        fromAgent: selectedAgent.id,
        toAgent: targetAgentId,
        context,
        reason,
        timestamp: new Date().toISOString(),
      };

      setTransfers((prev) => [...prev, transfer]);

      // Find target agent and start new session
      const targetAgent = agents.find((a) => a.id === targetAgentId);
      if (targetAgent) {
        startAgentSession(targetAgent);
      }

      // This would call backend API: POST /api/v1/adk/agents/transfer
    },
    [selectedAgent, agents, startAgentSession],
  );

  // Load persistent memory for agent
  const loadAgentDatabase = useCallback(
    (_agentId: string) => {
      if (!enableDatabaseManagement) return null;

      try {
        // This would call backend API: GET /api/v1/adk/agents/{agentId}/memory
        return {
          conversationHistory: [],
          userPreferences: {},
          contextData: {},
        };
      } catch (error) {
        console.error('Failed to load agent memory:', error);
        throw new Error(`Failed to load agent memory: ${error}`);
      }
    },
    [enableDatabaseManagement],
  );

  // Initialize discovery on mount
  useEffect(() => {
    if (enableAgentDiscovery) {
      void discoverAgents();
    }
  }, [enableAgentDiscovery, discoverAgents]);

  const getAgentStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4 text-success" />;
      case 'busy':
        return <Activity className="h-4 w-4 text-warning animate-pulse" />;
      case 'offline':
        return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      default:
        return <Bot className="h-4 w-4" />;
    }
  };

  const getAgentTypeColor = (type: string) => {
    switch (type) {
      case 'gl_code':
        return 'bg-info/10 text-info border-info/20';
      case 'debit_credit':
        return 'bg-success/10 text-success border-success/20';
      case 'reports':
        return 'bg-accent/10 text-accent-foreground border-accent/20';
      case 'coordinator':
        return 'bg-warning/10 text-warning border-warning/20';
      case 'customer':
        return 'bg-primary/10 text-primary border-primary/20';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className={cn('adk-agent-client space-y-4', className)}>
      {/* Agent Network Status */}
      <Card className="border-border shadow-card-shadow">
        <CardHeader className="pb-3">
          <CardTitle className="flex flex-wrap items-center justify-between">
            <div className="flex flex-wrap items-center gap-2">
              <Network className="h-5 w-5 text-primary" />
              <span className="truncate text-heading-4">ADK Agent Network</span>
            </div>
            <div className="flex flex-wrap items-center gap-2">
              <div
                className={cn(
                  'h-2 w-2 rounded-full',
                  agentNetworkStatus === 'connected'
                    ? 'bg-success'
                    : agentNetworkStatus === 'discovering'
                      ? 'bg-warning animate-pulse'
                      : 'bg-destructive',
                )}
              />
              <span className="truncate text-caption capitalize text-muted-foreground">
                {agentNetworkStatus}
              </span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="flex flex-wrap items-center justify-between">
            <div className="space-y-1">
              <div className="truncatebody font-medium text-primary">
                {agents.length} Agents Discovered
              </div>
              <div className="truncate text-caption text-muted-foreground">
                {agents.filter((a) => a.status === 'available').length}{' '}
                available, {agents.filter((a) => a.status === 'busy').length}{' '}
                busy
              </div>
            </div>

            <Button
              className="max-w-full"
              onClick={() => void discoverAgents()}
              disabled={isDiscovering}
              variant="outline"
              size="sm"
            >
              {isDiscovering && (
                <RotateCcw className="h-3 w-3 mr-1 animate-spin" />
              )}
              {isDiscovering ? 'Discovering...' : 'Refresh Network'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Agent Directory */}
      {enableAgentDiscovery && (
        <Card className="border-border shadow-card-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="flex flex-wrap items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <span className="truncate text-heading-4">Available Agents</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="space-y-3">
              {agents.map((agent) => (
                <div
                  key={agent.id}
                  className={cn(
                    'p-3 rounded-lg border transition-all cursor-pointer shadow-sm hover:shadow-md',
                    selectedAgent?.id === agent.id
                      ? 'border-primary bg-accent/20 shadow-card-shadow'
                      : 'border-border hover:bg-accent/10 hover:border-primary/50',
                  )}
                  onClick={() => void startAgentSession(agent)}
                >
                  <div className="flex flex-wrap items-center justify-between">
                    <div className="flex flex-wrap items-center gap-3">
                      {getAgentStatusIcon(agent.status)}
                      <div>
                        <div className="truncatebody font-semibold text-primary">
                          {agent.name}
                        </div>
                        <div className="flex flex-wrap items-center gap-2 mt-1">
                          <Badge
                            variant="outline"
                            className={cn(
                              'text-caption font-medium max-w-[150px] truncate',
                              getAgentTypeColor(agent.type),
                            )}
                          >
                            {agent.type.replace('_', ' ')}
                          </Badge>
                          <span className="truncate text-caption text-muted-foreground">
                            {agent.capabilities.length} capabilities
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      {agent.currentTask && (
                        <div className="truncate text-caption text-muted-foreground mb-1">
                          {agent.currentTask}
                        </div>
                      )}
                      <div className="flex flex-wrap items-center gap-1 truncate text-caption text-muted-foreground">
                        <Database className="h-3 w-3 text-muted-foreground" />
                        {agent.memorySize}KB
                      </div>
                    </div>
                  </div>

                  {/* Agent Tools */}
                  <div className="mt-2 flex flex-wrap flex-wrap gap-1">
                    {agent.toolsAvailable.slice(0, 3).map((tool) => (
                      <Badge
                        key={tool}
                        variant="secondary"
                        className="truncate text-caption max-w-[150px] truncate"
                      >
                        {tool.replace('_', ' ')}
                      </Badge>
                    ))}
                    {agent.toolsAvailable.length > 3 && (
                      <Badge
                        variant="secondary"
                        className="truncate text-caption max-w-[150px] truncate"
                      >
                        +{agent.toolsAvailable.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Session Management */}
      {currentSession && selectedAgent && (
        <Card className="border-border shadow-card-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="flex flex-wrap items-center justify-between">
              <div className="flex flex-wrap items-center gap-2">
                <Activity className="h-5 w-5 text-primary" />
                <span className="truncate text-heading-4">
                  Active Session: {selectedAgent.name}
                </span>
              </div>
              <Badge variant="default" className="max-w-[150px] truncate">
                Connected
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="space-y-4">
              {/* Session Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-label text-muted-foreground">
                    Session ID:
                  </span>
                  <div className="truncatecode text-caption">
                    {currentSession.id.slice(0, 16)}...
                  </div>
                </div>
                <div>
                  <span className="text-label text-muted-foreground">
                    Started:
                  </span>
                  <div className="text-body-small">
                    {new Date(currentSession.startTime).toLocaleTimeString()}
                  </div>
                </div>
              </div>

              {/* Database Status */}
              {enableDatabaseManagement && (
                <div className="space-y-2">
                  <div className="flex flex-wrap items-center justify-between">
                    <span className="flex flex-wrap items-center gap-1 text-label">
                      <Database className="h-3 w-3 text-muted-foreground" />
                      Database Usage
                    </span>
                    <span className="text-sm-small">
                      {Math.round(selectedAgent.memorySize * 0.7)}KB /{' '}
                      {selectedAgent.memorySize}KB
                    </span>
                  </div>
                  <Progress value={70} className="h-1.5" />
                </div>
              )}

              {/* Quick Actions */}
              <div className="flex flex-wrap gap-2">
                <Button
                  className="max-w-full truncatebutton"
                  variant="outline"
                  size="sm"
                  onClick={() => void loadAgentDatabase(selectedAgent.id)}
                >
                  <Database className="h-3 w-3 mr-1 text-muted-foreground" />
                  Load Database
                </Button>

                {enableA2AProtocol && agents.length > 1 && (
                  <Button
                    className="max-w-full truncatebutton"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const otherAgent = agents.find(
                        (a) =>
                          a.id !== selectedAgent.id && a.status === 'available',
                      );
                      if (otherAgent) {
                        void transferToAgent(
                          otherAgent.id,
                          { currentContext: 'user_request' },
                          'User requested transfer',
                        );
                      }
                    }}
                  >
                    <GitBranch className="h-3 w-3 mr-1 text-muted-foreground" />
                    Transfer Agent
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Agent Transfers */}
      {enableA2AProtocol && transfers.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex flex-wrap items-center gap-2">
              <GitBranch className="h-5 w-5" />
              Agent Transfers
            </CardTitle>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div className="space-y-2">
              {transfers.slice(-3).map((transfer, index) => (
                <div
                  key={index}
                  className="flex flex-wrap items-center justify-between p-2 bg-muted/50 rounded text-sm"
                >
                  <div className="flex flex-wrap items-center gap-2">
                    <GitBranch className="h-3 w-3 text-muted-foreground" />
                    <span className="text-body-small">
                      {agents.find((a) => a.id === transfer.fromAgent)?.name} →{' '}
                      {agents.find((a) => a.id === transfer.toAgent)?.name}
                    </span>
                  </div>
                  <div className="truncate text-caption text-muted-foreground">
                    {new Date(transfer.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Development Info */}
      <Alert className="border-info/20 bg-info/5">
        <Zap className="h-4 w-4 text-info" />
        <AlertDescription>
          <span className="font-semibold text-primary">
            ADK Integration Active:
          </span>
          <span className="text-body-small">
            {' '}
            Leveraging enterprise Agent Development Kit capabilities including
            agent discovery, A2A protocol, memory management, and sophisticated
            tool orchestration.
          </span>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default ADKAgentClient;
