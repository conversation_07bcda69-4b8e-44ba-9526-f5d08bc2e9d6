import React from 'react';
import { Clock } from 'lucide-react';

interface QueryHistoryProps {
  history: string[];
  onSelectQuery: (query: string) => void;
}

export const QueryHistory: React.FC<QueryHistoryProps> = ({
  history,
  onSelectQuery,
}) => {
  return (
    <div className="space-y-1">
      <div className="flex items-center gap-1 mb-2">
        <Clock className="w-3 h-3 text-muted-foreground" />
        <p className="text-xs font-medium text-muted-foreground">
          Recent queries
        </p>
      </div>
      <div className="space-y-1 max-h-24 overflow-y-auto">
        {history.map((query, index) => (
          <button
            key={`${query}-${index}`}
            onClick={() => onSelectQuery(query)}
            className="w-full text-left px-2 py-1 text-xs text-muted-foreground hover:text-foreground hover:bg-accent/50 rounded transition-all duration-200 truncate"
          >
            {query}
          </button>
        ))}
      </div>
    </div>
  );
};
