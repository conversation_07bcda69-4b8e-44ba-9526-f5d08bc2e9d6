import React from 'react';
import { User, <PERSON><PERSON>, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';

interface Message {
  id: string;
  type: 'user' | 'agent' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

interface ChatInterfaceProps {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = React.memo(({
  messages,
  isLoading,
  error,
}) => {
  return (
    <div className="space-y-4">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex gap-3 ${
            message.type === 'user' ? 'justify-end' : 'justify-start'
          }`}
        >
          {message.type !== 'user' && (
            <div className="flex-shrink-0 mt-1">
              {message.type === 'agent' ? (
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Bot className="w-5 h-5 text-primary" />
                </div>
              ) : (
                <div className="w-8 h-8 bg-destructive/10 rounded-lg flex items-center justify-center">
                  <AlertCircle className="w-5 h-5 text-destructive" />
                </div>
              )}
            </div>
          )}

          <div
            className={`max-w-[80%] ${
              message.type === 'user'
                ? 'bg-primary text-primary-foreground'
                : message.type === 'system'
                  ? 'bg-destructive/10 text-destructive-foreground'
                  : 'bg-accent text-accent-foreground'
            } rounded-lg px-4 py-2`}
          >
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
            <p className="text-xs opacity-70 mt-1">
              {format(message.timestamp, 'HH:mm')}
            </p>
          </div>

          {message.type === 'user' && (
            <div className="flex-shrink-0 mt-1">
              <div className="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                <User className="w-5 h-5 text-secondary-foreground" />
              </div>
            </div>
          )}
        </div>
      ))}

      {isLoading && (
        <div className="flex gap-3 justify-start animate-fade-in">
          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
            <Bot className="w-5 h-5 text-primary" />
          </div>
          <div className="bg-accent text-accent-foreground rounded-lg px-4 py-2">
            <div className="flex gap-1">
              <span
                className="w-2 h-2 bg-primary rounded-full animate-bounce"
                style={{ animationDelay: '0ms' }}
              />
              <span
                className="w-2 h-2 bg-primary rounded-full animate-bounce"
                style={{ animationDelay: '150ms' }}
              />
              <span
                className="w-2 h-2 bg-primary rounded-full animate-bounce"
                style={{ animationDelay: '300ms' }}
              />
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="flex justify-center animate-fade-in">
          <div className="bg-destructive/10 text-destructive-foreground rounded-lg px-4 py-2 text-sm">
            {error}
          </div>
        </div>
      )}
    </div>
  );
});

ChatInterface.displayName = 'ChatInterface';
