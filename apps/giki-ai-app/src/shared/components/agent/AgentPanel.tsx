/**
 * Agent Panel Component
 * Matches wireframe: docs/wireframes/04-agent-integration/01-agent-panel.md
 * Desktop: 400px width sidebar, Mobile: full-screen overlay
 */
import React, { useState, useRef, useEffect } from 'react';
import { X, Send, Paperclip } from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { ScrollArea } from '@/shared/components/ui/scroll-area';
import { logger } from '@/shared/utils/errorHandling';
import { unifiedAIService } from '@/shared/services/ai/UnifiedAIService';

interface AgentPanelProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile?: boolean;
}

interface Message {
  id: string;
  type: 'user' | 'agent' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

export const AgentPanel: React.FC<AgentPanelProps> = React.memo(({
  isOpen,
  onClose,
  isMobile = false,
}) => {
  const [query, setQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 'welcome',
      type: 'agent',
      content: 'How can I help you today?',
      timestamp: new Date(),
    },
  ]);
  const [error, setError] = useState<string | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Use unified AI service for agent communication

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!query.trim() || isLoading) return;

    // performanceMonitor.start('agent-query'); // TODO: Fix performance monitor import
    const userMessage = {
      id: `user-${Date.now()}`,
      type: 'user' as const,
      content: query,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setQuery('');
    setIsLoading(true);
    setError(null);

    // Focus back to input after sending
    setTimeout(() => inputRef.current?.focus(), 100);

    try {
      const response = await unifiedAIService.processConversation({
        message: query,
        context: {
          current_page: 'agent_panel',
          user_role: 'user',
          available_actions: ['upload', 'report', 'analyze'],
          current_data: {}
        }
      });

      const agentMessage = {
        id: `agent-${Date.now()}`,
        type: 'agent' as const,
        content:
          response.message ||
          'I apologize, but I encountered an issue processing your request.',
        timestamp: new Date(),
        metadata: response.data,
      };

      setMessages((prev) => [...prev, agentMessage]);
      // Success - scroll to bottom
      setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop =
            chatContainerRef.current.scrollHeight;
        }
      }, 100);
    } catch (err) {
      logger.error('Agent query failed', 'AgentPanel', err as Error);
      setError('Failed to get response. Please try again.');

      const errorMessage = {
        id: `error-${Date.now()}`,
        type: 'system' as const,
        content: 'I apologize, but I encountered an error. Please try again.',
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        if (isOpen) {
          onClose();
        }
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Focus input when panel opens
      setTimeout(() => inputRef.current?.focus(), 100);
    }

    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  if (!isOpen) return null;

  const panelClasses = isMobile
    ? 'fixed inset-0 z-50 bg-background'
    : 'fixed right-0 top-0 z-50 h-full w-[400px] bg-background border-l shadow-xl';

  return (
    <>
      {/* Mobile backdrop */}
      {isMobile && (
        <div className="fixed inset-0 bg-black/50 z-40" onClick={onClose} />
      )}

      {/* Panel */}
      <div className={panelClasses}>
        <div className="h-full flex flex-col">
          {/* Header */}
          <div
            className="flex items-center justify-between p-4 border-b"
            style={{ backgroundColor: 'var(--giki-primary)' }}
          >
            <h3 className="text-sm font-semibold text-white">
              {isMobile && (
                <button
                  onClick={onClose}
                  className="mr-3 p-1 hover:bg-white/20 rounded"
                >
                  ←
                </button>
              )}
              giki Assistant
            </h3>
            {!isMobile && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-white hover:bg-white/20 h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Chat Messages */}
          <ScrollArea className="flex-1" ref={chatContainerRef}>
            <div className="p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[85%] rounded-lg px-3 py-2 ${
                      message.type === 'user'
                        ? 'bg-slate-100 text-slate-900 ml-4'
                        : 'bg-white border border-slate-200 text-slate-900 mr-4 shadow-sm'
                    }`}
                  >
                    <div className="text-sm leading-relaxed">
                      {message.content}
                    </div>
                    {message.type === 'agent' &&
                      message.content.includes('March spending') && (
                        <div className="mt-2 space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span>Operations:</span>
                            <span className="font-mono">$45,230</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Marketing:</span>
                            <span className="font-mono">$12,450</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Travel:</span>
                            <span className="font-mono">$8,900</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-auto p-0 text-xs text-success mt-2 hover:bg-transparent hover:text-green-700"
                          >
                            View Full Report →
                          </Button>
                        </div>
                      )}
                    <div className="text-xs text-muted-foreground mt-1">
                      {formatTime(message.timestamp)}
                    </div>
                  </div>
                </div>
              ))}

              {/* Loading indicator */}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-white border rounded-lg px-3 py-2 mr-4">
                    <div className="flex items-center gap-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: '0.1s' }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: '0.2s' }}
                        ></div>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        Thinking...
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Error display */}
              {error && (
                <div className="flex justify-start">
                  <div className="bg-destructive/10 border border-destructive/20 rounded-lg px-3 py-2 mr-4">
                    <div className="text-sm text-destructive">{error}</div>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Input Area - Enhanced per wireframe */}
          <div className="p-4 border-t bg-slate-50/50">
            <form
              onSubmit={(e) => {
                e.preventDefault();
                void handleSubmit(e);
              }}
              className="space-y-2"
            >
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="Type your message..."
                  disabled={isLoading}
                  className="flex-1 border-slate-300 focus:border-purple-500 focus:ring-purple-500/20"
                  maxLength={1000}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  disabled={isLoading}
                  className="shrink-0 text-slate-500 hover:text-success hover:border-green-300"
                  title="Attach file"
                >
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || !query.trim()}
                  className="shrink-0 bg-giki-primary hover:bg-giki-primary/80 shadow-sm"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>

              {/* Quick action suggestions - Enhanced */}
              <div className="flex flex-wrap gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-6 text-xs text-slate-600 hover:text-success hover:bg-green-50"
                  onClick={() => setQuery("Show me this month's spending")}
                >
                  Monthly spending
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-6 text-xs text-slate-600 hover:text-success hover:bg-green-50"
                  onClick={() => setQuery('Upload transactions')}
                >
                  Upload files
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-6 text-xs text-slate-600 hover:text-success hover:bg-green-50"
                  onClick={() => setQuery('Generate report')}
                >
                  Generate report
                </Button>
              </div>

              {/* Helper text */}
              <div className="text-xs text-slate-500 text-center pt-1">
                Press Enter to send • Cmd+K to toggle panel
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
});

AgentPanel.displayName = 'AgentPanel';

export default AgentPanel;
