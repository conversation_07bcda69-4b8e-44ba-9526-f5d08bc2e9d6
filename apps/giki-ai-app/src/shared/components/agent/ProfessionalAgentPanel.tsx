/**
 * Professional Agent Panel Component
 * Modern agent interface with brand primary styling and enhanced UX
 * Integrates with AppLayout and WebSocket communication
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
// Removed Lucide imports - replaced with geometric icons
// Send → ⟶, Mic → ●, MicOff → ◯, Upload → ↑
// Loader → ⟲, Wifi → ◈, WifiOff → ◌
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Textarea } from '@/shared/components/ui/textarea';
import { useAgentWebSocket } from '@/shared/hooks/useAgentWebSocket';
import type { ChatResponse } from '@/shared/services/agentsApi';
import { AgentErrorBoundary } from '@/shared/components/error/FeatureErrorBoundary';

interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  type?: 'text' | 'file' | 'action' | 'system';
  metadata?: {
    confidence?: number;
    actionType?: string;
    fileUrl?: string;
    fileName?: string;
    processing_time?: number;
    suggestions?: string[];
    actions?: string[];
  };
}

interface AgentStatus {
  status: 'online' | 'busy' | 'typing' | 'offline';
  lastSeen?: Date;
  currentTask?: string;
  realTimeConnected?: boolean;
  capabilities?: string[];
}

interface ProfessionalAgentPanelProps {
  isOpen: boolean;
  onClose: () => void;
  agentName?: string;
  welcomeMessage?: string;
  showQuickActions?: boolean;
  enableFileUpload?: boolean;
  enableVoice?: boolean;
  maxHeight?: string;
  className?: string;
  onMessageSent?: (message: string) => void;
  onFileUploaded?: (file: File) => void;
}

export const ProfessionalAgentPanel: React.FC<ProfessionalAgentPanelProps> = React.memo(({
  isOpen,
  _onClose,
  agentName = 'giki',
  welcomeMessage = "Hello! I'm your AI assistant. I can help you with:\n• Categorizing transactions\n• Analyzing spending patterns\n• Generating reports\n• Answering questions about your data",
  showQuickActions = true,
  enableFileUpload = true,
  enableVoice = false,
  maxHeight = '600px',
  className = '',
  onMessageSent,
  onFileUploaded,
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [_isTyping, _setIsTyping] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [agentStatus, setAgentStatus] = useState<AgentStatus>({
    status: 'offline',
    realTimeConnected: false,
  });
  const [isLoading, setIsLoading] = useState(false);

  // WebSocket integration for real-time updates
  const {
    isConnected: _wsConnected,
    isConnecting: _wsConnecting,
    capabilities: wsCapabilities,
    lastMessage: _wsLastMessage,
    connectionError: _wsError,
  } = useAgentWebSocket({
    autoConnect: isOpen,
    onConnect: () => {
      setAgentStatus((prev) => ({
        ...prev,
        status: 'online',
        realTimeConnected: true,
        lastSeen: new Date(),
      }));
    },
    onDisconnect: () => {
      setAgentStatus((prev) => ({
        ...prev,
        realTimeConnected: false,
        status: prev.status === 'offline' ? 'offline' : 'online',
      }));
    },
    onMessage: (message) => {
      // Handle real-time agent updates
      if (message.type === 'agent.processing_started') {
        setAgentStatus((prev) => ({
          ...prev,
          status: 'busy',
          currentTask: message.data?.operation,
        }));
      } else if (message.type === 'agent.processing_completed') {
        setAgentStatus((prev) => ({
          ...prev,
          status: 'online',
          currentTask: undefined,
        }));
      } else if (message.type === 'agent.processing_error') {
        setAgentStatus((prev) => ({
          ...prev,
          status: 'online',
          currentTask: undefined,
        }));
      }
    },
    onError: (error) => {
      console.error('WebSocket error:', error);
      setAgentStatus((prev) => ({
        ...prev,
        status: 'offline',
        realTimeConnected: false,
      }));
    },
  });

  // Update capabilities when WebSocket provides them
  useEffect(() => {
    if (wsCapabilities?.capabilities) {
      setAgentStatus((prev) => ({
        ...prev,
        capabilities: wsCapabilities.capabilities,
      }));
    }
  }, [wsCapabilities]);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMsg: ChatMessage = {
        id: '1',
        content: welcomeMessage,
        sender: 'agent',
        timestamp: new Date(),
        type: 'text',
      };
      setMessages([welcomeMsg]);
    }
  }, [isOpen, welcomeMessage, messages.length]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when panel opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  const handleSendMessage = useCallback(async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      sender: 'user',
      timestamp: new Date(),
      type: 'text',
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setAgentStatus({ status: 'typing' });

    // Call external handler
    onMessageSent?.(userMessage.content);

    try {
      // Import agentsApi for real backend integration
      const { agentsApi } = await import('@/shared/services/agentsApi');

      // Send message to real ConversationalAgent backend
      const response = await agentsApi.sendNaturalLanguage(
        userMessage.content,
        { timestamp: Date.now() },
      );

      const agentResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: response.message,
        sender: 'agent',
        timestamp: new Date(),
        type: 'text',
        metadata: {
          confidence: 0.95,
          processing_time: response.processing_time_ms,
          suggestions: response.suggestions,
          actions: response.actions,
        },
      };

      setMessages((prev) => [...prev, agentResponse]);
      setIsLoading(false);
      setAgentStatus({ status: 'online' });
    } catch (error) {
      console.error('Failed to send message to agent:', error);

      const errorResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content:
          'I apologize, but I encountered an error processing your request. Please try again.',
        sender: 'agent',
        timestamp: new Date(),
        type: 'text',
        metadata: { confidence: 0.0 },
      };

      setMessages((prev) => [...prev, errorResponse]);
      setIsLoading(false);
      setAgentStatus({ status: 'online' });
    }
  }, [inputValue, isLoading, onMessageSent]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      void handleSendMessage();
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const fileMessage: ChatMessage = {
        id: Date.now().toString(),
        content: `Uploaded file: ${file.name}`,
        sender: 'user',
        timestamp: new Date(),
        type: 'file',
        metadata: { fileName: file.name, fileUrl: URL.createObjectURL(file) },
      };

      setMessages((prev) => [...prev, fileMessage]);
      onFileUploaded?.(file);
    }
  };

  const executeQuickAction = useCallback(
    async (actionType: string) => {
      if (isLoading) return;

      setIsLoading(true);
      setAgentStatus({ status: 'typing' });

      try {
        const { agentsApi } = await import('@/shared/services/agentsApi');

        let response: ChatResponse;
        let actionMessage = '';

        switch (actionType) {
          case 'categorize':
            response = await agentsApi.executeCommand(
              '/categorize',
              {},
              { quick_action: true },
            );
            actionMessage = 'Quick categorization started';
            break;
          case 'accuracy':
            response = await agentsApi.executeCommand(
              '/analyze',
              { analysis_type: 'accuracy' },
              { quick_action: true },
            );
            actionMessage = 'Accuracy analysis requested';
            break;
          case 'upload':
            fileInputRef.current?.click();
            setIsLoading(false);
            setAgentStatus({ status: 'online' });
            return;
          default:
            response = await agentsApi.sendNaturalLanguage(
              'Help me get started',
            );
            actionMessage = 'General help requested';
        }

        // Add user action message
        const userMessage: ChatMessage = {
          id: Date.now().toString(),
          content: actionMessage,
          sender: 'user',
          timestamp: new Date(),
          type: 'action',
        };

        // Add agent response
        const agentResponse: ChatMessage = {
          id: (Date.now() + 1).toString(),
          content: response.message,
          sender: 'agent',
          timestamp: new Date(),
          type: 'text',
          metadata: {
            confidence: 0.95,
            processing_time: response.processing_time_ms,
            suggestions: response.suggestions,
            actions: response.actions,
          },
        };

        setMessages((prev) => [...prev, userMessage, agentResponse]);
        setIsLoading(false);
        setAgentStatus({ status: 'online' });
      } catch (error) {
        console.error('Quick action failed:', error);
        setIsLoading(false);
        setAgentStatus({ status: 'online' });
      }
    },
    [isLoading],
  );

  const quickActions = [
    {
      icon: '↑',
      label: 'Quick Categorize',
      action: () => void executeQuickAction('categorize'),
    },
    {
      icon: '□',
      label: 'Upload File',
      action: () => void executeQuickAction('upload'),
    },
    {
      icon: '⚬',
      label: 'Accuracy Check',
      action: () => void executeQuickAction('accuracy'),
    },
  ];

  if (!isOpen) return null;

  return (
    <AgentErrorBoundary componentName="ProfessionalAgentPanel">
      <Card
      className={`
      ${className.includes('fixed') ? 'fixed right-6 bottom-20 z-40 w-96' : 'w-full h-full'}
      ${className.includes('shadow-none') ? '' : 'shadow-2xl'} 
      ${className.includes('border-none') ? '' : 'border-brand-primary/20'}
      ${className.includes('h-full') ? 'h-full' : `max-h-[${maxHeight}]`}
      transition-all duration-300 ease-out
      ${className}
    `}
    >
      {/* Header - Reduced height with minimal design */}
      <div
        className="p-2 rounded-t-lg"
        style={{
          background: '#295343',
          color: 'white',
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h3 className="font-semibold text-sm" style={{ color: 'white' }}>
              {agentName} Assistant
            </h3>
            <div className="flex items-center gap-1">
              <div
                className={`
                w-2 h-2 rounded-full
                ${
                  agentStatus.status === 'online'
                    ? 'bg-emerald-400'
                    : agentStatus.status === 'typing'
                      ? 'bg-emerald-400 animate-pulse'
                      : agentStatus.status === 'busy'
                        ? 'bg-amber-400'
                        : 'bg-slate-400'
                }
              `}
              />
              {agentStatus.realTimeConnected && (
                <span className="h-3 w-3 text-success text-sm font-bold flex items-center justify-center">◈</span>
              )}
              {!agentStatus.realTimeConnected &&
                agentStatus.status !== 'offline' && (
                  <span className="h-3 w-3 text-warning text-sm font-bold flex items-center justify-center">◌</span>
                )}
            </div>
          </div>
          {agentStatus.currentTask && (
            <div className="text-xs text-success/80">
              {agentStatus.currentTask}
            </div>
          )}
        </div>
      </div>

      <CardContent
        className={`p-0 flex flex-col ${className.includes('h-full') ? 'h-full' : 'h-[500px]'}`}
      >
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-white">
          {messages.map((message) => (
            <div key={message.id} className="space-y-2">
              {/* Message with sender label */}
              <div className="flex items-center gap-2">
                <div
                  className={`
                    w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium
                    ${
                      message.sender === 'user'
                        ? 'bg-brand-primary text-white'
                        : 'bg-ai-dark-blue text-white'
                    }
                  `}
                >
                  {message.sender === 'user' ? (
                    <span className="text-xs font-bold">□</span>
                  ) : (
                    <span className="text-xs font-bold">⚬</span>
                  )}
                </div>
                <span className="text-xs font-medium text-secondary-foreground">
                  {message.sender === 'user' ? 'You' : 'giki'}
                </span>
                <span className="text-xs text-muted-foreground">
                  {message.timestamp.toLocaleTimeString()}
                </span>
              </div>

              {/* Message Content - Full Width */}
              <div className="bg-secondary border border-border rounded-lg px-3 py-2 text-sm w-full">
                <div className="whitespace-pre-wrap">{message.content}</div>
                {message.metadata?.confidence && (
                  <div className="flex items-center gap-1 mt-2 text-xs text-muted-foreground">
                    <span className="text-xs font-bold">⚬</span>
                    {(message.metadata.confidence * 100).toFixed(0)}% confidence
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Agent typing indicator */}
          {isLoading && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-ai-dark-blue flex items-center justify-center">
                  <span className="text-xs font-bold text-white">⚬</span>
                </div>
                <span className="text-xs font-medium text-secondary-foreground">giki</span>
                <span className="text-xs text-muted-foreground">now</span>
              </div>
              <div className="bg-secondary border border-border rounded-lg px-3 py-2 text-sm w-full">
                <div className="flex items-center gap-2">
                  <span className="h-3 w-3 animate-spin text-ai-dark-blue text-sm font-bold flex items-center justify-center">⟲</span>
                  <span className="text-muted-foreground">Thinking...</span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Quick Actions */}
        {showQuickActions && (
          <div className="border-t border-border p-2 bg-white">
            <div className="flex gap-1">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="default"
                  size="sm"
                  onClick={action.action}
                  className="flex-1 text-xs bg-brand-primary text-white hover:bg-[#1d372e] border-0 px-2 py-1 h-7"
                >
                  <span className="text-sm font-bold">{action.icon}</span>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="border-t border-border p-4 bg-white rounded-b-lg">
          <div className="flex items-end gap-2">
            <div className="flex-1">
              <Textarea
                ref={inputRef as React.RefObject<HTMLTextAreaElement>}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="min-h-[40px] max-h-[100px] border-slate-300 focus:border-brand-primary focus:ring-brand-primary/20"
                rows={1}
              />
            </div>

            <div className="flex gap-1">
              {enableFileUpload && (
                <>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv,.xlsx,.xls,.pdf"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    className="border-slate-300 text-secondary-foreground hover:bg-slate-100"
                  >
                    <span className="h-4 w-4 text-lg font-bold flex items-center justify-center">↑</span>
                  </Button>
                </>
              )}

              {enableVoice && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsRecording(!isRecording)}
                  className={`border-slate-300 ${
                    isRecording
                      ? 'bg-red-50 text-error border-red-300'
                      : 'text-secondary-foreground hover:bg-slate-100'
                  }`}
                >
                  {isRecording ? (
                    <span className="h-4 w-4 text-lg font-bold flex items-center justify-center">◯</span>
                  ) : (
                    <span className="h-4 w-4 text-lg font-bold flex items-center justify-center">●</span>
                  )}
                </Button>
              )}

              <Button
                onClick={() => void handleSendMessage()}
                disabled={!inputValue.trim() || isLoading}
                className="bg-brand-primary hover:bg-[#1d372e] text-white"
                size="sm"
              >
                <span className="h-4 w-4 text-lg font-bold flex items-center justify-center">⟶</span>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    </AgentErrorBoundary>
  );
});

ProfessionalAgentPanel.displayName = 'ProfessionalAgentPanel';

export default ProfessionalAgentPanel;
