// apps/giki-ai-app/src/components/agent/AudioInput.tsx
import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/shared/components/ui/button';
import { Mic, Square, Play, Pause, Upload } from 'lucide-react';
import { useToast } from '@/shared/components/ui/use-toast';

interface AudioInputProps {
  onAudioProcessed: (result: unknown) => void;
  disabled?: boolean;
}

export const AudioInput: React.FC<AudioInputProps> = ({
  onAudioProcessed,
  disabled = false,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const { toast } = useToast();

  // Start recording audio
  const startRecording = useCallback(async () => {
    try {
      const stream = await navigator?.mediaDevices?.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000, // Optimized for Gemini 2.0 Flash
        },
      });

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus', // High quality format
      });

      const chunks: BlobPart[] = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event?.data?.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        setAudioBlob(blob);
        setAudioUrl(URL.createObjectURL(blob));
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);

      // Start recording timer
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);

      toast({
        title: 'Recording Started',
        description: 'Speak clearly for best transcription accuracy',
      });
    } catch (error) {
      console.error('Error starting recording:', error);
      toast({
        title: 'Recording Error',
        description: 'Could not access microphone. Please check permissions.',
        variant: 'destructive',
      });
    }
  }, [toast]);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef?.current?.stop();
      setIsRecording(false);

      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
        recordingIntervalRef.current = null;
      }

      toast({
        title: 'Recording Stopped',
        description: 'Audio ready for processing',
      });
    }
  }, [isRecording, toast]);

  // Play/pause recorded audio
  const togglePlayback = useCallback(() => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef?.current?.pause();
        setIsPlaying(false);
      } else {
        void audioRef?.current?.play();
        setIsPlaying(true);
      }
    }
  }, [isPlaying]);

  // Process audio with enhanced agent service
  const processAudio = useCallback(
    async (blob: Blob) => {
      setIsProcessing(true);

      try {
        // Convert blob to base64 for new audio agent API
        const arrayBuffer = await blob.arrayBuffer();
        const base64Audio = btoa(
          new Uint8Array(arrayBuffer).reduce(
            (data, byte) => data + String.fromCharCode(byte),
            '',
          ),
        );

        // Get audio format from blob type
        const audioFormat = blob?.type?.split('/')[1]?.split(';')[0] || 'webm';

        // Use new audio agent API endpoint
        const response = await fetch('/api/v1/audio/process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('access_token')}`,
          },
          body: JSON.stringify({
            audio_data: base64Audio,
            audio_format: audioFormat,
            language: 'auto-detect',
            context: {
              page: window?.location?.pathname,
              user_intent: 'financial_query',
              timestamp: new Date().toISOString(),
            },
          }),
        });

        if (!response.ok) {
          throw new Error(`Audio processing failed: ${response.statusText}`);
        }

        const result = (await response.json()) as {
          transcription?: string;
          agent_response?: string;
          confidence?: number;
          detected_language?: string;
          actions?: unknown;
          processing_time_ms?: number;
        };

        // Pass structured result to parent component
        onAudioProcessed({
          success: true,
          result: {
            transcription: result.transcription,
            agent_response: result.agent_response,
            confidence: result.confidence,
            detected_language: result.detected_language,
            actions: result.actions,
            processing_time_ms: result.processing_time_ms,
          },
        });

        toast({
          title: 'Audio Processed Successfully',
          description: `"${(result?.transcription || '').substring(0, 50)}${(result?.transcription || '').length > 50 ? '...' : ''}"`,
        });
      } catch (error) {
        console.error('Error processing audio:', error);
        toast({
          title: 'Processing Error',
          description: 'Failed to process audio with enhanced agent service',
          variant: 'destructive',
        });

        onAudioProcessed({
          success: false,
          message: (error as Error).message || 'Audio processing failed',
        });
      } finally {
        setIsProcessing(false);
      }
    },
    [onAudioProcessed, toast],
  );

  // Handle file upload
  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event?.target?.files?.[0];
      if (file) {
        // Validate file type (supported by Gemini 2.0 Flash)
        const supportedTypes = [
          'audio/wav',
          'audio/mp3',
          'audio/aiff',
          'audio/aac',
          'audio/ogg',
          'audio/flac',
        ];

        if (!supportedTypes.includes(file.type)) {
          toast({
            title: 'Unsupported Format',
            description:
              'Please upload WAV, MP3, AIFF, AAC, OGG, or FLAC files',
            variant: 'destructive',
          });
          return;
        }

        // Check file size (max 20MB for inline processing)
        if (file.size > 20 * 1024 * 1024) {
          toast({
            title: 'File Too Large',
            description: 'Please upload files smaller than 20MB',
            variant: 'destructive',
          });
          return;
        }

        setAudioBlob(file);
        setAudioUrl(URL.createObjectURL(file));

        toast({
          title: 'Audio File Loaded',
          description: `Ready to process ${file.name}`,
        });
      }
    },
    [toast],
  );

  // Format recording time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="w-full">
      {/* Modern Recording Controls */}
      <div className="flex flex-wrap items-center gap-2">
        {!isRecording ? (
          <Button
            className="max-w-full truncate text-caption px-3 py-1.5 h-8 flex flex-wrap items-center gap-1.5 border-border text-muted-foreground font-medium hover:border-primary hover:bg-primary/10 hover:text-primary rounded-lg transition-all duration-200"
            onClick={() => void startRecording()}
            disabled={disabled || isProcessing}
            variant="outline"
          >
            <Mic className="h-3.5 w-3.5" />
            <span className="font-medium">Record</span>
          </Button>
        ) : (
          <Button
            className="max-w-full truncate text-caption px-3 py-1.5 h-8 flex flex-wrap items-center gap-1.5 rounded-lg shadow-sm transition-all duration-200 font-medium"
            onClick={() => void stopRecording()}
            disabled={disabled}
            variant="secondary"
          >
            <Square className="h-3.5 w-3.5" />
            <span className="font-medium">
              Stop ({formatTime(recordingTime)})
            </span>
          </Button>
        )}

        <Button
          className="max-w-full truncate text-caption px-3 py-1.5 h-8 flex flex-wrap items-center gap-1.5 border-border text-muted-foreground font-medium hover:border-primary hover:bg-primary/10 hover:text-primary rounded-lg transition-all duration-200"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled || isProcessing}
          variant="outline"
        >
          <Upload className="h-3.5 w-3.5" />
          <span className="font-medium">Upload</span>
        </Button>
      </div>

      {/* Modern Audio Playback */}
      {audioUrl && (
        <div className="flex flex-wrap items-center gap-2 mt-2 p-2 bg-muted/40 rounded-lg border border-border">
          <Button
            className="max-w-full px-3 py-1.5 h-8 flex flex-wrap items-center gap-1.5 border-border text-muted-foreground font-medium hover:border-primary hover:bg-primary/10 hover:text-primary rounded-lg transition-all duration-200"
            onClick={() => void togglePlayback()}
            disabled={disabled}
            variant="outline"
            size="sm"
          >
            {isPlaying ? (
              <Pause className="h-3.5 w-3.5" />
            ) : (
              <Play className="h-3.5 w-3.5" />
            )}
            <span className="font-medium">{isPlaying ? 'Pause' : 'Play'}</span>
          </Button>
          <Button
            className="max-w-full px-3 py-1.5 h-8 truncate text-caption bg-primary hover:bg-primary/90 text-primary-foreground font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50"
            onClick={() => void (audioBlob && processAudio(audioBlob))}
            disabled={disabled || isProcessing || !audioBlob}
            size="sm"
          >
            {isProcessing ? (
              <div className="flex flex-wrap items-center gap-1.5">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                <span className="font-medium">Processing</span>
              </div>
            ) : (
              <span className="font-medium">Process</span>
            )}
          </Button>
        </div>
      )}

      {/* Hidden audio element for playback */}
      {audioUrl && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onEnded={() => setIsPlaying(false)}
          className="hidden"
        />
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="audio/wav,audio/mp3,audio/aiff,audio/aac,audio/ogg,audio/flac"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Status indicator - Compact */}
      {isRecording && (
        <div className="flex flex-wrap items-center gap-1.5 truncate text-caption text-destructive font-medium mt-1">
          <div className="animate-pulse rounded-full h-2 w-2 bg-destructive"></div>
          Recording...
        </div>
      )}
    </div>
  );
};
