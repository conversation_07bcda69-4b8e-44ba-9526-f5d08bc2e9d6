import React from 'react';
import { X, Sparkles } from 'lucide-react';

interface PanelHeaderProps {
  onToggleCollapse: () => void;
}

export const PanelHeader: React.FC<PanelHeaderProps> = ({
  onToggleCollapse,
}) => {
  return (
    <div className="px-4 py-3 border-b border-border/50 flex items-center justify-between bg-card/50 backdrop-blur-sm">
      <div className="flex items-center gap-2">
        <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
          <Sparkles className="w-5 h-5 text-primary" />
        </div>
        <div>
          <h2 className="text-sm font-semibold text-foreground">
            AI Assistant
          </h2>
          <p className="text-xs text-muted-foreground">Powered by Gemini 2.0</p>
        </div>
      </div>
      <button
        onClick={onToggleCollapse}
        className="p-1.5 hover:bg-accent rounded-md transition-colors duration-200"
        aria-label="Close AI Assistant panel"
      >
        <X className="w-4 h-4 text-muted-foreground" />
      </button>
    </div>
  );
};
