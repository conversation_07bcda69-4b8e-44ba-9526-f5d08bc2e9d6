/**
 * Performance Monitor Component
 *
 * Displays real-time API performance metrics in development mode
 */
import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { Activity, AlertCircle, CheckCircle } from 'lucide-react';

interface PerformanceMetrics {
  avgResponseTime: number;
  lastResponseTime: number;
  totalRequests: number;
  failedRequests: number;
  successRate: number;
}

export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    avgResponseTime: 0,
    lastResponseTime: 0,
    totalRequests: 0,
    failedRequests: 0,
    successRate: 100,
  });

  useEffect(() => {
    // Listen for performance events from API client
    const handlePerformanceUpdate = (
      event: CustomEvent<PerformanceMetrics>,
    ) => {
      setMetrics(event.detail);
    };

    window.addEventListener(
      'api-performance' as keyof WindowEventMap,
      handlePerformanceUpdate as EventListener,
    );

    return () => {
      window.removeEventListener(
        'api-performance' as keyof WindowEventMap,
        handlePerformanceUpdate as EventListener,
      );
    };
  }, []);

  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  const getPerformanceColor = (time: number) => {
    if (time < 200) return 'text-green-700 font-semibold';
    if (time < 500) return 'text-yellow-700 font-semibold';
    return 'text-red-700 font-semibold';
  };

  const getPerformanceBadge = (time: number) => {
    if (time < 200)
      return (
        <Badge
          variant="default"
          className="bg-success/10 truncategreen-900 border-green-300 font-semibold max-w-[150px] truncate"
        >
          Fast
        </Badge>
      );
    if (time < 500)
      return (
        <Badge
          variant="secondary"
          className="bg-warning/10 truncateyellow-900 border-yellow-300 font-semibold max-w-[150px] truncate"
        >
          Moderate
        </Badge>
      );
    return (
      <Badge variant="destructive" className="max-w-[150px] truncate">
        Slow
      </Badge>
    );
  };

  return (
    <div className="fixed bottom-4 right-4 z-modal">
      <Card className="w-80 shadow-lg">
        <CardContent className="p-4 overflow-hidden">
          <div className="flex flex-wrap items-center justify-between mb-3">
            <div className="flex flex-wrap items-center gap-2">
              <Activity className="h-4 w-4 truncate[hsl(var(--giki-text-primary))]" />
              <span className="text-status-badge text-[hsl(var(--giki-text-primary))]">
                Performance Monitor
              </span>
            </div>
            {getPerformanceBadge(metrics.avgResponseTime)}
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex flex-wrap justify-between">
              <span className="truncateslate-700 font-medium">
                Avg Response Time:
              </span>
              <span
                className={`font-mono ${getPerformanceColor(metrics.avgResponseTime)}`}
              >
                {metrics?.avgResponseTime?.toFixed(0)}ms
              </span>
            </div>

            <div className="flex flex-wrap justify-between">
              <span className="truncateslate-700 font-medium">
                Last Response:
              </span>
              <span
                className={`font-mono ${getPerformanceColor(metrics.lastResponseTime)}`}
              >
                {metrics?.lastResponseTime?.toFixed(0)}ms
              </span>
            </div>

            <div className="flex flex-wrap justify-between">
              <span className="truncateslate-700 font-medium">
                Success Rate:
              </span>
              <div className="flex flex-wrap items-center gap-1">
                {metrics.successRate >= 95 ? (
                  <CheckCircle className="h-3 w-3 text-success" />
                ) : (
                  <AlertCircle className="h-3 w-3 text-warning" />
                )}
                <span className="text-sm">
                  {metrics?.successRate?.toFixed(1)}%
                </span>
              </div>
            </div>

            <div className="flex flex-wrap justify-between truncate text-caption text-slate-700 font-medium">
              <span>Total: {metrics.totalRequests}</span>
              <span>Failed: {metrics.failedRequests}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceMonitor;
