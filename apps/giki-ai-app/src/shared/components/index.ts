/**
 * Shared Components Barrel Exports
 *
 * This file provides clean imports for all shared components
 * across the application.
 */

// UI Components (shadcn/ui and custom)
export * from './ui/button';
export * from './ui/input';
export * from './ui/card';
export * from './ui/dialog';
export * from './ui/form';
export * from './ui/table';
export * from './ui/toast';
export * from './ui/loading';
export * from './ui/error-display';
export * from './ui/unified-error';

// Layout Components (LEGACY - use EnhancedAppLayout instead)
export * from './layout/AuthLayout';
export * from './layout/MainContent';

// Chat Interface Components
export * from './agent/ChatInterface';
export * from './agent/ChatInterfaceToggle';
export * from './agent/AudioInput';

// Common Components
export { ThemeProvider } from '../../core/providers/ThemeProvider';
export { default as ThemeToggle } from './ui/ThemeToggle';
export { default as ErrorBoundary } from './error/ErrorBoundary';

// Datagrid Components
export { default as FormulaBar } from './datagrid/FormulaBar';

// Performance Components
export { default as PerformanceMonitor } from './performance/PerformanceMonitor';

// Toast Components (re-export for convenience)
export { ToastProvider, ToastViewport } from './ui/toast';

// Tooltip Components
export { TooltipProvider } from './ui/tooltip';
