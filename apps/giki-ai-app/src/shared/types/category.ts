// apps/giki-ai-app/src/types/category.ts
import { Category } from './categorization';

/**
 * Extends the base Category interface with additional properties for hierarchical display
 */
export interface HierarchicalCategory extends Category {
  children?: HierarchicalCategory[];
  has_children?: boolean;
  level?: number;
  assignable?: boolean;
  isExpanded?: boolean;
  isEditing?: boolean;
  parent?: HierarchicalCategory | null;

  // GL Code UI state
  isEditingGLCode?: boolean; // UI state for GL code editing
  glValidationErrors?: string[]; // GL code validation errors
  glSuggestions?: GLCodeSuggestion[]; // AI-powered GL suggestions
}

/**
 * AI-powered GL code suggestion
 */
export interface GLCodeSuggestion {
  gl_code: string;
  gl_account_name: string;
  gl_account_type: string;
  confidence: number;
  reasoning: string;
}

/**
 * GL code validation result
 */
export interface GLCodeValidation {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

/**
 * GL code analytics data
 */
export interface GLCodeAnalytics {
  overview: {
    total_categories: number;
    categories_with_gl_codes: number;
    coverage_percentage: number;
    missing_gl_codes: number;
  };
  account_type_distribution: Record<string, number>;
  missing_gl_categories: Array<{
    id: number;
    name: string;
    path: string;
    level: number;
  }>;
  code_range_analysis: Array<{
    range: string;
    used_codes: number;
    next_available: string;
  }>;
  recommendations: string[];
}
