// apps/giki-ai-app/src/types/api.ts

// For ChatPanel.tsx
export interface ChatMessage {
  // Renamed from ChatMessageContent
  id: string;
  text: string;
  sender: 'user' | 'agent' | 'system';
  timestamp: string; // ISO string format
  contentType?: 'text' | 'chart' | 'table' | 'error' | 'audio_response';
  contentData?: ChatMessageContentData | null; // Data for chart or table, or error details
  conversation_id?: string;
}

export type ChartData = {
  type: string; // e.g., 'bar', 'line'
  data: Array<Record<string, string | number>>;
  xKey: string;
  yKey: string;
};

export type TableData = {
  headers: string[];
  rows: Array<Array<string | number>>;
};

export type AudioData = {
  original_audio?: {
    url?: string;
    duration?: number;
    format?: string;
    transcription?: string;
    intent?: string;
    parameters?: Record<string, unknown>;
    confidence?: number;
    language?: string;
    processing_time?: number;
  };
  transcription?: string;
  audio_url?: string;
};

export type ChatMessageContentData = ChartData | TableData | AudioData;

export interface AgentResponse {
  conversation_id: string;
  message: ChatMessage; // Updated to use ChatMessage
  metadata?: {
    tokens_used?: number;
    confidence?: number;
    response_time?: number;
    cached?: boolean;
  };
  // Any other fields the agent might return at the top level
}

// For ReportsPage.tsx
export interface SpendingByCategoryItem {
  name: string; // Category name
  spending: number;
}

export interface SpendingByEntityItem {
  entity: string; // Entity/Vendor name
  spending: number;
}

export interface IncomeVsExpenseData {
  total_income: number;
  total_expenses: number;
  net_income_loss: number;
  // For chart:
  incomeByPeriod: { period: string; amount: number }[];
  expensesByPeriod: { period: string; amount: number }[];
}

// Transaction type is already in @/shared/types/categorization.ts,
// but if report transactions have a different structure, define it here.
// For now, assume existing Transaction type is sufficient.

export interface ReportExportParams {
  type: 'csv' | 'pdf';
  reportName: string; // e.g., 'transaction_list', 'spending_by_category'
  filters?: Record<string, unknown>; // Optional filters for the report
}

// General API error structure if needed
export interface ApiError {
  detail: string | { msg: string; type: string }[];
}

// API response wrapper type
export interface ApiResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}
