/**
 * Custom error types for consistent error management across the application
 */

// Base error type
export enum ErrorType {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NETWORK = 'network',
  NOT_FOUND = 'not_found',
  SERVER = 'server',
  UNKNOWN = 'unknown',
}

// Base error interface
export interface AppError {
  type: ErrorType;
  message: string;
  statusCode?: number;
  field?: string; // Kept for ValidationError and other specific AppError subtypes
  details?: Record<string, string[]>; // Aligned with ApiError from lib/utils/errorHandling.ts
  originalError?: unknown; // Added from ApiError
}

// Validation error
export interface ValidationError extends AppError {
  type: ErrorType.VALIDATION;
  field: string;
}

// Authentication error
export interface AuthenticationError extends AppError {
  type: ErrorType.AUTHENTICATION;
}

// Password validation rules
export const PASSWORD_RULES = {
  MIN_LENGTH: 8,
  REQUIRE_UPPERCASE: true,
  REQUIRE_LOWERCASE: true,
  REQUIRE_NUMBER: true,
  REQUIRE_SPECIAL_CHAR: true,
};

// Password validation error messages
export const PASSWORD_ERROR_MESSAGES = {
  MIN_LENGTH: `Password must be at least ${PASSWORD_RULES.MIN_LENGTH} characters long`,
  REQUIRE_UPPERCASE: 'Password must contain at least one uppercase letter',
  REQUIRE_LOWERCASE: 'Password must contain at least one lowercase letter',
  REQUIRE_NUMBER: 'Password must contain at least one number',
  REQUIRE_SPECIAL_CHAR: 'Password must contain at least one special character',
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
};

// Helper function to validate password
export function validatePassword(password: string): ValidationError | null {
  if (!password || password.length < PASSWORD_RULES.MIN_LENGTH) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.MIN_LENGTH,
      field: 'password',
    };
  }

  if (PASSWORD_RULES.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.REQUIRE_UPPERCASE,
      field: 'password',
    };
  }

  if (PASSWORD_RULES.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.REQUIRE_LOWERCASE,
      field: 'password',
    };
  }

  if (PASSWORD_RULES.REQUIRE_NUMBER && !/\d/.test(password)) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.REQUIRE_NUMBER,
      field: 'password',
    };
  }

  if (
    PASSWORD_RULES.REQUIRE_SPECIAL_CHAR &&
    !/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)
  ) {
    return {
      type: ErrorType.VALIDATION,
      message: PASSWORD_ERROR_MESSAGES.REQUIRE_SPECIAL_CHAR,
      field: 'password',
    };
  }

  return null;
}

// Helper function to create API errors
export function createApiError(error: Partial<AppError>): AppError {
  return {
    type: error.type || ErrorType.UNKNOWN,
    message:
      error.message || 'An error occurred while communicating with the server',
    statusCode: error.statusCode,
    details: error.details,
    originalError: error.originalError,
    field: error.field,
  };
}

// Helper function to create validation errors
export function createValidationError(
  field: string,
  message: string,
): ValidationError {
  return {
    type: ErrorType.VALIDATION,
    field,
    message,
  };
}

// Helper function to create authentication errors
export function createAuthenticationError(
  message: string,
  statusCode?: number,
): AuthenticationError {
  return {
    type: ErrorType.AUTHENTICATION,
    message,
    statusCode,
  };
}
// Type guard for AppError
export function isAppError(error: unknown): error is AppError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'type' in error &&
    'message' in error &&
    Object.values(ErrorType).includes((error as AppError).type)
  );
}

// User-friendly error messages by error type
export const USER_FRIENDLY_ERROR_MESSAGES: Record<ErrorType, string> = {
  [ErrorType.VALIDATION]: 'Please check your input and try again.',
  [ErrorType.AUTHENTICATION]: 'Please log in to continue.',
  [ErrorType.AUTHORIZATION]:
    "You don't have permission to perform this action.",
  [ErrorType.NETWORK]:
    'Connection error. Please check your internet and try again.',
  [ErrorType.NOT_FOUND]: 'The requested resource was not found.',
  [ErrorType.SERVER]:
    'Something went wrong on our end. Please try again later.',
  [ErrorType.UNKNOWN]: 'An unexpected error occurred. Please try again.',
};

// Get user-friendly error message
export function getUserFriendlyErrorMessage(error: unknown): string {
  if (isAppError(error)) {
    // If the error already has a specific message, use it
    if (error.message && !error.message.includes('Error:')) {
      return error.message;
    }
    // Otherwise, use the generic message for the error type
    return USER_FRIENDLY_ERROR_MESSAGES[error.type];
  }

  if (error instanceof Error) {
    // Check for common error patterns
    if (error.message.includes('Network') || error.message.includes('fetch')) {
      return USER_FRIENDLY_ERROR_MESSAGES[ErrorType.NETWORK];
    }
    if (
      error.message.includes('401') ||
      error.message.includes('unauthorized')
    ) {
      return USER_FRIENDLY_ERROR_MESSAGES[ErrorType.AUTHENTICATION];
    }
    if (error.message.includes('403') || error.message.includes('forbidden')) {
      return USER_FRIENDLY_ERROR_MESSAGES[ErrorType.AUTHORIZATION];
    }
    if (error.message.includes('404') || error.message.includes('not found')) {
      return USER_FRIENDLY_ERROR_MESSAGES[ErrorType.NOT_FOUND];
    }
    if (error.message.includes('500') || error.message.includes('server')) {
      return USER_FRIENDLY_ERROR_MESSAGES[ErrorType.SERVER];
    }
  }

  return USER_FRIENDLY_ERROR_MESSAGES[ErrorType.UNKNOWN];
}

// Common error scenarios with specific messages
export const ERROR_SCENARIOS = {
  FILE_UPLOAD: {
    SIZE_LIMIT: 'File is too large. Please use a file smaller than 10MB.',
    INVALID_FORMAT: 'Invalid file format. Please upload a CSV or Excel file.',
    PROCESSING_FAILED:
      'Failed to process file. Please check the format and try again.',
    TIMEOUT: 'File upload is taking too long. Please try with a smaller file.',
  },
  AUTHENTICATION: {
    EXPIRED_SESSION: 'Your session has expired. Please log in again.',
    INVALID_CREDENTIALS: 'Invalid email or password. Please try again.',
    ACCOUNT_LOCKED: 'Account temporarily locked. Please try again later.',
    EMAIL_NOT_VERIFIED: 'Please verify your email address to continue.',
  },
  CATEGORIZATION: {
    AI_UNAVAILABLE:
      'AI categorization is temporarily unavailable. Please try again later.',
    INSUFFICIENT_DATA:
      'Not enough data to categorize. Please provide more transactions.',
    ACCURACY_LOW:
      'Categorization confidence is low. Please review the results.',
  },
  DATA_LOADING: {
    SLOW_CONNECTION:
      'Loading is taking longer than usual due to slow connection.',
    LARGE_DATASET: 'Processing large dataset. This may take a few moments.',
    PARTIAL_LOAD:
      'Some data could not be loaded. Showing available information.',
  },
};
