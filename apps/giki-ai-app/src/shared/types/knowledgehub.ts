import type { SpendingByEntityItem } from './api';

// Entity type with detailed information
export interface Entity extends SpendingByEntityItem {
  id: string; // Add an id for keying if not present in SpendingByEntityItem
  category?: string;
  lastTransactionDate?: string; // Renamed for clarity
  website?: string;
  description?: string;
  logo?: string;
  email?: string;
  phone?: string;
  address?: string;
  transactionCount?: number;
  spendingTrend?: 'up' | 'down' | 'stable';
  spendingHistory?: { month: string; amount: number }[];
  tags?: string[];
  relatedEntities?: { id: string; name: string; relationship: string }[]; // Added id for related entities
  // Add any other fields that might come from the backend or are derived
}

// You might also want a type for the filters or sorting state if it becomes complex
export interface EntityFilters {
  searchTerm: string;
  category: string;
  // Add other filter criteria as needed
}

export interface EntitySortState {
  sortBy:
    | 'name'
    | 'spending'
    | 'lastTransactionDate'
    | 'transactionCount'
    | 'category'; // Added 'category'
  sortOrder: 'asc' | 'desc';
}
