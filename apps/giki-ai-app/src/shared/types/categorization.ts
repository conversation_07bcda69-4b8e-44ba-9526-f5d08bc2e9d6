// apps/giki-ai-app/src/types/categorization.ts

/**
 * Represents a financial transaction.
 */
export interface Transaction {
  id: string;
  date: string; // ISO 8601 date string e.g., "2025-07-04"
  description: string;
  amount: number;
  currency?: string; // e.g., "USD", "INR"

  // Category assignment
  category_id?: number | null;
  category_path?: string | null; // e.g., "Expenses - Software - Subscriptions"

  // AI suggestion
  ai_suggested_category_id?: number | null;
  ai_suggested_category_path?: string | null;
  ai_category_confidence?: number | null;

  // Status flags
  is_user_modified: boolean; // True if user manually set the category
  is_categorized: boolean; // True if either AI or user has categorized
  status: CategorizationStatus; // Added to hold the derived categorization status
  confidence_level?: 'high' | 'medium' | 'low'; // Derived confidence level for UI

  // Optional fields
  account?: string;
  account_id?: string;
  merchant?: string;
  notes?: string;
  transaction_type?: string;
  tags?: string[];

  // Additional fields used in tests and API responses
  created_at?: string;
  updated_at?: string;
  upload_id?: string;
  raw_text?: string;
  suggested_category_id?: number | null;
  suggested_category_path?: string | null;
  ai_confidence_score?: number | null;
  is_recurring?: boolean;
  linked_transaction_ids?: string[];
  sortOrder?: 'asc' | 'desc';
}

/**
 * Represents a category in a hierarchical structure.
 */
export interface Category {
  id: number;
  name: string;
  path: string; // Full path, e.g., "Expenses - Software - Subscriptions"
  parent_id?: number | null;
  children?: Category[]; // For nested categories

  // GL Code integration for accounting software
  gl_code?: string; // GL account code (e.g., "6001", "1200.1")
  gl_account_name?: string; // Human-readable account name
  gl_account_type?: string; // Account type (Asset, Liability, Revenue, Expense, Equity)

  // Learning metadata
  learned_from_onboarding?: boolean; // Category learned from tenant data
  confidence_score?: number; // AI confidence (0.0-1.0)
  frequency_score?: number; // Usage frequency score

  // Additional properties
  description?: string;
  active?: boolean;
  transaction_count?: number;
  level?: number;
  isExpanded?: boolean;
  isEditing?: boolean;
  parent?: Category | null;
  has_children?: boolean;
  assignable?: boolean;
}

/**
 * Upload response from the API
 */
export interface UploadResponse {
  upload_id: string;
  filename: string;
  content_type?: string;
  size: number;
  status: string;
  message?: string;
  headers?: string[];
  transaction_count?: number;
  column_mapping?: Record<string, unknown>;
  report_id?: string;
}

/**
 * Column mapping response after processing a file
 */
export interface ProcessedFileResponse {
  message: string;
  records_processed: number;
  errors: string[];
  categorization_job_id?: string;
  transactions_created?: number;
}

/**
 * Status of a background categorization job
 */
export interface JobStatus {
  status: string;
  progress: number;
  total_transactions: number;
  processed_transactions: number;
  error?: string;
  upload_id?: string;
  start_time?: string;
  end_time?: string;
}

/**
 * Represents the status of a transaction's categorization.
 */
export enum CategorizationStatus {
  AI_SUGGESTED = 'AI Suggested',
  USER_MODIFIED = 'User Modified',
  UNCATEGORIZED = 'Uncategorized',
}

/**
 * Represents a paginated API response.
 */
export interface PaginatedResponse<T> {
  items: T[];
  total_count: number;
  page: number;
  page_size: number;
  total_pages: number;
}
