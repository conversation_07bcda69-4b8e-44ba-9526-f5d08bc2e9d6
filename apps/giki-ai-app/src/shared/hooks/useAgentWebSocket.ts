/**
 * WebSocket Hook for Real-time Agent Communication
 *
 * Provides WebSocket connection to the ConversationalAgent backend
 * for real-time updates and bidirectional communication.
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { getAccessToken } from '@/features/auth/services/auth';
import useAuthStore from '@/shared/services/auth/authStore';

export interface WebSocketMessage {
  type: string;
  timestamp?: number;
  data?: Record<string, unknown>;
}

export interface AgentWebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  lastMessage: WebSocketMessage | null;
  connectionError: string | null;
}

export interface AgentCapabilities {
  agent: string;
  capabilities: string[];
  status: string;
}

export interface UseAgentWebSocketOptions {
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onMessage?: (message: WebSocketMessage) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: string) => void;
}

export const useAgentWebSocket = (options: UseAgentWebSocketOptions = {}) => {
  const {
    autoConnect = true,
    reconnectInterval = 5000,
    maxReconnectAttempts = 5,
    onMessage,
    onConnect,
    onDisconnect,
    onError,
  } = options;

  const [state, setState] = useState<AgentWebSocketState>({
    isConnected: false,
    isConnecting: false,
    lastMessage: null,
    connectionError: null,
  });

  const [capabilities, setCapabilities] = useState<AgentCapabilities | null>(
    null,
  );
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const getWebSocketUrl = useCallback(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    const port =
      window.location.hostname === 'localhost' ? '8000' : window.location.port;
    const token = getAccessToken();

    if (!token) {
      throw new Error('No access token available for WebSocket connection');
    }

    return `${protocol}//${host}:${port}/ws/agent?token=${encodeURIComponent(token)}`;
  }, []);

  const connect = useCallback(async () => {
    if (state.isConnecting || state.isConnected) {
      return;
    }

    setState((prev) => ({
      ...prev,
      isConnecting: true,
      connectionError: null,
    }));

    try {
      // Check token validity and refresh if needed before connecting
      const token = getAccessToken();
      if (!token) {
        throw new Error('No access token available');
      }

      // Parse token to check expiration
      try {
        // Define JWT payload type
        interface JWTPayload {
          exp: number;
        }
        const payload = JSON.parse(atob(token.split('.')[1])) as JWTPayload;
        const now = Math.floor(Date.now() / 1000);

        // If token expires in less than 5 minutes, refresh it
        if (payload.exp - now < 300) {
         ('WebSocket: Token expiring soon, refreshing...');
          const authStore = useAuthStore.getState();
          await authStore.refreshTokens();
        }
      } catch (error) {
        console.error('Failed to parse token for expiration check:', error);
      }

      const url = getWebSocketUrl();
      const ws = new WebSocket(url);

      ws.onopen = () => {
        setState((prev) => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          connectionError: null,
        }));
        reconnectAttemptsRef.current = 0;
        onConnect?.();

        // Request agent capabilities on connection
        ws.send(
          JSON.stringify({
            type: 'agent.capability_request',
            timestamp: Date.now(),
          }),
        );
      };

      ws.onmessage = (event: MessageEvent) => {
        try {
          const message = JSON.parse(event.data) as WebSocketMessage;

          setState((prev) => ({ ...prev, lastMessage: message }));
          onMessage?.(message);

          // Handle specific message types
          if (message.type === 'agent.capabilities' && message.data) {
            // Validate the data structure matches AgentCapabilities
            const capabilitiesData = message.data as AgentCapabilities;
            setCapabilities(capabilitiesData);
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.onclose = async (event: CloseEvent) => {
        setState((prev) => ({
          ...prev,
          isConnected: false,
          isConnecting: false,
          connectionError:
            event.code !== 1000 ? `Connection closed: ${event.reason}` : null,
        }));
        wsRef.current = null;
        onDisconnect?.();

        // Check if close was due to authentication error
        if (event.code === 1008 && event.reason?.includes('authentication')) {
         (
            'WebSocket closed due to authentication error, refreshing token...',
          );
          try {
            const authStore = useAuthStore.getState();
            await authStore.refreshTokens();
            // After successful refresh, attempt immediate reconnection
            reconnectTimeoutRef.current = setTimeout(() => {
              void connect();
            }, 1000);
            return;
          } catch (error) {
            console.error(
              'Failed to refresh token after WebSocket auth error:',
              error,
            );
            // Token refresh failed, user needs to re-login
            return;
          }
        }

        // Attempt reconnection if not a normal close
        if (
          event.code !== 1000 &&
          reconnectAttemptsRef.current < maxReconnectAttempts
        ) {
          reconnectAttemptsRef.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            void connect();
          }, reconnectInterval);
        }
      };

      ws.onerror = (_error: Event) => {
        const errorMessage = 'WebSocket connection error';
        setState((prev) => ({
          ...prev,
          connectionError: errorMessage,
          isConnecting: false,
        }));
        onError?.(errorMessage);
      };

      wsRef.current = ws;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to create WebSocket connection';
      setState((prev) => ({
        ...prev,
        connectionError: errorMessage,
        isConnecting: false,
      }));
      onError?.(errorMessage);
    }
  }, [
    state.isConnecting,
    state.isConnected,
    getWebSocketUrl,
    onConnect,
    onMessage,
    onDisconnect,
    onError,
    maxReconnectAttempts,
    reconnectInterval,
  ]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setState((prev) => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
      connectionError: null,
    }));
  }, []);

  const sendMessage = useCallback(
    (message: Omit<WebSocketMessage, 'timestamp'>) => {
      if (wsRef.current && state.isConnected) {
        const messageWithTimestamp = {
          ...message,
          timestamp: Date.now(),
        };
        wsRef.current.send(JSON.stringify(messageWithTimestamp));
        return true;
      }
      return false;
    },
    [state.isConnected],
  );

  const ping = useCallback(() => {
    return sendMessage({ type: 'ping' });
  }, [sendMessage]);

  const requestStateSync = useCallback(() => {
    return sendMessage({ type: 'ui.state_sync' });
  }, [sendMessage]);

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (autoConnect) {
      void connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect]); // Only run on mount/unmount

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...state,
    capabilities,
    connect,
    disconnect,
    sendMessage,
    ping,
    requestStateSync,
  };
};

export default useAgentWebSocket;
