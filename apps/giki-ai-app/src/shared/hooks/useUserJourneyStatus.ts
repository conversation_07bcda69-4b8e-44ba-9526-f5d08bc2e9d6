/**
 * User Journey Status Hook - Intelligent routing based on user data
 * Determines where users should go based on their categorization progress
 */
import { useState, useEffect } from 'react';
import { getDashboardData } from '@/features/dashboard/services/dashboardService';

export interface UserJourneyStatus {
  loading: boolean;
  error: boolean;
  shouldGoToDashboard: boolean;
  shouldGoToUpload: boolean;
  hasTransactions: boolean;
  hasCategorizedTransactions: boolean;
  categorizedPercentage: number;
  totalTransactions: number;
}

export const useUserJourneyStatus = (): UserJourneyStatus => {
  const [status, setStatus] = useState<UserJourneyStatus>({
    loading: true,
    error: false,
    shouldGoToDashboard: false,
    shouldGoToUpload: true,
    hasTransactions: false,
    hasCategorizedTransactions: false,
    categorizedPercentage: 0,
    totalTransactions: 0,
  });

  useEffect(() => {
    const checkUserStatus = async () => {
      try {
        setStatus(prev => ({ ...prev, loading: true, error: false }));
        
        const dashboardData = await getDashboardData();
        const metrics = dashboardData.metrics;
        
        const hasTransactions = metrics.totalTransactions > 0;
        const hasCategorizedTransactions = metrics.categorizedTransactions > 0;
        const categorizedPercentage = hasTransactions 
          ? (metrics.categorizedTransactions / metrics.totalTransactions) * 100 
          : 0;

        // Journey Logic:
        // 1. No transactions → Upload
        // 2. Has transactions but none categorized → Upload/Processing
        // 3. Has some categorized transactions → Dashboard (they've started the journey)
        const shouldGoToDashboard = hasCategorizedTransactions;
        const shouldGoToUpload = !hasCategorizedTransactions;

        setStatus({
          loading: false,
          error: false,
          shouldGoToDashboard,
          shouldGoToUpload,
          hasTransactions,
          hasCategorizedTransactions,
          categorizedPercentage,
          totalTransactions: metrics.totalTransactions,
        });
      } catch (error) {
        console.error('Error checking user journey status:', error);
        
        // On error, default to upload flow (safer for new users)
        setStatus({
          loading: false,
          error: true,
          shouldGoToDashboard: false,
          shouldGoToUpload: true,
          hasTransactions: false,
          hasCategorizedTransactions: false,
          categorizedPercentage: 0,
          totalTransactions: 0,
        });
      }
    };

    void checkUserStatus();
  }, []);

  return status;
};