/**
 * useUserJourneyStatus Hook Tests
 * 
 * Tests the user journey status hook that determines routing logic
 * based on transaction categorization status and backend data validation.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useUserJourneyStatus } from '../useUserJourneyStatus';
import * as dashboardService from '@/features/dashboard/services/dashboardService';

// Mock the dashboard service
const mockGetDashboard = vi.spyOn(dashboardService, 'getDashboard');

describe('useUserJourneyStatus', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when user has categorized transactions', () => {
    beforeEach(() => {
      mockGetDashboard.mockResolvedValue({
        totalTransactionCount: 150,
        categorizedTransactionCount: 150,
        uncategorizedTransactionCount: 0,
        categorizationRate: 100,
        lastProcessedDate: '2025-01-10',
        monthlyTrends: [],
        topCategories: [
          { name: 'Food & Dining', count: 50, percentage: 33.3 },
          { name: 'Transportation', count: 30, percentage: 20.0 },
          { name: 'Utilities', count: 70, percentage: 46.7 }
        ],
        recentTransactions: [],
        aiAccuracy: 87.5
      });
    });

    it('should return correct status for fully categorized transactions', async () => {
      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(true);
      expect(result.current.shouldGoToDashboard).toBe(true);
      expect(result.current.shouldGoToUpload).toBe(false);
      expect(result.current.totalTransactions).toBe(150);
      expect(result.current.categorizedTransactions).toBe(150);
      expect(result.current.uncategorizedTransactions).toBe(0);
      expect(result.current.categorizationRate).toBe(100);
      expect(result.current.lastProcessedDate).toBe('2025-01-10');
      expect(result.current.error).toBeNull();
    });
  });

  describe('when user has only uncategorized transactions', () => {
    beforeEach(() => {
      mockGetDashboard.mockResolvedValue({
        totalTransactionCount: 100,
        categorizedTransactionCount: 0,
        uncategorizedTransactionCount: 100,
        categorizationRate: 0,
        lastProcessedDate: null,
        monthlyTrends: [],
        topCategories: [
          { name: 'Uncategorized', count: 100, percentage: 100.0 }
        ],
        recentTransactions: [],
        aiAccuracy: 0
      });
    });

    it('should return correct status for uncategorized transactions', async () => {
      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(false);
      expect(result.current.shouldGoToDashboard).toBe(false);
      expect(result.current.shouldGoToUpload).toBe(true);
      expect(result.current.totalTransactions).toBe(100);
      expect(result.current.categorizedTransactions).toBe(0);
      expect(result.current.uncategorizedTransactions).toBe(100);
      expect(result.current.categorizationRate).toBe(0);
      expect(result.current.lastProcessedDate).toBeNull();
      expect(result.current.error).toBeNull();
    });
  });

  describe('when user has no transactions', () => {
    beforeEach(() => {
      mockGetDashboard.mockResolvedValue({
        totalTransactionCount: 0,
        categorizedTransactionCount: 0,
        uncategorizedTransactionCount: 0,
        categorizationRate: 0,
        lastProcessedDate: null,
        monthlyTrends: [],
        topCategories: [],
        recentTransactions: [],
        aiAccuracy: 0
      });
    });

    it('should return correct status for no transactions', async () => {
      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(false);
      expect(result.current.shouldGoToDashboard).toBe(false);
      expect(result.current.shouldGoToUpload).toBe(true);
      expect(result.current.totalTransactions).toBe(0);
      expect(result.current.categorizedTransactions).toBe(0);
      expect(result.current.uncategorizedTransactions).toBe(0);
      expect(result.current.categorizationRate).toBe(0);
      expect(result.current.lastProcessedDate).toBeNull();
      expect(result.current.error).toBeNull();
    });
  });

  describe('when dashboard service fails', () => {
    beforeEach(() => {
      mockGetDashboard.mockRejectedValue(new Error('Network error'));
    });

    it('should handle errors gracefully', async () => {
      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(false);
      expect(result.current.shouldGoToDashboard).toBe(false);
      expect(result.current.shouldGoToUpload).toBe(true);
      expect(result.current.totalTransactions).toBe(0);
      expect(result.current.error).toBe('Network error');
    });
  });

  describe('backend data validation (critical bug fix)', () => {
    it('should detect and correct backend inconsistency where all transactions are marked as categorized but are actually "Uncategorized"', async () => {
      // Simulate the bug we found and fixed - backend claims 100% categorized but all are "Uncategorized"
      mockGetDashboard.mockResolvedValue({
        totalTransactionCount: 100,
        categorizedTransactionCount: 100, // Backend incorrectly claims all are categorized
        uncategorizedTransactionCount: 0, // Backend incorrectly claims none are uncategorized
        categorizationRate: 100, // Backend incorrectly claims 100% rate
        lastProcessedDate: '2025-01-10',
        monthlyTrends: [],
        topCategories: [
          { name: 'Uncategorized', count: 100, percentage: 100.0 } // But all are actually "Uncategorized"
        ],
        recentTransactions: [],
        aiAccuracy: 87.5
      });

      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // The hook should detect the inconsistency and correct it
      expect(result.current.hasCategorizedTransactions).toBe(false); // Corrected from backend claim
      expect(result.current.shouldGoToDashboard).toBe(false); // Should NOT go to dashboard
      expect(result.current.shouldGoToUpload).toBe(true); // Should go to upload instead
      expect(result.current.categorizedTransactions).toBe(0); // Corrected count
      expect(result.current.uncategorizedTransactions).toBe(100); // Corrected count
      expect(result.current.categorizationRate).toBe(0); // Corrected rate
    });

    it('should handle partial categorization correctly', async () => {
      mockGetDashboard.mockResolvedValue({
        totalTransactionCount: 200,
        categorizedTransactionCount: 150,
        uncategorizedTransactionCount: 50,
        categorizationRate: 75,
        lastProcessedDate: '2025-01-09',
        monthlyTrends: [],
        topCategories: [
          { name: 'Food & Dining', count: 50, percentage: 25.0 },
          { name: 'Transportation', count: 30, percentage: 15.0 },
          { name: 'Utilities', count: 70, percentage: 35.0 },
          { name: 'Uncategorized', count: 50, percentage: 25.0 }
        ],
        recentTransactions: [],
        aiAccuracy: 87.5
      });

      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasCategorizedTransactions).toBe(true);
      expect(result.current.shouldGoToDashboard).toBe(true);
      expect(result.current.shouldGoToUpload).toBe(false);
      expect(result.current.totalTransactions).toBe(200);
      expect(result.current.categorizedTransactions).toBe(150);
      expect(result.current.uncategorizedTransactions).toBe(50);
      expect(result.current.categorizationRate).toBe(75);
    });
  });

  describe('loading states', () => {
    it('should start with loading state', () => {
      // Mock a pending promise
      mockGetDashboard.mockImplementation(() => new Promise(() => {}));

      const { result } = renderHook(() => useUserJourneyStatus());

      expect(result.current.isLoading).toBe(true);
      expect(result.current.hasCategorizedTransactions).toBe(false);
      expect(result.current.shouldGoToDashboard).toBe(false);
      expect(result.current.shouldGoToUpload).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('routing logic edge cases', () => {
    it('should prioritize dashboard when hasCategorizedTransactions is true', async () => {
      mockGetDashboard.mockResolvedValue({
        totalTransactionCount: 1,
        categorizedTransactionCount: 1,
        uncategorizedTransactionCount: 0,
        categorizationRate: 100,
        lastProcessedDate: '2025-01-10',
        monthlyTrends: [],
        topCategories: [
          { name: 'Food & Dining', count: 1, percentage: 100.0 }
        ],
        recentTransactions: [],
        aiAccuracy: 87.5
      });

      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.shouldGoToDashboard).toBe(true);
      expect(result.current.shouldGoToUpload).toBe(false);
    });

    it('should enforce mutual exclusivity in routing decisions', async () => {
      mockGetDashboard.mockResolvedValue({
        totalTransactionCount: 50,
        categorizedTransactionCount: 25,
        uncategorizedTransactionCount: 25,
        categorizationRate: 50,
        lastProcessedDate: '2025-01-10',
        monthlyTrends: [],
        topCategories: [
          { name: 'Food & Dining', count: 25, percentage: 50.0 },
          { name: 'Uncategorized', count: 25, percentage: 50.0 }
        ],
        recentTransactions: [],
        aiAccuracy: 87.5
      });

      const { result } = renderHook(() => useUserJourneyStatus());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // With categorized transactions present, should go to dashboard
      expect(result.current.shouldGoToDashboard).toBe(true);
      expect(result.current.shouldGoToUpload).toBe(false);
      
      // Mutual exclusivity check
      expect(result.current.shouldGoToDashboard && result.current.shouldGoToUpload).toBe(false);
    });
  });
});