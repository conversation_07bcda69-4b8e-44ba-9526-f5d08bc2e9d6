import { useState, useEffect, useCallback, useMemo } from 'react';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  Transaction,
  Category as HierarchicalCategory,
} from '@/shared/types/categorization';
import { fetchCategories } from '@/features/categories/services/categoryService';
import { updateTransactionCategory } from '@/features/transactions/services/transactionService';
import { apiClient } from '@/shared/services/api/apiClient';

export interface TransactionDataState {
  transactionsData: Transaction[];
  categories: HierarchicalCategory[];
  filteredTransactions: Transaction[];
  searchQuery: string;
  selectedTransactions: Set<string>;
  isLoading: boolean;
  error: string | null;

  // Pagination
  totalItems: number;
  totalPages: number;
  currentPage: number;
  isRefreshing: boolean;

  // Sorting
  sortDirection: 'asc' | 'desc';
  sortField: string;
}

export const useTransactionData = () => {
  const { toast } = useToast();

  // Core data state
  const [transactionsData, setTransactionsData] = useState<Transaction[]>([]);
  const [categories, setCategories] = useState<HierarchicalCategory[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(
    new Set(),
  );

  // Pagination state
  const [totalItems, setTotalItems] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // Sorting state
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [sortField, setSortField] = useState<string>('date');

  // Load categories on mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await fetchCategories();

        // Check if the result is an ApiError
        if ('type' in categoriesData && 'message' in categoriesData) {
          // It's an ApiError
          console.error('Error loading categories:', categoriesData);
          toast({
            title: 'Error',
            description: categoriesData.message || 'Failed to load categories',
            variant: 'destructive',
          });
          return;
        }

        // It's a successful response with Category[]
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error loading categories:', error);
        toast({
          title: 'Error',
          description: 'Failed to load categories',
          variant: 'destructive',
        });
      }
    };

    void loadCategories();
  }, [toast]);

  const loadTransactions = useCallback(
    async (page: number = 1, limit: number = 50) => {
      const loadingState = page === 1 ? setIsLoading : setIsRefreshing;
      loadingState(true);
      setError(null);

      try {
        const response = await apiClient.get<{
          items: Transaction[];
          total_count: number;
          page: number;
          page_size: number;
          total_pages: number;
        }>('/transactions/', {
          params: {
            page,
            limit,
            sort_by: sortField,
            sort_direction: sortDirection,
          },
        });

        const {
          items: transactions,
          total_count: total_items,
          total_pages,
        } = response.data;

        setTransactionsData(transactions || []);
        setTotalItems(total_items || 0);
        setTotalPages(total_pages || 1);
        setCurrentPage(page);
      } catch (error) {
        console.error('Error loading transactions:', error);
        setError('Failed to load transactions');
        toast({
          title: 'Error',
          description: 'Failed to load transactions',
          variant: 'destructive',
        });
      } finally {
        loadingState(false);
      }
    },
    [sortField, sortDirection, toast],
  );

  // Filter transactions based on search query
  const filteredTransactions = useMemo(() => {
    if (!searchQuery.trim()) {
      return transactionsData;
    }

    const query = searchQuery.toLowerCase();
    return transactionsData.filter(
      (transaction) =>
        transaction.description?.toLowerCase().includes(query) ||
        transaction.category_path?.toLowerCase().includes(query) ||
        transaction.amount?.toString().includes(query) ||
        transaction.date?.toLowerCase().includes(query),
    );
  }, [transactionsData, searchQuery]);

  const handleCategoryUpdate = useCallback(
    async (transactionId: string, categoryId: string) => {
      try {
        await updateTransactionCategory(transactionId, parseInt(categoryId));

        // Update local state
        setTransactionsData((prev) =>
          prev.map((transaction) =>
            transaction.id === transactionId
              ? { ...transaction, category: categoryId }
              : transaction,
          ),
        );

        toast({
          title: 'Category Updated',
          description: 'Transaction category has been updated successfully',
        });
      } catch (error) {
        console.error('Error updating category:', error);
        toast({
          title: 'Update Error',
          description: 'Failed to update transaction category',
          variant: 'destructive',
        });
      }
    },
    [toast],
  );

  const toggleTransactionSelection = useCallback((transactionId: string) => {
    setSelectedTransactions((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(transactionId)) {
        newSet.delete(transactionId);
      } else {
        newSet.add(transactionId);
      }
      return newSet;
    });
  }, []);

  const toggleAllTransactions = useCallback(() => {
    setSelectedTransactions((prev) => {
      const allSelected = filteredTransactions.every((transaction) =>
        prev.has(transaction.id),
      );

      if (allSelected) {
        // Deselect all visible transactions
        const newSet = new Set(prev);
        filteredTransactions.forEach((transaction) => {
          newSet.delete(transaction.id);
        });
        return newSet;
      } else {
        // Select all visible transactions
        const newSet = new Set(prev);
        filteredTransactions.forEach((transaction) => {
          newSet.add(transaction.id);
        });
        return newSet;
      }
    });
  }, [filteredTransactions]);

  const clearSelection = useCallback(() => {
    setSelectedTransactions(new Set());
  }, []);

  const handleSort = useCallback(
    (field: string) => {
      if (field === sortField) {
        setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
      } else {
        setSortField(field);
        setSortDirection('asc');
      }
    },
    [sortField],
  );

  const refreshTransactions = useCallback(() => {
    void loadTransactions(currentPage);
  }, [loadTransactions, currentPage]);

  // Load transactions on sort changes
  useEffect(() => {
    void loadTransactions(1);
  }, [loadTransactions]);

  return {
    // Data state
    transactionsData,
    categories,
    filteredTransactions,
    isLoading,
    error,

    // Search state
    searchQuery,
    setSearchQuery,

    // Selection state
    selectedTransactions,
    toggleTransactionSelection,
    toggleAllTransactions,
    clearSelection,

    // Pagination state
    totalItems,
    totalPages,
    currentPage,
    isRefreshing,

    // Sorting state
    sortDirection,
    sortField,

    // Actions
    loadTransactions,
    handleCategoryUpdate,
    handleSort,
    refreshTransactions,
    setCurrentPage,
  };
};
