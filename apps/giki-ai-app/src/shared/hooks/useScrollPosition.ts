import { useEffect, useRef, useCallback, useState } from 'react';
import { useLocation } from 'react-router-dom';

interface ScrollPosition {
  x: number;
  y: number;
}

// Store scroll positions for different routes
const scrollPositions = new Map<string, ScrollPosition>();

/**
 * Hook to manage scroll position persistence across navigation
 * Automatically saves and restores scroll position when navigating
 */
export function useScrollPosition(elementRef?: React.RefObject<HTMLElement>) {
  const location = useLocation();
  const savedPosition = useRef<ScrollPosition | null>(null);

  // Save current scroll position
  const saveScrollPosition = useCallback(() => {
    const element = elementRef?.current || window;
    const scrollElement =
      element === window ? document.documentElement : (element as HTMLElement);

    const position: ScrollPosition = {
      x: element === window ? window.scrollX : scrollElement.scrollLeft,
      y: element === window ? window.scrollY : scrollElement.scrollTop,
    };

    scrollPositions.set(location.pathname, position);
    savedPosition.current = position;
  }, [location.pathname, elementRef]);

  // Restore scroll position
  const restoreScrollPosition = useCallback(() => {
    const position = scrollPositions.get(location.pathname);
    if (!position) return;

    const element = elementRef?.current || window;

    // Use requestAnimationFrame for smooth restoration
    requestAnimationFrame(() => {
      if (element === window) {
        window.scrollTo({
          left: position.x,
          top: position.y,
          behavior: 'instant', // Use instant to avoid jarring animations on navigation
        });
      } else {
        const scrollElement = element as HTMLElement;
        scrollElement.scrollLeft = position.x;
        scrollElement.scrollTop = position.y;
      }
    });
  }, [location.pathname, elementRef]);

  // Scroll to top
  const scrollToTop = useCallback(
    (smooth = true) => {
      const element = elementRef?.current || window;

      if (element === window) {
        window.scrollTo({
          top: 0,
          behavior: smooth ? 'smooth' : 'instant',
        });
      } else {
        const scrollElement = element as HTMLElement;
        scrollElement.scrollTo({
          top: 0,
          behavior: smooth ? 'smooth' : 'instant',
        });
      }
    },
    [elementRef],
  );

  // Get current scroll position
  const getScrollPosition = useCallback((): ScrollPosition => {
    const element = elementRef?.current || window;
    const scrollElement =
      element === window ? document.documentElement : (element as HTMLElement);

    return {
      x: element === window ? window.scrollX : scrollElement.scrollLeft,
      y: element === window ? window.scrollY : scrollElement.scrollTop,
    };
  }, [elementRef]);

  // Save position on unmount and route change
  useEffect(() => {
    // Save on unmount
    return () => {
      saveScrollPosition();
    };
  }, [saveScrollPosition]);

  // Restore position when location changes
  useEffect(() => {
    // Small delay to ensure DOM is ready
    const timer = setTimeout(() => {
      restoreScrollPosition();
    }, 100);

    return () => clearTimeout(timer);
  }, [location.pathname, restoreScrollPosition]);

  return {
    saveScrollPosition,
    restoreScrollPosition,
    scrollToTop,
    getScrollPosition,
    savedPosition: savedPosition.current,
  };
}

/**
 * Hook to detect scroll direction
 */
export function useScrollDirection(threshold = 10) {
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(
    null,
  );
  const lastScrollY = useRef(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (Math.abs(currentScrollY - lastScrollY.current) < threshold) {
        return;
      }

      setScrollDirection(currentScrollY > lastScrollY.current ? 'down' : 'up');
      lastScrollY.current = currentScrollY;
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [threshold]);

  return scrollDirection;
}

/**
 * Hook to detect if element is in viewport
 */
export function useInViewport(
  ref: React.RefObject<HTMLElement>,
  options?: IntersectionObserverInit,
) {
  const [isInViewport, setIsInViewport] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsInViewport(entry.isIntersecting);
    }, options);

    observer.observe(ref.current);

    return () => {
      observer.disconnect();
    };
  }, [ref, options]);

  return isInViewport;
}
