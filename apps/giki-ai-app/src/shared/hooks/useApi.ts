/**
 * useApi Hook
 *
 * A React hook for making API requests with loading and error states.
 * This hook wraps the apiClient and provides a consistent interface for
 * making API requests from React components.
 */

import { useState, useCallback } from 'react';
import {
  apiClient,
  ApiRequestOptions,
  ApiResponse,
} from '@/shared/services/api/apiClient';
import { AppError, ErrorType, createApiError } from '@/shared/types/errors';

export interface UseApiState<T> {
  data: T | null;
  isLoading: boolean;
  error: AppError | null;
}

export interface UseApiActions<T, P = unknown> {
  execute: (params?: P) => Promise<ApiResponse<T>>;
  reset: () => void;
}

export type UseApiResult<T, P = unknown> = [
  UseApiState<T>,
  UseApiActions<T, P>,
];

/**
 * Hook for making GET requests
 */
export function useApiGet<T, P = Record<string, unknown>>(
  endpoint: string,
  options: ApiRequestOptions = {},
): UseApiResult<T, P> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const execute = useCallback(
    async (params?: P): Promise<ApiResponse<T>> => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        // Merge the params with any existing params in options
        const mergedOptions: ApiRequestOptions = {
          ...options,
          params: {
            ...(options.params || {}),
            ...(params || {}),
          },
        };

        const response = await apiClient.get<T>(endpoint, mergedOptions);

        setState({
          data: response.data,
          isLoading: false,
          error: null,
        });

        return response;
      } catch (error) {
        const appError =
          error instanceof Error
            ? createApiError({
                type: ErrorType.UNKNOWN,
                message: error.message,
                originalError: error,
              })
            : (error as AppError);

        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: appError,
        }));

        throw appError;
      }
    },
    [endpoint, options],
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      error: null,
    });
  }, []);

  return [state, { execute, reset }];
}

/**
 * Hook for making POST requests
 */
export function useApiPost<T, D = unknown>(
  endpoint: string,
  options: ApiRequestOptions = {},
): UseApiResult<T, D> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const execute = useCallback(
    async (data?: D): Promise<ApiResponse<T>> => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const response = await apiClient.post<T>(endpoint, data, options);

        setState({
          data: response.data,
          isLoading: false,
          error: null,
        });

        return response;
      } catch (error) {
        const appError =
          error instanceof Error
            ? createApiError({
                type: ErrorType.UNKNOWN,
                message: error.message,
                originalError: error,
              })
            : (error as AppError);

        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: appError,
        }));

        throw appError;
      }
    },
    [endpoint, options],
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      error: null,
    });
  }, []);

  return [state, { execute, reset }];
}

/**
 * Hook for making PUT requests
 */
export function useApiPut<T, D = unknown>(
  endpoint: string,
  options: ApiRequestOptions = {},
): UseApiResult<T, D> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const execute = useCallback(
    async (data?: D): Promise<ApiResponse<T>> => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const response = await apiClient.put<T>(endpoint, data, options);

        setState({
          data: response.data,
          isLoading: false,
          error: null,
        });

        return response;
      } catch (error) {
        const appError =
          error instanceof Error
            ? createApiError({
                type: ErrorType.UNKNOWN,
                message: error.message,
                originalError: error,
              })
            : (error as AppError);

        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: appError,
        }));

        throw appError;
      }
    },
    [endpoint, options],
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      error: null,
    });
  }, []);

  return [state, { execute, reset }];
}

/**
 * Hook for making PATCH requests
 */
export function useApiPatch<T, D = unknown>(
  endpoint: string,
  options: ApiRequestOptions = {},
): UseApiResult<T, D> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const execute = useCallback(
    async (data?: D): Promise<ApiResponse<T>> => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const response = await apiClient.patch<T>(endpoint, data, options);

        setState({
          data: response.data,
          isLoading: false,
          error: null,
        });

        return response;
      } catch (error) {
        const appError =
          error instanceof Error
            ? createApiError({
                type: ErrorType.UNKNOWN,
                message: error.message,
                originalError: error,
              })
            : (error as AppError);

        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: appError,
        }));

        throw appError;
      }
    },
    [endpoint, options],
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      error: null,
    });
  }, []);

  return [state, { execute, reset }];
}

/**
 * Hook for making DELETE requests
 */
export function useApiDelete<T>(
  endpoint: string,
  options: ApiRequestOptions = {},
): UseApiResult<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const execute = useCallback(async (): Promise<ApiResponse<T>> => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await apiClient.delete<T>(endpoint, options);

      setState({
        data: response.data,
        isLoading: false,
        error: null,
      });

      return response;
    } catch (error) {
      const appError =
        error instanceof Error
          ? createApiError({
              type: ErrorType.UNKNOWN,
              message: error.message,
              originalError: error,
            })
          : (error as AppError);

      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: appError,
      }));

      throw appError;
    }
  }, [endpoint, options]);

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      error: null,
    });
  }, []);

  return [state, { execute, reset }];
}

/**
 * Hook for making POST requests with application/x-www-form-urlencoded data
 */
export function useApiPostFormUrlEncoded<T, D = Record<string, string>>(
  endpoint: string,
  options: ApiRequestOptions = {},
): UseApiResult<T, D> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const execute = useCallback(
    async (data?: D): Promise<ApiResponse<T>> => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        // Type assertion for form data - postFormUrlEncoded expects string values
        const formData = data as Record<string, string>;
        const response = await apiClient.postFormUrlEncoded<T>(
          endpoint,
          formData,
          options,
        );

        setState({
          data: response.data,
          isLoading: false,
          error: null,
        });

        return response;
      } catch (error) {
        const appError =
          error instanceof Error
            ? createApiError({
                type: ErrorType.UNKNOWN,
                message: error.message,
                originalError: error,
              })
            : (error as AppError);

        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: appError,
        }));

        throw appError;
      }
    },
    [endpoint, options],
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      error: null,
    });
  }, []);

  return [state, { execute, reset }];
}

/**
 * Generic hook for making any type of API request
 */
export function useApi<T, P = unknown>(
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
  endpoint: string,
  options: ApiRequestOptions = {},
): UseApiResult<T, P> {
  switch (method) {
    case 'GET':
      return useApiGet<T, P>(endpoint, options);
    // Add other methods if a truly generic hook is needed beyond specific ones
    // For now, specific hooks like useApiPost, useApiGet, useApiPostFormUrlEncoded are preferred
    case 'POST': // This implies JSON post by default
      return useApiPost<T, P>(endpoint, options);
    case 'PUT':
      return useApiPut<T, P>(endpoint, options);
    case 'PATCH':
      return useApiPatch<T, P>(endpoint, options);
    case 'DELETE':
      return useApiDelete<T>(endpoint, options) as UseApiResult<T, P>;
    default:
      throw new Error(`Unsupported method: ${method as string}`);
  }
}

export default useApi;

// ============================================================================
// Enhanced Service-Specific Hooks (ADK Integration)
// ============================================================================

// TODO: Import services when they exist
// import {
//   validateFile,
//   extractFileMetadata,
//   uploadFileWithValidation,
//   type FileValidationResponse,
//   type FileMetadataResponse,
// } from "@/features/files/services/fileService"';
// import {
//   categorizeWithRAG,
//   type RAGCategorizationResponse,
// } from '@/features/intelligence/services/ragCorpusService';
// import {
//   runCompleteOnboardingWorkflow,
//   type TenantCreationResponse,
//   type DataSeedingResponse,
//   type OnboardingCompletionResponse,
// } from '@/features/onboarding/services/onboardingService';

// ============================================================================
// RAG Categorization Hook (ADK Integration)
// ============================================================================

export interface RAGCategorizationRequest {
  query_text: string;
  corpus_id?: string;
  top_k?: number;
}

export interface RAGCategorizationResponse {
  categorization_result?: {
    suggested_category: string;
    confidence: number;
    reasoning?: string;
    similar_transactions?: number;
  };
}

/**
 * Hook for RAG-based transaction categorization
 * Uses a dynamic endpoint based on corpus_id to match the existing service pattern
 */
export function useRAGCategorization() {
  const [state, setState] = useState<UseApiState<RAGCategorizationResponse>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const categorizeWithRAG = useCallback(
    async (request: RAGCategorizationRequest): Promise<ApiResponse<RAGCategorizationResponse>> => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        // Build dynamic endpoint to match existing service pattern
        const endpoint = `/api/v1/rag-corpus/${request.corpus_id}/query`;
        
        const response = await apiClient.post<RAGCategorizationResponse>(endpoint, {
          query: request.query_text,
          top_k: request.top_k || 5,
        });

        setState({
          data: response.data,
          isLoading: false,
          error: null,
        });

        return response;
      } catch (error) {
        const appError =
          error instanceof Error
            ? createApiError({
                type: ErrorType.UNKNOWN,
                message: error.message,
                originalError: error,
              })
            : (error as AppError);

        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: appError,
        }));

        throw appError;
      }
    },
    []
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      error: null,
    });
  }, []);

  return {
    data: state.data,
    isLoading: state.isLoading,
    error: state.error,
    categorizeWithRAG,
    reset,
  };
}
