/**
 * Loading state management hook with timeout detection
 *
 * Features:
 * - Automatic timeout detection
 * - Progressive loading messages
 * - Retry capabilities
 * - Performance tracking
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { useToast } from '@/shared/components/ui/use-toast';
import { logger } from '@/shared/lib/logger';

export interface LoadingStateOptions {
  timeout?: number; // Timeout in milliseconds
  slowThreshold?: number; // Threshold for "slow" warning
  progressMessages?: {
    initial: string;
    slow?: string;
    veryLow?: string;
    timeout?: string;
  };
  onTimeout?: () => void;
  onSlowLoading?: () => void;
}

export interface LoadingState {
  isLoading: boolean;
  message: string;
  duration: number;
  isSlowLoading: boolean;
  hasTimedOut: boolean;
}

export function useLoadingState(options: LoadingStateOptions = {}) {
  const {
    timeout = 30000, // 30 seconds default
    slowThreshold = 5000, // 5 seconds
    progressMessages = {
      initial: 'Loading...',
      slow: 'This is taking longer than usual...',
      veryLow: 'Almost there, please wait...',
      timeout: 'Operation timed out. Please try again.',
    },
    onTimeout,
    onSlowLoading,
  } = options;

  const { toast } = useToast();
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    message: progressMessages.initial,
    duration: 0,
    isSlowLoading: false,
    hasTimedOut: false,
  });

  const startTimeRef = useRef<number | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const slowLoadingRef = useRef<NodeJS.Timeout | null>(null);
  const verySlowLoadingRef = useRef<NodeJS.Timeout | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (slowLoadingRef.current) clearTimeout(slowLoadingRef.current);
    if (verySlowLoadingRef.current) clearTimeout(verySlowLoadingRef.current);
    if (durationIntervalRef.current) clearInterval(durationIntervalRef.current);
  }, []);

  // Start loading
  const startLoading = useCallback(
    (initialMessage?: string) => {
      clearTimers();
      startTimeRef.current = Date.now();

      setLoadingState({
        isLoading: true,
        message: initialMessage || progressMessages.initial,
        duration: 0,
        isSlowLoading: false,
        hasTimedOut: false,
      });

      // Track duration
      durationIntervalRef.current = setInterval(() => {
        if (startTimeRef.current) {
          const duration = Date.now() - startTimeRef.current;
          setLoadingState((prev) => ({ ...prev, duration }));
        }
      }, 100);

      // Set up slow loading detection
      slowLoadingRef.current = setTimeout(() => {
        setLoadingState((prev) => ({
          ...prev,
          isSlowLoading: true,
          message: progressMessages.slow || prev.message,
        }));
        onSlowLoading?.();

        logger.warn('Slow loading detected', {
          duration: slowThreshold,
          operation: initialMessage,
        });
      }, slowThreshold);

      // Set up very slow loading detection (2x slow threshold)
      verySlowLoadingRef.current = setTimeout(() => {
        setLoadingState((prev) => ({
          ...prev,
          message: progressMessages.veryLow || prev.message,
        }));
      }, slowThreshold * 2);

      // Set up timeout
      timeoutRef.current = setTimeout(() => {
        setLoadingState((prev) => ({
          ...prev,
          hasTimedOut: true,
          isLoading: false,
          message: progressMessages.timeout || 'Operation timed out',
        }));

        logger.error('Loading operation timed out', {
          timeout,
          operation: initialMessage,
        });

        toast({
          title: 'Operation Timed Out',
          description:
            'The operation took too long to complete. Please try again.',
          variant: 'destructive',
        });

        onTimeout?.();
        clearTimers();
      }, timeout);
    },
    [
      clearTimers,
      progressMessages,
      slowThreshold,
      timeout,
      onSlowLoading,
      onTimeout,
      toast,
    ],
  );

  // Stop loading
  const stopLoading = useCallback(
    (success = true) => {
      clearTimers();

      const duration = startTimeRef.current
        ? Date.now() - startTimeRef.current
        : 0;

      setLoadingState({
        isLoading: false,
        message: '',
        duration,
        isSlowLoading: false,
        hasTimedOut: false,
      });

      // Log performance metrics
      if (duration > 0) {
        const level = duration > slowThreshold ? 'warn' : 'info';
        logger[level]('Loading operation completed', {
          duration,
          success,
          wasSlowLoading: duration > slowThreshold,
        });
      }

      startTimeRef.current = null;
    },
    [clearTimers, slowThreshold],
  );

  // Update loading message
  const updateMessage = useCallback((message: string) => {
    setLoadingState((prev) => ({
      ...prev,
      message,
    }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);

  return {
    ...loadingState,
    startLoading,
    stopLoading,
    updateMessage,
  };
}

// Hook for managing multiple concurrent loading states
export function useConcurrentLoadingStates() {
  const [loadingStates, setLoadingStates] = useState<Map<string, boolean>>(
    new Map(),
  );

  const startLoading = useCallback((key: string) => {
    setLoadingStates((prev) => new Map(prev).set(key, true));
  }, []);

  const stopLoading = useCallback((key: string) => {
    setLoadingStates((prev) => {
      const next = new Map(prev);
      next.delete(key);
      return next;
    });
  }, []);

  const isAnyLoading = loadingStates.size > 0;
  const loadingKeys = Array.from(loadingStates.keys());

  return {
    startLoading,
    stopLoading,
    isAnyLoading,
    loadingKeys,
    loadingCount: loadingStates.size,
  };
}
