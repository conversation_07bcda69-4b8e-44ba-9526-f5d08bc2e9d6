/**
 * Agent Integration Hook - Cross-Page Agent Enhancement
 *
 * Provides seamless agent integration capabilities across all application pages.
 * Manages context-aware agent interactions, capability detection, and UI integration.
 */

import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAgentAssistance } from '../context/AgentAssistanceProvider';
import { toast } from '../components/ui/use-toast';

// Enhanced capability types
export interface AgentPageCapability {
  id: string;
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
  contextData?: unknown;
  quickActions: Array<{
    id: string;
    label: string;
    icon: string;
    action: () => void;
  }>;
}

export interface AgentPageConfig {
  pageId: string;
  pageName: string;
  capabilities: AgentPageCapability[];
  contextualHelp: string[];
  suggestedActions: string[];
  dataExtractors: {
    [key: string]: () => unknown;
  };
}

// Page-specific configurations
const PAGE_CONFIGURATIONS: Record<string, AgentPageConfig> = {
  '/dashboard': {
    pageId: 'dashboard',
    pageName: 'Dashboard Overview',
    capabilities: [
      {
        id: 'file_upload',
        name: 'Quick Upload',
        description: 'Upload files directly from the dashboard',
        icon: 'Upload',
        enabled: true,
        quickActions: [
          {
            id: 'upload_csv',
            label: 'Upload CSV',
            icon: 'FileText',
            action: () => {},
          },
          {
            id: 'upload_excel',
            label: 'Upload Excel',
            icon: 'Table',
            action: () => {},
          },
        ],
      },
      {
        id: 'generate_report',
        name: 'Generate Report',
        description: 'Create reports based on current data',
        icon: 'BarChart3',
        enabled: true,
        quickActions: [
          {
            id: 'accuracy_report',
            label: 'Accuracy Report',
            icon: 'Target',
            action: () => {},
          },
          {
            id: 'summary_report',
            label: 'Summary Report',
            icon: 'FileText',
            action: () => {},
          },
        ],
      },
      {
        id: 'data_analysis',
        name: 'Data Analysis',
        description: 'Analyze dashboard metrics and trends',
        icon: 'TrendingUp',
        enabled: true,
        quickActions: [
          {
            id: 'analyze_trends',
            label: 'Analyze Trends',
            icon: 'LineChart',
            action: () => {},
          },
        ],
      },
    ],
    contextualHelp: [
      'Upload new transaction files to get started',
      'Check your categorization accuracy metrics',
      'Review recent processing status',
      'Generate reports for your data',
    ],
    suggestedActions: [
      'upload_new_file',
      'check_accuracy',
      'generate_report',
      'review_categories',
    ],
    dataExtractors: {
      metrics: () => ({}), // Would extract current dashboard metrics
      recentFiles: () => [], // Would extract recent file uploads
      processingStatus: () => ({}), // Would extract processing status
    },
  },
  '/upload': {
    pageId: 'upload',
    pageName: 'File Upload',
    capabilities: [
      {
        id: 'file_processing',
        name: 'Smart Processing',
        description: 'AI-powered file analysis and processing',
        icon: 'Zap',
        enabled: true,
        quickActions: [
          {
            id: 'analyze_schema',
            label: 'Analyze Schema',
            icon: 'Search',
            action: () => {},
          },
          {
            id: 'preview_data',
            label: 'Preview Data',
            icon: 'Eye',
            action: () => {},
          },
        ],
      },
      {
        id: 'validation',
        name: 'Data Validation',
        description: 'Validate file format and data quality',
        icon: 'CheckCircle',
        enabled: true,
        quickActions: [
          {
            id: 'check_format',
            label: 'Check Format',
            icon: 'FileCheck',
            action: () => {},
          },
        ],
      },
    ],
    contextualHelp: [
      'Select Excel or CSV files to upload',
      'Ensure files have proper headers',
      'Check file format requirements',
      'Preview data before processing',
    ],
    suggestedActions: [
      'upload_file',
      'validate_format',
      'preview_data',
      'start_processing',
    ],
    dataExtractors: {
      uploadedFiles: () => [],
      validationResults: () => ({}),
      processingProgress: () => ({}),
    },
  },
  '/transactions': {
    pageId: 'transactions',
    pageName: 'Transaction Management',
    capabilities: [
      {
        id: 'categorization',
        name: 'AI Categorization',
        description: 'Intelligent transaction categorization',
        icon: 'Tags',
        enabled: true,
        quickActions: [
          {
            id: 'categorize_selected',
            label: 'Categorize Selected',
            icon: 'Play',
            action: () => {},
          },
          {
            id: 'review_suggestions',
            label: 'Review Suggestions',
            icon: 'Eye',
            action: () => {},
          },
        ],
      },
      {
        id: 'bulk_actions',
        name: 'Bulk Operations',
        description: 'Perform actions on multiple transactions',
        icon: 'Layers',
        enabled: true,
        quickActions: [
          {
            id: 'bulk_approve',
            label: 'Bulk Approve',
            icon: 'CheckSquare',
            action: () => {},
          },
          {
            id: 'bulk_edit',
            label: 'Bulk Edit',
            icon: 'Edit',
            action: () => {},
          },
        ],
      },
    ],
    contextualHelp: [
      'Review AI categorization suggestions',
      'Approve or modify categories',
      'Use bulk actions for efficiency',
      'Check accuracy metrics',
    ],
    suggestedActions: [
      'review_categories',
      'approve_suggestions',
      'bulk_update',
      'check_accuracy',
    ],
    dataExtractors: {
      selectedTransactions: () => [],
      categories: () => [],
      accuracyMetrics: () => ({}),
    },
  },
  '/reports': {
    pageId: 'reports',
    pageName: 'Reports & Analytics',
    capabilities: [
      {
        id: 'report_generation',
        name: 'Smart Reports',
        description: 'AI-powered report generation',
        icon: 'FileText',
        enabled: true,
        quickActions: [
          {
            id: 'generate_custom',
            label: 'Custom Report',
            icon: 'Settings',
            action: () => {},
          },
          {
            id: 'export_data',
            label: 'Export Data',
            icon: 'Download',
            action: () => {},
          },
        ],
      },
      {
        id: 'analytics',
        name: 'Data Analytics',
        description: 'Advanced data analysis and insights',
        icon: 'BarChart3',
        enabled: true,
        quickActions: [
          {
            id: 'analyze_trends',
            label: 'Trend Analysis',
            icon: 'TrendingUp',
            action: () => {},
          },
        ],
      },
    ],
    contextualHelp: [
      'Generate custom reports',
      'Export data in various formats',
      'Analyze spending patterns',
      'Track categorization accuracy',
    ],
    suggestedActions: [
      'generate_report',
      'export_data',
      'analyze_trends',
      'schedule_reports',
    ],
    dataExtractors: {
      reportData: () => ({}),
      exportFormats: () => [],
      analyticsData: () => ({}),
    },
  },
  '/accuracy': {
    pageId: 'accuracy',
    pageName: 'Accuracy Testing',
    capabilities: [
      {
        id: 'accuracy_testing',
        name: 'Test Management',
        description: 'Create and manage accuracy tests',
        icon: 'Target',
        enabled: true,
        quickActions: [
          {
            id: 'create_test',
            label: 'New Test',
            icon: 'Plus',
            action: () => {},
          },
          {
            id: 'run_validation',
            label: 'Run Validation',
            icon: 'Play',
            action: () => {},
          },
        ],
      },
      {
        id: 'milestone_tracking',
        name: 'Milestone Progress',
        description: 'Track M1, M2, M3 milestone progress',
        icon: 'Award',
        enabled: true,
        quickActions: [
          {
            id: 'check_m2_progress',
            label: 'M2 Progress',
            icon: 'BarChart3',
            action: () => {},
          },
        ],
      },
    ],
    contextualHelp: [
      'Create accuracy tests for validation',
      'Track milestone progress',
      'Compare different scenarios',
      'Analyze temporal accuracy',
    ],
    suggestedActions: [
      'create_test',
      'run_validation',
      'check_milestones',
      'analyze_results',
    ],
    dataExtractors: {
      testResults: () => ({}),
      milestoneStatus: () => ({}),
      accuracyTrends: () => [],
    },
  },
};

export const useAgentIntegration = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const {
    agentState,
    toggleAgent,
    expandAgent,
    collapseAgent,
    sendMessage,
    sendFileUpload,
    sendActionRequest,
    updatePageContext,
    setCurrentData,
    getAvailableCapabilities,
    canPerformAction,
  } = useAgentAssistance();

  const currentPageConfig = useMemo(() => {
    return (
      PAGE_CONFIGURATIONS[location.pathname] ||
      PAGE_CONFIGURATIONS['/dashboard']
    );
  }, [location.pathname]);

  const lastPageRef = useRef<string>('');

  // Update page context when location changes
  useEffect(() => {
    if (location.pathname !== lastPageRef.current) {
      updatePageContext({
        page: currentPageConfig.pageId,
        title: currentPageConfig.pageName,
        capabilities: currentPageConfig.capabilities.map((c) => c.id),
        availableActions: currentPageConfig.suggestedActions,
      });

      // Extract and set current page data
      const extractedData = Object.entries(
        currentPageConfig.dataExtractors,
      ).reduce(
        (acc, [key, extractor]) => {
          try {
            acc[key] = extractor();
          } catch (error) {
            console.warn(`Failed to extract ${key} data:`, error);
            acc[key] = null;
          }
          return acc;
        },
        {} as Record<string, unknown>,
      );

      setCurrentData(extractedData);
      lastPageRef.current = location.pathname;
    }
  }, [location.pathname, currentPageConfig, updatePageContext, setCurrentData]);

  // Enhanced quick actions
  const executeQuickAction = useCallback(
    (actionId: string, capability?: AgentPageCapability) => {
      if (capability) {
        const action = capability.quickActions.find((a) => a.id === actionId);
        if (action) {
          try {
            action.action();

            // Send action to agent for context
            sendActionRequest(actionId, {
              capability: capability.id,
              pageContext: currentPageConfig.pageId,
            });

            toast({
              title: 'Action Executed',
              description: `${action.label} completed successfully.`,
            });
          } catch (error) {
            console.error('Quick action failed:', error);
            toast({
              title: 'Action Failed',
              description: `Failed to execute ${action.label}. Please try again.`,
              variant: 'destructive',
            });
          }
        }
      }
    },
    [sendActionRequest, currentPageConfig.pageId],
  );

  // File upload with context
  const handleContextualFileUpload = useCallback(
    (files: FileList) => {
      const context = `Upload from ${currentPageConfig.pageName}`;
      sendFileUpload(files, context);

      toast({
        title: 'Files Uploaded',
        description: `Uploaded ${files.length} file(s) for processing.`,
      });
    },
    [sendFileUpload, currentPageConfig.pageName],
  );

  // Smart messaging with context
  const sendContextualMessage = useCallback(
    (message: string) => {
      const contextData = currentPageConfig.dataExtractors;
      const extractedContext = Object.entries(contextData).reduce(
        (acc, [key, extractor]) => {
          try {
            acc[key] = extractor();
          } catch (error) {
            acc[key] = null;
          }
          return acc;
        },
        {} as Record<string, unknown>,
      );

      sendMessage(message, {
        pageContext: currentPageConfig.pageId,
        pageCapabilities: currentPageConfig.capabilities.map((c) => c.id),
        currentData: extractedContext,
      });
    },
    [sendMessage, currentPageConfig],
  );

  // Navigation assistance
  const navigateWithContext = useCallback(
    (path: string, context?: string) => {
      navigate(path);

      if (context) {
        setTimeout(() => {
          sendMessage(`Navigated to ${path}: ${context}`, {
            navigationType: 'user_initiated',
            fromPage: currentPageConfig.pageId,
            toPage: path,
          });
        }, 500);
      }
    },
    [navigate, sendMessage, currentPageConfig.pageId],
  );

  // Page-specific help
  const getContextualHelp = useCallback(() => {
    return currentPageConfig.contextualHelp;
  }, [currentPageConfig.contextualHelp]);

  // Capability filtering
  const getEnabledCapabilities = useCallback(() => {
    return currentPageConfig.capabilities.filter((cap) => cap.enabled);
  }, [currentPageConfig.capabilities]);

  // Enhanced agent panel props
  const getAgentPanelProps = useCallback(() => {
    return {
      isExpanded: agentState?.isExpanded || false,
      onToggle: toggleAgent,
      onExpand: expandAgent,
      onCollapse: collapseAgent,
      currentPage: currentPageConfig.pageId,
      pageName: currentPageConfig.pageName,
      capabilities: getEnabledCapabilities(),
      quickActions: currentPageConfig.capabilities.flatMap((cap) =>
        cap.quickActions.map((action) => ({
          ...action,
          capabilityId: cap.id,
          execute: () => executeQuickAction(action.id, cap),
        })),
      ),
      contextualHelp: getContextualHelp(),
      onFileUpload: handleContextualFileUpload,
      onSendMessage: sendContextualMessage,
      onNavigate: navigateWithContext,
    };
  }, [
    agentState?.isExpanded,
    toggleAgent,
    expandAgent,
    collapseAgent,
    currentPageConfig,
    getEnabledCapabilities,
    executeQuickAction,
    getContextualHelp,
    handleContextualFileUpload,
    sendContextualMessage,
    navigateWithContext,
  ]);

  // Agent FAB props (simplified)
  const getAgentFABProps = useCallback(() => {
    return {
      isExpanded: agentState?.isExpanded || false,
      onToggle: toggleAgent,
      hasUnreadMessages: (agentState?.messages || []).some(
        (m) =>
          m &&
          m.sender === 'agent' &&
          !(m.metadata as { read?: boolean })?.read,
      ),
      processingRequest: agentState?.processingRequest || false,
      connectionStatus: agentState?.isConnected ? 'connected' : 'disconnected',
      showLogo: true,
      pageContext: currentPageConfig.pageId,
    };
  }, [
    agentState?.isExpanded,
    agentState?.messages,
    agentState?.processingRequest,
    agentState?.isConnected,
    toggleAgent,
    currentPageConfig.pageId,
  ]);

  return {
    // Page Configuration
    currentPageConfig,
    pageCapabilities: getEnabledCapabilities(),
    contextualHelp: getContextualHelp(),

    // Agent State
    isAgentExpanded: agentState?.isExpanded || false,
    isAgentConnected: agentState?.isConnected || false,
    agentMessages: agentState?.messages || [],
    processingRequest: agentState?.processingRequest || false,

    // Actions
    toggleAgent,
    expandAgent,
    collapseAgent,
    sendContextualMessage,
    handleContextualFileUpload,
    executeQuickAction,
    navigateWithContext,

    // Component Props
    getAgentPanelProps,
    getAgentFABProps,

    // Capability Checks
    canPerformAction,
    getAvailableCapabilities,

    // Utilities
    extractPageData: () => {
      return Object.entries(currentPageConfig.dataExtractors).reduce(
        (acc, [key, extractor]) => {
          try {
            acc[key] = extractor();
          } catch (error) {
            acc[key] = null;
          }
          return acc;
        },
        {} as Record<string, unknown>,
      );
    },
  };
};

export default useAgentIntegration;
