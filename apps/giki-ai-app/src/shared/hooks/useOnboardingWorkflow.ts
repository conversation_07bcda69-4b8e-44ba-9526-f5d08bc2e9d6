import { useState, useCallback } from 'react';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  startComprehensiveOnboarding,
  getOnboardingStatus,
  isOnboardingError,
  OnboardingStatusResponse,
} from '@/features/files/services/fileService';
import {
  uploadFile,
  getSchemaInterpretation,
} from '@/features/files/services/uploadService';

export interface OnboardingWorkflowState {
  // Workflow state
  workflowStep: string;
  onboardingStatus: OnboardingStatusResponse | null;

  // Upload state
  uploadedFiles: Array<{ file: File; uploadId: string; currency: string }>;
  isUploading: boolean;
  isProcessing: boolean;

  // Column interpretation state
  isInterpretingColumns: boolean;
  columnInterpretationResults: Record<string, unknown>;
  interpretedColumns: Record<string, unknown>;
  userConfirmedColumns: boolean;

  // File processing state
  uploadResponse: unknown;
  uploadProgress: number;
  fileColumns: string[];
  columnMapping: Record<string, string | null>;
  processedResult: unknown;
}

export const useOnboardingWorkflow = () => {
  const { toast } = useToast();

  // Workflow state
  const [workflowStep, setWorkflowStep] = useState<string>('upload');
  const [onboardingStatus, setOnboardingStatus] =
    useState<OnboardingStatusResponse | null>(null);

  // Upload state
  const [uploadedFiles, setUploadedFiles] = useState<
    Array<{ file: File; uploadId: string; currency: string }>
  >([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  // Column interpretation state
  const [isInterpretingColumns, setIsInterpretingColumns] =
    useState<boolean>(false);
  const [columnInterpretationResults, setColumnInterpretationResults] =
    useState<Record<string, unknown>>({});
  const [interpretedColumns, setInterpretedColumns] = useState<
    Record<string, unknown>
  >({});
  const [userConfirmedColumns, setUserConfirmedColumns] =
    useState<boolean>(false);

  // File processing state
  const [uploadResponse, setUploadResponse] = useState<unknown>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [fileColumns, setFileColumns] = useState<string[]>([]);
  const [columnMapping, setColumnMapping] = useState<
    Record<string, string | null>
  >({});
  const [processedResult, setProcessedResult] = useState<unknown>(null);

  const loadOnboardingStatus = useCallback(
    async (jobId?: string) => {
      if (!jobId) {
        console.warn('No jobId provided to loadOnboardingStatus');
        return;
      }

      try {
        const status = await getOnboardingStatus(jobId);
        if (isOnboardingError(status)) {
          toast({
            title: 'Error Loading Status',
            description: status.message,
            variant: 'destructive',
          });
          return;
        }
        setOnboardingStatus(status);
      } catch (error) {
        console.error('Error loading onboarding status:', error);
        toast({
          title: 'Error',
          description: 'Failed to load onboarding status',
          variant: 'destructive',
        });
      }
    },
    [toast],
  );

  const handleFileUpload = useCallback(
    async (files: Array<{ file: File; currency: string }>) => {
      setIsUploading(true);
      const newUploadedFiles: Array<{
        file: File;
        uploadId: string;
        currency: string;
      }> = [];

      try {
        for (const { file, currency } of files) {
          const response = await uploadFile(file);

          // Check if response is an ApiError
          if ('type' in response && 'message' in response) {
            throw new Error(response.message || 'Upload failed');
          }

          // It's a successful UploadResponse
          newUploadedFiles.push({
            file,
            uploadId: response.upload_id,
            currency,
          });
        }

        setUploadedFiles((prev) => [...prev, ...newUploadedFiles]);
        setWorkflowStep('interpret');

        toast({
          title: 'Files Uploaded',
          description: `Successfully uploaded ${files.length} file(s)`,
        });
      } catch (error) {
        console.error('Error uploading files:', error);
        toast({
          title: 'Upload Error',
          description: 'Failed to upload files. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsUploading(false);
      }
    },
    [toast],
  );

  const performColumnInterpretation = useCallback(async () => {
    if (uploadedFiles.length === 0) return;

    setIsInterpretingColumns(true);
    const results: Record<string, unknown> = {};

    try {
      for (const uploadedFile of uploadedFiles) {
        const interpretation = await getSchemaInterpretation(
          uploadedFile.uploadId,
        );
        results[uploadedFile.uploadId] = interpretation;
      }

      setColumnInterpretationResults(results);
      setInterpretedColumns(results);
      setWorkflowStep('confirm');

      toast({
        title: 'Schema Interpreted',
        description: 'Column mapping has been analyzed',
      });
    } catch (error) {
      console.error('Error interpreting columns:', error);
      toast({
        title: 'Interpretation Error',
        description: 'Failed to interpret column schema',
        variant: 'destructive',
      });
    } finally {
      setIsInterpretingColumns(false);
    }
  }, [uploadedFiles, toast]);

  const handleCompleteOnboarding = useCallback(async () => {
    if (!userConfirmedColumns || uploadedFiles.length === 0) {
      toast({
        title: 'Confirmation Required',
        description: 'Please confirm the column mappings before proceeding',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);

    try {
      const fileData = uploadedFiles.map((file) => ({
        uploadId: file.uploadId,
        currency: file.currency,
        fileName: file?.file?.name,
      }));

      const response = await startComprehensiveOnboarding({ files: fileData });

      if (isOnboardingError(response)) {
        throw new Error(response.message);
      }

      toast({
        title: 'Onboarding Started',
        description: 'Comprehensive onboarding process has been initiated',
      });

      // Refresh status to show progress
      await loadOnboardingStatus();
      setWorkflowStep('processing');
    } catch (error) {
      console.error('Error starting onboarding:', error);
      toast({
        title: 'Onboarding Error',
        description:
          error instanceof Error ? error.message : 'Failed to start onboarding',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [userConfirmedColumns, uploadedFiles, toast, loadOnboardingStatus]);

  const resetWorkflow = useCallback(() => {
    setWorkflowStep('upload');
    setUploadedFiles([]);
    setColumnInterpretationResults({});
    setInterpretedColumns({});
    setUserConfirmedColumns(false);
    setUploadResponse(null);
    setUploadProgress(0);
    setFileColumns([]);
    setColumnMapping({});
    setProcessedResult(null);
    setOnboardingStatus(null);
  }, []);

  const removeUploadedFile = useCallback((uploadId: string) => {
    setUploadedFiles((prev) =>
      prev.filter((file) => file.uploadId !== uploadId),
    );

    // Clean up related state
    setColumnInterpretationResults((prev) => {
      const { [uploadId]: _removed, ...rest } = prev;
      return rest;
    });

    setInterpretedColumns((prev) => {
      const { [uploadId]: _removed, ...rest } = prev;
      return rest;
    });
  }, []);

  return {
    // State
    workflowStep,
    onboardingStatus,
    uploadedFiles,
    isUploading,
    isProcessing,
    isInterpretingColumns,
    columnInterpretationResults,
    interpretedColumns,
    userConfirmedColumns,
    uploadResponse,
    uploadProgress,
    fileColumns,
    columnMapping,
    processedResult,

    // Actions
    loadOnboardingStatus,
    handleFileUpload,
    performColumnInterpretation,
    handleCompleteOnboarding,
    resetWorkflow,
    removeUploadedFile,
    setUserConfirmedColumns,
    setWorkflowStep,
  };
};
