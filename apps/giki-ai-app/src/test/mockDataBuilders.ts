/**
 * Mock Data Builders for giki.ai Tests
 * 
 * Provides builder functions for creating test data with sensible defaults
 * that can be easily overridden for specific test cases.
 */

import type { User } from '@/features/auth/types/user';
import type { Transaction } from '@/features/transactions/types/transaction';
import type { Category } from '@/features/categories/types/category';
import type { FileData } from '@/features/files/types/file';
import type { DashboardMetrics } from '@/features/dashboard/types/dashboard';

// Counter for generating unique IDs
let idCounter = 1;
const getNextId = () => String(idCounter++);

/**
 * User Builder
 */
export const buildUser = (overrides: Partial<User> = {}): User => ({
  id: parseInt(getNextId()),
  email: `user${idCounter}@testcompany.com`,
  full_name: `Test User ${idCounter}`,
  tenant_id: 3,
  is_active: true,
  is_admin: false,
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
  ...overrides,
});

/**
 * Transaction Builder
 */
export const buildTransaction = (overrides: Partial<Transaction> = {}): Transaction => ({
  id: getNextId(),
  date: '2025-01-01',
  description: `Test Transaction ${idCounter}`,
  amount: -100.00,
  vendor: `Test Vendor ${idCounter}`,
  category: 'Office Supplies',
  sub_category: null,
  gl_code: '5100',
  confidence_score: 0.95,
  ai_confidence: 0.95,
  is_categorized: true,
  tenant_id: 3,
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
  ...overrides,
});

/**
 * Category Builder
 */
export const buildCategory = (overrides: Partial<Category> = {}): Category => ({
  id: parseInt(getNextId()),
  name: `Test Category ${idCounter}`,
  parent_id: null,
  gl_code: `${1000 + idCounter}`,
  type: 'expense',
  is_active: true,
  tenant_id: 3,
  transaction_count: 0,
  confidence_threshold: 0.8,
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
  ...overrides,
});

/**
 * File Data Builder
 */
export const buildFileData = (overrides: Partial<FileData> = {}): FileData => ({
  id: getNextId(),
  filename: `test-file-${idCounter}.csv`,
  file_type: 'csv',
  size: 1024 * 10, // 10KB
  status: 'processed',
  transaction_count: 10,
  processed_count: 10,
  categorized_count: 8,
  error_count: 0,
  processing_started_at: '2025-01-01T00:00:00Z',
  processing_completed_at: '2025-01-01T00:05:00Z',
  tenant_id: 3,
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:05:00Z',
  ...overrides,
});

/**
 * Dashboard Metrics Builder
 */
export const buildDashboardMetrics = (overrides: Partial<DashboardMetrics> = {}): DashboardMetrics => ({
  totalTransactions: 100,
  totalIncome: 500000,
  totalExpenses: 150000,
  netProfit: 350000,
  categorizationRate: 85.5,
  timeSaved: 120, // minutes
  categoriesActive: 25,
  exportReadiness: 95,
  recentTransactions: [],
  categoryBreakdown: [],
  monthlyTrends: [],
  ...overrides,
});

/**
 * Build multiple items
 */
export const buildMany = <T>(
  builder: (overrides?: Partial<T>) => T,
  count: number,
  overridesArray: Partial<T>[] = []
): T[] => {
  return Array.from({ length: count }, (_, index) => 
    builder(overridesArray[index] || {})
  );
};

/**
 * Date helpers for test data
 */
export const dateHelpers = {
  today: () => new Date().toISOString().split('T')[0],
  yesterday: () => {
    const date = new Date();
    date.setDate(date.getDate() - 1);
    return date.toISOString().split('T')[0];
  },
  daysAgo: (days: number) => {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date.toISOString().split('T')[0];
  },
  monthsAgo: (months: number) => {
    const date = new Date();
    date.setMonth(date.getMonth() - months);
    return date.toISOString().split('T')[0];
  },
};

/**
 * Common test scenarios
 */
export const testScenarios = {
  // MIS Setup scenario data
  misSetup: () => ({
    categories: [
      buildCategory({ name: 'Revenue', type: 'income', gl_code: '4000' }),
      buildCategory({ name: 'Operating Expenses', type: 'expense', gl_code: '5000' }),
      buildCategory({ name: 'Cost of Goods Sold', type: 'expense', gl_code: '6000' }),
    ],
    transactions: buildMany(buildTransaction, 50, [
      { category: 'Revenue', amount: 10000 },
      { category: 'Operating Expenses', amount: -2000 },
      { category: 'Cost of Goods Sold', amount: -5000 },
    ]),
  }),
  
  // Uncategorized transactions scenario
  uncategorizedTransactions: () => ({
    transactions: buildMany(buildTransaction, 10, 
      Array(10).fill({ category: null, is_categorized: false })
    ),
  }),
  
  // File processing scenario
  fileProcessing: () => ({
    pendingFile: buildFileData({ status: 'pending', processed_count: 0 }),
    processingFile: buildFileData({ status: 'processing', processed_count: 5 }),
    processedFile: buildFileData({ status: 'processed' }),
    errorFile: buildFileData({ status: 'error', error_count: 10 }),
  }),
};

/**
 * Reset ID counter (useful for test isolation)
 */
export const resetIdCounter = () => {
  idCounter = 1;
};