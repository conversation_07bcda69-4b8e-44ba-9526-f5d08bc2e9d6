/**
 * Service Mocks for giki.ai Tests
 * 
 * Provides comprehensive mocks for all services used in the application.
 * These mocks simulate the actual service behavior without making real API calls.
 */

import { vi } from 'vitest';
import { mockApiClient } from './utils';
import { buildUser, buildTransaction, buildCategory, buildFileData, buildDashboardMetrics } from './mockDataBuilders';

/**
 * Auth Service Mocks
 */
export const authServiceMock = {
  login: vi.fn().mockImplementation(async (email: string, password: string) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    if (email === '<EMAIL>' || password === 'wrong') {
      throw new Error('Invalid credentials');
    }
    
    return {
      access_token: 'mock-jwt-token',
      refresh_token: 'mock-refresh-token',
      token_type: 'bearer',
    };
  }),
  
  logout: vi.fn().mockResolvedValue(undefined),
  
  getCurrentUser: vi.fn().mockResolvedValue(buildUser()),
  
  refreshToken: vi.fn().mockImplementation(async (refreshToken: string) => {
    if (!refreshToken) {
      throw new Error('No refresh token provided');
    }
    
    return {
      access_token: 'new-mock-jwt-token',
      refresh_token: 'new-mock-refresh-token',
      token_type: 'bearer',
    };
  }),
  
  register: vi.fn().mockImplementation(async (data: any) => {
    return buildUser({ email: data.email, full_name: data.full_name });
  }),
  
  verifyEmail: vi.fn().mockResolvedValue({ success: true }),
  
  resetPassword: vi.fn().mockResolvedValue({ success: true }),
};

vi.mock('@/features/auth/services/authService', () => authServiceMock);

/**
 * Dashboard Service Mocks
 */
export const dashboardServiceMock = {
  getMetrics: vi.fn().mockResolvedValue(buildDashboardMetrics()),
  
  getRecentActivity: vi.fn().mockResolvedValue({
    transactions: buildTransaction(),
    files: buildFileData(),
  }),
  
  getChartData: vi.fn().mockResolvedValue({
    labels: ['Jan', 'Feb', 'Mar'],
    datasets: [{
      label: 'Revenue',
      data: [10000, 12000, 15000],
    }],
  }),
};

vi.mock('@/features/dashboard/services/dashboardService', () => dashboardServiceMock);

/**
 * Transaction Service Mocks
 */
export const transactionServiceMock = {
  getTransactions: vi.fn().mockImplementation(async (params: any = {}) => {
    const transactions = Array(params.limit || 10).fill(null).map(() => buildTransaction());
    return {
      transactions,
      total: 100,
      page: params.page || 1,
      pages: 10,
    };
  }),
  
  getTransaction: vi.fn().mockImplementation(async (id: string) => {
    return buildTransaction({ id });
  }),
  
  updateTransaction: vi.fn().mockImplementation(async (id: string, data: any) => {
    return buildTransaction({ id, ...data });
  }),
  
  categorizeTransaction: vi.fn().mockImplementation(async (id: string, categoryId: number) => {
    return buildTransaction({ id, category: `Category ${categoryId}`, is_categorized: true });
  }),
  
  bulkCategorize: vi.fn().mockResolvedValue({
    success: 10,
    failed: 0,
  }),
  
  exportTransactions: vi.fn().mockResolvedValue({
    file_url: '/api/v1/exports/test-export.csv',
  }),
};

vi.mock('@/features/transactions/services/transactionService', () => transactionServiceMock);

/**
 * Category Service Mocks
 */
export const categoryServiceMock = {
  getCategories: vi.fn().mockImplementation(async () => {
    return Array(10).fill(null).map(() => buildCategory());
  }),
  
  getCategory: vi.fn().mockImplementation(async (id: number) => {
    return buildCategory({ id });
  }),
  
  createCategory: vi.fn().mockImplementation(async (data: any) => {
    return buildCategory(data);
  }),
  
  updateCategory: vi.fn().mockImplementation(async (id: number, data: any) => {
    return buildCategory({ id, ...data });
  }),
  
  deleteCategory: vi.fn().mockResolvedValue(undefined),
  
  getCategoryTree: vi.fn().mockResolvedValue([
    buildCategory({ name: 'Revenue', children: [
      buildCategory({ name: 'Product Sales', parent_id: 1 }),
      buildCategory({ name: 'Service Revenue', parent_id: 1 }),
    ]}),
  ]),
};

vi.mock('@/features/categories/services/categoryService', () => categoryServiceMock);

/**
 * File Service Mocks
 */
export const fileServiceMock = {
  uploadFile: vi.fn().mockImplementation(async (file: File) => {
    return buildFileData({ 
      filename: file.name,
      size: file.size,
      status: 'processing',
    });
  }),
  
  getFiles: vi.fn().mockImplementation(async () => {
    return Array(5).fill(null).map(() => buildFileData());
  }),
  
  getFile: vi.fn().mockImplementation(async (id: string) => {
    return buildFileData({ id });
  }),
  
  deleteFile: vi.fn().mockResolvedValue(undefined),
  
  getProcessingStatus: vi.fn().mockImplementation(async (id: string) => {
    return {
      status: 'processing',
      progress: 50,
      processed_count: 50,
      total_count: 100,
    };
  }),
};

vi.mock('@/features/files/services/fileService', () => fileServiceMock);

/**
 * WebSocket Event Simulators
 */
export const simulateWebSocketEvent = (eventType: string, data: any, delay = 100) => {
  setTimeout(() => {
    window.dispatchEvent(new CustomEvent(`ws:${eventType}`, { detail: data }));
  }, delay);
};

export const simulateTransactionUpdate = (transaction: any) => {
  simulateWebSocketEvent('transaction.updated', { transaction });
};

export const simulateFileProcessingComplete = (fileId: string) => {
  simulateWebSocketEvent('file.processed', { 
    file_id: fileId,
    status: 'processed',
    transaction_count: 100,
    categorized_count: 85,
  });
};

/**
 * Error Simulation Helpers
 */
export const simulateNetworkError = () => {
  mockApiClient.get.mockRejectedValueOnce(new Error('Network error'));
  mockApiClient.post.mockRejectedValueOnce(new Error('Network error'));
};

export const simulateAuthError = () => {
  mockApiClient.get.mockRejectedValueOnce({
    response: { status: 401, data: { detail: 'Unauthorized' } }
  });
};

export const simulateValidationError = (errors: Record<string, string[]>) => {
  mockApiClient.post.mockRejectedValueOnce({
    response: { 
      status: 422, 
      data: { 
        detail: 'Validation failed',
        errors,
      },
    },
  });
};

/**
 * Reset all service mocks
 */
export const resetServiceMocks = () => {
  Object.values(authServiceMock).forEach(mock => mock.mockReset());
  Object.values(dashboardServiceMock).forEach(mock => mock.mockReset());
  Object.values(transactionServiceMock).forEach(mock => mock.mockReset());
  Object.values(categoryServiceMock).forEach(mock => mock.mockReset());
  Object.values(fileServiceMock).forEach(mock => mock.mockReset());
};