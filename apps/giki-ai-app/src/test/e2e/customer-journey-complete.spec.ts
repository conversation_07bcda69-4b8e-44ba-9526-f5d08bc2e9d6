/**
 * E2E Customer Journey Tests
 * Complete end-to-end testing of the giki.ai MIS platform customer journey
 */

import { test, expect, Page } from '@playwright/test';
import { join } from 'path';

// Test data file paths
const TEST_DATA_DIR = '../../../../libs/test-data';
const BANK_CSV_FILE = join(__dirname, TEST_DATA_DIR, 'synthetic-bank-data/hdfc_bank_transactions.csv');
const CREDIT_CARD_CSV_FILE = join(__dirname, TEST_DATA_DIR, 'synthetic-bank-data/axis_bank_credit_card.csv');

// Test user credentials
const TEST_CREDENTIALS = {
  owner: {
    email: '<EMAIL>',
    password: 'GikiTest2025Secure',
  },
  manager: {
    email: '<EMAIL>', 
    password: 'GikiTest2025Secure',
  },
  employee: {
    email: '<EMAIL>',
    password: 'GikiTest2025Secure',
  },
};

class CustomerJourneyHelper {
  constructor(private page: Page) {}

  async login(userType: 'owner' | 'manager' | 'employee') {
    const credentials = TEST_CREDENTIALS[userType];
    
    await this.page.goto('/login');
    await this.page.fill('[data-testid="email-input"]', credentials.email);
    await this.page.fill('[data-testid="password-input"]', credentials.password);
    await this.page.click('[data-testid="login-button"]');
    
    // Wait for successful login
    await this.page.waitForURL('/dashboard');
    await expect(this.page.locator('[data-testid="user-menu"]')).toBeVisible();
  }

  async uploadFile(filePath: string) {
    await this.page.goto('/upload');
    
    // Upload file via file input
    const fileInput = this.page.locator('input[type="file"]');
    await fileInput.setInputFiles(filePath);
    
    // Wait for upload to complete
    await expect(this.page.locator('[data-testid="upload-success"]')).toBeVisible({ timeout: 30000 });
  }

  async waitForProcessingComplete() {
    // Wait for WebSocket processing updates
    await expect(this.page.locator('[data-testid="processing-complete"]')).toBeVisible({ timeout: 60000 });
  }

  async verifyDashboardMetrics() {
    await this.page.goto('/dashboard');
    
    // Verify key metrics are displayed
    await expect(this.page.locator('[data-testid="total-transactions"]')).toContainText(/\d+/);
    await expect(this.page.locator('[data-testid="categorization-rate"]')).toContainText(/%/);
    await expect(this.page.locator('[data-testid="export-readiness"]')).toContainText(/%/);
  }

  async reviewTransactions() {
    await this.page.goto('/transactions/review');
    
    // Wait for transactions to load
    await expect(this.page.locator('[data-testid="transaction-table"]')).toBeVisible();
    
    // Verify transaction details
    await expect(this.page.locator('[data-testid="transaction-row"]').first()).toBeVisible();
  }

  async exportTransactions(format: 'quickbooks' | 'xero' | 'csv' | 'tally') {
    await this.page.goto('/transactions/review');
    
    // Click export button
    await this.page.click('[data-testid="export-button"]');
    
    // Select format
    await this.page.click(`[data-testid="export-format-${format}"]`);
    
    // Wait for export to complete
    await expect(this.page.locator('[data-testid="export-success"]')).toBeVisible({ timeout: 30000 });
  }
}

test.describe('Complete Customer Journey - MIS Setup in 5 Minutes', () => {
  let helper: CustomerJourneyHelper;

  test.beforeEach(async ({ page }) => {
    helper = new CustomerJourneyHelper(page);
  });

  test('Owner completes full MIS setup journey', async ({ page }) => {
    // Step 1: Login as owner
    await helper.login('owner');
    
    // Step 2: Verify dashboard shows empty state for new tenant
    await helper.verifyDashboardMetrics();
    await expect(page.locator('[data-testid="onboarding-banner"]')).toBeVisible();
    
    // Step 3: Upload bank statement
    await helper.uploadFile(BANK_CSV_FILE);
    
    // Step 4: Wait for processing to complete
    await helper.waitForProcessingComplete();
    
    // Step 5: Verify categorization achieved baseline accuracy (87%+)
    await helper.verifyDashboardMetrics();
    await expect(page.locator('[data-testid="categorization-rate"]')).toContainText(/8[7-9]%|9[0-9]%/);
    
    // Step 6: Review transactions
    await helper.reviewTransactions();
    
    // Step 7: Verify export readiness
    await expect(page.locator('[data-testid="export-readiness"]')).toContainText(/[7-9][0-9]%/);
    
    // Step 8: Export to QuickBooks
    await helper.exportTransactions('quickbooks');
    
    // Verify completion time (should be under 5 minutes for this test)
    const testDuration = Date.now() - test.info().startTime;
    expect(testDuration).toBeLessThan(5 * 60 * 1000); // 5 minutes
  });

  test('Progressive enhancement detection and application', async ({ page }) => {
    // Start with baseline setup
    await helper.login('owner');
    await helper.uploadFile(BANK_CSV_FILE);
    await helper.waitForProcessingComplete();
    
    // Verify baseline accuracy
    await helper.verifyDashboardMetrics();
    const baselineAccuracy = await page.locator('[data-testid="categorization-rate"]').textContent();
    
    // Upload additional historical data
    await helper.uploadFile(CREDIT_CARD_CSV_FILE);
    await helper.waitForProcessingComplete();
    
    // Verify enhancement notification
    await expect(page.locator('[data-testid="enhancement-notification"]')).toBeVisible();
    await expect(page.locator('[data-testid="enhancement-type"]')).toContainText(/historical/i);
    
    // Apply enhancement
    await page.click('[data-testid="apply-enhancement"]');
    
    // Wait for enhancement processing
    await expect(page.locator('[data-testid="enhancement-complete"]')).toBeVisible({ timeout: 60000 });
    
    // Verify improved accuracy (15-20% improvement)
    await helper.verifyDashboardMetrics();
    const enhancedAccuracy = await page.locator('[data-testid="categorization-rate"]').textContent();
    
    // Should show improvement
    expect(enhancedAccuracy).not.toBe(baselineAccuracy);
  });
});

test.describe('Role-Based Access and Permissions', () => {
  let helper: CustomerJourneyHelper;

  test.beforeEach(async ({ page }) => {
    helper = new CustomerJourneyHelper(page);
  });

  test('Manager role has appropriate access', async ({ page }) => {
    await helper.login('manager');
    
    // Should access dashboard and transactions
    await helper.verifyDashboardMetrics();
    await helper.reviewTransactions();
    
    // Should be able to export
    await helper.uploadFile(BANK_CSV_FILE);
    await helper.waitForProcessingComplete();
    await helper.exportTransactions('csv');
    
    // Should not have admin settings access
    await page.goto('/settings/admin');
    await expect(page.locator('[data-testid="access-denied"]')).toBeVisible();
  });

  test('Employee role has limited access', async ({ page }) => {
    await helper.login('employee');
    
    // Should access dashboard (read-only)
    await helper.verifyDashboardMetrics();
    
    // Should not be able to upload files
    await page.goto('/upload');
    await expect(page.locator('[data-testid="upload-disabled"]')).toBeVisible();
    
    // Should not access export functionality
    await page.goto('/transactions/review');
    await expect(page.locator('[data-testid="export-button"]')).not.toBeVisible();
  });
});

test.describe('Data Quality and Export Validation', () => {
  let helper: CustomerJourneyHelper;

  test.beforeEach(async ({ page }) => {
    helper = new CustomerJourneyHelper(page);
    await helper.login('owner');
  });

  test('Export formats maintain data integrity', async ({ page }) => {
    // Upload and process transactions
    await helper.uploadFile(BANK_CSV_FILE);
    await helper.waitForProcessingComplete();
    
    // Test each export format
    const formats = ['quickbooks', 'xero', 'csv', 'tally'] as const;
    
    for (const format of formats) {
      await helper.exportTransactions(format);
      
      // Verify export completed successfully
      await expect(page.locator('[data-testid="export-success"]')).toBeVisible();
      
      // Verify download initiated
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="download-file"]');
      const download = await downloadPromise;
      
      // Verify file name contains format
      expect(download.suggestedFilename()).toContain(format);
    }
  });

  test('Categorization accuracy meets MIS standards', async ({ page }) => {
    await helper.uploadFile(BANK_CSV_FILE);
    await helper.waitForProcessingComplete();
    
    // Verify accuracy meets 87% baseline
    const accuracyText = await page.locator('[data-testid="categorization-rate"]').textContent();
    const accuracy = parseFloat(accuracyText?.replace('%', '') || '0');
    expect(accuracy).toBeGreaterThanOrEqual(87);
    
    // Verify all transactions have categories
    await helper.reviewTransactions();
    const uncategorizedCount = await page.locator('[data-testid="uncategorized-transaction"]').count();
    expect(uncategorizedCount).toBeLessThan(5); // Less than 5% uncategorized
  });
});

test.describe('Real-time Features and WebSocket Integration', () => {
  let helper: CustomerJourneyHelper;

  test.beforeEach(async ({ page }) => {
    helper = new CustomerJourneyHelper(page);
    await helper.login('owner');
  });

  test('File processing shows real-time progress', async ({ page }) => {
    await page.goto('/upload');
    
    // Start file upload
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(BANK_CSV_FILE);
    
    // Verify progress indicators appear
    await expect(page.locator('[data-testid="progress-bar"]')).toBeVisible();
    await expect(page.locator('[data-testid="processing-status"]')).toContainText(/processing/i);
    
    // Verify progress updates
    await expect(page.locator('[data-testid="progress-percentage"]')).toContainText(/%/);
    
    // Wait for completion
    await helper.waitForProcessingComplete();
    await expect(page.locator('[data-testid="processing-status"]')).toContainText(/complete/i);
  });

  test('Multiple users can work simultaneously', async ({ browser }) => {
    // Create two browser contexts for concurrent users
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    const helper1 = new CustomerJourneyHelper(page1);
    const helper2 = new CustomerJourneyHelper(page2);
    
    // Login as different users
    await helper1.login('owner');
    await helper2.login('manager');
    
    // Both should be able to access dashboard
    await Promise.all([
      helper1.verifyDashboardMetrics(),
      helper2.verifyDashboardMetrics(),
    ]);
    
    // Owner uploads file
    await helper1.uploadFile(BANK_CSV_FILE);
    
    // Manager should see updates via WebSocket
    await expect(page2.locator('[data-testid="processing-notification"]')).toBeVisible();
    
    await context1.close();
    await context2.close();
  });
});

test.describe('Performance and Load Testing', () => {
  let helper: CustomerJourneyHelper;

  test.beforeEach(async ({ page }) => {
    helper = new CustomerJourneyHelper(page);
    await helper.login('owner');
  });

  test('Large file processing completes within reasonable time', async ({ page }) => {
    const startTime = Date.now();
    
    // Upload large file (should be processed within 2 minutes)
    await helper.uploadFile(BANK_CSV_FILE);
    await helper.waitForProcessingComplete();
    
    const processingTime = Date.now() - startTime;
    expect(processingTime).toBeLessThan(2 * 60 * 1000); // 2 minutes max
    
    // Verify data quality maintained
    await helper.verifyDashboardMetrics();
    const accuracyText = await page.locator('[data-testid="categorization-rate"]').textContent();
    const accuracy = parseFloat(accuracyText?.replace('%', '') || '0');
    expect(accuracy).toBeGreaterThanOrEqual(85); // Maintain quality under load
  });

  test('Dashboard loads quickly with large datasets', async ({ page }) => {
    // Upload multiple files to create large dataset
    await helper.uploadFile(BANK_CSV_FILE);
    await helper.waitForProcessingComplete();
    
    // Measure dashboard load time
    const startTime = Date.now();
    await page.goto('/dashboard');
    await helper.verifyDashboardMetrics();
    const loadTime = Date.now() - startTime;
    
    expect(loadTime).toBeLessThan(3000); // Under 3 seconds
  });
});

test.describe('Error Handling and Recovery', () => {
  let helper: CustomerJourneyHelper;

  test.beforeEach(async ({ page }) => {
    helper = new CustomerJourneyHelper(page);
    await helper.login('owner');
  });

  test('Handles network interruptions gracefully', async ({ page }) => {
    // Start file upload
    await page.goto('/upload');
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(BANK_CSV_FILE);
    
    // Simulate network interruption
    await page.route('**/api/**', route => route.abort());
    
    // Should show error state
    await expect(page.locator('[data-testid="network-error"]')).toBeVisible();
    
    // Restore network
    await page.unroute('**/api/**');
    
    // Should recover automatically
    await expect(page.locator('[data-testid="connection-restored"]')).toBeVisible();
  });

  test('Validates file formats and shows helpful errors', async ({ page }) => {
    await page.goto('/upload');
    
    // Try to upload invalid file type
    const invalidFile = join(__dirname, '../../../../package.json');
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(invalidFile);
    
    // Should show format error
    await expect(page.locator('[data-testid="format-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="supported-formats"]')).toContainText(/csv|excel|qif/);
  });
});