/**
 * Customer Journey Tests - Authentication Flow
 * Tests the complete authentication journey from login to MIS setup
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { 
  render, 
  mockFetch, 
  setupUnauthenticatedState, 
  setupAuthenticatedState,
  mockServiceResponses,
  resetAllMocks,
  authStoreMock 
} from '@/test/utils';
import LoginForm from '@/features/auth/components/LoginForm';
import DashboardPage from '@/features/dashboard/pages/DashboardPage';

describe('Customer Journey - Authentication Flow', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('1. Initial Login Experience', () => {
    it('should complete full login journey with valid credentials', async () => {
      const user = userEvent.setup();
      setupUnauthenticatedState();
      
      // Mock successful auth flow
      mockServiceResponses.login('<EMAIL>', 'GikiTest2025Secure');
      
      render(<LoginForm />);
      
      // Step 1: User sees login form
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
      
      // Step 2: User enters credentials
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/password/i), 'GikiTest2025Secure');
      
      // Step 3: User submits form
      await user.click(screen.getByRole('button', { name: /sign in/i }));
      
      // Step 4: Verify authentication API calls
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/auth/token'),
          expect.objectContaining({
            method: 'POST',
            headers: expect.objectContaining({
              'Content-Type': 'application/x-www-form-urlencoded',
            }),
          })
        );
      });
    });

    it('should handle authentication errors gracefully', async () => {
      const user = userEvent.setup();
      setupUnauthenticatedState();
      
      // Mock failed authentication
      mockFetch({ detail: 'Invalid email or password' }, false, 401);
      
      render(<LoginForm />);
      
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/password/i), 'wrongpassword');
      await user.click(screen.getByRole('button', { name: /sign in/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/login failed|authentication failed/i)).toBeInTheDocument();
      });
      
      // Verify user remains unauthenticated
      expect(authStoreMock.getState().isAuthenticated).toBe(false);
    });

    it('should persist authentication with remember me option', async () => {
      const user = userEvent.setup();
      setupUnauthenticatedState();
      
      mockServiceResponses.login('<EMAIL>', 'GikiTest2025Secure');
      
      render(<LoginForm />);
      
      // Enable remember me
      const rememberMeCheckbox = screen.getByRole('checkbox', { name: /keep me signed in/i });
      await user.click(rememberMeCheckbox);
      expect(rememberMeCheckbox).toBeChecked();
      
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/password/i), 'GikiTest2025Secure');
      await user.click(screen.getByRole('button', { name: /sign in/i }));
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled();
      });
    });
  });

  describe('2. Post-Login Dashboard Access', () => {
    it('should redirect authenticated user to dashboard with metrics', async () => {
      setupAuthenticatedState();
      mockServiceResponses.dashboardMetrics();
      
      render(<DashboardPage />);
      
      // Verify dashboard loads with authenticated state
      expect(authStoreMock.getState().isAuthenticated).toBe(true);
      expect(authStoreMock.getState().userId).toBe('16');
      expect(authStoreMock.getState().tenantId).toBe('3');
      
      // Check for dashboard content
      await waitFor(() => {
        // Should show some dashboard content
        expect(screen.getByText(/dashboard|overview|metrics/i)).toBeInTheDocument();
      });
    });

    it('should handle token refresh seamlessly', async () => {
      setupAuthenticatedState();
      
      // Mock token refresh scenario
      mockServiceResponses.refreshToken();
      
      // Simulate expired token scenario
      mockFetch({ detail: 'Token expired' }, false, 401);
      
      render(<DashboardPage />);
      
      // Component should attempt to refresh token
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled();
      });
    });
  });

  describe('3. Session Management', () => {
    it('should handle logout flow completely', async () => {
      setupAuthenticatedState();
      
      // Simulate logout action
      authStoreMock.getState().logout();
      
      // Verify state is cleared
      expect(authStoreMock.getState().isAuthenticated).toBe(false);
      expect(authStoreMock.getState().token).toBe(null);
      expect(authStoreMock.getState().userId).toBe(null);
      expect(authStoreMock.getState().tenantId).toBe(null);
    });

    it('should maintain session across page refreshes', async () => {
      setupAuthenticatedState();
      
      // Simulate page refresh by checking auth
      authStoreMock.getState().checkAuth();
      
      // Auth state should persist
      expect(authStoreMock.getState().isAuthenticated).toBe(true);
      expect(authStoreMock.getState().userId).toBe('16');
    });
  });

  describe('4. MIS Onboarding Flow', () => {
    it('should guide new user through 5-minute MIS setup', async () => {
      setupAuthenticatedState();
      
      // Mock new tenant with no data
      mockServiceResponses.dashboardMetrics({
        totalTransactions: 0,
        totalIncome: 0,
        totalExpenses: 0,
        netProfit: 0,
        categorizationRate: 0,
        timeSaved: 0,
        categoriesActive: 0,
        exportReadiness: 0,
      });
      
      render(<DashboardPage />);
      
      await waitFor(() => {
        // Should show onboarding or empty state for new users
        expect(screen.getByText(/upload|get started|welcome/i)).toBeInTheDocument();
      });
    });

    it('should show progress indicators for MIS setup', async () => {
      setupAuthenticatedState();
      
      // Mock partial setup state
      mockServiceResponses.dashboardMetrics({
        totalTransactions: 15,
        totalIncome: 645000,
        totalExpenses: 144800,
        netProfit: 500200,
        categorizationRate: 87.5, // Baseline accuracy achieved
        timeSaved: 5, // 5 minutes saved
        categoriesActive: 12,
        exportReadiness: 60, // Partial readiness
      });
      
      render(<DashboardPage />);
      
      await waitFor(() => {
        // Should show progress indicators
        expect(screen.getByText(/87\.5%|categorization/i)).toBeInTheDocument();
      });
    });
  });

  describe('5. Role-Based Access', () => {
    it('should handle owner role permissions', async () => {
      setupAuthenticatedState({
        user: {
          id: 16,
          email: '<EMAIL>',
          tenant_id: 3,
          is_active: true,
          is_admin: true, // Owner role
          full_name: 'Test Owner',
        },
      });
      
      mockServiceResponses.dashboardMetrics();
      
      render(<DashboardPage />);
      
      // Owner should have full access
      expect(authStoreMock.getState().user?.is_admin).toBe(true);
    });

    it('should handle employee role permissions', async () => {
      setupAuthenticatedState({
        user: {
          id: 17,
          email: '<EMAIL>',
          tenant_id: 3,
          is_active: true,
          is_admin: false, // Employee role
          full_name: 'Test Employee',
        },
      });
      
      mockServiceResponses.dashboardMetrics();
      
      render(<DashboardPage />);
      
      // Employee should have limited access
      expect(authStoreMock.getState().user?.is_admin).toBe(false);
    });
  });

  describe('6. Error Recovery', () => {
    it('should recover from network errors', async () => {
      const user = userEvent.setup();
      setupUnauthenticatedState();
      
      // Simulate network error followed by success
      global.fetch = vi.fn()
        .mockRejectedValueOnce(new Error('Network Error'))
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: () => Promise.resolve({
            access_token: 'mock-token',
            refresh_token: 'mock-refresh-token',
          }),
        });
      
      render(<LoginForm />);
      
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/password/i), 'GikiTest2025Secure');
      await user.click(screen.getByRole('button', { name: /sign in/i }));
      
      // Should show error state
      await waitFor(() => {
        expect(screen.getByText(/error|failed/i)).toBeInTheDocument();
      });
    });

    it('should handle server errors gracefully', async () => {
      const user = userEvent.setup();
      setupUnauthenticatedState();
      
      mockFetch({ detail: 'Internal server error' }, false, 500);
      
      render(<LoginForm />);
      
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/password/i), 'GikiTest2025Secure');
      await user.click(screen.getByRole('button', { name: /sign in/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/error|failed/i)).toBeInTheDocument();
      });
    });
  });
});