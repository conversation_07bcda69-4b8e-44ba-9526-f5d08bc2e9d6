/**
 * Authentication Flow Integration Tests
 * Tests complete user authentication journey from login to dashboard access
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { setupServer } from 'msw/node';
import { rest } from 'msw';
import App from '@/core/app/App';

// Mock API server
const server = setupServer(
  // Login endpoint
  rest.post('/api/v1/auth/token', (req, res, ctx) => {
    return res(
      ctx.json({
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        token_type: 'bearer',
        expires_in: 3600,
      })
    );
  }),
  
  // User info endpoint
  rest.get('/api/v1/auth/me', (req, res, ctx) => {
    const authHeader = req.headers.get('Authorization');
    if (authHeader !== 'Bearer mock-access-token') {
      return res(ctx.status(401));
    }
    
    return res(
      ctx.json({
        id: 16,
        email: '<EMAIL>',
        tenant_id: 3,
        full_name: 'Test Owner',
        is_active: true,
        is_admin: false,
        organization_name: 'Test Company',
        created_at: '2024-01-01T00:00:00Z',
      })
    );
  }),
  
  // Dashboard data endpoint
  rest.get('/api/v1/dashboard/overview', (req, res, ctx) => {
    const authHeader = req.headers.get('Authorization');
    if (authHeader !== 'Bearer mock-access-token') {
      return res(ctx.status(401));
    }
    
    return res(
      ctx.json({
        metrics: {
          totalIncome: 150000,
          totalExpenses: 85000,
          netIncome: 65000,
          totalTransactions: 1250,
          categorizedTransactions: 1125,
          uncategorizedTransactions: 125,
          avgTransactionAmount: 120.00,
          topCategory: 'Professional Services',
          topCategoryAmount: 25000.00,
        },
        recentTransactions: [],
        categoryBreakdown: [],
        monthlyTrends: [],
        processingStatus: {
          totalFiles: 10,
          processedFiles: 8,
          pendingFiles: 2,
          failedFiles: 0,
          isProcessing: false,
        },
        quickActions: [],
        dateRange: {
          from: '2024-01-01',
          to: '2024-01-31',
        },
      })
    );
  })
);

// Start server before tests
beforeEach(() => server.listen());
afterEach(() => server.resetHandlers());
afterEach(() => server.close());

describe('Authentication Flow Integration', () => {
  beforeEach(() => {
    // Clear localStorage
    localStorage.clear();
    
    // Reset any auth state
    window.history.pushState({}, 'Login', '/login');
  });

  it('completes full login flow from unauthenticated to dashboard', async () => {
    const user = userEvent.setup();
    
    // Render the app
    render(<App />);
    
    // Should redirect to login page
    await waitFor(() => {
      expect(screen.getByText(/sign in to your account/i)).toBeInTheDocument();
    });
    
    // Fill in login form
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'GikiTest2025Secure');
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    // Wait for redirect to dashboard
    await waitFor(() => {
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
    }, { timeout: 5000 });
    
    // Verify dashboard content is loaded
    expect(screen.getByText(/total income/i)).toBeInTheDocument();
    expect(screen.getByText(/\$150k/i)).toBeInTheDocument();
    
    // Verify user info is displayed
    expect(screen.getByText(/test owner/i)).toBeInTheDocument();
    
    // Verify auth token is stored
    expect(localStorage.getItem('auth_token')).toBe('mock-access-token');
  });

  it('handles login failure with invalid credentials', async () => {
    const user = userEvent.setup();
    
    // Override login endpoint for failure
    server.use(
      rest.post('/api/v1/auth/token', (req, res, ctx) => {
        return res(
          ctx.status(401),
          ctx.json({
            detail: 'Invalid email or password',
          })
        );
      })
    );
    
    render(<App />);
    
    // Fill in login form with invalid credentials
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'wrongpassword');
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/invalid email or password/i)).toBeInTheDocument();
    });
    
    // Should remain on login page
    expect(screen.getByText(/sign in to your account/i)).toBeInTheDocument();
    
    // Should not store auth token
    expect(localStorage.getItem('auth_token')).toBeNull();
  });

  it('redirects authenticated users away from login page', async () => {
    // Set up authenticated state
    localStorage.setItem('auth_token', 'mock-access-token');
    localStorage.setItem('refresh_token', 'mock-refresh-token');
    
    render(<App />);
    
    // Should redirect to dashboard, not show login
    await waitFor(() => {
      expect(screen.queryByText(/sign in to your account/i)).not.toBeInTheDocument();
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
    });
  });

  it('handles token refresh when access token expires', async () => {
    const user = userEvent.setup();
    
    // Set up expired token scenario
    localStorage.setItem('auth_token', 'expired-token');
    localStorage.setItem('refresh_token', 'mock-refresh-token');
    
    // Mock refresh endpoint
    server.use(
      rest.post('/api/v1/auth/refresh', (req, res, ctx) => {
        return res(
          ctx.json({
            access_token: 'new-access-token',
            refresh_token: 'new-refresh-token',
            token_type: 'bearer',
            expires_in: 3600,
          })
        );
      }),
      
      // Update dashboard endpoint to accept new token
      rest.get('/api/v1/dashboard/overview', (req, res, ctx) => {
        const authHeader = req.headers.get('Authorization');
        if (authHeader === 'Bearer new-access-token') {
          return res(
            ctx.json({
              metrics: {
                totalIncome: 150000,
                totalExpenses: 85000,
                netIncome: 65000,
                totalTransactions: 1250,
                categorizedTransactions: 1125,
                uncategorizedTransactions: 125,
              },
            })
          );
        }
        return res(ctx.status(401));
      })
    );
    
    render(<App />);
    
    // Should automatically refresh token and load dashboard
    await waitFor(() => {
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
    });
    
    // Verify new token is stored
    expect(localStorage.getItem('auth_token')).toBe('new-access-token');
  });

  it('logs out user and clears session', async () => {
    const user = userEvent.setup();
    
    // Start authenticated
    localStorage.setItem('auth_token', 'mock-access-token');
    
    render(<App />);
    
    // Wait for dashboard to load
    await waitFor(() => {
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
    });
    
    // Find and click logout button
    const userMenu = screen.getByRole('button', { name: /user menu/i });
    await user.click(userMenu);
    
    const logoutButton = screen.getByRole('menuitem', { name: /log out/i });
    await user.click(logoutButton);
    
    // Should redirect to login page
    await waitFor(() => {
      expect(screen.getByText(/sign in to your account/i)).toBeInTheDocument();
    });
    
    // Should clear auth tokens
    expect(localStorage.getItem('auth_token')).toBeNull();
    expect(localStorage.getItem('refresh_token')).toBeNull();
  });

  it('persists authentication across page refreshes', async () => {
    // Set up authenticated state
    localStorage.setItem('auth_token', 'mock-access-token');
    localStorage.setItem('refresh_token', 'mock-refresh-token');
    
    const { unmount } = render(<App />);
    
    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
    });
    
    // Simulate page refresh by unmounting and remounting
    unmount();
    render(<App />);
    
    // Should still be authenticated
    await waitFor(() => {
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
    });
    
    // Should not show login page
    expect(screen.queryByText(/sign in to your account/i)).not.toBeInTheDocument();
  });

  it('handles network errors gracefully', async () => {
    const user = userEvent.setup();
    
    // Mock network error
    server.use(
      rest.post('/api/v1/auth/token', (req, res, ctx) => {
        return res.networkError('Failed to connect');
      })
    );
    
    render(<App />);
    
    // Try to login
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'GikiTest2025Secure');
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    // Should show network error message
    await waitFor(() => {
      expect(screen.getByText(/network error|connection failed/i)).toBeInTheDocument();
    });
  });

  it('supports remember me functionality', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Check remember me checkbox
    const rememberMeCheckbox = screen.getByRole('checkbox', { name: /remember me/i });
    await user.click(rememberMeCheckbox);
    
    // Fill in and submit form
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'GikiTest2025Secure');
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    // Wait for dashboard
    await waitFor(() => {
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
    });
    
    // Verify remember me preference is stored
    expect(localStorage.getItem('remember_me')).toBe('true');
  });

  it('handles concurrent authentication requests properly', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Fill in form
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'GikiTest2025Secure');
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    
    // Click submit multiple times quickly
    await user.click(submitButton);
    await user.click(submitButton);
    await user.click(submitButton);
    
    // Should still handle login correctly without errors
    await waitFor(() => {
      expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
    });
    
    // Should only store one token
    expect(localStorage.getItem('auth_token')).toBe('mock-access-token');
  });
});