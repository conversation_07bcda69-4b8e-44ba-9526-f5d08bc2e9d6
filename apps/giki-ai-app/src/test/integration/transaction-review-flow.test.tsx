/**
 * Transaction Review Flow Integration Tests
 * Tests complete transaction review journey from initial results to final approval
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { setupServer } from 'msw/node';
import { rest } from 'msw';
import App from '@/core/app/App';

// Mock API server
const server = setupServer(
  // Auth endpoints
  rest.get('/api/v1/auth/me', (req, res, ctx) => {
    return res(
      ctx.json({
        id: 16,
        email: '<EMAIL>',
        tenant_id: 3,
        full_name: 'Test Owner',
      })
    );
  }),
  
  // Get transactions for review
  rest.get('/api/v1/transactions/review', (req, res, ctx) => {
    const status = req.url.searchParams.get('status');
    
    const transactions = [
      {
        id: 'tx-001',
        date: '2024-01-15',
        description: 'Coffee Shop Payment',
        amount: -45.50,
        category_path: 'Meals & Entertainment/Coffee',
        ai_category_confidence: 0.95,
        status: 'ai_suggested',
        needs_review: false,
      },
      {
        id: 'tx-002',
        date: '2024-01-16',
        description: 'Unknown Vendor',
        amount: -123.45,
        category_path: 'Business Expense',
        ai_category_confidence: 0.45,
        status: 'needs_review',
        needs_review: true,
      },
      {
        id: 'tx-003',
        date: '2024-01-17',
        description: 'Office Supplies Store',
        amount: -89.99,
        category_path: 'Office Expenses/Supplies',
        ai_category_confidence: 0.92,
        status: 'ai_suggested',
        needs_review: false,
      },
    ];
    
    // Filter by status if provided
    const filteredTransactions = status 
      ? transactions.filter(t => t.status === status)
      : transactions;
    
    return res(
      ctx.json({
        transactions: filteredTransactions,
        summary: {
          total: transactions.length,
          needs_review: transactions.filter(t => t.needs_review).length,
          ai_suggested: transactions.filter(t => t.status === 'ai_suggested').length,
          user_approved: 0,
        },
      })
    );
  }),
  
  // Get categories for dropdown
  rest.get('/api/v1/categories', (req, res, ctx) => {
    return res(
      ctx.json({
        categories: [
          {
            id: 1,
            name: 'Office Expenses',
            path: 'Office Expenses',
            children: [
              { id: 11, name: 'Supplies', path: 'Office Expenses/Supplies' },
              { id: 12, name: 'Equipment', path: 'Office Expenses/Equipment' },
            ],
          },
          {
            id: 2,
            name: 'Meals & Entertainment',
            path: 'Meals & Entertainment',
            children: [
              { id: 21, name: 'Coffee', path: 'Meals & Entertainment/Coffee' },
              { id: 22, name: 'Client Meals', path: 'Meals & Entertainment/Client Meals' },
            ],
          },
          {
            id: 3,
            name: 'Professional Services',
            path: 'Professional Services',
            children: [
              { id: 31, name: 'Legal', path: 'Professional Services/Legal' },
              { id: 32, name: 'Consulting', path: 'Professional Services/Consulting' },
            ],
          },
        ],
      })
    );
  }),
  
  // Update transaction category
  rest.put('/api/v1/transactions/:id/category', async (req, res, ctx) => {
    const body = await req.json();
    return res(
      ctx.json({
        id: req.params.id,
        category_path: body.category_path,
        status: 'user_modified',
        updated_at: new Date().toISOString(),
      })
    );
  }),
  
  // Bulk approve transactions
  rest.post('/api/v1/transactions/bulk-approve', async (req, res, ctx) => {
    const body = await req.json();
    return res(
      ctx.json({
        approved_count: body.transaction_ids.length,
        message: `${body.transaction_ids.length} transactions approved`,
      })
    );
  }),
  
  // AI suggestion for transaction
  rest.post('/api/v1/transactions/:id/suggest', (req, res, ctx) => {
    return res(
      ctx.json({
        suggestions: [
          {
            category_path: 'Professional Services/Consulting',
            confidence: 0.89,
            reasoning: 'Similar to previous consulting payments',
          },
          {
            category_path: 'Office Expenses/Services',
            confidence: 0.76,
            reasoning: 'Could be office-related service',
          },
        ],
      })
    );
  })
);

// Start server before tests
beforeEach(() => {
  server.listen();
  localStorage.setItem('auth_token', 'mock-access-token');
});

afterEach(() => {
  server.resetHandlers();
  localStorage.clear();
});

afterEach(() => server.close());

describe('Transaction Review Flow Integration', () => {
  it('completes full transaction review and approval flow', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to transactions page
    const transactionsLink = await screen.findByRole('link', { name: /transactions/i });
    await user.click(transactionsLink);
    
    // Should show transaction table
    await waitFor(() => {
      expect(screen.getByText(/transaction management/i)).toBeInTheDocument();
    });
    
    // Should show transactions needing review
    expect(screen.getByText('Coffee Shop Payment')).toBeInTheDocument();
    expect(screen.getByText('Unknown Vendor')).toBeInTheDocument();
    expect(screen.getByText('Office Supplies Store')).toBeInTheDocument();
    
    // Filter to show only transactions needing review
    const statusFilter = screen.getByLabelText(/filter by status/i);
    await user.selectOptions(statusFilter, 'needs_review');
    
    // Should show only the review transaction
    await waitFor(() => {
      expect(screen.getByText('Unknown Vendor')).toBeInTheDocument();
      expect(screen.queryByText('Coffee Shop Payment')).not.toBeInTheDocument();
    });
    
    // Click on the transaction needing review
    const reviewRow = screen.getByText('Unknown Vendor').closest('tr');
    const editButton = within(reviewRow!).getByRole('button', { name: /edit/i });
    await user.click(editButton);
    
    // Should open edit modal
    await waitFor(() => {
      expect(screen.getByText(/edit transaction/i)).toBeInTheDocument();
    });
    
    // Change category
    const categorySelect = screen.getByLabelText(/category/i);
    await user.selectOptions(categorySelect, 'Professional Services/Consulting');
    
    // Save changes
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    // Should close modal and update transaction
    await waitFor(() => {
      expect(screen.queryByText(/edit transaction/i)).not.toBeInTheDocument();
    });
    
    // Transaction should be updated
    expect(screen.getByText('Professional Services → Consulting')).toBeInTheDocument();
    
    // Clear filter to see all transactions
    await user.selectOptions(statusFilter, 'all');
    
    // Select multiple transactions for bulk approval
    const checkboxes = screen.getAllByRole('checkbox', { name: /select transaction/i });
    await user.click(checkboxes[0]); // Coffee Shop
    await user.click(checkboxes[2]); // Office Supplies
    
    // Should show bulk actions
    expect(screen.getByText(/2 selected/i)).toBeInTheDocument();
    
    // Bulk approve
    const bulkActionsButton = screen.getByRole('button', { name: /bulk actions/i });
    await user.click(bulkActionsButton);
    
    const approveButton = screen.getByRole('menuitem', { name: /approve selected/i });
    await user.click(approveButton);
    
    // Should show confirmation
    await waitFor(() => {
      expect(screen.getByText(/2 transactions approved/i)).toBeInTheDocument();
    });
  });

  it('handles AI suggestions for difficult categorizations', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to transactions
    const transactionsLink = await screen.findByRole('link', { name: /transactions/i });
    await user.click(transactionsLink);
    
    // Find transaction needing review
    const reviewRow = await screen.findByText('Unknown Vendor');
    const editButton = within(reviewRow.closest('tr')!).getByRole('button', { name: /edit/i });
    await user.click(editButton);
    
    // Click AI suggestion button
    const aiSuggestButton = screen.getByRole('button', { name: /get ai suggestions/i });
    await user.click(aiSuggestButton);
    
    // Should show AI suggestions
    await waitFor(() => {
      expect(screen.getByText(/ai suggestions/i)).toBeInTheDocument();
      expect(screen.getByText('Professional Services → Consulting')).toBeInTheDocument();
      expect(screen.getByText(/89% confidence/i)).toBeInTheDocument();
    });
    
    // Accept first suggestion
    const acceptButton = screen.getByRole('button', { name: /accept suggestion 1/i });
    await user.click(acceptButton);
    
    // Category should be updated
    const categorySelect = screen.getByLabelText(/category/i);
    expect(categorySelect).toHaveValue('Professional Services/Consulting');
    
    // Save the change
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    // Transaction should be updated
    await waitFor(() => {
      expect(screen.getByText('Professional Services → Consulting')).toBeInTheDocument();
    });
  });

  it('supports detailed transaction editing', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate and open edit
    const transactionsLink = await screen.findByRole('link', { name: /transactions/i });
    await user.click(transactionsLink);
    
    const editButton = await screen.findByRole('button', { name: /edit/i });
    await user.click(editButton);
    
    // Should show all editable fields
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/amount/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/date/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
    
    // Edit description
    const descriptionInput = screen.getByLabelText(/description/i);
    await user.clear(descriptionInput);
    await user.type(descriptionInput, 'Local Coffee Shop - Team Meeting');
    
    // Edit amount
    const amountInput = screen.getByLabelText(/amount/i);
    await user.clear(amountInput);
    await user.type(amountInput, '47.85');
    
    // Add notes
    const notesInput = screen.getByLabelText(/notes/i);
    await user.type(notesInput, 'Weekly team meeting expenses');
    
    // Save changes
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    // Should reflect changes
    await waitFor(() => {
      expect(screen.getByText('Local Coffee Shop - Team Meeting')).toBeInTheDocument();
      expect(screen.getByText('$47.85')).toBeInTheDocument();
    });
  });

  it('handles validation errors during transaction updates', async () => {
    const user = userEvent.setup();
    
    // Mock validation error
    server.use(
      rest.put('/api/v1/transactions/:id/category', (req, res, ctx) => {
        return res(
          ctx.status(400),
          ctx.json({
            detail: 'Invalid category selected',
            errors: {
              category: ['Category does not exist'],
            },
          })
        );
      })
    );
    
    render(<App />);
    
    // Navigate and edit
    const transactionsLink = await screen.findByRole('link', { name: /transactions/i });
    await user.click(transactionsLink);
    
    const editButton = await screen.findByRole('button', { name: /edit/i });
    await user.click(editButton);
    
    // Try to save with invalid data
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    // Should show validation error
    await waitFor(() => {
      expect(screen.getByText(/invalid category selected/i)).toBeInTheDocument();
      expect(screen.getByText(/category does not exist/i)).toBeInTheDocument();
    });
    
    // Modal should remain open
    expect(screen.getByText(/edit transaction/i)).toBeInTheDocument();
  });

  it('supports filtering and searching transactions', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to transactions
    const transactionsLink = await screen.findByRole('link', { name: /transactions/i });
    await user.click(transactionsLink);
    
    // Use search function
    const searchInput = screen.getByPlaceholderText(/search transactions/i);
    await user.type(searchInput, 'Coffee');
    
    // Should filter to coffee transaction
    await waitFor(() => {
      expect(screen.getByText('Coffee Shop Payment')).toBeInTheDocument();
      expect(screen.queryByText('Unknown Vendor')).not.toBeInTheDocument();
    });
    
    // Clear search
    await user.clear(searchInput);
    
    // Filter by confidence level
    const confidenceFilter = screen.getByLabelText(/confidence level/i);
    await user.selectOptions(confidenceFilter, 'low');
    
    // Should show only low confidence transactions
    await waitFor(() => {
      expect(screen.getByText('Unknown Vendor')).toBeInTheDocument();
      expect(screen.queryByText('Coffee Shop Payment')).not.toBeInTheDocument();
    });
  });

  it('handles pagination for large transaction sets', async () => {
    const user = userEvent.setup();
    
    // Mock large transaction set
    server.use(
      rest.get('/api/v1/transactions/review', (req, res, ctx) => {
        const page = parseInt(req.url.searchParams.get('page') || '1');
        const limit = parseInt(req.url.searchParams.get('limit') || '50');
        
        // Generate mock transactions
        const allTransactions = Array.from({ length: 150 }, (_, i) => ({
          id: `tx-${i + 1}`,
          date: '2024-01-15',
          description: `Transaction ${i + 1}`,
          amount: -(Math.random() * 1000),
          category_path: 'Business Expense',
          ai_category_confidence: Math.random(),
          status: 'ai_suggested',
        }));
        
        const start = (page - 1) * limit;
        const end = start + limit;
        const pageTransactions = allTransactions.slice(start, end);
        
        return res(
          ctx.json({
            transactions: pageTransactions,
            total: allTransactions.length,
            page,
            total_pages: Math.ceil(allTransactions.length / limit),
          })
        );
      })
    );
    
    render(<App />);
    
    // Navigate to transactions
    const transactionsLink = await screen.findByRole('link', { name: /transactions/i });
    await user.click(transactionsLink);
    
    // Should show pagination controls
    await waitFor(() => {
      expect(screen.getByText(/page 1 of 3/i)).toBeInTheDocument();
    });
    
    // Navigate to next page
    const nextButton = screen.getByRole('button', { name: /next page/i });
    await user.click(nextButton);
    
    // Should update page
    await waitFor(() => {
      expect(screen.getByText(/page 2 of 3/i)).toBeInTheDocument();
    });
    
    // Should show different transactions
    expect(screen.getByText('Transaction 51')).toBeInTheDocument();
  });

  it('preserves review state when navigating between pages', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to transactions and make some changes
    const transactionsLink = await screen.findByRole('link', { name: /transactions/i });
    await user.click(transactionsLink);
    
    // Select a transaction
    const checkbox = await screen.findByRole('checkbox', { name: /select transaction/i });
    await user.click(checkbox);
    
    // Navigate away and back
    const dashboardLink = screen.getByRole('link', { name: /dashboard/i });
    await user.click(dashboardLink);
    
    await user.click(transactionsLink);
    
    // Selection should be preserved
    await waitFor(() => {
      const preservedCheckbox = screen.getByRole('checkbox', { name: /select transaction/i });
      expect(preservedCheckbox).toBeChecked();
    });
  });
});