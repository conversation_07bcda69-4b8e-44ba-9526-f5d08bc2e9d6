/**
 * File Upload Flow Integration Tests
 * Tests complete file upload journey from selection to categorization results
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { setupServer } from 'msw/node';
import { rest } from 'msw';
import App from '@/core/app/App';

// Mock file for testing
const createMockFile = (name: string, content: string, type: string = 'text/csv') => {
  const blob = new Blob([content], { type });
  const file = new File([blob], name, { type });
  return file;
};

// Mock CSV content
const mockCSVContent = `Date,Description,Amount,Category
2024-01-15,Coffee Shop,-45.50,
2024-01-16,Client Payment,2500.00,
2024-01-17,Office Supplies,-89.99,
2024-01-18,Software Subscription,-99.00,`;

// Mock API server
const server = setupServer(
  // Auth endpoints (user is already logged in)
  rest.get('/api/v1/auth/me', (req, res, ctx) => {
    return res(
      ctx.json({
        id: 16,
        email: '<EMAIL>',
        tenant_id: 3,
        full_name: 'Test Owner',
      })
    );
  }),
  
  // File upload endpoint
  rest.post('/api/v1/uploads/files', async (req, res, ctx) => {
    const formData = await req.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return res(ctx.status(400), ctx.json({ detail: 'No file provided' }));
    }
    
    return res(
      ctx.json({
        upload_id: 'upload-123',
        filename: file.name,
        content_type: file.type,
        size: file.size,
        status: 'uploaded',
        headers: ['Date', 'Description', 'Amount', 'Category'],
        transaction_count: 4,
      })
    );
  }),
  
  // Column mapping validation endpoint
  rest.post('/api/v1/uploads/:uploadId/validate-mapping', (req, res, ctx) => {
    return res(
      ctx.json({
        is_valid: true,
        mapped_columns: {
          date: 'Date',
          description: 'Description',
          amount: 'Amount',
          category: 'Category',
        },
        warnings: [],
      })
    );
  }),
  
  // Process file endpoint
  rest.post('/api/v1/uploads/:uploadId/process', (req, res, ctx) => {
    return res(
      ctx.json({
        report_id: 'report-456',
        status: 'processing',
        message: 'File processing started',
      })
    );
  }),
  
  // Processing status endpoint (WebSocket simulation)
  rest.get('/api/v1/reports/:reportId/status', (req, res, ctx) => {
    return res(
      ctx.json({
        status: 'completed',
        progress: 100,
        transactions_processed: 4,
        transactions_categorized: 4,
        accuracy_score: 0.95,
      })
    );
  }),
  
  // Get categorization results
  rest.get('/api/v1/reports/:reportId/results', (req, res, ctx) => {
    return res(
      ctx.json({
        transactions: [
          {
            id: 'tx-001',
            date: '2024-01-15',
            description: 'Coffee Shop',
            amount: -45.50,
            category_path: 'Meals & Entertainment/Coffee',
            ai_confidence: 0.95,
            status: 'categorized',
          },
          {
            id: 'tx-002',
            date: '2024-01-16',
            description: 'Client Payment',
            amount: 2500.00,
            category_path: 'Revenue/Consulting',
            ai_confidence: 0.98,
            status: 'categorized',
          },
          {
            id: 'tx-003',
            date: '2024-01-17',
            description: 'Office Supplies',
            amount: -89.99,
            category_path: 'Office Expenses/Supplies',
            ai_confidence: 0.92,
            status: 'categorized',
          },
          {
            id: 'tx-004',
            date: '2024-01-18',
            description: 'Software Subscription',
            amount: -99.00,
            category_path: 'Software/Development Tools',
            ai_confidence: 0.96,
            status: 'categorized',
          },
        ],
        summary: {
          total_transactions: 4,
          categorized: 4,
          needs_review: 0,
          average_confidence: 0.95,
        },
      })
    );
  })
);

// Start server before tests
beforeEach(() => {
  server.listen();
  // Set up authenticated state
  localStorage.setItem('auth_token', 'mock-access-token');
});

afterEach(() => {
  server.resetHandlers();
  localStorage.clear();
});

afterEach(() => server.close());

describe('File Upload Flow Integration', () => {
  it('completes full file upload and categorization flow', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to upload page
    const uploadLink = await screen.findByRole('link', { name: /upload/i });
    await user.click(uploadLink);
    
    // Wait for upload page to load
    await waitFor(() => {
      expect(screen.getByText(/upload your financial data/i)).toBeInTheDocument();
    });
    
    // Create mock file
    const file = createMockFile('transactions.csv', mockCSVContent);
    
    // Find file input and upload file
    const fileInput = screen.getByLabelText(/drag.*drop.*files/i);
    await user.upload(fileInput, file);
    
    // Should show file preview
    await waitFor(() => {
      expect(screen.getByText('transactions.csv')).toBeInTheDocument();
      expect(screen.getByText(/4 transactions detected/i)).toBeInTheDocument();
    });
    
    // Click continue to column mapping
    const continueButton = screen.getByRole('button', { name: /continue/i });
    await user.click(continueButton);
    
    // Should show column mapping
    await waitFor(() => {
      expect(screen.getByText(/map your columns/i)).toBeInTheDocument();
    });
    
    // Mappings should be auto-detected
    expect(screen.getByText(/date.*→.*date/i)).toBeInTheDocument();
    expect(screen.getByText(/description.*→.*description/i)).toBeInTheDocument();
    expect(screen.getByText(/amount.*→.*amount/i)).toBeInTheDocument();
    
    // Click process button
    const processButton = screen.getByRole('button', { name: /process file/i });
    await user.click(processButton);
    
    // Should show processing status
    await waitFor(() => {
      expect(screen.getByText(/processing your file/i)).toBeInTheDocument();
    });
    
    // Wait for processing to complete
    await waitFor(() => {
      expect(screen.getByText(/processing complete/i)).toBeInTheDocument();
    }, { timeout: 10000 });
    
    // Should show results
    expect(screen.getByText(/4 transactions categorized/i)).toBeInTheDocument();
    expect(screen.getByText(/95% average confidence/i)).toBeInTheDocument();
    
    // Should display categorized transactions
    expect(screen.getByText('Coffee Shop')).toBeInTheDocument();
    expect(screen.getByText('Meals & Entertainment → Coffee')).toBeInTheDocument();
    
    // Should show action buttons
    expect(screen.getByRole('button', { name: /review transactions/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /export results/i })).toBeInTheDocument();
  });

  it('handles file validation errors', async () => {
    const user = userEvent.setup();
    
    // Mock validation error
    server.use(
      rest.post('/api/v1/uploads/files', (req, res, ctx) => {
        return res(
          ctx.status(400),
          ctx.json({
            detail: 'Invalid file format. Please upload a CSV, Excel, or PDF file.',
          })
        );
      })
    );
    
    render(<App />);
    
    // Navigate to upload page
    const uploadLink = await screen.findByRole('link', { name: /upload/i });
    await user.click(uploadLink);
    
    // Create invalid file
    const file = createMockFile('image.jpg', 'invalid content', 'image/jpeg');
    
    // Upload file
    const fileInput = screen.getByLabelText(/drag.*drop.*files/i);
    await user.upload(fileInput, file);
    
    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/invalid file format/i)).toBeInTheDocument();
    });
    
    // Should not proceed to next step
    expect(screen.queryByRole('button', { name: /continue/i })).not.toBeInTheDocument();
  });

  it('handles large file uploads with progress tracking', async () => {
    const user = userEvent.setup();
    
    // Mock slow upload with progress
    let uploadProgress = 0;
    server.use(
      rest.post('/api/v1/uploads/files', async (req, res, ctx) => {
        // Simulate progress updates
        uploadProgress = 100;
        
        return res(
          ctx.delay(1000), // Simulate upload time
          ctx.json({
            upload_id: 'upload-123',
            filename: 'large-file.csv',
            size: 10485760, // 10MB
            status: 'uploaded',
          })
        );
      })
    );
    
    render(<App />);
    
    // Navigate to upload page
    const uploadLink = await screen.findByRole('link', { name: /upload/i });
    await user.click(uploadLink);
    
    // Create large file
    const largeContent = 'Date,Description,Amount\n'.repeat(100000);
    const file = createMockFile('large-file.csv', largeContent);
    
    // Upload file
    const fileInput = screen.getByLabelText(/drag.*drop.*files/i);
    await user.upload(fileInput, file);
    
    // Should show upload progress
    await waitFor(() => {
      expect(screen.getByText(/uploading/i)).toBeInTheDocument();
    });
    
    // Should show progress bar
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    
    // Wait for upload to complete
    await waitFor(() => {
      expect(screen.getByText(/upload complete/i)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('supports drag and drop file upload', async () => {
    render(<App />);
    
    // Navigate to upload page
    const uploadLink = await screen.findByRole('link', { name: /upload/i });
    fireEvent.click(uploadLink);
    
    // Wait for upload area
    const dropZone = await screen.findByText(/drag.*drop.*files/i);
    
    // Create file
    const file = createMockFile('drag-drop.csv', mockCSVContent);
    
    // Simulate drag and drop
    fireEvent.dragEnter(dropZone, {
      dataTransfer: {
        files: [file],
        types: ['Files'],
      },
    });
    
    // Should show drag over state
    await waitFor(() => {
      expect(dropZone).toHaveClass('drag-over');
    });
    
    fireEvent.drop(dropZone, {
      dataTransfer: {
        files: [file],
        types: ['Files'],
      },
    });
    
    // Should upload file
    await waitFor(() => {
      expect(screen.getByText('drag-drop.csv')).toBeInTheDocument();
    });
  });

  it('allows column mapping customization', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate and upload file
    const uploadLink = await screen.findByRole('link', { name: /upload/i });
    await user.click(uploadLink);
    
    const file = createMockFile('custom-columns.csv', mockCSVContent);
    const fileInput = screen.getByLabelText(/drag.*drop.*files/i);
    await user.upload(fileInput, file);
    
    // Continue to mapping
    const continueButton = await screen.findByRole('button', { name: /continue/i });
    await user.click(continueButton);
    
    // Find and change a mapping
    const descriptionMapping = screen.getByLabelText(/map.*description/i);
    await user.selectOptions(descriptionMapping, 'vendor');
    
    // Validate new mapping
    server.use(
      rest.post('/api/v1/uploads/:uploadId/validate-mapping', (req, res, ctx) => {
        return res(
          ctx.json({
            is_valid: true,
            mapped_columns: {
              date: 'Date',
              vendor: 'Description', // Changed mapping
              amount: 'Amount',
            },
            warnings: ['Description column mapped to vendor field'],
          })
        );
      })
    );
    
    // Should show warning
    await waitFor(() => {
      expect(screen.getByText(/description column mapped to vendor/i)).toBeInTheDocument();
    });
    
    // Can still process
    const processButton = screen.getByRole('button', { name: /process file/i });
    expect(processButton).not.toBeDisabled();
  });

  it('handles processing errors gracefully', async () => {
    const user = userEvent.setup();
    
    // Mock processing error
    server.use(
      rest.post('/api/v1/uploads/:uploadId/process', (req, res, ctx) => {
        return res(
          ctx.status(500),
          ctx.json({
            detail: 'Processing failed: Unable to connect to AI service',
          })
        );
      })
    );
    
    render(<App />);
    
    // Upload file and get to processing
    const uploadLink = await screen.findByRole('link', { name: /upload/i });
    await user.click(uploadLink);
    
    const file = createMockFile('error-test.csv', mockCSVContent);
    const fileInput = screen.getByLabelText(/drag.*drop.*files/i);
    await user.upload(fileInput, file);
    
    const continueButton = await screen.findByRole('button', { name: /continue/i });
    await user.click(continueButton);
    
    const processButton = await screen.findByRole('button', { name: /process file/i });
    await user.click(processButton);
    
    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/processing failed/i)).toBeInTheDocument();
      expect(screen.getByText(/unable to connect to ai service/i)).toBeInTheDocument();
    });
    
    // Should show retry button
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
  });

  it('supports multiple file formats', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to upload page
    const uploadLink = await screen.findByRole('link', { name: /upload/i });
    await user.click(uploadLink);
    
    // Test Excel file
    const excelContent = 'Excel file content';
    const excelFile = createMockFile('transactions.xlsx', excelContent, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    
    const fileInput = screen.getByLabelText(/drag.*drop.*files/i);
    await user.upload(fileInput, excelFile);
    
    // Should accept Excel file
    await waitFor(() => {
      expect(screen.getByText('transactions.xlsx')).toBeInTheDocument();
    });
  });

  it('preserves state when navigating between steps', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Upload file
    const uploadLink = await screen.findByRole('link', { name: /upload/i });
    await user.click(uploadLink);
    
    const file = createMockFile('state-test.csv', mockCSVContent);
    const fileInput = screen.getByLabelText(/drag.*drop.*files/i);
    await user.upload(fileInput, file);
    
    // Go to mapping
    const continueButton = await screen.findByRole('button', { name: /continue/i });
    await user.click(continueButton);
    
    // Go back
    const backButton = screen.getByRole('button', { name: /back/i });
    await user.click(backButton);
    
    // File should still be selected
    expect(screen.getByText('state-test.csv')).toBeInTheDocument();
    
    // Go forward again
    await user.click(screen.getByRole('button', { name: /continue/i }));
    
    // Mappings should be preserved
    expect(screen.getByText(/map your columns/i)).toBeInTheDocument();
  });
});