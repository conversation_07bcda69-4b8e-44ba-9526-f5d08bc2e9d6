/**
 * Visual Regression Testing Suite
 * Automated design consistency validation using screenshot comparison
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import path from 'path';
import fs from 'fs';

const SCREENSHOTS_DIR = path.join(process.cwd(), 'test-screenshots');
const BASELINE_DIR = path.join(SCREENSHOTS_DIR, 'baseline');
const CURRENT_DIR = path.join(SCREENSHOTS_DIR, 'current');
const DIFF_DIR = path.join(SCREENSHOTS_DIR, 'diff');

// Test configurations for different pages and states
const VISUAL_TEST_CASES = [
  {
    name: 'login-page',
    url: 'http://localhost:4200/login',
    description: 'Login page layout and styling',
    viewport: { width: 1920, height: 1080 },
  },
  {
    name: 'dashboard-overview',
    url: 'http://localhost:4200/dashboard',
    description: 'Dashboard overview with metrics',
    viewport: { width: 1920, height: 1080 },
    requiresAuth: true,
  },
  {
    name: 'dashboard-mobile',
    url: 'http://localhost:4200/dashboard',
    description: 'Dashboard mobile responsive layout',
    viewport: { width: 375, height: 812 },
    requiresAuth: true,
  },
  {
    name: 'transactions-table',
    url: 'http://localhost:4200/transactions',
    description: 'Transaction table layout and data',
    viewport: { width: 1920, height: 1080 },
    requiresAuth: true,
  },
  {
    name: 'upload-page',
    url: 'http://localhost:4200/upload',
    description: 'File upload interface',
    viewport: { width: 1920, height: 1080 },
    requiresAuth: true,
  },
  {
    name: 'reports-builder',
    url: 'http://localhost:4200/reports',
    description: 'Reports builder interface',
    viewport: { width: 1920, height: 1080 },
    requiresAuth: true,
  },
  {
    name: 'agent-panel-open',
    url: 'http://localhost:4200/dashboard',
    description: 'Dashboard with agent panel open',
    viewport: { width: 1920, height: 1080 },
    requiresAuth: true,
    interactions: ['click-agent-panel'],
  },
];

// Component-specific visual tests
const COMPONENT_TESTS = [
  {
    name: 'dashboard-metrics-grid',
    selector: '[data-testid="dashboard-metrics-grid"]',
    description: 'Dashboard metrics grid component',
  },
  {
    name: 'recent-transactions-list',
    selector: '[data-testid="recent-transactions-list"]',
    description: 'Recent transactions list component',
  },
  {
    name: 'category-breakdown-chart',
    selector: '[data-testid="category-breakdown-chart"]',
    description: 'Category breakdown chart component',
  },
  {
    name: 'professional-agent-panel',
    selector: '[data-testid="professional-agent-panel"]',
    description: 'Professional agent panel component',
  },
];

let browser: Browser;
let page: Page;

describe('Visual Regression Testing', () => {
  beforeAll(async () => {
    // Ensure directories exist
    [SCREENSHOTS_DIR, BASELINE_DIR, CURRENT_DIR, DIFF_DIR].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });

    // Launch browser
    browser = await chromium.launch({
      headless: true,
      args: ['--disable-animations', '--disable-transitions'],
    });
    
    page = await browser.newPage();
    
    // Set up consistent environment
    await page.addInitScript(() => {
      // Disable animations
      const style = document.createElement('style');
      style.textContent = `
        *, *::before, *::after {
          animation-duration: 0s !important;
          animation-delay: 0s !important;
          transition-duration: 0s !important;
          transition-delay: 0s !important;
        }
      `;
      document.head.appendChild(style);
      
      // Mock Date for consistency
      const constantDate = new Date('2024-01-15T10:00:00Z');
      Date.now = () => constantDate.getTime();
    });
  });

  afterAll(async () => {
    await browser?.close();
  });

  describe('Page Layout Tests', () => {
    VISUAL_TEST_CASES.forEach(testCase => {
      it(`should maintain visual consistency for ${testCase.name}`, async () => {
        // Set viewport
        await page.setViewportSize(testCase.viewport);
        
        // Navigate to page
        await page.goto(testCase.url);
        
        // Handle authentication if required
        if (testCase.requiresAuth) {
          await authenticateUser(page);
        }
        
        // Wait for page to be fully loaded
        await page.waitForLoadState('networkidle');
        
        // Perform any required interactions
        if (testCase.interactions) {
          await performInteractions(page, testCase.interactions);
        }
        
        // Wait for any dynamic content to stabilize
        await page.waitForTimeout(1000);
        
        // Take screenshot
        const currentScreenshot = path.join(CURRENT_DIR, `${testCase.name}.png`);
        await page.screenshot({
          path: currentScreenshot,
          fullPage: true,
        });
        
        // Compare with baseline if it exists
        const baselineScreenshot = path.join(BASELINE_DIR, `${testCase.name}.png`);
        
        if (fs.existsSync(baselineScreenshot)) {
          const isMatching = await compareScreenshots(
            baselineScreenshot,
            currentScreenshot,
            path.join(DIFF_DIR, `${testCase.name}-diff.png`)
          );
          
          expect(isMatching).toBe(true);
        } else {
          // Copy current as new baseline
          fs.copyFileSync(currentScreenshot, baselineScreenshot);
          console.log(`Created new baseline for ${testCase.name}`);
        }
      });
    });
  });

  describe('Component Visual Tests', () => {
    beforeAll(async () => {
      // Navigate to dashboard for component tests
      await page.goto('http://localhost:4200/dashboard');
      await authenticateUser(page);
      await page.waitForLoadState('networkidle');
    });

    COMPONENT_TESTS.forEach(component => {
      it(`should maintain visual consistency for ${component.name}`, async () => {
        // Wait for component to be visible
        await page.waitForSelector(component.selector, { state: 'visible' });
        
        // Take component screenshot
        const element = await page.locator(component.selector);
        const currentScreenshot = path.join(CURRENT_DIR, `${component.name}.png`);
        
        await element.screenshot({
          path: currentScreenshot,
        });
        
        // Compare with baseline
        const baselineScreenshot = path.join(BASELINE_DIR, `${component.name}.png`);
        
        if (fs.existsSync(baselineScreenshot)) {
          const isMatching = await compareScreenshots(
            baselineScreenshot,
            currentScreenshot,
            path.join(DIFF_DIR, `${component.name}-diff.png`)
          );
          
          expect(isMatching).toBe(true);
        } else {
          fs.copyFileSync(currentScreenshot, baselineScreenshot);
          console.log(`Created new baseline for ${component.name}`);
        }
      });
    });
  });

  describe('Dark Mode Visual Tests', () => {
    it('should maintain consistency in dark mode', async () => {
      // Enable dark mode
      await page.emulateMedia({ colorScheme: 'dark' });
      
      // Navigate to dashboard
      await page.goto('http://localhost:4200/dashboard');
      await authenticateUser(page);
      await page.waitForLoadState('networkidle');
      
      // Take screenshot
      const currentScreenshot = path.join(CURRENT_DIR, 'dashboard-dark-mode.png');
      await page.screenshot({
        path: currentScreenshot,
        fullPage: true,
      });
      
      // Compare with baseline
      const baselineScreenshot = path.join(BASELINE_DIR, 'dashboard-dark-mode.png');
      
      if (fs.existsSync(baselineScreenshot)) {
        const isMatching = await compareScreenshots(
          baselineScreenshot,
          currentScreenshot,
          path.join(DIFF_DIR, 'dashboard-dark-mode-diff.png')
        );
        
        expect(isMatching).toBe(true);
      } else {
        fs.copyFileSync(currentScreenshot, baselineScreenshot);
        console.log('Created new baseline for dark mode dashboard');
      }
    });
  });

  describe('Loading State Visual Tests', () => {
    it('should show consistent loading states', async () => {
      // Intercept API calls to simulate loading
      await page.route('/api/v1/dashboard/overview', route => {
        // Delay response to capture loading state
        setTimeout(() => {
          route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              metrics: {
                totalIncome: 150000,
                totalExpenses: 85000,
                netIncome: 65000,
                totalTransactions: 1250,
              },
            }),
          });
        }, 2000);
      });
      
      // Navigate to dashboard
      await page.goto('http://localhost:4200/dashboard');
      await authenticateUser(page);
      
      // Capture loading state quickly
      await page.waitForSelector('[data-testid="loading-skeleton"]', { state: 'visible' });
      
      const currentScreenshot = path.join(CURRENT_DIR, 'dashboard-loading.png');
      await page.screenshot({
        path: currentScreenshot,
        fullPage: true,
      });
      
      // Compare with baseline
      const baselineScreenshot = path.join(BASELINE_DIR, 'dashboard-loading.png');
      
      if (fs.existsSync(baselineScreenshot)) {
        const isMatching = await compareScreenshots(
          baselineScreenshot,
          currentScreenshot,
          path.join(DIFF_DIR, 'dashboard-loading-diff.png')
        );
        
        expect(isMatching).toBe(true);
      } else {
        fs.copyFileSync(currentScreenshot, baselineScreenshot);
        console.log('Created new baseline for loading state');
      }
    });
  });

  describe('Error State Visual Tests', () => {
    it('should show consistent error states', async () => {
      // Mock API error
      await page.route('/api/v1/dashboard/overview', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            detail: 'Internal server error',
          }),
        });
      });
      
      // Navigate to dashboard
      await page.goto('http://localhost:4200/dashboard');
      await authenticateUser(page);
      
      // Wait for error state
      await page.waitForSelector('[data-testid="error-message"]', { state: 'visible' });
      
      const currentScreenshot = path.join(CURRENT_DIR, 'dashboard-error.png');
      await page.screenshot({
        path: currentScreenshot,
        fullPage: true,
      });
      
      // Compare with baseline
      const baselineScreenshot = path.join(BASELINE_DIR, 'dashboard-error.png');
      
      if (fs.existsSync(baselineScreenshot)) {
        const isMatching = await compareScreenshots(
          baselineScreenshot,
          currentScreenshot,
          path.join(DIFF_DIR, 'dashboard-error-diff.png')
        );
        
        expect(isMatching).toBe(true);
      } else {
        fs.copyFileSync(currentScreenshot, baselineScreenshot);
        console.log('Created new baseline for error state');
      }
    });
  });
});

/**
 * Authenticate user for protected pages
 */
async function authenticateUser(page: Page) {
  // Check if already authenticated
  const currentUrl = page.url();
  if (currentUrl.includes('/login')) {
    // Fill in login form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'GikiTest2025Secure');
    await page.click('[data-testid="login-submit"]');
    
    // Wait for redirect
    await page.waitForURL('**/dashboard', { timeout: 10000 });
  }
  
  // Set auth token in localStorage for consistency
  await page.evaluate(() => {
    localStorage.setItem('auth_token', 'mock-access-token');
  });
}

/**
 * Perform page interactions for specific test cases
 */
async function performInteractions(page: Page, interactions: string[]) {
  for (const interaction of interactions) {
    switch (interaction) {
      case 'click-agent-panel':
        await page.click('[data-testid="agent-panel-toggle"]');
        await page.waitForSelector('[data-testid="professional-agent-panel"]', { state: 'visible' });
        break;
      case 'open-transaction-filter':
        await page.click('[data-testid="transaction-filter-button"]');
        await page.waitForSelector('[data-testid="filter-dropdown"]', { state: 'visible' });
        break;
      // Add more interactions as needed
    }
    
    // Wait for animation to complete
    await page.waitForTimeout(500);
  }
}

/**
 * Compare two screenshots and generate diff if different
 * This is a simplified version - in production, use a library like pixelmatch
 */
async function compareScreenshots(
  baselinePath: string,
  currentPath: string,
  diffPath: string
): Promise<boolean> {
  // For now, we'll use a simple file size comparison
  // In a real implementation, use proper image comparison
  const baselineStats = fs.statSync(baselinePath);
  const currentStats = fs.statSync(currentPath);
  
  // Allow for small differences in file size (compression variance)
  const sizeDifference = Math.abs(baselineStats.size - currentStats.size);
  const tolerance = baselineStats.size * 0.05; // 5% tolerance
  
  const isMatching = sizeDifference <= tolerance;
  
  if (!isMatching) {
    // In a real implementation, generate visual diff image here
    console.log(`Visual regression detected in ${path.basename(currentPath)}`);
    console.log(`Size difference: ${sizeDifference} bytes (tolerance: ${tolerance})`);
    
    // Copy current as diff for manual inspection
    fs.copyFileSync(currentPath, diffPath);
  }
  
  return isMatching;
}