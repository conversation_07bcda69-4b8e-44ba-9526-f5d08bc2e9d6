/**
 * Visual Testing Setup and Utilities
 * Automated setup for visual regression testing infrastructure
 */

import { describe, it, expect } from 'vitest';
import fs from 'fs';
import path from 'path';

const SCREENSHOTS_DIR = path.join(process.cwd(), 'test-screenshots');
const BASELINE_DIR = path.join(SCREENSHOTS_DIR, 'baseline');
const CURRENT_DIR = path.join(SCREENSHOTS_DIR, 'current');
const DIFF_DIR = path.join(SCREENSHOTS_DIR, 'diff');

/**
 * Visual testing configuration
 */
export interface VisualTestConfig {
  // Screenshot directories
  screenshotsDir: string;
  baselineDir: string;
  currentDir: string;
  diffDir: string;
  
  // Browser settings
  viewport: {
    width: number;
    height: number;
  };
  
  // Comparison settings
  threshold: number; // Pixel difference threshold
  includeDiffImage: boolean;
  
  // Test environment
  baseUrl: string;
  authRequired: boolean;
}

export const DEFAULT_CONFIG: VisualTestConfig = {
  screenshotsDir: SCREENSHOTS_DIR,
  baselineDir: BASELINE_DIR,
  currentDir: CURRENT_DIR,
  diffDir: DIFF_DIR,
  viewport: {
    width: 1920,
    height: 1080,
  },
  threshold: 0.2, // 0.2% pixel difference tolerance
  includeDiffImage: true,
  baseUrl: 'http://localhost:4200',
  authRequired: true,
};

/**
 * Test pages and their expected visual states
 */
export const VISUAL_TEST_PAGES = [
  {
    id: 'login',
    name: 'Login Page',
    path: '/login',
    description: 'User authentication page with form validation',
    authRequired: false,
    variants: [
      { name: 'default', description: 'Default login form' },
      { name: 'error', description: 'Login form with validation errors' },
      { name: 'loading', description: 'Login form in loading state' },
    ],
  },
  {
    id: 'dashboard',
    name: 'Dashboard Overview', 
    path: '/dashboard',
    description: 'Main dashboard with metrics and charts',
    authRequired: true,
    variants: [
      { name: 'default', description: 'Dashboard with sample data' },
      { name: 'empty', description: 'Dashboard with no data' },
      { name: 'loading', description: 'Dashboard loading state' },
      { name: 'error', description: 'Dashboard error state' },
    ],
  },
  {
    id: 'transactions',
    name: 'Transaction Management',
    path: '/transactions',
    description: 'Transaction table with filtering and bulk actions',
    authRequired: true,
    variants: [
      { name: 'default', description: 'Transaction table with data' },
      { name: 'filtered', description: 'Filtered transaction view' },
      { name: 'bulk-selected', description: 'Bulk actions activated' },
      { name: 'empty', description: 'No transactions state' },
    ],
  },
  {
    id: 'upload',
    name: 'File Upload',
    path: '/upload',
    description: 'File upload interface with drag-drop',
    authRequired: true,
    variants: [
      { name: 'default', description: 'Empty upload interface' },
      { name: 'dragover', description: 'Drag over state' },
      { name: 'file-selected', description: 'File selected for upload' },
      { name: 'processing', description: 'File processing state' },
      { name: 'results', description: 'Upload results display' },
    ],
  },
  {
    id: 'reports',
    name: 'Reports Builder',
    path: '/reports',
    description: 'Custom report builder interface',
    authRequired: true,
    variants: [
      { name: 'default', description: 'Report builder default state' },
      { name: 'building', description: 'Report being built' },
      { name: 'preview', description: 'Report preview mode' },
    ],
  },
];

/**
 * Component-specific visual tests
 */
export const VISUAL_TEST_COMPONENTS = [
  {
    id: 'dashboard-metrics',
    name: 'Dashboard Metrics Grid',
    selector: '[data-testid="dashboard-metrics-grid"]',
    description: 'Financial metrics display cards',
    page: '/dashboard',
    variants: [
      { name: 'profit', description: 'Showing profit scenario' },
      { name: 'loss', description: 'Showing loss scenario' },
      { name: 'break-even', description: 'Break even scenario' },
    ],
  },
  {
    id: 'agent-panel',
    name: 'Professional Agent Panel',
    selector: '[data-testid="professional-agent-panel"]',
    description: 'AI agent interaction panel',
    page: '/dashboard',
    variants: [
      { name: 'closed', description: 'Panel in closed state' },
      { name: 'open', description: 'Panel expanded and active' },
      { name: 'conversation', description: 'Active conversation state' },
    ],
  },
  {
    id: 'transaction-table',
    name: 'Transaction Data Table',
    selector: '[data-testid="transaction-table"]',
    description: 'Complex data table with sorting and filtering',
    page: '/transactions',
    variants: [
      { name: 'default', description: 'Standard table view' },
      { name: 'sorted', description: 'Table with column sorting' },
      { name: 'filtered', description: 'Table with active filters' },
    ],
  },
  {
    id: 'upload-zone',
    name: 'File Upload Zone',
    selector: '[data-testid="upload-zone"]',
    description: 'Drag and drop file upload area',
    page: '/upload',
    variants: [
      { name: 'inactive', description: 'Default inactive state' },
      { name: 'hover', description: 'Hover state' },
      { name: 'dragover', description: 'File being dragged over' },
      { name: 'error', description: 'Upload error state' },
    ],
  },
];

/**
 * Responsive breakpoints for testing
 */
export const RESPONSIVE_BREAKPOINTS = [
  { name: 'mobile', width: 375, height: 812 },
  { name: 'tablet', width: 768, height: 1024 },
  { name: 'desktop', width: 1920, height: 1080 },
  { name: 'large', width: 2560, height: 1440 },
];

/**
 * Setup visual testing infrastructure
 */
export function setupVisualTesting(config: Partial<VisualTestConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // Create necessary directories
  const dirs = [
    finalConfig.screenshotsDir,
    finalConfig.baselineDir,
    finalConfig.currentDir,
    finalConfig.diffDir,
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Created directory: ${dir}`);
    }
  });
  
  // Create .gitignore for screenshots
  const gitignorePath = path.join(finalConfig.screenshotsDir, '.gitignore');
  if (!fs.existsSync(gitignorePath)) {
    const gitignoreContent = `# Visual testing artifacts
current/
diff/
*.tmp
*.log

# Keep baseline images
!baseline/
baseline/.gitkeep
`;
    fs.writeFileSync(gitignorePath, gitignoreContent);
    console.log('Created .gitignore for visual testing');
  }
  
  // Create baseline directory keeper
  const keeperPath = path.join(finalConfig.baselineDir, '.gitkeep');
  if (!fs.existsSync(keeperPath)) {
    fs.writeFileSync(keeperPath, '');
  }
  
  return finalConfig;
}

/**
 * Generate visual test documentation
 */
export function generateVisualTestDocs(outputPath: string) {
  const docsContent = `# Visual Regression Testing Documentation

## Overview
This document describes the visual regression testing setup for giki.ai platform.

## Test Coverage

### Pages Tested
${VISUAL_TEST_PAGES.map(page => `
- **${page.name}** (${page.path})
  - ${page.description}
  - Auth Required: ${page.authRequired ? 'Yes' : 'No'}
  - Variants: ${page.variants.map(v => v.name).join(', ')}
`).join('')}

### Components Tested
${VISUAL_TEST_COMPONENTS.map(component => `
- **${component.name}**
  - ${component.description}
  - Selector: \`${component.selector}\`
  - Page: ${component.page}
  - Variants: ${component.variants.map(v => v.name).join(', ')}
`).join('')}

### Responsive Testing
${RESPONSIVE_BREAKPOINTS.map(bp => `
- **${bp.name}**: ${bp.width}x${bp.height}px
`).join('')}

## Running Visual Tests

### Prerequisites
1. Start the development server: \`pnpm serve\`
2. Ensure test database has consistent data
3. Run tests: \`pnpm test:visual\`

### Commands
\`\`\`bash
# Run all visual tests
pnpm test:visual

# Update baselines (run after UI changes)
pnpm test:visual:update

# Run specific page tests
pnpm test:visual --grep "dashboard"

# Run responsive tests
pnpm test:visual:responsive
\`\`\`

### Test Results
- **Baseline**: Reference images stored in \`test-screenshots/baseline/\`
- **Current**: Latest screenshots in \`test-screenshots/current/\`
- **Diffs**: Visual differences in \`test-screenshots/diff/\`

## Updating Baselines
When visual changes are intentional:
1. Review diff images to confirm changes are correct
2. Run \`pnpm test:visual:update\` to update baselines
3. Commit new baseline images to git

## Troubleshooting

### Common Issues
1. **Animations**: Disable CSS animations in test environment
2. **Font Loading**: Ensure web fonts are loaded before screenshots
3. **Dynamic Content**: Mock APIs for consistent data
4. **Timing**: Add appropriate wait conditions for content

### Environment Consistency
- Use fixed viewport sizes
- Disable animations and transitions
- Mock date/time for consistent timestamps
- Use deterministic test data

## CI/CD Integration
Visual tests should run on:
- Pull requests (compare with baseline)
- Main branch (update baselines if needed)
- Release branches (validate no regressions)
`;

  fs.writeFileSync(outputPath, docsContent);
  console.log(`Generated visual test documentation: ${outputPath}`);
}

describe('Visual Testing Setup', () => {
  it('should create necessary directories and files', () => {
    const config = setupVisualTesting();
    
    // Verify directories exist
    expect(fs.existsSync(config.screenshotsDir)).toBe(true);
    expect(fs.existsSync(config.baselineDir)).toBe(true);
    expect(fs.existsSync(config.currentDir)).toBe(true);
    expect(fs.existsSync(config.diffDir)).toBe(true);
    
    // Verify gitignore exists
    const gitignorePath = path.join(config.screenshotsDir, '.gitignore');
    expect(fs.existsSync(gitignorePath)).toBe(true);
  });

  it('should generate documentation', () => {
    const docsPath = path.join(process.cwd(), 'VISUAL-TESTING.md');
    generateVisualTestDocs(docsPath);
    
    expect(fs.existsSync(docsPath)).toBe(true);
    
    const content = fs.readFileSync(docsPath, 'utf-8');
    expect(content).toContain('Visual Regression Testing Documentation');
    expect(content).toContain('Pages Tested');
    expect(content).toContain('Components Tested');
  });
});