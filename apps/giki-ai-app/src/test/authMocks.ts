/**
 * Authentication Mocks for giki.ai Tests
 * 
 * Provides comprehensive mocks for authentication-related functions
 * to prevent token refresh errors during tests.
 */

import { vi } from 'vitest';

// Mock token storage functions
export const mockTokenStorage = {
  getAccessToken: vi.fn().mockReturnValue(null),
  getRefreshToken: vi.fn().mockReturnValue(null),
  setAccessToken: vi.fn(),
  setRefreshToken: vi.fn(),
  clearTokens: vi.fn(),
};

// Mock the auth module
vi.mock('@/features/auth/services/auth', () => ({
  getAccessToken: mockTokenStorage.getAccessToken,
  getRefreshToken: mockTokenStorage.getRefreshToken,
  setAccessToken: mockTokenStorage.setAccessToken,
  setRefreshToken: mockTokenStorage.setRefreshToken,
  clearTokens: mockTokenStorage.clearTokens,
}));

// Mock refresh token function
export const mockRefreshToken = vi.fn().mockImplementation(async (refreshToken: string) => {
  if (!refreshToken) {
    throw new Error('No refresh token available');
  }
  
  // Simulate token refresh
  return {
    accessToken: 'new-mock-access-token',
    refreshToken: 'new-mock-refresh-token',
  };
});

// Mock the authService refresh function
vi.mock('@/features/auth/services/authService', async () => {
  const actual = await vi.importActual('@/features/auth/services/authService');
  return {
    ...actual,
    refreshToken: mockRefreshToken,
  };
});

// Setup authenticated tokens
export const setupAuthenticatedTokens = () => {
  mockTokenStorage.getAccessToken.mockReturnValue('mock-jwt-token');
  mockTokenStorage.getRefreshToken.mockReturnValue('mock-refresh-token');
};

// Clear authenticated tokens
export const clearAuthenticatedTokens = () => {
  mockTokenStorage.getAccessToken.mockReturnValue(null);
  mockTokenStorage.getRefreshToken.mockReturnValue(null);
};

// Simulate token expiration
export const simulateTokenExpiration = () => {
  mockTokenStorage.getAccessToken.mockReturnValue('expired-token');
  mockRefreshToken.mockResolvedValueOnce({
    accessToken: 'refreshed-access-token',
    refreshToken: 'refreshed-refresh-token',
  });
};

// Reset all auth mocks
export const resetAuthMocks = () => {
  Object.values(mockTokenStorage).forEach(mock => mock.mockReset());
  mockRefreshToken.mockReset();
  clearAuthenticatedTokens();
};