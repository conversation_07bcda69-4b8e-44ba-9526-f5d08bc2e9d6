/**
 * Customer Journey Tests - File Upload & Transaction Processing Flow
 * Tests the complete journey from file upload to transaction review and export
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { 
  render, 
  mockFetch, 
  setupAuthenticatedState,
  mockServiceResponses,
  resetAllMocks,
  mockWebSocketService,
  mockUseWebSocket 
} from '@/test/utils';
import { BrowserRouter as Router } from 'react-router-dom';
import UploadPage from '@/features/upload/pages/UploadPage';
import TransactionReviewPage from '@/features/transactions/pages/TransactionReviewPage';

// Mock file for testing
const createMockFile = (name: string, type: string, content: string) => {
  const file = new File([content], name, { type });
  return file;
};

describe('Customer Journey - File Upload & Processing Flow', () => {
  beforeEach(() => {
    resetAllMocks();
    setupAuthenticatedState();
  });

  describe('1. File Upload Experience', () => {
    it('should complete successful file upload with drag and drop', async () => {
      const user = userEvent.setup();
      
      // Mock successful upload response
      mockServiceResponses.fileUpload('test-file-123');
      
      render(
        <Router>
          <UploadPage />
        </Router>
      );
      
      // Find upload area
      const uploadArea = screen.getByText(/drag.*drop|upload/i).closest('div');
      expect(uploadArea).toBeInTheDocument();
      
      // Create mock CSV file
      const csvFile = createMockFile(
        'transactions.csv',
        'text/csv',
        'Date,Description,Amount\n2025-01-01,Test Transaction,-100.00'
      );
      
      // Simulate file drop
      const fileInput = screen.getByLabelText(/choose.*file|browse/i);
      await user.upload(fileInput, csvFile);
      
      await waitFor(() => {
        expect(screen.getByText(/transactions\.csv/i)).toBeInTheDocument();
      });
    });

    it('should validate file format and show preview', async () => {
      const user = userEvent.setup();
      
      render(
        <Router>
          <UploadPage />
        </Router>
      );
      
      // Test with invalid file format
      const invalidFile = createMockFile('document.pdf', 'application/pdf', 'Invalid content');
      
      const fileInput = screen.getByLabelText(/choose.*file|browse/i);
      await user.upload(fileInput, invalidFile);
      
      await waitFor(() => {
        expect(screen.getByText(/invalid.*format|unsupported/i)).toBeInTheDocument();
      });
    });

    it('should handle file upload progress and completion', async () => {
      const user = userEvent.setup();
      
      // Mock progressive upload responses
      mockServiceResponses.fileUpload('upload-123');
      
      render(
        <Router>
          <UploadPage />
        </Router>
      );
      
      const csvFile = createMockFile(
        'bank_statement.csv',
        'text/csv',
        'Date,Description,Amount\n2025-01-01,Coffee Shop,-5.50\n2025-01-02,Salary,2500.00'
      );
      
      const fileInput = screen.getByLabelText(/choose.*file|browse/i);
      await user.upload(fileInput, csvFile);
      
      // Should show processing state
      await waitFor(() => {
        expect(screen.getByText(/processing|uploading/i)).toBeInTheDocument();
      });
    });
  });

  describe('2. Real-time Processing Updates', () => {
    it('should receive WebSocket updates during processing', async () => {
      render(
        <Router>
          <UploadPage />
        </Router>
      );
      
      // Simulate WebSocket connection
      expect(mockUseWebSocket).toHaveBeenCalled();
      
      // Simulate processing updates
      mockWebSocketService.emit('file_processing', {
        file_id: 'test-file-123',
        status: 'processing',
        progress: 50,
        transactions_processed: 25,
        total_transactions: 50,
      });
      
      // Should update UI with progress
      await waitFor(() => {
        expect(screen.getByText(/50%|processing/i)).toBeInTheDocument();
      });
    });

    it('should handle processing completion notification', async () => {
      render(
        <Router>
          <UploadPage />
        </Router>
      );
      
      // Simulate completion notification
      mockWebSocketService.emit('file_processing_complete', {
        file_id: 'test-file-123',
        status: 'completed',
        transactions_categorized: 50,
        accuracy_achieved: 87.5,
        enhancement_opportunities: ['historical', 'schema'],
      });
      
      await waitFor(() => {
        expect(screen.getByText(/completed|87\.5%/i)).toBeInTheDocument();
      });
    });

    it('should handle processing errors gracefully', async () => {
      render(
        <Router>
          <UploadPage />
        </Router>
      );
      
      // Simulate error notification
      mockWebSocketService.emit('file_processing_error', {
        file_id: 'test-file-123',
        status: 'error',
        error: 'Invalid file format detected',
      });
      
      await waitFor(() => {
        expect(screen.getByText(/error|failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('3. Transaction Review Experience', () => {
    it('should display processed transactions for review', async () => {
      // Mock transactions data
      mockServiceResponses.transactions([
        {
          id: '1',
          description: 'Coffee Shop',
          amount: -5.50,
          date: '2025-01-01',
          vendor: 'Local Cafe',
          category: 'Meals & Entertainment',
          ai_confidence: 0.95,
          tenant_id: 3,
        },
        {
          id: '2', 
          description: 'Salary Payment',
          amount: 2500.00,
          date: '2025-01-02',
          vendor: 'Employer Corp',
          category: 'Income - Salary',
          ai_confidence: 0.98,
          tenant_id: 3,
        },
      ]);
      
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/coffee shop/i)).toBeInTheDocument();
        expect(screen.getByText(/salary payment/i)).toBeInTheDocument();
        expect(screen.getByText(/95%|98%/)).toBeInTheDocument(); // AI confidence
      });
    });

    it('should allow bulk transaction updates', async () => {
      const user = userEvent.setup();
      
      mockServiceResponses.transactions([
        { id: '1', description: 'Transaction 1', amount: -100, date: '2025-01-01', category: 'Office Supplies' },
        { id: '2', description: 'Transaction 2', amount: -200, date: '2025-01-02', category: 'Office Supplies' },
      ]);
      
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/transaction 1/i)).toBeInTheDocument();
      });
      
      // Select multiple transactions
      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[0]);
      await user.click(checkboxes[1]);
      
      // Should show bulk action options
      await waitFor(() => {
        expect(screen.getByText(/bulk.*update|update.*selected/i)).toBeInTheDocument();
      });
    });

    it('should show categorization confidence and allow adjustments', async () => {
      const user = userEvent.setup();
      
      mockServiceResponses.transactions([
        {
          id: '1',
          description: 'Amazon Purchase',
          amount: -45.99,
          date: '2025-01-01',
          category: 'Office Supplies',
          ai_confidence: 0.75, // Lower confidence requiring review
        },
      ]);
      
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/amazon purchase/i)).toBeInTheDocument();
        expect(screen.getByText(/75%/)).toBeInTheDocument(); // Show confidence
      });
      
      // Should allow category change for low confidence items
      const categoryDropdown = screen.getByText(/office supplies/i);
      await user.click(categoryDropdown);
      
      await waitFor(() => {
        expect(screen.getByText(/meals.*entertainment|travel|equipment/i)).toBeInTheDocument();
      });
    });
  });

  describe('4. Export Readiness Flow', () => {
    it('should calculate and display export readiness', async () => {
      mockServiceResponses.dashboardMetrics({
        totalTransactions: 100,
        categorizationRate: 95.0,
        exportReadiness: 85, // 85% ready for export
      });
      
      mockServiceResponses.transactions([]);
      
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/85%.*ready|export.*readiness/i)).toBeInTheDocument();
      });
    });

    it('should guide user through export preparation', async () => {
      const user = userEvent.setup();
      
      mockServiceResponses.transactions([
        { id: '1', description: 'Uncategorized Item', amount: -50, category: null, ai_confidence: 0.4 },
      ]);
      
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/uncategorized|review.*required/i)).toBeInTheDocument();
      });
      
      // Should show guidance for improving export readiness
      expect(screen.getByText(/categorize.*improve|complete.*review/i)).toBeInTheDocument();
    });

    it('should offer export format selection', async () => {
      const user = userEvent.setup();
      
      // Mock high export readiness
      mockServiceResponses.dashboardMetrics({
        exportReadiness: 95,
        categorizationRate: 98.0,
      });
      
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      // Should show export options when ready
      await waitFor(() => {
        expect(screen.getByText(/export|download/i)).toBeInTheDocument();
      });
      
      const exportButton = screen.getByText(/export|download/i);
      await user.click(exportButton);
      
      await waitFor(() => {
        expect(screen.getByText(/quickbooks|xero|csv|tally/i)).toBeInTheDocument();
      });
    });
  });

  describe('5. Progressive Enhancement Detection', () => {
    it('should detect historical data enhancement opportunities', async () => {
      mockServiceResponses.dashboardMetrics({
        categorizationRate: 87.5, // Baseline
      });
      
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      // Simulate enhancement detection
      mockWebSocketService.emit('enhancement_detected', {
        type: 'historical',
        potential_improvement: 15,
        description: 'Historical data can improve accuracy by 15-20%',
      });
      
      await waitFor(() => {
        expect(screen.getByText(/historical.*15%|improve.*accuracy/i)).toBeInTheDocument();
      });
    });

    it('should detect schema enhancement opportunities', async () => {
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      mockWebSocketService.emit('enhancement_detected', {
        type: 'schema',
        potential_improvement: 20,
        description: 'Enhanced schema mapping can improve accuracy by 20%',
      });
      
      await waitFor(() => {
        expect(screen.getByText(/schema.*20%|mapping.*improvement/i)).toBeInTheDocument();
      });
    });

    it('should detect vendor enhancement opportunities', async () => {
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      mockWebSocketService.emit('enhancement_detected', {
        type: 'vendor',
        potential_improvement: 10,
        description: 'Vendor mapping can improve accuracy by 5-10%',
      });
      
      await waitFor(() => {
        expect(screen.getByText(/vendor.*10%|mapping.*accuracy/i)).toBeInTheDocument();
      });
    });
  });

  describe('6. Error Recovery and Validation', () => {
    it('should handle file upload failures gracefully', async () => {
      const user = userEvent.setup();
      
      // Mock upload failure
      mockFetch({ detail: 'File too large' }, false, 413);
      
      render(
        <Router>
          <UploadPage />
        </Router>
      );
      
      const csvFile = createMockFile('large_file.csv', 'text/csv', 'Large content...');
      const fileInput = screen.getByLabelText(/choose.*file|browse/i);
      await user.upload(fileInput, csvFile);
      
      await waitFor(() => {
        expect(screen.getByText(/too large|size.*limit/i)).toBeInTheDocument();
      });
    });

    it('should validate transaction data integrity', async () => {
      mockServiceResponses.transactions([
        {
          id: '1',
          description: '', // Missing description
          amount: null, // Invalid amount
          date: '2025-01-01',
          category: 'Office Supplies',
        },
      ]);
      
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/validation.*error|incomplete.*data/i)).toBeInTheDocument();
      });
    });

    it('should maintain data consistency across reconnections', async () => {
      render(
        <Router>
          <TransactionReviewPage />
        </Router>
      );
      
      // Simulate connection loss and reconnection
      mockWebSocketService.emit('connection_lost', {});
      
      await waitFor(() => {
        expect(screen.getByText(/connection.*lost|reconnecting/i)).toBeInTheDocument();
      });
      
      // Simulate reconnection
      mockWebSocketService.emit('connection_restored', {});
      
      await waitFor(() => {
        expect(screen.queryByText(/connection.*lost/i)).not.toBeInTheDocument();
      });
    });
  });
});