/* Giki AI Spacing & Layout System
 * Semantic spacing classes using design tokens
 */

/* === SEMANTIC SPACING CLASSES === */

/* Container Spacing */
.container-padding {
  padding: var(--giki-space-6); /* 24px - Default container padding */
}

.container-padding-sm {
  padding: var(--giki-space-4); /* 16px - Compact containers */
}

.container-padding-lg {
  padding: var(--giki-space-8); /* 32px - Spacious containers */
}

/* Section Spacing */
.section-spacing {
  margin-bottom: var(--giki-space-12); /* 48px - Between major sections */
}

.section-spacing-sm {
  margin-bottom: var(--giki-space-8); /* 32px - Between minor sections */
}

.section-spacing-lg {
  margin-bottom: var(--giki-space-16); /* 64px - Between page sections */
}

/* Component Spacing */
.component-gap {
  gap: var(--giki-space-4); /* 16px - Default component gap */
}

.component-gap-sm {
  gap: var(--giki-space-2); /* 8px - Tight component gap */
}

.component-gap-lg {
  gap: var(--giki-space-6); /* 24px - Loose component gap */
}

/* Card Spacing */
.card-padding {
  padding: var(--giki-space-6); /* 24px - Default card padding */
}

.card-padding-sm {
  padding: var(--giki-space-4); /* 16px - Compact card padding */
}

.card-padding-lg {
  padding: var(--giki-space-8); /* 32px - Spacious card padding */
}

/* Form Spacing */
.form-field-spacing {
  margin-bottom: var(--giki-space-4); /* 16px - Between form fields */
}

.form-section-spacing {
  margin-bottom: var(--giki-space-8); /* 32px - Between form sections */
}

.form-button-spacing {
  margin-top: var(--giki-space-6); /* 24px - Above form buttons */
}

/* List Spacing */
.list-item-spacing {
  margin-bottom: var(--giki-space-3); /* 12px - Between list items */
}

.list-section-spacing {
  margin-bottom: var(--giki-space-6); /* 24px - Between list sections */
}

/* Grid Spacing */
.grid-gap {
  gap: var(--giki-space-4); /* 16px - Default grid gap */
}

.grid-gap-sm {
  gap: var(--giki-space-2); /* 8px - Tight grid gap */
}

.grid-gap-lg {
  gap: var(--giki-space-6); /* 24px - Loose grid gap */
}

/* === LAYOUT UTILITIES === */

/* Flex Layout Utilities */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-col-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Grid Layout Utilities */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--giki-space-4);
}

.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--giki-space-4);
}

/* === PROFESSIONAL SPACING PATTERNS === */

/* Dashboard Layout */
.dashboard-container {
  padding: var(--giki-space-6);
  gap: var(--giki-space-6);
}

.dashboard-header {
  margin-bottom: var(--giki-space-8);
  padding-bottom: var(--giki-space-4);
  border-bottom: 1px solid hsl(var(--giki-border-primary));
}

.dashboard-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--giki-space-4);
  margin-bottom: var(--giki-space-8);
}

.dashboard-content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--giki-space-6);
}

/* Financial Data Layout */
.financial-table-container {
  padding: var(--giki-space-4);
  border-radius: var(--giki-radius-lg);
  background: hsl(var(--giki-card-bg));
  border: 1px solid hsl(var(--giki-border-primary));
}

.financial-header {
  padding: var(--giki-space-4) var(--giki-space-6);
  border-bottom: 1px solid hsl(var(--giki-border-primary));
  margin-bottom: var(--giki-space-4);
}

.financial-row {
  padding: var(--giki-space-3) var(--giki-space-6);
  border-bottom: 1px solid hsl(var(--giki-border-secondary));
}

/* Form Layout */
.form-container {
  max-width: 480px;
  padding: var(--giki-space-8);
  margin: 0 auto;
}

.form-group {
  margin-bottom: var(--giki-space-6);
}

.form-actions {
  margin-top: var(--giki-space-8);
  padding-top: var(--giki-space-6);
  border-top: 1px solid hsl(var(--giki-border-primary));
}

/* === RESPONSIVE SPACING === */
@media (max-width: 768px) {
  .container-padding {
    padding: var(--giki-space-4);
  }

  .dashboard-container {
    padding: var(--giki-space-4);
    gap: var(--giki-space-4);
  }

  .dashboard-content-grid {
    grid-template-columns: 1fr;
  }

  .card-padding {
    padding: var(--giki-space-4);
  }
}

@media (max-width: 480px) {
  .container-padding {
    padding: var(--giki-space-3);
  }

  .form-container {
    padding: var(--giki-space-6);
  }

  .section-spacing {
    margin-bottom: var(--giki-space-8);
  }
}
