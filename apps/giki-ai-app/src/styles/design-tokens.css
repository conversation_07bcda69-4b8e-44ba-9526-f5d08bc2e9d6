/* GIKI.AI DESIGN TOKENS - Single Source of Truth
 * Version: 2.1
 * Last Updated: 2025-01-27
 * Purpose: Consolidated design system tokens for consistent UI/UX
 *
 * === SYSTEMATIC COLOR MAPPING PHILOSOPHY ===
 *
 * Our design system is built on a systematic mapping between the light colors
 * in our logo and their corresponding dark variants used throughout the application.
 *
 * MAPPING PRINCIPLE:
 * The relationship between logo light green (140 69% 83%) and brand dark green
 * (140 60% 25%) establishes the foundation for all color mappings in our system.
 *
 * TRANSFORMATION FORMULA:
 * - Hue: Preserved exactly (maintains color identity)
 * - Saturation: Reduced from ~69% to ~60-70% (professional appearance)
 * - Lightness: Dramatically reduced from ~83-85% to ~25% (dark variants)
 *
 * COMPLETE MAPPING SYSTEM:
 * Logo Light → App Dark
 * • Green:  140 69% 83% (#B4F0C5) → Brand Dark Green (#295343)
 * • Pink:   0 69% 85% (#FFB6B6)  → 0 60% 25% (#5F1A1A)
 * • Blue:   210 100% 85% (#B6E0FF) → 210 70% 25% (#1A3F5F)
 * • Purple: 270 69% 85% (#E0B6FF) → 270 60% 25% (#3F1A5F)
 *
 * GRADIENT IMPLEMENTATION:
 * All gradients use equal contributions (0%, 33%, 66%, 100%) from each mapped
 * color, creating a unified design language that flows from logo to UI elements.
 *
 * This systematic approach ensures:
 * 1. Brand consistency between logo and application
 * 2. Professional dark variants suitable for UI elements
 * 3. Cohesive gradient systems across all components
 * 4. Scalable color system for future brand extensions
 *
 * For implementation guidelines, see: ./component-style-guide.md
 */

:root {
  /* === BRAND SYSTEM: Excel Familiarity + Modern Tailwind === */
  /* Primary Brand: Dark Green (Excel-like Professional Feel) */
  --giki-primary: #295343; /* Brand dark green - Excel familiarity foundation */
  --giki-primary-hover: #1f3d30; /* Darker green for hover states */
  --giki-primary-active: #295343; /* Same as primary for active */
  --giki-primary-foreground: #fefefe; /* Slightly warm off-white, not pure white */

  /* Logo Colors: Limited Use (From SVG Logo Only) */
  --giki-logo-green-light: #b4f0c5; /* Light green from logo - minimal use */
  --giki-logo-pink-light: #ffb6b6; /* Pink from logo - minimal use */
  --giki-logo-blue-light: #b6e0ff; /* Blue from logo - minimal use */
  --giki-logo-purple-light: #e0b6ff; /* Purple from logo - minimal use */

  /* Dark Professional Palette: AI Features & Accents */
  --giki-dark-green: #295343; /* Professional dark green for AI features */
  --giki-dark-red: #5f1a1a; /* Professional dark red for warnings */
  --giki-dark-blue: #1a3f5f; /* Professional dark blue for info */
  --giki-dark-purple: #3f1a5f; /* Professional dark purple for AI/agent features */

  /* Modern Warm Backgrounds */
  --giki-warm-white: #fefefe; /* Slightly warm off-white */
  --giki-warm-50: #fafaf9; /* Warm gray-50 */
  --giki-warm-100: #f5f5f4; /* Warm gray-100 */

  /* AI Gradient System: Professional Dark Colors */
  --giki-ai-gradient-primary: linear-gradient(
    135deg,
    var(--giki-primary) 0%,
    var(--giki-dark-green) 100%
  );
  --giki-ai-gradient-processing: linear-gradient(
    135deg,
    var(--giki-primary) 0%,
    var(--giki-dark-blue) 50%,
    var(--giki-dark-green) 100%
  );
  --giki-ai-gradient-thinking: linear-gradient(
    270deg,
    var(--giki-primary) 0%,
    var(--giki-logo-green-light) 50%,
    var(--giki-primary) 100%
  );
  --giki-ai-gradient-multi: linear-gradient(
    135deg,
    var(--giki-dark-green) 0%,
    var(--giki-dark-blue) 33%,
    var(--giki-dark-purple) 66%,
    var(--giki-dark-red) 100%
  );

  /* Excel-inspired Professional Colors (Using Brand Green) */
  --giki-excel-green: var(--giki-primary); /* #295343 - Brand dark green */
  --giki-excel-green-hover: var(--giki-primary-hover); /* #1f3d30 - Hover */
  --giki-excel-green-active: var(--giki-primary-active); /* #295343 - Active */
  --giki-excel-green-foreground: var(--giki-primary-foreground); /* #FFFFFF */

  /* === SEMANTIC COLORS (Professional) === */
  /* Success States - Brand Green Focused */
  --giki-success: var(--giki-primary); /* #295343 - Brand green for success */
  --giki-success-hover: var(
    --giki-dark-green
  ); /* #295343 - Dark green for hover */
  --giki-success-active: var(
    --giki-primary
  ); /* #295343 - Brand green for active */
  --giki-success-foreground: var(--giki-warm-white); /* Warm off-white text */

  /* Success Backgrounds (Light use) */
  --giki-success-light: var(
    --giki-logo-green-light
  ); /* #B4F0C5 - Light background when needed */
  --giki-success-light-foreground: var(
    --giki-dark-green
  ); /* #295343 - Dark text on light */

  /* Error States */
  --giki-destructive: 0 75% 50%; /* #DC2626 Professional Error Red */
  --giki-destructive-hover: 0 75% 45%; /* #C41E1E */
  --giki-destructive-active: 0 75% 40%; /* #AC1616 */
  --giki-destructive-foreground: 0 0% 100%; /* #FFFFFF */

  /* Warning States */
  --giki-warning: 35 85% 50%; /* #E67E22 Professional Warning Orange */
  --giki-warning-hover: 35 85% 45%; /* #D4711F */
  --giki-warning-active: 35 85% 40%; /* #C2641C */
  --giki-warning-foreground: 0 0% 100%; /* #FFFFFF */

  /* Info States */
  --giki-info: 207 100% 82%; /* #A9D0F5 - Using brand blue for info */
  --giki-info-hover: 207 100% 77%; /* #94C5F0 */
  --giki-info-active: 207 100% 72%; /* #7FB9EB */
  --giki-info-foreground: 210 10% 15%; /* #1E293B */

  /* === NEUTRAL PALETTE (Professional Grays) === */
  --giki-neutral-white: 0 0% 100%; /* #FFFFFF */
  --giki-neutral-50: 210 40% 98%; /* #f8fafc Tailwind slate-50 */
  --giki-neutral-100: 210 40% 96%; /* #f1f5f9 Tailwind slate-100 */
  --giki-neutral-200: 210 40% 94%; /* #e2e8f0 Tailwind slate-200 */
  --giki-neutral-300: 215 25% 87%; /* #cbd5e1 Tailwind slate-300 */
  --giki-neutral-400: 215 20% 65%; /* #94a3b8 Tailwind slate-400 */
  --giki-neutral-500: 215 16% 47%; /* #64748b Tailwind slate-500 */
  --giki-neutral-600: 215 19% 35%; /* #475569 Tailwind slate-600 */
  --giki-neutral-700: 215 25% 27%; /* #334155 Tailwind slate-700 */
  --giki-neutral-800: 215 28% 17%; /* #1e293b Tailwind slate-800 */
  --giki-neutral-900: 215 39% 11%; /* #0f172a Tailwind slate-900 */
  --giki-neutral-950: 215 39% 7%; /* #020617 Tailwind slate-950 */

  /* === TEXT COLORS (WCAG AA Compliant & Standardized) === */
  --giki-text-primary: var(
    --giki-neutral-900
  ); /* Maximum contrast - main content */
  --giki-text-secondary: var(
    --giki-neutral-700
  ); /* High contrast - secondary content */
  --giki-text-muted: var(
    --giki-neutral-600
  ); /* Improved contrast - captions, labels */
  --giki-text-disabled: var(
    --giki-neutral-500
  ); /* Better contrast - disabled states */
  --giki-text-on-dark: var(
    --giki-neutral-white
  ); /* White on dark backgrounds */
  --giki-text-on-primary: var(--giki-primary-foreground); /* White on primary */
  --giki-text-success: var(--giki-success); /* Success messages */
  --giki-text-error: var(--giki-destructive); /* Error messages */
  --giki-text-warning: var(--giki-warning); /* Warning messages */
  --giki-text-link: var(--giki-primary); /* Links and interactive text */
  --giki-text-link-hover: var(--giki-primary-hover); /* Link hover state */

  /* === BACKGROUND COLORS (Modern Warm) === */
  --giki-bg-primary: var(
    --giki-warm-white
  ); /* Warm off-white main background */
  --giki-bg-secondary: var(--giki-warm-50); /* Warm secondary background */
  --giki-bg-tertiary: var(--giki-warm-100); /* Warm tertiary background */
  --giki-bg-muted: var(--giki-neutral-200); /* Muted background */
  --giki-bg-accent: var(--giki-warm-100); /* Warm accent background */

  /* === BORDER COLORS === */
  --giki-border-primary: var(--giki-neutral-200); /* Primary borders */
  --giki-border-secondary: var(--giki-neutral-300); /* Secondary borders */
  --giki-border-muted: var(--giki-neutral-100); /* Muted borders */
  --giki-border-focus: var(--giki-primary); /* Focus ring color */

  /* === COMPONENT-SPECIFIC TOKENS === */
  /* Cards - Modern Warm Design */
  --giki-card-bg: var(--giki-bg-primary); /* Warm off-white background */
  --giki-card-border: var(--giki-border-primary);
  --giki-card-shadow:
    0 2px 8px 0 rgba(0, 0, 0, 0.08), 0 1px 3px 0 rgba(0, 0, 0, 0.04);
  --giki-card-shadow-premium:
    0 4px 20px 0 rgba(0, 0, 0, 0.12), 0 2px 8px 0 rgba(0, 0, 0, 0.08),
    0 1px 3px 0 rgba(0, 0, 0, 0.04);
  --giki-card-shadow-elevated:
    0 8px 32px 0 rgba(0, 0, 0, 0.16), 0 4px 16px 0 rgba(0, 0, 0, 0.12),
    0 2px 8px 0 rgba(0, 0, 0, 0.08);

  /* Inputs */
  --giki-input-bg: var(--giki-bg-primary);
  --giki-input-border: var(--giki-border-secondary);
  --giki-input-border-focus: var(--giki-border-focus);
  --giki-input-text: var(--giki-text-primary);
  --giki-input-placeholder: var(
    --giki-neutral-600
  ); /* Better contrast for placeholders */

  /* Navigation - Using Brand Green System */
  --giki-nav-bg: #234039; /* Dark green panel background */
  --giki-nav-border: #1f3d30; /* Darker border */
  --giki-nav-text: #fafafa; /* Very light text on dark */
  --giki-nav-text-active: #ffffff; /* White for active */
  --giki-nav-text-secondary: #e6e6e6; /* Better contrast secondary text */
  --giki-nav-icon-inactive: #d9d9d9; /* Better contrast for inactive icons */
  --giki-nav-icon-hover: #ffffff; /* Pure white on hover */
  --giki-nav-bg-hover: var(--giki-primary); /* #295343 - Brand green hover */
  --giki-nav-bg-active: var(--giki-primary);
  --giki-nav-item-active: var(--giki-primary);
  --giki-nav-item-hover: var(--giki-nav-bg-hover);

  /* Agent Panel - Professional AI Features */
  --giki-agent-bg: var(--giki-bg-primary); /* Warm background */
  --giki-agent-border: var(--giki-border-primary);
  --giki-agent-accent: var(
    --giki-dark-purple
  ); /* Professional dark purple for AI */
  --giki-agent-accent-foreground: var(
    --giki-warm-white
  ); /* Warm off-white text */
  --giki-agent-gradient: var(
    --giki-ai-gradient-multi
  ); /* Professional AI gradient */
  --giki-agent-header-bg: var(--giki-primary); /* Brand green for headers */

  /* === SPACING SCALE === */
  --giki-space-0: 0;
  --giki-space-1: 0.25rem; /* 4px */
  --giki-space-2: 0.5rem; /* 8px */
  --giki-space-3: 0.75rem; /* 12px */
  --giki-space-4: 1rem; /* 16px */
  --giki-space-5: 1.25rem; /* 20px */
  --giki-space-6: 1.5rem; /* 24px */
  --giki-space-8: 2rem; /* 32px */
  --giki-space-10: 2.5rem; /* 40px */
  --giki-space-12: 3rem; /* 48px */
  --giki-space-16: 4rem; /* 64px */
  --giki-space-20: 5rem; /* 80px */
  --giki-space-24: 6rem; /* 96px */

  /* === BORDER RADIUS SCALE === */
  --giki-radius-none: 0;
  --giki-radius-sm: 0.25rem; /* 4px */
  --giki-radius-md: 0.375rem; /* 6px */
  --giki-radius-lg: 0.5rem; /* 8px */
  --giki-radius-xl: 0.75rem; /* 12px */
  --giki-radius-2xl: 1rem; /* 16px */
  --giki-radius-full: 9999px;

  /* === SHADOW SCALE === */
  --giki-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --giki-shadow-md:
    0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --giki-shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --giki-shadow-xl:
    0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --giki-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* === TYPOGRAPHY SYSTEM === */
  --giki-font-sans: system-ui, -apple-system, sans-serif;
  --giki-font-mono: ui-monospace, 'SF Mono', 'Consolas', monospace;

  /* Font family tokens for CSS usage */
  --giki-font-family-sans: var(--giki-font-sans);
  --giki-font-family-display: var(--giki-font-sans);
  --giki-font-family-mono: var(--giki-font-mono);

  /* Font Sizes - Comprehensive Scale */
  --giki-text-xs: 0.75rem; /* 12px */
  --giki-text-sm: 0.875rem; /* 14px */
  --giki-text-base: 1rem; /* 16px */
  --giki-text-lg: 1.125rem; /* 18px */
  --giki-text-xl: 1.25rem; /* 20px */
  --giki-text-2xl: 1.5rem; /* 24px */
  --giki-text-3xl: 1.875rem; /* 30px */
  --giki-text-4xl: 2.25rem; /* 36px */
  --giki-text-5xl: 3rem; /* 48px */

  /* Font Weights */
  --giki-font-light: 300;
  --giki-font-normal: 400;
  --giki-font-medium: 500;
  --giki-font-semibold: 600;
  --giki-font-bold: 700;
  --giki-font-extrabold: 800;

  /* Font weight aliases for CSS usage */
  --giki-font-weight-light: var(--giki-font-light);
  --giki-font-weight-normal: var(--giki-font-normal);
  --giki-font-weight-medium: var(--giki-font-medium);
  --giki-font-weight-semibold: var(--giki-font-semibold);
  --giki-font-weight-bold: var(--giki-font-bold);
  --giki-font-weight-extrabold: var(--giki-font-extrabold);

  /* Line Heights */
  --giki-leading-tight: 1.25;
  --giki-leading-normal: 1.5;
  --giki-leading-relaxed: 1.75;

  /* === LAYOUT CONSTANTS === */
  --giki-layout-nav-width-collapsed: 4rem; /* 64px */
  --giki-layout-nav-width-expanded: 16rem; /* 256px */
  --giki-layout-agent-width-min: 20rem; /* 320px */
  --giki-layout-agent-width-max: 30rem; /* 480px */
  --giki-layout-header-height: 4rem; /* 64px */
  --giki-layout-content-padding: var(--giki-space-6); /* 24px */

  /* === Z-INDEX SCALE === */
  --giki-z-base: 0;
  --giki-z-dropdown: 10;
  --giki-z-sticky: 20;
  --giki-z-fixed: 30;
  --giki-z-modal-backdrop: 40;
  --giki-z-modal: 50;
  --giki-z-popover: 60;
  --giki-z-tooltip: 70;
  --giki-z-toast: 80;
  --giki-z-max: 9999;

  /* === ANIMATION TOKENS === */
  --giki-transition-fast: 150ms ease-in-out;
  --giki-transition-normal: 200ms ease-in-out;
  --giki-transition-slow: 300ms ease-in-out;

  /* Button ripple animation */
  --giki-ripple-duration: 600ms;

  /* === SHADCN/UI COMPATIBILITY === */
  /* Mapping to modern warm system for shadcn/ui components */
  --color-primary: var(--giki-primary); /* Brand dark green */
  --color-primary-foreground: var(
    --giki-primary-foreground
  ); /* Warm off-white */
  --color-primary-hover: var(--giki-primary-hover);
  --color-primary-active: var(--giki-primary-active);

  --color-background: var(--giki-bg-primary); /* Warm off-white background */
  --color-foreground: var(--giki-text-primary);
  --color-card: var(--giki-card-bg); /* Warm card backgrounds */
  --color-card-foreground: var(--giki-text-primary);
  --color-border: var(--giki-border-primary);
  --color-input: var(--giki-input-border);
  --color-ring: var(--giki-border-focus); /* Brand green focus rings */

  --color-muted: var(--giki-bg-muted);
  --color-muted-foreground: var(--giki-text-muted);
  --color-accent: var(
    --giki-dark-purple
  ); /* Professional dark purple for accents */
  --color-accent-foreground: var(--giki-warm-white); /* Warm off-white text */

  --color-destructive: var(--giki-destructive);
  --color-destructive-foreground: var(--giki-destructive-foreground);
  --color-success: var(--giki-success); /* Brand green for success */
  --color-success-foreground: var(
    --giki-success-foreground
  ); /* Warm off-white */
  --color-warning: var(--giki-warning);
  --color-warning-foreground: var(--giki-warning-foreground);

  --radius: var(--giki-radius-md);
  --shadow-excel-card: var(--giki-card-shadow);
}

/* === COMPONENT ANIMATIONS === */
@keyframes ripple {
  from {
    transform: scale(0);
    opacity: 0.6;
  }
  to {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ripple {
  animation: ripple var(--giki-ripple-duration) ease-out;
}
