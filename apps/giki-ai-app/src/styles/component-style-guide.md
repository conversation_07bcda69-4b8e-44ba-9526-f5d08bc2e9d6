# Giki.AI Component Style Guide

## Overview

This guide defines the visual design principles and implementation patterns for all UI components in the Giki.AI application. Our design system creates a cohesive, professional financial application that balances modern aesthetics with Excel-familiar patterns.

> **IMPORTANT**: This file is superseded by the comprehensive design system at docs/design-system/README.md
> **Please reference**:
>
> - Main Design System: docs/design-system/README.md
> - Brand Colors: docs/design-system/brand/colors.md
> - Component Library: docs/design-system/components/README.md
> - Excel Patterns: docs/design-system/brand/excel-patterns.md
> - Implementation Guide: docs/design-system/implementation-guide.md

## Core Design Principles

### 1. Brand Identity

- **Primary Brand**: Dark green professional theme (#295343)
- **Logo Colors**: Light gradients (Green, Pink, Blue, Purple) mapped to dark UI variants
- **Gradient System**: All gradients use the mapped dark variants for professional appearance

### 2. Visual Hierarchy

- **Elevation Levels**:
  - Level 0: Background surfaces
  - Level 1: Cards and containers (shadow-sm)
  - Level 2: Elevated cards (shadow-card-shadow)
  - Level 3: Premium/hover states (shadow-card-shadow-premium)
  - Level 4: Modals and overlays (shadow-card-shadow-elevated)

### 3. Component Patterns

#### Cards

```css
/* Base Card */
.giki-card {
  background: hsl(var(--giki-card-bg));
  border: 1px solid hsl(var(--giki-card-border));
  border-radius: var(--giki-radius-lg);
  box-shadow: var(--giki-card-shadow);
  transition: all var(--giki-transition-normal);
}

/* Branded Card with Gradient Border */
.giki-card-branded {
  position: relative;
  background: hsl(var(--giki-card-bg));
  border: 1px solid transparent;
  background-clip: padding-box;
}

.giki-card-branded::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--giki-radius-lg);
  padding: 1px;
  background: linear-gradient(
    135deg,
    hsl(var(--giki-brand-green-dark)) 0%,
    hsl(var(--giki-brand-blue-dark)) 33%,
    hsl(var(--giki-brand-purple-dark)) 66%,
    hsl(var(--giki-brand-pink-dark)) 100%
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}
```

#### Buttons

```css
/* Primary Button - Brand Green */
.giki-button-primary {
  background: linear-gradient(
    135deg,
    hsl(var(--giki-primary)) 0%,
    hsl(var(--giki-primary-hover)) 100%
  );
  color: hsl(var(--giki-primary-foreground));
  border: 1px solid hsl(var(--giki-primary-hover));
  box-shadow: 0 2px 4px hsla(var(--giki-primary), 0.2);
}

/* Gradient Button - Full Brand Spectrum */
.giki-button-gradient {
  background: linear-gradient(
    135deg,
    hsl(var(--giki-brand-green-dark)) 0%,
    hsl(var(--giki-brand-blue-dark)) 50%,
    hsl(var(--giki-brand-purple-dark)) 100%
  );
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px hsla(var(--giki-primary), 0.3);
}

/* Glass Button - Modern Transparent */
.giki-button-glass {
  background: hsla(var(--giki-card-bg), 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid hsla(var(--giki-border-primary), 0.3);
  color: hsl(var(--giki-text-primary));
}
```

#### Tables

```css
/* Excel-Inspired Table */
.giki-table {
  background: hsl(var(--giki-card-bg));
  border: 1px solid hsl(var(--giki-border-primary));
  border-radius: var(--giki-radius-lg);
  overflow: hidden;
}

.giki-table-header {
  background: linear-gradient(
    180deg,
    hsl(var(--giki-bg-secondary)) 0%,
    hsl(var(--giki-bg-muted)) 100%
  );
  border-bottom: 2px solid hsl(var(--giki-border-primary));
}

.giki-table-row:hover {
  background: hsl(var(--giki-bg-accent));
  box-shadow:
    inset 0 1px 0 hsla(var(--giki-primary), 0.1),
    inset 0 -1px 0 hsla(var(--giki-primary), 0.1);
}
```

#### Input Fields

```css
/* Professional Input */
.giki-input {
  background: hsl(var(--giki-input-bg));
  border: 1px solid hsl(var(--giki-input-border));
  border-radius: var(--giki-radius-md);
  transition: all var(--giki-transition-fast);
  font-weight: 500;
}

.giki-input:focus {
  border-color: hsl(var(--giki-input-border-focus));
  box-shadow: 0 0 0 3px hsla(var(--giki-primary), 0.1);
  background: hsl(var(--giki-bg-primary));
}

/* Financial Input */
.giki-input-financial {
  font-family: var(--giki-font-family-mono);
  font-variant-numeric: tabular-nums;
  text-align: right;
}
```

### 4. Animation Patterns

#### Hover Effects

- Cards: Slight elevation (translateY(-2px)) + enhanced shadow
- Buttons: Brightness adjustment + shadow expansion
- Tables: Row highlighting with smooth transitions
- Links: Color transition + underline animation

#### Loading States

```css
/* Skeleton Loading */
.giki-skeleton {
  background: linear-gradient(
    90deg,
    hsl(var(--giki-bg-muted)) 0%,
    hsl(var(--giki-bg-accent)) 50%,
    hsl(var(--giki-bg-muted)) 100%
  );
  background-size: 200% 100%;
  animation: giki-skeleton-pulse 1.5s ease-in-out infinite;
}

/* Progress Indicators */
.giki-progress {
  background: linear-gradient(
    90deg,
    hsl(var(--giki-brand-green-dark)) 0%,
    hsl(var(--giki-brand-blue-dark)) 50%,
    hsl(var(--giki-brand-purple-dark)) 100%
  );
  background-size: 200% 100%;
  animation: giki-progress-flow 2s linear infinite;
}
```

### 5. Glass Morphism Effects

```css
/* Glass Container */
.giki-glass {
  background: hsla(var(--giki-card-bg), 0.7);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid hsla(var(--giki-border-primary), 0.2);
  box-shadow: 0 8px 32px hsla(0, 0%, 0%, 0.1);
}

/* Glass with Gradient Border */
.giki-glass-gradient {
  position: relative;
  background: hsla(var(--giki-card-bg), 0.7);
  backdrop-filter: blur(12px);
}

.giki-glass-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(
    135deg,
    hsla(var(--giki-brand-green), 0.5),
    hsla(var(--giki-brand-purple), 0.5)
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask-composite: exclude;
}
```

### 6. Financial Data Display

```css
/* Currency Display */
.giki-currency {
  font-family: var(--giki-font-family-mono);
  font-variant-numeric: tabular-nums;
  font-weight: 600;
}

.giki-currency-positive {
  color: hsl(var(--giki-success));
}

.giki-currency-negative {
  color: hsl(var(--giki-destructive));
}

/* Data Grid Cells */
.giki-data-cell {
  padding: var(--giki-space-3);
  border-right: 1px solid hsla(var(--giki-border-primary), 0.5);
  font-family: var(--giki-font-family-mono);
  font-size: var(--giki-text-sm);
}
```

### 7. Component State Patterns

#### Interactive States

- **Default**: Base styling
- **Hover**: Elevated shadow + slight transform
- **Active**: Pressed appearance (scale: 0.98)
- **Focus**: Ring with brand color
- **Disabled**: 50% opacity + no pointer events

#### Validation States

- **Success**: Green border + check icon
- **Error**: Red border + error icon
- **Warning**: Orange border + warning icon
- **Info**: Blue border + info icon

### 8. Responsive Patterns

```css
/* Mobile-First Card Layout */
.giki-card-grid {
  display: grid;
  gap: var(--giki-space-4);
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* Responsive Typography */
@media (max-width: 768px) {
  .giki-heading-responsive {
    font-size: calc(var(--giki-text-2xl) * 0.875);
  }

  .giki-card {
    padding: var(--giki-space-3);
  }
}
```

### 9. Dark Mode Considerations

All components should use semantic color tokens that automatically adjust for dark mode:

- Use `hsl(var(--giki-*))` color tokens
- Avoid hardcoded colors
- Test glass effects in both modes
- Ensure sufficient contrast ratios

### 10. Performance Guidelines

- Use CSS transforms for animations (not position changes)
- Implement will-change for frequently animated elements
- Use CSS containment for complex components
- Minimize gradient complexity on mobile devices

## Implementation Checklist

When creating or updating a component:

- [ ] Uses design tokens for all values
- [ ] Implements proper hover/focus states
- [ ] Includes loading skeleton variant
- [ ] Supports dark mode
- [ ] Has consistent spacing
- [ ] Uses appropriate typography classes
- [ ] Implements smooth transitions
- [ ] Follows accessibility guidelines
- [ ] Uses brand colors appropriately
- [ ] Maintains visual hierarchy
