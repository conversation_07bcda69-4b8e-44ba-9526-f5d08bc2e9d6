# Typography Style Guide

This document outlines the standardized typography system for the giki.ai application. All components should use these semantic classes instead of direct Tailwind typography utilities.

> **IMPORTANT**: This file is superseded by the comprehensive design system typography specification
> **Please reference**:
>
> - Typography System: docs/design-system/foundations/typography.md (complete type system)
> - Design System: docs/design-system/README.md (overall guidelines)
> - Implementation Guide: docs/design-system/implementation-guide.md (developer guide)

## Typography Hierarchy

### Headings

```tsx
// ✅ CORRECT - Use semantic heading classes
<h1 className="text-heading-1">Page Title</h1>        // 36px, bold, tight
<h2 className="text-heading-2">Section Title</h2>     // 30px, bold, tight
<h3 className="text-heading-3">Subsection</h3>        // 24px, semibold, tight
<h4 className="text-heading-4">Card Title</h4>        // 20px, semibold, normal
<h5 className="text-heading-5">Small Heading</h5>     // 18px, medium, normal
<h6 className="text-heading-6">Minor Heading</h6>     // 16px, medium, normal, secondary color

// ❌ INCORRECT - Don't use direct Tailwind classes
<h1 className="text-3xl font-bold">Page Title</h1>
<h2 className="text-2xl font-semibold">Section Title</h2>
```

### Body Text

```tsx
// ✅ CORRECT - Use semantic body classes
<p className="text-body">Main content text</p>         // 16px, normal, relaxed
<p className="text-body-large">Emphasized text</p>     // 18px, normal, relaxed
<p className="text-body-small">Secondary text</p>      // 14px, normal, normal, secondary color

// ❌ INCORRECT - Don't use direct Tailwind classes
<p className="text-base">Main content text</p>
<p className="text-sm text-muted-foreground">Secondary text</p>
```

### Specialized Text

```tsx
// ✅ CORRECT - Use semantic specialized classes
<span className="text-caption">Caption text</span>          // 12px, normal, muted color
<label className="text-label">Form labels</label>           // 14px, medium, secondary color
<code className="text-code">Code snippets</code>            // 14px, mono, normal

// ❌ INCORRECT - Don't use direct Tailwind classes
<span className="text-xs text-muted-foreground">Caption</span>
<label className="text-sm font-medium">Form label</label>
```

## Component-Specific Typography

### Button Text

```tsx
// ✅ CORRECT - Use button-specific classes
<Button size="sm" className="text-button-sm">Small Button</Button>     // 12px, medium
<Button size="default" className="text-button-base">Button</Button>     // 14px, medium
<Button size="lg" className="text-button-lg">Large Button</Button>      // 16px, medium

// ❌ INCORRECT - Don't override button text styles
<Button className="text-xs">Small Button</Button>
<Button className="text-base font-bold">Button</Button>
```

### Card Content

```tsx
// ✅ CORRECT - Use card-specific classes
<GikiCard
  title="Card Title"        // Uses text-card-title automatically
  subtitle="Card subtitle"  // Uses text-card-subtitle automatically
>
  <div className="text-card-metric">$1,234</div>     // 30px, bold, mono, tabular
</GikiCard>

// ❌ INCORRECT - Don't use manual styling
<div className="text-lg font-semibold">Card Title</div>
<div className="text-3xl font-bold font-mono">$1,234</div>
```

### Financial Data

```tsx
// ✅ CORRECT - Use financial-specific classes
<div className="text-financial-large">$12,345</div>    // 24px, bold, mono, tabular
<div className="text-financial">$1,234</div>           // 16px, medium, mono, tabular
<div className="text-financial-small">$123</div>       // 14px, normal, mono, tabular

// ❌ INCORRECT - Don't use manual financial styling
<div className="text-2xl font-bold font-mono">$12,345</div>
<div className="text-base font-mono tabular-nums">$1,234</div>
```

### Table Text

```tsx
// ✅ CORRECT - Use table-specific classes (automatic in Table components)
<TableHead className="text-table-header">Header</TableHead>     // 14px, medium, secondary
<TableCell className="text-table-cell">Data</TableCell>         // 14px, normal, primary
<TableCell className="text-table-cell-mono">$123</TableCell>    // 14px, normal, mono, tabular

// ❌ INCORRECT - Don't override table typography
<th className="text-sm font-bold">Header</th>
<td className="text-xs">Data</td>
```

### Status and Badges

```tsx
// ✅ CORRECT - Use status-specific classes (automatic in Badge component)
<Badge>Status</Badge>                          // Uses text-status-badge automatically
<div className="text-status-badge">ACTIVE</div>  // 12px, semibold, uppercase

// ❌ INCORRECT - Don't use manual badge styling
<div className="text-xs font-semibold uppercase">ACTIVE</div>
```

## Color Classes

### Text Colors

```tsx
// ✅ CORRECT - Use semantic color classes
<p className="text-primary-foreground">Primary text</p>        // Maximum contrast
<p className="text-secondary-foreground">Secondary text</p>    // High contrast
<p className="text-muted-foreground">Muted text</p>            // Medium contrast

// ❌ INCORRECT - Don't use direct color classes
<p className="text-gray-900">Primary text</p>
<p className="text-gray-600">Secondary text</p>
```

### Status Colors (automatic in components)

```tsx
// ✅ CORRECT - Use semantic status colors
<Badge variant="success">Success</Badge>        // Green
<Badge variant="destructive">Error</Badge>      // Red
<Badge variant="warning">Warning</Badge>        // Orange

// Financial data colors are handled automatically in text-financial-* classes
```

## Responsive Typography

### Mobile Optimization

Typography automatically adjusts on mobile:

- `text-heading-1` becomes 30px on mobile (from 36px)
- `text-heading-2` becomes 24px on mobile (from 30px)
- `text-card-metric` becomes 24px on mobile (from 30px)

```tsx
// ✅ CORRECT - Responsive typography is automatic
<h1 className="text-heading-1">Title</h1>  // Automatically responsive

// ❌ INCORRECT - Don't add manual responsive classes
<h1 className="text-3xl md:text-4xl">Title</h1>
```

## Font Features

### Monospace for Financial Data

```tsx
// ✅ CORRECT - Monospace automatically applied for financial classes
<div className="text-financial">$1,234.56</div>        // Mono font + tabular numbers
<div className="text-card-metric">$12,345</div>        // Mono font + tabular numbers

// ❌ INCORRECT - Don't add manual font features
<div className="font-mono tabular-nums">$1,234.56</div>
```

### Font Feature Settings

Font feature settings are applied automatically:

- **Sans fonts**: Get optimized character variants
- **Mono fonts**: Get tabular numbers and better zero differentiation

## Migration Guide

### Common Replacements

```tsx
// OLD → NEW
"text-3xl font-bold"           → "text-heading-1"
"text-2xl font-semibold"       → "text-heading-3"
"text-lg font-semibold"        → "text-card-title"
"text-sm text-muted-foreground" → "text-body-small"
"text-xs font-semibold uppercase" → "text-status-badge"
"text-base font-mono"          → "text-financial"
"text-2xl font-bold font-mono" → "text-financial-large"
```

### Component Updates

1. **Buttons**: Replace text size classes with `text-button-*`
2. **Cards**: Use `text-card-*` for card content
3. **Tables**: Use `text-table-*` for table content
4. **Financial data**: Use `text-financial-*` for all monetary values
5. **Status indicators**: Use `text-status-badge` for status text

## Benefits

1. **Consistency**: All typography follows the same scale and weights
2. **Maintainability**: Changes to the design system update all components
3. **Accessibility**: Proper contrast ratios and font sizes
4. **Performance**: Reduced CSS bundle size from fewer utility classes
5. **Design Tokens**: Typography tied to design system tokens
6. **Responsive**: Automatic mobile optimization

## Rules

1. **NEVER** use direct Tailwind typography classes (`text-lg`, `font-bold`, etc.)
2. **ALWAYS** use semantic typography classes (`text-heading-4`, `text-body`, etc.)
3. **NEVER** override font families manually
4. **ALWAYS** use `text-financial-*` for monetary values
5. **NEVER** use `text-muted-foreground` - use `text-body-small` or `text-card-subtitle`
6. **ALWAYS** test typography changes on mobile devices
