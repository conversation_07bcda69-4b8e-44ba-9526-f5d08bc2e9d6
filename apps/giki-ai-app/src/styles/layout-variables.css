/**
 * Layout Variables - Standardized Spacing and Border System
 * 
 * Provides consistent design tokens for spacing, borders, shadows,
 * and other layout properties across the application.
 */

:root {
  /* Layout Heights */
  --header-height: 64px;
  --footer-height: auto; /* Dynamic based on content */
  --nav-collapsed-width: 64px;
  --nav-expanded-width: 240px;
  --agent-panel-width: 400px;

  /* Standard Border Radius */
  --border-radius-xs: 4px;
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-2xl: 24px;

  /* Panel and Card Styling */
  --panel-border-radius: 12px;
  --card-border-radius: 12px;
  --button-border-radius: 8px;
  --input-border-radius: 6px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

  /* Panel Shadows */
  --panel-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --dropdown-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);

  /* Standard Padding */
  --padding-xs: 8px;
  --padding-sm: 12px;
  --padding-md: 16px;
  --padding-lg: 24px;
  --padding-xl: 32px;
  --padding-2xl: 48px;

  /* Content Padding */
  --page-padding: 32px;
  --section-padding: 24px;
  --card-padding: 24px;
  --panel-padding: 16px;

  /* Standard Gaps */
  --gap-xs: 4px;
  --gap-sm: 8px;
  --gap-md: 12px;
  --gap-lg: 16px;
  --gap-xl: 24px;
  --gap-2xl: 32px;

  /* Section Gaps */
  --section-gap: 24px;
  --element-gap: 16px;
  --component-gap: 12px;
  --inline-gap: 8px;

  /* Standard Margins */
  --margin-xs: 4px;
  --margin-sm: 8px;
  --margin-md: 12px;
  --margin-lg: 16px;
  --margin-xl: 24px;
  --margin-2xl: 32px;

  /* Touch Targets */
  --touch-target-min: 44px;
  --button-height-sm: 36px;
  --button-height-md: 44px;
  --button-height-lg: 52px;

  /* Form Controls */
  --input-height: 44px;
  --select-height: 44px;
  --textarea-min-height: 88px;

  /* Grid Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Content Widths */
  --content-max-width: 1200px;
  --container-max-width: 1400px;
  --sidebar-min-width: 280px;
  --sidebar-max-width: 320px;

  /* Z-Index Layers */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;

  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 200ms;
  --duration-slow: 300ms;
  --duration-extra-slow: 500ms;

  /* Transition Easings */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Utility Classes for Common Layout Patterns */

/* Panel Styling */
.panel {
  background: var(--ui-background);
  border: 1px solid var(--ui-border);
  border-radius: var(--panel-border-radius);
  box-shadow: var(--panel-shadow);
}

/* Card Styling */
.card {
  background: var(--ui-background);
  border: 1px solid var(--ui-border);
  border-radius: var(--card-border-radius);
  padding: var(--card-padding);
  box-shadow: var(--card-shadow);
  transition: box-shadow var(--duration-normal) var(--ease-out);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* Standard Spacing */
.space-section {
  margin-bottom: var(--section-gap);
}

.space-element {
  margin-bottom: var(--element-gap);
}

.space-component {
  margin-bottom: var(--component-gap);
}

/* Padding Classes */
.p-page {
  padding: var(--page-padding);
}

.p-section {
  padding: var(--section-padding);
}

.p-card {
  padding: var(--card-padding);
}

.p-panel {
  padding: var(--panel-padding);
}

/* Gap Classes for Flexbox/Grid */
.gap-section {
  gap: var(--section-gap);
}

.gap-element {
  gap: var(--element-gap);
}

.gap-component {
  gap: var(--component-gap);
}

.gap-inline {
  gap: var(--inline-gap);
}

/* Text Overflow Classes */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Touch Target Classes */
.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Classes */
@media (max-width: 768px) {
  :root {
    --page-padding: 16px;
    --section-padding: 16px;
    --card-padding: 16px;
    --section-gap: 16px;
    --element-gap: 12px;
  }
}

/* Focus Styles */
.focus-ring {
  transition: box-shadow var(--duration-fast) var(--ease-out);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--brand-primary-alpha);
}

.focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--brand-primary-alpha);
}