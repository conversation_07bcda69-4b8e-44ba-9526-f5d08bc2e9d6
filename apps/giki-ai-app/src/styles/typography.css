/* Giki AI Typography System
 * Semantic typography classes using design tokens
 */

/* === HEADING CLASSES === */
.text-heading-1 {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-4xl);
  font-weight: var(--giki-font-weight-bold);
  line-height: var(--giki-leading-tight);
  letter-spacing: -0.025em;
  color: hsl(var(--giki-text-primary));
}

.text-heading-2 {
  font-family: var(--giki-font-family-display);
  font-size: var(--giki-text-3xl);
  font-weight: var(--giki-font-weight-bold);
  line-height: var(--giki-leading-tight);
  letter-spacing: -0.025em;
  color: hsl(var(--giki-text-primary));
}

.text-heading-3 {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-2xl);
  font-weight: var(--giki-font-weight-semibold);
  line-height: var(--giki-leading-tight);
  color: hsl(var(--giki-text-primary));
}

.text-heading-4 {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-xl);
  font-weight: var(--giki-font-weight-semibold);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-primary));
}

.text-heading-5 {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-lg);
  font-weight: var(--giki-font-weight-medium);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-primary));
}

.text-heading-6 {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-base);
  font-weight: var(--giki-font-weight-medium);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-secondary));
}

/* === BODY TEXT CLASSES === */
.text-body {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-base);
  font-weight: var(--giki-font-weight-normal);
  line-height: var(--giki-leading-relaxed);
  color: hsl(var(--giki-text-primary));
}

.text-body-large {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-lg);
  font-weight: var(--giki-font-weight-normal);
  line-height: var(--giki-leading-relaxed);
  color: hsl(var(--giki-text-primary));
}

.text-body-small {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-normal);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-secondary));
}

/* === SPECIALIZED CLASSES === */
.text-caption {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-xs);
  font-weight: var(--giki-font-weight-normal);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-muted));
}

.text-label {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-medium);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-secondary));
}

.text-button {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-medium);
  line-height: var(--giki-leading-normal);
  letter-spacing: 0.025em;
}

.text-code {
  font-family: var(--giki-font-family-mono);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-normal);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-primary));
}

/* === FINANCIAL DATA CLASSES === */
.text-financial-large {
  font-family: var(--giki-font-family-mono);
  font-size: var(--giki-text-2xl);
  font-weight: var(--giki-font-weight-bold);
  line-height: var(--giki-leading-tight);
  color: hsl(var(--giki-text-primary));
  font-variant-numeric: tabular-nums;
}

.text-financial {
  font-family: var(--giki-font-family-mono);
  font-size: var(--giki-text-base);
  font-weight: var(--giki-font-weight-medium);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-primary));
  font-variant-numeric: tabular-nums;
}

.text-financial-small {
  font-family: var(--giki-font-family-mono);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-normal);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-secondary));
  font-variant-numeric: tabular-nums;
}

/* === GRADIENT TEXT FOR BRANDING === */
.text-gradient {
  background: linear-gradient(
    135deg,
    hsl(var(--giki-brand-green-dark)) 0%,
    hsl(var(--giki-primary)) 25%,
    hsl(var(--giki-brand-blue-dark)) 50%,
    hsl(var(--giki-brand-purple-dark)) 75%,
    hsl(var(--giki-brand-pink-dark)) 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 8s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* === BUTTON TEXT CLASSES === */
.text-button-sm {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-xs);
  font-weight: var(--giki-font-weight-medium);
  line-height: var(--giki-leading-normal);
  letter-spacing: 0.025em;
}

.text-button-base {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-medium);
  line-height: var(--giki-leading-normal);
  letter-spacing: 0.025em;
}

.text-button-lg {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-base);
  font-weight: var(--giki-font-weight-medium);
  line-height: var(--giki-leading-normal);
  letter-spacing: 0.025em;
}

/* === CARD CONTENT CLASSES === */
.text-card-title {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-lg);
  font-weight: var(--giki-font-weight-semibold);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-primary));
}

.text-card-subtitle {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-normal);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-muted));
}

.text-card-metric {
  font-family: var(--giki-font-family-mono);
  font-size: var(--giki-text-3xl);
  font-weight: var(--giki-font-weight-bold);
  line-height: var(--giki-leading-tight);
  color: hsl(var(--giki-text-primary));
  font-variant-numeric: tabular-nums;
}

/* === TABLE TEXT CLASSES === */
.text-table-header {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-medium);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-secondary));
}

.text-table-cell {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-normal);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-primary));
}

.text-table-cell-mono {
  font-family: var(--giki-font-family-mono);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-normal);
  line-height: var(--giki-leading-normal);
  color: hsl(var(--giki-text-primary));
  font-variant-numeric: tabular-nums;
}

/* === STATUS TEXT CLASSES === */
.text-status-badge {
  font-family: var(--giki-font-family-sans);
  font-size: var(--giki-text-xs);
  font-weight: var(--giki-font-weight-semibold);
  line-height: var(--giki-leading-normal);
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.text-muted-foreground {
  color: hsl(var(--giki-text-muted));
}

.text-secondary-foreground {
  color: hsl(var(--giki-text-secondary));
}

.text-primary-foreground {
  color: hsl(var(--giki-text-primary));
}

/* === UTILITY CLASSES === */
.font-feature-settings-mono {
  font-feature-settings: 'tnum', 'zero', 'ss01';
}

.font-feature-settings-sans {
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11', 'ss01';
}

/* === RESPONSIVE TYPOGRAPHY === */
@media (max-width: 640px) {
  .text-heading-1 {
    font-size: var(--giki-text-3xl);
  }

  .text-heading-2 {
    font-size: var(--giki-text-2xl);
  }

  .text-card-metric {
    font-size: var(--giki-text-2xl);
  }
}
