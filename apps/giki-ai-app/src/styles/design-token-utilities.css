/**
 * Design Token Utility Classes
 * Provides easy-to-use CSS classes that map to design tokens
 * Use these classes instead of hard-coded colors throughout the app
 */

/* === BRAND COLOR UTILITIES === */
.bg-brand-primary { background-color: var(--giki-primary); }
.bg-brand-primary-hover { background-color: var(--giki-primary-hover); }
.text-brand-primary { color: var(--giki-primary); }
.border-brand-primary { border-color: var(--giki-primary); }

/* === SEMANTIC COLOR UTILITIES === */
.bg-success { background-color: var(--giki-success); }
.text-success { color: var(--giki-success); }
.border-success { border-color: var(--giki-success); }

.bg-error { background-color: hsl(var(--giki-destructive)); }
.text-error { color: hsl(var(--giki-destructive)); }
.border-error { border-color: hsl(var(--giki-destructive)); }

.bg-warning { background-color: hsl(var(--giki-warning)); }
.text-warning { color: hsl(var(--giki-warning)); }
.border-warning { border-color: hsl(var(--giki-warning)); }

/* === AI SYSTEM COLORS === */
.bg-ai-dark-green { background-color: var(--giki-dark-green); }
.bg-ai-dark-blue { background-color: var(--giki-dark-blue); }
.bg-ai-dark-purple { background-color: var(--giki-dark-purple); }
.bg-ai-dark-red { background-color: var(--giki-dark-red); }

.text-ai-dark-green { color: var(--giki-dark-green); }
.text-ai-dark-blue { color: var(--giki-dark-blue); }
.text-ai-dark-purple { color: var(--giki-dark-purple); }
.text-ai-dark-red { color: var(--giki-dark-red); }

/* === GRADIENT UTILITIES === */
.bg-gradient-ai-primary { background: var(--giki-ai-gradient-primary); }
.bg-gradient-ai-processing { background: var(--giki-ai-gradient-processing); }
.bg-gradient-ai-thinking { background: var(--giki-ai-gradient-thinking); }
.bg-gradient-ai-multi { background: var(--giki-ai-gradient-multi); }

/* === NAVIGATION UTILITIES === */
.bg-nav { background-color: var(--giki-nav-bg); }
.text-nav { color: var(--giki-nav-text); }
.text-nav-active { color: var(--giki-nav-text-active); }
.bg-nav-hover { background-color: var(--giki-nav-bg-hover); }

/* === AGENT PANEL UTILITIES === */
.bg-agent { background-color: var(--giki-agent-bg); }
.border-agent { border-color: var(--giki-agent-border); }
.bg-agent-accent { background-color: var(--giki-agent-accent); }
.text-agent-accent { color: var(--giki-agent-accent); }
.bg-agent-header { background-color: var(--giki-agent-header-bg); }

/* === SPECIFIC UTILITY CLASSES FOR COMMON PATTERNS === */
.btn-brand-primary {
  background-color: var(--giki-primary);
  color: var(--giki-primary-foreground);
}

.btn-brand-primary:hover {
  background-color: var(--giki-primary-hover);
}

.card-professional {
  background-color: var(--giki-card-bg);
  border: 1px solid var(--giki-border-primary);
  box-shadow: var(--giki-card-shadow);
  border-radius: var(--giki-radius-lg);
}

.input-professional {
  background-color: var(--giki-input-bg);
  border: 1px solid var(--giki-input-border);
  color: var(--giki-input-text);
}

.input-professional:focus {
  border-color: var(--giki-input-border-focus);
  box-shadow: 0 0 0 2px var(--giki-primary)/20;
}

/* === OPACITY MODIFIERS === */
.bg-brand-primary\/10 { background-color: var(--giki-primary); opacity: 0.1; }
.bg-brand-primary\/20 { background-color: var(--giki-primary); opacity: 0.2; }
.border-brand-primary\/20 { border-color: var(--giki-primary); opacity: 0.2; }

/* === HOVER STATES === */
.hover\:bg-brand-primary:hover { background-color: var(--giki-primary); }
.hover\:bg-brand-primary-hover:hover { background-color: var(--giki-primary-hover); }
.hover\:text-brand-primary:hover { color: var(--giki-primary); }

/* === FOCUS STATES === */
.focus\:border-brand-primary:focus { border-color: var(--giki-primary); }
.focus\:ring-brand-primary:focus { 
  box-shadow: 0 0 0 2px var(--giki-primary)/20;
}

/* === PROFESSIONAL VISUAL POLISH === */
/* Professional Button System */
.btn-professional {
  padding: 12px 24px;
  border-radius: var(--giki-radius-md);
  font-weight: var(--giki-font-weight-medium);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: var(--giki-text-sm);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-professional:hover {
  transform: translateY(-1px);
  box-shadow: var(--giki-shadow-md);
}

.btn-professional:active {
  transform: translateY(0);
}

/* Professional Card System */
.card-elevated {
  background: var(--giki-card-bg);
  border: 1px solid var(--giki-border-primary);
  border-radius: var(--giki-radius-lg);
  box-shadow: var(--giki-card-shadow);
  transition: all 0.2s ease;
}

.card-elevated:hover {
  box-shadow: var(--giki-card-shadow-premium);
  transform: translateY(-2px);
}

.card-interactive {
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-interactive:hover {
  border-color: var(--giki-primary);
  box-shadow: var(--giki-card-shadow-elevated);
  transform: translateY(-1px);
}

/* Professional Navigation Items */
.nav-item-professional {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--giki-nav-text);
  text-decoration: none;
  transition: all 0.2s ease;
  margin: 2px 12px;
  border-radius: var(--giki-radius-md);
}

.nav-item-professional:hover {
  background: var(--giki-nav-bg-hover);
  color: var(--giki-nav-text-active);
  transform: translateX(2px);
}

.nav-item-professional.active {
  background: var(--giki-nav-bg-active);
  color: var(--giki-nav-text-active);
  border-right: 3px solid var(--giki-primary);
  font-weight: var(--giki-font-weight-medium);
}

/* Professional Input System */
.input-elevated {
  background: var(--giki-input-bg);
  border: 1px solid var(--giki-input-border);
  border-radius: var(--giki-radius-md);
  padding: 12px;
  font-size: var(--giki-text-sm);
  transition: all 0.2s ease;
  box-shadow: var(--giki-shadow-sm);
}

.input-elevated:focus {
  border-color: var(--giki-input-border-focus);
  outline: none;
  box-shadow: 0 0 0 3px var(--giki-primary)/10, var(--giki-shadow-md);
  transform: translateY(-1px);
}

.input-elevated:hover {
  border-color: var(--giki-border-secondary);
  box-shadow: var(--giki-shadow-md);
}

/* Professional Status Indicators */
.status-professional {
  padding: 12px 16px;
  border-radius: var(--giki-radius-md);
  font-size: var(--giki-text-sm);
  font-weight: var(--giki-font-weight-medium);
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
}

.status-professional:hover {
  transform: translateX(2px);
  box-shadow: var(--giki-shadow-sm);
}

.status-success-professional {
  background: var(--giki-success-light)/20;
  color: var(--giki-success);
  border-left-color: var(--giki-success);
}

.status-warning-professional {
  background: hsl(var(--giki-warning))/10;
  color: hsl(var(--giki-warning));
  border-left-color: hsl(var(--giki-warning));
}

.status-error-professional {
  background: hsl(var(--giki-destructive))/10;
  color: hsl(var(--giki-destructive));
  border-left-color: hsl(var(--giki-destructive));
}

.status-info-professional {
  background: hsl(var(--giki-info))/10;
  color: hsl(var(--giki-info));
  border-left-color: hsl(var(--giki-info));
}

/* Professional Table System */
.table-professional {
  width: 100%;
  border-collapse: collapse;
  background: var(--giki-card-bg);
  border-radius: var(--giki-radius-lg);
  overflow: hidden;
  border: 1px solid var(--giki-border-primary);
  box-shadow: var(--giki-card-shadow);
}

.table-professional tr {
  transition: all 0.2s ease;
}

.table-professional tr:hover {
  background: var(--giki-bg-accent);
  transform: scale(1.01);
}

.table-professional th {
  background: var(--giki-bg-secondary);
  padding: 16px;
  text-align: left;
  font-weight: var(--giki-font-weight-semibold);
  color: var(--giki-text-secondary);
  font-size: var(--giki-text-sm);
}

.table-professional td {
  padding: 16px;
  border-top: 1px solid var(--giki-border-primary);
  color: var(--giki-text-primary);
  font-size: var(--giki-text-sm);
}

/* Professional Metric Cards */
.metric-card-professional {
  background: linear-gradient(135deg, var(--giki-success-light)/20 0%, var(--giki-success-light)/5 100%);
  padding: 24px;
  border-radius: var(--giki-radius-lg);
  border: 1px solid var(--giki-success)/20;
  transition: all 0.2s ease;
  cursor: pointer;
}

.metric-card-professional:hover {
  transform: translateY(-2px);
  box-shadow: var(--giki-card-shadow-premium);
  border-color: var(--giki-success)/30;
}

.metric-number-professional {
  font-size: var(--giki-text-3xl);
  font-weight: var(--giki-font-weight-bold);
  color: var(--giki-primary);
  font-family: var(--giki-font-mono);
  line-height: 1.2;
}

.metric-label-professional {
  color: var(--giki-primary);
  font-size: var(--giki-text-sm);
  margin-top: 8px;
  font-weight: var(--giki-font-weight-medium);
}

/* Professional Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* Professional Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, var(--giki-bg-muted) 25%, var(--giki-bg-secondary) 50%, var(--giki-bg-muted) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Professional Tooltip System */
.tooltip-professional {
  position: relative;
  display: inline-block;
}

.tooltip-professional::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--giki-neutral-800);
  color: var(--giki-warm-white);
  padding: 8px 12px;
  border-radius: var(--giki-radius-md);
  font-size: var(--giki-text-xs);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: var(--giki-z-tooltip);
  box-shadow: var(--giki-shadow-lg);
}

.tooltip-professional:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-4px);
}

/* === RESPONSIVE DESIGN UTILITIES === */
/* Mobile-First Responsive Grid System */
.responsive-grid-1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.responsive-grid-2 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.responsive-grid-3 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.responsive-grid-4 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

/* Tablet Breakpoint (768px+) */
@media (min-width: 768px) {
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop Breakpoint (1024px+) */
@media (min-width: 1024px) {
  .responsive-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .responsive-grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Large Desktop Breakpoint (1280px+) */
@media (min-width: 1280px) {
  .responsive-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Mobile Navigation Utilities */
.mobile-nav-hidden {
  display: none;
}

@media (max-width: 767px) {
  .mobile-nav-hidden {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
  }
  
  .mobile-nav-panel {
    position: fixed;
    top: 0;
    left: -100%;
    width: 280px;
    height: 100%;
    background: var(--giki-nav-bg);
    z-index: 50;
    transition: left 0.3s ease;
  }
  
  .mobile-nav-panel.open {
    left: 0;
  }
}

/* Responsive Typography */
.text-responsive-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-responsive-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-responsive-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-responsive-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-responsive-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-responsive-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-responsive-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  
  .text-responsive-xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  
  .text-responsive-2xl {
    font-size: 2rem;
    line-height: 2.5rem;
  }
  
  .text-responsive-3xl {
    font-size: 2.5rem;
    line-height: 3rem;
  }
}

/* Responsive Spacing */
.p-responsive {
  padding: 12px;
}

.p-responsive-lg {
  padding: 16px;
}

.p-responsive-xl {
  padding: 20px;
}

@media (min-width: 768px) {
  .p-responsive {
    padding: 16px;
  }
  
  .p-responsive-lg {
    padding: 24px;
  }
  
  .p-responsive-xl {
    padding: 32px;
  }
}

@media (min-width: 1024px) {
  .p-responsive {
    padding: 20px;
  }
  
  .p-responsive-lg {
    padding: 32px;
  }
  
  .p-responsive-xl {
    padding: 48px;
  }
}

/* Mobile-Optimized Cards */
.card-mobile-optimized {
  background: var(--giki-card-bg);
  border: 1px solid var(--giki-border-primary);
  border-radius: var(--giki-radius-lg);
  box-shadow: var(--giki-card-shadow);
  padding: 16px;
  margin: 8px 0;
}

@media (min-width: 768px) {
  .card-mobile-optimized {
    padding: 24px;
    margin: 12px 0;
  }
}

@media (min-width: 1024px) {
  .card-mobile-optimized {
    padding: 32px;
    margin: 16px 0;
  }
}

/* Touch-Friendly Button Sizing */
.btn-touch-friendly {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 20px;
  border-radius: var(--giki-radius-md);
  font-weight: var(--giki-font-weight-medium);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: var(--giki-text-sm);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Responsive Table */
.table-responsive {
  width: 100%;
  overflow-x: auto;
}

.table-responsive table {
  min-width: 600px;
}

@media (max-width: 767px) {
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .table-responsive table {
    min-width: 500px;
  }
  
  .table-responsive th,
  .table-responsive td {
    padding: 8px 12px;
  }
}

/* Mobile-First Layout Utilities */
.layout-mobile-stack {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

@media (min-width: 768px) {
  .layout-mobile-stack {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

/* Mobile Hide/Show Utilities */
.mobile-hidden {
  display: none;
}

.desktop-hidden {
  display: block;
}

@media (min-width: 768px) {
  .mobile-hidden {
    display: block;
  }
  
  .desktop-hidden {
    display: none;
  }
}