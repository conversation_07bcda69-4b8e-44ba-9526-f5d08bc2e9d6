import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';

interface PanelState {
  isAgentPanelCollapsed: boolean;
  isLeftNavCollapsed: boolean;
  agentPanelWidth: number;
  userPreferences: {
    rememberPanelStates: boolean;
    defaultAgentPanelCollapsed: boolean;
    defaultLeftNavCollapsed: boolean;
    defaultAgentPanelWidth: number;
  };
}

interface PanelStateContextType extends PanelState {
  toggleAgentPanel: () => void;
  toggleLeftNav: () => void;
  setAgentPanelWidth: (width: number) => void;
  resetToDefaults: () => void;
  updateUserPreferences: (
    preferences: Partial<PanelState['userPreferences']>,
  ) => void;
}

const defaultPanelState: PanelState = {
  isAgentPanelCollapsed: true, // Work-first design: AI panel closed by default
  isLeftNavCollapsed: true, // Professional layout: Navigation collapsed by default
  agentPanelWidth: 400,
  userPreferences: {
    rememberPanelStates: true,
    defaultAgentPanelCollapsed: true, // Work-first design: Default to collapsed
    defaultLeftNavCollapsed: true, // Professional layout: Navigation collapsed by default
    defaultAgentPanelWidth: 320,
  },
};

const STORAGE_KEY = 'giki-ai-panel-state';

const PanelStateContext = createContext<PanelStateContextType | undefined>(
  undefined,
);

interface PanelStateProviderProps {
  children: ReactNode;
}

export const PanelStateProvider: React.FC<PanelStateProviderProps> = ({
  children,
}) => {
  const [panelState, setPanelState] = useState<PanelState>(defaultPanelState);

  // Load state from localStorage on mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        // Type-safe JSON parsing with validation
        const parsedState = JSON.parse(savedState) as PanelState;
        // Validate and merge with defaults to handle version changes
        setPanelState({
          ...defaultPanelState,
          ...parsedState,
          userPreferences: {
            ...defaultPanelState.userPreferences,
            ...parsedState.userPreferences,
          },
        });
      }
    } catch (error) {
      // Failed to load panel state from localStorage - using defaults
      // Reset to defaults if corrupted
      localStorage.removeItem(STORAGE_KEY);
    }
  }, []);

  // Save state to localStorage with debouncing to prevent performance issues
  useEffect(() => {
    if (panelState?.userPreferences?.rememberPanelStates) {
      const timeoutId = setTimeout(() => {
        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(panelState));
        } catch (error) {
          // Failed to save panel state to localStorage - continuing without persistence
        }
      }, 300); // Debounce saves by 300ms

      return () => clearTimeout(timeoutId);
    }
  }, [panelState]);

  // Handle window resize for smart panel management with proper mutual exclusion
  useEffect(() => {
    const handleResize = () => {
      const currentWidth = window.innerWidth;

      setPanelState((prev) => {
        // Define breakpoints for consistent behavior
        const isMobile = currentWidth < 768;
        const isTablet = currentWidth >= 768 && currentWidth < 1200;
        const isDesktop = currentWidth >= 1200;

        // MOBILE: Both panels collapsed, agent panel disabled
        if (isMobile) {
          return {
            ...prev,
            isAgentPanelCollapsed: true,
            isLeftNavCollapsed: true,
          };
        }

        // TABLET: Force nav to 64px, allow agent overlay only
        if (isTablet) {
          return {
            ...prev,
            isLeftNavCollapsed: true, // Always collapsed on tablet
            // Agent panel can be open but as overlay, not affecting layout
          };
        }

        // DESKTOP: Allow both panels but with mutual exclusion for optimal content width
        if (isDesktop) {
          const minContentWidth = 600; // Minimum content area width
          const navWidth = prev.isLeftNavCollapsed ? 64 : 240;
          const agentWidth = prev.isAgentPanelCollapsed ? 0 : 400;
          const totalPanelWidth = navWidth + agentWidth;
          const availableContentWidth = currentWidth - totalPanelWidth;

          // If content area would be too narrow, prioritize agent panel
          if (availableContentWidth < minContentWidth && !prev.isAgentPanelCollapsed && !prev.isLeftNavCollapsed) {
            return {
              ...prev,
              isLeftNavCollapsed: true, // Collapse nav to preserve content width
            };
          }

          // If agent panel is collapsed and screen is wide enough, restore nav based on user preference
          if (
            prev.isAgentPanelCollapsed &&
            prev.isLeftNavCollapsed &&
            currentWidth >= 1400 &&
            !prev?.userPreferences?.defaultLeftNavCollapsed
          ) {
            return {
              ...prev,
              isLeftNavCollapsed: false,
            };
          }
        }

        return prev;
      });
    };

    window.addEventListener('resize', handleResize);

    // Initial check on mount
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const toggleAgentPanel = () => {
    setPanelState((prev) => {
      const currentWidth = window.innerWidth;
      const isMobile = currentWidth < 768;
      const isTablet = currentWidth >= 768 && currentWidth < 1200;
      const isDesktop = currentWidth >= 1200;

      // MOBILE: Agent panel is disabled
      if (isMobile) {
        return prev; // No change on mobile
      }

      const newCollapsed = !prev.isAgentPanelCollapsed;

      // TABLET: Agent panel opens as overlay, doesn't affect nav
      if (isTablet) {
        return {
          ...prev,
          isAgentPanelCollapsed: newCollapsed,
          // Nav stays collapsed on tablet regardless of agent state
        };
      }

      // DESKTOP: Apply mutual exclusion logic based on screen width
      if (isDesktop) {
        let shouldCollapseLeftNav = prev.isLeftNavCollapsed;

        if (!newCollapsed) {
          // Agent panel is expanding - check if we need to collapse nav
          const minContentWidth = 600;
          const navWidth = prev.isLeftNavCollapsed ? 64 : 240;
          const agentWidth = 400;
          const totalPanelWidth = navWidth + agentWidth;
          const availableContentWidth = currentWidth - totalPanelWidth;

          // If content would be too narrow, collapse nav for focused AI experience
          if (availableContentWidth < minContentWidth || currentWidth < 1400) {
            shouldCollapseLeftNav = true;
          }
        } else {
          // Agent panel is collapsing - restore nav based on screen size and user preference
          if (currentWidth >= 1400 && !prev?.userPreferences?.defaultLeftNavCollapsed) {
            shouldCollapseLeftNav = false;
          }
        }

        return {
          ...prev,
          isAgentPanelCollapsed: newCollapsed,
          isLeftNavCollapsed: shouldCollapseLeftNav,
        };
      }

      return prev;
    });
  };

  const toggleLeftNav = () => {
    setPanelState((prev) => {
      const currentWidth = window.innerWidth;
      const isMobile = currentWidth < 768;
      const isTablet = currentWidth >= 768 && currentWidth < 1200;
      const isDesktop = currentWidth >= 1200;

      // MOBILE: Never allow nav expansion
      if (isMobile) {
        return prev; // No change on mobile
      }

      // TABLET: Never allow nav expansion beyond 64px
      if (isTablet) {
        return prev; // No change on tablet
      }

      // DESKTOP: Apply mutual exclusion logic
      if (isDesktop) {
        const newNavCollapsed = !prev.isLeftNavCollapsed;
        
        // If expanding nav and agent panel is also open, check content width
        if (!newNavCollapsed && !prev.isAgentPanelCollapsed) {
          const minContentWidth = 600;
          const navWidth = 240; // Expanded nav width
          const agentWidth = 400; // Agent panel width
          const totalPanelWidth = navWidth + agentWidth;
          const availableContentWidth = currentWidth - totalPanelWidth;

          // If content would be too narrow, collapse agent panel to make room
          if (availableContentWidth < minContentWidth) {
            return {
              ...prev,
              isLeftNavCollapsed: newNavCollapsed,
              isAgentPanelCollapsed: true, // Auto-collapse agent to make room for nav
            };
          }
        }

        return {
          ...prev,
          isLeftNavCollapsed: newNavCollapsed,
        };
      }

      return prev;
    });
  };

  const setAgentPanelWidth = (width: number) => {
    // Constrain width between 320px and 600px
    const constrainedWidth = Math.max(320, Math.min(600, width));
    setPanelState((prev) => ({
      ...prev,
      agentPanelWidth: constrainedWidth,
    }));
  };

  const resetToDefaults = () => {
    setPanelState((prev) => ({
      ...prev,
      isAgentPanelCollapsed: prev?.userPreferences?.defaultAgentPanelCollapsed,
      isLeftNavCollapsed: prev?.userPreferences?.defaultLeftNavCollapsed,
      agentPanelWidth: prev?.userPreferences?.defaultAgentPanelWidth,
    }));
  };

  const updateUserPreferences = (
    preferences: Partial<PanelState['userPreferences']>,
  ) => {
    setPanelState((prev) => ({
      ...prev,
      userPreferences: {
        ...prev.userPreferences,
        ...preferences,
      },
    }));
  };

  const contextValue: PanelStateContextType = {
    ...panelState,
    toggleAgentPanel,
    toggleLeftNav,
    setAgentPanelWidth,
    resetToDefaults,
    updateUserPreferences,
  };

  return (
    <PanelStateContext.Provider value={contextValue}>
      {children}
    </PanelStateContext.Provider>
  );
};

export const usePanelState = (): PanelStateContextType => {
  const context = useContext(PanelStateContext);
  if (context === undefined) {
    throw new Error('usePanelState must be used within a PanelStateProvider');
  }
  return context;
};

export default PanelStateProvider;
