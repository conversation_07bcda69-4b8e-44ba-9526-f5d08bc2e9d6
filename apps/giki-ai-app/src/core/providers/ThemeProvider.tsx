import React, { createContext, useContext, useEffect } from 'react';

// Finance users prefer light themes like Excel and banking apps - no dark theme support
type Theme = 'light';

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
}

interface ThemeProviderState {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

const initialState: ThemeProviderState = {
  theme: 'light',
  setTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  // Always use light theme for finance users
  const theme: Theme = 'light';

  useEffect(() => {
    const root = window?.document?.documentElement;

    // Always apply light theme - finance users expect Excel/banking app experience
    if (root) {
      root.classList.remove('dark');
      root.classList.add('light');
      root.setAttribute('data-theme', 'light');
    }
  }, []);

  const value = {
    theme,
    setTheme: () => {
      // No-op: theme switching disabled for finance users
    },
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined)
    throw new Error('useTheme must be used within a ThemeProvider');

  return context;
};
