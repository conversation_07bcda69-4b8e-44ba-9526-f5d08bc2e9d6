export interface Environment {
  API_BASE_URL: string;
  ENVIRONMENT: 'development' | 'staging' | 'production';
}

export const env: Environment = {
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL as string,
  ENVIRONMENT: import.meta.env.VITE_ENVIRONMENT as
    | 'development'
    | 'staging'
    | 'production',
};

// Add runtime validation
if (!env.API_BASE_URL) throw new Error('VITE_API_BASE_URL is required');
if (!['development', 'staging', 'production'].includes(env.ENVIRONMENT)) {
  throw new Error('Invalid VITE_ENVIRONMENT value');
}

// Helper functions for environment checks
export const isDevelopment = () => env.ENVIRONMENT === 'development';
export const isStaging = () => env.ENVIRONMENT === 'staging';
export const isProduction = () => env.ENVIRONMENT === 'production';

export default env;
