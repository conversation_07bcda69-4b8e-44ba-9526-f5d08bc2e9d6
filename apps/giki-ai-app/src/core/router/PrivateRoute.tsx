import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import useAuthStore from '@/shared/services/auth/authStore';
import { Loading } from '@/shared/components/ui/loading';

interface PrivateRouteProps {
  children: React.ReactNode;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore();
  const location = useLocation();

  // Check authentication status when component mounts
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  if (isLoading) {
    return (
      <div className="flex flex-wrap justify-center items-center min-h-screen">
        <Loading size="lg" text="Verifying authentication..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    // User not authenticated, redirect to login page, saving the current location they were trying to access
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // User is authenticated, render the child route content
  return <>{children}</>;
};

export default PrivateRoute;
