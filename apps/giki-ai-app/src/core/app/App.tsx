import React, { useState, useCallback, useEffect, Suspense } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';

// Shared Components and Providers
import {
  ErrorBoundary,
  Loading,
  ThemeProvider,
  ToastProvider,
  ToastViewport,
  TooltipProvider,
} from '@/shared/components';
import { EnhancedAppLayout } from '@/shared/components/layout/EnhancedAppLayout';
import { PanelStateProvider } from '@/core/providers/PanelStateContext';
import { skipToMain } from '@/shared/utils/accessibility';
import { IntelligentRoute } from '@/shared/components/routing/IntelligentRoute';
import { SessionRecoveryProvider } from '@/features/auth/providers/SessionRecoveryProvider';
import { logger } from '@/shared/utils/errorHandling';
import '@/shared/styles/accessibility.css';
import '@/styles/layout-variables.css';

// Feature Imports using new structure
import { useAuth } from '@/features/auth';

// Lazy-loaded page components for code splitting
const LoginPage = React.lazy(() => import('@/features/auth/pages/LoginPage'));
const RegisterPage = React.lazy(() => import('@/features/auth/pages/RegisterPage'));
const GetStartedPage = React.lazy(() => import('@/features/auth/pages/GetStartedPage'));
const FirstLoginPage = React.lazy(() => import('@/features/auth/pages/FirstLoginPage'));

const TransactionReviewPage = React.lazy(() => import('@/features/transactions/pages/TransactionReviewPage'));

const UploadPage = React.lazy(() => import('@/features/files/pages/UploadPage'));
const ProcessingPage = React.lazy(() => import('@/features/files/pages/ProcessingPage'));
const ResultsPage = React.lazy(() => import('@/features/files/pages/ResultsPage'));

const ReportsPage = React.lazy(() => import('@/features/reports/pages/ReportsPage'));
const ExportPage = React.lazy(() => import('@/features/reports/pages/ExportPage'));

const DashboardPage = React.lazy(() => import('@/features/dashboard/pages/DashboardPage'));

// Import real implementations and services (not lazy-loaded)
import { ColumnMappingModal } from '../../features/files';
import { submitColumnMapping } from '@/features/files/services/fileService';

// Continue lazy loading for page components
const SettingsPage = React.lazy(() => import('@/features/admin/pages/SettingsPage'));
const CategoriesPage = React.lazy(() => import('@/features/categories/pages/CategoriesPage'));
const OnboardingWizardPage = React.lazy(() => import('@/features/onboarding/pages/OnboardingWizardPage'));

// Lazy-loaded accuracy testing pages
const AccuracyTestsPage = React.lazy(() => import('@/features/accuracy/pages/AccuracyTestsPage'));
const CreateAccuracyTestPage = React.lazy(() => import('@/features/accuracy/pages/CreateAccuracyTestPage'));
const TestResultsPage = React.lazy(() => import('@/features/accuracy/pages/TestResultsPage'));
const HistoricalAccuracyDashboardPage = React.lazy(() => import('@/features/accuracy/pages/HistoricalAccuracyDashboardPage'));
const DataLearningWorkflowPage = React.lazy(() => import('@/features/accuracy/pages/HistoricalAccuracyWorkflowPage'));
const StructuredSetupWorkflowPage = React.lazy(() => import('@/features/accuracy/pages/SchemaGuidedWorkflowPage'));

// Lazy-loaded Financial Intelligence pages
const CashflowDashboardPage = React.lazy(() => import('@/features/intelligence/pages/CashflowDashboardPage'));
const ProfitLossPage = React.lazy(() => import('@/features/intelligence/pages/ProfitLossPage'));
const BudgetVariancePage = React.lazy(() => import('@/features/intelligence/pages/BudgetVariancePage'));
const VendorSpendPage = React.lazy(() => import('@/features/intelligence/pages/VendorSpendPage'));
const FinancialForecastingPage = React.lazy(() => import('@/features/intelligence/pages/FinancialForecastingPage'));

// Error pages (not lazy-loaded as they're needed immediately on errors)
import NotFoundPage from '@/shared/components/error/NotFoundPage';

// Lazy-loaded Transaction Intelligence pages
const SmartReviewPage = React.lazy(() => import('@/features/transactions/pages/SmartReviewPage'));
const DuplicateDetectionPage = React.lazy(() => import('@/features/transactions/pages/DuplicateDetectionPage'));
const RecurringRulesPage = React.lazy(() => import('@/features/transactions/pages/RecurringRulesPage'));
const SearchFilterPage = React.lazy(() => import('@/features/transactions/pages/SearchFilterPage'));

// Lazy-loaded Reporting Suite pages
const ExecutiveSummaryPage = React.lazy(() => import('@/features/reports/pages/ExecutiveSummaryPage'));
const GLAnalysisPage = React.lazy(() => import('@/features/reports/pages/GLAnalysisPage'));
const TaxDashboardPage = React.lazy(() => import('@/features/reports/pages/TaxDashboardPage'));
const CustomReportBuilderPage = React.lazy(() => import('@/features/reports/pages/CustomReportBuilderPage'));
const MonthlySummaryPage = React.lazy(() => import('@/features/reports/pages/MonthlySummaryPage'));
const ExpenseTrendsPage = React.lazy(() => import('@/features/reports/pages/ExpenseTrendsPage'));

// Lazy-loaded Business Intelligence pages
const TrendsAnalysisPage = React.lazy(() => import('@/features/intelligence/pages/TrendsAnalysisPage'));
const PatternRecognitionPage = React.lazy(() => import('@/features/intelligence/pages/PatternRecognitionPage'));
const AnomalyDetectionPage = React.lazy(() => import('@/features/intelligence/pages/AnomalyDetectionPage'));
const ROIAnalysisPage = React.lazy(() => import('@/features/intelligence/pages/ROIAnalysisPage'));

// Lazy-loaded Integration & Operations pages
const AccountingSyncPage = React.lazy(() => import('@/features/admin/pages/AccountingSyncPage'));
const BankConnectionsPage = React.lazy(() => import('@/features/admin/pages/BankConnectionsPage'));
const AuditTrailPage = React.lazy(() => import('@/features/admin/pages/AuditTrailPage'));

// Lazy-loaded Admin & Settings pages
const UserManagementPage = React.lazy(() => import('@/features/admin/pages/UserManagementPage'));
const HelpHubPage = React.lazy(() => import('@/features/admin/pages/HelpHubPage'));

// Lazy-loaded onboarding pages
const ColumnMappingPage = React.lazy(() => import('@/features/onboarding/pages/ColumnMappingPage'));
const AITrainingPage = React.lazy(() => import('@/features/onboarding/pages/AITrainingPage'));
const AccuracyValidationPage = React.lazy(() => import('@/features/onboarding/pages/AccuracyValidationPage'));
const GoLivePage = React.lazy(() => import('@/features/onboarding/pages/GoLivePage'));

// Lazy-loaded MIS setup pages
const MISSetupIntroduction = React.lazy(() => import('@/features/onboarding/pages/MISSetupIntroduction'));
const MISCompanySetup = React.lazy(() => import('@/features/onboarding/pages/MISCompanySetup'));
const MISDataUpload = React.lazy(() => import('@/features/onboarding/pages/MISDataUpload'));
const MISEnhancementReview = React.lazy(() => import('@/features/onboarding/pages/MISEnhancementReview'));
const MISActivation = React.lazy(() => import('@/features/onboarding/pages/MISActivation'));

// Import Test Page for debugging (commented out - file doesn't exist)
// import TestAuthPage from '@/features/test/TestAuthPage';

// Import Agent Test Page (not lazy-loaded as it's for debugging)
import AgentTestPage from '@/features/agent/pages/AgentTestPage';

// Protected Route component
interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading, checkAuth } = useAuth();

  React.useEffect(() => {
    // Check auth status on component mount
    checkAuth();
  }, [checkAuth]);

  // Show loading while checking authentication
  if (isLoading) {
    return <Loading fullPage text="Checking authentication..." />;
  }

  // Return protected content or redirect to login
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

// Main App component with new domain-driven structure
function App() {
  const navigate = useNavigate();
  const { checkAuth } = useAuth();

  // Check authentication status on app load
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Add keyboard shortcut for skip to main
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Alt + M to skip to main content
      if (e.altKey && e.key === 'm') {
        e.preventDefault();
        skipToMain();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // State for Column Mapping Modal
  const [isMappingModalOpen, setIsMappingModalOpen] = useState(false);
  const [currentUploadId, setCurrentUploadId] = useState<string | null>(null);

  // Callback to update file processing results in upload component
  const [onFileProcessingComplete, setOnFileProcessingComplete] = useState<
    | ((
        uploadId: string,
        reportId: string | null,
        processingStats?: {
          totalRows?: number;
          successfulRows?: number;
          failedRows?: number;
          dataQualityScore?: number;
        },
      ) => void)
    | null
  >(null);

  // Handler for successful file upload with optional callback for processing completion
  const _handleUploadSuccessInApp = (
    uploadId: string,
    processingCompleteCallback?: (
      uploadId: string,
      reportId: string | null,
      processingStats?: {
        totalRows?: number;
        successfulRows?: number;
        failedRows?: number;
        dataQualityScore?: number;
      },
    ) => void,
  ) => {
    setCurrentUploadId(uploadId);
    setIsMappingModalOpen(true);

    // Store the callback to use after processing is complete
    if (processingCompleteCallback) {
      setOnFileProcessingComplete(() => processingCompleteCallback);
    }
  };

  // Handler for successful mapping confirmation
  const handleMappingConfirm = useCallback(
    async (uploadId: string, mapping: Record<string, string | null>) => {
      if (!uploadId) {
        setIsMappingModalOpen(false);
        return;
      }

      try {
        // Use the imported submitColumnMapping function

        // Submit column mapping and process transactions
        const processingResult = await submitColumnMapping(uploadId, mapping);

        if ('type' in processingResult) {
          // Handle API error
          throw new Error(processingResult.message);
        }

        // Close modal first
        setIsMappingModalOpen(false);

        // Update file processing results if callback is available
        if (onFileProcessingComplete && processingResult.report_id) {
          // Calculate processing stats from response if available
          const processingStats = {
            totalRows: processingResult.records_processed || 0,
            successfulRows: processingResult.records_processed || 0,
            failedRows: 0, // Would need to calculate from errors if available
            dataQualityScore: processingResult.errors?.length === 0 ? 100 : 85, // Simple calculation
          };

          onFileProcessingComplete(
            uploadId,
            processingResult.report_id,
            processingStats,
          );
        }

        setCurrentUploadId(null);

        // Navigate to processing view to follow proper upload workflow
        navigate(`/processing/${uploadId}`);
      } catch (err) {
        logger.error('Error processing column mapping', 'App', err as Error);
        setIsMappingModalOpen(false);
      }
    },
    [navigate, onFileProcessingComplete],
  );

  return (
    <ThemeProvider defaultTheme="light">
      <ToastProvider>
        <TooltipProvider>
          <PanelStateProvider>
            <SessionRecoveryProvider>
              <div className="min-h-screen bg-background text-foreground">
                {/* Skip to main content link for keyboard navigation */}
                <a
                  href="#main-content"
                  className="skip-link"
                  onClick={(e) => {
                    e.preventDefault();
                    skipToMain();
                  }}
                >
                  Skip to main content
                </a>
                <ErrorBoundary>
                  <Routes>
                    {/* Authentication Routes */}
                    <Route path="/login" element={<LoginPage />} />
                    <Route path="/register" element={<RegisterPage />} />
                    <Route path="/get-started" element={<GetStartedPage />} />
                    <Route
                      path="/first-login"
                      element={
                        <ProtectedRoute>
                          <FirstLoginPage />
                        </ProtectedRoute>
                      }
                    />

                    {/* Landing Page - Intelligent Routing Based on User Journey */}
                    <Route
                      path="/"
                      element={
                        <ProtectedRoute>
                          <IntelligentRoute />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/dashboard"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading dashboard..."
                                  />
                                }
                              >
                                <DashboardPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/onboarding"
                      element={<Navigate to="/onboarding/wizard" replace />}
                    />
                    <Route
                      path="/onboarding/wizard"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading onboarding wizard..."
                                  />
                                }
                              >
                                <OnboardingWizardPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/onboarding/mapping"
                      element={
                        <ProtectedRoute>
                          <ErrorBoundary>
                            <Suspense
                              fallback={
                                <Loading
                                  fullPage
                                  text="Loading column mapping..."
                                />
                              }
                            >
                              <ColumnMappingPage />
                            </Suspense>
                          </ErrorBoundary>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/onboarding/training"
                      element={
                        <ProtectedRoute>
                          <ErrorBoundary>
                            <Suspense
                              fallback={
                                <Loading
                                  fullPage
                                  text="Loading AI training..."
                                />
                              }
                            >
                              <AITrainingPage />
                            </Suspense>
                          </ErrorBoundary>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/onboarding/validation"
                      element={
                        <ProtectedRoute>
                          <ErrorBoundary>
                            <Suspense
                              fallback={
                                <Loading
                                  fullPage
                                  text="Loading accuracy validation..."
                                />
                              }
                            >
                              <AccuracyValidationPage />
                            </Suspense>
                          </ErrorBoundary>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/onboarding/go-live"
                      element={
                        <ProtectedRoute>
                          <ErrorBoundary>
                            <Suspense
                              fallback={
                                <Loading fullPage text="Loading go live..." />
                              }
                            >
                              <GoLivePage />
                            </Suspense>
                          </ErrorBoundary>
                        </ProtectedRoute>
                      }
                    />

                    {/* MIS Setup Routes */}
                    <Route
                      path="/onboarding/introduction"
                      element={
                        <ProtectedRoute>
                          <ErrorBoundary>
                            <Suspense
                              fallback={
                                <Loading
                                  fullPage
                                  text="Loading MIS introduction..."
                                />
                              }
                            >
                              <MISSetupIntroduction />
                            </Suspense>
                          </ErrorBoundary>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/onboarding/company-setup"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading company setup..."
                                  />
                                }
                              >
                                <MISCompanySetup />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/onboarding/data-upload"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading data upload..."
                                  />
                                }
                              >
                                <MISDataUpload />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/onboarding/enhancement-review"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading enhancement review..."
                                  />
                                }
                              >
                                <MISEnhancementReview />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/onboarding/mis-activation"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading MIS activation..."
                                  />
                                }
                              >
                                <MISActivation />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />

                    {/* Transactions - Upload and Review */}
                    <Route
                      path="/transactions"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading transactions..."
                                  />
                                }
                              >
                                <TransactionReviewPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/upload"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading fullPage text="Loading upload interface..." />
                                }
                              >
                                <UploadPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/processing"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading fullPage text="Loading AI processing..." />
                                }
                              >
                                <ProcessingPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/processing/:uploadId"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading fullPage text="Loading AI processing..." />
                                }
                              >
                                <ProcessingPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/results/:uploadId"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading fullPage text="Loading results..." />
                                }
                              >
                                <ResultsPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />

                    {/* Reports and Export */}
                    <Route
                      path="/reports"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading fullPage text="Loading reports..." />
                                }
                              >
                                <ReportsPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/export"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading fullPage text="Loading export..." />
                                }
                              >
                                <ExportPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/admin"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading settings..."
                                  />
                                }
                              >
                                <SettingsPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />

                    {/* Legacy Route Redirects - Business-Friendly */}
                    <Route
                      path="/work"
                      element={<Navigate to="/upload" replace />}
                    />
                    <Route
                      path="/transactions/upload"
                      element={<Navigate to="/upload" replace />}
                    />
                    <Route
                      path="/transaction-analysis"
                      element={<Navigate to="/transactions" replace />}
                    />
                    <Route
                      path="/categories"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading categories..."
                                  />
                                }
                              >
                                <CategoriesPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />

                    {/* Accuracy Testing Routes */}
                    <Route
                      path="/accuracy"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading accuracy tests..."
                                  />
                                }
                              >
                                <AccuracyTestsPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/accuracy/create"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading test creation..."
                                  />
                                }
                              >
                                <CreateAccuracyTestPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/accuracy/tests/:testId/results"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading test results..."
                                  />
                                }
                              >
                                <TestResultsPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/accuracy/historical-dashboard"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading historical accuracy dashboard..."
                                  />
                                }
                              >
                                <HistoricalAccuracyDashboardPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/accuracy/data-learning-workflow"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading data learning workflow..."
                                  />
                                }
                              >
                                <DataLearningWorkflowPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/accuracy/structured-setup-workflow"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading structured setup workflow..."
                                  />
                                }
                              >
                                <StructuredSetupWorkflowPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />

                    {/* Legacy Route Redirects */}
                    <Route
                      path="/accuracy/m2-dashboard"
                      element={
                        <Navigate to="/accuracy/historical-dashboard" replace />
                      }
                    />
                    <Route
                      path="/accuracy/m2-workflow"
                      element={
                        <Navigate
                          to="/accuracy/data-learning-workflow"
                          replace
                        />
                      }
                    />
                    <Route
                      path="/accuracy/historical-workflow"
                      element={
                        <Navigate
                          to="/accuracy/data-learning-workflow"
                          replace
                        />
                      }
                    />
                    <Route
                      path="/reports-index"
                      element={<Navigate to="/reports" replace />}
                    />
                    <Route
                      path="/reports/processing"
                      element={<Navigate to="/reports" replace />}
                    />
                    <Route
                      path="/reports/processing/:reportId"
                      element={<Navigate to="/reports" replace />}
                    />
                    <Route
                      path="/knowledge-hub"
                      element={<Navigate to="/admin" replace />}
                    />
                    <Route
                      path="/review"
                      element={<Navigate to="/transactions" replace />}
                    />
                    <Route
                      path="/settings"
                      element={<Navigate to="/admin" replace />}
                    />
                    <Route
                      path="/temporal-validation"
                      element={<Navigate to="/admin" replace />}
                    />
                    <Route
                      path="/rag-corpus-management"
                      element={<Navigate to="/admin" replace />}
                    />

                    {/* Financial Intelligence Routes */}
                    <Route
                      path="/intelligence"
                      element={<Navigate to="/intelligence/cashflow" replace />}
                    />
                    <Route
                      path="/intelligence/cashflow"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading cashflow dashboard..."
                                  />
                                }
                              >
                                <CashflowDashboardPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/intelligence/profit-loss"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading P&L statement..."
                                  />
                                }
                              >
                                <ProfitLossPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/intelligence/budget-variance"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading budget variance..."
                                  />
                                }
                              >
                                <BudgetVariancePage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/intelligence/vendor-spend"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading vendor analysis..."
                                  />
                                }
                              >
                                <VendorSpendPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/intelligence/forecasting"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading financial forecasting..."
                                  />
                                }
                              >
                                <FinancialForecastingPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/intelligence/trends"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading trends analysis..."
                                  />
                                }
                              >
                                <TrendsAnalysisPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/intelligence/patterns"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading pattern recognition..."
                                  />
                                }
                              >
                                <PatternRecognitionPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/intelligence/anomalies"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading anomaly detection..."
                                  />
                                }
                              >
                                <AnomalyDetectionPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/intelligence/roi"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading ROI analysis..."
                                  />
                                }
                              >
                                <ROIAnalysisPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />

                    {/* Transaction Intelligence Routes */}
                    <Route
                      path="/transactions/smart-review"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading smart review..."
                                  />
                                }
                              >
                                <SmartReviewPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/transactions/duplicates"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading duplicate detection..."
                                  />
                                }
                              >
                                <DuplicateDetectionPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/transactions/rules"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading recurring rules..."
                                  />
                                }
                              >
                                <RecurringRulesPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/transactions/search"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading search & filter..."
                                  />
                                }
                              >
                                <SearchFilterPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />

                    {/* Reports Routes */}
                    <Route
                      path="/reports/monthly-summary"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading monthly summary..."
                                  />
                                }
                              >
                                <MonthlySummaryPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/reports/builder"
                      element={<Navigate to="/reports/custom" replace />}
                    />
                    <Route
                      path="/reports/expense-trends"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading expense trends..."
                                  />
                                }
                              >
                                <ExpenseTrendsPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/reports/tax-export"
                      element={<Navigate to="/reports/tax" replace />}
                    />
                    <Route
                      path="/reports/budget-analysis"
                      element={
                        <Navigate to="/intelligence/budget-variance" replace />
                      }
                    />

                    {/* Files Route - Quick Fix Redirect */}
                    <Route
                      path="/files"
                      element={<Navigate to="/transactions" replace />}
                    />

                    {/* Extended Reporting Routes */}
                    <Route
                      path="/reports/executive"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading executive summary..."
                                  />
                                }
                              >
                                <ExecutiveSummaryPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/reports/gl-analysis"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading GL analysis..."
                                  />
                                }
                              >
                                <GLAnalysisPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/reports/tax"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading tax dashboard..."
                                  />
                                }
                              >
                                <TaxDashboardPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/reports/custom"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading custom report builder..."
                                  />
                                }
                              >
                                <CustomReportBuilderPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />

                    {/* Integration & Operations Routes */}
                    <Route
                      path="/admin/accounting-sync"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading accounting sync..."
                                  />
                                }
                              >
                                <AccountingSyncPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/admin/bank-connections"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading bank connections..."
                                  />
                                }
                              >
                                <BankConnectionsPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/admin/audit-trail"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading audit trail..."
                                  />
                                }
                              >
                                <AuditTrailPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/admin/users"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading user management..."
                                  />
                                }
                              >
                                <UserManagementPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/help"
                      element={
                        <ProtectedRoute>
                          <EnhancedAppLayout>
                            <ErrorBoundary>
                              <Suspense
                                fallback={
                                  <Loading
                                    fullPage
                                    text="Loading help hub..."
                                  />
                                }
                              >
                                <HelpHubPage />
                              </Suspense>
                            </ErrorBoundary>
                          </EnhancedAppLayout>
                        </ProtectedRoute>
                      }
                    />

                    {/* Mobile Routes - Redirect to responsive dashboard */}
                    <Route
                      path="/mobile"
                      element={<Navigate to="/dashboard" replace />}
                    />

                    {/* Debug Routes - Remove in production */}
                    <Route
                      path="/debug/agent"
                      element={
                        <ProtectedRoute>
                          <ErrorBoundary>
                            <Suspense
                              fallback={
                                <Loading
                                  fullPage
                                  text="Loading agent test..."
                                />
                              }
                            >
                              <AgentTestPage />
                            </Suspense>
                          </ErrorBoundary>
                        </ProtectedRoute>
                      }
                    />
                    {/* 
                    <Route
                      path="/debug/auth"
                      element={
                        <ProtectedRoute>
                          <ErrorBoundary>
                            <Suspense
                              fallback={
                                <Loading
                                  fullPage
                                  text="Loading auth debug..."
                                />
                              }
                            >
                              <TestAuthPage />
                            </Suspense>
                          </ErrorBoundary>
                        </ProtectedRoute>
                      }
                    />
                    */}

                    {/* Redirect legacy routes */}

                    {/* Professional 404 route with brand integration */}
                    <Route path="*" element={<NotFoundPage />} />
                  </Routes>

                  {/* Column Mapping Modal */}
                  {isMappingModalOpen && (
                    <ColumnMappingModal
                      isOpen={isMappingModalOpen}
                      onClose={() => {
                        setIsMappingModalOpen(false);
                        setCurrentUploadId(null);
                      }}
                      onConfirm={(uploadId, mapping) =>
                        void handleMappingConfirm(uploadId, mapping)
                      }
                      uploadId={currentUploadId}
                    />
                  )}
                </ErrorBoundary>
              </div>
            </SessionRecoveryProvider>
          </PanelStateProvider>
          <ToastViewport />
        </TooltipProvider>
      </ToastProvider>
    </ThemeProvider>
  );
}

export default App;
