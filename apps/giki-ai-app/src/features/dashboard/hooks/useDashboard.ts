import { useState, useEffect, useCallback, useRef } from 'react';
import {
  getDashboardData,
  refreshDashboardData,
} from '../services/dashboardService';
import type { DashboardError, DashboardState } from '../types/dashboard';
import { useWebSocket, useWebSocketStatus } from '@/shared/services/websocket/WebSocketService';

// Import DashboardData type
import type { DashboardData } from '../types/dashboard';

// Global state for request deduplication
let globalRefreshPromise: Promise<DashboardData> | null = null;
let globalRefreshTimestamp = 0;
const DEBOUNCE_DELAY = 1000; // 1 second debounce

interface UseDashboardOptions {
  dateRange?: { from: string; to: string };
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseDashboardReturn extends DashboardState {
  refresh: () => Promise<void>;
  setDateRange: (dateRange: { from: string; to: string }) => void;
  isRefreshing: boolean;
}

export const useDashboard = (
  options: UseDashboardOptions = {},
): UseDashboardReturn => {
  const {
    dateRange: initialDateRange,
    autoRefresh = false,
    refreshInterval = 5 * 60 * 1000, // 5 minutes
  } = options;

  const [state, setState] = useState<DashboardState>({
    data: null,
    loading: true,
    error: null,
    lastUpdated: null,
  });

  const [dateRange, setDateRangeState] = useState(initialDateRange);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const debounceTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());
  
  // WebSocket connection status
  const { connected: isWebSocketConnected } = useWebSocketStatus();

  const loadDashboardData = useCallback(
    async (showLoading = true) => {
      // Request deduplication - prevent concurrent calls
      const now = Date.now();
      if (
        globalRefreshPromise &&
        now - globalRefreshTimestamp < DEBOUNCE_DELAY
      ) {
        // Reuse existing request to prevent duplication
        try {
          const data = await globalRefreshPromise;
          setState({
            data,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          });
        } catch (error) {
          // Handle error from shared request
          const dashboardError: DashboardError = {
            code: 'DASHBOARD_LOAD_ERROR',
            message:
              error instanceof Error
                ? error.message
                : 'Failed to load dashboard data',
            retryable: true,
          };
          setState({
            data: null,
            loading: false,
            error: dashboardError,
            lastUpdated: null,
          });
        }
        return;
      }

      if (showLoading) {
        setState((prev) => ({ ...prev, loading: true, error: null }));
      }

      // Create new request and share it globally
      globalRefreshTimestamp = now;
      globalRefreshPromise = getDashboardData(dateRange);

      try {
        const data = await globalRefreshPromise;
        setState({
          data,
          loading: false,
          error: null,
          lastUpdated: new Date().toISOString(),
        });
      } catch (error) {
        const dashboardError: DashboardError = {
          code: 'DASHBOARD_LOAD_ERROR',
          message:
            error instanceof Error
              ? error.message
              : 'Failed to load dashboard data',
          retryable: true,
        };

        setState({
          data: null,
          loading: false,
          error: dashboardError,
          lastUpdated: null,
        });
      } finally {
        // Clear global promise after completion
        setTimeout(() => {
          globalRefreshPromise = null;
        }, 100);
      }
    },
    [dateRange],
  );

  const refresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      const data = await refreshDashboardData(dateRange);
      setState({
        data,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
      });
    } catch (error) {
      const dashboardError: DashboardError = {
        code: 'DASHBOARD_REFRESH_ERROR',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to refresh dashboard data',
        retryable: true,
      };

      setState((prev) => ({
        ...prev,
        error: dashboardError,
      }));
    } finally {
      setIsRefreshing(false);
    }
  }, [dateRange]);

  const setDateRange = useCallback(
    (newDateRange: { from: string; to: string }) => {
      setDateRangeState(newDateRange);
    },
    [],
  );

  // WebSocket real-time event handlers for dashboard updates
  useWebSocket('transaction.categorized', (payload: any) => {
    // Incrementally update dashboard metrics when transactions are categorized
    setState(prev => {
      if (!prev.data) return prev;
      return {
        ...prev,
        data: {
          ...prev.data,
          metrics: {
            ...prev.data.metrics,
            totalTransactions: (prev.data.metrics.totalTransactions || 0) + 1,
            categorizedTransactions: (prev.data.metrics.categorizedTransactions || 0) + 1,
          },
        },
        lastUpdated: new Date().toISOString(),
      };
    });
  }, []);

  useWebSocket('file.processed', (payload: any) => {
    // Refresh dashboard when new files are processed
    if (payload.status === 'completed') {
      void loadDashboardData(false); // Refresh without showing loading state
    }
  }, [loadDashboardData]);

  useWebSocket('batch.categorization.completed', (payload: any) => {
    // Refresh dashboard when batch categorization completes
    void loadDashboardData(false); // Refresh without showing loading state
  }, [loadDashboardData]);

  useWebSocket('accuracy.updated', (payload: any) => {
    // Update accuracy metrics in real-time
    setState(prev => {
      if (!prev.data) return prev;
      return {
        ...prev,
        data: {
          ...prev.data,
          metrics: {
            ...prev.data.metrics,
            averageAccuracy: payload.accuracy || prev.data.metrics.averageAccuracy,
          },
        },
        lastUpdated: new Date().toISOString(),
      };
    });
  }, []);

  // Initial load
  useEffect(() => {
    void loadDashboardData();
  }, [loadDashboardData]);

  // Auto refresh - smarter polling based on WebSocket connectivity
  useEffect(() => {
    if (!autoRefresh) return;

    // If WebSocket is connected, reduce polling frequency since we get real-time updates
    const actualRefreshInterval = isWebSocketConnected 
      ? refreshInterval * 3 // Poll 3x less frequently when WebSocket is active
      : refreshInterval;

    const interval = setInterval(() => {
      void loadDashboardData(false); // Silent refresh
    }, actualRefreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, loadDashboardData, isWebSocketConnected]);

  // Listen for data update events from other parts of the app
  useEffect(() => {
    const debouncedRefresh = (eventType: string, delay: number = 500) => {
      // Clear existing timeout for this event type
      const existingTimeout = debounceTimeouts.current.get(eventType);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      // Set new timeout
      const timeout = setTimeout(() => {
        void loadDashboardData(false);
        debounceTimeouts.current.delete(eventType);
      }, delay);

      debounceTimeouts.current.set(eventType, timeout);
    };

    const handleDataUpdate = () => debouncedRefresh('dataUpdate');
    const handleUploadComplete = () => debouncedRefresh('uploadComplete', 2000);
    const handleTransactionUpdated = () =>
      debouncedRefresh('transactionUpdated');
    const handleCategoryUpdated = () => debouncedRefresh('categoryUpdated');

    window.addEventListener('dataUpdate', handleDataUpdate);
    window.addEventListener('uploadComplete', handleUploadComplete);
    window.addEventListener('transactionUpdated', handleTransactionUpdated);
    window.addEventListener('categoryUpdated', handleCategoryUpdated);

    return () => {
      window.removeEventListener('dataUpdate', handleDataUpdate);
      window.removeEventListener('uploadComplete', handleUploadComplete);
      window.removeEventListener(
        'transactionUpdated',
        handleTransactionUpdated,
      );
      window.removeEventListener('categoryUpdated', handleCategoryUpdated);

      // Clear all timeouts
      debounceTimeouts.current.forEach((timeout) => clearTimeout(timeout));
      debounceTimeouts.current.clear();
    };
  }, [loadDashboardData]);

  return {
    ...state,
    refresh,
    setDateRange,
    isRefreshing,
  };
};
