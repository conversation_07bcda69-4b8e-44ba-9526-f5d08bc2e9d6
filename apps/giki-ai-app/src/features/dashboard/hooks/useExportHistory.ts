/**
 * Export History Hook
 * 
 * Manages export history data and provides functionality for
 * viewing recent exports and quick re-export actions
 */

import { useState, useEffect, useCallback } from 'react';
import { logger } from '@/shared/utils/errorHandling';

export interface ExportHistoryItem {
  id: string;
  formatId: string;
  formatName: string;
  fileName: string;
  fileSize: string;
  status: 'completed' | 'failed' | 'processing';
  createdAt: Date;
  downloadUrl?: string;
  expiresAt?: Date;
  filters?: {
    dateFrom?: string;
    dateTo?: string;
    categories?: string[];
    transactions?: string[];
  };
  error?: string;
}

export interface UseExportHistoryReturn {
  exports: ExportHistoryItem[];
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  downloadExport: (exportId: string) => Promise<void>;
  reExport: (exportId: string) => Promise<void>;
  deleteExport: (exportId: string) => Promise<void>;
}

/**
 * Hook to manage export history
 */
export const useExportHistory = (): UseExportHistoryReturn => {
  const [exports, setExports] = useState<ExportHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // For now, we'll use localStorage to store export history
  // In the future, this should come from a backend API
  const loadExportHistory = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get from localStorage for now
      const stored = localStorage.getItem('giki_export_history');
      const historyData = stored ? JSON.parse(stored) : [];
      
      // Convert dates back to Date objects
      const exports: ExportHistoryItem[] = historyData.map((item: any) => ({
        ...item,
        createdAt: new Date(item.createdAt),
        expiresAt: item.expiresAt ? new Date(item.expiresAt) : undefined,
      }));

      // Sort by most recent first
      exports.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      setExports(exports.slice(0, 10)); // Keep only latest 10
    } catch (error) {
      logger.error('Failed to load export history', 'useExportHistory', error as Error);
      setError('Failed to load export history');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Add new export to history
  const addExportToHistory = useCallback((exportItem: Omit<ExportHistoryItem, 'id'>) => {
    const newExport: ExportHistoryItem = {
      ...exportItem,
      id: Date.now().toString(),
    };

    setExports(prev => {
      const updated = [newExport, ...prev].slice(0, 10); // Keep only latest 10
      
      // Save to localStorage
      try {
        localStorage.setItem('giki_export_history', JSON.stringify(updated));
      } catch (error) {
        logger.error('Failed to save export history', 'useExportHistory', error as Error);
      }
      
      return updated;
    });
  }, []);

  // Download an existing export
  const downloadExport = useCallback(async (exportId: string) => {
    try {
      const exportItem = exports.find(e => e.id === exportId);
      if (!exportItem) {
        throw new Error('Export not found');
      }

      if (exportItem.status !== 'completed') {
        throw new Error('Export is not ready for download');
      }

      // Check if export has expired
      if (exportItem.expiresAt && exportItem.expiresAt < new Date()) {
        throw new Error('Export has expired and is no longer available');
      }

      // For now, simulate download by opening reports page
      // In the future, this should download the actual file
      const downloadUrl = `/reports?export=true&download=${exportId}`;
      window.open(downloadUrl, '_blank');
      
    } catch (error) {
      logger.error(`Failed to download export ${exportId}`, 'useExportHistory', error as Error);
      throw error;
    }
  }, [exports]);

  // Re-export with same parameters
  const reExport = useCallback(async (exportId: string) => {
    try {
      const exportItem = exports.find(e => e.id === exportId);
      if (!exportItem) {
        throw new Error('Export not found');
      }

      // Build export URL with same parameters
      const params = new URLSearchParams({
        export: 'true',
        format: exportItem.formatId,
      });

      if (exportItem.filters?.dateFrom) {
        params.append('dateFrom', exportItem.filters.dateFrom);
      }
      if (exportItem.filters?.dateTo) {
        params.append('dateTo', exportItem.filters.dateTo);
      }
      if (exportItem.filters?.categories?.length) {
        params.append('categories', exportItem.filters.categories.join(','));
      }
      if (exportItem.filters?.transactions?.length) {
        params.append('transactions', exportItem.filters.transactions.join(','));
      }

      const exportUrl = `/reports?${params.toString()}`;
      window.open(exportUrl, '_blank');
      
    } catch (error) {
      logger.error(`Failed to re-export ${exportId}`, 'useExportHistory', error as Error);
      throw error;
    }
  }, [exports]);

  // Delete export from history
  const deleteExport = useCallback(async (exportId: string) => {
    try {
      setExports(prev => {
        const updated = prev.filter(e => e.id !== exportId);
        
        // Save to localStorage
        try {
          localStorage.setItem('giki_export_history', JSON.stringify(updated));
        } catch (error) {
          logger.error('Failed to save export history', 'useExportHistory', error as Error);
        }
        
        return updated;
      });
    } catch (error) {
      logger.error(`Failed to delete export ${exportId}`, 'useExportHistory', error as Error);
      throw error;
    }
  }, []);

  const refresh = useCallback(async () => {
    await loadExportHistory();
  }, [loadExportHistory]);

  // Load history on mount
  useEffect(() => {
    loadExportHistory();
  }, [loadExportHistory]);

  // Listen for export events to update history
  useEffect(() => {
    const handleExportComplete = (event: CustomEvent) => {
      const { formatId, formatName, fileName, filters } = event.detail;
      
      addExportToHistory({
        formatId,
        formatName,
        fileName,
        fileSize: '2.3MB', // This should come from the actual export
        status: 'completed',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        filters,
      });
    };

    window.addEventListener('export-completed', handleExportComplete as EventListener);
    
    return () => {
      window.removeEventListener('export-completed', handleExportComplete as EventListener);
    };
  }, [addExportToHistory]);

  return {
    exports,
    isLoading,
    error,
    refresh,
    downloadExport,
    reExport,
    deleteExport,
  };
};