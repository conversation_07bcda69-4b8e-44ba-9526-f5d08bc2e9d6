/**
 * Export Readiness Hook - Real Backend Integration
 * ==============================================
 * 
 * Provides real-time export readiness data from backend APIs
 */

import { useState, useEffect, useCallback } from 'react';
import { ExportApiService } from '@/features/reports/services/exportApiService';
import { logger } from '@/shared/utils/errorHandling';

export interface ExportFormat {
  id: string;
  name: string;
  ready: boolean;
  issues?: string[];
  complianceScore?: number;
  category?: string; // e.g., 'quickbooks', 'xero', 'generic'
}

export interface ExportReadinessData {
  ready: boolean;
  readyFormats: number;
  totalFormats: number;
  lastCheck: Date | null;
  formats: ExportFormat[];
  criticalIssues: string[];
  isLoading: boolean;
  error: string | null;
}

export interface UseExportReadinessReturn extends ExportReadinessData {
  refresh: () => Promise<void>;
  checkFormat: (formatId: string) => Promise<void>;
}

/**
 * Hook to fetch and manage export readiness data
 */
export const useExportReadiness = (): UseExportReadinessReturn => {
  const [data, setData] = useState<ExportReadinessData>({
    ready: false,
    readyFormats: 0,
    totalFormats: 0,
    lastCheck: null,
    formats: [],
    criticalIssues: [],
    isLoading: true,
    error: null,
  });

  const checkExportReadiness = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, isLoading: true, error: null }));
      
      // 1. Get all available export formats
      const formatInfos = await ExportApiService.getExportFormats();
      
      // 2. Check readiness for each format
      const formatPromises = formatInfos.map(async (formatInfo) => {
        try {
          const readinessResult = await ExportApiService.checkExportReadiness(formatInfo.id);
          
          // Calculate compliance score from compliance report
          const complianceScore = readinessResult.compliance_report?.overall_score || 0;
          
          // Categorize formats for better organization
          const category = formatInfo.id.includes('quickbooks') ? 'quickbooks' :
                          formatInfo.id.includes('xero') ? 'xero' :
                          formatInfo.id.includes('csv') || formatInfo.id.includes('excel') ? 'generic' :
                          'other';

          return {
            id: formatInfo.id,
            name: formatInfo.name,
            ready: readinessResult.ready_for_export,
            complianceScore,
            category,
            issues: readinessResult.validation_results
              .filter(result => result.status === 'failed' || result.status === 'warning')
              .map(result => result.message)
          };
        } catch (error) {
          logger.error(`Failed to check readiness for format ${formatInfo.id}`, 'useExportReadiness', error as Error);
          return {
            id: formatInfo.id,
            name: formatInfo.name,
            ready: false,
            complianceScore: 0,
            category: 'other',
            issues: ['Unable to check format readiness']
          };
        }
      });

      const formats = await Promise.all(formatPromises);
      const readyFormats = formats.filter(format => format.ready).length;
      const totalFormats = formats.length;
      
      // Collect critical issues across all formats
      const allIssues = formats.flatMap(format => format.issues || []);
      const uniqueIssues = Array.from(new Set(allIssues));
      
      // Determine overall readiness (80% threshold)
      const ready = readyFormats / totalFormats >= 0.8;

      setData({
        ready,
        readyFormats,
        totalFormats,
        lastCheck: new Date(),
        formats,
        criticalIssues: uniqueIssues.slice(0, 3), // Show top 3 issues
        isLoading: false,
        error: null,
      });
      
    } catch (error) {
      logger.error('Failed to fetch export readiness data', 'useExportReadiness', error as Error);
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load export readiness data. Please try again.'
      }));
    }
  }, []);

  const checkFormat = useCallback(async (formatId: string) => {
    try {
      const readinessResult = await ExportApiService.checkExportReadiness(formatId);
      
      setData(prev => ({
        ...prev,
        formats: prev.formats.map(format => 
          format.id === formatId 
            ? {
                ...format,
                ready: readinessResult.ready_for_export,
                issues: readinessResult.validation_results
                  .filter(result => result.status === 'failed' || result.status === 'warning')
                  .map(result => result.message)
              }
            : format
        ),
        lastCheck: new Date()
      }));
      
    } catch (error) {
      logger.error(`Failed to check format ${formatId}`, 'useExportReadiness', error as Error);
    }
  }, []);

  const refresh = useCallback(async () => {
    await checkExportReadiness();
  }, [checkExportReadiness]);

  // Initial load
  useEffect(() => {
    checkExportReadiness();
  }, [checkExportReadiness]);

  return {
    ...data,
    refresh,
    checkFormat
  };
};