import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError, logger } from '@/shared/utils/errorHandling';
import {
  getIncomeVsExpenseReport,
  getSpendingByCategoryReport,
} from '@/features/reports/services/reportService';
import { fetchTransactions } from '@/features/transactions/services/transactionService';
import type { Transaction } from '@/shared/types/transaction';
import type {
  DashboardData,
  DashboardMetrics,
  RecentTransaction,
  CategoryBreakdownItem,
  MonthlyTrendData,
  ProcessingStatus,
  Action,
} from '../types/dashboard';

// API Response interfaces
interface MonthlyTrendApiItem {
  month: string;
  total_income: number;
  total_expenses: number;
  net_amount: number;
  transaction_count: number;
}

interface ActionsApiResponse {
  actions: Action[];
}

interface MonthlyTrendsApiResponse {
  items: MonthlyTrendApiItem[];
}

/**
 * Dashboard Service
 * Aggregates data from various APIs to provide comprehensive dashboard view
 */

export class DashboardService {
  private static instance: DashboardService;
  private cache: Map<string, { data: unknown; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService();
    }
    return DashboardService.instance;
  }

  private isCacheValid(key: string): boolean {
    const cached = this?.cache?.get(key);
    if (!cached) return false;
    return Date.now() - cached.timestamp < this.CACHE_TTL;
  }

  private getCached<T>(key: string): T | null {
    if (this.isCacheValid(key)) {
      return (this?.cache?.get(key)?.data as T) || null;
    }
    return null;
  }

  private setCache<T>(key: string, data: T): void {
    this?.cache?.set(key, { data, timestamp: Date.now() });
  }

  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(dateRange?: {
    from: string;
    to: string;
  }): Promise<DashboardData> {
    const cacheKey = `dashboard-${JSON.stringify(dateRange)}`;
    const cached = this.getCached<DashboardData>(cacheKey);
    if (cached) return cached;

    try {
      // Fetch category data once and reuse (fixes API duplication)
      let categoryData: { name: string; spending: number }[] = [];
      try {
        categoryData = await getSpendingByCategoryReport();
      } catch (error) {
        // Category data fetch failed, using fallback data
      }

      // Fetch accuracy data for historical trends
      let accuracyData: any = null;
      try {
        const accuracyResponse = await apiClient.get('/accuracy/temporal-data');
        accuracyData = accuracyResponse.data;
      } catch (error) {
        // Accuracy data failed, will use fallback
      }

      // Fetch all other data in parallel for better performance
      const [
        metrics,
        recentTransactions,
        categoryBreakdown,
        monthlyTrends,
        processingStatus,
        quickActions,
      ] = await Promise.all([
        this.getMetrics(dateRange, categoryData, accuracyData),
        this.getRecentTransactions(),
        Promise.resolve(this.getCategoryBreakdown(dateRange, categoryData)),
        this.getMonthlyTrends(dateRange),
        this.getProcessingStatus(),
        this.getActions(),
      ]);

      const dashboardData: DashboardData = {
        metrics,
        recentTransactions,
        categoryBreakdown,
        monthlyTrends,
        processingStatus,
        quickActions,
        dateRange: dateRange || this.getDefaultDateRange(),
        // Add accuracy data to the dashboard data
        accuracyHistory: accuracyData?.data || [],
      };

      this.setCache(cacheKey, dashboardData);
      return dashboardData;
    } catch (error) {
      throw handleApiError(error, {
        context: 'getDashboardData',
        defaultMessage: 'Failed to load dashboard data',
      });
    }
  }

  /**
   * Get financial metrics using real categorization data
   */
  private async getMetrics(
    dateRange?: {
      from: string;
      to: string;
    },
    categoryData?: { name: string; spending: number }[],
    accuracyData?: any,
  ): Promise<DashboardMetrics> {
    try {
      // Get real categorization metrics from new API
      let categorizationMetrics: any = null;
      try {
        const response = await apiClient.get('/categories/metrics/categorization');
        categorizationMetrics = response.data;
      } catch (error) {
        // Categorization metrics failed, will use fallback
      }

      // Get income vs expense data with fallback
      let incomeExpenseData: {
        totalIncome: number;
        totalExpense: number;
        netAmount: number;
      };
      try {
        incomeExpenseData = await getIncomeVsExpenseReport(
          dateRange
            ? { from: new Date(dateRange.from), to: new Date(dateRange.to) }
            : undefined,
        );
      } catch (error) {
        // Income/expense report failed, using fallback data
        incomeExpenseData = { totalIncome: 0, totalExpense: 0, netAmount: 0 };
      }

      // Use real categorization metrics if available
      let totalTransactionCount = 0;
      let categorizedCount = 0;
      let uncategorizedCount = 0;

      if (categorizationMetrics?.categorization_summary) {
        totalTransactionCount = categorizationMetrics.categorization_summary.total_transactions;
        categorizedCount = categorizationMetrics.categorization_summary.categorized_transactions;
        uncategorizedCount = categorizationMetrics.categorization_summary.uncategorized_transactions;
        
        // VALIDATION: Check if backend metrics are inconsistent with actual categorization
        // If top category is "Uncategorized" and it has all transactions, then override the metrics
        const topCategories = categorizationMetrics.top_categories || [];
        const uncategorizedCategory = topCategories.find(cat => cat.name === "Uncategorized");
        if (uncategorizedCategory && uncategorizedCategory.count === totalTransactionCount) {
          // Backend bug: All transactions are marked as categorized but they're all "Uncategorized"
          categorizedCount = 0;
          uncategorizedCount = totalTransactionCount;
        }
      } else {
        // Fallback to transaction API if categorization metrics not available
        try {
          const transactionsResponse = await fetchTransactions({
            startDate: dateRange?.from,
            endDate: dateRange?.to,
            pageSize: 100,
          });

          const transactions = Array.isArray(transactionsResponse)
            ? transactionsResponse
            : transactionsResponse.items || [];

          if (transactionsResponse && 'total_count' in transactionsResponse) {
            totalTransactionCount = transactionsResponse.total_count;
          } else {
            totalTransactionCount = transactions.length;
          }

          const sampleCategorizedCount = transactions.filter(
            (t) => t.category_path || t.ai_suggested_category_path,
          ).length;
          const categorizedRatio =
            transactions.length > 0 ? sampleCategorizedCount / transactions.length : 0;
          categorizedCount = Math.round(totalTransactionCount * categorizedRatio);
          uncategorizedCount = totalTransactionCount - categorizedCount;
        } catch (error) {
          // Complete fallback
          totalTransactionCount = 0;
          categorizedCount = 0;
          uncategorizedCount = 0;
        }
      }

      // Calculate average transaction amount (still using transaction API)
      let avgTransactionAmount = 0;
      try {
        const sampleResponse = await fetchTransactions({
          pageSize: 50,
        });
        const transactions = Array.isArray(sampleResponse) 
          ? sampleResponse 
          : sampleResponse.items || [];
        
        if (transactions.length > 0) {
          avgTransactionAmount = transactions.reduce((sum, t) => sum + Math.abs(t.amount), 0) / transactions.length;
        }
      } catch (error) {
        // Failed to get average amount
        avgTransactionAmount = 0;
      }

      // Use pre-fetched category data (prevents duplicate API call)
      const topCategory =
        categoryData && categoryData.length > 0 ? categoryData[0] : null;

      return {
        totalIncome: incomeExpenseData.totalIncome,
        totalExpenses: incomeExpenseData.totalExpense,
        netIncome: incomeExpenseData.netAmount,
        totalTransactions: totalTransactionCount,
        categorizedTransactions: categorizedCount,
        uncategorizedTransactions: uncategorizedCount,
        avgTransactionAmount,
        topCategory: topCategory?.name || 'No data available',
        topCategoryAmount: topCategory?.spending || 0,
      };
    } catch (error) {
      // Final fallback to prevent dashboard from breaking
      return {
        totalIncome: 0,
        totalExpenses: 0,
        netIncome: 0,
        totalTransactions: 0,
        categorizedTransactions: 0,
        uncategorizedTransactions: 0,
        avgTransactionAmount: 0,
        topCategory: 'Dashboard loading...',
        topCategoryAmount: 0,
      };
    }
  }

  /**
   * Get recent transactions
   */
  private async getRecentTransactions(): Promise<RecentTransaction[]> {
    try {
      const response = await fetchTransactions({
        pageSize: 10,
        sortBy: 'date',
        sortDirection: 'desc',
      });

      const transactions = Array.isArray(response) ? response : response.items;

      return transactions.slice(0, 5).map((t) => ({
        id: t.id,
        description: t.description,
        amount: t.amount,
        date: t.date,
        category: t.category_path || t.ai_suggested_category_path || undefined,
        status: this.getTransactionStatus(t),
        transaction_type: t.transaction_type as 'income' | 'expense',
      }));
    } catch (error) {
      logger.error('DashboardService: Failed to fetch recent transactions', 'dashboardService', error as Error);
      throw new Error(`Failed to load recent transactions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get category breakdown
   */
  private getCategoryBreakdown(
    _dateRange?: {
      from: string;
      to: string;
    },
    categoryData?: { name: string; spending: number; transaction_count?: number }[],
  ): CategoryBreakdownItem[] {
    try {
      // Use pre-fetched category data (prevents duplicate API call)
      if (!categoryData || categoryData.length === 0) {
        logger.warn('DashboardService: No category data available for breakdown', 'dashboardService');
        throw new Error('No category spending data available. Upload transactions to see category breakdown.');
      }

      const totalAmount = categoryData.reduce(
        (sum, item) => sum + item.spending,
        0,
      );

      return categoryData.slice(0, 5).map((item, index) => ({
        category: item.name,
        amount: item.spending,
        percentage: totalAmount > 0 ? (item.spending / totalAmount) * 100 : 0,
        count: item.transaction_count || 0, // Use transaction count from backend
        color: this.getCategoryColor(index),
      }));
    } catch (error) {
      logger.error('DashboardService: Failed to process category breakdown', 'dashboardService', error as Error);
      throw error; // Re-throw since this includes meaningful user errors
    }
  }

  /**
   * Get monthly trends data
   */
  private async getMonthlyTrends(dateRange?: {
    from: string;
    to: string;
  }): Promise<MonthlyTrendData[]> {
    try {
      const params = new URLSearchParams();
      if (dateRange?.from) params.append('start_date', dateRange.from);
      if (dateRange?.to) params.append('end_date', dateRange.to);

      const response = await apiClient.get<MonthlyTrendsApiResponse>(
        `/reports/monthly-trends?${params.toString()}`,
      );

      return response?.data?.items.map((item) => ({
        month: item.month,
        income: item.total_income,
        expenses: item.total_expenses,
        net: item.net_amount,
        transactionCount: item.transaction_count,
      }));
    } catch (error) {
      logger.error('DashboardService: Failed to fetch monthly trends', 'dashboardService', error as Error);
      throw new Error(`Failed to load monthly trends: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get processing status
   */
  private async getProcessingStatus(): Promise<ProcessingStatus> {
    try {
      const response = await apiClient.get<ProcessingStatus>(
        '/files/processing-status',
      );
      return response.data;
    } catch (error) {
      throw handleApiError(error, {
        context: 'getProcessingStatus',
        defaultMessage: 'Failed to fetch file processing status',
      });
    }
  }

  /**
   * Get quick actions
   */
  private async getActions(): Promise<Action[]> {
    try {
      const response = await apiClient.get<ActionsApiResponse>(
        '/auth/quick-actions',
      );
      return response?.data?.actions || [];
    } catch (error) {
      throw handleApiError(error, {
        context: 'getActions',
        defaultMessage: 'Failed to fetch user quick actions',
      });
    }
  }

  /**
   * Refresh dashboard data
   */
  async refreshDashboardData(dateRange?: {
    from: string;
    to: string;
  }): Promise<DashboardData> {
    const cacheKey = `dashboard-${JSON.stringify(dateRange)}`;
    this?.cache?.delete(cacheKey);
    return this.getDashboardData(dateRange);
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    this?.cache?.clear();
  }

  // Helper methods
  private getTransactionStatus(
    transaction: Transaction,
  ): 'categorized' | 'needs_review' | 'ai_suggested' {
    if (transaction.is_user_modified || transaction.category_path) {
      return 'categorized';
    }
    if (transaction.ai_suggested_category_path) {
      return 'ai_suggested';
    }
    return 'needs_review';
  }

  private getCategoryColor(index: number): string {
    const colors = [
      'hsl(var(--giki-brand-blue))', // blue
      'hsl(var(--giki-success))', // emerald/green
      'hsl(var(--giki-brand-purple))', // violet
      'hsl(var(--giki-warning))', // amber
      'hsl(var(--giki-destructive))', // red
      'hsl(var(--giki-info))', // cyan
      'hsl(var(--giki-brand-green))', // lime/green
      'hsl(var(--giki-brand-pink))', // pink
    ];
    return colors[index % colors.length];
  }

  private getDefaultDateRange(): { from: string; to: string } {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    return {
      from: thirtyDaysAgo.toISOString().split('T')[0],
      to: today.toISOString().split('T')[0],
    };
  }
}

// Export singleton instance
export const dashboardService = DashboardService.getInstance();

// Export convenience functions
export const getDashboardData = (dateRange?: { from: string; to: string }) =>
  dashboardService.getDashboardData(dateRange);

export const refreshDashboardData = (dateRange?: {
  from: string;
  to: string;
}) => dashboardService.refreshDashboardData(dateRange);

export const clearDashboardCache = () => dashboardService.clearCache();
