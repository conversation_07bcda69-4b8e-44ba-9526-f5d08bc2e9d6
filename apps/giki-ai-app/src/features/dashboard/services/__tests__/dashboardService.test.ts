/**
 * Dashboard Service Tests
 * 
 * Tests the dashboard service including the critical backend data validation
 * that fixes inconsistent categorization metrics from the backend API.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { getDashboard } from '../dashboardService';
import * as apiClient from '@/shared/services/api/apiClient';
import * as reportService from '@/features/reports/services/reportService';

// Mock the API client and report service
const mockApiGet = vi.spyOn(apiClient.apiClient, 'get');
const mockGetTotalTransactionCount = vi.spyOn(reportService, 'getTotalTransactionCount');

describe('dashboardService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getDashboard', () => {
    describe('normal categorization scenarios', () => {
      it('should return correct data for fully categorized transactions', async () => {
        // Mock API responses for normal case
        mockApiGet
          .mockResolvedValueOnce({ // /dashboard/metrics
            data: {
              total_revenue: 50000,
              total_expenses: 30000,
              net_profit: 20000,
              total_transactions: 150,
              processed_transactions: 150,
              pending_transactions: 0
            }
          })
          .mockResolvedValueOnce({ // /transactions/fast
            data: []
          })
          .mockResolvedValueOnce({ // /categories/metrics/categorization
            data: {
              total_transactions: 150,
              categorized_transactions: 150,
              uncategorized_transactions: 0,
              categorization_rate: 100.0,
              top_categories: [
                { name: 'Food & Dining', count: 50, percentage: 33.3 },
                { name: 'Transportation', count: 30, percentage: 20.0 },
                { name: 'Utilities', count: 70, percentage: 46.7 }
              ]
            }
          })
          .mockResolvedValueOnce({ // /accuracy/temporal-data
            data: { ai_accuracy: 87.5 }
          })
          .mockResolvedValueOnce({ // /reports/monthly-trends
            data: []
          });

        mockGetTotalTransactionCount.mockResolvedValue(150);

        const result = await getDashboard();

        expect(result.totalTransactionCount).toBe(150);
        expect(result.categorizedTransactionCount).toBe(150);
        expect(result.uncategorizedTransactionCount).toBe(0);
        expect(result.categorizationRate).toBe(100);
        expect(result.topCategories).toHaveLength(3);
        expect(result.topCategories.find(cat => cat.name === 'Uncategorized')).toBeUndefined();
      });

      it('should return correct data for partially categorized transactions', async () => {
        mockApiGet
          .mockResolvedValueOnce({ // /dashboard/metrics
            data: {
              total_revenue: 40000,
              total_expenses: 25000,
              net_profit: 15000,
              total_transactions: 200,
              processed_transactions: 150,
              pending_transactions: 50
            }
          })
          .mockResolvedValueOnce({ // /transactions/fast
            data: []
          })
          .mockResolvedValueOnce({ // /categories/metrics/categorization
            data: {
              total_transactions: 200,
              categorized_transactions: 150,
              uncategorized_transactions: 50,
              categorization_rate: 75.0,
              top_categories: [
                { name: 'Food & Dining', count: 50, percentage: 25.0 },
                { name: 'Transportation', count: 30, percentage: 15.0 },
                { name: 'Utilities', count: 70, percentage: 35.0 },
                { name: 'Uncategorized', count: 50, percentage: 25.0 }
              ]
            }
          })
          .mockResolvedValueOnce({ // /accuracy/temporal-data
            data: { ai_accuracy: 87.5 }
          })
          .mockResolvedValueOnce({ // /reports/monthly-trends
            data: []
          });

        mockGetTotalTransactionCount.mockResolvedValue(200);

        const result = await getDashboard();

        expect(result.totalTransactionCount).toBe(200);
        expect(result.categorizedTransactionCount).toBe(150);
        expect(result.uncategorizedTransactionCount).toBe(50);
        expect(result.categorizationRate).toBe(75);
        expect(result.topCategories).toHaveLength(4);
        expect(result.topCategories.find(cat => cat.name === 'Uncategorized')).toBeDefined();
      });
    });

    describe('CRITICAL: backend data validation and inconsistency fixes', () => {
      it('should detect and fix backend inconsistency where all transactions are claimed as categorized but are actually "Uncategorized"', async () => {
        // This is the critical bug we found and fixed during user journey testing
        mockApiGet
          .mockResolvedValueOnce({ // /dashboard/metrics
            data: {
              total_revenue: 30000,
              total_expenses: 20000,
              net_profit: 10000,
              total_transactions: 100,
              processed_transactions: 100, // Backend claims all processed
              pending_transactions: 0
            }
          })
          .mockResolvedValueOnce({ // /transactions/fast
            data: []
          })
          .mockResolvedValueOnce({ // /categories/metrics/categorization - INCONSISTENT DATA
            data: {
              total_transactions: 100,
              categorized_transactions: 100, // Backend INCORRECTLY claims all categorized
              uncategorized_transactions: 0, // Backend INCORRECTLY claims none uncategorized
              categorization_rate: 100.0, // Backend INCORRECTLY claims 100% rate
              top_categories: [
                { name: 'Uncategorized', count: 100, percentage: 100.0 } // BUT all are actually "Uncategorized"!
              ]
            }
          })
          .mockResolvedValueOnce({ // /accuracy/temporal-data
            data: { ai_accuracy: 87.5 }
          })
          .mockResolvedValueOnce({ // /reports/monthly-trends
            data: []
          });

        mockGetTotalTransactionCount.mockResolvedValue(100);

        const result = await getDashboard();

        // The service should detect the inconsistency and correct it
        expect(result.totalTransactionCount).toBe(100);
        expect(result.categorizedTransactionCount).toBe(0); // CORRECTED from backend's claim of 100
        expect(result.uncategorizedTransactionCount).toBe(100); // CORRECTED from backend's claim of 0
        expect(result.categorizationRate).toBe(0); // CORRECTED from backend's claim of 100
        expect(result.topCategories).toHaveLength(1);
        expect(result.topCategories[0].name).toBe('Uncategorized');
        expect(result.topCategories[0].count).toBe(100);
      });

      it('should handle multiple "Uncategorized" categories correctly', async () => {
        mockApiGet
          .mockResolvedValueOnce({ // /dashboard/metrics
            data: {
              total_revenue: 25000,
              total_expenses: 15000,
              net_profit: 10000,
              total_transactions: 80,
              processed_transactions: 80,
              pending_transactions: 0
            }
          })
          .mockResolvedValueOnce({ // /transactions/fast
            data: []
          })
          .mockResolvedValueOnce({ // /categories/metrics/categorization
            data: {
              total_transactions: 80,
              categorized_transactions: 80, // Backend claim
              uncategorized_transactions: 0, // Backend claim
              categorization_rate: 100.0, // Backend claim
              top_categories: [
                { name: 'Uncategorized', count: 50, percentage: 62.5 },
                { name: 'uncategorized', count: 20, percentage: 25.0 }, // Different case
                { name: 'UNCATEGORIZED', count: 10, percentage: 12.5 } // Different case
              ]
            }
          })
          .mockResolvedValueOnce({ // /accuracy/temporal-data
            data: { ai_accuracy: 87.5 }
          })
          .mockResolvedValueOnce({ // /reports/monthly-trends
            data: []
          });

        mockGetTotalTransactionCount.mockResolvedValue(80);

        const result = await getDashboard();

        // Should detect that all variations of "uncategorized" mean the same thing
        expect(result.categorizedTransactionCount).toBe(0); // All are uncategorized
        expect(result.uncategorizedTransactionCount).toBe(80); // Sum of all uncategorized variants
        expect(result.categorizationRate).toBe(0);
      });

      it('should not trigger validation fix when categorization is genuinely correct', async () => {
        mockApiGet
          .mockResolvedValueOnce({ // /dashboard/metrics
            data: {
              total_revenue: 45000,
              total_expenses: 28000,
              net_profit: 17000,
              total_transactions: 120,
              processed_transactions: 120,
              pending_transactions: 0
            }
          })
          .mockResolvedValueOnce({ // /transactions/fast
            data: []
          })
          .mockResolvedValueOnce({ // /categories/metrics/categorization
            data: {
              total_transactions: 120,
              categorized_transactions: 120,
              uncategorized_transactions: 0,
              categorization_rate: 100.0,
              top_categories: [
                { name: 'Food & Dining', count: 40, percentage: 33.3 },
                { name: 'Transportation', count: 30, percentage: 25.0 },
                { name: 'Utilities', count: 50, percentage: 41.7 }
              ]
            }
          })
          .mockResolvedValueOnce({ // /accuracy/temporal-data
            data: { ai_accuracy: 87.5 }
          })
          .mockResolvedValueOnce({ // /reports/monthly-trends
            data: []
          });

        mockGetTotalTransactionCount.mockResolvedValue(120);

        const result = await getDashboard();

        // Should NOT trigger validation fix - data is genuinely correct
        expect(result.categorizedTransactionCount).toBe(120); // Keep backend values
        expect(result.uncategorizedTransactionCount).toBe(0);
        expect(result.categorizationRate).toBe(100);
        expect(result.topCategories.find(cat => cat.name === 'Uncategorized')).toBeUndefined();
      });
    });

    describe('error handling', () => {
      it('should handle API failures gracefully', async () => {
        mockApiGet.mockRejectedValue(new Error('Network error'));
        mockGetTotalTransactionCount.mockRejectedValue(new Error('Network error'));

        await expect(getDashboard()).rejects.toThrow('Network error');
      });

      it('should handle missing categorization data', async () => {
        mockApiGet
          .mockResolvedValueOnce({ // /dashboard/metrics
            data: {
              total_revenue: 0,
              total_expenses: 0,
              net_profit: 0,
              total_transactions: 0,
              processed_transactions: 0,
              pending_transactions: 0
            }
          })
          .mockResolvedValueOnce({ // /transactions/fast
            data: []
          })
          .mockResolvedValueOnce({ // /categories/metrics/categorization
            data: {
              total_transactions: 0,
              categorized_transactions: 0,
              uncategorized_transactions: 0,
              categorization_rate: 0,
              top_categories: []
            }
          })
          .mockResolvedValueOnce({ // /accuracy/temporal-data
            data: { ai_accuracy: 0 }
          })
          .mockResolvedValueOnce({ // /reports/monthly-trends
            data: []
          });

        mockGetTotalTransactionCount.mockResolvedValue(0);

        const result = await getDashboard();

        expect(result.totalTransactionCount).toBe(0);
        expect(result.categorizedTransactionCount).toBe(0);
        expect(result.uncategorizedTransactionCount).toBe(0);
        expect(result.categorizationRate).toBe(0);
        expect(result.topCategories).toHaveLength(0);
      });
    });

    describe('validation logic edge cases', () => {
      it('should handle case where Uncategorized count exceeds total transactions (backend data corruption)', async () => {
        mockApiGet
          .mockResolvedValueOnce({ // /dashboard/metrics
            data: {
              total_revenue: 20000,
              total_expenses: 12000,
              net_profit: 8000,
              total_transactions: 50,
              processed_transactions: 50,
              pending_transactions: 0
            }
          })
          .mockResolvedValueOnce({ // /transactions/fast
            data: []
          })
          .mockResolvedValueOnce({ // /categories/metrics/categorization
            data: {
              total_transactions: 50,
              categorized_transactions: 50,
              uncategorized_transactions: 0,
              categorization_rate: 100.0,
              top_categories: [
                { name: 'Uncategorized', count: 75, percentage: 150.0 } // Data corruption: more than total!
              ]
            }
          })
          .mockResolvedValueOnce({ // /accuracy/temporal-data
            data: { ai_accuracy: 87.5 }
          })
          .mockResolvedValueOnce({ // /reports/monthly-trends
            data: []
          });

        mockGetTotalTransactionCount.mockResolvedValue(50);

        const result = await getDashboard();

        // Should cap uncategorized count to total transaction count
        expect(result.categorizedTransactionCount).toBe(0);
        expect(result.uncategorizedTransactionCount).toBe(50); // Capped to total
        expect(result.categorizationRate).toBe(0);
      });
    });

    describe('routing decision support', () => {
      it('should provide data that supports Dashboard routing for categorized transactions', async () => {
        mockApiGet
          .mockResolvedValueOnce({ // /dashboard/metrics
            data: {
              total_revenue: 60000,
              total_expenses: 35000,
              net_profit: 25000,
              total_transactions: 180,
              processed_transactions: 180,
              pending_transactions: 0
            }
          })
          .mockResolvedValueOnce({ // /transactions/fast
            data: []
          })
          .mockResolvedValueOnce({ // /categories/metrics/categorization
            data: {
              total_transactions: 180,
              categorized_transactions: 180,
              uncategorized_transactions: 0,
              categorization_rate: 100.0,
              top_categories: [
                { name: 'Food & Dining', count: 60, percentage: 33.3 },
                { name: 'Transportation', count: 50, percentage: 27.8 },
                { name: 'Utilities', count: 70, percentage: 38.9 }
              ]
            }
          })
          .mockResolvedValueOnce({ // /accuracy/temporal-data
            data: { ai_accuracy: 87.5 }
          })
          .mockResolvedValueOnce({ // /reports/monthly-trends
            data: []
          });

        mockGetTotalTransactionCount.mockResolvedValue(180);

        const result = await getDashboard();

        // Data should support routing to Dashboard (hasCategorizedTransactions = true)
        expect(result.categorizedTransactionCount).toBeGreaterThan(0);
        expect(result.totalTransactionCount).toBeGreaterThan(0);
        expect(result.categorizationRate).toBeGreaterThan(0);
      });

      it('should provide data that supports Upload routing for uncategorized transactions', async () => {
        mockApiGet
          .mockResolvedValueOnce({ // /dashboard/metrics
            data: {
              total_revenue: 0,
              total_expenses: 0,
              net_profit: 0,
              total_transactions: 90,
              processed_transactions: 90,
              pending_transactions: 0
            }
          })
          .mockResolvedValueOnce({ // /transactions/fast
            data: []
          })
          .mockResolvedValueOnce({ // /categories/metrics/categorization - Backend inconsistency
            data: {
              total_transactions: 90,
              categorized_transactions: 90, // Backend incorrectly claims categorized
              uncategorized_transactions: 0,
              categorization_rate: 100.0,
              top_categories: [
                { name: 'Uncategorized', count: 90, percentage: 100.0 } // But all are uncategorized
              ]
            }
          })
          .mockResolvedValueOnce({ // /accuracy/temporal-data
            data: { ai_accuracy: 0 }
          })
          .mockResolvedValueOnce({ // /reports/monthly-trends
            data: []
          });

        mockGetTotalTransactionCount.mockResolvedValue(90);

        const result = await getDashboard();

        // Data should support routing to Upload (hasCategorizedTransactions = false)
        expect(result.categorizedTransactionCount).toBe(0); // Fixed by validation
        expect(result.uncategorizedTransactionCount).toBe(90);
        expect(result.categorizationRate).toBe(0);
      });
    });
  });
});