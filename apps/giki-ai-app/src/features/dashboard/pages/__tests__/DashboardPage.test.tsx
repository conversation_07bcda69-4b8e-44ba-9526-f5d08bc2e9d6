/**
 * DashboardPage Component Tests
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { 
  render, 
  mockFetch, 
  setupAuthenticatedState, 
  mockDashboardData,
  mockTransaction 
} from '@/test/utils';
import DashboardPage from '../DashboardPage';

describe('DashboardPage', () => {
  beforeEach(() => {
    setupAuthenticatedState();
  });

  it('renders dashboard with loading state initially', () => {
    render(<DashboardPage />);
    
    expect(screen.getByText(/loading dashboard/i)).toBeInTheDocument();
  });

  it('displays dashboard metrics when data loads', async () => {
    // Mock dashboard API responses
    mockFetch({
      ...mockDashboardData,
      recentTransactions: [mockTransaction],
    });
    
    render(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/financial dashboard/i)).toBeInTheDocument();
    });
    
    // Check for metric cards
    expect(screen.getByText(/transactions processed/i)).toBeInTheDocument();
    expect(screen.getByText(/categorization rate/i)).toBeInTheDocument();
    expect(screen.getByText(/time saved/i)).toBeInTheDocument();
    expect(screen.getByText(/export readiness/i)).toBeInTheDocument();
    
    // Check for metric values
    expect(screen.getByText('15')).toBeInTheDocument(); // Total transactions
    expect(screen.getByText('100.0%')).toBeInTheDocument(); // Categorization rate
    expect(screen.getByText('10/10')).toBeInTheDocument(); // Export readiness
  });

  it('displays recent activity section', async () => {
    mockFetch({
      ...mockDashboardData,
      recentTransactions: [mockTransaction],
    });
    
    render(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/recent activity/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(mockTransaction.description)).toBeInTheDocument();
  });

  it('displays quick actions section', async () => {
    mockFetch({
      ...mockDashboardData,
      recentTransactions: [],
    });
    
    render(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/quick actions/i)).toBeInTheDocument();
    });
    
    // Check for quick action buttons
    expect(screen.getByText(/upload new files/i)).toBeInTheDocument();
    expect(screen.getByText(/review categories/i)).toBeInTheDocument();
    expect(screen.getByText(/export to accounting/i)).toBeInTheDocument();
    expect(screen.getByText(/generate report/i)).toBeInTheDocument();
  });

  it('allows changing time range filter', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockDashboardData,
      recentTransactions: [],
    });
    
    render(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/financial dashboard/i)).toBeInTheDocument();
    });
    
    // Check default time range button
    const sevenDaysButton = screen.getByRole('button', { name: /7 days/i });
    const thirtyDaysButton = screen.getByRole('button', { name: /30 days/i });
    
    expect(sevenDaysButton).toBeInTheDocument();
    expect(thirtyDaysButton).toBeInTheDocument();
    
    // Click 30 days button
    await user.click(thirtyDaysButton);
    
    // Verify API call with new time range
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('timeRange=30d'),
        expect.any(Object)
      );
    });
  });

  it('handles refresh button click', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockDashboardData,
      recentTransactions: [],
    });
    
    render(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/financial dashboard/i)).toBeInTheDocument();
    });
    
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    await user.click(refreshButton);
    
    // Should make fresh API calls
    expect(global.fetch).toHaveBeenCalledTimes(2); // Initial load + refresh
  });

  it('displays empty state when no transactions exist', async () => {
    mockFetch({
      totalTransactions: 0,
      totalIncome: 0,
      totalExpenses: 0,
      netProfit: 0,
      categorizationRate: 0,
      timeSaved: 0,
      categoriesActive: 0,
      exportReadiness: 0,
      recentTransactions: [],
    });
    
    render(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/no transactions/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/upload your first file/i)).toBeInTheDocument();
  });

  it('handles API error gracefully', async () => {
    // Mock API error
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));
    
    render(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/unable to load dashboard/i)).toBeInTheDocument();
    });
    
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
  });

  it('navigates to upload page when clicking upload quick action', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockDashboardData,
      recentTransactions: [],
    });
    
    render(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/upload new files/i)).toBeInTheDocument();
    });
    
    const uploadLink = screen.getByRole('link', { name: /upload new files/i });
    expect(uploadLink).toHaveAttribute('href', '/upload');
  });

  it('displays processing status when files are being processed', async () => {
    mockFetch({
      ...mockDashboardData,
      processingStatus: {
        isProcessing: true,
        currentFile: 'test-file.xlsx',
        progress: 50,
      },
      recentTransactions: [],
    });
    
    render(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/processing/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/test-file\.xlsx/i)).toBeInTheDocument();
    expect(screen.getByText(/50%/i)).toBeInTheDocument();
  });
});