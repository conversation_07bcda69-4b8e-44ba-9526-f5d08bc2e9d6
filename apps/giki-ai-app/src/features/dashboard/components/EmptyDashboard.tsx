/**
 * Professional Empty Dashboard State - Based on comprehensive mockup
 * Implements welcome messaging, interactive upload, and workflow guidance
 */
import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
// Removed Lucide imports - replaced with geometric icons
// CheckCircle → ✓
import { Button } from '@/shared/components/ui/button';

interface EmptyDashboardProps {
  onFileUpload?: (files: FileList) => void;
}

const EmptyDashboard: React.FC<EmptyDashboardProps> = ({ onFileUpload }) => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragActive, setIsDragActive] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isGoalsSet, setIsGoalsSet] = useState(false);
  const [monthlyBudget, setMonthlyBudget] = useState('$10,000');
  const [mainCategories, setMainCategories] = useState(
    'Office, Travel, Software',
  );

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files && files.length > 0) {
      setIsUploading(true);
      setUploadProgress(0);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            setIsUploading(false);
            // Navigate to upload page or trigger onFileUpload callback
            if (onFileUpload) {
              onFileUpload(files);
            } else {
              navigate('/upload');
            }
            return 100;
          }
          return prev + 25;
        });
      }, 750);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const handleGoalsSave = () => {
    setIsGoalsSet(true);
    setTimeout(() => setIsGoalsSet(false), 2000);
  };

  if (isUploading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center max-w-md w-full mx-4">
          <div className="w-12 h-12 border-3 border-brand-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h3 className="text-xl font-semibold text-brand-primary mb-2">
            Processing Your File
          </h3>
          <p className="text-slate-600 mb-6">
            AI is analyzing your transactions...
          </p>

          <div className="w-full bg-slate-200 rounded-full h-2 mb-4">
            <div
              className="bg-brand-primary h-2 rounded-full transition-all duration-500"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>

          <p className="text-sm text-slate-500">
            {uploadProgress === 0 && 'Starting analysis...'}
            {uploadProgress === 25 && 'Uploading file...'}
            {uploadProgress === 50 && 'Reading file structure...'}
            {uploadProgress === 75 && 'AI analyzing transactions...'}
            {uploadProgress === 100 && 'Complete! Redirecting...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Professional Header */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-brand-primary mb-3">
              Welcome to giki.ai
            </h1>
            <p className="text-lg text-slate-600">
              Your AI-powered financial assistant
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-12 space-y-12">
        {/* Getting Started Section */}
        <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden">
          <div className="bg-brand-primary px-6 py-4">
            <h2 className="text-lg font-semibold text-white flex items-center gap-2">
              <span className="text-xl">↑</span>
              GET STARTED
            </h2>
          </div>

          <div className="p-8">
            {/* Interactive Upload Area */}
            <div
              className={`
                border-2 border-dashed rounded-xl p-12 text-center transition-all duration-300 cursor-pointer
                ${
                  isDragActive
                    ? 'border-[#1D372E] bg-green-50 transform scale-105'
                    : 'border-brand-primary bg-green-25 hover:bg-green-50 hover:border-[#1D372E] hover:transform hover:scale-105'
                }
              `}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleFileSelect}
            >
              <div className="mb-6">
                <div className="text-5xl text-brand-primary mb-4 animate-pulse">
                  ↑
                </div>
                <h3 className="text-xl font-semibold text-brand-primary mb-2">
                  Upload Your First File
                </h3>
                <p className="text-slate-600 mb-2">
                  Drop your transaction file here or click to browse
                </p>
                <p className="text-sm text-slate-500 mb-6">
                  Excel (.xlsx) or CSV accepted
                </p>
              </div>

              <Button
                size="lg"
                className="bg-brand-primary hover:bg-brand-primary-hover text-white font-medium"
              >
                □ Choose Files
              </Button>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".xlsx,.xls,.csv"
                className="hidden"
                onChange={(e) => handleFileUpload(e.target.files)}
              />
            </div>
          </div>
        </div>

        {/* How It Works Section */}
        <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden">
          <div className="bg-brand-primary px-6 py-4">
            <h2 className="text-lg font-semibold text-white">HOW IT WORKS</h2>
          </div>

          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  step: 1,
                  icon: '□',
                  title: 'UPLOAD',
                  description: 'Upload your transaction files (Excel or CSV)',
                },
                {
                  step: 2,
                  icon: '⚬',
                  title: 'AI MAGIC',
                  description:
                    'Our AI reads and categorizes every transaction automatically',
                },
                {
                  step: 3,
                  icon: '□',
                  title: 'INSIGHTS',
                  description:
                    'Get instant financial insights and professional reports',
                },
              ].map((item, index) => (
                <div
                  key={item.step}
                  className="text-center transform transition-all duration-300 hover:scale-105"
                  style={{
                    animation: `fadeInUp 0.6s ease forwards ${0.1 * (index + 1)}s`,
                    opacity: 0,
                  }}
                >
                  <div className="w-12 h-12 bg-brand-primary text-white rounded-full flex items-center justify-center font-semibold text-lg mx-auto mb-4">
                    {item.step}
                  </div>
                  <div className="text-3xl mb-4">{item.icon}</div>
                  <h3 className="font-semibold text-slate-900 mb-2 uppercase tracking-wide text-sm">
                    {item.title}
                  </h3>
                  <p className="text-sm text-slate-600 leading-relaxed">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Setup Section */}
        <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden">
          <div className="bg-brand-primary px-6 py-4">
            <h2 className="text-lg font-semibold text-white">QUICK SETUP</h2>
          </div>

          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Financial Goals Setup */}
              <div className="border border-slate-200 rounded-lg p-6">
                <div className="text-2xl mb-3">⚬</div>
                <h3 className="font-semibold text-slate-900 mb-4">
                  Set your financial goals and budget targets
                </h3>

                <div className="space-y-3 mb-4">
                  <input
                    type="text"
                    value={monthlyBudget}
                    onChange={(e) => setMonthlyBudget(e.target.value)}
                    placeholder="Monthly Budget (e.g., $10,000)"
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent"
                  />
                  <input
                    type="text"
                    value={mainCategories}
                    onChange={(e) => setMainCategories(e.target.value)}
                    placeholder="Main Categories (e.g., Office, Travel, Software)"
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent"
                  />
                </div>

                <Button
                  onClick={handleGoalsSave}
                  className={`
                    w-full transition-all duration-200
                    ${
                      isGoalsSet
                        ? 'bg-green-600 hover:bg-green-700'
                        : 'bg-brand-primary hover:bg-brand-primary-hover'
                    }
                  `}
                >
                  {isGoalsSet ? (
                    <span className="flex items-center gap-2">
                      <span className="w-4 h-4 text-lg font-bold flex items-center justify-center">✓</span>
                      Goals Saved!
                    </span>
                  ) : (
                    'Save Goals'
                  )}
                </Button>
              </div>

              {/* Learning Resources */}
              <div className="border border-slate-200 rounded-lg p-6">
                <div className="text-2xl mb-3">□</div>
                <h3 className="font-semibold text-slate-900 mb-4">
                  Learn about advanced features
                </h3>
                <p className="text-sm text-slate-600 mb-4">
                  Get up to speed with tutorials and detailed guides
                </p>

                <div className="grid grid-cols-1 gap-3">
                  <Button
                    variant="outline"
                    className="border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white"
                  >
                    <div className="text-center">
                      <div>Watch Tutorial</div>
                      <small className="text-xs opacity-75">
                        3 min overview
                      </small>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    className="border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white"
                  >
                    <div className="text-center">
                      <div>Read Documentation</div>
                      <small className="text-xs opacity-75">
                        Detailed guides
                      </small>
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Professional Tip Section */}
        <div className="bg-gradient-to-r from-giki-primary/5 to-giki-primary/10 border border-giki-primary/20 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <div className="text-brand-primary text-lg">⚬</div>
            <p className="text-brand-primary font-medium">
              <strong>Tip:</strong> Start with one month of transactions to see
              AI categorization working
            </p>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
};

export default EmptyDashboard;
