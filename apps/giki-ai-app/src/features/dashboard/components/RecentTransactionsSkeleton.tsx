import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Skeleton } from '@/shared/components/ui/skeleton';

export const RecentTransactionsSkeleton: React.FC = () => {
  return (
    <Card role="status" aria-label="Loading recent transactions">
      <CardHeader className="flex flex-wrap flex-row items-center justify-between space-y-0">
        <CardTitle className="text-card-foreground-title">
          Recent Transactions
        </CardTitle>
        <Skeleton className="h-9 w-20" /> {/* View All button */}
      </CardHeader>
      <CardContent className="space-y-3 overflow-hidden">
        {/* Render 6 skeleton transaction rows */}
        {Array.from({ length: 6 }).map((_, index) => (
          <div
            key={index}
            className="flex flex-wrap items-center justify-between p-3 border rounded-lg"
          >
            <div className="flex flex-wrap items-center space-x-3">
              <Skeleton className="h-10 w-10 rounded-lg" /> {/* Icon */}
              <div className="space-y-1">
                <Skeleton className="h-4 w-32" /> {/* Description */}
                <div className="flex flex-wrap items-center space-x-2">
                  <Skeleton className="h-3 w-16 sm:w-16" /> {/* Date */}
                  <Skeleton className="h-4 w-16 sm:w-16 rounded-full" />{' '}
                  {/* Category badge */}
                </div>
              </div>
            </div>
            <div className="text-right space-y-1">
              <Skeleton className="h-4 w-20" /> {/* Amount */}
              <Skeleton className="h-3 w-12" /> {/* AI confidence */}
            </div>
          </div>
        ))}

        {/* Load more skeleton */}
        <div className="flex flex-wrap justify-center pt-4">
          <Skeleton className="h-4 w-32" />
        </div>

        <span className="sr-only">
          Loading recent transactions, please wait...
        </span>
      </CardContent>
    </Card>
  );
};

export default RecentTransactionsSkeleton;
