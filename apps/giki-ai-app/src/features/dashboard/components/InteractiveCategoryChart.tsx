import React, { useState } from 'react';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  PieChart as PieChartIcon,
  BarChart3,
  ArrowRight,
  TrendingUp,
  Eye,
  Layers,
} from 'lucide-react';
import type { CategoryBreakdownItem } from '../types/dashboard';
import { cn } from '@/shared/utils/utils';

interface InteractiveCategoryChartProps {
  data: CategoryBreakdownItem[];
  onViewAll: () => void;
  onCategoryDrillDown?: (category: string) => void;
}

const COLORS = [
  'hsl(var(--giki-brand-blue))',
  'hsl(var(--giki-destructive))',
  'hsl(var(--giki-success))',
  'hsl(var(--giki-warning))',
  'hsl(var(--giki-brand-purple))',
  'hsl(var(--giki-brand-pink))',
  'hsl(var(--giki-info))',
  'hsl(var(--giki-brand-green))',
  'hsl(var(--giki-brand-green-dark))',
  'hsl(var(--giki-brand-blue-dark))',
];

export const InteractiveCategoryChart: React.FC<
  InteractiveCategoryChartProps
> = ({ data, onViewAll, onCategoryDrillDown }) => {
  const [chartType, setChartType] = useState<'pie' | 'bar'>('pie');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number): string => {
    return `${percentage.toFixed(1)}%`;
  };

  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);

  // Enhanced data with colors and calculated values
  const enhancedData = data.slice(0, 8).map((item, index) => ({
    ...item,
    color: item.color || COLORS[index % COLORS.length],
    fill: item.color || COLORS[index % COLORS.length],
  }));

  type EnhancedCategoryData = CategoryBreakdownItem & {
    color: string;
    fill: string;
  };

  interface TooltipPayload {
    payload: EnhancedCategoryData;
    value: number;
  }

  const CustomTooltip = ({
    active,
    payload,
  }: {
    active?: boolean;
    payload?: TooltipPayload[];
  }) => {
    if (active && payload && payload.length > 0) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-border rounded-lg shadow-lg min-w-[200px]">
          <div className="flex flex-wrap items-center space-x-2 mb-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: data.color }}
            />
            <p className="font-semibold">{data.category}</p>
          </div>
          <div className="space-y-1">
            <div className="flex flex-wrap justify-between">
              <span className="text-sm">Amount:</span>
              <span className="font-semibold">
                {formatCurrency(data.amount)}
              </span>
            </div>
            <div className="flex flex-wrap justify-between">
              <span className="text-sm">Percentage:</span>
              <span className="font-semibold">
                {formatPercentage(data.percentage)}
              </span>
            </div>
            <div className="flex flex-wrap justify-between">
              <span className="text-sm">Transactions:</span>
              <span className="font-semibold">{data.count}</span>
            </div>
            <div className="flex flex-wrap justify-between border-t pt-1">
              <span className="text-sm">Avg per transaction:</span>
              <span className="font-semibold">
                {formatCurrency(data.amount / data.count)}
              </span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const onPieClick = (data: EnhancedCategoryData) => {
    setSelectedCategory(data.category);
    if (onCategoryDrillDown) {
      onCategoryDrillDown(data.category);
    }
  };

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Category Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="flex flex-wrap flex-col items-center justify-center py-12 text-center space-y-4">
            <div className="p-4 bg-muted/50 rounded-full">
              <PieChartIcon className="w-8 h-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">No Category Data</h3>
              <p className="text-sm max-w-md">
                Categorize your transactions to see spending breakdown by
                category.
              </p>
            </div>
            <Button
              className="max-w-full"
              variant="outline"
              onClick={() => (window.location.href = '/transactions')}
            >
              Review Transactions
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold">
              Category Breakdown
            </CardTitle>
            <div className="flex flex-wrap items-center space-x-2">
              <Badge variant="secondary" className="max-w-[150px] truncate">
                {data.length} categories
              </Badge>
              <Badge variant="outline" className="max-w-[150px] truncate">
                {formatCurrency(totalAmount)} total
              </Badge>
            </div>
          </div>
          <div className="flex flex-wrap items-center space-x-2">
            <Button
              className="max-w-full"
              variant={chartType === 'pie' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('pie')}
            >
              <PieChartIcon className="w-4 h-4" />
            </Button>
            <Button
              className="max-w-full"
              variant={chartType === 'bar' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('bar')}
            >
              <BarChart3 className="w-4 h-4" />
            </Button>
            <Button
              className="max-w-full"
              variant="ghost"
              size="sm"
              onClick={() => void onViewAll()}
            >
              View All
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6 overflow-hidden">
        {/* Interactive Chart */}
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'pie' ? (
              <PieChart>
                <Pie
                  data={enhancedData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({
                    category,
                    percentage,
                  }: {
                    category: string;
                    percentage: number;
                  }) => `${category}: ${formatPercentage(percentage)}`}
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="amount"
                  onClick={(data: EnhancedCategoryData) =>
                    void onPieClick(data)
                  }
                  className="cursor-pointer"
                >
                  {enhancedData.map((entry) => (
                    <Cell
                      key={`cell-${entry.category}`}
                      fill={entry.color}
                      stroke={
                        selectedCategory === entry.category
                          ? 'hsl(var(--giki-foreground))'
                          : 'none'
                      }
                      strokeWidth={selectedCategory === entry.category ? 2 : 0}
                    />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            ) : (
              <BarChart
                data={enhancedData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--giki-neutral-100))"
                />
                <XAxis
                  dataKey="category"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 11, fill: 'hsl(var(--giki-neutral-500))' }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: 'hsl(var(--giki-neutral-500))' }}
                  tickFormatter={(value: number) => formatCurrency(value)}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar
                  dataKey="amount"
                  radius={[4, 4, 0, 0]}
                  className="cursor-pointer"
                  onClick={(data: EnhancedCategoryData) => onPieClick(data)}
                >
                  {enhancedData.map((entry) => (
                    <Cell key={`cell-${entry.category}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Category List with Actions */}
        <div className="space-y-3">
          <div className="flex flex-wrap items-center justify-between">
            <h4 className="font-medium text-foreground">Top Categories</h4>
            {selectedCategory && (
              <Badge
                variant="outline"
                className="text-xs max-w-[150px] truncate"
              >
                Selected: {selectedCategory}
              </Badge>
            )}
          </div>

          <div className="space-y-2 max-h-40 overflow-y-auto">
            {enhancedData.slice(0, 5).map((item) => (
              <div
                key={item.category}
                className={cn(
                  'flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-all hover:bg-muted/50',
                  selectedCategory === item.category &&
                    'bg-muted border-primary',
                )}
                onClick={() => onPieClick(item)}
              >
                <div className="flex flex-wrap items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded-full flex flex-wrap-shrink-0"
                    style={{ backgroundColor: item.color }}
                  />
                  <div className="min-w-0 flex flex-wrap-1">
                    <p className="font-medium truncate">{item.category}</p>
                    <p className="text-xs text-muted-foreground">
                      {item.count} transactions
                    </p>
                  </div>
                </div>

                <div className="flex flex-wrap items-center space-x-3">
                  <div className="text-right">
                    <p className="font-semibold">
                      {formatCurrency(item.amount)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatPercentage(item.percentage)}
                    </p>
                  </div>
                  {onCategoryDrillDown && (
                    <Button
                      className="max-w-full h-8 w-8 p-0"
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onCategoryDrillDown(item.category);
                      }}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {data.length > 5 && (
            <div className="flex flex-wrap items-center justify-between pt-3 border-t border-border">
              <span className="text-sm">
                +{data.length - 5} more categories
              </span>
              <Button
                className="max-w-full"
                variant="ghost"
                size="sm"
                onClick={() => void onViewAll()}
              >
                <Layers className="w-4 h-4 mr-2" />
                View All Categories
              </Button>
            </div>
          )}
        </div>

        {/* Quick Insights */}
        {data.length > 0 && (
          <div className="pt-4 border-t border-border">
            <div className="flex flex-wrap items-start space-x-3 p-3 bg-info/5 rounded-lg">
              <div className="p-1 bg-info/10 rounded">
                <TrendingUp className="w-4 h-4 text-info" />
              </div>
              <div className="flex flex-wrap-1 min-w-0">
                <p className="font-medium text-info-foreground">
                  Top Category: {data[0].category}
                </p>
                <p className="text-xs text-info">
                  {formatPercentage(data[0].percentage)} of total spending •{' '}
                  {formatCurrency(data[0].amount)}
                </p>
                <p className="text-xs text-info mt-1">
                  Average: {formatCurrency(data[0].amount / data[0].count)} per
                  transaction
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InteractiveCategoryChart;
