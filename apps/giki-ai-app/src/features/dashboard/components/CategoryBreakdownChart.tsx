import React from 'react';

import { Button } from '@/shared/components/ui/button';
import { <PERSON>R<PERSON>, PieC<PERSON>, TrendingUp } from 'lucide-react';
import type { CategoryBreakdownItem } from '../types/dashboard';

interface CategoryBreakdownChartProps {
  data: CategoryBreakdownItem[];
  onViewAll: () => void;
}

export const CategoryBreakdownChart: React.FC<CategoryBreakdownChartProps> = ({
  data,
  onViewAll,
}) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number): string => {
    return `${percentage.toFixed(1)}%`;
  };

  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);

  if (data.length === 0) {
    return (
      <div className="border border-gray-200 shadow-sm overflow-hidden bg-white">
        <div className="bg-brand-primary text-white px-6 py-4">
          <h3 className="text-lg font-semibold">Category Breakdown</h3>
        </div>
        <div className="p-6 overflow-hidden">
          <div className="flex flex-wrap flex-col items-center justify-center py-12 text-center space-y-4">
            <div className="p-4 bg-muted/50 rounded-full">
              <PieChart className="w-8 h-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="truncate text-heading-5">No Category Data</h3>
              <p className="text-body-small max-w-md">
                Categorize your transactions to see spending breakdown by
                category.
              </p>
            </div>
            <Button
              className="max-w-full border-brand-primary text-brand-primary hover:bg-green-50"
              variant="outline"
              onClick={() => (window.location.href = '/transactions')}
            >
              Review Transactions
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="border border-gray-200 shadow-sm overflow-hidden bg-white">
      <div className="bg-brand-primary text-white px-6 py-4">
        <div className="flex flex-wrap items-center justify-between">
          <h3 className="text-lg font-semibold">Category Breakdown</h3>
          <Button
            className="max-w-full border-white/20 text-white hover:bg-white/10"
            variant="ghost"
            size="sm"
            onClick={() => void onViewAll()}
          >
            View All
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
      <div className="p-6 space-y-4 overflow-hidden">
        {/* Chart Visualization */}
        <div className="relative">
          {/* Simple Visual Chart - TODO: Replace with actual chart library */}
          <div className="space-y-3">
            {data.slice(0, 5).map((item) => (
              <div key={item.category} className="space-y-2">
                <div className="flex flex-wrap items-center justify-between text-sm">
                  <div className="flex flex-wrap items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{
                        backgroundColor:
                          item.color || 'hsl(var(--giki-brand-blue))',
                      }}
                    />
                    <span className="font-medium truncate">
                      {item.category}
                    </span>
                  </div>
                  <span className="text-muted-foreground">
                    {formatPercentage(item.percentage)}
                  </span>
                </div>

                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-500"
                    style={{
                      backgroundColor:
                        item.color || 'hsl(var(--giki-brand-blue))',
                      width: `${item.percentage}%`,
                    }}
                  />
                </div>

                <div className="flex flex-wrap items-center justify-between truncate text-caption text-muted-foreground">
                  <span>{formatCurrency(item.amount)}</span>
                  {item.count > 0 && <span>{item.count} transactions</span>}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Summary */}
        <div className="pt-4 border-t border-border space-y-3">
          <div className="flex flex-wrap items-center justify-between">
            <span className="text-label">Total Spending</span>
            <span className="text-status-badge">
              {formatCurrency(totalAmount)}
            </span>
          </div>

          {data.length > 5 && (
            <div className="flex flex-wrap items-center justify-between text-body-small">
              <span>+{data.length - 5} more categories</span>
              <Button
                className="max-w-full text-brand-primary hover:bg-green-50"
                variant="ghost"
                size="sm"
                onClick={() => void onViewAll()}
              >
                View All
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )}
        </div>

        {/* Insights */}
        {data.length > 0 && (
          <div className="pt-4 border-t border-border">
            <div className="flex flex-wrap items-start space-x-3 p-3 bg-info/5 rounded-lg">
              <div className="p-1 bg-info/10 rounded">
                <TrendingUp className="w-4 h-4 text-info" />
              </div>
              <div className="flex flex-wrap-1">
                <p className="text-label text-info-foreground">
                  Top Category: {data[0].category}
                </p>
                <p className="truncate text-caption text-info">
                  {formatPercentage(data[0].percentage)} of total spending •{' '}
                  {formatCurrency(data[0].amount)}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryBreakdownChart;
