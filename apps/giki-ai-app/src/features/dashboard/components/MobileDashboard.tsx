/**
 * Mobile Dashboard Component
 * Implements wireframe: docs/wireframes/05-mobile-journey/01-mobile-dashboard.md
 */
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Card } from '@/shared/components/ui/card';
import { useAuth } from '@/features/auth';
import {
  Menu,
  Bell,
  CheckCircle,
  Folder,
  Eye,
  Home,
  BarChart3,
  Settings,
  MessageCircle,
  TrendingUp,
  Activity,
} from 'lucide-react';

interface Activity {
  id: string;
  type: 'success' | 'report' | 'processing';
  icon: React.ReactNode;
  title: string;
  description: string;
  time: string;
}

interface DashboardStats {
  transactions: number;
  accuracy: number;
  reviewCount: number;
  unreadCount: number;
}

export const MobileDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const firstName =
    user?.full_name?.split(' ')[0] || user?.email?.split('@')[0] || 'there';

  const [stats] = useState<DashboardStats>({
    transactions: 2847,
    accuracy: 96,
    reviewCount: 23,
    unreadCount: 2,
  });

  const [activities] = useState<Activity[]>([
    {
      id: '1',
      type: 'success',
      icon: <CheckCircle className="h-4 w-4" />,
      title: '156 processed',
      description: 'March data.xlsx',
      time: '2 min ago',
    },
    {
      id: '2',
      type: 'report',
      icon: <BarChart3 className="h-4 w-4" />,
      title: 'Report ready',
      description: 'March Summary',
      time: '1 hour ago',
    },
    {
      id: '3',
      type: 'processing',
      icon: <Activity className="h-4 w-4" />,
      title: 'AI categorizing',
      description: 'Q1 expenses.csv',
      time: '3 hours ago',
    },
  ]);

  // Pull to refresh simulation
  const [refreshing, setRefreshing] = useState(false);

  const _handleRefresh = async () => {
    setRefreshing(true);
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const openAgent = () => {
    // Open agent panel or navigate to agent page
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Mobile Header */}
      <header className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <Button variant="ghost" size="icon" className="text-gray-600">
          <Menu className="h-5 w-5" />
        </Button>

        <h1 className="font-semibold text-lg text-gray-900">giki.ai</h1>

        <Button variant="ghost" size="icon" className="relative text-gray-600">
          <Bell className="h-5 w-5" />
          {stats.unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full" />
          )}
        </Button>
      </header>

      {/* Pull to refresh indicator */}
      {refreshing && (
        <div className="text-center py-2 bg-green-50 border-b border-green-200">
          <div className="text-sm text-brand-primary">Refreshing...</div>
        </div>
      )}

      {/* Main Content */}
      <div className="p-4 space-y-6">
        {/* Greeting */}
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-1">
            Good morning, {firstName}! ↑
          </h2>
        </div>

        {/* Hero Stats Card */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden">
          <div className="bg-brand-primary text-white px-6 py-4">
            <h3 className="text-base font-semibold text-center">⊞ This Week</h3>
          </div>
          <div className="p-6">
            <div className="text-center space-y-4">
              <div>
                <div className="text-4xl font-bold text-brand-primary font-mono">
                  {stats.transactions.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">Transactions</div>
              </div>

              {/* Progress Bar */}
              <div className="relative h-3 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="absolute inset-y-0 left-0 bg-brand-primary rounded-full transition-all duration-500"
                  style={{ width: `${stats.accuracy}%` }}
                />
              </div>

              <div className="text-sm text-brand-primary">
                {stats.accuracy}% Accuracy
              </div>

              <Badge className="bg-green-100 text-brand-primary border-green-200">
                <CheckCircle className="h-3 w-3 mr-1" />
                All Done
              </Badge>
            </div>
          </div>
        </Card>

        {/* Quick Actions */}
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-3">
            Quick Actions
          </h3>

          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              className="h-20 flex-col gap-2 border-brand-primary text-brand-primary hover:bg-green-50"
              onClick={() => navigate('/upload')}
            >
              <Folder className="h-6 w-6" />
              <span>⊞ Upload</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex-col gap-2 relative border-brand-primary text-brand-primary hover:bg-green-50"
              onClick={() => navigate('/transactions')}
            >
              <Eye className="h-6 w-6" />
              <span>∷ Review</span>
              {stats.reviewCount > 0 && (
                <Badge className="absolute -top-2 -right-2 bg-orange-100 text-orange-700 border-orange-200 text-xs">
                  {stats.reviewCount}
                </Badge>
              )}
            </Button>
          </div>
        </div>

        {/* Recent Activity Feed */}
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-3">
            Recent Activity
          </h3>

          <div className="space-y-2">
            {activities.map((activity) => (
              <Card
                key={activity.id}
                className="p-3 border-gray-200 hover:border-gray-300 transition-colors"
              >
                <div className="flex items-start gap-3">
                  <div
                    className={`mt-0.5 ${
                      activity.type === 'success' && 'text-brand-primary'
                    } ${activity.type === 'report' && 'text-brand-primary'} ${
                      activity.type === 'processing' && 'text-orange-600'
                    }`}
                  >
                    {activity.icon}
                  </div>

                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {activity.title}
                    </p>
                    <p className="text-xs text-gray-600">
                      {activity.description}
                    </p>
                  </div>

                  <span className="text-xs text-gray-500 whitespace-nowrap">
                    {activity.time}
                  </span>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="text-center">
          <div className="inline-flex items-center gap-2 text-sm text-gray-500">
            <TrendingUp className="h-4 w-4" />
            <span>Your AI is getting smarter every day</span>
          </div>
        </div>
      </div>

      {/* Floating Agent Button */}
      <Button
        size="icon"
        className="fixed bottom-20 right-4 h-14 w-14 rounded-full shadow-lg bg-brand-primary hover:bg-brand-primary-hover border-0"
        onClick={openAgent}
      >
        <MessageCircle className="h-6 w-6 text-white" />
      </Button>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 border-t border-gray-200 bg-white">
        <div className="grid grid-cols-4">
          <Button
            variant="ghost"
            className="h-16 flex-col gap-1 rounded-none text-gray-600 hover:text-brand-primary hover:bg-green-50"
            onClick={() => navigate('/dashboard')}
          >
            <Home className="h-5 w-5" />
            <span className="text-xs">Home</span>
          </Button>

          <Button
            variant="ghost"
            className="h-16 flex-col gap-1 rounded-none text-gray-600 hover:text-brand-primary hover:bg-green-50"
            onClick={() => navigate('/upload')}
          >
            <Folder className="h-5 w-5" />
            <span className="text-xs">Files</span>
          </Button>

          <Button
            variant="ghost"
            className="h-16 flex-col gap-1 rounded-none text-gray-600 hover:text-brand-primary hover:bg-green-50"
            onClick={() => navigate('/reports')}
          >
            <BarChart3 className="h-5 w-5" />
            <span className="text-xs">Reports</span>
          </Button>

          <Button
            variant="ghost"
            className="h-16 flex-col gap-1 rounded-none text-gray-600 hover:text-brand-primary hover:bg-green-50"
            onClick={() => navigate('/settings')}
          >
            <Settings className="h-5 w-5" />
            <span className="text-xs">Settings</span>
          </Button>
        </div>
      </nav>
    </div>
  );
};
