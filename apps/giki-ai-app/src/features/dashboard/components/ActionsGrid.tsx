import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { Upload, FileCheck, BarChart3, Folder } from 'lucide-react';
import type { Action } from '../types/dashboard';
import { cn } from '@/shared/utils/utils';

interface ActionsGridProps {
  actions: Action[];
}

export const ActionsGrid: React.FC<ActionsGridProps> = ({ actions }) => {
  const getIcon = (iconName: string) => {
    const iconMap = {
      Upload,
      FileCheck,
      BarChart3,
      Folder,
    };

    const IconComponent = iconMap[iconName as keyof typeof iconMap] || Upload;
    return IconComponent;
  };

  // Professional unified styling using design tokens
  const cardClasses = {
    bg: 'bg-white hover:bg-success/10',
    border: 'border-gray-200 hover:border-brand-primary',
    icon: 'bg-brand-primary text-white',
    text: 'text-gray-900',
    accent: 'text-gray-600',
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {actions.map((action) => {
        const IconComponent = getIcon(action.icon);

        return (
          <Link
            key={action.id}
            to={action.route}
            aria-label={`${action.title}: ${action.description}`}
            className={action.isEnabled ? '' : 'pointer-events-none'}
          >
            <Card
              className={cn(
                'group cursor-pointer transition-colors border border-gray-200 shadow-sm hover:shadow-md focus-within:ring-2 focus-within:ring-brand-primary/20 focus-within:ring-offset-2',
                action.isEnabled ? cardClasses.bg : 'bg-gray-100',
                action.isEnabled
                  ? cardClasses.border
                  : 'border-gray-300 opacity-60',
                action.isEnabled ? 'hover:shadow-md' : 'cursor-not-allowed',
              )}
            >
              <CardContent className="p-6 overflow-hidden">
                <div className="flex flex-wrap items-start space-x-4">
                  <div
                    className={cn(
                      'p-2 rounded-md',
                      action.isEnabled
                        ? cardClasses.icon
                        : 'bg-gray-300 text-gray-500',
                    )}
                  >
                    <IconComponent className="w-4 h-4" />
                  </div>

                  <div className="flex flex-wrap-1 min-w-0">
                    <h3
                      className={cn(
                        'font-medium text-sm mb-1',
                        action.isEnabled
                          ? cardClasses.text
                          : 'text-muted-foreground',
                      )}
                    >
                      {action.title}
                    </h3>

                    <p className="truncate text-caption text-muted-foreground">
                      {action.description}
                    </p>

                    {action.badge && (
                      <Badge
                        variant="outline"
                        className="mt-2 max-w-[150px] truncate"
                      >
                        {action.badge}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        );
      })}
    </div>
  );
};

export default ActionsGrid;
