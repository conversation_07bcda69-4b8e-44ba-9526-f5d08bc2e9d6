import React, { useMemo, useCallback } from 'react';
import { Card } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  ArrowRight,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  AlertCircle,
  Brain,
} from 'lucide-react';
import type { RecentTransaction } from '../types/dashboard';
import { cn } from '@/shared/utils/utils';

interface RecentTransactionsListProps {
  transactions: RecentTransaction[];
  onViewAll: () => void;
}

export const RecentTransactionsList: React.FC<RecentTransactionsListProps> = ({
  transactions,
  onViewAll,
}) => {
  // Memoize currency formatter to avoid recreating NumberFormat instance
  const formatCurrency = useCallback((amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(Math.abs(amount));
  }, []);

  // Memoize date formatter with current time dependency
  const formatDate = useCallback((dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;

    return date.toLocaleDateString('en-IN', {
      month: 'short',
      day: 'numeric',
    });
  }, []);

  // Memoize status badge component to prevent re-rendering
  const getStatusBadge = useCallback((status: RecentTransaction['status']) => {
    switch (status) {
      case 'categorized':
        return (
          <Badge
            variant="default"
            className="bg-success/10 text-success border-success/20 max-w-[150px] truncate"
          >
            <CheckCircle className="w-3 h-3 mr-1" />
            Categorized
          </Badge>
        );
      case 'ai_suggested':
        return (
          <Badge
            variant="secondary"
            className="bg-info/10 text-info border-info/20 max-w-[150px] truncate"
          >
            <Brain className="w-3 h-3 mr-1" />
            AI Suggested
          </Badge>
        );
      case 'needs_review':
        return (
          <Badge
            variant="outline"
            className="bg-warning/10 text-warning border-warning/20 max-w-[150px] truncate"
          >
            <AlertCircle className="w-3 h-3 mr-1" />
            Needs Review
          </Badge>
        );
    }
  }, []);

  // Memoize transaction icon component to prevent re-rendering
  const getTransactionIcon = useCallback((type: 'income' | 'expense') => {
    return type === 'income' ? (
      <div className="card-padding-sm bg-success/10 rounded-lg">
        <TrendingUp className="h-4 w-4 text-success" />
      </div>
    ) : (
      <div className="card-padding-sm bg-destructive/10 rounded-lg">
        <TrendingDown className="h-4 w-4 text-destructive" />
      </div>
    );
  }, []);

  // Memoize view all handler to prevent unnecessary re-renders
  const handleViewAll = useCallback(() => {
    onViewAll();
  }, [onViewAll]);

  // Memoize transaction list to prevent re-rendering when transactions haven't changed
  const transactionItems = useMemo(() => {
    return transactions.map((transaction) => (
      <div
        key={transaction.id}
        className="flex flex-wrap items-center component-gap card-padding rounded-lg border border-border bg-card hover:bg-muted/50 transition-colors"
      >
        {/* Transaction Type Icon */}
        {getTransactionIcon(transaction.transaction_type)}

        {/* Transaction Details */}
        <div className="flex flex-wrap-1 min-w-0">
          <div className="flex flex-wrap items-start justify-between">
            <div className="flex flex-wrap-1 min-w-0">
              <p className="font-medium text-foreground truncate">
                {transaction.description}
              </p>
              <div className="flex flex-wrap items-center component-gap-sm form-field-spacing">
                <span className="text-xs text-muted-foreground">
                  {formatDate(transaction.date)}
                </span>
                {transaction.category && (
                  <>
                    <span className="text-xs text-muted-foreground">•</span>
                    <span className="text-xs text-muted-foreground truncate">
                      {transaction.category}
                    </span>
                  </>
                )}
              </div>
            </div>

            <div className="flex flex-wrap flex-col items-end component-gap-sm component-gap">
              <span
                className={cn(
                  'text-sm font-semibold text-financial',
                  transaction.transaction_type === 'income'
                    ? 'text-success'
                    : 'text-destructive',
                )}
              >
                {transaction.transaction_type === 'income' ? '+' : '-'}
                {formatCurrency(transaction.amount)}
              </span>
              {getStatusBadge(transaction.status)}
            </div>
          </div>
        </div>
      </div>
    ));
  }, [transactions, formatCurrency, formatDate, getTransactionIcon, getStatusBadge]);

  if (transactions.length === 0) {
    return (
      <Card className="border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-brand-primary text-white px-6 py-4">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            <h3 className="text-base font-semibold">⊞ Recent Transactions</h3>
          </div>
        </div>
        <div className="p-6">
          <div className="flex flex-wrap flex-col items-center justify-center py-12 text-center space-y-4">
            <div className="p-4 bg-muted/50 rounded-full">
              <Clock className="w-8 h-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">No Recent Transactions</h3>
              <p className="text-sm max-w-md">
                Upload some financial data to see your recent transactions here.
              </p>
            </div>
            <Button
              className="max-w-full"
              variant="outline"
              onClick={() => (window.location.href = '/upload')}
            >
              Upload Data
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="border border-gray-200 shadow-sm overflow-hidden">
      <div className="bg-brand-primary text-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            <h3 className="text-base font-semibold">⊞ Recent Transactions</h3>
          </div>
          <Button
            className="bg-white/10 hover:bg-white/20 text-white border-white/20"
            variant="outline"
            size="sm"
            onClick={handleViewAll}
          >
            View All
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
      <div className="p-6 space-y-4">
        {transactionItems}

        {/* Summary Footer */}
        <div className="pt-4 border-t border-border">
          <div className="flex flex-wrap items-center justify-between text-sm">
            <span className="text-muted-foreground">
              Showing {transactions.length} recent transactions
            </span>
            <Button
              className="max-w-full"
              variant="ghost"
              size="sm"
              onClick={handleViewAll}
            >
              View All Transactions
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default RecentTransactionsList;
