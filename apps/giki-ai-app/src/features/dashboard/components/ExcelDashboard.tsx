/**
 * Excel-Inspired Dashboard with Real Transaction Data
 *
 * Professional Excel-like dashboard component that integrates with real transaction API.
 * Provides comprehensive financial data visualization and analysis.
 */
import React, { useState, useEffect } from 'react';
import ExcelLayout from '@/shared/components/ui/excel-layout';
import { MetricCard } from '@/shared/components/ui/card';
import ExcelTable from '@/shared/components/ui/excel-table';
import { Button } from '@/shared/components/ui/button';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  PieChart,
  BarChart3,
  AlertCircle,
  Loader2,
} from 'lucide-react';
import { Transaction } from '@/shared/types/categorization';
import {
  fetchTransactions,
  getTransactionStats,
} from '@/features/transactions/services/transactionService';
import { DashboardErrorBoundary } from '@/shared/components/error/FeatureErrorBoundary';

const tableColumns = [
  {
    key: 'date',
    label: 'Date',
    type: 'date' as const,
    sortable: true,
    width: '100px',
  },
  {
    key: 'description',
    label: 'Description',
    type: 'text' as const,
    sortable: true,
    width: '300px',
  },
  {
    key: 'amount',
    label: 'Amount',
    type: 'currency' as const,
    sortable: true,
    aggregatable: true,
    width: '120px',
  },
  {
    key: 'category_path',
    label: 'Category',
    type: 'text' as const,
    sortable: true,
    width: '200px',
  },
  {
    key: 'account',
    label: 'Account',
    type: 'text' as const,
    sortable: true,
    width: '150px',
  },
  {
    key: 'status',
    label: 'Status',
    type: 'status' as const,
    sortable: true,
    width: '120px',
  },
  {
    key: 'ai_category_confidence',
    label: 'Confidence',
    type: 'percentage' as const,
    sortable: true,
    width: '100px',
  },
];

interface TransactionSummary {
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  transactionCount: number;
  categorizedCount: number;
}

const ExcelDashboard: React.FC = () => {
  const [selectedView, setSelectedView] = useState('overview');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [summary, setSummary] = useState<TransactionSummary | null>(null);
  const [categories, setCategories] = useState<
    Array<{ name: string; count: number; amount: number }>
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load real transaction data on component mount
  useEffect(() => {
    const loadDashboardData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Get current month date range
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          .toISOString()
          .split('T')[0];
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
          .toISOString()
          .split('T')[0];

        // Fetch transactions for current month
        const transactionResponse = await fetchTransactions({
          startDate,
          endDate,
          pageSize: 100, // Get more transactions for better overview
        });

        let transactionData: Transaction[] = [];
        if (Array.isArray(transactionResponse)) {
          transactionData = transactionResponse;
        } else if (transactionResponse && 'items' in transactionResponse) {
          transactionData = transactionResponse.items;
        }

        // Fetch transaction stats
        const _statsResponse = await getTransactionStats({
          start: startDate,
          end: endDate,
        });

        setTransactions(transactionData);

        // Calculate summary metrics from real data
        const totalIncome = transactionData
          .filter((t) => t.amount > 0)
          .reduce((sum, t) => sum + t.amount, 0);

        const totalExpenses = transactionData
          .filter((t) => t.amount < 0)
          .reduce((sum, t) => sum + Math.abs(t.amount), 0);

        const netIncome = totalIncome - totalExpenses;
        const categorizedCount = transactionData.filter(
          (t) => t.category_path || t.ai_suggested_category_path,
        ).length;

        setSummary({
          totalIncome,
          totalExpenses,
          netIncome,
          transactionCount: transactionData.length,
          categorizedCount,
        });

        // Group transactions by category for summary
        const categoryMap = new Map<
          string,
          { count: number; amount: number }
        >();
        transactionData.forEach((transaction) => {
          const categoryName =
            transaction.category_path ||
            transaction.ai_suggested_category_path ||
            'Uncategorized';
          if (!categoryMap.has(categoryName)) {
            categoryMap.set(categoryName, { count: 0, amount: 0 });
          }
          const existing = categoryMap.get(categoryName);
          existing.count += 1;
          existing.amount += transaction.amount;
        });

        const categoryArray = Array.from(categoryMap.entries())
          .map(([name, data]) => ({ name, ...data }))
          .sort((a, b) => Math.abs(b.amount) - Math.abs(a.amount))
          .slice(0, 6); // Top 6 categories

        setCategories(categoryArray);
      } catch (err) {
        console.error('Error loading dashboard data:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to load dashboard data',
        );
      } finally {
        setIsLoading(false);
      }
    };

    void loadDashboardData();
  }, []);

  // Show loading state
  if (isLoading) {
    return (
      <ExcelLayout
        title="Financial Dashboard"
        subtitle="Loading transaction data..."
      >
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Loading...</span>
        </div>
      </ExcelLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <ExcelLayout title="Financial Dashboard" subtitle="Error loading data">
        <div className="flex items-center justify-center h-64">
          <AlertCircle className="h-8 w-8 text-destructive" />
          <span className="ml-2 text-destructive">{error}</span>
        </div>
      </ExcelLayout>
    );
  }

  // Use real data or fallback values
  const totalIncome = summary?.totalIncome || 0;
  const totalExpenses = summary?.totalExpenses || 0;
  const netIncome = summary?.netIncome || 0;

  return (
    <DashboardErrorBoundary componentName="ExcelDashboard">
      <ExcelLayout
      title="Financial Dashboard"
      subtitle="Excel-inspired professional data presentation"
      showToolbar={true}
      showFormulaBar={false}
      actions={
        <div className="flex flex-wrap items-center gap-2">
          <Button
            className="max-w-full"
            variant={selectedView === 'overview' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedView('overview')}
          >
            Overview
          </Button>
          <Button
            className="max-w-full"
            variant={selectedView === 'transactions' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedView('transactions')}
          >
            Transactions
          </Button>
          <Button
            className="max-w-full"
            variant={selectedView === 'analytics' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedView('analytics')}
          >
            Analytics
          </Button>
        </div>
      }
    >
      <div className="p-6 space-y-6">
        {selectedView === 'overview' && (
          <>
            {/* Financial Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <MetricCard
                title="Total Income"
                value={totalIncome}
                subtitle="This month"
                icon={TrendingUp}
              />

              <MetricCard
                title="Total Expenses"
                value={totalExpenses}
                subtitle="This month"
                icon={TrendingDown}
              />

              <MetricCard
                title="Net Income"
                value={netIncome}
                subtitle="This month"
                icon={DollarSign}
              />

              <MetricCard
                title="Transactions"
                value={summary?.transactionCount || 0}
                subtitle="Total count"
                icon={CreditCard}
              />
            </div>

            {/* Quick Summary Table */}
            <div className="bg-white rounded-lg border border-border p-4">
              <h3 className="text-card-foreground-title mb-4 flex flex-wrap items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Category Summary
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categories.length > 0 ? (
                  categories.map((category) => (
                    <div
                      key={category.name}
                      className="flex flex-wrap items-center justify-between p-3 bg-muted/50 rounded border"
                    >
                      <span className="text-label">{category.name}</span>
                      <div className="text-right">
                        <div className="text-sm text-financial">
                          {new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD',
                          }).format(category.amount)}
                        </div>
                        <div className="truncate text-caption text-muted-foreground">
                          {category.count} transactions
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-full text-center text-muted-foreground p-8">
                    No transaction data available for this month.
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {selectedView === 'transactions' && (
          <ExcelTable
            data={transactions}
            columns={tableColumns}
            title="Transaction Details"
            showAggregations={true}
            enableSelection={true}
            enableFiltering={true}
            enableExport={true}
          />
        )}

        {selectedView === 'analytics' && (
          <div className="bg-white rounded-lg border border-border p-6">
            <div className="flex flex-wrap items-center gap-2 mb-4">
              <PieChart className="h-5 w-5" />
              <h3 className="text-card-foreground-title">
                Financial Analytics
              </h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Income vs Expenses */}
              <div className="p-4 bg-muted/50 rounded border">
                <h4 className="font-medium mb-3">Income vs Expenses</h4>
                <div className="space-y-3">
                  <div className="flex flex-wrap items-center justify-between">
                    <span className="text-sm">Total Income</span>
                    <span className="text-sm text-success">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                      }).format(totalIncome)}
                    </span>
                  </div>
                  <div className="flex flex-wrap items-center justify-between">
                    <span className="text-sm">Total Expenses</span>
                    <span className="text-sm text-destructive">
                      -
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD',
                      }).format(totalExpenses)}
                    </span>
                  </div>
                  <div className="pt-2 border-t border-border">
                    <div className="flex flex-wrap items-center justify-between font-semibold">
                      <span>Net Income</span>
                      <span
                        className={`font-mono ${netIncome >= 0 ? 'text-success' : 'text-destructive'}`}
                      >
                        {new Intl.NumberFormat('en-US', {
                          style: 'currency',
                          currency: 'USD',
                        }).format(netIncome)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Account Breakdown */}
              <div className="p-4 bg-muted/50 rounded border">
                <h4 className="font-medium mb-3">Account Breakdown</h4>
                <div className="space-y-2">
                  {(() => {
                    // Group transactions by account
                    const accountMap = new Map<
                      string,
                      { count: number; amount: number }
                    >();
                    transactions.forEach((transaction) => {
                      const accountName =
                        transaction.account || 'Unknown Account';
                      if (!accountMap.has(accountName)) {
                        accountMap.set(accountName, { count: 0, amount: 0 });
                      }
                      const existing = accountMap.get(accountName);
                      existing.count += 1;
                      existing.amount += transaction.amount;
                    });

                    const accounts = Array.from(accountMap.entries());

                    return accounts.length > 0 ? (
                      accounts.map(([account, data]) => (
                        <div
                          key={account}
                          className="flex flex-wrap items-center justify-between"
                        >
                          <span className="text-sm">{account}</span>
                          <div className="text-right">
                            <div
                              className={`font-mono text-sm ${data.amount >= 0 ? 'text-success' : 'text-destructive'}`}
                            >
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD',
                              }).format(data.amount)}
                            </div>
                            <div className="truncate text-caption text-muted-foreground">
                              {data.count} transactions
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center text-muted-foreground">
                        No account data available
                      </div>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ExcelLayout>
    </DashboardErrorBoundary>
  );
};

export default ExcelDashboard;
