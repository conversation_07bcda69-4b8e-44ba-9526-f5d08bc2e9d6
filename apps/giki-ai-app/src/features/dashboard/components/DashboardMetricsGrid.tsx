import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
// Removed Lucide imports - replaced with geometric icons
// TrendingUp → ↗, TrendingDown → ↘, ArrowUpDown → ↕
// CheckCircle → ✓, AlertCircle → ⚠, Activity → ⟲  
// Target → ◯, Zap → ⟡
import { formatCompactNumber } from '@/shared/utils/utils';
import {
  formatCurrencyForMetrics,
  getFinancialDisplayClass,
} from '@/shared/utils/formatCurrency';
import { ProcessingAccuracyTooltip } from '@/shared/components/ui/accuracy-tooltip';
import type { DashboardMetrics } from '../types/dashboard';

interface DashboardMetricsGridProps {
  metrics: DashboardMetrics;
}

export const DashboardMetricsGrid: React.FC<DashboardMetricsGridProps> = ({
  metrics,
}) => {
  const categorizationPercentage =
    metrics.totalTransactions > 0
      ? (metrics.categorizedTransactions / metrics.totalTransactions) * 100
      : 0;

  const getNetIncomeIcon = () => {
    if (metrics.netIncome > 0) return '↗';
    if (metrics.netIncome < 0) return '↘';
    return '↕';
  };

  const getNetIncomeLabel = () => {
    if (metrics.netIncome > 0) return 'Profit';
    if (metrics.netIncome < 0) return 'Loss';
    return 'Break Even';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 dashboard-grid-gap">
      {/* Total Income */}
      <Card className="border-brand-primary/20 shadow-lg">
        <CardHeader className="dashboard-card-header">
          <div className="flex items-center justify-between">
            <CardTitle className="text-table-header">
              Total Income
            </CardTitle>
            <div className="dashboard-metric-icon bg-success/10 rounded-full">
              <span className="h-4 w-4 text-brand-primary text-lg font-bold">↗</span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="dashboard-card-content">
          <div className="component-gap-sm">
            <div
              className={`text-card-metric ${getFinancialDisplayClass('metric')} text-brand-primary`}
            >
              {formatCurrencyForMetrics(metrics.totalIncome)}
            </div>
            <p className="text-caption text-muted-foreground">
              Revenue generated this period
            </p>
            <div className="flex items-center component-gap-xs">
              <span className="h-3 w-3 text-brand-primary text-sm font-bold">⟲</span>
              <span className="text-xs text-brand-primary font-medium">
                Revenue stream active
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Total Expenses */}
      <Card className="border-[#F4A460]/20 shadow-lg">
        <CardHeader className="dashboard-card-header">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Expenses
            </CardTitle>
            <div className="dashboard-metric-icon bg-[#F4A460]/10 rounded-full">
              <span className="h-4 w-4 text-[#F4A460] text-lg font-bold">↘</span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="dashboard-card-content">
          <div className="component-gap-sm">
            <div
              className={`text-2xl ${getFinancialDisplayClass('metric')} text-[#F4A460]`}
            >
              {formatCurrencyForMetrics(metrics.totalExpenses)}
            </div>
            <p className="text-xs text-gray-500">Total amount spent</p>
            <div className="flex items-center component-gap-xs">
              <span className="h-3 w-3 text-[#F4A460] text-sm font-bold">◯</span>
              <span className="text-xs text-[#F4A460] font-medium">
                Budget tracking enabled
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Net Income */}
      <Card
        className={`shadow-lg ${
          metrics.netIncome > 0
            ? 'border-brand-primary/20'
            : metrics.netIncome < 0
              ? 'border-[#DC2626]/20'
              : 'border-gray-200'
        }`}
      >
        <CardHeader className="dashboard-card-header">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-gray-600">
              Net Income
            </CardTitle>
            <div
              className={`dashboard-metric-icon rounded-full ${
                metrics.netIncome > 0
                  ? 'bg-success/10'
                  : metrics.netIncome < 0
                    ? 'bg-[#FEE2E2]'
                    : 'bg-gray-100'
              }`}
            >
              <span className={`text-lg font-bold ${
                  metrics.netIncome > 0
                    ? 'text-brand-primary'
                    : metrics.netIncome < 0
                      ? 'text-destructive'
                      : 'text-muted-foreground'
                }`}>
                {getNetIncomeIcon()}
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="dashboard-card-content">
          <div className="component-gap-sm">
            <div
              className={`text-2xl ${getFinancialDisplayClass('metric')} ${
                metrics.netIncome > 0
                  ? 'text-brand-primary'
                  : metrics.netIncome < 0
                    ? 'text-[#DC2626]'
                    : 'text-gray-600'
              }`}
            >
              {formatCurrencyForMetrics(Math.abs(metrics.netIncome))}
            </div>
            <p className="text-xs text-gray-500">
              {metrics.netIncome >= 0
                ? 'Profit this period'
                : 'Loss this period'}
            </p>
            <Badge
              variant={
                metrics.netIncome > 0
                  ? 'default'
                  : metrics.netIncome < 0
                    ? 'destructive'
                    : 'secondary'
              }
              className="text-xs"
            >
              {getNetIncomeLabel()}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Transaction Processing */}
      <Card className="border-[#1A3F5F]/20 shadow-lg">
        <CardHeader className="dashboard-card-header">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-gray-600">
              AI Processing
            </CardTitle>
            <div className="p-2 bg-[#1A3F5F]/10 rounded-full">
              <span className="h-4 w-4 text-[#1A3F5F] text-lg font-bold">⟡</span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="text-2xl font-bold text-[#1A3F5F]">
              {formatCompactNumber(metrics.totalTransactions)}
            </div>
            <p className="text-xs text-gray-500">Transactions processed</p>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <ProcessingAccuracyTooltip
                  accuracy={categorizationPercentage / 100}
                  className="text-gray-600"
                >
                  <span>Categorization Progress</span>
                </ProcessingAccuracyTooltip>
                <span className="font-medium">
                  {isNaN(categorizationPercentage)
                    ? '0.0'
                    : categorizationPercentage.toFixed(1)}
                  %
                </span>
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-brand-primary h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${isNaN(categorizationPercentage) ? 0 : categorizationPercentage}%`,
                  }}
                />
              </div>

              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center component-gap-xs">
                  <span className="h-3 w-3 text-brand-primary text-sm font-bold">✓</span>
                  <span className="text-gray-600">
                    {formatCompactNumber(metrics.categorizedTransactions)} done
                  </span>
                </div>

                {metrics.uncategorizedTransactions > 0 && (
                  <div className="flex items-center component-gap-xs">
                    <span className="h-3 w-3 text-[#F4A460] text-sm font-bold">⚠</span>
                    <span className="text-gray-600">
                      {formatCompactNumber(metrics.uncategorizedTransactions)}{' '}
                      pending
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardMetricsGrid;
