/**
 * RecentTransactionsList Component Tests - Performance Optimized Component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { RecentTransactionsList } from '../RecentTransactionsList';
import type { RecentTransaction } from '../../types/dashboard';

// Mock transactions for testing
const mockTransactions: RecentTransaction[] = [
  {
    id: '1',
    description: 'Coffee Shop Payment',
    amount: 12.50,
    date: '2024-01-15T10:30:00Z',
    category: 'Food & Beverage',
    transaction_type: 'expense',
    status: 'categorized',
  },
  {
    id: '2',
    description: 'Client Payment Received',
    amount: 2500.00,
    date: '2024-01-14T14:20:00Z',
    category: 'Revenue',
    transaction_type: 'income',
    status: 'categorized',
  },
  {
    id: '3',
    description: 'Office Supplies',
    amount: 89.99,
    date: '2024-01-13T09:15:00Z',
    category: 'Office Expenses',
    transaction_type: 'expense',
    status: 'ai_suggested',
  },
  {
    id: '4',
    description: 'Unknown Transaction',
    amount: 45.00,
    date: '2024-01-12T16:45:00Z',
    category: null,
    transaction_type: 'expense',
    status: 'needs_review',
  },
];

const mockOnViewAll = vi.fn();

const renderRecentTransactionsList = (transactions: RecentTransaction[] = mockTransactions) => {
  return render(
    <RecentTransactionsList
      transactions={transactions}
      onViewAll={mockOnViewAll}
    />
  );
};

describe('RecentTransactionsList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Empty State', () => {
    it('renders empty state when no transactions', () => {
      renderRecentTransactionsList([]);
      
      expect(screen.getByText('No Recent Transactions')).toBeInTheDocument();
      expect(screen.getByText(/upload some financial data/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /upload data/i })).toBeInTheDocument();
    });

    it('has working upload link in empty state', async () => {
      const user = userEvent.setup();
      
      // Mock window.location
      const originalLocation = window.location;
      delete (window as any).location;
      window.location = { ...originalLocation, href: '' };
      
      renderRecentTransactionsList([]);
      
      const uploadButton = screen.getByRole('button', { name: /upload data/i });
      await user.click(uploadButton);
      
      expect(window.location.href).toBe('/upload');
      
      // Restore original location
      window.location = originalLocation;
    });
  });

  describe('Transactions Display', () => {
    it('renders transaction list with correct header', () => {
      renderRecentTransactionsList();
      
      expect(screen.getByText('⊞ Recent Transactions')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /view all/i })).toBeInTheDocument();
    });

    it('displays all transactions', () => {
      renderRecentTransactionsList();
      
      expect(screen.getByText('Coffee Shop Payment')).toBeInTheDocument();
      expect(screen.getByText('Client Payment Received')).toBeInTheDocument();
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
      expect(screen.getByText('Unknown Transaction')).toBeInTheDocument();
    });

    it('shows transaction count in footer', () => {
      renderRecentTransactionsList();
      
      expect(screen.getByText('Showing 4 recent transactions')).toBeInTheDocument();
    });

    it('formats currency amounts correctly', () => {
      renderRecentTransactionsList();
      
      // Check for formatted currency (with rupee symbol and proper formatting)
      expect(screen.getByText(/₹13/)).toBeInTheDocument(); // 12.50 rounded
      expect(screen.getByText(/₹2,500/)).toBeInTheDocument();
      expect(screen.getByText(/₹90/)).toBeInTheDocument(); // 89.99 rounded
      expect(screen.getByText(/₹45/)).toBeInTheDocument();
    });

    it('shows correct transaction type indicators', () => {
      renderRecentTransactionsList();
      
      // Income should show + and TrendingUp icon styling
      const incomeAmounts = screen.getByText(/\+₹2,500/);
      expect(incomeAmounts).toBeInTheDocument();
      
      // Expenses should show - and TrendingDown icon styling
      const expenseAmounts = screen.getAllByText(/-₹/);
      expect(expenseAmounts).toHaveLength(3); // 3 expense transactions
    });
  });

  describe('Date Formatting', () => {
    it('formats recent dates correctly', () => {
      // Create transactions with specific dates for testing
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const threeDaysAgo = new Date(today);
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
      
      const recentTransactions: RecentTransaction[] = [
        {
          ...mockTransactions[0],
          date: today.toISOString(),
        },
        {
          ...mockTransactions[1],
          date: yesterday.toISOString(),
        },
        {
          ...mockTransactions[2],
          date: threeDaysAgo.toISOString(),
        },
      ];
      
      renderRecentTransactionsList(recentTransactions);
      
      expect(screen.getByText('Today')).toBeInTheDocument();
      expect(screen.getByText('Yesterday')).toBeInTheDocument();
      expect(screen.getByText('3 days ago')).toBeInTheDocument();
    });
  });

  describe('Status Badges', () => {
    it('displays correct status badges', () => {
      renderRecentTransactionsList();
      
      expect(screen.getByText('Categorized')).toBeInTheDocument();
      expect(screen.getByText('AI Suggested')).toBeInTheDocument();
      expect(screen.getByText('Needs Review')).toBeInTheDocument();
    });

    it('shows appropriate icons for each status', () => {
      renderRecentTransactionsList();
      
      // Should have CheckCircle, Brain, and AlertCircle icons
      const statusBadges = screen.getAllByRole('generic');
      const badgesWithIcons = statusBadges.filter(badge => 
        badge.textContent?.includes('Categorized') ||
        badge.textContent?.includes('AI Suggested') ||
        badge.textContent?.includes('Needs Review')
      );
      
      expect(badgesWithIcons.length).toBeGreaterThan(0);
    });
  });

  describe('Categories Display', () => {
    it('shows categories when available', () => {
      renderRecentTransactionsList();
      
      expect(screen.getByText('Food & Beverage')).toBeInTheDocument();
      expect(screen.getByText('Revenue')).toBeInTheDocument();
      expect(screen.getByText('Office Expenses')).toBeInTheDocument();
    });

    it('handles missing categories gracefully', () => {
      renderRecentTransactionsList();
      
      // Transaction with null category should still render without error
      expect(screen.getByText('Unknown Transaction')).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('calls onViewAll when header View All button is clicked', async () => {
      const user = userEvent.setup();
      renderRecentTransactionsList();
      
      const headerViewAllButton = screen.getAllByRole('button', { name: /view all/i })[0];
      await user.click(headerViewAllButton);
      
      expect(mockOnViewAll).toHaveBeenCalledTimes(1);
    });

    it('calls onViewAll when footer View All button is clicked', async () => {
      const user = userEvent.setup();
      renderRecentTransactionsList();
      
      const footerViewAllButton = screen.getByRole('button', { name: /view all transactions/i });
      await user.click(footerViewAllButton);
      
      expect(mockOnViewAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('Performance Optimizations', () => {
    it('memoizes transaction items properly', () => {
      const { rerender } = renderRecentTransactionsList();
      
      // Get initial transaction count
      const initialTransactionCount = screen.getAllByText(/₹/).length;
      
      // Re-render with same props
      rerender(
        <RecentTransactionsList
          transactions={mockTransactions}
          onViewAll={mockOnViewAll}
        />
      );
      
      // Should still show same number of transactions
      const rerenderedTransactionCount = screen.getAllByText(/₹/).length;
      expect(rerenderedTransactionCount).toBe(initialTransactionCount);
    });

    it('handles callback optimization correctly', () => {
      renderRecentTransactionsList();
      
      // Verify onViewAll is a function
      expect(typeof mockOnViewAll).toBe('function');
    });

    it('memoizes format functions', () => {
      const { rerender } = renderRecentTransactionsList();
      
      // Component should render without issues on re-render (memoized functions work)
      rerender(
        <RecentTransactionsList
          transactions={mockTransactions}
          onViewAll={mockOnViewAll}
        />
      );
      
      expect(screen.getByText('Coffee Shop Payment')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      renderRecentTransactionsList();
      
      const heading = screen.getByText('⊞ Recent Transactions');
      expect(heading).toBeInTheDocument();
    });

    it('has accessible buttons', () => {
      renderRecentTransactionsList();
      
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeInTheDocument();
        expect(button).not.toHaveAttribute('aria-label', '');
      });
    });

    it('has proper color contrast for transaction types', () => {
      renderRecentTransactionsList();
      
      // Income transactions should have success styling
      const incomeElement = screen.getByText(/\+₹2,500/);
      expect(incomeElement).toHaveClass('text-success');
      
      // Expense transactions should have destructive styling
      const expenseElements = screen.getAllByText(/-₹/);
      expenseElements.forEach(element => {
        expect(element).toHaveClass('text-destructive');
      });
    });
  });

  describe('Edge Cases', () => {
    it('handles extremely large amounts', () => {
      const largeAmountTransaction: RecentTransaction[] = [{
        ...mockTransactions[0],
        amount: 999999.99,
      }];
      
      renderRecentTransactionsList(largeAmountTransaction);
      
      // Should format large numbers correctly
      expect(screen.getByText(/₹10,00,000/)).toBeInTheDocument(); // Indian formatting
    });

    it('handles zero amounts', () => {
      const zeroAmountTransaction: RecentTransaction[] = [{
        ...mockTransactions[0],
        amount: 0,
      }];
      
      renderRecentTransactionsList(zeroAmountTransaction);
      
      expect(screen.getByText(/₹0/)).toBeInTheDocument();
    });

    it('handles very old dates', () => {
      const oldDate = new Date('2020-01-01T10:30:00Z');
      const oldTransaction: RecentTransaction[] = [{
        ...mockTransactions[0],
        date: oldDate.toISOString(),
      }];
      
      renderRecentTransactionsList(oldTransaction);
      
      // Should show formatted date for old transactions
      expect(screen.getByText(/Jan/)).toBeInTheDocument();
    });

    it('handles malformed dates gracefully', () => {
      const malformedDateTransaction: RecentTransaction[] = [{
        ...mockTransactions[0],
        date: 'invalid-date',
      }];
      
      // Should not crash with invalid date
      expect(() => {
        renderRecentTransactionsList(malformedDateTransaction);
      }).not.toThrow();
    });
  });
});