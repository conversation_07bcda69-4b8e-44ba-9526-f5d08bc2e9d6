/**
 * DashboardMetricsGrid Component Tests - Critical Dashboard Metrics Display
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { DashboardMetricsGrid } from '../DashboardMetricsGrid';
import type { DashboardMetrics } from '../../types/dashboard';

// Mock dashboard metrics for testing
const mockProfitableMetrics: DashboardMetrics = {
  totalIncome: 150000.75,
  totalExpenses: 85000.25,
  netIncome: 65000.50,
  totalTransactions: 1250,
  categorizedTransactions: 1125,
  uncategorizedTransactions: 125,
  avgTransactionAmount: 120.00,
  topCategory: 'Professional Services',
  topCategoryAmount: 25000.00,
};

const mockLossMetrics: DashboardMetrics = {
  totalIncome: 50000.00,
  totalExpenses: 75000.00,
  netIncome: -25000.00,
  totalTransactions: 800,
  categorizedTransactions: 600,
  uncategorizedTransactions: 200,
  avgTransactionAmount: 93.75,
  topCategory: 'Office Expenses',
  topCategoryAmount: 15000.00,
};

const mockBreakEvenMetrics: DashboardMetrics = {
  totalIncome: 100000.00,
  totalExpenses: 100000.00,
  netIncome: 0.00,
  totalTransactions: 500,
  categorizedTransactions: 500,
  uncategorizedTransactions: 0,
  avgTransactionAmount: 200.00,
  topCategory: 'Revenue',
  topCategoryAmount: 100000.00,
};

const mockEmptyMetrics: DashboardMetrics = {
  totalIncome: 0,
  totalExpenses: 0,
  netIncome: 0,
  totalTransactions: 0,
  categorizedTransactions: 0,
  uncategorizedTransactions: 0,
  avgTransactionAmount: 0,
  topCategory: '',
  topCategoryAmount: 0,
};

describe('DashboardMetricsGrid', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering Structure', () => {
    it('renders all four metric cards', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      expect(screen.getByText('Total Income')).toBeInTheDocument();
      expect(screen.getByText('Total Expenses')).toBeInTheDocument();
      expect(screen.getByText('Net Income')).toBeInTheDocument();
      expect(screen.getByText('AI Processing')).toBeInTheDocument();
    });

    it('renders grid layout with responsive classes', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      const grid = container.querySelector('.grid');
      expect(grid).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-4', 'gap-6');
    });

    it('displays card titles and icons correctly', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Check for card titles
      expect(screen.getByText('Total Income')).toBeInTheDocument();
      expect(screen.getByText('Total Expenses')).toBeInTheDocument();
      expect(screen.getByText('Net Income')).toBeInTheDocument();
      expect(screen.getByText('AI Processing')).toBeInTheDocument();
    });
  });

  describe('Profitable Scenario', () => {
    it('displays positive net income correctly', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Should show profit badge
      expect(screen.getByText('Profit')).toBeInTheDocument();
      
      // Should display absolute net income value (not compacted since under 100K)
      expect(screen.getByText('$65,000.50')).toBeInTheDocument();
      
      // Should show profit text
      expect(screen.getByText('Profit this period')).toBeInTheDocument();
    });

    it('applies correct styling for profit scenario', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Net income card should have success border
      const netIncomeCard = screen.getByText('Net Income').closest('.border-brand-primary\\/20');
      expect(netIncomeCard).toBeInTheDocument();
      
      // Profit badge should have default variant
      const profitBadge = screen.getByText('Profit');
      expect(profitBadge).toBeInTheDocument();
    });

    it('shows trending up icon for profit', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Net income section should show TrendingUp icon (via React.createElement)
      const netIncomeSection = screen.getByText('Net Income').closest('div');
      expect(netIncomeSection).toBeInTheDocument();
    });
  });

  describe('Loss Scenario', () => {
    it('displays negative net income correctly', () => {
      render(<DashboardMetricsGrid metrics={mockLossMetrics} />);
      
      // Should show loss badge
      expect(screen.getByText('Loss')).toBeInTheDocument();
      
      // Should display absolute net income value (not compacted since under 100K)
      expect(screen.getByText('$25,000.00')).toBeInTheDocument();
      
      // Should show loss text
      expect(screen.getByText('Loss this period')).toBeInTheDocument();
    });

    it('applies correct styling for loss scenario', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockLossMetrics} />);
      
      // Net income card should have destructive border
      const netIncomeCard = screen.getByText('Net Income').closest('.border-\\[\\#DC2626\\]\\/20');
      expect(netIncomeCard).toBeInTheDocument();
      
      // Loss badge should have destructive variant
      const lossBadge = screen.getByText('Loss');
      expect(lossBadge).toBeInTheDocument();
    });
  });

  describe('Break Even Scenario', () => {
    it('displays zero net income correctly', () => {
      render(<DashboardMetricsGrid metrics={mockBreakEvenMetrics} />);
      
      // Should show break even badge
      expect(screen.getByText('Break Even')).toBeInTheDocument();
      
      // Should display zero value (formatted as compact currency)
      expect(screen.getByText('$0.00')).toBeInTheDocument();
      
      // Should show profit text (since netIncome >= 0)
      expect(screen.getByText('Profit this period')).toBeInTheDocument();
    });

    it('applies neutral styling for break even scenario', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockBreakEvenMetrics} />);
      
      // Break even badge should have secondary variant
      const breakEvenBadge = screen.getByText('Break Even');
      expect(breakEvenBadge).toBeInTheDocument();
    });
  });

  describe('Income and Expense Display', () => {
    it('formats and displays total income correctly', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Check formatted income display - should be compact for values >= 100K
      expect(screen.getByText('$150K')).toBeInTheDocument();
      expect(screen.getByText('Revenue generated this period')).toBeInTheDocument();
      expect(screen.getByText('Revenue stream active')).toBeInTheDocument();
    });

    it('formats and displays total expenses correctly', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Check formatted expenses display - values under 100K should show formatted with thousands separators
      expect(screen.getByText('$85,000.25')).toBeInTheDocument();
      expect(screen.getByText('Total amount spent')).toBeInTheDocument();
      expect(screen.getByText('Budget tracking enabled')).toBeInTheDocument();
    });

    it('handles zero income and expenses', () => {
      render(<DashboardMetricsGrid metrics={mockEmptyMetrics} />);
      
      // Should display zero values without crashing
      expect(screen.getByText('Total Income')).toBeInTheDocument();
      expect(screen.getByText('Total Expenses')).toBeInTheDocument();
    });
  });

  describe('Transaction Processing Display', () => {
    it('displays transaction count correctly', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Should show compact number format for transactions
      expect(screen.getByText('1.3K')).toBeInTheDocument(); // 1250 formatted
      expect(screen.getByText('Transactions processed')).toBeInTheDocument();
    });

    it('calculates and displays categorization percentage', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // 1125/1250 = 90%
      expect(screen.getByText('90.0%')).toBeInTheDocument();
      expect(screen.getByText('Categorization Progress')).toBeInTheDocument();
    });

    it('displays categorized transaction count', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      expect(screen.getByText('1.1K done')).toBeInTheDocument(); // 1125 formatted
    });

    it('displays uncategorized transactions when present', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      expect(screen.getByText('125 pending')).toBeInTheDocument();
    });

    it('hides pending transactions when none exist', () => {
      render(<DashboardMetricsGrid metrics={mockBreakEvenMetrics} />);
      
      // Should not show pending when uncategorizedTransactions = 0
      expect(screen.queryByText(/pending/)).not.toBeInTheDocument();
    });

    it('handles zero transactions gracefully', () => {
      render(<DashboardMetricsGrid metrics={mockEmptyMetrics} />);
      
      // Should show 0.0% instead of NaN
      expect(screen.getByText('0.0%')).toBeInTheDocument();
      expect(screen.getByText('0 done')).toBeInTheDocument();
    });
  });

  describe('Progress Bar Display', () => {
    it('sets correct progress bar width for categorization', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Find progress bar element
      const progressBar = container.querySelector('.bg-brand-primary.h-2.rounded-full');
      expect(progressBar).toHaveStyle({ width: '90%' }); // 90% categorized
    });

    it('handles zero progress correctly', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockEmptyMetrics} />);
      
      const progressBar = container.querySelector('.bg-brand-primary.h-2.rounded-full');
      expect(progressBar).toHaveStyle({ width: '0%' });
    });

    it('handles 100% completion correctly', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockBreakEvenMetrics} />);
      
      const progressBar = container.querySelector('.bg-brand-primary.h-2.rounded-full');
      expect(progressBar).toHaveStyle({ width: '100%' }); // 500/500 = 100%
    });
  });

  describe('Status Icons and Colors', () => {
    it('uses correct brand colors for income', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Income section should use brand-primary color
      const incomeSection = screen.getByText('Total Income').closest('.border-brand-primary\\/20');
      expect(incomeSection).toBeInTheDocument();
    });

    it('uses correct colors for expenses', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Expenses section should use sandy brown color
      const expensesSection = screen.getByText('Total Expenses').closest('.border-\\[\\#F4A460\\]\\/20');
      expect(expensesSection).toBeInTheDocument();
    });

    it('displays appropriate icons for each metric', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Check that activity indicators are present
      expect(screen.getByText('Revenue stream active')).toBeInTheDocument();
      expect(screen.getByText('Budget tracking enabled')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('provides accessible card structure', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Cards should have proper semantic structure - using data-slot selector
      const cards = document.querySelectorAll('[data-slot="card"]');
      expect(cards.length).toBe(4); // Should have exactly 4 metric cards
    });

    it('has descriptive text for metrics', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Each metric should have descriptive text
      expect(screen.getByText('Revenue generated this period')).toBeInTheDocument();
      expect(screen.getByText('Total amount spent')).toBeInTheDocument();
      expect(screen.getByText('Transactions processed')).toBeInTheDocument();
    });

    it('provides tooltip for categorization progress', () => {
      render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // ProcessingAccuracyTooltip should be present
      expect(screen.getByText('Categorization Progress')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles very large numbers correctly', () => {
      const largeMetrics: DashboardMetrics = {
        ...mockProfitableMetrics,
        totalIncome: 999999999,
        totalExpenses: 250000000,
        netIncome: 749999999,
        totalTransactions: 1000000,
      };
      
      render(<DashboardMetricsGrid metrics={largeMetrics} />);
      
      // Should format large numbers appropriately with compact notation
      expect(screen.getByText('$1B')).toBeInTheDocument(); // 999M formatted as $1B for income
      expect(screen.getByText('$250M')).toBeInTheDocument(); // 250M for expenses  
      expect(screen.getByText('$750M')).toBeInTheDocument(); // 750M for net income
    });

    it('handles negative transaction counts gracefully', () => {
      const negativeMetrics: DashboardMetrics = {
        ...mockEmptyMetrics,
        totalTransactions: -5,
        categorizedTransactions: -2,
        uncategorizedTransactions: -3,
      };
      
      expect(() => {
        render(<DashboardMetricsGrid metrics={negativeMetrics} />);
      }).not.toThrow();
    });

    it('handles undefined/null values in calculations', () => {
      const invalidMetrics = {
        ...mockEmptyMetrics,
        totalTransactions: undefined as any,
        categorizedTransactions: null as any,
      };
      
      expect(() => {
        render(<DashboardMetricsGrid metrics={invalidMetrics} />);
      }).not.toThrow();
    });
  });

  describe('Performance Considerations', () => {
    it('renders efficiently with minimal DOM operations', () => {
      const { container } = render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Should render 4 cards efficiently
      const cards = container.querySelectorAll('.border-brand-primary\\/20, .border-\\[\\#F4A460\\]\\/20, .border-\\[\\#DC2626\\]\\/20, .border-gray-200, .border-\\[\\#1A3F5F\\]\\/20');
      expect(cards.length).toBeGreaterThan(0);
    });

    it('handles rapid prop changes without errors', () => {
      const { rerender } = render(<DashboardMetricsGrid metrics={mockProfitableMetrics} />);
      
      // Rapidly change metrics
      rerender(<DashboardMetricsGrid metrics={mockLossMetrics} />);
      rerender(<DashboardMetricsGrid metrics={mockBreakEvenMetrics} />);
      rerender(<DashboardMetricsGrid metrics={mockEmptyMetrics} />);
      
      // Should handle changes without crashing
      expect(screen.getByText('AI Processing')).toBeInTheDocument();
    });
  });
});