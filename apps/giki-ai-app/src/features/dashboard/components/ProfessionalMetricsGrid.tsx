/**
 * Professional Metrics Grid - Enhanced Dashboard Component
 *
 * Professional B2B metrics dashboard with real-time data visualization,
 * enhanced analytics, and professional design system integration.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { toast } from '../../../shared/components/ui/use-toast';
import {
  formatCurrencyForMetrics,
} from '../../../shared/utils/formatCurrency';
import { Button } from '../../../shared/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import { Badge } from '../../../shared/components/ui/badge';
import { Progress } from '../../../shared/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../shared/components/ui/select';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../../shared/components/ui/tooltip';
import {
  Target,
  Award,
  Activity,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Minus,
  CheckCircle,
} from 'lucide-react';

// Types
interface MetricDefinition {
  id: string;
  title: string;
  value: number | string;
  previousValue?: number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  unit: 'percentage' | 'number' | 'currency' | 'time';
  format: 'decimal' | 'integer' | 'currency' | 'percentage' | 'duration';
  category: 'accuracy' | 'performance' | 'volume' | 'quality' | 'business';
  priority: 'high' | 'medium' | 'low';
  description: string;
  target?: number;
  threshold: {
    excellent: number;
    good: number;
    warning: number;
  };
  trend: Array<{
    date: string;
    value: number;
  }>;
  drilldownAction?: () => void;
}

interface MetricsConfiguration {
  layout: 'grid' | 'list' | 'cards';
  timeRange: '24h' | '7d' | '30d' | '90d';
  showTrends: boolean;
  showTargets: boolean;
  autoRefresh: boolean;
  refreshInterval: number;
  categories: string[];
  priorities: string[];
}

// Component Props
interface ProfessionalMetricsGridProps {
  className?: string;
  layout?: 'grid' | 'list' | 'cards';
  showConfiguration?: boolean;
  autoRefresh?: boolean;
  onMetricClick?: (metric: MetricDefinition) => void;
}

// Default Metrics
const DEFAULT_METRICS: MetricDefinition[] = [
  {
    id: 'overall_accuracy',
    title: 'Overall Accuracy',
    value: 87.3,
    previousValue: 84.1,
    change: 3.2,
    changeType: 'increase',
    unit: 'percentage',
    format: 'percentage',
    category: 'accuracy',
    priority: 'high',
    description: 'Current categorization accuracy across all transactions',
    target: 85,
    threshold: { excellent: 90, good: 85, warning: 80 },
    trend: [
      { date: '2024-01-01', value: 82.1 },
      { date: '2024-02-01', value: 83.5 },
      { date: '2024-03-01', value: 84.8 },
      { date: '2024-04-01', value: 86.2 },
      { date: '2024-05-01', value: 87.3 },
    ],
  },
  {
    id: 'processing_speed',
    title: 'Processing Speed',
    value: 1.2,
    previousValue: 1.8,
    change: -0.6,
    changeType: 'decrease',
    unit: 'time',
    format: 'decimal',
    category: 'performance',
    priority: 'high',
    description: 'Average time to categorize a transaction (seconds)',
    target: 2.0,
    threshold: { excellent: 1.0, good: 1.5, warning: 2.0 },
    trend: [
      { date: '2024-01-01', value: 2.1 },
      { date: '2024-02-01', value: 1.9 },
      { date: '2024-03-01', value: 1.6 },
      { date: '2024-04-01', value: 1.4 },
      { date: '2024-05-01', value: 1.2 },
    ],
  },
  {
    id: 'transactions_processed',
    title: 'Transactions Processed',
    value: 12847,
    previousValue: 11203,
    change: 14.7,
    changeType: 'increase',
    unit: 'number',
    format: 'integer',
    category: 'volume',
    priority: 'medium',
    description: 'Total transactions processed this period',
    threshold: { excellent: 15000, good: 10000, warning: 5000 },
    trend: [
      { date: '2024-01-01', value: 8432 },
      { date: '2024-02-01', value: 9678 },
      { date: '2024-03-01', value: 10845 },
      { date: '2024-04-01', value: 11203 },
      { date: '2024-05-01', value: 12847 },
    ],
  },
  {
    id: 'gl_code_compliance',
    title: 'GL Code Compliance',
    value: 94.2,
    previousValue: 91.8,
    change: 2.4,
    changeType: 'increase',
    unit: 'percentage',
    format: 'percentage',
    category: 'quality',
    priority: 'high',
    description: 'Percentage of transactions with valid GL codes',
    target: 95,
    threshold: { excellent: 95, good: 90, warning: 85 },
    trend: [
      { date: '2024-01-01', value: 88.5 },
      { date: '2024-02-01', value: 89.7 },
      { date: '2024-03-01', value: 91.2 },
      { date: '2024-04-01', value: 91.8 },
      { date: '2024-05-01', value: 94.2 },
    ],
  },
  {
    id: 'confidence_score',
    title: 'AI Confidence',
    value: 89.7,
    previousValue: 87.3,
    change: 2.4,
    changeType: 'increase',
    unit: 'percentage',
    format: 'percentage',
    category: 'quality',
    priority: 'medium',
    description: 'Average AI confidence in categorization decisions',
    threshold: { excellent: 90, good: 85, warning: 80 },
    trend: [
      { date: '2024-01-01', value: 84.2 },
      { date: '2024-02-01', value: 85.8 },
      { date: '2024-03-01', value: 86.9 },
      { date: '2024-04-01', value: 87.3 },
      { date: '2024-05-01', value: 89.7 },
    ],
  },
  {
    id: 'error_rate',
    title: 'Error Rate',
    value: 2.1,
    previousValue: 3.4,
    change: -1.3,
    changeType: 'decrease',
    unit: 'percentage',
    format: 'percentage',
    category: 'quality',
    priority: 'high',
    description: 'Percentage of transactions requiring manual correction',
    threshold: { excellent: 2, good: 5, warning: 10 },
    trend: [
      { date: '2024-01-01', value: 5.2 },
      { date: '2024-02-01', value: 4.6 },
      { date: '2024-03-01', value: 3.8 },
      { date: '2024-04-01', value: 3.4 },
      { date: '2024-05-01', value: 2.1 },
    ],
  },
  {
    id: 'file_uploads',
    title: 'File Uploads',
    value: 156,
    previousValue: 142,
    change: 9.9,
    changeType: 'increase',
    unit: 'number',
    format: 'integer',
    category: 'volume',
    priority: 'low',
    description: 'Number of files uploaded this period',
    threshold: { excellent: 200, good: 150, warning: 100 },
    trend: [
      { date: '2024-01-01', value: 123 },
      { date: '2024-02-01', value: 134 },
      { date: '2024-03-01', value: 145 },
      { date: '2024-04-01', value: 142 },
      { date: '2024-05-01', value: 156 },
    ],
  },
  {
    id: 'milestone_progress',
    title: 'M2 Milestone',
    value: 'In Progress',
    change: 0,
    changeType: 'neutral',
    unit: 'percentage',
    format: 'percentage',
    category: 'business',
    priority: 'high',
    description: 'Current milestone completion status',
    threshold: { excellent: 100, good: 75, warning: 50 },
    trend: [
      { date: '2024-01-01', value: 25 },
      { date: '2024-02-01', value: 40 },
      { date: '2024-03-01', value: 55 },
      { date: '2024-04-01', value: 70 },
      { date: '2024-05-01', value: 85 },
    ],
  },
];

export const ProfessionalMetricsGrid: React.FC<
  ProfessionalMetricsGridProps
> = React.memo(({
  className = '',
  layout: initialLayout = 'grid',
  showConfiguration = true,
  autoRefresh = true,
  onMetricClick,
}) => {
  // State
  const [metrics, _setMetrics] = useState<MetricDefinition[]>(DEFAULT_METRICS);
  const [config, setConfig] = useState<MetricsConfiguration>({
    layout: initialLayout,
    timeRange: '30d',
    showTrends: true,
    showTargets: true,
    autoRefresh,
    refreshInterval: 30000,
    categories: ['accuracy', 'performance', 'quality', 'business'],
    priorities: ['high', 'medium'],
  });
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Load metrics data
  const loadMetrics = useCallback(async () => {
    try {
      setLoading(true);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // In real implementation, would call:
      // const response = await dashboardService.getMetrics(config);
      // setMetrics(response.metrics);

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load metrics:', error);
      toast({
        title: 'Error',
        description: 'Failed to load dashboard metrics.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [config]);

  // Auto-refresh effect
  useEffect(() => {
    void loadMetrics();

    if (config.autoRefresh) {
      const interval = setInterval(
        () => void loadMetrics(),
        config.refreshInterval,
      );
      return () => clearInterval(interval);
    }
  }, [loadMetrics, config.autoRefresh, config.refreshInterval]);

  // Filter metrics
  const filteredMetrics = useMemo(() => {
    return metrics.filter(
      (metric) =>
        config.categories.includes(metric.category) &&
        config.priorities.includes(metric.priority),
    );
  }, [metrics, config]);

  // Format value
  const formatValue = useCallback((metric: MetricDefinition): string => {
    const value = metric.value;

    switch (metric.format) {
      case 'percentage':
        return `${typeof value === 'number' ? value.toFixed(1) : value}%`;
      case 'currency':
        return formatCurrencyForMetrics(typeof value === 'number' ? value : 0);
      case 'integer':
        return typeof value === 'number'
          ? new Intl.NumberFormat('en-US').format(Math.round(value))
          : String(value);
      case 'decimal':
        return typeof value === 'number' ? value.toFixed(2) : String(value);
      case 'duration':
        return typeof value === 'number'
          ? `${value.toFixed(1)}s`
          : String(value);
      default:
        return String(value);
    }
  }, []);

  // Get status color
  const getStatusColor = useCallback((metric: MetricDefinition): string => {
    if (typeof metric.value !== 'number') return 'text-gray-600';

    const value = metric.value;
    const { excellent, good, warning } = metric.threshold;

    if (metric.changeType === 'decrease' && metric.id === 'error_rate') {
      // For error rate, lower is better
      if (value <= excellent) return 'text-success';
      if (value <= good) return 'text-blue-600';
      if (value <= warning) return 'text-yellow-600';
      return 'text-error';
    } else {
      // For most metrics, higher is better
      if (value >= excellent) return 'text-success';
      if (value >= good) return 'text-blue-600';
      if (value >= warning) return 'text-yellow-600';
      return 'text-error';
    }
  }, []);

  // Get trend icon
  const getTrendIcon = useCallback((metric: MetricDefinition) => {
    const isGoodChange =
      metric.id === 'error_rate'
        ? metric.changeType === 'decrease'
        : metric.changeType === 'increase';

    if (metric.changeType === 'neutral') {
      return <Minus className="w-4 h-4 text-gray-500" />;
    }

    const iconClass = isGoodChange ? 'text-green-500' : 'text-red-500';

    return metric.changeType === 'increase' ? (
      <ArrowUp className={`w-4 h-4 ${iconClass}`} />
    ) : (
      <ArrowDown className={`w-4 h-4 ${iconClass}`} />
    );
  }, []);

  // Get progress percentage for target metrics
  const getProgressPercentage = useCallback(
    (metric: MetricDefinition): number => {
      if (!metric.target || typeof metric.value !== 'number') return 0;
      return Math.min((metric.value / metric.target) * 100, 100);
    },
    [],
  );

  // Render metric card
  const renderMetricCard = useCallback(
    (metric: MetricDefinition) => {
      const statusColor = getStatusColor(metric);
      const trendIcon = getTrendIcon(metric);
      const progressPercentage = getProgressPercentage(metric);

      return (
        <Card
          key={metric.id}
          className="card-elevated card-interactive"
          onClick={() => onMetricClick?.(metric)}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600">
                {metric.title}
              </CardTitle>
              <div className="flex items-center gap-1">
                <Badge
                  variant="outline"
                  className={`text-xs ${
                    metric.priority === 'high'
                      ? 'border-red-200 text-error'
                      : metric.priority === 'medium'
                        ? 'border-yellow-200 text-yellow-600'
                        : 'border-gray-200 text-gray-600'
                  }`}
                >
                  {metric.priority}
                </Badge>
                {config.showTrends && trendIcon}
              </div>
            </div>
          </CardHeader>

          <CardContent>
            <div className="space-y-3">
              {/* Main Value */}
              <div className="flex items-center justify-between">
                <div
                  className={`text-2xl metric-number-professional ${statusColor}`}
                >
                  {formatValue(metric)}
                </div>

                {typeof metric.change === 'number' && metric.change !== 0 && (
                  <div className="text-right">
                    <div
                      className={`text-sm font-medium ${
                        metric.changeType === 'increase'
                          ? 'text-success'
                          : metric.changeType === 'decrease'
                            ? 'text-error'
                            : 'text-gray-600'
                      }`}
                    >
                      {metric.changeType === 'increase' ? '+' : ''}
                      {metric.change.toFixed(1)}
                      {metric.unit === 'percentage' ? '%' : ''}
                    </div>
                    <div className="text-xs text-gray-500">vs previous</div>
                  </div>
                )}
              </div>

              {/* Progress Bar for Target Metrics */}
              {config.showTargets &&
                metric.target &&
                typeof metric.value === 'number' && (
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">
                        Target:{' '}
                        {formatValue({ ...metric, value: metric.target })}
                      </span>
                      <span className="text-xs font-medium">
                        {progressPercentage.toFixed(0)}%
                      </span>
                    </div>
                    <Progress value={progressPercentage} className="h-2" />
                  </div>
                )}

              {/* Description */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <p className="text-xs text-gray-600 truncate">
                      {metric.description}
                    </p>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">{metric.description}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </CardContent>
        </Card>
      );
    },
    [
      config,
      getStatusColor,
      getTrendIcon,
      getProgressPercentage,
      formatValue,
      onMetricClick,
    ],
  );

  // Render layout
  const renderLayout = useCallback(() => {
    const gridClass =
      config.layout === 'grid'
        ? 'responsive-grid-4'
        : config.layout === 'cards'
          ? 'grid grid-cols-1 lg:grid-cols-2 gap-6'
          : 'space-y-4';

    return (
      <div className={gridClass}>{filteredMetrics.map(renderMetricCard)}</div>
    );
  }, [config.layout, filteredMetrics, renderMetricCard]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Performance Metrics
          </h3>
          <p className="text-sm text-gray-600">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>

        <div className="flex items-center gap-3">
          {showConfiguration && (
            <>
              <Select
                value={config.timeRange}
                onValueChange={(value: string) =>
                  setConfig((prev) => ({
                    ...prev,
                    timeRange: value as '24h' | '7d' | '30d' | '90d',
                  }))
                }
              >
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="24h">24h</SelectItem>
                  <SelectItem value="7d">7d</SelectItem>
                  <SelectItem value="30d">30d</SelectItem>
                  <SelectItem value="90d">90d</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={config.layout}
                onValueChange={(value: string) =>
                  setConfig((prev) => ({
                    ...prev,
                    layout: value as 'grid' | 'cards' | 'list',
                  }))
                }
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="grid">Grid</SelectItem>
                  <SelectItem value="cards">Cards</SelectItem>
                  <SelectItem value="list">List</SelectItem>
                </SelectContent>
              </Select>
            </>
          )}

          <Button
            variant="outline"
            onClick={() => void loadMetrics()}
            disabled={loading}
            className="btn-professional"
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="responsive-grid-4">
        <Card className="card-elevated border-l-4 border-l-brand-primary">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="w-5 h-5" style={{ color: 'var(--giki-primary)' }} />
              <div>
                <p className="text-sm text-gray-600">Accuracy Target</p>
                <p className="text-lg font-bold text-gray-900">85%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="card-elevated border-l-4 border-l-success">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Metrics Healthy</p>
                <p className="text-lg font-bold text-gray-900">
                  {
                    filteredMetrics.filter((m) =>
                      getStatusColor(m).includes('green'),
                    ).length
                  }
                  /{filteredMetrics.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="card-elevated border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Active Monitoring</p>
                <p className="text-lg font-bold text-gray-900">
                  {config.autoRefresh ? 'ON' : 'OFF'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="card-elevated border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Award className="w-5 h-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">M2 Progress</p>
                <p className="text-lg font-bold text-gray-900">85%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Metrics Grid */}
      {loading ? (
        <div className="responsive-grid-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <Card key={index} className="loading-shimmer card-elevated">
              <CardHeader className="pb-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-2 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        renderLayout()
      )}
    </div>
  );
});

ProfessionalMetricsGrid.displayName = 'ProfessionalMetricsGrid';

export default ProfessionalMetricsGrid;
