import React from 'react';
import { Card, CardContent, CardHeader } from '@/shared/components/ui/card';
import { Skeleton } from '@/shared/components/ui/skeleton';

export const DashboardMetricsSkeleton: React.FC = () => {
  return (
    <div
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      role="status"
      aria-label="Loading dashboard metrics"
    >
      {/* Render 4 skeleton metric cards */}
      {Array.from({ length: 4 }).map((_, index) => (
        <Card key={index} className="relative overflow-hidden">
          <CardHeader className="flex flex-wrap flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-8 w-8 rounded-lg" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-32 mb-2" />
            <Skeleton className="h-3 w-20" />

            {/* Transaction card gets extra skeleton elements */}
            {index === 3 && (
              <div className="mt-3 space-y-2">
                <Skeleton className="h-3 w-28" />
                <Skeleton className="h-2 w-full rounded-full" />
                <Skeleton className="h-3 w-20" />
              </div>
            )}
          </CardContent>
          <Skeleton className="absolute bottom-0 left-0 right-0 h-1" />
        </Card>
      ))}
      <span className="sr-only">Loading dashboard metrics, please wait...</span>
    </div>
  );
};

export default DashboardMetricsSkeleton;
