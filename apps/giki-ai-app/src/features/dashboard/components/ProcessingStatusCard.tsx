import React from 'react';
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import {
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  Loader2,
  Activity,
} from 'lucide-react';
import type { ProcessingStatus } from '../types/dashboard';
import { cn } from '@/shared/utils/utils';

interface ProcessingStatusCardProps {
  status: ProcessingStatus;
}

export const ProcessingStatusCard: React.FC<ProcessingStatusCardProps> = ({
  status,
}) => {
  const {
    totalFiles,
    processedFiles,
    pendingFiles,
    failedFiles,
    lastProcessed,
    isProcessing,
  } = status;

  const formatLastProcessed = (timestamp?: string) => {
    if (!timestamp) return 'Never';

    const date = new Date(timestamp);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();

    // Check for future dates or invalid time differences
    if (diffMs < 0) {
      return 'Recently';
    }

    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 365) return `${diffDays}d ago`;

    // For very old dates, show the actual date
    return date.toLocaleDateString();
  };

  const getProcessingProgress = () => {
    if (totalFiles === 0) return 0;
    return (processedFiles / totalFiles) * 100;
  };

  const getStatusMessage = () => {
    if (isProcessing) return 'Processing files...';
    if (totalFiles === 0) return 'No files uploaded yet';
    if (failedFiles > 0) return `${failedFiles} files failed processing`;
    if (pendingFiles > 0) return `${pendingFiles} files waiting to process`;
    if (processedFiles === totalFiles)
      return 'All files processed successfully';
    return 'Processing complete';
  };

  const getStatusColor = () => {
    if (isProcessing) return 'text-success';
    if (failedFiles > 0) return 'text-error';
    if (pendingFiles > 0) return 'text-amber-600';
    if (processedFiles === totalFiles && totalFiles > 0)
      return 'text-success';
    return 'text-muted-foreground';
  };

  const getStatusIcon = () => {
    if (isProcessing)
      return <Loader2 className="w-4 h-4 animate-spin text-info" />;
    if (failedFiles > 0)
      return <AlertCircle className="w-4 h-4 text-destructive" />;
    if (pendingFiles > 0) return <Clock className="w-4 h-4 text-amber-600" />;
    if (processedFiles === totalFiles && totalFiles > 0)
      return <CheckCircle className="w-4 h-4 text-success" />;
    return <Activity className="w-4 h-4 text-muted-foreground" />;
  };

  // Don't show the card if there's no relevant processing activity
  if (totalFiles === 0 && !isProcessing) {
    return null;
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex flex-wrap items-center justify-between">
          <CardTitle className="text-card-foreground-title flex flex-wrap items-center space-x-2">
            <FileText className="w-5 h-5" />
            <span>Processing Status</span>
          </CardTitle>

          <div className="flex flex-wrap items-center space-x-2">
            {getStatusIcon()}
            <Badge
              variant={
                isProcessing
                  ? 'default'
                  : failedFiles > 0
                    ? 'destructive'
                    : 'secondary'
              }
              className={cn(
                isProcessing && 'animate-pulse',
                'max-w-[150px] truncate',
              )}
            >
              {isProcessing
                ? 'Processing'
                : failedFiles > 0
                  ? 'Issues'
                  : 'Ready'}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 overflow-hidden">
        {/* Status Message */}
        <div className="flex flex-wrap items-center space-x-2">
          <span className={cn('text-sm font-medium', getStatusColor())}>
            {getStatusMessage()}
          </span>
        </div>

        {/* Progress Bar */}
        {totalFiles > 0 && (
          <div className="space-y-2">
            <div className="flex flex-wrap items-center justify-between truncate text-caption text-muted-foreground">
              <span>Progress</span>
              <span>
                {processedFiles}/{totalFiles} files
              </span>
            </div>
            <Progress value={getProcessingProgress()} className="h-2" />
          </div>
        )}

        {/* Status Grid */}
        {totalFiles > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2">
            <div className="text-center space-y-1">
              <div className="flex flex-wrap items-center justify-center">
                <CheckCircle className="w-4 h-4 text-success mr-1" />
                <span className="text-sm font-semibold text-success">
                  {processedFiles}
                </span>
              </div>
              <p className="truncate text-caption text-muted-foreground">
                Processed
              </p>
            </div>

            <div className="text-center space-y-1">
              <div className="flex flex-wrap items-center justify-center">
                <Clock className="w-4 h-4 text-amber-600 mr-1" />
                <span className="text-sm font-semibold text-amber-600">
                  {pendingFiles}
                </span>
              </div>
              <p className="truncate text-caption text-muted-foreground">
                Pending
              </p>
            </div>

            <div className="text-center space-y-1">
              <div className="flex flex-wrap items-center justify-center">
                <AlertCircle className="w-4 h-4 text-destructive mr-1" />
                <span className="text-sm font-semibold text-destructive">
                  {failedFiles}
                </span>
              </div>
              <p className="truncate text-caption text-muted-foreground">
                Failed
              </p>
            </div>
          </div>
        )}

        {/* Last Processed */}
        {lastProcessed && (
          <div className="pt-2 border-t border-border">
            <div className="flex flex-wrap items-center justify-between truncate text-caption text-muted-foreground">
              <span>Last processed:</span>
              <span>{formatLastProcessed(lastProcessed)}</span>
            </div>
          </div>
        )}

        {/* Processing Tips */}
        {totalFiles === 0 && !isProcessing && (
          <div className="text-center py-4">
            <p className="text-xs text-muted-foreground mb-2">
              Upload financial data to start processing
            </p>
            <button
              onClick={() => (window.location.href = '/upload')}
              className="text-sm text-primary hover:text-primary/80 font-medium"
            >
              Upload Files →
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProcessingStatusCard;
