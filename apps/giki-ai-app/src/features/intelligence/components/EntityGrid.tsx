import React, { useMemo, useCallback } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shared/components/ui/avatar';
import type { Entity } from '@/shared/types/knowledgehub'; // Corrected import

interface EntityGridProps {
  entities: Entity[];
  onEntitySelect: (entity: Entity) => void;
  formatCurrency: (amount: number) => string;
}

export const EntityGrid: React.FC<EntityGridProps> = ({
  entities,
  onEntitySelect,
  formatCurrency,
}) => {
  // Memoize entity click handler to prevent unnecessary re-renders
  const handleEntityClick = useCallback((entity: Entity) => {
    onEntitySelect(entity);
  }, [onEntitySelect]);

  // Memoize the entity cards to prevent re-rendering when props haven't changed
  const entityCards = useMemo(() => {
    return entities.map((entity) => (
      // Applied professional design system: consistent card styling with brand colors
      <Card
        key={entity.entity}
        className="overflow-hidden cursor-pointer border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200"
        onClick={() => handleEntityClick(entity)}
      >
          <CardHeader className="card-padding-sm">
            <div className="flex flex-wrap items-center component-gap-sm">
              {' '}
              {/* Increased gap */}
              <Avatar className="h-10 w-10">
                {' '}
                {/* Slightly larger Avatar */}
                <AvatarImage src={entity.logo} alt={entity.entity} />
                <AvatarFallback>
                  {entity?.entity?.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {/* Professional design system: consistent heading styling */}
              <CardTitle className="text-base font-medium text-brand-primary">
                {entity.entity}
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="pb-3 px-4 overflow-hidden">
            {' '}
            {/* Adjusted padding */}
            <div className="space-y-2">
              <div className="flex flex-wrap justify-between items-center">
                {/* Secondary Text: 15px, Regular (400), Steel Grey. text-sm is 14px. */}
                <span className="text-sm text-secondary">Total Spending</span>
                <span className="font-semibold text-brand-primary">
                  {formatCurrency(entity.spending)}
                </span>{' '}
                {/* Made amount bolder */}
              </div>
              <div className="flex flex-wrap justify-between items-center">
                <span className="text-sm text-secondary">Category</span>
                <Badge
                  variant="outline"
                  className="text-secondary border-secondary/40 max-w-[150px] truncate"
                >
                  {entity.category}
                </Badge>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-3 pb-4 px-4">
            {' '}
            {/* Adjusted padding */}
            {/* Professional design system: consistent button styling with brand colors */}
            <Button
              className="max-w-full w-full border-brand-primary text-brand-primary hover:bg-brand-primary/10"
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click from also firing
                handleEntityClick(entity);
              }}
            >
              View Details
            </Button>
          </CardFooter>
        </Card>
      ));
  }, [entities, formatCurrency, handleEntityClick]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
      {entityCards}
    </div>
  );
};

export default EntityGrid;
