import React from 'react';
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetDescription,
  SheetFooter,
} from '@/shared/components/ui/sheet';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Separator } from '@/shared/components/ui/separator';
import { ScrollArea } from '@/shared/components/ui/scroll-area';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shared/components/ui/avatar';
import {
  Globe,
  Mail,
  Phone,
  MapPin,
  ExternalLink,
  Tag,
  ArrowUpRight,
  ArrowDownRight,
  Download,
} from 'lucide-react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from 'recharts';
import type { Entity } from '@/shared/types/knowledgehub'; // Corrected import

interface EntityDetailSheetProps {
  entity: Entity | null;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  formatCurrency: (amount: number) => string;
}

export const EntityDetailSheet: React.FC<EntityDetailSheetProps> = ({
  entity,
  isOpen,
  onOpenChange,
  formatCurrency,
}) => {
  if (!entity) {
    return null;
  }

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <div className="flex flex-wrap items-center gap-2">
            <Avatar className="h-10 w-10">
              <AvatarImage src={entity.logo} alt={entity.entity} />
              <AvatarFallback>
                {entity?.entity?.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              {/* SheetTitle should align with H2: 22px, Semi-Bold (600), Primary Text */}
              <SheetTitle className="truncate text-heading-4 text-primary-text">
                {entity.entity}
              </SheetTitle>{' '}
              {/* text-xl is 20px, close to 22px */}
              {/* SheetDescription for secondary info: 15px, Regular (400), Secondary Text */}
              <SheetDescription className="text-sm text-secondary-text">
                {entity.category}
              </SheetDescription>{' '}
              {/* text-sm is 14px, close to 15px */}
            </div>
          </div>
        </SheetHeader>

        <ScrollArea className="h-[calc(100vh-180px)] mt-6 pr-4">
          {/* Entity Overview */}
          <div className="mb-6">
            {/* H3: 18px, Medium (500), Primary Text */}
            <h3 className="truncate text-heading-5 text-primary-text mb-2">
              Overview
            </h3>
            {/* Body Text: 15px, Regular (400), Secondary Text */}
            <p className="text-sm text-secondary-text mb-4">
              {entity.description}
            </p>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="truncate text-caption text-secondary-text">
                  Total Spending
                </p>{' '}
                {/* text-xs is 12px, for labels */}
                <p className="font-semibold text-lg text-primary-text">
                  {formatCurrency(entity.spending)}
                </p>
              </div>
              <div className="space-y-1">
                <p className="truncate text-caption text-secondary-text">
                  Transactions
                </p>
                <p className="font-semibold text-lg text-primary-text">
                  {entity.transactionCount}
                </p>
              </div>
              <div className="space-y-1">
                <p className="truncate text-caption text-secondary-text">
                  Category
                </p>
                <p className="font-medium text-primary-text">
                  {entity.category}
                </p>
              </div>
              <div className="space-y-1">
                <p className="truncate text-caption text-secondary-text">
                  Last Transaction
                </p>
                <p className="font-medium text-primary-text">
                  {entity.lastTransactionDate
                    ? new Date(entity.lastTransactionDate).toLocaleDateString()
                    : 'N/A'}
                </p>{' '}
                {/* Corrected to lastTransactionDate */}
              </div>
            </div>
          </div>
          <Separator className="my-4 bg-border" />{' '}
          {/* Added themed separator color */}
          {/* Spending Trend Visualization */}
          {entity.spendingHistory && entity?.spendingHistory?.length > 0 && (
            <div className="mb-6">
              <h3 className="truncate text-heading-5 text-primary-text mb-2">
                Spending History
              </h3>
              <div className="h-[200px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={entity.spendingHistory}>
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke="hsl(var(--border))"
                    />{' '}
                    {/* Themed grid */}
                    <XAxis
                      dataKey="month"
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />{' '}
                    {/* Themed axis */}
                    <YAxis
                      stroke="hsl(var(--muted-foreground))"
                      fontSize={12}
                    />{' '}
                    {/* Themed axis */}
                    <Tooltip
                      formatter={(value) => formatCurrency(value as number)}
                      contentStyle={{
                        backgroundColor: 'hsl(var(--popover))',
                        borderColor: 'hsl(var(--border))',
                      }}
                      labelStyle={{ color: 'hsl(var(--popover-foreground))' }}
                      itemStyle={{ color: 'hsl(var(--popover-foreground))' }}
                    />
                    <Line
                      type="monotone"
                      dataKey="amount"
                      stroke="hsl(var(--primary))" // Use theme primary color
                      activeDot={{ r: 8, fill: 'hsl(var(--primary))' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              <div className="flex flex-wrap items-center mt-2">
                <p className="text-sm text-secondary-text">Spending Trend: </p>
                <Badge
                  variant="outline"
                  className={`ml-2 max-w-[150px] truncate ${
                    entity.spendingTrend === 'up'
                      ? 'text-destructive-foreground border-destructive bg-destructive/20'
                      : entity.spendingTrend === 'down'
                        ? 'text-success-foreground border-success bg-success/20' // Assuming success color exists or use a green
                        : 'text-muted-foreground border-muted-foreground/50'
                  }`}
                >
                  {entity.spendingTrend === 'up' && (
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                  )}
                  {entity.spendingTrend === 'down' && (
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                  )}
                  {entity.spendingTrend === 'up'
                    ? 'Increasing'
                    : entity.spendingTrend === 'down'
                      ? 'Decreasing'
                      : 'Stable'}
                </Badge>
              </div>
            </div>
          )}
          <Separator className="my-4 bg-border" />
          {/* Contact Information */}
          <div className="mb-6">
            <h3 className="truncate text-heading-5 text-primary-text mb-2">
              Contact Information
            </h3>
            <div className="space-y-3">
              {entity.website && (
                <div className="flex flex-wrap items-center">
                  <Globe className="h-4 w-4 mr-2 text-muted-foreground" />
                  <a
                    href={entity.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:underline flex flex-wrap items-center" // text-primary for links
                  >
                    {entity.website}
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
              )}
              {entity.email && (
                <div className="flex flex-wrap items-center">
                  <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                  <p className="text-sm text-secondary-text">{entity.email}</p>
                </div>
              )}
              {entity.phone && (
                <div className="flex flex-wrap items-center">
                  <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                  <p className="text-sm text-secondary-text">{entity.phone}</p>
                </div>
              )}
              {entity.address && (
                <div className="flex flex-wrap items-start">
                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground mt-0.5" />
                  <p className="text-sm text-secondary-text">
                    {entity.address}
                  </p>
                </div>
              )}
            </div>
          </div>
          <Separator className="my-4 bg-border" />
          {/* Tags */}
          {entity.tags && entity?.tags?.length > 0 && (
            <div className="mb-6">
              <h3 className="truncate text-heading-5 text-primary-text mb-2">
                Tags
              </h3>
              <div className="flex flex-wrap flex-wrap gap-2">
                {entity?.tags?.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="bg-secondary/20 text-secondary-foreground hover:bg-secondary/30 max-w-[150px] truncate"
                  >
                    {' '}
                    {/* Themed secondary badge */}
                    <Tag className="h-3 w-3 mr-1" />
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          {/* Related Entities */}
          {entity.relatedEntities && entity?.relatedEntities?.length > 0 && (
            <div className="mb-6">
              <h3 className="truncate text-heading-5 text-primary-text mb-2">
                Related Entities
              </h3>
              <div className="space-y-2">
                {entity?.relatedEntities?.map((related, index) => (
                  <div
                    key={index}
                    className="flex flex-wrap justify-between items-center p-2 rounded-md border border-border bg-card"
                  >
                    {' '}
                    {/* Themed border and bg */}
                    <div className="font-medium text-primary-text">
                      {related.name}
                    </div>
                    <Badge
                      variant="outline"
                      className="text-secondary-text border-secondary-text/40 max-w-[150px] truncate"
                    >
                      {related.relationship}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
        </ScrollArea>

        <SheetFooter className="mt-4 pt-4 border-t border-border">
          {' '}
          {/* Added border top */}
          {/* Secondary/Outline Button style */}
          <Button
            className="max-w-full border-primary text-primary hover:bg-primary/10"
            variant="outline"
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default EntityDetailSheet;
