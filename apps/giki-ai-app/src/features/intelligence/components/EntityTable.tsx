import React, { use<PERSON>emo, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import type { Entity, EntitySortState } from '@/shared/types/knowledgehub';

interface EntityTableProps {
  entities: Entity[];
  onSortChange: (sortBy: EntitySortState['sortBy']) => void;
  currentSortBy: EntitySortState['sortBy'];
  currentSortOrder: 'asc' | 'desc';
  onEntitySelect: (entity: Entity) => void;
  formatCurrency: (amount: number) => string;
}

export const EntityTable: React.FC<EntityTableProps> = ({
  entities,
  onSortChange,
  currentSortBy,
  currentSortOrder,
  onEntitySelect,
  formatCurrency,
}) => {
  // Memoize sort handlers to prevent unnecessary re-renders
  const handleSortChange = useCallback((sortBy: EntitySortState['sortBy']) => {
    onSortChange(sortBy);
  }, [onSortChange]);

  const handleEntitySelect = useCallback((entity: Entity) => {
    onEntitySelect(entity);
  }, [onEntitySelect]);

  // Helper to determine if a column is the current sort column
  const isSortColumn = useCallback((columnName: EntitySortState['sortBy']) =>
    currentSortBy === columnName, [currentSortBy]);

  // Memoize table rows to prevent re-rendering when entities haven't changed
  const tableRows = useMemo(() => {
    return entities.map((entity) => (
      <TableRow
        key={entity.id}
        onClick={() => handleEntitySelect(entity)}
        className="cursor-pointer hover:bg-muted/50"
      >
        <TableCell className="text-left">{entity.entity}</TableCell>
        <TableCell className="text-right text-muted-foreground text-sm">
          {formatCurrency(entity.spending)}
        </TableCell>
        <TableCell>
          {entity.category && (
            <Badge
              variant="outline"
              className="text-muted-foreground border-muted-foreground/40 max-w-[150px] truncate"
            >
              {entity.category}
            </Badge>
          )}
        </TableCell>
        <TableCell className="text-center text-muted-foreground text-sm">
          {entity.transactionCount}
        </TableCell>
        <TableCell className="text-muted-foreground text-sm">
          {entity.lastTransactionDate
            ? new Date(entity.lastTransactionDate).toLocaleDateString()
            : 'N/A'}
        </TableCell>
        <TableCell>
          <Button
            className="max-w-full"
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleEntitySelect(entity);
            }}
          >
            Details
          </Button>
        </TableCell>
      </TableRow>
    ));
  }, [entities, formatCurrency, handleEntitySelect]);

  return (
    <div className="rounded-md border bg-gradient-card shadow-premium">
      <Table>
        <TableHeader>
          <TableRow>
            {/* Updated TableHead to use font-semibold and primary text color from theme */}
            <TableHead
              className="cursor-pointer text-foreground font-semibold"
              onClick={() => handleSortChange('name')}
            >
              Entity Name
              {isSortColumn('name') && (
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead
              className="text-right cursor-pointer text-foreground font-semibold"
              onClick={() => handleSortChange('spending')}
            >
              Total Spending
              {isSortColumn('spending') && (
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer text-foreground font-semibold"
              onClick={() => handleSortChange('category')}
            >
              Category
              {isSortColumn('category') && (
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer text-foreground font-semibold"
              onClick={() => handleSortChange('transactionCount')}
            >
              Transactions
              {isSortColumn('transactionCount') && (
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead
              className="cursor-pointer text-foreground font-semibold"
              onClick={() => handleSortChange('lastTransactionDate')}
            >
              Last Transaction
              {isSortColumn('lastTransactionDate') && (
                <span className="ml-1">
                  {currentSortOrder === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </TableHead>
            <TableHead className="w-[100px] text-foreground font-semibold">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tableRows}
        </TableBody>
      </Table>
    </div>
  );
};

// Re-eval trigger comment
export default EntityTable;
