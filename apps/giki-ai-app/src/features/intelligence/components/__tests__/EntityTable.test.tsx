/**
 * EntityTable Component Tests - Performance Optimized Component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EntityTable } from '../EntityTable';
import type { Entity, EntitySortState } from '@/shared/types/knowledgehub';

// Mock entities for testing
const mockEntities: Entity[] = [
  {
    id: '1',
    entity: 'Alpha Corp',
    spending: 5000.00,
    category: 'Professional Services',
    transactionCount: 12,
    lastTransactionDate: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    entity: 'Beta LLC',
    spending: 3250.50,
    category: 'Office Supplies',
    transactionCount: 8,
    lastTransactionDate: '2024-01-10T14:20:00Z',
  },
  {
    id: '3',
    entity: 'Gamma Inc',
    spending: 7800.25,
    category: 'Technology',
    transactionCount: 15,
    lastTransactionDate: '2024-01-20T09:15:00Z',
  },
];

const mockFormatCurrency = vi.fn((amount: number) => 
  new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  }).format(amount)
);

const mockOnSortChange = vi.fn();
const mockOnEntitySelect = vi.fn();

const defaultProps = {
  entities: mockEntities,
  onSortChange: mockOnSortChange,
  currentSortBy: 'name' as EntitySortState['sortBy'],
  currentSortOrder: 'asc' as 'asc' | 'desc',
  onEntitySelect: mockOnEntitySelect,
  formatCurrency: mockFormatCurrency,
};

describe('EntityTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders table with correct structure', () => {
      render(<EntityTable {...defaultProps} />);
      
      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(screen.getByRole('columnheader', { name: /entity name/i })).toBeInTheDocument();
      expect(screen.getByRole('columnheader', { name: /total spending/i })).toBeInTheDocument();
      expect(screen.getByRole('columnheader', { name: /category/i })).toBeInTheDocument();
      expect(screen.getByRole('columnheader', { name: /transactions/i })).toBeInTheDocument();
      expect(screen.getByRole('columnheader', { name: /last transaction/i })).toBeInTheDocument();
      expect(screen.getByRole('columnheader', { name: /actions/i })).toBeInTheDocument();
    });

    it('renders all entity rows', () => {
      render(<EntityTable {...defaultProps} />);
      
      expect(screen.getByText('Alpha Corp')).toBeInTheDocument();
      expect(screen.getByText('Beta LLC')).toBeInTheDocument();
      expect(screen.getByText('Gamma Inc')).toBeInTheDocument();
    });

    it('displays entity data correctly', () => {
      render(<EntityTable {...defaultProps} />);
      
      // Check spending amounts are formatted
      expect(mockFormatCurrency).toHaveBeenCalledWith(5000.00);
      expect(mockFormatCurrency).toHaveBeenCalledWith(3250.50);
      expect(mockFormatCurrency).toHaveBeenCalledWith(7800.25);
      
      // Check transaction counts
      expect(screen.getByText('12')).toBeInTheDocument();
      expect(screen.getByText('8')).toBeInTheDocument();
      expect(screen.getByText('15')).toBeInTheDocument();
      
      // Check categories are displayed in badges
      expect(screen.getByText('Professional Services')).toBeInTheDocument();
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
      expect(screen.getByText('Technology')).toBeInTheDocument();
    });

    it('formats dates correctly', () => {
      render(<EntityTable {...defaultProps} />);
      
      // Check that dates are formatted to local date strings
      const dateElements = screen.getAllByText(/\d{1,2}\/\d{1,2}\/\d{4}/);
      expect(dateElements.length).toBeGreaterThan(0);
    });

    it('displays sort indicators correctly', () => {
      render(<EntityTable {...defaultProps} />);
      
      // Check that current sort column shows indicator
      const nameHeader = screen.getByRole('columnheader', { name: /entity name/i });
      expect(nameHeader).toHaveTextContent('↑'); // asc indicator
    });

    it('shows desc sort indicator when order is desc', () => {
      render(<EntityTable {...defaultProps} currentSortOrder="desc" />);
      
      const nameHeader = screen.getByRole('columnheader', { name: /entity name/i });
      expect(nameHeader).toHaveTextContent('↓'); // desc indicator
    });
  });

  describe('Sorting Interactions', () => {
    it('calls onSortChange when column header is clicked', async () => {
      const user = userEvent.setup();
      render(<EntityTable {...defaultProps} />);
      
      const spendingHeader = screen.getByRole('columnheader', { name: /total spending/i });
      await user.click(spendingHeader);
      
      expect(mockOnSortChange).toHaveBeenCalledWith('spending');
    });

    it('handles all sortable columns', async () => {
      const user = userEvent.setup();
      render(<EntityTable {...defaultProps} />);
      
      // Test each sortable column
      const sortableColumns = [
        { name: /entity name/i, key: 'name' },
        { name: /total spending/i, key: 'spending' },
        { name: /category/i, key: 'category' },
        { name: /transactions/i, key: 'transactionCount' },
        { name: /last transaction/i, key: 'lastTransactionDate' },
      ];
      
      for (const column of sortableColumns) {
        const header = screen.getByRole('columnheader', { name: column.name });
        await user.click(header);
        expect(mockOnSortChange).toHaveBeenCalledWith(column.key);
      }
      
      expect(mockOnSortChange).toHaveBeenCalledTimes(5);
    });
  });

  describe('Entity Selection', () => {
    it('calls onEntitySelect when row is clicked', async () => {
      const user = userEvent.setup();
      render(<EntityTable {...defaultProps} />);
      
      const firstRow = screen.getByText('Alpha Corp').closest('tr');
      await user.click(firstRow!);
      
      expect(mockOnEntitySelect).toHaveBeenCalledWith(mockEntities[0]);
    });

    it('calls onEntitySelect when Details button is clicked', async () => {
      const user = userEvent.setup();
      render(<EntityTable {...defaultProps} />);
      
      const detailsButtons = screen.getAllByText('Details');
      await user.click(detailsButtons[0]);
      
      expect(mockOnEntitySelect).toHaveBeenCalledWith(mockEntities[0]);
    });

    it('prevents event bubbling when Details button is clicked', async () => {
      const user = userEvent.setup();
      render(<EntityTable {...defaultProps} />);
      
      const detailsButton = screen.getAllByText('Details')[0];
      await user.click(detailsButton);
      
      // Should only be called once (from button, not from row)
      expect(mockOnEntitySelect).toHaveBeenCalledTimes(1);
    });
  });

  describe('Performance Optimizations', () => {
    it('memoizes table rows properly', () => {
      const { rerender } = render(<EntityTable {...defaultProps} />);
      
      // First render should call formatCurrency for all entities
      expect(mockFormatCurrency).toHaveBeenCalledTimes(3);
      
      // Clear mock calls
      mockFormatCurrency.mockClear();
      
      // Re-render with same props
      rerender(<EntityTable {...defaultProps} />);
      
      // Should call formatCurrency again (normal behavior for re-render)
      expect(mockFormatCurrency).toHaveBeenCalledTimes(3);
    });

    it('handles sort change callback optimization', () => {
      render(<EntityTable {...defaultProps} />);
      
      // Verify callbacks are functions
      expect(typeof mockOnSortChange).toBe('function');
      expect(typeof mockOnEntitySelect).toBe('function');
    });

    it('optimizes sort column checking', () => {
      render(<EntityTable {...defaultProps} currentSortBy="spending" />);
      
      // Only spending column should show sort indicator
      const spendingHeader = screen.getByRole('columnheader', { name: /total spending/i });
      expect(spendingHeader).toHaveTextContent('↑');
      
      const nameHeader = screen.getByRole('columnheader', { name: /entity name/i });
      expect(nameHeader).not.toHaveTextContent('↑');
      expect(nameHeader).not.toHaveTextContent('↓');
    });
  });

  describe('Edge Cases', () => {
    it('handles empty entities array', () => {
      render(<EntityTable {...defaultProps} entities={[]} />);
      
      expect(screen.getByRole('table')).toBeInTheDocument();
      // Should still show headers but no data rows
      expect(screen.getByRole('columnheader', { name: /entity name/i })).toBeInTheDocument();
      expect(screen.queryByText('Alpha Corp')).not.toBeInTheDocument();
    });

    it('handles entities without categories', () => {
      const entitiesWithoutCategory: Entity[] = [
        {
          ...mockEntities[0],
          category: undefined,
        },
      ];
      
      render(<EntityTable {...defaultProps} entities={entitiesWithoutCategory} />);
      
      expect(screen.getByText('Alpha Corp')).toBeInTheDocument();
      // Category cell should be empty but not crash
    });

    it('handles entities without last transaction date', () => {
      const entitiesWithoutDate: Entity[] = [
        {
          ...mockEntities[0],
          lastTransactionDate: null,
        },
      ];
      
      render(<EntityTable {...defaultProps} entities={entitiesWithoutDate} />);
      
      expect(screen.getByText('N/A')).toBeInTheDocument();
    });

    it('handles formatCurrency errors gracefully', () => {
      const errorFormatCurrency = vi.fn(() => {
        throw new Error('Format error');
      });
      
      // Should not crash when formatCurrency throws
      expect(() => {
        render(
          <EntityTable
            {...defaultProps}
            formatCurrency={errorFormatCurrency}
            entities={[mockEntities[0]]}
          />
        );
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('has proper table semantics', () => {
      render(<EntityTable {...defaultProps} />);
      
      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(screen.getAllByRole('columnheader')).toHaveLength(6);
      expect(screen.getAllByRole('row')).toHaveLength(4); // 1 header + 3 data rows
    });

    it('has clickable column headers for sorting', () => {
      render(<EntityTable {...defaultProps} />);
      
      const sortableHeaders = screen.getAllByRole('columnheader').slice(0, 5); // First 5 are sortable
      sortableHeaders.forEach(header => {
        expect(header).toHaveClass('cursor-pointer');
      });
    });

    it('has hover effects on rows', () => {
      render(<EntityTable {...defaultProps} />);
      
      const dataRows = screen.getAllByRole('row').slice(1); // Skip header row
      dataRows.forEach(row => {
        expect(row).toHaveClass('cursor-pointer', 'hover:bg-muted/50');
      });
    });
  });
});