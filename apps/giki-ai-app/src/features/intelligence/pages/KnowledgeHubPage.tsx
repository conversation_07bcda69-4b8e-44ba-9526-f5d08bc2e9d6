import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/shared/components/ui/card';
import { Input } from '@/shared/components/ui/input';
import { Button } from '@/shared/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>bsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { Badge } from '@/shared/components/ui/badge';
import {
  EntityTable,
  EntityGrid,
  EntityDetailSheet,
  // RagCorpusManagement, // Commented out pending Architect decision
} from '../components';
import type { Entity, EntitySortState } from '@/shared/types/knowledgehub';
import type { SpendingByEntityItem } from '@/shared/types/api'; // Needed for enhanceEntityData
import { environment } from '@/environments/environment';
import {
  <PERSON><PERSON><PERSON> as RechartsPieChart,
  Pie as RechartsPie,
  Cell as RechartsCell,
  ResponsiveContainer,
  BarChart as RechartsBarChart,
  Bar as RechartsBar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from 'recharts';

import { getSpendingByEntityReport } from '@/features/reports/services/reportService';

// Consolidate lucide-react imports and ensure all used icons are present
import {
  Search,
  Filter,
  Building2,
  BarChart3 as BarChartIconLucide,
  PieChart as PieChartIconLucide, // This is the lucide icon, aliased
  AlertCircle as LucideAlertCircle,
  FileText as LucideFileText,
  RefreshCw,
} from 'lucide-react';
// import { Loading } from '@/shared/components/ui/loading'; // Import Loading component
// import RagCorpusManagement from '@/components/knowledgehub/RagCorpusManagement'; // Ensure this is commented if not used

// Colors for charts - can be moved to a constants file if used elsewhere
// Updated to align with apps/giki-ai-app/specs/design-requirements.md
const COLORS = [
  'hsl(var(--giki-brand-emerald))', // Secondary (Logo-inspired Green)
  'hsl(var(--giki-brand-purple))', // Agent Interaction Accent (Logo-inspired Lavender)
  'hsl(var(--giki-brand-blue))', // Tertiary Accent 1 (Logo-inspired Soft Blue)
  'hsl(var(--giki-brand-peach))', // Tertiary Accent 2 (Logo-inspired Soft Peach)
  'hsl(var(--giki-neutral-600))', // Primary (Cool Slate Grey)
  'hsl(var(--giki-success))', // Success (Strong Green)
];

const KnowledgeHubPage: React.FC = () => {
  const [entities, setEntities] = useState<Entity[]>([]);
  const [filteredEntities, setFilteredEntities] = useState<Entity[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedEntity, setSelectedEntity] = useState<Entity | null>(null);
  const [error, setError] = useState<string | null>(null);

  const [activeTab, setActiveTab] = useState('entities');
  const [sortState, setSortState] = useState<EntitySortState>({
    sortBy: 'spending',
    sortOrder: 'desc',
  });
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');
  const [isDetailsPanelOpen, setIsDetailsPanelOpen] = useState(false);
  const [topEntities, setTopEntities] = useState<Entity[]>([]);
  const [entityCategories, setEntityCategories] = useState<
    { name: string; count: number }[]
  >([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Convert spending data to entity format with real data only
  const enhanceEntityData = (data: SpendingByEntityItem[]): Entity[] => {
    return data.map((entity, index) => {
      return {
        ...entity,
        id: `entity-${index}-${entity?.entity?.replace(/\s+/g, '-')}`, // Ensure unique ID
        // Only include real data - remove all fake generated fields
        category: undefined, // Will be determined from actual transaction data
        lastTransactionDate: undefined, // Will be determined from actual transactions
        website: undefined, // Only include if we have real data
        description: undefined, // Only include if we have real data
        logo: `${environment.avatarService.baseUrl}?name=${encodeURIComponent(entity.entity)}&${environment.avatarService.defaultParams}`, // Avatar service is acceptable for placeholder images
        email: undefined, // Only include if we have real data
        phone: undefined, // Only include if we have real data
        address: undefined, // Only include if we have real data
        transactionCount: undefined, // Will be determined from actual transaction count
        spendingTrend: undefined, // Will be determined from actual spending analysis
        spendingHistory: undefined, // Will be determined from real transaction history
        tags: undefined, // Will be determined from actual transaction patterns
        relatedEntities: [], // Will be determined from actual data relationships
      };
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Extract categories from entities
  const extractCategories = (entitiesToCategorize: Entity[]) => {
    const categoryCounts: Record<string, number> = {};

    entitiesToCategorize.forEach((entity) => {
      if (entity.category) {
        categoryCounts[entity.category] =
          (categoryCounts[entity.category] || 0) + 1;
      }
    });

    return Object.entries(categoryCounts).map(([name, count]) => ({
      name,
      count,
    }));
  };

  // Get top entities by spending
  const getTopEntities = (entitiesToFilter: Entity[], count: number = 5) => {
    return [...entitiesToFilter]
      .sort((a, b) => b.spending - a.spending)
      .slice(0, count);
  };

  // Apply sorting and filtering
  const applySortingAndFiltering = useCallback(
    (
      entitiesToFilter: Entity[],
      currentSearchTerm: string,
      currentSortState: EntitySortState,
      currentCategoryFilter: string,
    ): Entity[] => {
      let result = [...entitiesToFilter];

      if (currentSearchTerm.trim() !== '') {
        result = result.filter(
          (entity) =>
            entity.entity
              .toLowerCase()
              .includes(currentSearchTerm.toLowerCase()) ||
            (entity.category &&
              entity.category
                .toLowerCase()
                .includes(currentSearchTerm.toLowerCase())) ||
            (entity.tags &&
              entity?.tags?.some((tag) =>
                tag.toLowerCase().includes(currentSearchTerm.toLowerCase()),
              )),
        );
      }

      if (currentCategoryFilter !== 'all') {
        result = result.filter(
          (entity) => entity.category === currentCategoryFilter,
        );
      }

      result = [...result].sort((a, b) => {
        const valA = a[currentSortState.sortBy as keyof Entity];
        const valB = b[currentSortState.sortBy as keyof Entity];

        if (typeof valA === 'number' && typeof valB === 'number') {
          return currentSortState.sortOrder === 'asc'
            ? valA - valB
            : valB - valA;
        }
        if (typeof valA === 'string' && typeof valB === 'string') {
          return currentSortState.sortOrder === 'asc'
            ? valA.localeCompare(valB)
            : valB.localeCompare(valA);
        }
        if (currentSortState.sortBy === 'lastTransactionDate') {
          const dateA = a.lastTransactionDate
            ? new Date(a.lastTransactionDate).getTime()
            : 0;
          const dateB = b.lastTransactionDate
            ? new Date(b.lastTransactionDate).getTime()
            : 0;
          return currentSortState.sortOrder === 'asc'
            ? dateA - dateB
            : dateB - dateA;
        }
        return 0;
      });

      return result;
    },
    [],
  );

  // Fetch entity data
  useEffect(() => {
    const fetchEntities = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getSpendingByEntityReport();
        const enhancedData = enhanceEntityData(data);
        setEntities(enhancedData);
      } catch (err) {
        setError('Failed to fetch entity data');
        console.error('Error fetching entities:', err);
      } finally {
        setLoading(false);
      }
    };

    void fetchEntities();
  }, [refreshTrigger]);

  // Apply filters when search term, sort, or category filter changes
  useEffect(() => {
    if (entities.length > 0 || !loading) {
      // Run if entities are loaded or loading has finished (even if empty)
      const filtered = applySortingAndFiltering(
        entities,
        searchTerm,
        sortState,
        categoryFilter,
      );
      setFilteredEntities(filtered);
      setTopEntities(getTopEntities(filtered));
      setEntityCategories(extractCategories(filtered));
    }
  }, [
    searchTerm,
    entities,
    sortState,
    categoryFilter,
    applySortingAndFiltering,
    loading,
  ]);

  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e?.target?.value);
  };

  // Handle entity selection
  const handleEntitySelect = (entity: Entity) => {
    setSelectedEntity(entity);
    setIsDetailsPanelOpen(true);
  };

  // Handle sort change
  const handleSortChange = (newSortBy: EntitySortState['sortBy']) => {
    setSortState((prevSortState) => {
      if (prevSortState.sortBy === newSortBy) {
        return {
          ...prevSortState,
          sortOrder: prevSortState.sortOrder === 'asc' ? 'desc' : 'asc',
        };
      }
      const defaultSortOrder =
        newSortBy === 'spending' ||
        newSortBy === 'transactionCount' ||
        newSortBy === 'lastTransactionDate'
          ? 'desc'
          : 'asc';
      return { sortBy: newSortBy, sortOrder: defaultSortOrder };
    });
  };

  // Handle category filter change
  const handleCategoryFilterChange = (category: string) => {
    setCategoryFilter(category);
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  const mainContent = (
    <div className="container mx-auto p-4 md:p-6 lg:p-8">
      {' '}
      {/* Added responsive padding */}
      <header className="mb-8">
        {/* Ensure H1 uses Inter, 28px, Bold (700). Tailwind text-3xl is 30px, font-bold is 700. Inter should be global. */}
        <h1 className="truncate text-heading-1 text-primary-text">
          Knowledge Hub
        </h1>
        {/* Ensure p uses Inter, 15px, Regular (400), color Steel Grey (#6C757D) */}
        <p className="text-secondary-text mt-2 text-base">
          {' '}
          {/* text-base is 16px, text-sm is 14px. 15px is custom. Using text-base for now. */}
          Explore vendors and entities identified from your cost transaction
          data
        </p>
      </header>
      {/* Search and Filter Controls */}
      <div className="mb-6">
        <div className="flex flex-wrap flex-wrap gap-4 items-center">
          <div className="relative flex flex-wrap-grow max-w-md">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 truncate[hsl(var(--giki-input-placeholder))]" />
            <Input
              placeholder="Search entities..."
              value={searchTerm}
              onChange={handleSearch}
              className="pl-8 border-border focus:border-green-500 focus:ring-green-500"
              aria-label="Search entities by name, category, or tags"
            />
          </div>

          <Select
            value={categoryFilter}
            onValueChange={handleCategoryFilterChange}
          >
            <SelectTrigger className="w-[200px] border-border focus:border-green-500 focus:ring-green-500">
              <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                <span className="font-medium">All Categories</span>
                <span className="text-muted-foreground ml-2">
                  ({entities.length})
                </span>
              </SelectItem>
              {entityCategories.map((category, index) => (
                <SelectItem key={index} value={category.name}>
                  <span className="font-medium">{category.name}</span>
                  <span className="text-muted-foreground ml-2">
                    ({category.count})
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            className="max-w-full border-border hover:bg-muted/50"
            variant="outline"
            size="icon"
            onClick={() => void handleRefresh()}
            aria-label="Refresh entity data"
            title="Refresh entity data"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </Button>
        </div>
      </div>
      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="grid w-full grid-cols-2 bg-muted p-1 rounded-lg">
          <TabsTrigger
            value="entities"
            className="flex flex-wrap items-center justify-center gap-2 data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all"
          >
            <Building2 className="h-4 w-4" />
            <span className="font-medium">Entities</span>
            <Badge
              variant="secondary"
              className="ml-1 truncate text-caption bg-gray-200 text-muted-foreground max-w-[150px]"
            >
              {filteredEntities.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="insights"
            className="flex flex-wrap items-center justify-center gap-2 data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all"
          >
            <PieChartIconLucide className="h-4 w-4" />
            <span className="font-medium">Insights</span>
            <Badge
              variant="secondary"
              className="ml-1 truncate text-caption bg-gray-200 text-muted-foreground max-w-[150px]"
            >
              {entityCategories.length}
            </Badge>
          </TabsTrigger>
        </TabsList>

        {/* Entities Tab Content */}
        <TabsContent value="entities" className="mt-4">
          <Card>
            <CardHeader className="pb-4 border-b border-gray-100">
              <div className="flex flex-wrap justify-between items-center">
                <div>
                  <CardTitle className="text-card-foreground-title text-foreground mb-1">
                    Entity List
                  </CardTitle>
                  <p className="text-body-small">
                    {filteredEntities.length} entities found
                    {searchTerm && ` matching "${searchTerm}"`}
                    {categoryFilter !== 'all' && ` in ${categoryFilter}`}
                  </p>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button
                    className={`max-w-full ${viewMode === 'table' ? 'bg-muted text-foreground' : 'text-muted-foreground'} hover:bg-muted`}
                    variant="ghost"
                    size="sm"
                    onClick={() => setViewMode('table')}
                    aria-label="Table view"
                  >
                    <LucideFileText className="h-4 w-4" />
                  </Button>
                  <Button
                    className={`max-w-full ${viewMode === 'grid' ? 'bg-muted text-foreground' : 'text-muted-foreground'} hover:bg-muted`}
                    variant="ghost"
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    aria-label="Grid view"
                  >
                    <BarChartIconLucide className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0 overflow-hidden">
              {loading ? (
                <div className="flex flex-wrap flex-col justify-center items-center h-64 space-y-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
                  <div className="text-center">
                    <p className="text-foreground font-medium">
                      Loading entities...
                    </p>
                    <p className="text-muted-foreground text-sm mt-1">
                      Analyzing your transaction data to identify vendors and
                      entities
                    </p>
                  </div>
                </div>
              ) : error ? (
                <Alert
                  variant="destructive"
                  className="m-4 bg-red-50 border-red-200 truncatered-800 shadow-md"
                  role="alert"
                  aria-live="polite"
                >
                  <LucideAlertCircle className="h-5 w-5 text-destructive" />
                  <AlertTitle className="truncatered-900 font-semibold text-base">
                    Unable to Load Entity Data
                  </AlertTitle>
                  <AlertDescription className="text-destructive text-sm mt-2">
                    <div className="space-y-2">
                      <p>{error}</p>
                      <p className="truncate text-caption text-destructive">
                        This might be due to a network issue or server
                        maintenance.
                      </p>
                      <div className="flex flex-wrap gap-2 mt-3">
                        <Button
                          className="max-w-full border-red-300 text-destructive hover:bg-red-50"
                          variant="outline"
                          size="sm"
                          onClick={() => void handleRefresh()}
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          Try Again
                        </Button>
                        <Button
                          className="max-w-full text-destructive hover:bg-red-50"
                          variant="ghost"
                          size="sm"
                          onClick={() => (window.location.href = '/support')}
                        >
                          Contact Support
                        </Button>
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              ) : filteredEntities.length === 0 ? (
                <div className="text-center py-16 px-6">
                  <div className="max-w-md mx-auto">
                    <Building2 className="mx-auto h-16 w-16 sm:w-16 truncate[hsl(var(--giki-text-disabled))] mb-6" />
                    <h3 className="truncate text-heading-4 text-foreground mb-3">
                      {searchTerm || categoryFilter !== 'all'
                        ? 'No matching entities found'
                        : 'No entities available'}
                    </h3>
                    <p className="text-muted-foreground mb-6 leading-relaxed">
                      {searchTerm || categoryFilter !== 'all'
                        ? "Try adjusting your search terms or filter criteria to find the entities you're looking for."
                        : 'Upload and process your transaction data to automatically discover vendors, merchants, and other entities from your financial records.'}
                    </p>
                    {!searchTerm && categoryFilter === 'all' && (
                      <Button
                        className="max-w-full bg-green-600 hover:bg-green-700 text-white"
                        onClick={() => (window.location.href = '/upload')}
                      >
                        Upload Transaction Data
                      </Button>
                    )}
                    {(searchTerm || categoryFilter !== 'all') && (
                      <div className="flex flex-wrap gap-2 justify-center">
                        <Button
                          className="max-w-full"
                          variant="outline"
                          onClick={() => {
                            setSearchTerm('');
                            setCategoryFilter('all');
                          }}
                        >
                          Clear Filters
                        </Button>
                        <Button
                          className="max-w-full"
                          onClick={() => void handleRefresh()}
                          variant="outline"
                        >
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Refresh
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ) : viewMode === 'table' ? (
                <EntityTable
                  entities={filteredEntities}
                  onSortChange={handleSortChange}
                  currentSortBy={sortState.sortBy}
                  currentSortOrder={sortState.sortOrder}
                  onEntitySelect={handleEntitySelect}
                  formatCurrency={formatCurrency}
                />
              ) : (
                <EntityGrid
                  entities={filteredEntities}
                  onEntitySelect={handleEntitySelect}
                  formatCurrency={formatCurrency}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights Tab Content */}
        <TabsContent value="insights" className="mt-4 space-y-6">
          {/* Category Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Category Breakdown</CardTitle>
              <CardDescription>
                Distribution of entities by category
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Pie Chart */}
                <div className="h-[300px] overflow-y-auto">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <RechartsPie
                        data={entityCategories}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="hsl(var(--giki-brand-purple))"
                        dataKey="count"
                        nameKey="name"
                        label={({ name, percent }) =>
                          `${name}: ${(percent * 100).toFixed(0)}%`
                        }
                      >
                        {entityCategories.map((_, index) => (
                          <RechartsCell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </RechartsPie>
                      <Tooltip
                        formatter={(value) => [
                          `${String(value)} entities`,
                          'Count',
                        ]}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>

                {/* Category List */}
                <div>
                  <h3 className="text-card-foreground-title mb-4">
                    Categories
                  </h3>
                  <div className="space-y-2">
                    {entityCategories.map((category, index) => (
                      <div
                        key={index}
                        className="flex flex-wrap justify-between items-center p-2 rounded-md border"
                      >
                        <div className="flex flex-wrap items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{
                              backgroundColor: COLORS[index % COLORS.length],
                            }}
                          />
                          <span>{category.name}</span>
                        </div>
                        <Badge
                          variant="outline"
                          className="max-w-[150px] truncate"
                        >
                          {category.count} entities
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Entities */}
          <Card>
            <CardHeader>
              <CardTitle>Top Entities by Spending</CardTitle>
              <CardDescription>
                Entities with the highest total spending
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Bar Chart */}
                <div className="h-[300px] overflow-y-auto">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={topEntities}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis
                        type="category"
                        dataKey="entity"
                        width={100}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip
                        formatter={(value) => formatCurrency(value as number)}
                      />
                      {/* Updated fill to use the first color from our theme COLORS array */}
                      <RechartsBar dataKey="spending" fill={COLORS[0]}>
                        {topEntities.map((_, index) => (
                          <RechartsCell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </RechartsBar>
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>

                {/* Top Entities List */}
                <div>
                  <h3 className="text-card-foreground-title mb-4">
                    Top Entities
                  </h3>
                  <div className="space-y-2">
                    {topEntities.map((entity, index) => (
                      <div
                        key={index}
                        className="flex flex-wrap justify-between items-center p-2 rounded-md border"
                      >
                        <div className="flex flex-wrap items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{
                              backgroundColor: COLORS[index % COLORS.length],
                            }}
                          />
                          <span>{entity.entity}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">
                            {formatCurrency(entity.spending)}
                          </div>
                          <div className="truncate text-caption text-muted-foreground">
                            {entity.category}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* RAG Corpus Tab Content - Conditionally render or remove based on Architect's decision */}
        {/*
        <TabsContent value="rag-corpus" className="mt-4">
          <RagCorpusManagement />
        </TabsContent>
        */}
      </Tabs>
      {/* Entity Detail Sheet */}
      <EntityDetailSheet
        entity={selectedEntity}
        isOpen={isDetailsPanelOpen}
        onOpenChange={setIsDetailsPanelOpen}
        formatCurrency={formatCurrency}
      />
    </div>
  );

  return mainContent;
};

export default KnowledgeHubPage;
