/**
 * Budget Variance Analysis Page - Financial Intelligence
 * Professional budget vs actual analysis with variance explanations
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { formatCurrency as formatCurrencyUtil } from '@/shared/utils/formatCurrency';

interface BudgetVariance {
  category: string;
  budget: number;
  actual: number;
  variance: number;
  variancePercent: number;
  status: 'under' | 'over' | 'on-track';
  explanation?: string;
}

export const BudgetVariancePage: React.FC = () => {
  const [_selectedPeriod, _setSelectedPeriod] = useState('2025-03');
  const [isLoading, setIsLoading] = useState(true);

  const budgetData: BudgetVariance[] = [
    {
      category: 'Sales & Marketing',
      budget: 20000,
      actual: 18000,
      variance: -2000,
      variancePercent: -10.0,
      status: 'under',
      explanation: 'Delayed campaign launch pushed spend to next month',
    },
    {
      category: 'Operations',
      budget: 15000,
      actual: 16500,
      variance: 1500,
      variancePercent: 10.0,
      status: 'over',
      explanation: 'Unplanned equipment maintenance and supplies',
    },
    {
      category: 'Software & Tools',
      budget: 6000,
      actual: 5670,
      variance: -330,
      variancePercent: -5.5,
      status: 'on-track',
      explanation: 'Annual renewal discount achieved',
    },
    {
      category: 'Travel & Entertainment',
      budget: 5000,
      actual: 7230,
      variance: 2230,
      variancePercent: 44.6,
      status: 'over',
      explanation: 'Unexpected client visits and conference attendance',
    },
    {
      category: 'Professional Services',
      budget: 8000,
      actual: 7200,
      variance: -800,
      variancePercent: -10.0,
      status: 'under',
      explanation: 'Legal work completed under budget',
    },
  ];

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const formatCurrency = (amount: number | null | undefined) =>
    formatCurrencyUtil(amount, {
      currency: 'USD',
      showCurrency: true,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'under':
        return 'text-success bg-success/10 border-success/20';
      case 'over':
        return 'text-destructive bg-destructive/10 border-destructive/20';
      case 'on-track':
        return 'text-info bg-info/10 border-info/20';
      default:
        return 'text-muted-foreground bg-muted border-border';
    }
  };

  const getVarianceColor = (variance: number) =>
    variance > 0
      ? 'text-destructive'
      : variance < 0
        ? 'text-success'
        : 'text-muted-foreground';

  const totalBudget = budgetData.reduce((sum, item) => sum + item.budget, 0);
  const totalActual = budgetData.reduce((sum, item) => sum + item.actual, 0);
  const totalVariance = totalActual - totalBudget;
  const totalVariancePercent =
    totalBudget > 0 ? (totalVariance / totalBudget) * 100 : 0;

  if (isLoading) {
    return (
      <div className="p-8 space-y-6 animate-pulse">
        <div className="h-8 bg-muted rounded w-1/4"></div>
        <div className="grid grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-32 bg-muted rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 bg-white min-h-screen">
      {/* Professional Header - Enhanced Design */}
      <div className="mb-8">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-semibold text-primary-foreground mb-2">
              Budget Variance Analysis
            </h1>
            <p className="text-secondary-foreground text-base">
              Budget vs actual performance with detailed variance explanations
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              className="border-brand-primary text-brand-primary hover:bg-success/10"
            >
              Export Analysis
            </Button>
            <Button className="bg-brand-primary hover:bg-brand-primary-hover text-white">
              Adjust Budget
            </Button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card className="border border-border shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Total Budget
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-brand-primary font-mono mb-1">
              {formatCurrency(totalBudget)}
            </div>
            <div className="text-xs text-muted-foreground">March 2025</div>
          </CardContent>
        </Card>

        <Card className="border border-border shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Actual Spend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-brand-primary font-mono mb-1">
              {formatCurrency(totalActual)}
            </div>
            <div className="text-xs text-muted-foreground">As of today</div>
          </CardContent>
        </Card>

        <Card className="border border-border shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Total Variance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className={`text-4xl font-bold font-mono mb-1 ${getVarianceColor(totalVariance)}`}
            >
              {totalVariance > 0 ? '+' : ''}
              {formatCurrency(Math.abs(totalVariance))}
            </div>
            <div
              className={`text-xs font-medium ${getVarianceColor(totalVariance)}`}
            >
              {totalVariancePercent > 0 ? '+' : ''}
              {isNaN(totalVariancePercent)
                ? '0.0'
                : totalVariancePercent.toFixed(1)}
              % vs budget
            </div>
          </CardContent>
        </Card>

        <Card className="border border-border shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Budget Utilization
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-brand-primary font-mono mb-1">
              {totalBudget > 0
                ? ((totalActual / totalBudget) * 100).toFixed(1)
                : '0.0'}
              %
            </div>
            <div className="text-xs text-muted-foreground">of total budget</div>
          </CardContent>
        </Card>
      </div>

      {/* Variance Analysis Table */}
      <Card className="border border-border shadow-sm overflow-hidden mb-8">
        <CardHeader className="bg-brand-primary text-white px-6 py-4">
          <CardTitle className="text-base font-semibold">
            Detailed Variance Analysis - {_selectedPeriod}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted border-b border-border">
                <tr>
                  <th className="text-left py-4 px-6 font-semibold text-primary-foreground">
                    Category
                  </th>
                  <th className="text-right py-4 px-6 font-semibold text-primary-foreground">
                    Budget
                  </th>
                  <th className="text-right py-4 px-6 font-semibold text-primary-foreground">
                    Actual
                  </th>
                  <th className="text-right py-4 px-6 font-semibold text-primary-foreground">
                    Variance
                  </th>
                  <th className="text-right py-4 px-6 font-semibold text-primary-foreground">
                    Variance %
                  </th>
                  <th className="text-center py-4 px-6 font-semibold text-primary-foreground">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody>
                {budgetData.map((item, index) => (
                  <React.Fragment key={index}>
                    <tr className="border-b border-border hover:bg-success/10 transition-colors cursor-pointer">
                      <td className="py-4 px-6 font-semibold text-primary-foreground">
                        {item.category}
                      </td>
                      <td className="py-4 px-6 text-right font-mono text-primary-foreground">
                        {formatCurrency(item.budget)}
                      </td>
                      <td className="py-4 px-6 text-right font-mono font-semibold text-primary-foreground">
                        {formatCurrency(item.actual)}
                      </td>
                      <td
                        className={`py-4 px-6 text-right font-mono font-bold ${getVarianceColor(item.variance)}`}
                      >
                        {item.variance > 0 ? '+' : ''}
                        {formatCurrency(Math.abs(item.variance))}
                      </td>
                      <td
                        className={`py-4 px-6 text-right font-bold ${getVarianceColor(item.variance)}`}
                      >
                        {item.variancePercent > 0 ? '+' : ''}
                        {isNaN(item.variancePercent)
                          ? '0.0'
                          : item.variancePercent.toFixed(1)}
                        %
                      </td>
                      <td className="py-4 px-6 text-center">
                        <span
                          className={`px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(item.status)}`}
                        >
                          {item.status === 'under'
                            ? 'Under Budget'
                            : item.status === 'over'
                              ? 'Over Budget'
                              : 'On Track'}
                        </span>
                      </td>
                    </tr>
                    {item.explanation && (
                      <tr className="bg-muted">
                        <td
                          colSpan={6}
                          className="py-3 px-6 pl-12 text-sm text-secondary-foreground italic"
                        >
                          <strong>Analysis:</strong> {item.explanation}
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Visual Variance Chart */}
      <Card className="border border-border shadow-sm overflow-hidden mb-8">
        <CardHeader className="bg-brand-primary text-white px-6 py-4">
          <CardTitle className="text-base font-semibold">
            Budget vs Actual Comparison
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {budgetData.map((item, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-primary-foreground">
                    {item.category}
                  </span>
                  <span className="text-sm text-secondary-foreground">
                    {formatCurrency(item.actual)} /{' '}
                    {formatCurrency(item.budget)}
                  </span>
                </div>
                <div className="relative h-8 bg-muted rounded-lg overflow-hidden">
                  {/* Budget bar */}
                  <div className="absolute inset-0 bg-muted-foreground/20 opacity-50"></div>
                  {/* Actual bar */}
                  <div
                    className={`absolute top-0 left-0 h-full rounded-lg transition-all duration-500 ${
                      item.variance > 0 ? 'bg-destructive' : 'bg-success'
                    }`}
                    style={{
                      width: `${Math.min((item.actual / item.budget) * 100, 100)}%`,
                    }}
                  ></div>
                  {/* Variance indicator */}
                  {item.variance !== 0 && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-xs font-bold text-white">
                        {item.variancePercent > 0 ? '+' : ''}
                        {isNaN(item.variancePercent)
                          ? '0.0'
                          : item.variancePercent.toFixed(1)}
                        %
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Insights */}
      <Card className="bg-gradient-to-br from-brand-primary to-ai-dark-blue text-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <span className="text-2xl">⚬</span>
            Variance Analysis Insights
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white/10 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Largest Variance</h4>
              <p className="text-white/90 text-sm">
                Travel & Entertainment shows the highest variance at +44.6% over
                budget (+$2,230). This was driven by unexpected client visits
                and conference attendance.
              </p>
            </div>
            <div className="bg-white/10 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Best Performance</h4>
              <p className="text-white/90 text-sm">
                Sales & Marketing came in 10% under budget (-$2,000) due to
                delayed campaign launch. This creates opportunity for additional
                Q2 marketing investment.
              </p>
            </div>
            <div className="bg-white/10 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Overall Trend</h4>
              <p className="text-white/90 text-sm">
                Total spend is tracking slightly over budget (+
                {isNaN(totalVariancePercent)
                  ? '0.0'
                  : totalVariancePercent.toFixed(1)}
                %), primarily due to operational and travel overruns. Most
                categories remain well-controlled.
              </p>
            </div>
            <div className="bg-white/10 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Recommendations</h4>
              <p className="text-white/90 text-sm">
                Consider implementing pre-approval for travel expenses over $500
                and reallocating unused marketing budget to offset operational
                overruns.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BudgetVariancePage;
