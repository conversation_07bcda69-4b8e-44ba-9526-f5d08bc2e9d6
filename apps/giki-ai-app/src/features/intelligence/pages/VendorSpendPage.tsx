/**
 * Vendor Spend Analysis Page - Financial Intelligence
 * Comprehensive vendor analysis with spending patterns and insights
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';

interface VendorData {
  vendor: string;
  totalSpend: number;
  transactionCount: number;
  averageTransaction: number;
  category: string;
  lastPayment: string;
  paymentTerms: string;
  trend: 'up' | 'down' | 'stable';
  trendPercent: number;
}

export const VendorSpendPage: React.FC = () => {
  const [_selectedPeriod, _setSelectedPeriod] = useState('2025-03');
  const [isLoading, setIsLoading] = useState(true);

  const vendorData: VendorData[] = [
    {
      vendor: 'Microsoft Corporation',
      totalSpend: 12500,
      transactionCount: 3,
      averageTransaction: 4167,
      category: 'Software & Tools',
      lastPayment: '2025-03-15',
      paymentTerms: 'Net 30',
      trend: 'up',
      trendPercent: 8.5,
    },
    {
      vendor: 'Amazon Web Services',
      totalSpend: 8900,
      transactionCount: 12,
      averageTransaction: 742,
      category: 'Infrastructure',
      lastPayment: '2025-03-20',
      paymentTerms: 'Net 15',
      trend: 'up',
      trendPercent: 15.2,
    },
    {
      vendor: 'Office Depot',
      totalSpend: 6750,
      transactionCount: 8,
      averageTransaction: 844,
      category: 'Office Supplies',
      lastPayment: '2025-03-18',
      paymentTerms: 'Net 30',
      trend: 'down',
      trendPercent: -12.3,
    },
    {
      vendor: 'FedEx Corporation',
      totalSpend: 4200,
      transactionCount: 24,
      averageTransaction: 175,
      category: 'Shipping',
      lastPayment: '2025-03-22',
      paymentTerms: 'Net 15',
      trend: 'stable',
      trendPercent: 2.1,
    },
    {
      vendor: 'Salesforce Inc',
      totalSpend: 3800,
      transactionCount: 1,
      averageTransaction: 3800,
      category: 'Software & Tools',
      lastPayment: '2025-03-01',
      paymentTerms: 'Net 30',
      trend: 'stable',
      trendPercent: 0.0,
    },
  ];

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);

  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      case 'stable':
        return '→';
      default:
        return '→';
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-error';
      case 'down':
        return 'text-success';
      case 'stable':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  const totalSpend = vendorData.reduce(
    (sum, vendor) => sum + vendor.totalSpend,
    0,
  );
  const totalTransactions = vendorData.reduce(
    (sum, vendor) => sum + vendor.transactionCount,
    0,
  );
  const averageVendorSpend = totalSpend / vendorData.length;

  if (isLoading) {
    return (
      <div className="p-8 space-y-6 animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
        <div className="grid grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-32 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-responsive-lg bg-white min-h-screen">
      {/* Professional Header - Enhanced Design */}
      <div className="mb-8">
        <div className="layout-mobile-stack">
          <div>
            <h1 className="text-responsive-3xl font-semibold text-gray-700 mb-2">
              Vendor Spend Analysis
            </h1>
            <p className="text-gray-500 text-base">
              Comprehensive vendor analysis with spending patterns and payment
              insights
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              className="btn-professional border-brand-primary text-brand-primary hover:bg-brand-primary/10"
            >
              Export Analysis
            </Button>
            <Button className="btn-professional bg-brand-primary hover:bg-brand-primary-hover text-white">
              Vendor Report
            </Button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="responsive-grid-4 mb-8">
        <Card className="card-elevated">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-500 uppercase tracking-wide">
              Total Vendor Spend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="metric-number-professional">
              {formatCurrency(totalSpend)}
            </div>
            <div className="text-xs text-gray-400">March 2025</div>
          </CardContent>
        </Card>

        <Card className="card-elevated">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-500 uppercase tracking-wide">
              Active Vendors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="metric-number-professional">
              {vendorData.length}
            </div>
            <div className="text-xs text-gray-400">with transactions</div>
          </CardContent>
        </Card>

        <Card className="card-elevated">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-500 uppercase tracking-wide">
              Total Transactions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="metric-number-professional">
              {totalTransactions}
            </div>
            <div className="text-xs text-gray-400">vendor payments</div>
          </CardContent>
        </Card>

        <Card className="card-elevated">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-500 uppercase tracking-wide">
              Avg Vendor Spend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="metric-number-professional">
              {formatCurrency(averageVendorSpend)}
            </div>
            <div className="text-xs text-gray-400">per vendor</div>
          </CardContent>
        </Card>
      </div>

      {/* Vendor Analysis Table */}
      <Card className="card-elevated overflow-hidden mb-8">
        <CardHeader className="bg-brand-primary text-white px-6 py-4">
          <CardTitle className="text-base font-semibold">
            Vendor Spend Analysis - {_selectedPeriod}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="table-responsive">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="text-left py-4 px-6 font-semibold text-gray-700">
                    Vendor
                  </th>
                  <th className="text-right py-4 px-6 font-semibold text-gray-700">
                    Total Spend
                  </th>
                  <th className="text-center py-4 px-6 font-semibold text-gray-700">
                    Transactions
                  </th>
                  <th className="text-right py-4 px-6 font-semibold text-gray-700">
                    Avg Transaction
                  </th>
                  <th className="text-left py-4 px-6 font-semibold text-gray-700">
                    Category
                  </th>
                  <th className="text-center py-4 px-6 font-semibold text-gray-700">
                    Last Payment
                  </th>
                  <th className="text-center py-4 px-6 font-semibold text-gray-700">
                    Trend
                  </th>
                </tr>
              </thead>
              <tbody>
                {vendorData.map((vendor, index) => (
                  <tr
                    key={index}
                    className="border-b border-gray-200 hover:bg-success/10 transition-all duration-200 cursor-pointer hover:transform hover:scale-[1.01]"
                  >
                    <td className="py-4 px-6">
                      <div>
                        <div className="font-semibold text-gray-900">
                          {vendor.vendor}
                        </div>
                        <div className="text-sm text-gray-500">
                          {vendor.paymentTerms}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-right font-mono font-bold text-gray-900">
                      {formatCurrency(vendor.totalSpend)}
                    </td>
                    <td className="py-4 px-6 text-center font-mono text-gray-700">
                      {vendor.transactionCount}
                    </td>
                    <td className="py-4 px-6 text-right font-mono text-gray-700">
                      {formatCurrency(vendor.averageTransaction)}
                    </td>
                    <td className="py-4 px-6 text-gray-700">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        {vendor.category}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-center text-gray-700">
                      {formatDate(vendor.lastPayment)}
                    </td>
                    <td className="py-4 px-6 text-center">
                      <div
                        className={`flex items-center justify-center gap-1 ${getTrendColor(vendor.trend)}`}
                      >
                        <span className="text-lg">
                          {getTrendIcon(vendor.trend)}
                        </span>
                        <span className="font-semibold text-sm">
                          {vendor.trendPercent > 0 ? '+' : ''}
                          {vendor.trendPercent.toFixed(1)}%
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Vendor Spend Distribution */}
      <Card className="card-elevated overflow-hidden mb-8">
        <CardHeader className="bg-brand-primary text-white px-6 py-4">
          <CardTitle className="text-base font-semibold">
            Vendor Spend Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {vendorData.map((vendor, index) => {
              const percentage = (vendor.totalSpend / totalSpend) * 100;
              return (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-gray-900">
                      {vendor.vendor}
                    </span>
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-gray-600">
                        {percentage.toFixed(1)}%
                      </span>
                      <span className="font-mono font-semibold text-gray-900">
                        {formatCurrency(vendor.totalSpend)}
                      </span>
                    </div>
                  </div>
                  <div className="relative h-6 bg-gray-200 rounded-lg overflow-hidden">
                    <div
                      className="absolute top-0 left-0 h-full bg-brand-primary rounded-lg transition-all duration-500"
                      style={{ width: `${percentage}%` }}
                    ></div>
                    <div className="absolute inset-0 flex items-center justify-start pl-3">
                      <span className="text-xs font-medium text-white">
                        {vendor.transactionCount} transactions
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Payment Terms Analysis */}
      <div className="responsive-grid-2 mb-8">
        <Card className="border border-gray-200 shadow-sm overflow-hidden">
          <CardHeader className="bg-brand-primary text-white px-6 py-4">
            <CardTitle className="text-base font-semibold">
              Payment Terms Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from(new Set(vendorData.map((v) => v.paymentTerms))).map(
                (terms, index) => {
                  const vendorsWithTerms = vendorData.filter(
                    (v) => v.paymentTerms === terms,
                  );
                  const totalSpendForTerms = vendorsWithTerms.reduce(
                    (sum, v) => sum + v.totalSpend,
                    0,
                  );
                  const percentage = (totalSpendForTerms / totalSpend) * 100;

                  return (
                    <div
                      key={index}
                      className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                    >
                      <div>
                        <div className="font-semibold text-gray-900">
                          {terms}
                        </div>
                        <div className="text-sm text-gray-600">
                          {vendorsWithTerms.length} vendor
                          {vendorsWithTerms.length !== 1 ? 's' : ''}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-mono font-bold text-gray-900">
                          {formatCurrency(totalSpendForTerms)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {percentage.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  );
                },
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-200 shadow-sm overflow-hidden">
          <CardHeader className="bg-brand-primary text-white px-6 py-4">
            <CardTitle className="text-base font-semibold">
              Category Spending
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from(new Set(vendorData.map((v) => v.category))).map(
                (category, index) => {
                  const vendorsInCategory = vendorData.filter(
                    (v) => v.category === category,
                  );
                  const totalSpendForCategory = vendorsInCategory.reduce(
                    (sum, v) => sum + v.totalSpend,
                    0,
                  );
                  const percentage = (totalSpendForCategory / totalSpend) * 100;

                  return (
                    <div
                      key={index}
                      className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                    >
                      <div>
                        <div className="font-semibold text-gray-900">
                          {category}
                        </div>
                        <div className="text-sm text-gray-600">
                          {vendorsInCategory.length} vendor
                          {vendorsInCategory.length !== 1 ? 's' : ''}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-mono font-bold text-gray-900">
                          {formatCurrency(totalSpendForCategory)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {percentage.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  );
                },
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Insights */}
      <Card className="bg-gradient-to-br from-brand-primary to-ai-dark-blue text-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <span className="text-2xl">⚬</span>
            Vendor Spend Insights
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white/10 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Top Vendor</h4>
              <p className="text-white/90 text-sm">
                Microsoft Corporation is your largest vendor at{' '}
                {formatCurrency(vendorData[0].totalSpend)}(
                {((vendorData[0].totalSpend / totalSpend) * 100).toFixed(1)}% of
                total vendor spend), with spending trending up 8.5% this month.
              </p>
            </div>
            <div className="bg-white/10 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Payment Terms</h4>
              <p className="text-white/90 text-sm">
                Most vendors offer Net 30 terms, providing good cash flow
                flexibility. Consider negotiating extended terms with
                high-volume vendors for better cash management.
              </p>
            </div>
            <div className="bg-white/10 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Spending Trends</h4>
              <p className="text-white/90 text-sm">
                AWS spending increased 15.2% this month, likely due to
                infrastructure scaling. Office Depot spending decreased 12.3%,
                indicating improved procurement efficiency.
              </p>
            </div>
            <div className="bg-white/10 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Optimization Opportunities</h4>
              <p className="text-white/90 text-sm">
                Consider consolidating similar vendors in the Software & Tools
                category to negotiate volume discounts and simplify vendor
                management processes.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorSpendPage;
