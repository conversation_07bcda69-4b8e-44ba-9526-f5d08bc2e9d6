/**
 * Knowledge Hub Feature Types
 *
 * Type definitions for AI knowledge and entity management.
 */

export interface Entity {
  id: string;
  name: string;
  type: 'person' | 'organization' | 'location' | 'product' | 'concept';
  description?: string;
  confidence: number;
  metadata?: Record<string, unknown>;
  created_at: Date;
  updated_at: Date;
}

export interface EntityRelationship {
  id: string;
  source_entity_id: string;
  target_entity_id: string;
  relationship_type: string;
  confidence: number;
  metadata?: Record<string, unknown>;
}

export interface KnowledgeEntry {
  id: string;
  title: string;
  content: string;
  type: 'fact' | 'rule' | 'pattern' | 'example';
  entities: string[];
  tags: string[];
  source?: string;
  confidence: number;
  created_at: Date;
  updated_at: Date;
}

export interface RAGCorpusEntry {
  id: string;
  content: string;
  metadata: {
    source: string;
    chunk_index: number;
    total_chunks: number;
    embedding_model: string;
    last_updated: Date;
  };
  embeddings?: number[];
}

export interface SearchQuery {
  query: string;
  filters?: {
    entity_types?: string[];
    confidence_min?: number;
    date_range?: {
      start: Date;
      end: Date;
    };
  };
  limit?: number;
}

export interface SearchResult {
  items: (Entity | KnowledgeEntry)[];
  total: number;
  took_ms: number;
}
