/**
 * Agent Hook
 *
 * Custom hook for managing AI agent interactions.
 */

import { useState, useCallback } from 'react';
import { unifiedAIService } from '@/shared/services/ai/UnifiedAIService';

export interface AgentMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export interface AgentSession {
  id: string;
  messages: AgentMessage[];
  isActive: boolean;
  createdAt: Date;
}

export interface UseAgentReturn {
  session: AgentSession | null;
  isLoading: boolean;
  error: string | null;
  sendMessage: (content: string) => Promise<void>;
  startNewSession: () => void;
  endSession: () => void;
}

export const useAgent = (): UseAgentReturn => {
  const [session, setSession] = useState<AgentSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(
    async (content: string): Promise<void> => {
      if (!session) return;

      setIsLoading(true);
      setError(null);

      const userMessage: AgentMessage = {
        id: Math.random().toString(36).substr(2, 9),
        role: 'user',
        content,
        timestamp: new Date(),
      };

      setSession((prev) =>
        prev
          ? {
              ...prev,
              messages: [...prev.messages, userMessage],
            }
          : null,
      );

      try {
        // Use unified AI service
        const response = await unifiedAIService.processConversation({
          message: content,
          session_id: session.id
        });

        const assistantMessage: AgentMessage = {
          id: Math.random().toString(36).substr(2, 9),
          role: 'assistant',
          content: response.message,
          timestamp: new Date(),
        };

        setSession((prev) =>
          prev
            ? {
                ...prev,
                messages: [...prev.messages, assistantMessage],
              }
            : null,
        );
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to send message';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [session],
  );

  const startNewSession = useCallback(() => {
    const newSession: AgentSession = {
      id: Math.random().toString(36).substr(2, 9),
      messages: [],
      isActive: true,
      createdAt: new Date(),
    };
    setSession(newSession);
    setError(null);
  }, []);

  const endSession = useCallback(() => {
    setSession((prev) => (prev ? { ...prev, isActive: false } : null));
  }, []);

  return {
    session,
    isLoading,
    error,
    sendMessage,
    startNewSession,
    endSession,
  };
};
