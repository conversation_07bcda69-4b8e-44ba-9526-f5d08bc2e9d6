/**
 * Intelligence Feature Barrel Exports
 *
 * This file provides clean imports for all AI/intelligence-related
 * components, hooks, and services.
 */

// Components
export { default as EntityGrid } from './components/EntityGrid';
export { default as EntityTable } from './components/EntityTable';
export { default as EntityDetailSheet } from './components/EntityDetailSheet';
export { default as RagCorpusManagement } from './components/RagCorpusManagement';

// Pages
export { default as KnowledgeHubPage } from './pages/KnowledgeHubPage';
export { default as RAGCorpusManagementPage } from './pages/RAGCorpusManagementPage';

// Services
// Note: agentService and chatService are deprecated
// Use UnifiedAIService from @/shared/services/ai/UnifiedAIService instead

// Types
export * from './types/knowledgehub';

// Hooks
export * from './hooks/useAgent';
