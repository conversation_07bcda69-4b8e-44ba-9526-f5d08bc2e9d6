/**
 * ADK Agent Service
 *
 * Frontend service for interacting with backend ADK (Agent Development Kit) capabilities.
 * Provides agent discovery, coordination, memory management, and A2A protocol support.
 */

import { logger } from '@/shared/utils/errorHandling';

interface ADKAgent {
  id: string;
  name: string;
  type: string;
  status: 'available' | 'busy' | 'offline' | 'error';
  capabilities: string[];
  currentTask?: string;
  lastActivity: string;
  memorySize: number;
  toolsAvailable: string[];
}

interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
}

interface Artifact {
  id: string;
  type: string;
  data: unknown;
  metadata?: Record<string, unknown>;
}

interface AgentMemoryContext {
  conversationHistory: ConversationMessage[];
  userPreferences: Record<string, unknown>;
  contextData: Record<string, unknown>;
  artifacts: Artifact[];
}

interface AgentTransferRequest {
  fromAgentId: string;
  toAgentId: string;
  context: AgentMemoryContext;
  reason: string;
  preserveMemory?: boolean;
}

interface ADKToolInvocation {
  toolName: string;
  parameters: Record<string, unknown>;
  agentId: string;
  sessionId?: string;
}

interface AgentSession {
  id: string;
  agentId: string;
  conversationId: string;
  memoryContext: AgentMemoryContext;
  startTime: string;
  lastInteraction: string;
  isActive: boolean;
}

class ADKAgentService {
  private baseUrl: string;
  private currentSession: AgentSession | null = null;
  private networkStatus: 'online' | 'offline' | 'unknown' = 'unknown';
  private lastNetworkCheck: number = 0;

  constructor() {
    this.baseUrl =
      process?.env?.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
    this.initializeNetworkStatus();
  }

  /**
   * Initialize network status monitoring
   */
  private initializeNetworkStatus(): void {
    // Check initial network status
    this.checkNetworkStatus();

    // Listen for network status changes
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.networkStatus = 'online';
        logger.info('ADKAgentService: Network status changed to online', 'adkAgentService');
      });
      
      window.addEventListener('offline', () => {
        this.networkStatus = 'offline';
        logger.warn('ADKAgentService: Network status changed to offline', 'adkAgentService');
      });
    }
  }

  /**
   * Check current network status
   */
  private async checkNetworkStatus(): Promise<boolean> {
    // Rate limit network checks to once per 30 seconds
    const now = Date.now();
    if (now - this.lastNetworkCheck < 30000) {
      return this.networkStatus === 'online';
    }

    try {
      const response = await fetch(`${this.baseUrl.replace('/api/v1', '')}/health`, {
        method: 'HEAD',
        cache: 'no-cache',
      });
      
      this.networkStatus = response.ok ? 'online' : 'offline';
      this.lastNetworkCheck = now;
      
      return this.networkStatus === 'online';
    } catch (error) {
      this.networkStatus = 'offline';
      this.lastNetworkCheck = now;
      return false;
    }
  }

  /**
   * Get current network status
   */
  getNetworkStatus(): 'online' | 'offline' | 'unknown' {
    return this.networkStatus;
  }

  /**
   * Discover available ADK agents in the system
   */
  async discoverAgents(): Promise<ADKAgent[]> {
    try {
      // Check network status first
      const isOnline = await this.checkNetworkStatus();
      if (!isOnline) {
        throw new Error('ADK agent service is offline. Please check your network connection.');
      }

      logger.info('ADKAgentService: Discovering available agents', 'adkAgentService');

      const response = await fetch(`${this.baseUrl}/adk/agents/discover`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('You do not have permission to access ADK agents.');
        }
        if (response.status === 404) {
          throw new Error('ADK agent discovery endpoint not found. Feature may not be available.');
        }
        if (response.status >= 500) {
          throw new Error('ADK agent service is experiencing issues. Please try again later.');
        }
        throw new Error(`Agent discovery failed: ${response.statusText}`);
      }

      const agents = (await response.json()) as ADKAgent[];
      logger.info('ADKAgentService: Successfully discovered agents', 'adkAgentService', {
        agentCount: agents.length,
      });

      return agents;
    } catch (error) {
      logger.error('ADKAgentService: Failed to discover agents', 'adkAgentService', error as Error);
      
      // Provide meaningful error message based on error type
      if (error instanceof Error && error.message.includes('Failed to fetch')) {
        throw new Error('ADK agent service is unavailable. Please check your network connection and try again.');
      }
      
      // Re-throw user-friendly errors as-is
      if (error instanceof Error && (
        error.message.includes('Authentication required') ||
        error.message.includes('permission') ||
        error.message.includes('not found') ||
        error.message.includes('experiencing issues') ||
        error.message.includes('offline')
      )) {
        throw error;
      }
      
      throw new Error(`Failed to discover ADK agents: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Start a new agent session with memory preloading
   */
  async startAgentSession(
    agentId: string,
    preloadMemory: boolean = true,
  ): Promise<AgentSession> {
    try {
      const response = await fetch(
        `${this.baseUrl}/adk/agents/${agentId}/sessions`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            preloadMemory,
            contextSize: 'standard',
          }),
        },
      );

      if (!response.ok) {
        throw new Error(
          `Failed to start agent session: ${response.statusText}`,
        );
      }

      const session = (await response.json()) as AgentSession;
      this.currentSession = session;
      return session;
    } catch (error) {
      // Failed to start agent session - error will be rethrown
      throw error;
    }
  }

  /**
   * Transfer conversation to another agent using A2A protocol
   */
  async transferToAgent(
    transferRequest: AgentTransferRequest,
  ): Promise<AgentSession> {
    try {
      const response = await fetch(`${this.baseUrl}/adk/agents/transfer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(transferRequest),
      });

      if (!response.ok) {
        throw new Error(`Agent transfer failed: ${response.statusText}`);
      }

      const newSession = (await response.json()) as AgentSession;
      this.currentSession = newSession;
      return newSession;
    } catch (error) {
      // Failed to transfer to agent - error will be rethrown
      throw error;
    }
  }

  /**
   * Load persistent memory for an agent
   */
  async loadAgentMemory(agentId: string): Promise<AgentMemoryContext> {
    try {
      const response = await fetch(
        `${this.baseUrl}/adk/agents/${agentId}/memory`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to load agent memory: ${response.statusText}`);
      }

      return (await response.json()) as AgentMemoryContext;
    } catch (error) {
      // Failed to load agent memory - error will be rethrown
      throw error;
    }
  }

  /**
   * Preload memory context for faster agent responses
   */
  async preloadMemory(
    agentId: string,
    contextData: Record<string, unknown>,
  ): Promise<void> {
    try {
      const response = await fetch(
        `${this.baseUrl}/adk/agents/${agentId}/memory/preload`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({ contextData }),
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to preload memory: ${response.statusText}`);
      }
    } catch (error) {
      // Failed to preload memory - error will be rethrown
      throw error;
    }
  }

  /**
   * Invoke advanced ADK tools (load_artifacts, openapi_tool, etc.)
   */
  async invokeADKTool(invocation: ADKToolInvocation): Promise<unknown> {
    try {
      const response = await fetch(`${this.baseUrl}/adk/tools/invoke`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(invocation),
      });

      if (!response.ok) {
        throw new Error(`Tool invocation failed: ${response.statusText}`);
      }

      return (await response.json()) as unknown;
    } catch (error) {
      // Failed to invoke ADK tool - error will be rethrown
      throw error;
    }
  }

  /**
   * Get real-time agent network status
   */
  async getAgentNetworkStatus(): Promise<{
    totalAgents: number;
    availableAgents: number;
    busyAgents: number;
    offlineAgents: number;
    networkHealth: 'healthy' | 'degraded' | 'offline';
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/adk/network/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get network status: ${response.statusText}`);
      }

      return (await response.json()) as {
        totalAgents: number;
        availableAgents: number;
        busyAgents: number;
        offlineAgents: number;
        networkHealth: 'healthy' | 'degraded' | 'offline';
      };
    } catch (error) {
      // Failed to get agent network status - returning offline status
      return {
        totalAgents: 0,
        availableAgents: 0,
        busyAgents: 0,
        offlineAgents: 0,
        networkHealth: 'offline',
      };
    }
  }

  /**
   * Load artifacts using ADK load_artifacts tool
   */
  async loadArtifacts(artifactIds: string[]): Promise<Artifact[]> {
    const result = await this.invokeADKTool({
      toolName: 'load_artifacts',
      parameters: { artifact_ids: artifactIds },
      agentId: this.currentSession?.agentId || 'system',
    });
    return result as Artifact[];
  }

  /**
   * Search knowledge base using Vertex AI search tool
   */
  async searchKnowledge(query: string): Promise<unknown[]> {
    const result = await this.invokeADKTool({
      toolName: 'vertex_ai_search_tool',
      parameters: {
        query,
        data_store_id: 'financial-knowledge-base',
      },
      agentId: this.currentSession?.agentId || 'system',
    });
    return result as unknown[];
  }

  /**
   * Use Google Search tool for real-time data
   */
  async googleSearch(query: string): Promise<unknown[]> {
    const result = await this.invokeADKTool({
      toolName: 'google_search_tool',
      parameters: { query },
      agentId: this.currentSession?.agentId || 'system',
    });
    return result as unknown[];
  }

  /**
   * Get user choice using interactive ADK tool
   */
  async getUserChoice(prompt: string, options: string[]): Promise<string> {
    const result = await this.invokeADKTool({
      toolName: 'get_user_choice',
      parameters: { prompt, options },
      agentId: this.currentSession?.agentId || 'system',
    });
    return (result as { choice: string }).choice;
  }


  /**
   * Get current active session
   */
  getCurrentSession(): AgentSession | null {
    return this.currentSession;
  }

  /**
   * End current agent session
   */
  async endSession(): Promise<void> {
    if (!this.currentSession) return;

    try {
      await fetch(
        `${this.baseUrl}/adk/sessions/${this?.currentSession?.id}/end`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        },
      );
    } catch (error) {
      // Failed to end session - cleaning up anyway
    } finally {
      this.currentSession = null;
    }
  }
}

// Export singleton instance
export const adkAgentService = new ADKAgentService();
export default adkAgentService;
