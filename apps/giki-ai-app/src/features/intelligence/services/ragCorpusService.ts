/**
 * RAG Corpus Service
 *
 * Manages RAG corpus operations and categorization functionality.
 * Updated to match backend API structure from archived RAG corpus router.
 */

import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError } from '@/shared/utils/errorHandling';

export interface RAGCorpus {
  id: string;
  display_name: string;
  status: RAGCorpusStatus;
  tenant_id: number;
  created_at: string | null;
  updated_at: string | null;
}

export enum RAGCorpusStatus {
  CREATED = 'created',
  IMPORTING = 'importing',
  ACTIVE = 'active',
  FAILED = 'failed',
  DELETED = 'deleted',
}

export interface RAGCorpusList {
  items: RAGCorpus[];
}

export interface CreateRAGCorpusRequest {
  display_name: string;
}

export interface CategorizationRequest {
  query_text: string;
  corpus_id?: string;
  top_k?: number;
}

export interface CategorizationResponse {
  categorization_result?: {
    suggested_category: string;
    confidence: number;
    reasoning?: string;
    similar_transactions?: number;
  };
}

/**
 * Create a new RAG corpus
 */
export const createRAGCorpus = async (
  displayName: string,
): Promise<RAGCorpus> => {
  try {
    const response = await apiClient.post<RAGCorpus>('/rag-corpus', {
      display_name: displayName,
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error, {
      context: 'createRAGCorpus',
      defaultMessage: 'Failed to create RAG corpus.',
    });
  }
};

/**
 * List all RAG corpora
 */
export const listRAGCorpora = async (): Promise<RAGCorpusList> => {
  try {
    const response = await apiClient.get<RAGCorpusList>('/rag-corpus');
    return response.data;
  } catch (error) {
    throw handleApiError(error, {
      context: 'listRAGCorpora',
      defaultMessage: 'Failed to list RAG corpora.',
    });
  }
};

/**
 * Delete a RAG corpus
 */
export const deleteRAGCorpus = async (corpusId: string): Promise<void> => {
  try {
    await apiClient.delete(`/rag-corpus/${corpusId}`);
  } catch (error) {
    throw handleApiError(error, {
      context: 'deleteRAGCorpus',
      defaultMessage: 'Failed to delete RAG corpus.',
    });
  }
};

/**
 * Query a RAG corpus for categorization
 */
export const categorizeWithRAG = async (
  request: CategorizationRequest,
): Promise<CategorizationResponse> => {
  try {
    const response = await apiClient.post<CategorizationResponse>(
      `/rag-corpus/${request.corpus_id}/query`,
      {
        query: request.query_text,
        top_k: request.top_k || 5,
      },
    );
    return response.data;
  } catch (error) {
    throw handleApiError(error, {
      context: 'categorizeWithRAG',
      defaultMessage: 'Failed to categorize with RAG.',
    });
  }
};

/**
 * Get RAG corpus details
 */
export const getRAGCorpus = async (corpusId: string): Promise<RAGCorpus> => {
  try {
    const response = await apiClient.get<RAGCorpus>(`/rag-corpus/${corpusId}`);
    return response.data;
  } catch (error) {
    throw handleApiError(error, {
      context: 'getRAGCorpus',
      defaultMessage: 'Failed to get RAG corpus details.',
    });
  }
};

/**
 * Enrich a RAG corpus with transaction data
 */
export const enrichRAGCorpus = async (
  corpusId: string,
  options?: {
    incremental?: boolean;
    limit?: number;
    offset?: number;
  },
): Promise<{
  corpus_id: string;
  status: string;
  message: string;
}> => {
  try {
    const response = await apiClient.post<{
      corpus_id: string;
      status: string;
      message: string;
    }>(`/rag-corpus/${corpusId}/enrich`, {
      incremental: options?.incremental ?? true,
      limit: options?.limit ?? 1000,
      offset: options?.offset ?? 0,
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error, {
      context: 'enrichRAGCorpus',
      defaultMessage: 'Failed to enrich RAG corpus.',
    });
  }
};
