/**
 * GL Code Compliance Page - 45-Minute Structured Workflow
 * Comprehensive GL code compliance interface for M3 milestone
 * Integrates schema guidance, management, and validation
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';

import { Badge } from '@/shared/components/ui/badge';
import { ProgressBar } from '@/shared/components/ui/ProgressBar';
import {
  CheckCircle,
  AlertTriangle,
  Clock,
  Target,
  FileSpreadsheet,
  Settings,
  BookOpen,
  Upload,
  Download,
  Brain,
  Shield,
  Layers,
  TrendingUp,
  RefreshCw,
  Eye,
} from 'lucide-react';
import { SchemaGuidedFlow } from '@/features/onboarding/components/SchemaGuidedFlow';
import { GLCodeManagementSystem } from '@/features/categories/components/GLCodeManagementSystem';
import { useToast } from '@/shared/components/ui/use-toast';
import { useAuth } from '@/features/auth/hooks/useAuth';

interface ComplianceStep {
  id: string;
  title: string;
  description: string;
  estimatedTime: number;
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
  required: boolean;
}

interface ComplianceMetrics {
  overallScore: number;
  schemaCompliance: number;
  mappingAccuracy: number;
  validationSuccess: number;
  totalCategories: number;
  mappedCategories: number;
  validationErrors: number;
}

interface ComplianceWorkflow {
  currentStep: number;
  totalSteps: number;
  elapsedTime: number;
  estimatedCompletion: number;
  isCompleted: boolean;
  steps: ComplianceStep[];
}

export const GLCodeCompliancePage: React.FC = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  const [currentWorkflow, setCurrentWorkflow] = useState<
    'overview' | 'schema_setup' | 'management' | 'validation' | 'completion'
  >('overview');

  const [workflowState, setWorkflowState] = useState<ComplianceWorkflow>({
    currentStep: 0,
    totalSteps: 7,
    elapsedTime: 0,
    estimatedCompletion: 45,
    isCompleted: false,
    steps: [
      {
        id: 'setup_schema',
        title: 'Schema Setup',
        description: 'Define GL code structure and upload chart of accounts',
        estimatedTime: 10,
        status: 'pending',
        required: true,
      },
      {
        id: 'category_mapping',
        title: 'Category Mapping',
        description: 'Map business categories to GL codes',
        estimatedTime: 8,
        status: 'pending',
        required: true,
      },
      {
        id: 'validation_rules',
        title: 'Validation Rules',
        description: 'Configure compliance and validation rules',
        estimatedTime: 5,
        status: 'pending',
        required: true,
      },
      {
        id: 'bulk_operations',
        title: 'Bulk Operations',
        description: 'Apply mappings to existing transactions',
        estimatedTime: 7,
        status: 'pending',
        required: false,
      },
      {
        id: 'testing_validation',
        title: 'Testing & Validation',
        description: 'Test schema with sample transactions',
        estimatedTime: 8,
        status: 'pending',
        required: true,
      },
      {
        id: 'reporting_setup',
        title: 'Reporting Setup',
        description: 'Configure compliance reports and exports',
        estimatedTime: 5,
        status: 'pending',
        required: false,
      },
      {
        id: 'final_review',
        title: 'Final Review',
        description: 'Review and activate compliance system',
        estimatedTime: 2,
        status: 'pending',
        required: true,
      },
    ],
  });

  const [complianceMetrics, setComplianceMetrics] = useState<ComplianceMetrics>(
    {
      overallScore: 0,
      schemaCompliance: 0,
      mappingAccuracy: 0,
      validationSuccess: 0,
      totalCategories: 0,
      mappedCategories: 0,
      validationErrors: 0,
    },
  );

  const [showSchemaFlow, setShowSchemaFlow] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Timer for workflow tracking
  useEffect(() => {
    const timer = setInterval(() => {
      setWorkflowState((prev) => ({
        ...prev,
        elapsedTime: prev.elapsedTime + 1,
      }));
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  const loadComplianceMetrics = useCallback(() => {
    try {
      // Simulate loading compliance metrics from backend
      // In real implementation, this would fetch from API
      setTimeout(() => {
        setComplianceMetrics({
          overallScore: 72,
          schemaCompliance: 85,
          mappingAccuracy: 78,
          validationSuccess: 65,
          totalCategories: 24,
          mappedCategories: 19,
          validationErrors: 3,
        });
      }, 1000);
    } catch (error) {
      console.error('Error loading compliance metrics:', error);
      toast({
        title: 'Error Loading Metrics',
        description: 'Failed to load compliance metrics. Please try again.',
        variant: 'destructive',
      });
    }
  }, [toast]);

  // Load initial compliance state
  useEffect(() => {
    loadComplianceMetrics();
  }, [loadComplianceMetrics]);

  const _startWorkflowStep = useCallback((_stepId: string) => {
    setWorkflowState((prev) => ({
      ...prev,
      steps: prev.steps.map((step) =>
        step.id === _stepId ? { ...step, status: 'in_progress' } : step,
      ),
    }));
  }, []);

  const completeWorkflowStep = useCallback(
    (stepId: string) => {
      setWorkflowState((prev) => {
        const updatedSteps = prev.steps.map((step) =>
          step.id === stepId ? { ...step, status: 'completed' } : step,
        );
        const completedCount = updatedSteps.filter(
          (s) => s.status === 'completed',
        ).length;

        return {
          ...prev,
          currentStep: completedCount,
          steps: updatedSteps,
          isCompleted: completedCount === prev.totalSteps,
        };
      });

      // Update compliance metrics when steps complete
      loadComplianceMetrics();
    },
    [loadComplianceMetrics],
  );

  const handleSchemaComplete = useCallback(
    (results: unknown) => {
     ('Schema setup completed:', results);
      completeWorkflowStep('setup_schema');
      setShowSchemaFlow(false);
      setCurrentWorkflow('management');

      toast({
        title: 'Schema Setup Complete',
        description: 'GL code schema has been successfully configured.',
      });
    },
    [completeWorkflowStep, toast],
  );

  const handleQuickSetup = useCallback(async () => {
    try {
      setIsProcessing(true);

      // Simulate quick setup process
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // Mark multiple steps as completed for quick setup
      ['setup_schema', 'category_mapping', 'validation_rules'].forEach(
        (stepId) => {
          completeWorkflowStep(stepId);
        },
      );

      setCurrentWorkflow('validation');

      toast({
        title: 'Quick Setup Complete',
        description: 'Basic GL code compliance has been configured.',
      });
    } catch {
      toast({
        title: 'Setup Failed',
        description:
          'Quick setup encountered an error. Please try manual setup.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [completeWorkflowStep, toast]);

  const generateComplianceReport = useCallback(async () => {
    try {
      setIsProcessing(true);

      // Simulate report generation
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Create downloadable report
      const reportData = {
        timestamp: new Date().toISOString(),
        metrics: complianceMetrics,
        workflow: workflowState,
        user: user?.email,
      };

      const blob = new Blob([JSON.stringify(reportData, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `gl-compliance-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: 'Report Generated',
        description: 'Compliance report has been downloaded.',
      });
    } catch {
      toast({
        title: 'Report Generation Failed',
        description: 'Failed to generate compliance report.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [complianceMetrics, workflowState, user, toast]);

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getStepStatusIcon = (status: ComplianceStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-success" />;
      case 'in_progress':
        return <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />;
      case 'skipped':
        return <Eye className="w-5 h-5 text-gray-400" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getComplianceScoreColor = (score: number) => {
    if (score >= 95) return 'text-success';
    if (score >= 80) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-error';
  };

  const renderOverviewStep = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <Shield
          size={64}
          className="mx-auto mb-6"
          style={{ color: 'var(--giki-primary)' }}
        />
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          GL Code Compliance Setup
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          45-minute structured workflow for complete GL code compliance
        </p>
      </div>

      {/* Current Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Overall Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className={`text-3xl font-bold font-mono ${getComplianceScoreColor(complianceMetrics.overallScore)}`}
            >
              {complianceMetrics.overallScore}%
            </div>
            <p className="text-sm text-gray-600 mt-1">M3 Target: 95%</p>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {complianceMetrics.mappedCategories}/
              {complianceMetrics.totalCategories}
            </div>
            <p className="text-sm text-gray-600 mt-1">Mapped categories</p>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Time Elapsed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {formatTime(workflowState.elapsedTime)}
            </div>
            <p className="text-sm text-gray-600 mt-1">of 45m estimated</p>
          </CardContent>
        </Card>

        <Card className="border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold font-mono text-gray-900">
              {workflowState.currentStep}/{workflowState.totalSteps}
            </div>
            <ProgressBar
              value={
                (workflowState.currentStep / workflowState.totalSteps) * 100
              }
              className="mt-2"
            />
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Target className="w-5 h-5" />
              Quick Setup (15 minutes)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-blue-700 mb-4">
              Automated setup using default GL code structure and industry best
              practices.
            </p>
            <div className="space-y-2 text-sm text-blue-600 mb-4">
              <div>• Standard chart of accounts</div>
              <div>• Common business categories</div>
              <div>• Basic validation rules</div>
            </div>
            <Button
              onClick={() => void handleQuickSetup()}
              disabled={isProcessing}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isProcessing ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Setting up...
                </>
              ) : (
                <>
                  <Target className="w-4 h-4 mr-2" />
                  Start Quick Setup
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <Settings className="w-5 h-5" />
              Custom Setup (45 minutes)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-green-700 mb-4">
              Comprehensive setup with your existing GL codes and custom
              categorization rules.
            </p>
            <div className="space-y-2 text-sm text-success mb-4">
              <div>• Import your chart of accounts</div>
              <div>• Custom category mapping</div>
              <div>• Advanced validation rules</div>
            </div>
            <Button
              onClick={() => setCurrentWorkflow('schema_setup')}
              className="w-full"
              style={{ backgroundColor: 'var(--giki-primary)' }}
            >
              <Layers className="w-4 h-4 mr-2" />
              Start Custom Setup
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Workflow Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5" />
            Compliance Workflow Steps
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {workflowState.steps.map((step, _index) => (
              <div
                key={step.id}
                className={`flex items-center justify-between p-4 rounded-lg border ${
                  step.status === 'completed'
                    ? 'bg-green-50 border-green-200'
                    : step.status === 'in_progress'
                      ? 'bg-blue-50 border-blue-200'
                      : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div className="flex items-center gap-3">
                  {getStepStatusIcon(step.status)}
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {step.title}
                    </h4>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Badge variant={step.required ? 'default' : 'secondary'}>
                    {step.required ? 'Required' : 'Optional'}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    {formatTime(step.estimatedTime)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderSchemaSetupStep = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Schema-Guided Setup
          </h2>
          <p className="text-gray-600">
            Configure your GL code structure and category mappings
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => setCurrentWorkflow('overview')}
        >
          ← Back to Overview
        </Button>
      </div>

      <SchemaGuidedFlow
        onComplete={handleSchemaComplete}
        onBack={() => setCurrentWorkflow('overview')}
      />
    </div>
  );

  const renderManagementStep = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            GL Code Management
          </h2>
          <p className="text-gray-600">
            Manage and validate your GL code mappings
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentWorkflow('overview')}
          >
            ← Back to Overview
          </Button>
          <Button
            onClick={() => setCurrentWorkflow('validation')}
            style={{ backgroundColor: 'var(--giki-primary)' }}
          >
            Continue to Validation →
          </Button>
        </div>
      </div>

      <GLCodeManagementSystem
        onHierarchyChange={(hierarchy) => {
          setComplianceMetrics((prev) => ({
            ...prev,
            totalCategories: hierarchy.totalCategories,
            mappedCategories: hierarchy.validationSummary.validMappings,
            schemaCompliance:
              (hierarchy.validationSummary.validMappings /
                hierarchy.totalCategories) *
              100,
          }));
        }}
        complianceMode={true}
        showAdvancedFeatures={true}
      />
    </div>
  );

  const renderValidationStep = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Compliance Validation
          </h2>
          <p className="text-gray-600">
            Review and validate your compliance setup
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentWorkflow('management')}
          >
            ← Back to Management
          </Button>
          <Button
            onClick={() => setCurrentWorkflow('completion')}
            style={{ backgroundColor: 'var(--giki-primary)' }}
          >
            Complete Setup →
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" style={{ color: 'var(--giki-primary)' }} />
              Compliance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Schema Compliance</span>
                <span
                  className={`font-bold ${getComplianceScoreColor(complianceMetrics.schemaCompliance)}`}
                >
                  {complianceMetrics.schemaCompliance}%
                </span>
              </div>
              <ProgressBar value={complianceMetrics.schemaCompliance} />

              <div className="flex items-center justify-between">
                <span className="font-medium">Mapping Accuracy</span>
                <span
                  className={`font-bold ${getComplianceScoreColor(complianceMetrics.mappingAccuracy)}`}
                >
                  {complianceMetrics.mappingAccuracy}%
                </span>
              </div>
              <ProgressBar value={complianceMetrics.mappingAccuracy} />

              <div className="flex items-center justify-between">
                <span className="font-medium">Validation Success</span>
                <span
                  className={`font-bold ${getComplianceScoreColor(complianceMetrics.validationSuccess)}`}
                >
                  {complianceMetrics.validationSuccess}%
                </span>
              </div>
              <ProgressBar value={complianceMetrics.validationSuccess} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              Validation Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {complianceMetrics.validationErrors > 0 ? (
                <>
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <span className="text-yellow-800">Unmapped Categories</span>
                    <Badge variant="destructive">
                      {complianceMetrics.validationErrors}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <span className="text-blue-800">Duplicate GL Codes</span>
                    <Badge variant="secondary">2</Badge>
                  </div>
                </>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="w-16 h-16 text-success mx-auto mb-4" />
                  <p className="text-green-700 font-medium">
                    No validation issues found
                  </p>
                  <p className="text-sm text-success">
                    Your setup is compliance-ready
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" style={{ color: 'var(--giki-primary)' }} />
            AI Validation Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-gray-600">
              Test your GL code setup with sample transactions to ensure
              accuracy.
            </p>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-semibold mb-2">Upload Test File</h4>
              <p className="text-gray-600 mb-4">
                Upload a sample transaction file to validate your setup
              </p>
              <input
                type="file"
                accept=".xlsx,.xls,.csv"
                className="hidden"
                id="validation-upload"
              />
              <label
                htmlFor="validation-upload"
                className="inline-block px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 cursor-pointer font-semibold"
              >
                Select Test File
              </label>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderCompletionStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle size={64} className="mx-auto mb-6 text-success" />
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          GL Code Compliance Complete!
        </h2>
        <p className="text-xl text-gray-600 mb-8">
          Your system is now configured for 95% GL code compliance
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="text-center">
          <CardContent className="p-6">
            <div
              className={`text-4xl font-bold mb-2 ${getComplianceScoreColor(complianceMetrics.overallScore)}`}
            >
              {complianceMetrics.overallScore}%
            </div>
            <p className="text-gray-600">Overall Compliance Score</p>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardContent className="p-6">
            <div className="text-4xl font-bold mb-2 text-success">
              {complianceMetrics.mappedCategories}
            </div>
            <p className="text-gray-600">Categories Mapped</p>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardContent className="p-6">
            <div
              className="text-4xl font-bold mb-2"
              style={{ color: 'var(--giki-primary)' }}
            >
              {formatTime(workflowState.elapsedTime)}
            </div>
            <p className="text-gray-600">Setup Time</p>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-center gap-4">
        <Button
          onClick={() => void generateComplianceReport()}
          disabled={isProcessing}
          variant="outline"
        >
          {isProcessing ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Download className="w-4 h-4 mr-2" />
          )}
          Download Report
        </Button>

        <Button
          onClick={() => setCurrentWorkflow('overview')}
          style={{ backgroundColor: 'var(--giki-primary)' }}
        >
          <FileSpreadsheet className="w-4 h-4 mr-2" />
          View Dashboard
        </Button>
      </div>

      <Card className="bg-green-50 border-green-200">
        <CardContent className="p-6">
          <h3 className="font-semibold text-green-800 mb-3">Next Steps:</h3>
          <ul className="space-y-2 text-green-700">
            <li>• Your AI categorization now follows GL code structure</li>
            <li>
              • New transactions will automatically map to correct GL codes
            </li>
            <li>
              • Monthly compliance reports are available in the Reports section
            </li>
            <li>• You can modify mappings anytime in the GL Code Management</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );

  const renderCurrentWorkflow = () => {
    switch (currentWorkflow) {
      case 'schema_setup':
        return renderSchemaSetupStep();
      case 'management':
        return renderManagementStep();
      case 'validation':
        return renderValidationStep();
      case 'completion':
        return renderCompletionStep();
      default:
        return renderOverviewStep();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {renderCurrentWorkflow()}
      </div>
    </div>
  );
};

export default GLCodeCompliancePage;
