import React, { useState, useEffect } from 'react';
import { Card } from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { Button } from '@/shared/components/ui/button';
import { Progress } from '@/shared/components/ui/progress';
import {
  TrendingUp,
  TrendingDown,
  Target,
  Zap,
  AlertTriangle,
  CheckCircle2,
  RefreshCcw,
} from 'lucide-react';
import { confidenceService, ConfidenceMetric } from '../services/confidenceService';
import { logger } from '@/shared/utils/errorHandling';


interface ConfidenceScoringDisplayProps {
  tenantId?: number;
  industry?: string;
  refreshInterval?: number; // in seconds
}

export const ConfidenceScoringDisplay: React.FC<
  ConfidenceScoringDisplayProps
> = ({
  tenantId: _tenantId,
  industry = 'general_business',
  refreshInterval = 30,
}) => {
  const [metrics, setMetrics] = useState<ConfidenceMetric[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [overallScore, setOverallScore] = useState<number>(0);

  // Real confidence metrics loading using backend APIs
  const fetchConfidenceMetrics = async () => {
    setIsLoading(true);

    try {
      logger.info('ConfidenceScoringDisplay: Fetching confidence metrics', 'ConfidenceScoringDisplay');

      // Get real confidence metrics from backend
      const [confidenceMetrics, overallScore] = await Promise.all([
        confidenceService.getConfidenceMetrics(),
        confidenceService.getOverallConfidenceScore(),
      ]);

      setMetrics(confidenceMetrics);
      setOverallScore(overallScore);
      setLastRefresh(new Date());

      logger.info('ConfidenceScoringDisplay: Metrics loaded successfully', 'ConfidenceScoringDisplay', {
        metricsCount: confidenceMetrics.length,
        overallScore,
      });

    } catch (error) {
      logger.error('ConfidenceScoringDisplay: Error fetching metrics', 'ConfidenceScoringDisplay', error as Error);
      
      // Set empty state instead of fake data
      setMetrics([]);
      setOverallScore(0);
    } finally {
      setLastRefresh(new Date());
      setIsLoading(false);
    }
  };

  // Auto-refresh functionality
  useEffect(() => {
    void fetchConfidenceMetrics();

    const interval = setInterval(() => {
      void fetchConfidenceMetrics();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-success bg-green-100';
    if (confidence >= 0.8) return 'text-blue-600 bg-blue-100';
    if (confidence >= 0.7) return 'text-yellow-600 bg-yellow-100';
    return 'text-error bg-red-100';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.9) return 'Excellent';
    if (confidence >= 0.8) return 'Good';
    if (confidence >= 0.7) return 'Fair';
    return 'Needs Review';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-3 h-3 text-success" />;
      case 'down':
        return <TrendingDown className="w-3 h-3 text-error" />;
      default:
        return <Target className="w-3 h-3 text-gray-600" />;
    }
  };

  return (
    <Card className="w-full border border-gray-200 shadow-sm">
      <div className="bg-gradient-to-r from-brand-primary to-[#1D372E] text-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            <h3 className="text-base font-semibold">
              Real-Time Confidence Scoring
            </h3>
            <Badge
              variant="outline"
              className="bg-white/10 text-white border-white/20"
            >
              M3 Giki Enhanced
            </Badge>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="text-sm font-medium">Overall Score</div>
              <div className="text-lg font-bold">
                {(overallScore * 100).toFixed(1)}%
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => void fetchConfidenceMetrics()}
              disabled={isLoading}
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
            >
              <RefreshCcw
                className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`}
              />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Overall Progress */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Industry Alignment Score (
              {industry.replace('_', ' ').toUpperCase()})
            </span>
            <span className="text-sm text-gray-500">
              {lastRefresh
                ? `Updated ${lastRefresh.toLocaleTimeString()}`
                : 'Never updated'}
            </span>
          </div>
          <Progress value={overallScore * 100} className="h-3" />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Needs Work</span>
            <span>Good</span>
            <span>Excellent</span>
          </div>
        </div>

        {/* Individual Metrics */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Zap className="w-4 h-4 text-yellow-500" />
            <span className="font-medium text-gray-900">
              Category Performance Metrics
            </span>
          </div>

          {metrics.map((metric, index) => (
            <div
              key={index}
              className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {metric.category}
                    </h4>
                    <p className="text-sm text-gray-500">
                      GL Code: {metric.glCode}
                    </p>
                  </div>
                  <Badge
                    className={`${getConfidenceColor(metric.confidence)} border-0`}
                  >
                    {getConfidenceLabel(metric.confidence)}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  {getTrendIcon(metric.trend)}
                  <span className="text-lg font-bold text-gray-900">
                    {(metric.confidence * 100).toFixed(0)}%
                  </span>
                </div>
              </div>

              {/* Detailed Breakdown */}
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Industry Match</span>
                    <span className="font-medium">
                      {(metric.industry_match * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress
                    value={metric.industry_match * 100}
                    className="h-1 mt-1"
                  />
                </div>
                <div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Template Align</span>
                    <span className="font-medium">
                      {(metric.template_alignment * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress
                    value={metric.template_alignment * 100}
                    className="h-1 mt-1"
                  />
                </div>
                <div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Historical</span>
                    <span className="font-medium">
                      {(metric.historical_accuracy * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress
                    value={metric.historical_accuracy * 100}
                    className="h-1 mt-1"
                  />
                </div>
              </div>

              {/* Warning for low confidence */}
              {metric.confidence < 0.8 && (
                <div className="flex items-center gap-2 mt-3 p-2 bg-yellow-50 rounded border border-yellow-200">
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    Consider reviewing this categorization for improved accuracy
                  </span>
                </div>
              )}

              {/* High confidence indicator */}
              {metric.confidence >= 0.9 && (
                <div className="flex items-center gap-2 mt-3 p-2 bg-green-50 rounded border border-green-200">
                  <CheckCircle2 className="w-4 h-4 text-success" />
                  <span className="text-sm text-green-800">
                    Highly accurate categorization - ready for automation
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Performance Summary */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600 mb-2">
            ⚬ M3 Giki Performance Summary
          </div>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">High Confidence:</span>
              <span className="ml-2 font-medium text-green-700">
                {metrics.filter((m) => m.confidence >= 0.9).length} categories
              </span>
            </div>
            <div>
              <span className="text-gray-600">Needs Review:</span>
              <span className="ml-2 font-medium text-yellow-700">
                {metrics.filter((m) => m.confidence < 0.8).length} categories
              </span>
            </div>
            <div>
              <span className="text-gray-600">Auto-refresh:</span>
              <span className="ml-2 font-medium text-blue-700">
                Every {refreshInterval}s
              </span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ConfidenceScoringDisplay;
