import React, { useState, useEffect } from 'react';
import { Card } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Badge } from '@/shared/components/ui/badge';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import {
  Loader2,
  Upload,
  Download,
  Save,
  AlertTriangle,
  CheckCircle2,
  GripVertical,
  Trash2,
  Plus,
  Target,
  Zap,
  FileSpreadsheet,
} from 'lucide-react';

interface GLMapping {
  id: string;
  category_id: number;
  category_name: string;
  category_path: string;
  gl_code: string;
  gl_account_name: string;
  gl_account_type: string;
  confidence: number;
  status: 'unchanged' | 'modified' | 'new' | 'deleted';
  validation_status: 'valid' | 'invalid' | 'warning' | 'pending';
  validation_message?: string;
}

interface BulkGLCodeManagerProps {
  tenantId?: number;
  industry?: string;
  onMappingsUpdate?: (mappings: GLMapping[]) => void;
}

export const BulkGLCodeManager: React.FC<BulkGLCodeManagerProps> = ({
  tenantId,
  industry = 'general_business',
  onMappingsUpdate,
}) => {
  const [mappings, setMappings] = useState<GLMapping[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [bulkAccountType, setBulkAccountType] = useState<string>('');
  const [searchFilter, setSearchFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Load initial mappings
  useEffect(() => {
    void loadMappings();
  }, [tenantId]);

  const loadMappings = () => {
    setIsLoading(true);
    try {
      // Mock data for demonstration - replace with actual API call
      const mockMappings: GLMapping[] = [
        {
          id: '1',
          category_id: 1,
          category_name: 'Office Supplies',
          category_path: 'Expenses > Office Supplies',
          gl_code: '6001',
          gl_account_name: 'Office Supplies Expense',
          gl_account_type: 'Expense',
          confidence: 0.95,
          status: 'unchanged',
          validation_status: 'valid',
        },
        {
          id: '2',
          category_id: 2,
          category_name: 'Travel Expenses',
          category_path: 'Expenses > Travel',
          gl_code: '6200',
          gl_account_name: 'Travel and Transportation',
          gl_account_type: 'Expense',
          confidence: 0.78,
          status: 'unchanged',
          validation_status: 'warning',
          validation_message: 'GL code exists but account name mismatch',
        },
        {
          id: '3',
          category_id: 3,
          category_name: 'Professional Services',
          category_path: 'Expenses > Professional Services',
          gl_code: '',
          gl_account_name: '',
          gl_account_type: 'Expense',
          confidence: 0.0,
          status: 'new',
          validation_status: 'pending',
          validation_message: 'Requires GL code assignment',
        },
        {
          id: '4',
          category_id: 4,
          category_name: 'Software Subscriptions',
          category_path: 'Expenses > Technology',
          gl_code: '6500',
          gl_account_name: 'Software and Technology',
          gl_account_type: 'Expense',
          confidence: 0.92,
          status: 'unchanged',
          validation_status: 'valid',
        },
      ];

      setMappings(mockMappings);
    } catch (error) {
      console.error('Failed to load GL mappings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const validateMappings = async () => {
    setIsValidating(true);
    try {
      // Validate each mapping
      const updatedMappings = await Promise.all(
        mappings.map((mapping) => {
          if (!mapping.gl_code) {
            return {
              ...mapping,
              validation_status: 'invalid' as const,
              validation_message: 'GL code is required',
            };
          }

          // Simulate validation API call
          const isValid = /^\d{4}$/.test(mapping.gl_code);
          return {
            ...mapping,
            validation_status: isValid
              ? ('valid' as const)
              : ('invalid' as const),
            validation_message: isValid ? undefined : 'Invalid GL code format',
          };
        }),
      );

      setMappings(updatedMappings);
    } catch (error) {
      console.error('Validation failed:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const saveMappings = async () => {
    setIsSaving(true);
    try {
      const _changedMappings = mappings.filter((m) => m.status !== 'unchanged');

      // Simulate save API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Mark all as unchanged after successful save
      const savedMappings = mappings.map((m) => ({
        ...m,
        status: 'unchanged' as const,
      }));

      setMappings(savedMappings);
      onMappingsUpdate?.(savedMappings);
    } catch (error) {
      console.error('Failed to save mappings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const updateMapping = (id: string, updates: Partial<GLMapping>) => {
    setMappings((prev) =>
      prev.map((mapping) =>
        mapping.id === id
          ? {
              ...mapping,
              ...updates,
              status:
                mapping.status === 'unchanged' ? 'modified' : mapping.status,
            }
          : mapping,
      ),
    );
  };

  const deleteMapping = (id: string) => {
    setMappings((prev) => prev.filter((m) => m.id !== id));
  };

  const addNewMapping = () => {
    const newMapping: GLMapping = {
      id: Date.now().toString(),
      category_id: 0,
      category_name: 'New Category',
      category_path: 'Expenses > New Category',
      gl_code: '',
      gl_account_name: '',
      gl_account_type: 'Expense',
      confidence: 0,
      status: 'new',
      validation_status: 'pending',
    };
    setMappings((prev) => [...prev, newMapping]);
  };

  const applyBulkAccountType = () => {
    if (!bulkAccountType) return;

    selectedRows.forEach((id) => {
      updateMapping(id, { gl_account_type: bulkAccountType });
    });

    setSelectedRows(new Set());
    setBulkAccountType('');
  };

  const handleDragStart = (e: React.DragEvent, id: string) => {
    setDraggedItem(id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();

    if (!draggedItem || draggedItem === targetId) return;

    const draggedIndex = mappings.findIndex((m) => m.id === draggedItem);
    const targetIndex = mappings.findIndex((m) => m.id === targetId);

    if (draggedIndex === -1 || targetIndex === -1) return;

    const newMappings = [...mappings];
    const [draggedMapping] = newMappings.splice(draggedIndex, 1);
    newMappings.splice(targetIndex, 0, draggedMapping);

    setMappings(newMappings);
    setDraggedItem(null);
  };

  const toggleRowSelection = (id: string) => {
    const newSelection = new Set(selectedRows);
    if (newSelection.has(id)) {
      newSelection.delete(id);
    } else {
      newSelection.add(id);
    }
    setSelectedRows(newSelection);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'modified':
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            Modified
          </Badge>
        );
      case 'new':
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            New
          </Badge>
        );
      case 'deleted':
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            Deleted
          </Badge>
        );
      default:
        return null;
    }
  };

  const getValidationIcon = (validation_status: string) => {
    switch (validation_status) {
      case 'valid':
        return <CheckCircle2 className="w-4 h-4 text-success" />;
      case 'invalid':
        return <AlertTriangle className="w-4 h-4 text-error" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Loader2 className="w-4 h-4 text-gray-400" />;
    }
  };

  const filteredMappings = mappings.filter((mapping) => {
    const matchesSearch =
      mapping.category_name
        .toLowerCase()
        .includes(searchFilter.toLowerCase()) ||
      mapping.gl_code.includes(searchFilter);
    const matchesStatus =
      statusFilter === 'all' || mapping.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const changedCount = mappings.filter((m) => m.status !== 'unchanged').length;
  const validCount = mappings.filter(
    (m) => m.validation_status === 'valid',
  ).length;

  return (
    <Card className="w-full border border-gray-200 shadow-sm">
      <div className="bg-gradient-to-r from-brand-primary to-[#1D372E] text-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            <h3 className="text-base font-semibold">
              M3 Giki Bulk GL Code Management
            </h3>
            <Badge
              variant="outline"
              className="bg-white/10 text-white border-white/20"
            >
              {industry.replace('_', ' ').toUpperCase()}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="bg-white/10 text-white border-white/20"
            >
              {validCount}/{mappings.length} Valid
            </Badge>
            {changedCount > 0 && (
              <Badge
                variant="outline"
                className="bg-yellow-500/20 text-yellow-100 border-yellow-300/20"
              >
                {changedCount} Changes
              </Badge>
            )}
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Control Panel */}
        <div className="flex flex-wrap gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex-1 min-w-64">
            <Label htmlFor="search">Search Categories</Label>
            <Input
              id="search"
              placeholder="Search by category name or GL code..."
              value={searchFilter}
              onChange={(e) => setSearchFilter(e.target.value)}
            />
          </div>

          <div className="min-w-48">
            <Label htmlFor="status-filter">Filter by Status</Label>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="unchanged">Unchanged</SelectItem>
                <SelectItem value="modified">Modified</SelectItem>
                <SelectItem value="new">New</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-end gap-2">
            <Button
              variant="outline"
              onClick={addNewMapping}
              className="border-green-500 text-success hover:bg-green-50"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Mapping
            </Button>

            <Button
              variant="outline"
              onClick={() => void validateMappings()}
              disabled={isValidating}
              className="border-blue-500 text-blue-600 hover:bg-blue-50"
            >
              {isValidating ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Zap className="w-4 h-4 mr-2" />
              )}
              Validate All
            </Button>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedRows.size > 0 && (
          <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-blue-900">
                {selectedRows.size} rows selected
              </span>

              <div className="flex items-center gap-2">
                <Select
                  value={bulkAccountType}
                  onValueChange={setBulkAccountType}
                >
                  <SelectTrigger className="w-48 bg-white">
                    <SelectValue placeholder="Select account type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Asset">Asset</SelectItem>
                    <SelectItem value="Liability">Liability</SelectItem>
                    <SelectItem value="Equity">Equity</SelectItem>
                    <SelectItem value="Revenue">Revenue</SelectItem>
                    <SelectItem value="Expense">Expense</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  onClick={applyBulkAccountType}
                  disabled={!bulkAccountType}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Apply to Selected
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* GL Mappings Table */}
        <div className="rounded-lg border overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={
                      selectedRows.size === filteredMappings.length &&
                      filteredMappings.length > 0
                    }
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedRows(
                          new Set(filteredMappings.map((m) => m.id)),
                        );
                      } else {
                        setSelectedRows(new Set());
                      }
                    }}
                  />
                </TableHead>
                <TableHead className="w-8"></TableHead>
                <TableHead>Category</TableHead>
                <TableHead>GL Code</TableHead>
                <TableHead>Account Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Confidence</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMappings.map((mapping) => (
                <TableRow
                  key={mapping.id}
                  className={`hover:bg-gray-50 ${draggedItem === mapping.id ? 'opacity-50' : ''}`}
                  draggable
                  onDragStart={(e) => handleDragStart(e, mapping.id)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, mapping.id)}
                >
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedRows.has(mapping.id)}
                      onChange={() => toggleRowSelection(mapping.id)}
                    />
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center gap-2">
                      <GripVertical className="w-4 h-4 text-gray-400 cursor-grab" />
                      {getValidationIcon(mapping.validation_status)}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div>
                      <div className="font-medium">{mapping.category_name}</div>
                      <div className="text-xs text-gray-500">
                        {mapping.category_path}
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <Input
                      value={mapping.gl_code}
                      onChange={(e) =>
                        updateMapping(mapping.id, { gl_code: e.target.value })
                      }
                      placeholder="e.g., 6001"
                      className="w-24"
                    />
                  </TableCell>

                  <TableCell>
                    <Input
                      value={mapping.gl_account_name}
                      onChange={(e) =>
                        updateMapping(mapping.id, {
                          gl_account_name: e.target.value,
                        })
                      }
                      placeholder="Account name"
                      className="min-w-48"
                    />
                  </TableCell>

                  <TableCell>
                    <Select
                      value={mapping.gl_account_type}
                      onValueChange={(value) =>
                        updateMapping(mapping.id, { gl_account_type: value })
                      }
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asset">Asset</SelectItem>
                        <SelectItem value="Liability">Liability</SelectItem>
                        <SelectItem value="Equity">Equity</SelectItem>
                        <SelectItem value="Revenue">Revenue</SelectItem>
                        <SelectItem value="Expense">Expense</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>

                  <TableCell>
                    <Badge
                      variant="outline"
                      className={
                        mapping.confidence >= 0.9
                          ? 'bg-green-100 text-green-800'
                          : mapping.confidence >= 0.7
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                      }
                    >
                      {(mapping.confidence * 100).toFixed(0)}%
                    </Badge>
                  </TableCell>

                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(mapping.status)}
                      {mapping.validation_message && (
                        <span
                          className="text-xs text-gray-500"
                          title={mapping.validation_message}
                        >
                          ⚠
                        </span>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteMapping(mapping.id)}
                      className="text-error hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center mt-6">
          <div className="flex gap-2">
            <Button variant="outline" className="border-gray-300 text-gray-700">
              <Upload className="w-4 h-4 mr-2" />
              Import CSV
            </Button>
            <Button variant="outline" className="border-gray-300 text-gray-700">
              <Download className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
            <Button
              variant="outline"
              className="border-blue-500 text-blue-600 hover:bg-blue-50"
            >
              <FileSpreadsheet className="w-4 h-4 mr-2" />
              Generate Template
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => void loadMappings()}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : (
                'Reset Changes'
              )}
            </Button>

            <Button
              onClick={() => void saveMappings()}
              disabled={isSaving || changedCount === 0}
              className="bg-brand-primary hover:bg-brand-primary-hover text-white"
            >
              {isSaving ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              Save {changedCount > 0 ? `${changedCount} Changes` : 'All'}
            </Button>
          </div>
        </div>

        {/* Status Summary */}
        {changedCount > 0 && (
          <Alert className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You have {changedCount} unsaved changes. Make sure to save before
              leaving this page.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </Card>
  );
};

export default BulkGLCodeManager;
