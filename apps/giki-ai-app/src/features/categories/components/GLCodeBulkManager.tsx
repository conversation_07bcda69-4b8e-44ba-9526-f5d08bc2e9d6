import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Progress } from '@/shared/components/ui/progress';
// import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Badge } from '@/shared/components/ui/badge';
import { categoryService } from '@/features/categories/services/categoryService';

interface GLCodeAssignment {
  category_id: number;
  category_name: string;
  status: 'simulated' | 'applied' | 'error';
  suggested_gl_code: string;
  suggested_account_name: string;
  suggested_account_type: string;
  confidence: number;
}

interface GLCodeAssignmentResult {
  assignments: GLCodeAssignment[];
}

export const GLCodeBulkManager: React.FC = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [assignments, setAssignments] = useState<GLCodeAssignment[]>([]);
  const [progress, setProgress] = useState(0);

  const handleAutoAssign = async (dryRun: boolean = true) => {
    setIsAnalyzing(true);
    setProgress(0);

    const result = await categoryService.autoAssignGLCodes(dryRun);

    if (result && typeof result === 'object' && 'assignments' in result) {
      setAssignments((result as GLCodeAssignmentResult).assignments);
      setProgress(100);
    }

    setIsAnalyzing(false);
  };

  const applyAssignments = async () => {
    const mappings = assignments
      .filter((a) => a.status === 'simulated')
      .map((a) => ({
        category_id: a.category_id,
        gl_code: a.suggested_gl_code,
        gl_account_name: a.suggested_account_name,
        gl_account_type: a.suggested_account_type,
      }));

    const result = await categoryService.bulkUpdateGLMappings(mappings);

    if (
      result &&
      typeof result === 'object' &&
      'successful_updates' in result
    ) {
      // Refresh the assignments to show applied status
      void handleAutoAssign(true);
    }
  };

  return (
    <Card className="w-full border border-gray-200 shadow-sm">
      <CardHeader className="bg-brand-primary text-white px-6 py-4">
        <CardTitle className="text-white">Bulk GL Code Assignment</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 overflow-hidden">
        <div className="flex flex-wrap gap-2">
          <Button
            className="max-w-full bg-brand-primary text-white hover:bg-brand-primary/90"
            onClick={() => void handleAutoAssign(true)}
            disabled={isAnalyzing}
          >
            Preview Auto-Assignment
          </Button>
          <Button
            className="max-w-full border-brand-primary text-brand-primary hover:bg-brand-primary/10"
            onClick={() => void applyAssignments()}
            disabled={assignments.length === 0}
            variant="outline"
          >
            Apply Assignments (
            {assignments.filter((a) => a.status === 'simulated').length})
          </Button>
        </div>

        {isAnalyzing && (
          <div className="space-y-2">
            <Progress value={progress} />
            <p className="text-sm text-[hsl(var(--giki-text-muted))]">
              Analyzing categories...
            </p>
          </div>
        )}

        {assignments.length > 0 && (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            <h4 className="font-medium">Suggested Assignments:</h4>
            {assignments.map((assignment, index) => (
              <div
                key={index}
                className="flex flex-wrap items-center gap-2 p-2 border rounded"
              >
                <span className="flex flex-wrap-1 font-medium">
                  {assignment.category_name}
                </span>
                <Badge className="max-w-[150px] truncate">
                  {assignment.suggested_gl_code}
                </Badge>
                <Badge variant="secondary" className="max-w-[150px] truncate">
                  {(assignment.confidence * 100).toFixed(0)}% confidence
                </Badge>
                <Badge
                  variant={
                    assignment.status === 'applied' ? 'default' : 'outline'
                  }
                  className="max-w-[150px] truncate"
                >
                  {assignment.status}
                </Badge>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GLCodeBulkManager;
