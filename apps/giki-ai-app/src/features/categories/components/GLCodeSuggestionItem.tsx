/**
 * Memoized GL Code Suggestion Item Component
 * Optimized for performance when rendering lists of GL code suggestions
 */
import React from 'react';
import { Badge } from '@/shared/components/ui/badge';
import { Button } from '@/shared/components/ui/button';

interface GLCodeSuggestion {
  gl_code: string;
  gl_account_name: string;
  gl_account_type: string;
  confidence: number;
}

interface GLCodeSuggestionItemProps {
  suggestion: GLCodeSuggestion;
  onApply: (suggestion: GLCodeSuggestion) => void;
}

// Memoized suggestion item component to prevent unnecessary re-renders
export const GLCodeSuggestionItem = React.memo<GLCodeSuggestionItemProps>(({
  suggestion,
  onApply,
}) => {
  return (
    <div className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
      <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-2">
        <div>
          <Badge className="bg-brand-primary text-white">
            {suggestion.gl_code}
          </Badge>
        </div>
        <div className="md:col-span-2">
          <p className="text-sm font-medium text-gray-800 truncate">
            {suggestion.gl_account_name}
          </p>
          <p className="text-xs text-gray-500">
            Type: {suggestion.gl_account_type}
          </p>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Badge
          variant="secondary"
          className={`text-xs ${
            suggestion.confidence > 0.8
              ? 'bg-green-100 text-green-800'
              : suggestion.confidence > 0.6
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-800'
          }`}
        >
          {(suggestion.confidence * 100).toFixed(0)}% match
        </Badge>
        <Button
          size="sm"
          onClick={() => onApply(suggestion)}
          className="bg-brand-primary hover:bg-brand-primary-hover text-white"
        >
          Apply
        </Button>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Only re-render if the suggestion data changes
  return (
    prevProps.suggestion.gl_code === nextProps.suggestion.gl_code &&
    prevProps.suggestion.gl_account_name === nextProps.suggestion.gl_account_name &&
    prevProps.suggestion.gl_account_type === nextProps.suggestion.gl_account_type &&
    prevProps.suggestion.confidence === nextProps.suggestion.confidence
  );
});

GLCodeSuggestionItem.displayName = 'GLCodeSuggestionItem';

export default GLCodeSuggestionItem;