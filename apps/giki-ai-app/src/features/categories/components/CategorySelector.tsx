import React from 'react';
import { Category } from '@/shared/types/categorization';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { cn } from '@/shared/utils/utils';

interface CategorySelectorProps {
  categories: Category[];
  selectedCategoryId: number | null | undefined;
  onSelectCategory: (categoryId: number | null) => void;
  className?: string;
  placeholder?: string;
}

const CategorySelector: React.FC<CategorySelectorProps> = ({
  categories,
  selectedCategoryId,
  onSelectCategory,
  className,
  placeholder = '-- Select Category --',
}) => {
  const handleValueChange = (value: string) => {
    onSelectCategory(
      value && value !== '__placeholder__' ? parseInt(value, 10) : null,
    );
  };

  // Recursive function to flatten categories for SelectItem rendering
  // We will display the full path for clarity in a flat list.
  // For a true hierarchical/tree select, a more complex component (like Popover + Command) would be needed.
  const renderCategoryItems = (categoryList: Category[]): JSX.Element[] => {
    const items: JSX.Element[] = [];
    const traverse = (nodes: Category[], depth: number) => {
      for (const category of nodes) {
        items.push(
          <SelectItem key={category.id} value={String(category.id)}>
            {/* Basic indentation for visual hierarchy, though SelectItem itself is flat */}
            <span style={{ paddingLeft: `${depth * 0.5}rem` }}>
              {category.path || category.name}
            </span>
          </SelectItem>,
        );
        if (category.children && category?.children?.length > 0) {
          traverse(category.children, depth + 1);
        }
      }
    };
    traverse(categoryList, 0);
    return items;
  };

  const selectedCategoryPath =
    categories.find((c) => c.id === selectedCategoryId)?.path ||
    categories.find((c) => c.id === selectedCategoryId)?.name;

  return (
    <Select
      value={selectedCategoryId?.toString() || ''}
      onValueChange={handleValueChange}
    >
      <SelectTrigger
        className={cn(
          'w-full shadow-sm border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20',
          className,
        )}
      >
        <SelectValue placeholder={placeholder}>
          {selectedCategoryPath || placeholder}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="__placeholder__">{placeholder}</SelectItem>
        {renderCategoryItems(categories)}
      </SelectContent>
    </Select>
  );
};

export default CategorySelector;
export { CategorySelector };
