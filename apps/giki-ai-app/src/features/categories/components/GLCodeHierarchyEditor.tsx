/**
 * GL Code Hierarchy Editor Component
 * Professional editor for M3 milestone GL code management
 * Drag-and-drop hierarchy editor with validation and compliance checking
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Trees,
  Plus,
  Edit,
  Trash2,
  X,
  Check,
  AlertTriangle,
  Download,
  Search,
  Filter,
  ChevronRight,
  ChevronDown,
  FolderOpen,
  Folder,
  Hash,
  Shield,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Badge } from '@/shared/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Textarea } from '@/shared/components/ui/textarea';
import { Label } from '@/shared/components/ui/label';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  glCodeService,
  type GLCode,
  type GLCodeHierarchy,
  type GLCodeValidationResult,
} from '../services/glCodeService';

interface GLCodeNode extends GLCode {
  children?: GLCodeNode[];
  isExpanded?: boolean;
  isEditing?: boolean;
  isDragOver?: boolean;
}

interface GLCodeHierarchyEditorProps {
  hierarchyId?: string;
  onHierarchyChange?: (hierarchy: GLCodeHierarchy) => void;
  onValidationResult?: (result: GLCodeValidationResult) => void;
  className?: string;
}

export const GLCodeHierarchyEditor: React.FC<GLCodeHierarchyEditorProps> = ({
  hierarchyId,
  onHierarchyChange,
  onValidationResult,
  className = '',
}) => {
  const { toast } = useToast();

  // State management
  const [_hierarchy, setHierarchy] = useState<GLCodeHierarchy | null>(null);
  const [nodes, setNodes] = useState<GLCodeNode[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [selectedNode, setSelectedNode] = useState<GLCodeNode | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] =
    useState<GLCodeValidationResult | null>(null);
  const [_isDirty, setIsDirty] = useState(false);

  const loadHierarchy = useCallback(async () => {
    if (!hierarchyId) return;

    try {
      const hierarchyData = await glCodeService.getHierarchy(hierarchyId);
      const glCodes = await glCodeService.getGLCodes(hierarchyId);

      // Type guard to ensure we have valid data
      if (
        hierarchyData &&
        typeof hierarchyData === 'object' &&
        'id' in hierarchyData &&
        Array.isArray(glCodes)
      ) {
        setHierarchy(hierarchyData);
        setNodes(buildTreeStructure(glCodes));
        onHierarchyChange?.(hierarchyData);
      } else {
        throw new Error('Invalid response format from API');
      }
    } catch (error) {
      console.error('Failed to load GL code hierarchy:', error);
      toast({
        title: 'Error',
        description: 'Failed to load GL code hierarchy',
        variant: 'destructive',
      });
    }
  }, [hierarchyId, onHierarchyChange, toast]);

  // Load hierarchy
  useEffect(() => {
    if (hierarchyId) {
      void loadHierarchy();
    }
  }, [hierarchyId, loadHierarchy]);

  // Build tree structure from flat GL codes
  const buildTreeStructure = (glCodes: GLCode[]): GLCodeNode[] => {
    const codeMap = new Map<string, GLCodeNode>();
    const rootNodes: GLCodeNode[] = [];

    // Create map of all codes
    glCodes.forEach((code) => {
      codeMap.set(code.id, { ...code, children: [], isExpanded: false });
    });

    // Build parent-child relationships
    glCodes.forEach((code) => {
      const node = codeMap.get(code.id);
      if (node && code.parentId) {
        const parent = codeMap.get(code.parentId);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(node);
        }
      } else if (node) {
        rootNodes.push(node);
      }
    });

    return rootNodes;
  };

  // Filter nodes based on search and filter criteria
  const filteredNodes = React.useMemo(() => {
    const filterNodes = (
      nodes: GLCodeNode[],
      predicate: (node: GLCodeNode) => boolean,
    ): GLCodeNode[] => {
      return nodes.reduce<GLCodeNode[]>((acc, node) => {
        const filteredChildren =
          node.children && Array.isArray(node.children)
            ? filterNodes(node.children, predicate)
            : [];

        if (predicate(node) || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren,
          });
        }

        return acc;
      }, []);
    };

    let filtered = nodes;

    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filterNodes(
        filtered,
        (node) =>
          node.code?.toLowerCase().includes(searchLower) ||
          node.name?.toLowerCase().includes(searchLower) ||
          node.description?.toLowerCase().includes(searchLower),
      );
    }

    if (filterType !== 'all') {
      filtered = filterNodes(
        filtered,
        (node) => node.accountType === filterType || filterType === 'all',
      );
    }

    return filtered;
  }, [nodes, searchTerm, filterType]);

  // Node operations
  const toggleNode = (nodeId: string) => {
    const currentNode = getNodeById(nodes, nodeId);
    if (currentNode) {
      setNodes((prev) =>
        updateNodeInTree(prev, nodeId, {
          isExpanded: !currentNode.isExpanded,
        }),
      );
    }
  };

  const selectNode = (node: GLCodeNode) => {
    setSelectedNode(node);
  };

  const startEditing = (nodeId: string) => {
    setNodes((prev) => updateNodeInTree(prev, nodeId, { isEditing: true }));
  };

  const cancelEditing = (nodeId: string) => {
    setNodes((prev) => updateNodeInTree(prev, nodeId, { isEditing: false }));
  };

  const saveNode = async (node: GLCodeNode) => {
    if (!hierarchyId) return;

    try {
      if (node.id.startsWith('new-')) {
        // Create new node
        const newNode = await glCodeService.createGLCode(hierarchyId, {
          code: node.code,
          name: node.name,
          description: node.description,
          level: node.level,
          parentId: node.parentId,
          accountType: node.accountType,
          category: node.category,
          isActive: node.isActive,
        });

        // Type guard for API response
        if (newNode && typeof newNode === 'object' && 'id' in newNode) {
          setNodes((prev) =>
            replaceNodeInTree(prev, node.id, {
              ...newNode,
              isEditing: false,
            }),
          );
        } else {
          throw new Error('Failed to create GL code - invalid response');
        }
      } else {
        // Update existing node
        const updatedNode = await glCodeService.updateGLCode(
          hierarchyId,
          node.id,
          {
            code: node.code,
            name: node.name,
            description: node.description,
            accountType: node.accountType,
            category: node.category,
            isActive: node.isActive,
          },
        );

        // Type guard for API response
        if (
          updatedNode &&
          typeof updatedNode === 'object' &&
          'id' in updatedNode
        ) {
          setNodes((prev) =>
            updateNodeInTree(prev, node.id, {
              ...updatedNode,
              isEditing: false,
            }),
          );
        } else {
          throw new Error('Failed to update GL code - invalid response');
        }
      }

      setIsDirty(true);
      toast({
        title: 'Success',
        description: 'GL code saved successfully',
      });
    } catch (error) {
      console.error('Failed to save GL code:', error);
      toast({
        title: 'Error',
        description: 'Failed to save GL code',
        variant: 'destructive',
      });
    }
  };

  const deleteNode = async (nodeId: string) => {
    if (!hierarchyId) return;

    try {
      await glCodeService.deleteGLCode(hierarchyId, nodeId);
      setNodes((prev) => removeNodeFromTree(prev, nodeId));
      setIsDirty(true);

      toast({
        title: 'Success',
        description: 'GL code deleted successfully',
      });
    } catch (error) {
      console.error('Failed to delete GL code:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete GL code',
        variant: 'destructive',
      });
    }
  };

  const addChildNode = (parentNode: GLCodeNode) => {
    const newNode: GLCodeNode = {
      id: `new-${Date.now()}`,
      code: '',
      name: '',
      description: '',
      level: parentNode.level + 1,
      parentId: parentNode.id,
      isActive: true,
      accountType: parentNode.accountType,
      category: parentNode.category,
      children: [],
      isEditing: true,
      isExpanded: false,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'current-user',
        compliance: {
          gaap: false,
          ifrs: false,
          sox: false,
        },
        mappings: {},
      },
    };

    setNodes((prev) => addNodeToTree(prev, parentNode.id, newNode));
    setNodes((prev) =>
      updateNodeInTree(prev, parentNode.id, { isExpanded: true }),
    );
  };

  // Tree manipulation helpers
  const getNodeById = (
    nodes: GLCodeNode[],
    nodeId: string,
  ): GLCodeNode | null => {
    for (const node of nodes) {
      if (node.id === nodeId) return node;
      if (node.children && Array.isArray(node.children)) {
        const found = getNodeById(node.children, nodeId);
        if (found) return found;
      }
    }
    return null;
  };

  const updateNodeInTree = (
    nodes: GLCodeNode[],
    nodeId: string,
    updates: Partial<GLCodeNode>,
  ): GLCodeNode[] => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return { ...node, ...updates };
      }
      if (node.children && Array.isArray(node.children)) {
        return {
          ...node,
          children: updateNodeInTree(node.children, nodeId, updates),
        };
      }
      return node;
    });
  };

  const replaceNodeInTree = (
    nodes: GLCodeNode[],
    oldId: string,
    newNode: GLCodeNode,
  ): GLCodeNode[] => {
    return nodes.map((node) => {
      if (node.id === oldId) {
        return newNode;
      }
      if (node.children && Array.isArray(node.children)) {
        return {
          ...node,
          children: replaceNodeInTree(node.children, oldId, newNode),
        };
      }
      return node;
    });
  };

  const removeNodeFromTree = (
    nodes: GLCodeNode[],
    nodeId: string,
  ): GLCodeNode[] => {
    return nodes.filter((node) => {
      if (node.id === nodeId) return false;
      if (node.children && Array.isArray(node.children)) {
        node.children = removeNodeFromTree(node.children, nodeId);
      }
      return true;
    });
  };

  const addNodeToTree = (
    nodes: GLCodeNode[],
    parentId: string,
    newNode: GLCodeNode,
  ): GLCodeNode[] => {
    return nodes.map((node) => {
      if (node.id === parentId) {
        return {
          ...node,
          children: [...(node.children || []), newNode],
        };
      }
      if (node.children && Array.isArray(node.children)) {
        return {
          ...node,
          children: addNodeToTree(node.children, parentId, newNode),
        };
      }
      return node;
    });
  };

  // Validation
  const validateHierarchy = async () => {
    if (!hierarchyId) return;

    setIsValidating(true);
    try {
      const result = await glCodeService.validateHierarchy(hierarchyId);

      // Type guard for validation result
      if (result && typeof result === 'object' && 'isValid' in result) {
        const validationResult = result;
        setValidationResult(validationResult);
        onValidationResult?.(validationResult);

        toast({
          title: validationResult.isValid
            ? 'Validation Passed'
            : 'Validation Issues Found',
          description: validationResult.isValid
            ? 'GL code hierarchy is compliant'
            : `Found ${Array.isArray(validationResult.errors) ? validationResult.errors.length : 0} errors and ${Array.isArray(validationResult.warnings) ? validationResult.warnings.length : 0} warnings`,
          variant: validationResult.isValid ? 'default' : 'destructive',
        });
      } else {
        throw new Error('Invalid validation response from API');
      }
    } catch (error) {
      console.error('Failed to validate hierarchy:', error);
      toast({
        title: 'Error',
        description: 'Failed to validate hierarchy',
        variant: 'destructive',
      });
    } finally {
      setIsValidating(false);
    }
  };

  // Export/Import
  const exportHierarchy = async () => {
    if (!hierarchyId) return;

    try {
      const result = await glCodeService.exportGLCodes(hierarchyId, 'excel');
      if (
        result &&
        typeof result === 'object' &&
        'downloadUrl' in result &&
        result.downloadUrl &&
        typeof result.downloadUrl === 'string'
      ) {
        window.open(result.downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Failed to export hierarchy:', error);
      toast({
        title: 'Error',
        description: 'Failed to export hierarchy',
        variant: 'destructive',
      });
    }
  };

  const _handleImport = async (file: File) => {
    if (!hierarchyId) return;

    try {
      const result = await glCodeService.importGLCodes(hierarchyId, file, {
        format: 'excel',
        overwriteExisting: false,
      });

      toast({
        title: 'Import Complete',
        description: `Imported ${(result as { importedCount?: number; skippedCount?: number })?.importedCount || 0} GL codes, skipped ${(result as { importedCount?: number; skippedCount?: number })?.skippedCount || 0}`,
      });

      await loadHierarchy();
    } catch (error) {
      console.error('Failed to import GL codes:', error);
      toast({
        title: 'Error',
        description: 'Failed to import GL codes',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Controls */}
      <Card className="border-brand-primary/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 text-brand-primary">
                <Trees className="h-5 w-5" />
                GL Code Hierarchy Editor
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Manage your chart of accounts with drag-and-drop interface
              </p>
            </div>

            <div className="flex items-center gap-2">
              {validationResult && (
                <Badge
                  variant={validationResult.isValid ? 'default' : 'destructive'}
                >
                  {validationResult.isValid ? 'Valid' : 'Issues Found'}
                </Badge>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={() => void validateHierarchy()}
                disabled={isValidating}
                className="border-brand-primary text-brand-primary hover:bg-brand-primary/10"
              >
                <Shield className="h-4 w-4 mr-2" />
                {isValidating ? 'Validating...' : 'Validate'}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => void exportHierarchy()}
                className="border-brand-primary text-brand-primary hover:bg-brand-primary/10"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search GL codes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Account Types</SelectItem>
                <SelectItem value="asset">Assets</SelectItem>
                <SelectItem value="liability">Liabilities</SelectItem>
                <SelectItem value="equity">Equity</SelectItem>
                <SelectItem value="revenue">Revenue</SelectItem>
                <SelectItem value="expense">Expenses</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tree View */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="border-gray-200">
            <CardHeader>
              <CardTitle className="text-lg">Chart of Accounts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {filteredNodes.map((node) => (
                  <TreeNodeComponent
                    key={node.id}
                    node={node}
                    level={0}
                    onToggle={toggleNode}
                    onSelect={selectNode}
                    onEdit={startEditing}
                    onSave={(node) => void saveNode(node)}
                    onCancel={cancelEditing}
                    onDelete={(nodeId) => void deleteNode(nodeId)}
                    onAddChild={addChildNode}
                    selectedNodeId={selectedNode?.id}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Details Panel */}
        <div>
          <Card className="border-gray-200">
            <CardHeader>
              <CardTitle className="text-lg">Details</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedNode ? (
                <GLCodeDetailsPanel node={selectedNode} />
              ) : (
                <div className="text-center text-gray-500 py-8">
                  Select a GL code to view details
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Validation Results */}
      {validationResult && !validationResult.isValid && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Validation Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {validationResult.errors.map((error, index) => (
                <div key={index} className="flex items-start gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-error mt-0.5 flex-shrink-0" />
                  <div>
                    <span className="font-medium">{error.code}</span>
                    <p className="text-gray-700">{error.message}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// Tree Node Component
interface TreeNodeComponentProps {
  node: GLCodeNode;
  level: number;
  onToggle: (nodeId: string) => void;
  onSelect: (node: GLCodeNode) => void;
  onEdit: (nodeId: string) => void;
  onSave: (node: GLCodeNode) => void;
  onCancel: (nodeId: string) => void;
  onDelete: (nodeId: string) => void;
  onAddChild: (parentNode: GLCodeNode) => void;
  selectedNodeId?: string;
}

const TreeNodeComponent: React.FC<TreeNodeComponentProps> = ({
  node,
  level,
  onToggle,
  onSelect,
  onEdit,
  onSave,
  onCancel,
  onDelete,
  onAddChild,
  selectedNodeId,
}) => {
  const [editingNode, setEditingNode] = useState(node);

  const hasChildren =
    node.children && Array.isArray(node.children) && node.children.length > 0;
  const isSelected = selectedNodeId === node.id;

  const handleSave = () => {
    onSave(editingNode);
  };

  if (node.isEditing) {
    return (
      <div
        className={`ml-${level * 4} p-3 border rounded-lg bg-blue-50 border-blue-200`}
      >
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <Input
              placeholder="GL Code"
              value={editingNode.code}
              onChange={(e) =>
                setEditingNode((prev) => ({ ...prev, code: e.target.value }))
              }
              className="text-sm"
            />
            <Input
              placeholder="Name"
              value={editingNode.name}
              onChange={(e) =>
                setEditingNode((prev) => ({ ...prev, name: e.target.value }))
              }
              className="text-sm"
            />
          </div>

          <Textarea
            placeholder="Description"
            value={editingNode.description}
            onChange={(e) =>
              setEditingNode((prev) => ({
                ...prev,
                description: e.target.value,
              }))
            }
            className="text-sm"
            rows={2}
          />

          <div className="flex items-center gap-2">
            <Button
              size="sm"
              onClick={() => void handleSave()}
              className="bg-brand-primary hover:bg-[#1e3d2f]"
            >
              <Check className="h-3 w-3 mr-1" />
              Save
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onCancel(node.id)}
            >
              <X className="h-3 w-3 mr-1" />
              Cancel
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`ml-${level * 4}`}>
      <div
        className={`flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 cursor-pointer ${
          isSelected ? 'bg-brand-primary/10 border border-brand-primary/20' : ''
        }`}
        onClick={() => onSelect(node)}
      >
        <div className="flex items-center gap-1">
          {hasChildren ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation();
                onToggle(node.id);
              }}
            >
              {node.isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          ) : (
            <div className="w-6" />
          )}

          {hasChildren ? (
            node.isExpanded ? (
              <FolderOpen className="h-4 w-4 text-brand-primary" />
            ) : (
              <Folder className="h-4 w-4 text-brand-primary" />
            )
          ) : (
            <Hash className="h-4 w-4 text-gray-400" />
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-mono text-sm font-medium">{node.code}</span>
            <span className="text-sm truncate">{node.name}</span>
          </div>
        </div>

        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs">
            {node.accountType}
          </Badge>

          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onEdit(node.id);
            }}
          >
            <Edit className="h-3 w-3" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onAddChild(node);
            }}
          >
            <Plus className="h-3 w-3" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-error hover:text-red-700"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(node.id);
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {node.isExpanded && hasChildren && node.children && (
        <div className="ml-4">
          {node.children.map((child) => (
            <TreeNodeComponent
              key={child.id}
              node={child}
              level={level + 1}
              onToggle={onToggle}
              onSelect={onSelect}
              onEdit={onEdit}
              onSave={onSave}
              onCancel={onCancel}
              onDelete={onDelete}
              onAddChild={onAddChild}
              selectedNodeId={selectedNodeId}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Details Panel Component
interface GLCodeDetailsPanelProps {
  node: GLCodeNode;
}

const GLCodeDetailsPanel: React.FC<GLCodeDetailsPanelProps> = ({ node }) => {
  return (
    <div className="space-y-4">
      <div>
        <Label className="text-sm font-medium">GL Code</Label>
        <p className="font-mono text-lg font-bold text-brand-primary">
          {node.code}
        </p>
      </div>

      <div>
        <Label className="text-sm font-medium">Name</Label>
        <p className="text-sm">{node.name}</p>
      </div>

      <div>
        <Label className="text-sm font-medium">Description</Label>
        <p className="text-sm text-gray-600">{node.description}</p>
      </div>

      <div>
        <Label className="text-sm font-medium">Account Type</Label>
        <Badge variant="outline" className="mt-1">
          {node.accountType}
        </Badge>
      </div>

      <div>
        <Label className="text-sm font-medium">Level</Label>
        <p className="text-sm">{node.level}</p>
      </div>

      <div>
        <Label className="text-sm font-medium">Status</Label>
        <Badge
          variant={node.isActive ? 'default' : 'secondary'}
          className="mt-1"
        >
          {node.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </div>

      <div>
        <Label className="text-sm font-medium">Compliance</Label>
        <div className="space-y-1 mt-1">
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${node.metadata?.compliance?.gaap ? 'bg-green-500' : 'bg-gray-300'}`}
            />
            <span className="text-xs">GAAP</span>
          </div>
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${node.metadata?.compliance?.ifrs ? 'bg-green-500' : 'bg-gray-300'}`}
            />
            <span className="text-xs">IFRS</span>
          </div>
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${node.metadata?.compliance?.sox ? 'bg-green-500' : 'bg-gray-300'}`}
            />
            <span className="text-xs">SOX</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GLCodeHierarchyEditor;
