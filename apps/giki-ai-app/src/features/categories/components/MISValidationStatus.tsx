/**
 * MIS Validation Status Component
 *
 * Displays the current status of MIS (Management Information System) structure
 * validation with health metrics, issues, and recommendations for business users.
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { Button } from '@/shared/components/ui/button';
import { Progress } from '@/shared/components/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/components/ui/tooltip';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import {
  CheckCircle,
  AlertCircle,
  XCircle,
  TrendingUp,
  Shield,
  BarChart3,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  Info,
} from 'lucide-react';

interface MISValidationData {
  tenant_id: number;
  health_score: number;
  health_status: 'excellent' | 'good' | 'fair' | 'poor';
  structure_valid: boolean;
  categorization_rate: number;
  gl_coverage: number;
  total_categories: number;
  hierarchy_depth: number;
  health_issues: string[];
  recommendations: string[];
}

interface MISValidationStatusProps {
  className?: string;
  showDetails?: boolean;
  onValidationRefresh?: () => void;
  refreshing?: boolean;
}

export const MISValidationStatus: React.FC<MISValidationStatusProps> = ({
  className = '',
  showDetails = true,
  onValidationRefresh,
  refreshing = false,
}) => {
  const [validationData, setValidationData] =
    useState<MISValidationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(false);

  const fetchValidationData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        '/api/v1/categories/validate/structure-health',
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        throw new Error(`Validation check failed: ${response.status}`);
      }

      const data = (await response.json()) as MISValidationData;
      setValidationData(data);
    } catch (err) {
      console.error('Failed to fetch MIS validation data:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to validate MIS structure',
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    void fetchValidationData();
  }, []);

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
        return <CheckCircle className="h-5 w-5 text-success" />;
      case 'good':
        return <CheckCircle className="h-5 w-5 text-blue-600" />;
      case 'fair':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case 'poor':
        return <XCircle className="h-5 w-5 text-error" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-success bg-green-100';
      case 'good':
        return 'text-blue-600 bg-blue-100';
      case 'fair':
        return 'text-yellow-600 bg-yellow-100';
      case 'poor':
        return 'text-error bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-success';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-error';
  };

  const handleRefresh = async () => {
    await fetchValidationData();
    onValidationRefresh?.();
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center">
            <Shield className="h-4 w-4 mr-2" />
            MIS Structure Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-20">
            <RefreshCw className="h-5 w-5 animate-spin text-brand-primary" />
            <span className="ml-2 text-sm text-gray-500">
              Validating structure...
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center">
            <Shield className="h-4 w-4 mr-2" />
            MIS Structure Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Validation Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button
            variant="outline"
            size="sm"
            onClick={() => void handleRefresh()}
            className="mt-2"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!validationData) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center">
            <Shield className="h-4 w-4 mr-2" />
            MIS Structure Health
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge
              className={getHealthStatusColor(validationData.health_status)}
            >
              {getHealthStatusIcon(validationData.health_status)}
              <span className="ml-1 capitalize">
                {validationData.health_status}
              </span>
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => void handleRefresh()}
              disabled={refreshing}
            >
              <RefreshCw
                className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`}
              />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Health Score */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="text-sm font-medium flex items-center">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Health Score
                    <Info className="h-3 w-3 ml-1 text-gray-400" />
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    Overall MIS structure health based on completeness,
                    compliance, and accuracy
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <span
              className={`text-lg font-bold ${getHealthScoreColor(validationData.health_score)}`}
            >
              {validationData.health_score}/100
            </span>
          </div>
          <Progress value={validationData.health_score} className="h-2" />
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-lg font-bold text-brand-primary">
              {validationData.categorization_rate}%
            </div>
            <div className="text-xs text-gray-500">Categorization Rate</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">
              {validationData.gl_coverage}%
            </div>
            <div className="text-xs text-gray-500">GL Code Coverage</div>
          </div>
        </div>

        {/* Structure Stats */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Categories:</span>
            <span className="ml-1 font-medium">
              {validationData.total_categories}
            </span>
          </div>
          <div>
            <span className="text-gray-500">Depth:</span>
            <span className="ml-1 font-medium">
              {validationData.hierarchy_depth} levels
            </span>
          </div>
        </div>

        {/* Issues & Recommendations */}
        {showDetails &&
          (validationData.health_issues.length > 0 ||
            validationData.recommendations.length > 0) && (
            <div className="space-y-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setExpanded(!expanded)}
                className="flex items-center justify-between w-full p-2 h-auto"
              >
                <span className="text-sm font-medium">
                  {validationData.health_issues.length > 0
                    ? 'Issues & Recommendations'
                    : 'Recommendations'}
                </span>
                {expanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>

              {expanded && (
                <div className="space-y-3">
                  {/* Health Issues */}
                  {validationData.health_issues.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-error">
                        Issues Found:
                      </h4>
                      <ul className="space-y-1">
                        {validationData.health_issues.map((issue, index) => (
                          <li key={index} className="flex items-start text-sm">
                            <XCircle className="h-3 w-3 text-red-500 mt-1 mr-2 flex-shrink-0" />
                            <span className="text-gray-700">{issue}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Recommendations */}
                  {validationData.recommendations.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-blue-600">
                        Recommendations:
                      </h4>
                      <ul className="space-y-1">
                        {validationData.recommendations
                          .slice(0, 3)
                          .map((rec, index) => (
                            <li
                              key={index}
                              className="flex items-start text-sm"
                            >
                              <TrendingUp className="h-3 w-3 text-blue-500 mt-1 mr-2 flex-shrink-0" />
                              <span className="text-gray-700">{rec}</span>
                            </li>
                          ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

        {/* Structure Status */}
        <div className="flex items-center justify-between pt-2 border-t">
          <span className="text-sm text-gray-500">Structure Valid:</span>
          <div className="flex items-center">
            {validationData.structure_valid ? (
              <CheckCircle className="h-4 w-4 text-success" />
            ) : (
              <XCircle className="h-4 w-4 text-error" />
            )}
            <span
              className={`ml-1 text-sm ${
                validationData.structure_valid
                  ? 'text-success'
                  : 'text-error'
              }`}
            >
              {validationData.structure_valid ? 'Valid' : 'Issues Found'}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MISValidationStatus;
