import React from 'react';
import { Badge } from '@/shared/components/ui/badge';

interface ConfidenceIndicatorProps {
  level?: 'high' | 'medium' | 'low';
  confidence?: number;
  value?: number;
  className?: string;
}

/**
 * Confidence Indicator Component
 *
 * Displays confidence level with color-coded badge.
 */
const ConfidenceIndicator: React.FC<ConfidenceIndicatorProps> = ({
  level,
  confidence,
  value,
  className,
}) => {
  // Convert confidence number to level if provided
  const confidenceLevel =
    level ||
    (confidence !== undefined
      ? confidence >= 0.8
        ? 'high'
        : confidence >= 0.6
          ? 'medium'
          : 'low'
      : 'low');
  const confidenceValue = value || confidence;
  const getVariant = () => {
    switch (confidenceLevel) {
      case 'high':
        return 'default';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getColor = () => {
    switch (confidenceLevel) {
      case 'high':
        return 'text-success bg-success/10 border-success/20 font-semibold';
      case 'medium':
        return 'text-warning bg-warning/10 border-warning/20 font-semibold';
      case 'low':
        return 'text-destructive bg-destructive/10 border-destructive/20 font-semibold';
      default:
        return 'text-foreground bg-muted border-border font-semibold';
    }
  };

  return (
    <Badge
      variant={getVariant()}
      className={`${getColor()} ${className || ''}`}
    >
      {confidenceLevel.charAt(0).toUpperCase() + confidenceLevel.slice(1)}
      {confidenceValue !== undefined &&
        ` (${Math.round(confidenceValue * 100)}%)`}
    </Badge>
  );
};

export default ConfidenceIndicator;
