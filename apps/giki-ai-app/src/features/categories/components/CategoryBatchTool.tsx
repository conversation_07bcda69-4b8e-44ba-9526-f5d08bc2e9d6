import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardT<PERSON>le,
  CardDescription,
  CardFooter,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Checkbox } from '@/shared/components/ui/checkbox';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { CheckSquare, AlertCircle, Database } from 'lucide-react';
import { Loading } from '@/shared/components/ui/loading'; // Import Loading component
import { Transaction, Category } from '@/shared/types/categorization';
import { updateBatchCategories } from '@/features/transactions/services/transactionService';
import { RAGCorpus } from '@/features/intelligence/services/ragCorpusService';
import ConfidenceIndicator from './ConfidenceIndicator';

interface CategoryBatchToolProps {
  selectedTransactions: Transaction[];
  categories: Category[];
  onCategorized: () => void;
  ragCorpora?: RAGCorpus[];
  onCancel: () => void;
}

const CategoryBatchTool: React.FC<CategoryBatchToolProps> = ({
  selectedTransactions,
  categories,
  onCategorized,
  ragCorpora = [],
  onCancel,
}) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(
    null,
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [useAI, setUseAI] = useState(false);
  const [selectedCorpusId, setSelectedCorpusId] = useState<string | null>(null);
  const [confidenceThreshold, setConfidenceThreshold] = useState<string>('0.7');

  const handleCategoryChange = (value: string) => {
    setSelectedCategoryId(value === 'null' ? null : value);
  };

  const handleCorpusChange = (value: string) => {
    setSelectedCorpusId(value);
  };

  const handleConfidenceThresholdChange = (value: string) => {
    setConfidenceThreshold(value);
  };

  const handleApply = async () => {
    if (!selectedCategoryId && !useAI) {
      setError('Please select a category or enable AI categorization');
      return;
    }

    if (useAI && !selectedCorpusId) {
      setError('Please select a RAG corpus for AI categorization');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const transactionIds = selectedTransactions.map((t) => t.id);

      await updateBatchCategories({
        transaction_ids: transactionIds,
        category_id: selectedCategoryId ? parseInt(selectedCategoryId) : null,
        use_ai: useAI,
        rag_corpus_id: selectedCorpusId,
        confidence_threshold: parseFloat(confidenceThreshold),
      });

      setSuccess(true);
      setTimeout(() => {
        onCategorized();
      }, 1500);
    } catch {
      setError('Failed to update categories. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Batch Categorization</CardTitle>
        <CardDescription>
          Apply a category to {selectedTransactions.length} selected transaction
          {selectedTransactions.length !== 1 ? 's' : ''}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 overflow-hidden">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert variant="success">
            <CheckSquare className="h-4 w-4" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>
              Categories updated successfully!
            </AlertDescription>
          </Alert>
        )}

        <div className="flex flex-wrap items-center space-x-2">
          <Checkbox
            id="useAI"
            checked={useAI}
            onCheckedChange={(checked) => setUseAI(checked === true)}
          />
          <label
            htmlFor="useAI"
            className="text-label leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Use AI to categorize transactions
          </label>
        </div>

        {useAI ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-label">Select RAG Corpus</label>
              <Select
                value={selectedCorpusId || ''}
                onValueChange={handleCorpusChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a RAG corpus" />
                </SelectTrigger>
                <SelectContent>
                  {ragCorpora.length === 0 ? (
                    <SelectItem value="none" disabled>
                      No RAG corpora available
                    </SelectItem>
                  ) : (
                    ragCorpora.map((corpus) => (
                      <SelectItem key={corpus.id} value={corpus.id}>
                        <div className="flex flex-wrap items-center">
                          <Database className="mr-2 h-4 w-4 truncate text-muted" />
                          {corpus.display_name}
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-label">Confidence Threshold</label>
              <Select
                value={confidenceThreshold}
                onValueChange={handleConfidenceThresholdChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select confidence threshold" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0.9">
                    <div className="flex flex-wrap items-center">
                      <ConfidenceIndicator confidence={0.9} className="mr-2" />
                      High (90%)
                    </div>
                  </SelectItem>
                  <SelectItem value="0.7">
                    <div className="flex flex-wrap items-center">
                      <ConfidenceIndicator confidence={0.7} className="mr-2" />
                      Medium (70%)
                    </div>
                  </SelectItem>
                  <SelectItem value="0.5">
                    <div className="flex flex-wrap items-center">
                      <ConfidenceIndicator confidence={0.5} className="mr-2" />
                      Low (50%)
                    </div>
                  </SelectItem>
                  <SelectItem value="0">
                    <div className="flex flex-wrap items-center">
                      <ConfidenceIndicator confidence={0.1} className="mr-2" />
                      Any confidence
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <label className="text-label">Select Category</label>
            <Select
              value={selectedCategoryId || 'null'}
              onValueChange={handleCategoryChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="null">Uncategorized</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={String(category.id)}>
                    {category.path}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex flex-wrap justify-between">
        <Button
          className="max-w-full"
          variant="outline"
          onClick={() => void onCancel()}
        >
          Cancel
        </Button>
        <Button
          className="max-w-full"
          onClick={() => void handleApply()}
          disabled={loading}
        >
          {loading ? (
            <Loading size="sm" text="Processing..." className="py-0" />
          ) : (
            'Apply to Selected'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default CategoryBatchTool;
