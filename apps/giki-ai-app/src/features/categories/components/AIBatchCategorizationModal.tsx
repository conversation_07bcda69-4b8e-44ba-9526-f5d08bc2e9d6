import React, { useState, useEffect, useCallback } from 'react';
// Trivial change to force cache invalidation
import { Loading } from '@/shared/components/ui/loading';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import {
  AlertCircle,
  ArrowRight,
  BarChart3,
  CheckCircle2,
  Lightbulb,
  Settings,
  Sparkles,
  ThumbsUp,
  X,
} from 'lucide-react';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { Switch } from '@/shared/components/ui/switch';
import { Label } from '@/shared/components/ui/label';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { toast } from '@/shared/components/ui/use-toast';
import { cn } from '@/shared/utils/utils';
import { Transaction, Category } from '@/shared/types/categorization';
import { updateBatchCategories } from '@/features/transactions/services/transactionService';

// Define types for batch categorization
interface AICategorySuggestion {
  categoryId: number;
  categoryPath: string;
  confidence: number;
  matchCount: number;
  transactionIds: string[];
}

interface AIBatchCategorizationProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTransactions: Transaction[];
  categories: Category[];
  onCategorizeSuccess: () => void;
}

// AI categorization function
const fetchAICategorySuggestions = async (
  transactions: Transaction[],
  useRagCorpus: boolean = false,
  confidenceThreshold: number = 0.7,
  ragCorpusId?: string,
): Promise<AICategorySuggestion[]> => {
  try {
    const transactionIds = transactions.map((t) => t.id);

    // Call the batch categorization endpoint with AI
    await updateBatchCategories({
      transaction_ids: transactionIds,
      category_id: null,
      use_ai: true,
      rag_corpus_id: useRagCorpus && ragCorpusId ? ragCorpusId : null,
      confidence_threshold: confidenceThreshold,
    });

    // The API categorizes transactions but doesn't return detailed suggestions
    // For now, return a summary indicating success
    return [
      {
        categoryId: 0,
        categoryPath: 'AI Categorization Applied',
        confidence: confidenceThreshold,
        matchCount: transactions.length,
        transactionIds: transactionIds,
      },
    ];
  } catch (error) {
    console.error('Failed to get AI categorization suggestions:', error);
    throw error;
  }
};

const AIBatchCategorizationModal: React.FC<AIBatchCategorizationProps> = ({
  isOpen,
  onClose,
  selectedTransactions,
  categories: _categories,
  onCategorizeSuccess,
}) => {
  const [categorySuggestions, setCategorySuggestions] = useState<
    AICategorySuggestion[]
  >([]);
  const [selectedSuggestions, setSelectedSuggestions] = useState<Set<number>>(
    new Set(),
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [useRagCorpus, setUseRagCorpus] = useState<boolean>(true);
  const [confidenceThreshold, setConfidenceThreshold] = useState<string>('0.7'); // as string for input
  const [activeTab, setActiveTab] = useState<string>('ai-suggestions');

  const loadSuggestions = useCallback(async () => {
    if (!isOpen) return;

    setIsLoading(true);
    setError(null);

    try {
      const suggestions = await fetchAICategorySuggestions(
        selectedTransactions,
        useRagCorpus,
        parseFloat(confidenceThreshold),
      );

      setCategorySuggestions(suggestions);

      // Auto-select all suggestions by default
      setSelectedSuggestions(new Set(suggestions.map((s) => s.categoryId)));
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to load AI category suggestions',
      );
      toast({
        title: 'Error',
        description:
          'Failed to load AI category suggestions. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [isOpen, selectedTransactions, useRagCorpus, confidenceThreshold]);

  useEffect(() => {
    if (isOpen && selectedTransactions.length > 0) {
      void loadSuggestions();
    }
  }, [isOpen, selectedTransactions, useRagCorpus, loadSuggestions]);

  const handleConfidenceChange = (value: string) => {
    setConfidenceThreshold(value);
  };

  const handleRegenerateClick = () => {
    void loadSuggestions();
  };

  const toggleSuggestion = (categoryId: number) => {
    setSelectedSuggestions((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedSuggestions.size === categorySuggestions.length) {
      // If all are selected, clear selection
      setSelectedSuggestions(new Set());
    } else {
      // Otherwise, select all
      setSelectedSuggestions(
        new Set(categorySuggestions.map((s) => s.categoryId)),
      );
    }
  };

  const handleApplySuggestions = async () => {
    if (selectedSuggestions.size === 0) {
      toast({
        title: 'No categories selected',
        description: 'Please select at least one category to apply.',
        variant: 'default',
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Collect all transaction IDs for each selected category
      const categorizationTasks = categorySuggestions
        .filter((suggestion) => selectedSuggestions.has(suggestion.categoryId))
        .map(async (suggestion) => {
          // In a real implementation, we would batch these calls
          // or have a single endpoint that handles multiple category assignments
          await updateBatchCategories({
            transaction_ids: suggestion.transactionIds,
            category_id: suggestion.categoryId,
          });

          return {
            categoryId: suggestion.categoryId,
            categoryPath: suggestion.categoryPath,
            count: suggestion?.transactionIds?.length,
          };
        });

      await Promise.all(categorizationTasks);

      const totalCategorized = categorySuggestions
        .filter((s) => selectedSuggestions.has(s.categoryId))
        .reduce((sum, s) => sum + s?.transactionIds?.length, 0);

      toast({
        title: 'Categorization Complete',
        description: `Successfully categorized ${totalCategorized} transactions.`,
        variant: 'default',
      });

      onCategorizeSuccess();
      onClose();
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to apply category suggestions',
      );
      toast({
        title: 'Error',
        description: 'Failed to apply category suggestions. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Format percentage
  const formatPercent = (value: number) => `${Math.round(value * 100)}%`;

  // Get confidence badge color and icon
  const getConfidenceBadgeClass = (confidence: number) => {
    if (confidence >= 0.9) return 'bg-green-50 text-green-700 border-green-200';
    if (confidence >= 0.8) return 'bg-blue-50 text-blue-700 border-blue-200';
    if (confidence >= 0.7) return 'bg-blue-50 text-success border-blue-200';
    return 'bg-amber-50 text-amber-700 border-amber-200';
  };

  // Get confidence icon
  const getConfidenceIcon = (confidence: number) => {
    if (confidence >= 0.9) return <CheckCircle2 className="h-3 w-3 mr-1" />;
    if (confidence >= 0.7) return <ThumbsUp className="h-3 w-3 mr-1" />;
    return <AlertCircle className="h-3 w-3 mr-1" />;
  };

  // Calculate coverage percentage
  const calculateCoverage = () => {
    const coveredTransactionIds = new Set<string>();

    categorySuggestions
      .filter((suggestion) => selectedSuggestions.has(suggestion.categoryId))
      .forEach((suggestion) => {
        suggestion?.transactionIds?.forEach((id) =>
          coveredTransactionIds.add(id),
        );
      });

    return coveredTransactionIds.size / selectedTransactions.length;
  };

  return (
    <Dialog open={isOpen} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex flex-wrap items-center text-xl">
            <Sparkles className="mr-2 h-5 w-5 text-primary" />
            AI-Assisted Batch Categorization
          </DialogTitle>
          <DialogDescription className="text-base">
            Automatically categorize multiple transactions at once using AI
            suggestions.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Settings */}
          <Card className="shadow-md border-muted">
            <CardHeader className="pb-2">
              <CardTitle className="text-base flex flex-wrap items-center">
                <Settings className="h-4 w-4 mr-2 text-primary" />
                AI Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="overflow-hidden">
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex flex-wrap items-center space-x-2">
                  <Switch
                    id="use-rag"
                    checked={useRagCorpus}
                    onCheckedChange={setUseRagCorpus}
                  />
                  <Label htmlFor="use-rag" className="font-medium">
                    Use RAG corpus
                  </Label>
                </div>

                <div className="flex flex-wrap items-center space-x-2">
                  <Label htmlFor="confidence-threshold" className="font-medium">
                    Min. Confidence:
                  </Label>
                  <Select
                    value={confidenceThreshold}
                    onValueChange={handleConfidenceChange}
                  >
                    <SelectTrigger className="w-[120px] shadow-md">
                      <SelectValue placeholder="Confidence" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0.5">50%</SelectItem>
                      <SelectItem value="0.6">60%</SelectItem>
                      <SelectItem value="0.7">70%</SelectItem>
                      <SelectItem value="0.8">80%</SelectItem>
                      <SelectItem value="0.9">90%</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  className="max-w-full shadow-md ml-auto"
                  variant="outline"
                  size="sm"
                  onClick={() => void handleRegenerateClick()}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loading size="sm" text="Generating..." />
                  ) : (
                    <>
                      <Lightbulb className="mr-2 h-4 w-4" />
                      Regenerate
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Main Content */}
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-4"
          >
            <TabsList className="w-full shadow-md">
              <TabsTrigger value="ai-suggestions" className="flex flex-1">
                <Sparkles className="mr-2 h-4 w-4" />
                AI Suggestions
              </TabsTrigger>
              <TabsTrigger value="summary" className="flex flex-1">
                <BarChart3 className="mr-2 h-4 w-4" />
                Summary
              </TabsTrigger>
            </TabsList>

            <TabsContent value="ai-suggestions">
              {isLoading ? (
                <Card className="flex flex-wrap flex-col items-center justify-center py-12 shadow-md border-muted">
                  <Loading
                    size="lg"
                    text={`Analyzing ${selectedTransactions.length} transactions...`}
                  />
                  <Progress
                    value={45}
                    className="w-full sm:w-64 h-2 mb-2 mt-4"
                  />
                  <p className="text-xs text-text-secondary">
                    This may take a moment
                  </p>
                </Card>
              ) : error ? (
                <Card className="shadow-md border-destructive">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex flex-wrap items-center text-destructive">
                      <AlertCircle className="h-5 w-5 mr-2" />
                      Error Processing Transactions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="overflow-hidden">
                    <p className="text-muted-foreground mb-4">{error}</p>
                    <Button
                      className="max-w-full shadow-md"
                      variant="outline"
                      size="sm"
                      onClick={() => void handleRegenerateClick()}
                    >
                      <ArrowRight className="mr-2 h-4 w-4" />
                      Try Again
                    </Button>
                  </CardContent>
                </Card>
              ) : categorySuggestions.length === 0 ? (
                <Card className="shadow-md border-muted">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex flex-wrap items-center text-amber-600">
                      <AlertCircle className="h-5 w-5 mr-2" />
                      No Suggestions Found
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="overflow-hidden">
                    <p className="text-muted-foreground mb-4">
                      No category suggestions met the confidence threshold. Try
                      lowering the confidence threshold or modifying your
                      selection.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        className="max-w-full shadow-md"
                        variant="outline"
                        size="sm"
                        onClick={() => void handleRegenerateClick()}
                      >
                        <Lightbulb className="mr-2 h-4 w-4" />
                        Try Lower Confidence
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  <div className="flex flex-wrap justify-between items-center mb-2">
                    <div className="flex flex-wrap items-center">
                      <Checkbox
                        id="select-all"
                        checked={
                          selectedSuggestions.size ===
                            categorySuggestions.length &&
                          categorySuggestions.length > 0
                        }
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all categories"
                        className="mr-2"
                      />
                      <Label
                        htmlFor="select-all"
                        className="text-sm font-medium"
                      >
                        Select All Categories
                      </Label>
                    </div>
                    <p className="text-sm text-[hsl(var(--giki-text-muted))]">
                      Showing {categorySuggestions.length} AI suggestions for{' '}
                      {selectedTransactions.length} transactions
                    </p>
                  </div>

                  <div className="overflow-auto rounded-md border shadow-md">
                    <Table>
                      <TableHeader className="bg-muted/50">
                        <TableRow>
                          <TableHead className="w-[40px]">Select</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead className="w-[120px] text-center">
                            Confidence
                          </TableHead>
                          <TableHead className="w-[100px] text-right">
                            Transactions
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {categorySuggestions.map((suggestion) => (
                          <TableRow
                            key={suggestion.categoryId}
                            className="hover:bg-muted/30 transition-colors"
                          >
                            <TableCell>
                              <Checkbox
                                checked={selectedSuggestions.has(
                                  suggestion.categoryId,
                                )}
                                onCheckedChange={() =>
                                  toggleSuggestion(suggestion.categoryId)
                                }
                                aria-label={`Select category ${suggestion.categoryPath}`}
                              />
                            </TableCell>
                            <TableCell className="font-medium">
                              <div className="flex flex-wrap items-center">
                                <Badge
                                  variant="outline"
                                  className="mr-2 px-1 py-0 h-5 w-5 flex flex-wrap items-center justify-center bg-background max-w-[150px] truncate"
                                >
                                  <Sparkles className="h-3 w-3" />
                                </Badge>
                                {suggestion.categoryPath}
                              </div>
                            </TableCell>
                            <TableCell className="text-center">
                              <Badge
                                variant="outline"
                                className={cn(
                                  getConfidenceBadgeClass(
                                    suggestion.confidence,
                                  ),
                                  'shadow-sm flex items-center justify-center max-w-[150px] truncate',
                                )}
                              >
                                {getConfidenceIcon(suggestion.confidence)}
                                {formatPercent(suggestion.confidence)}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right font-medium">
                              <Badge
                                variant="secondary"
                                className="shadow-sm flex flex-wrap items-center justify-center max-w-[150px] truncate"
                              >
                                <span className="flex flex-wrap items-center">
                                  <BarChart3 className="h-3 w-3 mr-1" />
                                  {suggestion.matchCount}{' '}
                                  {suggestion.matchCount === 1
                                    ? 'transaction'
                                    : 'transactions'}
                                </span>
                                <span className="ml-1 text-xs opacity-70">
                                  (
                                  {Math.round(
                                    (suggestion.matchCount /
                                      selectedTransactions.length) *
                                      100,
                                  )}
                                  %)
                                </span>
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="summary">
              <div className="space-y-4">
                <Alert
                  className={
                    categorySuggestions.length === 0
                      ? 'bg-amber-50 border-amber-200'
                      : 'bg-info/5 border-blue-200'
                  }
                >
                  <div className="flex flex-wrap items-center">
                    {categorySuggestions.length === 0 ? (
                      <AlertCircle className="h-4 w-4 text-amber-500" />
                    ) : (
                      <Lightbulb className="h-4 w-4 text-blue-500" />
                    )}
                  </div>
                  <AlertTitle>
                    {categorySuggestions.length === 0
                      ? 'No AI Suggestions Available'
                      : `AI Found ${categorySuggestions.length} Categories`}
                  </AlertTitle>
                  <AlertDescription>
                    {categorySuggestions.length === 0
                      ? 'Try adjusting the confidence threshold or updating your transaction selection.'
                      : `The AI has suggested categories for ${calculateCoverage() * 100}% of your selected transactions.`}
                  </AlertDescription>
                </Alert>

                {categorySuggestions.length > 0 && (
                  <>
                    <Card className="shadow-md">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">
                          Coverage Analysis
                        </CardTitle>
                        <CardDescription>
                          Percentage of transactions covered by AI suggestions
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="overflow-hidden">
                        <div className="space-y-4">
                          <div className="flex flex-wrap justify-between items-end">
                            <span className="text-2xl font-bold">
                              {formatPercent(calculateCoverage())}
                            </span>
                            <span className="text-sm text-[hsl(var(--giki-text-muted))]">
                              {selectedSuggestions.size} of{' '}
                              {categorySuggestions.length} categories selected
                            </span>
                          </div>
                          <Progress
                            value={calculateCoverage() * 100}
                            className="h-2"
                          />
                        </div>
                      </CardContent>
                    </Card>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card className="shadow-md">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">
                            Top Categories
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="overflow-hidden">
                          <ul className="space-y-2">
                            {categorySuggestions
                              .sort((a, b) => b.matchCount - a.matchCount)
                              .slice(0, 3)
                              .map((suggestion) => (
                                <li
                                  key={suggestion.categoryId}
                                  className="flex flex-wrap justify-between items-center"
                                >
                                  <span className="text-sm">
                                    {suggestion?.categoryPath
                                      ?.split(' > ')
                                      .pop()}
                                  </span>
                                  <Badge
                                    variant="outline"
                                    className="shadow-sm flex flex-wrap items-center max-w-[150px] truncate"
                                  >
                                    <BarChart3 className="h-3 w-3 mr-1" />
                                    {suggestion.matchCount} transactions
                                  </Badge>
                                </li>
                              ))}
                          </ul>
                        </CardContent>
                      </Card>

                      <Card className="shadow-md">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">
                            Confidence Levels
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="overflow-hidden">
                          <ul className="space-y-2">
                            {[
                              { label: 'Very High (90%+)', min: 0.9, max: 1.0 },
                              { label: 'High (80-89%)', min: 0.8, max: 0.89 },
                              { label: 'Medium (70-79%)', min: 0.7, max: 0.79 },
                              { label: 'Low (<70%)', min: 0, max: 0.69 },
                            ].map((level) => {
                              const count = categorySuggestions.filter(
                                (s) =>
                                  s.confidence >= level.min &&
                                  s.confidence <= level.max,
                              ).length;

                              return (
                                <li
                                  key={level.label}
                                  className="flex flex-wrap justify-between items-center"
                                >
                                  <span className="text-sm">{level.label}</span>
                                  <Badge
                                    variant="outline"
                                    className={cn(
                                      getConfidenceBadgeClass(
                                        (level.min + level.max) / 2,
                                      ),
                                      'flex items-center max-w-[150px] truncate',
                                    )}
                                  >
                                    {getConfidenceIcon(
                                      (level.min + level.max) / 2,
                                    )}
                                    {count}{' '}
                                    {count === 1 ? 'category' : 'categories'}
                                  </Badge>
                                </li>
                              );
                            })}
                          </ul>
                        </CardContent>
                      </Card>
                    </div>
                  </>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="flex flex-wrap items-center justify-between pt-4 border-t">
          <div className="text-sm text-text-secondary flex flex-wrap items-center gap-2">
            <Badge
              variant="outline"
              className="shadow-md flex flex-wrap items-center max-w-[150px] truncate"
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              {selectedTransactions.length} transactions
            </Badge>
            {selectedSuggestions.size > 0 && (
              <Badge
                variant="secondary"
                className="shadow-md flex flex-wrap items-center max-w-[150px] truncate"
              >
                <CheckCircle2 className="h-3 w-3 mr-1" />
                {selectedSuggestions.size} categories selected
                {categorySuggestions.length > 0 && (
                  <span className="ml-1 text-xs opacity-70">
                    (
                    {Math.round(
                      (selectedSuggestions.size / categorySuggestions.length) *
                        100,
                    )}
                    %)
                  </span>
                )}
              </Badge>
            )}
          </div>
          <div className="flex flex-wrap gap-3">
            <Button
              className="max-w-full shadow-md"
              variant="outline"
              onClick={() => void onClose()}
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              className="max-w-full shadow-md"
              onClick={() => void handleApplySuggestions()}
              disabled={
                isLoading || isProcessing || selectedSuggestions.size === 0
              }
            >
              {isProcessing ? (
                <Loading size="sm" text="Applying..." />
              ) : (
                <>
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  Apply Selected Categories
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AIBatchCategorizationModal;
