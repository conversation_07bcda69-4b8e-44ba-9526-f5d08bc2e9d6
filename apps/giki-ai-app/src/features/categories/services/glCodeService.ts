/**
 * GL Code Management Service
 * Professional service for M3 milestone GL code hierarchy and validation
 * Handles chart of accounts, compliance checking, and export validation
 */
import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError, ApiError } from '@/shared/utils/errorHandling';

export interface GLCode {
  id: string;
  code: string;
  name: string;
  description: string;
  level: number;
  parentId?: string;
  children?: GLCode[];
  isActive: boolean;
  accountType: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  category: string;
  subcategory?: string;
  metadata: {
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    compliance: {
      gaap: boolean;
      ifrs: boolean;
      sox: boolean;
    };
    mappings: {
      quickbooks?: string;
      sage?: string;
      netsuite?: string;
    };
  };
}

export interface GLCodeHierarchy {
  id: string;
  name: string;
  description: string;
  version: string;
  status: 'draft' | 'active' | 'archived';
  rootCodes: GLCode[];
  totalCodes: number;
  complianceLevel: 'basic' | 'standard' | 'enterprise';
  lastModified: string;
}

export interface GLCodeMapping {
  transactionId: string;
  originalCategory: string;
  suggestedGLCode: string;
  confidence: number;
  reason: string;
  alternativeOptions: Array<{
    code: string;
    confidence: number;
    reason: string;
  }>;
  validationStatus: 'pending' | 'approved' | 'rejected';
  reviewedBy?: string;
  reviewedAt?: string;
}

export interface GLCodeValidationResult {
  isValid: boolean;
  errors: Array<{
    code: string;
    message: string;
    severity: 'error' | 'warning' | 'info';
    field?: string;
  }>;
  warnings: string[];
  suggestions: Array<{
    type: 'structure' | 'naming' | 'compliance';
    message: string;
    recommendation: string;
  }>;
  complianceScore: number;
  coverageAnalysis: {
    totalTransactions: number;
    mappedTransactions: number;
    unmappedTransactions: number;
    coveragePercentage: number;
  };
}

// ============================================================================
// API RESPONSE INTERFACES
// ============================================================================

export interface GLCodeHierarchyResponse {
  data: GLCodeHierarchy[];
}

export interface GLCodeHierarchySingleResponse {
  data: GLCodeHierarchy;
}

export interface GLCodeResponse {
  data: GLCode[];
}

export interface GLCodeSingleResponse {
  data: GLCode;
}

export interface GLCodeValidationResponse {
  data: GLCodeValidationResult;
}

export interface GLCodeMappingResponse {
  data: GLCodeMapping[];
}

export interface GLCodeMappingSingleResponse {
  data: GLCodeMapping;
}

export interface GLCodeImportResponse {
  data: {
    success: boolean;
    importedCount: number;
    skippedCount: number;
    errors: Array<{
      row: number;
      message: string;
    }>;
  };
}

export interface GLCodeExportResponse {
  data: {
    downloadUrl: string;
    expiresAt: string;
  };
}

export interface GLCodeComplianceResponse {
  data: {
    overall: {
      score: number;
      status: 'compliant' | 'non_compliant' | 'partial';
      lastAudit: string;
    };
    gaap: {
      compliant: boolean;
      issues: string[];
      recommendations: string[];
    };
    ifrs: {
      compliant: boolean;
      issues: string[];
      recommendations: string[];
    };
    sox: {
      compliant: boolean;
      issues: string[];
      recommendations: string[];
    };
    auditTrail: Array<{
      timestamp: string;
      action: string;
      user: string;
      details: string;
    }>;
  };
}

export interface GLCodeSuggestionResponse {
  data: Array<{
    glCode: GLCode;
    confidence: number;
    reason: string;
  }>;
}

export interface GLCodeBulkUpdateResponse {
  data: {
    updated: number;
    failed: number;
    errors: Array<{
      mappingId: string;
      error: string;
    }>;
  };
}

export interface GLCodeAnalyticsResponse {
  data: {
    totalMappings: number;
    approvedMappings: number;
    pendingMappings: number;
    rejectedMappings: number;
    averageConfidence: number;
    topMappedCategories: Array<{
      category: string;
      count: number;
      averageConfidence: number;
    }>;
    complianceMetrics: {
      gaapCompliant: number;
      ifrsCompliant: number;
      soxCompliant: number;
    };
    accuracyTrends: Array<{
      date: string;
      accuracy: number;
      totalMappings: number;
    }>;
  };
}

// ============================================================================
// TYPE GUARDS
// ============================================================================

function _isGLCodeHierarchyResponse(
  data: unknown,
): data is GLCodeHierarchyResponse {
  return (
    typeof data === 'object' &&
    data !== null &&
    'data' in data &&
    Array.isArray((data as { data: unknown }).data)
  );
}

function _isGLCodeResponse(data: unknown): data is GLCodeResponse {
  return (
    typeof data === 'object' &&
    data !== null &&
    'data' in data &&
    Array.isArray((data as { data: unknown }).data)
  );
}

function _isGLCodeValidationResponse(
  data: unknown,
): data is GLCodeValidationResponse {
  return (
    typeof data === 'object' &&
    data !== null &&
    'data' in data &&
    typeof (data as { data: unknown }).data === 'object'
  );
}

class GLCodeService {
  private baseUrl = '/api/gl-codes';

  /**
   * Get GL code hierarchies
   */
  async getHierarchies(): Promise<GLCodeHierarchy[] | ApiError> {
    try {
      const response = await apiClient.get<GLCodeHierarchyResponse>(
        `${this.baseUrl}/hierarchies`,
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getHierarchies',
        defaultMessage: 'Failed to get GL code hierarchies.',
      });
    }
  }

  /**
   * Get specific GL code hierarchy
   */
  async getHierarchy(hierarchyId: string): Promise<GLCodeHierarchy | ApiError> {
    try {
      const response = await apiClient.get<GLCodeHierarchySingleResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}`,
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getHierarchy',
        defaultMessage: 'Failed to get GL code hierarchy.',
      });
    }
  }

  /**
   * Create new GL code hierarchy
   */
  async createHierarchy(
    hierarchy: Partial<GLCodeHierarchy>,
  ): Promise<GLCodeHierarchy | ApiError> {
    try {
      const response = await apiClient.post<GLCodeHierarchySingleResponse>(
        `${this.baseUrl}/hierarchies`,
        hierarchy,
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'createHierarchy',
        defaultMessage: 'Failed to create GL code hierarchy.',
      });
    }
  }

  /**
   * Update GL code hierarchy
   */
  async updateHierarchy(
    hierarchyId: string,
    updates: Partial<GLCodeHierarchy>,
  ): Promise<GLCodeHierarchy | ApiError> {
    try {
      const response = await apiClient.put<GLCodeHierarchySingleResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}`,
        updates,
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'updateHierarchy',
        defaultMessage: 'Failed to update GL code hierarchy.',
      });
    }
  }

  /**
   * Get GL codes for a hierarchy
   */
  async getGLCodes(
    hierarchyId: string,
    filters?: {
      accountType?: string;
      level?: number;
      isActive?: boolean;
      search?: string;
    },
  ): Promise<GLCode[] | ApiError> {
    try {
      const response = await apiClient.get<GLCodeResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/codes`,
        {
          params: filters,
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getGLCodes',
        defaultMessage: 'Failed to get GL codes.',
      });
    }
  }

  /**
   * Create new GL code
   */
  async createGLCode(
    hierarchyId: string,
    glCode: Partial<GLCode>,
  ): Promise<GLCode | ApiError> {
    try {
      const response = await apiClient.post<GLCodeSingleResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/codes`,
        glCode,
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'createGLCode',
        defaultMessage: 'Failed to create GL code.',
      });
    }
  }

  /**
   * Update GL code
   */
  async updateGLCode(
    hierarchyId: string,
    codeId: string,
    updates: Partial<GLCode>,
  ): Promise<GLCode | ApiError> {
    try {
      const response = await apiClient.put<GLCodeSingleResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/codes/${codeId}`,
        updates,
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'updateGLCode',
        defaultMessage: 'Failed to update GL code.',
      });
    }
  }

  /**
   * Delete GL code
   */
  async deleteGLCode(
    hierarchyId: string,
    codeId: string,
  ): Promise<void | ApiError> {
    try {
      await apiClient.delete<void>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/codes/${codeId}`,
      );
    } catch (error) {
      return handleApiError(error, {
        context: 'deleteGLCode',
        defaultMessage: 'Failed to delete GL code.',
      });
    }
  }

  /**
   * Validate GL code hierarchy
   */
  async validateHierarchy(
    hierarchyId: string,
  ): Promise<GLCodeValidationResult | ApiError> {
    try {
      const response = await apiClient.post<GLCodeValidationResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/validate`,
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'validateHierarchy',
        defaultMessage: 'Failed to validate GL code hierarchy.',
      });
    }
  }

  /**
   * Generate GL code mappings for transactions
   */
  async generateMappings(
    hierarchyId: string,
    transactionIds: string[],
    options?: {
      confidenceThreshold?: number;
      includeAlternatives?: boolean;
      autoApprove?: boolean;
    },
  ): Promise<GLCodeMapping[] | ApiError> {
    try {
      const response = await apiClient.post<GLCodeMappingResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/mappings`,
        {
          transactionIds,
          options,
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'generateMappings',
        defaultMessage: 'Failed to generate GL code mappings.',
      });
    }
  }

  /**
   * Approve GL code mapping
   */
  async approveMapping(
    mappingId: string,
    reviewNotes?: string,
  ): Promise<GLCodeMapping | ApiError> {
    try {
      const response = await apiClient.post<GLCodeMappingSingleResponse>(
        `${this.baseUrl}/mappings/${mappingId}/approve`,
        {
          reviewNotes,
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'approveMapping',
        defaultMessage: 'Failed to approve GL code mapping.',
      });
    }
  }

  /**
   * Reject GL code mapping
   */
  async rejectMapping(
    mappingId: string,
    reason: string,
    alternativeCode?: string,
  ): Promise<GLCodeMapping | ApiError> {
    try {
      const response = await apiClient.post<GLCodeMappingSingleResponse>(
        `${this.baseUrl}/mappings/${mappingId}/reject`,
        {
          reason,
          alternativeCode,
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'rejectMapping',
        defaultMessage: 'Failed to reject GL code mapping.',
      });
    }
  }

  /**
   * Import GL codes from file
   */
  async importGLCodes(
    hierarchyId: string,
    file: File,
    options?: {
      format: 'csv' | 'excel' | 'quickbooks' | 'sage';
      overwriteExisting?: boolean;
      validateOnly?: boolean;
    },
  ): Promise<
    | {
        success: boolean;
        importedCount: number;
        skippedCount: number;
        errors: Array<{
          row: number;
          message: string;
        }>;
      }
    | ApiError
  > {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('options', JSON.stringify(options));

      const response = await apiClient.post<GLCodeImportResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/import`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'importGLCodes',
        defaultMessage: 'Failed to import GL codes.',
      });
    }
  }

  /**
   * Export GL codes
   */
  async exportGLCodes(
    hierarchyId: string,
    format: 'csv' | 'excel' | 'pdf' | 'json' = 'excel',
  ): Promise<{ downloadUrl: string; expiresAt: string } | ApiError> {
    try {
      const response = await apiClient.post<GLCodeExportResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/export`,
        {
          format,
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'exportGLCodes',
        defaultMessage: 'Failed to export GL codes.',
      });
    }
  }

  /**
   * Get compliance report
   */
  async getComplianceReport(hierarchyId: string): Promise<
    | {
        overall: {
          score: number;
          status: 'compliant' | 'non_compliant' | 'partial';
          lastAudit: string;
        };
        gaap: {
          compliant: boolean;
          issues: string[];
          recommendations: string[];
        };
        ifrs: {
          compliant: boolean;
          issues: string[];
          recommendations: string[];
        };
        sox: {
          compliant: boolean;
          issues: string[];
          recommendations: string[];
        };
        auditTrail: Array<{
          timestamp: string;
          action: string;
          user: string;
          details: string;
        }>;
      }
    | ApiError
  > {
    try {
      const response = await apiClient.get<GLCodeComplianceResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/compliance`,
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getComplianceReport',
        defaultMessage: 'Failed to get compliance report.',
      });
    }
  }

  /**
   * Search GL codes
   */
  async searchGLCodes(
    hierarchyId: string,
    query: string,
    filters?: {
      accountType?: string;
      level?: number;
    },
  ): Promise<GLCode[] | ApiError> {
    try {
      const response = await apiClient.get<GLCodeResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/search`,
        {
          params: { query, ...filters },
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'searchGLCodes',
        defaultMessage: 'Failed to search GL codes.',
      });
    }
  }

  /**
   * Get mapping suggestions for category
   */
  async getMappingSuggestions(
    hierarchyId: string,
    category: string,
    transactionContext?: {
      amount?: number;
      description?: string;
      vendor?: string;
    },
  ): Promise<
    | Array<{
        glCode: GLCode;
        confidence: number;
        reason: string;
      }>
    | ApiError
  > {
    try {
      const response = await apiClient.post<GLCodeSuggestionResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/suggestions`,
        {
          category,
          transactionContext,
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getMappingSuggestions',
        defaultMessage: 'Failed to get mapping suggestions.',
      });
    }
  }

  /**
   * Bulk update GL code mappings
   */
  async bulkUpdateMappings(
    mappingUpdates: Array<{
      mappingId: string;
      action: 'approve' | 'reject' | 'modify';
      newGLCode?: string;
      notes?: string;
    }>,
  ): Promise<
    | {
        updated: number;
        failed: number;
        errors: Array<{
          mappingId: string;
          error: string;
        }>;
      }
    | ApiError
  > {
    try {
      const response = await apiClient.post<GLCodeBulkUpdateResponse>(
        `${this.baseUrl}/mappings/bulk-update`,
        {
          updates: mappingUpdates,
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'bulkUpdateMappings',
        defaultMessage: 'Failed to bulk update GL code mappings.',
      });
    }
  }

  /**
   * Get mapping analytics
   */
  async getMappingAnalytics(
    hierarchyId: string,
    timeRange?: {
      start: string;
      end: string;
    },
  ): Promise<
    | {
        totalMappings: number;
        approvedMappings: number;
        pendingMappings: number;
        rejectedMappings: number;
        averageConfidence: number;
        topMappedCategories: Array<{
          category: string;
          count: number;
          averageConfidence: number;
        }>;
        complianceMetrics: {
          gaapCompliant: number;
          ifrsCompliant: number;
          soxCompliant: number;
        };
        accuracyTrends: Array<{
          date: string;
          accuracy: number;
          totalMappings: number;
        }>;
      }
    | ApiError
  > {
    try {
      const response = await apiClient.get<GLCodeAnalyticsResponse>(
        `${this.baseUrl}/hierarchies/${hierarchyId}/analytics`,
        {
          params: timeRange,
        },
      );
      return response.data.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getMappingAnalytics',
        defaultMessage: 'Failed to get mapping analytics.',
      });
    }
  }
}

// Singleton instance
export const glCodeService = new GLCodeService();

export default GLCodeService;
