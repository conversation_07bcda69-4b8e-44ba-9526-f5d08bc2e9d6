import type {
  GLCodeValidation,
  GLCodeSuggestion,
  GLCodeAnalytics,
} from '@/shared/types/category';
import { apiClient } from '@/shared/services/api/apiClient'; // Corrected import path
import { handleApiError, type ApiError } from '@/shared/utils/errorHandling'; // Corrected ApiError import

export interface Category {
  id: number;
  name: string;
  parent_id: number | null;
  path: string;
  children?: Category[]; // Optional children for hierarchical data
  gl_code?: string; // Optional GL code for accounting integration
  gl_account_name?: string;
  gl_account_type?: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  // Add any other fields that might be part of the category
}

export interface AddCategoryPayload {
  name: string;
  parentId: number | null;
}

/**
 * Adds a new category to the server.
 * @param payload - The data for the new category.
 * @returns A promise that resolves with the new category or an ApiError.
 */
export const addCategory = async (
  payload: AddCategoryPayload,
): Promise<Category | ApiError> => {
  try {
    const response = await apiClient.post<Category>(
      '/categories',
      { name: payload.name, parent_id: payload.parentId }, // Match backend expectation
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      // error is implicitly unknown
      context: 'addCategory',
      defaultMessage: 'Failed to add category.',
    });
  }
};

/**
 * Fetches all categories from the server.
 * @returns A promise that resolves with an array of categories or an ApiError.
 */
export const fetchCategories = async (): Promise<Category[] | ApiError> => {
  try {
    const response = await apiClient.get<Category[]>('/categories');
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      // error is implicitly unknown
      context: 'fetchCategories',
      defaultMessage: 'Failed to fetch categories.',
    });
  }
};

export interface UpdateCategoryPayload {
  name: string;
}

// Additional types for GL-related operations
export interface GLTemplate {
  id: string;
  name: string;
  gl_code: string;
  gl_account_name: string;
  gl_account_type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  industry: string;
  description?: string;
}

export interface CacheStats {
  total_entries: number;
  hit_rate: number;
  last_updated: string;
  memory_usage_mb: number;
}

export interface CacheWarmResult {
  categories_cached: number;
  time_elapsed_ms: number;
  success: boolean;
}

/**
 * Updates an existing category on the server.
 * @param categoryId - The ID of the category to update.
 * @param newName - The new name for the category.
 * @returns A promise that resolves with the updated category or an ApiError.
 */
export const updateCategory = async (
  categoryId: number,
  newName: string,
): Promise<Category | ApiError> => {
  try {
    const response = await apiClient.put<Category>(
      `/categories/${categoryId}`,
      { name: newName },
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'updateCategory',
      defaultMessage: 'Failed to update category.',
    });
  }
};

/**
 * Deletes a category from the server.
 * @param categoryId - The ID of the category to delete.
 * @returns A promise that resolves with no content or an ApiError.
 */
export const deleteCategory = async (
  categoryId: number,
): Promise<void | ApiError> => {
  try {
    await apiClient.delete(`/categories/${categoryId}`);
    return Promise.resolve();
  } catch (error) {
    return handleApiError(error, {
      context: 'deleteCategory',
      defaultMessage: 'Failed to delete category.',
    });
  }
};

// Service object for easier testing and imports
export const categoryService = {
  getCategories: async (
    activeOnly?: boolean,
  ): Promise<Category[] | ApiError> => {
    try {
      const params = activeOnly ? { active_only: true } : undefined;
      const response = await apiClient.get<Category[]>('/categories', {
        params,
      });
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getCategories',
        defaultMessage: 'Failed to fetch categories.',
      });
    }
  },

  getCategory: async (id: number): Promise<Category | ApiError> => {
    try {
      const response = await apiClient.get<Category>(`/categories/${id}`);
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getCategory',
        defaultMessage: 'Failed to fetch category.',
      });
    }
  },

  createCategory: async (
    data: AddCategoryPayload,
  ): Promise<Category | ApiError> => {
    try {
      const response = await apiClient.post<Category>('/categories', data);
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'createCategory',
        defaultMessage: 'Failed to create category.',
      });
    }
  },

  updateCategory: async (
    id: number,
    data: Partial<Category>,
  ): Promise<Category | ApiError> => {
    try {
      const response = await apiClient.put<Category>(`/categories/${id}`, data);
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'updateCategory',
        defaultMessage: 'Failed to update category.',
      });
    }
  },

  deleteCategory: async (id: number): Promise<void | ApiError> => {
    try {
      await apiClient.delete(`/categories/${id}`);
    } catch (error) {
      return handleApiError(error, {
        context: 'deleteCategory',
        defaultMessage: 'Failed to delete category.',
      });
    }
  },

  suggestCategory: async (
    description: string,
    amount: number,
  ): Promise<unknown> => {
    try {
      const response = await apiClient.post('/categories/suggest', {
        description,
        amount,
      });
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'suggestCategory',
        defaultMessage: 'Failed to suggest category.',
      });
    }
  },

  getCategoryStats: async (
    dateFrom?: string,
    dateTo?: string,
  ): Promise<unknown[]> => {
    try {
      const params =
        dateFrom && dateTo
          ? { date_from: dateFrom, date_to: dateTo }
          : undefined;
      const response = await apiClient.get<unknown[]>('/categories/stats', {
        params,
      });
      return response.data;
    } catch {
      return [];
    }
  },

  bulkCategorizeTransactions: async (
    transactionIds: number[],
    categoryId: number,
  ): Promise<unknown> => {
    try {
      const response = await apiClient.put('/categories/bulk-categorize', {
        transaction_ids: transactionIds,
        category_id: categoryId,
      });
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'bulkCategorizeTransactions',
        defaultMessage: 'Failed to bulk categorize transactions.',
      });
    }
  },

  // ============================================================================
  // GL Code Management Methods
  // ============================================================================

  validateGLCode: async (
    glCode: string,
  ): Promise<GLCodeValidation | ApiError> => {
    try {
      const response = await apiClient.post<GLCodeValidation>(
        '/categories/validate-gl-code',
        {
          gl_code: glCode,
        },
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'validateGLCode',
        defaultMessage: 'Failed to validate GL code.',
      });
    }
  },

  suggestGLCodes: async (
    categoryName: string,
    categoryPath: string,
    accountType?: string,
  ): Promise<GLCodeSuggestion[] | ApiError> => {
    try {
      const response = await apiClient.post<GLCodeSuggestion[]>(
        '/categories/suggest-gl-codes',
        {
          category_name: categoryName,
          category_path: categoryPath,
          account_type: accountType,
        },
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'suggestGLCodes',
        defaultMessage: 'Failed to get GL code suggestions.',
      });
    }
  },

  updateGLCodeMapping: async (
    categoryId: number,
    glCode?: string,
    glAccountName?: string,
    glAccountType?: string,
  ): Promise<Category | ApiError> => {
    try {
      const response = await apiClient.put<Category>(
        `/categories/${categoryId}/gl-mapping`,
        {
          gl_code: glCode,
          gl_account_name: glAccountName,
          gl_account_type: glAccountType,
        },
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'updateGLCodeMapping',
        defaultMessage: 'Failed to update GL code mapping.',
      });
    }
  },

  getGLCodeAnalytics: async (): Promise<GLCodeAnalytics | ApiError> => {
    try {
      const response = await apiClient.get<GLCodeAnalytics>(
        '/categories/gl-analytics',
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getGLCodeAnalytics',
        defaultMessage: 'Failed to fetch GL code analytics.',
      });
    }
  },

  autoAssignGLCodes: async (dryRun: boolean = true): Promise<unknown> => {
    try {
      const response = await apiClient.post(
        '/categories/auto-assign-gl-codes',
        {
          dry_run: dryRun,
        },
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'autoAssignGLCodes',
        defaultMessage: 'Failed to auto-assign GL codes.',
      });
    }
  },

  bulkUpdateGLMappings: async (
    mappings: Array<{
      category_id: number;
      gl_code?: string;
      gl_account_name?: string;
      gl_account_type?: string;
    }>,
  ): Promise<unknown> => {
    try {
      const response = await apiClient.put('/categories/bulk-gl-mappings', {
        mappings,
      });
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'bulkUpdateGLMappings',
        defaultMessage: 'Failed to bulk update GL mappings.',
      });
    }
  },

  exportGLMappings: async (
    format: string = 'csv',
  ): Promise<string | ApiError> => {
    try {
      const response = await apiClient.get<string>(
        `/categories/export-gl-mappings?format=${format}`,
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'exportGLMappings',
        defaultMessage: 'Failed to export GL mappings.',
      });
    }
  },

  // ============================================================================
  // M3 Giki Enhanced Industry-Specific Methods
  // ============================================================================

  getIndustryGLTemplates: async (
    industry: string,
  ): Promise<{ templates: GLTemplate[] } | ApiError> => {
    try {
      const response = await apiClient.get<{ templates: GLTemplate[] }>(
        `/categories/industry-templates/${industry}`,
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getIndustryGLTemplates',
        defaultMessage: 'Failed to fetch industry GL templates.',
      });
    }
  },

  suggestGLCodesWithIndustryContext: async (
    categoryName: string,
    categoryPath: string,
    accountType: string,
    industry: string,
    tenantId?: number,
  ): Promise<GLCodeSuggestion[] | ApiError> => {
    try {
      const response = await apiClient.post<GLCodeSuggestion[]>(
        '/categories/suggest-gl-codes-enhanced',
        {
          category_name: categoryName,
          category_path: categoryPath,
          account_type: accountType,
          industry: industry,
          tenant_id: tenantId,
        },
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'suggestGLCodesWithIndustryContext',
        defaultMessage: 'Failed to get industry-enhanced GL code suggestions.',
      });
    }
  },

  getGLCodeCacheStats: async (): Promise<CacheStats | ApiError> => {
    try {
      const response = await apiClient.get<CacheStats>(
        '/categories/cache-stats',
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getGLCodeCacheStats',
        defaultMessage: 'Failed to fetch GL code cache statistics.',
      });
    }
  },

  warmGLCodeCache: async (
    tenantId?: number,
  ): Promise<CacheWarmResult | ApiError> => {
    try {
      const response = await apiClient.post<CacheWarmResult>(
        '/categories/warm-cache',
        {
          tenant_id: tenantId,
        },
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'warmGLCodeCache',
        defaultMessage: 'Failed to warm GL code cache.',
      });
    }
  },

  getIndustrySpecificCategories: async (
    industry: string,
  ): Promise<Category[] | ApiError> => {
    try {
      const response = await apiClient.get<Category[]>(
        `/categories/industry-categories/${industry}`,
      );
      return response.data;
    } catch (error) {
      return handleApiError(error, {
        context: 'getIndustrySpecificCategories',
        defaultMessage: 'Failed to fetch industry-specific categories.',
      });
    }
  },
};
