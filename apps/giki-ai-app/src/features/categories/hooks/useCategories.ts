/**
 * Categories Hook
 *
 * Custom hook for managing category operations.
 */

import { useState, useCallback } from 'react';
import { Category } from '@/shared/types/categorization';
import type { ApiError } from '@/shared/utils/errorHandling';

const isApiError = (value: unknown): value is ApiError => {
  return typeof value === 'object' && value !== null && 'type' in value;
};

export interface UseCategoriesReturn {
  categories: Category[];
  isLoading: boolean;
  error: string | null;
  fetchCategories: () => Promise<void>;
  createCategory: (category: Omit<Category, 'id'>) => Promise<Category>;
  updateCategory: (id: number, updates: Partial<Category>) => Promise<Category>;
  deleteCategory: (id: number) => Promise<void>;
}

export const useCategories = (): UseCategoriesReturn => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      // Use the category service to fetch categories
      const { fetchCategories: fetchCategoriesApi } = await import(
        '../services/categoryService'
      );
      const response = await fetchCategoriesApi();

      if (isApiError(response)) {
        throw new Error(response.message || 'Failed to fetch categories');
      }

      // Map the response to the expected format
      const mappedCategories: Category[] = response.map(
        (cat: {
          id: number;
          name: string;
          path?: string;
          parent_id?: number | null;
          level?: number;
          is_active?: boolean;
          active?: boolean;
          gl_code?: string;
        }) => ({
          id: cat.id,
          name: cat.name,
          path: cat.path ?? cat.name,
          parent_id: cat.parent_id ?? undefined,
          level: cat.level ?? 0,
          active: cat.is_active !== false || cat.active !== false,
          gl_code: cat.gl_code ?? undefined,
        }),
      );

      setCategories(mappedCategories);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch categories';
      setError(errorMessage);
      console.error('Error fetching categories:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createCategory = useCallback(
    async (category: Omit<Category, 'id'>): Promise<Category> => {
      try {
        const { addCategory: apiCreateCategory } = await import(
          '../services/categoryService'
        );
        const response = await apiCreateCategory({
          name: category.name,
          parentId: category.parent_id || null,
        });

        if (isApiError(response)) {
          throw new Error(response.message || 'Failed to create category');
        }

        const newCategory: Category = {
          id: response.id,
          name: response.name,
          path: response.path || response.name,
          parent_id: response.parent_id || undefined,
          level: 0,
          active: true,
          gl_code: undefined,
        };

        setCategories((prev) => [...prev, newCategory]);
        return newCategory;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to create category';
        setError(errorMessage);
        throw err;
      }
    },
    [],
  );

  const updateCategory = useCallback(
    async (id: number, updates: Partial<Category>): Promise<Category> => {
      try {
        const { updateCategory: apiUpdateCategory } = await import(
          '../services/categoryService'
        );
        const response = await apiUpdateCategory(id, updates.name || '');

        if (isApiError(response)) {
          throw new Error(response.message || 'Failed to update category');
        }

        const updatedCategory: Category = {
          id: response.id,
          name: response.name,
          path: response.path || response.name,
          parent_id: response.parent_id || undefined,
          level: 0,
          active: true,
          gl_code: undefined,
          ...updates,
        };

        setCategories((prev) =>
          prev.map((cat) => (cat.id === id ? updatedCategory : cat)),
        );

        return updatedCategory;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to update category';
        setError(errorMessage);
        throw err;
      }
    },
    [],
  );

  const deleteCategory = useCallback(async (id: number): Promise<void> => {
    try {
      const { deleteCategory: apiDeleteCategory } = await import(
        '../services/categoryService'
      );
      const response = await apiDeleteCategory(id);

      if (response && isApiError(response)) {
        throw new Error(response.message || 'Failed to delete category');
      }

      setCategories((prev) => prev.filter((cat) => cat.id !== id));
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to delete category';
      setError(errorMessage);
      throw err;
    }
  }, []);

  return {
    categories,
    isLoading,
    error,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
  };
};
