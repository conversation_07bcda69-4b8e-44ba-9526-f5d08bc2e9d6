/**
 * Categories Feature Barrel Exports
 *
 * This file provides clean imports for all category-related
 * components, hooks, and services.
 */

// Components
export { default as CategoryManagementView } from './components/CategoryManagementView';
export { default as CategorySelector } from './components/CategorySelector';
export { default as CategoryBatchTool } from './components/CategoryBatchTool';
export { default as GLCodeManager } from './components/GLCodeManager';
export { default as GLCodeBulkManager } from './components/GLCodeBulkManager';
export { default as GLCodeAnalyticsDashboard } from './components/GLCodeAnalyticsDashboard';
export { default as AIBatchCategorizationModal } from './components/AIBatchCategorizationModal';

// Pages
export { default as CategoryManagementPage } from './pages/CategoryManagementPage';

// Services
export * from './services/categoryService';

// Types
export type { Category as CategoryType } from './types/category';
export * from './types/categorization';

// Hooks
export * from './hooks/useCategories';
