/**
 * Categories Management Page - Professional B2B Category Management
 * Matches wireframe: docs/wireframes/03-daily-use-journey/04-categories.md
 * Brand consistency: Professional Excel-inspired design with #295343 green
 */
import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useWebSocket, useWebSocketStatus } from '@/shared/services/websocket/WebSocketService';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Badge } from '@/shared/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { Checkbox } from '@/shared/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import { gradientButtonStyle, applyGradientHover } from '@/shared/utils/brandGradients';
// Removed Lucide imports - replaced with geometric icons
// TreePine → ▲, Plus → +, Edit → ✎, Trash2 → ✗, Download → ↓
// Upload → ↑, Search → ⌕, Filter → ∷, MoreHorizontal → ⋯
// TrendingUp → ↗, Target → ◯, FolderTree → ☖, ChevronRight → ›, ChevronDown → ⌄
import {
  formatCurrencyForTable,
  getFinancialDisplayClass,
} from '@/shared/utils/formatCurrency';
import { StandardLoadingStates } from '@/shared/components/ui/standardized-loading';
import '@/styles/layout-variables.css';

interface CategoryNode {
  id: string;
  name: string;
  glCode?: string;
  parent?: string;
  level: number;
  children: CategoryNode[];
  transactionCount: number;
  totalAmount: number;
  lastUsed?: Date;
  isExpanded?: boolean;
  budget?: number;
  description?: string;
}

interface CategoryStats {
  totalCategories: number;
  activeCategories: number;
  hierarchyLevels: number;
  unmappedTransactions: number;
}

const CategoriesPage: React.FC = () => {
  const navigate = useNavigate();

  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('all');
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<CategoryNode | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);

  // Mock data - in production this would come from API
  const [categories, setCategories] = useState<CategoryNode[]>([
    {
      id: '1',
      name: 'Operating Expenses',
      glCode: '5000',
      level: 0,
      children: [
        {
          id: '11',
          name: 'Office Supplies',
          glCode: '5100',
          parent: '1',
          level: 1,
          children: [],
          transactionCount: 34,
          totalAmount: 2450,
          lastUsed: new Date(2025, 2, 20),
          budget: 3000,
          description: 'General office supplies and materials',
        },
        {
          id: '12',
          name: 'Software & Tools',
          glCode: '5200',
          parent: '1',
          level: 1,
          children: [
            {
              id: '121',
              name: 'SaaS Subscriptions',
              glCode: '5210',
              parent: '12',
              level: 2,
              children: [],
              transactionCount: 12,
              totalAmount: 3200,
              lastUsed: new Date(2025, 2, 22),
            },
          ],
          transactionCount: 19,
          totalAmount: 5670,
          lastUsed: new Date(2025, 2, 22),
          budget: 6000,
          isExpanded: true,
        },
      ],
      transactionCount: 53,
      totalAmount: 8120,
      lastUsed: new Date(2025, 2, 22),
      isExpanded: true,
      budget: 15000,
    },
    {
      id: '2',
      name: 'Revenue',
      glCode: '4000',
      level: 0,
      children: [
        {
          id: '21',
          name: 'Product Sales',
          glCode: '4100',
          parent: '2',
          level: 1,
          children: [],
          transactionCount: 23,
          totalAmount: 45000,
          lastUsed: new Date(2025, 2, 21),
        },
        {
          id: '22',
          name: 'Service Revenue',
          glCode: '4200',
          parent: '2',
          level: 1,
          children: [],
          transactionCount: 15,
          totalAmount: 28000,
          lastUsed: new Date(2025, 2, 19),
        },
      ],
      transactionCount: 38,
      totalAmount: 73000,
      lastUsed: new Date(2025, 2, 21),
      isExpanded: false,
    },
    {
      id: '3',
      name: 'Travel & Entertainment',
      glCode: '5300',
      level: 0,
      children: [],
      transactionCount: 27,
      totalAmount: 7230,
      lastUsed: new Date(2025, 2, 18),
      budget: 8000,
      description: 'Business travel, meals, and client entertainment',
    },
  ]);

  const [stats] = useState<CategoryStats>({
    totalCategories: 7,
    activeCategories: 6,
    hierarchyLevels: 3,
    unmappedTransactions: 12,
  });

  // Simulate loading for consistent UX
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);
    return () => clearTimeout(timer);
  }, []);

  // WebSocket connection status
  const { connected: isWebSocketConnected } = useWebSocketStatus();

  // Show loading state
  if (isLoading) {
    return (
      <div className="container-padding min-h-screen flex items-center justify-center">
        <StandardLoadingStates.Dashboard />
      </div>
    );
  }

  // Real-time WebSocket event handlers for category updates
  useWebSocket('category.created', (payload: any) => {
    // Add new category to the list in real-time
    const newCategory: CategoryNode = {
      id: payload.id || `cat_${Date.now()}`,
      name: payload.name || 'New Category',
      glCode: payload.gl_code || payload.glCode,
      level: payload.level || 0,
      parent: payload.parent_id || payload.parent,
      children: [],
      transactionCount: 0,
      totalAmount: 0,
      lastUsed: new Date(),
      isExpanded: false,
      budget: payload.budget,
      description: payload.description,
    };

    setCategories(prev => {
      if (payload.parent_id || payload.parent) {
        // Add as child category
        return prev.map(cat => {
          if (cat.id === (payload.parent_id || payload.parent)) {
            return {
              ...cat,
              children: [...cat.children, newCategory]
            };
          }
          return cat;
        });
      } else {
        // Add as top-level category
        return [...prev, newCategory];
      }
    });
  }, []);

  useWebSocket('category.updated', (payload: any) => {
    // Update existing category in real-time
    setCategories(prev => {
      const updateCategoryInTree = (categories: CategoryNode[]): CategoryNode[] => {
        return categories.map(cat => {
          if (cat.id === payload.id) {
            return {
              ...cat,
              name: payload.name || cat.name,
              glCode: payload.gl_code || payload.glCode || cat.glCode,
              budget: payload.budget ?? cat.budget,
              description: payload.description || cat.description,
            };
          }
          if (cat.children.length > 0) {
            return {
              ...cat,
              children: updateCategoryInTree(cat.children)
            };
          }
          return cat;
        });
      };
      return updateCategoryInTree(prev);
    });
  }, []);

  useWebSocket('transaction.categorized', (payload: any) => {
    // Update transaction counts when transactions are categorized
    if (payload.category_id) {
      setCategories(prev => {
        const updateTransactionCount = (categories: CategoryNode[]): CategoryNode[] => {
          return categories.map(cat => {
            if (cat.id === payload.category_id) {
              return {
                ...cat,
                transactionCount: cat.transactionCount + 1,
                totalAmount: cat.totalAmount + (payload.amount || 0),
                lastUsed: new Date(),
              };
            }
            if (cat.children.length > 0) {
              return {
                ...cat,
                children: updateTransactionCount(cat.children)
              };
            }
            return cat;
          });
        };
        return updateTransactionCount(prev);
      });
    }
  }, []);

  // Flatten categories for table display
  const flattenCategories = (nodes: CategoryNode[]): CategoryNode[] => {
    const result: CategoryNode[] = [];

    const traverse = (node: CategoryNode) => {
      result.push(node);
      if (node.isExpanded && node.children.length > 0) {
        node.children.forEach((child) => traverse(child));
      }
    };

    nodes.forEach((node) => traverse(node));
    return result;
  };

  // Toggle category expansion
  const toggleExpansion = useCallback(
    (categoryId: string) => {
      const updateNode = (nodes: CategoryNode[]): CategoryNode[] => {
        return nodes.map((node) => {
          if (node.id === categoryId) {
            return { ...node, isExpanded: !node.isExpanded };
          }
          if (node.children.length > 0) {
            return { ...node, children: updateNode(node.children) };
          }
          return node;
        });
      };

      setCategories(updateNode(categories));
    },
    [categories],
  );

  // Handle selection
  const handleSelectCategory = useCallback(
    (categoryId: string, checked: boolean) => {
      setSelectedIds((prev) => {
        const newSet = new Set(prev);
        if (checked) {
          newSet.add(categoryId);
        } else {
          newSet.delete(categoryId);
        }
        return newSet;
      });
    },
    [],
  );

  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        const allIds = flattenCategories(categories).map((cat) => cat.id);
        setSelectedIds(new Set(allIds));
      } else {
        setSelectedIds(new Set());
      }
    },
    [categories],
  );

  // Filter categories
  const filteredCategories = categories.filter((category) => {
    const matchesSearch =
      category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (category.glCode && category.glCode.includes(searchQuery)) ||
      (category.description &&
        category.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesLevel =
      selectedLevel === 'all' ||
      (selectedLevel === '0' && category.level === 0) ||
      (selectedLevel === '1' && category.level === 1) ||
      (selectedLevel === '2' && category.level === 2);

    return matchesSearch && matchesLevel;
  });

  // Bulk operations
  const handleBulkDelete = useCallback(() => {
    const confirmed = window.confirm(
      `Delete ${selectedIds.size} categories? This action cannot be undone.`,
    );
    if (confirmed) {
     ('Deleting categories:', Array.from(selectedIds));
      setSelectedIds(new Set());
    }
  }, [selectedIds]);

  const handleBulkExport = useCallback(() => {
    const selectedCategories = flatCategories.filter(cat => selectedIds.has(cat.id));
    
    if (selectedCategories.length === 0) {
      alert('Please select categories to export.');
      return;
    }

    // Create export URL with selected category IDs for the reports page
    const categoryIds = Array.from(selectedIds).join(',');
    const exportUrl = `/reports?export=true&categories=${categoryIds}&type=category-breakdown`;
    
    // Open export page with category filter in new tab
    window.open(exportUrl, '_blank');
    
    // Show success message
    const categoryNames = selectedCategories.slice(0, 3).map(c => c.name).join(', ');
    const message = selectedCategories.length > 3 
      ? `${categoryNames} and ${selectedCategories.length - 3} more categories`
      : categoryNames;
    
    alert(`Opening export for: ${message}`);
  }, [selectedIds, flatCategories]);

  // Create/Edit handlers
  const handleCreateCategory = useCallback(() => {
    setEditingCategory(null);
    setIsCreateDialogOpen(true);
  }, []);

  const handleEditCategory = useCallback((category: CategoryNode) => {
    setEditingCategory(category);
    setIsCreateDialogOpen(true);
  }, []);

  // Render category row with indentation - Enhanced mobile responsiveness
  const renderCategoryRow = (category: CategoryNode) => {
    const hasChildren = category.children.length > 0;
    const indentLevel = category.level * (window.innerWidth < 640 ? 12 : 24); // Reduced indent on mobile

    return (
      <TableRow key={category.id} className="hover:bg-gray-50">
        <TableCell className="w-8 sm:w-12 py-2 sm:py-3">
          <Checkbox
            checked={selectedIds.has(category.id)}
            onCheckedChange={(checked) =>
              handleSelectCategory(category.id, checked as boolean)
            }
            className="scale-90 sm:scale-100"
          />
        </TableCell>

        <TableCell className="py-2 sm:py-3 min-w-[120px]">
          <div
            className="flex items-center"
            style={{ paddingLeft: `${indentLevel}px` }}
          >
            {hasChildren && (
              <button
                onClick={() => toggleExpansion(category.id)}
                className="mr-1 sm:mr-2 p-1 hover:bg-gray-200 rounded touch-target"
              >
                {category.isExpanded ? (
                  <span className="w-3 h-3 sm:w-4 sm:h-4 text-sm sm:text-lg font-bold flex items-center justify-center">⌄</span>
                ) : (
                  <span className="w-3 h-3 sm:w-4 sm:h-4 text-sm sm:text-lg font-bold flex items-center justify-center">›</span>
                )}
              </button>
            )}
            <span className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 text-success text-sm sm:text-lg font-bold flex items-center justify-center">▲</span>
            <div className="min-w-0 flex-1">
              <div className="font-medium text-gray-800 text-sm sm:text-base truncate">{category.name}</div>
              {category.description && (
                <div className="text-xs text-gray-500 truncate sm:whitespace-normal">
                  {category.description}
                </div>
              )}
              {/* Show GL Code on mobile since column is hidden */}
              <div className="sm:hidden text-xs text-gray-400 font-mono">
                {category.glCode || 'No GL Code'}
              </div>
            </div>
          </div>
        </TableCell>

        <TableCell className="font-mono text-sm py-2 sm:py-3 hidden sm:table-cell">
          {category.glCode || '-'}
        </TableCell>

        <TableCell className="text-center py-2 sm:py-3 hidden md:table-cell">
          <Badge variant="outline" className="text-xs">
            Level {category.level}
          </Badge>
        </TableCell>

        <TableCell className="font-mono text-right py-2 sm:py-3 hidden lg:table-cell text-sm">
          {category.transactionCount}
        </TableCell>

        <TableCell className={`${getFinancialDisplayClass('currency')} py-2 sm:py-3 text-right`}>
          <div className="text-sm sm:text-base">
            {formatCurrencyForTable(category.totalAmount)}
          </div>
          {/* Show transaction count on mobile since column is hidden */}
          <div className="lg:hidden text-xs text-gray-500">
            {category.transactionCount} transactions
          </div>
        </TableCell>

        <TableCell className="py-2 sm:py-3 hidden xl:table-cell">
          {category.budget && (
            <div className="flex items-center gap-2">
              <span className={`${getFinancialDisplayClass('small')} text-sm`}>
                {formatCurrencyForTable(category.budget)}
              </span>
              <div
                className={`w-2 h-2 rounded-full ${
                  category.totalAmount > category.budget
                    ? 'bg-destructive'
                    : 'bg-success'
                }`}
              ></div>
            </div>
          )}
        </TableCell>

        <TableCell className="text-sm text-gray-500 py-2 sm:py-3 hidden lg:table-cell">
          {category.lastUsed?.toLocaleDateString()}
        </TableCell>

        <TableCell className="py-2 sm:py-3">
          <div className="flex items-center gap-0.5 sm:gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEditCategory(category)}
              className="h-8 w-8 p-0 touch-target"
            >
              <span className="w-3 h-3 sm:w-4 sm:h-4 text-sm sm:text-lg font-bold flex items-center justify-center">✎</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-destructive hover:text-destructive/80 h-8 w-8 p-0 touch-target"
              onClick={() =>('Delete category:', category.id)}
            >
              <span className="w-3 h-3 sm:w-4 sm:h-4 text-sm sm:text-lg font-bold flex items-center justify-center">✗</span>
            </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 touch-target">
              <span className="w-3 h-3 sm:w-4 sm:h-4 text-sm sm:text-lg font-bold flex items-center justify-center">⋯</span>
            </Button>
          </div>
        </TableCell>
      </TableRow>
    );
  };

  return (
    <div className="p-page min-h-screen" style={{ backgroundColor: '#ffffff' }}>
      {/* Enhanced Header with Better Visual Hierarchy */}
      <div className="space-section">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-semibold text-gray-700 space-component">
              Category Management
            </h1>
            <p className="text-gray-500 text-base">
              Organize transaction categories and GL code mappings
            </p>
          </div>
          <div className="flex items-center gap-element">
            <Button variant="outline">
              <span className="w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center">↑</span>
              Import
            </Button>
            <Button 
              variant="outline"
              onClick={() => {
                const exportUrl = `/reports?export=true&type=category-breakdown&all=true`;
                window.open(exportUrl, '_blank');
              }}
            >
              <span className="w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center">↓</span>
              Export Categories
            </Button>
            <Button
              onClick={handleCreateCategory}
              className="text-white"
              style={gradientButtonStyle}
              onMouseEnter={(e) => applyGradientHover(e.currentTarget, true)}
              onMouseLeave={(e) => applyGradientHover(e.currentTarget, false)}
            >
              <span className="w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center">+</span>
              New Category
            </Button>
          </div>
        </div>
      </div>

      <div>
        {/* Statistics Cards - Enhanced mobile spacing */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-element space-section">
          <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Total Categories</p>
                  <p className="text-2xl font-bold text-gray-800">
                    {stats.totalCategories}
                  </p>
                </div>
                <span className="w-8 h-8 text-3xl font-bold flex items-center justify-center text-success">☖</span>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">
                    Active Categories
                  </p>
                  <p className="text-2xl font-bold text-gray-800">
                    {stats.activeCategories}
                  </p>
                </div>
                <span className="w-8 h-8 text-3xl font-bold flex items-center justify-center text-success">↗</span>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Hierarchy Levels</p>
                  <p className="text-2xl font-bold text-gray-800">
                    {stats.hierarchyLevels}
                  </p>
                </div>
                <span className="w-8 h-8 text-3xl font-bold flex items-center justify-center text-success">▲</span>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">Unmapped</p>
                  <p className="text-2xl font-bold text-destructive">
                    {stats.unmappedTransactions}
                  </p>
                </div>
                <span className="w-8 h-8 text-3xl font-bold flex items-center justify-center text-destructive">◯</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters - Enhanced mobile layout */}
        <Card className="space-section border border-gray-200 shadow-sm">
          <CardContent className="p-3 sm:p-card">
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 items-stretch sm:items-center">
              <div className="flex gap-2 sm:gap-3">
                <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                  <SelectTrigger className="w-full sm:w-32">
                    <span className="w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center">∷</span>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="0">Level 0</SelectItem>
                    <SelectItem value="1">Level 1</SelectItem>
                    <SelectItem value="2">Level 2</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1 max-w-md">
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 text-lg font-bold flex items-center justify-center">⌕</span>
                  <Input
                    placeholder="Search categories..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 text-sm"
                  />
                </div>
              </div>

              <Button
                variant="outline"
                onClick={() => navigate('/categories/analytics')}
                className="w-full sm:w-auto text-sm"
              >
                <span className="w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center">↗</span>
                <span className="hidden sm:inline">Analytics</span>
                <span className="sm:hidden">Analytics</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Categories Table - Enhanced mobile responsiveness */}
        <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardHeader className="p-3 sm:p-section">
            <CardTitle className="flex flex-col sm:flex-row sm:items-center justify-between text-gray-700 gap-2">
              <span className="text-lg sm:text-xl">Category Hierarchy</span>
              {selectedIds.size > 0 && (
                <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
                  <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap">
                    {selectedIds.size} selected
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkExport}
                    className="text-xs sm:text-sm px-2 sm:px-3"
                  >
                    <span className="w-3 h-3 sm:w-4 sm:h-4 mr-1 text-sm sm:text-lg font-bold flex items-center justify-center">↓</span>
                    <span className="hidden sm:inline">Export</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkDelete}
                    className="text-destructive border-destructive/20 hover:bg-destructive/5 text-xs sm:text-sm px-2 sm:px-3"
                  >
                    <span className="w-3 h-3 sm:w-4 sm:h-4 mr-1 text-sm sm:text-lg font-bold flex items-center justify-center">✗</span>
                    <span className="hidden sm:inline">Delete</span>
                  </Button>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table className="min-w-full">
              <TableHeader>
                <TableRow className="bg-giki-primary">
                  <TableHead className="w-8 sm:w-12 text-white">
                    <Checkbox
                      checked={selectedIds.size > 0}
                      onCheckedChange={handleSelectAll}
                      className="scale-90 sm:scale-100"
                    />
                  </TableHead>
                  <TableHead className="text-white font-semibold text-xs sm:text-sm min-w-[120px]">
                    Category Name
                  </TableHead>
                  <TableHead className="text-white font-semibold text-xs sm:text-sm hidden sm:table-cell">
                    GL Code
                  </TableHead>
                  <TableHead className="text-white font-semibold text-center text-xs sm:text-sm hidden md:table-cell">
                    Level
                  </TableHead>
                  <TableHead className="text-white font-semibold text-right text-xs sm:text-sm hidden lg:table-cell">
                    Transactions
                  </TableHead>
                  <TableHead className="text-white font-semibold text-right text-xs sm:text-sm">
                    Amount
                  </TableHead>
                  <TableHead className="text-white font-semibold text-right text-xs sm:text-sm hidden xl:table-cell">
                    Budget
                  </TableHead>
                  <TableHead className="text-white font-semibold text-xs sm:text-sm hidden lg:table-cell">
                    Last Used
                  </TableHead>
                  <TableHead className="text-white font-semibold w-20 sm:w-32 text-xs sm:text-sm">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {flattenCategories(filteredCategories).map((category) =>
                  renderCategoryRow(category),
                )}
              </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Professional Tip */}
        <div className="space-section text-center">
          <p className="text-sm text-gray-600">
            ⚬ Tip: Use GL codes to seamlessly export to your accounting system
          </p>
        </div>
      </div>

      {/* Create/Edit Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? 'Edit Category' : 'Create New Category'}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Category Name
              </label>
              <Input
                placeholder="e.g., Office Supplies"
                defaultValue={editingCategory?.name}
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                GL Code (Optional)
              </label>
              <Input
                placeholder="e.g., 5100"
                defaultValue={editingCategory?.glCode}
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Parent Category
              </label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select parent (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None (Top Level)</SelectItem>
                  <SelectItem value="1">Operating Expenses</SelectItem>
                  <SelectItem value="2">Revenue</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Description (Optional)
              </label>
              <Input
                placeholder="Brief description..."
                defaultValue={editingCategory?.description}
              />
            </div>
            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button className="bg-brand-primary hover:bg-brand-primary-hover text-white">
                {editingCategory ? 'Update' : 'Create'} Category
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CategoriesPage;
