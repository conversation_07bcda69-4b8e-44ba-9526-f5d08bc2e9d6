/**
 * CategoriesPage Component Tests
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { 
  render, 
  mockFetch, 
  setupAuthenticatedState 
} from '@/test/utils';
import CategoriesPage from '../CategoriesPage';

// Mock categories data with hierarchy
const mockCategories = [
  {
    id: '1',
    name: 'Income',
    parent_id: null,
    level: 0,
    path: 'Income',
    gl_code: '4000',
    transaction_count: 25,
    total_amount: 125000,
    children: [
      {
        id: '2',
        name: 'Consulting Revenue',
        parent_id: '1',
        level: 1,
        path: 'Income > Consulting Revenue',
        gl_code: '4100',
        transaction_count: 15,
        total_amount: 87500,
      },
      {
        id: '3',
        name: 'Product Sales',
        parent_id: '1',
        level: 1,
        path: 'Income > Product Sales',
        gl_code: '4200',
        transaction_count: 10,
        total_amount: 37500,
      },
    ],
  },
  {
    id: '4',
    name: 'Expenses',
    parent_id: null,
    level: 0,
    path: 'Expenses',
    gl_code: '5000',
    transaction_count: 78,
    total_amount: -89500,
    children: [
      {
        id: '5',
        name: 'Technology',
        parent_id: '4',
        level: 1,
        path: 'Expenses > Technology',
        gl_code: '5100',
        transaction_count: 32,
        total_amount: -45000,
        children: [
          {
            id: '6',
            name: 'Software Subscriptions',
            parent_id: '5',
            level: 2,
            path: 'Expenses > Technology > Software Subscriptions',
            gl_code: '5110',
            transaction_count: 20,
            total_amount: -25000,
          },
          {
            id: '7',
            name: 'Hardware',
            parent_id: '5',
            level: 2,
            path: 'Expenses > Technology > Hardware',
            gl_code: '5120',
            transaction_count: 12,
            total_amount: -20000,
          },
        ],
      },
      {
        id: '8',
        name: 'Office Expenses',
        parent_id: '4',
        level: 1,
        path: 'Expenses > Office Expenses',
        gl_code: '5200',
        transaction_count: 46,
        total_amount: -44500,
      },
    ],
  },
];

const mockCategoryStats = {
  total_categories: 8,
  categories_with_transactions: 6,
  uncategorized_transactions: 5,
  accuracy_score: 94.2,
  last_updated: '2024-01-15T14:30:00Z',
};

describe('CategoriesPage', () => {
  beforeEach(() => {
    setupAuthenticatedState();
  });

  it('renders categories management interface correctly', async () => {
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/category management/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/category tree/i)).toBeInTheDocument();
    expect(screen.getByText(/statistics/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /add category/i })).toBeInTheDocument();
  });

  it('displays category statistics summary', async () => {
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/8.*total categories/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/6.*with transactions/i)).toBeInTheDocument();
    expect(screen.getByText(/5.*uncategorized/i)).toBeInTheDocument();
    expect(screen.getByText(/94\.2%.*accuracy/i)).toBeInTheDocument();
  });

  it('shows hierarchical category tree structure', async () => {
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/income/i)).toBeInTheDocument();
    });
    
    // Check root categories
    expect(screen.getByText(/^income$/i)).toBeInTheDocument();
    expect(screen.getByText(/^expenses$/i)).toBeInTheDocument();
    
    // Check GL codes
    expect(screen.getByText(/4000/i)).toBeInTheDocument();
    expect(screen.getByText(/5000/i)).toBeInTheDocument();
    
    // Check transaction counts and amounts
    expect(screen.getByText(/25.*transactions/i)).toBeInTheDocument();
    expect(screen.getByText(/\$125,000/i)).toBeInTheDocument();
  });

  it('allows expanding and collapsing category hierarchies', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/expenses/i)).toBeInTheDocument();
    });
    
    // Find and click expand button for Technology category
    const technologyExpandButton = screen.getByRole('button', { name: /expand.*technology/i });
    await user.click(technologyExpandButton);
    
    // Should show subcategories
    await waitFor(() => {
      expect(screen.getByText(/software subscriptions/i)).toBeInTheDocument();
      expect(screen.getByText(/hardware/i)).toBeInTheDocument();
    });
    
    // Click collapse button
    const technologyCollapseButton = screen.getByRole('button', { name: /collapse.*technology/i });
    await user.click(technologyCollapseButton);
    
    // Subcategories should be hidden
    expect(screen.queryByText(/software subscriptions/i)).not.toBeInTheDocument();
  });

  it('supports adding new categories', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /add category/i })).toBeInTheDocument();
    });
    
    const addButton = screen.getByRole('button', { name: /add category/i });
    await user.click(addButton);
    
    // Should open add category modal
    await waitFor(() => {
      expect(screen.getByText(/add new category/i)).toBeInTheDocument();
    });
    
    // Fill out category form
    const nameInput = screen.getByLabelText(/category name/i);
    const parentSelect = screen.getByLabelText(/parent category/i);
    const glCodeInput = screen.getByLabelText(/gl code/i);
    
    await user.type(nameInput, 'Marketing Expenses');
    await user.selectOptions(parentSelect, 'Expenses');
    await user.type(glCodeInput, '5300');
    
    // Mock successful creation
    mockFetch({
      id: '9',
      name: 'Marketing Expenses',
      parent_id: '4',
      gl_code: '5300',
      path: 'Expenses > Marketing Expenses',
    });
    
    const submitButton = screen.getByRole('button', { name: /create category/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/categories'),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('Marketing Expenses'),
        })
      );
    });
  });

  it('allows editing existing categories', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/consulting revenue/i)).toBeInTheDocument();
    });
    
    // Find and click edit button for a category
    const editButton = screen.getByRole('button', { name: /edit.*consulting revenue/i });
    await user.click(editButton);
    
    // Should open edit modal
    await waitFor(() => {
      expect(screen.getByText(/edit category/i)).toBeInTheDocument();
    });
    
    // Form should be pre-filled
    const nameInput = screen.getByDisplayValue(/consulting revenue/i);
    const glCodeInput = screen.getByDisplayValue(/4100/i);
    
    expect(nameInput).toBeInTheDocument();
    expect(glCodeInput).toBeInTheDocument();
    
    // Modify the name
    await user.clear(nameInput);
    await user.type(nameInput, 'Professional Services Revenue');
    
    // Mock successful update
    mockFetch({
      id: '2',
      name: 'Professional Services Revenue',
      gl_code: '4100',
      updated: true,
    });
    
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/categories/2'),
        expect.objectContaining({
          method: 'PUT',
          body: expect.stringContaining('Professional Services Revenue'),
        })
      );
    });
  });

  it('validates GL code uniqueness', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    const addButton = screen.getByRole('button', { name: /add category/i });
    await user.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText(/gl code/i)).toBeInTheDocument();
    });
    
    const nameInput = screen.getByLabelText(/category name/i);
    const glCodeInput = screen.getByLabelText(/gl code/i);
    
    await user.type(nameInput, 'Test Category');
    await user.type(glCodeInput, '4100'); // Duplicate GL code
    
    // Mock validation error
    mockFetch(
      { detail: 'GL code 4100 already exists' },
      false,
      400
    );
    
    const submitButton = screen.getByRole('button', { name: /create category/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/gl code.*already exists/i)).toBeInTheDocument();
    });
  });

  it('supports deleting categories with confirmation', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/hardware/i)).toBeInTheDocument();
    });
    
    // Find delete button for a leaf category (no children)
    const deleteButton = screen.getByRole('button', { name: /delete.*hardware/i });
    await user.click(deleteButton);
    
    // Should show confirmation dialog
    await waitFor(() => {
      expect(screen.getByText(/confirm deletion/i)).toBeInTheDocument();
      expect(screen.getByText(/12.*transactions.*reassigned/i)).toBeInTheDocument();
    });
    
    // Select reassignment category
    const reassignSelect = screen.getByLabelText(/reassign to/i);
    await user.selectOptions(reassignSelect, 'Software Subscriptions');
    
    // Mock successful deletion
    mockFetch({ deleted: true, reassigned_transactions: 12 });
    
    const confirmButton = screen.getByRole('button', { name: /confirm delete/i });
    await user.click(confirmButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/categories/7'),
        expect.objectContaining({
          method: 'DELETE',
        })
      );
    });
  });

  it('prevents deleting categories with children', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/technology/i)).toBeInTheDocument();
    });
    
    // Try to delete category with children
    const deleteButton = screen.getByRole('button', { name: /delete.*technology/i });
    await user.click(deleteButton);
    
    await waitFor(() => {
      expect(screen.getByText(/cannot delete.*subcategories/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/move.*delete.*subcategories first/i)).toBeInTheDocument();
  });

  it('displays category usage analytics', async () => {
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/usage analytics/i)).toBeInTheDocument();
    });
    
    // Should show most and least used categories
    expect(screen.getByText(/most used.*office expenses.*46.*transactions/i)).toBeInTheDocument();
    expect(screen.getByText(/least used.*product sales.*10.*transactions/i)).toBeInTheDocument();
    
    // Should show categories without transactions
    expect(screen.getByText(/unused categories.*2/i)).toBeInTheDocument();
  });

  it('supports searching and filtering categories', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText(/search categories/i)).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText(/search categories/i);
    await user.type(searchInput, 'software');
    
    // Should filter to show only matching categories
    await waitFor(() => {
      expect(screen.getByText(/software subscriptions/i)).toBeInTheDocument();
    });
    
    // Other categories should be hidden or dimmed
    expect(screen.queryByText(/hardware/i)).not.toBeInTheDocument();
  });

  it('shows bulk category operations', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      categories: mockCategories,
      stats: mockCategoryStats,
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getAllByRole('checkbox')).toHaveLength(9); // 8 categories + select all
    });
    
    // Select multiple categories
    const checkboxes = screen.getAllByRole('checkbox');
    await user.click(checkboxes[1]); // First category
    await user.click(checkboxes[3]); // Third category
    
    // Should show bulk actions toolbar
    expect(screen.getByText(/2.*categories selected/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /bulk edit/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /export selected/i })).toBeInTheDocument();
  });

  it('handles empty categories state', async () => {
    mockFetch({
      categories: [],
      stats: {
        total_categories: 0,
        categories_with_transactions: 0,
        uncategorized_transactions: 150,
        accuracy_score: 0,
      },
    });
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/no categories configured/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/150.*uncategorized transactions/i)).toBeInTheDocument();
    expect(screen.getByText(/create your first category/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /quick setup/i })).toBeInTheDocument();
  });

  it('handles API errors gracefully', async () => {
    // Mock API error
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));
    
    render(<CategoriesPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/unable to load categories/i)).toBeInTheDocument();
    });
    
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
  });
});