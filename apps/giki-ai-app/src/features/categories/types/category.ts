/**
 * Category Feature Types
 *
 * Type definitions for category management functionality.
 */

export interface Category {
  id: number;
  name: string;
  parent_id?: number | null;
  level: number;
  is_active: boolean;
  gl_code?: string;
  gl_account_name?: string;
  gl_account_type?: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  color?: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

export interface CategoryHierarchy {
  id: number;
  name: string;
  children: CategoryHierarchy[];
  level: number;
  gl_code?: string;
  color?: string;
}

export interface CategoryFilter {
  level?: number;
  parent_id?: number;
  is_active?: boolean;
  search?: string;
}

export interface CategoryStats {
  totalCategories: number;
  activeCategories: number;
  maxLevel: number;
  categoriesWithGLCode: number;
}
