/**
 * Categorization Feature Types
 *
 * Type definitions for categorization functionality.
 */

export interface CategorizationRequest {
  transactionId: string;
  description: string;
  amount: number;
  date: Date;
  account?: string;
}

export interface CategorizationSuggestion {
  category_id: string;
  category_name: string;
  confidence: number;
  reasoning?: string;
  similar_transactions?: string[];
}

export interface CategorizationResult {
  transaction_id: string;
  suggestions: CategorizationSuggestion[];
  selected_category_id?: string;
  user_feedback?: 'accepted' | 'rejected' | 'modified';
  feedback_reason?: string;
}

export interface BatchCategorizationRequest {
  transaction_ids: string[];
  options?: {
    confidence_threshold?: number;
    max_suggestions?: number;
    learning_mode?: boolean;
  };
}

export interface BatchCategorizationResult {
  total_processed: number;
  successful: number;
  failed: number;
  results: CategorizationResult[];
  errors?: { transaction_id: string; error: string }[];
}
