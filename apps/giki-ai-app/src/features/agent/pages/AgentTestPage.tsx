/**
 * Agent Test Page - For testing the Enhanced Agent Panel
 * 
 * This page demonstrates the real-time agent communication features
 */

import React, { useState } from 'react';
import { EnhancedAgentPanel } from '../components/EnhancedAgentPanel';
import { useAgentCommunication } from '../hooks/useAgentCommunication';
import { ChatResponse } from '@/shared/services/agentsApi';

const AgentTestPage: React.FC = () => {
  const [lastResponse, setLastResponse] = useState<ChatResponse | null>(null);
  const { state, sendMessage, sendCommand, getStatus, getCommands } = useAgentCommunication();

  const handleAgentResponse = (response: ChatResponse) => {
    setLastResponse(response);
    console.log('Agent response:', response);
  };

  const handleTestCommand = async (command: string) => {
    await sendCommand(command, { test: true });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Agent Communication Test</h1>
          <p className="text-gray-600">Test the enhanced agent panel with real-time WebSocket communication</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Agent Panel */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200" style={{ height: '600px' }}>
              <EnhancedAgentPanel
                className="h-full"
                onAgentResponse={handleAgentResponse}
                initialMessage="Hello! I'm ready to help with your financial data."
              />
            </div>
          </div>

          {/* Control Panel */}
          <div className="space-y-6">
            {/* Agent Status */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Agent Status</h2>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Connected:</span>
                  <span className={`text-sm font-medium ${state.isConnected ? 'text-success' : 'text-error'}`}>
                    {state.isConnected ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Typing:</span>
                  <span className={`text-sm font-medium ${state.isTyping ? 'text-blue-600' : 'text-gray-400'}`}>
                    {state.isTyping ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Processing:</span>
                  <span className={`text-sm font-medium ${state.isProcessing ? 'text-orange-600' : 'text-gray-400'}`}>
                    {state.isProcessing ? 'Yes' : 'No'}
                  </span>
                </div>
                {state.currentCommand && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Command:</span>
                    <span className="text-sm font-medium text-blue-600">{state.currentCommand}</span>
                  </div>
                )}
                {state.error && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                    <p className="text-sm text-error">{state.error}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Test Commands */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Commands</h2>
              <div className="space-y-2">
                <button
                  onClick={() => handleTestCommand('/filter')}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded border border-gray-200"
                >
                  /filter - Filter transactions
                </button>
                <button
                  onClick={() => handleTestCommand('/analyze')}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded border border-gray-200"
                >
                  /analyze - Analyze data
                </button>
                <button
                  onClick={() => handleTestCommand('/export')}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded border border-gray-200"
                >
                  /export - Export data
                </button>
                <button
                  onClick={() => sendMessage('Show me my recent transactions')}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded border border-gray-200"
                >
                  Natural Language Test
                </button>
              </div>
            </div>

            {/* Last Response */}
            {lastResponse && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Last Response</h2>
                <div className="space-y-2">
                  <div className="text-sm">
                    <span className="font-medium text-gray-600">Success:</span>{' '}
                    <span className={lastResponse.success ? 'text-success' : 'text-error'}>
                      {lastResponse.success ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="font-medium text-gray-600">Message:</span>{' '}
                    <span className="text-gray-900">{lastResponse.message}</span>
                  </div>
                  {lastResponse.data && (
                    <div className="text-sm">
                      <span className="font-medium text-gray-600">Data:</span>
                      <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-x-auto">
                        {JSON.stringify(lastResponse.data, null, 2)}
                      </pre>
                    </div>
                  )}
                  {lastResponse.processing_time_ms && (
                    <div className="text-sm">
                      <span className="font-medium text-gray-600">Processing Time:</span>{' '}
                      <span className="text-gray-900">{lastResponse.processing_time_ms}ms</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentTestPage;