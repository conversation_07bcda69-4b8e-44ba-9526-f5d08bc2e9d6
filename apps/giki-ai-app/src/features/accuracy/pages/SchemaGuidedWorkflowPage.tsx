/**
 * Structured Setup Workflow
 * Professional 45-minute setup process for GL code compliance and structured categorization
 * Provides customers with schema-driven categorization accuracy through their chart of accounts
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Clock,
  CheckCircle2,
  TrendingUp,
  BarChart3,
  FileText,
  ArrowRight,
  AlertCircle,
  Building,
  BookOpen,
  Shield,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { ProgressBar } from '@/shared/components/ui/ProgressBar';

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  estimatedTime: string;
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
}

interface StructuredSetupWorkflowPageProps {
  className?: string;
}

type WorkflowStage =
  | 'intro'
  | 'schema_upload'
  | 'mapping_validation'
  | 'compliance_review'
  | 'dashboard'
  | 'complete';

export const StructuredSetupWorkflowPage: React.FC<
  StructuredSetupWorkflowPageProps
> = ({ className = '' }) => {
  const navigate = useNavigate();
  const [currentStage, setCurrentStage] = useState<WorkflowStage>('intro');
  const [_workflowData, _setWorkflowData] = useState<Record<string, unknown>>(
    {},
  );
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [startTime] = useState(Date.now());

  // Professional workflow steps for GL Code compliance
  const workflowSteps: WorkflowStep[] = [
    {
      id: 'intro',
      title: 'Schema Setup Introduction',
      description:
        'Learn how to align transactions with your chart of accounts',
      estimatedTime: '3 min',
      status:
        currentStage === 'intro'
          ? 'in_progress'
          : getCurrentStepIndex() > 0
            ? 'completed'
            : 'pending',
    },
    {
      id: 'schema_upload',
      title: 'Upload Chart of Accounts',
      description: 'Share your GL code structure and categorization schema',
      estimatedTime: '8 min',
      status:
        currentStage === 'schema_upload'
          ? 'in_progress'
          : getCurrentStepIndex() > 1
            ? 'completed'
            : 'pending',
    },
    {
      id: 'mapping_validation',
      title: 'Mapping Validation',
      description: 'Verify transaction-to-GL code mapping accuracy',
      estimatedTime: '15 min',
      status:
        currentStage === 'mapping_validation'
          ? 'in_progress'
          : getCurrentStepIndex() > 2
            ? 'completed'
            : 'pending',
    },
    {
      id: 'compliance_review',
      title: 'Compliance Review',
      description: 'Review categorization compliance and accuracy metrics',
      estimatedTime: '12 min',
      status:
        currentStage === 'compliance_review'
          ? 'in_progress'
          : getCurrentStepIndex() > 3
            ? 'completed'
            : 'pending',
    },
    {
      id: 'dashboard',
      title: 'Compliance Dashboard',
      description: 'Monitor ongoing GL code compliance and accuracy',
      estimatedTime: '7 min',
      status:
        currentStage === 'dashboard'
          ? 'in_progress'
          : getCurrentStepIndex() > 4
            ? 'completed'
            : 'pending',
    },
  ];

  function getCurrentStepIndex(): number {
    const stages: WorkflowStage[] = [
      'intro',
      'schema_upload',
      'mapping_validation',
      'compliance_review',
      'dashboard',
      'complete',
    ];
    return stages.indexOf(currentStage);
  }

  // Track elapsed time
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeElapsed(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime]);

  const getProgressPercentage = (): number => {
    const stepIndex = getCurrentStepIndex();
    return Math.min(100, (stepIndex / (workflowSteps.length - 1)) * 100);
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStepComplete = (stepData: Record<string, unknown>) => {
    _setWorkflowData((prev) => ({ ...prev, [currentStage]: stepData }));

    // Move to next stage
    const stages: WorkflowStage[] = [
      'intro',
      'schema_upload',
      'mapping_validation',
      'compliance_review',
      'dashboard',
      'complete',
    ];
    const currentIndex = stages.indexOf(currentStage);
    if (currentIndex < stages.length - 1) {
      setCurrentStage(stages[currentIndex + 1]);
    }
  };

  const renderIntroStage = () => (
    <div className="space-y-8">
      <div className="text-center">
        <div
          className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center"
          style={{ backgroundColor: 'rgba(41, 83, 67, 0.1)' }}
        >
          <Building size={40} style={{ color: 'var(--giki-primary)' }} />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Structured GL Code Setup
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Align your transaction categorization with your chart of accounts for
          95%+ accuracy and full compliance in 45 minutes
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <Card className="text-center p-6">
          <TrendingUp
            size={32}
            className="mx-auto mb-3"
            style={{ color: 'var(--giki-primary)' }}
          />
          <h3 className="font-semibold text-lg mb-2">95%+ Accuracy</h3>
          <p className="text-gray-600 text-sm">
            Schema-driven precision categorization
          </p>
        </Card>

        <Card className="text-center p-6">
          <Shield
            size={32}
            className="mx-auto mb-3"
            style={{ color: 'var(--giki-primary)' }}
          />
          <h3 className="font-semibold text-lg mb-2">Full Compliance</h3>
          <p className="text-gray-600 text-sm">Automated GL code validation</p>
        </Card>

        <Card className="text-center p-6">
          <BookOpen
            size={32}
            className="mx-auto mb-3"
            style={{ color: 'var(--giki-primary)' }}
          />
          <h3 className="font-semibold text-lg mb-2">Your Schema</h3>
          <p className="text-gray-600 text-sm">
            Uses your existing chart of accounts
          </p>
        </Card>
      </div>

      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-6 w-6" style={{ color: 'var(--giki-primary)' }} />
            Setup Process Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {workflowSteps.map((step, index) => (
              <div
                key={step.id}
                className="flex items-center gap-4 p-4 border rounded-lg"
              >
                <div
                  className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
                  style={{
                    backgroundColor:
                      step.status === 'completed'
                        ? '#10B981'
                        : step.status === 'in_progress'
                          ? '#295343'
                          : '#E5E7EB',
                    color: step.status === 'pending' ? '#6B7280' : 'white',
                  }}
                >
                  {step.status === 'completed' ? (
                    <CheckCircle2 size={16} />
                  ) : (
                    index + 1
                  )}
                </div>
                <div className="flex-1">
                  <div className="font-medium">{step.title}</div>
                  <div className="text-sm text-gray-600">
                    {step.description}
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {step.estimatedTime}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="text-center">
        <Button
          onClick={() => setCurrentStage('schema_upload')}
          className="text-white hover:opacity-90 text-lg px-8 py-4"
          style={{ backgroundColor: 'var(--giki-primary)' }}
        >
          Begin GL Setup <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </div>
  );

  const renderSchemaUploadStage = () => (
    <div className="space-y-6">
      <div className="text-center">
        <FileText
          size={48}
          className="mx-auto mb-4"
          style={{ color: 'var(--giki-primary)' }}
        />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Upload Your Chart of Accounts
        </h2>
        <p className="text-gray-600">
          Share your GL code structure to ensure perfect categorization
          alignment
        </p>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-6">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <FileText size={32} className="mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600 mb-4">
              Drop your chart of accounts file here or click to browse
            </p>
            <p className="text-sm text-gray-500 mb-4">
              Supports Excel, CSV, or PDF formats
            </p>
            <Button
              className="text-white hover:opacity-90"
              style={{ backgroundColor: 'var(--giki-primary)' }}
            >
              Select File
            </Button>
          </div>

          <div className="mt-6 space-y-3">
            <h3 className="font-medium text-gray-900">
              What we&apos;ll extract:
            </h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• GL account codes and descriptions</li>
              <li>• Account categories and hierarchies</li>
              <li>• Business rules and mappings</li>
              <li>• Compliance requirements</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      <div className="text-center">
        <Button
          onClick={() => setCurrentStage('mapping_validation')}
          className="text-white hover:opacity-90"
          style={{ backgroundColor: 'var(--giki-primary)' }}
        >
          Continue to Mapping <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderCompleteStage = () => (
    <div className="space-y-6 text-center">
      <div className="w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
        <CheckCircle2 size={40} className="text-success" />
      </div>

      <h2 className="text-2xl font-bold text-gray-900">
        Structured GL Code Setup Complete!
      </h2>

      <p className="text-gray-600 max-w-md mx-auto">
        Your schema-guided categorization is now active. All transactions will
        be automatically categorized according to your chart of accounts.
      </p>

      <Card className="max-w-sm mx-auto">
        <CardContent className="p-4">
          <div className="text-sm text-gray-600 space-y-2">
            <div className="flex justify-between">
              <span>Setup Time:</span>
              <span className="font-medium">{formatTime(timeElapsed)}</span>
            </div>
            <div className="flex justify-between">
              <span>Target Time:</span>
              <span>45:00</span>
            </div>
            <div className="flex justify-between">
              <span>Performance:</span>
              <span
                className={`font-medium ${timeElapsed <= 2700 ? 'text-success' : 'text-yellow-600'}`}
              >
                {timeElapsed <= 2700 ? 'Excellent' : 'Good'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-4 justify-center">
        <Button variant="outline" onClick={() => navigate('/dashboard')}>
          Go to Dashboard
        </Button>
        <Button
          onClick={() => navigate('/accuracy/schema-compliance-dashboard')}
          className="text-white hover:opacity-90"
          style={{ backgroundColor: 'var(--giki-primary)' }}
        >
          View Compliance Dashboard
        </Button>
      </div>
    </div>
  );

  return (
    <div className={className}>
      {/* Header with Progress */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                Structured GL Code Setup
              </h1>
              <p className="text-sm text-gray-600">
                Step {getCurrentStepIndex() + 1} of {workflowSteps.length} •{' '}
                {formatTime(timeElapsed)} elapsed
              </p>
            </div>

            <div className="flex items-center gap-4">
              {timeElapsed > 2700 && (
                <div className="flex items-center gap-1 text-yellow-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">Over 45 min target</span>
                </div>
              )}

              <Badge
                variant="outline"
                style={{ borderColor: 'var(--giki-primary)', color: 'var(--giki-primary)' }}
              >
                Structured Setup
              </Badge>
            </div>
          </div>

          <div className="max-w-2xl">
            <ProgressBar
              value={getProgressPercentage()}
              className="mb-2"
              showLabel={false}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Start</span>
              <span>{getProgressPercentage().toFixed(0)}% Complete</span>
              <span>Finish</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8 bg-gray-50">
        {currentStage === 'intro' && renderIntroStage()}

        {currentStage === 'schema_upload' && renderSchemaUploadStage()}

        {(currentStage === 'mapping_validation' ||
          currentStage === 'compliance_review' ||
          currentStage === 'dashboard') && (
          <div className="text-center space-y-6">
            <div className="w-16 h-16 mx-auto rounded-full bg-blue-100 flex items-center justify-center">
              <Clock size={24} className="text-blue-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900">
              {currentStage === 'mapping_validation'
                ? 'Validating GL Code Mappings'
                : currentStage === 'compliance_review'
                  ? 'Reviewing Compliance Metrics'
                  : 'Setting Up Compliance Dashboard'}
            </h2>
            <p className="text-gray-600">
              Our AI is analyzing your chart of accounts and creating precise
              categorization rules...
            </p>
            <div className="max-w-md mx-auto">
              <ProgressBar value={75} showLabel={false} />
              <p className="text-sm text-gray-500 mt-2">
                Processing your GL code structure
              </p>
            </div>
            <Button
              onClick={() => handleStepComplete({})}
              className="text-white hover:opacity-90"
              style={{ backgroundColor: 'var(--giki-primary)' }}
            >
              Continue <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        )}

        {currentStage === 'complete' && renderCompleteStage()}
      </div>
    </div>
  );
};

export default StructuredSetupWorkflowPage;
