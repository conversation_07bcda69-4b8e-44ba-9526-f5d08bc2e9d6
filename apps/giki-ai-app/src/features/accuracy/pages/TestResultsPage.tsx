/**
 * Test Results Page
 *
 * Displays detailed results for a specific accuracy test,
 * including metrics, AI judge evaluation, and transaction analysis.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from '../../../shared/components/ui/use-toast';
import { Button } from '../../../shared/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../../shared/components/ui/tabs';
import { Badge } from '../../../shared/components/ui/badge';
import { Progress } from '../../../shared/components/ui/progress';
import {
  ArrowLeft,
  BarChart3,
  TrendingUp,
  Target,
  Brain,
  Download,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Clock,
  Calendar,
} from 'lucide-react';
import { formatDistanceToNow, parseISO } from 'date-fns';

import { accuracyService } from '../services/accuracyService';
import type {
  AccuracyTestDetails,
  AccuracyTestStatus,
} from '../types/accuracy';

import TransactionResultsTable from '../components/TransactionResultsTable';
import AccuracyMetricsChart from '../components/AccuracyMetricsChart';

export const TestResultsPage: React.FC = () => {
  const { testId } = useParams<{ testId: string }>();
  const navigate = useNavigate();

  const [test, setTest] = useState<AccuracyTestDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<
    'overview' | 'transactions' | 'analysis'
  >('overview');

  const loadTestDetails = useCallback(async () => {
    try {
      setLoading(true);
      const testDetails = await accuracyService.getTestDetails(
        parseInt(testId),
      );
      setTest(testDetails);
    } catch (error) {
      console.error('Failed to load test details:', error);
      toast({
        title: 'Error',
        description: 'Failed to load test details. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [testId]);

  useEffect(() => {
    if (testId) {
      void loadTestDetails();
    }
  }, [testId, loadTestDetails]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTestDetails();
    setRefreshing(false);
  };

  const handleExportResults = async () => {
    try {
      const blob = await accuracyService.exportTestResults(parseInt(testId));
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `accuracy_test_${testId}_results.xlsx`;
      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      toast({
        title: 'Success',
        description: 'Test results exported successfully.',
      });
    } catch (error) {
      console.error('Failed to export results:', error);
      toast({
        title: 'Error',
        description: 'Failed to export results. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const getStatusColor = (status: AccuracyTestStatus): string => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: AccuracyTestStatus) => {
    switch (status) {
      case 'completed':
        return CheckCircle;
      case 'running':
        return RefreshCw;
      case 'failed':
        return XCircle;
      case 'pending':
        return Clock;
      case 'cancelled':
        return AlertCircle;
      default:
        return AlertCircle;
    }
  };

  const formatAccuracy = (value?: number): string => {
    if (value === undefined || value === null) return 'N/A';
    return `${value.toFixed(1)}%`;
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return 'N/A';

    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h`;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        </div>
      </div>
    );
  }

  if (!test) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <AlertCircle className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Test not found
          </h3>
          <p className="text-gray-600 mb-4">
            The accuracy test you&apos;re looking for doesn&apos;t exist or has
            been deleted.
          </p>
          <Button onClick={() => navigate('/accuracy')}>Back to Tests</Button>
        </div>
      </div>
    );
  }

  const StatusIcon = getStatusIcon(test.status);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigate('/accuracy')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          <div>
            <div className="flex items-center gap-3 mb-1">
              <h1 className="text-3xl font-bold text-gray-900">
                {test.test_name}
              </h1>
              <Badge className={`${getStatusColor(test.status)}`}>
                <StatusIcon className="w-3 h-3 mr-1" />
                {test.status.charAt(0).toUpperCase() + test.status.slice(1)}
              </Badge>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Brain className="w-4 h-4" />
                {test.scenario
                  .replace('_', ' ')
                  .split(' ')
                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(' ')}
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {test.completed_at
                  ? `Completed ${formatDistanceToNow(parseISO(test.completed_at))} ago`
                  : `Created ${formatDistanceToNow(parseISO(test.created_at))} ago`}
              </div>
              {test.execution_duration_seconds && (
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {formatDuration(test.execution_duration_seconds)}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => void handleRefresh()}
            disabled={refreshing}
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>

          {test.status === 'completed' && (
            <Button
              variant="outline"
              onClick={() => void handleExportResults()}
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </div>

      {test.description && (
        <Card className="mb-8">
          <CardContent className="pt-6">
            <p className="text-gray-700">{test.description}</p>
          </CardContent>
        </Card>
      )}

      <Tabs
        value={activeTab}
        onValueChange={(value) =>
          setActiveTab(value as 'overview' | 'transactions' | 'analysis')
        }
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transaction Results</TabsTrigger>
          <TabsTrigger value="analysis">Analysis</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          {test.status === 'completed' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                    <Target className="w-4 h-4 mr-2" />
                    Overall Accuracy
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-success">
                    {formatAccuracy(test.overall_accuracy)}
                  </div>
                  {test.overall_accuracy !== undefined && (
                    <Progress
                      value={test.overall_accuracy}
                      className="mt-2 h-2"
                    />
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Success Rate
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-success">
                    {formatAccuracy(test.success_rate)}
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    {test.successful_categorizations} /{' '}
                    {test.total_transactions} transactions
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                    <Brain className="w-4 h-4 mr-2" />
                    AI Judge Score
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-success">
                    {formatAccuracy(test.ai_judge_accuracy)}
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    Confidence:{' '}
                    {test.ai_judge_confidence_avg
                      ? (test.ai_judge_confidence_avg * 100).toFixed(1) + '%'
                      : 'N/A'}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Quality Score
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-orange-600">
                    {test.total_transactions > 0
                      ? (
                          ((test.non_generic_categories +
                            test.hierarchical_categories +
                            test.categories_with_gl_codes) /
                            (test.total_transactions * 3)) *
                          100
                        ).toFixed(1)
                      : 0}
                    %
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    Non-generic + Hierarchical + GL codes
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Quality Breakdown */}
          {test.status === 'completed' && (
            <Card>
              <CardHeader>
                <CardTitle>Quality Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-700 mb-1">
                      {Math.round(
                        (test.non_generic_categories /
                          test.total_transactions) *
                          100,
                      )}
                      %
                    </div>
                    <div className="text-sm font-medium text-success mb-2">
                      Non-Generic Categories
                    </div>
                    <div className="text-xs text-blue-500">
                      {test.non_generic_categories} / {test.total_transactions}{' '}
                      transactions
                    </div>
                    <Progress
                      value={
                        (test.non_generic_categories /
                          test.total_transactions) *
                        100
                      }
                      className="mt-2 h-2"
                    />
                  </div>

                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-700 mb-1">
                      {Math.round(
                        (test.hierarchical_categories /
                          test.total_transactions) *
                          100,
                      )}
                      %
                    </div>
                    <div className="text-sm font-medium text-success mb-2">
                      Hierarchical Structure
                    </div>
                    <div className="text-xs text-purple-500">
                      {test.hierarchical_categories} / {test.total_transactions}{' '}
                      transactions
                    </div>
                    <Progress
                      value={
                        (test.hierarchical_categories /
                          test.total_transactions) *
                        100
                      }
                      className="mt-2 h-2"
                    />
                  </div>

                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-700 mb-1">
                      {Math.round(
                        (test.categories_with_gl_codes /
                          test.total_transactions) *
                          100,
                      )}
                      %
                    </div>
                    <div className="text-sm font-medium text-success mb-2">
                      With GL Codes
                    </div>
                    <div className="text-xs text-green-500">
                      {test.categories_with_gl_codes} /{' '}
                      {test.total_transactions} transactions
                    </div>
                    <Progress
                      value={
                        (test.categories_with_gl_codes /
                          test.total_transactions) *
                        100
                      }
                      className="mt-2 h-2"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Precision, Recall, F1 */}
          {test.status === 'completed' && test.precision !== undefined && (
            <Card>
              <CardHeader>
                <CardTitle>Statistical Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      {formatAccuracy(test.precision && test.precision * 100)}
                    </div>
                    <div className="text-sm font-medium text-gray-600">
                      Precision
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      True Positives / (True Positives + False Positives)
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      {formatAccuracy(test.recall && test.recall * 100)}
                    </div>
                    <div className="text-sm font-medium text-gray-600">
                      Recall
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      True Positives / (True Positives + False Negatives)
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      {formatAccuracy(test.f1_score && test.f1_score * 100)}
                    </div>
                    <div className="text-sm font-medium text-gray-600">
                      F1 Score
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Harmonic mean of Precision and Recall
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Test Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Test Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-700 mb-3">
                    Test Parameters
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Scenario:</span>
                      <span className="font-medium">
                        {test.scenario.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Transactions:</span>
                      <span className="font-medium">
                        {test.total_transactions.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">
                        Confidence Threshold:
                      </span>
                      <span className="font-medium">
                        {test.confidence_threshold || 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-700 mb-3">
                    Execution Details
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <Badge
                        className={`text-xs ${getStatusColor(test.status)}`}
                      >
                        {test.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration:</span>
                      <span className="font-medium">
                        {formatDuration(test.execution_duration_seconds)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Created:</span>
                      <span className="font-medium">
                        {formatDistanceToNow(parseISO(test.created_at))} ago
                      </span>
                    </div>
                    {test.completed_at && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Completed:</span>
                        <span className="font-medium">
                          {formatDistanceToNow(parseISO(test.completed_at))} ago
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Transaction Results Tab */}
        <TabsContent value="transactions">
          {test.status === 'completed' ? (
            <TransactionResultsTable testId={parseInt(testId)} />
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <FileText className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Transaction results not available
                </h3>
                <p className="text-gray-600">
                  Test must be completed to view detailed transaction results.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Analysis Tab */}
        <TabsContent value="analysis">
          {test.status === 'completed' ? (
            <AccuracyMetricsChart testId={parseInt(testId)} />
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <BarChart3 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Analysis not available
                </h3>
                <p className="text-gray-600">
                  Test must be completed to view detailed analysis and charts.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TestResultsPage;
