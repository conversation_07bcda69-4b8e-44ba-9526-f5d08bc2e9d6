/**
 * Data Learning Accuracy Workflow
 * Professional 30-minute setup process for learning from existing transaction patterns
 * Provides customers with AI accuracy improvements through their transaction history
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Clock,
  CheckCircle2,
  TrendingUp,
  BarChart3,
  Database,
  ArrowRight,
  Award,
  AlertCircle,
  Brain,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import TemporalAccuracyFlow from '@/features/onboarding/components/TemporalAccuracyFlow';
import HistoricalAccuracyDashboardPage from './HistoricalAccuracyDashboardPage';
import { ProgressBar } from '@/shared/components/ui/ProgressBar';

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  estimatedTime: string;
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
  component?: React.ComponentType<Record<string, unknown>>;
}

interface DataLearningWorkflowPageProps {
  className?: string;
}

type WorkflowStage =
  | 'intro'
  | 'data_upload'
  | 'ai_learning'
  | 'accuracy_review'
  | 'dashboard'
  | 'complete';

interface AILearningData {
  baselineAccuracy: number;
  improvedAccuracy: number;
  patternsDetected: number;
  transactionsProcessed: number;
}

interface WorkflowDataType {
  data_upload?: AILearningData;
  ai_learning?: AILearningData;
}

export const DataLearningWorkflowPage: React.FC<
  DataLearningWorkflowPageProps
> = ({ className = '' }) => {
  const navigate = useNavigate();
  const [currentStage, setCurrentStage] = useState<WorkflowStage>('intro');
  const [workflowData, setWorkflowData] = useState<WorkflowDataType>({});
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [startTime] = useState(Date.now());

  // Professional workflow steps
  const workflowSteps: WorkflowStep[] = [
    {
      id: 'intro',
      title: 'Setup Introduction',
      description: 'Learn how to improve your categorization accuracy',
      estimatedTime: '2 min',
      status:
        currentStage === 'intro'
          ? 'in_progress'
          : getCurrentStepIndex() > 0
            ? 'completed'
            : 'pending',
    },
    {
      id: 'data_upload',
      title: 'Upload Historical Data',
      description: 'Share your existing categorized transactions for learning',
      estimatedTime: '5 min',
      status:
        currentStage === 'data_upload'
          ? 'in_progress'
          : getCurrentStepIndex() > 1
            ? 'completed'
            : 'pending',
    },
    {
      id: 'ai_learning',
      title: 'AI Learning Process',
      description: 'Our AI analyzes your categorization preferences',
      estimatedTime: '10 min',
      status:
        currentStage === 'ai_learning'
          ? 'in_progress'
          : getCurrentStepIndex() > 2
            ? 'completed'
            : 'pending',
    },
    {
      id: 'accuracy_review',
      title: 'Review Improvements',
      description: 'See your improved categorization accuracy results',
      estimatedTime: '8 min',
      status:
        currentStage === 'accuracy_review'
          ? 'in_progress'
          : getCurrentStepIndex() > 3
            ? 'completed'
            : 'pending',
    },
    {
      id: 'dashboard',
      title: 'Performance Dashboard',
      description: 'Monitor ongoing accuracy and performance metrics',
      estimatedTime: '5 min',
      status:
        currentStage === 'dashboard'
          ? 'in_progress'
          : getCurrentStepIndex() > 4
            ? 'completed'
            : 'pending',
    },
  ];

  function getCurrentStepIndex(): number {
    const stages: WorkflowStage[] = [
      'intro',
      'data_upload',
      'ai_learning',
      'accuracy_review',
      'dashboard',
      'complete',
    ];
    return stages.indexOf(currentStage);
  }

  // Track elapsed time
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeElapsed(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime]);

  const getProgressPercentage = (): number => {
    const stepIndex = getCurrentStepIndex();
    return Math.min(100, (stepIndex / (workflowSteps.length - 1)) * 100);
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStepComplete = (stepData: unknown) => {
    setWorkflowData((prev) => ({ ...prev, [currentStage]: stepData }));

    // Move to next stage
    const stages: WorkflowStage[] = [
      'intro',
      'data_upload',
      'ai_learning',
      'accuracy_review',
      'dashboard',
      'complete',
    ];
    const currentIndex = stages.indexOf(currentStage);
    if (currentIndex < stages.length - 1) {
      setCurrentStage(stages[currentIndex + 1]);
    }
  };

  const handleHistoricalDataComplete = (results: {
    baselineAccuracy: number;
    improvedAccuracy: number;
    patternsDetected: number;
    transactionsProcessed: number;
  }) => {
    setWorkflowData((prev) => ({
      ...prev,
      data_upload: results,
      ai_learning: {
        baselineAccuracy: results.baselineAccuracy,
        improvedAccuracy: results.improvedAccuracy,
        patternsDetected: results.patternsDetected,
        transactionsProcessed: results.transactionsProcessed,
      },
    }));
    setCurrentStage('accuracy_review');
  };

  const renderIntroStage = () => (
    <div className="space-y-8">
      <div className="text-center">
        <div
          className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center"
          style={{ backgroundColor: 'rgba(41, 83, 67, 0.1)' }}
        >
          <Brain size={40} style={{ color: 'var(--giki-primary)' }} />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          AI Data Learning Setup
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Let our AI learn from your existing transaction categorizations to
          achieve 90%+ accuracy in just 30 minutes
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <Card className="text-center p-6">
          <TrendingUp
            size={32}
            className="mx-auto mb-3"
            style={{ color: 'var(--giki-primary)' }}
          />
          <h3 className="font-semibold text-lg mb-2">90%+ Accuracy</h3>
          <p className="text-gray-600 text-sm">
            Achieve exceptional categorization precision
          </p>
        </Card>

        <Card className="text-center p-6">
          <Clock
            size={32}
            className="mx-auto mb-3"
            style={{ color: 'var(--giki-primary)' }}
          />
          <h3 className="font-semibold text-lg mb-2">30 Minutes</h3>
          <p className="text-gray-600 text-sm">
            Quick setup for immediate results
          </p>
        </Card>

        <Card className="text-center p-6">
          <Database
            size={32}
            className="mx-auto mb-3"
            style={{ color: 'var(--giki-primary)' }}
          />
          <h3 className="font-semibold text-lg mb-2">Your Data</h3>
          <p className="text-gray-600 text-sm">
            Learn from your existing patterns
          </p>
        </Card>
      </div>

      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-6 w-6" style={{ color: 'var(--giki-primary)' }} />
            Setup Process Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {workflowSteps.map((step, index) => (
              <div
                key={step.id}
                className="flex items-center gap-4 p-4 border rounded-lg"
              >
                <div
                  className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
                  style={{
                    backgroundColor:
                      step.status === 'completed'
                        ? '#10B981'
                        : step.status === 'in_progress'
                          ? '#295343'
                          : '#E5E7EB',
                    color: step.status === 'pending' ? '#6B7280' : 'white',
                  }}
                >
                  {step.status === 'completed' ? (
                    <CheckCircle2 size={16} />
                  ) : (
                    index + 1
                  )}
                </div>
                <div className="flex-1">
                  <div className="font-medium">{step.title}</div>
                  <div className="text-sm text-gray-600">
                    {step.description}
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {step.estimatedTime}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="text-center">
        <Button
          onClick={() => setCurrentStage('data_upload')}
          className="text-white hover:opacity-90 text-lg px-8 py-4"
          style={{ backgroundColor: 'var(--giki-primary)' }}
        >
          Begin Setup <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </div>
  );

  const renderAccuracyReview = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Award
          size={48}
          className="mx-auto mb-4"
          style={{ color: 'var(--giki-primary)' }}
        />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Accuracy Enhancement Complete
        </h2>
        <p className="text-gray-600">
          Your AI has successfully learned from your transaction patterns
        </p>
      </div>

      {workflowData.ai_learning && (
        <Card className="max-w-2xl mx-auto">
          <CardContent className="p-6">
            <div className="grid grid-cols-2 gap-6 text-center">
              <div>
                <div
                  className="text-3xl font-bold"
                  style={{ color: 'var(--giki-primary)' }}
                >
                  {(workflowData.ai_learning.improvedAccuracy * 100).toFixed(1)}
                  %
                </div>
                <div className="text-sm text-gray-600">Current Accuracy</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-success">
                  +
                  {(
                    (workflowData.ai_learning.improvedAccuracy -
                      workflowData.ai_learning.baselineAccuracy) *
                    100
                  ).toFixed(1)}
                  %
                </div>
                <div className="text-sm text-gray-600">Improvement</div>
              </div>
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="text-sm text-gray-600 space-y-1">
                <div>
                  •{' '}
                  {workflowData.ai_learning.transactionsProcessed.toLocaleString()}{' '}
                  transactions analyzed
                </div>
                <div>
                  • {workflowData.ai_learning.patternsDetected} categorization
                  patterns identified
                </div>
                <div>• Ready for enhanced transaction processing</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="text-center">
        <Button
          onClick={() => setCurrentStage('dashboard')}
          className="text-white hover:opacity-90"
          style={{ backgroundColor: 'var(--giki-primary)' }}
        >
          View Performance Dashboard <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderCompleteStage = () => (
    <div className="space-y-6 text-center">
      <div className="w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
        <CheckCircle2 size={40} className="text-success" />
      </div>

      <h2 className="text-2xl font-bold text-gray-900">Setup Complete!</h2>

      <p className="text-gray-600 max-w-md mx-auto">
        Your accuracy enhancement is complete. The AI is now ready to categorize
        your transactions with improved precision.
      </p>

      <Card className="max-w-sm mx-auto">
        <CardContent className="p-4">
          <div className="text-sm text-gray-600 space-y-2">
            <div className="flex justify-between">
              <span>Setup Time:</span>
              <span className="font-medium">{formatTime(timeElapsed)}</span>
            </div>
            <div className="flex justify-between">
              <span>Target Time:</span>
              <span>30:00</span>
            </div>
            <div className="flex justify-between">
              <span>Performance:</span>
              <span
                className={`font-medium ${timeElapsed <= 1800 ? 'text-success' : 'text-yellow-600'}`}
              >
                {timeElapsed <= 1800 ? 'Excellent' : 'Good'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-4 justify-center">
        <Button variant="outline" onClick={() => navigate('/dashboard')}>
          Go to Dashboard
        </Button>
        <Button
          onClick={() => navigate('/accuracy/historical-dashboard')}
          className="text-white hover:opacity-90"
          style={{ backgroundColor: 'var(--giki-primary)' }}
        >
          View Accuracy Dashboard
        </Button>
      </div>
    </div>
  );

  return (
    <div className={className}>
      {/* Header with Progress */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                Data Learning Setup
              </h1>
              <p className="text-sm text-gray-600">
                Step {getCurrentStepIndex() + 1} of {workflowSteps.length} •{' '}
                {formatTime(timeElapsed)} elapsed
              </p>
            </div>

            <div className="flex items-center gap-4">
              {timeElapsed > 1800 && (
                <div className="flex items-center gap-1 text-yellow-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">Over 30 min target</span>
                </div>
              )}

              <Badge
                variant="outline"
                style={{ borderColor: 'var(--giki-primary)', color: 'var(--giki-primary)' }}
              >
                Data Learning
              </Badge>
            </div>
          </div>

          <div className="max-w-2xl">
            <ProgressBar
              value={getProgressPercentage()}
              className="mb-2"
              showLabel={false}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Start</span>
              <span>{getProgressPercentage().toFixed(0)}% Complete</span>
              <span>Finish</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8 bg-gray-50">
        {currentStage === 'intro' && renderIntroStage()}

        {currentStage === 'data_upload' && (
          <div className="max-w-4xl mx-auto">
            <TemporalAccuracyFlow
              onComplete={handleHistoricalDataComplete}
              onBack={() => setCurrentStage('intro')}
            />
          </div>
        )}

        {currentStage === 'accuracy_review' && renderAccuracyReview()}

        {currentStage === 'dashboard' && (
          <div>
            <div className="mb-6 text-center">
              <Button
                onClick={() => handleStepComplete({})}
                className="text-white hover:opacity-90 mb-4"
                style={{ backgroundColor: 'var(--giki-primary)' }}
              >
                Complete Setup <CheckCircle2 className="ml-2 h-4 w-4" />
              </Button>
            </div>
            <HistoricalAccuracyDashboardPage />
          </div>
        )}

        {currentStage === 'complete' && renderCompleteStage()}
      </div>
    </div>
  );
};

export default DataLearningWorkflowPage;
