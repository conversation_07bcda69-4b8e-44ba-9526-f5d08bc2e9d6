/**
 * Historical Accuracy Performance Dashboard
 * Professional accuracy monitoring and performance analytics dashboard
 * Real-time metrics for transaction categorization accuracy improvement
 */
import React, { useState, useMemo } from 'react';
import {
  Activity,
  BarChart3,
  TrendingUp,
  Target,
  Zap,
  Download,
  RefreshCw,
  Award,
  AlertCircle,
  CheckCircle2,
  Clock,
  Database,
  Brain,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import TemporalAccuracyChart from '../components/TemporalAccuracyChart';
import AccuracyTrendsPanel from '../components/AccuracyTrendsPanel';
import HistoricalValidationDashboard from '../components/HistoricalValidationDashboard';
import { useTemporalAccuracy } from '../services/temporalAccuracyService';

interface AccuracyMetric {
  period: string;
  accuracy: number;
  confidence: number;
  transactionCount: number;
  categoriesUsed: number;
  manualCorrections: number;
  averageProcessingTime: number;
  topCategories: Array<{
    name: string;
    accuracy: number;
    count: number;
  }>;
}

interface HistoricalAccuracyDashboardPageProps {
  className?: string;
}

export const HistoricalAccuracyDashboardPage: React.FC<
  HistoricalAccuracyDashboardPageProps
> = ({ className = '' }) => {
  const [timeRange, setTimeRange] = useState<string>('30d');
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Use the temporal accuracy service hook
  // TODO: Fix type mismatch - useTemporalAccuracy doesn't return these properties
  const { isValidating, error } = useTemporalAccuracy();
  const accuracyData: unknown[] = [];
  const loading = isValidating;
  const refreshData = async () => {
    // TODO: Implement refresh
  };

  // Generate sample data for demonstration
  const sampleAccuracyMetrics: AccuracyMetric[] = useMemo(
    () => [
      {
        period: 'This Week',
        accuracy: 0.924,
        confidence: 0.89,
        transactionCount: 1247,
        categoriesUsed: 23,
        manualCorrections: 94,
        averageProcessingTime: 0.8,
        topCategories: [
          { name: 'Office Supplies', accuracy: 0.96, count: 187 },
          { name: 'Travel Expenses', accuracy: 0.94, count: 156 },
          { name: 'Software Subscriptions', accuracy: 0.98, count: 89 },
        ],
      },
      {
        period: 'Last Week',
        accuracy: 0.908,
        confidence: 0.86,
        transactionCount: 1189,
        categoriesUsed: 21,
        manualCorrections: 109,
        averageProcessingTime: 0.9,
        topCategories: [
          { name: 'Office Supplies', accuracy: 0.93, count: 172 },
          { name: 'Travel Expenses', accuracy: 0.91, count: 149 },
          { name: 'Marketing', accuracy: 0.89, count: 96 },
        ],
      },
      {
        period: 'This Month',
        accuracy: 0.916,
        confidence: 0.88,
        transactionCount: 5234,
        categoriesUsed: 27,
        manualCorrections: 437,
        averageProcessingTime: 0.85,
        topCategories: [
          { name: 'Office Supplies', accuracy: 0.95, count: 789 },
          { name: 'Travel Expenses', accuracy: 0.92, count: 634 },
          { name: 'Software Subscriptions', accuracy: 0.97, count: 345 },
        ],
      },
    ],
    [],
  );

  const currentMetrics = sampleAccuracyMetrics[0]; // This week's data

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshData();
      setLastRefresh(new Date());
    } catch (err) {
      console.error('Failed to refresh accuracy data:', err);
    } finally {
      setRefreshing(false);
    }
  };

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatTime = (seconds: number): string => {
    return `${seconds.toFixed(1)}s`;
  };

  return (
    <div className={className}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <Brain className="h-8 w-8" style={{ color: 'var(--giki-primary)' }} />
                Accuracy Performance Dashboard
              </h1>
              <p className="text-gray-600 mt-1">
                Monitor and analyze your transaction categorization accuracy in
                real-time
              </p>
            </div>

            <div className="flex items-center gap-4">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                onClick={() => {
                  handleRefresh().catch(console.error);
                }}
                disabled={refreshing}
                className="flex items-center gap-2"
              >
                <RefreshCw
                  className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`}
                />
                Refresh
              </Button>

              <Button
                className="text-white hover:opacity-90 flex items-center gap-2"
                style={{ backgroundColor: 'var(--giki-primary)' }}
              >
                <Download className="h-4 w-4" />
                Export Report
              </Button>
            </div>
          </div>

          <div className="mt-4 flex items-center gap-6 text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-success" />
              Historical Learning Active
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Last updated: {lastRefresh.toLocaleTimeString()}
            </div>
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              {currentMetrics.transactionCount.toLocaleString()} transactions
              analyzed
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-6 bg-gray-50">
        {/* Key Performance Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Overall Accuracy
                  </p>
                  <p
                    className="text-2xl font-bold"
                    style={{ color: 'var(--giki-primary)' }}
                  >
                    {formatPercentage(currentMetrics.accuracy)}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    +1.6% from last week
                  </p>
                </div>
                <Target className="h-8 w-8" style={{ color: 'var(--giki-primary)' }} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Confidence Score
                  </p>
                  <p className="text-2xl font-bold text-blue-600">
                    {formatPercentage(currentMetrics.confidence)}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    +3.2% improvement
                  </p>
                </div>
                <Award className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Processing Speed
                  </p>
                  <p className="text-2xl font-bold text-purple-600">
                    {formatTime(currentMetrics.averageProcessingTime)}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">Per transaction</p>
                </div>
                <Zap className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Manual Corrections
                  </p>
                  <p className="text-2xl font-bold text-orange-600">
                    {currentMetrics.manualCorrections}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {formatPercentage(
                      currentMetrics.manualCorrections /
                        currentMetrics.transactionCount,
                    )}{' '}
                    of total
                  </p>
                </div>
                <AlertCircle className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Accuracy Trends Chart */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" style={{ color: 'var(--giki-primary)' }} />
                Accuracy Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <TemporalAccuracyChart
                  data={accuracyData || []}
                  loading={loading}
                  timeRange={timeRange}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" style={{ color: 'var(--giki-primary)' }} />
                Top Performing Categories
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currentMetrics.topCategories.map((category, index) => (
                  <div
                    key={category.name}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold"
                        style={{
                          backgroundColor:
                            index === 0
                              ? '#295343'
                              : index === 1
                                ? '#60A5FA'
                                : '#A78BFA',
                          color: 'white',
                        }}
                      >
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {category.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {category.count} transactions
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div
                        className="font-semibold"
                        style={{ color: 'var(--giki-primary)' }}
                      >
                        {formatPercentage(category.accuracy)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" style={{ color: 'var(--giki-primary)' }} />
                Accuracy Trends Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AccuracyTrendsPanel
                metrics={sampleAccuracyMetrics}
                timeRange={timeRange}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" style={{ color: 'var(--giki-primary)' }} />
                Historical Validation Dashboard
              </CardTitle>
            </CardHeader>
            <CardContent>
              <HistoricalValidationDashboard
                validationData={accuracyData || []}
                loading={loading}
                error={error}
              />
            </CardContent>
          </Card>
        </div>

        {/* Performance Insights */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" style={{ color: 'var(--giki-primary)' }} />
              Performance Insights & Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle2 className="h-4 w-4 text-success" />
                  <span className="font-medium text-green-800">
                    Excellent Performance
                  </span>
                </div>
                <p className="text-sm text-green-700">
                  Your accuracy has improved by 1.6% this week. Office Supplies
                  categorization is performing exceptionally well.
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-800">Trending Up</span>
                </div>
                <p className="text-sm text-blue-700">
                  Confidence scores are consistently increasing. The AI is
                  learning your categorization patterns effectively.
                </p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <span className="font-medium text-yellow-800">
                    Optimization Opportunity
                  </span>
                </div>
                <p className="text-sm text-yellow-700">
                  Consider reviewing Travel Expenses categories to reduce manual
                  corrections by an estimated 15%.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default HistoricalAccuracyDashboardPage;
