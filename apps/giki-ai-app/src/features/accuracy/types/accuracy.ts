/**
 * Type definitions for accuracy testing functionality
 */

export type AccuracyTestScenario =
  | 'historical_data'
  | 'schema_only'
  | 'zero_onboarding';

export type AccuracyTestStatus =
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface AccuracyTestSummary {
  test_id: number;
  test_name: string;
  description?: string;
  scenario: AccuracyTestScenario;
  status: AccuracyTestStatus;
  created_at: string;
  completed_at?: string;
  execution_duration_seconds?: number;

  // Test Results
  total_transactions: number;
  successful_categorizations: number;
  success_rate: number;

  // Accuracy Metrics
  overall_accuracy?: number;
  precision?: number;
  recall?: number;
  f1_score?: number;

  // AI Judge Results
  ai_judge_accuracy?: number;
  ai_judge_confidence_avg?: number;

  // Quality Metrics
  non_generic_categories: number;
  hierarchical_categories: number;
  categories_with_gl_codes: number;

  // Configuration
  confidence_threshold?: number;
  max_transactions?: number;
}

export interface AccuracyTestDetails extends AccuracyTestSummary {
  // Additional detailed information
  dataset_file_name?: string;
  category_schema_file_name?: string;
  error_message?: string;

  // Detailed metrics
  avg_confidence: number;
  quality_score: number;
}

export interface TransactionResult {
  transaction_id: string;
  description: string;
  amount: number;
  original_category?: string;
  ai_category: string;
  ai_hierarchy?: string;
  ai_confidence: number;
  ai_reasoning?: string;

  // Quality indicators
  is_generic: boolean;
  has_hierarchy: boolean;
  has_gl_codes: boolean;

  // Correctness evaluation
  is_correct?: boolean; // null if not evaluated
  ai_judge_score?: number;
  ai_judge_reasoning?: string;
}

export interface AccuracyTestFilter {
  scenario?: AccuracyTestScenario;
  status?: AccuracyTestStatus;
  created_after?: string;
  created_before?: string;
}

export interface AccuracyDashboardData {
  total_tests: number;
  average_accuracy: number;
  tests_by_scenario: Record<AccuracyTestScenario, number>;
  quality_metrics: {
    non_generic_rate: number;
    hierarchical_rate: number;
    gl_code_rate: number;
  };
  recent_tests: AccuracyTestSummary[];
}

export interface TransactionResultsFilter {
  page?: number;
  page_size?: number;
  search?: string;
  correctness?: 'correct' | 'incorrect' | 'partial';
  confidence_range?: 'high' | 'medium' | 'low';
  quality_filter?: 'high_quality' | 'generic' | 'hierarchical';
  sort_by?: 'confidence' | 'description' | 'correctness';
  sort_order?: 'asc' | 'desc';
}

export interface TransactionResultsResponse {
  results: TransactionResult[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface AccuracyMetrics {
  overall_accuracy: number;
  precision: number;
  recall: number;
  f1_score: number;
  success_rate: number;
  avg_confidence: number;

  quality_metrics: {
    non_generic_rate: number;
    hierarchical_rate: number;
    gl_code_rate: number;
  };
}

export interface ConfidenceDistribution {
  high: number; // 80%+
  medium: number; // 60-80%
  low: number; // <60%
}

export interface CategoryBreakdown {
  category: string;
  count: number;
  accuracy_rate: number;
  avg_confidence: number;
}

export interface ErrorPattern {
  pattern: string;
  count: number;
  description: string;
  examples: string[];
}

export interface ErrorAnalysis {
  total_errors: number;
  correct_categorizations: number;
  partial_matches: number;
  common_error_patterns: ErrorPattern[];
}

export interface AccuracyAnalytics {
  // Core metrics
  overall_accuracy: number;
  success_rate: number;
  ai_judge_accuracy: number;
  precision: number;
  recall: number;
  f1_score: number;
  avg_confidence: number;
  confidence_threshold?: number;

  // Quality metrics
  quality_metrics: {
    non_generic_rate: number;
    hierarchical_rate: number;
    gl_code_rate: number;
  };

  // Distribution analysis
  confidence_distribution: ConfidenceDistribution;
  category_breakdown: CategoryBreakdown[];

  // Error analysis
  error_analysis: ErrorAnalysis;
}

export interface CreateTestRequest {
  test_name: string;
  description?: string;
  scenario: AccuracyTestScenario;
  max_transactions?: number;
  confidence_threshold?: number;
  dataset_file?: File;
  category_schema_file?: File;
}

export interface CreateTestResponse {
  test_id: number;
  message: string;
  status: AccuracyTestStatus;
}
