/**
 * Temporal Accuracy Service
 * Enhanced backend integration for M2 milestone temporal accuracy tracking
 * Handles progressive accuracy polling, validation, and historical analysis
 */
import React from 'react';
import { apiClient } from '@/shared/services/api/apiClient';

export interface TemporalAccuracyDataPoint {
  timestamp: string;
  accuracy: number;
  confidence: number;
  transactionCount: number;
  manualCorrections: number;
  processingTime: number;
  categoryBreakdown: Array<{
    category: string;
    accuracy: number;
    count: number;
  }>;
}

export interface ValidationDataset {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  startTime: string;
  endTime?: string;
  accuracy: {
    initial: number;
    current: number;
    target: number;
  };
  metadata: {
    transactionCount: number;
    dateRange: {
      start: string;
      end: string;
    };
    categories: string[];
    confidence: number;
  };
}

export interface MilestoneValidationResults {
  milestone: 'M1' | 'M2' | 'M3';
  status: 'pending' | 'in_progress' | 'achieved' | 'failed';
  overallAccuracy: number;
  targetAccuracy: number;
  datasets: ValidationDataset[];
  metrics: {
    totalTransactions: number;
    avgProcessingTime: number;
    improvementRate: number;
    consistencyScore: number;
  };
  timestamp: string;
}

export interface ProgressPollResult {
  isComplete: boolean;
  progress: number;
  currentStage: string;
  estimatedTimeRemaining?: number;
  intermediateResults?: TemporalAccuracyDataPoint[];
  error?: string;
}

class TemporalAccuracyService {
  private baseUrl = '/api/accuracy/temporal';
  private pollInterval = 2000; // 2 seconds
  private activePollingSessions = new Map<string, AbortController>();

  /**
   * Start temporal accuracy validation for a dataset
   */
  async startValidation(
    datasetId: string,
    options: {
      targetAccuracy?: number;
      enableRealTimeProgress?: boolean;
      validationMode?: 'full' | 'sample' | 'incremental';
    } = {},
  ): Promise<{ sessionId: string; initialResults: ValidationDataset }> {
    const response = await apiClient.post<{
      sessionId: string;
      initialResults: ValidationDataset;
    }>(`${this.baseUrl}/validate`, {
      datasetId,
      options,
    });

    return (
      response.data || {
        sessionId: `session_${Date.now()}`,
        initialResults: {
          id: datasetId,
          name: 'Temporal Validation Dataset',
          description:
            'Historical data validation for temporal accuracy improvement',
          status: 'processing' as const,
          progress: 0,
          startTime: new Date().toISOString(),
          accuracy: {
            initial: 75.0,
            current: 75.0,
            target: 85.0,
          },
          metadata: {
            transactionCount: 1000,
            dateRange: { start: '2024-01-01', end: '2024-12-31' },
            categories: [],
            confidence: 0.92,
          },
        },
      }
    );
  }

  /**
   * Poll validation progress with real-time updates
   */
  async pollValidationProgress(
    sessionId: string,
    onProgress?: (result: ProgressPollResult) => void,
    onComplete?: (finalResults: ValidationDataset) => void,
    onError?: (error: Error) => void,
  ): Promise<void> {
    // Cancel any existing polling for this session
    this.stopPolling(sessionId);

    const abortController = new AbortController();
    this.activePollingSessions.set(sessionId, abortController);

    try {
      while (!abortController.signal.aborted) {
        const response = await apiClient.get<ProgressPollResult>(
          `${this.baseUrl}/progress/${sessionId}`,
          { signal: abortController.signal },
        );

        const result: ProgressPollResult = response.data || {
          isComplete: false,
          progress: 0,
          currentStage: 'initializing' as const,
        };

        onProgress?.(result);

        if (result.isComplete) {
          const finalResults = await this.getValidationResults(sessionId);
          onComplete?.(finalResults);
          break;
        }

        if (result.error) {
          throw new Error(result.error);
        }

        // Wait before next poll
        await new Promise((resolve) => setTimeout(resolve, this.pollInterval));
      }
    } catch (error) {
      if (!abortController.signal.aborted) {
        onError?.(error as Error);
      }
    } finally {
      this.activePollingSessions.delete(sessionId);
    }
  }

  /**
   * Stop polling for a specific session
   */
  stopPolling(sessionId: string): void {
    const controller = this.activePollingSessions.get(sessionId);
    if (controller) {
      controller.abort();
      this.activePollingSessions.delete(sessionId);
    }
  }

  /**
   * Stop all active polling sessions
   */
  stopAllPolling(): void {
    for (const [_sessionId, controller] of this.activePollingSessions) {
      controller.abort();
    }
    this.activePollingSessions.clear();
  }

  /**
   * Get validation results for a completed session
   */
  async getValidationResults(sessionId: string): Promise<ValidationDataset> {
    const response = await apiClient.get<ValidationDataset>(
      `${this.baseUrl}/results/${sessionId}`,
    );
    return (
      response.data || {
        id: sessionId,
        name: 'Validation Dataset',
        description: 'Temporal validation results',
        status: 'completed' as const,
        progress: 100,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        accuracy: {
          initial: 0,
          current: 0,
          target: 85,
        },
        metadata: {
          transactionCount: 0,
          dateRange: { start: '', end: '' },
          categories: [],
          confidence: 0,
        },
      }
    );
  }

  /**
   * Get temporal accuracy timeline for a dataset
   */
  async getAccuracyTimeline(
    datasetId: string,
    timeRange?: {
      start: string;
      end: string;
    },
  ): Promise<TemporalAccuracyDataPoint[]> {
    const params = timeRange
      ? `?start=${timeRange.start}&end=${timeRange.end}`
      : '';
    const response = await apiClient.get<TemporalAccuracyDataPoint[]>(
      `${this.baseUrl}/timeline/${datasetId}${params}`,
    );
    return response.data || [];
  }

  /**
   * Get milestone validation status
   */
  async getMilestoneValidation(
    milestone: 'M1' | 'M2' | 'M3',
  ): Promise<MilestoneValidationResults> {
    const response = await apiClient.get<MilestoneValidationResults>(
      `${this.baseUrl}/milestone/${milestone}`,
    );
    return (
      response.data || {
        milestone,
        status: 'pending' as const,
        overallAccuracy: 0,
        targetAccuracy: milestone === 'M1' ? 85 : milestone === 'M2' ? 90 : 95,
        datasets: [],
        metrics: {
          totalTransactions: 0,
          avgProcessingTime: 0,
          improvementRate: 0,
          consistencyScore: 0,
        },
        timestamp: new Date().toISOString(),
      }
    );
  }

  /**
   * Trigger milestone validation process
   */
  async validateMilestone(
    milestone: 'M1' | 'M2' | 'M3',
    datasetIds: string[],
  ): Promise<{ validationId: string; status: string }> {
    const response = await apiClient.post<{
      validationId: string;
      status: string;
    }>(`${this.baseUrl}/milestone/${milestone}/validate`, {
      datasetIds,
    });
    return (
      response.data || {
        validationId: `validation_${Date.now()}`,
        status: 'initiated',
      }
    );
  }

  /**
   * Get historical accuracy trends
   */
  async getHistoricalTrends(
    timeRange: {
      start: string;
      end: string;
    },
    aggregation: 'hourly' | 'daily' | 'weekly' | 'monthly' = 'daily',
  ): Promise<TemporalAccuracyDataPoint[]> {
    const response = await apiClient.get<TemporalAccuracyDataPoint[]>(
      `${this.baseUrl}/trends`,
      {
        params: {
          start: timeRange.start,
          end: timeRange.end,
          aggregation,
        },
      },
    );
    return response.data || [];
  }

  /**
   * Get accuracy comparison between datasets
   */
  async compareDatasets(datasetIds: string[]): Promise<{
    comparison: Array<{
      datasetId: string;
      accuracy: number;
      improvement: number;
      rank: number;
    }>;
    insights: string[];
    recommendations: string[];
  }> {
    const response = await apiClient.post<{
      comparison: Array<{
        datasetId: string;
        accuracy: number;
        improvement: number;
        rank: number;
      }>;
      insights: string[];
      recommendations: string[];
    }>(`${this.baseUrl}/compare`, {
      datasetIds,
    });
    return (
      response.data || {
        comparison: [],
        insights: [],
        recommendations: [],
      }
    );
  }

  /**
   * Export validation results
   */
  async exportResults(
    sessionId: string,
    format: 'json' | 'csv' | 'excel' = 'json',
  ): Promise<{ downloadUrl: string; expiresAt: string }> {
    const response = await apiClient.post<{
      downloadUrl: string;
      expiresAt: string;
    }>(`${this.baseUrl}/export/${sessionId}`, {
      format,
    });
    return (
      response.data || {
        downloadUrl: '',
        expiresAt: new Date(Date.now() + 3600000).toISOString(),
      }
    );
  }

  /**
   * Get live accuracy metrics (for real-time dashboard)
   */
  async getLiveMetrics(): Promise<{
    currentAccuracy: number;
    trend: 'improving' | 'stable' | 'declining';
    lastUpdate: string;
    activeValidations: number;
    recentActivity: Array<{
      type: 'validation_started' | 'validation_completed' | 'accuracy_improved';
      timestamp: string;
      details: string;
    }>;
  }> {
    const response = await apiClient.get<{
      currentAccuracy: number;
      trend: 'improving' | 'stable' | 'declining';
      lastUpdate: string;
      activeValidations: number;
      recentActivity: Array<{
        type:
          | 'validation_started'
          | 'validation_completed'
          | 'accuracy_improved';
        timestamp: string;
        details: string;
      }>;
    }>(`${this.baseUrl}/live-metrics`);
    return (
      response.data || {
        currentAccuracy: 0,
        trend: 'stable' as const,
        lastUpdate: new Date().toISOString(),
        activeValidations: 0,
        recentActivity: [],
      }
    );
  }

  /**
   * Subscribe to real-time accuracy updates via Server-Sent Events
   */
  subscribeToUpdates(
    onUpdate: (data: TemporalAccuracyDataPoint) => void,
    onError?: (error: Error) => void,
  ): () => void {
    const eventSource = new EventSource(`${this.baseUrl}/stream`);

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(
          event.data as string,
        ) as TemporalAccuracyDataPoint;
        onUpdate(data);
      } catch {
        onError?.(new Error('Failed to parse SSE data'));
      }
    };

    eventSource.onerror = (_error) => {
      onError?.(new Error('SSE connection error'));
    };

    // Return cleanup function
    return () => {
      eventSource.close();
    };
  }

  /**
   * Get confidence distribution for a dataset
   */
  async getConfidenceDistribution(datasetId: string): Promise<{
    distribution: Array<{
      confidenceRange: string;
      count: number;
      accuracy: number;
    }>;
    avgConfidence: number;
    lowConfidenceThreshold: number;
  }> {
    const response = await apiClient.get<{
      distribution: Array<{
        confidenceRange: string;
        count: number;
        accuracy: number;
      }>;
      avgConfidence: number;
      lowConfidenceThreshold: number;
    }>(`${this.baseUrl}/confidence/${datasetId}`);
    return (
      response.data || {
        distribution: [],
        avgConfidence: 0,
        lowConfidenceThreshold: 0.5,
      }
    );
  }

  /**
   * Get accuracy breakdown by category
   */
  async getCategoryAccuracy(datasetId: string): Promise<
    Array<{
      category: string;
      accuracy: number;
      transactionCount: number;
      avgConfidence: number;
      improvement: number;
    }>
  > {
    const response = await apiClient.get<
      Array<{
        category: string;
        accuracy: number;
        transactionCount: number;
        avgConfidence: number;
        improvement: number;
      }>
    >(`${this.baseUrl}/category-breakdown/${datasetId}`);
    return response.data || [];
  }

  /**
   * Generate accuracy improvement recommendations
   */
  async getImprovementRecommendations(datasetId: string): Promise<{
    recommendations: Array<{
      type:
        | 'model_retraining'
        | 'data_quality'
        | 'parameter_tuning'
        | 'category_mapping';
      priority: 'high' | 'medium' | 'low';
      description: string;
      expectedImprovement: number;
      estimatedEffort: string;
    }>;
    currentBottlenecks: string[];
    nextSteps: string[];
  }> {
    const response = await apiClient.get(
      `${this.baseUrl}/recommendations/${datasetId}`,
    );
    const defaultResult = {
      recommendations: [],
      currentBottlenecks: [],
      nextSteps: [],
    };

    if (!response.data || typeof response.data !== 'object') {
      return defaultResult;
    }

    interface RecommendationResponse {
      recommendations: Array<{
        type:
          | 'data_quality'
          | 'model_retraining'
          | 'parameter_tuning'
          | 'category_mapping';
        priority: 'high' | 'medium' | 'low';
        description: string;
        expectedImprovement: number;
        estimatedEffort: string;
      }>;
      currentBottlenecks: string[];
      nextSteps: string[];
    }

    const data = response.data as RecommendationResponse;

    if (!data.recommendations || !data.currentBottlenecks || !data.nextSteps) {
      return defaultResult;
    }

    return data;
  }

  /**
   * Validate specific transactions for accuracy testing
   */
  async validateTransactions(
    transactionIds: string[],
    expectedCategories?: string[],
  ): Promise<
    Array<{
      transactionId: string;
      predictedCategory: string;
      expectedCategory?: string;
      confidence: number;
      isCorrect?: boolean;
      reasoning: string;
    }>
  > {
    const response = await apiClient.post(
      `${this.baseUrl}/validate-transactions`,
      {
        transactionIds,
        expectedCategories,
      },
    );
    return (response.data || []) as Array<{
      transactionId: string;
      predictedCategory: string;
      expectedCategory?: string;
      confidence: number;
      isCorrect?: boolean;
      reasoning: string;
    }>;
  }
}

// Singleton instance
export const temporalAccuracyService = new TemporalAccuracyService();

// React hook for temporal accuracy management
export const useTemporalAccuracy = () => {
  const [isValidating, setIsValidating] = React.useState(false);
  const [validationProgress, setValidationProgress] = React.useState(0);
  const [currentStage, setCurrentStage] = React.useState('');
  const [error, setError] = React.useState<string | null>(null);

  const startValidation = async (
    datasetId: string,
    options?: Parameters<typeof temporalAccuracyService.startValidation>[1],
  ) => {
    try {
      setIsValidating(true);
      setError(null);

      const { sessionId } = await temporalAccuracyService.startValidation(
        datasetId,
        options,
      );

      // Start polling for progress
      void temporalAccuracyService.pollValidationProgress(
        sessionId,
        (progress) => {
          setValidationProgress(progress.progress);
          setCurrentStage(progress.currentStage);
        },
        (_finalResults) => {
          setIsValidating(false);
          setValidationProgress(100);
          setCurrentStage('Completed');
        },
        (error) => {
          setIsValidating(false);
          setError(error.message);
        },
      );

      return { sessionId };
    } catch (error) {
      setIsValidating(false);
      setError((error as Error).message);
      throw error;
    }
  };

  const stopValidation = (sessionId: string) => {
    temporalAccuracyService.stopPolling(sessionId);
    setIsValidating(false);
    setValidationProgress(0);
    setCurrentStage('');
  };

  return {
    isValidating,
    validationProgress,
    currentStage,
    error,
    startValidation,
    stopValidation,
    service: temporalAccuracyService,
  };
};

export default TemporalAccuracyService;
