/**
 * Accuracy Trends Panel Component
 * Statistical progression analysis for M2 milestone validation
 * Professional dashboard for accuracy metrics and trends
 */
import React, { useMemo } from 'react';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Activity,
  Target,
  Clock,
  Award,
  Users,
  Zap,
  CheckCircle2,
  AlertTriangle,
} from 'lucide-react';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';

interface AccuracyMetric {
  period: string;
  accuracy: number;
  confidence: number;
  transactionCount: number;
  categoriesUsed: number;
  manualCorrections: number;
  averageProcessingTime: number;
  topCategories: Array<{
    name: string;
    count: number;
    accuracy: number;
  }>;
}

interface AccuracyTrendsPanelProps {
  data: AccuracyMetric[];
  currentPeriod: string;
  milestone: 'M1' | 'M2' | 'M3';
  showDetailed?: boolean;
  onExportReport?: () => void;
  onDrillDown?: (period: string) => void;
  className?: string;
}

export const AccuracyTrendsPanel: React.FC<AccuracyTrendsPanelProps> = ({
  data,
  currentPeriod,
  milestone,
  showDetailed = true,
  onExportReport,
  onDrillDown: _onDrillDown,
  className = '',
}) => {
  // Calculate comprehensive analytics
  const analytics = useMemo(() => {
    if (data.length === 0) return null;

    const latest = data[data.length - 1];
    const previous = data.length > 1 ? data[data.length - 2] : latest;

    // Trend calculations
    const accuracyTrend = latest.accuracy - previous.accuracy;
    const confidenceTrend = latest.confidence - previous.confidence;
    const volumeTrend = latest.transactionCount - previous.transactionCount;

    // Overall statistics
    const totalTransactions = data.reduce(
      (sum, d) => sum + d.transactionCount,
      0,
    );
    const totalCorrections = data.reduce(
      (sum, d) => sum + d.manualCorrections,
      0,
    );
    const avgAccuracy =
      data.reduce((sum, d) => sum + d.accuracy, 0) / data.length;
    const avgProcessingTime =
      data.reduce((sum, d) => sum + d.averageProcessingTime, 0) / data.length;

    // Performance metrics
    const correctionRate = totalCorrections / totalTransactions;
    const consistencyScore =
      1 -
      (Math.max(...data.map((d) => d.accuracy)) -
        Math.min(...data.map((d) => d.accuracy)));
    const velocityTrend =
      data.length > 1
        ? (data[data.length - 1].averageProcessingTime -
            data[0].averageProcessingTime) /
          data[0].averageProcessingTime
        : 0;

    // Milestone progress
    const milestoneTargets = { M1: 0.85, M2: 0.9, M3: 0.95 };
    const targetAccuracy = milestoneTargets[milestone];
    const progressToTarget = latest.accuracy / targetAccuracy;

    return {
      latest,
      trends: {
        accuracy: accuracyTrend,
        confidence: confidenceTrend,
        volume: volumeTrend,
        velocity: velocityTrend,
      },
      overall: {
        totalTransactions,
        totalCorrections,
        avgAccuracy,
        avgProcessingTime,
        correctionRate,
        consistencyScore,
      },
      milestone: {
        target: targetAccuracy,
        progress: progressToTarget,
        achieved: latest.accuracy >= targetAccuracy,
      },
    };
  }, [data, milestone]);

  if (!analytics) {
    return (
      <Card className={`border-gray-200 ${className}`}>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            No accuracy trends data available
          </div>
        </CardContent>
      </Card>
    );
  }

  const { latest, trends, overall, milestone: milestoneData } = analytics;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Current Status */}
      <Card className="border-brand-primary/20 bg-gradient-to-br from-brand-primary/5 to-brand-primary/10">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                <BarChart3 className="h-6 w-6" style={{ color: 'var(--giki-primary)' }} />
                Accuracy Trends Analysis ({milestone})
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Period: {currentPeriod} •{' '}
                {overall.totalTransactions.toLocaleString()} transactions
                processed
              </p>
            </div>

            <div className="flex items-center gap-4">
              {milestoneData.achieved && (
                <div className="flex items-center gap-1 text-success">
                  <Award className="h-5 w-5" />
                  <span className="font-medium">Target Achieved!</span>
                </div>
              )}

              {onExportReport && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onExportReport}
                  className="hover:opacity-80"
                  style={{ borderColor: 'var(--giki-primary)', color: 'var(--giki-primary)' }}
                >
                  Export Report
                </Button>
              )}
            </div>
          </div>

          {/* Key Performance Indicators */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div
              className="bg-white rounded-lg p-4 border"
              style={{ borderColor: 'rgba(41, 83, 67, 0.2)' }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <div
                    className="text-2xl font-bold"
                    style={{ color: 'var(--giki-primary)' }}
                  >
                    {(latest.accuracy * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-600">Current Accuracy</div>
                </div>
                <div className="flex items-center gap-1">
                  {trends.accuracy > 0 ? (
                    <TrendingUp className="h-4 w-4 text-success" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-error" />
                  )}
                  <span
                    className={`text-xs ${trends.accuracy > 0 ? 'text-success' : 'text-error'}`}
                  >
                    {trends.accuracy > 0 ? '+' : ''}
                    {(trends.accuracy * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-4 border border-blue-200">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {(latest.confidence * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-600">Avg Confidence</div>
                </div>
                <div className="flex items-center gap-1">
                  <Activity className="h-4 w-4 text-blue-600" />
                  <span
                    className={`text-xs ${trends.confidence > 0 ? 'text-success' : 'text-error'}`}
                  >
                    {trends.confidence > 0 ? '+' : ''}
                    {(trends.confidence * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-4 border border-purple-200">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {latest.transactionCount.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-600">This Period</div>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4 text-purple-600" />
                  <span
                    className={`text-xs ${trends.volume > 0 ? 'text-success' : 'text-error'}`}
                  >
                    {trends.volume > 0 ? '+' : ''}
                    {trends.volume.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-4 border border-orange-200">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {latest.averageProcessingTime.toFixed(1)}s
                  </div>
                  <div className="text-xs text-gray-600">Processing Time</div>
                </div>
                <div className="flex items-center gap-1">
                  <Zap className="h-4 w-4 text-orange-600" />
                  <span
                    className={`text-xs ${trends.velocity < 0 ? 'text-success' : 'text-error'}`}
                  >
                    {trends.velocity < 0 ? '' : '+'}
                    {(trends.velocity * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Milestone Progress */}
      <Card className="border-gray-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Target className="h-5 w-5" style={{ color: 'var(--giki-primary)' }} />
              Milestone Progress ({milestone})
            </h3>
            <div
              className={`flex items-center gap-1 ${milestoneData.achieved ? 'text-success' : 'text-yellow-600'}`}
            >
              {milestoneData.achieved ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <AlertTriangle className="h-4 w-4" />
              )}
              <span className="font-medium">
                {milestoneData.achieved ? 'Achieved' : 'In Progress'}
              </span>
            </div>
          </div>

          <div className="space-y-4">
            {/* Progress Bar */}
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-600">
                  Progress to {milestone} Target
                </span>
                <span className="font-medium">
                  {(milestoneData.progress * 100).toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="h-3 rounded-full transition-all duration-500"
                  style={{
                    width: `${Math.min(100, milestoneData.progress * 100)}%`,
                    backgroundColor: milestoneData.achieved
                      ? '#10B981'
                      : '#295343',
                  }}
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0%</span>
                <span>Target: {(milestoneData.target * 100).toFixed(0)}%</span>
                <span>100%</span>
              </div>
            </div>

            {/* Milestone Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm font-medium text-gray-700 mb-1">
                  Current Accuracy
                </div>
                <div className="text-lg font-bold" style={{ color: 'var(--giki-primary)' }}>
                  {(latest.accuracy * 100).toFixed(2)}%
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm font-medium text-gray-700 mb-1">
                  Target Accuracy
                </div>
                <div className="text-lg font-bold text-gray-600">
                  {(milestoneData.target * 100).toFixed(0)}%
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm font-medium text-gray-700 mb-1">
                  Gap to Target
                </div>
                <div
                  className={`text-lg font-bold ${
                    milestoneData.achieved
                      ? 'text-success'
                      : 'text-yellow-600'
                  }`}
                >
                  {milestoneData.achieved
                    ? '□ Met'
                    : `${((milestoneData.target - latest.accuracy) * 100).toFixed(1)}%`}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Analytics */}
      {showDetailed && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Performance Metrics */}
          <Card className="border-gray-200">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Activity className="h-5 w-5" style={{ color: 'var(--giki-primary)' }} />
                Performance Metrics
              </h3>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    Manual Correction Rate
                  </span>
                  <span className="font-medium">
                    {(overall.correctionRate * 100).toFixed(2)}%
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    Consistency Score
                  </span>
                  <span className="font-medium">
                    {(overall.consistencyScore * 100).toFixed(1)}%
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    Average Processing Time
                  </span>
                  <span className="font-medium">
                    {overall.avgProcessingTime.toFixed(2)}s
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    Total Transactions
                  </span>
                  <span className="font-medium">
                    {overall.totalTransactions.toLocaleString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Categories Performance */}
          <Card className="border-gray-200">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <PieChart className="h-5 w-5" style={{ color: 'var(--giki-primary)' }} />
                Top Categories
              </h3>

              <div className="space-y-3">
                {latest.topCategories?.slice(0, 5).map((category, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between"
                  >
                    <div>
                      <div className="font-medium text-sm">{category.name}</div>
                      <div className="text-xs text-gray-500">
                        {category.count} transactions
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-sm">
                        {(category.accuracy * 100).toFixed(1)}%
                      </div>
                      <div className="w-16 bg-gray-200 rounded-full h-1">
                        <div
                          className="h-1 rounded-full"
                          style={{
                            width: `${category.accuracy * 100}%`,
                            backgroundColor: 'var(--giki-primary)',
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Footer with timestamp */}
      <div className="text-xs text-gray-500 flex items-center justify-between">
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <span>Last updated: {new Date().toLocaleString()}</span>
        </div>
        <div>Data source: M2 Temporal Accuracy Validation</div>
      </div>
    </div>
  );
};

export default AccuracyTrendsPanel;
