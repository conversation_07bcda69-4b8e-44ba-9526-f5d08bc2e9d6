/**
 * Accuracy Performance Dashboard
 *
 * Professional analytics dashboard for categorization accuracy trends,
 * historical validation, and performance tracking with enhanced visualization.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { toast } from '../../../shared/components/ui/use-toast';
import { Button } from '../../../shared/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../../shared/components/ui/tabs';
import { Badge } from '../../../shared/components/ui/badge';
import { Progress } from '../../../shared/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../shared/components/ui/select';
import {
  Target,
  Clock,
  CheckCircle,
  RefreshCw,
  Download,
  Zap,
  Award,
  Activity,
  ArrowUp,
  ArrowDown,
  Minus,
} from 'lucide-react';

import { TemporalAccuracyChart } from './TemporalAccuracyChart';
import { AccuracyTrendsPanel } from './AccuracyTrendsPanel';
import { HistoricalValidationDashboard } from './HistoricalValidationDashboard';
import { TemporalAccuracyValidationPanel as HistoricalValidationPanel } from './TemporalAccuracyValidationPanel';
import { accuracyService } from '../services/accuracyService';
import {
  OverallAccuracyTooltip,
  CategorizationAccuracyTooltip,
} from '@/shared/components/ui/accuracy-tooltip';

// Types
interface AccuracyMetrics {
  overall_accuracy: number;
  improvement_over_time: number;
  temporal_consistency: number;
  milestone_progress: {
    m1_accuracy: number;
    m2_accuracy: number;
    m3_target: number;
    current_milestone: 'M1' | 'M2' | 'M3';
  };
  quality_indicators: {
    non_generic_rate: number;
    hierarchical_accuracy: number;
    gl_code_compliance: number;
    business_appropriateness: number;
  };
  performance_metrics: {
    processing_speed: number;
    throughput_per_hour: number;
    error_rate: number;
    uptime_percentage: number;
  };
}

interface TemporalData {
  period: string;
  accuracy: number;
  improvement: number;
  transaction_count: number;
  confidence: number;
  trend: 'improving' | 'stable' | 'declining';
  categories_processed?: number;
  ai_confidence?: number;
  manual_overrides?: number;
  avg_processing_time?: number;
  top_categories?: Array<{
    name: string;
    count: number;
    accuracy: number;
  }>;
}

interface CategoryBreakdown {
  category_name: string;
  accuracy_percentage: number;
  transaction_count: number;
  confidence_score: number;
  improvement_trend: number;
  validation_status: 'excellent' | 'good' | 'needs_improvement' | 'critical';
}

interface MilestoneStatus {
  milestone: 'M1' | 'M2' | 'M3';
  name: string;
  target_accuracy: number;
  current_accuracy: number;
  status: 'completed' | 'in_progress' | 'pending';
  completion_date?: string;
  validation_method: string;
  confidence_level: number;
}

// Component Props
interface AccuracyDashboardProps {
  className?: string;
  timeRange?: '7d' | '30d' | '90d' | '1y';
  showAdvanced?: boolean;
  milestoneFilter?: 'all' | 'M1' | 'M2' | 'M3';
}

export const AccuracyDashboard: React.FC<AccuracyDashboardProps> = React.memo(({
  className = '',
  timeRange: initialTimeRange = '30d',
  showAdvanced: _showAdvanced = true,
  milestoneFilter: _milestoneFilter = 'all',
}) => {
  // State
  const [metrics, setMetrics] = useState<AccuracyMetrics | null>(null);
  const [temporalData, setTemporalData] = useState<TemporalData[]>([]);
  const [categoryBreakdown, setCategoryBreakdown] = useState<
    CategoryBreakdown[]
  >([]);
  const [milestoneStatus, setMilestoneStatus] = useState<MilestoneStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState(initialTimeRange);
  const [activeTab, setActiveTab] = useState('overview');

  // Load dashboard data
  const loadDashboardData = useCallback(async () => {
    try {
      setRefreshing(true);

      const [
        metricsResponse,
        temporalResponse,
        categoryResponse,
        milestoneResponse,
      ] = await Promise.all([
        accuracyService.getDashboardMetrics({ timeRange }),
        accuracyService.getTemporalAccuracyData({ timeRange }),
        accuracyService.getCategoryAccuracyBreakdown({ timeRange }),
        accuracyService.getMilestoneStatus(),
      ]);

      setMetrics(metricsResponse as AccuracyMetrics);
      setTemporalData(
        (temporalResponse as { data?: TemporalData[] }).data || [],
      );
      setCategoryBreakdown(
        (categoryResponse as { categories?: CategoryBreakdown[] }).categories ||
          [],
      );
      setMilestoneStatus(
        (milestoneResponse as { milestones?: MilestoneStatus[] }).milestones ||
          [],
      );
    } catch (error) {
      console.error('Failed to load accuracy dashboard data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load accuracy data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [timeRange]);

  useEffect(() => {
    void loadDashboardData();
  }, [loadDashboardData]);

  // Computed values
  const trendIndicator = useMemo(() => {
    if (!metrics) return 'stable';
    if (metrics.improvement_over_time > 2) return 'improving';
    if (metrics.improvement_over_time < -2) return 'declining';
    return 'stable';
  }, [metrics]);

  const currentMilestone = useMemo(() => {
    return (
      milestoneStatus.find((m) => m.status === 'in_progress') ||
      milestoneStatus.find((m) => m.status === 'completed') ||
      milestoneStatus[0]
    );
  }, [milestoneStatus]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <ArrowUp className="w-4 h-4 text-green-500" />;
      case 'declining':
        return <ArrowDown className="w-4 h-4 text-red-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-success bg-green-100';
      case 'in_progress':
        return 'text-blue-600 bg-blue-100';
      case 'pending':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getValidationStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-success';
      case 'good':
        return 'text-blue-600';
      case 'needs_improvement':
        return 'text-yellow-600';
      case 'critical':
        return 'text-error';
      default:
        return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div
            className="animate-spin rounded-full h-8 w-8 border-b-2"
            style={{ borderColor: 'var(--giki-primary)' }}
          ></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 bg-white min-h-screen">
      {/* Professional Header - Enhanced Design */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-semibold text-gray-700 mb-2">
              Accuracy Analytics
            </h1>
            <p className="text-gray-500 text-base">
              Comprehensive categorization accuracy tracking and milestone
              progress
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Select
              value={timeRange}
              onValueChange={(value: '7d' | '30d' | '90d' | '1y') =>
                setTimeRange(value)
              }
            >
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => void loadDashboardData()}
              disabled={refreshing}
              className="border-brand-primary text-brand-primary hover:bg-green-50"
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>

            <Button
              variant="outline"
              className="border-brand-primary text-brand-primary hover:bg-green-50"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards - Professional Style */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
                <Target className="w-4 h-4 mr-2" />
                <OverallAccuracyTooltip
                  accuracy={metrics.overall_accuracy / 100}
                  className="text-gray-500"
                >
                  <span>Overall Accuracy</span>
                </OverallAccuracyTooltip>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <div className="text-4xl font-bold text-brand-primary font-mono">
                  {metrics.overall_accuracy.toFixed(1)}%
                </div>
                {getTrendIcon(trendIndicator)}
              </div>
              <Progress value={metrics.overall_accuracy} className="mt-2 h-2" />
              <p className="text-xs text-gray-400 mt-1">
                {metrics.improvement_over_time > 0 ? '+' : ''}
                {metrics.improvement_over_time.toFixed(1)}% vs previous period
              </p>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
                <Activity className="w-4 h-4 mr-2" />
                Temporal Consistency
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold text-blue-600 font-mono">
                {metrics.temporal_consistency.toFixed(1)}%
              </div>
              <Progress
                value={metrics.temporal_consistency}
                className="mt-2 h-2"
              />
              <p className="text-xs text-gray-400 mt-1">
                Accuracy stability over time
              </p>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
                <Award className="w-4 h-4 mr-2" />
                Current Milestone
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <div className="text-4xl font-bold text-purple-600 font-mono">
                  {currentMilestone?.milestone || 'N/A'}
                </div>
                <Badge
                  className={getStatusColor(
                    currentMilestone?.status || 'pending',
                  )}
                >
                  {currentMilestone?.status}
                </Badge>
              </div>
              <CategorizationAccuracyTooltip
                accuracy={(currentMilestone?.current_accuracy || 0) / 100}
                className="text-xs text-gray-400 mt-1"
              >
                <p>{currentMilestone?.current_accuracy.toFixed(1)}% accuracy</p>
              </CategorizationAccuracyTooltip>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
                <Zap className="w-4 h-4 mr-2" />
                Processing Speed
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold text-success font-mono">
                {metrics.performance_metrics.throughput_per_hour}
              </div>
              <p className="text-xs text-gray-400">transactions/hour</p>
              <p className="text-xs text-success mt-1">
                {metrics.performance_metrics.processing_speed}ms avg
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="temporal">Temporal Analysis</TabsTrigger>
          <TabsTrigger value="historical-validation">
            Historical Validation
          </TabsTrigger>
          <TabsTrigger value="categories">Category Breakdown</TabsTrigger>
          <TabsTrigger value="milestones">Milestones</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Quality Indicators - Professional Style */}
          {metrics && (
            <Card className="border border-gray-200 shadow-sm overflow-hidden">
              <CardHeader className="bg-brand-primary text-white px-6 py-4">
                <CardTitle className="text-base font-semibold">
                  Quality Indicators
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 font-mono">
                      {metrics.quality_indicators.non_generic_rate.toFixed(1)}%
                    </div>
                    <p className="text-sm text-gray-500">Non-Generic Rate</p>
                    <Progress
                      value={metrics.quality_indicators.non_generic_rate}
                      className="mt-2 h-2"
                    />
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-success font-mono">
                      {metrics.quality_indicators.hierarchical_accuracy.toFixed(
                        1,
                      )}
                      %
                    </div>
                    <p className="text-sm text-gray-500">
                      Hierarchical Accuracy
                    </p>
                    <Progress
                      value={metrics.quality_indicators.hierarchical_accuracy}
                      className="mt-2 h-2"
                    />
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600 font-mono">
                      {metrics.quality_indicators.gl_code_compliance.toFixed(1)}
                      %
                    </div>
                    <p className="text-sm text-gray-500">GL Code Compliance</p>
                    <Progress
                      value={metrics.quality_indicators.gl_code_compliance}
                      className="mt-2 h-2"
                    />
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-brand-primary font-mono">
                      {metrics.quality_indicators.business_appropriateness.toFixed(
                        1,
                      )}
                      %
                    </div>
                    <p className="text-sm text-gray-500">
                      Business Appropriateness
                    </p>
                    <Progress
                      value={
                        metrics.quality_indicators.business_appropriateness
                      }
                      className="mt-2 h-2"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Temporal Trends */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TemporalAccuracyChart
              data={temporalData.map((d) => ({
                date: d.period,
                accuracy: d.accuracy,
                confidence: d.confidence,
                transactionCount: d.transaction_count,
                categoriesProcessed: d.categories_processed || 0,
                aiConfidence: d.ai_confidence || d.confidence,
                manualOverrides: d.manual_overrides || 0,
              }))}
              timeRange={
                timeRange === '7d'
                  ? 'week'
                  : timeRange === '30d'
                    ? 'month'
                    : timeRange === '90d'
                      ? 'quarter'
                      : 'year'
              }
              milestone="Historical"
            />

            <AccuracyTrendsPanel
              data={temporalData.map((d) => ({
                period: d.period,
                accuracy: d.accuracy,
                confidence: d.confidence,
                transactionCount: d.transaction_count,
                categoriesUsed: d.categories_processed || 0,
                manualCorrections: d.manual_overrides || 0,
                averageProcessingTime: d.avg_processing_time || 0,
                topCategories: d.top_categories || [],
              }))}
              currentPeriod={timeRange}
              milestone="Historical"
            />
          </div>
        </TabsContent>

        <TabsContent value="temporal" className="space-y-6">
          <HistoricalValidationDashboard
            milestone="Historical"
            showExportOptions={true}
          />
        </TabsContent>

        <TabsContent value="historical-validation" className="space-y-6">
          <HistoricalValidationPanel
            tenantId={2}
            showDetailed={true}
            onValidationComplete={(result) => {
              console.log('Historical validation completed:', result);
              // Trigger dashboard data refresh
              void loadDashboardData();
            }}
          />
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card className="border border-gray-200 shadow-sm overflow-hidden">
            <CardHeader className="bg-brand-primary text-white px-6 py-4">
              <CardTitle className="text-base font-semibold">
                Category Performance Analysis
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-0">
                {categoryBreakdown.map((category, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-6 border-b border-gray-100 hover:bg-green-50 transition-colors last:border-b-0"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium text-gray-900 truncate">
                          {category.category_name}
                        </h4>
                        <Badge
                          className={getValidationStatusColor(
                            category.validation_status,
                          )}
                        >
                          {category.validation_status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">
                        {category.transaction_count} transactions processed
                      </p>
                    </div>

                    <div className="flex items-center gap-4 shrink-0">
                      <div className="text-right">
                        <div className="text-lg font-bold text-brand-primary font-mono">
                          {category.accuracy_percentage.toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-500">
                          {category.confidence_score.toFixed(1)}% confidence
                        </div>
                      </div>

                      <div className="w-24">
                        <Progress
                          value={category.accuracy_percentage}
                          className="h-2"
                        />
                      </div>

                      <div className="flex items-center gap-1">
                        {getTrendIcon(
                          category.improvement_trend > 0
                            ? 'improving'
                            : category.improvement_trend < 0
                              ? 'declining'
                              : 'stable',
                        )}
                        <span className="text-sm font-medium">
                          {category.improvement_trend > 0 ? '+' : ''}
                          {category.improvement_trend.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="milestones" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {milestoneStatus.map((milestone, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      {milestone.milestone}: {milestone.name}
                    </CardTitle>
                    <Badge className={getStatusColor(milestone.status)}>
                      {milestone.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-600">Progress</span>
                        <span className="text-sm font-medium">
                          {milestone.current_accuracy.toFixed(1)}% /{' '}
                          {milestone.target_accuracy}%
                        </span>
                      </div>
                      <Progress
                        value={
                          (milestone.current_accuracy /
                            milestone.target_accuracy) *
                          100
                        }
                        className="h-2"
                      />
                    </div>

                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-gray-600">
                          Validation Method:
                        </span>
                        <span className="font-medium">
                          {milestone.validation_method}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Confidence Level:</span>
                        <span className="font-medium">
                          {milestone.confidence_level.toFixed(1)}%
                        </span>
                      </div>
                      {milestone.completion_date && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Completed:</span>
                          <span className="font-medium">
                            {milestone.completion_date}
                          </span>
                        </div>
                      )}
                    </div>

                    {milestone.status === 'completed' && (
                      <div className="flex items-center gap-2 text-success">
                        <CheckCircle className="w-4 h-4" />
                        <span className="text-sm font-medium">
                          Milestone Achieved
                        </span>
                      </div>
                    )}

                    {milestone.status === 'in_progress' && (
                      <div className="flex items-center gap-2 text-blue-600">
                        <Clock className="w-4 h-4" />
                        <span className="text-sm font-medium">In Progress</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Status Bar (Inline - No Floating Notifications) */}
      <div className="flex gap-4 mt-6 flex-wrap">
        <div className="px-4 py-3 rounded border-l-4 border-brand-primary bg-blue-50 text-brand-primary text-sm font-medium">
          Historical Validation Status:{' '}
          {metrics
            ? 'System ready for accuracy analysis'
            : 'Loading metrics...'}
        </div>
        {currentMilestone?.status === 'in_progress' && (
          <div className="px-4 py-3 rounded border-l-4 border-blue-500 bg-blue-50 text-blue-700 text-sm font-medium">
            {currentMilestone.milestone} in progress:{' '}
            {currentMilestone.current_accuracy.toFixed(1)}% /{' '}
            {currentMilestone.target_accuracy}% target
          </div>
        )}
      </div>
    </div>
  );
});

AccuracyDashboard.displayName = 'AccuracyDashboard';

export default AccuracyDashboard;
