/**
 * Transaction Results Table Component
 *
 * Displays detailed categorization results for each transaction
 * with AI vs original category comparison and quality indicators.
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import { Badge } from '../../../shared/components/ui/badge';
import { Button } from '../../../shared/components/ui/button';
import { Input } from '../../../shared/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../shared/components/ui/select';
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
  Filter,
  ArrowUpDown,
  Eye,
} from 'lucide-react';
import { toast } from '../../../shared/components/ui/use-toast';

import { accuracyService } from '../services/accuracyService';
import type { TransactionResult } from '../types/accuracy';

interface TransactionResultsTableProps {
  testId: number;
}

interface TableFilters {
  search: string;
  correctness: 'all' | 'correct' | 'incorrect' | 'partial';
  confidence: 'all' | 'high' | 'medium' | 'low';
  quality: 'all' | 'high_quality' | 'generic' | 'hierarchical';
}

const TransactionResultsTable: React.FC<TransactionResultsTableProps> = ({
  testId,
}) => {
  const [results, setResults] = useState<TransactionResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState<TableFilters>({
    search: '',
    correctness: 'all',
    confidence: 'all',
    quality: 'all',
  });
  const [sortBy, setSortBy] = useState<
    'confidence' | 'description' | 'correctness'
  >('confidence');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const loadResults = useCallback(async () => {
    try {
      setLoading(true);
      const response = await accuracyService.getTransactionResults(testId, {
        page: currentPage,
        page_size: 50,
        search: filters.search || undefined,
        correctness:
          filters.correctness !== 'all' ? filters.correctness : undefined,
        confidence_range:
          filters.confidence !== 'all' ? filters.confidence : undefined,
        quality_filter: filters.quality !== 'all' ? filters.quality : undefined,
        sort_by: sortBy,
        sort_order: sortOrder,
      });

      setResults(response.results);
      setTotalPages(Math.ceil(response.total / 50));
    } catch (error) {
      console.error('Failed to load transaction results:', error);
      toast({
        title: 'Error',
        description: 'Failed to load transaction results. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [testId, currentPage, filters, sortBy, sortOrder]);

  useEffect(() => {
    void loadResults();
  }, [loadResults]);

  const getCorrectnessIcon = (isCorrect: boolean | null) => {
    if (isCorrect === null)
      return <AlertCircle className="w-4 h-4 text-gray-400" />;
    return isCorrect ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const getCorrectnessText = (isCorrect: boolean | null) => {
    if (isCorrect === null) return 'Unknown';
    return isCorrect ? 'Correct' : 'Incorrect';
  };

  const getCorrectnessColor = (isCorrect: boolean | null) => {
    if (isCorrect === null) return 'bg-gray-100 text-gray-800';
    return isCorrect
      ? 'bg-green-100 text-green-800'
      : 'bg-red-100 text-red-800';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-success';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-error';
  };

  const formatConfidence = (confidence: number): string => {
    return `${(confidence * 100).toFixed(1)}%`;
  };

  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('desc');
    }
  };

  if (loading && results.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading transaction results...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Eye className="w-5 h-5" />
            Transaction Results
          </CardTitle>

          <div className="text-sm text-gray-600">
            Showing {results.length} of {results.length * totalPages} results
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search descriptions..."
              value={filters.search}
              onChange={(e) =>
                setFilters({ ...filters, search: e.target.value })
              }
              className="pl-10"
            />
          </div>

          <Select
            value={filters.correctness}
            onValueChange={(value) =>
              setFilters({
                ...filters,
                correctness: value as TableFilters['correctness'],
              })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Correctness" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Results</SelectItem>
              <SelectItem value="correct">Correct</SelectItem>
              <SelectItem value="incorrect">Incorrect</SelectItem>
              <SelectItem value="partial">Partial</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.confidence}
            onValueChange={(value) =>
              setFilters({
                ...filters,
                confidence: value as TableFilters['confidence'],
              })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Confidence" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Confidence</SelectItem>
              <SelectItem value="high">High (80%+)</SelectItem>
              <SelectItem value="medium">Medium (60-80%)</SelectItem>
              <SelectItem value="low">Low (&lt;60%)</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.quality}
            onValueChange={(value) =>
              setFilters({
                ...filters,
                quality: value as TableFilters['quality'],
              })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Quality" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Quality</SelectItem>
              <SelectItem value="high_quality">High Quality</SelectItem>
              <SelectItem value="generic">Generic</SelectItem>
              <SelectItem value="hierarchical">Hierarchical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Results Table */}
        <div className="border rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('description')}
                      className="h-auto p-0 font-medium text-gray-500 hover:text-gray-700"
                    >
                      Transaction
                      <ArrowUpDown className="w-3 h-3 ml-1" />
                    </Button>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Original Category
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    AI Category
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('confidence')}
                      className="h-auto p-0 font-medium text-gray-500 hover:text-gray-700"
                    >
                      Confidence
                      <ArrowUpDown className="w-3 h-3 ml-1" />
                    </Button>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('correctness')}
                      className="h-auto p-0 font-medium text-gray-500 hover:text-gray-700"
                    >
                      Correctness
                      <ArrowUpDown className="w-3 h-3 ml-1" />
                    </Button>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quality
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {results.map((result) => (
                  <tr key={result.transaction_id} className="hover:bg-gray-50">
                    <td className="px-4 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                          {result.description}
                        </div>
                        <div className="text-sm text-gray-500">
                          ${result.amount.toFixed(2)}
                        </div>
                      </div>
                    </td>

                    <td className="px-4 py-4">
                      <div className="text-sm text-gray-900">
                        {result.original_category || (
                          <span className="text-gray-400 italic">
                            Not available
                          </span>
                        )}
                      </div>
                    </td>

                    <td className="px-4 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {result.ai_category}
                        </div>
                        {result.ai_hierarchy && (
                          <div className="text-xs text-gray-500 mt-1">
                            {result.ai_hierarchy}
                          </div>
                        )}
                      </div>
                    </td>

                    <td className="px-4 py-4">
                      <div
                        className={`text-sm font-medium ${getConfidenceColor(result.ai_confidence)}`}
                      >
                        {formatConfidence(result.ai_confidence)}
                      </div>
                    </td>

                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        {getCorrectnessIcon(result.is_correct)}
                        <Badge
                          className={`text-xs ${getCorrectnessColor(result.is_correct)}`}
                        >
                          {getCorrectnessText(result.is_correct)}
                        </Badge>
                      </div>
                    </td>

                    <td className="px-4 py-4">
                      <div className="flex flex-wrap gap-1">
                        {!result.is_generic && (
                          <Badge
                            variant="outline"
                            className="text-xs text-success"
                          >
                            Specific
                          </Badge>
                        )}
                        {result.has_hierarchy && (
                          <Badge
                            variant="outline"
                            className="text-xs text-success"
                          >
                            Hierarchical
                          </Badge>
                        )}
                        {result.has_gl_codes && (
                          <Badge
                            variant="outline"
                            className="text-xs text-success"
                          >
                            GL Codes
                          </Badge>
                        )}
                        {result.is_generic && (
                          <Badge
                            variant="outline"
                            className="text-xs text-gray-600"
                          >
                            Generic
                          </Badge>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {results.length === 0 && !loading && (
          <div className="text-center py-12">
            <Filter className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No results found
            </h3>
            <p className="text-gray-600">
              Try adjusting your filters to see more results.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TransactionResultsTable;
