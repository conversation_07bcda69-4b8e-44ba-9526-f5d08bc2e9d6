/**
 * Temporal Accuracy Chart Component
 * Professional timeline visualization for accuracy performance tracking
 * Shows accuracy improvement over time with confidence intervals
 */
import React, { useMemo, useCallback } from 'react';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Calendar,
  Target,
  Award,
  Clock,
  CheckCircle2,
} from 'lucide-react';
import { Card, CardContent } from '@/shared/components/ui/card';

interface AccuracyDataPoint {
  date: string;
  accuracy: number;
  confidence: number;
  transactionCount: number;
  categoriesProcessed: number;
  aiConfidence: number;
  manualOverrides: number;
}

interface TemporalAccuracyChartProps {
  data: AccuracyDataPoint[];
  timeRange: 'week' | 'month' | 'quarter' | 'year';
  showConfidenceInterval?: boolean;
  showTrends?: boolean;
  setupType?: 'Quick' | 'DataLearning' | 'Structured';
  className?: string;
}

export const TemporalAccuracyChart: React.FC<TemporalAccuracyChartProps> = ({
  data,
  timeRange,
  showConfidenceInterval = true,
  showTrends = true,
  setupType = 'DataLearning',
  className = '',
}) => {
  // Calculate statistics and trends
  const stats = useMemo(() => {
    if (data.length === 0) return null;

    const latestPoint = data[data.length - 1];
    const earliestPoint = data[0];
    const improvement = latestPoint.accuracy - earliestPoint.accuracy;
    const avgAccuracy =
      data.reduce((sum, point) => sum + point.accuracy, 0) / data.length;
    const totalTransactions = data.reduce(
      (sum, point) => sum + point.transactionCount,
      0,
    );

    // Calculate trend (simple linear regression)
    const n = data.length;
    const sumX = data.reduce((sum, _, i) => sum + i, 0);
    const sumY = data.reduce((sum, point) => sum + point.accuracy, 0);
    const sumXY = data.reduce((sum, point, i) => sum + i * point.accuracy, 0);
    const sumXX = data.reduce((sum, _, i) => sum + i * i, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const isImproving = slope > 0;

    return {
      latest: latestPoint.accuracy,
      improvement,
      avgAccuracy,
      totalTransactions,
      isImproving,
      trend: slope,
      confidenceRange: {
        min: Math.min(...data.map((d) => d.accuracy)),
        max: Math.max(...data.map((d) => d.accuracy)),
      },
    };
  }, [data]);

  // Chart dimensions and scales
  const chartWidth = 800;
  const chartHeight = 300;
  const padding = { top: 20, right: 40, bottom: 60, left: 60 };
  const plotWidth = chartWidth - padding.left - padding.right;
  const plotHeight = chartHeight - padding.top - padding.bottom;

  // Scale functions
  const xScale = useCallback(
    (index: number) => (index / (data.length - 1)) * plotWidth + padding.left,
    [data.length, plotWidth, padding.left],
  );
  const yScale = useCallback(
    (accuracy: number) => {
      const minAccuracy = Math.max(0, (stats?.confidenceRange.min || 0) - 0.05);
      const maxAccuracy = Math.min(1, (stats?.confidenceRange.max || 1) + 0.05);
      return (
        chartHeight -
        padding.bottom -
        ((accuracy - minAccuracy) / (maxAccuracy - minAccuracy)) * plotHeight
      );
    },
    [
      stats?.confidenceRange.min,
      stats?.confidenceRange.max,
      chartHeight,
      padding.bottom,
      plotHeight,
    ],
  );

  // Generate path for accuracy line
  const accuracyPath = useMemo(() => {
    if (data.length < 2) return '';

    let path = `M ${xScale(0)} ${yScale(data[0].accuracy)}`;
    for (let i = 1; i < data.length; i++) {
      path += ` L ${xScale(i)} ${yScale(data[i].accuracy)}`;
    }
    return path;
  }, [data, xScale, yScale]);

  // Generate confidence interval area
  const confidenceArea = useMemo(() => {
    if (!showConfidenceInterval || data.length < 2) return '';

    const upperPath = data
      .map(
        (point, i) =>
          `${i === 0 ? 'M' : 'L'} ${xScale(i)} ${yScale(point.accuracy + 0.02)}`,
      )
      .join(' ');

    const lowerPath = data
      .slice()
      .reverse()
      .map(
        (point, i) =>
          `${i === 0 ? 'L' : 'L'} ${xScale(data.length - 1 - i)} ${yScale(point.accuracy - 0.02)}`,
      )
      .join(' ');

    return `${upperPath} ${lowerPath} Z`;
  }, [data, showConfidenceInterval, xScale, yScale]);

  if (!stats) {
    return (
      <Card className={`border-gray-200 ${className}`}>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            No temporal accuracy data available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`border-gray-200 ${className}`}>
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-brand-primary" />
              Accuracy Progression ({setupType})
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              AI categorization accuracy improvement over {timeRange}
            </p>
          </div>

          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-2xl font-bold text-brand-primary">
                {(stats.latest * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-gray-600">Current Accuracy</div>
            </div>

            {showTrends && (
              <div className="flex items-center gap-1">
                {stats.isImproving ? (
                  <TrendingUp className="h-4 w-4 text-success" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-error" />
                )}
                <span
                  className={`text-sm font-medium ${
                    stats.isImproving ? 'text-success' : 'text-error'
                  }`}
                >
                  {stats.improvement > 0 ? '+' : ''}
                  {(stats.improvement * 100).toFixed(1)}%
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Chart SVG */}
        <div className="mb-6 overflow-x-auto">
          <svg
            width={chartWidth}
            height={chartHeight}
            className="border border-gray-200 rounded-lg bg-gradient-to-br from-gray-50 to-white"
          >
            {/* Grid lines */}
            <defs>
              <pattern
                id="grid"
                width="40"
                height="30"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M 40 0 L 0 0 0 30"
                  fill="none"
                  stroke="#f3f4f6"
                  strokeWidth="1"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />

            {/* Y-axis labels */}
            {[0.6, 0.7, 0.8, 0.9, 1.0].map((accuracy) => (
              <g key={accuracy}>
                <line
                  x1={padding.left - 5}
                  y1={yScale(accuracy)}
                  x2={padding.left}
                  y2={yScale(accuracy)}
                  stroke="#6b7280"
                  strokeWidth="1"
                />
                <text
                  x={padding.left - 10}
                  y={yScale(accuracy) + 4}
                  textAnchor="end"
                  className="text-xs fill-gray-600"
                >
                  {(accuracy * 100).toFixed(0)}%
                </text>
              </g>
            ))}

            {/* Confidence interval area */}
            {showConfidenceInterval && confidenceArea && (
              <path
                d={confidenceArea}
                fill="#295343"
                fillOpacity="0.1"
                stroke="none"
              />
            )}

            {/* Accuracy line */}
            <path
              d={accuracyPath}
              fill="none"
              stroke="#295343"
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* Data points */}
            {data.map((point, index) => (
              <g key={index}>
                <circle
                  cx={xScale(index)}
                  cy={yScale(point.accuracy)}
                  r="4"
                  fill="#295343"
                  stroke="white"
                  strokeWidth="2"
                  className="hover:r-6 cursor-pointer transition-all"
                />

                {/* Hover tooltip */}
                <g className="opacity-0 hover:opacity-100 transition-opacity">
                  <rect
                    x={xScale(index) - 40}
                    y={yScale(point.accuracy) - 40}
                    width="80"
                    height="30"
                    fill="rgba(0,0,0,0.8)"
                    rx="4"
                  />
                  <text
                    x={xScale(index)}
                    y={yScale(point.accuracy) - 20}
                    textAnchor="middle"
                    className="text-xs fill-white"
                  >
                    {(point.accuracy * 100).toFixed(1)}%
                  </text>
                </g>
              </g>
            ))}

            {/* X-axis labels */}
            {data.map((point, index) => {
              if (index % Math.ceil(data.length / 6) === 0) {
                // Show every nth label
                return (
                  <text
                    key={index}
                    x={xScale(index)}
                    y={chartHeight - padding.bottom + 20}
                    textAnchor="middle"
                    className="text-xs fill-gray-600"
                  >
                    {new Date(point.date).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                    })}
                  </text>
                );
              }
              return null;
            })}

            {/* Setup type target markers */}
            {setupType === 'DataLearning' && (
              <g>
                <line
                  x1={padding.left}
                  y1={yScale(0.9)}
                  x2={chartWidth - padding.right}
                  y2={yScale(0.9)}
                  stroke="#f59e0b"
                  strokeWidth="2"
                  strokeDasharray="5,5"
                />
                <text
                  x={chartWidth - padding.right - 5}
                  y={yScale(0.9) - 5}
                  textAnchor="end"
                  className="text-xs fill-yellow-600 font-medium"
                >
                  Target: 90%
                </text>
              </g>
            )}
          </svg>
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gradient-to-br from-brand-primary/5 to-brand-primary/10 rounded-lg p-3 border border-brand-primary/20">
            <div className="flex items-center gap-2 mb-1">
              <Target className="h-4 w-4 text-brand-primary" />
              <span className="text-xs font-medium text-gray-700">
                Avg Accuracy
              </span>
            </div>
            <div className="text-lg font-bold text-brand-primary">
              {(stats.avgAccuracy * 100).toFixed(1)}%
            </div>
          </div>

          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-3 border border-blue-200">
            <div className="flex items-center gap-2 mb-1">
              <Calendar className="h-4 w-4 text-blue-600" />
              <span className="text-xs font-medium text-gray-700">
                Total Processed
              </span>
            </div>
            <div className="text-lg font-bold text-blue-600">
              {stats.totalTransactions.toLocaleString()}
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-3 border border-green-200">
            <div className="flex items-center gap-2 mb-1">
              <Award className="h-4 w-4 text-success" />
              <span className="text-xs font-medium text-gray-700">
                Improvement
              </span>
            </div>
            <div className="text-lg font-bold text-success">
              +{(stats.improvement * 100).toFixed(1)}%
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-3 border border-purple-200">
            <div className="flex items-center gap-2 mb-1">
              <CheckCircle2 className="h-4 w-4 text-purple-600" />
              <span className="text-xs font-medium text-gray-700">
                Setup Type
              </span>
            </div>
            <div className="text-lg font-bold text-purple-600">{setupType}</div>
          </div>
        </div>

        {/* Professional Footer */}
        <div className="mt-4 pt-4 border-t border-gray-200 flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>Updated: {new Date().toLocaleString()}</span>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <div className="w-3 h-0.5 bg-brand-primary"></div>
              <span>Accuracy</span>
            </div>
            {showConfidenceInterval && (
              <div className="flex items-center gap-1">
                <div className="w-3 h-0.5 bg-brand-primary/20"></div>
                <span>Confidence Interval</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TemporalAccuracyChart;
