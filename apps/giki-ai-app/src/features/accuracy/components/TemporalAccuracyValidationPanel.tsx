/**
 * Temporal Accuracy Validation Panel
 *
 * Enhanced component for executing and displaying temporal accuracy validation results,
 * integrating with the professional UI system and accuracy dashboard.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { toast } from '../../../shared/components/ui/use-toast';
import { Button } from '../../../shared/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import { Badge } from '../../../shared/components/ui/badge';
import { Progress } from '../../../shared/components/ui/progress';
import {
  Target,
  Clock,
  CheckCircle,
  RefreshCw,
  Play,
  Award,
  TrendingUp,
  Calendar,
  Users,
  Activity,
} from 'lucide-react';

import { accuracyService } from '../services/accuracyService';

interface ReadinessCheckResult {
  ready_for_m2: boolean;
  issues: string[];
  data_validation?: {
    training_period?: {
      transactions: number;
      months_covered: number;
      period: string;
    };
    testing_period?: {
      transactions: number;
      months_covered: number;
      period: string;
    };
  };
}

interface TemporalValidationResult {
  tenant_id: number;
  milestone: string;
  validation_results: {
    monthly_results: Array<{
      month: string;
      improvement_rate: number;
      transactions_tested: number;
      confidence: number;
    }>;
  };
  milestone_status: {
    status: string;
    passed: boolean;
    improvement_rate: number;
    monthly_breakdown: Array<{
      month: string;
      improvement_rate: number;
      transactions_tested: number;
      passed_threshold: boolean;
    }>;
  };
}

interface TemporalAccuracyValidationPanelProps {
  className?: string;
  tenantId?: number;
  onValidationComplete?: (result: TemporalValidationResult) => void;
  showDetailed?: boolean;
}

export const TemporalAccuracyValidationPanel: React.FC<
  TemporalAccuracyValidationPanelProps
> = ({
  className = '',
  tenantId = 2,
  onValidationComplete,
  showDetailed = true,
}) => {
  const [validationResult, setValidationResult] =
    useState<TemporalValidationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [readinessCheck, setReadinessCheck] =
    useState<ReadinessCheckResult | null>(null);
  const [validationStatus, setValidationStatus] = useState<
    'idle' | 'checking' | 'ready' | 'running' | 'completed' | 'failed'
  >('idle');

  // Check data learning validation readiness on component mount
  const checkReadiness = useCallback(async () => {
    try {
      setValidationStatus('checking');
      const readiness = accuracyService.validateHistoricalReadiness(tenantId);
      setReadinessCheck(readiness);

      if (readiness.ready_for_m2) {
        setValidationStatus('ready');
      } else {
        setValidationStatus('failed');
        toast({
          title: 'Data Learning Validation Not Ready',
          description: `Issues found: ${readiness.issues.join(', ')}`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Failed to check data learning readiness:', error);
      setValidationStatus('failed');
      toast({
        title: 'Error',
        description: 'Failed to check data learning validation readiness.',
        variant: 'destructive',
      });
    }
  }, [tenantId]);

  useEffect(() => {
    void checkReadiness();
  }, [checkReadiness]);

  const runDataLearningValidation = async () => {
    try {
      setLoading(true);
      setValidationStatus('running');

      const result = accuracyService.runHistoricalValidation(tenantId);
      setValidationResult(result);
      setValidationStatus('completed');

      toast({
        title: 'Data Learning Validation Complete',
        description: `Overall improvement rate: ${result.milestone_status.improvement_rate}%`,
        variant: result.milestone_status.passed ? 'default' : 'destructive',
      });

      if (onValidationComplete) {
        onValidationComplete(result);
      }
    } catch (error) {
      console.error('Failed to run data learning validation:', error);
      setValidationStatus('failed');
      toast({
        title: 'Validation Failed',
        description: 'Failed to run data learning accuracy validation.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PASSED':
      case 'completed':
        return 'text-success bg-green-100';
      case 'running':
      case 'checking':
        return 'text-blue-600 bg-blue-100';
      case 'failed':
        return 'text-error bg-red-100';
      case 'ready':
        return 'text-success bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = () => {
    switch (validationStatus) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-success" />;
      case 'running':
      case 'checking':
        return <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />;
      case 'ready':
        return <Play className="w-5 h-5 text-success" />;
      case 'failed':
        return <Target className="w-5 h-5 text-error" />;
      default:
        return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div
                className="p-2 rounded-lg"
                style={{ backgroundColor: 'var(--giki-primary)', color: 'white' }}
              >
                <Award className="w-6 h-6" />
              </div>
              <div>
                <CardTitle className="text-xl">
                  Data Learning Accuracy Validation
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Learn from historical transaction patterns to improve
                  categorization accuracy
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Badge className={getStatusColor(validationStatus)}>
                {getStatusIcon()}
                <span className="ml-2 capitalize">{validationStatus}</span>
              </Badge>

              {validationStatus === 'ready' && (
                <Button
                  onClick={() => void runDataLearningValidation()}
                  disabled={loading}
                  style={{ backgroundColor: 'var(--giki-primary)', color: 'white' }}
                >
                  <Play className="w-4 h-4 mr-2" />
                  Run Validation
                </Button>
              )}

              {validationStatus !== 'checking' && (
                <Button
                  variant="outline"
                  onClick={() => void checkReadiness()}
                  disabled={loading}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* Readiness Check */}
          {readinessCheck && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                  <Calendar className="w-4 h-4 mr-2" />
                  Training Period (Jan-Jun 2024)
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Transactions:</span>
                    <span className="font-medium">
                      {readinessCheck.data_validation?.training_period
                        ?.transactions || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Months Covered:</span>
                    <span className="font-medium">
                      {readinessCheck.data_validation?.training_period
                        ?.months_covered || 0}
                      /6
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Period:</span>
                    <span className="font-medium text-xs">
                      {readinessCheck.data_validation?.training_period
                        ?.period || 'N/A'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="p-4 border rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                  <Users className="w-4 h-4 mr-2" />
                  Testing Period (Jul-Dec 2024)
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Transactions:</span>
                    <span className="font-medium">
                      {readinessCheck.data_validation?.testing_period
                        ?.transactions || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Months Covered:</span>
                    <span className="font-medium">
                      {readinessCheck.data_validation?.testing_period
                        ?.months_covered || 0}
                      /6
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Period:</span>
                    <span className="font-medium text-xs">
                      {readinessCheck.data_validation?.testing_period?.period ||
                        'N/A'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Validation Results */}
          {validationResult && showDetailed && (
            <div className="space-y-6">
              {/* Overall Status */}
              <div
                className="p-6 border rounded-lg"
                style={{
                  borderColor: validationResult.milestone_status.passed
                    ? '#295343'
                    : '#ef4444',
                }}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Validation Results
                  </h3>
                  <Badge
                    className={getStatusColor(
                      validationResult.milestone_status.status,
                    )}
                  >
                    {validationResult.milestone_status.status}
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div
                      className="text-3xl font-bold"
                      style={{ color: 'var(--giki-primary)' }}
                    >
                      {validationResult.milestone_status.improvement_rate}%
                    </div>
                    <p className="text-sm text-gray-600">
                      Overall Improvement Rate
                    </p>
                    <Progress
                      value={validationResult.milestone_status.improvement_rate}
                      className="mt-2 h-2"
                    />
                  </div>

                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {
                        validationResult.milestone_status.monthly_breakdown.filter(
                          (m) => m.passed_threshold,
                        ).length
                      }
                    </div>
                    <p className="text-sm text-gray-600">
                      Months Passed (of 6)
                    </p>
                    <Progress
                      value={
                        (validationResult.milestone_status.monthly_breakdown.filter(
                          (m) => m.passed_threshold,
                        ).length /
                          6) *
                        100
                      }
                      className="mt-2 h-2"
                    />
                  </div>

                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600">
                      {validationResult.milestone_status.monthly_breakdown.reduce(
                        (sum, m) => sum + m.transactions_tested,
                        0,
                      )}
                    </div>
                    <p className="text-sm text-gray-600">
                      Total Transactions Tested
                    </p>
                    <div className="mt-2 flex items-center justify-center">
                      <Activity className="w-4 h-4 text-gray-500 mr-1" />
                      <span className="text-xs text-gray-600">
                        Across 6 months
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Monthly Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2" />
                    Monthly Progression Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {validationResult.milestone_status.monthly_breakdown.map(
                      (month, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            <div
                              className="w-2 h-2 rounded-full"
                              style={{
                                backgroundColor: month.passed_threshold
                                  ? '#295343'
                                  : '#ef4444',
                              }}
                            ></div>
                            <div>
                              <div className="font-medium text-gray-900">
                                {month.month}
                              </div>
                              <div className="text-sm text-gray-600">
                                {month.transactions_tested} transactions
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-4">
                            <div className="text-right">
                              <div
                                className="font-bold"
                                style={{
                                  color: month.passed_threshold
                                    ? '#295343'
                                    : '#ef4444',
                                }}
                              >
                                {month.improvement_rate}%
                              </div>
                              <div className="text-xs text-gray-500">
                                improvement rate
                              </div>
                            </div>

                            <div className="w-24">
                              <Progress
                                value={month.improvement_rate}
                                className="h-2"
                              />
                            </div>

                            <Badge
                              variant="outline"
                              className={
                                month.passed_threshold
                                  ? 'text-success border-green-600'
                                  : 'text-error border-red-600'
                              }
                            >
                              {month.passed_threshold ? 'Passed' : 'Failed'}
                            </Badge>
                          </div>
                        </div>
                      ),
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TemporalAccuracyValidationPanel;
