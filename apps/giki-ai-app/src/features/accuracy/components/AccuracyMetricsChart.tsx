/**
 * Accuracy Metrics Chart Component
 *
 * Displays visual charts and analysis for accuracy test results,
 * including confidence distribution, category quality, and trends.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '../../../shared/components/ui/card';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bs<PERSON>rigger,
} from '../../../shared/components/ui/tabs';
import { Badge } from '../../../shared/components/ui/badge';
import { Progress } from '../../../shared/components/ui/progress';
import {
  BarChart3,
  PieChart,
  TrendingUp,
  Target,
  Brain,
  CheckCircle,
  XCircle,
  AlertTriangle,
} from 'lucide-react';
import { toast } from '../../../shared/components/ui/use-toast';

import { accuracyService } from '../services/accuracyService';
import type { AccuracyAnalytics } from '../types/accuracy';

interface AccuracyMetricsChartProps {
  testId: number;
}

const AccuracyMetricsChart: React.FC<AccuracyMetricsChartProps> = ({
  testId,
}) => {
  const [analytics, setAnalytics] = useState<AccuracyAnalytics | null>(null);
  const [loading, setLoading] = useState(true);

  const loadAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      const data = await accuracyService.getTestAnalytics(testId);
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to load analytics:', error);
      toast({
        title: 'Error',
        description: 'Failed to load test analytics. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [testId]);

  useEffect(() => {
    void loadAnalytics();
  }, [loadAnalytics]);

  if (loading) {
    return (
      <Card className="border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-brand-primary text-white px-6 py-4">
          <h3 className="text-base font-semibold">⊞ Test Analytics</h3>
        </div>
        <div className="p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary mx-auto mb-4"></div>
          <p className="text-brand-primary font-medium">Loading analytics...</p>
        </div>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card className="border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-brand-primary text-white px-6 py-4">
          <h3 className="text-base font-semibold">⊞ Test Analytics</h3>
        </div>
        <div className="p-6 text-center">
          <AlertTriangle className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Analytics not available
          </h3>
          <p className="text-gray-600">Unable to load test analytics data.</p>
        </div>
      </Card>
    );
  }

  const formatPercentage = (value: number | undefined): string => {
    if (value === undefined || value === null || isNaN(value)) {
      return '0.0%';
    }
    return `${value.toFixed(1)}%`;
  };

  const getConfidenceBuckets = () => {
    if (!analytics?.confidence_distribution) {
      return { high: 0, medium: 0, low: 0 };
    }

    const total =
      (analytics.confidence_distribution.high || 0) +
      (analytics.confidence_distribution.medium || 0) +
      (analytics.confidence_distribution.low || 0);

    if (total === 0) return { high: 0, medium: 0, low: 0 };

    return {
      high: ((analytics.confidence_distribution.high || 0) / total) * 100,
      medium: ((analytics.confidence_distribution.medium || 0) / total) * 100,
      low: ((analytics.confidence_distribution.low || 0) / total) * 100,
    };
  };

  const confidenceBuckets = getConfidenceBuckets();

  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="confidence">Confidence</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="errors">Error Analysis</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Overall Performance */}
            <Card className="border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-brand-primary text-white px-6 py-4">
                <div className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  <h3 className="text-base font-semibold">
                    □ Overall Performance
                  </h3>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Overall Accuracy</span>
                      <span className="font-medium">
                        {formatPercentage(analytics?.overall_accuracy)}
                      </span>
                    </div>
                    <Progress
                      value={analytics?.overall_accuracy || 0}
                      className="h-2"
                    />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Success Rate</span>
                      <span className="font-medium">
                        {formatPercentage(analytics?.success_rate)}
                      </span>
                    </div>
                    <Progress
                      value={analytics?.success_rate || 0}
                      className="h-2"
                    />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>AI Judge Accuracy</span>
                      <span className="font-medium">
                        {formatPercentage(analytics?.ai_judge_accuracy)}
                      </span>
                    </div>
                    <Progress
                      value={analytics?.ai_judge_accuracy || 0}
                      className="h-2"
                    />
                  </div>
                </div>
              </div>
            </Card>

            <Card className="border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-brand-primary text-white px-6 py-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5" />
                  <h3 className="text-base font-semibold">
                    ⊞ Quality Breakdown
                  </h3>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Non-Generic Categories</span>
                      <span className="font-medium">
                        {formatPercentage(
                          analytics?.quality_metrics?.non_generic_rate,
                        )}
                      </span>
                    </div>
                    <Progress
                      value={analytics?.quality_metrics?.non_generic_rate || 0}
                      className="h-2"
                    />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Hierarchical Structure</span>
                      <span className="font-medium">
                        {formatPercentage(
                          analytics?.quality_metrics?.hierarchical_rate,
                        )}
                      </span>
                    </div>
                    <Progress
                      value={analytics?.quality_metrics?.hierarchical_rate || 0}
                      className="h-2"
                    />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>With GL Codes</span>
                      <span className="font-medium">
                        {formatPercentage(
                          analytics?.quality_metrics?.gl_code_rate,
                        )}
                      </span>
                    </div>
                    <Progress
                      value={analytics?.quality_metrics?.gl_code_rate || 0}
                      className="h-2"
                    />
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Statistical Metrics */}
          <Card className="border border-gray-200 shadow-sm overflow-hidden">
            <div className="bg-brand-primary text-white px-6 py-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                <h3 className="text-base font-semibold">
                  ∷ Statistical Analysis
                </h3>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-brand-primary mb-2">
                    {formatPercentage((analytics?.precision || 0) * 100)}
                  </div>
                  <div className="text-sm font-medium text-gray-700 mb-1">
                    Precision
                  </div>
                  <div className="text-xs text-gray-500">
                    How many selected items are relevant
                  </div>
                  <Progress
                    value={(analytics?.precision || 0) * 100}
                    className="mt-2 h-1"
                  />
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-brand-primary mb-2">
                    {formatPercentage((analytics?.recall || 0) * 100)}
                  </div>
                  <div className="text-sm font-medium text-gray-700 mb-1">
                    Recall
                  </div>
                  <div className="text-xs text-gray-500">
                    How many relevant items are selected
                  </div>
                  <Progress
                    value={(analytics?.recall || 0) * 100}
                    className="mt-2 h-1"
                  />
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-brand-primary mb-2">
                    {formatPercentage((analytics?.f1_score || 0) * 100)}
                  </div>
                  <div className="text-sm font-medium text-gray-700 mb-1">
                    F1 Score
                  </div>
                  <div className="text-xs text-gray-500">
                    Harmonic mean of precision and recall
                  </div>
                  <Progress
                    value={(analytics?.f1_score || 0) * 100}
                    className="mt-2 h-1"
                  />
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        {/* Confidence Tab */}
        <TabsContent value="confidence" className="space-y-6">
          <Card className="border border-gray-200 shadow-sm overflow-hidden">
            <div className="bg-brand-primary text-white px-6 py-4">
              <div className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                <h3 className="text-base font-semibold">
                  ⊙ Confidence Distribution
                </h3>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {/* Confidence Chart */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-brand-primary mb-1">
                      {analytics?.confidence_distribution?.high || 0}
                    </div>
                    <div className="text-sm font-medium text-brand-primary mb-2">
                      High Confidence
                    </div>
                    <div className="text-xs text-gray-600 mb-2">80% - 100%</div>
                    <Progress value={confidenceBuckets.high} className="h-2" />
                    <div className="text-xs text-brand-primary mt-1">
                      {formatPercentage(confidenceBuckets.high)}
                    </div>
                  </div>

                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-700 mb-1">
                      {analytics?.confidence_distribution?.medium || 0}
                    </div>
                    <div className="text-sm font-medium text-yellow-600 mb-2">
                      Medium Confidence
                    </div>
                    <div className="text-xs text-yellow-500 mb-2">
                      60% - 80%
                    </div>
                    <Progress
                      value={confidenceBuckets.medium}
                      className="h-2"
                    />
                    <div className="text-xs text-yellow-500 mt-1">
                      {formatPercentage(confidenceBuckets.medium)}
                    </div>
                  </div>

                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-700 mb-1">
                      {analytics?.confidence_distribution?.low || 0}
                    </div>
                    <div className="text-sm font-medium text-error mb-2">
                      Low Confidence
                    </div>
                    <div className="text-xs text-red-500 mb-2">0% - 60%</div>
                    <Progress value={confidenceBuckets.low} className="h-2" />
                    <div className="text-xs text-red-500 mt-1">
                      {formatPercentage(confidenceBuckets.low)}
                    </div>
                  </div>
                </div>

                {/* Confidence vs Accuracy Correlation */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-700 mb-3">
                    Confidence vs Accuracy Analysis
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Average Confidence:</span>
                      <span className="font-medium ml-2">
                        {formatPercentage(
                          (analytics?.avg_confidence || 0) * 100,
                        )}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">
                        Confidence Threshold:
                      </span>
                      <span className="font-medium ml-2">
                        {analytics?.confidence_threshold
                          ? (
                              (analytics.confidence_threshold || 0) * 100
                            ).toFixed(0) + '%'
                          : 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className="mt-3 text-xs text-gray-600">
                    Higher confidence typically correlates with better accuracy.
                    Consider the confidence threshold when evaluating results.
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        {/* Categories Tab */}
        <TabsContent value="categories" className="space-y-6">
          <Card className="border border-gray-200 shadow-sm overflow-hidden">
            <div className="bg-brand-primary text-white px-6 py-4">
              <div className="flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                <h3 className="text-base font-semibold">⚬ Category Analysis</h3>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Top Categories */}
                <div>
                  <h4 className="font-medium text-gray-700 mb-4">
                    Most Frequent Categories
                  </h4>
                  <div className="space-y-3">
                    {(analytics?.category_breakdown || []).map(
                      (category, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between"
                        >
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {category.category}
                            </div>
                            <Progress
                              value={
                                (category.count /
                                  (analytics?.category_breakdown?.[0]?.count ||
                                    1)) *
                                100
                              }
                              className="mt-1 h-1"
                            />
                          </div>
                          <div className="ml-4 text-sm text-gray-600">
                            {category.count}
                          </div>
                        </div>
                      ),
                    )}
                  </div>
                </div>

                {/* Category Quality */}
                <div>
                  <h4 className="font-medium text-gray-700 mb-4">
                    Category Quality Distribution
                  </h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded">
                      <div>
                        <div className="text-sm font-medium text-blue-700">
                          Specific Categories
                        </div>
                        <div className="text-xs text-success">
                          Non-generic, meaningful categorizations
                        </div>
                      </div>
                      <Badge className="bg-blue-100 text-blue-800">
                        {Math.round(
                          analytics?.quality_metrics?.non_generic_rate || 0,
                        )}
                        %
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded">
                      <div>
                        <div className="text-sm font-medium text-purple-700">
                          Hierarchical
                        </div>
                        <div className="text-xs text-success">
                          Multi-level category structure
                        </div>
                      </div>
                      <Badge className="bg-purple-100 text-purple-800">
                        {Math.round(
                          analytics?.quality_metrics?.hierarchical_rate || 0,
                        )}
                        %
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-green-50 rounded">
                      <div>
                        <div className="text-sm font-medium text-green-700">
                          With GL Codes
                        </div>
                        <div className="text-xs text-success">
                          Includes general ledger codes
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        {Math.round(
                          analytics?.quality_metrics?.gl_code_rate || 0,
                        )}
                        %
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        {/* Error Analysis Tab */}
        <TabsContent value="errors" className="space-y-6">
          <Card className="border border-gray-200 shadow-sm overflow-hidden">
            <div className="bg-brand-primary text-white px-6 py-4">
              <div className="flex items-center gap-2">
                <XCircle className="w-5 h-5" />
                <h3 className="text-base font-semibold">↑ Error Analysis</h3>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {/* Error Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-700 mb-1">
                      {analytics?.error_analysis?.total_errors || 0}
                    </div>
                    <div className="text-sm font-medium text-error">
                      Total Errors
                    </div>
                    <div className="text-xs text-red-500 mt-1">
                      {formatPercentage(
                        ((analytics?.error_analysis?.total_errors || 0) /
                          ((analytics?.error_analysis?.total_errors || 0) +
                            (analytics?.error_analysis
                              ?.correct_categorizations || 0))) *
                          100,
                      )}
                    </div>
                  </div>

                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-brand-primary mb-1">
                      {analytics?.error_analysis?.correct_categorizations || 0}
                    </div>
                    <div className="text-sm font-medium text-brand-primary">
                      Correct
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {formatPercentage(
                        ((analytics?.error_analysis?.correct_categorizations ||
                          0) /
                          ((analytics?.error_analysis?.total_errors || 0) +
                            (analytics?.error_analysis
                              ?.correct_categorizations || 0))) *
                          100,
                      )}
                    </div>
                  </div>

                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-700 mb-1">
                      {analytics?.error_analysis?.partial_matches || 0}
                    </div>
                    <div className="text-sm font-medium text-yellow-600">
                      Partial Matches
                    </div>
                    <div className="text-xs text-yellow-500 mt-1">
                      Close but not exact matches
                    </div>
                  </div>
                </div>

                {/* Common Error Patterns */}
                <div>
                  <h4 className="font-medium text-gray-700 mb-4">
                    Common Error Patterns
                  </h4>
                  <div className="space-y-3">
                    {(
                      analytics?.error_analysis?.common_error_patterns || []
                    ).map((pattern, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="text-sm font-medium text-gray-900">
                            {pattern.pattern}
                          </div>
                          <Badge variant="outline">
                            {pattern.count} occurrences
                          </Badge>
                        </div>
                        <div className="text-xs text-gray-600">
                          {pattern.description}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Improvement Suggestions */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-700 mb-3 flex items-center gap-2">
                    <TrendingUp className="w-4 h-4" />
                    Improvement Suggestions
                  </h4>
                  <div className="space-y-2 text-sm text-blue-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>
                        Review low-confidence predictions for pattern
                        recognition
                      </li>
                      <li>
                        Consider additional training data for categories with
                        high error rates
                      </li>
                      <li>
                        Evaluate confidence thresholds based on use case
                        requirements
                      </li>
                      <li>
                        Analyze partial matches for potential category
                        refinement
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AccuracyMetricsChart;
