import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/Button';
import { Card } from '@/shared/components/ui/Card';
import { ArrowRight, Shield, CheckCircle, TrendingUp } from 'lucide-react';

export const MISSetupIntroduction: React.FC = () => {
  const navigate = useNavigate();

  const benefits = [
    {
      icon: '□',
      title: 'Complete MIS Structure',
      description:
        'Get a full Management Information System with Income/Expense hierarchy and GL codes',
    },
    {
      icon: '↗',
      title: 'AI-Powered Intelligence',
      description:
        'Smart categorization that learns and improves with your business data',
    },
    {
      icon: '⚬',
      title: 'Progressive Enhancement',
      description:
        'Start immediately, enhance accuracy as you add more business context',
    },
  ];

  const handleGetStarted = () => {
    navigate('/onboarding/company-setup');
  };

  return (
    <div className="min-h-screen bg-[#F9FAFB] flex items-center justify-center p-6">
      <div className="max-w-5xl w-full">
        <Card className="p-8 md:p-12 shadow-lg border border-[#E1E5E9] bg-white">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-brand-primary mb-6">
              Set up your intelligent MIS in minutes
            </h1>
            <p className="text-xl text-[#6B7280] max-w-3xl mx-auto leading-relaxed">
              Get a complete Management Information System with AI-powered
              categorization that adapts to your business needs
            </p>
          </div>

          {/* Benefits */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="flex flex-col items-center text-center p-8 bg-[#F8F9FA] border border-[#E1E5E9] rounded-xl hover:shadow-md transition-all duration-200 hover:border-brand-primary/30"
              >
                <div className="w-16 h-16 bg-brand-primary text-white rounded-xl flex items-center justify-center text-2xl font-bold mb-4">
                  {benefit.icon}
                </div>
                <h3 className="font-semibold text-[#1A1D29] mb-3 text-lg">
                  {benefit.title}
                </h3>
                <p className="text-[#6B7280] leading-relaxed">{benefit.description}</p>
              </div>
            ))}
          </div>

          {/* How it works */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-10">
            <h3 className="font-semibold text-gray-900 mb-3">How it works:</h3>
            <ol className="space-y-2 text-gray-700">
              <li className="flex items-start">
                <span className="font-semibold mr-2">1.</span>
                <span>Tell us about your business (2 minutes)</span>
              </li>
              <li className="flex items-start">
                <span className="font-semibold mr-2">2.</span>
                <span>We'll create your MIS structure with AI</span>
              </li>
              <li className="flex items-start">
                <span className="font-semibold mr-2">3.</span>
                <span>Optionally enhance with your existing data</span>
              </li>
              <li className="flex items-start">
                <span className="font-semibold mr-2">4.</span>
                <span>Start categorizing transactions immediately</span>
              </li>
            </ol>
          </div>

          {/* CTA */}
          <div className="text-center">
            <Button
              size="lg"
              onClick={handleGetStarted}
              className="min-w-[200px]"
            >
              Get Started
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <p className="text-sm text-gray-500 mt-4">
              No credit card required • Takes less than 5 minutes
            </p>
          </div>

          {/* Trust indicators */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                <span>Bank-level security</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                <span>SOC 2 compliant</span>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                <span>95%+ accuracy</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default MISSetupIntroduction;
