/**
 * Accuracy Validation Page - Onboarding Step 4
 * Matches wireframe: docs/wireframes/02-onboarding-journey/07-validation.md
 */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Card } from '@/shared/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { Progress } from '@/shared/components/ui/progress';
import { Badge } from '@/shared/components/ui/badge';
import { Separator } from '@/shared/components/ui/separator';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  Target,
  BarChart3,
  CheckCircle,
  XCircle,
  Lightbulb,
  Info,
  RotateCcw,
  AlertCircle,
  Trophy,
  ArrowRight,
} from 'lucide-react';
import { cn } from '@/shared/utils/utils';

interface MonthlyData {
  name: string;
  total: number;
  correct: number;
  accuracy: number;
}

interface Mismatch {
  vendor: string;
  expected: string;
  actual: string;
}

interface HighConfidenceCategory {
  name: string;
  accuracy: number;
}

const AccuracyValidationPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // Get data from previous step
  const locationState = location.state as Record<string, unknown> | null;
  const uploadId =
    typeof locationState?.uploadId === 'string' ? locationState.uploadId : '';
  const fileName =
    typeof locationState?.fileName === 'string' ? locationState.fileName : '';
  const transactionCount =
    typeof locationState?.transactionCount === 'number'
      ? locationState.transactionCount
      : 0;
  const columnMapping = locationState?.columnMapping || {};

  const [accuracy, setAccuracy] = useState(0);
  const [isRetraining, setIsRetraining] = useState(false);
  const [expandedMonth, setExpandedMonth] = useState<string | null>(null);

  const [monthlyData] = useState<MonthlyData[]>([
    { name: 'March 2025', total: 523, correct: 486, accuracy: 93 },
    { name: 'February', total: 498, correct: 447, accuracy: 90 },
    { name: 'January', total: 512, correct: 468, accuracy: 91 },
    { name: 'December', total: 687, correct: 629, accuracy: 92 },
    { name: 'November', total: 445, correct: 401, accuracy: 90 },
    { name: 'October', total: 478, correct: 435, accuracy: 91 },
  ]);

  const [mismatches] = useState<Mismatch[]>([
    { vendor: 'DELTA AIRLINES', expected: 'Travel', actual: 'Transportation' },
    {
      vendor: 'FOREIGN TRANSACTION FEE',
      expected: 'Banking Fees',
      actual: 'Miscellaneous',
    },
    { vendor: 'AMAZON BUSINESS', expected: 'Business', actual: 'Shopping' },
  ]);

  const [highConfidenceCategories] = useState<HighConfidenceCategory[]>([
    { name: 'Utilities', accuracy: 0 },
    { name: 'Subscriptions', accuracy: 0 },
    { name: 'Groceries', accuracy: 0 },
    { name: 'Restaurants', accuracy: 0 },
  ]);

  const insights = [
    'Best results on recurring transactions',
    'Travel expenses show more variety in categorization',
    'Improving over time as patterns stabilize',
  ];

  // Calculate overall accuracy
  useEffect(() => {
    const totalTransactions = monthlyData.reduce(
      (sum, month) => sum + month.total,
      0,
    );
    const totalCorrect = monthlyData.reduce(
      (sum, month) => sum + month.correct,
      0,
    );
    const overallAccuracy = Math.round(
      (totalCorrect / totalTransactions) * 100,
    );
    setAccuracy(overallAccuracy);
  }, [monthlyData]);

  const retrainWithFeedback = () => {
    setIsRetraining(true);

    // Simulate retraining process
    toast({
      title: 'Retraining with feedback',
      description: 'Learning from mismatch corrections...',
    });

    // Simulate retraining delay
    setTimeout(() => {
      setIsRetraining(false);
      toast({
        title: 'Model updated!',
        description: 'Re-running validation with improved patterns.',
        variant: 'default',
      });
    }, 3000);
  };

  const goLive = () => {
    if (accuracy < 85) {
      toast({
        title: 'Accuracy too low',
        description: 'Please retrain or review mismatches before going live.',
        variant: 'destructive',
      });
      return;
    }

    navigate('/onboarding/go-live', {
      state: {
        uploadId,
        fileName,
        transactionCount,
        columnMapping,
        accuracy,
        validated: true,
      },
    });
  };

  const getAccuracyColor = (acc: number) => {
    if (acc >= 90) return 'text-success';
    if (acc >= 85) return 'text-blue-600';
    return 'text-amber-600';
  };

  const getProgressColor = (acc: number) => {
    if (acc >= 90) return 'bg-green-600';
    if (acc >= 85) return 'bg-blue-600';
    return 'bg-amber-600';
  };

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-slate-900">giki.ai</h1>
            <nav className="hidden md:flex items-center space-x-6">
              <button
                onClick={() => navigate('/')}
                className="text-slate-600 hover:text-slate-900 transition-colors"
              >
                Dashboard
              </button>
              <span className="text-slate-900 font-medium">Upload</span>
              <button
                onClick={() => navigate('/transactions')}
                className="text-slate-600 hover:text-slate-900 transition-colors"
              >
                Transactions
              </button>
              <button
                onClick={() => navigate('/categories')}
                className="text-slate-600 hover:text-slate-900 transition-colors"
              >
                Categories
              </button>
              <button
                onClick={() => navigate('/reports')}
                className="text-slate-600 hover:text-slate-900 transition-colors"
              >
                Reports
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 md:px-6 py-8 md:py-12">
        {/* Step Indicator */}
        <div className="text-center mb-8">
          <div className="text-sm text-slate-500 mb-2">
            Step 4 of 4: Accuracy Testing
          </div>
        </div>

        {/* Perfect Score Celebration */}
        {accuracy >= 98 && (
          <div className="text-center py-8 mb-6">
            <Card className="max-w-2xl mx-auto p-8">
              <Trophy className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-slate-900">
                Outstanding Accuracy! ⚬
              </h3>
              <p className="text-slate-600">
                Your categorization patterns are incredibly consistent.
              </p>
            </Card>
          </div>
        )}

        {/* Overall Accuracy Hero */}
        <Card className="p-6 mb-6">
          <div className="flex items-center gap-2 mb-4">
            <Target className="h-6 w-6 text-success" />
            <h2 className="text-lg font-semibold text-slate-900">
              ⚬ Testing AI Accuracy on Your Historical Data
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-baseline mb-2">
                <span className="text-sm text-slate-600">
                  Overall Accuracy:
                </span>
                <span
                  className={cn(
                    'text-2xl font-bold flex items-center gap-2',
                    getAccuracyColor(accuracy),
                  )}
                >
                  {accuracy}%
                  {accuracy >= 85 && <CheckCircle className="h-5 w-5" />}
                </span>
              </div>
              <Progress
                value={accuracy}
                className="h-3"
                style={{
                  background: `linear-gradient(to right, ${getProgressColor(accuracy)} 0%, ${getProgressColor(accuracy)} ${accuracy}%, #e2e8f0 ${accuracy}%, #e2e8f0 100%)`,
                }}
              />
            </div>

            <div className="flex items-center gap-2 text-sm text-slate-600">
              <Info className="h-4 w-4" />
              <span>Target: 85%+ for production readiness</span>
            </div>
          </div>
        </Card>

        {/* Monthly Breakdown Table */}
        <Card className="p-6 mb-6">
          <div className="flex items-center gap-2 mb-4">
            <BarChart3 className="h-5 w-5 text-success" />
            <h3 className="font-semibold text-slate-900">
              □ Month-by-Month Accuracy
            </h3>
          </div>

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-slate-700">Month</TableHead>
                  <TableHead className="text-right text-slate-700">
                    Total
                  </TableHead>
                  <TableHead className="text-right text-slate-700">
                    Correct
                  </TableHead>
                  <TableHead className="text-slate-700">Accuracy</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {monthlyData.map((month) => (
                  <React.Fragment key={month.name}>
                    <TableRow
                      onClick={() =>
                        setExpandedMonth(
                          expandedMonth === month.name ? null : month.name,
                        )
                      }
                      className="cursor-pointer hover:bg-slate-50 border-slate-200"
                    >
                      <TableCell className="font-medium text-slate-900">
                        {month.name}
                      </TableCell>
                      <TableCell className="text-right text-slate-900">
                        {month.total}
                      </TableCell>
                      <TableCell className="text-right text-slate-900">
                        {month.correct}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={month.accuracy}
                            className="h-2 w-20"
                            style={{
                              background: `linear-gradient(to right, ${getProgressColor(month.accuracy)} 0%, ${getProgressColor(month.accuracy)} ${month.accuracy}%, #e2e8f0 ${month.accuracy}%, #e2e8f0 100%)`,
                            }}
                          />
                          <span
                            className={cn(
                              'text-sm font-medium w-12 text-right',
                              getAccuracyColor(month.accuracy),
                            )}
                          >
                            {month.accuracy}%
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                    {expandedMonth === month.name && (
                      <TableRow>
                        <TableCell colSpan={4} className="bg-slate-50">
                          <div className="p-4">
                            <h4 className="font-medium mb-2 text-slate-900">
                              Transaction Details for {month.name}
                            </h4>
                            <p className="text-sm text-slate-600">
                              {month.correct} out of {month.total} transactions
                              categorized correctly.
                              {month.total - month.correct > 0 &&
                                ` ${month.total - month.correct} mismatches to review.`}
                            </p>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>

          <Separator className="my-4" />

          <div>
            <h4 className="font-medium mb-3 flex items-center gap-2 text-slate-900">
              <Lightbulb className="h-4 w-4 text-amber-500" />∷ Performance
              Insights:
            </h4>
            <ul className="space-y-2 text-sm text-slate-600">
              {insights.map((insight, i) => (
                <li key={i} className="flex items-start gap-2">
                  <span className="text-success mt-0.5">•</span>
                  <span>{insight}</span>
                </li>
              ))}
            </ul>
          </div>
        </Card>

        {/* Issues and Successes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <XCircle className="h-5 w-5 text-error" />
              <h3 className="font-semibold text-slate-900">
                □ Common Mismatches
              </h3>
            </div>

            <div className="space-y-4">
              {mismatches.map((mismatch, i) => (
                <div
                  key={i}
                  className="p-3 bg-red-50 rounded-lg border border-red-100"
                >
                  <div className="font-medium text-sm mb-1 text-slate-900">
                    {mismatch.vendor}
                  </div>
                  <div className="text-xs space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="text-slate-600">Expected:</span>
                      <Badge
                        variant="outline"
                        className="text-xs border-slate-300"
                      >
                        {mismatch.expected}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-slate-600">Got:</span>
                      <Badge
                        variant="secondary"
                        className="text-xs bg-slate-100"
                      >
                        {mismatch.actual}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}

              <Button variant="outline" className="w-full" size="sm">
                Review Mismatches →
              </Button>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <CheckCircle className="h-5 w-5 text-success" />
              <h3 className="font-semibold text-slate-900">
                ↑ High Confidence Categories
              </h3>
            </div>

            <div className="space-y-3">
              {highConfidenceCategories.map((category, i) => (
                <div key={i} className="flex items-center justify-between">
                  <span className="text-sm text-slate-900">
                    {category.name}:
                  </span>
                  <div className="flex items-center gap-2">
                    <Progress value={category.accuracy} className="h-2 w-20" />
                    <span className="text-sm font-medium text-success w-12 text-right">
                      {category.accuracy}%
                    </span>
                  </div>
                </div>
              ))}

              <Button variant="ghost" className="w-full" size="sm">
                View All Categories →
              </Button>
            </div>
          </Card>
        </div>

        {/* Below Threshold Warning */}
        {accuracy < 85 && (
          <Alert className="mb-6" variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Accuracy Below Target</AlertTitle>
            <AlertDescription>
              We need 85%+ accuracy for reliable automatic categorization. You
              can:
              <ul className="mt-2 ml-4 list-disc">
                <li>Review and correct mismatches</li>
                <li>Upload more historical data</li>
                <li>Proceed with manual review mode</li>
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => void retrainWithFeedback()}
            disabled={isRetraining}
            className="border-slate-300 text-slate-700 hover:bg-slate-50"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            {isRetraining ? 'Retraining...' : 'Retrain with Feedback'}
          </Button>

          <Button
            onClick={goLive}
            disabled={accuracy < 85}
            className={cn(
              'bg-green-600 hover:bg-green-700 text-white',
              accuracy < 85 && 'opacity-50 cursor-not-allowed',
            )}
          >
            Continue to Go Live
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </main>
    </div>
  );
};

export default AccuracyValidationPage;
