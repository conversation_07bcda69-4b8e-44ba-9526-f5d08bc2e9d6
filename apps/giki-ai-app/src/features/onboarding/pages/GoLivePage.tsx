/**
 * Go Live Page - Final Onboarding Step
 * Matches wireframe: docs/wireframes/02-onboarding-journey/08-go-live.md
 */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Card } from '@/shared/components/ui/card';
import { Label } from '@/shared/components/ui/label';
import { gradientButtonStyle, applyGradientHover } from '@/shared/utils/brandGradients';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Switch } from '@/shared/components/ui/switch';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Separator } from '@/shared/components/ui/separator';
import {
  CheckCircle,
  BarChart3,
  Target,
  FileText,
  Clock,
  Lightbulb,
  Settings,
  ArrowLeft,
  Rocket,
  Trophy,
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/shared/components/ui/use-toast';
import { cn } from '@/shared/utils/utils';

interface TrainingResults {
  accuracy: number;
  patterns: number;
  transactions: number;
  setupTime: number;
}

const GoLivePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // Get data from previous steps
  const locationState = location.state as Record<string, unknown> | null;
  const uploadId =
    typeof locationState?.uploadId === 'string' ? locationState.uploadId : '';
  const fileName =
    typeof locationState?.fileName === 'string' ? locationState.fileName : '';
  const transactionCount =
    typeof locationState?.transactionCount === 'number'
      ? locationState.transactionCount
      : 0;
  const columnMapping = locationState?.columnMapping || {};
  const accuracy =
    typeof locationState?.accuracy === 'number' ? locationState.accuracy : 0;
  const validated =
    typeof locationState?.validated === 'boolean'
      ? locationState.validated
      : false;

  const [isActivating, setIsActivating] = useState(false);
  const [confidenceThreshold, setConfidenceThreshold] = useState(85);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [autoReportsEnabled, setAutoReportsEnabled] = useState(true);

  const [results] = useState<TrainingResults>({
    accuracy: accuracy || 91.3,
    patterns: 780,
    transactions: transactionCount || 4523,
    setupTime: 8,
  });

  // Celebration effect
  useEffect(() => {
    // Simple celebration effect when page loads
    if (validated) {
      toast({
        title: '⚬ Training Complete!',
        description: 'Your AI model is ready for production use.',
      });
    }
  }, [validated, toast]);

  const handleGoLive = () => {
    setIsActivating(true);

    try {
      // Simulate API call to activate production mode
      toast({
        title: '↑ Activating production mode...',
        description: 'Setting up your personalized AI system.',
      });

      // Simulate activation delay
      setTimeout(() => {
        // Navigate to dashboard with welcome state
        navigate('/', {
          state: {
            welcome: true,
            accuracy: results.accuracy,
            patterns: results.patterns,
          },
        });

        toast({
          title: "⚬ You're Live!",
          description:
            'Upload your first production file to begin categorization.',
          variant: 'default',
        });
      }, 2000);
    } catch {
      toast({
        title: 'Activation failed',
        description: 'Please try again or contact support.',
        variant: 'destructive',
      });
      setIsActivating(false);
    }
  };

  const goBack = () => {
    navigate('/onboarding/validation', {
      state: {
        uploadId,
        fileName,
        transactionCount,
        columnMapping,
      },
    });
  };

  return (
    <div className="p-8 bg-white min-h-screen">
      {/* Professional Header - Enhanced Design */}
      <div className="mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="text-5xl mb-4">⚬</div>
          <h1 className="text-3xl font-semibold text-gray-700 mb-2">
            Ready to Go Live!
          </h1>
          <p className="text-gray-500 text-base">System Ready for Production</p>
        </motion.div>
      </div>

      <div className="max-w-6xl mx-auto">
        {/* Perfect Score Celebration */}
        {results.accuracy >= 95 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-8 mb-6"
          >
            <Card className="max-w-2xl mx-auto border border-yellow-200 shadow-sm bg-yellow-50 p-8">
              <Trophy className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-yellow-800">
                Exceptional Accuracy! ⚬
              </h3>
              <p className="text-yellow-700">
                Your categorization patterns are incredibly consistent and
                reliable.
              </p>
            </Card>
          </motion.div>
        )}

        {/* Training Results Summary */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden mb-6">
          <div className="text-white px-6 py-4" style={{ background: 'linear-gradient(135deg, #295343 0%, #1A3F5F 100%)' }}>
            <div className="text-center">
              <CheckCircle className="h-8 w-8 mx-auto mb-2" />
              <h2 className="text-base font-semibold">
                ⊞ System Ready for Production
              </h2>
            </div>
          </div>
          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="font-semibold mb-4 text-slate-900">
                  Training Complete
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                      <BarChart3 className="h-5 w-5 text-success" />
                    </div>
                    <div>
                      <div className="font-semibold text-slate-900">
                        {results.accuracy}% Accuracy
                      </div>
                      <div className="text-sm text-slate-600">Achieved</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <Target className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-slate-900">
                        {results.patterns} Patterns
                      </div>
                      <div className="text-sm text-slate-600">Learned</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                      <FileText className="h-5 w-5 text-success" />
                    </div>
                    <div>
                      <div className="font-semibold text-slate-900">
                        {results.transactions.toLocaleString()} Transactions
                      </div>
                      <div className="text-sm text-slate-600">Analyzed</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-amber-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-slate-900">
                        {results.setupTime} Minutes Total
                      </div>
                      <div className="text-sm text-slate-600">Setup Time</div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-4 text-slate-900">
                  From now on, giki.ai will:
                </h3>

                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-success mt-0.5 flex-shrink-0" />
                    <span className="text-slate-700">
                      Auto-categorize all new transactions instantly
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-success mt-0.5 flex-shrink-0" />
                    <span className="text-slate-700">
                      Apply your learned patterns with high confidence
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-success mt-0.5 flex-shrink-0" />
                    <span className="text-slate-700">
                      Flag low-confidence items for optional review
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-success mt-0.5 flex-shrink-0" />
                    <span className="text-slate-700">
                      Generate reports automatically
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </Card>

        {/* What Happens Next */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden mb-6">
          <div className="text-white px-6 py-4" style={{ background: 'linear-gradient(135deg, #295343 0%, #1A3F5F 100%)' }}>
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              <h3 className="text-base font-semibold">∷ What Happens Next</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-4">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-3">
                  <span className="font-bold text-success">1</span>
                </div>
                <h4 className="font-medium mb-1 text-slate-900">
                  Upload new files
                </h4>
                <p className="text-sm text-slate-600">Instant categorization</p>
              </div>

              <div className="text-center p-4">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-3">
                  <span className="font-bold text-success">2</span>
                </div>
                <h4 className="font-medium mb-1 text-slate-900">
                  Review flagged items
                </h4>
                <p className="text-sm text-slate-600">
                  Optional, only if you want
                </p>
              </div>

              <div className="text-center p-4">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-3">
                  <span className="font-bold text-success">3</span>
                </div>
                <h4 className="font-medium mb-1 text-slate-900">
                  Export reports
                </h4>
                <p className="text-sm text-slate-600">
                  Ready when you need them
                </p>
              </div>

              <div className="text-center p-4">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-3">
                  <span className="font-bold text-success">4</span>
                </div>
                <h4 className="font-medium mb-1 text-slate-900">
                  Continuous learning
                </h4>
                <p className="text-sm text-slate-600">
                  Accuracy improves over time
                </p>
              </div>
            </div>

            <Alert className="mt-4 border-amber-200 bg-amber-50">
              <Lightbulb className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                ⚬ Pro tip: Upload files regularly for best results
              </AlertDescription>
            </Alert>
          </div>
        </Card>

        {/* Quick Settings */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden mb-8">
          <div className="text-white px-6 py-4" style={{ background: 'linear-gradient(135deg, #295343 0%, #1A3F5F 100%)' }}>
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              <h3 className="text-base font-semibold">
                □ Settings You Can Change Anytime
              </h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="confidence" className="text-slate-700">
                  Confidence threshold for auto-approval:
                </Label>
                <Select
                  value={confidenceThreshold.toString()}
                  onValueChange={(value) =>
                    setConfidenceThreshold(parseInt(value))
                  }
                >
                  <SelectTrigger className="w-[100px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="80">80%</SelectItem>
                    <SelectItem value="85">85%</SelectItem>
                    <SelectItem value="90">90%</SelectItem>
                    <SelectItem value="95">95%</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <Label htmlFor="notifications" className="text-slate-700">
                  Email notifications for processing:
                </Label>
                <Switch
                  id="notifications"
                  checked={notificationsEnabled}
                  onCheckedChange={setNotificationsEnabled}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <Label htmlFor="auto-reports" className="text-slate-700">
                  Auto-generate monthly reports:
                </Label>
                <Switch
                  id="auto-reports"
                  checked={autoReportsEnabled}
                  onCheckedChange={setAutoReportsEnabled}
                />
              </div>
            </div>

            {/* Settings Help Text */}
            <div className="mt-4 text-xs text-slate-500">
              {confidenceThreshold < 85 &&
                'Lower threshold = more automatic approvals, slightly less accuracy'}
              {confidenceThreshold > 85 &&
                'Higher threshold = more manual reviews, higher accuracy'}
            </div>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={goBack}
            className="border-brand-primary text-brand-primary hover:bg-green-50"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Testing
          </Button>

          <Button
            size="lg"
            onClick={handleGoLive}
            disabled={isActivating}
            className={cn(
              'min-w-[200px] text-white',
              isActivating && 'opacity-50',
            )}
            style={gradientButtonStyle}
            onMouseEnter={(e) => applyGradientHover(e.currentTarget, true)}
            onMouseLeave={(e) => applyGradientHover(e.currentTarget, false)}
          >
            <Rocket className="h-5 w-5 mr-2" />
            {isActivating ? 'Activating...' : '↑ Go Live'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default GoLivePage;
