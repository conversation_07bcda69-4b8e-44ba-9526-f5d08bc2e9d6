import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../auth/hooks/useAuth';
import OnboardingWizard from '../components/OnboardingWizard';

export const OnboardingWizardPage: React.FC = () => {
  const { user: _user, checkAuth } = useAuth();
  const _navigate = useNavigate();

  const handleOnboardingComplete = useCallback(
    (results: Record<string, unknown>) => {
     ('Onboarding completed:', results);

      // Store onboarding completion in localStorage for future reference
      localStorage.setItem(
        'giki_onboarding_completed',
        JSON.stringify({
          completedAt: new Date().toISOString(),
          path: typeof results.path === 'string' ? results.path : 'unknown',
          onboardingId:
            typeof results.onboardingId === 'string'
              ? results.onboardingId
              : null,
          aiResults: results.aiResults || null,
        }),
      );

      // Refresh auth state to update user context if needed
      checkAuth();

      // Navigate to dashboard - the completion handler in wizard will handle this
      // but this is a fallback
      if (typeof results.path === 'string') {
       (`↑ ${results.path} onboarding completed successfully`);
      }
    },
    [checkAuth],
  );

  return <OnboardingWizard onComplete={handleOnboardingComplete} />;
};

export default OnboardingWizardPage;
