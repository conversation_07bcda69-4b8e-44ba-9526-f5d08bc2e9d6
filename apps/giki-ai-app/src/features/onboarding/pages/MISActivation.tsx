import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/Button';
import { Card } from '@/shared/components/ui/Card';
import {
  ArrowLeft,
  Check,
  Play,
  Loader2,
  TrendingUp,
  Shield,
  Clock,
  BarChart3,
} from 'lucide-react';
import { completeMISSetup } from '../services/misSetupService';
import { useToast } from '@/shared/components/ui/use-toast';

export const MISActivation: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isActivating, setIsActivating] = useState(false);
  const [activationComplete, setActivationComplete] = useState(false);
  const [setupId, setSetupId] = useState<string | null>(null);
  const [selectedEnhancements, setSelectedEnhancements] = useState<string[]>(
    [],
  );

  useEffect(() => {
    // Load setup data from session storage
    const storedSetupId = sessionStorage.getItem('mis_setup_id');
    const storedEnhancements = sessionStorage.getItem('selected_enhancements');

    if (storedSetupId) {
      setSetupId(storedSetupId);
    }

    if (storedEnhancements) {
      setSelectedEnhancements(JSON.parse(storedEnhancements));
    }
  }, []);

  const handleActivation = async () => {
    if (!setupId) {
      toast({
        title: 'Setup Error',
        description:
          'Setup ID not found. Please start the setup process again.',
        variant: 'destructive',
      });
      return;
    }

    setIsActivating(true);

    try {
      // Complete MIS setup
      await completeMISSetup(setupId);

      setActivationComplete(true);

      toast({
        title: 'MIS Activated Successfully',
        description: 'Your Management Information System is now ready to use.',
      });

      // Clear session storage
      sessionStorage.removeItem('mis_setup_id');
      sessionStorage.removeItem('company_info');
      sessionStorage.removeItem('detected_enhancements');
      sessionStorage.removeItem('selected_enhancements');
      sessionStorage.removeItem('skipped_data_upload');
    } catch (error) {
      console.error('MIS activation error:', error);
      toast({
        title: 'Activation Error',
        description: 'Failed to activate MIS. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsActivating(false);
    }
  };

  const handleGoToDashboard = () => {
    navigate('/');
  };

  const features = [
    {
      icon: <BarChart3 className="h-6 w-6 text-blue-600" />,
      title: 'Intelligent Categorization',
      description: 'AI-powered transaction categorization out of the box',
    },
    {
      icon: <TrendingUp className="h-6 w-6 text-success" />,
      title: 'Progressive Enhancement',
      description: `${selectedEnhancements.length > 0 ? 'Enhanced with your data for' : 'Ready for'} improved accuracy over time`,
    },
    {
      icon: <Shield className="h-6 w-6 text-purple-600" />,
      title: 'Compliance Ready',
      description: 'Built-in GL code mapping and audit trail capabilities',
    },
    {
      icon: <Clock className="h-6 w-6 text-orange-600" />,
      title: 'Real-time Processing',
      description: 'Instant categorization and report generation',
    },
  ];

  if (activationComplete) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <Card className="p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Check className="h-8 w-8 text-success" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                MIS Activated Successfully!
              </h2>
              <p className="text-gray-600">
                Your Management Information System is now ready to process
                transactions and generate reports.
              </p>
            </div>

            <div className="space-y-4 mb-8">
              <div className="text-left bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">
                  What's Next?
                </h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                    <span>
                      Start uploading transaction files for automatic
                      categorization
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                    <span>Review and approve AI-categorized transactions</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                    <span>Generate financial reports and insights</span>
                  </li>
                  {selectedEnhancements.length > 0 && (
                    <li className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                      <span>
                        Your {selectedEnhancements.length} enhancement
                        {selectedEnhancements.length === 1
                          ? ' is'
                          : 's are'}{' '}
                        active
                      </span>
                    </li>
                  )}
                </ul>
              </div>
            </div>

            <Button onClick={handleGoToDashboard} size="lg" className="w-full">
              Go to Dashboard
            </Button>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-3xl mx-auto">
        <Card className="p-8">
          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">Step 4 of 4</span>
              <span className="text-sm text-gray-600">MIS Activation</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full"
                style={{ width: '100%' }}
              />
            </div>
          </div>

          {/* Header */}
          <div className="mb-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Ready to Activate Your MIS
            </h2>
            <p className="text-gray-600">
              Your Management Information System is configured and ready to go
              live. Click activate to start processing transactions.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg"
              >
                <div className="flex-shrink-0">{feature.icon}</div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Enhancement Summary */}
          {selectedEnhancements.length > 0 && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">
                Active Enhancements
              </h3>
              <p className="text-sm text-gray-600">
                {selectedEnhancements.length} enhancement
                {selectedEnhancements.length === 1 ? '' : 's'} will be applied:{' '}
                {selectedEnhancements.join(', ')}
              </p>
            </div>
          )}

          {/* Activation Button */}
          <div className="text-center mb-6">
            <Button
              onClick={handleActivation}
              disabled={isActivating}
              size="lg"
              className="w-full sm:w-auto px-12"
            >
              {isActivating ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Activating MIS...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-5 w-5" />
                  Activate MIS
                </>
              )}
            </Button>
          </div>

          {/* Back Button */}
          <div className="flex justify-center">
            <Button
              type="button"
              variant="ghost"
              onClick={() => {
                if (selectedEnhancements.length > 0) {
                  navigate('/onboarding/enhancement-review');
                } else {
                  navigate('/onboarding/data-upload');
                }
              }}
              disabled={isActivating}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default MISActivation;
