/**
 * AI Training Progress Page - Onboarding Step 3
 * Matches wireframe: docs/wireframes/02-onboarding-journey/06-training.md
 */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Card } from '@/shared/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { Progress } from '@/shared/components/ui/progress';
import { Badge } from '@/shared/components/ui/badge';
import { Separator } from '@/shared/components/ui/separator';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  Brain,
  BarChart3,
  Target,
  Search,
  ArrowRight,
  Lightbulb,
  CheckCircle,
  Info,
} from 'lucide-react';
import { motion } from 'framer-motion';

interface CategoryProgress {
  name: string;
  icon: string;
  patterns: number;
  confidence: number;
}

interface LearnedPattern {
  vendor: string;
  category: string;
  confidence: number;
}

interface TrainingStats {
  totalTransactions: number;
  uniqueVendors: number;
  categoriesFound: number;
  patternsLearned: number;
}

interface Insight {
  text: string;
}

const AITrainingPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get data from previous step
  const locationState = location.state as Record<string, unknown> | null;
  const uploadId =
    typeof locationState?.uploadId === 'string' ? locationState.uploadId : '';
  const fileName =
    typeof locationState?.fileName === 'string' ? locationState.fileName : '';
  const transactionCount =
    typeof locationState?.transactionCount === 'number'
      ? locationState.transactionCount
      : 0;
  const columnMapping = locationState?.columnMapping || {};

  const [progress, setProgress] = useState(0);
  const [currentCategory, setCurrentCategory] = useState('Shopping');
  const [estimatedTime, setEstimatedTime] = useState(2);
  const [isComplete, setIsComplete] = useState(false);

  const [categories] = useState<CategoryProgress[]>([
    { name: 'Shopping', icon: '□', patterns: 234, confidence: 85 },
    { name: 'Food & Dining', icon: '⊞', patterns: 189, confidence: 92 },
    { name: 'Transportation', icon: '↑', patterns: 156, confidence: 94 },
    { name: 'Travel', icon: '∷', patterns: 67, confidence: 72 },
    { name: 'Utilities', icon: '⚬', patterns: 45, confidence: 98 },
    { name: 'Business', icon: '□', patterns: 89, confidence: 81 },
  ]);

  const [learnedPatterns, setLearnedPatterns] = useState<LearnedPattern[]>([
    { vendor: 'AMZN', category: 'Shopping', confidence: 89 },
    { vendor: 'UBER', category: 'Transportation', confidence: 98 },
    { vendor: 'NETFLIX', category: 'Entertainment', confidence: 100 },
    { vendor: 'WHOLE FOODS', category: 'Food & Dining', confidence: 95 },
  ]);

  const [stats] = useState<TrainingStats>({
    totalTransactions: transactionCount || 4523,
    uniqueVendors: 342,
    categoriesFound: 23,
    patternsLearned: 780,
  });

  const [insights] = useState<Insight[]>([
    { text: 'You categorize Uber as "Transportation" not "Travel"' },
    { text: 'Amazon purchases split between "Shopping" and "Business"' },
    { text: 'Recurring subscriptions properly identified' },
  ]);

  // Simulate training progress
  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = Math.min(prev + Math.random() * 5, 100);

        // Update current learning category
        if (newProgress > 25 && newProgress < 50) {
          setCurrentCategory('Food & Dining');
        } else if (newProgress > 50 && newProgress < 75) {
          setCurrentCategory('Travel & Entertainment');
        } else if (newProgress > 75) {
          setCurrentCategory('Business Expenses');
        }

        // Update estimated time
        const remaining = 100 - newProgress;
        setEstimatedTime(Math.max(Math.ceil(remaining / 50), 0));

        // Mark complete
        if (newProgress >= 100) {
          setIsComplete(true);
          clearInterval(interval);
        }

        return newProgress;
      });

      // Occasionally add new patterns
      if (Math.random() > 0.8) {
        addNewPattern();
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const addNewPattern = () => {
    const newPatterns = [
      { vendor: 'STARBUCKS', category: 'Food & Dining', confidence: 96 },
      { vendor: 'TARGET', category: 'Shopping', confidence: 88 },
      { vendor: 'LYFT', category: 'Transportation', confidence: 94 },
      { vendor: 'SPOTIFY', category: 'Entertainment', confidence: 99 },
    ];

    const randomPattern =
      newPatterns[Math.floor(Math.random() * newPatterns.length)];
    setLearnedPatterns((prev) => [randomPattern, ...prev.slice(0, 4)]);
  };

  const goToValidation = () => {
    navigate('/onboarding/validation', {
      state: {
        uploadId,
        fileName,
        transactionCount,
        columnMapping,
        trainingComplete: true,
      },
    });
  };

  return (
    <div className="p-8 bg-white min-h-screen">
      {/* Professional Header - Enhanced Design */}
      <div className="mb-8">
        <div className="text-center mb-8">
          <div className="text-sm text-gray-500 mb-2">
            Step 3 of 4: AI Training
          </div>
          <h1 className="text-3xl font-semibold text-gray-700 mb-2">
            AI Training Progress
          </h1>
          <p className="text-gray-500 text-base">
            Learning your categorization patterns
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto">
        {!isComplete ? (
          <>
            {/* Main Progress Card */}
            <Card className="border border-gray-200 shadow-sm overflow-hidden mb-6">
              <div className="bg-brand-primary text-white px-6 py-4">
                <div className="flex items-center gap-3">
                  <div className="animate-pulse">
                    <Brain className="h-6 w-6" />
                  </div>
                  <h2 className="text-base font-semibold">
                    ⚬ Training AI on Your Patterns
                  </h2>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <Progress value={progress} className="h-3" />

                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">
                      Currently learning: {currentCategory} patterns
                    </span>
                    <span className="font-medium text-slate-900">
                      {Math.round(progress)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            {/* Category Learning Grid */}
            <Card className="border border-gray-200 shadow-sm overflow-hidden mb-6">
              <div className="bg-brand-primary text-white px-6 py-4">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  <h3 className="text-base font-semibold">
                    ∷ Learning Progress
                  </h3>
                </div>
              </div>
              <div className="p-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-slate-700">
                        Category Group
                      </TableHead>
                      <TableHead className="text-slate-700">
                        Patterns Found
                      </TableHead>
                      <TableHead className="text-slate-700">
                        Confidence
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categories.map((category) => (
                      <TableRow
                        key={category.name}
                        className="border-slate-200"
                      >
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{category.icon}</span>
                            <span className="font-medium text-slate-900">
                              {category.name}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-slate-600">
                          {category.patterns} patterns
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Progress
                              value={category.confidence}
                              className="h-2 w-20"
                            />
                            <span className="text-sm font-medium text-slate-900">
                              {category.confidence}%
                            </span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                <Separator className="my-4" />

                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2 text-slate-900">
                    <Lightbulb className="h-4 w-4 text-amber-500" />⚬ Insights
                    Discovered:
                  </h4>
                  <ul className="space-y-2 text-sm text-slate-600">
                    {insights.map((insight, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <span className="text-success mt-0.5">•</span>
                        <span>{insight.text}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </Card>

            {/* Stats and Patterns Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-brand-primary text-white px-6 py-4">
                  <div className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    <h3 className="text-base font-semibold">
                      ⚬ Training Stats
                    </h3>
                  </div>
                </div>
                <div className="p-6">
                  <dl className="space-y-3">
                    <div className="flex justify-between">
                      <dt className="text-slate-600">Total Transactions:</dt>
                      <dd className="font-medium text-slate-900">
                        {stats.totalTransactions.toLocaleString()}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-slate-600">Unique Vendors:</dt>
                      <dd className="font-medium text-slate-900">
                        {stats.uniqueVendors}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-slate-600">Categories Found:</dt>
                      <dd className="font-medium text-slate-900">
                        {stats.categoriesFound}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-slate-600">Patterns Learned:</dt>
                      <dd className="font-medium text-success">
                        {stats.patternsLearned}
                      </dd>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between">
                      <dt className="text-slate-600">Est. Time:</dt>
                      <dd className="font-medium text-slate-900">
                        ~{estimatedTime} minutes
                      </dd>
                    </div>
                  </dl>
                </div>
              </Card>

              <Card className="border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-brand-primary text-white px-6 py-4">
                  <div className="flex items-center gap-2">
                    <Search className="h-5 w-5" />
                    <h3 className="text-base font-semibold">
                      ⚬ Sample Learned Patterns
                    </h3>
                  </div>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    {learnedPatterns.map((pattern, i) => (
                      <motion.div
                        key={`${pattern.vendor}-${i}`}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center justify-between text-sm"
                      >
                        <span className="font-mono text-slate-600">
                          {pattern.vendor}
                        </span>
                        <span className="flex items-center gap-2">
                          <ArrowRight className="h-3 w-3 text-slate-400" />
                          <Badge
                            variant="secondary"
                            className="bg-green-100 text-green-700"
                          >
                            {pattern.category}
                          </Badge>
                          <span className="text-xs text-slate-500">
                            ({pattern.confidence}%)
                          </span>
                        </span>
                      </motion.div>
                    ))}
                    <div className="text-sm text-slate-500 animate-pulse">
                      More patterns being discovered...
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Slow Training Info */}
            {progress > 0 && progress < 30 && (
              <Alert className="mt-6 border-blue-200 bg-blue-50">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  Training is taking a bit longer due to the variety in your
                  transactions. This actually means we&rsquo;re learning more
                  nuanced patterns!
                </AlertDescription>
              </Alert>
            )}
          </>
        ) : (
          /* Completion State */
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12"
          >
            <Card className="max-w-2xl mx-auto border border-gray-200 shadow-sm p-8">
              <CheckCircle className="h-16 w-16 text-success mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2 text-slate-900">
                Training Complete! ⚬
              </h2>
              <p className="text-slate-600 mb-6">
                Learned {stats.patternsLearned} patterns from your data
              </p>
              <Button
                onClick={goToValidation}
                className="bg-brand-primary hover:bg-brand-primary-hover text-white"
              >
                Test Accuracy
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AITrainingPage;
