import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/shared/components/ui/Button';
import { Card } from '@/shared/components/ui/Card';
import {
  ArrowLeft,
  ArrowRight,
  Upload,
  FileSpreadsheet,
  AlertCircle,
  Check,
  X,
} from 'lucide-react';
import { useToast } from '@/shared/components/ui/use-toast';
import { detectEnhancements } from '../services/misSetupService';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  status: 'uploading' | 'analyzing' | 'complete' | 'error';
  enhancements?: Enhancement[];
}

interface Enhancement {
  type: 'historical' | 'schema' | 'vendor';
  confidence: number;
  recordCount?: number;
  description: string;
}

export const MISDataUpload: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [detectedEnhancements, setDetectedEnhancements] = useState<
    Enhancement[]
  >([]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      // Create file entries with uploading status
      const newFiles: UploadedFile[] = acceptedFiles.map((file) => ({
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        status: 'uploading' as const,
      }));

      setFiles((prev) => [...prev, ...newFiles]);

      // Upload and analyze files
      for (let i = 0; i < acceptedFiles.length; i++) {
        const file = acceptedFiles[i];
        const fileEntry = newFiles[i];

        try {
          // Skip upload for MIS setup - go directly to enhancement detection
          // Update status to analyzing
          setFiles((prev) =>
            prev.map((f) =>
              f.id === fileEntry.id ? { ...f, status: 'analyzing' } : f,
            ),
          );

          // Analyze for enhancements
          const enhancements = await detectEnhancements(file);

          // Update with results
          setFiles((prev) =>
            prev.map((f) =>
              f.id === fileEntry.id
                ? {
                    ...f,
                    status: 'complete',
                    enhancements,
                  }
                : f,
            ),
          );

          // Aggregate detected enhancements
          if (enhancements.length > 0) {
            setDetectedEnhancements((prev) => [...prev, ...enhancements]);
          }
        } catch (error) {
          setFiles((prev) =>
            prev.map((f) =>
              f.id === fileEntry.id ? { ...f, status: 'error' } : f,
            ),
          );
          toast({
            title: 'Upload Error',
            description: `Failed to process ${file.name}`,
            variant: 'destructive',
          });
        }
      }
    },
    [toast],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
      'text/csv': ['.csv'],
    },
    multiple: true,
  });

  const handleSkip = () => {
    // Store that user skipped data upload
    sessionStorage.setItem('skipped_data_upload', 'true');
    navigate('/onboarding/mis-activation');
  };

  const handleContinue = () => {
    if (detectedEnhancements.length > 0) {
      // Store enhancements for next step
      sessionStorage.setItem(
        'detected_enhancements',
        JSON.stringify(detectedEnhancements),
      );
      navigate('/onboarding/enhancement-review');
    } else {
      navigate('/onboarding/mis-activation');
    }
  };

  const removeFile = (fileId: string) => {
    setFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const hasHistoricalData = detectedEnhancements.some(
    (e) => e.type === 'historical',
  );
  const hasSchemaData = detectedEnhancements.some((e) => e.type === 'schema');

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-3xl w-full">
        <Card className="p-8">
          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">Step 2 of 4</span>
              <span className="text-sm text-gray-600">
                Data Upload (Optional)
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full"
                style={{ width: '50%' }}
              />
            </div>
          </div>

          {/* Header */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Enhance with your business data (optional)
            </h2>
            <p className="text-gray-600">
              Upload any financial data to improve categorization accuracy.
              We'll automatically detect how to use your data.
            </p>
          </div>

          {/* Upload area */}
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-700 font-medium mb-2">
              Drop files here or click to browse
            </p>
            <p className="text-sm text-gray-500">
              Supports Excel (.xlsx, .xls) and CSV files
            </p>
          </div>

          {/* Uploaded files list */}
          {files.length > 0 && (
            <div className="mt-6 space-y-3">
              <h3 className="font-semibold text-gray-900">Uploaded Files</h3>
              {files.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <FileSpreadsheet className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900">{file.name}</p>
                      <p className="text-sm text-gray-500">
                        {(file.size / 1024).toFixed(1)} KB
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {file.status === 'uploading' && (
                      <span className="text-sm text-gray-500">
                        Uploading...
                      </span>
                    )}
                    {file.status === 'analyzing' && (
                      <span className="text-sm text-blue-600">
                        Analyzing...
                      </span>
                    )}
                    {file.status === 'complete' && (
                      <Check className="h-5 w-5 text-success" />
                    )}
                    {file.status === 'error' && (
                      <AlertCircle className="h-5 w-5 text-error" />
                    )}
                    <button
                      onClick={() => removeFile(file.id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Detected enhancements */}
          {detectedEnhancements.length > 0 && (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">
                Enhancement Opportunities Detected
              </h3>
              <ul className="space-y-2 text-sm">
                {hasHistoricalData && (
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 text-success mt-0.5" />
                    <span>
                      Historical transaction patterns found - can improve
                      accuracy by 15-20%
                    </span>
                  </li>
                )}
                {hasSchemaData && (
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 text-success mt-0.5" />
                    <span>
                      GL code structure detected - can ensure compliance with
                      your accounting system
                    </span>
                  </li>
                )}
              </ul>
            </div>
          )}

          {/* Info box */}
          <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex gap-3">
              <AlertCircle className="h-5 w-5 text-gray-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-gray-600">
                <p className="font-medium mb-1">What can you upload?</p>
                <ul className="space-y-1">
                  <li>
                    • Transaction history with categories (improves accuracy)
                  </li>
                  <li>
                    • Chart of accounts or GL structure (ensures compliance)
                  </li>
                  <li>• Vendor lists (enhances vendor recognition)</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between mt-8">
            <Button
              type="button"
              variant="ghost"
              onClick={() => navigate('/onboarding/company-setup')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>

            <div className="flex gap-3">
              <Button type="button" variant="outline" onClick={handleSkip}>
                Skip for now
              </Button>

              <Button
                onClick={handleContinue}
                disabled={
                  isAnalyzing ||
                  files.some(
                    (f) => f.status === 'uploading' || f.status === 'analyzing',
                  )
                }
              >
                Continue
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default MISDataUpload;
