import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/Button';
import { Card } from '@/shared/components/ui/Card';
import {
  ArrowLeft,
  ArrowRight,
  Check,
  AlertCircle,
  TrendingUp,
  FileSpreadsheet,
  Users,
} from 'lucide-react';

interface Enhancement {
  type: 'historical' | 'schema' | 'vendor';
  title: string;
  description: string;
  accuracy_gain: string;
  time_estimate: string;
  priority: 'recommended' | 'highly recommended' | 'optional';
  confidence: number;
  record_count?: number;
}

export const MISEnhancementReview: React.FC = () => {
  const navigate = useNavigate();
  const [enhancements, setEnhancements] = useState<Enhancement[]>([]);
  const [selectedEnhancements, setSelectedEnhancements] = useState<string[]>(
    [],
  );

  useEffect(() => {
    // Load detected enhancements from session storage
    const storedEnhancements = sessionStorage.getItem('detected_enhancements');
    if (storedEnhancements) {
      setEnhancements(JSON.parse(storedEnhancements));
    }
  }, []);

  const toggleEnhancement = (type: string) => {
    setSelectedEnhancements((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type],
    );
  };

  const getEnhancementIcon = (type: string) => {
    switch (type) {
      case 'historical':
        return <TrendingUp className="h-6 w-6 text-blue-600" />;
      case 'schema':
        return <FileSpreadsheet className="h-6 w-6 text-success" />;
      case 'vendor':
        return <Users className="h-6 w-6 text-purple-600" />;
      default:
        return <AlertCircle className="h-6 w-6 text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'highly recommended':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'recommended':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'optional':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleContinue = () => {
    // Store selected enhancements for activation
    sessionStorage.setItem(
      'selected_enhancements',
      JSON.stringify(selectedEnhancements),
    );
    navigate('/onboarding/mis-activation');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        <Card className="p-8">
          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">Step 3 of 4</span>
              <span className="text-sm text-gray-600">Enhancement Review</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full"
                style={{ width: '75%' }}
              />
            </div>
          </div>

          {/* Header */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Enhancement Opportunities Detected
            </h2>
            <p className="text-gray-600">
              We found ways to improve your MIS accuracy and compliance. Select
              the enhancements you'd like to apply.
            </p>
          </div>

          {/* Enhancement Cards */}
          <div className="space-y-4 mb-8">
            {enhancements.map((enhancement) => (
              <div
                key={enhancement.type}
                className={`border-2 rounded-lg p-6 cursor-pointer transition-all ${
                  selectedEnhancements.includes(enhancement.type)
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => toggleEnhancement(enhancement.type)}
              >
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    {getEnhancementIcon(enhancement.type)}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {enhancement.title}
                        </h3>
                        <p className="text-gray-600 mb-3">
                          {enhancement.description}
                        </p>

                        <div className="flex items-center gap-4 text-sm">
                          <span className="flex items-center gap-1">
                            <TrendingUp className="h-4 w-4 text-success" />
                            <span className="font-medium text-success">
                              {enhancement.accuracy_gain} accuracy gain
                            </span>
                          </span>

                          <span className="text-gray-500">
                            {enhancement.time_estimate} setup time
                          </span>

                          {enhancement.record_count && (
                            <span className="text-gray-500">
                              {enhancement.record_count.toLocaleString()}{' '}
                              records
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col items-end gap-2">
                        <span
                          className={`px-2 py-1 text-xs font-medium border rounded-md ${getPriorityColor(enhancement.priority)}`}
                        >
                          {enhancement.priority}
                        </span>

                        <div className="flex items-center gap-1">
                          <span className="text-xs text-gray-500">
                            {Math.round(enhancement.confidence * 100)}%
                            confidence
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex-shrink-0">
                    <div
                      className={`w-6 h-6 rounded border-2 flex items-center justify-center ${
                        selectedEnhancements.includes(enhancement.type)
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`}
                    >
                      {selectedEnhancements.includes(enhancement.type) && (
                        <Check className="h-4 w-4 text-white" />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Summary */}
          {selectedEnhancements.length > 0 && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">
                Selected Enhancements Summary
              </h3>
              <p className="text-sm text-gray-600">
                You've selected {selectedEnhancements.length} enhancement
                {selectedEnhancements.length === 1 ? '' : 's'}. These will be
                applied to your MIS setup and can improve your overall
                categorization accuracy.
              </p>
            </div>
          )}

          {/* Info box */}
          <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex gap-3">
              <AlertCircle className="h-5 w-5 text-gray-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-gray-600">
                <p className="font-medium mb-1">About Enhancements</p>
                <p>
                  Enhancements are optional improvements that use your uploaded
                  data to make categorization more accurate and compliant with
                  your specific business needs. You can always add or modify
                  these later in your settings.
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between">
            <Button
              type="button"
              variant="ghost"
              onClick={() => navigate('/onboarding/data-upload')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>

            <Button onClick={handleContinue}>
              Continue
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default MISEnhancementReview;
