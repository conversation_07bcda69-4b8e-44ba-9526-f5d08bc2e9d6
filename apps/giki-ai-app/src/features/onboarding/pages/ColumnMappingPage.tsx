/**
 * Column Mapping Page - Onboarding Step 2
 * Matches wireframe: docs/wireframes/02-onboarding-journey/05-column-mapping.md
 */
import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Card } from '@/shared/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  CheckCircle,
  Target,
  Star,
  Sparkles,
  ArrowLeft,
  ArrowRight,
  Lightbulb,
  Info,
  AlertCircle,
} from 'lucide-react';
import { cn } from '@/shared/utils/utils';

interface DetectedColumn {
  name: string;
  mapping: string | null;
  samples: string[];
  confidence?: number;
}

interface ColumnMappingPageProps {
  uploadId?: string;
  fileName?: string;
  transactionCount?: number;
}

const ColumnMappingPage: React.FC<ColumnMappingPageProps> = ({
  uploadId: propUploadId,
  fileName: propFileName,
  transactionCount: propTransactionCount,
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();

  // Get data from props or URL params
  const uploadId = propUploadId || searchParams.get('uploadId');
  const fileName =
    propFileName || searchParams.get('fileName') || 'uploaded_file.xlsx';
  const transactionCount =
    propTransactionCount ||
    parseInt(searchParams.get('transactionCount') || '0');

  const [detectedColumns, setDetectedColumns] = useState<DetectedColumn[]>([]);
  const [hasCategory, setHasCategory] = useState(false);

  // Mock detected columns for demonstration
  useEffect(() => {
    // Simulate loading detected columns
    const mockColumns: DetectedColumn[] = [
      {
        name: 'Transaction Date',
        mapping: 'date',
        samples: ['03/15/2025', '03/14/2025', '03/13/2025'],
        confidence: 95,
      },
      {
        name: 'Description',
        mapping: 'description',
        samples: ['AMZN Marketplace', 'Uber Trip', 'Starbucks Coffee'],
        confidence: 98,
      },
      {
        name: 'Debit',
        mapping: 'amount',
        samples: ['$45.32', '$18.50', '$5.75'],
        confidence: 90,
      },
      {
        name: 'Category',
        mapping: 'category',
        samples: ['Shopping', 'Transportation', 'Food & Dining'],
        confidence: 85,
      },
      {
        name: 'Credit',
        mapping: 'skip',
        samples: ['$1,200.00', '-', '$500.00'],
        confidence: 60,
      },
    ];

    setDetectedColumns(mockColumns);
    setHasCategory(mockColumns.some((col) => col.mapping === 'category'));
  }, []);

  const updateMapping = (columnName: string, newMapping: string) => {
    setDetectedColumns((prev) =>
      prev.map((col) =>
        col.name === columnName ? { ...col, mapping: newMapping } : col,
      ),
    );

    // Update category detection
    const hasCategoryMapping = detectedColumns.some(
      (col) => col.mapping === 'category' || newMapping === 'category',
    );
    setHasCategory(hasCategoryMapping);

    // Show helper if category is selected
    if (newMapping === 'category') {
      toast({
        title: 'Great choice!',
        description: "We'll learn from these categories to improve accuracy.",
        variant: 'default',
      });
    }
  };

  const autoDetect = () => {
    // Simulate auto-detection improvement
    setDetectedColumns((prev) =>
      prev.map((col) => {
        // Improve confidence for obvious mappings
        if (col.name.toLowerCase().includes('date')) {
          return { ...col, mapping: 'date', confidence: 98 };
        }
        if (col.name.toLowerCase().includes('desc')) {
          return { ...col, mapping: 'description', confidence: 95 };
        }
        if (
          col.name.toLowerCase().includes('amount') ||
          col.name.toLowerCase().includes('debit')
        ) {
          return { ...col, mapping: 'amount', confidence: 92 };
        }
        return col;
      }),
    );

    toast({
      title: 'Columns auto-detected!',
      description: 'Please verify the mapping is correct.',
      variant: 'default',
    });
  };

  const hasRequiredMappings = () => {
    const mappings = detectedColumns.map((col) => col.mapping);
    return (
      mappings.includes('date') &&
      mappings.includes('description') &&
      (mappings.includes('amount') ||
        mappings.includes('debit') ||
        mappings.includes('credit'))
    );
  };

  const confirmMapping = () => {
    if (!hasRequiredMappings()) {
      toast({
        title: 'Missing required mappings',
        description:
          'Please map Date, Description, and at least one Amount field.',
        variant: 'destructive',
      });
      return;
    }

    // Navigate to next step
    navigate('/onboarding/training', {
      state: {
        uploadId,
        fileName,
        transactionCount,
        columnMapping: detectedColumns.reduce(
          (acc, col) => {
            if (col.mapping && col.mapping !== 'skip') {
              acc[col.mapping] = col.name;
            }
            return acc;
          },
          {} as Record<string, string>,
        ),
      },
    });
  };

  const getMappingOptions = () => [
    { value: 'date', label: 'Date' },
    { value: 'description', label: 'Description' },
    { value: 'amount', label: 'Amount' },
    { value: 'category', label: 'Category' },
    { value: 'vendor', label: 'Vendor' },
    { value: 'account', label: 'Account' },
    { value: 'reference', label: 'Reference' },
    { value: 'skip', label: 'Skip this column' },
  ];

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-slate-900">giki.ai</h1>
            <nav className="hidden md:flex items-center space-x-6">
              <button
                onClick={() => navigate('/')}
                className="text-slate-600 hover:text-slate-900 transition-colors"
              >
                Dashboard
              </button>
              <span className="text-slate-900 font-medium">Upload</span>
              <button
                onClick={() => navigate('/transactions')}
                className="text-slate-600 hover:text-slate-900 transition-colors"
              >
                Transactions
              </button>
              <button
                onClick={() => navigate('/categories')}
                className="text-slate-600 hover:text-slate-900 transition-colors"
              >
                Categories
              </button>
              <button
                onClick={() => navigate('/reports')}
                className="text-slate-600 hover:text-slate-900 transition-colors"
              >
                Reports
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 md:px-6 py-8 md:py-12">
        {/* Breadcrumb */}
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate('/onboarding/upload')}
            className="text-slate-600 hover:text-slate-900"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Upload
          </Button>
          <div className="text-sm text-slate-500">
            Step 2 of 4: Column Mapping
          </div>
        </div>

        {/* Success Banner */}
        <Alert className="mb-6 border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-success" />
          <AlertTitle className="text-green-800">
            File Uploaded Successfully
          </AlertTitle>
          <AlertDescription className="text-green-700">
            Found {transactionCount.toLocaleString()} transactions in &ldquo;
            {fileName}&rdquo;
          </AlertDescription>
        </Alert>

        {/* Column Mapping Card */}
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Target className="h-5 w-5 text-success" />
            <h2 className="text-lg font-semibold text-slate-900">
              Confirm Column Mapping
            </h2>
          </div>

          <p className="text-slate-600 mb-6">
            We&rsquo;ve detected your columns. Please confirm they&rsquo;re
            correct:
          </p>

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-slate-700 font-medium">
                    Your Column Name
                  </TableHead>
                  <TableHead className="text-slate-700 font-medium">
                    Maps To
                  </TableHead>
                  <TableHead className="text-slate-700 font-medium">
                    Sample Data
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {detectedColumns.map((column) => (
                  <TableRow key={column.name} className="border-slate-200">
                    <TableCell className="font-medium text-slate-900">
                      {column.name}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Select
                          value={column.mapping || 'skip'}
                          onValueChange={(value) =>
                            updateMapping(column.name, value)
                          }
                        >
                          <SelectTrigger
                            className={cn(
                              'w-[180px] border-slate-300 focus:border-green-600',
                              column.mapping === 'category' &&
                                'border-amber-400 bg-amber-50',
                            )}
                          >
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {getMappingOptions().map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                <div className="flex items-center gap-2">
                                  {option.label}
                                  {option.value === 'category' && (
                                    <Star className="h-3 w-3 text-amber-500" />
                                  )}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {column.mapping === 'category' && (
                          <Star className="h-4 w-4 text-amber-500" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-slate-600 text-sm">
                      <div className="space-y-1">
                        {column.samples.map((sample, i) => (
                          <div key={i} className="truncate max-w-[200px]">
                            {sample}
                          </div>
                        ))}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <Alert className="mt-4 border-amber-200 bg-amber-50">
            <Star className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              Category column is critical for training our AI. Make sure to map
              your existing categories!
            </AlertDescription>
          </Alert>

          <div className="flex justify-between mt-6">
            <Button
              variant="outline"
              onClick={autoDetect}
              className="border-green-600 text-success hover:bg-green-50"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Auto-detect
            </Button>
            <Button
              onClick={confirmMapping}
              disabled={!hasRequiredMappings()}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Confirm Mapping
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </Card>

        {/* No Category Warning */}
        {!hasCategory && (
          <Alert className="mt-6" variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No Category Column Detected</AlertTitle>
            <AlertDescription>
              Please select which column contains your categories. This is
              required for AI training.
            </AlertDescription>
          </Alert>
        )}

        {/* Alternative Options for No Category */}
        {!hasCategory && (
          <Card className="p-4 bg-slate-50 mt-4 border border-slate-200">
            <p className="text-sm text-slate-700 mb-3">
              <Lightbulb className="h-4 w-4 inline mr-2 text-amber-500" />
              No category column detected. Do you want to:
            </p>
            <div className="space-y-2">
              <Button
                size="sm"
                variant="outline"
                className="w-full justify-start"
              >
                Upload a different file with categories
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="w-full justify-start"
              >
                Continue without training (less accurate)
              </Button>
            </div>
          </Card>
        )}

        {/* Multiple Amount Columns Info */}
        {detectedColumns.filter((col) =>
          ['debit', 'credit'].includes(col.mapping || ''),
        ).length > 1 && (
          <Alert className="mt-4 border-blue-200 bg-blue-50">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              We&rsquo;ll combine your Debit and Credit columns into a single
              Amount field. Credits will be shown as negative values.
            </AlertDescription>
          </Alert>
        )}
      </main>
    </div>
  );
};

export default ColumnMappingPage;
