import React from 'react';

interface OnboardingPath {
  id: 'zero' | 'historical' | 'schema';
  title: string;
  badge: string;
  description: string;
  icon: string;
  setupTime: string;
  accuracy: string;
  aiMode: string;
  aiDescription: string;
  benefits: string[];
  businessContext: string[];
  color: string;
  recommended?: boolean;
}

const onboardingPaths: OnboardingPath[] = [
  {
    id: 'zero',
    title: 'Quick Start',
    badge: 'Fastest',
    description: 'Start immediately - AI figures everything out',
    icon: '∷',
    setupTime: '5 min',
    accuracy: '',
    aiMode: 'Pure AI Business Intelligence',
    aiDescription:
      'AI creates intelligent business hierarchies without training data',
    benefits: [
      'Want to start immediately',
      'No existing categorization',
      'Trust AI to create smart categories',
    ],
    businessContext: [
      'Small business or professional services',
      'Need quick categorization without complex setup',
      'Value speed and convenience',
    ],
    color: 'var(--giki-primary)',
    recommended: true,
  },
  {
    id: 'historical',
    title: 'Learn from History',
    badge: 'Most Accurate',
    description: 'Learn from your historical transaction patterns',
    icon: '□',
    setupTime: '45 min',
    accuracy: '',
    aiMode: 'Historical Pattern Learning',
    aiDescription: 'AI learns from your categorized transaction history',
    benefits: [
      'Companies with existing categorization',
      'Want high-quality categorization from day one',
      'Have historical files with categories',
    ],
    businessContext: [
      'Growing business with historical data',
      'Need to improve categorization quality over time',
      'Have existing transaction history to learn from',
    ],
    color: '#059669',
  },
  {
    id: 'schema',
    title: 'Use Your MIS Structure',
    badge: 'MIS Compliance',
    description: 'AI follows your provided MIS reporting structure',
    icon: '⊞',
    setupTime: '30 min',
    accuracy: '',
    aiMode: 'Schema-Guided Intelligence',
    aiDescription: 'AI follows your existing accounting structure',
    benefits: [
      'Have MIS structure or category hierarchy',
      'Want AI to follow specific schema',
      'Need accounting system compliance',
    ],
    businessContext: [
      'Established business with accounting systems',
      'Need MIS reporting compliance and complex hierarchy',
      'Require audit trails and compliance validation',
    ],
    color: '#7C3AED',
  },
];

interface OnboardingPathSelectorProps {
  onPathSelect: (pathId: string) => void;
}

export const OnboardingPathSelector: React.FC<OnboardingPathSelectorProps> = ({
  onPathSelect,
}) => {
  const [_selectedPath, _setSelectedPath] = React.useState<string>('');
  const [_hoveredPath, _setHoveredPath] = React.useState<string | null>(null);

  const handlePathSelect = (pathId: string) => {
    _setSelectedPath(pathId);
    onPathSelect(pathId);
  };

  const getBadgeStyles = (path: OnboardingPath) => {
    if (path.recommended) {
      return {
        backgroundColor: '#E8F5E8',
        color: 'var(--giki-primary)',
        border: '1px solid #295343',
      };
    }
    return {
      backgroundColor: path.id === 'schema' ? '#F3E8FF' : '#ECFDF5',
      color: path.id === 'schema' ? '#7C3AED' : '#059669',
      border: `1px solid ${path.id === 'schema' ? '#7C3AED' : '#059669'}`,
    };
  };

  const getCardStyles = (path: OnboardingPath, isHovered: boolean) => ({
    border: path.recommended ? `2px solid ${path.color}` : '2px solid #E5E7EB',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    transform: isHovered ? 'translateY(-4px)' : 'translateY(0)',
    boxShadow: isHovered
      ? `0 8px 25px rgba(${
          path.color === '#295343'
            ? '41, 83, 67'
            : path.color === '#7C3AED'
              ? '124, 58, 237'
              : '5, 150, 105'
        }, 0.15)`
      : 'none',
  });

  return (
    <div className="bg-white p-8 rounded-xl border border-gray-200 mb-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-semibold text-gray-800 mb-3">
          Choose Your Getting Started Approach
        </h2>
        <p className="text-gray-600 text-lg">
          Three ways to get the most accurate AI categorization for your
          business
        </p>
      </div>

      <div className="space-y-6 mb-8 max-w-5xl mx-auto">
        {onboardingPaths.map((path) => (
          <div
            key={path.id}
            className="bg-white rounded-xl p-8 transition-all duration-300"
            style={getCardStyles(path, _hoveredPath === path.id)}
            onClick={() => handlePathSelect(path.id)}
            onMouseEnter={() => _setHoveredPath(path.id)}
            onMouseLeave={() => _setHoveredPath(null)}
            role="button"
            tabIndex={0}
            aria-label={`Select ${path.title} onboarding path`}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handlePathSelect(path.id);
              }
            }}
          >
            {/* Top Row - Title, Badge, and CTA */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <span className="text-4xl" style={{ color: path.color }}>
                  {path.icon}
                </span>
                <div>
                  <h3
                    className={`text-2xl font-bold m-0 ${
                      path.recommended ? 'text-brand-primary' : 'text-gray-800'
                    }`}
                  >
                    {path.title}
                  </h3>
                  <p className="text-gray-600 text-base mt-1">
                    {path.description}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <span
                  className="text-sm font-medium px-4 py-2 rounded-full"
                  style={getBadgeStyles(path)}
                >
                  {path.badge}
                </span>
                <button
                  className={`px-8 py-3 rounded-lg font-semibold text-base transition-colors whitespace-nowrap ${
                    path.recommended
                      ? 'bg-brand-primary text-white hover:bg-brand-primary-hover'
                      : 'bg-white border-2 border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                  aria-label={`Select ${path.title}`}
                >
                  Select This Path →
                </button>
              </div>
            </div>

            {/* Setup Time and AI Mode */}
            <div className="flex items-center gap-8 mb-6">
              <div className="text-center">
                <div
                  className="text-3xl font-bold"
                  style={{ color: path.color }}
                >
                  {path.setupTime}
                </div>
                <div className="text-base text-gray-600">Setup Time</div>
              </div>
              <div className="flex-1">
                <div
                  className="text-base font-semibold mb-2"
                  style={{ color: path.color }}
                >
                  AI Mode: {path.aiMode}
                </div>
                <div className="text-gray-600 text-base">
                  {path.aiDescription}
                </div>
              </div>
            </div>

            {/* Bottom Row - Context and Benefits side by side */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Business Context */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
                <div className="font-semibold text-gray-800 text-base mb-3">
                  Business Context:
                </div>
                <div className="text-gray-700 text-base space-y-2">
                  {path.businessContext.map((context, index) => (
                    <div key={index}>• {context}</div>
                  ))}
                </div>
              </div>

              {/* Benefits */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-5">
                <div className="font-semibold text-gray-800 text-base mb-3">
                  Best For:
                </div>
                <div className="text-gray-700 text-base space-y-2">
                  {path.benefits.map((benefit, index) => (
                    <div key={index}>• {benefit}</div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-slate-600">⚬</span>
          <span className="font-semibold text-slate-700">
            Not Sure Which Path?
          </span>
        </div>
        <p className="text-slate-600 text-sm m-0">
          Most customers choose <strong>Quick Start</strong> to start
          immediately without preparation. Choose{' '}
          <strong>Learn from History</strong> if you have historical categorized
          data. Choose <strong>Use Your MIS Structure</strong> if you have MIS
          structure but no categorized files.
        </p>
      </div>
    </div>
  );
};

export default OnboardingPathSelector;
