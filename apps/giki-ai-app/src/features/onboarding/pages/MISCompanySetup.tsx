import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/shared/components/ui/Button';
import { Card } from '@/shared/components/ui/Card';
import { Input } from '@/shared/components/ui/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/Select';
import { Label } from '@/shared/components/ui/Label';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useToast } from '@/shared/components/ui/use-toast';
import { startMISSetup } from '../services/misSetupService';

const companySetupSchema = z.object({
  companyName: z.string().min(1, 'Company name is required'),
  industry: z.string().min(1, 'Industry is required'),
  companySize: z.string().min(1, 'Company size is required'),
  fiscalYearEnd: z
    .string()
    .regex(
      /^(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/,
      'Must be in MM-DD format',
    ),
  defaultCurrency: z.string().min(1, 'Currency is required'),
});

type CompanySetupForm = z.infer<typeof companySetupSchema>;

const industries = [
  { value: 'technology', label: 'Technology & Software' },
  { value: 'retail', label: 'Retail & E-commerce' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'healthcare', label: 'Healthcare & Medical' },
  { value: 'finance', label: 'Financial Services' },
  { value: 'consulting', label: 'Consulting & Professional Services' },
  { value: 'real_estate', label: 'Real Estate' },
  { value: 'hospitality', label: 'Hospitality & Tourism' },
  { value: 'education', label: 'Education' },
  { value: 'non_profit', label: 'Non-Profit' },
  { value: 'other', label: 'Other' },
];

const companySizes = [
  { value: '1-10', label: '1-10 employees' },
  { value: '11-50', label: '11-50 employees' },
  { value: '51-200', label: '51-200 employees' },
  { value: '201-500', label: '201-500 employees' },
  { value: '500+', label: '500+ employees' },
];

const currencies = [
  { value: 'USD', label: 'USD - US Dollar' },
  { value: 'EUR', label: 'EUR - Euro' },
  { value: 'GBP', label: 'GBP - British Pound' },
  { value: 'CAD', label: 'CAD - Canadian Dollar' },
  { value: 'AUD', label: 'AUD - Australian Dollar' },
  { value: 'INR', label: 'INR - Indian Rupee' },
  { value: 'JPY', label: 'JPY - Japanese Yen' },
  { value: 'CNY', label: 'CNY - Chinese Yuan' },
];

export const MISCompanySetup: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<CompanySetupForm>({
    resolver: zodResolver(companySetupSchema),
    defaultValues: {
      fiscalYearEnd: '12-31',
      defaultCurrency: 'USD',
    },
  });

  const onSubmit = async (data: CompanySetupForm) => {
    setIsSubmitting(true);
    try {
      // Initialize MIS setup with company information
      const response = await startMISSetup({
        company_info: {
          name: data.companyName,
          industry: data.industry,
          size: data.companySize,
          fiscal_year_end: data.fiscalYearEnd,
          default_currency: data.defaultCurrency,
        },
      });

      // Store setup ID for later steps
      sessionStorage.setItem('mis_setup_id', response.setupId);
      sessionStorage.setItem('company_info', JSON.stringify(data));

      // Navigate to data upload (optional step)
      navigate('/onboarding/data-upload');
    } catch (error) {
      toast({
        title: 'Setup Error',
        description: 'Failed to initialize MIS setup. Please try again.',
        variant: 'destructive',
      });
      console.error('MIS setup error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <Card className="p-8">
          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">Step 1 of 4</span>
              <span className="text-sm text-gray-600">Company Information</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full"
                style={{ width: '25%' }}
              />
            </div>
          </div>

          {/* Header */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Tell us about your business
            </h2>
            <p className="text-gray-600">
              This helps us create an MIS structure tailored to your industry
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
              <Label htmlFor="companyName">Company Name</Label>
              <Input
                id="companyName"
                {...register('companyName')}
                placeholder="Acme Corporation"
                className="mt-1"
              />
              {errors.companyName && (
                <p className="text-sm text-error mt-1">
                  {errors.companyName.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="industry">Industry</Label>
              <Select
                value={watch('industry')}
                onValueChange={(value) => setValue('industry', value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select your industry" />
                </SelectTrigger>
                <SelectContent>
                  {industries.map((industry) => (
                    <SelectItem key={industry.value} value={industry.value}>
                      {industry.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.industry && (
                <p className="text-sm text-error mt-1">
                  {errors.industry.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="companySize">Company Size</Label>
              <Select
                value={watch('companySize')}
                onValueChange={(value) => setValue('companySize', value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select company size" />
                </SelectTrigger>
                <SelectContent>
                  {companySizes.map((size) => (
                    <SelectItem key={size.value} value={size.value}>
                      {size.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.companySize && (
                <p className="text-sm text-error mt-1">
                  {errors.companySize.message}
                </p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="fiscalYearEnd">Fiscal Year End</Label>
                <Input
                  id="fiscalYearEnd"
                  {...register('fiscalYearEnd')}
                  placeholder="MM-DD"
                  className="mt-1"
                />
                {errors.fiscalYearEnd && (
                  <p className="text-sm text-error mt-1">
                    {errors.fiscalYearEnd.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="defaultCurrency">Default Currency</Label>
                <Select
                  value={watch('defaultCurrency')}
                  onValueChange={(value) => setValue('defaultCurrency', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.defaultCurrency && (
                  <p className="text-sm text-error mt-1">
                    {errors.defaultCurrency.message}
                  </p>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="ghost"
                onClick={() => navigate('/onboarding/introduction')}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Setting up...' : 'Continue'}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default MISCompanySetup;
