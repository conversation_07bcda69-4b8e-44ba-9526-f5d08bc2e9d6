import { useState, useCallback } from 'react';
import {
  startMISSetup,
  detectEnhancements,
  completeMISSetup,
  type CompanyInfo,
  type Enhancement,
  type MISSetupRequest,
} from '../services/misSetupService';

// Frontend interface that matches test expectations
export interface CompanyInfoForm {
  name: string;
  industry: string;
  size: string;
  fiscalYearEnd: string;
  defaultCurrency: string;
}

export interface UploadedFile {
  id: string;
  filename: string;
  status: 'uploading' | 'completed' | 'failed';
  file?: File;
}

export interface UseMISSetupReturn {
  // State
  companyInfo: CompanyInfoForm;
  uploadedFiles: UploadedFile[];
  enhancementOpportunities: Enhancement[];
  isLoading: boolean;
  error: string | null;

  // Actions
  updateCompanyInfo: (updates: Partial<CompanyInfoForm>) => void;
  updateUploadedFiles: (files: UploadedFile[]) => void;
  continueToNextStep: () => Promise<void>;
  skipToActivation: () => Promise<void>;
}

/**
 * Hook for managing MIS setup state and operations
 */
export function useMISSetup(): UseMISSetupReturn {
  const [companyInfo, setCompanyInfo] = useState<CompanyInfoForm>({
    name: '',
    industry: '',
    size: '',
    fiscalYearEnd: '',
    defaultCurrency: 'USD',
  });

  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [enhancementOpportunities, setEnhancementOpportunities] = useState<
    Enhancement[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateCompanyInfo = useCallback((updates: Partial<CompanyInfoForm>) => {
    setCompanyInfo((prev) => ({ ...prev, ...updates }));
    setError(null);
  }, []);

  const updateUploadedFiles = useCallback((files: UploadedFile[]) => {
    setUploadedFiles(files);
  }, []);

  const continueToNextStep = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Convert form data to service format
      const companyData: CompanyInfo = {
        name: companyInfo.name,
        industry: companyInfo.industry,
        size: companyInfo.size,
        fiscal_year_end: companyInfo.fiscalYearEnd,
        default_currency: companyInfo.defaultCurrency,
      };

      const setupRequest: MISSetupRequest = {
        company_info: companyData,
        uploaded_files: uploadedFiles.map((f) => f.id),
      };

      const setupResponse = await startMISSetup(setupRequest);

      // Update enhancement opportunities from response
      setEnhancementOpportunities(setupResponse.enhancementOpportunities);

      // Detect enhancements from uploaded files if any
      if (uploadedFiles.length > 0) {
        const allEnhancements: Enhancement[] = [];

        for (const uploadedFile of uploadedFiles) {
          if (uploadedFile.file) {
            try {
              const fileEnhancements = await detectEnhancements(
                uploadedFile.file,
              );
              allEnhancements.push(...fileEnhancements);
            } catch (enhancementError) {
              console.warn(
                `Failed to detect enhancements for ${uploadedFile.filename}:`,
                enhancementError,
              );
              // Continue with other files
            }
          }
        }

        // Merge with existing enhancements
        setEnhancementOpportunities((prev) => [...prev, ...allEnhancements]);
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to continue setup';
      setError(errorMessage);
      throw err; // Re-throw for component error handling
    } finally {
      setIsLoading(false);
    }
  }, [companyInfo, uploadedFiles]);

  const skipToActivation = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Start minimal MIS setup without files
      const companyData: CompanyInfo = {
        name: companyInfo.name || 'My Company',
        industry: companyInfo.industry || 'General Business',
        size: companyInfo.size || 'Small',
        fiscal_year_end: companyInfo.fiscalYearEnd || 'December',
        default_currency: companyInfo.defaultCurrency || 'USD',
      };

      const setupRequest: MISSetupRequest = {
        company_info: companyData,
      };

      const setupResponse = await startMISSetup(setupRequest);

      // Complete setup immediately without enhancements
      await completeMISSetup(setupResponse.setupId);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to activate MIS';
      setError(errorMessage);
      throw err; // Re-throw for component error handling
    } finally {
      setIsLoading(false);
    }
  }, [companyInfo]);

  return {
    companyInfo,
    uploadedFiles,
    enhancementOpportunities,
    isLoading,
    error,
    updateCompanyInfo,
    updateUploadedFiles,
    continueToNextStep,
    skipToActivation,
  };
}

export default useMISSetup;
