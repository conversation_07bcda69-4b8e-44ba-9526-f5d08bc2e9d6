import React, { useState, useCallback } from 'react';
import {
  ChevronRightIcon,
  BuildingOfficeIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';

export interface BusinessContextData {
  // Basic Information
  industry: string;
  company_size: string;
  company_website?: string;
  business_description?: string;

  // Financial Context
  fiscal_year_end: string;
  primary_currency: string;
  accounting_method: 'accrual' | 'cash';
  existing_accounting_system?: string;

  // Geographic Context
  primary_country: string;
  tax_jurisdiction: string;

  // Business Model Context
  business_model: string;
  revenue_streams: string[];

  // MIS Specific Requirements
  reporting_requirements: string[];
  gl_code_preference: '4_digit' | '5_digit' | 'custom';
  department_structure?: string;
}

interface BusinessContextFormProps {
  onSubmit: (data: BusinessContextData) => void;
  onBack?: () => void;
  initialData?: Partial<BusinessContextData>;
  className?: string;
}

const INDUSTRY_OPTIONS = [
  { value: 'technology', label: 'Technology & Software' },
  { value: 'professional_services', label: 'Professional Services' },
  { value: 'healthcare', label: 'Healthcare & Medical' },
  { value: 'retail', label: 'Retail & E-commerce' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'hospitality', label: 'Hospitality & Food Service' },
  { value: 'financial_services', label: 'Financial Services' },
  { value: 'real_estate', label: 'Real Estate' },
  { value: 'education', label: 'Education' },
  { value: 'construction', label: 'Construction' },
  { value: 'transportation', label: 'Transportation & Logistics' },
  { value: 'other', label: 'Other' },
];

const COMPANY_SIZE_OPTIONS = [
  { value: 'startup', label: 'Startup (1-10 employees)' },
  { value: 'small', label: 'Small Business (11-50 employees)' },
  { value: 'medium', label: 'Medium Business (51-200 employees)' },
  { value: 'large', label: 'Large Business (201-1000 employees)' },
  { value: 'enterprise', label: 'Enterprise (1000+ employees)' },
];

const BUSINESS_MODEL_OPTIONS = [
  { value: 'b2b_services', label: 'B2B Services' },
  { value: 'b2c_services', label: 'B2C Services' },
  { value: 'product_sales', label: 'Product Sales' },
  { value: 'saas', label: 'SaaS/Subscription' },
  { value: 'marketplace', label: 'Marketplace/Platform' },
  { value: 'consulting', label: 'Consulting' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'retail', label: 'Retail' },
  { value: 'mixed', label: 'Mixed Model' },
];

const REVENUE_STREAM_OPTIONS = [
  { value: 'product_sales', label: 'Product Sales' },
  { value: 'service_revenue', label: 'Service Revenue' },
  { value: 'subscription_revenue', label: 'Subscription/Recurring Revenue' },
  { value: 'licensing', label: 'Licensing Fees' },
  { value: 'commission', label: 'Commission Revenue' },
  { value: 'advertising', label: 'Advertising Revenue' },
  { value: 'consulting', label: 'Consulting Fees' },
  { value: 'rental_income', label: 'Rental Income' },
  { value: 'interest_income', label: 'Interest Income' },
  { value: 'other', label: 'Other Revenue' },
];

const REPORTING_REQUIREMENTS = [
  { value: 'monthly_pl', label: 'Monthly P&L Reports' },
  { value: 'quarterly_reports', label: 'Quarterly Financial Reports' },
  { value: 'budget_variance', label: 'Budget vs Actual Analysis' },
  { value: 'department_breakdown', label: 'Department-wise Breakdown' },
  { value: 'project_tracking', label: 'Project-based Tracking' },
  { value: 'tax_compliance', label: 'Tax Compliance Reporting' },
  { value: 'investor_reports', label: 'Investor Reports' },
  { value: 'board_reports', label: 'Board Reports' },
  { value: 'cash_flow', label: 'Cash Flow Analysis' },
  { value: 'roi_analysis', label: 'ROI Analysis' },
];

const ACCOUNTING_SYSTEMS = [
  { value: 'quickbooks', label: 'QuickBooks' },
  { value: 'xero', label: 'Xero' },
  { value: 'sage', label: 'Sage' },
  { value: 'netsuite', label: 'NetSuite' },
  { value: 'sap', label: 'SAP' },
  { value: 'excel', label: 'Excel/Spreadsheets' },
  { value: 'other', label: 'Other System' },
  { value: 'none', label: 'No Current System' },
];

export const BusinessContextForm: React.FC<BusinessContextFormProps> = ({
  onSubmit,
  onBack,
  initialData = {},
  className = '',
}) => {
  const [formData, setFormData] = useState<BusinessContextData>({
    industry: initialData.industry || '',
    company_size: initialData.company_size || '',
    company_website: initialData.company_website || '',
    business_description: initialData.business_description || '',
    fiscal_year_end: initialData.fiscal_year_end || 'december',
    primary_currency: initialData.primary_currency || 'USD',
    accounting_method: initialData.accounting_method || 'accrual',
    existing_accounting_system: initialData.existing_accounting_system || '',
    primary_country: initialData.primary_country || 'US',
    tax_jurisdiction: initialData.tax_jurisdiction || 'US',
    business_model: initialData.business_model || '',
    revenue_streams: initialData.revenue_streams || [],
    reporting_requirements: initialData.reporting_requirements || [],
    gl_code_preference: initialData.gl_code_preference || '4_digit',
    department_structure: initialData.department_structure || '',
  });

  const [currentStep, setCurrentStep] = useState(0);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const steps = [
    {
      title: 'Company Profile',
      description: 'Basic information about your business',
      icon: BuildingOfficeIcon,
    },
    {
      title: 'Financial Setup',
      description: 'Accounting and financial preferences',
      icon: CurrencyDollarIcon,
    },
    {
      title: 'Reporting Needs',
      description: 'Customize reports and structure',
      icon: GlobeAltIcon,
    },
  ];

  const validateStep = useCallback(
    (step: number): boolean => {
      const newErrors: Record<string, string> = {};

      switch (step) {
        case 0:
          if (!formData.industry) newErrors.industry = 'Industry is required';
          if (!formData.company_size)
            newErrors.company_size = 'Company size is required';
          if (!formData.business_model)
            newErrors.business_model = 'Business model is required';
          break;
        case 1:
          if (!formData.primary_currency)
            newErrors.primary_currency = 'Currency is required';
          if (!formData.accounting_method)
            newErrors.accounting_method = 'Accounting method is required';
          break;
        case 2:
          if (formData.revenue_streams.length === 0) {
            newErrors.revenue_streams =
              'At least one revenue stream is required';
          }
          break;
      }

      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    },
    [formData],
  );

  const handleNext = useCallback(() => {
    if (validateStep(currentStep)) {
      if (currentStep === steps.length - 1) {
        onSubmit(formData);
      } else {
        setCurrentStep(currentStep + 1);
      }
    }
  }, [currentStep, steps.length, validateStep, formData, onSubmit]);

  const handleBack = useCallback(() => {
    if (currentStep === 0) {
      onBack?.();
    } else {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep, onBack]);

  const updateFormData = useCallback(
    (updates: Partial<BusinessContextData>) => {
      setFormData((prev) => ({ ...prev, ...updates }));
      // Clear related errors
      const updatedFields = Object.keys(updates);
      setErrors((prev) => {
        const newErrors = { ...prev };
        updatedFields.forEach((field) => delete newErrors[field]);
        return newErrors;
      });
    },
    [],
  );

  const toggleArrayValue = useCallback((array: string[], value: string) => {
    return array.includes(value)
      ? array.filter((item) => item !== value)
      : [...array, value];
  }, []);

  const renderStep0 = () => (
    <div className="form-section-spacing">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Industry *
        </label>
        <select
          value={formData.industry}
          onChange={(e) => updateFormData({ industry: e.target.value })}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            errors.industry ? 'border-red-300' : 'border-gray-300'
          }`}
        >
          <option value="">Select your industry</option>
          {INDUSTRY_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {errors.industry && (
          <p className="mt-1 text-sm text-error">{errors.industry}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Company Size *
        </label>
        <select
          value={formData.company_size}
          onChange={(e) => updateFormData({ company_size: e.target.value })}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            errors.company_size ? 'border-red-300' : 'border-gray-300'
          }`}
        >
          <option value="">Select company size</option>
          {COMPANY_SIZE_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {errors.company_size && (
          <p className="mt-1 text-sm text-error">{errors.company_size}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Business Model *
        </label>
        <select
          value={formData.business_model}
          onChange={(e) => updateFormData({ business_model: e.target.value })}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            errors.business_model ? 'border-red-300' : 'border-gray-300'
          }`}
        >
          <option value="">Select business model</option>
          {BUSINESS_MODEL_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {errors.business_model && (
          <p className="mt-1 text-sm text-error">{errors.business_model}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Company Website
        </label>
        <input
          type="url"
          value={formData.company_website}
          onChange={(e) => updateFormData({ company_website: e.target.value })}
          placeholder="https://your-company.com"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <p className="mt-1 text-sm text-gray-500">
          Used for additional business context analysis
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Business Description
        </label>
        <textarea
          value={formData.business_description}
          onChange={(e) =>
            updateFormData({ business_description: e.target.value })
          }
          placeholder="Brief description of what your business does..."
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>
    </div>
  );

  const renderStep1 = () => (
    <div className="form-section-spacing">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Primary Currency *
          </label>
          <select
            value={formData.primary_currency}
            onChange={(e) =>
              updateFormData({ primary_currency: e.target.value })
            }
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.primary_currency ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            <option value="USD">USD - US Dollar</option>
            <option value="EUR">EUR - Euro</option>
            <option value="GBP">GBP - British Pound</option>
            <option value="CAD">CAD - Canadian Dollar</option>
            <option value="AUD">AUD - Australian Dollar</option>
            <option value="INR">INR - Indian Rupee</option>
            <option value="JPY">JPY - Japanese Yen</option>
            <option value="OTHER">Other</option>
          </select>
          {errors.primary_currency && (
            <p className="mt-1 text-sm text-error">
              {errors.primary_currency}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fiscal Year End
          </label>
          <select
            value={formData.fiscal_year_end}
            onChange={(e) =>
              updateFormData({ fiscal_year_end: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="december">December 31</option>
            <option value="march">March 31</option>
            <option value="june">June 30</option>
            <option value="september">September 30</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Accounting Method *
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
            <input
              type="radio"
              name="accounting_method"
              value="accrual"
              checked={formData.accounting_method === 'accrual'}
              onChange={(e) =>
                updateFormData({
                  accounting_method: e.target.value as 'accrual' | 'cash',
                })
              }
              className="mr-3"
            />
            <div>
              <div className="font-medium">Accrual Accounting</div>
              <div className="text-sm text-gray-600">
                Record transactions when they occur
              </div>
            </div>
          </label>
          <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
            <input
              type="radio"
              name="accounting_method"
              value="cash"
              checked={formData.accounting_method === 'cash'}
              onChange={(e) =>
                updateFormData({
                  accounting_method: e.target.value as 'accrual' | 'cash',
                })
              }
              className="mr-3"
            />
            <div>
              <div className="font-medium">Cash Accounting</div>
              <div className="text-sm text-gray-600">
                Record transactions when cash changes hands
              </div>
            </div>
          </label>
        </div>
        {errors.accounting_method && (
          <p className="mt-1 text-sm text-error">
            {errors.accounting_method}
          </p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Current Accounting System
        </label>
        <select
          value={formData.existing_accounting_system}
          onChange={(e) =>
            updateFormData({ existing_accounting_system: e.target.value })
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Select current system (optional)</option>
          {ACCOUNTING_SYSTEMS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <p className="mt-1 text-sm text-gray-500">
          We'll optimize integration with your existing system
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          GL Code Preference
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
            <input
              type="radio"
              name="gl_code_preference"
              value="4_digit"
              checked={formData.gl_code_preference === '4_digit'}
              onChange={(e) =>
                updateFormData({
                  gl_code_preference: e.target.value as
                    | '4_digit'
                    | '5_digit'
                    | 'custom',
                })
              }
              className="mr-3"
            />
            <div>
              <div className="font-medium">4-Digit</div>
              <div className="text-sm text-gray-600">
                Standard format (4000-9999)
              </div>
            </div>
          </label>
          <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
            <input
              type="radio"
              name="gl_code_preference"
              value="5_digit"
              checked={formData.gl_code_preference === '5_digit'}
              onChange={(e) =>
                updateFormData({
                  gl_code_preference: e.target.value as
                    | '4_digit'
                    | '5_digit'
                    | 'custom',
                })
              }
              className="mr-3"
            />
            <div>
              <div className="font-medium">5-Digit</div>
              <div className="text-sm text-gray-600">
                Extended format (40000-99999)
              </div>
            </div>
          </label>
          <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
            <input
              type="radio"
              name="gl_code_preference"
              value="custom"
              checked={formData.gl_code_preference === 'custom'}
              onChange={(e) =>
                updateFormData({
                  gl_code_preference: e.target.value as
                    | '4_digit'
                    | '5_digit'
                    | 'custom',
                })
              }
              className="mr-3"
            />
            <div>
              <div className="font-medium">Custom</div>
              <div className="text-sm text-gray-600">Your existing format</div>
            </div>
          </label>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="form-section-spacing">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Revenue Streams *{' '}
          <span className="text-sm text-gray-500">(Select all that apply)</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {REVENUE_STREAM_OPTIONS.map((option) => (
            <label
              key={option.value}
              className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
            >
              <input
                type="checkbox"
                checked={formData.revenue_streams.includes(option.value)}
                onChange={(e) => {
                  const newStreams = e.target.checked
                    ? [...formData.revenue_streams, option.value]
                    : formData.revenue_streams.filter(
                        (stream) => stream !== option.value,
                      );
                  updateFormData({ revenue_streams: newStreams });
                }}
                className="mr-3"
              />
              <span className="text-sm">{option.label}</span>
            </label>
          ))}
        </div>
        {errors.revenue_streams && (
          <p className="mt-1 text-sm text-error">{errors.revenue_streams}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Reporting Requirements{' '}
          <span className="text-sm text-gray-500">(Select all that apply)</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {REPORTING_REQUIREMENTS.map((option) => (
            <label
              key={option.value}
              className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
            >
              <input
                type="checkbox"
                checked={formData.reporting_requirements.includes(option.value)}
                onChange={(e) => {
                  const newRequirements = e.target.checked
                    ? [...formData.reporting_requirements, option.value]
                    : formData.reporting_requirements.filter(
                        (req) => req !== option.value,
                      );
                  updateFormData({ reporting_requirements: newRequirements });
                }}
                className="mr-3"
              />
              <span className="text-sm">{option.label}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Department Structure
        </label>
        <textarea
          value={formData.department_structure}
          onChange={(e) =>
            updateFormData({ department_structure: e.target.value })
          }
          placeholder="List your main departments (e.g., Sales, Marketing, Operations, R&D)"
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <p className="mt-1 text-sm text-gray-500">
          This helps create department-specific expense categories
        </p>
      </div>
    </div>
  );

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {/* Step Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const IconComponent = step.icon;
            return (
              <React.Fragment key={index}>
                <div className="flex items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      index <= currentStep
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-400'
                    }`}
                  >
                    <IconComponent className="w-5 h-5" />
                  </div>
                  <div className="ml-3">
                    <div
                      className={`text-sm font-medium ${
                        index <= currentStep ? 'text-blue-600' : 'text-gray-400'
                      }`}
                    >
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500">
                      {step.description}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <ChevronRightIcon className="w-5 h-5 text-gray-300 mx-4" />
                )}
              </React.Fragment>
            );
          })}
        </div>
      </div>

      {/* Form Content */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {steps[currentStep].title}
          </h2>
          <p className="text-gray-600">{steps[currentStep].description}</p>
        </div>

        {currentStep === 0 && renderStep0()}
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}

        {/* Navigation */}
        <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
          <button
            onClick={handleBack}
            className="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {currentStep === 0 ? 'Back to Path Selection' : 'Previous'}
          </button>
          <button
            onClick={handleNext}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {currentStep === steps.length - 1 ? 'Complete Setup' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BusinessContextForm;
