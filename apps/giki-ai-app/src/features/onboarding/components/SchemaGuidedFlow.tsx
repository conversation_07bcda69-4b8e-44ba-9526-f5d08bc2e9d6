import React, { useState, useCallback } from 'react';
import {
  FileSpreadsheet,
  GitBranch,
  CheckCircle,
  AlertCircle,
  Upload,
  Edit3,
  Shield,
  ChevronDown,
  ChevronRight,
  Target,
  Clock,
  Layers,
} from 'lucide-react';
import { useAgent } from '@/features/intelligence/hooks/useAgent';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { Card } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';

interface GLCodeNode {
  id: string;
  code: string;
  name: string;
  level: number;
  parent?: string;
  children?: GLCodeNode[];
  expanded?: boolean;
}

interface SchemaGuidedFlowProps {
  onComplete: (results: unknown) => void;
  onBack: () => void;
}

type SchemaType = 'gl-codes' | 'custom-categories';
type SchemaMode = 'upload' | 'manual';

export const SchemaGuidedFlow: React.FC<SchemaGuidedFlowProps> = ({
  onComplete,
  onBack,
}) => {
  const { user: _user } = useAuth();
  const { sendMessage } = useAgent();
  const [currentStep, setCurrentStep] = useState<
    | 'welcome'
    | 'structure'
    | 'schema'
    | 'mapping'
    | 'validation'
    | 'sample'
    | 'review'
    | 'complete'
  >('welcome');
  const [schemaType, setSchemaType] = useState<SchemaType>('gl-codes');
  const [schemaMode, setSchemaMode] = useState<SchemaMode>('upload');
  const [uploadedSchema, setUploadedSchema] = useState<File | null>(null);
  const [glCodeHierarchy, setGlCodeHierarchy] = useState<GLCodeNode[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [validationResults, setValidationResults] = useState<{
    valid: boolean;
    warnings: string[];
    stats: {
      totalCodes: number;
      levels: number;
      coverage: number;
    };
  } | null>(null);

  const renderStepIndicator = () => {
    const steps = [
      'Welcome',
      'Structure',
      'Schema',
      'Mapping',
      'Validation',
      'Sample',
      'Review',
      'Complete',
    ];
    const currentIndex = steps.findIndex(
      (s) => s.toLowerCase() === currentStep,
    );

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => (
          <React.Fragment key={step}>
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                index <= currentIndex
                  ? 'bg-giki-primary text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}
            >
              {index < currentIndex ? <CheckCircle size={16} /> : index + 1}
            </div>
            {index < steps.length - 1 && (
              <div
                className={`w-16 h-1 ${
                  index < currentIndex ? 'bg-giki-primary' : 'bg-gray-200'
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  const toggleNode = (nodeId: string) => {
    const updateTree = (nodes: GLCodeNode[]): GLCodeNode[] => {
      return nodes.map((node) => {
        if (node.id === nodeId) {
          return { ...node, expanded: !node.expanded };
        }
        if (node.children) {
          return { ...node, children: updateTree(node.children) };
        }
        return node;
      });
    };
    setGlCodeHierarchy(updateTree(glCodeHierarchy));
  };

  const renderGLCodeNode = (node: GLCodeNode, depth: number = 0) => (
    <div key={node.id} className="mb-1">
      <div
        className={`flex items-center gap-2 p-2 hover:bg-gray-50 rounded cursor-pointer`}
        style={{ paddingLeft: `${depth * 24 + 8}px` }}
        onClick={() => node.children && toggleNode(node.id)}
      >
        {node.children &&
          node.children.length > 0 &&
          (node.expanded ? (
            <ChevronDown size={16} className="text-gray-500" />
          ) : (
            <ChevronRight size={16} className="text-gray-500" />
          ))}
        <span className="font-mono text-sm text-giki-primary">{node.code}</span>
        <span className="text-sm text-gray-700">{node.name}</span>
      </div>
      {node.expanded &&
        node.children?.map((child) => renderGLCodeNode(child, depth + 1))}
    </div>
  );

  const handleSchemaUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        setUploadedSchema(file);
        // Simulate schema parsing
        setTimeout(() => {
          setGlCodeHierarchy([
            {
              id: '1',
              code: '1000',
              name: 'Assets',
              level: 1,
              expanded: true,
              children: [
                {
                  id: '1.1',
                  code: '1100',
                  name: 'Current Assets',
                  level: 2,
                  parent: '1',
                  children: [
                    {
                      id: '1.1.1',
                      code: '1110',
                      name: 'Cash',
                      level: 3,
                      parent: '1.1',
                    },
                    {
                      id: '1.1.2',
                      code: '1120',
                      name: 'Accounts Receivable',
                      level: 3,
                      parent: '1.1',
                    },
                  ],
                },
              ],
            },
            {
              id: '2',
              code: '5000',
              name: 'Revenue',
              level: 1,
              expanded: false,
              children: [
                {
                  id: '2.1',
                  code: '5100',
                  name: 'Product Revenue',
                  level: 2,
                  parent: '2',
                },
                {
                  id: '2.2',
                  code: '5200',
                  name: 'Service Revenue',
                  level: 2,
                  parent: '2',
                },
              ],
            },
          ]);
          setValidationResults({
            valid: true,
            warnings: ['Some transactions may need manual mapping'],
            stats: {
              totalCodes: 8,
              levels: 3,
              coverage: 92,
            },
          });
        }, 1000);
      }
    },
    [],
  );

  const processSchemaWithAI = async () => {
    setIsProcessing(true);
    setCurrentStep('validation');

    try {
      // Create schema with AI
      const _result = await sendMessage(
        `/create type:schema schema_type:${schemaType} schema_mode:true`,
      );

      // Simulate processing
      setTimeout(() => {
        setIsProcessing(false);
        setCurrentStep('sample');
      }, 2000);
    } catch (error) {
      console.error('Schema processing error:', error);
      setIsProcessing(false);
    }
  };

  const renderWelcomeStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Layers size={64} className="mx-auto mb-6 text-giki-primary" />
        <h2 className="text-3xl font-bold text-gray-800 mb-4">
          Schema-Guided Setup
        </h2>
        <p className="text-xl text-gray-600 mb-8">
          AI follows your GL code structure for 95% accuracy
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 text-center">
          <Target size={32} className="mx-auto mb-3 text-giki-primary" />
          <h3 className="font-semibold text-xl mb-2">95% Accuracy</h3>
          <p className="text-gray-600 text-sm">
            AI follows your exact business structure
          </p>
        </Card>
        <Card className="p-6 text-center">
          <Clock size={32} className="mx-auto mb-3 text-blue-600" />
          <h3 className="font-semibold text-xl mb-2">30 Minutes</h3>
          <p className="text-gray-600 text-sm">
            Quick setup with schema mapping
          </p>
        </Card>
        <Card className="p-6 text-center">
          <Shield size={32} className="mx-auto mb-3 text-purple-600" />
          <h3 className="font-semibold text-xl mb-2">Compliance Ready</h3>
          <p className="text-gray-600 text-sm">
            Follows your GL code requirements
          </p>
        </Card>
      </div>

      <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <h3 className="font-semibold text-purple-800 mb-3">Perfect For:</h3>
        <ul className="space-y-2 text-purple-700">
          <li>• Companies with established GL code hierarchies</li>
          <li>• Organizations requiring specific categorization rules</li>
          <li>• Businesses with compliance requirements</li>
          <li>• Teams wanting AI to follow exact structures</li>
        </ul>
      </div>

      <div className="flex justify-end">
        <Button
          onClick={() => setCurrentStep('structure')}
          className="bg-giki-primary hover:bg-giki-primary-dark"
        >
          Start Schema Setup →
        </Button>
      </div>
    </div>
  );

  const renderStructureStep = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-6">
        <GitBranch size={32} className="text-giki-primary" />
        <div>
          <h3 className="text-2xl font-semibold">Choose Schema Structure</h3>
          <p className="text-gray-600">
            Select how you want to organize your categories
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card
          className={`p-6 cursor-pointer transition-all ${
            schemaType === 'gl-codes'
              ? 'border-2 border-giki-primary bg-giki-primary/5'
              : 'border hover:border-gray-300'
          }`}
          onClick={() => setSchemaType('gl-codes')}
        >
          <div className="flex items-center gap-3 mb-4">
            <FileSpreadsheet size={24} className="text-giki-primary" />
            <h4 className="font-semibold text-xl">GL Code Structure</h4>
            {schemaType === 'gl-codes' && (
              <CheckCircle size={20} className="text-giki-primary ml-auto" />
            )}
          </div>
          <p className="text-gray-600 mb-4">
            Use your existing General Ledger codes and chart of accounts
          </p>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle size={16} className="text-success" />
              <span>4-level hierarchy support</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle size={16} className="text-success" />
              <span>Compliance-ready structure</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle size={16} className="text-success" />
              <span>Financial reporting compatible</span>
            </div>
          </div>
        </Card>

        <Card
          className={`p-6 cursor-pointer transition-all ${
            schemaType === 'custom-categories'
              ? 'border-2 border-giki-primary bg-giki-primary/5'
              : 'border hover:border-gray-300'
          }`}
          onClick={() => setSchemaType('custom-categories')}
        >
          <div className="flex items-center gap-3 mb-4">
            <Edit3 size={24} className="text-blue-600" />
            <h4 className="font-semibold text-xl">Custom Categories</h4>
            {schemaType === 'custom-categories' && (
              <CheckCircle size={20} className="text-giki-primary ml-auto" />
            )}
          </div>
          <p className="text-gray-600 mb-4">
            Create a flexible category structure for your business
          </p>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle size={16} className="text-success" />
              <span>Flexible naming</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle size={16} className="text-success" />
              <span>Business-specific categories</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle size={16} className="text-success" />
              <span>Easy to modify</span>
            </div>
          </div>
        </Card>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          ← Back
        </Button>
        <Button
          onClick={() => setCurrentStep('schema')}
          className="bg-giki-primary hover:bg-giki-primary-dark"
        >
          Continue →
        </Button>
      </div>
    </div>
  );

  const renderSchemaStep = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-6">
        <FileSpreadsheet size={32} className="text-giki-primary" />
        <div>
          <h3 className="text-2xl font-semibold">
            Provide Your {schemaType === 'gl-codes' ? 'GL Codes' : 'Categories'}
          </h3>
          <p className="text-gray-600">Upload a file or enter manually</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <Button
          variant={schemaMode === 'upload' ? 'default' : 'outline'}
          onClick={() => setSchemaMode('upload')}
          className={schemaMode === 'upload' ? 'bg-giki-primary' : ''}
        >
          <Upload size={20} className="mr-2" />
          Upload File
        </Button>
        <Button
          variant={schemaMode === 'manual' ? 'default' : 'outline'}
          onClick={() => setSchemaMode('manual')}
          className={schemaMode === 'manual' ? 'bg-giki-primary' : ''}
        >
          <Edit3 size={20} className="mr-2" />
          Enter Manually
        </Button>
      </div>

      {schemaMode === 'upload' ? (
        <div className="space-y-4">
          <div className="border-2 border-dashed border-giki-primary/30 rounded-xl p-8 text-center bg-giki-primary/5">
            <FileSpreadsheet
              size={48}
              className="mx-auto mb-4 text-giki-primary"
            />
            <h4 className="text-xl font-semibold mb-2">
              Upload Your Schema File
            </h4>
            <p className="text-gray-600 mb-4">
              Excel or CSV file with GL codes and descriptions
            </p>
            <input
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleSchemaUpload}
              className="hidden"
              id="schema-upload"
            />
            <label
              htmlFor="schema-upload"
              className="inline-block px-6 py-3 bg-giki-primary text-white rounded-lg hover:bg-giki-primary-dark cursor-pointer font-semibold"
            >
              Select File
            </label>
          </div>

          {uploadedSchema && (
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <FileSpreadsheet size={24} className="text-giki-primary" />
                <div className="flex-1">
                  <div className="font-medium">{uploadedSchema.name}</div>
                  <div className="text-sm text-gray-600">
                    {(uploadedSchema.size / 1024).toFixed(2)} KB • Ready to
                    process
                  </div>
                </div>
                <CheckCircle size={20} className="text-success" />
              </div>
            </Card>
          )}

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-semibold mb-2">File Requirements:</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div>• Column 1: GL Code (e.g., 1000, 1100, 1110)</div>
              <div>• Column 2: Description (e.g., Assets, Current Assets)</div>
              <div>• Optional: Parent code for hierarchy</div>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <Card className="p-4">
            <h4 className="font-semibold mb-4">Build Your Schema:</h4>
            <div className="space-y-3">
              <div className="flex gap-3">
                <input
                  type="text"
                  placeholder="GL Code"
                  className="flex-1 px-3 py-2 border rounded-lg"
                />
                <input
                  type="text"
                  placeholder="Description"
                  className="flex-2 px-3 py-2 border rounded-lg"
                />
                <Button variant="outline">Add</Button>
              </div>
            </div>
          </Card>
          <div className="text-sm text-gray-600">
            Add your GL codes one by one, specifying parent relationships
          </div>
        </div>
      )}

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => setCurrentStep('structure')}>
          ← Back
        </Button>
        <Button
          onClick={() => setCurrentStep('mapping')}
          disabled={!uploadedSchema && glCodeHierarchy.length === 0}
          className="bg-giki-primary hover:bg-giki-primary-dark"
        >
          Continue to Mapping →
        </Button>
      </div>
    </div>
  );

  const renderMappingStep = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-6">
        <GitBranch size={32} className="text-giki-primary" />
        <div>
          <h3 className="text-2xl font-semibold">Schema Preview & Mapping</h3>
          <p className="text-gray-600">
            Review how AI will map transactions to your schema
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="font-semibold mb-4">GL Code Hierarchy</h4>
          <div className="max-h-96 overflow-y-auto">
            {glCodeHierarchy.map((node) => renderGLCodeNode(node))}
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="font-semibold mb-4">AI Mapping Preview</h4>
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700">
                &ldquo;STARBUCKS COFFEE&rdquo;
              </div>
              <div className="text-sm text-gray-600">
                → 5400 - Meals & Entertainment
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700">
                &ldquo;AMAZON WEB SERVICES&rdquo;
              </div>
              <div className="text-sm text-gray-600">
                → 6200 - Software Subscriptions
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-700">
                &ldquo;UNITED AIRLINES&rdquo;
              </div>
              <div className="text-sm text-gray-600">
                → 5300 - Travel Expenses
              </div>
            </div>
          </div>
        </Card>
      </div>

      {validationResults && (
        <Card className="p-4">
          <div className="flex items-center gap-3 mb-3">
            {validationResults.valid ? (
              <CheckCircle size={20} className="text-success" />
            ) : (
              <AlertCircle size={20} className="text-amber-600" />
            )}
            <h4 className="font-semibold">Schema Validation</h4>
          </div>
          <div className="grid grid-cols-3 gap-4 mb-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-giki-primary">
                {validationResults.stats.totalCodes}
              </div>
              <div className="text-sm text-gray-600">GL Codes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {validationResults.stats.levels}
              </div>
              <div className="text-sm text-gray-600">Hierarchy Levels</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success">
                {validationResults.stats.coverage}%
              </div>
              <div className="text-sm text-gray-600">Coverage</div>
            </div>
          </div>
          {validationResults.warnings.length > 0 && (
            <div className="text-sm text-amber-700 bg-amber-50 p-2 rounded">
              {validationResults.warnings[0]}
            </div>
          )}
        </Card>
      )}

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => setCurrentStep('schema')}>
          ← Back
        </Button>
        <Button
          onClick={() => void processSchemaWithAI()}
          className="bg-giki-primary hover:bg-giki-primary-dark"
        >
          Apply Schema →
        </Button>
      </div>
    </div>
  );

  const renderValidationStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Shield
          size={48}
          className="mx-auto mb-4 text-giki-primary animate-pulse"
        />
        <h3 className="text-2xl font-semibold mb-2">
          Validating & Applying Schema
        </h3>
        <p className="text-gray-600">AI is learning your GL code structure</p>
      </div>

      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-medium">Schema Validation</span>
            <CheckCircle size={20} className="text-success" />
          </div>
          <div className="flex items-center justify-between">
            <span className="font-medium">Hierarchy Processing</span>
            <CheckCircle size={20} className="text-success" />
          </div>
          <div className="flex items-center justify-between">
            <span className="font-medium">AI Training</span>
            {isProcessing ? (
              <div className="w-5 h-5 border-2 border-giki-primary border-t-transparent rounded-full animate-spin" />
            ) : (
              <CheckCircle size={20} className="text-success" />
            )}
          </div>
        </div>
      </Card>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-2">
          <AlertCircle size={16} className="text-blue-600" />
          <span className="text-sm text-blue-800">
            AI is mapping your GL codes to transaction patterns
          </span>
        </div>
      </div>
    </div>
  );

  const renderSampleStep = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-6">
        <Upload size={32} className="text-giki-primary" />
        <div>
          <h3 className="text-2xl font-semibold">Test with Sample File</h3>
          <p className="text-gray-600">
            Optional: Upload a sample file to test the schema
          </p>
        </div>
      </div>

      <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center">
        <FileSpreadsheet size={48} className="mx-auto mb-4 text-gray-400" />
        <h4 className="text-xl font-semibold mb-2">
          Upload Sample Transactions
        </h4>
        <p className="text-gray-600 mb-4">
          Test how your schema categorizes real transactions
        </p>
        <input
          type="file"
          accept=".xlsx,.xls,.csv"
          className="hidden"
          id="sample-upload"
        />
        <label
          htmlFor="sample-upload"
          className="inline-block px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 cursor-pointer font-semibold"
        >
          Select Sample File
        </label>
      </div>

      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-semibold mb-2">What to Expect:</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <div>• AI will categorize using your GL code schema</div>
          <div>• You&rsquo;ll see accuracy metrics and results</div>
          <div>• Make adjustments if needed</div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => setCurrentStep('mapping')}>
          ← Back
        </Button>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => {
              setCurrentStep('complete');
              onComplete({
                schemaType,
                glCodeCount: validationResults?.stats.totalCodes || 0,
                accuracy: 95,
              });
            }}
          >
            Skip Sample
          </Button>
          <Button className="bg-giki-primary hover:bg-giki-primary-dark">
            Process Sample →
          </Button>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (currentStep) {
      case 'welcome':
        return renderWelcomeStep();
      case 'structure':
        return renderStructureStep();
      case 'schema':
        return renderSchemaStep();
      case 'mapping':
        return renderMappingStep();
      case 'validation':
        return renderValidationStep();
      case 'sample':
        return renderSampleStep();
      default:
        return null;
    }
  };

  return (
    <div className="bg-white p-8 rounded-xl border border-gray-200">
      {renderStepIndicator()}
      {renderContent()}
    </div>
  );
};

export default SchemaGuidedFlow;
