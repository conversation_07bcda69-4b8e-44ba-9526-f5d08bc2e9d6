import React, { useState, useCallback } from 'react';
import {
  Upload,
  FileText,
  Plus,
  Check,
  AlertCircle,
  TreePine,
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';

interface SchemaCategory {
  id: string;
  name: string;
  code?: string;
  parent?: string;
  children?: SchemaCategory[];
}

interface SchemaUploadInterfaceProps {
  onSchemaComplete: (schema: SchemaCategory[]) => void;
  onBack: () => void;
}

export const SchemaUploadInterface: React.FC<SchemaUploadInterfaceProps> = ({
  onSchemaComplete,
  onBack,
}) => {
  const [uploadMode, setUploadMode] = useState<'file' | 'manual'>('file');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [schema, setSchema] = useState<SchemaCategory[]>([]);
  const [manualCategory, setManualCategory] = useState({ name: '', code: '' });
  const [selectedParent, setSelectedParent] = useState<string>('');

  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        setUploadedFile(file);
        setIsProcessing(true);

        // Simulate processing
        setTimeout(() => {
          setIsProcessing(false);
          // Mock parsed schema
          const mockSchema: SchemaCategory[] = [
            {
              id: '1',
              name: 'Operating Expenses',
              code: '5000',
              children: [
                {
                  id: '11',
                  name: 'Office Supplies',
                  code: '5100',
                  parent: '1',
                },
                {
                  id: '12',
                  name: 'Travel & Entertainment',
                  code: '5200',
                  parent: '1',
                },
              ],
            },
            {
              id: '2',
              name: 'Revenue',
              code: '4000',
              children: [
                { id: '21', name: 'Product Sales', code: '4100', parent: '2' },
                {
                  id: '22',
                  name: 'Service Revenue',
                  code: '4200',
                  parent: '2',
                },
              ],
            },
          ];
          setSchema(mockSchema);
        }, 2000);
      }
    },
    [],
  );

  const handleManualAdd = useCallback(() => {
    if (manualCategory.name) {
      const newCategory: SchemaCategory = {
        id: Date.now().toString(),
        name: manualCategory.name,
        code: manualCategory.code || undefined,
        parent: selectedParent || undefined,
      };

      if (selectedParent) {
        // Add as child to existing category
        setSchema((prev) =>
          prev.map((cat) => {
            if (cat.id === selectedParent) {
              return {
                ...cat,
                children: [...(cat.children || []), newCategory],
              };
            }
            return cat;
          }),
        );
      } else {
        // Add as top-level category
        setSchema((prev) => [...prev, newCategory]);
      }

      setManualCategory({ name: '', code: '' });
      setSelectedParent('');
    }
  }, [manualCategory, selectedParent]);

  const renderCategoryTree = (categories: SchemaCategory[], level = 0) => {
    return categories.map((category) => (
      <div key={category.id} style={{ marginLeft: `${level * 24}px` }}>
        <div className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg mb-2">
          <div className="flex items-center gap-2">
            <TreePine size={16} style={{ color: '#7C3AED' }} />
            <span className="font-medium text-gray-800">{category.name}</span>
            {category.code && (
              <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                {category.code}
              </span>
            )}
          </div>
        </div>
        {category.children && renderCategoryTree(category.children, level + 1)}
      </div>
    ));
  };

  const getFlatCategories = (
    categories: SchemaCategory[],
  ): SchemaCategory[] => {
    const flat: SchemaCategory[] = [];
    categories.forEach((cat) => {
      flat.push(cat);
      if (cat.children) {
        flat.push(...getFlatCategories(cat.children));
      }
    });
    return flat;
  };

  return (
    <div className="bg-white p-8 rounded-xl border border-gray-200 mb-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">
            Schema Upload Interface
          </h2>
          <p className="text-gray-600">
            Upload your chart of accounts or build your category structure
            manually
          </p>
        </div>
        <button
          onClick={onBack}
          className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          ← Back to Path Selection
        </button>
      </div>

      {/* Mode Selection */}
      <div className="grid grid-cols-2 gap-4 mb-8">
        <button
          onClick={() => setUploadMode('file')}
          className={`p-4 border-2 rounded-xl transition-all ${
            uploadMode === 'file'
              ? 'border-purple-500 bg-purple-50'
              : 'border-gray-200 hover:border-gray-300'
          }`}
        >
          <FileText
            size={24}
            className="mx-auto mb-2"
            style={{ color: '#7C3AED' }}
          />
          <div className="font-semibold text-gray-800">Upload File</div>
          <div className="text-sm text-gray-600">
            Excel/CSV with your schema
          </div>
        </button>

        <button
          onClick={() => setUploadMode('manual')}
          className={`p-4 border-2 rounded-xl transition-all ${
            uploadMode === 'manual'
              ? 'border-purple-500 bg-purple-50'
              : 'border-gray-200 hover:border-gray-300'
          }`}
        >
          <Plus
            size={24}
            className="mx-auto mb-2"
            style={{ color: '#7C3AED' }}
          />
          <div className="font-semibold text-gray-800">Build Manually</div>
          <div className="text-sm text-gray-600">
            Create categories one by one
          </div>
        </button>
      </div>

      {/* File Upload Mode */}
      {uploadMode === 'file' && (
        <div className="space-y-6">
          <div className="border-2 border-dashed border-purple-300 rounded-xl p-8 text-center bg-purple-50">
            <Upload
              size={48}
              className="mx-auto mb-4"
              style={{ color: '#7C3AED' }}
            />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Upload Chart of Accounts
            </h3>
            <p className="text-gray-600 mb-4">
              Excel or CSV file with category names and GL codes
            </p>
            <input
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileUpload}
              className="hidden"
              id="schema-upload"
            />
            <label
              htmlFor="schema-upload"
              className="inline-block px-6 py-3 bg-purple-600 text-white rounded-md hover:bg-purple-700 cursor-pointer"
            >
              Choose File
            </label>
          </div>

          {uploadedFile && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <FileText size={20} style={{ color: '#7C3AED' }} />
                <div className="flex-1">
                  <div className="font-medium text-gray-800">
                    {uploadedFile.name}
                  </div>
                  <div className="text-sm text-gray-600">
                    {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                  </div>
                </div>
                {isProcessing ? (
                  <div className="text-purple-600">Processing...</div>
                ) : (
                  <Check size={20} className="text-success" />
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Manual Mode */}
      {uploadMode === 'manual' && (
        <div className="space-y-6">
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <h3 className="font-semibold text-purple-800 mb-3">
              Add New Category
            </h3>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category Name *
                </label>
                <input
                  type="text"
                  value={manualCategory.name}
                  onChange={(e) =>
                    setManualCategory((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder="e.g., Operating Expenses"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  GL Code (Optional)
                </label>
                <input
                  type="text"
                  value={manualCategory.code}
                  onChange={(e) =>
                    setManualCategory((prev) => ({
                      ...prev,
                      code: e.target.value,
                    }))
                  }
                  placeholder="e.g., 5000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Parent Category
                </label>
                <Select
                  value={selectedParent}
                  onValueChange={(value) => setSelectedParent(value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Top Level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Top Level</SelectItem>
                    {getFlatCategories(schema).map((cat) => (
                      <SelectItem key={cat.id} value={cat.id}>
                        {cat.name} {cat.code && `(${cat.code})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <button
              onClick={handleManualAdd}
              disabled={!manualCategory.name}
              className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400"
            >
              Add Category
            </button>
          </div>
        </div>
      )}

      {/* Schema Preview */}
      {schema.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Schema Preview
          </h3>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-80 overflow-y-auto">
            {renderCategoryTree(schema)}
          </div>

          <div className="flex items-center gap-2 mt-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
            <AlertCircle size={16} style={{ color: '#7C3AED' }} />
            <span className="text-sm text-purple-800">
              AI will use this structure to guide categorization of your
              transactions
            </span>
          </div>

          <div className="flex justify-end mt-6">
            <button
              onClick={() => onSchemaComplete(schema)}
              className="px-6 py-3 bg-purple-600 text-white rounded-md hover:bg-purple-700 font-semibold"
            >
              Configure AI with This Schema →
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SchemaUploadInterface;
