/**
 * Historical File Upload Component - Onboarding Step 2
 * Matches wireframe: docs/wireframes/02-onboarding-journey/04-file-upload.md
 */
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  Upload,
  File,
  CheckCircle,
  X,
  AlertTriangle,
  FileSpreadsheet,
} from 'lucide-react';

interface SelectedFile {
  file: File;
  id: string;
  size: string;
}

interface HistoricalFileUploadProps {
  onFilesUpload?: (files: File[]) => void;
  onCancel?: () => void;
  isUploading?: boolean;
  currentStep?: number;
  totalSteps?: number;
}

const HistoricalFileUpload: React.FC<HistoricalFileUploadProps> = ({
  onFilesUpload,
  onCancel,
  isUploading = false,
  currentStep = 2,
  totalSteps = 5,
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedFiles, setSelectedFiles] = useState<SelectedFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    // Check file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
    ];
    const allowedExtensions = ['.xlsx', '.xls', '.csv'];

    const hasValidType = allowedTypes.includes(file.type);
    const hasValidExtension = allowedExtensions.some((ext) =>
      file.name.toLowerCase().endsWith(ext),
    );

    if (!hasValidType && !hasValidExtension) {
      return 'Only Excel (.xlsx, .xls) and CSV files are accepted';
    }

    // Check file size (50MB limit)
    if (file.size > 50 * 1024 * 1024) {
      return 'File size must be less than 50MB';
    }

    return null;
  };

  const handleFileSelect = useCallback(
    (files: File[]) => {
      const validFiles: SelectedFile[] = [];
      const errors: string[] = [];

      files.forEach((file) => {
        const error = validateFile(file);
        if (error) {
          errors.push(`${file.name}: ${error}`);
        } else {
          validFiles.push({
            file,
            id: `${file.name}-${Date.now()}`,
            size: formatFileSize(file.size),
          });
        }
      });

      if (errors.length > 0) {
        toast({
          title: 'File validation errors',
          description: errors.join('. '),
          variant: 'destructive',
        });
      }

      if (validFiles.length > 0) {
        setSelectedFiles((prev) => [...prev, ...validFiles]);
      }
    },
    [toast],
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);

      const files = Array.from(e.dataTransfer.files);
      handleFileSelect(files);
    },
    [handleFileSelect],
  );

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      handleFileSelect(files);
    }
  };

  const removeFile = (id: string) => {
    setSelectedFiles((prev) => prev.filter((f) => f.id !== id));
  };

  const handleUpload = () => {
    if (selectedFiles.length === 0) {
      toast({
        title: 'No files selected',
        description: 'Please select at least one file to upload',
        variant: 'destructive',
      });
      return;
    }

    const files = selectedFiles.map((sf) => sf.file);
    onFilesUpload?.(files);
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate('/');
    }
  };

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-slate-900">giki.ai</h1>
            <nav className="hidden md:flex items-center space-x-6">
              <span className="text-slate-400">Dashboard</span>
              <span className="text-slate-400">Upload</span>
              <span className="text-slate-400">Transactions</span>
              <span className="text-slate-400">Categories</span>
              <span className="text-slate-400">Reports</span>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 md:px-6 py-8 md:py-12">
        <div className="space-y-8">
          {/* Title */}
          <div className="text-center">
            <h1 className="text-2xl md:text-3xl font-bold text-slate-900 mb-2">
              Upload Your Historical Data
            </h1>
            <p className="text-slate-600">
              Upload your existing transaction files with categories so we can
              learn your patterns
            </p>
          </div>

          {/* Main Upload Card */}
          <Card className="max-w-3xl mx-auto">
            <CardContent className="p-6 md:p-8">
              {/* Upload Area */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 md:p-12 text-center transition-colors cursor-pointer ${
                  isDragging
                    ? 'border-green-600 bg-green-50'
                    : 'border-slate-300 hover:border-green-600 hover:bg-slate-50'
                }`}
                onDrop={handleDrop}
                onDragOver={(e) => e.preventDefault()}
                onDragEnter={() => setIsDragging(true)}
                onDragLeave={() => setIsDragging(false)}
                onClick={() => document.getElementById('file-input')?.click()}
              >
                <FileSpreadsheet className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                <h3 className="text-lg font-semibold text-slate-900 mb-2">
                  □ Drop files here or browse
                </h3>
                <p className="text-slate-600 mb-4">
                  Excel (.xlsx) or CSV files only
                  <br />
                  Must include your category column
                </p>
                <Button
                  variant="outline"
                  className="border-green-600 text-success hover:bg-green-50"
                  onClick={(e) => {
                    e.stopPropagation();
                    document.getElementById('file-input')?.click();
                  }}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Select Files
                </Button>
                <input
                  id="file-input"
                  type="file"
                  multiple
                  accept=".xlsx,.xls,.csv"
                  onChange={handleFileInputChange}
                  className="hidden"
                />
              </div>

              {/* Selected Files */}
              {selectedFiles.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-semibold text-slate-900 mb-4">
                    Files Selected:
                  </h4>
                  <div className="space-y-3">
                    {selectedFiles.map((selectedFile) => (
                      <div
                        key={selectedFile.id}
                        className="flex items-center justify-between p-3 bg-slate-50 rounded-lg border"
                      >
                        <div className="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-success" />
                          <div>
                            <div className="font-medium text-slate-900">
                              {selectedFile.file.name}
                            </div>
                            <div className="text-sm text-slate-500">
                              {selectedFile.size}
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(selectedFile.id)}
                          className="text-slate-500 hover:text-error"
                        >
                          <X className="h-4 w-4" />
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Warning Message */}
              <Alert className="mt-6 border-amber-200 bg-amber-50">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
                <AlertDescription className="text-amber-800">
                  <strong>Remember:</strong> Your files must have categories
                  already assigned. We need these to learn your categorization
                  patterns.
                </AlertDescription>
              </Alert>

              {/* Example Format */}
              <div className="mt-6">
                <h4 className="font-semibold text-slate-900 mb-3">
                  Example format:
                </h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm border border-slate-200 rounded-lg">
                    <thead className="bg-slate-100">
                      <tr>
                        <th className="px-4 py-2 text-left border-r border-slate-200">
                          Date
                        </th>
                        <th className="px-4 py-2 text-left border-r border-slate-200">
                          Description
                        </th>
                        <th className="px-4 py-2 text-left border-r border-slate-200">
                          Amount
                        </th>
                        <th className="px-4 py-2 text-left font-semibold text-purple-700">
                          Category
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-t border-slate-200">
                        <td className="px-4 py-2 border-r border-slate-200">
                          2024-01-15
                        </td>
                        <td className="px-4 py-2 border-r border-slate-200">
                          Office Supplies
                        </td>
                        <td className="px-4 py-2 border-r border-slate-200">
                          150.00
                        </td>
                        <td className="px-4 py-2 font-medium text-green-700">
                          Operations
                        </td>
                      </tr>
                      <tr className="border-t border-slate-200">
                        <td className="px-4 py-2 border-r border-slate-200">
                          2024-01-16
                        </td>
                        <td className="px-4 py-2 border-r border-slate-200">
                          Client Lunch
                        </td>
                        <td className="px-4 py-2 border-r border-slate-200">
                          89.50
                        </td>
                        <td className="px-4 py-2 font-medium text-green-700">
                          Entertainment
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 mt-8 justify-center">
                <Button
                  onClick={handleUpload}
                  disabled={selectedFiles.length === 0 || isUploading}
                  className="bg-green-600 hover:bg-green-700 text-white min-w-[160px]"
                >
                  {isUploading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Uploading...
                    </>
                  ) : (
                    <>Upload Files →</>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isUploading}
                  className="min-w-[160px]"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Progress Indicator */}
          <div className="max-w-3xl mx-auto">
            <div className="flex items-center justify-center space-x-2 text-sm text-slate-600">
              <div className="flex items-center space-x-1">
                {Array.from({ length: totalSteps }, (_, i) => (
                  <div
                    key={i}
                    className={`h-2 w-2 rounded-full ${
                      i < currentStep
                        ? 'bg-green-600'
                        : i === currentStep - 1
                          ? 'bg-green-600'
                          : 'bg-slate-300'
                    }`}
                  />
                ))}
              </div>
              <span>
                Step {currentStep} of {totalSteps}
              </span>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default HistoricalFileUpload;
