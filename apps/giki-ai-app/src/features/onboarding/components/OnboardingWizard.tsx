import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MISSetupIntroduction from '../pages/MISSetupIntroduction';
import MISCompanySetup from '../pages/MISCompanySetup';
import MISDataUpload from '../pages/MISDataUpload';
import { ProgressBar } from '../../../shared/components/ui/ProgressBar';
import { startMISSetup, completeMISSetup } from '../services/misSetupService';

type OnboardingStep =
  | 'introduction'
  | 'company-setup'
  | 'data-upload'
  | 'enhancement-review'
  | 'activation'
  | 'complete'
  | 'error';

interface OnboardingResults {
  setupId: string;
  companyData?: Record<string, unknown>;
  enhancements?: unknown[];
}

interface OnboardingWizardProps {
  onComplete: (results: OnboardingResults) => void;
}

export const OnboardingWizard: React.FC<OnboardingWizardProps> = ({
  onComplete,
}) => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] =
    useState<OnboardingStep>('introduction');
  const [setupId, setSetupId] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [_isLoading, setIsLoading] = useState(false);

  const getProgressPercentage = () => {
    switch (currentStep) {
      case 'introduction':
        return 20;
      case 'company-setup':
        return 40;
      case 'data-upload':
        return 60;
      case 'enhancement-review':
        return 80;
      case 'activation':
        return 90;
      case 'complete':
        return 100;
      case 'error':
        return 0;
      default:
        return 0;
    }
  };

  const _getStepTitle = () => {
    switch (currentStep) {
      case 'introduction':
        return 'Welcome to giki.ai MIS';
      case 'company-setup':
        return 'Company Information';
      case 'data-upload':
        return 'Optional Data Enhancement';
      case 'enhancement-review':
        return 'Review Enhancements';
      case 'activation':
        return 'Activating Your MIS';
      case 'complete':
        return 'MIS Ready!';
      default:
        return 'MIS Setup';
    }
  };

  const handleSetupComplete = useCallback(async () => {
    if (setupId) {
      try {
        await completeMISSetup(setupId);
        setCurrentStep('complete');
      } catch {
        setError('Failed to complete MIS setup');
        setCurrentStep('error');
      }
    }
  }, [setupId]);

  // Completion handler - redirect intelligently based on user data
  const handleFinalCompletion = useCallback(() => {
    navigate('/');
  }, [navigate]);

  // Auto-complete after activation
  useEffect(() => {
    if (currentStep === 'activation') {
      const timer = setTimeout(() => {
        void handleSetupComplete();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [currentStep, handleSetupComplete]);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <img
              src="/images/giki-logo.svg"
              alt="giki.ai logo"
              width="32"
              height="25"
              className="h-8 w-auto"
            />
            <h1 className="text-3xl font-bold text-gray-800">giki.ai Setup</h1>
          </div>
          <p className="text-lg text-gray-600 mb-6">
            Complete MIS setup in 5 minutes with intelligent categorization
          </p>

          {/* Progress Bar */}
          <div className="max-w-md mx-auto">
            <ProgressBar
              value={getProgressPercentage()}
              className="mb-2"
              showLabel={true}
            />
            <div className="text-sm text-gray-500">
              Step {Math.ceil(getProgressPercentage() / 20)} of 5
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {currentStep === 'introduction' && (
            <div className="p-0">
              <MISSetupIntroduction
                onGetStarted={() => setCurrentStep('company-setup')}
              />
            </div>
          )}

          {currentStep === 'company-setup' && (
            <div className="p-0">
              <MISCompanySetup
                onSubmit={async (companyInfo) => {
                  const response = await startMISSetup({ companyInfo });
                  setSetupId(response.setupId);
                  setCurrentStep('data-upload');
                }}
                onBack={() => setCurrentStep('introduction')}
              />
            </div>
          )}

          {currentStep === 'data-upload' && (
            <div className="p-0">
              <MISDataUpload
                setupId={setupId}
                onComplete={(enhancements) => {
                  if (enhancements.length > 0) {
                    setCurrentStep('enhancement-review');
                  } else {
                    setCurrentStep('activation');
                  }
                }}
                onSkip={() => setCurrentStep('activation')}
                onBack={() => setCurrentStep('company-setup')}
              />
            </div>
          )}

          {currentStep === 'enhancement-review' && (
            <div className="p-8 text-center">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Enhancement Opportunities Detected
              </h2>
              <p className="text-gray-600 mb-6">
                We detected ways to improve your categorization with additional data
              </p>
              <button
                onClick={() => setCurrentStep('activation')}
                className="px-8 py-3 text-white rounded-lg hover:opacity-90 font-semibold"
                style={{ backgroundColor: 'var(--giki-primary)' }}
              >
                Apply Enhancements Later →
              </button>
            </div>
          )}

          {currentStep === 'activation' && (
            <div className="p-8 text-center">
              <div className="mb-8">
                <div
                  className="w-16 h-16 mx-auto mb-4 border-4 border-t-transparent rounded-full animate-spin"
                  style={{
                    borderColor: 'var(--giki-primary)',
                    borderTopColor: 'transparent',
                  }}
                ></div>
                <h2 className="text-2xl font-semibold text-gray-800 mb-2">
                  Activating Your MIS
                </h2>
                <p className="text-gray-600">
                  Setting up your complete Management Information System...
                </p>
              </div>

              <div
                className="rounded-lg p-4 max-w-md mx-auto"
                style={{
                  backgroundColor: 'rgba(41, 83, 67, 0.1)',
                  border: '1px solid rgba(41, 83, 67, 0.3)',
                }}
              >
                <div className="text-sm" style={{ color: '#1F2937' }}>
                  <div>Creating industry-specific categories...</div>
                  <div className="text-xs mt-2">
                    50-100 categories • Income/Expense hierarchy • GL codes
                    assigned
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 'complete' && (
            <div className="p-8 text-center">
              <div className="mb-8">
                <div className="w-16 h-16 mx-auto mb-4 bg-success/10 rounded-full flex items-center justify-center">
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    className="text-brand-primary"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <h2 className="text-2xl font-semibold text-gray-800 mb-2">
                  MIS Setup Complete!
                </h2>
                <p className="text-gray-600 mb-6">
                  Your Management Information System is ready for intelligent categorization
                </p>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4 max-w-md mx-auto mb-6">
                  <div className="text-sm text-green-800">
                    Complete MIS with hierarchical categorization, GL codes, and
                    intelligent AI processing
                  </div>
                </div>

                <button
                  onClick={() => void handleFinalCompletion()}
                  className="px-8 py-3 text-white rounded-lg hover:opacity-90 font-semibold"
                  style={{ backgroundColor: 'var(--giki-primary)' }}
                >
                  Go to Dashboard →
                </button>

                <div className="mt-4 text-xs text-gray-600">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="font-semibold text-blue-600">50-100</div>
                      <div>Categories</div>
                    </div>
                    <div>
                      <div className="font-semibold text-success">□</div>
                      <div>Ready</div>
                    </div>
                    <div>
                      <div className="font-semibold text-purple-600">100%</div>
                      <div>GL Codes</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 'error' && (
            <div className="p-8 text-center">
              <div className="mb-8">
                <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    className="text-error"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                </div>
                <h2 className="text-2xl font-semibold text-gray-800 mb-2">
                  Setup Error
                </h2>
                <p className="text-gray-600 mb-6">
                  {error || 'An unexpected error occurred during onboarding'}
                </p>

                <div className="flex gap-4 justify-center">
                  <button
                    onClick={() => setCurrentStep('introduction')}
                    className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 font-semibold"
                  >
                    ← Try Again
                  </button>
                  <button
                    onClick={() => navigate('/')}
                    className="px-6 py-3 text-white rounded-lg hover:opacity-90 font-semibold"
                    style={{ backgroundColor: 'var(--giki-primary)' }}
                  >
                    Skip to Dashboard
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OnboardingWizard;
