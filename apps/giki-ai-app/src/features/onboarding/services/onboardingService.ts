/**
 * Onboarding Service - Backend Integration
 * Handles three-path onboarding system with real API endpoints
 */

import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError, ApiError } from '@/shared/utils/errorHandling';
import { getCurrentUser } from '@/features/auth/services/authService';

// Onboarding types based on actual API response
export interface OnboardingInitResponse {
  tenant_id: number;
  onboarding_type: string;
  onboarding_stage: string;
  is_onboarding_complete: boolean;
  last_activity: string;
  uploaded_files: Array<{
    id: string;
    filename: string;
    size?: number;
    created_at?: string;
  }>;
  total_transactions: number;
  transactions_with_labels: number;
  date_range_start: string;
  date_range_end: string;
  rag_corpus_status: {
    tenant_id: number;
    corpus_exists: boolean;
    corpus_name: string | null;
    created_at: string | null;
    last_updated: string | null;
    total_patterns: number;
    unique_categories: number;
    source_files: Array<{ filename: string; created_at?: string }>;
  };
  validation_runs: Array<{ id: string; created_at: string; accuracy?: number }>;
  latest_validation: {
    id: string;
    accuracy: number;
    created_at: string;
  } | null;
  meets_accuracy_threshold: boolean;
  approved_for_production: boolean;
  approved_at: string | null;
  approval_notes: string | null;
}

export interface OnboardingProgressResponse {
  onboarding_id: string;
  type: 'zero_onboarding' | 'schema_guided' | 'historical_data';
  status: 'initialized' | 'ai_training' | 'accuracy_validation' | 'completed';
  progress: number;
  steps_completed: string[];
  current_step: string;
  next_step: string;
  ai_training_results?: {
    categories_configured: number;
    gl_codes_mapped: number;
    validation_accuracy: number;
  };
}

export interface BusinessContext {
  industry: string;
  company_size: string;
  company_website?: string;
  business_description?: string;
  fiscal_year_end?: string;
  primary_currency?: string;
  accounting_method?: 'accrual' | 'cash';
  existing_accounting_system?: string;
  primary_country?: string;
  tax_jurisdiction?: string;
  business_model?: string;
  revenue_streams?: string[];
  reporting_requirements?: string[];
  gl_code_preference?: '4_digit' | '5_digit' | 'custom';
  department_structure?: string;
}

/**
 * Initialize onboarding process
 */
export const startOnboarding = async (
  onboardingType: 'zero_onboarding' | 'schema_guided' | 'historical_data',
  businessContext?: BusinessContext,
): Promise<OnboardingInitResponse | ApiError> => {
  try {
    // Get current user to access tenant information
    const currentUser = await getCurrentUser();
    if (!currentUser.tenant_id) {
      throw new Error('User tenant information not available');
    }

    // Choose the correct endpoint based on onboarding type
    let endpoint: string;
    let requestBody: Record<string, unknown>;

    if (onboardingType === 'zero_onboarding') {
      // M1 Quick Start - use start-zero endpoint
      endpoint = '/onboarding/start-zero';
      requestBody = {
        tenant_id: currentUser.tenant_id,
        tenant_name: `Tenant ${currentUser.tenant_id}`, // Default tenant name
        contact_email: currentUser.email,
        business_type: businessContext?.industry || 'technology',
        ...(businessContext?.companyWebsite && {
          company_website: businessContext.companyWebsite,
        }),
      };
    } else if (onboardingType === 'schema_guided') {
      // M3 MIS Structure - use start-schema-only endpoint
      endpoint = '/onboarding/start-schema-only';
      requestBody = {
        tenant_id: currentUser.tenant_id,
        tenant_name: `Tenant ${currentUser.tenant_id}`,
        contact_email: currentUser.email,
        ...(businessContext?.industry && {
          business_type: businessContext.industry,
        }),
        ...(businessContext?.companyWebsite && {
          company_website: businessContext.companyWebsite,
        }),
      };
    } else {
      // M2 Historical Data - use start endpoint
      endpoint = '/onboarding/start';
      requestBody = {
        tenant_id: currentUser.tenant_id,
        tenant_name: `Tenant ${currentUser.tenant_id}`,
        contact_email: currentUser.email,
        expected_transaction_volume: 1000, // Default volume
        onboarding_type: 'historical_data',
        ...(businessContext?.industry && {
          business_type: businessContext.industry,
          industry: businessContext.industry,
        }),
        ...(businessContext?.companyWebsite && {
          company_website: businessContext.companyWebsite,
        }),
      };
    }

    const response = await apiClient.post<OnboardingInitResponse>(
      endpoint,
      requestBody,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'startOnboarding',
      defaultMessage: 'Failed to start onboarding process.',
      showToast: false,
    });
  }
};

/**
 * Upload schema file for schema-guided onboarding
 */
export const uploadOnboardingSchema = async (
  onboardingId: string,
  schemaFile: File,
  glCodeFormat: '4_digit' | '5_digit' | 'custom' = '4_digit',
): Promise<{ status: string; schema_processed: boolean } | ApiError> => {
  try {
    const formData = new FormData();
    formData.append('schema_file', schemaFile);
    formData.append('gl_code_format', glCodeFormat);

    const response = await apiClient.postFormData<{
      status: string;
      schema_processed: boolean;
    }>(`/onboarding/${onboardingId}/schema-upload`, formData);

    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'uploadOnboardingSchema',
      defaultMessage: 'Failed to upload schema file.',
      showToast: false,
    });
  }
};

/**
 * Upload historical data for training
 */
export const uploadHistoricalData = async (
  files: File[],
  currency: string = 'USD',
  onProgress?: (progress: number) => void,
): Promise<
  | {
      status: string;
      summary: {
        total_files_processed: number;
        successful_uploads: number;
        failed_uploads: number;
        total_transactions_imported: number;
        rag_corpus_built: boolean;
      };
      upload_results: Array<{
        filename: string;
        status: string;
        transactions_imported: number;
        report_id: string | null;
        error?: string;
      }>;
      message: string;
    }
  | ApiError
> => {
  try {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });
    formData.append('year', new Date().getFullYear().toString());
    formData.append('has_category_labels', 'true');
    formData.append('currency', currency);

    const response = await apiClient.postFormData(
      '/onboarding/batch-upload-files',
      formData,
      {
        onUploadProgress: (() => {
          let lastProgressUpdate = 0;
          return (progressEvent: { loaded?: number; total?: number }) => {
            const now = Date.now();
            if (
              onProgress &&
              progressEvent.total &&
              progressEvent.loaded &&
              now - lastProgressUpdate > 100
            ) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total,
              );
              onProgress(progress);
              lastProgressUpdate = now;
            }
          };
        })(),
        // Note: timeout handled by apiClient configuration
      },
    );

    // Type assertion since we know the expected structure
    return response.data as {
      status: string;
      summary: {
        total_files_processed: number;
        successful_uploads: number;
        failed_uploads: number;
        total_transactions_imported: number;
        rag_corpus_built: boolean;
      };
      upload_results: Array<{
        filename: string;
        status: string;
        transactions_imported: number;
        report_id: string | null;
        error?: string;
      }>;
      message: string;
    };
  } catch (error) {
    return handleApiError(error, {
      context: 'uploadHistoricalData',
      defaultMessage: 'Failed to upload historical data.',
      showToast: false,
    });
  }
};

/**
 * Get onboarding progress
 */
export const getOnboardingProgress = async (
  onboardingId: string,
): Promise<OnboardingProgressResponse | ApiError> => {
  try {
    const response = await apiClient.get<OnboardingProgressResponse>(
      `/onboarding/${onboardingId}/progress`,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getOnboardingProgress',
      defaultMessage: 'Failed to get onboarding progress.',
      showToast: false,
    });
  }
};

/**
 * Complete onboarding and redirect to dashboard
 */
export const completeOnboarding = async (
  onboardingId: string,
): Promise<{ status: string; redirect_url: string } | ApiError> => {
  try {
    const response = await apiClient.post<{
      status: string;
      redirect_url: string;
    }>(`/onboarding/${onboardingId}/complete`);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'completeOnboarding',
      defaultMessage: 'Failed to complete onboarding.',
      showToast: false,
    });
  }
};

/**
 * Poll onboarding status with real-time updates
 */
export const pollOnboardingStatus = (
  onboardingId: string,
  onStatusUpdate: (progress: OnboardingProgressResponse) => void,
  onError: (error: string) => void,
): (() => void) => {
  const pollInterval = setInterval(async () => {
    try {
      const progressResult = await getOnboardingProgress(onboardingId);

      if ('error' in progressResult) {
        const errorResult = progressResult as ApiError;
        onError(errorResult.message || 'Failed to get progress');
        clearInterval(pollInterval);
        return;
      }

      // Type guard to ensure we have OnboardingProgressResponse
      if ('onboarding_id' in progressResult && 'status' in progressResult) {
        onStatusUpdate(progressResult);

        // Stop polling when completed
        if (progressResult.status === 'completed') {
          clearInterval(pollInterval);
        }
      }
    } catch (_error) {
      onError('Network error while checking progress');
      clearInterval(pollInterval);
    }
  }, 2000); // Poll every 2 seconds

  // Return cleanup function
  return () => clearInterval(pollInterval);
};

/**
 * Validate uploaded files before onboarding
 */
export const validateOnboardingFiles = (
  files: File[],
  onboardingType: 'historical_data' | 'schema_guided',
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check file count
  if (files.length === 0) {
    errors.push('At least one file is required');
  }

  if (files.length > 10) {
    errors.push('Maximum 10 files allowed');
  }

  // Check file types
  const allowedExtensions = ['csv', 'xlsx', 'xls'];
  const invalidFiles = files.filter((file) => {
    const extension = file.name.split('.').pop()?.toLowerCase();
    return !extension || !allowedExtensions.includes(extension);
  });

  if (invalidFiles.length > 0) {
    errors.push(
      `Unsupported file formats: ${invalidFiles.map((f) => f.name).join(', ')}`,
    );
  }

  // Check file sizes (50MB max per file)
  const maxSize = 50 * 1024 * 1024;
  const oversizedFiles = files.filter((file) => file.size > maxSize);

  if (oversizedFiles.length > 0) {
    errors.push(
      `Files too large (max 50MB): ${oversizedFiles.map((f) => f.name).join(', ')}`,
    );
  }

  // Specific validation for schema files
  if (onboardingType === 'schema_guided') {
    if (files.length > 1) {
      errors.push('Schema upload supports only one file');
    }

    // Check if filename suggests it contains schema/GL codes
    const schemaKeywords = ['schema', 'chart', 'accounts', 'gl', 'category'];
    const hasSchemaName = files.some((file) =>
      schemaKeywords.some((keyword) =>
        file.name.toLowerCase().includes(keyword),
      ),
    );

    if (!hasSchemaName) {
      // Warning, not error
      console.warn('File name does not suggest it contains schema/GL codes');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Save business context for enhanced MIS categorization
 */
export const saveBusinessContext = async (
  businessContext: BusinessContext,
): Promise<{ success: boolean; message?: string } | ApiError> => {
  try {
    const response = await apiClient.post<{
      success: boolean;
      message: string;
      business_context_saved: boolean;
      onboarding_status: OnboardingInitResponse;
    }>('/onboarding/business-context', businessContext);

    return {
      success: response.data.success,
      message: response.data.message,
    };
  } catch (error) {
    return handleApiError(error, {
      context: 'saveBusinessContext',
      defaultMessage: 'Failed to save business context.',
      showToast: false,
    });
  }
};

/**
 * Get business context for a tenant
 */
export const getBusinessContext = async (): Promise<
  BusinessContext | ApiError
> => {
  try {
    const response = await apiClient.get<{
      success: boolean;
      business_context: BusinessContext;
    }>('/onboarding/business-context');

    return response.data.business_context;
  } catch (error) {
    return handleApiError(error, {
      context: 'getBusinessContext',
      defaultMessage: 'Failed to get business context.',
      showToast: false,
    });
  }
};
