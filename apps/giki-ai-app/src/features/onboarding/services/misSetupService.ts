import { apiClient } from '@/shared/services/api/apiClient';

export interface CompanyInfo {
  name: string;
  industry: string;
  size: string;
  fiscal_year_end: string;
  default_currency: string;
}

export interface MISSetupRequest {
  company_info: CompanyInfo;
  uploaded_files?: string[];
}

export interface MISSetupResponse {
  setupId: string;
  status: 'active' | 'pending';
  enhancementOpportunities: Enhancement[];
  baselineAccuracy: number;
}

export interface Enhancement {
  type: 'historical' | 'schema' | 'vendor';
  title: string;
  description: string;
  accuracyGain: string;
  timeEstimate: string;
  priority: 'recommended' | 'highly recommended' | 'optional';
  confidence: number;
  recordCount?: number;
}

export interface EnhancementRequest {
  type: 'historical' | 'schema';
  files: File[];
  applyRetrospectively?: boolean;
}

export interface EnhancementResponse {
  status: 'completed' | 'processing' | 'failed';
  accuracyImprovement: number;
  transactionsUpdated?: number;
}

/**
 * Start unified MIS setup
 */
export async function startMISSetup(
  request: MISSetupRequest,
): Promise<MISSetupResponse> {
  const response = await apiClient.post('/onboarding/mis/setup', request);
  return response.data;
}

/**
 * Upload files for enhancement detection
 */
export async function uploadFiles(files: File[]): Promise<string[]> {
  const formData = new FormData();
  files.forEach((file) => formData.append('files', file));

  const response = await apiClient.post('/files/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data.fileIds;
}

/**
 * Detect enhancement opportunities from uploaded file
 */
export async function detectEnhancements(file: File): Promise<Enhancement[]> {
  const formData = new FormData();
  formData.append('file', file);

  const response = await apiClient.post(
    '/onboarding/mis/detect-enhancements',
    formData,
  );

  return response.data.enhancements;
}

/**
 * Apply enhancement to MIS setup
 */
export async function applyEnhancement(
  setupId: string,
  enhancement: EnhancementRequest,
): Promise<EnhancementResponse> {
  const formData = new FormData();
  formData.append('type', enhancement.type);
  formData.append(
    'applyRetrospectively',
    String(enhancement.applyRetrospectively || false),
  );

  enhancement.files.forEach((file) => formData.append('files', file));

  const response = await apiClient.post(
    `/onboarding/mis/enhance/${setupId}`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );

  return response.data;
}

/**
 * Get MIS setup status
 */
export async function getMISSetupStatus(setupId: string) {
  const response = await apiClient.get(`/onboarding/mis/setup/${setupId}`);
  return response.data;
}

/**
 * Complete MIS setup and activate
 */
export async function completeMISSetup(setupId: string) {
  const response = await apiClient.post(
    `/onboarding/mis/setup/${setupId}/complete`,
  );
  return response.data;
}

/**
 * Get enhancement recommendations based on current setup
 */
export async function getEnhancementRecommendations() {
  const response = await apiClient.get(
    '/onboarding/mis/enhancement-recommendations',
  );
  return response.data.recommendations;
}
