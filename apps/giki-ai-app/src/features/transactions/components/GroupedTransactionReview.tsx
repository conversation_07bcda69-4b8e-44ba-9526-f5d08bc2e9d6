/**
 * Grouped Transaction Review Component
 * 
 * Implements the grouped interface from mockup design for efficient bulk review
 * Groups similar transactions for bulk actions based on confidence levels
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { 
  ChevronDown, 
  ChevronUp, 
  CheckCircle, 
  XCircle,
  Users,
  DollarSign,
  Calendar,
  Eye,
  Filter
} from 'lucide-react';
import { ConfidenceIndicator, ConfidenceSummary } from './ConfidenceIndicator';
import { type Transaction } from '@/shared/types/categorization';
import { getConfidenceLevel } from '../services/transactionService';

interface TransactionGroup {
  id: string;
  pattern: string;
  transactions: Transaction[];
  confidence: number;
  totalAmount: number;
  suggestedCategory: string;
  suggestedCategoryId: number;
  canBulkApprove: boolean;
}

interface GroupedTransactionReviewProps {
  transactions: Transaction[];
  onGroupAction: (groupId: string, action: 'approve' | 'reject' | 'customize') => void;
  onTransactionAction: (transactionId: string, action: 'approve' | 'reject' | 'customize') => void;
  onBulkAction: (transactionIds: string[], action: 'approve' | 'reject' | 'customize') => void;
  isLoading?: boolean;
  className?: string;
}

/**
 * Group transactions by similarity pattern for bulk review
 */
function groupTransactionsByPattern(transactions: Transaction[]): TransactionGroup[] {
  const groups: Map<string, TransactionGroup> = new Map();
  
  transactions.forEach(transaction => {
    // Create a simple pattern based on description and amount similarity
    const pattern = extractPattern(transaction.description || '');
    const confidence = transaction.ai_category_confidence || 0;
    
    if (!groups.has(pattern)) {
      groups.set(pattern, {
        id: `group_${pattern}_${Date.now()}`,
        pattern,
        transactions: [],
        confidence: 0,
        totalAmount: 0,
        suggestedCategory: transaction.category_path || transaction.ai_suggested_category_path || transaction.ai_suggested_category || 'Business Expense',
        suggestedCategoryId: transaction.category_id || 0,
        canBulkApprove: false,
      });
    }
    
    const group = groups.get(pattern)!;
    group.transactions.push(transaction);
    group.totalAmount += transaction.amount || 0;
  });
  
  // Calculate group statistics
  groups.forEach(group => {
    group.confidence = group.transactions.reduce((sum, t) => sum + (t.ai_category_confidence || 0), 0) / group.transactions.length;
    group.canBulkApprove = group.confidence >= 0.8 && group.transactions.length >= 2;
  });
  
  return Array.from(groups.values()).sort((a, b) => b.confidence - a.confidence);
}

/**
 * Extract pattern from transaction description
 */
function extractPattern(description: string): string {
  // Simple pattern extraction - remove numbers and dates
  return description
    .replace(/\d+/g, '')
    .replace(/\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b/gi, '')
    .replace(/\s+/g, ' ')
    .trim()
    .toLowerCase()
    .substring(0, 50);
}

export const GroupedTransactionReview: React.FC<GroupedTransactionReviewProps> = ({
  transactions,
  onGroupAction,
  onTransactionAction,
  onBulkAction,
  isLoading = false,
  className = "",
}) => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [selectedGroups, setSelectedGroups] = useState<Set<string>>(new Set());
  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'grouped' | 'individual'>('grouped');
  const [confidenceFilter, setConfidenceFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');

  // Group transactions by pattern
  const transactionGroups = useMemo(() => {
    const groups = groupTransactionsByPattern(transactions);
    
    // Apply confidence filter
    if (confidenceFilter !== 'all') {
      return groups.filter(group => {
        const level = getConfidenceLevel(group.confidence);
        return level === confidenceFilter;
      });
    }
    
    return groups;
  }, [transactions, confidenceFilter]);

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  const toggleGroupSelection = (groupId: string) => {
    setSelectedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  const handleGroupAction = (groupId: string, action: 'approve' | 'reject' | 'customize') => {
    onGroupAction(groupId, action);
  };

  const handleBulkApprove = () => {
    const selectedGroupTransactions = transactionGroups
      .filter(group => selectedGroups.has(group.id))
      .flatMap(group => group.transactions)
      .map(t => t.id);
    
    onBulkAction(selectedGroupTransactions, 'approve');
    setSelectedGroups(new Set());
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="h-20 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold">Review Transactions</h2>
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grouped' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grouped')}
            >
              <Users className="h-4 w-4 mr-1" />
              Grouped
            </Button>
            <Button
              variant={viewMode === 'individual' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('individual')}
            >
              <Eye className="h-4 w-4 mr-1" />
              Individual
            </Button>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <select
            value={confidenceFilter}
            onChange={(e) => setConfidenceFilter(e.target.value as any)}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="all">All Confidence</option>
            <option value="high">High Confidence</option>
            <option value="medium">Medium Confidence</option>
            <option value="low">Low Confidence</option>
          </select>
          
          {selectedGroups.size > 0 && (
            <Button
              onClick={handleBulkApprove}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              Approve Selected ({selectedGroups.size})
            </Button>
          )}
        </div>
      </div>

      {/* Confidence Summary */}
      <ConfidenceSummary
        confidence={transactions.length > 0 
          ? transactions.reduce((sum, t) => sum + (t.ai_category_confidence || 0), 0) / transactions.length
          : 0}
        title="Review Progress"
        description={`${transactions.length} transactions ready for review`}
      />

      {/* Grouped View */}
      {viewMode === 'grouped' && (
        <div className="space-y-4">
          {transactionGroups.map(group => (
            <Card key={group.id} className="border-l-4 border-l-blue-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={selectedGroups.has(group.id)}
                      onCheckedChange={() => toggleGroupSelection(group.id)}
                    />
                    <div>
                      <CardTitle className="text-lg">
                        {group.pattern || 'Similar Transactions'}
                      </CardTitle>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                        <span className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {group.transactions.length} transactions
                        </span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          ${Math.abs(group.totalAmount).toFixed(2)}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(group.transactions[0].date).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <ConfidenceIndicator
                      confidence={group.confidence}
                      size="sm"
                      showText={true}
                    />
                    <Badge variant="outline" className="text-xs">
                      {group.suggestedCategory}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleGroupExpansion(group.id)}
                    >
                      {expandedGroups.has(group.id) ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                {/* Group Actions */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleGroupAction(group.id, 'approve')}
                      disabled={!group.canBulkApprove}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Approve All
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleGroupAction(group.id, 'reject')}
                    >
                      <XCircle className="h-4 w-4 mr-1" />
                      Reject All
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleGroupAction(group.id, 'customize')}
                    >
                      <Filter className="h-4 w-4 mr-1" />
                      Customize
                    </Button>
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    {group.canBulkApprove ? (
                      <span className="text-success">Ready for bulk approval</span>
                    ) : (
                      <span className="text-yellow-600">Review recommended</span>
                    )}
                  </div>
                </div>

                {/* Expanded Transaction List */}
                {expandedGroups.has(group.id) && (
                  <div className="space-y-2 border-t pt-4">
                    {group.transactions.map(transaction => (
                      <div
                        key={transaction.id}
                        className="flex items-center justify-between p-3 border rounded-lg bg-gray-50"
                      >
                        <div className="flex items-center gap-3">
                          <Checkbox
                            checked={selectedTransactions.has(transaction.id)}
                            onCheckedChange={(checked) => {
                              setSelectedTransactions(prev => {
                                const newSet = new Set(prev);
                                if (checked) {
                                  newSet.add(transaction.id);
                                } else {
                                  newSet.delete(transaction.id);
                                }
                                return newSet;
                              });
                            }}
                          />
                          <div>
                            <div className="font-medium">{transaction.description}</div>
                            <div className="text-sm text-gray-600">
                              {new Date(transaction.date).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          <div className="text-right">
                            <div className="font-medium">
                              ${Math.abs(transaction.amount || 0).toFixed(2)}
                            </div>
                            <ConfidenceIndicator
                              confidence={transaction.ai_category_confidence}
                              size="sm"
                            />
                          </div>
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onTransactionAction(transaction.id, 'approve')}
                            >
                              <CheckCircle className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onTransactionAction(transaction.id, 'reject')}
                            >
                              <XCircle className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Individual View */}
      {viewMode === 'individual' && (
        <div className="space-y-3">
          {transactions.map(transaction => (
            <Card key={transaction.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={selectedTransactions.has(transaction.id)}
                    onCheckedChange={(checked) => {
                      setSelectedTransactions(prev => {
                        const newSet = new Set(prev);
                        if (checked) {
                          newSet.add(transaction.id);
                        } else {
                          newSet.delete(transaction.id);
                        }
                        return newSet;
                      });
                    }}
                  />
                  <div>
                    <div className="font-medium">{transaction.description}</div>
                    <div className="text-sm text-gray-600">
                      {new Date(transaction.date).toLocaleDateString()} • {transaction.category_path || transaction.ai_suggested_category_path || transaction.ai_suggested_category || 'Business Expense'}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="font-medium">
                      ${Math.abs(transaction.amount || 0).toFixed(2)}
                    </div>
                    <ConfidenceIndicator
                      confidence={transaction.ai_category_confidence}
                      size="sm"
                      variant="minimal"
                    />
                  </div>
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      onClick={() => onTransactionAction(transaction.id, 'approve')}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onTransactionAction(transaction.id, 'reject')}
                    >
                      <XCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default GroupedTransactionReview;