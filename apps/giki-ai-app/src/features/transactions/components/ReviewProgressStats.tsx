/**
 * Review Progress Stats Component
 * Real-time statistics and progress indicators for transaction review workflow
 */
import React, { useMemo } from 'react';
import {
  TrendingUp,
  CheckCircle2,
  AlertTriangle,
  Target,
  Users,
  BarChart3,
  Zap,
} from 'lucide-react';
import { Card, CardContent } from '@/shared/components/ui/card';
import { type Transaction } from '@/shared/types/categorization';
import {
  formatConfidence,
  getConfidenceLevel,
  needsReview,
} from '../services/transactionService';

interface ReviewProgressStatsProps {
  transactions: Transaction[];
  totalTransactions?: number;
  className?: string;
  showDetailed?: boolean;
  timeRange?: string;
}

export const ReviewProgressStats: React.FC<ReviewProgressStatsProps> = ({
  transactions,
  totalTransactions,
  className = '',
  showDetailed = false,
  timeRange = 'This Month',
}) => {
  // Calculate comprehensive statistics
  const stats = useMemo(() => {
    const total = transactions.length;
    const totalAmount = transactions.reduce(
      (sum, t) => sum + Math.abs(t.amount),
      0,
    );

    // Review status counts
    const needsReviewCount = transactions.filter((t) =>
      needsReview(t, 0.85),
    ).length;
    const highConfidenceCount = transactions.filter(
      (t) => t.ai_category_confidence && t.ai_category_confidence >= 0.9,
    ).length;
    const userModifiedCount = transactions.filter(
      (t) => t.is_user_modified,
    ).length;
    const uncategorizedCount = transactions.filter(
      (t) => !t.category_id && !t.ai_suggested_category_id,
    ).length;

    // Confidence distribution
    const highConfidence = transactions.filter(
      (t) => getConfidenceLevel(t.ai_category_confidence) === 'high',
    ).length;
    const mediumConfidence = transactions.filter(
      (t) => getConfidenceLevel(t.ai_category_confidence) === 'medium',
    ).length;
    const lowConfidence = transactions.filter(
      (t) => getConfidenceLevel(t.ai_category_confidence) === 'low',
    ).length;

    // Average confidence
    const confidenceValues = transactions
      .map((t) => t.ai_category_confidence)
      .filter((c) => c !== undefined && c !== null);
    const avgConfidence =
      confidenceValues.length > 0
        ? confidenceValues.reduce((sum, c) => sum + c, 0) /
          confidenceValues.length
        : 0;

    // Progress calculations
    const reviewProgress =
      total > 0 ? ((total - needsReviewCount) / total) * 100 : 0;
    const completionRate =
      total > 0 ? ((userModifiedCount + highConfidenceCount) / total) * 100 : 0;

    return {
      total,
      totalAmount,
      needsReviewCount,
      highConfidenceCount,
      userModifiedCount,
      uncategorizedCount,
      highConfidence,
      mediumConfidence,
      lowConfidence,
      avgConfidence,
      reviewProgress,
      completionRate,
      processed: total - uncategorizedCount,
    };
  }, [transactions]);

  // Main metrics for quick overview
  const mainMetrics = [
    {
      label: 'Needs Review',
      value: stats.needsReviewCount,
      total: stats.total,
      icon: AlertTriangle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      progress:
        stats.total > 0 ? (stats.needsReviewCount / stats.total) * 100 : 0,
    },
    {
      label: 'High Confidence',
      value: stats.highConfidenceCount,
      total: stats.total,
      icon: CheckCircle2,
      color: 'text-success',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      progress:
        stats.total > 0 ? (stats.highConfidenceCount / stats.total) * 100 : 0,
    },
    {
      label: 'User Modified',
      value: stats.userModifiedCount,
      total: stats.total,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      progress:
        stats.total > 0 ? (stats.userModifiedCount / stats.total) * 100 : 0,
    },
    {
      label: 'Avg Confidence',
      value: formatConfidence(stats.avgConfidence),
      displayValue: formatConfidence(stats.avgConfidence),
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      progress: stats.avgConfidence * 100,
    },
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Review Progress
          </h3>
          <p className="text-sm text-gray-600">
            {timeRange} • {stats.total} transactions
          </p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900">
            ${stats.totalAmount.toLocaleString()}
          </div>
          <div className="text-sm text-gray-600">Total Value</div>
        </div>
      </div>

      {/* Main Progress Indicators */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {mainMetrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <Card key={index} className={`${metric.borderColor}`}>
              <CardContent className={`p-4 ${metric.bgColor}`}>
                <div className="flex items-center gap-2 mb-2">
                  <Icon className={`h-4 w-4 ${metric.color}`} />
                  <span className="text-sm font-medium text-gray-700">
                    {metric.label}
                  </span>
                </div>
                <div className={`text-2xl font-bold ${metric.color}`}>
                  {metric.displayValue || metric.value}
                </div>
                {metric.total && (
                  <div className="text-xs text-gray-600 mt-1">
                    of {metric.total} total
                  </div>
                )}
                {/* Progress bar */}
                <div className="w-full bg-gray-200 rounded-full h-1 mt-2">
                  <div
                    className={`h-1 rounded-full transition-all duration-300 ${metric.color
                      .replace('text-', 'bg-')
                      .replace('-600', '-500')}`}
                    style={{ width: `${Math.min(100, metric.progress)}%` }}
                  />
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Detailed Statistics */}
      {showDetailed && (
        <>
          {/* Confidence Distribution */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <BarChart3 className="h-5 w-5 text-gray-600" />
                <h4 className="font-semibold text-gray-900">
                  Confidence Distribution
                </h4>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-success">
                    {stats.highConfidence}
                  </div>
                  <div className="text-sm text-gray-600">High (≥90%)</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{
                        width: `${stats.total > 0 ? (stats.highConfidence / stats.total) * 100 : 0}%`,
                      }}
                    />
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {stats.mediumConfidence}
                  </div>
                  <div className="text-sm text-gray-600">Medium (70-89%)</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className="bg-yellow-500 h-2 rounded-full"
                      style={{
                        width: `${stats.total > 0 ? (stats.mediumConfidence / stats.total) * 100 : 0}%`,
                      }}
                    />
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-error">
                    {stats.lowConfidence}
                  </div>
                  <div className="text-sm text-gray-600">Low (&lt;70%)</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className="bg-red-500 h-2 rounded-full"
                      style={{
                        width: `${stats.total > 0 ? (stats.lowConfidence / stats.total) * 100 : 0}%`,
                      }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Overall Progress */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <Target className="h-5 w-5 text-gray-600" />
                <h4 className="font-semibold text-gray-900">
                  Overall Progress
                </h4>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Review Progress</span>
                    <span className="font-medium">
                      {stats.reviewProgress.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${stats.reviewProgress}%` }}
                    />
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {stats.total - stats.needsReviewCount} of {stats.total}{' '}
                    reviewed
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Completion Rate</span>
                    <span className="font-medium">
                      {stats.completionRate.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-green-500 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${stats.completionRate}%` }}
                    />
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {stats.userModifiedCount + stats.highConfidenceCount} of{' '}
                    {stats.total} completed
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Quick Actions Summary */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Zap className="h-5 w-5 text-blue-600" />
              <div>
                <h4 className="font-medium text-blue-900">Ready for Action</h4>
                <p className="text-sm text-blue-700">
                  {stats.needsReviewCount > 0
                    ? `${stats.needsReviewCount} transactions need your review`
                    : 'All transactions reviewed! Ready for export.'}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold text-blue-900">
                {((stats.reviewProgress + stats.completionRate) / 2).toFixed(0)}
                %
              </div>
              <div className="text-xs text-blue-600">Overall Score</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Compact Stats Component
 * Minimal stats display for smaller spaces
 */
interface CompactStatsProps {
  transactions: Transaction[];
  className?: string;
}

export const CompactReviewStats: React.FC<CompactStatsProps> = ({
  transactions,
  className = '',
}) => {
  const needsReviewCount = transactions.filter((t) =>
    needsReview(t, 0.85),
  ).length;
  const highConfidenceCount = transactions.filter(
    (t) => t.ai_category_confidence && t.ai_category_confidence >= 0.9,
  ).length;
  const total = transactions.length;

  return (
    <div className={`flex items-center gap-4 ${className}`}>
      <div className="flex items-center gap-1">
        <AlertTriangle className="h-4 w-4 text-yellow-600" />
        <span className="text-sm font-medium text-yellow-800">
          {needsReviewCount}
        </span>
        <span className="text-xs text-gray-600">need review</span>
      </div>

      <div className="flex items-center gap-1">
        <CheckCircle2 className="h-4 w-4 text-success" />
        <span className="text-sm font-medium text-green-800">
          {highConfidenceCount}
        </span>
        <span className="text-xs text-gray-600">high confidence</span>
      </div>

      <div className="flex items-center gap-1">
        <BarChart3 className="h-4 w-4 text-blue-600" />
        <span className="text-sm font-medium text-blue-800">{total}</span>
        <span className="text-xs text-gray-600">total</span>
      </div>
    </div>
  );
};

export default ReviewProgressStats;
