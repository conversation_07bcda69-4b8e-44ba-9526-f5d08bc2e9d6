/**
 * Enhancement Option Cards - Interactive MIS improvement options
 * 
 * Provides visual cards for different enhancement types with progress tracking
 * and accuracy improvement indicators (89% → 95%)
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import { 
  CheckCircle, 
  Clock, 
  FileText, 
  Building, 
  Settings, 
  ArrowRight,
  Sparkles,
  BarChart3,
  Target
} from 'lucide-react';

interface EnhancementOption {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  accuracyImprovement: number; // Percentage improvement
  timeEstimate: string;
  difficulty: 'easy' | 'medium' | 'advanced';
  status: 'available' | 'in_progress' | 'completed' | 'locked';
  currentAccuracy: number;
  targetAccuracy: number;
  benefits: string[];
  requirements?: string[];
}

interface EnhancementOptionCardsProps {
  currentAccuracy: number;
  onSelectEnhancement: (enhancementId: string) => void;
  onStartEnhancement: (enhancementId: string) => void;
  completedEnhancements: string[];
  inProgressEnhancements: string[];
}

export const EnhancementOptionCards: React.FC<EnhancementOptionCardsProps> = ({
  currentAccuracy,
  onSelectEnhancement,
  onStartEnhancement,
  completedEnhancements,
  inProgressEnhancements,
}) => {
  const [selectedCard, setSelectedCard] = useState<string | null>(null);

  const enhancementOptions: EnhancementOption[] = [
    {
      id: 'quick-review',
      title: 'Quick Review',
      description: 'Review and approve medium-confidence categorizations in bulk',
      icon: <CheckCircle className="h-6 w-6" />,
      accuracyImprovement: 8,
      timeEstimate: '5-10 minutes',
      difficulty: 'easy',
      status: 'available',
      currentAccuracy: currentAccuracy,
      targetAccuracy: Math.min(95, currentAccuracy + 8),
      benefits: [
        'Immediate accuracy boost',
        'Bulk approval workflow',
        'Review medium-confidence transactions',
        'Quick wins for common patterns'
      ],
      requirements: ['Transactions with 70-89% confidence available']
    },
    {
      id: 'company-profile',
      title: 'Company Profile',
      description: 'Provide business context to improve industry-specific categorization',
      icon: <Building className="h-6 w-6" />,
      accuracyImprovement: 15,
      timeEstimate: '15-20 minutes',
      difficulty: 'medium',
      status: completedEnhancements.includes('company-profile') ? 'completed' : 'available',
      currentAccuracy: currentAccuracy,
      targetAccuracy: Math.min(95, currentAccuracy + 15),
      benefits: [
        'Industry-specific categories',
        'Business context awareness',
        'Vendor pattern recognition',
        'Seasonal adjustment capability'
      ],
      requirements: ['Business information', 'Industry classification']
    },
    {
      id: 'category-rules',
      title: 'Category Rules',
      description: 'Create custom rules for specific vendors and transaction patterns',
      icon: <Settings className="h-6 w-6" />,
      accuracyImprovement: 12,
      timeEstimate: '10-15 minutes',
      difficulty: 'advanced',
      status: completedEnhancements.includes('category-rules') ? 'completed' : 'available',
      currentAccuracy: currentAccuracy,
      targetAccuracy: Math.min(95, currentAccuracy + 12),
      benefits: [
        'Custom vendor mappings',
        'Rule-based categorization',
        'Exception handling',
        'Future-proof accuracy'
      ],
      requirements: ['Identified vendor patterns', 'Custom categorization needs']
    },
    {
      id: 'historical-data',
      title: 'Historical Data',
      description: 'Upload historical transactions to improve pattern recognition',
      icon: <FileText className="h-6 w-6" />,
      accuracyImprovement: 20,
      timeEstimate: '20-30 minutes',
      difficulty: 'medium',
      status: completedEnhancements.includes('historical-data') ? 'completed' : 'available',
      currentAccuracy: currentAccuracy,
      targetAccuracy: Math.min(95, currentAccuracy + 20),
      benefits: [
        'Pattern learning from history',
        'Seasonal trend recognition',
        'Vendor behavior analysis',
        'Comprehensive accuracy boost'
      ],
      requirements: ['Historical transaction files', 'Data upload capability']
    }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-orange-100 text-orange-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'locked': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <Sparkles className="h-4 w-4" />;
      case 'in_progress': return <Clock className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'locked': return <Settings className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const handleCardClick = (option: EnhancementOption) => {
    if (option.status === 'locked') return;
    
    setSelectedCard(selectedCard === option.id ? null : option.id);
    onSelectEnhancement(option.id);
  };

  const handleStartEnhancement = (optionId: string) => {
    onStartEnhancement(optionId);
    setSelectedCard(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h3 className="text-2xl font-semibold text-brand-primary mb-2">
          Improve Your MIS Accuracy
        </h3>
        <p className="text-gray-600">
          Choose enhancement options to boost your categorization accuracy from {currentAccuracy}% to 95%+
        </p>
      </div>

      {/* Enhancement Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {enhancementOptions.map((option) => (
          <Card 
            key={option.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedCard === option.id ? 'ring-2 ring-brand-primary shadow-lg' : ''
            } ${
              option.status === 'locked' ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            onClick={() => handleCardClick(option)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-brand-primary/10 rounded-lg text-brand-primary">
                    {option.icon}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{option.title}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                  </div>
                </div>
                
                <div className="flex flex-col items-end gap-2">
                  <Badge 
                    variant="outline" 
                    className={`${getStatusColor(option.status)} text-xs`}
                  >
                    {getStatusIcon(option.status)}
                    <span className="ml-1 capitalize">{option.status.replace('_', ' ')}</span>
                  </Badge>
                  
                  <Badge 
                    variant="outline" 
                    className={`${getDifficultyColor(option.difficulty)} text-xs`}
                  >
                    {option.difficulty}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Accuracy Improvement Visualization */}
              <div className="bg-gradient-to-r from-brand-primary/10 to-ai-dark-blue/10 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-brand-primary">Accuracy Improvement</span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold text-brand-primary">{option.currentAccuracy}%</span>
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                    <span className="text-lg font-bold text-[#059669]">{option.targetAccuracy}%</span>
                  </div>
                </div>
                
                <div className="relative">
                  <Progress 
                    value={option.currentAccuracy} 
                    className="h-2 mb-1"
                  />
                  <div className="flex justify-between text-xs text-gray-600">
                    <span>Current</span>
                    <span className="text-[#059669] font-medium">
                      +{option.accuracyImprovement}% improvement
                    </span>
                  </div>
                </div>
              </div>

              {/* Time Estimate */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span>Time required: {option.timeEstimate}</span>
              </div>

              {/* Benefits */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-brand-primary">Benefits:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {option.benefits.slice(0, 3).map((benefit, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-brand-primary rounded-full" />
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Expanded Details */}
              {selectedCard === option.id && (
                <div className="mt-4 pt-4 border-t space-y-3">
                  {/* All Benefits */}
                  {option.benefits.length > 3 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-brand-primary">Additional Benefits:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {option.benefits.slice(3).map((benefit, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-brand-primary rounded-full" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Requirements */}
                  {option.requirements && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-brand-primary">Requirements:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {option.requirements.map((requirement, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <Target className="h-3 w-3 text-brand-primary" />
                            {requirement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Action Button */}
                  <div className="flex justify-end pt-2">
                    <Button 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStartEnhancement(option.id);
                      }}
                      disabled={option.status === 'locked' || option.status === 'completed'}
                      className="bg-brand-primary hover:bg-brand-primary-hover"
                    >
                      {option.status === 'completed' ? 'Completed' : 
                       option.status === 'in_progress' ? 'Continue' : 'Start Enhancement'}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Overall Progress */}
      <div className="bg-gradient-to-r from-brand-primary/5 to-ai-dark-blue/5 rounded-xl p-6 mt-8">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-brand-primary">Overall Progress</h4>
          <div className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-brand-primary" />
            <span className="text-lg font-bold text-brand-primary">
              {completedEnhancements.length}/{enhancementOptions.length} Complete
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Current Accuracy:</span>
            <span className="font-semibold text-brand-primary">{currentAccuracy}%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Target Accuracy:</span>
            <span className="font-semibold text-[#059669]">95%+</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Potential Improvement:</span>
            <span className="font-semibold text-[#059669]">
              +{Math.min(95 - currentAccuracy, 
                enhancementOptions.reduce((sum, opt) => sum + opt.accuracyImprovement, 0))}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancementOptionCards;