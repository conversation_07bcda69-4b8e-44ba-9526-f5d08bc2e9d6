/**
 * Transaction Components Index
 * Professional review workflow components for transaction management
 */

// Main review workflow components
export {
  default as ConfidenceIndicator,
  ConfidenceBadge,
  ConfidenceProgress,
  ConfidenceSummary,
} from './ConfidenceIndicator';
export {
  default as TransactionStatusBadge,
  StatusDot,
  WorkflowStatus,
  CombinedStatus,
} from './TransactionStatusBadge';
export {
  default as BulkActionPanel,
  BulkActionSummary,
} from './BulkActionPanel';
export {
  default as ReviewProgressStats,
  CompactReviewStats,
} from './ReviewProgressStats';
export {
  default as GroupedTransactionReview,
} from './GroupedTransactionReview';
export {
  default as IndividualTransactionReview,
} from './IndividualTransactionReview';
export {
  default as EnhancementOptionCards,
} from './EnhancementOptionCards';
export {
  default as AccuracyProgressTracker,
} from './AccuracyProgressTracker';

// Re-export existing components for backward compatibility
export { default as TransactionTable } from './TransactionTable';

// Component types are not exported from ConfidenceIndicator
// Using internal interfaces, external users should use component props directly

// Type exports temporarily removed until components export their interfaces
// Users should rely on React.ComponentProps<typeof Component> for now
