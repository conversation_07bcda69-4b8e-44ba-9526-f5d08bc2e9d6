/**
 * Memoized Transaction Card Component for Mobile View
 * Optimized for performance when rendering large lists of transactions
 */
import React from 'react';
import { Checkbox } from '@/shared/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';

interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  category: string;
  confidence: number;
  isSelected: boolean;
}

interface TransactionCardProps {
  transaction: Transaction;
  categories: string[];
  onSelect: (id: string, selected: boolean) => void;
  onCategoryChange: (id: string, category: string) => void;
  getConfidenceIcon: (confidence: number) => React.ReactNode;
}

// Memoized transaction card component for mobile view
export const TransactionCard = React.memo<TransactionCardProps>(({
  transaction,
  categories,
  onSelect,
  onCategoryChange,
  getConfidenceIcon,
}) => {
  return (
    <div className="card-padding-sm border-b border-slate-100 last:border-b-0">
      <div className="flex items-start justify-between">
        <div className="flex items-center component-gap-sm">
          <Checkbox
            checked={transaction.isSelected}
            onCheckedChange={(checked) =>
              onSelect(transaction.id, checked as boolean)
            }
          />
          <span className="text-sm font-mono text-slate-500">
            {transaction.date}
          </span>
        </div>
        {getConfidenceIcon(transaction.confidence)}
      </div>

      <div className="font-medium text-slate-900 form-field-spacing">
        {transaction.description}
      </div>

      <div className="text-lg font-mono font-semibold form-field-spacing">
        ${transaction.amount.toLocaleString()}
      </div>

      {transaction.confidence < 90 ? (
        <Select
          value={transaction.category}
          onValueChange={(value) =>
            onCategoryChange(transaction.id, value)
          }
        >
          <SelectTrigger className="w-full">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : (
        <div className="text-sm text-slate-600">
          □ {transaction.category}
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for optimal performance
  return (
    prevProps.transaction.id === nextProps.transaction.id &&
    prevProps.transaction.isSelected === nextProps.transaction.isSelected &&
    prevProps.transaction.category === nextProps.transaction.category &&
    prevProps.transaction.confidence === nextProps.transaction.confidence &&
    prevProps.categories === nextProps.categories
  );
});

TransactionCard.displayName = 'TransactionCard';

export default TransactionCard;