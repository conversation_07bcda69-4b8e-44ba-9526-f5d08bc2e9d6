/**
 * TransactionTable Component Tests - Complex Data Table with React Table v8
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TransactionTable } from '../TransactionTable';
import { Transaction, CategorizationStatus } from '@/shared/types/categorization';

// Mock the child components
vi.mock('../ConfidenceIndicator', () => ({
  ConfidenceIndicator: ({ confidence, size }: { confidence: number; size: string }) => (
    <div data-testid="confidence-indicator" data-confidence={confidence} data-size={size}>
      {Math.round(confidence * 100)}%
    </div>
  ),
}));

vi.mock('../TransactionStatusBadge', () => ({
  TransactionStatusBadge: ({ transaction }: { transaction: Transaction }) => (
    <div data-testid="status-badge" data-status={transaction.status}>
      {transaction.status}
    </div>
  ),
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Mock transactions for testing
const createMockTransaction = (overrides: Partial<Transaction> = {}): Transaction => ({
  id: 'tx-001',
  date: '2024-01-15',
  description: 'Coffee Shop Payment',
  amount: -45.50,
  currency: 'USD',
  category_id: 1,
  category_path: 'Meals & Entertainment/Coffee',
  ai_suggested_category_id: 1,
  ai_suggested_category_path: 'Meals & Entertainment/Coffee',
  ai_category_confidence: 0.95,
  is_user_modified: false,
  is_categorized: true,
  status: CategorizationStatus.AI_SUGGESTED,
  confidence_level: 'high',
  account: 'Business Checking',
  account_id: 'acc-001',
  merchant: 'Local Coffee Co.',
  notes: 'Client meeting',
  transaction_type: 'expense',
  tags: ['business', 'meeting'],
  created_at: '2024-01-15T10:30:00Z',
  updated_at: '2024-01-15T10:30:00Z',
  upload_id: 'upload-001',
  ...overrides,
});

const mockTransactions: Transaction[] = [
  createMockTransaction({
    id: 'tx-001',
    date: '2024-01-15',
    description: 'Coffee Shop Payment',
    amount: -45.50,
    category_path: 'Meals & Entertainment/Coffee',
    status: CategorizationStatus.AI_SUGGESTED,
    merchant: 'Local Coffee Co.',
  }),
  createMockTransaction({
    id: 'tx-002',
    date: '2024-01-16',
    description: 'Client Payment Received',
    amount: 2500.00,
    category_path: 'Revenue/Consulting',
    status: CategorizationStatus.USER_MODIFIED,
    is_user_modified: true,
    transaction_type: 'income',
    merchant: 'ABC Consulting Inc.',
  }),
  createMockTransaction({
    id: 'tx-003',
    date: '2024-01-17',
    description: 'Office Supplies',
    amount: -89.99,
    category_path: 'Office Expenses/Supplies',
    status: CategorizationStatus.UNCATEGORIZED,
    is_categorized: false,
    ai_category_confidence: 0.45,
    confidence_level: 'low',
    merchant: 'Office Depot',
  }),
  createMockTransaction({
    id: 'tx-004',
    date: '2024-01-18',
    description: 'Software Subscription',
    amount: -99.00,
    category_path: 'Software/Subscriptions/Development',
    gl_code: '6001',
    status: CategorizationStatus.AI_SUGGESTED,
    merchant: 'Software Co.',
  }),
];

const mockCallbacks = {
  onRefresh: vi.fn(),
  onExport: vi.fn(),
  onBulkAction: vi.fn(),
  onTransactionUpdate: vi.fn(),
};

describe('TransactionTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders table with all transactions', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Check table structure exists
      expect(screen.getByRole('table')).toBeInTheDocument();
      
      // Check all transactions are displayed
      expect(screen.getByText('Coffee Shop Payment')).toBeInTheDocument();
      expect(screen.getByText('Client Payment Received')).toBeInTheDocument();
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
      expect(screen.getByText('Software Subscription')).toBeInTheDocument();
    });

    it('renders correct table headers', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Check for main column headers
      expect(screen.getByText('Date')).toBeInTheDocument();
      expect(screen.getByText('Description')).toBeInTheDocument();
      expect(screen.getByText('Amount')).toBeInTheDocument();
      expect(screen.getByText('Category')).toBeInTheDocument();
      expect(screen.getByText('GL Code')).toBeInTheDocument();
      expect(screen.getByText('Status')).toBeInTheDocument();
      expect(screen.getByText('Account')).toBeInTheDocument();
    });

    it('displays correct transaction data formatting', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Check date formatting - should see different dates now
      expect(screen.getByText('01/15/2024')).toBeInTheDocument();
      expect(screen.getByText('01/16/2024')).toBeInTheDocument();
      expect(screen.getByText('01/17/2024')).toBeInTheDocument();
      expect(screen.getByText('01/18/2024')).toBeInTheDocument();
      
      // Check currency formatting
      expect(screen.getByText('$45.50')).toBeInTheDocument(); // Expense amount (absolute)
      expect(screen.getByText('$2,500.00')).toBeInTheDocument(); // Income amount
      
      // Check merchant display - now each transaction has different merchant
      expect(screen.getByText('Local Coffee Co.')).toBeInTheDocument();
      expect(screen.getByText('ABC Consulting Inc.')).toBeInTheDocument();
      expect(screen.getByText('Office Depot')).toBeInTheDocument();
      expect(screen.getByText('Software Co.')).toBeInTheDocument();
    });
  });

  describe('Bulk Actions and Selection', () => {
    it('renders selection checkboxes when bulk actions enabled', () => {
      render(<TransactionTable transactions={mockTransactions} enableBulkActions={true} />);
      
      // Should have select all checkbox in header
      const selectAllCheckbox = screen.getByLabelText('Select all');
      expect(selectAllCheckbox).toBeInTheDocument();
      
      // Should have individual row checkboxes
      const rowCheckboxes = screen.getAllByLabelText('Select row');
      expect(rowCheckboxes).toHaveLength(mockTransactions.length);
    });

    it('does not render selection checkboxes when bulk actions disabled', () => {
      render(<TransactionTable transactions={mockTransactions} enableBulkActions={false} />);
      
      // Should not have select all checkbox
      expect(screen.queryByLabelText('Select all')).not.toBeInTheDocument();
      
      // Should not have individual row checkboxes  
      expect(screen.queryByLabelText('Select row')).not.toBeInTheDocument();
    });

    it('handles row selection correctly', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} enableBulkActions={true} />);
      
      // Select first row
      const firstRowCheckbox = screen.getAllByLabelText('Select row')[0];
      await user.click(firstRowCheckbox);
      
      // Verify selection count updated
      expect(screen.getByText('1 of 4 row(s) selected.')).toBeInTheDocument();
    });

    it('shows bulk actions menu when rows selected', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} enableBulkActions={true} {...mockCallbacks} />);
      
      // Select first row
      const firstRowCheckbox = screen.getAllByLabelText('Select row')[0];
      await user.click(firstRowCheckbox);
      
      // Should show bulk actions button
      expect(screen.getByText('Actions (1)')).toBeInTheDocument();
      
      // Click bulk actions dropdown
      await user.click(screen.getByText('Actions (1)'));
      
      // Check bulk action options
      expect(screen.getByText('Approve selected')).toBeInTheDocument();
      expect(screen.getByText('Flag for review')).toBeInTheDocument();
      expect(screen.getByText('Batch categorize')).toBeInTheDocument();
      expect(screen.getByText('Export selected')).toBeInTheDocument();
    });

    it('calls onBulkAction with correct parameters', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} enableBulkActions={true} {...mockCallbacks} />);
      
      // Select first row
      const firstRowCheckbox = screen.getAllByLabelText('Select row')[0];
      await user.click(firstRowCheckbox);
      
      // Click bulk actions and approve
      await user.click(screen.getByText('Actions (1)'));
      await user.click(screen.getByText('Approve selected'));
      
      expect(mockCallbacks.onBulkAction).toHaveBeenCalledWith('approve', ['tx-001']);
    });
  });

  describe('Sorting Functionality', () => {
    it('renders sortable column headers with arrow icons', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Find sortable headers (they should have ArrowUpDown icon)
      const dateHeader = screen.getByText('Date').closest('button');
      const descriptionHeader = screen.getByText('Description').closest('button');
      const amountHeader = screen.getByText('Amount').closest('button');
      const categoryHeader = screen.getByText('Category').closest('button');
      
      expect(dateHeader).toBeInTheDocument();
      expect(descriptionHeader).toBeInTheDocument();
      expect(amountHeader).toBeInTheDocument();
      expect(categoryHeader).toBeInTheDocument();
    });

    it('handles column sorting when header clicked', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Click on Date header to sort
      const dateHeader = screen.getByText('Date').closest('button');
      await user.click(dateHeader!);
      
      // Table should re-render with sorted data
      // Since all test transactions have same date, order shouldn't change visually
      expect(screen.getByText('Coffee Shop Payment')).toBeInTheDocument();
    });

    it('toggles sort direction when same header clicked twice', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} />);
      
      const amountHeader = screen.getByText('Amount').closest('button');
      
      // Click once for ascending
      await user.click(amountHeader!);
      
      // Click again for descending
      await user.click(amountHeader!);
      
      // Content should still be present (testing sort toggle logic)
      expect(screen.getByText('Coffee Shop Payment')).toBeInTheDocument();
    });
  });

  describe('Filtering and Search', () => {
    it('renders global search input', () => {
      render(<TransactionTable transactions={mockTransactions} enableFiltering={true} />);
      
      expect(screen.getByPlaceholderText('Search transactions...')).toBeInTheDocument();
    });

    it('renders status filter dropdown', () => {
      render(<TransactionTable transactions={mockTransactions} enableFiltering={true} />);
      
      expect(screen.getByText('Filter status')).toBeInTheDocument();
    });

    it('filters transactions by search term', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} enableFiltering={true} />);
      
      const searchInput = screen.getByPlaceholderText('Search transactions...');
      
      // Search for "Coffee"
      await user.type(searchInput, 'Coffee');
      
      // Should still show the coffee transaction
      await waitFor(() => {
        expect(screen.getByText('Coffee Shop Payment')).toBeInTheDocument();
      });
    });

    it('does not render filters when filtering disabled', () => {
      render(<TransactionTable transactions={mockTransactions} enableFiltering={false} />);
      
      // Search should still be present (it's always available)
      expect(screen.getByPlaceholderText('Search transactions...')).toBeInTheDocument();
      
      // Status filter should not be present
      expect(screen.queryByText('Filter status')).not.toBeInTheDocument();
    });
  });

  describe('Export Functionality', () => {
    it('renders export dropdown when enabled', () => {
      render(<TransactionTable transactions={mockTransactions} enableExport={true} {...mockCallbacks} />);
      
      expect(screen.getByText('Export')).toBeInTheDocument();
    });

    it('does not render export dropdown when disabled', () => {
      render(<TransactionTable transactions={mockTransactions} enableExport={false} />);
      
      expect(screen.queryByText('Export')).not.toBeInTheDocument();
    });

    it('shows export options when clicked', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} enableExport={true} {...mockCallbacks} />);
      
      await user.click(screen.getByText('Export'));
      
      expect(screen.getByText('Export as CSV')).toBeInTheDocument();
      expect(screen.getByText('Export as Excel')).toBeInTheDocument();
      expect(screen.getByText('Export as PDF')).toBeInTheDocument();
    });

    it('calls onExport with correct format', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} enableExport={true} {...mockCallbacks} />);
      
      await user.click(screen.getByText('Export'));
      await user.click(screen.getByText('Export as CSV'));
      
      expect(mockCallbacks.onExport).toHaveBeenCalledWith('csv');
    });
  });

  describe('Data Display and Formatting', () => {
    it('formats amounts correctly with currency', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Check various amount formats
      expect(screen.getByText('$45.50')).toBeInTheDocument(); // Expense (shows absolute)
      expect(screen.getByText('$2,500.00')).toBeInTheDocument(); // Large income
      expect(screen.getByText('$89.99')).toBeInTheDocument(); // Other expense
    });

    it('displays category paths correctly', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Check category display - categories with "/" are converted to "→"
      expect(screen.getByText('Meals & Entertainment → Coffee')).toBeInTheDocument();
      expect(screen.getByText('Revenue → Consulting')).toBeInTheDocument();
      expect(screen.getByText('Office Expenses → Supplies')).toBeInTheDocument();
    });

    it('shows abbreviated category paths for long hierarchies', () => {
      const longCategoryTransaction = createMockTransaction({
        category_path: 'Business Expenses/Office/Technology/Software/Development Tools/IDE Licenses',
      });
      
      render(<TransactionTable transactions={[longCategoryTransaction]} />);
      
      // Should show abbreviated format for long paths
      expect(screen.getByText('... → Development Tools → IDE Licenses')).toBeInTheDocument();
    });

    it('displays GL codes when available', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // One transaction has GL code
      expect(screen.getByText('6001')).toBeInTheDocument();
      
      // Others should show dash
      const dashElements = screen.getAllByText('—');
      expect(dashElements.length).toBeGreaterThan(0);
    });

    it('handles invalid amount data gracefully', () => {
      const invalidAmountTransaction = createMockTransaction({
        amount: NaN,
      });
      
      render(<TransactionTable transactions={[invalidAmountTransaction]} />);
      
      expect(screen.getByText('Data Issue')).toBeInTheDocument();
    });
  });

  describe('Actions and Interactions', () => {
    it('renders action dropdown for each row', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Should have action dropdowns (MoreHorizontal icons)
      const actionButtons = screen.getAllByLabelText('Open menu');
      expect(actionButtons).toHaveLength(mockTransactions.length);
    });

    it('shows action menu options when clicked', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} />);
      
      const firstActionButton = screen.getAllByLabelText('Open menu')[0];
      await user.click(firstActionButton);
      
      expect(screen.getByText('Copy transaction ID')).toBeInTheDocument();
      expect(screen.getByText('View details')).toBeInTheDocument();
      expect(screen.getByText('Edit transaction')).toBeInTheDocument();
      expect(screen.getByText('Flag for review')).toBeInTheDocument();
    });

    it('copies transaction ID to clipboard', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} />);
      
      const firstActionButton = screen.getAllByLabelText('Open menu')[0];
      await user.click(firstActionButton);
      
      await user.click(screen.getByText('Copy transaction ID'));
      
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('tx-001');
    });
  });

  describe('Pagination', () => {
    it('displays pagination information', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Check pagination info
      expect(screen.getByText('0 of 4 row(s) selected.')).toBeInTheDocument();
      expect(screen.getByText('Page 1 of 1')).toBeInTheDocument();
    });

    it('shows page size selector', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      expect(screen.getByText('Rows per page')).toBeInTheDocument();
      expect(screen.getByDisplayValue('50')).toBeInTheDocument(); // Default page size
    });

    it('renders pagination navigation buttons', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // Navigation buttons (using screen reader text)
      expect(screen.getByLabelText('Go to first page')).toBeInTheDocument();
      expect(screen.getByLabelText('Go to previous page')).toBeInTheDocument();
      expect(screen.getByLabelText('Go to next page')).toBeInTheDocument();
      expect(screen.getByLabelText('Go to last page')).toBeInTheDocument();
    });

    it('disables navigation when on single page', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      // With only 4 transactions, all nav buttons should be disabled
      expect(screen.getByLabelText('Go to first page')).toBeDisabled();
      expect(screen.getByLabelText('Go to previous page')).toBeDisabled();
      expect(screen.getByLabelText('Go to next page')).toBeDisabled();
      expect(screen.getByLabelText('Go to last page')).toBeDisabled();
    });
  });

  describe('Loading and Empty States', () => {
    it('shows loading state when isLoading is true', () => {
      render(<TransactionTable transactions={[]} isLoading={true} />);
      
      expect(screen.getByText('Loading transactions...')).toBeInTheDocument();
    });

    it('shows empty state when no transactions', () => {
      render(<TransactionTable transactions={[]} isLoading={false} />);
      
      expect(screen.getByText('No transactions found.')).toBeInTheDocument();
    });

    it('shows refresh button with loading animation', () => {
      render(<TransactionTable transactions={mockTransactions} isLoading={true} {...mockCallbacks} />);
      
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      expect(refreshButton).toBeDisabled(); // Should be disabled when loading
    });

    it('calls onRefresh when refresh button clicked', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} isLoading={false} {...mockCallbacks} />);
      
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      await user.click(refreshButton);
      
      expect(mockCallbacks.onRefresh).toHaveBeenCalled();
    });
  });

  describe('Column Visibility', () => {
    it('renders column visibility dropdown', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      expect(screen.getByText('Columns')).toBeInTheDocument();
    });

    it('shows column visibility options when clicked', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} />);
      
      await user.click(screen.getByText('Columns'));
      
      // Should show toggles for hideable columns
      expect(screen.getByText('date')).toBeInTheDocument();
      expect(screen.getByText('description')).toBeInTheDocument();
      expect(screen.getByText('amount')).toBeInTheDocument();
    });
  });

  describe('Performance and Optimization', () => {
    it('handles large datasets efficiently', () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => 
        createMockTransaction({ 
          id: `tx-${i}`,
          description: `Transaction ${i}`,
          amount: Math.random() * 1000 - 500,
        })
      );
      
      const { container } = render(<TransactionTable transactions={largeDataset} pageSize={50} />);
      
      // Should render without performance issues
      expect(container.querySelector('table')).toBeInTheDocument();
      
      // Should only render page size rows (50) due to pagination
      const rows = container.querySelectorAll('tbody tr');
      expect(rows.length).toBeLessThanOrEqual(50);
    });

    it('updates efficiently on prop changes', () => {
      const { rerender } = render(<TransactionTable transactions={mockTransactions} />);
      
      // Update with new transactions
      const newTransactions = [...mockTransactions, createMockTransaction({ 
        id: 'tx-005',
        description: 'New Transaction',
      })];
      
      rerender(<TransactionTable transactions={newTransactions} />);
      
      expect(screen.getByText('New Transaction')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper table semantics', () => {
      render(<TransactionTable transactions={mockTransactions} />);
      
      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(screen.getAllByRole('columnheader').length).toBeGreaterThan(0);
      expect(screen.getAllByRole('row').length).toBeGreaterThan(1); // Headers + data rows
    });

    it('provides accessible labels for interactive elements', () => {
      render(<TransactionTable transactions={mockTransactions} enableBulkActions={true} />);
      
      expect(screen.getByLabelText('Select all')).toBeInTheDocument();
      expect(screen.getAllByLabelText('Select row').length).toBe(mockTransactions.length);
      expect(screen.getAllByLabelText('Open menu').length).toBe(mockTransactions.length);
    });

    it('has proper keyboard navigation support', async () => {
      const user = userEvent.setup();
      render(<TransactionTable transactions={mockTransactions} enableBulkActions={true} />);
      
      // Tab to first focusable element
      await user.tab();
      
      // Should focus on first interactive element (search input)
      expect(screen.getByPlaceholderText('Search transactions...')).toHaveFocus();
    });
  });
});