/**
 * Memoized Transaction Row Component
 * Optimized for performance when rendering large lists of transactions
 */
import React from 'react';
import { Checkbox } from '@/shared/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';

interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  category: string;
  confidence: number;
  isSelected: boolean;
}

interface TransactionRowProps {
  transaction: Transaction;
  categories: string[];
  onSelect: (id: string, selected: boolean) => void;
  onCategoryChange: (id: string, category: string) => void;
  getConfidenceIcon: (confidence: number) => React.ReactNode;
  getConfidenceText: (confidence: number) => string;
}

// Memoized transaction row component to prevent unnecessary re-renders
export const TransactionRow = React.memo<TransactionRowProps>(({
  transaction,
  categories,
  onSelect,
  onCategoryChange,
  getConfidenceIcon,
  getConfidenceText,
}) => {
  return (
    <tr className="border-b border-slate-100 hover:bg-slate-50">
      <td className="p-3">
        <Checkbox
          checked={transaction.isSelected}
          onCheckedChange={(checked) =>
            onSelect(transaction.id, checked as boolean)
          }
        />
      </td>
      <td className="p-3 text-sm font-mono">
        {transaction.date}
      </td>
      <td className="p-3 text-sm font-medium">
        {transaction.description}
      </td>
      <td className="p-3 text-sm font-mono text-right">
        ${transaction.amount.toLocaleString()}
      </td>
      <td className="p-3">
        {transaction.confidence < 90 ? (
          <Select
            value={transaction.category}
            onValueChange={(value) =>
              onCategoryChange(transaction.id, value)
            }
          >
            <SelectTrigger className="w-[140px] h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : (
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {transaction.category}
            </span>
            <span className="text-xs text-slate-500">
              {getConfidenceText(transaction.confidence)}
            </span>
          </div>
        )}
      </td>
      <td className="p-3 text-center">
        {getConfidenceIcon(transaction.confidence)}
      </td>
    </tr>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for optimal performance
  return (
    prevProps.transaction.id === nextProps.transaction.id &&
    prevProps.transaction.isSelected === nextProps.transaction.isSelected &&
    prevProps.transaction.category === nextProps.transaction.category &&
    prevProps.transaction.confidence === nextProps.transaction.confidence &&
    prevProps.categories === nextProps.categories // Reference equality is fine for categories array
  );
});

TransactionRow.displayName = 'TransactionRow';

export default TransactionRow;