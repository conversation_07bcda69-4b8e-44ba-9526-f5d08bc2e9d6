/**
 * Confidence Indicator Component
 * Visual indicator for AI categorization confidence levels with professional styling
 */
import React from 'react';
import { CheckCircle2, AlertCircle, XCircle, HelpCircle } from 'lucide-react';
import {
  getConfidenceLevel,
  formatConfidence,
} from '../services/transactionService';
import { ConfidenceScoreTooltip } from '@/shared/components/ui/accuracy-tooltip';

interface ConfidenceIndicatorProps {
  confidence?: number | null;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  showTooltip?: boolean;
  className?: string;
}

export const ConfidenceIndicator: React.FC<ConfidenceIndicatorProps> = ({
  confidence,
  size = 'md',
  showText = false,
  showTooltip = true,
  className = '',
}) => {
  const level =
    confidence !== undefined && confidence !== null
      ? getConfidenceLevel(confidence)
      : 'unknown';

  const formattedConfidence = formatConfidence(confidence);

  // Size mappings
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  // Level configurations
  const levelConfig = {
    high: {
      icon: CheckCircle2,
      color: 'text-success',
      bgColor: 'bg-green-100',
      borderColor: 'border-green-200',
      dotColor: 'bg-green-500',
      label: 'High Confidence',
      description: 'AI is highly confident in this categorization',
    },
    medium: {
      icon: AlertCircle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      borderColor: 'border-yellow-200',
      dotColor: 'bg-yellow-500',
      label: 'Medium Confidence',
      description: 'AI has moderate confidence, may need review',
    },
    low: {
      icon: XCircle,
      color: 'text-error',
      bgColor: 'bg-red-100',
      borderColor: 'border-red-200',
      dotColor: 'bg-red-500',
      label: 'Low Confidence',
      description: 'AI has low confidence, review recommended',
    },
    unknown: {
      icon: HelpCircle,
      color: 'text-gray-500',
      bgColor: 'bg-gray-100',
      borderColor: 'border-gray-200',
      dotColor: 'bg-gray-400',
      label: 'Unknown',
      description: 'Confidence level not available',
    },
  };

  const config = levelConfig[level];
  const Icon = config.icon;
  const tooltipText = showTooltip
    ? `${config.label}: ${formattedConfidence} - ${config.description}`
    : '';

  const confidenceValue = confidence && confidence > 0 ? confidence : 0;

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {/* Icon Version with CEO-friendly tooltip */}
      {showTooltip ? (
        <ConfidenceScoreTooltip confidence={confidenceValue}>
          <div className="relative">
            <Icon className={`${sizeClasses[size]} ${config.color}`} />
          </div>
        </ConfidenceScoreTooltip>
      ) : (
        <div className="relative" title={tooltipText}>
          <Icon className={`${sizeClasses[size]} ${config.color}`} />
        </div>
      )}

      {/* Dot Version (Alternative) */}
      <div
        className={`rounded-full ${sizeClasses[size]} ${config.dotColor} hidden`}
        title={tooltipText}
      />

      {/* Text Display */}
      {showText && (
        <span
          className={`${textSizeClasses[size]} ${config.color} font-medium`}
        >
          {formattedConfidence}
        </span>
      )}
    </div>
  );
};

/**
 * Confidence Badge Component
 * Badge-style indicator with background and border
 */
interface ConfidenceBadgeProps {
  confidence?: number | null;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'subtle';
  className?: string;
}

export const ConfidenceBadge: React.FC<ConfidenceBadgeProps> = ({
  confidence,
  size = 'md',
  variant = 'default',
  className = '',
}) => {
  const level =
    confidence !== undefined && confidence !== null
      ? getConfidenceLevel(confidence)
      : 'unknown';

  const formattedConfidence = formatConfidence(confidence);

  // Size mappings
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base',
  };

  // Level configurations for badges
  const levelConfig = {
    high: {
      default: 'bg-green-100 text-green-800 border-green-200',
      outline: 'border-green-600 text-success bg-transparent',
      subtle: 'bg-green-50 text-green-700 border-green-100',
      label: 'High',
    },
    medium: {
      default: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      outline: 'border-yellow-600 text-yellow-600 bg-transparent',
      subtle: 'bg-yellow-50 text-yellow-700 border-yellow-100',
      label: 'Medium',
    },
    low: {
      default: 'bg-red-100 text-red-800 border-red-200',
      outline: 'border-red-600 text-error bg-transparent',
      subtle: 'bg-red-50 text-red-700 border-red-100',
      label: 'Low',
    },
    unknown: {
      default: 'bg-gray-100 text-gray-800 border-gray-200',
      outline: 'border-gray-600 text-gray-600 bg-transparent',
      subtle: 'bg-gray-50 text-gray-700 border-gray-100',
      label: 'N/A',
    },
  };

  const config = levelConfig[level];
  const variantClasses = config[variant];

  return (
    <span
      className={`inline-flex items-center rounded-md border font-medium ${sizeClasses[size]} ${variantClasses} ${className}`}
      title={`Confidence: ${formattedConfidence}`}
    >
      {config.label}
    </span>
  );
};

/**
 * Confidence Progress Bar
 * Linear progress indicator for confidence levels
 */
interface ConfidenceProgressProps {
  confidence?: number | null;
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  className?: string;
}

export const ConfidenceProgress: React.FC<ConfidenceProgressProps> = ({
  confidence,
  size = 'md',
  showPercentage = false,
  className = '',
}) => {
  const level = getConfidenceLevel(confidence);
  const percentage = confidence ? Math.round(confidence * 100) : 0;
  const formattedConfidence = formatConfidence(confidence);

  // Size mappings
  const heightClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
  };

  const textClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  // Color based on confidence level
  const progressColor = {
    high: 'bg-green-500',
    medium: 'bg-yellow-500',
    low: 'bg-red-500',
    unknown: 'bg-gray-400',
  }[level];

  return (
    <div className={`w-full ${className}`}>
      <div className={`w-full bg-gray-200 rounded-full ${heightClasses[size]}`}>
        <div
          className={`${heightClasses[size]} rounded-full transition-all duration-300 ${progressColor}`}
          style={{ width: `${percentage}%` }}
        />
      </div>
      {showPercentage && (
        <div className={`mt-1 text-right ${textClasses[size]} text-gray-600`}>
          {formattedConfidence}
        </div>
      )}
    </div>
  );
};

/**
 * Confidence Summary Component
 * Comprehensive confidence display with multiple visual elements
 */
interface ConfidenceSummaryProps {
  confidence?: number | null;
  title?: string;
  description?: string;
  className?: string;
}

export const ConfidenceSummary: React.FC<ConfidenceSummaryProps> = ({
  confidence,
  title = 'AI Confidence',
  description,
  className = '',
}) => {
  const level = getConfidenceLevel(confidence);
  const formattedConfidence = formatConfidence(confidence);

  const levelConfig = {
    high: {
      color: 'text-success',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      description: 'High confidence categorization - ready for production use',
    },
    medium: {
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      description: 'Medium confidence - consider reviewing before approval',
    },
    low: {
      color: 'text-error',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      description: 'Low confidence - manual review strongly recommended',
    },
    unknown: {
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200',
      description: 'Confidence level not available',
    },
  };

  const config = levelConfig[level];

  return (
    <div
      className={`rounded-lg border p-4 ${config.bgColor} ${config.borderColor} ${className}`}
    >
      <div className="flex items-center justify-between mb-2">
        <h4 className={`font-medium ${config.color}`}>{title}</h4>
        <ConfidenceIndicator confidence={confidence} showText />
      </div>

      <ConfidenceProgress confidence={confidence} showPercentage />

      <p className={`mt-2 text-sm ${config.color}`}>
        {description || config.description}
      </p>
    </div>
  );
};

export default ConfidenceIndicator;
