/**
 * Individual Transaction Review Component
 * 
 * Implements the individual transaction review interface from mockup design
 * Shows one transaction at a time with AI suggestions and approve/skip actions
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/shared/components/ui/card';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { type Transaction } from '@/shared/types/categorization';
import { type Category } from '@/features/categories/services/categoryService';
import { 
  getAISuggestion, 
  findSimilarTransactions,
  getConfidenceExplanation,
  applySingleCategorization,
  applyBatchCategorization,
  createCategorizationRule,
  type AISuggestion,
  type SimilarTransaction 
} from '../services/aiSuggestionService';
import { useToast } from '@/shared/components/ui/use-toast';

interface IndividualTransactionReviewProps {
  transactions: Transaction[];
  categories: Category[];
  onApprove: (transactionId: string, categoryId: number) => void;
  onSkip: (transactionId: string) => void;
  onBulkApprove: (transactionIds: string[]) => void;
  onCreateRule: (pattern: string, categoryId: number) => void;
  isLoading?: boolean;
  className?: string;
}

export const IndividualTransactionReview: React.FC<IndividualTransactionReviewProps> = ({
  transactions,
  categories,
  onApprove,
  onSkip,
  onBulkApprove,
  onCreateRule,
  isLoading = false,
  className = "",
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [similarTransactions, setSimilarTransactions] = useState<SimilarTransaction[]>([]);
  const [aiSuggestion, setAiSuggestion] = useState<AISuggestion | null>(null);
  const [loadingSuggestion, setLoadingSuggestion] = useState(false);
  const [showNewCategoryDialog, setShowNewCategoryDialog] = useState(false);
  const { toast } = useToast();

  const currentTransaction = transactions[currentIndex];

  // Keyboard shortcuts for quick approve/skip
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Only process shortcuts when not typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (event.key) {
        case 'Enter':
          event.preventDefault();
          handleApprove();
          break;
        case ' ':
          event.preventDefault();
          handleSkip();
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (currentIndex < transactions.length - 1) {
            setCurrentIndex(currentIndex + 1);
          }
          break;
        case 'ArrowLeft':
          event.preventDefault();
          if (currentIndex > 0) {
            setCurrentIndex(currentIndex - 1);
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentIndex, transactions.length, selectedCategoryId]);

  // Fetch AI suggestion and similar transactions
  useEffect(() => {
    if (currentTransaction && categories.length > 0) {
      fetchAISuggestion();
      fetchSimilarTransactions();
    }
  }, [currentTransaction, categories]);
  
  const fetchAISuggestion = async () => {
    if (!currentTransaction) return;
    
    setLoadingSuggestion(true);
    try {
      const suggestion = await getAISuggestion(currentTransaction, categories);
      if (suggestion) {
        setAiSuggestion(suggestion);
        setSelectedCategoryId(suggestion.categoryId);
      } else {
        // Fallback to existing suggestion
        const fallbackId = currentTransaction.category_id || 
          currentTransaction.ai_suggested_category_id ||
          categories[0]?.id;
        setSelectedCategoryId(fallbackId);
      }
    } catch (error) {
      console.error('Failed to get AI suggestion:', error);
      // Use fallback
      const fallbackId = currentTransaction.category_id || 
        currentTransaction.ai_suggested_category_id ||
        categories[0]?.id;
      setSelectedCategoryId(fallbackId);
    } finally {
      setLoadingSuggestion(false);
    }
  };
  
  const fetchSimilarTransactions = async () => {
    if (!currentTransaction) return;
    
    try {
      const similar = await findSimilarTransactions(currentTransaction, transactions);
      setSimilarTransactions(similar);
    } catch (error) {
      console.error('Failed to find similar transactions:', error);
      setSimilarTransactions([]);
    }
  };

  const extractMerchantPattern = (description: string): string => {
    // Extract merchant name pattern (simplified)
    return description
      .replace(/\d+/g, '')
      .replace(/[^a-zA-Z\s]/g, '')
      .trim()
      .toLowerCase()
      .split(' ')[0]; // Get first word as pattern
  };

  const handleApprove = async () => {
    if (currentTransaction && selectedCategoryId) {
      try {
        // Call backend API to update transaction
        const result = await applySingleCategorization(currentTransaction.id, selectedCategoryId);
        
        if (result.success) {
          // Call parent handler
          onApprove(currentTransaction.id, selectedCategoryId);
          
          // Show success notification
          toast({
            title: "Transaction Approved",
            description: `Successfully categorized "${currentTransaction.description?.slice(0, 30)}..."`,
            duration: 2000,
          });
          
          // Move to next transaction
          if (currentIndex < transactions.length - 1) {
            setCurrentIndex(currentIndex + 1);
          } else {
            toast({
              title: "Review Complete",
              description: "All transactions have been reviewed!",
              duration: 3000,
            });
          }
        } else {
          throw new Error(result.error || 'Failed to approve transaction');
        }
      } catch (error) {
        console.error('Approval failed:', error);
        toast({
          title: "Approval Failed",
          description: error instanceof Error ? error.message : 'Failed to approve transaction',
          variant: "destructive",
          duration: 5000,
        });
      }
    }
  };

  const handleSkip = () => {
    if (currentTransaction) {
      onSkip(currentTransaction.id);
      // Move to next transaction
      if (currentIndex < transactions.length - 1) {
        setCurrentIndex(currentIndex + 1);
      }
    }
  };

  const handleBulkApprove = async () => {
    if (similarTransactions.length > 0 && selectedCategoryId) {
      try {
        const transactionIds = [
          currentTransaction.id,
          ...similarTransactions.map(t => t.id)
        ];
        
        // Call backend API for batch categorization
        const result = await applyBatchCategorization(transactionIds, selectedCategoryId);
        
        if (result.successCount > 0) {
          // Call parent handler
          onBulkApprove(transactionIds);
          
          // Show success notification
          toast({
            title: "Bulk Approval Success",
            description: `Approved ${result.successCount} of ${transactionIds.length} transactions`,
            duration: 3000,
          });
          
          // Show warning if some failed
          if (result.failedCount > 0) {
            toast({
              title: "Partial Success",
              description: `${result.failedCount} transactions could not be processed`,
              variant: "destructive",
              duration: 5000,
            });
          }
          
          // Skip past all similar transactions
          setCurrentIndex(Math.min(currentIndex + similarTransactions.length + 1, transactions.length - 1));
        } else {
          throw new Error('No transactions were processed successfully');
        }
      } catch (error) {
        console.error('Bulk approval failed:', error);
        toast({
          title: "Bulk Approval Failed",
          description: error instanceof Error ? error.message : 'Failed to approve transactions',
          variant: "destructive",
          duration: 5000,
        });
      }
    }
  };

  const handleCreateRule = async () => {
    if (currentTransaction && selectedCategoryId) {
      try {
        const pattern = extractMerchantPattern(currentTransaction.description || '');
        
        // Call backend API to create rule
        const result = await createCategorizationRule(pattern, selectedCategoryId);
        
        if (result) {
          // Call parent handler
          onCreateRule(pattern, selectedCategoryId);
          
          // Show success notification
          toast({
            title: "Rule Created",
            description: `Created categorization rule for "${pattern}"`,
            duration: 3000,
          });
        } else {
          throw new Error('Failed to create categorization rule');
        }
      } catch (error) {
        console.error('Rule creation failed:', error);
        toast({
          title: "Rule Creation Failed",
          description: error instanceof Error ? error.message : 'Failed to create rule',
          variant: "destructive",
          duration: 5000,
        });
      }
    }
  };

  const handleCategoryChange = (value: string) => {
    if (value === 'other') {
      setShowNewCategoryDialog(true);
    } else {
      setSelectedCategoryId(parseInt(value));
    }
  };

  const handleNewCategoryCreate = (newCategoryName: string) => {
    // This would integrate with category creation API
    toast({
      title: "Category Creation",
      description: `"${newCategoryName}" will be created after review completion`,
      duration: 3000,
    });
    setShowNewCategoryDialog(false);
  };

  const formatConfidence = (confidence?: number): string => {
    if (!confidence) return 'Low confidence';
    const percentage = Math.round(confidence * 100);
    return `${percentage}% confidence`;
  };

  const getCategoryPath = (categoryId: number): string => {
    const category = categories.find(c => c.id === categoryId);
    return category?.path || category?.name || 'Unknown';
  };

  if (isLoading || !currentTransaction) {
    return (
      <Card className="loading-shimmer card-elevated">
        <CardContent className="p-6">
          <div className="h-32 bg-gray-200 rounded"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Review Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-brand-primary">
          {transactions.length} Transactions Need Review
        </h2>
        <div className="flex items-center gap-2">
          <Badge variant="outline">All</Badge>
          <Badge variant="secondary">Low Confidence</Badge>
          <Badge variant="secondary">Uncategorized</Badge>
        </div>
      </div>

      {/* Single Transaction Card */}
      <Card className="card-elevated bg-gray-50">
        <CardContent className="p-6">
          {/* Transaction Info */}
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {currentTransaction.description || 'Unknown Transaction'}
              </h3>
              <p className="text-sm text-gray-600">
                {new Date(currentTransaction.date).toLocaleDateString()}
              </p>
            </div>
            <div className="text-xl font-semibold text-gray-900">
              ${Math.abs(currentTransaction.amount || 0).toFixed(2)}
            </div>
          </div>

          {/* AI Suggestion */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-semibold text-gray-600">
                AI Suggestion:
              </label>
              {loadingSuggestion && (
                <span className="text-xs text-brand-primary animate-pulse">⚬ Analyzing...</span>
              )}
              {aiSuggestion && (
                <Badge 
                  variant="outline" 
                  style={{ 
                    borderColor: getConfidenceExplanation(aiSuggestion.confidence).color,
                    color: getConfidenceExplanation(aiSuggestion.confidence).color
                  }}
                >
                  {getConfidenceExplanation(aiSuggestion.confidence).label}
                </Badge>
              )}
            </div>
            <Select
              value={selectedCategoryId?.toString() || ''}
              onValueChange={handleCategoryChange}
              disabled={loadingSuggestion}
            >
              <SelectTrigger className="w-full bg-white">
                <SelectValue>
                  {selectedCategoryId 
                    ? getCategoryPath(selectedCategoryId)
                    : 'Select a category'
                  }
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    <div className="flex items-center justify-between w-full">
                      <span>{category.path || category.name}</span>
                      {category.id === aiSuggestion?.categoryId && (
                        <span className="text-xs text-brand-primary ml-2">
                          {Math.round((aiSuggestion.confidence || 0) * 100)}%
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
                <SelectItem value="other">Other...</SelectItem>
              </SelectContent>
            </Select>
            {aiSuggestion?.reasoning && (
              <p className="text-xs text-gray-600 mt-1">{aiSuggestion.reasoning}</p>
            )}
          </div>

          {/* Review Actions */}
          <div className="layout-mobile-stack">
            <Button 
              onClick={handleApprove}
              className="btn-professional btn-touch-friendly flex-1 bg-success hover:bg-success-dark text-white"
            >
              □ Approve
            </Button>
            <Button 
              onClick={handleSkip}
              variant="outline"
              className="btn-professional btn-touch-friendly flex-1"
            >
              Skip
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {similarTransactions.length > 0 && (
        <Card className="card-elevated bg-success/10 border-brand-primary/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold text-brand-primary">
                Similar Transactions Found
              </h3>
              <Badge variant="secondary" className="bg-brand-primary/10 text-brand-primary">
                {similarTransactions.length} matches
              </Badge>
            </div>
            
            {/* Similar transactions preview */}
            <div className="space-y-1 mb-3 max-h-32 overflow-y-auto">
              {similarTransactions.slice(0, 3).map((similar) => (
                <div key={similar.id} className="flex items-center justify-between text-xs">
                  <span className="text-gray-600 truncate max-w-[200px]">{similar.description}</span>
                  <span className="text-gray-900 font-medium">${Math.abs(similar.amount).toFixed(2)}</span>
                </div>
              ))}
              {similarTransactions.length > 3 && (
                <div className="text-xs text-gray-500">
                  And {similarTransactions.length - 3} more...
                </div>
              )}
            </div>
            
            <div className="layout-mobile-stack">
              <Button 
                onClick={handleBulkApprove}
                className="btn-professional btn-touch-friendly flex-1 bg-gradient-ai-primary text-white text-sm"
              >
                □ Approve All Similar
              </Button>
              <Button 
                onClick={handleCreateRule}
                variant="outline"
                className="btn-professional btn-touch-friendly flex-1 border-2 border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white text-sm"
              >
                ∷ Create Rule
              </Button>
            </div>
            
            {aiSuggestion?.suggestedRule && (
              <div className="mt-3 p-2 bg-white rounded border border-brand-primary/10">
                <p className="text-xs text-gray-600">
                  <span className="font-semibold">Suggested Rule:</span> {aiSuggestion.suggestedRule.description}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Progress Indicator & Keyboard Shortcuts */}
      <div className="text-sm text-gray-600 text-center space-y-2">
        <div>Transaction {currentIndex + 1} of {transactions.length}</div>
        <div className="text-xs text-gray-500">
          Shortcuts: Enter (Approve) • Space (Skip) • ← → (Navigate)
        </div>
      </div>

      {/* New Category Dialog (Simple version) */}
      {showNewCategoryDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-brand-primary mb-4">Create New Category</h3>
            <input
              type="text"
              placeholder="Enter category name..."
              className="input-elevated w-full mb-4"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  const input = e.target as HTMLInputElement;
                  if (input.value.trim()) {
                    handleNewCategoryCreate(input.value.trim());
                  }
                } else if (e.key === 'Escape') {
                  setShowNewCategoryDialog(false);
                }
              }}
              autoFocus
            />
            <div className="flex gap-3">
              <button
                onClick={() => setShowNewCategoryDialog(false)}
                className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded-lg font-semibold"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  const input = document.querySelector('input[placeholder="Enter category name..."]') as HTMLInputElement;
                  if (input?.value.trim()) {
                    handleNewCategoryCreate(input.value.trim());
                  }
                }}
                className="flex-1 bg-brand-primary text-white py-2 px-4 rounded-lg font-semibold"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IndividualTransactionReview;