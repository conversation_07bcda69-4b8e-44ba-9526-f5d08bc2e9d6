/**
 * Review Transactions Component - Excel-like Interface
 * Matches wireframe: docs/wireframes/03-daily-use-journey/03-review.md
 */
import React, { useState, useMemo, useCallback } from 'react';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  AlertCircle,
  Download,
} from 'lucide-react';
import { TransactionRow } from './TransactionRow';
import { TransactionCard } from './TransactionCard';

interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  category: string;
  confidence: number;
  isSelected: boolean;
}

interface ReviewTransactionsProps {
  transactions?: Transaction[];
  onTransactionUpdate?: (id: string, updates: Partial<Transaction>) => void;
  onBulkApprove?: (ids: string[]) => void;
  onBulkUpdate?: (ids: string[], updates: Partial<Transaction>) => void;
  onExport?: () => void;
}

const ReviewTransactionsComponent: React.FC<ReviewTransactionsProps> = ({
  transactions: propTransactions,
  onTransactionUpdate,
  onBulkApprove,
  onBulkUpdate,
  onExport,
}) => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<
    'all' | 'needs-review' | 'approved'
  >('all');
  const [filterMonth, setFilterMonth] = useState<string>('this-month');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(25);

  // Mock data if no transactions provided
  const [transactions, setTransactions] = useState<Transaction[]>(
    propTransactions || [
      {
        id: '1',
        date: 'Mar 15',
        description: 'AMAZON WEB SERVICES',
        amount: 1250,
        category: 'Technology',
        confidence: 95,
        isSelected: false,
      },
      {
        id: '2',
        date: 'Mar 15',
        description: 'DELTA AIRLINES',
        amount: 487,
        category: 'Travel',
        confidence: 75,
        isSelected: false,
      },
      {
        id: '3',
        date: 'Mar 14',
        description: 'STARBUCKS #1234',
        amount: 47.5,
        category: 'Meals',
        confidence: 80,
        isSelected: false,
      },
      {
        id: '4',
        date: 'Mar 14',
        description: 'OFFICE DEPOT',
        amount: 234,
        category: 'Supplies',
        confidence: 92,
        isSelected: false,
      },
      {
        id: '5',
        date: 'Mar 13',
        description: 'UBER *TRIP',
        amount: 32,
        category: 'Transport',
        confidence: 88,
        isSelected: false,
      },
    ],
  );

  const categories = [
    'Technology',
    'Travel',
    'Meals',
    'Supplies',
    'Transport',
    'Operations',
    'Entertainment',
    'Marketing',
    'Professional Services',
    'Utilities',
  ];

  // Filter and search logic
  const filteredTransactions = useMemo(() => {
    return transactions.filter((transaction) => {
      // Search filter
      if (
        searchTerm &&
        !transaction.description
          .toLowerCase()
          .includes(searchTerm.toLowerCase())
      ) {
        return false;
      }

      // Status filter
      if (filterStatus === 'needs-review' && transaction.confidence >= 90) {
        return false;
      }
      if (filterStatus === 'approved' && transaction.confidence < 90) {
        return false;
      }

      return true;
    });
  }, [transactions, searchTerm, filterStatus]);

  // Pagination
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTransactions = filteredTransactions.slice(startIndex, endIndex);

  const needsReviewCount = transactions.filter((t) => t.confidence < 90).length;
  const selectedCount = transactions.filter((t) => t.isSelected).length;

  const getConfidenceIcon = React.useCallback((confidence: number) => {
    if (confidence >= 90) {
      return <CheckCircle className="h-4 w-4 text-success" />;
    }
    return <AlertCircle className="h-4 w-4 text-amber-600" />;
  }, []);

  const getConfidenceText = React.useCallback((confidence: number) => {
    if (confidence >= 90) {
      return 'High conf';
    }
    return 'Needs review';
  }, []);

  const handleTransactionSelect = useCallback((id: string, selected: boolean) => {
    setTransactions((prev) =>
      prev.map((t) => (t.id === id ? { ...t, isSelected: selected } : t)),
    );
  }, []);

  const handleSelectAll = useCallback((selected: boolean) => {
    setTransactions((prev) =>
      prev.map((t) => ({ ...t, isSelected: selected })),
    );
  }, []);

  const handleCategoryChange = useCallback((id: string, newCategory: string) => {
    setTransactions((prev) =>
      prev.map((t) =>
        t.id === id
          ? { ...t, category: newCategory, confidence: 95 } // Boost confidence after manual selection
          : t,
      ),
    );
    onTransactionUpdate?.(id, { category: newCategory, confidence: 95 });

    toast({
      title: 'Category updated',
      description: `Transaction categorized as ${newCategory}`,
    });
  }, [onTransactionUpdate, toast]);

  const handleBulkApprove = () => {
    const selectedIds = transactions
      .filter((t) => t.isSelected)
      .map((t) => t.id);
    if (selectedIds.length === 0) {
      toast({
        title: 'No transactions selected',
        description: 'Please select transactions to approve',
        variant: 'destructive',
      });
      return;
    }

    setTransactions((prev) =>
      prev.map((t) =>
        selectedIds.includes(t.id)
          ? { ...t, confidence: 95, isSelected: false }
          : t,
      ),
    );

    onBulkApprove?.(selectedIds);

    toast({
      title: 'Transactions approved',
      description: `${selectedIds.length} transactions approved`,
    });
  };

  const handleExport = () => {
    onExport?.();
    toast({
      title: 'Export started',
      description: 'Your transaction export will download shortly',
    });
  };

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl md:text-2xl font-bold text-slate-900">
                Review & Approve Transactions
              </h1>
              <p className="text-sm text-slate-600 mt-1">March 2025</p>
            </div>
          </div>
        </div>
      </header>

      {/* Filter Bar */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 md:px-6 py-4">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <Select
                value={filterStatus}
                onValueChange={(value: 'all' | 'needs-review' | 'approved') =>
                  setFilterStatus(value)
                }
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="needs-review">Needs Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterMonth} onValueChange={setFilterMonth}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="this-month">This Month</SelectItem>
                  <SelectItem value="last-month">Last Month</SelectItem>
                  <SelectItem value="this-quarter">This Quarter</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </div>

            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-[250px]"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="max-w-7xl mx-auto px-4 md:px-6 py-4">
        <p className="text-sm text-slate-600">
          <span className="font-medium text-amber-600">
            {needsReviewCount} need review
          </span>
          {' · '}
          <span className="font-medium">
            {transactions.length} total this month
          </span>
          {selectedCount > 0 && (
            <>
              {' · '}
              <span className="font-medium text-success">
                {selectedCount} selected
              </span>
            </>
          )}
        </p>
      </div>

      {/* Transaction Table */}
      <div className="max-w-7xl mx-auto px-4 md:px-6 pb-8">
        <div className="bg-white border border-slate-200 rounded-lg overflow-hidden">
          {/* Desktop Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-100 border-b border-slate-200">
                <tr>
                  <th className="text-left p-3 w-12">
                    <Checkbox
                      checked={
                        selectedCount === transactions.length &&
                        transactions.length > 0
                      }
                      onCheckedChange={handleSelectAll}
                    />
                  </th>
                  <th className="text-left p-3 font-medium text-slate-700">
                    Date
                  </th>
                  <th className="text-left p-3 font-medium text-slate-700">
                    Description
                  </th>
                  <th className="text-right p-3 font-medium text-slate-700">
                    Amount
                  </th>
                  <th className="text-left p-3 font-medium text-slate-700">
                    Category
                  </th>
                  <th className="text-center p-3 font-medium text-slate-700 w-12">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody>
                {currentTransactions.map((transaction) => (
                  <TransactionRow
                    key={transaction.id}
                    transaction={transaction}
                    categories={categories}
                    onSelect={handleTransactionSelect}
                    onCategoryChange={handleCategoryChange}
                    getConfidenceIcon={getConfidenceIcon}
                    getConfidenceText={getConfidenceText}
                  />
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden">
            {currentTransactions.map((transaction) => (
              <TransactionCard
                key={transaction.id}
                transaction={transaction}
                categories={categories}
                onSelect={handleTransactionSelect}
                onCategoryChange={handleCategoryChange}
                getConfidenceIcon={getConfidenceIcon}
              />
            ))}
          </div>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className="w-8 h-8"
                  >
                    {page}
                  </Button>
                );
              })}
              {totalPages > 5 && <span className="text-slate-500">...</span>}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage((prev) => Math.min(totalPages, prev + 1))
              }
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <p className="text-sm text-slate-600">
            Showing {startIndex + 1}-
            {Math.min(endIndex, filteredTransactions.length)} of{' '}
            {filteredTransactions.length} transactions
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 mt-6">
          <Button
            onClick={handleBulkApprove}
            disabled={selectedCount === 0}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            Approve All ({selectedCount})
          </Button>

          <Button variant="outline" disabled={selectedCount === 0}>
            Update Selected
          </Button>

          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
// Only re-render when props change (transactions list or callbacks)
const ReviewTransactions = React.memo(ReviewTransactionsComponent);

export default ReviewTransactions;
