/**
 * Transaction Status Badge Component
 * Professional status indicators for transaction categorization states
 */
import React from 'react';
import {
  CheckCircle2,
  AlertCircle,
  Clock,
  User,
  Bot,
  XCircle,
} from 'lucide-react';
import { type Transaction } from '@/shared/types/categorization';
import { needsReview } from '../services/transactionService';

interface TransactionStatusBadgeProps {
  transaction: Transaction;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'minimal';
  showIcon?: boolean;
  className?: string;
}

export const TransactionStatusBadge: React.FC<TransactionStatusBadgeProps> = ({
  transaction,
  size = 'md',
  variant = 'default',
  showIcon = true,
  className = '',
}) => {
  // Determine the actual status based on transaction properties
  const getActualStatus = () => {
    if (transaction.is_user_modified) {
      return 'user_modified';
    }

    if (needsReview(transaction, 0.85)) {
      return 'needs_review';
    }

    if (
      transaction.ai_category_confidence &&
      transaction.ai_category_confidence >= 0.9
    ) {
      return 'high_confidence';
    }

    if (transaction.category_id || transaction.ai_suggested_category_id) {
      return 'ai_suggested';
    }

    return 'uncategorized';
  };

  const actualStatus = getActualStatus();

  // Size mappings
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base',
  };

  const iconSizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  // Status configurations
  const statusConfig = {
    user_modified: {
      label: 'User Modified',
      icon: User,
      default: 'bg-[#1A3F5F]/10 text-[#1A3F5F] border-[#1A3F5F]/20',
      outline: 'border-[#1A3F5F] text-[#1A3F5F] bg-transparent',
      minimal: 'text-[#1A3F5F] bg-transparent',
      description: 'Manually categorized by user',
    },
    high_confidence: {
      label: 'High Confidence',
      icon: CheckCircle2,
      default: 'bg-success/10 text-brand-primary border-brand-primary/20',
      outline: 'border-brand-primary text-brand-primary bg-transparent',
      minimal: 'text-brand-primary bg-transparent',
      description: 'AI is highly confident in categorization',
    },
    ai_suggested: {
      label: 'AI Suggested',
      icon: Bot,
      default: 'bg-[#3F1A5F]/10 text-[#3F1A5F] border-[#3F1A5F]/20',
      outline: 'border-[#3F1A5F] text-[#3F1A5F] bg-transparent',
      minimal: 'text-[#3F1A5F] bg-transparent',
      description: 'AI has suggested a category',
    },
    needs_review: {
      label: 'Needs Review',
      icon: AlertCircle,
      default: 'bg-[#F4A460]/10 text-[#F4A460] border-[#F4A460]/20',
      outline: 'border-[#F4A460] text-[#F4A460] bg-transparent',
      minimal: 'text-[#F4A460] bg-transparent',
      description: 'Low confidence, manual review recommended',
    },
    uncategorized: {
      label: 'Uncategorized',
      icon: XCircle,
      default: 'bg-error/10 text-[#DC2626] border-[#DC2626]/20',
      outline: 'border-[#DC2626] text-[#DC2626] bg-transparent',
      minimal: 'text-[#DC2626] bg-transparent',
      description: 'No category assigned',
    },
    processing: {
      label: 'Processing',
      icon: Clock,
      default: 'bg-gray-100 text-gray-800 border-gray-200',
      outline: 'border-gray-600 text-gray-600 bg-transparent',
      minimal: 'text-gray-600 bg-transparent',
      description: 'Currently being processed',
    },
  };

  const config = statusConfig[actualStatus];
  const Icon = config.icon;
  const variantClasses = config[variant];
  const borderClass = variant === 'minimal' ? '' : 'border';

  return (
    <span
      className={`inline-flex items-center gap-1 rounded-md font-medium ${borderClass} ${sizeClasses[size]} ${variantClasses} ${className}`}
      title={config.description}
    >
      {showIcon && <Icon className={iconSizeClasses[size]} />}
      {config.label}
    </span>
  );
};

/**
 * Simple Status Dot Component
 * Minimal dot indicator for status
 */
interface StatusDotProps {
  transaction: Transaction;
  size?: 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  className?: string;
}

export const StatusDot: React.FC<StatusDotProps> = ({
  transaction,
  size = 'md',
  showTooltip = true,
  className = '',
}) => {
  const getActualStatus = () => {
    if (transaction.is_user_modified) return 'user_modified';
    if (needsReview(transaction, 0.85)) return 'needs_review';
    if (
      transaction.ai_category_confidence &&
      transaction.ai_category_confidence >= 0.9
    )
      return 'high_confidence';
    if (transaction.category_id || transaction.ai_suggested_category_id)
      return 'ai_suggested';
    return 'uncategorized';
  };

  const actualStatus = getActualStatus();

  const sizeClasses = {
    sm: 'h-2 w-2',
    md: 'h-3 w-3',
    lg: 'h-4 w-4',
  };

  const statusColors = {
    user_modified: 'bg-[#1A3F5F]',
    high_confidence: 'bg-brand-primary',
    ai_suggested: 'bg-[#3F1A5F]',
    needs_review: 'bg-[#F4A460]',
    uncategorized: 'bg-error',
    processing: 'bg-gray-400',
  };

  const statusLabels = {
    user_modified: 'User Modified',
    high_confidence: 'High Confidence',
    ai_suggested: 'AI Suggested',
    needs_review: 'Needs Review',
    uncategorized: 'Uncategorized',
    processing: 'Processing',
  };

  return (
    <div
      className={`rounded-full ${sizeClasses[size]} ${statusColors[actualStatus]} ${className}`}
      title={showTooltip ? statusLabels[actualStatus] : undefined}
    />
  );
};

/**
 * Workflow Status Component
 * Shows the transaction's position in the review workflow
 */
interface WorkflowStatusProps {
  transaction: Transaction;
  showSteps?: boolean;
  className?: string;
}

export const WorkflowStatus: React.FC<WorkflowStatusProps> = ({
  transaction,
  showSteps = false,
  className = '',
}) => {
  const isUserModified = transaction.is_user_modified;
  const isHighConfidence =
    transaction.ai_category_confidence &&
    transaction.ai_category_confidence >= 0.9;
  const needsReviewFlag = needsReview(transaction, 0.85);
  const isUncategorized =
    !transaction.category_id && !transaction.ai_suggested_category_id;

  const steps = [
    {
      key: 'uploaded',
      label: 'Uploaded',
      completed: true,
      icon: CheckCircle2,
      color: 'text-success',
    },
    {
      key: 'ai_categorized',
      label: 'AI Categorized',
      completed: !isUncategorized,
      icon: Bot,
      color: !isUncategorized ? 'text-success' : 'text-gray-400',
    },
    {
      key: 'reviewed',
      label: 'Reviewed',
      completed: isUserModified || (!needsReviewFlag && isHighConfidence),
      icon: User,
      color:
        isUserModified || (!needsReviewFlag && isHighConfidence)
          ? 'text-success'
          : 'text-gray-400',
    },
    {
      key: 'approved',
      label: 'Approved',
      completed: isUserModified || isHighConfidence,
      icon: CheckCircle2,
      color:
        isUserModified || isHighConfidence ? 'text-success' : 'text-gray-400',
    },
  ];

  if (!showSteps) {
    // Simple progress indicator
    const completedSteps = steps.filter((step) => step.completed).length;
    const totalSteps = steps.length;
    const progressPercentage = (completedSteps / totalSteps) * 100;

    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="flex-1 bg-gray-200 rounded-full h-2">
          <div
            className="bg-green-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        <span className="text-sm text-gray-600">
          {completedSteps}/{totalSteps}
        </span>
      </div>
    );
  }

  // Detailed step view
  return (
    <div className={`flex items-center gap-4 ${className}`}>
      {steps.map((step, index) => {
        const Icon = step.icon;
        return (
          <div key={step.key} className="flex items-center gap-2">
            <Icon className={`h-4 w-4 ${step.color}`} />
            <span
              className={`text-sm ${step.completed ? 'text-gray-900' : 'text-gray-400'}`}
            >
              {step.label}
            </span>
            {index < steps.length - 1 && (
              <div className="w-8 h-px bg-gray-300 mx-2" />
            )}
          </div>
        );
      })}
    </div>
  );
};

/**
 * Combined Status Display
 * Comprehensive status display with multiple indicators
 */
interface CombinedStatusProps {
  transaction: Transaction;
  showBadge?: boolean;
  showDot?: boolean;
  showWorkflow?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const CombinedStatus: React.FC<CombinedStatusProps> = ({
  transaction,
  showBadge = true,
  showDot = false,
  showWorkflow = false,
  size = 'md',
  className = '',
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showDot && <StatusDot transaction={transaction} size={size} />}
      {showBadge && (
        <TransactionStatusBadge transaction={transaction} size={size} />
      )}
      {showWorkflow && <WorkflowStatus transaction={transaction} />}
    </div>
  );
};

export default TransactionStatusBadge;
