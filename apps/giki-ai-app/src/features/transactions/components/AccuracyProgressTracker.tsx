/**
 * Accuracy Progress Tracker - Visual 89% → 95% progress indicator
 * 
 * Shows real-time accuracy improvements with visual progress bars,
 * milestone tracking, and enhancement impact visualization
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Progress } from '@/shared/components/ui/progress';
import { Badge } from '@/shared/components/ui/badge';
import { 
  TrendingUp, 
  Target, 
  CheckCircle, 
  Clock, 
  Sparkles,
  Award,
  BarChart3
} from 'lucide-react';

interface AccuracyMilestone {
  accuracy: number;
  label: string;
  description: string;
  achieved: boolean;
  achievedAt?: Date;
  color: string;
}

interface AccuracyImprovementEvent {
  timestamp: Date;
  previousAccuracy: number;
  newAccuracy: number;
  improvement: number;
  source: string;
  description: string;
}

interface AccuracyProgressTrackerProps {
  currentAccuracy: number;
  baselineAccuracy: number;
  targetAccuracy: number;
  improvements: AccuracyImprovementEvent[];
  isLoading?: boolean;
  showDetailed?: boolean;
  className?: string;
}

export const AccuracyProgressTracker: React.FC<AccuracyProgressTrackerProps> = ({
  currentAccuracy,
  baselineAccuracy = 87,
  targetAccuracy = 95,
  improvements,
  isLoading = false,
  showDetailed = true,
  className = ""
}) => {
  const [animatedAccuracy, setAnimatedAccuracy] = useState(baselineAccuracy);
  const [recentImprovement, setRecentImprovement] = useState<AccuracyImprovementEvent | null>(null);

  // Animate accuracy changes
  useEffect(() => {
    const duration = 1000; // 1 second animation
    const steps = 60;
    const increment = (currentAccuracy - animatedAccuracy) / steps;
    
    if (Math.abs(increment) > 0.01) {
      const timer = setInterval(() => {
        setAnimatedAccuracy(prev => {
          const next = prev + increment;
          if (Math.abs(next - currentAccuracy) < Math.abs(increment)) {
            clearInterval(timer);
            return currentAccuracy;
          }
          return next;
        });
      }, duration / steps);
      
      return () => clearInterval(timer);
    }
  }, [currentAccuracy, animatedAccuracy]);

  // Track recent improvements
  useEffect(() => {
    if (improvements.length > 0) {
      const mostRecent = improvements[improvements.length - 1];
      setRecentImprovement(mostRecent);
    }
  }, [improvements]);

  const milestones: AccuracyMilestone[] = [
    {
      accuracy: 87,
      label: 'Baseline',
      description: 'Initial AI categorization',
      achieved: currentAccuracy >= 87,
      achievedAt: new Date(Date.now() - 3600000), // 1 hour ago
      color: 'bg-gray-500'
    },
    {
      accuracy: 90,
      label: 'Good',
      description: 'Quick review improvements',
      achieved: currentAccuracy >= 90,
      achievedAt: currentAccuracy >= 90 ? new Date(Date.now() - 1800000) : undefined, // 30 min ago
      color: 'bg-blue-500'
    },
    {
      accuracy: 92,
      label: 'Very Good',
      description: 'Company profile enhancement',
      achieved: currentAccuracy >= 92,
      achievedAt: currentAccuracy >= 92 ? new Date(Date.now() - 900000) : undefined, // 15 min ago
      color: 'bg-green-500'
    },
    {
      accuracy: 95,
      label: 'Excellent',
      description: 'Full MIS optimization',
      achieved: currentAccuracy >= 95,
      achievedAt: currentAccuracy >= 95 ? new Date() : undefined,
      color: 'bg-success'
    }
  ];

  const progressPercentage = ((currentAccuracy - baselineAccuracy) / (targetAccuracy - baselineAccuracy)) * 100;
  const totalImprovement = currentAccuracy - baselineAccuracy;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Progress Card */}
      <Card className="border-l-4 border-l-[#295343]">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl text-brand-primary">MIS Accuracy Progress</CardTitle>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-[#059669]" />
              <span className="text-2xl font-bold text-brand-primary">
                {animatedAccuracy.toFixed(1)}%
              </span>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Visual Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Baseline ({baselineAccuracy}%)</span>
              <span className="text-[#059669] font-medium">Target ({targetAccuracy}%)</span>
            </div>
            
            <div className="relative">
              <Progress 
                value={progressPercentage} 
                className="h-4"
              />
              
              {/* Milestone Markers */}
              <div className="absolute inset-0 flex items-center">
                {milestones.map((milestone, index) => {
                  const position = ((milestone.accuracy - baselineAccuracy) / (targetAccuracy - baselineAccuracy)) * 100;
                  return (
                    <div 
                      key={milestone.accuracy}
                      className="absolute flex flex-col items-center"
                      style={{ left: `${position}%`, transform: 'translateX(-50%)' }}
                    >
                      <div className={`w-3 h-3 rounded-full ${milestone.color} ${
                        milestone.achieved ? 'ring-2 ring-white' : 'opacity-50'
                      }`} />
                      <span className="text-xs mt-1 text-gray-600">{milestone.accuracy}%</span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="space-y-1">
              <div className="text-2xl font-bold text-brand-primary">
                +{totalImprovement.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Total Improvement</div>
            </div>
            
            <div className="space-y-1">
              <div className="text-2xl font-bold text-[#059669]">
                {milestones.filter(m => m.achieved).length}/{milestones.length}
              </div>
              <div className="text-sm text-gray-600">Milestones</div>
            </div>
            
            <div className="space-y-1">
              <div className="text-2xl font-bold text-brand-primary">
                {(targetAccuracy - currentAccuracy).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Remaining</div>
            </div>
          </div>

          {/* Recent Improvement */}
          {recentImprovement && (
            <div className="bg-gradient-to-r from-[#059669]/10 to-brand-primary/10 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Sparkles className="h-4 w-4 text-[#059669]" />
                <span className="text-sm font-medium text-brand-primary">Recent Improvement</span>
              </div>
              <div className="text-sm text-gray-600">
                <span className="font-semibold">+{recentImprovement.improvement.toFixed(1)}%</span> from {recentImprovement.source}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {recentImprovement.description}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed View */}
      {showDetailed && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Milestones */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg text-brand-primary flex items-center gap-2">
                <Target className="h-5 w-5" />
                Milestones
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {milestones.map((milestone) => (
                  <div 
                    key={milestone.accuracy}
                    className={`flex items-center gap-3 p-3 rounded-lg border ${
                      milestone.achieved ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      milestone.achieved ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'
                    }`}>
                      {milestone.achieved ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Clock className="h-4 w-4" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-brand-primary">{milestone.accuracy}%</span>
                        <Badge variant="outline" className="text-xs">
                          {milestone.label}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">{milestone.description}</div>
                      {milestone.achievedAt && (
                        <div className="text-xs text-gray-500">
                          Achieved {milestone.achievedAt.toLocaleTimeString()}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Improvement History */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg text-brand-primary flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Improvement History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {improvements.length > 0 ? (
                  improvements.slice(-5).reverse().map((improvement, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
                      <div className="w-8 h-8 rounded-full bg-brand-primary text-white flex items-center justify-center">
                        <TrendingUp className="h-4 w-4" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-brand-primary">
                            +{improvement.improvement.toFixed(1)}%
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {improvement.source}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600">{improvement.description}</div>
                        <div className="text-xs text-gray-500">
                          {improvement.timestamp.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Award className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No improvements yet</p>
                    <p className="text-sm">Start with Quick Review to see your first improvement!</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AccuracyProgressTracker;