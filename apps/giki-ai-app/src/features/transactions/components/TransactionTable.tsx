/**
 * Transaction Table - Professional B2B Excel-inspired data management
 * Replaces legacy TransactionTable with advanced features and professional design
 */

import React, { useState, useMemo, useCallback } from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  Table as ReactTable,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
// Removed Lucide imports - replaced with geometric icons
// ArrowUpDown → ↕, ChevronDown → ⌄, Download → ↓, Filter → ∷
// MoreHorizontal → ⋯, RefreshCw → ⟲, Search → ⌕

import { Badge } from '@/shared/components/ui/badge';
import { Button } from '@/shared/components/ui/button';
import { Checkbox } from '@/shared/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu';
import { Input } from '@/shared/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { ConfidenceIndicator } from './ConfidenceIndicator';
import { TransactionStatusBadge } from './TransactionStatusBadge';
import { EmptyTransactions } from '@/shared/components/ui/empty-states';
import { type Transaction } from '@/shared/types/categorization';

interface TransactionTableProps {
  transactions: Transaction[];
  isLoading?: boolean;
  onRefresh?: () => void;
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void;
  onBulkAction?: (action: string, transactionIds: string[]) => void;
  onTransactionUpdate?: (
    transactionId: string,
    updates: Partial<Transaction>,
  ) => void;
  onUpdateTransaction?: (transaction: Transaction) => void;
  onSaveChanges?: () => void;
  hasUnsavedChanges?: boolean;
  selectedRows?: Set<string>;
  onSelectionChange?: (selectedRows: Set<string>) => void;
  enableBulkActions?: boolean;
  enableExport?: boolean;
  enableFiltering?: boolean;
  pageSize?: number;
}

export function TransactionTable({
  transactions,
  isLoading = false,
  onRefresh,
  onExport,
  onBulkAction,
  onTransactionUpdate,
  enableBulkActions = true,
  enableExport = true,
  enableFiltering = true,
  pageSize = 50,
}: TransactionTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [globalFilter, setGlobalFilter] = useState('');

  // Professional data columns with Excel-inspired functionality
  const columns: ColumnDef<Transaction>[] = useMemo(
    () => [
      // Selection column for bulk actions
      ...(enableBulkActions
        ? [
            {
              id: 'select',
              header: ({ table }) => (
                <Checkbox
                  checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && 'indeterminate')
                  }
                  onCheckedChange={(value) =>
                    table.toggleAllPageRowsSelected(!!value)
                  }
                  aria-label="Select all"
                />
              ),
              cell: ({ row }) => (
                <Checkbox
                  checked={row.getIsSelected()}
                  onCheckedChange={(value) => row.toggleSelected(!!value)}
                  aria-label="Select row"
                />
              ),
              enableSorting: false,
              enableHiding: false,
            },
          ]
        : []),

      // Date column with professional formatting
      {
        accessorKey: 'date',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="font-semibold text-brand-primary hover:text-[#1D372E]"
          >
            Date
            <span className="ml-2 h-4 w-4 text-lg font-bold flex items-center justify-center">↕</span>
          </Button>
        ),
        cell: ({ row }) => (
          <div className="font-mono text-sm">
            {new Date(row.getValue('date')).toLocaleDateString('en-US', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
            })}
          </div>
        ),
      },

      // Description with truncation and tooltip
      {
        accessorKey: 'description',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="font-semibold text-brand-primary hover:text-[#1D372E]"
          >
            Description
            <span className="ml-2 h-4 w-4 text-lg font-bold flex items-center justify-center">↕</span>
          </Button>
        ),
        cell: ({ row }) => (
          <div className="max-w-xs">
            <div
              className="font-medium truncate"
              title={row.getValue('description')}
            >
              {row.getValue('description')}
            </div>
            {row.original.merchant && (
              <div className="text-sm text-gray-500 truncate">
                {row.original.merchant}
              </div>
            )}
          </div>
        ),
      },

      // Amount with currency formatting
      {
        accessorKey: 'amount',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="font-semibold text-brand-primary hover:text-[#1D372E] justify-end"
          >
            Amount
            <span className="ml-2 h-4 w-4 text-lg font-bold flex items-center justify-center">↕</span>
          </Button>
        ),
        cell: ({ row }) => {
          const rawAmount = row.getValue('amount');
          const amount =
            rawAmount != null &&
            typeof rawAmount !== 'object' &&
            (typeof rawAmount === 'string' || typeof rawAmount === 'number')
              ? parseFloat(
                  typeof rawAmount === 'string'
                    ? rawAmount
                    : rawAmount.toString(),
                )
              : 0;
          const isValidAmount = !isNaN(amount);
          const safeAmount = isValidAmount ? amount : 0;
          const currency = 'USD'; // Default to USD as currency is not in the Transaction type

          return (
            <div className="text-right font-mono">
              <span
                className={`font-semibold ${
                  safeAmount >= 0 ? 'text-success' : 'text-error'
                } ${!isValidAmount ? 'text-orange-600' : ''}`}
              >
                {isValidAmount ? (
                  new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: currency,
                  }).format(Math.abs(safeAmount))
                ) : (
                  <span
                    className="text-orange-600 text-xs"
                    title="Invalid amount data"
                  >
                    Data Issue
                  </span>
                )}
              </span>
            </div>
          );
        },
      },

      // Category with confidence score
      {
        accessorKey: 'category_path',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="font-semibold text-brand-primary hover:text-[#1D372E]"
          >
            Category
            <span className="ml-2 h-4 w-4 text-lg font-bold flex items-center justify-center">↕</span>
          </Button>
        ),
        cell: ({ row }) => (
          <div className="flex items-center component-gap-sm">
            <Badge
              variant="outline"
              className="font-mono text-xs max-w-[200px]"
              title={(() => {
                const categoryPath =
                  row.getValue('category_path') ||
                  row.original.ai_suggested_category_path ||
                  row.original.ai_suggested_category ||
                  'Business Expense';
                if (Array.isArray(categoryPath)) {
                  return categoryPath.join(' → ');
                }
                if (typeof categoryPath === 'string') {
                  return categoryPath;
                }
                return 'Business expense category assigned';
              })()}
            >
              {(() => {
                const categoryPath =
                  row.getValue('category_path') ||
                  row.original.ai_suggested_category_path ||
                  row.original.ai_suggested_category ||
                  'Business Expense';

                // Handle array format (hierarchical categories)
                if (Array.isArray(categoryPath)) {
                  const fullPath = categoryPath.join(' → ');
                  // Show abbreviated version if too long
                  if (categoryPath.length > 2) {
                    return `... → ${categoryPath.slice(-2).join(' → ')}`;
                  }
                  return fullPath;
                }

                // Handle null or undefined - already handled in fallback chain above
                if (categoryPath === null || categoryPath === undefined) {
                  return 'Business Expense';
                }

                // Handle string format (might contain slashes for hierarchy)
                if (typeof categoryPath === 'string') {
                  // Check if it's a hierarchical path with slashes
                  if (categoryPath.includes('/')) {
                    const parts = categoryPath
                      .split('/')
                      .filter((p) => p.trim());
                    if (parts.length > 2) {
                      // Show abbreviated version: "... → Parent → Child"
                      return `... → ${parts.slice(-2).join(' → ')}`;
                    } else if (parts.length > 1) {
                      // Show full hierarchy with arrows
                      return parts.join(' → ');
                    }
                  }
                  return categoryPath;
                }

                // Handle other types
                if (
                  typeof categoryPath === 'number' ||
                  typeof categoryPath === 'boolean'
                ) {
                  return String(categoryPath);
                }

                // For objects or other complex types
                return 'Business Expense';
              })()}
            </Badge>
            <ConfidenceIndicator
              confidence={row.original.ai_category_confidence || 0}
              size="sm"
              showText={false}
            />
          </div>
        ),
      },

      // GL Code (if available)
      {
        accessorKey: 'gl_code',
        header: 'GL Code',
        cell: ({ row }) => {
          const glCode = row.getValue('gl_code');
          if (!glCode || glCode === null || glCode === undefined) {
            return <span className="text-gray-400 text-xs">—</span>;
          }
          let glCodeString: string;
          if (typeof glCode === 'string') {
            glCodeString = glCode;
          } else if (
            typeof glCode === 'number' ||
            typeof glCode === 'boolean'
          ) {
            glCodeString = String(glCode);
          } else {
            // For objects or other complex types, return dash
            return <span className="text-gray-400 text-xs">—</span>;
          }
          return (
            <Badge variant="secondary" className="font-mono text-xs">
              {glCodeString}
            </Badge>
          );
        },
      },

      // Status with professional styling
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => (
          <TransactionStatusBadge transaction={row.original} />
        ),
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },

      // Account information
      {
        accessorKey: 'account',
        header: 'Account',
        cell: ({ row }) => (
          <div className="font-mono text-sm">{row.getValue('account')}</div>
        ),
      },

      // Actions dropdown
      {
        id: 'actions',
        enableHiding: false,
        cell: ({ row }) => {
          const transaction = row.original;

          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <span className="h-4 w-4 text-lg font-bold flex items-center justify-center">⋯</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => navigator.clipboard.writeText(transaction.id)}
                >
                  Copy transaction ID
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>View details</DropdownMenuItem>
                <DropdownMenuItem>Edit transaction</DropdownMenuItem>
                <DropdownMenuItem>Flag for review</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    [enableBulkActions],
  );

  const table: ReactTable<Transaction> = useReactTable({
    data: transactions,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize,
      },
    },
  });

  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const selectedTransactionIds = selectedRows.map((row) => row.original.id);

  const handleBulkAction = useCallback(
    (action: string) => {
      if (onBulkAction && selectedTransactionIds.length > 0) {
        onBulkAction(action, selectedTransactionIds);
        setRowSelection({});
      }
    },
    [onBulkAction, selectedTransactionIds],
  );

  return (
    <div className="w-full">
      {/* Professional toolbar - Enhanced mobile layout */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 form-field-spacing">
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 flex-1">
          {/* Global search - Full width on mobile */}
          <div className="relative flex-1 sm:flex-initial sm:max-w-sm">
            <span className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 text-lg font-bold flex items-center justify-center">⌕</span>
            <Input
              placeholder="Search transactions..."
              value={globalFilter ?? ''}
              onChange={(event) => setGlobalFilter(String(event.target.value))}
              className="pl-10 text-sm"
            />
          </div>

          {/* Status filter */}
          {enableFiltering && (
            <Select
              value={
                (table.getColumn('status')?.getFilterValue() as string[])?.join(
                  ',',
                ) || ''
              }
              onValueChange={(value) =>
                table
                  .getColumn('status')
                  ?.setFilterValue(value ? [value] : undefined)
              }
            >
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Filter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="reviewed">Reviewed</SelectItem>
                <SelectItem value="processed">Processed</SelectItem>
                <SelectItem value="flagged">Flagged</SelectItem>
              </SelectContent>
            </Select>
          )}

          {/* Column visibility - Hidden on mobile */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="hidden sm:flex ml-auto">
                <span className="mr-2 h-4 w-4 text-lg font-bold flex items-center justify-center">∷</span>
                Columns <span className="ml-2 h-4 w-4 text-lg font-bold flex items-center justify-center">⌄</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex flex-wrap items-center gap-2 sm:gap-3">
          {/* Bulk actions - Responsive styling */}
          {enableBulkActions && selectedTransactionIds.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="text-sm px-3 py-2">
                  <span className="hidden sm:inline">Actions </span>({selectedTransactionIds.length})
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleBulkAction('approve')}>
                  Approve selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('flag')}>
                  Flag for review
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleBulkAction('categorize')}
                >
                  Batch categorize
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => handleBulkAction('export')}
                  className="text-blue-600"
                >
                  Export selected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Export options - Mobile-friendly */}
          {enableExport && onExport && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="text-sm px-3 py-2">
                  <span className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 text-sm sm:text-lg font-bold flex items-center justify-center">↓</span>
                  <span className="hidden sm:inline">Export</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => onExport('csv')}>
                  Export as CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onExport('excel')}>
                  Export as Excel
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onExport('pdf')}>
                  Export as PDF
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Refresh button - Mobile-optimized */}
          {onRefresh && (
            <Button variant="outline" onClick={onRefresh} disabled={isLoading} className="px-3 py-2">
              <span
                className={`h-3 w-3 sm:h-4 sm:w-4 text-sm sm:text-lg font-bold flex items-center justify-center ${isLoading ? 'animate-spin' : ''}`}
              >
                ⟲
              </span>
            </Button>
          )}
        </div>
      </div>

      {/* Professional data table - Mobile responsive */}
      <div className="rounded-md border border-gray-200 bg-white overflow-hidden">
        <div className="overflow-x-auto">
          <Table className="min-w-full">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="bg-gray-50">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="font-semibold text-brand-primary"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className="hover:bg-gray-50"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <span className="h-6 w-6 text-2xl font-bold flex items-center justify-center animate-spin text-brand-primary">⟲</span>
                      <span className="ml-2 text-gray-500">
                        Loading transactions...
                      </span>
                    </div>
                  ) : (
                    <div className="col-span-full py-8">
                      <EmptyTransactions />
                    </div>
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          </Table>
        </div>
      </div>

      {/* Professional pagination - Mobile responsive */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4 card-padding-sm">
        <div className="text-xs sm:text-sm text-gray-500 order-2 sm:order-1">
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-6 w-full sm:w-auto order-1 sm:order-2">
          <div className="flex items-center gap-2 sm:gap-3">
            <p className="text-xs sm:text-sm font-medium whitespace-nowrap">Rows per page</p>
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value) => {
                table.setPageSize(Number(value));
              }}
            >
              <SelectTrigger className="h-8 w-[70px] text-sm">
                <SelectValue
                  placeholder={table.getState().pagination.pageSize}
                />
              </SelectTrigger>
              <SelectContent side="top">
                {[25, 50, 100, 200].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex w-full sm:w-[100px] items-center justify-center text-xs sm:text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{' '}
            {table.getPageCount()}
          </div>
          <div className="flex items-center gap-1 sm:gap-2 justify-center">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 sm:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to first page</span>
              <span className="text-xs">⟨⟨</span>
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0 touch-target"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to previous page</span>
              <span className="text-sm">⟨</span>
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0 touch-target"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to next page</span>
              <span className="text-sm">⟩</span>
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 sm:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to last page</span>
              <span className="text-xs">⟩⟩</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TransactionTable;
