/**
 * Bulk Action Panel Component
 * Professional bulk operations interface for transaction review workflow
 */
import React, { useState, useCallback } from 'react';
import {
  CheckCircle,
  Download,
  Tag,
  Archive,
  RefreshCw,
  X,
  AlertTriangle,
  Users,
} from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Textarea } from '@/shared/components/ui/textarea';
import { toast } from '@/shared/components/ui/use-toast';
import { type Transaction } from '@/shared/types/categorization';
import { type Category } from '@/features/categories/services/categoryService';

interface BulkActionPanelProps {
  selectedTransactions: Transaction[];
  selectedIds: Set<string>;
  categories: Category[];
  isProcessing?: boolean;
  onApproveSelected: () => Promise<void>;
  onBulkCategoryChange: (
    categoryId: number,
    categoryName: string,
  ) => Promise<void>;
  onExportSelected: (format: 'csv' | 'excel' | 'pdf') => Promise<void>;
  onClearSelection: () => void;
  onSelectAll?: () => void;
  className?: string;
}

export const BulkActionPanel: React.FC<BulkActionPanelProps> = ({
  selectedTransactions,
  selectedIds,
  categories,
  isProcessing = false,
  onApproveSelected,
  onBulkCategoryChange,
  onExportSelected,
  onClearSelection,
  onSelectAll,
  className = '',
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [bulkOperation, setBulkOperation] = useState<string>('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [approvalNotes, setApprovalNotes] = useState('');
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel' | 'pdf'>(
    'excel',
  );

  const selectionCount = selectedIds.size;
  const totalAmount = selectedTransactions.reduce(
    (sum, t) => sum + Math.abs(t.amount),
    0,
  );
  const needsReviewCount = selectedTransactions.filter(
    (t) => !t.ai_category_confidence || t.ai_category_confidence < 0.85,
  ).length;

  const handleBulkCategoryChange = useCallback(async () => {
    if (!selectedCategoryId) {
      toast({
        title: 'Category Required',
        description: 'Please select a category for bulk assignment.',
        variant: 'destructive',
      });
      return;
    }

    const category = categories.find(
      (c) => c.id.toString() === selectedCategoryId,
    );
    if (!category) return;

    try {
      await onBulkCategoryChange(category.id, category.name);
      setSelectedCategoryId('');
      toast({
        title: 'Bulk Category Update',
        description: `${selectionCount} transactions updated to "${category.name}".`,
      });
    } catch (_error) {
      toast({
        title: 'Update Failed',
        description: 'Failed to update transaction categories.',
        variant: 'destructive',
      });
    }
  }, [selectedCategoryId, categories, selectionCount, onBulkCategoryChange]);

  const handleExport = useCallback(async () => {
    try {
      await onExportSelected(exportFormat);
      toast({
        title: 'Export Started',
        description: `Exporting ${selectionCount} transactions to ${exportFormat.toUpperCase()}.`,
      });
    } catch (_error) {
      toast({
        title: 'Export Failed',
        description: 'Failed to export selected transactions.',
        variant: 'destructive',
      });
    }
  }, [exportFormat, selectionCount, onExportSelected]);

  const handleApproval = useCallback(async () => {
    try {
      await onApproveSelected();
      setApprovalNotes('');
    } catch (_error) {
      // Error handling is done in the parent component
    }
  }, [onApproveSelected]);

  if (selectionCount === 0) {
    return null;
  }

  return (
    <Card
      className={`section-spacing-sm border border-gray-200 shadow-sm overflow-hidden ${className}`}
    >
      <div className="bg-brand-primary text-white px-6 py-4">
        <div className="flex items-center component-gap-sm">
          <Users className="h-5 w-5" />
          <h3 className="text-base font-semibold">
            ⊞ Bulk Actions ({selectionCount} selected)
          </h3>
        </div>
      </div>
      <div className="card-padding-lg">
        {/* Header with Selection Summary */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center component-gap-sm">
              <span className="font-medium text-brand-primary">
                {selectionCount} transaction{selectionCount > 1 ? 's' : ''}{' '}
                selected
              </span>
            </div>

            <div className="text-sm text-gray-600">
              Total: ${totalAmount.toFixed(2)}
            </div>

            {needsReviewCount > 0 && (
              <div className="flex items-center gap-1 text-sm text-yellow-600">
                <AlertTriangle className="h-3 w-3" />
                {needsReviewCount} need{needsReviewCount > 1 ? '' : 's'} review
              </div>
            )}
          </div>

          <div className="flex items-center component-gap-sm">
            {onSelectAll && (
              <Button
                variant="outline"
                size="sm"
                onClick={onSelectAll}
                className="text-xs"
              >
                Select All
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={onClearSelection}
              className="text-xs border-brand-primary text-brand-primary hover:bg-green-50"
            >
              <X className="h-3 w-3 mr-1" />
              Clear
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2 mb-4">
          <Button
            size="sm"
            onClick={handleApproval}
            disabled={isProcessing}
            className="bg-brand-primary hover:bg-brand-primary-hover text-white"
          >
            {isProcessing ? (
              <>
                <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                Approving...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-1" />
                Approve All ({selectionCount})
              </>
            )}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={isProcessing}
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            disabled={isProcessing}
          >
            <Tag className="h-4 w-4 mr-1" />
            Bulk Category
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              setBulkOperation(bulkOperation === 'archive' ? '' : 'archive')
            }
            disabled={isProcessing}
            className={bulkOperation === 'archive' ? 'bg-gray-100' : ''}
          >
            <Archive className="h-4 w-4 mr-1" />
            More Actions
          </Button>
        </div>

        {/* Advanced Options */}
        {showAdvanced && (
          <div className="border-t pt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Bulk Category Assignment */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Assign Category to All Selected:
                </label>
                <div className="flex gap-2">
                  <Select
                    value={selectedCategoryId}
                    onValueChange={setSelectedCategoryId}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="Choose category..." />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem
                          key={category.id}
                          value={category.id.toString()}
                        >
                          {category.name}
                          {category.gl_code && (
                            <span className="text-xs text-gray-500 ml-1">
                              ({category.gl_code})
                            </span>
                          )}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={handleBulkCategoryChange}
                    disabled={!selectedCategoryId || isProcessing}
                    size="sm"
                  >
                    Apply
                  </Button>
                </div>
              </div>

              {/* Export Options */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Export Format:
                </label>
                <div className="flex gap-2">
                  <Select
                    value={exportFormat}
                    onValueChange={(value: 'csv' | 'excel' | 'pdf') =>
                      setExportFormat(value)
                    }
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                      <SelectItem value="csv">CSV (.csv)</SelectItem>
                      <SelectItem value="pdf">PDF Report</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={handleExport}
                    disabled={isProcessing}
                    size="sm"
                    variant="outline"
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                </div>
              </div>
            </div>

            {/* Approval Notes */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Approval Notes (Optional):
              </label>
              <Textarea
                value={approvalNotes}
                onChange={(e) => setApprovalNotes(e.target.value)}
                placeholder="Add notes for this bulk approval..."
                className="resize-none"
                rows={2}
              />
            </div>
          </div>
        )}

        {/* Additional Operations */}
        {bulkOperation === 'archive' && (
          <div className="border-t pt-4 mt-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">
                    Archive Selected Transactions
                  </h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    This will move {selectionCount} transactions to the archive.
                    They can be restored later if needed.
                  </p>
                  <div className="flex gap-2 mt-3">
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-yellow-700 border-yellow-300 hover:bg-yellow-100"
                      disabled={isProcessing}
                    >
                      <Archive className="h-4 w-4 mr-1" />
                      Archive {selectionCount} Transaction
                      {selectionCount > 1 ? 's' : ''}
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setBulkOperation('')}
                      className="text-gray-600"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Progress Indicator */}
        {isProcessing && (
          <div className="border-t pt-4 mt-4">
            <div className="flex items-center component-gap-sm">
              <RefreshCw className="h-4 w-4 text-brand-primary animate-spin" />
              <span className="text-sm text-brand-primary">
                Processing {selectionCount} transactions...
              </span>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

/**
 * Bulk Action Summary Component
 * Shows summary of what will happen with bulk operations
 */
interface BulkActionSummaryProps {
  selectedTransactions: Transaction[];
  operation: 'approve' | 'categorize' | 'export' | 'archive';
  targetCategory?: Category;
  className?: string;
}

export const BulkActionSummary: React.FC<BulkActionSummaryProps> = ({
  selectedTransactions,
  operation,
  targetCategory,
  className = '',
}) => {
  const count = selectedTransactions.length;
  const totalAmount = selectedTransactions.reduce(
    (sum, t) => sum + Math.abs(t.amount),
    0,
  );

  const summaryData = {
    approve: {
      title: `Approve ${count} Transactions`,
      description: `This will mark all selected transactions as approved and ready for export.`,
      icon: CheckCircle,
      color: 'text-brand-primary',
      bgColor: 'bg-green-50',
      borderColor: 'border-brand-primary',
    },
    categorize: {
      title: `Categorize ${count} Transactions`,
      description: `This will assign "${targetCategory?.name}" to all selected transactions.`,
      icon: Tag,
      color: 'text-brand-primary',
      bgColor: 'bg-green-50',
      borderColor: 'border-brand-primary',
    },
    export: {
      title: `Export ${count} Transactions`,
      description: `This will generate a downloadable file with all selected transaction data.`,
      icon: Download,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
    },
    archive: {
      title: `Archive ${count} Transactions`,
      description: `This will move selected transactions to the archive. They can be restored later.`,
      icon: Archive,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
    },
  };

  const config = summaryData[operation];
  const Icon = config.icon;

  return (
    <Card className={`${config.bgColor} ${config.borderColor} ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Icon className={`h-5 w-5 ${config.color} mt-0.5`} />
          <div>
            <h4 className={`font-medium ${config.color}`}>{config.title}</h4>
            <p className="text-sm text-gray-600 mt-1">{config.description}</p>
            <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
              <span>Total Amount: ${totalAmount.toFixed(2)}</span>
              <span>
                {count} transaction{count > 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BulkActionPanel;
