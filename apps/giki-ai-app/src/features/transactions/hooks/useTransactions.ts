/**
 * Transactions Hook
 *
 * Custom hook for managing transaction operations.
 */

import { useState, useCallback } from 'react';
import {
  TransactionFilter,
  PaginatedTransactions,
  TransactionSummary,
} from '../types/transaction';
import { Transaction } from '@/shared/types/categorization';
import {
  FetchTransactionsParams,
  updateTransactionCategory,
  fetchTransactions as fetchTransactionsAPI,
} from '../services/transactionService';

// Helper function to calculate transaction summary
const calculateSummary = (transactions: Transaction[]): TransactionSummary => {
  const totalIncome = transactions
    .filter((t) => t.amount > 0)
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpenses = transactions
    .filter((t) => t.amount < 0)
    .reduce((sum, t) => sum + Math.abs(t.amount), 0);

  const netIncome = totalIncome - totalExpenses;
  const transactionCount = transactions.length;
  const averageTransaction =
    transactionCount > 0 ? (totalIncome + totalExpenses) / transactionCount : 0;

  return {
    totalIncome,
    totalExpenses,
    netIncome,
    transactionCount,
    averageTransaction,
  };
};

export interface UseTransactionsReturn {
  transactions: Transaction[];
  paginatedData: PaginatedTransactions | null;
  summary: TransactionSummary | null;
  isLoading: boolean;
  error: string | null;
  fetchTransactions: (
    filter?: TransactionFilter,
    page?: number,
  ) => Promise<void>;
  updateTransaction: (
    id: string,
    updates: Partial<Transaction>,
  ) => Promise<void>;
  deleteTransaction: (id: string) => Promise<void>;
  categorizeTransaction: (id: string, categoryId: string) => Promise<void>;
}

export const useTransactions = (): UseTransactionsReturn => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [paginatedData, setPaginatedData] =
    useState<PaginatedTransactions | null>(null);
  const [summary, setSummary] = useState<TransactionSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTransactions = useCallback(
    async (filter?: TransactionFilter, page = 1): Promise<void> => {
      setIsLoading(true);
      setError(null);

      try {
        // Map filter to API params
        const extendedFilter = filter as {
          uploadId?: string;
          status?: string;
          pageSize?: number;
          sortBy?: string;
          sortDirection?: string;
        };
        const params: FetchTransactionsParams = {
          uploadId: extendedFilter?.uploadId,
          startDate: filter?.dateRange?.start.toISOString().split('T')[0],
          endDate: filter?.dateRange?.end.toISOString().split('T')[0],
          status: extendedFilter?.status,
          categoryId: filter?.categories?.[0], // Use first category if multiple
          searchTerm: filter?.searchTerm,
          page,
          pageSize: extendedFilter?.pageSize || 50,
          sortBy: extendedFilter?.sortBy,
          sortDirection: extendedFilter?.sortDirection as
            | 'asc'
            | 'desc'
            | undefined,
          minAmount: filter?.amountRange?.min,
          maxAmount: filter?.amountRange?.max,
        };

        const response = await fetchTransactionsAPI(params);

        // Handle the response format
        if (response && typeof response === 'object' && 'items' in response) {
          // It's a paginated response
          const paginatedResponse = response as {
            items: Transaction[];
            total_count: number;
            page: number;
            page_size: number;
            total_pages: number;
          };
          setTransactions(paginatedResponse.items);
          setPaginatedData({
            items: paginatedResponse.items,
            total: paginatedResponse.total_count || 0,
            page: paginatedResponse.page || 1,
            limit: paginatedResponse.page_size || 10,
            hasNext:
              (paginatedResponse.page || 1) <
              (paginatedResponse.total_pages || 1),
            hasPrev: (paginatedResponse.page || 1) > 1,
          });

          // Calculate summary from transactions
          const summary = calculateSummary(paginatedResponse.items);
          setSummary(summary);
        } else if (Array.isArray(response)) {
          // Legacy array response
          const transactionArray = response;
          setTransactions(transactionArray);
          setPaginatedData({
            items: transactionArray,
            total: transactionArray.length,
            page: 1,
            limit: transactionArray.length,
            hasNext: false,
            hasPrev: false,
          });

          const summary = calculateSummary(transactionArray);
          setSummary(summary);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to fetch transactions';
        setError(errorMessage);
        console.error('Error fetching transactions:', err);
      } finally {
        setIsLoading(false);
      }
    },
    [],
  );

  const updateTransaction = useCallback(
    async (id: string, updates: Partial<Transaction>): Promise<void> => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Call real API endpoint to update transaction
        const response = await fetch(`/api/v1/transactions/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
          body: JSON.stringify(updates),
        });
        
        if (!response.ok) {
          throw new Error(`Failed to update transaction: ${response.statusText}`);
        }
        
        const updatedTransaction = await response.json();
        
        // Update local state with server response
        setTransactions((prev) =>
          prev.map((transaction) =>
            transaction.id === id ? updatedTransaction : transaction,
          ),
        );
        
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to update transaction';
        setError(errorMessage);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [],
  );

  const deleteTransaction = useCallback(async (id: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      // Call real API endpoint to delete transaction
      const response = await fetch(`/api/v1/transactions/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete transaction: ${response.statusText}`);
      }
      
      // Remove from local state after successful deletion
      setTransactions((prev) =>
        prev.filter((transaction) => transaction.id !== id),
      );
      
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to delete transaction';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const categorizeTransaction = useCallback(
    async (id: string, categoryId: string): Promise<void> => {
      try {
        // Use the imported updateTransactionCategory function

        // Call the real API
        const updatedTransaction = await updateTransactionCategory(
          id,
          parseInt(categoryId),
        );

        // Update local state with the response
        setTransactions((prev) =>
          prev.map((transaction) =>
            transaction.id === id ? updatedTransaction : transaction,
          ),
        );

        // Update paginated data if it exists
        if (paginatedData) {
          setPaginatedData({
            ...paginatedData,
            items: paginatedData?.items?.map((transaction) =>
              transaction.id === id ? updatedTransaction : transaction,
            ),
          });
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to categorize transaction';
        setError(errorMessage);
        throw err;
      }
    },
    [paginatedData],
  );

  return {
    transactions,
    paginatedData,
    summary,
    isLoading,
    error,
    fetchTransactions,
    updateTransaction,
    deleteTransaction,
    categorizeTransaction,
  };
};
