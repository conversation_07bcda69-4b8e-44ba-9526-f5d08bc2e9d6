/**
 * Transaction Feature Types
 *
 * Type definitions for transaction management functionality.
 */

import { Transaction as SharedTransaction } from '@/shared/types/categorization';

// Use the shared Transaction type for consistency with API
export type Transaction = SharedTransaction;

export interface FeatureTransaction {
  id: string;
  amount: number;
  description: string;
  date: Date;
  category_id?: string;
  category_name?: string;
  account_id: string;
  account_name?: string;
  transaction_type: 'debit' | 'credit';
  balance?: number;
  reference?: string;
  notes?: string;
  tags?: string[];
  is_recurring?: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface TransactionFilter {
  dateRange?: {
    start: Date;
    end: Date;
  };
  categories?: string[];
  accounts?: string[];
  amountRange?: {
    min: number;
    max: number;
  };
  transactionType?: 'debit' | 'credit';
  searchTerm?: string;
}

export interface PaginatedTransactions {
  items: Transaction[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface TransactionSummary {
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  transactionCount: number;
  averageTransaction: number;
}
