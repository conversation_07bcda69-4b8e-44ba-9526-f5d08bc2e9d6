/**
 * TransactionReviewPage Component Tests
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { 
  render, 
  mockFetch, 
  setupAuthenticatedState,
  mockTransaction 
} from '@/test/utils';
import TransactionReviewPage from '../TransactionReviewPage';

// Extended mock transaction data
const mockTransactions = [
  {
    ...mockTransaction,
    id: '1',
    description: 'Amazon Web Services',
    amount: -299.99,
    category: 'Technology > Cloud Services',
    ai_confidence: 0.95,
    needs_review: false,
  },
  {
    ...mockTransaction,
    id: '2',
    description: 'Office Supplies Inc',
    amount: -156.78,
    category: 'Office > Supplies',
    ai_confidence: 0.72,
    needs_review: true,
  },
  {
    ...mockTransaction,
    id: '3',
    description: 'Client Payment',
    amount: 2500.00,
    category: 'Income > Consulting',
    ai_confidence: 0.88,
    needs_review: false,
  },
];

const mockCategories = [
  { id: '1', name: 'Technology', parent_id: null },
  { id: '2', name: 'Cloud Services', parent_id: '1' },
  { id: '3', name: 'Office', parent_id: null },
  { id: '4', name: 'Supplies', parent_id: '3' },
  { id: '5', name: 'Income', parent_id: null },
  { id: '6', name: 'Consulting', parent_id: '5' },
];

describe('TransactionReviewPage', () => {
  beforeEach(() => {
    setupAuthenticatedState();
  });

  it('renders transaction review interface correctly', async () => {
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      page: 1,
      total_pages: 1,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/transaction review/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/3.*transactions/i)).toBeInTheDocument();
    expect(screen.getByRole('table')).toBeInTheDocument();
  });

  it('displays transaction list with all columns', async () => {
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/Amazon Web Services/i)).toBeInTheDocument();
    });
    
    // Check table headers
    expect(screen.getByText(/date/i)).toBeInTheDocument();
    expect(screen.getByText(/description/i)).toBeInTheDocument();
    expect(screen.getByText(/amount/i)).toBeInTheDocument();
    expect(screen.getByText(/category/i)).toBeInTheDocument();
    expect(screen.getByText(/confidence/i)).toBeInTheDocument();
    
    // Check transaction data
    expect(screen.getByText(/Amazon Web Services/i)).toBeInTheDocument();
    expect(screen.getByText(/-299\.99/i)).toBeInTheDocument();
    expect(screen.getByText(/Technology.*Cloud Services/i)).toBeInTheDocument();
    expect(screen.getByText(/95%/i)).toBeInTheDocument();
  });

  it('highlights transactions needing review', async () => {
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/Office Supplies Inc/i)).toBeInTheDocument();
    });
    
    // Transaction needing review should have visual indicator
    const reviewRow = screen.getByText(/Office Supplies Inc/i).closest('tr');
    expect(reviewRow).toHaveClass(/needs-review|highlighted|warning/);
    
    // Check confidence indicator
    expect(screen.getByText(/72%/i)).toBeInTheDocument();
  });

  it('allows filtering transactions by review status', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/filter/i)).toBeInTheDocument();
    });
    
    // Filter by "needs review"
    const filterDropdown = screen.getByRole('combobox', { name: /filter/i });
    await user.selectOptions(filterDropdown, 'needs_review');
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('needs_review=true'),
        expect.any(Object)
      );
    });
  });

  it('supports search functionality', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText(/search transactions/i)).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText(/search transactions/i);
    await user.type(searchInput, 'Amazon');
    
    // Should debounce and search
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('search=Amazon'),
        expect.any(Object)
      );
    }, { timeout: 1000 });
  });

  it('allows inline category editing', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/Office Supplies Inc/i)).toBeInTheDocument();
    });
    
    // Click on category cell to edit
    const categoryCell = screen.getByText(/Office.*Supplies/i);
    await user.click(categoryCell);
    
    // Should show category dropdown
    await waitFor(() => {
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });
    
    // Select new category
    const categorySelect = screen.getByRole('combobox');
    await user.selectOptions(categorySelect, 'Technology > Cloud Services');
    
    // Should save automatically
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/transactions/2/category'),
        expect.objectContaining({
          method: 'PUT',
        })
      );
    });
  });

  it('supports bulk selection and operations', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getAllByRole('checkbox')).toHaveLength(4); // 3 rows + select all
    });
    
    // Select all transactions
    const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
    await user.click(selectAllCheckbox);
    
    // Check bulk action toolbar appears
    expect(screen.getByText(/3.*selected/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /bulk approve/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /bulk categorize/i })).toBeInTheDocument();
  });

  it('performs bulk approval operation', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getAllByRole('checkbox')).toHaveLength(4);
    });
    
    // Select first two transactions
    const checkboxes = screen.getAllByRole('checkbox');
    await user.click(checkboxes[1]); // First transaction
    await user.click(checkboxes[2]); // Second transaction
    
    expect(screen.getByText(/2.*selected/i)).toBeInTheDocument();
    
    // Mock bulk approval response
    mockFetch({ approved: 2, updated_transactions: ['1', '2'] });
    
    const bulkApproveButton = screen.getByRole('button', { name: /bulk approve/i });
    await user.click(bulkApproveButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/transactions/bulk-approve'),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('"transaction_ids":["1","2"]'),
        })
      );
    });
  });

  it('shows AI suggestion tooltips', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/95%/i)).toBeInTheDocument();
    });
    
    // Hover over confidence score
    const confidenceScore = screen.getByText(/95%/i);
    await user.hover(confidenceScore);
    
    await waitFor(() => {
      expect(screen.getByText(/ai confidence/i)).toBeInTheDocument();
      expect(screen.getByText(/high confidence.*categorization/i)).toBeInTheDocument();
    });
  });

  it('supports pagination through large transaction lists', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      transactions: mockTransactions,
      total: 50,
      page: 1,
      total_pages: 5,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/page 1 of 5/i)).toBeInTheDocument();
    });
    
    // Test next page
    const nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('page=2'),
        expect.any(Object)
      );
    });
  });

  it('handles sorting by different columns', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/date/i)).toBeInTheDocument();
    });
    
    // Click date header to sort
    const dateHeader = screen.getByText(/date/i);
    await user.click(dateHeader);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('sort=date'),
        expect.any(Object)
      );
    });
    
    // Click again to reverse sort
    await user.click(dateHeader);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('sort=-date'),
        expect.any(Object)
      );
    });
  });

  it('displays empty state when no transactions exist', async () => {
    mockFetch({
      transactions: [],
      total: 0,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/no transactions to review/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/upload your first file/i)).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /upload files/i })).toBeInTheDocument();
  });

  it('handles API errors gracefully', async () => {
    // Mock API error
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/unable to load transactions/i)).toBeInTheDocument();
    });
    
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
  });

  it('provides undo functionality for recent changes', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      transactions: mockTransactions,
      total: 3,
      categories: mockCategories,
    });
    
    render(<TransactionReviewPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/Office Supplies Inc/i)).toBeInTheDocument();
    });
    
    // Make a category change
    const categoryCell = screen.getByText(/Office.*Supplies/i);
    await user.click(categoryCell);
    
    const categorySelect = screen.getByRole('combobox');
    await user.selectOptions(categorySelect, 'Technology > Cloud Services');
    
    // Mock successful update
    mockFetch({ success: true, transaction_id: '2' });
    
    await waitFor(() => {
      expect(screen.getByText(/category updated/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /undo/i })).toBeInTheDocument();
    });
    
    // Test undo
    const undoButton = screen.getByRole('button', { name: /undo/i });
    await user.click(undoButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/transactions/2/category'),
        expect.objectContaining({
          method: 'PUT',
          body: expect.stringContaining('Office > Supplies'),
        })
      );
    });
  });
});