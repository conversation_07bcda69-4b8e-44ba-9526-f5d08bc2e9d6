/**
 * Review and Improvements Page - Comprehensive MIS enhancement interface
 * 
 * Combines accuracy tracking, enhancement options, and review workflows
 * in a unified interface for continuous MIS improvement
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/shared/components/ui/card';
import { <PERSON><PERSON> } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs';
import { 
  ArrowLeft, 
  TrendingUp, 
  CheckCircle,
  Clock,
  Sparkles,
  BarChart3,
  AlertCircle
} from 'lucide-react';

import { EnhancementOptionCards } from '../components/EnhancementOptionCards';
import { AccuracyProgressTracker } from '../components/AccuracyProgressTracker';
import { GroupedTransactionReview } from '../components/GroupedTransactionReview';
import { ConfidenceSummary } from '../components/ConfidenceSummary';
import { toast } from '@/shared/components/ui/use-toast';

// Mock data - replace with real API calls
const mockTransactions = [
  {
    id: '1',
    date: '2024-01-15',
    description: 'AMAZON.COM*M49J92 AMZN.COM/BILLWA',
    amount: -47.99,
    category_path: 'Office Supplies',
    category_id: 12,
    ai_category_confidence: 0.92,
    ai_grouping_id: 'amazon_office_supplies',
    status: 'pending_review'
  },
  {
    id: '2',
    date: '2024-01-15',
    description: 'AMAZON.COM*M49K83 AMZN.COM/BILLWA',
    amount: -23.45,
    category_path: 'Office Supplies',
    category_id: 12,
    ai_category_confidence: 0.89,
    ai_grouping_id: 'amazon_office_supplies',
    status: 'pending_review'
  },
  {
    id: '3',
    date: '2024-01-14',
    description: 'STARBUCKS STORE #1234',
    amount: -5.67,
    category_path: 'Meals & Entertainment',
    category_id: 25,
    ai_category_confidence: 0.76,
    ai_grouping_id: 'starbucks_meals',
    status: 'needs_review'
  },
  {
    id: '4',
    date: '2024-01-14',
    description: 'UBER TRIP 123ABC',
    amount: -18.50,
    category_path: 'Transportation',
    category_id: 18,
    ai_category_confidence: 0.85,
    ai_grouping_id: 'uber_transport',
    status: 'pending_review'
  },
  {
    id: '5',
    date: '2024-01-13',
    description: 'MICROSOFT OFFICE 365',
    amount: -12.99,
    category_path: 'Software & Subscriptions',
    category_id: 31,
    ai_category_confidence: 0.95,
    ai_grouping_id: 'microsoft_subscriptions',
    status: 'auto_categorized'
  }
];

const mockAccuracyImprovements = [
  {
    timestamp: new Date(Date.now() - 3600000),
    previousAccuracy: 87,
    newAccuracy: 89.5,
    improvement: 2.5,
    source: 'Quick Review',
    description: 'Bulk approved 15 medium-confidence transactions'
  },
  {
    timestamp: new Date(Date.now() - 1800000),
    previousAccuracy: 89.5,
    newAccuracy: 91.2,
    improvement: 1.7,
    source: 'Company Profile',
    description: 'Added business context for retail industry'
  }
];

interface ReviewAndImprovementsPageProps {}

export const ReviewAndImprovementsPage: React.FC<ReviewAndImprovementsPageProps> = () => {
  const navigate = useNavigate();
  const [currentAccuracy, setCurrentAccuracy] = useState(91.2);
  const [completedEnhancements, setCompletedEnhancements] = useState<string[]>(['quick-review']);
  const [inProgressEnhancements, setInProgressEnhancements] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);

  // Calculate review statistics
  const reviewStats = React.useMemo(() => {
    const needsReview = mockTransactions.filter(t => t.status === 'needs_review').length;
    const pendingReview = mockTransactions.filter(t => t.status === 'pending_review').length;
    const autoCategorizd = mockTransactions.filter(t => t.status === 'auto_categorized').length;
    
    return {
      needsReview,
      pendingReview,
      autoCategorizd,
      total: mockTransactions.length
    };
  }, []);

  const handleSelectEnhancement = (enhancementId: string) => {
    console.log('Selected enhancement:', enhancementId);
  };

  const handleStartEnhancement = async (enhancementId: string) => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update state based on enhancement type
      switch (enhancementId) {
        case 'quick-review':
          setActiveTab('review');
          break;
        case 'company-profile':
          // Navigate to company profile setup
          navigate('/onboarding/company-profile');
          break;
        case 'category-rules':
          // Navigate to category rules setup
          navigate('/categories/rules');
          break;
        case 'historical-data':
          // Navigate to historical data upload
          navigate('/upload/historical');
          break;
        default:
          break;
      }

      toast({
        title: 'Enhancement Started',
        description: `Starting ${enhancementId.replace('-', ' ')} enhancement...`,
      });
    } catch (error) {
      toast({
        title: 'Enhancement Failed',
        description: 'An error occurred while starting the enhancement.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGroupAction = async (groupId: string, action: 'approve' | 'reject' | 'customize') => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      if (action === 'approve') {
        // Simulate accuracy improvement
        setCurrentAccuracy(prev => Math.min(95, prev + 0.5));
      }

      toast({
        title: 'Group Action Complete',
        description: `Successfully ${action}d transaction group.`,
      });
    } catch (error) {
      toast({
        title: 'Action Failed',
        description: 'An error occurred while processing the group action.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTransactionAction = async (transactionId: string, action: 'approve' | 'reject' | 'customize') => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (action === 'approve') {
        // Simulate slight accuracy improvement
        setCurrentAccuracy(prev => Math.min(95, prev + 0.1));
      }

      toast({
        title: 'Transaction Updated',
        description: `Transaction ${action}d successfully.`,
      });
    } catch (error) {
      toast({
        title: 'Update Failed',
        description: 'An error occurred while updating the transaction.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkAction = async (transactionIds: string[], action: 'approve' | 'reject' | 'customize') => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (action === 'approve') {
        // Simulate accuracy improvement based on number of transactions
        const improvement = transactionIds.length * 0.1;
        setCurrentAccuracy(prev => Math.min(95, prev + improvement));
      }

      toast({
        title: 'Bulk Action Complete',
        description: `Successfully ${action}d ${transactionIds.length} transactions.`,
      });
    } catch (error) {
      toast({
        title: 'Bulk Action Failed',
        description: 'An error occurred while processing the bulk action.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-4 md:px-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/dashboard')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">Review & Improvements</h1>
              <p className="text-sm text-gray-600">Enhance your MIS accuracy through guided improvements</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="text-sm">
              <TrendingUp className="h-3 w-3 mr-1" />
              {currentAccuracy.toFixed(1)}% Accuracy
            </Badge>
            <div className="text-right">
              <div className="text-sm font-semibold text-gray-900">Business Owner</div>
              <div className="text-xs text-gray-600">MIS Enhancement</div>
            </div>
            <div className="w-8 h-8 bg-brand-primary text-white rounded-lg flex items-center justify-center text-sm font-semibold">
              BO
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-6 md:px-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="enhancements" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Enhancements
            </TabsTrigger>
            <TabsTrigger value="review" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Review Queue
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-brand-primary">{currentAccuracy.toFixed(1)}%</div>
                      <div className="text-sm text-gray-600">Current Accuracy</div>
                    </div>
                    <TrendingUp className="h-8 w-8 text-[#059669]" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-brand-primary">{reviewStats.needsReview}</div>
                      <div className="text-sm text-gray-600">Needs Review</div>
                    </div>
                    <AlertCircle className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-brand-primary">{reviewStats.pendingReview}</div>
                      <div className="text-sm text-gray-600">Pending Review</div>
                    </div>
                    <Clock className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-brand-primary">{completedEnhancements.length}</div>
                      <div className="text-sm text-gray-600">Enhancements Done</div>
                    </div>
                    <CheckCircle className="h-8 w-8 text-[#059669]" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Accuracy Progress Tracker */}
            <AccuracyProgressTracker
              currentAccuracy={currentAccuracy}
              baselineAccuracy={87}
              targetAccuracy={95}
              improvements={mockAccuracyImprovements}
              isLoading={isLoading}
              showDetailed={true}
            />
          </TabsContent>

          {/* Enhancements Tab */}
          <TabsContent value="enhancements" className="space-y-6">
            <EnhancementOptionCards
              currentAccuracy={currentAccuracy}
              onSelectEnhancement={handleSelectEnhancement}
              onStartEnhancement={handleStartEnhancement}
              completedEnhancements={completedEnhancements}
              inProgressEnhancements={inProgressEnhancements}
            />
          </TabsContent>

          {/* Review Queue Tab */}
          <TabsContent value="review" className="space-y-6">
            {/* Review Summary */}
            <ConfidenceSummary
              transactions={mockTransactions}
              title="Review Queue Summary"
            />

            {/* Grouped Transaction Review */}
            <GroupedTransactionReview
              transactions={mockTransactions}
              onGroupAction={handleGroupAction}
              onTransactionAction={handleTransactionAction}
              onBulkAction={handleBulkAction}
              isLoading={isLoading}
            />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default ReviewAndImprovementsPage;