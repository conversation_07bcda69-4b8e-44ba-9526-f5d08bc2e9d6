/**
 * Transaction Review Page
 * For reviewing and approving AI-categorized transactions
 * Matches wireframe: docs/wireframes/03-daily-use-journey/03-review.md
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { Transaction } from '@/shared/types/categorization';
import { useWebSocket, useWebSocketStatus } from '@/shared/services/websocket/WebSocketService';
import { logger } from '@/shared/utils/errorHandling';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Card, CardContent } from '@/shared/components/ui/card';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { StandardLoadingStates } from '@/shared/components/ui/standardized-loading';


// Removed Lucide imports - replaced with geometric icons
// AlertCircle → ⚠, CheckCircle2 → ✓, Search → ⌕, ArrowLeft → ←, ArrowRight → →
// Filter → ∷, History → ⟲, Keyboard → ⌨, RotateCcw → ⟲, Download → ↓
import { toast } from '@/shared/components/ui/use-toast';
import {
  fetchTransactions,
  getReviewQueue,
  bulkApproveTransactions,
  updateTransactionCategoryEnhanced,
  getTransactionStats,
  getConfidenceLevel,
  needsReview,
  type ReviewQueueResponse,
} from '../services/transactionService';
import {
  fetchCategories,
  type Category,
} from '@/features/categories/services/categoryService';
import { isApiError } from '@/shared/utils/errorHandling';
import {
  ReviewProgressStats,
  BulkActionPanel,
  IndividualTransactionReview,
} from '../components';
import '@/styles/layout-variables.css';

// Enhanced filter and pagination types
interface TransactionFilters {
  status: 'all' | 'needs_review' | 'high_confidence' | 'pending_approval';
  dateRange: 'all' | 'this_month' | 'last_month' | 'last_week' | 'custom';
  search: string;
  confidenceLevel: 'all' | 'high' | 'medium' | 'low';
  amountRange: { min?: number; max?: number };
  categoryFilter: string;
  customDateRange?: { start: string; end: string };
}

interface ReviewHistory {
  id: string;
  timestamp: string;
  action: 'approve' | 'categorize' | 'bulk_approve';
  transactionCount: number;
  details: string;
}

interface SavedFilter {
  id: string;
  name: string;
  filters: TransactionFilters;
  isDefault?: boolean;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

interface ReviewStats {
  totalTransactions: number;
  needsReview: number;
  highConfidence: number;
  avgConfidence: number;
  pendingApproval: number;
}

const TransactionReviewPage: React.FC = () => {
  const _navigate = useNavigate();
  const {
    isAuthenticated: authHookAuthenticated,
    user,
    isLoading: authLoading,
  } = useAuth();

  // State management
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [_reviewQueue, setReviewQueue] = useState<ReviewQueueResponse | null>(
    null,
  );
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [isBulkApproving, setIsBulkApproving] = useState(false);
  const [isUpdatingCategory, setIsUpdatingCategory] = useState<string | null>(
    null,
  );
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [reviewHistory, setReviewHistory] = useState<ReviewHistory[]>([]);
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);

  // Enhanced filters and pagination
  const [filters, setFilters] = useState<TransactionFilters>({
    status: 'needs_review',
    dateRange: 'this_month',
    search: '',
    confidenceLevel: 'all',
    amountRange: {},
    categoryFilter: 'all',
  });

  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 25,
  });

  // WebSocket status and connection
  const { connected: wsConnected, reconnect: wsReconnect } = useWebSocketStatus();

  // WebSocket event listeners for real-time updates
  useWebSocket('transaction.updated', (payload: any) => {
    logger.info('TransactionReviewPage: Transaction updated via WebSocket', 'TransactionReviewPage', {
      transactionId: payload.transaction_id,
      updates: payload.updates,
    });
    setTransactions(prev => prev.map(transaction => 
      transaction.id === payload.transaction_id 
        ? { ...transaction, ...payload.updates }
        : transaction
    ));
  });

  useWebSocket('transaction.categorized', (payload: any) => {
    logger.info('TransactionReviewPage: Transaction categorized via WebSocket', 'TransactionReviewPage', {
      transactionId: payload.transaction_id,
      category: payload.category,
      confidence: payload.confidence,
      needsReview: payload.needs_review,
    });
    setTransactions(prev => prev.map(transaction => 
      transaction.id === payload.transaction_id 
        ? { 
            ...transaction, 
            category: payload.category,
            confidence: payload.confidence,
            needs_review: payload.needs_review
          }
        : transaction
    ));
  });

  useWebSocket('transaction.approved', (payload: any) => {
    logger.info('TransactionReviewPage: Transaction approved via WebSocket', 'TransactionReviewPage', {
      transactionId: payload.transaction_id,
    });
    setTransactions(prev => prev.map(transaction => 
      transaction.id === payload.transaction_id 
        ? { ...transaction, approved: true, needs_review: false }
        : transaction
    ));
  });

  useWebSocket('transaction.bulk_approved', (payload: any) => {
    logger.info('TransactionReviewPage: Bulk transactions approved via WebSocket', 'TransactionReviewPage', {
      transactionIds: payload.transaction_ids,
      count: payload.transaction_ids?.length || 0,
    });
    const approvedIds = new Set(payload.transaction_ids);
    setTransactions(prev => prev.map(transaction => 
      approvedIds.has(transaction.id) 
        ? { ...transaction, approved: true, needs_review: false }
        : transaction
    ));
    setSelectedIds(new Set()); // Clear selection after bulk approval
  });

  useWebSocket('transaction.processing_progress', (payload: any) => {
    logger.info('TransactionReviewPage: Processing progress update via WebSocket', 'TransactionReviewPage', {
      needsReviewCount: payload.needs_review_count,
      highConfidenceCount: payload.high_confidence_count,
      avgConfidence: payload.avg_confidence,
    });
    // Update stats with real-time progress
    setStats(prev => prev ? {
      ...prev,
      needsReview: payload.needs_review_count,
      highConfidence: payload.high_confidence_count,
      avgConfidence: payload.avg_confidence
    } : null);
  });

  // Utility function for date range calculation
  const getDateRangeForApi = (
    dateRange: string,
    customRange?: { start: string; end: string },
  ) => {
    const now = new Date();

    switch (dateRange) {
      case 'this_month': {
        return {
          start: new Date(now.getFullYear(), now.getMonth(), 1)
            .toISOString()
            .split('T')[0],
          end: new Date().toISOString().split('T')[0],
        };
      }
      case 'last_month': {
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
        return {
          start: lastMonth.toISOString().split('T')[0],
          end: endLastMonth.toISOString().split('T')[0],
        };
      }
      case 'last_week': {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return {
          start: weekAgo.toISOString().split('T')[0],
          end: new Date().toISOString().split('T')[0],
        };
      }
      case 'custom':
        return customRange;
      default:
        return undefined;
    }
  };

  // Memoize the date range to prevent unnecessary recalculations
  const dateRange = useMemo(() => {
    return getDateRangeForApi(filters.dateRange, filters.customDateRange);
  }, [filters.dateRange, filters.customDateRange]);

  // Enhanced data loading with real API integration
  useEffect(() => {
    const loadData = async () => {
      // Loading transaction review data
      // Checking authentication state for data loading

      // Skip if auth is still loading
      if (authLoading) {
        // Auth still loading, waiting for authentication
        return;
      }

      // Only proceed if authenticated
      if (!authHookAuthenticated) {
        // Not authenticated, skipping data load
        setIsLoading(false);
        setError('Please log in to view transactions.');
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Load data in parallel
        const promises = [
          // Load transactions with enhanced filtering
          fetchTransactions({
            page: pagination.currentPage,
            pageSize: pagination.itemsPerPage,
            status: filters.status === 'all' ? undefined : filters.status,
            startDate: dateRange?.start,
            endDate: dateRange?.end,
            searchTerm: filters.search || undefined,
            categoryId:
              filters.categoryFilter === 'all'
                ? undefined
                : filters.categoryFilter,
            minAmount: filters.amountRange.min,
            maxAmount: filters.amountRange.max,
          }),
          // Load categories
          fetchCategories(),
          // Load review queue for priority items
          getReviewQueue({
            confidence_threshold: 0.85,
            limit: 50,
            date_range: dateRange,
          }),
          // Load statistics
          getTransactionStats(dateRange),
        ];

        const [
          transactionsResult,
          categoriesResult,
          reviewQueueResult,
          statsResult,
        ] = await Promise.all(promises);

        // Process transactions
        let processedTransactions: Transaction[] = [];
        if (!isApiError(transactionsResult)) {
          if (Array.isArray(transactionsResult)) {
            processedTransactions = transactionsResult as Transaction[];
          } else if (
            transactionsResult &&
            typeof transactionsResult === 'object' &&
            'items' in transactionsResult
          ) {
            const paginatedResult = transactionsResult;
            processedTransactions = paginatedResult.items || [];
            // Update pagination from API response
            setPagination((prev) => ({
              ...prev,
              totalItems: paginatedResult.total_count || 0,
              totalPages: paginatedResult.total_pages || 1,
            }));
          }
        } else {
          // Error fetching transactions - handled by error state
        }

        // Apply additional client-side filters
        processedTransactions = applyClientFilters(
          processedTransactions,
          filters,
        );

        setTransactions(processedTransactions);

        // Set categories with type guard
        if (!isApiError(categoriesResult) && Array.isArray(categoriesResult)) {
          setCategories(categoriesResult as Category[]);
        } else if (isApiError(categoriesResult)) {
          // Error fetching categories - handled by error state
        }

        // Set review queue with type guard
        if (reviewQueueResult && !isApiError(reviewQueueResult)) {
          setReviewQueue(reviewQueueResult as ReviewQueueResponse);
        } else if (isApiError(reviewQueueResult)) {
          // Error fetching review queue - handled by error state
        }

        // Set stats with type guard
        if (statsResult && !isApiError(statsResult)) {
          const stats = statsResult as unknown as {
            total_transactions: number;
            needs_review: number;
            confidence_distribution: { high_confidence: number };
            processing_summary: { avg_confidence: number };
          };
          setStats({
            totalTransactions: stats.total_transactions,
            needsReview: stats.needs_review,
            highConfidence: stats.confidence_distribution.high_confidence,
            avgConfidence: stats.processing_summary.avg_confidence,
            pendingApproval: stats.needs_review,
          });
        } else if (isApiError(statsResult)) {
          // Error fetching stats - handled by error state
        }
      } catch (err) {
        // Error loading data - handled by error state and toast
        setError('Failed to load transactions. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    void loadData();
  }, [
    pagination.currentPage,
    pagination.itemsPerPage,
    filters.status,
    filters.search,
    filters.confidenceLevel,
    filters.categoryFilter,
    filters.amountRange.min,
    filters.amountRange.max,
    dateRange,
    authHookAuthenticated,
    authLoading,
  ]);

  // Client-side filtering for additional criteria not handled by API
  const applyClientFilters = (
    transactions: Transaction[],
    filters: TransactionFilters,
  ) => {
    let filtered = [...transactions];

    // Confidence level filter
    if (filters.confidenceLevel !== 'all') {
      filtered = filtered.filter((t) => {
        const level = getConfidenceLevel(t.ai_category_confidence);
        return level === filters.confidenceLevel;
      });
    }

    return filtered;
  };

  // Get confidence indicator
  const _getConfidenceIndicator = (confidence: number | undefined) => {
    if (!confidence || confidence < 0.9) {
      return <span className="text-sm text-warning">⚠</span>;
    }
    return <span className="text-sm text-success">✓</span>;
  };

  // Enhanced category change with real API call
  const handleCategoryChange = useCallback(
    async (transactionId: string, categoryId: number, categoryName: string) => {
      setIsUpdatingCategory(transactionId);

      try {
        const result = await updateTransactionCategoryEnhanced(transactionId, {
          category_id: categoryId,
          confidence: 1.0,
          manual_override: true,
        });

        if (isApiError(result)) {
          toast({
            title: 'Update Failed',
            description: result.message || 'Failed to update category.',
            variant: 'destructive',
          });
          return;
        }

        // Update local state with API response
        setTransactions((prev) =>
          prev.map((t) =>
            t.id === transactionId
              ? {
                  ...t,
                  category_id: categoryId,
                  category_path: categoryName, // category_path is a string
                  ai_category_confidence: 1.0,
                  is_user_modified: true,
                  user_corrected: true,
                }
              : t,
          ),
        );

        toast({
          title: 'Category Updated',
          description: `Transaction categorized as "${categoryName}".`,
        });
      } catch (error) {
        // Error updating category - handled by toast notification
        toast({
          title: 'Update Failed',
          description: 'An unexpected error occurred.',
          variant: 'destructive',
        });
      } finally {
        setIsUpdatingCategory(null);
      }
    },
    [],
  );

  // Handle selection
  const handleSelectTransaction = useCallback(
    (transactionId: string, checked: boolean) => {
      setSelectedIds((prev) => {
        const newSet = new Set(prev);
        if (checked) {
          newSet.add(transactionId);
        } else {
          newSet.delete(transactionId);
        }
        return newSet;
      });
    },
    [],
  );

  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        const needsReviewIds = transactions
          .filter((t) => (t.ai_category_confidence || 0) < 0.9)
          .map((t) => t.id);
        setSelectedIds(new Set(needsReviewIds));
      } else {
        setSelectedIds(new Set());
      }
    },
    [transactions],
  );

  // Enhanced bulk approval with real API call
  const handleApproveSelected = useCallback(async () => {
    if (selectedIds.size === 0) return;

    setIsBulkApproving(true);

    try {
      const result = await bulkApproveTransactions({
        transaction_ids: Array.from(selectedIds),
        approval_notes: 'Bulk approval from review interface',
        auto_apply_suggestions: true,
      });

      if (isApiError(result)) {
        toast({
          title: 'Approval Failed',
          description: result.message || 'Failed to approve transactions.',
          variant: 'destructive',
        });
        return;
      }

      // Update local state based on API response
      setTransactions((prev) =>
        prev.map((t) =>
          selectedIds.has(t.id)
            ? {
                ...t,
                ai_category_confidence: 1.0,
                is_categorized: true,
                confidence_level: 'high',
                is_user_modified: true,
              }
            : t,
        ),
      );

      setSelectedIds(new Set());

      toast({
        title: 'Transactions Approved',
        description: `${result.approved_count} transactions approved successfully. Processing time: ${result.summary?.processing_time_ms || 0}ms`,
      });

      // Refresh stats - only if result is valid
      if (stats && !isApiError(result)) {
        setStats((prev) =>
          prev
            ? {
                ...prev,
                needsReview: Math.max(
                  0,
                  prev.needsReview - (result.approved_count || 0),
                ),
                highConfidence:
                  prev.highConfidence + (result.approved_count || 0),
              }
            : null,
        );
      }
    } catch (error) {
      // Error in bulk approval - handled by toast notification
      toast({
        title: 'Approval Failed',
        description: 'An unexpected error occurred during bulk approval.',
        variant: 'destructive',
      });
    } finally {
      setIsBulkApproving(false);
    }
  }, [selectedIds, stats]);

  // Clear selection handler
  const onClearSelection = useCallback(() => {
    setSelectedIds(new Set());
  }, []);

  // Enhanced bulk category change handler
  const handleBulkCategoryChange = useCallback(
    async (categoryId: number, categoryName: string) => {
      const selectedTransactions = transactions.filter((t) =>
        selectedIds.has(t.id),
      );

      try {
        // Update each transaction
        for (const transaction of selectedTransactions) {
          await updateTransactionCategoryEnhanced(transaction.id, {
            category_id: categoryId,
            confidence: 1.0,
            manual_override: true,
          });
        }

        // Update local state
        setTransactions((prev) =>
          prev.map((t) =>
            selectedIds.has(t.id)
              ? {
                  ...t,
                  category_id: categoryId,
                  category_path: categoryName, // category_path is a string
                  ai_category_confidence: 1.0,
                  is_user_modified: true,
                  user_corrected: true,
                }
              : t,
          ),
        );

        // Add to history
        const historyEntry: ReviewHistory = {
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
          action: 'categorize',
          transactionCount: selectedIds.size,
          details: `Bulk categorized ${selectedIds.size} transactions as "${categoryName}"`,
        };
        setReviewHistory((prev) => [historyEntry, ...prev].slice(0, 50));

        onClearSelection();
      } catch (error) {
        // Error in bulk category change - handled by toast notification
        throw error;
      }
    },
    [selectedIds, transactions, onClearSelection],
  );

  // Enhanced export handler with real export functionality
  const handleExportSelected = useCallback(
    async (format: 'csv' | 'excel' | 'pdf') => {
      try {
        const selectedTransactions = transactions.filter((t) =>
          selectedIds.has(t.id),
        );
        
        if (selectedTransactions.length === 0) {
          toast({
            title: 'No Transactions Selected',
            description: 'Please select transactions to export.',
            variant: 'destructive',
          });
          return;
        }

        // Navigate to export page with selected transaction IDs
        const transactionIds = Array.from(selectedIds).join(',');
        const exportUrl = `/reports?export=true&format=${format}&transactions=${transactionIds}`;
        
        // Add to history
        const historyEntry: ReviewHistory = {
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
          action: 'export',
          transactionCount: selectedIds.size,
          details: `Exported ${selectedIds.size} transactions to ${format.toUpperCase()}`,
        };
        setReviewHistory((prev) => [historyEntry, ...prev].slice(0, 50));

        // Open export in new tab to preserve current review state
        window.open(exportUrl, '_blank');
        
        toast({
          title: 'Export Started',
          description: `Opening export page for ${selectedIds.size} transactions.`,
        });
      } catch (error) {
        logger.error('Failed to export selected transactions', 'TransactionReviewPage', error as Error);
        toast({
          title: 'Export Failed',
          description: 'Failed to start export process. Please try again.',
          variant: 'destructive',
        });
      }
    },
    [selectedIds, transactions],
  );

  // Get real-time statistics from state and transactions
  const displayStats = React.useMemo(() => {
    if (stats) {
      return stats;
    }

    // Fallback to client-side calculation
    const needsReviewLocal = transactions.filter((t) =>
      needsReview(t, 0.85),
    ).length;
    const highConfidenceLocal = transactions.filter(
      (t) => (t.ai_category_confidence || 0) >= 0.9,
    ).length;
    const total = transactions.length;
    const avgConfidence =
      total > 0
        ? transactions.reduce(
            (sum, t) => sum + (t.ai_category_confidence || 0),
            0,
          ) / total
        : 0;

    return {
      totalTransactions: total,
      needsReview: needsReviewLocal,
      highConfidence: highConfidenceLocal,
      avgConfidence,
      pendingApproval: needsReviewLocal,
    };
  }, [transactions, stats]);

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, currentPage: newPage }));
  };

  // Save current filter as preset
  const saveCurrentFilter = useCallback(() => {
    const filterName = prompt('Enter a name for this filter preset:');
    if (filterName) {
      const newFilter: SavedFilter = {
        id: Date.now().toString(),
        name: filterName,
        filters: { ...filters },
      };
      setSavedFilters((prev) => [...prev, newFilter]);
      toast({
        title: 'Filter Saved',
        description: `Filter preset "${filterName}" has been saved.`,
      });
    }
  }, [filters]);

  // Load saved filter
  const loadSavedFilter = useCallback((savedFilter: SavedFilter) => {
    setFilters(savedFilter.filters);
    toast({
      title: 'Filter Applied',
      description: `Applied filter preset "${savedFilter.name}".`,
    });
  }, []);

  // Reset filters to default
  const resetFilters = useCallback(() => {
    setFilters({
      status: 'needs_review',
      dateRange: 'this_month',
      search: '',
      confidenceLevel: 'all',
      amountRange: {},
      categoryFilter: 'all',
    });
  }, []);

  // Group action handler for GroupedTransactionReview
  const handleGroupAction = useCallback(async (groupId: string, action: 'approve' | 'reject' | 'customize') => {
    try {
      // Find all transactions in this group
      const groupTransactions = transactions.filter(t => 
        t.ai_grouping_id === groupId || 
        t.description?.toLowerCase().includes(groupId.toLowerCase())
      );
      
      if (groupTransactions.length === 0) {
        toast({
          title: 'Group Not Found',
          description: 'No transactions found in this group.',
          variant: 'destructive',
        });
        return;
      }

      if (action === 'approve') {
        const transactionIds = groupTransactions.map(t => t.id);
        const result = await bulkApproveTransactions({
          transaction_ids: transactionIds,
          approval_notes: `Group approval for pattern: ${groupId}`,
          auto_apply_suggestions: true,
        });

        if (isApiError(result)) {
          toast({
            title: 'Group Approval Failed',
            description: result.message || 'Failed to approve group.',
            variant: 'destructive',
          });
          return;
        }

        // Update local state
        setTransactions(prev => prev.map(t => 
          transactionIds.includes(t.id) ? {
            ...t,
            ai_category_confidence: 1.0,
            is_categorized: true,
            confidence_level: 'high',
            is_user_modified: true,
          } : t
        ));

        toast({
          title: 'Group Approved',
          description: `${groupTransactions.length} transactions approved successfully.`,
        });
      } else if (action === 'reject') {
        // For reject, we'll clear the AI suggestions
        setTransactions(prev => prev.map(t => 
          groupTransactions.some(gt => gt.id === t.id) ? {
            ...t,
            ai_suggested_category: null,
            ai_category_confidence: 0,
            confidence_level: 'low',
          } : t
        ));

        toast({
          title: 'Group Rejected',
          description: `${groupTransactions.length} suggestions cleared.`,
        });
      }
    } catch (error) {
      toast({
        title: 'Group Action Failed',
        description: 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  }, [transactions]);

  // Transaction action handler for GroupedTransactionReview
  const handleTransactionAction = useCallback(async (transactionId: string, action: 'approve' | 'reject' | 'customize') => {
    const transaction = transactions.find(t => t.id === transactionId);
    if (!transaction) return;

    if (action === 'approve') {
      const result = await bulkApproveTransactions({
        transaction_ids: [transactionId],
        approval_notes: 'Individual approval from grouped review',
        auto_apply_suggestions: true,
      });

      if (isApiError(result)) {
        toast({
          title: 'Approval Failed',
          description: result.message || 'Failed to approve transaction.',
          variant: 'destructive',
        });
        return;
      }

      setTransactions(prev => prev.map(t => 
        t.id === transactionId ? {
          ...t,
          ai_category_confidence: 1.0,
          is_categorized: true,
          confidence_level: 'high',
          is_user_modified: true,
        } : t
      ));

      toast({
        title: 'Transaction Approved',
        description: 'Transaction approved successfully.',
      });
    } else if (action === 'reject') {
      setTransactions(prev => prev.map(t => 
        t.id === transactionId ? {
          ...t,
          ai_suggested_category: null,
          ai_category_confidence: 0,
          confidence_level: 'low',
        } : t
      ));

      toast({
        title: 'Suggestion Rejected',
        description: 'AI suggestion cleared.',
      });
    }
  }, [transactions]);

  // Bulk review action handler for GroupedTransactionReview
  const handleBulkReviewAction = useCallback(async (transactionIds: string[], action: 'approve' | 'reject' | 'customize') => {
    if (action === 'approve') {
      const result = await bulkApproveTransactions({
        transaction_ids: transactionIds,
        approval_notes: 'Bulk approval from grouped review interface',
        auto_apply_suggestions: true,
      });

      if (isApiError(result)) {
        toast({
          title: 'Bulk Approval Failed',
          description: result.message || 'Failed to approve transactions.',
          variant: 'destructive',
        });
        return;
      }

      setTransactions(prev => prev.map(t => 
        transactionIds.includes(t.id) ? {
          ...t,
          ai_category_confidence: 1.0,
          is_categorized: true,
          confidence_level: 'high',
          is_user_modified: true,
        } : t
      ));

      toast({
        title: 'Bulk Approval Complete',
        description: `${transactionIds.length} transactions approved successfully.`,
      });
    } else if (action === 'reject') {
      setTransactions(prev => prev.map(t => 
        transactionIds.includes(t.id) ? {
          ...t,
          ai_suggested_category: null,
          ai_category_confidence: 0,
          confidence_level: 'low',
        } : t
      ));

      toast({
        title: 'Bulk Rejection Complete',
        description: `${transactionIds.length} suggestions cleared.`,
      });
    }
  }, []);

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <StandardLoadingStates.Transactions />
      </div>
    );
  }

  return (
    <div className="p-page min-h-screen" style={{ backgroundColor: '#ffffff' }}>
      {/* Enhanced Header with Better Visual Hierarchy */}
      <div className="space-section">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-semibold text-gray-700 space-component">
              Review & Approve Transactions
            </h1>
            <p className="text-gray-500 text-base">
              AI-powered transaction categorization review • March 2025
            </p>
          </div>
          <div className="flex items-center gap-element">
            {/* Export to Accounting Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => _navigate('/reports?export=true')}
              className="border-brand-primary text-brand-primary hover:bg-green-50 flex items-center gap-2"
            >
              <span className="text-sm">↓</span>
              Export to Accounting
            </Button>
            
            {/* WebSocket Status Indicator */}
            <div className="flex items-center gap-inline">
              <div className={`w-2 h-2 rounded-full ${wsConnected ? 'bg-green-600' : 'bg-red-600'}`} />
              <span className="text-sm text-gray-600">
                {wsConnected ? 'Connected' : 'Disconnected'}
              </span>
              {!wsConnected && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={wsReconnect}
                  className="border-brand-primary text-brand-primary hover:bg-green-50"
                >
                  Reconnect
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div>
        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="space-section">
            <span className="h-4 w-4 text-lg font-bold flex items-center justify-center">⚠</span>
            <AlertTitle>Unable to Load Transactions</AlertTitle>
            <AlertDescription>
              {error.includes('authenticated') || error.includes('401')
                ? 'Please upload transaction files to get started with AI categorization.'
                : 'Unable to connect to services. Please check your connection or try again later.'}
            </AlertDescription>
          </Alert>
        )}

        {/* Enhanced Filter Bar */}
        <Card className="space-section border border-gray-200 shadow-sm">
          <CardContent className="p-card">
            <div className="space-element">
              {/* Primary Filters */}
              <div className="flex flex-col lg:flex-row gap-element items-center">
                <div className="flex gap-inline">
                  <Select
                    value={filters.status}
                    onValueChange={(value) =>
                      setFilters((prev) => ({
                        ...prev,
                        status: value as
                          | 'all'
                          | 'needs_review'
                          | 'high_confidence'
                          | 'pending_approval',
                      }))
                    }
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="needs_review">
                        □ Needs Review
                      </SelectItem>
                      <SelectItem value="high_confidence">
                        ⚬ High Confidence
                      </SelectItem>
                      <SelectItem value="pending_approval">
                        ⧖ Pending Approval
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    value={filters.dateRange}
                    onValueChange={(value) =>
                      setFilters((prev) => ({
                        ...prev,
                        dateRange: value as
                          | 'all'
                          | 'this_month'
                          | 'last_month'
                          | 'last_week'
                          | 'custom',
                      }))
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="this_month">This Month</SelectItem>
                      <SelectItem value="last_month">Last Month</SelectItem>
                      <SelectItem value="last_week">Last Week</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    value={filters.confidenceLevel}
                    onValueChange={(value) =>
                      setFilters((prev) => ({
                        ...prev,
                        confidenceLevel: value as
                          | 'all'
                          | 'high'
                          | 'medium'
                          | 'low',
                      }))
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Confidence" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Levels</SelectItem>
                      <SelectItem value="high">High (≥90%)</SelectItem>
                      <SelectItem value="medium">Medium (70-89%)</SelectItem>
                      <SelectItem value="low">Low (&lt;70%)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">⌕</span>
                    <Input
                      placeholder="Search transactions... (Ctrl+F)"
                      value={filters.search}
                      onChange={(e) =>
                        setFilters((prev) => ({
                          ...prev,
                          search: e.target.value,
                        }))
                      }
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  >
                    <span className="text-sm mr-1">∷</span>
                    Advanced
                  </Button>

                  <Button variant="outline" size="sm" onClick={resetFilters}>
                    <span className="text-sm mr-1">⟲</span>
                    Reset
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowKeyboardShortcuts(true)}
                  >
                    <span className="text-sm mr-1">⌨</span>
                    Shortcuts
                  </Button>
                </div>
              </div>

              {/* Advanced Filters */}
              {showAdvancedFilters && (
                <div className="border-t pt-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium text-foreground mb-1 block">
                        Amount Range
                      </label>
                      <div className="flex gap-2">
                        <Input
                          type="number"
                          placeholder="Min"
                          value={filters.amountRange.min || ''}
                          onChange={(e) =>
                            setFilters((prev) => ({
                              ...prev,
                              amountRange: {
                                ...prev.amountRange,
                                min: e.target.value
                                  ? parseFloat(e.target.value)
                                  : undefined,
                              },
                            }))
                          }
                          className="text-sm"
                        />
                        <Input
                          type="number"
                          placeholder="Max"
                          value={filters.amountRange.max || ''}
                          onChange={(e) =>
                            setFilters((prev) => ({
                              ...prev,
                              amountRange: {
                                ...prev.amountRange,
                                max: e.target.value
                                  ? parseFloat(e.target.value)
                                  : undefined,
                              },
                            }))
                          }
                          className="text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-foreground mb-1 block">
                        Category Filter
                      </label>
                      <Select
                        value={filters.categoryFilter}
                        onValueChange={(value) =>
                          setFilters((prev) => ({
                            ...prev,
                            categoryFilter: value,
                          }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="All Categories" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          {categories.map((category) => (
                            <SelectItem
                              key={category.id}
                              value={category.id.toString()}
                            >
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-foreground mb-1 block">
                        Filter Presets
                      </label>
                      <div className="flex gap-2">
                        <Select
                          onValueChange={(value) => {
                            const filter = savedFilters.find(
                              (f) => f.id === value,
                            );
                            if (filter) loadSavedFilter(filter);
                          }}
                        >
                          <SelectTrigger className="flex-1">
                            <SelectValue placeholder="Load preset" />
                          </SelectTrigger>
                          <SelectContent>
                            {savedFilters.map((filter) => (
                              <SelectItem key={filter.id} value={filter.id}>
                                {filter.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={saveCurrentFilter}
                        >
                          Save
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Professional Progress Stats */}
        <ReviewProgressStats
          transactions={transactions}
          totalTransactions={displayStats.totalTransactions}
          timeRange="March 2025"
          showDetailed={true}
          className="space-section"
        />

        {/* Individual Transaction Review */}
        <IndividualTransactionReview
          transactions={transactions}
          categories={categories}
          onApprove={handleCategoryChange}
          onSkip={(transactionId) => {
            // Mark transaction as skipped
            console.log('Skipped transaction:', transactionId);
          }}
          onBulkApprove={async (transactionIds) => {
            // Handle bulk approve
            await bulkApproveTransactions({
              transaction_ids: transactionIds,
              approval_notes: 'Bulk approval for similar transactions',
              auto_apply_suggestions: true,
            });
          }}
          onCreateRule={(pattern, categoryId) => {
            // Create rule for pattern
            console.log('Create rule:', pattern, categoryId);
          }}
          isLoading={isLoading}
          className="space-section"
        />

        {/* Pagination */}
        <div className="flex items-center justify-between space-section">
          <div className="flex items-center gap-element">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={pagination.currentPage <= 1}
            >
              <span className="text-sm mr-1">←</span>
              Previous
            </Button>

            <span className="text-sm text-muted-foreground">
              Page {pagination.currentPage} of {pagination.totalPages}
            </span>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={pagination.currentPage >= pagination.totalPages}
            >
              Next
              <span className="text-sm ml-1">→</span>
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            Showing{' '}
            {Math.min(pagination.itemsPerPage, displayStats.totalTransactions)}{' '}
            of {displayStats.totalTransactions} transactions
          </div>
        </div>

        {/* Professional Bulk Actions */}
        <BulkActionPanel
          selectedTransactions={transactions.filter((t) =>
            selectedIds.has(t.id),
          )}
          selectedIds={selectedIds}
          categories={categories}
          isProcessing={isBulkApproving}
          onApproveSelected={handleApproveSelected}
          onBulkCategoryChange={handleBulkCategoryChange}
          onExportSelected={handleExportSelected}
          onClearSelection={onClearSelection}
          onSelectAll={() => handleSelectAll(true)}
          className="space-section"
        />

        {/* Review History Panel */}
        {reviewHistory.length > 0 && (
          <Card className="space-section border border-gray-200 shadow-sm">
            <CardContent className="p-card">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-sm text-muted-foreground">⟲</span>
                <h4 className="font-medium text-foreground">Recent Actions</h4>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {reviewHistory.slice(0, 5).map((entry) => (
                  <div
                    key={entry.id}
                    className="flex items-center justify-between text-sm"
                  >
                    <span className="text-muted-foreground">{entry.details}</span>
                    <span className="text-xs text-muted-foreground">
                      {new Date(entry.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Keyboard Shortcuts Modal */}
        {showKeyboardShortcuts && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md m-4">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Keyboard Shortcuts</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowKeyboardShortcuts(false)}
                  >
                    ×
                  </Button>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span>Select All</span>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">
                      Ctrl+A
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Approve Selected</span>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">
                      Ctrl+Enter
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Focus Search</span>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">
                      Ctrl+F
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Toggle Needs Review</span>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">
                      R
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Toggle High Confidence</span>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">
                      H
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Clear Selection</span>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">
                      Esc
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Show Shortcuts</span>
                    <kbd className="px-2 py-1 bg-muted rounded text-xs">
                      ?
                    </kbd>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default TransactionReviewPage;
