/**
 * Transaction Review Page
 *
 * Enhanced transaction review functionality with real-time editing,
 * categorization updates, and bulk operations.
 */
import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { TransactionTable } from '../components/TransactionTable';
import {
  Transaction,
  CategorizationStatus,
} from '@/shared/types/categorization';
import { Button } from '@/shared/components/ui/button';

import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Input } from '@/shared/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Download, RefreshCw, AlertCircle, Search } from 'lucide-react';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  fetchTransactions,
  updateTransactionCategory,
  updateBatchCategories,
} from '../services/transactionService';

interface FilterOptions {
  dateRange: 'all' | 'week' | 'month' | 'quarter' | 'year';
  category: string;
  status: 'all' | 'categorized' | 'uncategorized' | 'pending';
  amountMin: string;
  amountMax: string;
  searchQuery: string;
}

const ReviewPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<
    Transaction[]
  >([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(
    new Set(),
  );
  const [modifiedTransactions, setModifiedTransactions] = useState<Set<string>>(
    new Set(),
  );
  const [_bulkEditMode, setBulkEditMode] = useState(false);

  // Filter state
  const [filters, setFilters] = useState<FilterOptions>({
    dateRange:
      (searchParams.get('dateRange') as FilterOptions['dateRange']) || 'all',
    category: searchParams.get('category') || 'all',
    status: (searchParams.get('status') as FilterOptions['status']) || 'all',
    amountMin: searchParams.get('amountMin') || '',
    amountMax: searchParams.get('amountMax') || '',
    searchQuery: searchParams.get('search') || '',
  });

  // Statistics
  const _stats = React.useMemo(() => {
    const totalTransactions = filteredTransactions.length;
    const totalAmount = filteredTransactions.reduce(
      (sum, t) =>
        sum + (t.transaction_type === 'credit' ? t.amount : -t.amount),
      0,
    );
    const pendingCount = filteredTransactions.filter(
      (t) => !t.is_categorized,
    ).length;
    const categorizedCount = filteredTransactions.filter(
      (t) => t.category_path && t.category_path !== 'Other',
    ).length;

    return {
      totalTransactions,
      totalAmount,
      pendingCount,
      categorizedCount,
      categorizationRate:
        totalTransactions > 0
          ? (categorizedCount / totalTransactions) * 100
          : 0,
    };
  }, [filteredTransactions]);

  // Define applyFilters before using it in useEffect
  const applyFilters = useCallback(
    (transactionList: Transaction[], filterOptions: FilterOptions) => {
      let filtered = [...transactionList];

      // Date range filter
      if (filterOptions.dateRange !== 'all') {
        const now = new Date();
        const cutoffDate = new Date();

        switch (filterOptions.dateRange) {
          case 'week':
            cutoffDate.setDate(now.getDate() - 7);
            break;
          case 'month':
            cutoffDate.setMonth(now.getMonth() - 1);
            break;
          case 'quarter':
            cutoffDate.setMonth(now.getMonth() - 3);
            break;
          case 'year':
            cutoffDate.setFullYear(now.getFullYear() - 1);
            break;
        }

        filtered = filtered.filter((t) => new Date(t.date) >= cutoffDate);
      }

      // Category filter
      if (filterOptions.category !== 'all') {
        filtered = filtered.filter((t) =>
          t.category_path
            ?.toLowerCase()
            .includes(filterOptions.category.toLowerCase()),
        );
      }

      // Status filter
      if (filterOptions.status !== 'all') {
        switch (filterOptions.status) {
          case 'categorized':
            filtered = filtered.filter(
              (t) =>
                t.is_categorized &&
                t.category_path &&
                t.category_path !== 'Other',
            );
            break;
          case 'uncategorized':
            filtered = filtered.filter(
              (t) =>
                !t.is_categorized ||
                !t.category_path ||
                t.category_path === 'Other',
            );
            break;
          case 'pending':
            filtered = filtered.filter(
              (t) => t.status === CategorizationStatus.UNCATEGORIZED,
            );
            break;
        }
      }

      // Amount filters
      if (filterOptions.amountMin) {
        const minAmount = parseFloat(filterOptions.amountMin);
        if (!isNaN(minAmount)) {
          filtered = filtered.filter((t) => Math.abs(t.amount) >= minAmount);
        }
      }

      if (filterOptions.amountMax) {
        const maxAmount = parseFloat(filterOptions.amountMax);
        if (!isNaN(maxAmount)) {
          filtered = filtered.filter((t) => Math.abs(t.amount) <= maxAmount);
        }
      }

      // Search query filter
      if (filterOptions.searchQuery) {
        const query = filterOptions.searchQuery.toLowerCase();
        filtered = filtered.filter(
          (t) =>
            t?.description?.toLowerCase().includes(query) ||
            t.category_path?.toLowerCase().includes(query) ||
            t.merchant?.toLowerCase().includes(query),
        );
      }

      setFilteredTransactions(filtered);
    },
    [],
  );

  // Load transactions on component mount
  useEffect(() => {
    const loadTransactions = async () => {
      setIsLoading(true);
      try {
        const response = await fetchTransactions({
          page: 1,
          pageSize: 1000, // Load more for comprehensive review
        });
        const loadedTransactions = Array.isArray(response)
          ? response
          : response.items || [];
        setTransactions(loadedTransactions);
        applyFilters(loadedTransactions, filters);
      } catch (_error) {
        console.error('Error loading transactions:', _error);
        toast({
          title: 'Error loading transactions',
          description: 'Failed to load transaction data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    void loadTransactions();
  }, [toast, applyFilters, filters]);

  const updateSearchParams = useCallback(() => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all' && value !== '') {
        params.set(key, String(value));
      }
    });
    setSearchParams(params);
  }, [filters, setSearchParams]);

  // Move function declarations above their usage
  useEffect(() => {
    applyFilters(transactions, filters);
    updateSearchParams();
  }, [transactions, filters, applyFilters, updateSearchParams]);

  const handleFilterChange = (key: keyof FilterOptions, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const _handleUpdateTransaction = useCallback(
    (id: string, field: keyof Transaction, value: unknown) => {
      setTransactions((prev) =>
        prev.map((transaction) =>
          transaction.id === id
            ? { ...transaction, [field]: value }
            : transaction,
        ),
      );
      // Track which transactions have been modified
      setModifiedTransactions((prev) => new Set(prev).add(id));
      setHasUnsavedChanges(true);
    },
    [],
  );

  const _handleBulkUpdate = useCallback(
    async (updates: Partial<Transaction>) => {
      if (selectedTransactions.size === 0) return;

      setIsSaving(true);
      try {
        const transactionIds = Array.from(selectedTransactions);
        await updateBatchCategories({
          transaction_ids: transactionIds,
          category_id: updates.category_id || null,
        });

        // Update local state
        setTransactions((prev) =>
          prev.map((transaction) =>
            selectedTransactions.has(transaction.id)
              ? { ...transaction, ...updates }
              : transaction,
          ),
        );

        setSelectedTransactions(new Set());
        setBulkEditMode(false);
        setHasUnsavedChanges(false);

        toast({
          title: 'Bulk update successful',
          description: `Updated ${transactionIds.length} transactions.`,
        });
      } catch (error) {
        console.error('Error in bulk update:', error);
        toast({
          title: 'Bulk update failed',
          description: 'Failed to update transactions. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsSaving(false);
      }
    },
    [selectedTransactions, toast],
  );

  const handleSaveChanges = useCallback(async () => {
    if (modifiedTransactions.size === 0) {
      toast({
        title: 'No changes',
        description: 'No modifications to save.',
      });
      return;
    }

    setIsSaving(true);
    try {
      // Save only modified transactions
      const changedTransactions = transactions.filter((t) =>
        modifiedTransactions.has(t.id),
      );

      let savedCount = 0;
      for (const transaction of changedTransactions) {
        if (transaction.category_id) {
          await updateTransactionCategory(
            transaction.id,
            transaction.category_id,
          );
          savedCount++;
        }
      }

      // Clear tracking after successful save
      setModifiedTransactions(new Set());
      setHasUnsavedChanges(false);

      toast({
        title: 'Changes saved',
        description: `Successfully saved ${savedCount} transaction updates.`,
      });
    } catch (error) {
      console.error('Error saving changes:', error);
      toast({
        title: 'Error saving changes',
        description: 'Failed to save transaction updates. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  }, [transactions, modifiedTransactions, toast]);

  const handleExport = useCallback(() => {
    // Navigate to professional export system with current filter context
    const params = new URLSearchParams({
      export: 'true',
      source: 'review-page'
    });

    // Add current filter context to export
    if (filters.status !== 'all') {
      params.set('status', filters.status);
    }
    if (filters.searchQuery) {
      params.set('search', filters.searchQuery);
    }
    if (filters.dateRange !== 'all') {
      params.set('dateRange', filters.dateRange);
    }

    navigate(`/reports?${params.toString()}`);
  }, [navigate, filters]);

  const handleRefresh = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetchTransactions({
        page: 1,
        pageSize: 1000,
      });
      const loadedTransactions = Array.isArray(response)
        ? response
        : response.items || [];
      setTransactions(loadedTransactions);
      applyFilters(loadedTransactions, filters);

      toast({
        title: 'Data refreshed',
        description: 'Transaction data has been updated.',
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: 'Error refreshing data',
        description: 'Failed to refresh transaction data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [filters, toast, applyFilters]);

  return (
    <div className="space-y-6">
      {/* Simple header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Transactions</h1>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => void handleRefresh()}
            disabled={isLoading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => void handleExport()}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Simple search filter */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search transactions..."
            value={filters.searchQuery}
            onChange={(e) =>
              handleFilterChange('searchQuery', e?.target?.value)
            }
            className="pl-8"
          />
        </div>
        <Select
          value={filters.status}
          onValueChange={(value) => handleFilterChange('status', value)}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All Statuses" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="categorized">Categorized</SelectItem>
            <SelectItem value="uncategorized">Uncategorized</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Save changes alert */}
      {hasUnsavedChanges && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>You have unsaved changes.</span>
            <Button
              onClick={() => void handleSaveChanges()}
              disabled={isSaving}
              size="sm"
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Clean transaction table */}
      <TransactionTable
        transactions={filteredTransactions}
        onUpdateTransaction={(transaction: Transaction) => {
          // TransactionTable expects a simple update function
          setTransactions((prev) =>
            prev.map((t) => (t.id === transaction.id ? transaction : t)),
          );
        }}
        onSaveChanges={() => void handleSaveChanges()}
        hasUnsavedChanges={hasUnsavedChanges}
        isLoading={isLoading}
        selectedRows={selectedTransactions}
        onSelectionChange={setSelectedTransactions}
      />
    </div>
  );
};

export default ReviewPage;
