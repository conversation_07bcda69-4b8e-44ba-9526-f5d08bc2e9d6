/**
 * LoginForm Component Tests
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockFetch, setupUnauthenticatedState } from '@/test/utils';
import LoginForm from '../LoginForm';

describe('LoginForm', () => {
  beforeEach(() => {
    setupUnauthenticatedState();
  });

  it('renders login form correctly', () => {
    render(<LoginForm />);
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    expect(screen.getByText(/keep me signed in/i)).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    expect(screen.getByText(/password is required/i)).toBeInTheDocument();
  });

  it('validates email format', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    // Type an email that passes HTML5 validation but fails our regex
    await user.type(emailInput, 'test@invalid');
    await user.type(passwordInput, 'validpassword123');
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('submits login form with valid credentials', async () => {
    const user = userEvent.setup();
    
    // Mock successful login response
    mockFetch({
      access_token: 'mock-token',
      refresh_token: 'mock-refresh-token',
      token_type: 'bearer',
    });
    
    render(<LoginForm />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'GikiTest2025Secure');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/token'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded',
          }),
          body: expect.stringContaining('username=owner%40testcompany.com'),
        })
      );
    });
  });

  it('displays error message on login failure', async () => {
    const user = userEvent.setup();
    
    // Mock failed login response
    mockFetch(
      { detail: 'Invalid email or password' },
      false,
      401
    );
    
    render(<LoginForm />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'wrongpassword');
    await user.click(submitButton);
    
    await waitFor(() => {
      // The form displays a generic error message, not the specific API error
      expect(screen.getByText(/login failed|authentication failed/i)).toBeInTheDocument();
    });
  });

  it('toggles remember me checkbox', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);
    
    const rememberMeCheckbox = screen.getByRole('checkbox', { name: /keep me signed in/i });
    expect(rememberMeCheckbox).not.toBeChecked();
    
    await user.click(rememberMeCheckbox);
    expect(rememberMeCheckbox).toBeChecked();
  });

  it('disables submit button while loading', async () => {
    const user = userEvent.setup();
    
    // Mock delayed response
    global.fetch = vi.fn().mockImplementation(() => 
      new Promise(resolve => {
        setTimeout(() => resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({
            access_token: 'mock-token',
            refresh_token: 'mock-refresh-token',
          }),
        }), 100);
      })
    );
    
    render(<LoginForm />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'GikiTest2025Secure');
    await user.click(submitButton);
    
    // Button should be disabled while loading
    expect(submitButton).toBeDisabled();
    expect(screen.getByText(/signing in/i)).toBeInTheDocument();
  });
});