/**
 * Login Form - Professional Authentication Interface
 *
 * Modern, professional login form with enhanced security features,
 * validation, and professional B2B design system integration.
 */

import React, { useState, useCallback } from 'react';
import {
  Eye,
  EyeOff,
  Mail,
  Lock,
  AlertCircle,
  CheckCircle,
  Shield,
} from 'lucide-react';
import { Button } from '../../../shared/components/ui/button';
import { Input } from '../../../shared/components/ui/input';
import { Label } from '../../../shared/components/ui/label';
import { gradientButtonStyle, applyGradientHover } from '@/shared/utils/brandGradients';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import { Alert, AlertDescription } from '../../../shared/components/ui/alert';
import { Progress } from '../../../shared/components/ui/progress';
import { Badge } from '../../../shared/components/ui/badge';
import { useAuth } from '../hooks/useAuth';
import { toast } from '../../../shared/components/ui/use-toast';

// Types
interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface ValidationErrors {
  email?: string;
  password?: string;
  general?: string;
}

interface LoginAttempt {
  timestamp: Date;
  success: boolean;
  ip?: string;
}

// Component Props
interface LoginFormProps {
  onSuccess?: () => void;
  onForgotPassword?: () => void;
  className?: string;
  showSecurityFeatures?: boolean;
  allowRememberMe?: boolean;
}

export const LoginForm: React.FC<LoginFormProps> = React.memo(({
  onSuccess,
  onForgotPassword,
  className = '',
  showSecurityFeatures = true,
  allowRememberMe = true,
}) => {
  // State
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState<LoginAttempt[]>([]);
  const [securityLevel, setSecurityLevel] = useState<'low' | 'medium' | 'high'>(
    'low',
  );

  const { login, isLoading: authLoading, error: authError } = useAuth();

  // Password security analysis
  const analyzePasswordSecurity = useCallback((password: string) => {
    let level: 'low' | 'medium' | 'high' = 'low';

    if (password.length >= 8) {
      level = 'medium';
    }
    if (
      password.length >= 12 &&
      /[A-Z]/.test(password) &&
      /[a-z]/.test(password) &&
      /[0-9]/.test(password) &&
      /[^A-Za-z0-9]/.test(password)
    ) {
      level = 'high';
    }

    setSecurityLevel(level);
  }, []);

  // Form validation
  const validateForm = useCallback((): boolean => {
    const newErrors: ValidationErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) {
        return;
      }

      try {
        setLoading(true);
        setErrors({});

        const result = await login(
          formData.email,
          formData.password,
          formData.rememberMe,
        );

        if (result.success) {
          // Track successful login
          const attempt: LoginAttempt = {
            timestamp: new Date(),
            success: true,
          };
          setLoginAttempts((prev) => [attempt, ...prev.slice(0, 4)]);

          toast({
            title: 'Login Successful',
            description: 'Welcome to giki.ai platform.',
          });

          onSuccess?.();
        } else {
          // Track failed login
          const attempt: LoginAttempt = {
            timestamp: new Date(),
            success: false,
          };
          setLoginAttempts((prev) => [attempt, ...prev.slice(0, 4)]);

          setErrors({
            general:
              result.error || 'Login failed. Please check your credentials.',
          });
        }
      } catch (error) {
        console.error('Login error:', error);
        setErrors({
          general: 'An unexpected error occurred. Please try again.',
        });
      } finally {
        setLoading(false);
      }
    },
    [formData, validateForm, login, onSuccess],
  );

  // Handle input changes
  const handleInputChange = useCallback(
    (field: keyof LoginFormData, value: string | boolean) => {
      setFormData((prev) => ({ ...prev, [field]: value }));

      // Clear specific field error
      if (errors[field as keyof ValidationErrors]) {
        setErrors((prev) => ({ ...prev, [field]: undefined }));
      }

      // Analyze password security
      if (field === 'password' && typeof value === 'string') {
        analyzePasswordSecurity(value);
      }
    },
    [errors, analyzePasswordSecurity],
  );

  // Security indicator colors
  const getSecurityColor = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'high':
        return 'text-brand-primary bg-success/10';
      case 'medium':
        return 'text-brand-primary bg-warning/10';
      case 'low':
        return 'text-brand-primary bg-error/10';
    }
  };

  const getSecurityProgress = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'high':
        return 100;
      case 'medium':
        return 60;
      case 'low':
        return 20;
    }
  };

  // Recent login attempts display
  const recentAttempts = loginAttempts.slice(0, 3);

  return (
    <div className={`max-w-md mx-auto ${className}`}>
      <Card
        className="card-elevated border-0 border-t-4 border-t-brand-primary"
      >
        <CardHeader className="text-center card-padding">
          <div
            className="w-16 h-16 mx-auto rounded-full flex-center form-field-spacing"
            style={{ background: 'var(--giki-ai-gradient-processing)' }}
          >
            <Shield className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-heading-2">
            Sign In to giki.ai
          </CardTitle>
          <p className="text-body-small mt-2">
            Professional financial categorization platform
          </p>
        </CardHeader>

        <CardContent className="card-padding">
          {/* Security Status */}
          {showSecurityFeatures && recentAttempts.length > 0 && (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                <div className="flex-between">
                  <span className="text-caption">Recent activity:</span>
                  <div className="flex gap-1">
                    {recentAttempts.map((attempt, index) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full ${
                          attempt.success ? '' : 'bg-error'
                        }`}
                        style={attempt.success ? { background: 'var(--giki-ai-gradient-processing)' } : {}}
                        title={`${attempt.success ? 'Success' : 'Failed'} - ${attempt.timestamp.toLocaleTimeString()}`}
                      />
                    ))}
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Error Display */}
          {(errors.general || authError) && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.general || authError}</AlertDescription>
            </Alert>
          )}

          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSubmit(e).catch(console.error);
            }}
            className="form-group"
          >
            {/* Email Field */}
            <div className="form-field-spacing">
              <Label
                htmlFor="email"
                className="text-label"
              >
                Email Address
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="Enter your email"
                  className={`input-elevated pl-10 ${errors.email ? 'border-destructive' : ''}`}
                  disabled={loading || authLoading}
                  autoComplete="email"
                />
              </div>
              {errors.email && (
                <p className="text-caption text-destructive">{errors.email}</p>
              )}
            </div>

            {/* Password Field */}
            <div className="form-field-spacing">
              <Label
                htmlFor="password"
                className="text-label"
              >
                Password
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) =>
                    handleInputChange('password', e.target.value)
                  }
                  placeholder="Enter your password"
                  className={`input-elevated pl-10 pr-10 ${errors.password ? 'border-destructive' : ''}`}
                  disabled={loading || authLoading}
                  autoComplete="current-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  disabled={loading || authLoading}
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-caption text-destructive">{errors.password}</p>
              )}

              {/* Password Security Indicator */}
              {showSecurityFeatures && formData.password && (
                <div className="component-gap-sm">
                  <div className="flex-between">
                    <span className="text-caption text-muted-foreground">
                      Security Level:
                    </span>
                    <Badge className={getSecurityColor(securityLevel)}>
                      {securityLevel.toUpperCase()}
                    </Badge>
                  </div>
                  <Progress
                    value={getSecurityProgress(securityLevel)}
                    className="h-2"
                  />
                </div>
              )}
            </div>

            {/* Remember Me */}
            {allowRememberMe && (
              <div className="flex items-center">
                <input
                  id="remember-me"
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={(e) =>
                    handleInputChange('rememberMe', e.target.checked)
                  }
                  className="h-4 w-4 text-brand-primary focus:ring-brand-primary border-brand-primary rounded"
                  disabled={loading || authLoading}
                />
                <Label
                  htmlFor="remember-me"
                  className="ml-2 text-label"
                >
                  Keep me signed in for 30 days
                </Label>
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              className="btn-professional btn-touch-friendly w-full text-white text-button-base"
              style={gradientButtonStyle}
              onMouseEnter={(e) => applyGradientHover(e.currentTarget, true)}
              onMouseLeave={(e) => applyGradientHover(e.currentTarget, false)}
              disabled={loading || authLoading}
            >
              {loading || authLoading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span className="text-button-base">Signing In...</span>
                </div>
              ) : (
                <span className="text-button-base">Sign In</span>
              )}
            </Button>

            {/* Forgot Password Link */}
            {onForgotPassword && (
              <div className="text-center">
                <button
                  type="button"
                  onClick={onForgotPassword}
                  className="text-caption text-brand-primary hover:text-[var(--giki-primary-hover)] underline"
                  disabled={loading || authLoading}
                >
                  Forgot your password?
                </button>
              </div>
            )}
          </form>

          {/* Security Notice */}
          {showSecurityFeatures && (
            <div className="section-spacing-sm card-padding bg-gray-50 rounded-lg">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-brand-primary mt-0.5" />
                <div className="text-sm">
                  <p className="text-label text-primary-foreground">Secure Connection</p>
                  <p className="text-body-small text-secondary-foreground mt-1">
                    Your login is protected with enterprise-grade security
                    including encryption, secure authentication, and audit
                    logging.
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
});

LoginForm.displayName = 'LoginForm';

export default LoginForm;
