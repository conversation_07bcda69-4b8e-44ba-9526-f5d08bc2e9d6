/**
 * Auth Hook
 *
 * Custom hook for managing authentication state and operations.
 */

import { useCallback, useState, useEffect } from 'react';
import useAuthStore from '@/shared/services/auth/authStore';
import { User, RegisterCredentials } from '../types/auth';
import {
  login as authServiceLogin,
  register as authServiceRegister,
} from '../services/authService';

export interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  token: string | null;
  login: (
    email: string,
    password: string,
    rememberMe?: boolean,
  ) => Promise<{ success: boolean; error?: string }>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => void;
  resetPassword: (email: string) => Promise<void>;
}

export const useAuth = (): UseAuthReturn => {
  const {
    isAuthenticated,
    isLoading,
    error,
    userId,
    tenantId,
    token,
    logout: storeLogout,
    checkAuth: storeCheckAuth,
  } = useAuthStore();

  // Store user profile data from API
  const [userProfile, setUserProfile] = useState<User | null>(null);
  
  // Fetch user profile data when authenticated
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (isAuthenticated && !userProfile) {
        try {
          const response = await fetch('/api/v1/auth/me', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            },
          });
          
          if (response.ok) {
            const userData = await response.json();
            setUserProfile(userData);
          }
        } catch (error) {
          // Failed to fetch user profile - continuing without user data
        }
      }
    };
    
    fetchUserProfile();
  }, [isAuthenticated, userProfile]);
  
  // Return real user data from API or null
  const user: User | null = userProfile;

  const login = useCallback(
    async (
      email: string,
      password: string,
      _rememberMe?: boolean,
    ): Promise<{ success: boolean; error?: string }> => {
      try {
        // Call the auth service to get tokens
        const tokens = await authServiceLogin({
          email: email,
          password: password,
        });

        // Use the auth store to process the tokens
        const storeLogin = useAuthStore.getState().login;
        storeLogin({
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken || null,
        });

        return { success: true };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Login failed';
        return { success: false, error: errorMessage };
      }
    },
    [],
  );

  const register = useCallback(
    async (credentials: RegisterCredentials): Promise<void> => {
      // Call the auth service for registration
      await authServiceRegister({
        email: credentials.email,
        password: credentials.password,
      });

      // Registration successful - user will need to login
      // No automatic login after registration
    },
    [],
  );

  const logout = useCallback((): Promise<void> => {
    storeLogout();
    setUserProfile(null); // Clear user profile on logout
    return Promise.resolve();
  }, [storeLogout]);

  const checkAuth = useCallback((): void => {
    storeCheckAuth();
  }, [storeCheckAuth]);

  const resetPassword = useCallback(async (email: string): Promise<void> => {
    // TODO: Implement password reset API call
    try {
      const response = await fetch('/api/v1/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      
      if (!response.ok) {
        throw new Error('Password reset request failed');
      }
      
      // Password reset email sent successfully
    } catch (error) {
      throw new Error('Failed to send password reset email');
    }
  }, []);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    token,
    login,
    register,
    logout,
    checkAuth,
    resetPassword,
  };
};
