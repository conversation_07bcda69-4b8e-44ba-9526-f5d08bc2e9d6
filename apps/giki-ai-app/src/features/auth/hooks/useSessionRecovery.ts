/**
 * Session recovery hook for automatic authentication error handling
 *
 * Features:
 * - Automatic token refresh on 401 errors
 * - Session timeout detection
 * - Network error recovery
 * - User notification for unrecoverable errors
 */

import { useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/shared/components/ui/use-toast';
import useAuthStore from '@/shared/services/auth/authStore';
import { logger } from '@/shared/lib/logger';
import { refreshToken } from '../services/authService';
import { getRefreshToken, clearTokens } from '../services/auth';

interface SessionRecoveryOptions {
  onSessionExpired?: () => void;
  onNetworkError?: () => void;
  enableAutoRefresh?: boolean;
  refreshThreshold?: number; // Minutes before expiry to refresh
}

export function useSessionRecovery(options: SessionRecoveryOptions = {}) {
  const {
    onSessionExpired,
    onNetworkError,
    enableAutoRefresh = true,
    refreshThreshold = 5,
  } = options;

  const navigate = useNavigate();
  const { toast } = useToast();
  const { isAuthenticated, logout } = useAuthStore();
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isRefreshingRef = useRef(false);

  // Parse JWT to get expiration time
  const getTokenExpiration = useCallback((token: string): Date | null => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1])) as { exp?: number };
      if (payload.exp && typeof payload.exp === 'number') {
        return new Date(payload.exp * 1000);
      }
    } catch (error) {
      logger.error('Failed to parse token expiration', { error });
    }
    return null;
  }, []);

  // Schedule automatic token refresh
  const scheduleTokenRefresh = useCallback(
    (accessToken: string) => {
      if (!enableAutoRefresh) return;

      // Clear any existing timer
      if (refreshTimerRef.current) {
        clearTimeout(refreshTimerRef.current);
      }

      const expiration = getTokenExpiration(accessToken);
      if (!expiration) return;

      const now = new Date();
      const timeUntilExpiry = expiration.getTime() - now.getTime();
      const refreshTime = timeUntilExpiry - refreshThreshold * 60 * 1000;

      if (refreshTime > 0) {
        const minutesUntilRefresh = Math.round(refreshTime / 1000 / 60);
        logger.info('📅 Scheduling token refresh', {
          expiresAt: expiration.toISOString(),
          refreshAt: new Date(now.getTime() + refreshTime).toISOString(),
          minutesUntilRefresh,
          minutesUntilExpiry: Math.round(timeUntilExpiry / 1000 / 60),
        });

        refreshTimerRef.current = setTimeout(() => {
          logger.info('⚬ Executing scheduled token refresh');
          performTokenRefresh().catch((error) => {
            logger.error('□ Scheduled token refresh failed', {
              error,
              willRetryOn401: true,
            });
          });
        }, refreshTime);
      } else if (timeUntilExpiry > 0) {
        // Token expires soon, refresh immediately
        logger.warn('⚠️ Token expires soon, refreshing immediately', {
          minutesUntilExpiry: Math.round(timeUntilExpiry / 1000 / 60),
        });
        performTokenRefresh().catch((error) => {
          logger.error('□ Immediate token refresh failed', { error });
        });
      } else {
        logger.warn('⚠️ Token already expired, cannot schedule refresh');
      }
    },
    [enableAutoRefresh, refreshThreshold, getTokenExpiration],
  );

  // Perform token refresh
  const performTokenRefresh = useCallback(async () => {
    if (isRefreshingRef.current) return;

    const currentRefreshToken = getRefreshToken();
    if (!currentRefreshToken) {
      logger.warn('No refresh token available for automatic refresh');
      return;
    }

    isRefreshingRef.current = true;
    logger.info('↑ Starting automatic token refresh');

    try {
      const tokens = await refreshToken(currentRefreshToken);

      // Update auth store with new tokens
      const authStore = useAuthStore.getState();
      authStore.login({
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken || currentRefreshToken,
      });

      // Schedule next refresh
      scheduleTokenRefresh(tokens.accessToken);

      logger.info('✅ Token refresh successful', {
        newTokenReceived: true,
        nextRefreshScheduled: true,
      });
    } catch (error) {
      logger.error('□ Automatic token refresh failed', {
        error,
        willLogoutUser: true,
      });

      // Clear tokens and redirect to login
      clearTokens();
      logout();

      toast({
        title: 'Session Expired',
        description: 'Your session has expired. Please log in again.',
        variant: 'destructive',
      });

      onSessionExpired?.();
      navigate('/login');
    } finally {
      isRefreshingRef.current = false;
    }
  }, [navigate, toast, logout, onSessionExpired, scheduleTokenRefresh]);

  // Handle authentication errors globally
  useEffect(() => {
    const handleAuthError = (
      event: CustomEvent<{ statusCode: number; message: string }>,
    ) => {
      const { statusCode, message } = event.detail;

      if (statusCode === 401 && !isRefreshingRef.current) {
        // Token expired or invalid
        logger.warn(
          '🚨 401 Authentication error detected - attempting token refresh',
          {
            statusCode,
            message,
            isAlreadyRefreshing: isRefreshingRef.current,
          },
        );

        // Attempt to refresh token
        performTokenRefresh().catch((error) => {
          logger.error('□ Auth error token refresh failed', {
            error,
            willRedirectToLogin: true,
          });
        });
      } else if (statusCode === 401 && isRefreshingRef.current) {
        logger.info(
          'ℹ️ 401 error received while already refreshing - ignoring duplicate',
        );
      } else if (statusCode === 403) {
        // Access forbidden
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to access this resource.',
          variant: 'destructive',
        });
      }
    };

    const handleNetworkError = (event: CustomEvent<{ message: string }>) => {
      logger.error('Network error detected', { message: event.detail.message });

      toast({
        title: 'Connection Error',
        description: 'Please check your internet connection and try again.',
        variant: 'destructive',
      });

      onNetworkError?.();
    };

    // Listen for authentication and network errors
    window.addEventListener('auth-error', handleAuthError as EventListener);
    window.addEventListener(
      'network-error',
      handleNetworkError as EventListener,
    );

    return () => {
      window.removeEventListener(
        'auth-error',
        handleAuthError as EventListener,
      );
      window.removeEventListener(
        'network-error',
        handleNetworkError as EventListener,
      );
    };
  }, [performTokenRefresh, toast, onNetworkError]);

  // Schedule initial token refresh on mount
  useEffect(() => {
    if (isAuthenticated) {
      const accessToken = localStorage.getItem('accessToken');
      if (accessToken) {
        logger.info(
          '↑ Session recovery initialized - scheduling token refresh',
        );
        scheduleTokenRefresh(accessToken);
      } else {
        logger.warn(
          '⚠️ Authenticated but no access token found in localStorage',
        );
      }
    } else {
      logger.info('ℹ️ User not authenticated - session recovery inactive');
    }

    return () => {
      if (refreshTimerRef.current) {
        clearTimeout(refreshTimerRef.current);
      }
    };
  }, [isAuthenticated, scheduleTokenRefresh]);

  // Monitor for token changes
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'accessToken' && e.newValue) {
        scheduleTokenRefresh(e.newValue);
      } else if (e.key === 'accessToken' && !e.newValue) {
        // Token removed, clear refresh timer
        if (refreshTimerRef.current) {
          clearTimeout(refreshTimerRef.current);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [scheduleTokenRefresh]);

  return {
    performTokenRefresh,
    isRefreshing: isRefreshingRef.current,
  };
}

// Export the hook for global usage
export const useGlobalSessionRecovery = () => {
  return useSessionRecovery({
    enableAutoRefresh: true,
    refreshThreshold: 10, // Refresh 10 minutes before expiry for better reliability
  });
};
