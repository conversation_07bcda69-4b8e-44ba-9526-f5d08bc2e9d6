import React from 'react';
import { useSessionRecovery } from '../hooks/useSessionRecovery';

interface SessionRecoveryProviderProps {
  children: React.ReactNode;
}

export function SessionRecoveryProvider({
  children,
}: SessionRecoveryProviderProps) {
  // Initialize global session recovery
  useSessionRecovery({
    enableAutoRefresh: true,
    refreshThreshold: 10, // Refresh 10 minutes before expiry for better reliability
  });

  return <>{children}</>;
}
