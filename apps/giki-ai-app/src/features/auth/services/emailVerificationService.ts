import { ErrorType, createApiError, isAppError } from '@/shared/types/errors';
import { apiClient } from '@/shared/services/api/apiClient';

export interface EmailVerificationResponse {
  verified: boolean;
  message: string;
}

export const sendVerificationEmail = async (email: string): Promise<void> => {
  try {
    await apiClient.post('/auth/send-verification-email', { email });
    // apiClient handles non-ok responses by throwing AppError
  } catch (error) {
    if (isAppError(error)) {
      throw error;
    }
    throw createApiError({
      type: ErrorType.UNKNOWN,
      message:
        error instanceof Error
          ? error.message
          : 'Unknown error sending verification email',
      // details: { originalError: error } // Keep original error in details if needed
    });
  }
};

export const verifyEmail = async (
  token: string,
): Promise<EmailVerificationResponse> => {
  try {
    const response = await apiClient.post<{ message?: string }>(
      '/auth/verify-email',
      { token },
    );
    return {
      verified: true,
      message: response?.data?.message || 'Email successfully verified',
    };
  } catch (error) {
    if (isAppError(error)) {
      // Customize message for specific error types if needed
      if (error.type === ErrorType.NOT_FOUND && error.statusCode === 404) {
        throw createApiError({
          type: ErrorType.VALIDATION,
          message: 'Verification token not found or expired.',
          statusCode: error.statusCode,
          // details: { originalError: error }
        });
      }
      throw error;
    }
    throw createApiError({
      type: ErrorType.UNKNOWN,
      message:
        error instanceof Error
          ? error.message
          : 'Unknown error verifying email',
      // details: { originalError: error }
    });
  }
};

export const resendVerificationEmail = async (email: string): Promise<void> => {
  try {
    await apiClient.post('/auth/resend-verification-email', { email });
    // apiClient handles non-ok responses by throwing AppError
  } catch (error) {
    if (isAppError(error)) {
      throw error;
    }
    throw createApiError({
      type: ErrorType.UNKNOWN,
      message:
        error instanceof Error
          ? error.message
          : 'Unknown error resending verification email',
      // details: { originalError: error }
    });
  }
};
