import { apiClient } from '@/shared/services/api/apiClient';
import {
  ErrorType,
  createAuthenticationError,
  createApiError,
  AppError,
} from '@/shared/types/errors';
import { logger, RetryHandler } from '@/shared/utils/errorHandling';
import { getApiBaseUrl } from '@/shared/utils/api';
import { User } from '../types/auth';

// Authentication retry configuration
const authRetryHandler = new RetryHandler({
  maxAttempts: 2, // Less aggressive for auth
  initialDelay: 1000,
  maxDelay: 5000,
  retryableErrors: [ErrorType.NETWORK, ErrorType.SERVER],
});

// API endpoint configuration
const AUTH_BASE_PATH = '/auth';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegistrationCredentials {
  email: string;
  password: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  tokenType?: string;
}

// Interface for the raw token response from the backend
interface RawTokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type?: string;
}

/**
 * Login function with automatic retry on transient failures
 */
export const login = async (
  credentials: LoginCredentials,
): Promise<AuthTokens> => {
  logger.info('authService.login called', 'authService', {
    email: credentials.email,
  });

  const executeLogin = async (): Promise<AuthTokens> => {
    const response = await apiClient.postFormUrlEncoded<RawTokenResponse>(
      `${AUTH_BASE_PATH}/token`,
      {
        username: credentials.email,
        password: credentials.password,
      },
      {
        requiresAuth: false,
        signal: AbortSignal.timeout(30000),
        retry: false, // We handle retry at this level for better control
      },
    );

    const { data } = response;
    const tokens: AuthTokens = {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      tokenType: data.token_type || 'bearer',
    };
    return tokens;
  };

  try {
    // Use retry handler for network/server errors only
    return await authRetryHandler.execute(executeLogin, 'login');
  } catch (error) {
    logger.error('Authentication failed', 'authService', error as Error);

    const authError = error as AppError;

    // Don't retry authentication errors (wrong password, etc.)
    if (authError.statusCode === 401) {
      throw createAuthenticationError(
        'Invalid email or password',
        authError.statusCode,
      );
    } else if (authError.statusCode === 403) {
      throw createAuthenticationError(
        'Account is inactive or locked',
        authError.statusCode,
      );
    } else if (authError.statusCode === 429) {
      throw createAuthenticationError(
        'Too many login attempts. Please try again later.',
        authError.statusCode,
      );
    } else if (
      authError.statusCode === 408 ||
      (error instanceof Error && error.name === 'TimeoutError') ||
      (error instanceof Error && error.name === 'AbortError')
    ) {
      throw createAuthenticationError(
        'Authentication is taking longer than expected. Please try again.',
        408,
      );
    }
    throw error;
  }
};

/**
 * Refreshes an access token using a refresh token with retry capability.
 * This function is called by apiClient's internal refresh mechanism.
 * It MUST use direct fetch to avoid circular dependencies with apiClient.
 */
export const refreshToken = async (
  currentRefreshToken: string,
): Promise<AuthTokens> => {
  logger.info('authService.refreshToken called', 'authService');

  const apiBaseUrl = getApiBaseUrl();
  const fullRefreshUrl = `${apiBaseUrl}/api/v1${AUTH_BASE_PATH}/refresh`;

  const executeRefresh = async (): Promise<AuthTokens> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

    try {
      const response = await fetch(fullRefreshUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${currentRefreshToken}`,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify({ refresh_token: currentRefreshToken }),
        credentials: 'include',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = 'Token refresh failed';
        try {
          const errorData = (await response.json()) as { detail?: string };
          errorMessage = errorData.detail || errorMessage;
        } catch (e) {
          logger.warn(
            'Could not parse error response during refresh',
            'authService',
            e as Error,
          );
        }

        // Don't retry on authentication errors
        if (response.status === 401 || response.status === 403) {
          throw createAuthenticationError(
            response.status === 401
              ? 'Refresh token expired or invalid'
              : 'Access denied',
            response.status,
          );
        }

        // Retry on server errors
        if (response.status >= 500) {
          throw createApiError({
            type: ErrorType.SERVER,
            message: errorMessage,
            statusCode: response.status,
          });
        }

        throw createAuthenticationError(errorMessage, response.status);
      }

      const data = (await response.json()) as RawTokenResponse;
      const tokens: AuthTokens = {
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
        tokenType: data.token_type || 'bearer',
      };
      return tokens;
    } catch (error) {
      clearTimeout(timeoutId);

      // Handle abort errors
      if (error instanceof Error && error.name === 'AbortError') {
        throw createApiError({
          type: ErrorType.NETWORK,
          message: 'Token refresh timed out',
          statusCode: 408,
        });
      }

      // Handle network errors
      if (
        error instanceof TypeError &&
        error.message.includes('Failed to fetch')
      ) {
        throw createApiError({
          type: ErrorType.NETWORK,
          message: 'Network error during token refresh',
          statusCode: 0,
        });
      }

      throw error;
    }
  };

  try {
    // Use retry handler for network/server errors
    return await authRetryHandler.execute(executeRefresh, 'refreshToken');
  } catch (error) {
    logger.error(
      'Token refresh error in authService',
      'authService',
      error as Error,
    );

    // Ensure a proper AppError is thrown for apiClient to catch
    if (error && typeof error === 'object' && 'type' in error) {
      throw error; // It's already an AppError, re-throw
    }

    // Otherwise, wrap it
    const message =
      error instanceof Error
        ? error.message
        : 'Token refresh failed due to an unexpected error';
    throw createAuthenticationError(message);
  }
};

/**
 * Registers a new user.
 */
export const register = async (
  credentials: RegistrationCredentials,
): Promise<{ message: string }> => {
  logger.info('authService.register called', 'authService', {
    email: credentials.email,
  });
  try {
    const response = await apiClient.post<{ message: string }>(
      `${AUTH_BASE_PATH}/register`,
      {
        email: credentials.email,
        password: credentials.password,
      },
      { requiresAuth: false }, // Registration does not require prior authentication
    );
    return { message: response?.data?.message || 'Registration successful' };
  } catch (error) {
    logger.error(
      'Registration error in authService',
      'authService',
      error as Error,
    );
    if ((error as AppError).statusCode === 409) {
      throw createAuthenticationError(
        'An account with this email already exists.',
        (error as AppError).statusCode,
      );
    } else if (
      (error as AppError).statusCode === 400 ||
      (error as AppError).type === ErrorType.VALIDATION
    ) {
      throw createAuthenticationError(
        'Invalid registration data. Please check your inputs.',
        (error as AppError).statusCode,
      );
    }
    throw error;
  }
};

/**
 * Logs out a user.
 * Server-side logout to invalidate refresh token.
 */
export const logout = async (currentRefreshToken?: string): Promise<void> => {
  logger.info('authService.logout called', 'authService');
  if (currentRefreshToken) {
    try {
      // Logout doesn't need the access token, but needs to send the refresh token.
      // apiClient's default post will try to use access token if requiresAuth is true.
      // So, set requiresAuth to false and manually add Authorization header.
      await apiClient.post<void>(
        `${AUTH_BASE_PATH}/logout`,
        {}, // No body needed for logout typically, or backend specific
        {
          requiresAuth: false, // Don't use access token
          headers: {
            Authorization: `Bearer ${currentRefreshToken}`,
          },
        },
      );
    } catch (error) {
      logger.warn(
        'Failed to call server-side logout, proceeding with client-side only',
        'authService',
        error as Error,
      );
      // apiClient will throw an AppError, which is fine.
      // We don't want to re-throw here as client-side token clearing should still happen.
    }
  }
  // Client-side token clearing is handled by the auth store.
  return Promise.resolve();
};

/**
 * Gets the current user's profile with automatic retry.
 */
export const getCurrentUser = async (): Promise<User> => {
  logger.info('authService.getCurrentUser called', 'authService');

  try {
    const response = await apiClient.get<User>(`${AUTH_BASE_PATH}/me`, {
      retry: {
        maxAttempts: 2,
        initialDelay: 500,
        maxDelay: 5000,
        backoffMultiplier: 2,
        retryableErrors: [ErrorType.NETWORK, ErrorType.SERVER],
      },
    });
    return response.data;
  } catch (error) {
    logger.error(
      'Error getting user profile in authService',
      'authService',
      error as Error,
    );

    const authError = error as AppError;

    // Provide better error messages
    if (authError.statusCode === 401) {
      throw createAuthenticationError(
        'Your session has expired. Please log in again.',
        authError.statusCode,
      );
    } else if (authError.type === ErrorType.NETWORK) {
      throw createAuthenticationError(
        'Unable to load user profile. Please check your connection.',
        0,
      );
    }

    throw error;
  }
};
