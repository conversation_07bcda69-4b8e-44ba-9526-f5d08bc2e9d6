/**
 * Professional Landing + Login Page
 * Following: docs/design-system/page-mockups/000-landing-login.html
 * Turn Messy Statements Into Organized Books
 */
import React, { useEffect, useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Card, CardContent } from '@/shared/components/ui/card';
import { useAuth } from '../hooks/useAuth';
import { useToast } from '@/shared/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { brandGradients } from '@/shared/utils/brandGradients';
import { spacing, layoutSpacing } from '@/shared/utils/spacing';

const LoginPage: React.FC = () => {
  const { isAuthenticated, login } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // If user is already authenticated, redirect intelligently
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/', {
        replace: true,
      });
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await login(email, password);
      if (result.success) {
        toast({
          title: 'Welcome back!',
          description: "You've been signed in successfully.",
        });
        navigate('/', {
          replace: true,
        });
      } else {
        toast({
          title: 'Sign in failed',
          description:
            result.error || 'Please check your credentials and try again.',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        title: 'Sign in failed',
        description: 'Please check your credentials and try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };


  return (
    <div className="min-h-screen bg-background font-inter flex flex-col">
      {/* Main Landing Container - Two Column Layout */}
      <main className="flex-1">
        <div className="max-w-[1400px] w-full mx-auto" style={{ padding: `${layoutSpacing.sectionGap} ${layoutSpacing.containerPadding}` }}>
          <div className="grid grid-cols-1 lg:grid-cols-[1fr_460px] items-start" style={{ gap: layoutSpacing.sectionGapLarge }}>
          {/* Hero Section - Left Column */}
          <div style={{ paddingRight: layoutSpacing.sectionGap }}>
            {/* Hero Title */}
            <h1 className="text-5xl lg:text-6xl font-extrabold text-gray-900 leading-tight" style={{ marginBottom: layoutSpacing.sectionGap }}>
              Turn Messy Statements<br/>
              Into <span className="text-giki-primary">Organized Books</span>
            </h1>

            {/* Hero Subtitle */}
            <p className="text-2xl text-gray-600 leading-relaxed" style={{ marginBottom: layoutSpacing.sectionGap }}>
              AI categorizes your transactions. Export to any accounting software.
            </p>

            {/* Process Preview Banner */}
            <div className="text-white rounded-2xl shadow-lg" style={{ 
              padding: layoutSpacing.sectionGap, 
              background: brandGradients.primary,
              boxShadow: '0 8px 32px rgba(41, 83, 67, 0.2)'
            }}>
              <div className="flex items-center justify-around">
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl mx-auto border-2 border-white/30" style={{ marginBottom: spacing[4] }}>
                    ↑
                  </div>
                  <div className="font-semibold text-base">Bank Statements</div>
                  <div className="text-sm opacity-80" style={{ marginTop: spacing[1] }}>Upload any format</div>
                </div>
                <div className="text-2xl opacity-60">→</div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl mx-auto border-2 border-white/30" style={{ marginBottom: spacing[4] }}>
                    ⚬
                  </div>
                  <div className="font-semibold text-base">AI Categorized</div>
                  <div className="text-sm opacity-80" style={{ marginTop: spacing[1] }}>Intelligent processing</div>
                </div>
                <div className="text-2xl opacity-60">→</div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl mx-auto border-2 border-white/30" style={{ marginBottom: spacing[4] }}>
                    □
                  </div>
                  <div className="font-semibold text-base">Export Ready</div>
                  <div className="text-sm opacity-80" style={{ marginTop: spacing[1] }}>QuickBooks, Xero, CSV</div>
                </div>
              </div>
            </div>

            {/* Get in Touch - Business Setup Service */}
            <div className="text-center" style={{ marginTop: layoutSpacing.sectionGap }}>
              <div className="text-center" style={{ marginBottom: spacing[3] }}>
                <p className="text-gray-600 text-sm">
                  Need help getting started? We'll set everything up for you.
                </p>
              </div>
              <button
                className="inline-flex items-center px-8 py-3 border-2 border-giki-primary text-giki-primary bg-white rounded-xl font-semibold text-lg hover:bg-giki-primary hover:text-white transition-all duration-200 shadow-md hover:shadow-lg"
                onClick={() => window.open('mailto:<EMAIL>?subject=Business MIS Setup - Let\'s Get Started&body=Hi! I\'d like to learn more about having the giki.ai team set up my MIS. Here are some details about my business:%0D%0A%0D%0ABusiness Name: %0D%0AIndustry: %0D%0AMonthly Transaction Volume: %0D%0AAccounting Software: %0D%0A%0D%0APlease let me know the next steps!', '_blank')}
              >
                Get Expert Setup Help
              </button>
            </div>

          </div>

          {/* Professional Login Panel - Right Column */}
          <Card className="bg-white border border-border rounded-[20px]" style={{ 
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.08)' 
          }}>
            <CardContent style={{ padding: layoutSpacing.sectionGap }}>
              {/* Login Header */}
              <div className="text-center" style={{ marginBottom: layoutSpacing.sectionGap }}>
                <h2 className="text-3xl font-bold text-gray-900" style={{ marginBottom: spacing[2] }}>Welcome Back</h2>
                <p className="text-gray-600">
                  Access your MIS dashboard
                </p>
              </div>

              {/* Login Form */}
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit(e).catch(console.error);
                }}
                style={{ display: 'flex', flexDirection: 'column', gap: spacing[5] }}
              >
                {/* Email Field */}
                <div>
                  <Label
                    htmlFor="email"
                    className="text-sm font-semibold text-gray-700 block"
                    style={{ marginBottom: spacing[2] }}
                  >
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full border border-border rounded-[10px] text-[1.0625rem] focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:border-primary transition-all duration-200"
                    style={{ padding: `${spacing[4]} ${spacing[5]}` }}
                    required
                  />
                </div>

                {/* Password Field */}
                <div>
                  <Label
                    htmlFor="password"
                    className="text-sm font-semibold text-gray-700 block"
                    style={{ marginBottom: spacing[2] }}
                  >
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="••••••••"
                    className="w-full border border-border rounded-[10px] text-[1.0625rem] focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:border-primary transition-all duration-200"
                    style={{ padding: `${spacing[4]} ${spacing[5]}` }}
                    required
                  />
                </div>

                {/* Remember Me & Forgot Password */}
                <div className="flex items-center justify-between text-sm" style={{ marginBottom: spacing[6] }}>
                  <div className="flex items-center" style={{ gap: spacing[2] }}>
                    <input type="checkbox" id="remember" className="rounded border-gray-300" />
                    <label htmlFor="remember" className="text-gray-600">Remember me</label>
                  </div>
                  <Link to="/forgot-password" className="text-giki-primary hover:text-giki-primary-hover font-medium">
                    Forgot password?
                  </Link>
                </div>

                {/* Sign In Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full text-white text-lg font-semibold rounded-xl transition-all duration-200"
                  style={{
                    background: brandGradients.primary,
                    boxShadow: brandGradients.boxShadow,
                    padding: `${spacing[4]} 0`
                  }}
                  onMouseEnter={(e) => {
                    if (!isLoading) {
                      e.currentTarget.style.background = brandGradients.primaryHover;
                      e.currentTarget.style.boxShadow = '0 6px 20px rgba(41,83,67,0.3)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isLoading) {
                      e.currentTarget.style.background = brandGradients.primary;
                      e.currentTarget.style.boxShadow = brandGradients.boxShadow;
                    }
                  }}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    'Sign In'
                  )}
                </Button>
              </form>

              {/* Divider */}
              <div className="relative" style={{ margin: `${spacing[5]} 0` }}>
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-white text-gray-500" style={{ padding: `0 ${spacing[4]}` }}>or</span>
                </div>
              </div>

              {/* Create Account Section */}
              <div className="text-center">
                <p className="text-sm text-gray-600">
                  New to giki.ai? <Link to="/signup" className="text-giki-primary font-medium hover:text-giki-primary-hover">Create account</Link>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

      </div>
    </main>
    </div>
  );
};

export default LoginPage;
