/**
 * Get Started Page - Authentic Contact Form
 * Matches wireframe: docs/wireframes/01-prospect-journey/02-get-started.md
 */
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Textarea } from '@/shared/components/ui/textarea';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  Sparkles,
  CheckCircle,
  Target,
  TrendingUp,
  Clock,
  FileSpreadsheet,
} from 'lucide-react';
import { contactService, ContactFormData } from '../services/contactService';
import { logger } from '@/shared/utils/errorHandling';

const GetStartedPage: React.FC = () => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    phone: '',
    message: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      logger.info('GetStartedPage: Submitting contact form', 'GetStartedPage', {
        email: formData.email,
        company: formData.company,
      });

      // Validate form data
      const validationErrors = contactService.validateContactForm(formData as ContactFormData);
      if (validationErrors.length > 0) {
        toast({
          title: 'Please fix the following errors',
          description: validationErrors.join(', '),
          variant: 'destructive',
        });
        return;
      }

      // Submit real contact form
      const response = await contactService.submitContactForm(formData as ContactFormData);

      if (response.success) {
        toast({
          title: 'Request received',
          description: response.message || 'Our team will contact you within 24 hours to discuss your financial intelligence needs.',
          variant: 'default',
        });

        // Reset form on success
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          company: '',
          phone: '',
          message: '',
        });

        logger.info('GetStartedPage: Contact form submitted successfully', 'GetStartedPage', {
          contactId: response.contactId,
        });
      } else {
        throw new Error(response.message || 'Form submission failed');
      }

    } catch (error) {
      logger.error('GetStartedPage: Error submitting contact form', 'GetStartedPage', error as Error);
      
      toast({
        title: 'Something went wrong',
        description: (error as Error).message || 'Please try again or contact us directly.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-[#E8F5E8]/30">
      {/* Header Navigation */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link to="/" className="flex items-center gap-2">
              <Sparkles className="h-8 w-8 text-brand-primary" />
              <span className="text-2xl font-bold bg-gradient-to-r from-brand-primary to-[#1D372E] bg-clip-text text-transparent">
                giki.ai
              </span>
            </Link>
            <Link
              to="/how-it-works"
              className="text-slate-600 hover:text-slate-900 font-medium transition-colors"
            >
              How It Works
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Left Side - Value Proposition */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h1 className="text-4xl lg:text-5xl font-bold text-slate-900 leading-tight">
                Professional Financial
                <br />
                <span className="bg-gradient-to-r from-brand-primary to-[#1D372E] bg-clip-text text-transparent">
                  Intelligence Platform
                </span>
              </h1>

              <p className="text-xl text-slate-600 leading-relaxed">
                Transform your financial data into actionable business insights.
                <br />
                AI-powered categorization with professional-grade accuracy.
              </p>
            </div>

            {/* Pain Points */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-slate-900">No more:</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-slate-700">
                  <span className="text-brand-primary">•</span>
                  Manual categorization
                </div>
                <div className="flex items-center gap-3 text-slate-700">
                  <span className="text-brand-primary">•</span>
                  Spreadsheet formatting
                </div>
                <div className="flex items-center gap-3 text-slate-700">
                  <span className="text-brand-primary">•</span>
                  Data entry errors
                </div>
                <div className="flex items-center gap-3 text-slate-700">
                  <span className="text-brand-primary">•</span>
                  Weekend report prep
                </div>
              </div>
            </div>

            {/* Product Preview Placeholder */}
            <Card className="p-8 bg-gradient-to-br from-[#E8F5E8] to-[#E8F5E8] border-brand-primary/20">
              <div className="text-center space-y-4">
                <FileSpreadsheet className="h-16 w-16 mx-auto text-brand-primary" />
                <h4 className="text-lg font-semibold text-slate-900">
                  See how it works
                </h4>
                <p className="text-slate-600">
                  Real functionality, honest representation
                </p>
              </div>
            </Card>

            {/* Benefits */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-slate-900">
                What you get:
              </h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-brand-primary" />
                  <span className="text-slate-700">Your data, organized</span>
                </div>
                <div className="flex items-center gap-3">
                  <TrendingUp className="h-5 w-5 text-brand-primary" />
                  <span className="text-slate-700">
                    Reports when you need them
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <Target className="h-5 w-5 text-brand-primary" />
                  <span className="text-slate-700">
                    Answers to finance questions
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-orange-600" />
                  <span className="text-slate-700">Time back in your day</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Contact Form */}
          <div className="flex justify-center lg:justify-end">
            <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-6">
                <CardTitle className="text-2xl font-bold text-slate-900">
                  Let&apos;s Talk
                </CardTitle>
                <p className="text-slate-600 mt-2">We&apos;ll reach out soon</p>
              </CardHeader>

              <CardContent>
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    handleSubmit(e).catch(console.error);
                  }}
                  className="space-y-4"
                >
                  {/* First Name */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="firstName"
                      className="text-sm font-medium text-slate-700"
                    >
                      First Name
                    </Label>
                    <Input
                      id="firstName"
                      type="text"
                      value={formData.firstName}
                      onChange={(e) =>
                        handleInputChange('firstName', e.target.value)
                      }
                      className="border-slate-300 focus:border-brand-primary focus:ring-brand-primary"
                      placeholder="Enter your first name"
                      required
                    />
                  </div>

                  {/* Last Name */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="lastName"
                      className="text-sm font-medium text-slate-700"
                    >
                      Last Name
                    </Label>
                    <Input
                      id="lastName"
                      type="text"
                      value={formData.lastName}
                      onChange={(e) =>
                        handleInputChange('lastName', e.target.value)
                      }
                      className="border-slate-300 focus:border-brand-primary focus:ring-brand-primary"
                      placeholder="Enter your last name"
                      required
                    />
                  </div>

                  {/* Work Email */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="email"
                      className="text-sm font-medium text-slate-700"
                    >
                      Work Email
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) =>
                        handleInputChange('email', e.target.value)
                      }
                      className="border-slate-300 focus:border-brand-primary focus:ring-brand-primary"
                      placeholder="Enter your work email"
                      required
                    />
                  </div>

                  {/* Company */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="company"
                      className="text-sm font-medium text-slate-700"
                    >
                      Company
                    </Label>
                    <Input
                      id="company"
                      type="text"
                      value={formData.company}
                      onChange={(e) =>
                        handleInputChange('company', e.target.value)
                      }
                      className="border-slate-300 focus:border-brand-primary focus:ring-brand-primary"
                      placeholder="Enter your company name"
                      required
                    />
                  </div>

                  {/* Phone (Optional) */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="phone"
                      className="text-sm font-medium text-slate-700"
                    >
                      Phone (Optional)
                    </Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) =>
                        handleInputChange('phone', e.target.value)
                      }
                      className="border-slate-300 focus:border-brand-primary focus:ring-brand-primary"
                      placeholder="Enter your phone number"
                    />
                  </div>

                  {/* Message */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="message"
                      className="text-sm font-medium text-slate-700"
                    >
                      Tell us about your needs:
                    </Label>
                    <Textarea
                      id="message"
                      value={formData.message}
                      onChange={(e) =>
                        handleInputChange('message', e.target.value)
                      }
                      className="border-slate-300 focus:border-purple-500 focus:ring-purple-500 min-h-[80px]"
                      placeholder="(Optional message)"
                    />
                  </div>

                  {/* Submit Button */}
                  <div className="pt-2">
                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-brand-primary to-[#1D372E] hover:from-[#1D372E] hover:to-brand-primary text-white font-semibold py-2.5"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Sending...' : 'Let&apos;s Talk'}
                    </Button>
                  </div>

                  <div className="text-center text-sm text-slate-600 pt-2">
                    We&apos;ll reach out soon
                  </div>

                  {/* Back to Login */}
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-slate-300" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-white px-2 text-slate-500">or</span>
                    </div>
                  </div>

                  <div className="text-center">
                    <span className="text-sm text-slate-600">
                      Already have an account?{' '}
                      <Link
                        to="/"
                        className="text-brand-primary hover:text-[#1D372E] font-medium transition-colors"
                      >
                        Sign in →
                      </Link>
                    </span>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default GetStartedPage;
