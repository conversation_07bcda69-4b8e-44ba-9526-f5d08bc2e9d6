/**
 * Export Format Selector Component
 * Allows users to choose export format with live preview
 */

import React, { useState } from 'react';
import { Card } from '@/shared/components/ui/card';

interface ExportFormat {
  id: 'excel' | 'quickbooks' | 'csv';
  name: string;
  description: string;
  icon: string;
  fileExtension: string;
}

interface ExportFormatSelectorProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
  transactionCount: number;
  categoryCount: number;
  accuracy: number;
}

const formats: ExportFormat[] = [
  {
    id: 'excel',
    name: 'Excel',
    description: 'Complete workbook',
    icon: '∷',
    fileExtension: '.xlsx'
  },
  {
    id: 'quickbooks',
    name: 'QuickBooks',
    description: 'QBO format',
    icon: '⊞',
    fileExtension: '.qbo'
  },
  {
    id: 'csv',
    name: 'CSV',
    description: 'Simple format',
    icon: '□',
    fileExtension: '.csv'
  }
];

export const ExportFormatSelector = React.memo<ExportFormatSelectorProps>(({
  selectedFormat,
  onFormatChange,
  transactionCount,
  categoryCount,
  accuracy
}) => {
  const [exportOptions, setExportOptions] = useState({
    includeHierarchies: true,
    includeConfidenceScores: true,
    includeOriginalDescriptions: false,
    includeTransactionNotes: false
  });

  const handleOptionChange = (option: string, checked: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      [option]: checked
    }));
  };

  const renderPreview = () => {
    switch (selectedFormat) {
      case 'excel':
        return (
          <div>
            <div className="flex gap-2 mb-3">
              <span className="bg-muted px-3 py-2 rounded text-caption">Sheet 1: Transactions</span>
              <span className="bg-success/10 px-3 py-2 rounded text-caption text-brand-primary font-medium">Sheet 2: Summary</span>
              <span className="bg-muted px-3 py-2 rounded text-caption">Sheet 3: Pivot</span>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full text-table-cell">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-3 text-table-header">Date</th>
                    <th className="text-left py-2 px-3 text-table-header">Vendor</th>
                    <th className="text-right py-2 px-3 text-table-header">Amount</th>
                    <th className="text-left py-2 px-3 text-table-header">Category</th>
                    <th className="text-left py-2 px-3 text-table-header">Sub-Category</th>
                    <th className="text-left py-2 px-3 text-table-header">GL Code</th>
                    {exportOptions.includeConfidenceScores && <th className="text-left py-2 px-3 text-table-header">Confidence</th>}
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="py-2 px-3 text-table-cell">2024-03-15</td>
                    <td className="py-2 px-3 text-table-cell">AMAZON.COM</td>
                    <td className="py-2 px-3 text-right text-table-cell-mono">$127.49</td>
                    <td className="py-2 px-3 text-table-cell">Operating Expenses</td>
                    <td className="py-2 px-3 text-table-cell">Office Supplies</td>
                    <td className="py-2 px-3 text-table-cell-mono">5100</td>
                    {exportOptions.includeConfidenceScores && <td className="py-2 px-3 text-table-cell-mono">94%</td>}
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        );
      case 'quickbooks':
        return (
          <div className="bg-success/10 border border-brand-primary rounded-lg p-4">
            <div className="text-heading-5 text-brand-primary mb-2">QBO Import Format</div>
            <pre className="text-code text-muted-foreground">
{`!TRNS TRNSID TRNSTYPE DATE ACCNT NAME CLASS AMOUNT DOCNUM MEMO
!SPL SPLID TRNSTYPE DATE ACCNT NAME CLASS AMOUNT DOCNUM MEMO
!ENDTRNS`}
            </pre>
          </div>
        );
      case 'csv':
        return (
          <div className="bg-muted border border-border rounded-lg p-4">
            <pre className="text-code">
{`Date,Description,Amount,Category${exportOptions.includeConfidenceScores ? ',Confidence' : ''}
2024-03-15,"AMAZON.COM AMZN.COM/BILL",127.49,"Office Supplies"${exportOptions.includeConfidenceScores ? ',94' : ''}
2024-03-14,"UBER TRIP SAN FRANCISCO",23.45,"Travel & Entertainment"${exportOptions.includeConfidenceScores ? ',98' : ''}`}
            </pre>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-8">
      {/* Format Selector */}
      <div className="flex justify-center gap-4">
        {formats.map((format) => (
          <div
            key={format.id}
            onClick={() => onFormatChange(format.id)}
            className={`bg-white border-2 rounded-xl p-4 px-6 cursor-pointer transition-all ${
              selectedFormat === format.id
                ? 'border-brand-primary shadow-lg'
                : 'border-border hover:border-muted-foreground'
            }`}
          >
            <div className="flex items-center gap-3">
              <span className="text-2xl">{format.icon}</span>
              <div>
                <div className={`text-heading-5 ${selectedFormat === format.id ? 'text-brand-primary' : 'text-primary-foreground'}`}>
                  {format.name}
                </div>
                <div className="text-caption text-muted-foreground">{format.description}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Format Preview */}
      <Card className="p-6">
        <h3 className="text-heading-4 text-brand-primary mb-4">
          Preview: <span>{formats.find(f => f.id === selectedFormat)?.name}</span> Format
        </h3>
        {renderPreview()}
      </Card>

      {/* Export Options */}
      <Card className="p-4">
        <h4 className="text-heading-5 mb-3">Export Options</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={exportOptions.includeHierarchies}
              onChange={(e) => handleOptionChange('includeHierarchies', e.target.checked)}
              className="rounded border-border text-brand-primary focus:ring-brand-primary"
            />
            <span className="text-label">Include category hierarchies</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={exportOptions.includeConfidenceScores}
              onChange={(e) => handleOptionChange('includeConfidenceScores', e.target.checked)}
              className="rounded border-border text-brand-primary focus:ring-brand-primary"
            />
            <span className="text-label">Add confidence scores</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={exportOptions.includeOriginalDescriptions}
              onChange={(e) => handleOptionChange('includeOriginalDescriptions', e.target.checked)}
              className="rounded border-border text-brand-primary focus:ring-brand-primary"
            />
            <span className="text-label">Include original descriptions</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={exportOptions.includeTransactionNotes}
              onChange={(e) => handleOptionChange('includeTransactionNotes', e.target.checked)}
              className="rounded border-border text-brand-primary focus:ring-brand-primary"
            />
            <span className="text-label">Add transaction notes</span>
          </label>
        </div>
      </Card>

      {/* Export Stats */}
      <div className="text-center text-caption text-muted-foreground">
        {transactionCount} transactions • {categoryCount} categories • {accuracy}% accuracy
      </div>
    </div>
  );
});

ExportFormatSelector.displayName = 'ExportFormatSelector';

export default ExportFormatSelector;