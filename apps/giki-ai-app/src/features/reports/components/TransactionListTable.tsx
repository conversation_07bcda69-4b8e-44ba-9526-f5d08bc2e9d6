'use client';

import React from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
} from '@/shared/components/ui/table'; // Assuming Shadcn components are in @/shared/components/ui
import { Badge } from '@/shared/components/ui/badge';

// Define a type for our transaction data
interface Transaction {
  id: string;
  date: string;
  description: string;
  category: string;
  amount: number;
  status: 'Pending' | 'Completed' | 'Failed';
}

// Sample transaction data
const transactions: Transaction[] = [
  {
    id: 'txn_1',
    date: '2024-05-15',
    description: 'Office Supplies Purchase',
    category: 'Office Expenses',
    amount: 125.5,
    status: 'Completed',
  },
  {
    id: 'txn_2',
    date: '2024-05-14',
    description: 'Software Subscription Renewal',
    category: 'Software',
    amount: 49.99,
    status: 'Completed',
  },
  {
    id: 'txn_3',
    date: '2024-05-13',
    description: 'Client Dinner',
    category: 'Meals & Entertainment',
    amount: 85.0,
    status: 'Pending',
  },
  {
    id: 'txn_4',
    date: '2024-05-12',
    description: 'Travel Expenses - Conference',
    category: 'Travel',
    amount: 350.75,
    status: 'Completed',
  },
  {
    id: 'txn_5',
    date: '2024-05-11',
    description: 'Online Advertisement Campaign',
    category: 'Marketing',
    amount: 200.0,
    status: 'Failed',
  },
];

const TransactionListTable: React.FC = () => {
  const totalAmount = transactions.reduce((sum, transaction) => {
    return transaction.status === 'Completed' ? sum + transaction.amount : sum;
  }, 0);

  return (
    <div data-testid="transaction-list-table-container">
      <Table>
        <TableCaption className="text-caption">A list of recent transactions.</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[150px] text-table-header">
              Date
            </TableHead>
            <TableHead className="text-table-header">
              Description
            </TableHead>
            <TableHead className="text-table-header">
              Category
            </TableHead>
            <TableHead className="text-table-header">
              Status
            </TableHead>
            <TableHead className="text-right text-table-header">
              Amount
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction) => (
            <TableRow
              key={transaction.id}
              data-testid={`transaction-row-${transaction.id}`}
            >
              <TableCell className="text-table-cell">
                {transaction.date}
              </TableCell>
              <TableCell className="text-table-cell">
                {transaction.description}
              </TableCell>
              <TableCell className="text-table-cell">
                {transaction.category}
              </TableCell>
              <TableCell>
                <Badge
                  variant={
                    transaction.status === 'Completed'
                      ? 'default'
                      : transaction.status === 'Pending'
                        ? 'secondary'
                        : 'destructive'
                  }
                  className="max-w-[150px] truncate text-status-badge"
                >
                  {transaction.status}
                </Badge>
              </TableCell>
              <TableCell className="text-right text-table-cell-mono">
                ${transaction?.amount?.toFixed(2)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={4} className="text-table-header">
              Total (Completed)
            </TableCell>
            <TableCell
              className="text-right text-table-cell-mono font-semibold"
              data-testid="total-amount"
            >
              ${totalAmount.toFixed(2)}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
};

export default TransactionListTable;
