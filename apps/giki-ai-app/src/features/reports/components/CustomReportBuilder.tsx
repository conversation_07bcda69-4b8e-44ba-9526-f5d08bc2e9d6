import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Label } from '@/shared/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
// import { Checkbox } from '@/shared/components/ui/checkbox';
import { DateRangePicker } from '@/shared/components/ui/date-range-picker';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/shared/components/ui/accordion';
import { Badge } from '@/shared/components/ui/badge';
import { toast } from '@/shared/components/ui/use-toast';
import { Input } from '@/shared/components/ui/input';
import { Textarea } from '@/shared/components/ui/textarea';
import {
  // FileText,
  Download,
  Filter,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  // DollarSign,
  // Calendar,
  Plus,
  X,
  AlertCircle,
  Save,
  Play,
} from 'lucide-react';
import { Loading } from '@/shared/components/ui/loading';
import {
  generateCustomReport,
  saveCustomReport,
  getSavedCustomReports,
  validateCustomReportConfig,
  formatCustomReportForExport,
  AVAILABLE_METRICS,
  AVAILABLE_DIMENSIONS,
  type CustomReportConfig,
  type CustomReportMetric,
  type SavedCustomReport,
  type CustomReportData,
} from '../services/customReportService';
import { isAppError } from '@/shared/types/errors';

// Helper function to safely convert values to strings
const safeStringify = (value: unknown): string => {
  if (value === null || value === undefined) return '';
  if (typeof value === 'string') return value;
  if (typeof value === 'number' || typeof value === 'boolean')
    return String(value);
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value);
    } catch {
      return '[Complex Object]';
    }
  }
  return value as string;
};

// Use service types instead of local interfaces

const availableMetrics = AVAILABLE_METRICS;
const availableDimensions = AVAILABLE_DIMENSIONS;
const allAvailableFields = [...availableMetrics, ...availableDimensions];

interface CustomReportBuilderProps {
  onGenerate?: (reportData: CustomReportData) => void;
  onConfigGenerate?: (config: CustomReportConfig) => void;
}

export const CustomReportBuilder = React.memo<CustomReportBuilderProps>(({
  onGenerate,
  onConfigGenerate,
}) => {
  const [reportName, setReportName] = useState('');
  const [reportDescription, setReportDescription] = useState('');
  const [selectedMetrics, setSelectedMetrics] = useState<CustomReportMetric[]>(
    [],
  );
  const [selectedDimensions, setSelectedDimensions] = useState<
    CustomReportMetric[]
  >([]);
  const [filters, setFilters] = useState<
    Array<{
      field: CustomReportMetric;
      operator: string;
      value: unknown;
    }>
  >([]);
  const [chartType, setChartType] = useState<'bar' | 'pie' | 'line' | 'table'>(
    'bar',
  );
  const [groupBy, setGroupBy] = useState<
    'day' | 'week' | 'month' | 'quarter' | 'year' | undefined
  >();
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [reportData, setReportData] = useState<CustomReportData | null>(null);
  const [savedReports, setSavedReports] = useState<SavedCustomReport[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Load saved reports on component mount
  useEffect(() => {
    const loadSavedReports = () => {
      const saved = getSavedCustomReports();
      setSavedReports(saved);
    };
    loadSavedReports();
  }, []);

  // Real-time validation
  useEffect(() => {
    if (
      reportName ||
      selectedMetrics.length > 0 ||
      selectedDimensions.length > 0
    ) {
      const config: CustomReportConfig = {
        name: reportName,
        description: reportDescription,
        metrics: selectedMetrics,
        dimensions: selectedDimensions,
        filters: [],
        chartType,
        dateRange:
          dateRange.from && dateRange.to
            ? { from: dateRange.from, to: dateRange.to }
            : undefined,
        groupBy,
      };
      const validation = validateCustomReportConfig(config);
      setValidationErrors(validation.errors);
    }
  }, [
    reportName,
    reportDescription,
    selectedMetrics,
    selectedDimensions,
    chartType,
    dateRange,
    groupBy,
  ]);

  const handleAddMetric = (fieldId: string) => {
    const field = availableMetrics.find((f) => f.id === fieldId);
    if (field && !selectedMetrics.some((m) => m.id === fieldId)) {
      setSelectedMetrics([...selectedMetrics, field]);
    }
  };

  const handleRemoveMetric = (fieldId: string) => {
    setSelectedMetrics(selectedMetrics.filter((m) => m.id !== fieldId));
  };

  const handleAddDimension = (fieldId: string) => {
    const field = availableDimensions.find((f) => f.id === fieldId);
    if (field && !selectedDimensions.some((d) => d.id === fieldId)) {
      setSelectedDimensions([...selectedDimensions, field]);
    }
  };

  const handleRemoveDimension = (fieldId: string) => {
    setSelectedDimensions(selectedDimensions.filter((d) => d.id !== fieldId));
  };

  const handleAddFilter = () => {
    const defaultField = allAvailableFields[0];
    if (defaultField) {
      setFilters([
        ...filters,
        {
          field: defaultField,
          operator: 'equals',
          value: '',
        },
      ]);
    }
  };

  const handleRemoveFilter = (index: number) => {
    setFilters(filters.filter((_, i) => i !== index));
  };

  const handleUpdateFilter = (
    index: number,
    updates: Partial<(typeof filters)[0]>,
  ) => {
    const newFilters = [...filters];
    newFilters[index] = { ...newFilters[index], ...updates };
    setFilters(newFilters);
  };

  const handleGenerateReport = async () => {
    if (validationErrors.length > 0) {
      toast({
        title: 'Validation Error',
        description: validationErrors[0],
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);

    try {
      const config: CustomReportConfig = {
        name: reportName,
        description: reportDescription,
        metrics: selectedMetrics,
        dimensions: selectedDimensions,
        filters: [],
        chartType,
        dateRange:
          dateRange.from && dateRange.to
            ? { from: dateRange.from, to: dateRange.to }
            : undefined,
        groupBy,
      };

      const result = await generateCustomReport(config);

      if (isAppError(result)) {
        toast({
          title: 'Error',
          description: result.message || 'Failed to generate report',
          variant: 'destructive',
        });
        return;
      }

      // TypeScript now knows result is CustomReportData
      const reportData = result as CustomReportData;
      setReportData(reportData);
      onGenerate?.(reportData);
      onConfigGenerate?.(config);

      toast({
        title: 'Report Generated',
        description: `Your custom report "${reportName}" has been generated successfully.`,
      });
    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate report. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveReport = async () => {
    if (validationErrors.length > 0) {
      toast({
        title: 'Validation Error',
        description: validationErrors[0],
        variant: 'destructive',
      });
      return;
    }

    try {
      const config: CustomReportConfig = {
        name: reportName,
        description: reportDescription,
        metrics: selectedMetrics,
        dimensions: selectedDimensions,
        filters: [],
        chartType,
        dateRange:
          dateRange.from && dateRange.to
            ? { from: dateRange.from, to: dateRange.to }
            : undefined,
        groupBy,
      };

      const savedReport = await saveCustomReport(config);
      setSavedReports((prev) => [...prev, savedReport]);

      toast({
        title: 'Report Saved',
        description: `Your report configuration "${reportName}" has been saved.`,
      });
    } catch (error) {
      console.error('Error saving report:', error);
      toast({
        title: 'Error',
        description: 'Failed to save report. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const loadSavedReport = (report: SavedCustomReport) => {
    setReportName(report.name);
    setReportDescription(report.description);
    setSelectedMetrics(report.metrics);
    setSelectedDimensions(report.dimensions);
    setFilters([]);
    setChartType(report.chartType);
    setDateRange(report.dateRange || {});
    setGroupBy(report.groupBy);
  };

  const handleExportReport = () => {
    if (!reportData) {
      toast({
        title: 'Error',
        description: 'Please generate a report first before exporting.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const csvData = formatCustomReportForExport(reportData, 'csv');
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${reportName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${new Date().toISOString().split('T')[0]}.csv`;
      document?.body?.appendChild(link);
      link.click();
      document?.body?.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: 'Export Successful',
        description: 'Report has been exported to CSV.',
      });
    } catch (error) {
      console.error('Error exporting report:', error);
      toast({
        title: 'Export Error',
        description: 'Failed to export report. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-heading-3">Custom Report Builder</CardTitle>
          <CardDescription className="text-body-small text-muted-foreground">
            Create custom reports by selecting metrics, dimensions, and filters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 overflow-hidden">
          {/* Report Details */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="report-name">Report Name</Label>
              <Input
                id="report-name"
                value={reportName}
                onChange={(e) => setReportName(e?.target?.value)}
                placeholder="e.g., Monthly Spending Analysis"
              />
            </div>
            <div>
              <Label htmlFor="report-description">Description (Optional)</Label>
              <Textarea
                id="report-description"
                value={reportDescription}
                onChange={(e) => setReportDescription(e?.target?.value)}
                placeholder="Describe what this report shows..."
                rows={2}
              />
            </div>
          </div>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="p-3 border border-red-200 bg-red-50 rounded-md">
              <div className="flex flex-wrap items-center gap-2">
                <AlertCircle className="h-4 w-4 text-destructive" />
                <span className="text-label text-destructive">
                  Validation Issues:
                </span>
              </div>
              <ul className="mt-1 text-caption text-destructive list-disc list-inside">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Saved Reports */}
          {savedReports.length > 0 && (
            <div>
              <Label className="text-label">Load Saved Report</Label>
              <Select
                onValueChange={(value) => {
                  const report = savedReports.find((r) => r.id === value);
                  if (report) loadSavedReport(report);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a saved report" />
                </SelectTrigger>
                <SelectContent>
                  {savedReports.map((report) => (
                    <SelectItem key={report.id} value={report.id}>
                      {report.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <Accordion type="single" collapsible className="w-full">
            {/* Metrics Section */}
            <AccordionItem value="metrics">
              <AccordionTrigger>
                <div className="flex flex-wrap items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  <span className="text-heading-5">Metrics ({selectedMetrics.length})</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div>
                  <Label className="text-label">Available Metrics</Label>
                  <Select onValueChange={handleAddMetric}>
                    <SelectTrigger>
                      <SelectValue placeholder="Add a metric" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableMetrics.map((metric) => (
                        <SelectItem key={metric.id} value={metric.id}>
                          {metric.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  {selectedMetrics.map((metric) => (
                    <div
                      key={metric.id}
                      className="flex flex-wrap items-center justify-between p-2 border rounded"
                    >
                      <div className="flex flex-wrap items-center gap-2">
                        <Badge className="max-w-[150px] truncate">
                          {metric.aggregation}
                        </Badge>
                        <span className="text-body">{metric.name}</span>
                      </div>
                      <Button
                        className="max-w-full"
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveMetric(metric.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Dimensions Section */}
            <AccordionItem value="dimensions">
              <AccordionTrigger>
                <div className="flex flex-wrap items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span className="text-heading-5">Dimensions ({selectedDimensions.length})</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div>
                  <Label className="text-label">Available Dimensions</Label>
                  <Select onValueChange={handleAddDimension}>
                    <SelectTrigger>
                      <SelectValue placeholder="Add a dimension" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableDimensions.map((dimension) => (
                        <SelectItem key={dimension.id} value={dimension.id}>
                          {dimension.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  {selectedDimensions.map((dimension) => (
                    <div
                      key={dimension.id}
                      className="flex flex-wrap items-center justify-between p-2 border rounded"
                    >
                      <span className="text-body">{dimension.name}</span>
                      <Button
                        className="max-w-full"
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveDimension(dimension.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Filters Section */}
            <AccordionItem value="filters">
              <AccordionTrigger>
                <div className="flex flex-wrap items-center gap-2">
                  <Filter className="h-4 w-4" />
                  <span className="text-heading-5">Filters ({filters.length})</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <Button
                  className="max-w-full"
                  onClick={() => void handleAddFilter()}
                  size="sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Filter
                </Button>
                <div className="space-y-2">
                  {filters.map((filter, index) => (
                    <div
                      key={index}
                      className="flex flex-wrap items-center gap-2 p-2 border rounded"
                    >
                      <Select
                        value={filter?.field?.id}
                        onValueChange={(value) => {
                          const field = allAvailableFields.find(
                            (f) => f.id === value,
                          );
                          if (field) handleUpdateFilter(index, { field });
                        }}
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {allAvailableFields.map((field) => (
                            <SelectItem key={field.id} value={field.id}>
                              {field.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Select
                        value={filter.operator}
                        onValueChange={(value) =>
                          handleUpdateFilter(index, { operator: value })
                        }
                      >
                        <SelectTrigger className="w-[100px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="equals">Equals</SelectItem>
                          <SelectItem value="contains">Contains</SelectItem>
                          <SelectItem value="gt">Greater Than</SelectItem>
                          <SelectItem value="lt">Less Than</SelectItem>
                          <SelectItem value="between">Between</SelectItem>
                          <SelectItem value="in">In List</SelectItem>
                        </SelectContent>
                      </Select>
                      <Input
                        value={safeStringify(filter.value)}
                        onChange={(e) =>
                          handleUpdateFilter(index, { value: e?.target?.value })
                        }
                        placeholder="Value"
                        className="flex flex-wrap-1"
                      />
                      <Button
                        className="max-w-full"
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveFilter(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Display Options */}
            <AccordionItem value="display">
              <AccordionTrigger>
                <div className="flex flex-wrap items-center gap-2">
                  <PieChart className="h-4 w-4" />
                  <span className="text-heading-5">Display Options</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-label">Chart Type</Label>
                    <Select
                      value={chartType}
                      onValueChange={(value) =>
                        setChartType(value as 'table' | 'line' | 'pie' | 'bar')
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bar">Bar Chart</SelectItem>
                        <SelectItem value="pie">Pie Chart</SelectItem>
                        <SelectItem value="line">Line Chart</SelectItem>
                        <SelectItem value="table">Table</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-label">Group By</Label>
                    <Select
                      value={groupBy || ''}
                      onValueChange={(value) =>
                        setGroupBy(
                          (value as
                            | 'day'
                            | 'week'
                            | 'month'
                            | 'quarter'
                            | 'year') || undefined,
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select grouping" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">None</SelectItem>
                        <SelectItem value="day">Day</SelectItem>
                        <SelectItem value="week">Week</SelectItem>
                        <SelectItem value="month">Month</SelectItem>
                        <SelectItem value="quarter">Quarter</SelectItem>
                        <SelectItem value="year">Year</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label className="text-label">Date Range</Label>
                  <DateRangePicker
                    onChange={(range) => setDateRange(range || {})}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
        <CardFooter className="flex flex-wrap justify-between">
          <div className="flex flex-wrap gap-2">
            <Button
              className="max-w-full"
              onClick={() => void handleGenerateReport()}
              disabled={isGenerating || validationErrors.length > 0}
            >
              {isGenerating ? (
                <Loading className="h-4 w-4 mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              <span className="text-button-base">{isGenerating ? 'Generating...' : 'Generate Report'}</span>
            </Button>
            <Button
              className="max-w-full"
              variant="outline"
              onClick={() => void handleSaveReport()}
              disabled={validationErrors.length > 0}
            >
              <Save className="h-4 w-4 mr-2" />
              <span className="text-button-base">Save Configuration</span>
            </Button>
            {reportData && (
              <Button
                className="max-w-full"
                variant="outline"
                onClick={() => void handleExportReport()}
              >
                <Download className="h-4 w-4 mr-2" />
                <span className="text-button-base">Export CSV</span>
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
});

CustomReportBuilder.displayName = 'CustomReportBuilder';

export default CustomReportBuilder;
