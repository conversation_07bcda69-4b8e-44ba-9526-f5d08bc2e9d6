import React from 'react';
import { ArrowRight, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Badge,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  <PERSON>ltipTrigger,
  Alert,
  AlertDescription,
} from '@/shared/components/ui';
import ExcelTable, {
  ExcelTableColumn,
} from '@/shared/components/ui/excel-table';

export interface ColumnMapping {
  original_name: string;
  mapped_field: string | null;
  confidence?: number;
  method?: 'ai' | 'manual' | 'auto';
  data_type?: string;
  sample_value?: string;
}

// Helper function to safely convert values to strings
const safeStringify = (value: unknown): string => {
  if (value === null || value === undefined) return 'N/A';
  if (typeof value === 'string') return value;
  if (typeof value === 'number' || typeof value === 'boolean')
    return String(value);
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value);
    } catch {
      return '[Complex Object]';
    }
  }
  // For any other type, convert to string
  return value as string;
};

interface ColumnMappingTableProps {
  mappings: ColumnMapping[];
  title?: string;
  description?: string;
  showConfidence?: boolean;
  showSamples?: boolean;
  variant?: 'table' | 'cards' | 'simple';
  className?: string;
}

export function ColumnMappingTable({
  mappings,
  title = 'Column Mappings',
  description = 'How columns from your file were mapped to system fields',
  showConfidence = true,
  showSamples = false,
  variant = 'table',
  className = '',
}: ColumnMappingTableProps) {
  const getMappingIcon = (mapped: boolean, confidence?: number) => {
    if (!mapped) {
      return <XCircle className="h-4 w-4 truncatered-500" />;
    }
    if (confidence && confidence >= 0.8) {
      return <CheckCircle className="h-4 w-4 truncategreen-500" />;
    }
    return <AlertCircle className="h-4 w-4 truncateyellow-500" />;
  };

  const getMethodBadge = (method?: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'outline'> = {
      ai: 'default',
      manual: 'secondary',
      auto: 'outline',
    };

    if (!method) return null;

    return (
      <Badge
        variant={variants[method] || 'outline'}
        className="truncate text-caption max-w-[150px] truncate"
      >
        {method.toUpperCase()}
      </Badge>
    );
  };

  const getConfidenceBadge = (confidence?: number) => {
    if (!confidence) return null;

    const percentage = (confidence * 100).toFixed(0);
    let variant: 'default' | 'secondary' | 'destructive' = 'secondary';

    if (confidence >= 0.8) variant = 'default';
    else if (confidence < 0.5) variant = 'destructive';

    return (
      <Badge
        variant={variant}
        className="truncate text-caption max-w-[150px] truncate"
      >
        {percentage}%
      </Badge>
    );
  };

  // Table variant using ExcelTable
  if (variant === 'table') {
    const tableData = mappings.map((mapping) => ({
      'Original Column': mapping.original_name,
      'Mapped To': mapping.mapped_field || 'Unmapped',
      Type: mapping.data_type || 'Unknown',
      ...(showConfidence ? { Confidence: mapping.confidence || 0 } : {}),
      Method: mapping.method || 'Unknown',
      ...(showSamples ? { Sample: mapping.sample_value || '' } : {}),
      _isMapped: !!mapping.mapped_field,
      _confidence: mapping.confidence,
    }));

    const columns: ExcelTableColumn[] = [
      {
        key: 'Original Column',
        label: 'Original Column',
        type: 'text' as const,
      },
      {
        key: 'Mapped To',
        label: 'Mapped To',
        type: 'text' as const,
        formatValue: (value: unknown) => safeStringify(value),
      },
      {
        key: 'Type',
        label: 'Data Type',
        type: 'text' as const,
      },
    ];

    if (showConfidence) {
      columns.push({
        key: 'Confidence',
        label: 'Confidence',
        type: 'percentage' as const,
        formatValue: (value: unknown) => getConfidenceBadge(value as number),
      });
    }

    columns.push({
      key: 'Method',
      label: 'Method',
      type: 'text' as const,
      formatValue: (value: unknown) => getMethodBadge(value as string),
    });

    if (showSamples) {
      columns.push({
        key: 'Sample',
        label: 'Sample Value',
        type: 'text' as const,
        formatValue: (value: unknown) => (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="truncate text-caption text-muted-foreground truncate max-w-[150px] block">
                  {safeStringify(value)}
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">
                  {value ? safeStringify(value) : 'No sample available'}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ),
      });
    }

    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <ExcelTable data={tableData} columns={columns} enableFiltering />
        </CardContent>
      </Card>
    );
  }

  // Cards variant
  if (variant === 'cards') {
    const mappedColumns = mappings.filter((m) => m.mapped_field);
    const unmappedColumns = mappings.filter((m) => !m.mapped_field);

    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-label mb-3">
                Mapped Columns ({mappedColumns.length})
              </h4>
              <div className="space-y-2">
                {mappedColumns.map((mapping, idx) => (
                  <div key={idx} className="p-3 bg-muted rounded-lg space-y-2">
                    <div className="flex flex-wrap items-center justify-between">
                      <div className="flex flex-wrap items-center gap-2">
                        <CheckCircle className="h-4 w-4 truncategreen-500" />
                        <span className="text-label">
                          {mapping.original_name}
                        </span>
                      </div>
                      {getMethodBadge(mapping.method)}
                    </div>
                    <div className="flex flex-wrap items-center gap-2 text-body-small">
                      <ArrowRight className="h-3 w-3" />
                      <span>{mapping.mapped_field}</span>
                      {showConfidence && getConfidenceBadge(mapping.confidence)}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-label mb-3">
                Unmapped Columns ({unmappedColumns.length})
              </h4>
              <div className="space-y-2">
                {unmappedColumns.map((mapping, idx) => (
                  <div key={idx} className="p-3 bg-muted rounded-lg">
                    <div className="flex flex-wrap items-center gap-2">
                      <XCircle className="h-4 w-4 truncatered-500" />
                      <span className="text-sm">{mapping.original_name}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {unmappedColumns.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {unmappedColumns.length} column
                {unmappedColumns.length > 1 ? 's' : ''} could not be
                automatically mapped. These columns will be ignored during
                processing.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    );
  }

  // Simple variant (like in FileProcessingReportPage)
  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <h3 className="text-label mb-2">{title}</h3>
        <p className="truncate text-caption text-muted-foreground mb-4">
          {description}
        </p>
      </div>

      <div className="space-y-1">
        {mappings.map((mapping, idx) => (
          <div
            key={idx}
            className="flex flex-wrap items-center gap-3 p-2 bg-muted rounded text-sm"
          >
            {getMappingIcon(!!mapping.mapped_field, mapping.confidence)}
            <span className="flex flex-wrap-1">{mapping.original_name}</span>
            {mapping.mapped_field ? (
              <>
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
                <span className="flex flex-wrap-1">{mapping.mapped_field}</span>
                {showConfidence && getConfidenceBadge(mapping.confidence)}
              </>
            ) : (
              <span className="text-muted-foreground">Not mapped</span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

export default ColumnMappingTable;
