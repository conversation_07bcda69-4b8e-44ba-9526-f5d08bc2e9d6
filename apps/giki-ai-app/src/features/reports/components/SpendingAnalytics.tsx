import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import {
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  ComposedChart,
} from 'recharts';
import { TrendingUp, TrendingDown, Activity, DollarSign } from 'lucide-react';
import { formatCurrency } from '@/features/reports/services/reportService';

interface MonthlyTrendData {
  month: string;
  income: number;
  expense: number;
  net: number;
}

interface SpendingAnalyticsProps {
  monthlyData: MonthlyTrendData[];
  currency?: string;
}

export const SpendingAnalytics = React.memo<SpendingAnalyticsProps>(({
  monthlyData,
  currency = 'INR',
}) => {
  // Calculate metrics
  const totalIncome = monthlyData.reduce((sum, item) => sum + item.income, 0);
  const totalExpense = monthlyData.reduce((sum, item) => sum + item.expense, 0);
  const avgMonthlyIncome =
    monthlyData.length > 0 ? totalIncome / monthlyData.length : 0;
  const avgMonthlyExpense =
    monthlyData.length > 0 ? totalExpense / monthlyData.length : 0;

  // Calculate growth trends
  const latestMonth = monthlyData[monthlyData.length - 1];
  const previousMonth = monthlyData[monthlyData.length - 2];
  const incomeGrowth =
    previousMonth && latestMonth
      ? ((latestMonth.income - previousMonth.income) / previousMonth.income) *
        100
      : 0;
  const expenseGrowth =
    previousMonth && latestMonth
      ? ((latestMonth.expense - previousMonth.expense) /
          previousMonth.expense) *
        100
      : 0;

  // Custom tooltip for charts
  interface TooltipPayload {
    name: string;
    value: number;
    color: string;
  }

  interface CustomTooltipProps {
    active?: boolean;
    payload?: TooltipPayload[];
    label?: string;
  }

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="text-label mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {formatCurrency(entry.value, currency)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="border border-gray-200 shadow-sm overflow-hidden bg-white">
          <div className="bg-brand-primary text-white px-6 py-4">
            <h3 className="text-sm font-semibold flex flex-wrap items-center justify-between">
              <span>Avg Monthly Income</span>
              <DollarSign className="h-4 w-4" />
            </h3>
          </div>
          <div className="p-6 overflow-hidden">
            <div className="text-2xl font-bold text-success">
              {formatCurrency(avgMonthlyIncome, currency)}
            </div>
            {incomeGrowth !== 0 && (
              <p className="truncate text-caption text-[hsl(var(--giki-text-muted))] mt-1 flex flex-wrap items-center">
                {incomeGrowth > 0 ? (
                  <TrendingUp className="h-3 w-3 text-success mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-destructive mr-1" />
                )}
                {Math.abs(incomeGrowth).toFixed(1)}% vs last month
              </p>
            )}
          </div>
        </div>

        <div className="border border-gray-200 shadow-sm overflow-hidden bg-white">
          <div className="bg-brand-primary text-white px-6 py-4">
            <h3 className="text-sm font-semibold flex flex-wrap items-center justify-between">
              <span>Avg Monthly Expense</span>
              <Activity className="h-4 w-4" />
            </h3>
          </div>
          <div className="p-6 overflow-hidden">
            <div className="text-2xl font-bold text-destructive">
              {formatCurrency(avgMonthlyExpense, currency)}
            </div>
            {expenseGrowth !== 0 && (
              <p className="truncate text-caption text-[hsl(var(--giki-text-muted))] mt-1 flex flex-wrap items-center">
                {expenseGrowth > 0 ? (
                  <TrendingUp className="h-3 w-3 text-destructive mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-success mr-1" />
                )}
                {Math.abs(expenseGrowth).toFixed(1)}% vs last month
              </p>
            )}
          </div>
        </div>

        <div className="border border-gray-200 shadow-sm overflow-hidden bg-white">
          <div className="bg-brand-primary text-white px-6 py-4">
            <h3 className="text-sm font-semibold">Best Month</h3>
          </div>
          <div className="p-6 overflow-hidden">
            <div className="text-lg font-bold">
              {
                monthlyData.reduce(
                  (best, current) => (current.net > best.net ? current : best),
                  monthlyData[0] || { month: 'N/A', net: 0 },
                ).month
              }
            </div>
            <p className="truncate text-caption text-[hsl(var(--giki-text-muted))]">
              Highest net income
            </p>
          </div>
        </div>

        <div className="border border-gray-200 shadow-sm overflow-hidden bg-white">
          <div className="bg-brand-primary text-white px-6 py-4">
            <h3 className="text-sm font-semibold">Savings Rate</h3>
          </div>
          <div className="p-6 overflow-hidden">
            <div className="text-lg font-bold">
              {totalIncome > 0
                ? `${(((totalIncome - totalExpense) / totalIncome) * 100).toFixed(1)}%`
                : '0%'}
            </div>
            <p className="truncate text-caption text-[hsl(var(--giki-text-muted))]">
              Of total income
            </p>
          </div>
        </div>
      </div>

      {/* Cash Flow Chart */}
      <div className="border border-gray-200 shadow-sm overflow-hidden bg-white">
        <div className="bg-brand-primary text-white px-6 py-4">
          <h3 className="text-xl font-semibold">Cash Flow Analysis</h3>
          <p className="text-white/80 text-sm">
            Monthly income, expenses, and net cash flow
          </p>
        </div>
        <div className="p-6 overflow-hidden">
          <div style={{ width: '100%', height: 400 }}>
            <ResponsiveContainer>
              <ComposedChart data={monthlyData}>
                <defs>
                  <linearGradient
                    id="incomeGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="5%"
                      stopColor="hsl(var(--giki-success))"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="hsl(var(--giki-success))"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                  <linearGradient
                    id="expenseGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="5%"
                      stopColor="hsl(var(--giki-destructive))"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="hsl(var(--giki-destructive))"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                />
                <XAxis
                  dataKey="month"
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                />
                <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="income"
                  stroke="hsl(var(--giki-success))"
                  fill="url(#incomeGradient)"
                  strokeWidth={2}
                  name="Income"
                />
                <Area
                  type="monotone"
                  dataKey="expense"
                  stroke="hsl(var(--giki-destructive))"
                  fill="url(#expenseGradient)"
                  strokeWidth={2}
                  name="Expenses"
                />
                <Line
                  type="monotone"
                  dataKey="net"
                  stroke="hsl(var(--giki-brand-blue))"
                  strokeWidth={3}
                  dot={{ fill: 'hsl(var(--giki-brand-blue))', r: 4 }}
                  name="Net Cash Flow"
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Expense Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Expense Trend Analysis</CardTitle>
          <CardDescription>
            Track your spending patterns over time
          </CardDescription>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div style={{ width: '100%', height: 300 }}>
            <ResponsiveContainer>
              <BarChart data={monthlyData}>
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                />
                <XAxis
                  dataKey="month"
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                />
                <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Bar
                  dataKey="expense"
                  fill="hsl(var(--giki-brand-purple))"
                  radius={[8, 8, 0, 0]}
                  name="Monthly Expenses"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Income Growth Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Income Growth</CardTitle>
            <CardDescription>Monthly income progression</CardDescription>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div style={{ width: '100%', height: 250 }}>
              <ResponsiveContainer>
                <LineChart data={monthlyData}>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="hsl(var(--border))"
                  />
                  <XAxis
                    dataKey="month"
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="income"
                    stroke="hsl(var(--giki-success))"
                    strokeWidth={3}
                    dot={{ fill: 'hsl(var(--giki-success))', r: 4 }}
                    name="Income"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Savings Trend</CardTitle>
            <CardDescription>Net amount saved each month</CardDescription>
          </CardHeader>
          <CardContent className="overflow-hidden">
            <div style={{ width: '100%', height: 250 }}>
              <ResponsiveContainer>
                <BarChart data={monthlyData}>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="hsl(var(--border))"
                  />
                  <XAxis
                    dataKey="month"
                    stroke="hsl(var(--muted-foreground))"
                    fontSize={12}
                  />
                  <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar
                    dataKey="net"
                    fill="hsl(var(--giki-primary))"
                    radius={[8, 8, 0, 0]}
                    name="Net Savings"
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
});

SpendingAnalytics.displayName = 'SpendingAnalytics';

export default SpendingAnalytics;
