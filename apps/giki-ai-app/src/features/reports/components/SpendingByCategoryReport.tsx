import React, { useEffect, useState } from 'react';
import { apiClient } from '@/shared/services/api/apiClient';
import type { SpendingByCategoryItem } from '@/shared/types/api';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { formatCurrency } from '@/shared/utils/formatCurrency';

interface SpendingByCategoryReportProps {
  dateRange: { from: Date | undefined; to: Date | undefined };
}

const SpendingByCategoryReport: React.FC<SpendingByCategoryReportProps> = ({
  dateRange,
}) => {
  const [data, setData] = useState<SpendingByCategoryItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // TODO: Pass dateRange to API call when backend supports it
       (
          'Fetching spending by category with date range:',
          dateRange,
        );
        const response = await apiClient.get<SpendingByCategoryItem[]>(
          '/reports/spending-by-category',
        );
        const reportData = response.data;
        setData(reportData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch report');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    void fetchData();
  }, [dateRange]);

  if (loading)
    return <div className="text-center p-4">Loading report data...</div>;
  // Error styling will be handled by a potential Alert component from @giki-ai/ui or global styles
  if (error)
    return (
      <Alert variant="destructive" className="m-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error Fetching Report</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  if (!data || data.length === 0)
    return (
      <div className="text-center p-4">No data available for this report.</div>
    );

  const totalSpending = data.reduce((sum, item) => sum + item.spending, 0);

  return (
    // The outer div can remain for now, or be replaced by a Card if this component is meant to be self-contained visually.
    // For now, focusing on removing explicit color/dark mode classes.
    <div>
      {/* Professional design system: consistent heading with brand color */}
      <h3 className="truncate text-heading-5 mb-4 text-brand-primary">
        Spending by Category
      </h3>
      {/* Professional design system: consistent card styling */}
      <div className="mb-6 p-4 rounded border border-gray-200 shadow-sm">
        {' '}
        {/* Professional card styling with shadow */}
        [Bar Chart Placeholder - Total: {formatCurrency(totalSpending)}]
        {/* Placeholder for chart library integration */}
      </div>
      <div className="overflow-x-auto">
        {/* Consider replacing with @giki-ai/ui Table component in a future segment if available and appropriate */}
        {/* min-w-full divide-y divide-gray-200 dark:divide-gray-700 - divide classes removed, assuming table component handles borders */}
        <div className="overflow-x-auto">
          <table className="min-w-full">
            {/* bg-gray-50 dark:bg-gray-600 removed */}
            <thead className="border-b border-border">
              {' '}
              {/* Ensuring consistent border color */}
              <tr>
                {/* px-6 py-3 text-left text-xs font-medium uppercase tracking-wider are structural/typographic */}
                {/* text-gray-500 dark:text-gray-300 removed */}
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-caption font-medium text-[hsl(var(--giki-text-muted))] uppercase tracking-wider"
                >
                  Category Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-caption font-medium text-[hsl(var(--giki-text-muted))] uppercase tracking-wider"
                >
                  Total Amount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-caption font-medium text-[hsl(var(--giki-text-muted))] uppercase tracking-wider"
                >
                  Percentage of Total
                </th>
              </tr>
            </thead>
            {/* bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 removed */}
            <tbody className="divide-y divide-border">
              {' '}
              {/* Assuming themed divide for tbody */}
              {data.map((item) => (
                <tr key={item.name}>
                  {/* px-6 py-4 whitespace-nowrap text-sm font-medium are structural/typographic */}
                  {/* text-gray-900 dark:text-white removed */}
                  <td className="px-6 py-4 whitespace-nowrap text-label text-foreground max-w-0 overflow-hidden">
                    <div className="truncate">{item.name}</div>
                  </td>
                  {/* px-6 py-4 whitespace-nowrap text-sm are structural/typographic */}
                  {/* text-gray-500 dark:text-gray-300 removed */}
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[hsl(var(--giki-text-muted))] max-w-0 overflow-hidden">
                    <div className="truncate">
                      {formatCurrency(item?.spending)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[hsl(var(--giki-text-muted))] max-w-0 overflow-hidden">
                    <div className="truncate">
                      {(() => {
                        const spending = item?.spending || 0;
                        if (
                          totalSpending > 0 &&
                          !isNaN(spending) &&
                          !isNaN(totalSpending)
                        ) {
                          const percentage = (spending / totalSpending) * 100;
                          return isNaN(percentage)
                            ? '0.00'
                            : percentage.toFixed(2);
                        }
                        return '0.00';
                      })()}
                      %
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SpendingByCategoryReport;
