/**
 * Export Validation System - M3 Compliance Component
 *
 * Professional export validation system with compliance checking,
 * format validation, and GL code integrity verification.
 */

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { toast } from '../../../shared/components/ui/use-toast';
import { Button } from '../../../shared/components/ui/button';
import { 
  ExportApiService 
} from '../services/exportApiService';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../../shared/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../../shared/components/ui/tabs';
import { Badge } from '../../../shared/components/ui/badge';
import { Progress } from '../../../shared/components/ui/progress';
import { Input } from '../../../shared/components/ui/input';
import { Label } from '../../../shared/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../shared/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../shared/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../shared/components/ui/dialog';
// Removed Lucide imports - replaced with geometric icons
// Download → ↓, CheckCircle → ✓, XCircle → ✗, AlertTriangle → ⚠
// Target → ◯, Shield → △, Eye → ◎, FileCheck → ✓


// Types
interface ExportValidationRule {
  id: string;
  name: string;
  description: string;
  type: 'format' | 'data_integrity' | 'gl_compliance' | 'business_rules';
  severity: 'error' | 'warning' | 'info';
  enabled: boolean;
  parameters: Record<string, unknown>;
}

interface ValidationResult {
  ruleId: string;
  ruleName: string;
  status: 'passed' | 'failed' | 'warning';
  message: string;
  details?: string;
  affectedRecords: number;
  severity: 'error' | 'warning' | 'info';
  suggestions: string[];
}

interface ExportFormat {
  id: string;
  name: string;
  description: string;
  fileExtension: string;
  supportsGLCodes: boolean;
  supportsHierarchy: boolean;
  compliance: string[];
  validationRules: string[];
}

interface ExportConfiguration {
  format: string;
  includeGLCodes: boolean;
  includeHierarchy: boolean;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  filters: {
    categories: string[];
    accuracyThreshold: number;
    includeUnverified: boolean;
  };
  compliance: {
    requireGLCodes: boolean;
    enforceHierarchy: boolean;
    validateAmounts: boolean;
    checkDuplicates: boolean;
  };
}

interface ComplianceReport {
  overall_score: number;
  total_checks: number;
  passed_checks: number;
  warnings: number;
  errors: number;
  compliance_areas: {
    gl_codes: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
    data_integrity: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
    format_compliance: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
  };
}

// Component Props
interface ExportValidationSystemProps {
  className?: string;
  onExportComplete?: (result: unknown) => void;
  _showAdvancedValidation?: boolean;
  complianceMode?: boolean;
}

// Validation rules are generated dynamically based on export format requirements

// Export formats are loaded dynamically from the backend API

export const ExportValidationSystem = React.memo<ExportValidationSystemProps>(({
  className = '',
  onExportComplete,
  _showAdvancedValidation = true,
  complianceMode = false,
}) => {
  // State
  const [validationRules, setValidationRules] = useState<
    ExportValidationRule[]
  >([]);
  const [validationResults, setValidationResults] = useState<
    ValidationResult[]
  >([]);
  const [complianceReport, setComplianceReport] =
    useState<ComplianceReport | null>(null);
  const [_loading, _setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [showValidationDialog, setShowValidationDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('configuration');
  const [exportFormats, setExportFormats] = useState<ExportFormat[]>([]);
  const [loadingFormats, setLoadingFormats] = useState(true);

  // Export Configuration
  const [exportConfig, setExportConfig] = useState<ExportConfiguration>({
    format: 'quickbooks_csv',
    includeGLCodes: true,
    includeHierarchy: true,
    dateRange: {
      startDate: new Date(new Date().getFullYear(), 0, 1)
        .toISOString()
        .split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
    },
    filters: {
      categories: [],
      accuracyThreshold: 85,
      includeUnverified: false,
    },
    compliance: {
      requireGLCodes: true,
      enforceHierarchy: false,
      validateAmounts: true,
      checkDuplicates: true,
    },
  });

  // Load export formats from API on component mount
  useEffect(() => {
    const loadExportFormats = async () => {
      try {
        setLoadingFormats(true);
        const formatInfos = await ExportApiService.getExportFormats();
        const formats = formatInfos.map(ExportApiService.formatInfoToExportFormat);
        setExportFormats(formats);
        
        // Set default format to first available
        if (formats.length > 0 && !exportConfig.format) {
          setExportConfig(prev => ({ ...prev, format: formats[0].id }));
        }
      } catch (error) {
        console.error('Failed to load export formats:', error);
        toast({
          title: 'Failed to Load Export Formats',
          description: 'Cannot load export formats. Please check your connection and try again.',
          variant: 'destructive'
        });
        // No fallback to fake data - keep empty formats array
      } finally {
        setLoadingFormats(false);
      }
    };

    loadExportFormats();
  }, []);

  // Get current format configuration
  const currentFormat = useMemo(() => {
    return (
      exportFormats.find((f) => f.id === exportConfig.format) ||
      exportFormats[0]
    );
  }, [exportConfig.format, exportFormats]);

  // Generate validation rules based on format requirements
  const applicableRules = useMemo(() => {
    if (!currentFormat) return [];
    
    // Generate rules dynamically based on format capabilities
    const generatedRules: ExportValidationRule[] = [];
    
    if (currentFormat.supportsGLCodes) {
      generatedRules.push({
        id: 'gl_code_presence',
        name: 'GL Code Presence',
        description: 'Ensure all transactions have valid GL codes',
        type: 'gl_compliance',
        severity: 'error',
        enabled: true,
        parameters: { requireAll: true },
      });
    }
    
    if (currentFormat.supportsHierarchy) {
      generatedRules.push({
        id: 'hierarchy_integrity',
        name: 'Category Hierarchy Integrity',
        description: 'Validate category hierarchy is complete and consistent',
        type: 'business_rules',
        severity: 'warning',
        enabled: true,
        parameters: { enforceLeafNodes: true },
      });
    }
    
    // Always include basic validation rules
    generatedRules.push({
      id: 'amount_validation',
      name: 'Amount Validation',
      description: 'Validate transaction amounts are positive and formatted correctly',
      type: 'data_integrity',
      severity: 'error',
      enabled: true,
      parameters: { allowZero: false },
    });
    
    return generatedRules;
  }, [currentFormat]);

  // Run validation using real API
  const runValidation = useCallback(async () => {
    if (!currentFormat) return;
    
    try {
      setValidating(true);

      // Call real backend API for export readiness
      const readinessResponse = await ExportApiService.checkExportReadiness(
        currentFormat.id,
        {
          date_from: exportConfig.dateRange.startDate,
          date_to: exportConfig.dateRange.endDate,
          include_uncategorized: !exportConfig.filters.includeUnverified,
        }
      );

      // Convert backend results to component format
      const results = ExportApiService.convertValidationResults(
        readinessResponse.validation_results
      );

      setValidationResults(results);
      setComplianceReport(readinessResponse.compliance_report);
      setShowValidationDialog(true);

      const errorChecks = results.filter((r) => r.status === 'failed').length;
      const passedChecks = results.filter((r) => r.status === 'passed').length;

      toast({
        title: 'Validation Complete',
        description: `${passedChecks}/${results.length} checks passed. ${errorChecks} errors found.`,
        variant: errorChecks > 0 ? 'destructive' : 'default',
      });
    } catch (error) {
      console.error('Validation failed:', error);
      toast({
        title: 'Validation Failed',
        description: 'Failed to validate export data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setValidating(false);
    }
  }, [currentFormat, exportConfig]);

  // Execute export using real API
  const executeExport = useCallback(async () => {
    if (!currentFormat) return;
    
    try {
      setExporting(true);

      // Run validation first if not already done
      if (validationResults.length === 0) {
        await runValidation();
        return; // Let user review validation results first
      }

      // Check for blocking errors
      const errors = validationResults.filter(
        (r) => r.status === 'failed' && r.severity === 'error',
      );
      if (errors.length > 0 && complianceMode) {
        toast({
          title: 'Export Blocked',
          description: `${errors.length} critical errors must be resolved before export.`,
          variant: 'destructive',
        });
        return;
      }

      // Download export file using real API
      const exportBlob = await ExportApiService.downloadExport(
        currentFormat.id,
        {
          date_from: exportConfig.dateRange.startDate,
          date_to: exportConfig.dateRange.endDate,
          include_uncategorized: !exportConfig.filters.includeUnverified,
          export_options: {
            include_gl_codes: exportConfig.includeGLCodes,
            include_hierarchy: exportConfig.includeHierarchy,
            include_metadata: true
          }
        }
      );

      // Generate filename with format and date
      const filename = `export_${currentFormat.id}_${new Date().toISOString().split('T')[0]}.${currentFormat.fileExtension}`;
      
      // Download file
      ExportApiService.triggerFileDownload(exportBlob, filename);

      toast({
        title: 'Export Complete',
        description: `Data exported successfully as ${currentFormat.name}.`,
      });

      onExportComplete?.(exportBlob);
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setExporting(false);
    }
  }, [
    exportConfig,
    validationResults,
    runValidation,
    complianceMode,
    currentFormat,
    onExportComplete,
  ]);

  // Get validation status summary
  const validationSummary = useMemo(() => {
    if (validationResults.length === 0) return null;

    const errors = validationResults.filter(
      (r) => r.status === 'failed',
    ).length;
    const warnings = validationResults.filter(
      (r) => r.status === 'warning',
    ).length;
    const passed = validationResults.filter(
      (r) => r.status === 'passed',
    ).length;

    return { errors, warnings, passed, total: validationResults.length };
  }, [validationResults]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-primary-foreground">
            Export Validation
          </h2>
          <p className="text-secondary-foreground mt-1">
            Professional export system with M3 compliance validation
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={runValidation}
            disabled={validating || exporting}
          >
            <span
              className={`w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center ${validating ? 'animate-spin' : ''}`}
            >
              △
            </span>
            Validate
          </Button>

          <Button
            onClick={executeExport}
            disabled={
              validating ||
              exporting ||
              (complianceMode &&
                validationSummary?.errors &&
                validationSummary.errors > 0)
            }
            style={{ backgroundColor: 'var(--giki-primary)' }}
          >
            <span
              className={`w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center ${exporting ? 'animate-spin' : ''}`}
            >
              ↓
            </span>
            {exporting ? 'Exporting...' : 'Export'}
          </Button>
        </div>
      </div>

      {/* Validation Summary */}
      {validationSummary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground flex items-center">
                <span className="w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center">✓</span>
                Passed
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success">
                {validationSummary.passed}
              </div>
              <p className="text-xs text-muted-foreground">
                of {validationSummary.total} checks
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                <span className="w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center">⚠</span>
                Warnings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-warning">
                {validationSummary.warnings}
              </div>
              <p className="text-xs text-muted-foreground">require attention</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                <span className="w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center">✗</span>
                Errors
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">
                {validationSummary.errors}
              </div>
              <p className="text-xs text-muted-foreground">must be fixed</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                <span className="w-4 h-4 mr-2 text-lg font-bold flex items-center justify-center">◯</span>
                Compliance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold" style={{ color: 'var(--giki-primary)' }}>
                {complianceReport?.overall_score || 0}%
              </div>
              <Progress
                value={complianceReport?.overall_score || 0}
                className="mt-2 h-2"
              />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Configuration */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="configuration">Export Configuration</TabsTrigger>
          <TabsTrigger value="validation">Validation Rules</TabsTrigger>
          <TabsTrigger value="results">Validation Results</TabsTrigger>
        </TabsList>

        <TabsContent value="configuration" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Export Format */}
            <Card>
              <CardHeader>
                <CardTitle>Export Format</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="format">Format</Label>
                  <Select
                    value={exportConfig.format}
                    onValueChange={(value) =>
                      setExportConfig((prev) => ({ ...prev, format: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingFormats ? (
                        <SelectItem value="loading" disabled>
                          Loading formats...
                        </SelectItem>
                      ) : (
                        exportFormats.map((format) => (
                          <SelectItem key={format.id} value={format.id}>
                            {format.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-600 mt-1">
                    {currentFormat?.description || 'Loading export format...'}
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="include-gl-codes"
                      checked={exportConfig.includeGLCodes}
                      onChange={(e) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          includeGLCodes: e.target.checked,
                        }))
                      }
                      disabled={!currentFormat?.supportsGLCodes}
                    />
                    <Label htmlFor="include-gl-codes">Include GL Codes</Label>
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="include-hierarchy"
                      checked={exportConfig.includeHierarchy}
                      onChange={(e) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          includeHierarchy: e.target.checked,
                        }))
                      }
                      disabled={!currentFormat?.supportsHierarchy}
                    />
                    <Label htmlFor="include-hierarchy">
                      Include Category Hierarchy
                    </Label>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Compliance Standards</h4>
                  <div className="flex flex-wrap gap-2">
                    {(currentFormat?.compliance || []).map((standard) => (
                      <Badge key={standard} variant="outline">
                        {standard}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Date Range & Filters */}
            <Card>
              <CardHeader>
                <CardTitle>Data Selection</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start-date">Start Date</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={exportConfig.dateRange.startDate}
                      onChange={(e) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          dateRange: {
                            ...prev.dateRange,
                            startDate: e.target.value,
                          },
                        }))
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor="end-date">End Date</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={exportConfig.dateRange.endDate}
                      onChange={(e) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          dateRange: {
                            ...prev.dateRange,
                            endDate: e.target.value,
                          },
                        }))
                      }
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="accuracy-threshold">
                    Minimum Accuracy (%)
                  </Label>
                  <Input
                    id="accuracy-threshold"
                    type="number"
                    min="0"
                    max="100"
                    value={exportConfig.filters.accuracyThreshold}
                    onChange={(e) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        filters: {
                          ...prev.filters,
                          accuracyThreshold: parseInt(e.target.value),
                        },
                      }))
                    }
                  />
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="include-unverified"
                    checked={exportConfig.filters.includeUnverified}
                    onChange={(e) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        filters: {
                          ...prev.filters,
                          includeUnverified: e.target.checked,
                        },
                      }))
                    }
                  />
                  <Label htmlFor="include-unverified">
                    Include Unverified Transactions
                  </Label>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="validation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Validation Rules</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Enabled</TableHead>
                    <TableHead>Rule</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {validationRules.map((rule) => (
                    <TableRow key={rule.id}>
                      <TableCell>
                        <input
                          type="checkbox"
                          checked={rule.enabled}
                          onChange={(e) => {
                            setValidationRules((prev) =>
                              prev.map((r) =>
                                r.id === rule.id
                                  ? { ...r, enabled: e.target.checked }
                                  : r,
                              ),
                            );
                          }}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{rule.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {rule.type.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={
                            rule.severity === 'error'
                              ? 'text-error bg-red-100'
                              : rule.severity === 'warning'
                                ? 'text-yellow-600 bg-yellow-100'
                                : 'text-blue-600 bg-blue-100'
                          }
                        >
                          {rule.severity}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-gray-600">
                        {rule.description}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          {validationResults.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Validation Results</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Status</TableHead>
                      <TableHead>Rule</TableHead>
                      <TableHead>Message</TableHead>
                      <TableHead>Affected Records</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {validationResults.map((result, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          {result.status === 'passed' && (
                            <span className="w-5 h-5 text-success text-xl font-bold flex items-center justify-center">✓</span>
                          )}
                          {result.status === 'warning' && (
                            <span className="w-5 h-5 text-warning text-xl font-bold flex items-center justify-center">⚠</span>
                          )}
                          {result.status === 'failed' && (
                            <span className="w-5 h-5 text-destructive text-xl font-bold flex items-center justify-center">✗</span>
                          )}
                        </TableCell>
                        <TableCell className="font-medium">
                          {result.ruleName}
                        </TableCell>
                        <TableCell>{result.message}</TableCell>
                        <TableCell>{result.affectedRecords}</TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm">
                            <span className="w-4 h-4 text-lg font-bold flex items-center justify-center">◎</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <span className="w-12 h-12 mx-auto text-muted-foreground mb-4 text-5xl font-bold flex items-center justify-center">✓</span>
                <p className="text-secondary-foreground">
                  No validation results yet. Run validation to see results.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Validation Results Dialog */}
      <Dialog
        open={showValidationDialog}
        onOpenChange={setShowValidationDialog}
      >
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Validation Report</DialogTitle>
          </DialogHeader>

          {complianceReport && (
            <div className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-success">
                    {complianceReport.passed_checks}
                  </div>
                  <p className="text-sm text-gray-600">Passed</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-warning">
                    {complianceReport.warnings}
                  </div>
                  <p className="text-sm text-muted-foreground">Warnings</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-destructive">
                    {complianceReport.errors}
                  </div>
                  <p className="text-sm text-muted-foreground">Errors</p>
                </div>
              </div>

              <div className="space-y-4">
                {Object.entries(complianceReport.compliance_areas).map(
                  ([area, data]) => (
                    <Card key={area}>
                      <CardHeader>
                        <CardTitle className="text-lg capitalize">
                          {area.replace('_', ' ')} - {data.score}%
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Progress value={data.score} className="mb-4" />

                        {data.issues.length > 0 && (
                          <div className="mb-4">
                            <h4 className="font-medium text-error mb-2">
                              Issues:
                            </h4>
                            <ul className="list-disc list-inside text-sm text-gray-600">
                              {data.issues.map((issue, index) => (
                                <li key={index}>{issue}</li>
                              ))}
                            </ul>
                          </div>
                        )}

                        <div>
                          <h4 className="font-medium text-blue-600 mb-2">
                            Recommendations:
                          </h4>
                          <ul className="list-disc list-inside text-sm text-gray-600">
                            {data.recommendations.map((rec, index) => (
                              <li key={index}>{rec}</li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ),
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
});

ExportValidationSystem.displayName = 'ExportValidationSystem';

export default ExportValidationSystem;
