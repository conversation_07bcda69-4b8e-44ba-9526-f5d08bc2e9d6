/**
 * Consolidated Reports Service
 *
 * Handles all report generation, management, and export functionality.
 */
import axios from 'axios';
import { apiClient } from '@/shared/services/api/apiClient';
import { getAccessToken } from '@/features/auth/services/auth';
import type {
  SpendingByCategoryItem,
  SpendingByEntityItem,
  IncomeVsExpenseData,
  ReportExportParams,
} from '@/shared/types/api';
import { handleApiError, ApiError } from '@/shared/utils/errorHandling';

// Enhanced Report Types
export interface ReportRequest {
  report_type: 'income_statement' | 'balance_sheet' | 'cash_flow' | 'custom';
  start_date: string;
  end_date: string;
  format?: 'json' | 'csv' | 'pdf';
  filters?: {
    categories?: string[];
    accounts?: string[];
    min_amount?: number;
    max_amount?: number;
  };
}

export interface ReportResponse {
  report_id: string;
  report_type: string;
  status: 'generating' | 'completed' | 'failed';
  download_url?: string;
  data?: unknown;
  created_at: string;
  expires_at?: string;
}

export interface ReportSummary {
  total_income: number;
  total_expenses: number;
  net_income: number;
  transaction_count: number;
  date_range: {
    start_date: string;
    end_date: string;
  };
}

export interface CategoryBreakdown {
  category: string;
  amount: number;
  percentage: number;
  transaction_count: number;
}

export interface MonthlyTrend {
  month: string;
  income: number;
  expenses: number;
  net: number;
}

// =============================================================================
// EXISTING FUNCTIONALITY (maintain compatibility)
// =============================================================================

export const getSpendingByCategoryReport = async (): Promise<
  SpendingByCategoryItem[]
> => {
  try {
    // Backend returns SpendingByCategoryResponse with {items: SpendingByCategoryItem[], total_records: number}
    // Backend SpendingByCategoryItem has {category_path: string, total_amount: number, transaction_count: number}
    // Frontend expects SpendingByCategoryItem with {name: string, spending: number}
    const response = await apiClient.get<{
      items: Array<{
        category_path: string;
        total_amount: number;
        transaction_count: number;
      }>;
      total_records: number;
    }>('/reports/spending-by-category');

    // Validate response data
    if (!response.data || !Array.isArray(response?.data?.items)) {
      // Invalid response format for spending by category report
      return [];
    }

    // Map backend response to frontend format
    return response.data.items.map((item) => ({
      name: item.category_path,
      spending: item.total_amount,
    }));
  } catch (error) {
    // Error in getSpendingByCategoryReport - handled by error handler
    throw handleApiError(error, {
      showToast: false,
      context: 'getSpendingByCategoryReport',
      defaultMessage:
        'Failed to fetch spending by category report. This might be due to no processed transactions or a temporary server issue.',
    });
  }
};

export const getSpendingByEntityReport = async (): Promise<
  SpendingByEntityItem[]
> => {
  try {
    // Backend returns EntitySpendingResponse with {items: EntitySpendingItem[], total_records: number}
    // EntitySpendingItem has {entity_name: string, total_amount: number, transaction_count: number}
    // Frontend expects SpendingByEntityItem[] with {entity: string, spending: number}
    const response = await apiClient.get<{
      items: Array<{
        entity_name: string;
        total_amount: number;
        transaction_count: number;
      }>;
      total_records: number;
    }>('/reports/spending-by-entity');

    // Map backend response to frontend format
    return response.data.items.map((item) => ({
      entity: item.entity_name,
      spending: item.total_amount,
    }));
  } catch (error) {
    // Error in getSpendingByEntityReport - handled by error handler
    throw handleApiError(error, {
      showToast: false,
      context: 'getSpendingByEntityReport',
      defaultMessage: 'Failed to fetch spending by entity report',
    });
  }
};

// Test-compatible interface
export interface IncomeVsExpenseReportData {
  totalIncome: number;
  totalExpense: number;
  netAmount: number;
  monthlyData: Array<{
    month: string;
    income: number;
    expense: number;
  }>;
  isEmpty: boolean;
}

export const getIncomeVsExpenseReport = async (dateRange?: {
  from: Date;
  to: Date;
}): Promise<IncomeVsExpenseReportData> => {
  try {
    // Build query parameters for date range
    const params = new URLSearchParams();
    if (dateRange?.from) {
      params.append('start_date', dateRange.from.toISOString().split('T')[0]);
    }
    if (dateRange?.to) {
      params.append('end_date', dateRange.to.toISOString().split('T')[0]);
    }

    // Fetch both summary and monthly trends data
    const [summaryResponse, trendsResponse] = await Promise.all([
      apiClient.get<IncomeVsExpenseData>('/reports/income-expense-summary' + (params.toString() ? `?${params.toString()}` : '')),
      apiClient.get<{ items: Array<{ month: string; total_income: number; total_expenses: number }> }>('/reports/monthly-trends' + (params.toString() ? `?${params.toString()}` : '')),
    ]);

    // Validate response data and provide defaults for missing data
    const data = summaryResponse.data;
    if (!data) {
      // No data returned from income vs expense report
      return {
        totalIncome: 0,
        totalExpense: 0,
        netAmount: 0,
        monthlyData: [],
        isEmpty: true,
      };
    }

    const totalIncome = data.total_income || 0;
    const totalExpense = data.total_expenses || 0;
    const netAmount = data.net_income_loss || 0;

    // Process monthly trends data for chart display
    const monthlyData = trendsResponse.data?.items?.map(item => ({
      month: item.month,
      income: item.total_income || 0,
      expense: item.total_expenses || 0,
    })) || [];

    // Ensure all required fields are present with real data
    return {
      totalIncome,
      totalExpense,
      netAmount,
      monthlyData,
      isEmpty: totalIncome === 0 && totalExpense === 0,
    };
  } catch (error) {
    // Error in getIncomeVsExpenseReport - handled by error handler
    throw handleApiError(error, {
      showToast: false,
      context: 'getIncomeVsExpenseReport',
      defaultMessage:
        'Failed to fetch income vs. expense report. This might be due to no processed transactions or a temporary server issue.',
    });
  }
};

// Legacy function for backward compatibility
export const getIncomeVsExpenseReportLegacy =
  async (): Promise<IncomeVsExpenseData> => {
    try {
      const response = await apiClient.get<IncomeVsExpenseData>(
        '/reports/income-expense-summary',
      );

      // Validate response data and provide defaults for missing data
      const data = response.data;
      if (!data) {
        // No data returned from income vs expense report
        return {
          total_income: 0,
          total_expenses: 0,
          net_income_loss: 0,
          incomeByPeriod: [],
          expensesByPeriod: [],
        };
      }

      // Ensure all required fields are present with fallbacks
      return {
        total_income: data.total_income || 0,
        total_expenses: data.total_expenses || 0,
        net_income_loss: data.net_income_loss || 0,
        incomeByPeriod: data.incomeByPeriod || [],
        expensesByPeriod: data.expensesByPeriod || [],
      };
    } catch (error) {
      // Error in getIncomeVsExpenseReportLegacy - handled by error handler
      throw handleApiError(error, {
        showToast: false,
        context: 'getIncomeVsExpenseReportLegacy',
        defaultMessage:
          'Failed to fetch income vs. expense report. This might be due to no processed transactions or a temporary server issue.',
      });
    }
  };

export const exportReport = async (
  params: ReportExportParams,
): Promise<Blob> => {
  try {
    const queryParams = new URLSearchParams({
      type: params.type,
      report_name: params.reportName,
    });

    // The erroneous apiClient.post call above was removed.
    // The direct axios call below is the intended implementation.
    const token = getAccessToken();
    const headers: Record<string, string> = {
      Accept: 'application/pdf', // Or the specific blob type expected
    };
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Use axios directly for blob response, constructing full URL from apiClient's base
    // This bypasses apiClient's interceptors if they cause issues with blob response typing,
    // but requires manual auth header. Token refresh won't be handled by this direct call.
    const response = await axios.post<Blob>(
      `${apiClient.getBaseUrl()}/reports/export?${queryParams.toString()}`,
      params.filters || {},
      {
        headers,
        responseType: 'blob',
      },
    );
    return response.data;
  } catch (error) {
    // Error in exportReport - handled by error handler
    throw handleApiError(error, {
      showToast: false,
      context: 'exportReport',
      defaultMessage: `Failed to export ${params?.type?.toUpperCase()} report`,
    });
  }
};

// =============================================================================
// ENHANCED FUNCTIONALITY (new comprehensive reporting)
// =============================================================================

/**
 * Generate a financial report
 */
export const generateReport = async (
  request: ReportRequest,
): Promise<ReportResponse | ApiError> => {
  try {
    const response = await apiClient.post<ReportResponse>(
      '/reports/generate',
      request,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'generateReport',
      defaultMessage: 'Failed to generate report.',
    });
  }
};

/**
 * Get report status and data
 */
export const getReport = async (
  reportId: string,
): Promise<ReportResponse | ApiError> => {
  try {
    const response = await apiClient.get<ReportResponse>(
      `/reports/${reportId}`,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getReport',
      defaultMessage: 'Failed to get report.',
    });
  }
};

/**
 * Get financial summary for a date range
 */
export const getFinancialSummary = async (
  startDate: string,
  endDate: string,
): Promise<ReportSummary | ApiError> => {
  try {
    const response = await apiClient.get<ReportSummary>('/reports/summary', {
      params: {
        start_date: startDate,
        end_date: endDate,
      },
    });
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getFinancialSummary',
      defaultMessage: 'Failed to get financial summary.',
    });
  }
};

/**
 * Get financial summary comparison between current and previous period
 */
export const getFinancialSummaryComparison = async (
  currentStartDate: string,
  currentEndDate: string,
): Promise<{
  current: ReportSummary;
  previous: ReportSummary;
  changes: {
    totalExpensesChange: string;
    totalIncomeChange: string;
    netIncomeChange: string;
  };
} | ApiError> => {
  try {
    // Calculate previous period dates (same duration as current period)
    const currentStart = new Date(currentStartDate);
    const currentEnd = new Date(currentEndDate);
    const durationMs = currentEnd.getTime() - currentStart.getTime();
    
    const previousEnd = new Date(currentStart.getTime() - 1); // Day before current period starts
    const previousStart = new Date(previousEnd.getTime() - durationMs);
    
    // Fetch both periods in parallel
    const [currentResult, previousResult] = await Promise.all([
      getFinancialSummary(currentStartDate, currentEndDate),
      getFinancialSummary(
        previousStart.toISOString().split('T')[0],
        previousEnd.toISOString().split('T')[0]
      ),
    ]);

    // Handle errors from either call
    if ('error' in currentResult) return currentResult;
    if ('error' in previousResult) return previousResult;

    const current = currentResult as ReportSummary;
    const previous = previousResult as ReportSummary;

    // Calculate percentage changes
    const calculateChange = (currentValue: number, previousValue: number): string => {
      if (previousValue === 0) {
        return currentValue > 0 ? '+100%' : '0%';
      }
      const change = ((currentValue - previousValue) / previousValue) * 100;
      return change >= 0 ? `+${change.toFixed(1)}%` : `${change.toFixed(1)}%`;
    };

    const changes = {
      totalExpensesChange: calculateChange(current.total_expenses, previous.total_expenses),
      totalIncomeChange: calculateChange(current.total_income, previous.total_income),
      netIncomeChange: calculateChange(current.net_income, previous.net_income),
    };

    return {
      current,
      previous,
      changes,
    };
  } catch (error) {
    return handleApiError(error, {
      context: 'getFinancialSummaryComparison',
      defaultMessage: 'Failed to get financial summary comparison.',
    });
  }
};

/**
 * Get category breakdown for expenses/income
 */
export const getCategoryBreakdown = async (
  startDate: string,
  endDate: string,
  type: 'income' | 'expense' = 'expense',
): Promise<CategoryBreakdown[] | ApiError> => {
  try {
    const response = await apiClient.get<CategoryBreakdown[]>(
      '/reports/category-breakdown',
      {
        params: {
          start_date: startDate,
          end_date: endDate,
          type,
        },
      },
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getCategoryBreakdown',
      defaultMessage: 'Failed to get category breakdown.',
    });
  }
};

/**
 * Get monthly trends
 */
export const getMonthlyTrends = async (
  startDate?: string,
  endDate?: string,
): Promise<MonthlyTrend[] | ApiError> => {
  try {
    // Backend returns MonthlyTrendsResponse with {items: MonthlyTrendItem[], total_months: number}
    // MonthlyTrendItem has {month: string, total_income: number, total_expenses: number, net_amount: number, transaction_count: number}
    const response = await apiClient.get<{
      items: Array<{
        month: string;
        total_income: number;
        total_expenses: number;
        net_amount: number;
        transaction_count: number;
      }>;
      total_months: number;
    }>('/reports/monthly-trends', {
      params: {
        ...(startDate && { start_date: startDate }),
        ...(endDate && { end_date: endDate }),
      },
    });

    // Map backend response to frontend format
    return response.data.items.map((item) => ({
      month: item.month,
      income: item.total_income,
      expenses: item.total_expenses,
      net: item.net_amount,
    }));
  } catch (error) {
    return handleApiError(error, {
      context: 'getMonthlyTrends',
      defaultMessage: 'Failed to get monthly trends.',
    });
  }
};

/**
 * Download report as file
 */
export const downloadReport = async (
  reportId: string,
  format: 'csv' | 'pdf' = 'csv',
): Promise<Blob | ApiError> => {
  try {
    const response = await apiClient.get(`/reports/${reportId}/download`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data as Blob;
  } catch (error) {
    return handleApiError(error, {
      context: 'downloadReport',
      defaultMessage: 'Failed to download report.',
    });
  }
};

/**
 * Get list of available reports
 */
export const getReportHistory = async (
  page: number = 1,
  perPage: number = 20,
): Promise<{ reports: ReportResponse[]; total: number } | ApiError> => {
  try {
    const response = await apiClient.get('/reports/history', {
      params: {
        page,
        per_page: perPage,
      },
    });
    return response.data as { reports: ReportResponse[]; total: number };
  } catch (error) {
    return handleApiError(error, {
      context: 'getReportHistory',
      defaultMessage: 'Failed to get report history.',
    });
  }
};

/**
 * Delete a report
 */
export const deleteReport = async (
  reportId: string,
): Promise<void | ApiError> => {
  try {
    await apiClient.delete(`/reports/${reportId}`);
  } catch (error) {
    return handleApiError(error, {
      context: 'deleteReport',
      defaultMessage: 'Failed to delete report.',
    });
  }
};

/**
 * Get available report templates
 */
export const getReportTemplates = async (): Promise<unknown[] | ApiError> => {
  try {
    const response = await apiClient.get('/reports/templates');
    return response.data as unknown[];
  } catch (error) {
    return handleApiError(error, {
      context: 'getReportTemplates',
      defaultMessage: 'Failed to get report templates.',
    });
  }
};

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Format currency for display
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
};

/**
 * Format percentage for display
 */
export const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

/**
 * Get date range presets
 */
export const getDateRangePresets = () => {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  return {
    'This Month': {
      start_date: new Date(currentYear, currentMonth, 1)
        .toISOString()
        .split('T')[0],
      end_date: today.toISOString().split('T')[0],
    },
    'Last Month': {
      start_date: new Date(currentYear, currentMonth - 1, 1)
        .toISOString()
        .split('T')[0],
      end_date: new Date(currentYear, currentMonth, 0)
        .toISOString()
        .split('T')[0],
    },
    'This Quarter': {
      start_date: new Date(currentYear, Math.floor(currentMonth / 3) * 3, 1)
        .toISOString()
        .split('T')[0],
      end_date: today.toISOString().split('T')[0],
    },
    'This Year': {
      start_date: new Date(currentYear, 0, 1).toISOString().split('T')[0],
      end_date: today.toISOString().split('T')[0],
    },
    'Last Year': {
      start_date: new Date(currentYear - 1, 0, 1).toISOString().split('T')[0],
      end_date: new Date(currentYear - 1, 11, 31).toISOString().split('T')[0],
    },
  };
};

/**
 * Validate date range
 */
export const validateDateRange = (
  startDate: string,
  endDate: string,
): { isValid: boolean; error?: string } => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const today = new Date();

  if (start > end) {
    return {
      isValid: false,
      error: 'Start date must be before end date',
    };
  }

  if (end > today) {
    return {
      isValid: false,
      error: 'End date cannot be in the future',
    };
  }

  // Check if date range is too large (e.g., more than 2 years)
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays > 730) {
    // 2 years
    return {
      isValid: false,
      error: 'Date range cannot exceed 2 years',
    };
  }

  return { isValid: true };
};
