/**
 * Real Tax Service - Replaces fake tax calculations
 * Calculates tax data from actual financial transactions
 */

import { apiClient } from '@/shared/services/api/apiClient';
import { logger } from '@/shared/utils/errorHandling';

export interface TaxSummary {
  taxable_income: number;
  total_deductions: number;
  estimated_tax_liability: number;
  quarterly_payments: number;
  tax_year: number;
  current_quarter: number;
}

export interface QuarterlySummary {
  quarter: number;
  income: number;
  deductions: number;
  estimated_tax: number;
  payments_made: number;
  due_date: string;
  status: 'filed' | 'pending' | 'overdue';
}

export interface DeductibleExpense {
  category: string;
  amount: number;
  gl_code: string;
  transaction_count: number;
}

export interface IncomeExpenseData {
  total_income: number;
  total_expenses: number;
  net_income: number;
  period_start: string;
  period_end: string;
}

export interface CategorySpending {
  category: string;
  amount: number;
  gl_code?: string;
  transaction_count: number;
}

/**
 * Tax Service - Real implementation using actual financial data
 */
export class TaxService {
  private readonly STANDARD_TAX_RATE = 0.25; // 25% estimated tax rate
  private readonly QUARTERLY_SAFE_HARBOR = 0.25; // 25% quarterly safe harbor

  /**
   * Get real tax summary for a given year
   */
  async getTaxSummary(year: number): Promise<TaxSummary> {
    try {
      logger.info('TaxService.getTaxSummary called', 'taxService', { year });

      // Get real annual income/expense data
      const startDate = `${year}-01-01`;
      const endDate = `${year}-12-31`;
      
      const incomeExpenseResponse = await apiClient.get<IncomeExpenseData>(
        '/reports/income-expense-summary',
        {
          params: {
            start_date: startDate,
            end_date: endDate,
          },
        }
      );

      const financialData = incomeExpenseResponse.data;

      // Get deductible expenses (business expense categories)
      const deductibleExpenses = await this.getDeductibleExpenses(year);
      const totalDeductions = deductibleExpenses.reduce((sum, expense) => sum + Math.abs(expense.amount), 0);

      // Calculate real tax liability
      const taxableIncome = Math.max(0, financialData.total_income - totalDeductions);
      const estimatedTaxLiability = taxableIncome * this.STANDARD_TAX_RATE;

      // Get actual quarterly payments (if any payment tracking exists)
      const quarterlyPayments = await this.getQuarterlyPayments(year);

      return {
        taxable_income: taxableIncome,
        total_deductions: totalDeductions,
        estimated_tax_liability: estimatedTaxLiability,
        quarterly_payments: quarterlyPayments,
        tax_year: year,
        current_quarter: Math.ceil(new Date().getMonth() / 3) + 1,
      };

    } catch (error) {
      logger.error('Error calculating tax summary', 'taxService', error as Error);
      
      // Return empty data instead of fake data
      return {
        taxable_income: 0,
        total_deductions: 0,
        estimated_tax_liability: 0,
        quarterly_payments: 0,
        tax_year: year,
        current_quarter: Math.ceil(new Date().getMonth() / 3) + 1,
      };
    }
  }

  /**
   * Get real quarterly tax summary
   */
  async getQuarterlySummary(year: number): Promise<QuarterlySummary[]> {
    try {
      logger.info('TaxService.getQuarterlySummary called', 'taxService', { year });

      const quarters: QuarterlySummary[] = [];

      for (let quarter = 1; quarter <= 4; quarter++) {
        const { startDate, endDate } = this.getQuarterDates(year, quarter);

        // Get real quarterly financial data
        const incomeExpenseResponse = await apiClient.get<IncomeExpenseData>(
          '/reports/income-expense-summary',
          {
            params: {
              start_date: startDate,
              end_date: endDate,
            },
          }
        );

        const quarterData = incomeExpenseResponse.data;

        // Get quarterly deductions
        const quarterlyDeductions = await this.getQuarterlyDeductions(year, quarter);
        const totalDeductions = quarterlyDeductions.reduce((sum, expense) => sum + Math.abs(expense.amount), 0);

        // Calculate quarterly tax estimate
        const quarterlyTaxableIncome = Math.max(0, quarterData.total_income - totalDeductions);
        const estimatedQuarterlyTax = quarterlyTaxableIncome * this.QUARTERLY_SAFE_HARBOR;

        // Determine status based on current date and quarter
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentQuarter = Math.ceil((currentDate.getMonth() + 1) / 3);
        
        let status: 'filed' | 'pending' | 'overdue';
        if (year < currentYear || (year === currentYear && quarter < currentQuarter)) {
          status = 'filed'; // Past quarters assumed filed
        } else if (year === currentYear && quarter === currentQuarter) {
          status = 'pending'; // Current quarter
        } else {
          status = 'pending'; // Future quarters
        }

        quarters.push({
          quarter,
          income: quarterData.total_income,
          deductions: totalDeductions,
          estimated_tax: estimatedQuarterlyTax,
          payments_made: 0, // TODO: Implement payment tracking
          due_date: this.getQuarterDueDate(year, quarter),
          status,
        });
      }

      return quarters;

    } catch (error) {
      logger.error('Error calculating quarterly summary', 'taxService', error as Error);
      return [];
    }
  }

  /**
   * Get real deductible expenses from transaction data
   */
  async getDeductibleExpenses(year: number): Promise<DeductibleExpense[]> {
    try {
      logger.info('TaxService.getDeductibleExpenses called', 'taxService', { year });

      const startDate = `${year}-01-01`;
      const endDate = `${year}-12-31`;

      // Get spending by category (business expenses only)
      const categoryResponse = await apiClient.get<{ categories: CategorySpending[] }>(
        '/reports/spending-by-category',
        {
          params: {
            start_date: startDate,
            end_date: endDate,
          },
        }
      );

      // Filter for business expense categories (negative amounts)
      const businessExpenses = categoryResponse.data.categories
        .filter(category => category.amount < 0) // Expenses are negative
        .map(category => ({
          category: category.category,
          amount: Math.abs(category.amount), // Convert to positive for display
          gl_code: category.gl_code || '5000', // Default to expense GL code
          transaction_count: category.transaction_count,
        }))
        .filter(expense => expense.amount > 0) // Only include actual expenses
        .sort((a, b) => b.amount - a.amount); // Sort by amount descending

      return businessExpenses;

    } catch (error) {
      logger.error('Error getting deductible expenses', 'taxService', error as Error);
      return [];
    }
  }

  /**
   * Get quarterly deductions for a specific quarter
   */
  private async getQuarterlyDeductions(year: number, quarter: number): Promise<DeductibleExpense[]> {
    const { startDate, endDate } = this.getQuarterDates(year, quarter);

    try {
      const categoryResponse = await apiClient.get<{ categories: CategorySpending[] }>(
        '/reports/spending-by-category',
        {
          params: {
            start_date: startDate,
            end_date: endDate,
          },
        }
      );

      return categoryResponse.data.categories
        .filter(category => category.amount < 0)
        .map(category => ({
          category: category.category,
          amount: Math.abs(category.amount),
          gl_code: category.gl_code || '5000',
          transaction_count: category.transaction_count,
        }));

    } catch (error) {
      logger.error('Error getting quarterly deductions', 'taxService', error as Error);
      return [];
    }
  }

  /**
   * Get quarterly payment amounts (placeholder for future payment tracking)
   */
  private async getQuarterlyPayments(year: number): Promise<number> {
    // TODO: Implement actual quarterly payment tracking
    // This would require a separate payments table or tax payment transactions
    logger.info('Quarterly payment tracking not yet implemented', 'taxService');
    return 0;
  }

  /**
   * Get quarter date ranges
   */
  private getQuarterDates(year: number, quarter: number): { startDate: string; endDate: string } {
    const quarterMap = {
      1: { start: '01-01', end: '03-31' },
      2: { start: '04-01', end: '06-30' },
      3: { start: '07-01', end: '09-30' },
      4: { start: '10-01', end: '12-31' },
    };

    const dates = quarterMap[quarter as keyof typeof quarterMap];
    return {
      startDate: `${year}-${dates.start}`,
      endDate: `${year}-${dates.end}`,
    };
  }

  /**
   * Get quarter due dates (US tax calendar)
   */
  private getQuarterDueDate(year: number, quarter: number): string {
    const dueDateMap = {
      1: `${year}-04-15`,
      2: `${year}-06-15`, 
      3: `${year}-09-15`,
      4: `${year + 1}-01-15`,
    };

    return dueDateMap[quarter as keyof typeof dueDateMap];
  }
}

// Export singleton instance
export const taxService = new TaxService();