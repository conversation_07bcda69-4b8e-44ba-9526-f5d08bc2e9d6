/**
 * Export API Service - Real Backend Integration
 * ============================================
 * 
 * Connects ExportValidationSystem to real backend APIs:
 * - /api/v1/exports/formats - Get available export formats
 * - /api/v1/exports/readiness/{format_id} - Validate export readiness  
 * - /api/v1/exports/download - Download export file
 */

import { apiClient } from '@/shared/services/api/apiClient';
import { logger } from '@/shared/utils/errorHandling';

// Types from backend API
export interface ExportFormatInfo {
  id: string;
  name: string;
  description: string;
  file_extension: string;
  supports_multi_currency: boolean;
  requires_account_codes: boolean;
  required_fields: string[];
}

export interface ExportReadinessRequest {
  format_id: string;
  date_from?: string;
  date_to?: string;
  category_ids?: number[];
  account_ids?: number[];
  include_uncategorized?: boolean;
}

export interface ExportReadinessResponse {
  format_id: string;
  ready_for_export: boolean;
  validation_results: ValidationResult[];
  compliance_report: ComplianceReport;
  total_transactions: number;
  export_preview: {
    sample_records: any[];
    estimated_file_size: string;
    export_time_estimate: string;
  };
}

export interface ValidationResult {
  check_id: string;
  check_name: string;
  status: 'passed' | 'warning' | 'failed';
  message: string;
  affected_records: number;
  severity: 'error' | 'warning' | 'info';
  suggestions: string[];
}

export interface ComplianceReport {
  overall_score: number;
  total_checks: number;
  passed_checks: number;
  warnings: number;
  errors: number;
  compliance_areas: {
    gl_codes: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
    data_integrity: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
    format_compliance: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
  };
}

export interface ExportDownloadRequest {
  format_id: string;
  date_from?: string;
  date_to?: string;
  category_ids?: number[];
  account_ids?: number[];
  include_uncategorized?: boolean;
  export_options?: {
    include_gl_codes?: boolean;
    include_hierarchy?: boolean;
    include_metadata?: boolean;
  };
}

export class ExportApiService {
  /**
   * Get all available export formats from backend
   */
  static async getExportFormats(): Promise<ExportFormatInfo[]> {
    try {
      const response = await apiClient.get<ExportFormatInfo[]>('/exports/formats');
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch export formats', 'exportApiService', error as Error);
      throw new Error('Failed to load export formats. Please try again.');
    }
  }

  /**
   * Check export readiness and get validation results for a specific format
   */
  static async checkExportReadiness(
    formatId: string,
    options: Omit<ExportReadinessRequest, 'format_id'> = {}
  ): Promise<ExportReadinessResponse> {
    try {
      // The backend readiness endpoint is a GET request, not POST
      const response = await apiClient.get<{
        is_ready: boolean;
        messages: string[];
        format_name: string;
      }>(`/exports/readiness/${formatId}`);

      // Convert backend response to frontend format
      const validationResults = response.data.messages.map((message, index) => ({
        check_id: `readiness_${index}`,
        check_name: `Export Readiness Check ${index + 1}`,
        status: response.data.is_ready ? 'passed' : 'failed' as 'passed' | 'warning' | 'failed',
        message: message,
        affected_records: 0,
        severity: response.data.is_ready ? 'info' : 'error' as 'error' | 'warning' | 'info',
        suggestions: response.data.is_ready ? [] : ['Categorize transactions before exporting']
      }));

      // Create minimal compliance report from actual backend data only
      const complianceReport: ComplianceReport = {
        overall_score: response.data.is_ready ? 100 : 0, // Only use real readiness status
        total_checks: validationResults.length,
        passed_checks: response.data.is_ready ? validationResults.length : 0,
        warnings: 0,
        errors: response.data.is_ready ? 0 : validationResults.length,
        compliance_areas: {
          gl_codes: {
            score: response.data.is_ready ? 100 : 0, // Only use real readiness - no fake scores
            issues: [], // Only include real issues from backend messages
            recommendations: [] // Only include real recommendations from backend
          },
          data_integrity: {
            score: response.data.is_ready ? 100 : 0, // Only use real readiness - no fake scores
            issues: [], // Only include real issues from backend messages
            recommendations: [] // Only include real recommendations from backend
          },
          format_compliance: {
            score: response.data.is_ready ? 100 : 0, // Only use real readiness - no fake scores
            issues: [], // Only include real issues from backend messages
            recommendations: [] // Only include real recommendations from backend
          }
        }
      };

      return {
        format_id: formatId,
        ready_for_export: response.data.is_ready,
        validation_results: validationResults,
        compliance_report: complianceReport,
        total_transactions: 0, // Backend doesn't provide this in readiness response
        export_preview: {
          sample_records: [], // Backend doesn't provide sample records in readiness check
          estimated_file_size: response.data.is_ready ? 'Ready for export' : 'N/A',
          export_time_estimate: response.data.is_ready ? '< 2 minutes' : 'N/A'
        }
      };
    } catch (error) {
      logger.error('Failed to check export readiness', 'exportApiService', error as Error);
      throw new Error('Failed to validate export data. Please try again.');
    }
  }

  /**
   * Download export file in specified format
   */
  static async downloadExport(
    formatId: string,
    options: Omit<ExportDownloadRequest, 'format_id'> = {}
  ): Promise<Blob> {
    try {
      const request: ExportDownloadRequest = {
        format_id: formatId,
        include_uncategorized: true,
        export_options: {
          include_gl_codes: true,
          include_hierarchy: true,
          include_metadata: true
        },
        ...options
      };

      const response = await apiClient.post('/exports/download', request, {
        responseType: 'blob'
      });

      return response.data as Blob;
    } catch (error) {
      logger.error('Failed to download export', 'exportApiService', error as Error);
      throw new Error('Failed to download export file. Please try again.');
    }
  }

  /**
   * Helper: Convert ExportFormatInfo to local ExportFormat type
   */
  static formatInfoToExportFormat(formatInfo: ExportFormatInfo) {
    return {
      id: formatInfo.id,
      name: formatInfo.name,
      description: formatInfo.description,
      fileExtension: formatInfo.file_extension,
      supportsGLCodes: formatInfo.requires_account_codes,
      supportsHierarchy: formatInfo.supports_multi_currency,
      compliance: [formatInfo.name.split(' ')[0]], // Extract primary compliance
      validationRules: [
        'gl_code_presence',
        'amount_validation',
        'date_consistency',
        ...(formatInfo.requires_account_codes ? ['hierarchy_integrity'] : [])
      ]
    };
  }

  /**
   * Helper: Convert backend validation results to component format
   */
  static convertValidationResults(backendResults: ValidationResult[]) {
    return backendResults.map(result => ({
      ruleId: result.check_id,
      ruleName: result.check_name,
      status: result.status,
      message: result.message,
      affectedRecords: result.affected_records,
      severity: result.severity,
      suggestions: result.suggestions
    }));
  }

  /**
   * Helper: Trigger file download in browser
   */
  static triggerFileDownload(blob: Blob, filename: string) {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}