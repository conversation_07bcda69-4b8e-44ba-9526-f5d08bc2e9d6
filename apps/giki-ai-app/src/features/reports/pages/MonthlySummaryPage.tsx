/**
 * Monthly Summary Report Page
 * Professional B2B financial summary with key metrics and insights
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  getFinancialSummary,
  getCategoryBreakdown,
  type ReportSummary,
} from '../services/reportService';
import { exportTransactions } from '@/features/transactions/services/exportService';
import { fetchTransactions } from '@/features/transactions/services/transactionService';
import { isApiError } from '@/shared/utils/errorHandling';
import {
  ArrowLeft,
  Download,
  TrendingUp,
  TrendingDown,
  Calendar,
  DollarSign,
  FileText,
  BarChart3,
} from 'lucide-react';

interface MonthlyMetric {
  label: string;
  value: string;
  change: number;
  trend: 'up' | 'down' | 'flat';
  subtitle?: string;
}

interface CategorySummary {
  name: string;
  amount: number;
  percentage: number;
  change: number;
  count: number;
}

const MonthlySummaryPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // State
  const [selectedMonth, setSelectedMonth] = useState(() => {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [metrics, setMetrics] = useState<MonthlyMetric[]>([]);
  const [topExpenses, setTopExpenses] = useState<CategorySummary[]>([]);
  const [topIncome, setTopIncome] = useState<CategorySummary[]>([]);
  const [summary, setSummary] = useState<ReportSummary | null>(null);

  // Helper function to calculate month-over-month change
  const calculateMonthOverMonthChange = async (currentValue: number, currentStartDate: Date, currentEndDate: Date): Promise<number> => {
    try {
      // Calculate previous month dates
      const prevMonth = new Date(currentStartDate);
      prevMonth.setMonth(prevMonth.getMonth() - 1);
      const prevStartDate = new Date(prevMonth.getFullYear(), prevMonth.getMonth(), 1);
      const prevEndDate = new Date(prevMonth.getFullYear(), prevMonth.getMonth() + 1, 0);
      
      // Format dates for API
      const formatDate = (date: Date) => date.toISOString().split('T')[0];
      
      // Get previous month summary
      const prevSummaryResult = await getFinancialSummary(
        formatDate(prevStartDate),
        formatDate(prevEndDate),
      );
      
      if (!isApiError(prevSummaryResult) && prevSummaryResult.total_income !== undefined) {
        const prevValue = prevSummaryResult.total_income || 0;
        if (prevValue === 0) return 0;
        return ((currentValue - prevValue) / prevValue) * 100;
      }
      
      return 0;
    } catch {
      return 0;
    }
  };

  // Load monthly data
  useEffect(() => {
    const loadMonthlyData = async () => {
      try {
        setIsLoading(true);

        // Parse selected month
        const [year, month] = selectedMonth.split('-');
        const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
        const endDate = new Date(parseInt(year), parseInt(month), 0);

        // Format dates for API
        const formatDate = (date: Date) => date.toISOString().split('T')[0];

        // Get financial summary
        const summaryResult = await getFinancialSummary(
          formatDate(startDate),
          formatDate(endDate),
        );

        if (!isApiError(summaryResult)) {
          setSummary(summaryResult);

          // Calculate metrics
          const totalIncome = summaryResult.total_income || 0;
          const totalExpenses = summaryResult.total_expenses || 0;
          const netIncome = summaryResult.net_income || 0;
          const transactionCount = summaryResult.transaction_count || 0;

          const profitMargin =
            totalIncome > 0 ? (netIncome / totalIncome) * 100 : 0;

          const avgTransactionSize =
            transactionCount > 0
              ? (totalExpenses + totalIncome) / transactionCount
              : 0;

          // Calculate real month-over-month changes
          const [revenueChange, expenseChange, avgTransactionChange] = await Promise.all([
            calculateMonthOverMonthChange(totalIncome, startDate, endDate),
            calculateMonthOverMonthChange(Math.abs(totalExpenses), startDate, endDate),
            calculateMonthOverMonthChange(Math.abs(avgTransactionSize), startDate, endDate),
          ]);

          const metricsData: MonthlyMetric[] = [
            {
              label: 'Total Revenue',
              value: `$${totalIncome.toLocaleString()}`,
              change: revenueChange,
              trend: revenueChange >= 0 ? 'up' : 'down',
              subtitle: `${transactionCount} income transactions`,
            },
            {
              label: 'Total Expenses',
              value: `$${Math.abs(totalExpenses).toLocaleString()}`,
              change: expenseChange,
              trend: expenseChange >= 0 ? 'up' : 'down',
              subtitle: `${transactionCount} expense transactions`,
            },
            {
              label: 'Net Income',
              value: `$${netIncome.toLocaleString()}`,
              change: profitMargin,
              trend: netIncome >= 0 ? 'up' : 'down',
              subtitle: `${profitMargin.toFixed(1)}% margin`,
            },
            {
              label: 'Avg Transaction',
              value: `$${Math.abs(avgTransactionSize).toLocaleString()}`,
              change: avgTransactionChange,
              trend: Math.abs(avgTransactionChange) < 1 ? 'flat' : (avgTransactionChange >= 0 ? 'up' : 'down'),
              subtitle: `${transactionCount} total`,
            },
          ];
          setMetrics(metricsData);
        }

        // Get expense breakdown
        const expenseBreakdown = await getCategoryBreakdown(
          formatDate(startDate),
          formatDate(endDate),
          'expense',
        );

        if (!isApiError(expenseBreakdown)) {
          const expenseData: CategorySummary[] = expenseBreakdown
            .slice(0, 5)
            .map((cat) => ({
              name: cat.category,
              amount: Math.abs(cat.amount),
              percentage: cat.percentage,
              change: 5.5, // TODO: Calculate from previous month
              count: cat.transaction_count,
            }));
          setTopExpenses(expenseData);
        }

        // Get income breakdown
        const incomeBreakdown = await getCategoryBreakdown(
          formatDate(startDate),
          formatDate(endDate),
          'income',
        );

        if (!isApiError(incomeBreakdown)) {
          const incomeData: CategorySummary[] = incomeBreakdown
            .slice(0, 5)
            .map((cat) => ({
              name: cat.category,
              amount: cat.amount,
              percentage: cat.percentage,
              change: 8.2, // TODO: Calculate from previous month
              count: cat.transaction_count,
            }));
          setTopIncome(incomeData);
        }
      } catch (error) {
        console.error('Error loading monthly data:', error);
        toast({
          title: 'Error Loading Data',
          description: 'Failed to load monthly summary. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    void loadMonthlyData();
  }, [selectedMonth, toast]);

  // Export handlers
  const handleExportExcel = async () => {
    try {
      setIsExporting(true);

      const [year, month] = selectedMonth.split('-');
      const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
      const endDate = new Date(parseInt(year), parseInt(month), 0);

      // Get transactions for export
      const transactions = await fetchTransactions({
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        pageSize: 10000,
      });

      if ('items' in transactions) {
        await exportTransactions(transactions.items, {
          format: 'excel',
          fileName: `monthly-summary-${selectedMonth}.xlsx`,
          includeSummary: true,
          includeMetadata: true,
        });

        toast({
          title: 'Export Complete',
          description: 'Monthly summary has been downloaded.',
        });
      }
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export monthly summary.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Generate month options for selector
  const monthOptions: Array<{ value: string; label: string }> = [];
  const now = new Date();
  for (let i = 0; i < 12; i++) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const value = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    const label = date.toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric',
    });
    monthOptions.push({ value, label });
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div
            className="w-8 h-8 border-4 border-t-transparent rounded-full animate-spin mx-auto mb-4"
            style={{ borderColor: 'var(--giki-primary)', borderTopColor: 'transparent' }}
          />
          <p className="text-gray-600">Loading monthly summary...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/reports')}
                className="p-2"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">
                  Monthly Summary
                </h1>
                <p className="text-gray-600">
                  Comprehensive financial overview
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                <SelectTrigger className="w-48">
                  <Calendar className="w-4 h-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {monthOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={() => void handleExportExcel()}
                disabled={isExporting}
                className="bg-brand-primary hover:bg-brand-primary-hover text-white"
              >
                <Download className="w-4 h-4 mr-2" />
                {isExporting ? 'Exporting...' : 'Export'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {metrics.map((metric, index) => (
            <Card key={index} className="border border-gray-200">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-2">
                  <p className="text-sm font-medium text-gray-600">
                    {metric.label}
                  </p>
                  {metric.trend === 'up' ? (
                    <TrendingUp className="w-5 h-5 text-success" />
                  ) : metric.trend === 'down' ? (
                    <TrendingDown className="w-5 h-5 text-error" />
                  ) : (
                    <DollarSign className="w-5 h-5 text-gray-400" />
                  )}
                </div>
                <p className="text-2xl font-bold text-gray-800 mb-1">
                  {metric.value}
                </p>
                <p className="text-sm text-gray-500">
                  {metric.trend !== 'flat' && (
                    <span
                      className={
                        metric.trend === 'up'
                          ? 'text-success'
                          : 'text-error'
                      }
                    >
                      {metric.trend === 'up' ? '+' : ''}
                      {metric.change.toFixed(1)}%
                    </span>
                  )}
                  {metric.subtitle && (
                    <span className="ml-2">{metric.subtitle}</span>
                  )}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Expenses */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-800">
                <BarChart3 className="w-5 h-5" />
                Top Expenses
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topExpenses.map((category, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-800">
                          {category.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {category.count} transactions •{' '}
                          {category.percentage.toFixed(1)}%
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-mono font-semibold text-gray-800">
                          ${category.amount.toLocaleString()}
                        </p>
                        <p className="text-sm text-success">
                          +{category.change.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="h-2 rounded-full"
                        style={{
                          width: `${category.percentage}%`,
                          backgroundColor: 'var(--giki-primary)',
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Income */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-800">
                <TrendingUp className="w-5 h-5" />
                Top Income Sources
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topIncome.map((category, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-800">
                          {category.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {category.count} transactions •{' '}
                          {category.percentage.toFixed(1)}%
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-mono font-semibold text-gray-800">
                          ${category.amount.toLocaleString()}
                        </p>
                        <p className="text-sm text-success">
                          +{category.change.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="h-2 rounded-full bg-green-600"
                        style={{
                          width: `${category.percentage}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Summary Insights */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-800">
              <FileText className="w-5 h-5" />
              Financial Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600 mb-2">Operating Margin</p>
                <p className="text-2xl font-bold text-gray-800">
                  {summary && summary.total_income > 0
                    ? (
                        (summary.net_income / summary.total_income) *
                        100
                      ).toFixed(1)
                    : '0.0'}
                  %
                </p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600 mb-2">Expense Ratio</p>
                <p className="text-2xl font-bold text-gray-800">
                  {summary && summary.total_income > 0
                    ? (
                        (Math.abs(summary.total_expenses) /
                          summary.total_income) *
                        100
                      ).toFixed(1)
                    : '0.0'}
                  %
                </p>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600 mb-2">Daily Average</p>
                <p className="text-2xl font-bold text-gray-800">
                  $
                  {summary
                    ? (Math.abs(summary.total_expenses) / 30).toFixed(0)
                    : '0'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MonthlySummaryPage;
