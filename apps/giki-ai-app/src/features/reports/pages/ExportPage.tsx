/**
 * Export Page - Streamlined export interface
 * Matches mockup: docs/design-system/mockups/reports/500-export-options.html
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { ExportFormatSelector } from '../components/ExportFormatSelector';
import { exportTransactions } from '@/features/transactions/services/exportService';
import { useToast } from '@/shared/components/ui/use-toast';

const ExportPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedFormat, setSelectedFormat] = useState('excel');
  const [isExporting, setIsExporting] = useState(false);
  const [exportComplete, setExportComplete] = useState(false);

  // Mock data - would come from API in real implementation
  const exportStats = {
    transactionCount: 247,
    categoryCount: 18,
    accuracy: 89
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Call actual export API
      const response = await exportTransactions({
        format: selectedFormat === 'excel' ? 'xlsx' : selectedFormat,
        include_headers: true,
        date_range: 'this_month'
      });

      if ('error' in response) {
        throw new Error(response.error);
      }

      setExportComplete(true);
      
      toast({
        title: 'Export Successful',
        description: `Your ${selectedFormat.toUpperCase()} file has been downloaded.`,
      });
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: 'Please try again or contact support.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const getDownloadText = () => {
    switch (selectedFormat) {
      case 'excel':
        return 'Download Excel File';
      case 'quickbooks':
        return 'Download QBO File';
      case 'csv':
        return 'Download CSV File';
      default:
        return 'Download File';
    }
  };

  if (exportComplete) {
    return (
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="text-center bg-success text-white p-12 rounded-xl">
          <div className="text-5xl mb-4">□</div>
          <h2 className="text-2xl font-bold mb-2">Download Complete!</h2>
          <p className="text-lg mb-6">Your file has been downloaded successfully.</p>
          <Button
            onClick={() => navigate('/dashboard')}
            className="bg-white text-success hover:bg-muted"
          >
            Go to Dashboard →
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-brand-primary mb-2">Export Your Data</h1>
        <p className="text-lg text-secondary-foreground">Choose your format and download</p>
      </div>

      {/* Export Format Selector */}
      <ExportFormatSelector
        selectedFormat={selectedFormat}
        onFormatChange={setSelectedFormat}
        transactionCount={exportStats.transactionCount}
        categoryCount={exportStats.categoryCount}
        accuracy={exportStats.accuracy}
      />

      {/* Download Button */}
      <div className="text-center mt-8">
        <Button
          onClick={handleExport}
          disabled={isExporting}
          className="bg-gradient-to-r from-brand-primary to-ai-dark-blue hover:from-[#1D372E] hover:to-[#15324D] text-white px-8 py-4 text-lg"
        >
          {isExporting ? (
            <>
              <span className="animate-pulse mr-2">⚬</span>
              <span>Preparing... {Math.round(Math.random() * 100)}%</span>
            </>
          ) : (
            <>
              <span className="mr-2">↑</span>
              <span>{getDownloadText()}</span>
            </>
          )}
        </Button>
        <div className="mt-2 text-sm text-secondary-foreground">
          {exportStats.transactionCount} transactions • {exportStats.categoryCount} categories • {exportStats.accuracy}% accuracy
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between mt-12">
        <Button
          variant="outline"
          onClick={() => navigate(-1)}
          className="border-2 border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white"
        >
          <span className="mr-2">←</span>
          Back to Results
        </Button>
        <Button
          variant="outline"
          onClick={() => navigate('/dashboard')}
          className="border-2 border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white"
        >
          Continue to Dashboard
          <span className="ml-2">→</span>
        </Button>
      </div>
    </div>
  );
};

export default ExportPage;