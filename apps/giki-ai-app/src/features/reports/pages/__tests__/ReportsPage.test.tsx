/**
 * ReportsPage Component Tests
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { 
  render, 
  mockFetch, 
  setupAuthenticatedState 
} from '@/test/utils';
import ReportsPage from '../ReportsPage';

// Mock report data
const mockReportData = {
  totalIncome: 125000,
  totalExpenses: 87500,
  netProfit: 37500,
  transactionCount: 245,
  categoryBreakdown: [
    { category: 'Technology', amount: 25000, percentage: 28.6 },
    { category: 'Office Supplies', amount: 15000, percentage: 17.1 },
    { category: 'Marketing', amount: 12500, percentage: 14.3 },
  ],
  monthlyTrends: [
    { month: '2024-01', income: 42000, expenses: 28000 },
    { month: '2024-02', income: 38000, expenses: 31000 },
    { month: '2024-03', income: 45000, expenses: 28500 },
  ],
};

const mockExportFormats = [
  { id: 'quickbooks_desktop', name: '<PERSON>B<PERSON>s Desktop (IIF)', available: true },
  { id: 'quickbooks_online', name: 'QuickBooks Online (CSV)', available: true },
  { id: 'xero', name: 'Xero', available: true },
  { id: 'sage', name: 'Sage 50', available: false, reason: 'Missing GL codes' },
  { id: 'excel', name: 'Excel (XLSX)', available: true },
  { id: 'csv', name: 'CSV Format', available: true },
];

describe('ReportsPage', () => {
  beforeEach(() => {
    setupAuthenticatedState();
  });

  it('renders reports interface correctly', async () => {
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/financial reports/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/report type/i)).toBeInTheDocument();
    expect(screen.getByText(/time period/i)).toBeInTheDocument();
    expect(screen.getByText(/export options/i)).toBeInTheDocument();
  });

  it('displays financial summary metrics', async () => {
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/\$125,000/)).toBeInTheDocument(); // Total income
    });
    
    expect(screen.getByText(/\$87,500/)).toBeInTheDocument(); // Total expenses
    expect(screen.getByText(/\$37,500/)).toBeInTheDocument(); // Net profit
    expect(screen.getByText(/245.*transactions/i)).toBeInTheDocument();
  });

  it('shows category breakdown chart', async () => {
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/category breakdown/i)).toBeInTheDocument();
    });
    
    // Check category data
    expect(screen.getByText(/technology.*28\.6%/i)).toBeInTheDocument();
    expect(screen.getByText(/office supplies.*17\.1%/i)).toBeInTheDocument();
    expect(screen.getByText(/marketing.*14\.3%/i)).toBeInTheDocument();
  });

  it('displays monthly trends chart', async () => {
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/monthly trends/i)).toBeInTheDocument();
    });
    
    // Should show chart with data points
    expect(screen.getByRole('img', { name: /monthly trends chart/i })).toBeInTheDocument();
  });

  it('allows changing report time period', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/time period/i)).toBeInTheDocument();
    });
    
    // Change time period
    const periodSelect = screen.getByRole('combobox', { name: /time period/i });
    await user.selectOptions(periodSelect, 'last_quarter');
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('period=last_quarter'),
        expect.any(Object)
      );
    });
  });

  it('supports different report types', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/report type/i)).toBeInTheDocument();
    });
    
    // Available report types
    expect(screen.getByText(/profit.*loss/i)).toBeInTheDocument();
    expect(screen.getByText(/expense analysis/i)).toBeInTheDocument();
    expect(screen.getByText(/cash flow/i)).toBeInTheDocument();
    expect(screen.getByText(/vendor spending/i)).toBeInTheDocument();
    
    // Select expense analysis
    const reportTypeSelect = screen.getByRole('combobox', { name: /report type/i });
    await user.selectOptions(reportTypeSelect, 'expense_analysis');
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('type=expense_analysis'),
        expect.any(Object)
      );
    });
  });

  it('displays available export formats', async () => {
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/export formats/i)).toBeInTheDocument();
    });
    
    // Check available formats
    expect(screen.getByText(/quickbooks desktop/i)).toBeInTheDocument();
    expect(screen.getByText(/quickbooks online/i)).toBeInTheDocument();
    expect(screen.getByText(/xero/i)).toBeInTheDocument();
    expect(screen.getByText(/excel.*xlsx/i)).toBeInTheDocument();
    
    // Check availability indicators
    expect(screen.getAllByText(/□.*available/i)).toHaveLength(5); // 5 available formats
  });

  it('shows unavailable export formats with reasons', async () => {
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/sage 50/i)).toBeInTheDocument();
    });
    
    // Should show unavailable format with reason
    expect(screen.getByText(/missing gl codes/i)).toBeInTheDocument();
    
    // Export button should be disabled for unavailable format
    const sageExportButton = screen.getByRole('button', { name: /export.*sage/i });
    expect(sageExportButton).toBeDisabled();
  });

  it('initiates export process for available formats', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/quickbooks desktop/i)).toBeInTheDocument();
    });
    
    // Mock export initiation
    mockFetch({
      export_id: 'export-123',
      status: 'generating',
      estimated_time: 30,
    });
    
    const exportButton = screen.getByRole('button', { name: /export.*quickbooks desktop/i });
    await user.click(exportButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/exports/download'),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('quickbooks_desktop'),
        })
      );
    });
  });

  it('shows export progress and completion', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/excel.*xlsx/i)).toBeInTheDocument();
    });
    
    // Mock export in progress
    mockFetch({
      export_id: 'export-456',
      status: 'generating',
      progress: 60,
      estimated_time: 15,
    });
    
    const excelExportButton = screen.getByRole('button', { name: /export.*excel/i });
    await user.click(excelExportButton);
    
    await waitFor(() => {
      expect(screen.getByText(/generating export/i)).toBeInTheDocument();
      expect(screen.getByText(/60%/i)).toBeInTheDocument();
      expect(screen.getByText(/15.*seconds/i)).toBeInTheDocument();
    });
  });

  it('provides download link when export completes', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    // Mock completed export
    mockFetch({
      export_id: 'export-789',
      status: 'completed',
      download_url: '/api/v1/exports/download/export-789',
      filename: 'financial_report_2024.xlsx',
      file_size: '2.4 MB',
    });
    
    const csvExportButton = screen.getByRole('button', { name: /export.*csv/i });
    await user.click(csvExportButton);
    
    await waitFor(() => {
      expect(screen.getByText(/export complete/i)).toBeInTheDocument();
      expect(screen.getByText(/financial_report_2024\.xlsx/i)).toBeInTheDocument();
      expect(screen.getByText(/2\.4 MB/i)).toBeInTheDocument();
    });
    
    const downloadLink = screen.getByRole('link', { name: /download/i });
    expect(downloadLink).toHaveAttribute('href', '/api/v1/exports/download/export-789');
  });

  it('supports custom date range selection', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/time period/i)).toBeInTheDocument();
    });
    
    // Select custom date range
    const periodSelect = screen.getByRole('combobox', { name: /time period/i });
    await user.selectOptions(periodSelect, 'custom');
    
    // Should show date picker inputs
    await waitFor(() => {
      expect(screen.getByLabelText(/start date/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/end date/i)).toBeInTheDocument();
    });
    
    // Set custom dates
    const startDateInput = screen.getByLabelText(/start date/i);
    const endDateInput = screen.getByLabelText(/end date/i);
    
    await user.clear(startDateInput);
    await user.type(startDateInput, '2024-01-01');
    await user.clear(endDateInput);
    await user.type(endDateInput, '2024-03-31');
    
    const applyButton = screen.getByRole('button', { name: /apply/i });
    await user.click(applyButton);
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('start_date=2024-01-01&end_date=2024-03-31'),
        expect.any(Object)
      );
    });
  });

  it('handles empty data state', async () => {
    mockFetch({
      totalIncome: 0,
      totalExpenses: 0,
      netProfit: 0,
      transactionCount: 0,
      categoryBreakdown: [],
      monthlyTrends: [],
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/no data available/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/no transactions.*time period/i)).toBeInTheDocument();
    expect(screen.getByText(/upload transactions.*generate reports/i)).toBeInTheDocument();
  });

  it('displays export history and re-download options', async () => {
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
      exportHistory: [
        {
          id: 'export-001',
          format: 'Excel (XLSX)',
          created_at: '2024-01-15T10:30:00Z',
          filename: 'financial_report_q1.xlsx',
          download_url: '/api/v1/exports/download/export-001',
        },
        {
          id: 'export-002', 
          format: 'QuickBooks Online (CSV)',
          created_at: '2024-01-10T14:20:00Z',
          filename: 'quickbooks_export.csv',
          download_url: '/api/v1/exports/download/export-002',
        },
      ],
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/export history/i)).toBeInTheDocument();
    });
    
    // Check export history items
    expect(screen.getByText(/financial_report_q1\.xlsx/i)).toBeInTheDocument();
    expect(screen.getByText(/quickbooks_export\.csv/i)).toBeInTheDocument();
    expect(screen.getByText(/jan 15.*10:30/i)).toBeInTheDocument();
    expect(screen.getByText(/jan 10.*14:20/i)).toBeInTheDocument();
    
    // Should have re-download links
    expect(screen.getAllByRole('link', { name: /re-download/i })).toHaveLength(2);
  });

  it('handles API errors gracefully', async () => {
    // Mock API error
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/unable to load report data/i)).toBeInTheDocument();
    });
    
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
  });

  it('supports refreshing report data', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      ...mockReportData,
      exportFormats: mockExportFormats,
    });
    
    render(<ReportsPage />);
    
    await waitFor(() => {
      expect(screen.getByText(/financial reports/i)).toBeInTheDocument();
    });
    
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    await user.click(refreshButton);
    
    // Should make fresh API calls
    expect(global.fetch).toHaveBeenCalledTimes(2); // Initial load + refresh
  });
});