/**
 * Reports Page - Professional B2B Financial Reports
 * Matches wireframe: docs/wireframes/03-daily-use-journey/05-reports.md
 * Brand consistency: Professional Excel-inspired design with #295343 green
 */
import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useWebSocket, useWebSocketStatus } from '@/shared/services/websocket/WebSocketService';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Badge } from '@/shared/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  generateReport,
  getFinancialSummary,
  getFinancialSummaryComparison,
  getCategoryBreakdown,
  getDateRangePresets,
  type ReportSummary,
  type CategoryBreakdown,
} from '../services/reportService';
import { exportTransactions } from '@/features/transactions/services/exportService';
import { fetchTransactions } from '@/features/transactions/services/transactionService';
import { useToast } from '@/shared/components/ui/use-toast';
import { ExportValidationSystem } from '../components/ExportValidationSystem';
import { gradientButtonStyle, applyGradientHover } from '@/shared/utils/brandGradients';
import '@/styles/layout-variables.css';

import {
  BarChart3,
  PieChart,
  TrendingUp,
  Download,
  Calendar,
  FileText,
  Filter,
  Search,
  Plus,
  Eye,
  Settings,
} from 'lucide-react';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'financial' | 'operational' | 'compliance';
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  format: 'excel' | 'pdf' | 'csv';
  lastGenerated?: Date;
  isScheduled: boolean;
}

interface QuickMetric {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'flat';
  color: string;
}

interface CategorySpend {
  category: string;
  amount: number;
  percentage: number;
  transactions: number;
  budget?: number;
}

const ReportsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const { toast } = useToast();

  // State management
  const [selectedPeriod, setSelectedPeriod] = useState('this_month');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [reportTemplates, setReportTemplates] = useState<ReportTemplate[]>([]);
  const [quickMetrics, setQuickMetrics] = useState<QuickMetric[]>([]);
  const [periodComparison, setPeriodComparison] = useState<{
    changes: {
      totalExpensesChange: string;
      totalIncomeChange: string;
      netIncomeChange: string;
    };
  } | null>(null);
  const [categorySpending, setCategorySpending] = useState<CategorySpend[]>([]);
  const [showExportSection, setShowExportSection] = useState<boolean>(searchParams.get('export') === 'true');
  const [dateRange, setDateRange] = useState(() => {
    const presets = getDateRangePresets();
    return presets['This Month'];
  });

  // WebSocket connection status
  const { connected: isWebSocketConnected } = useWebSocketStatus();

  // Real-time WebSocket event handlers for report updates
  useWebSocket('report.ready', (payload: any) => {
    // Update report templates when new reports are generated
    setReportTemplates(prev => {
      const updatedTemplate = prev.find(t => t.id === payload.template_id);
      if (updatedTemplate) {
        return prev.map(t => 
          t.id === payload.template_id 
            ? { ...t, lastGenerated: new Date(), status: 'ready' }
            : t
        );
      }
      return prev;
    });

    toast({
      title: 'Report Ready',
      description: `${payload.report_name || 'Report'} has been generated and is ready for download.`,
    });
  }, [toast]);

  useWebSocket('transaction.categorized', (payload: any) => {
    // Refresh financial data when transactions are categorized
    // Debounced to prevent excessive updates
    const refreshData = () => {
      // Only refresh if the transaction affects current date range
      const transactionDate = new Date(payload.date || payload.transaction_date);
      const rangeStart = new Date(dateRange.start_date);
      const rangeEnd = new Date(dateRange.end_date);
      
      if (transactionDate >= rangeStart && transactionDate <= rangeEnd) {
        // Update quick metrics incrementally
        setQuickMetrics(prev => {
          return prev.map(metric => {
            if (metric.label === 'Total Expenses' && payload.category_type === 'expense') {
              const amount = parseFloat(metric.value.replace(/[^0-9.-]/g, ''));
              const newAmount = amount + Math.abs(payload.amount || 0);
              return {
                ...metric,
                value: `$${newAmount.toLocaleString()}`,
              };
            }
            if (metric.label === 'Total Income' && payload.category_type === 'income') {
              const amount = parseFloat(metric.value.replace(/[^0-9.-]/g, ''));
              const newAmount = amount + Math.abs(payload.amount || 0);
              return {
                ...metric,
                value: `$${newAmount.toLocaleString()}`,
              };
            }
            return metric;
          });
        });

        // Update category spending
        if (payload.category_name) {
          setCategorySpending(prev => {
            const existing = prev.find(cat => cat.category === payload.category_name);
            if (existing) {
              return prev.map(cat => 
                cat.category === payload.category_name
                  ? { 
                      ...cat, 
                      amount: cat.amount + Math.abs(payload.amount || 0),
                      transactions: cat.transactions + 1 
                    }
                  : cat
              );
            }
            return prev;
          });
        }
      }
    };

    // Debounce the refresh to avoid excessive updates
    const timeoutId = setTimeout(refreshData, 500);
    return () => clearTimeout(timeoutId);
  }, [dateRange]);

  useWebSocket('file.processed', (payload: any) => {
    // Refresh reports data when new files are processed
    if (payload.status === 'completed') {
      // Trigger a silent reload of reports data
      setTimeout(() => {
        const loadData = async () => {
          try {
            const summaryResult = await getFinancialSummary(dateRange.start_date, dateRange.end_date);
            if (!('error' in summaryResult) && summaryResult) {
              const summary = summaryResult as ReportSummary;
              // Update quick metrics with fresh data
              const metrics: QuickMetric[] = [
                {
                  label: 'Total Expenses',
                  value: `$${(summary.total_expenses || 0).toLocaleString()}`,
                  change: '+12% vs last month',
                  trend: 'up',
                  color: 'hsl(var(--destructive))',
                },
                {
                  label: 'Total Income',
                  value: `$${(summary.total_income || 0).toLocaleString()}`,
                  change: '+8% vs last month',
                  trend: 'up',
                  color: 'hsl(var(--giki-primary))',
                },
                {
                  label: 'Net Result',
                  value: `$${(summary.net_income || 0).toLocaleString()}`,
                  change: (summary.net_income || 0) >= 0 ? 'Profit' : 'Loss',
                  trend: (summary.net_income || 0) >= 0 ? 'up' : 'down',
                  color: (summary.net_income || 0) >= 0 ? 'hsl(var(--giki-primary))' : 'hsl(var(--destructive))',
                },
              ];
              setQuickMetrics(metrics);
            }
          } catch (error) {
            console.error('Error refreshing reports data:', error);
          }
        };
        loadData();
      }, 1000); // Wait 1 second to ensure processing is complete
    }
  }, [dateRange]);

  // Load real data from backend
  useEffect(() => {
    const loadReportsData = async () => {
      try {
        setIsLoading(true);

        // Load financial summary and previous period comparison for quick metrics
        const [summaryResult, comparisonResult] = await Promise.all([
          getFinancialSummary(dateRange.start_date, dateRange.end_date),
          getFinancialSummaryComparison(dateRange.start_date, dateRange.end_date),
        ]);

        if (!('error' in summaryResult) && summaryResult) {
          const summary = summaryResult as ReportSummary;
          
          // Handle comparison data
          let comparison = null;
          if (!('error' in comparisonResult) && comparisonResult) {
            comparison = comparisonResult;
            setPeriodComparison(comparison);
          }

          const metrics: QuickMetric[] = [
            {
              label: 'Total Expenses',
              value: `$${(summary.total_expenses || 0).toLocaleString()}`,
              change: comparison?.changes.totalExpensesChange || '0%',
              trend: comparison?.changes.totalExpensesChange ? 
                     (comparison.changes.totalExpensesChange.startsWith('+') ? 'up' : 
                      comparison.changes.totalExpensesChange.startsWith('-') ? 'down' : 'flat') : 'flat',
              color: 'hsl(var(--giki-primary))',
            },
            {
              label: 'Total Income',
              value: `$${(summary.total_income || 0).toLocaleString()}`,
              change: comparison?.changes.totalIncomeChange || `${summary.transaction_count || 0} transactions`,
              trend: comparison?.changes.totalIncomeChange ? 
                     (comparison.changes.totalIncomeChange.startsWith('+') ? 'up' : 
                      comparison.changes.totalIncomeChange.startsWith('-') ? 'down' : 'flat') : 'up',
              color: 'hsl(var(--giki-primary))',
            },
            {
              label: 'Net Result',
              value: `$${(summary.net_income || 0).toLocaleString()}`,
              change: comparison?.changes.netIncomeChange || ((summary.net_income || 0) >= 0 ? 'Profit' : 'Loss'),
              trend: comparison?.changes.netIncomeChange ? 
                     (comparison.changes.netIncomeChange.startsWith('+') ? 'up' : 
                      comparison.changes.netIncomeChange.startsWith('-') ? 'down' : 'flat') :
                     ((summary.net_income || 0) >= 0 ? 'up' : 'down'),
              color: (summary.net_income || 0) >= 0 ? 'hsl(var(--giki-primary))' : 'hsl(var(--destructive))',
            },
            {
              label: 'Report Formats',
              value: '3',
              change: 'Excel, PDF, CSV',
              trend: 'flat',
              color: 'hsl(var(--muted-foreground))',
            },
          ];
          setQuickMetrics(metrics);
        }

        // Load category breakdown
        const categoryResult = await getCategoryBreakdown(
          dateRange.start_date,
          dateRange.end_date,
          'expense',
        );

        if (!('error' in categoryResult)) {
          const categories = categoryResult as CategoryBreakdown[];
          // Ensure categories is an array before mapping
          if (Array.isArray(categories)) {
            const categoryData: CategorySpend[] = categories.map(
              (cat, index) => ({
                category: cat.category,
                amount: cat.amount,
                percentage: cat.percentage,
                transactions: cat.transaction_count,
                budget: index < 3 ? cat.amount * 1.2 : undefined, // Mock budget for top categories
              }),
            );
            setCategorySpending(categoryData);
          } else {
            console.warn('Categories data is not an array:', categories);
            setCategorySpending([]);
          }
        }

        // Set up report templates (these could come from backend in future)
        const templates: ReportTemplate[] = [
          {
            id: 'monthly_pl',
            name: 'Monthly P&L Statement',
            description: 'Profit & Loss breakdown by category',
            category: 'financial',
            frequency: 'monthly',
            format: 'excel',
            lastGenerated: new Date(),
            isScheduled: true,
          },
          {
            id: 'expense_analysis',
            name: 'Expense Analysis by Category',
            description: 'Detailed spending breakdown and trends',
            category: 'operational',
            frequency: 'weekly',
            format: 'pdf',
            lastGenerated: new Date(),
            isScheduled: false,
          },
          {
            id: 'budget_vs_actual',
            name: 'Budget vs Actual Report',
            description: 'Compare actual spending against budgets',
            category: 'financial',
            frequency: 'monthly',
            format: 'excel',
            lastGenerated: new Date(),
            isScheduled: true,
          },
          {
            id: 'tax_export',
            name: 'Tax Preparation Export',
            description: 'Transaction export formatted for tax filing',
            category: 'compliance',
            frequency: 'quarterly',
            format: 'csv',
            isScheduled: false,
          },
          {
            id: 'vendor_analysis',
            name: 'Vendor Spending Analysis',
            description: 'Top vendors and payment patterns',
            category: 'operational',
            frequency: 'monthly',
            format: 'pdf',
            lastGenerated: new Date(),
            isScheduled: false,
          },
        ];
        setReportTemplates(templates);
      } catch (error) {
        console.error('Error loading reports data:', error);
        toast({
          title: 'Error Loading Reports',
          description: 'Failed to load reports data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    void loadReportsData();
  }, [dateRange, toast]);

  // Update date range when period changes
  useEffect(() => {
    const presets = getDateRangePresets();
    const newDateRange =
      presets[
        selectedPeriod === 'this_month'
          ? 'This Month'
          : selectedPeriod === 'last_month'
            ? 'Last Month'
            : selectedPeriod === 'this_quarter'
              ? 'This Quarter'
              : 'This Year'
      ];
    if (newDateRange) {
      setDateRange(newDateRange);
    }
  }, [selectedPeriod]);

  // Generate report handler
  const handleExportComplete = useCallback((result: unknown) => {
    toast({
      title: 'Export Successful',
      description: 'Your data has been exported successfully.',
    });
    // Additional handling logic here
  }, [toast]);

  const handleGenerateReport = useCallback(
    async (templateId: string) => {
      try {
        setIsGenerating(true);

        const template = reportTemplates.find((t) => t.id === templateId);
        if (!template) {
          throw new Error('Report template not found');
        }

        // Generate report using real backend service
        const reportRequest = {
          report_type:
            template.category === 'financial'
              ? 'income_statement'
              : ('custom' as const),
          start_date: dateRange.start_date,
          end_date: dateRange.end_date,
          format:
            template.format === 'excel'
              ? 'json'
              : (template.format as 'json' | 'csv' | 'pdf'),
        };

        const result = await generateReport(reportRequest);

        if ('error' in result) {
          throw new Error(result.error as string);
        }

        // If report generation started successfully
        toast({
          title: 'Report Generation Started',
          description: `${template.name} is being generated. You'll be notified when it's ready.`,
        });

        // Update template last generated time
        setReportTemplates((prev) =>
          prev.map((t) =>
            t.id === templateId ? { ...t, lastGenerated: new Date() } : t,
          ),
        );
      } catch (error) {
        console.error('Error generating report:', error);
        toast({
          title: 'Report Generation Failed',
          description:
            error instanceof Error
              ? error.message
              : 'Failed to generate report',
          variant: 'destructive',
        });
      } finally {
        setIsGenerating(false);
      }
    },
    [reportTemplates, dateRange, toast],
  );

  // Export handlers
  const handleExportExcel = useCallback(async () => {
    try {
      setIsGenerating(true);

      // Get current transactions for export
      const transactions = await fetchTransactions({
        startDate: dateRange.start_date,
        endDate: dateRange.end_date,
        pageSize: 1000, // Get more transactions for export
      });

      if ('items' in transactions) {
        await exportTransactions(transactions.items, {
          format: 'excel',
          fileName: `financial-report-${selectedPeriod}.xlsx`,
          includeSummary: true,
          includeMetadata: true,
        });

        toast({
          title: 'Excel Export Complete',
          description: 'Your financial report has been downloaded.',
        });
      }
    } catch (error) {
      console.error('Excel export error:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export Excel file. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  }, [dateRange, selectedPeriod, toast]);

  const handleExportPDF = useCallback(async () => {
    try {
      setIsGenerating(true);

      // Get current transactions for export
      const transactions = await fetchTransactions({
        startDate: dateRange.start_date,
        endDate: dateRange.end_date,
        pageSize: 1000, // Get more transactions for export
      });

      if ('items' in transactions) {
        await exportTransactions(transactions.items, {
          format: 'pdf',
          fileName: `financial-report-${selectedPeriod}.pdf`,
          includeSummary: true,
          includeMetadata: true,
        });

        toast({
          title: 'PDF Export Complete',
          description: 'Your financial report has been downloaded.',
        });
      }
    } catch (error) {
      console.error('PDF export error:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export PDF file. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  }, [dateRange, selectedPeriod, toast]);

  // Filter templates based on search and category
  const filteredTemplates = reportTemplates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory =
      selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div
            className="w-8 h-8 border-4 border-t-transparent rounded-full animate-spin mx-auto mb-4"
            style={{ borderColor: 'hsl(var(--giki-primary))', borderTopColor: 'transparent' }}
          />
          <p className="text-gray-600">Loading financial reports...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-page min-h-screen" style={{ backgroundColor: 'var(--giki-bg-primary)' }}>
      {/* Enhanced Header with Better Visual Hierarchy */}
      <div className="space-section">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-semibold text-gray-700 space-component">
              Financial Reports & Analytics
            </h1>
            <p className="text-gray-500 text-base">
              AI-powered reporting and analytics • Generate detailed reports and export data for analysis
            </p>
          </div>
          <Button
            onClick={() => navigate('/reports/builder')}
            className="text-white"
            style={gradientButtonStyle}
            onMouseEnter={(e) => applyGradientHover(e.currentTarget, true)}
            onMouseLeave={(e) => applyGradientHover(e.currentTarget, false)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Custom Report
          </Button>
        </div>
      </div>

      <div>
        {/* Quick Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-element space-section">
          {quickMetrics.map((metric, index) => (
            <Card key={index} className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardContent className="p-card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 mb-1">{metric.label}</p>
                    <p className="text-xl font-bold text-gray-800">
                      {metric.value}
                    </p>
                    <p className="text-sm text-gray-500">{metric.change}</p>
                  </div>
                  <div
                    className="w-2 h-12 rounded-full"
                    style={{ backgroundColor: metric.color }}
                  ></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters */}
        <Card className="space-section border border-gray-200 shadow-sm">
          <CardContent className="p-card">
            <div className="flex flex-col sm:flex-row gap-element items-center">
              <div className="flex gap-3">
                <Select
                  value={selectedPeriod}
                  onValueChange={setSelectedPeriod}
                >
                  <SelectTrigger className="w-40">
                    <Calendar className="w-4 h-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="this_month">This Month</SelectItem>
                    <SelectItem value="last_month">Last Month</SelectItem>
                    <SelectItem value="this_quarter">This Quarter</SelectItem>
                    <SelectItem value="this_year">This Year</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={selectedCategory}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger className="w-36">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="financial">Financial</SelectItem>
                    <SelectItem value="operational">Operational</SelectItem>
                    <SelectItem value="compliance">Compliance</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search reports..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => void handleExportExcel()}
                  disabled={isGenerating}
                >
                  <Download className="w-4 h-4 mr-2" />
                  {isGenerating ? 'Exporting...' : 'Excel'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => void handleExportPDF()}
                  disabled={isGenerating}
                >
                  <Download className="w-4 h-4 mr-2" />
                  {isGenerating ? 'Exporting...' : 'PDF'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-element space-section">
          {/* Report Templates */}
          <div className="lg:col-span-2">
            <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardHeader className="p-section">
                <CardTitle className="flex items-center gap-inline text-gray-700">
                  <FileText className="w-5 h-5" />
                  Report Templates
                </CardTitle>
              </CardHeader>
              <CardContent className="p-section">
                <div className="space-y-4">
                  {filteredTemplates.map((template) => (
                    <div
                      key={template.id}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-gray-800">
                              {template.name}
                            </h3>
                            {template.isScheduled && (
                              <Badge className="bg-green-100 text-green-800 border-green-200">
                                Scheduled
                              </Badge>
                            )}
                            <Badge variant="outline" className="text-xs">
                              {template.frequency}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            {template.description}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>Format: {template.format.toUpperCase()}</span>
                            {template.lastGenerated && (
                              <span>
                                Last:{' '}
                                {template.lastGenerated.toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/reports/${template.id}`)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleGenerateReport(template.id)}
                            disabled={isGenerating}
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Settings className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Category Spending Analysis */}
          <div>
            <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardHeader className="p-section">
                <CardTitle className="flex items-center gap-inline text-gray-700">
                  <PieChart className="w-5 h-5" />
                  Category Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent className="p-section">
                <div className="space-y-3">
                  {categorySpending.map((category, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">
                          {category.category}
                        </span>
                        <span className="text-sm font-mono text-gray-800">
                          ${category.amount.toLocaleString()}
                        </span>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 rounded-full"
                          style={{
                            width: `${category.percentage}%`,
                            backgroundColor: 'hsl(var(--giki-primary))',
                          }}
                        ></div>
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>
                          {category.percentage}% • {category.transactions}{' '}
                          transactions
                        </span>
                        {category.budget && (
                          <span
                            className={
                              category.amount > category.budget
                                ? 'text-error'
                                : 'text-success'
                            }
                          >
                            Budget: ${category.budget.toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="space-element border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardHeader className="p-section">
                <CardTitle className="flex items-center gap-inline text-gray-700">
                  <TrendingUp className="w-5 h-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="p-section">
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => navigate('/reports/monthly-summary')}
                  >
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Monthly Summary
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => navigate('/reports/expense-trends')}
                  >
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Expense Trends
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => navigate('/reports/tax-export')}
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Tax Export
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => navigate('/reports/budget-analysis')}
                  >
                    <PieChart className="w-4 h-4 mr-2" />
                    Budget Analysis
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start bg-giki-primary-light hover:bg-giki-primary-light/80"
                    onClick={() => {
                      // Programmatically switch to export tab
                      const exportTabElement = document.querySelector('[value="export"]') as HTMLElement;
                      if (exportTabElement) {
                        exportTabElement.click();
                      }
                    }}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export to Accounting Software
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main Content Area with Tabs */}
        <div className="space-section">
          <Tabs defaultValue={showExportSection ? "export" : "templates"} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="templates">Report Templates</TabsTrigger>
              <TabsTrigger value="export">Export to Accounting</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="templates" className="space-y-section">
              {/* Existing report templates content - moved from above */}
              <div className="grid gap-grid md:grid-cols-2 lg:grid-cols-3">
                {filteredTemplates.map((template) => (
                  <Card
                    key={template.id}
                    className="border border-gray-200 hover:border-gray-300 transition-colors cursor-pointer"
                    onClick={() => handleGenerateReport(template)}
                  >
                    <CardHeader className="p-section">
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-lg text-gray-700">
                            {template.name}
                          </CardTitle>
                          <p className="text-sm text-gray-600 mt-inline">
                            {template.description}
                          </p>
                        </div>
                        <Badge variant="outline" className="capitalize">
                          {template.format}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="p-section pt-0">
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>{template.frequency}</span>
                        {template.lastGenerated && (
                          <span>
                            Last: {new Date(template.lastGenerated).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      {template.isScheduled && (
                        <Badge variant="secondary" className="mt-2">
                          <Calendar className="w-3 h-3 mr-1" />
                          Scheduled
                        </Badge>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="export" className="space-y-section">
              <Card className="border border-gray-200 shadow-sm">
                <CardHeader className="p-section">
                  <CardTitle className="flex items-center gap-inline text-gray-700">
                    <Download className="w-5 h-5" />
                    Export to Accounting Software
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-section">
                  <ExportValidationSystem 
                    onExportComplete={handleExportComplete}
                    complianceMode={true}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="analytics" className="space-y-section">
              {/* Analytics content */}
              <Card className="border border-gray-200 shadow-sm">
                <CardHeader className="p-section">
                  <CardTitle className="flex items-center gap-inline text-gray-700">
                    <BarChart3 className="w-5 h-5" />
                    Financial Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-section">
                  <p className="text-gray-600">
                    Advanced analytics and insights coming soon...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Professional Tip */}
        <div className="space-section text-center">
          <p className="text-sm text-gray-600">
            ⚬ Tip: Schedule regular reports to automate your financial analysis
            workflow
          </p>
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;
