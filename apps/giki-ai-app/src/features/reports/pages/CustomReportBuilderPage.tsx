/**
 * Custom Report Builder Page
 * Professional custom financial report builder with validation
 */
import React from 'react';
import { CustomReportBuilder } from '../components/CustomReportBuilder';
import { ExportValidationSystem } from '../components/ExportValidationSystem';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';

export const CustomReportBuilderPage: React.FC = () => {
  return (
    <div className="p-6 space-y-6 bg-[var(--giki-bg-primary)] min-h-screen">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-[var(--giki-text-primary)] mb-2">
            Custom Report Builder
          </h1>
          <p className="text-[var(--giki-text-muted)]">
            Build professional financial reports with advanced customization and export validation
          </p>
        </div>
        
        <Tabs defaultValue="builder" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="builder">Report Builder</TabsTrigger>
            <TabsTrigger value="validation">Export Validation</TabsTrigger>
          </TabsList>
          
          <TabsContent value="builder">
            <CustomReportBuilder />
          </TabsContent>
          
          <TabsContent value="validation">
            <ExportValidationSystem />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CustomReportBuilderPage;
