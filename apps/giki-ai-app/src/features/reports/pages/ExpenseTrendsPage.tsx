/**
 * Expense Trends Report Page
 * Professional B2B expense analysis with trend visualization
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { useToast } from '@/shared/components/ui/use-toast';
import {
  getFinancialSummary,
  getCategoryBreakdown,
} from '../services/reportService';
import { exportTransactions } from '@/features/transactions/services/exportService';
import { fetchTransactions } from '@/features/transactions/services/transactionService';
import {
  ArrowLeft,
  Download,
  TrendingUp,
  TrendingDown,
  Calendar,
  AlertCircle,
  BarChart3,
  Activity,
} from 'lucide-react';

interface TrendData {
  month: string;
  amount: number;
  count: number;
  categories: { [key: string]: number };
}

interface CategoryTrend {
  name: string;
  current: number;
  previous: number;
  change: number;
  trend: 'up' | 'down' | 'flat';
  sparkline: number[];
}

interface ExpenseAlert {
  type: 'warning' | 'info' | 'success';
  message: string;
  category?: string;
  amount?: number;
}

const ExpenseTrendsPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // State
  const [timeRange, setTimeRange] = useState('6months');
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [monthlyTrends, setMonthlyTrends] = useState<TrendData[]>([]);
  const [categoryTrends, setCategoryTrends] = useState<CategoryTrend[]>([]);
  const [alerts, setAlerts] = useState<ExpenseAlert[]>([]);
  const [totalExpense, setTotalExpense] = useState(0);
  const [avgMonthlyExpense, setAvgMonthlyExpense] = useState(0);

  // Load trend data
  useEffect(() => {
    const loadTrendData = async () => {
      try {
        setIsLoading(true);

        // Calculate date range based on selection
        const endDate = new Date();
        const startDate = new Date();
        const monthsBack =
          timeRange === '3months' ? 3 : timeRange === '6months' ? 6 : 12;
        startDate.setMonth(startDate.getMonth() - monthsBack);

        const formatDate = (date: Date) => date.toISOString().split('T')[0];

        // Initialize monthly data structure
        const monthlyData: TrendData[] = [];
        const categoryAggregates: { [key: string]: number[] } = {};

        // Load data for each month
        for (let i = 0; i < monthsBack; i++) {
          const monthEnd = new Date(
            endDate.getFullYear(),
            endDate.getMonth() - i,
            0,
          );
          const monthStart = new Date(
            endDate.getFullYear(),
            endDate.getMonth() - i - 1,
            1,
          );

          // Get financial summary for the month
          const summary = await getFinancialSummary(
            formatDate(monthStart),
            formatDate(monthEnd),
          );

          if (!('error' in summary)) {
            // Get category breakdown for the month
            const categories = await getCategoryBreakdown(
              formatDate(monthStart),
              formatDate(monthEnd),
              'expense',
            );

            const categoryData: { [key: string]: number } = {};
            if (!('error' in categories) && Array.isArray(categories)) {
              categories.forEach((cat) => {
                categoryData[cat.category] = Math.abs(cat.amount);

                // Track for sparklines
                if (!categoryAggregates[cat.category]) {
                  categoryAggregates[cat.category] = new Array(monthsBack).fill(
                    0,
                  );
                }
                categoryAggregates[cat.category][i] = Math.abs(cat.amount);
              });
            }

            monthlyData.unshift({
              month: monthStart.toLocaleDateString('en-US', {
                month: 'short',
                year: 'numeric',
              }),
              amount: Math.abs(summary.total_expenses),
              count: summary.expense_transaction_count || 0,
              categories: categoryData,
            });
          }
        }

        setMonthlyTrends(monthlyData);

        // Calculate total and average
        const total = monthlyData.reduce((sum, month) => sum + month.amount, 0);
        const calculatedAvgMonthlyExpense = total / monthsBack;
        setTotalExpense(total);
        setAvgMonthlyExpense(calculatedAvgMonthlyExpense);

        // Calculate category trends
        const trends: CategoryTrend[] = [];
        const currentMonth = monthlyData[monthlyData.length - 1];
        const previousMonth = monthlyData[monthlyData.length - 2];

        if (currentMonth && previousMonth) {
          const allCategories = new Set([
            ...Object.keys(currentMonth.categories),
            ...Object.keys(previousMonth.categories),
          ]);

          allCategories.forEach((category) => {
            const current = currentMonth.categories[category] || 0;
            const previous = previousMonth.categories[category] || 0;
            const change =
              previous > 0 ? ((current - previous) / previous) * 100 : 0;

            trends.push({
              name: category,
              current,
              previous,
              change,
              trend: change > 5 ? 'up' : change < -5 ? 'down' : 'flat',
              sparkline: categoryAggregates[category] || [],
            });
          });

          // Sort by current amount
          trends.sort((a, b) => b.current - a.current);
          setCategoryTrends(trends.slice(0, 8)); // Top 8 categories
        }

        // Generate alerts
        const expenseAlerts: ExpenseAlert[] = [];

        // Check for rapid increases
        trends.forEach((trend) => {
          if (trend.change > 20 && trend.current > 1000) {
            expenseAlerts.push({
              type: 'warning',
              message: `${trend.name} expenses increased by ${trend.change.toFixed(1)}%`,
              category: trend.name,
              amount: trend.current,
            });
          }
        });

        // Check for high spending categories
        const highSpendingThreshold = calculatedAvgMonthlyExpense * 0.3; // 30% of average
        trends.forEach((trend) => {
          if (trend.current > highSpendingThreshold) {
            expenseAlerts.push({
              type: 'info',
              message: `${trend.name} represents ${((trend.current / (currentMonth?.amount || 1)) * 100).toFixed(1)}% of total expenses`,
              category: trend.name,
              amount: trend.current,
            });
          }
        });

        // Add positive alerts for decreases
        trends.forEach((trend) => {
          if (trend.change < -10 && trend.previous > 1000) {
            expenseAlerts.push({
              type: 'success',
              message: `${trend.name} expenses decreased by ${Math.abs(trend.change).toFixed(1)}%`,
              category: trend.name,
              amount: trend.current,
            });
          }
        });

        setAlerts(expenseAlerts.slice(0, 5)); // Top 5 alerts
      } catch (error) {
        console.error('Error loading trend data:', error);
        toast({
          title: 'Error Loading Trends',
          description: 'Failed to load expense trends. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    void loadTrendData();
  }, [timeRange, toast]);

  // Export handler
  const handleExportExcel = async () => {
    try {
      setIsExporting(true);

      const endDate = new Date();
      const startDate = new Date();
      const monthsBack =
        timeRange === '3months' ? 3 : timeRange === '6months' ? 6 : 12;
      startDate.setMonth(startDate.getMonth() - monthsBack);

      // Get transactions for export
      const transactions = await fetchTransactions({
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        pageSize: 10000,
      });

      if ('items' in transactions) {
        await exportTransactions(transactions.items, {
          format: 'excel',
          fileName: `expense-trends-${timeRange}.xlsx`,
          includeSummary: true,
          includeMetadata: true,
        });

        toast({
          title: 'Export Complete',
          description: 'Expense trends report has been downloaded.',
        });
      }
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export expense trends.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Calculate max value for chart scaling
  const maxMonthlyAmount = Math.max(...monthlyTrends.map((m) => m.amount), 1);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div
            className="w-8 h-8 border-4 border-t-transparent rounded-full animate-spin mx-auto mb-4"
            style={{ borderColor: 'var(--giki-primary)', borderTopColor: 'transparent' }}
          />
          <p className="text-gray-600">Analyzing expense trends...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/reports')}
                className="p-2"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">
                  Expense Trends
                </h1>
                <p className="text-gray-600">
                  Analyze spending patterns over time
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-40">
                  <Calendar className="w-4 h-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3months">Last 3 months</SelectItem>
                  <SelectItem value="6months">Last 6 months</SelectItem>
                  <SelectItem value="12months">Last 12 months</SelectItem>
                </SelectContent>
              </Select>
              <Button
                onClick={() => void handleExportExcel()}
                disabled={isExporting}
                className="bg-brand-primary hover:bg-brand-primary-hover text-white"
              >
                <Download className="w-4 h-4 mr-2" />
                {isExporting ? 'Exporting...' : 'Export'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="border border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-gray-600">
                  Total Expenses
                </p>
                <BarChart3 className="w-5 h-5 text-gray-400" />
              </div>
              <p className="text-2xl font-bold text-gray-800">
                ${totalExpense.toLocaleString()}
              </p>
              <p className="text-sm text-gray-500">
                Over {timeRange.replace('months', ' months')}
              </p>
            </CardContent>
          </Card>

          <Card className="border border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-gray-600">
                  Monthly Average
                </p>
                <Activity className="w-5 h-5 text-gray-400" />
              </div>
              <p className="text-2xl font-bold text-gray-800">
                ${avgMonthlyExpense.toLocaleString()}
              </p>
              <p className="text-sm text-gray-500">Per month</p>
            </CardContent>
          </Card>

          <Card className="border border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-gray-600">Trend</p>
                {monthlyTrends.length >= 2 &&
                monthlyTrends[monthlyTrends.length - 1].amount >
                  monthlyTrends[monthlyTrends.length - 2].amount ? (
                  <TrendingUp className="w-5 h-5 text-error" />
                ) : (
                  <TrendingDown className="w-5 h-5 text-success" />
                )}
              </div>
              <p className="text-2xl font-bold text-gray-800">
                {monthlyTrends.length >= 2 ? (
                  <>
                    {(
                      ((monthlyTrends[monthlyTrends.length - 1].amount -
                        monthlyTrends[monthlyTrends.length - 2].amount) /
                        monthlyTrends[monthlyTrends.length - 2].amount) *
                      100
                    ).toFixed(1)}
                    %
                  </>
                ) : (
                  '0.0%'
                )}
              </p>
              <p className="text-sm text-gray-500">Month over month</p>
            </CardContent>
          </Card>
        </div>

        {/* Alerts */}
        {alerts.length > 0 && (
          <div className="mb-8 space-y-3">
            {alerts.map((alert, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border flex items-start gap-3 ${
                  alert.type === 'warning'
                    ? 'bg-amber-50 border-amber-200'
                    : alert.type === 'success'
                      ? 'bg-green-50 border-green-200'
                      : 'bg-blue-50 border-blue-200'
                }`}
              >
                <AlertCircle
                  className={`w-5 h-5 mt-0.5 ${
                    alert.type === 'warning'
                      ? 'text-amber-600'
                      : alert.type === 'success'
                        ? 'text-success'
                        : 'text-blue-600'
                  }`}
                />
                <div className="flex-1">
                  <p className="font-medium text-gray-800">{alert.message}</p>
                  {alert.amount && (
                    <p className="text-sm text-gray-600 mt-1">
                      Current amount: ${alert.amount.toLocaleString()}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Monthly Trend Chart */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-800">
              <BarChart3 className="w-5 h-5" />
              Monthly Expense Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-end justify-between gap-2">
              {monthlyTrends.map((month, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div className="w-full bg-gray-200 rounded-t relative flex-1 flex items-end">
                    <div
                      className="w-full rounded-t transition-all duration-300"
                      style={{
                        height: `${(month.amount / maxMonthlyAmount) * 100}%`,
                        backgroundColor: 'var(--giki-primary)',
                      }}
                    >
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs font-mono whitespace-nowrap">
                        ${(month.amount / 1000).toFixed(1)}k
                      </div>
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 mt-2">{month.month}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Category Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-800">
              <Activity className="w-5 h-5" />
              Category Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {categoryTrends.map((category, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <p className="font-medium text-gray-800">
                        {category.name}
                      </p>
                      {category.trend === 'up' ? (
                        <TrendingUp className="w-4 h-4 text-error" />
                      ) : category.trend === 'down' ? (
                        <TrendingDown className="w-4 h-4 text-success" />
                      ) : null}
                    </div>
                    <div className="text-right">
                      <p className="font-mono font-semibold text-gray-800">
                        ${category.current.toLocaleString()}
                      </p>
                      <p
                        className={`text-sm ${
                          category.change > 0
                            ? 'text-error'
                            : 'text-success'
                        }`}
                      >
                        {category.change > 0 ? '+' : ''}
                        {category.change.toFixed(1)}%
                      </p>
                    </div>
                  </div>

                  {/* Mini sparkline */}
                  <div className="h-8 flex items-end gap-0.5">
                    {category.sparkline.map((value, i) => (
                      <div
                        key={i}
                        className="flex-1 bg-gray-300 rounded-t"
                        style={{
                          height: `${(value / Math.max(...category.sparkline, 1)) * 100}%`,
                          backgroundColor:
                            i === category.sparkline.length - 1
                              ? '#295343'
                              : '#D1D5DB',
                        }}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ExpenseTrendsPage;
