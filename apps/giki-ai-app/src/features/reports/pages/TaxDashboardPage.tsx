/**
 * Tax Dashboard Page
 * Professional tax reporting and compliance dashboard for month-end workflows
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  Calculator,
  DollarSign,
  FileText,
  TrendingUp,
  Download,
  Clock,
  CheckCircle,
  AlertTriangle,
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/shared/utils/utils';
import { taxService, TaxSummary, QuarterlySummary, DeductibleExpense } from '../services/taxService';
import { logger } from '@/shared/utils/errorHandling';

export const TaxDashboardPage: React.FC = () => {
  const [taxSummary, setTaxSummary] = useState<TaxSummary | null>(null);
  const [quarterlySummary, setQuarterlySummary] = useState<QuarterlySummary[]>(
    [],
  );
  const [deductibleExpenses, setDeductibleExpenses] = useState<
    DeductibleExpense[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Real tax data loading using actual API calls
  useEffect(() => {
    const loadTaxData = async () => {
      setLoading(true);
      try {
        logger.info('TaxDashboardPage: Loading tax data', 'TaxDashboardPage', { selectedYear });

        // Load real tax data using taxService
        const [taxSummaryData, quarterlyData, expensesData] = await Promise.all([
          taxService.getTaxSummary(selectedYear),
          taxService.getQuarterlySummary(selectedYear),
          taxService.getDeductibleExpenses(selectedYear),
        ]);

        setTaxSummary(taxSummaryData);
        setQuarterlySummary(quarterlyData);
        setDeductibleExpenses(expensesData);

        logger.info('TaxDashboardPage: Tax data loaded successfully', 'TaxDashboardPage', {
          taxableIncome: taxSummaryData.taxable_income,
          quarterlyCount: quarterlyData.length,
          expenseCount: expensesData.length,
        });

      } catch (error) {
        logger.error('TaxDashboardPage: Error loading tax data', 'TaxDashboardPage', error as Error);
        
        // Set empty states instead of fake data
        setTaxSummary({
          taxable_income: 0,
          total_deductions: 0,
          estimated_tax_liability: 0,
          quarterly_payments: 0,
          tax_year: selectedYear,
          current_quarter: Math.ceil(new Date().getMonth() / 3) + 1,
        });
        setQuarterlySummary([]);
        setDeductibleExpenses([]);
      } finally {
        setLoading(false);
      }
    };

    void loadTaxData();
  }, [selectedYear]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'filed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'filed':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'overdue':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleExportTaxReport = async () => {
    try {
      logger.info('TaxDashboardPage: Exporting tax report', 'TaxDashboardPage', { selectedYear });
      
      // TODO: Implement real tax report export through backend API
      // This would typically call:
      // await apiClient.get(`/api/v1/reports/export/tax-report/${selectedYear}`, { responseType: 'blob' });
      
      logger.warn('TaxDashboardPage: Tax report export not yet implemented', 'TaxDashboardPage');
      
    } catch (error) {
      logger.error('TaxDashboardPage: Error exporting tax report', 'TaxDashboardPage', error as Error);
    }
  };

  if (loading) {
    return (
      <div className="p-8 space-y-6 bg-gray-50 min-h-screen">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Tax Dashboard
          </h1>
          <p className="text-gray-600">
            Professional tax reporting and compliance management
          </p>
        </div>
        <div className="flex gap-3">
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-transparent"
          >
            <option value={2024}>Tax Year 2024</option>
            <option value={2023}>Tax Year 2023</option>
            <option value={2022}>Tax Year 2022</option>
          </select>
          <Button
            onClick={handleExportTaxReport}
            className="bg-brand-primary hover:bg-brand-primary-hover text-white"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Tax Summary Cards */}
      {taxSummary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Taxable Income
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(taxSummary.taxable_income)}
              </div>
              <p className="text-xs text-muted-foreground">
                Year to date {taxSummary.tax_year}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Deductions
              </CardTitle>
              <Calculator className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(taxSummary.total_deductions)}
              </div>
              <p className="text-xs text-muted-foreground">
                Deductible business expenses
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Estimated Tax
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(taxSummary.estimated_tax_liability)}
              </div>
              <p className="text-xs text-muted-foreground">
                Annual tax liability
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Payments Made
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(taxSummary.quarterly_payments)}
              </div>
              <div
                className={`text-xs ${taxSummary.quarterly_payments >= taxSummary.estimated_tax_liability ? 'text-success' : 'text-orange-600'}`}
              >
                {taxSummary.quarterly_payments >=
                taxSummary.estimated_tax_liability
                  ? 'Fully paid'
                  : `${formatCurrency(taxSummary.estimated_tax_liability - taxSummary.quarterly_payments)} remaining`}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Tabs */}
      <Tabs defaultValue="quarterly" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="quarterly">Quarterly Summary</TabsTrigger>
          <TabsTrigger value="deductions">Deductible Expenses</TabsTrigger>
          <TabsTrigger value="workflow">Month-End Workflow</TabsTrigger>
        </TabsList>

        <TabsContent value="quarterly" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quarterly Tax Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {quarterlySummary.map((quarter) => (
                  <div
                    key={quarter.quarter}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(quarter.status)}
                        <span className="font-medium">
                          Q{quarter.quarter} {selectedYear}
                        </span>
                      </div>
                      <Badge className={getStatusColor(quarter.status)}>
                        {quarter.status.charAt(0).toUpperCase() +
                          quarter.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-6 text-sm">
                      <div>
                        <span className="text-gray-500">Income:</span>{' '}
                        {formatCurrency(quarter.income)}
                      </div>
                      <div>
                        <span className="text-gray-500">Deductions:</span>{' '}
                        {formatCurrency(quarter.deductions)}
                      </div>
                      <div>
                        <span className="text-gray-500">Estimated Tax:</span>{' '}
                        {formatCurrency(quarter.estimated_tax)}
                      </div>
                      <div>
                        <span className="text-gray-500">Due:</span>{' '}
                        {formatDate(quarter.due_date)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="deductions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Deductible Business Expenses</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {deductibleExpenses.map((expense) => (
                  <div
                    key={expense.category}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div>
                        <div className="font-medium">{expense.category}</div>
                        <div className="text-sm text-gray-500">
                          GL: {expense.gl_code} • {expense.transaction_count}{' '}
                          transactions
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        {formatCurrency(expense.amount)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {(
                          (expense.amount /
                            (taxSummary?.total_deductions || 1)) *
                          100
                        ).toFixed(1)}
                        % of total
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workflow" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Month-End Tax Workflow</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-success" />
                    <div>
                      <div className="font-medium">
                        Review Monthly Transactions
                      </div>
                      <div className="text-sm text-gray-500">
                        All transactions categorized and approved
                      </div>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    Complete
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-success" />
                    <div>
                      <div className="font-medium">Reconcile Bank Accounts</div>
                      <div className="text-sm text-gray-500">
                        All accounts reconciled for the period
                      </div>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    Complete
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-yellow-600" />
                    <div>
                      <div className="font-medium">
                        Record Accrual Adjustments
                      </div>
                      <div className="text-sm text-gray-500">
                        Add month-end accruals and deferrals
                      </div>
                    </div>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    In Progress
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="font-medium">Generate Tax Reports</div>
                      <div className="text-sm text-gray-500">
                        Export monthly and quarterly reports
                      </div>
                    </div>
                  </div>
                  <Badge className="bg-gray-100 text-gray-800">Pending</Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="font-medium">Close Period</div>
                      <div className="text-sm text-gray-500">
                        Lock transactions and finalize period
                      </div>
                    </div>
                  </div>
                  <Badge className="bg-gray-100 text-gray-800">Pending</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TaxDashboardPage;
