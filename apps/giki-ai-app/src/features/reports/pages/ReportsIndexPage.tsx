'use client';

import React from 'react';
import TransactionListTable from '../components/TransactionListTable';

const ReportsPage: React.FC = () => {
  return (
    <div className="page-container bg-white min-h-screen">
      {/* Professional Header - Enhanced Design */}
      <div className="section-spacing-md">
        <h1 className="text-3xl font-semibold text-gray-700 mb-2">
          Financial Reports
        </h1>
        <p className="text-gray-500 text-base">
          Generate comprehensive financial reports and transaction analytics
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 grid-gap-lg">
        {/* Cash Flow Report Section */}
        <div className="border border-gray-200 shadow-sm rounded-lg overflow-hidden">
          <div className="bg-brand-primary text-white card-header">
            <h2 className="text-base font-semibold">Cash Flow Report</h2>
          </div>
          <div className="card-content">
            <div
              data-testid="cash-flow-placeholder"
              className="border-2 border-dashed border-gray-300 rounded-md page-container text-center text-gray-500"
            >
              Cash Flow Report Placeholder
            </div>
          </div>
        </div>

        {/* MIS Report Section */}
        <div className="border border-gray-200 shadow-sm rounded-lg overflow-hidden">
          <div className="bg-brand-primary text-white card-header">
            <h2 className="text-base font-semibold">MIS Report</h2>
          </div>
          <div className="card-content">
            <div
              data-testid="mis-placeholder"
              className="border-2 border-dashed border-gray-300 rounded-md page-container text-center text-gray-500"
            >
              MIS Report Placeholder
            </div>
          </div>
        </div>

        {/* Expense Breakdown Section */}
        <div className="border border-gray-200 shadow-sm rounded-lg overflow-hidden">
          <div className="bg-brand-primary text-white card-header">
            <h2 className="text-base font-semibold">Expense Breakdown</h2>
          </div>
          <div className="card-content">
            <div
              data-testid="expense-breakdown-placeholder"
              className="border-2 border-dashed border-gray-300 rounded-md page-container text-center text-gray-500"
            >
              Expense Breakdown Placeholder
            </div>
          </div>
        </div>

        {/* Employee Cost Section */}
        <div className="border border-gray-200 shadow-sm rounded-lg overflow-hidden">
          <div className="bg-brand-primary text-white card-header">
            <h2 className="text-base font-semibold">Employee Cost</h2>
          </div>
          <div className="card-content">
            <div
              data-testid="employee-cost-placeholder"
              className="border-2 border-dashed border-gray-300 rounded-md page-container text-center text-gray-500"
            >
              Employee Cost Analysis Placeholder
            </div>
          </div>
        </div>

        {/* Transaction List Section - Full Width */}
        <div className="border border-gray-200 shadow-sm rounded-lg overflow-hidden md:col-span-2">
          <div className="bg-brand-primary text-white card-header">
            <h2 className="text-base font-semibold">Transaction List</h2>
          </div>
          <div className="card-content">
            {/* Replaced placeholder with TransactionListTable component */}
            <TransactionListTable />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;
