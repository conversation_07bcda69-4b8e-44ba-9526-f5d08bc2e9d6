/**
 * Reports Feature Types
 *
 * Type definitions for reporting functionality.
 */

export interface ReportFilter {
  dateRange?: {
    start: Date;
    end: Date;
  };
  categories?: string[];
  amountRange?: {
    min: number;
    max: number;
  };
  accountIds?: string[];
}

export interface ReportData {
  id: string;
  title: string;
  type: 'income-expense' | 'spending-by-category' | 'custom';
  data: unknown;
  generatedAt: Date;
  filters: ReportFilter;
}

export interface ReportConfig {
  title: string;
  type: string;
  filters: ReportFilter;
  visualization: 'chart' | 'table' | 'hybrid';
  exportFormats: ('pdf' | 'excel' | 'csv')[];
}
