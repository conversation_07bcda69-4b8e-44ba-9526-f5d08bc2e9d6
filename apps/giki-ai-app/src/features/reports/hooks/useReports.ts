/**
 * Reports Hook
 *
 * Custom hook for managing report generation and data.
 */

import { useState, useCallback } from 'react';
import { ReportData, ReportConfig } from '../types/reports';
import { unifiedAIService } from '@/shared/services/ai/UnifiedAIService';

export interface UseReportsReturn {
  reports: ReportData[];
  isLoading: boolean;
  error: string | null;
  generateReport: (config: ReportConfig) => Promise<ReportData>;
  deleteReport: (reportId: string) => Promise<void>;
  refreshReports: () => Promise<void>;
}

export const useReports = (): UseReportsReturn => {
  const [reports, setReports] = useState<ReportData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateReport = useCallback(
    async (config: ReportConfig): Promise<ReportData> => {
      setIsLoading(true);
      setError(null);

      try {
        // Use unified AI service for report generation
        const query = `Generate a ${config.type} report for ${config.title}`;
        const response = await unifiedAIService.generateReportFromQuery(
          query,
          'current-user', // This would normally come from auth context
          { 
            useCache: true,
            type: config.type,
            filters: config.filters
          }
        );

        const report: ReportData = {
          id: `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: config.title,
          type: config.type as ReportData['type'],
          data: response.data || {},
          generatedAt: new Date(),
          filters: config.filters,
        };

        setReports((prev) => [...prev, report]);
        return report;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to generate report';
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    [],
  );

  const deleteReport = useCallback(async (reportId: string): Promise<void> => {
    try {
      // For now, just remove from local state
      // TODO: Implement backend deletion when report persistence is added
      setReports((prev) => prev.filter((report) => report.id !== reportId));
    } catch (err) {
      console.error('Failed to delete report:', err);
      // Still remove from local state for UX
      setReports((prev) => prev.filter((report) => report.id !== reportId));
    }
  }, []);

  const refreshReports = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      // For now, reports are managed in local state
      // TODO: Implement backend report persistence and history fetching
      setReports((prev) => prev); // Keep existing reports
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to refresh reports';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    reports,
    isLoading,
    error,
    generateReport,
    deleteReport,
    refreshReports,
  };
};
