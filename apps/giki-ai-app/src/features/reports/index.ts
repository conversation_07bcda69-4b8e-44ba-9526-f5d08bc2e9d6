/**
 * Reports Feature Barrel Exports
 *
 * This file provides clean imports for all reporting-related
 * components, hooks, and services.
 */

// Components
export { default as IncomeVsExpenseReport } from './components/IncomeVsExpenseReport';
export { default as SpendingByCategoryReport } from './components/SpendingByCategoryReport';
export { default as TransactionListTable } from './components/TransactionListTable';
export { default as FileProcessingReportCard } from './components/FileProcessingReportCard';
export type { ProcessingReportSummary } from './components/FileProcessingReportCard';
export { default as ColumnMappingTable } from './components/ColumnMappingTable';
export type { ColumnMapping } from './components/ColumnMappingTable';
export { default as RowProcessingDetails } from './components/RowProcessingDetails';
export type { RowDetail } from './components/RowProcessingDetails';

// Pages
export { default as ReportsPage } from './pages/ReportsPage';
export { default as ReportsIndexPage } from './pages/ReportsIndexPage';
export { default as FileProcessingReportPage } from './pages/FileProcessingReportPage';
export { default as FileProcessingReportsList } from './pages/FileProcessingReportsList';

// Services
export * from './services/reportService';

// Types
export * from './types/reports';

// Hooks
export * from './hooks/useReports';
