/**
 * UploadPage Component Tests
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { 
  render, 
  mockFetch, 
  setupAuthenticatedState 
} from '@/test/utils';
import UploadPage from '../UploadPage';

// Mock file for upload testing
const mockFile = new File(['test content'], 'test-transactions.xlsx', {
  type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
});

describe('UploadPage', () => {
  beforeEach(() => {
    setupAuthenticatedState();
  });

  it('renders upload interface correctly', () => {
    render(<UploadPage />);
    
    expect(screen.getByText(/upload financial data/i)).toBeInTheDocument();
    expect(screen.getByText(/drag.*drop.*files/i)).toBeInTheDocument();
    expect(screen.getByText(/click to browse/i)).toBeInTheDocument();
    expect(screen.getByText(/supported formats/i)).toBeInTheDocument();
  });

  it('displays supported file formats', () => {
    render(<UploadPage />);
    
    expect(screen.getByText(/excel.*xlsx.*xls/i)).toBeInTheDocument();
    expect(screen.getByText(/csv/i)).toBeInTheDocument();
    expect(screen.getByText(/maximum file size/i)).toBeInTheDocument();
  });

  it('handles file selection via input', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      upload_id: 'test-upload-123',
      status: 'uploaded',
      filename: 'test-transactions.xlsx',
    });
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, mockFile);
    
    expect(screen.getByText(/test-transactions\.xlsx/i)).toBeInTheDocument();
  });

  it('validates file format restrictions', async () => {
    const user = userEvent.setup();
    
    const invalidFile = new File(['test'], 'invalid.txt', { type: 'text/plain' });
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, invalidFile);
    
    expect(screen.getByText(/unsupported file format/i)).toBeInTheDocument();
    expect(screen.getByText(/please upload.*excel.*csv/i)).toBeInTheDocument();
  });

  it('validates file size limits', async () => {
    const user = userEvent.setup();
    
    // Create large file (>50MB mock)
    const largeFile = new File(['x'.repeat(50 * 1024 * 1024 + 1)], 'large.xlsx', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, largeFile);
    
    expect(screen.getByText(/file too large/i)).toBeInTheDocument();
    expect(screen.getByText(/maximum.*50.*mb/i)).toBeInTheDocument();
  });

  it('shows upload progress during file upload', async () => {
    const user = userEvent.setup();
    
    // Mock upload progress response
    mockFetch({
      upload_id: 'test-upload-123',
      status: 'uploading',
      progress: 45,
    });
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, mockFile);
    
    await waitFor(() => {
      expect(screen.getByText(/uploading/i)).toBeInTheDocument();
      expect(screen.getByText(/45%/i)).toBeInTheDocument();
    });
  });

  it('displays schema interpretation interface after upload', async () => {
    const user = userEvent.setup();
    
    // Mock successful upload followed by schema detection
    mockFetch({
      upload_id: 'test-upload-123',
      status: 'uploaded',
      schema_detected: true,
      columns: [
        { name: 'Date', type: 'date', confidence: 0.95 },
        { name: 'Description', type: 'text', confidence: 0.90 },
        { name: 'Amount', type: 'number', confidence: 0.98 },
      ],
    });
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, mockFile);
    
    await waitFor(() => {
      expect(screen.getByText(/schema detected/i)).toBeInTheDocument();
      expect(screen.getByText(/column mapping/i)).toBeInTheDocument();
    });
    
    // Check detected columns
    expect(screen.getByText(/date.*95%/i)).toBeInTheDocument();
    expect(screen.getByText(/description.*90%/i)).toBeInTheDocument();
    expect(screen.getByText(/amount.*98%/i)).toBeInTheDocument();
  });

  it('allows manual column mapping adjustments', async () => {
    const user = userEvent.setup();
    
    mockFetch({
      upload_id: 'test-upload-123',
      status: 'uploaded',
      schema_detected: true,
      columns: [
        { name: 'Date', type: 'date', confidence: 0.95 },
        { name: 'Description', type: 'text', confidence: 0.90 },
      ],
    });
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, mockFile);
    
    await waitFor(() => {
      expect(screen.getByText(/column mapping/i)).toBeInTheDocument();
    });
    
    // Test manual column type change
    const typeSelector = screen.getByDisplayValue(/date/i);
    await user.selectOptions(typeSelector, 'text');
    
    expect(screen.getByDisplayValue(/text/i)).toBeInTheDocument();
  });

  it('processes file after schema confirmation', async () => {
    const user = userEvent.setup();
    
    // Mock upload and schema confirmation
    mockFetch({
      upload_id: 'test-upload-123',
      status: 'processing',
      progress: 0,
      estimated_time: 120,
    });
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, mockFile);
    
    // Mock schema confirmation step
    await waitFor(() => {
      expect(screen.getByText(/column mapping/i)).toBeInTheDocument();
    });
    
    const confirmButton = screen.getByRole('button', { name: /confirm.*process/i });
    await user.click(confirmButton);
    
    await waitFor(() => {
      expect(screen.getByText(/processing.*file/i)).toBeInTheDocument();
      expect(screen.getByText(/estimated.*2.*minutes/i)).toBeInTheDocument();
    });
  });

  it('shows real-time processing updates', async () => {
    const user = userEvent.setup();
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, mockFile);
    
    // Mock processing updates
    mockFetch({
      upload_id: 'test-upload-123',
      status: 'processing',
      progress: 60,
      transactions_processed: 150,
      categories_detected: 12,
      current_operation: 'AI categorization',
    });
    
    await waitFor(() => {
      expect(screen.getByText(/60%.*complete/i)).toBeInTheDocument();
      expect(screen.getByText(/150.*transactions/i)).toBeInTheDocument();
      expect(screen.getByText(/12.*categories/i)).toBeInTheDocument();
      expect(screen.getByText(/ai categorization/i)).toBeInTheDocument();
    });
  });

  it('displays completion summary with results', async () => {
    const user = userEvent.setup();
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, mockFile);
    
    // Mock completion
    mockFetch({
      upload_id: 'test-upload-123',
      status: 'completed',
      progress: 100,
      results: {
        transactions_processed: 245,
        categories_created: 18,
        accuracy_score: 87.5,
        processing_time: 95,
      },
    });
    
    await waitFor(() => {
      expect(screen.getByText(/processing complete/i)).toBeInTheDocument();
      expect(screen.getByText(/245.*transactions/i)).toBeInTheDocument();
      expect(screen.getByText(/18.*categories/i)).toBeInTheDocument();
      expect(screen.getByText(/87\.5%.*accuracy/i)).toBeInTheDocument();
    });
    
    // Check for next step buttons
    expect(screen.getByRole('button', { name: /view transactions/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /upload another/i })).toBeInTheDocument();
  });

  it('handles upload errors gracefully', async () => {
    const user = userEvent.setup();
    
    // Mock upload error
    mockFetch(
      { detail: 'File processing failed' },
      false,
      500
    );
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, mockFile);
    
    await waitFor(() => {
      expect(screen.getByText(/upload failed/i)).toBeInTheDocument();
      expect(screen.getByText(/file processing failed/i)).toBeInTheDocument();
    });
    
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
  });

  it('supports multiple file upload queue', async () => {
    const user = userEvent.setup();
    
    const file1 = new File(['content1'], 'file1.xlsx', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const file2 = new File(['content2'], 'file2.csv', {
      type: 'text/csv',
    });
    
    mockFetch({
      uploads: [
        { upload_id: 'upload-1', filename: 'file1.xlsx', status: 'uploaded' },
        { upload_id: 'upload-2', filename: 'file2.csv', status: 'uploading' },
      ],
    });
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, [file1, file2]);
    
    await waitFor(() => {
      expect(screen.getByText(/file1\.xlsx/i)).toBeInTheDocument();
      expect(screen.getByText(/file2\.csv/i)).toBeInTheDocument();
    });
    
    expect(screen.getByText(/2.*files.*selected/i)).toBeInTheDocument();
  });

  it('allows canceling uploads in progress', async () => {
    const user = userEvent.setup();
    
    render(<UploadPage />);
    
    const fileInput = screen.getByLabelText(/choose files/i);
    await user.upload(fileInput, mockFile);
    
    // Mock upload in progress
    mockFetch({
      upload_id: 'test-upload-123',
      status: 'uploading',
      progress: 35,
    });
    
    await waitFor(() => {
      expect(screen.getByText(/uploading/i)).toBeInTheDocument();
    });
    
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);
    
    expect(screen.getByText(/upload cancelled/i)).toBeInTheDocument();
  });
});