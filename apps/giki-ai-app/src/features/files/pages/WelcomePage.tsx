/**
 * Onboarding Welcome Page - Step 1 of 5
 * Matches wireframe: docs/wireframes/02-onboarding-journey/03-welcome.md
 */
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Checkbox } from '@/shared/components/ui/checkbox';
import { useToast } from '@/shared/components/ui/use-toast';
import { useAuth } from '@/features/auth';
import {
  FolderOpen,
  HelpCircle,
  FileSpreadsheet,
  ArrowRight,
} from 'lucide-react';

const WelcomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();

  // Checklist state (optional visual feedback)
  const [checklist, setChecklist] = useState({
    historicalData: false,
    hasCategories: false,
    correctFormat: false,
  });

  const handleChecklistChange = (item: string, checked: boolean) => {
    setChecklist((prev) => ({
      ...prev,
      [item]: checked,
    }));
  };

  const handleContinue = () => {
    navigate('/onboarding/upload');
  };

  const handleHelp = () => {
    toast({
      title: 'Help Resources',
      description:
        'Sample files and detailed guides are available in our help center.',
      variant: 'default',
    });
  };

  const userName =
    user?.full_name?.split(' ')[0] || user?.email?.split('@')[0] || 'there';

  return (
    <div className="p-8 bg-white min-h-screen">
      {/* Professional Header - Enhanced Design */}
      <div className="mb-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-semibold text-gray-700 mb-2">
            Let&rsquo;s Set Up Your AI Assistant
          </h1>
          <p className="text-gray-500 text-base">
            Welcome to giki.ai - Complete automated MIS preparation platform
          </p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto">
        {/* Welcome Card */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden mb-8">
          <CardContent className="bg-gradient-to-br from-brand-primary to-ai-dark-blue text-white p-8">
            <div className="text-center space-y-4">
              <h2 className="text-2xl font-semibold">Welcome, {userName}! ↑</h2>
              <p className="text-white/90 leading-relaxed text-base">
                We&rsquo;ll train your AI using your historical data.
                <br />
                This one-time setup takes about 10 minutes.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Preparation Checklist */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden mb-8">
          <div className="bg-brand-primary text-white px-6 py-4">
            <div className="flex items-center gap-3">
              <FolderOpen className="h-6 w-6" />
              <h3 className="text-base font-semibold">Prepare Your Files</h3>
            </div>
          </div>
          <CardContent className="p-6">
            <div className="space-y-4 mb-6">
              <label className="flex items-start gap-3 cursor-pointer">
                <Checkbox
                  checked={checklist.historicalData}
                  onCheckedChange={(checked) =>
                    handleChecklistChange('historicalData', checked as boolean)
                  }
                  className="mt-1"
                />
                <span className="text-slate-700">
                  12+ months of transaction history
                </span>
              </label>

              <label className="flex items-start gap-3 cursor-pointer">
                <Checkbox
                  checked={checklist.hasCategories}
                  onCheckedChange={(checked) =>
                    handleChecklistChange('hasCategories', checked as boolean)
                  }
                  className="mt-1"
                />
                <span className="text-slate-700">
                  Files include your existing categories
                </span>
              </label>

              <label className="flex items-start gap-3 cursor-pointer">
                <Checkbox
                  checked={checklist.correctFormat}
                  onCheckedChange={(checked) =>
                    handleChecklistChange('correctFormat', checked as boolean)
                  }
                  className="mt-1"
                />
                <span className="text-slate-700">
                  Excel (.xlsx) or CSV format
                </span>
              </label>
            </div>

            {/* Example Format */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm font-medium text-gray-700 mb-2">
                Example columns:
              </p>
              <div className="flex items-center gap-4 text-sm text-gray-600 font-mono">
                <span>Date</span>
                <span>|</span>
                <span>Description</span>
                <span>|</span>
                <span>Amount</span>
                <span>|</span>
                <span>Category</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Progress Tracker */}
        <Card className="border border-gray-200 shadow-sm overflow-hidden mb-8">
          <div className="bg-brand-primary text-white px-6 py-4">
            <h4 className="text-base font-semibold">
              Your Progress: Step 1 of 5
            </h4>
          </div>
          <CardContent className="p-6">
            {/* Progress Steps */}
            <div className="flex items-center justify-center space-x-2 md:space-x-4">
              {/* Step 1 - Active */}
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 bg-brand-primary text-white rounded-full flex items-center justify-center text-sm font-semibold">
                  1
                </div>
                <span className="text-xs text-gray-600 mt-1 hidden sm:block">
                  Prepare
                </span>
              </div>

              <div className="w-6 md:w-12 h-0.5 bg-slate-300"></div>

              {/* Step 2 - Upcoming */}
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 border-2 border-slate-300 text-slate-400 rounded-full flex items-center justify-center text-sm font-semibold">
                  2
                </div>
                <span className="text-xs text-slate-400 mt-1 hidden sm:block">
                  Upload
                </span>
              </div>

              <div className="w-6 md:w-12 h-0.5 bg-slate-300"></div>

              {/* Step 3 - Upcoming */}
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 border-2 border-slate-300 text-slate-400 rounded-full flex items-center justify-center text-sm font-semibold">
                  3
                </div>
                <span className="text-xs text-slate-400 mt-1 hidden sm:block">
                  Map
                </span>
              </div>

              <div className="w-6 md:w-12 h-0.5 bg-slate-300"></div>

              {/* Step 4 - Upcoming */}
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 border-2 border-slate-300 text-slate-400 rounded-full flex items-center justify-center text-sm font-semibold">
                  4
                </div>
                <span className="text-xs text-slate-400 mt-1 hidden sm:block">
                  Train
                </span>
              </div>

              <div className="w-6 md:w-12 h-0.5 bg-slate-300"></div>

              {/* Step 5 - Upcoming */}
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 border-2 border-slate-300 text-slate-400 rounded-full flex items-center justify-center text-sm font-semibold">
                  5
                </div>
                <span className="text-xs text-slate-400 mt-1 hidden sm:block">
                  Go
                </span>
              </div>
            </div>

            {/* Mobile Progress Labels */}
            <div className="flex justify-between mt-3 sm:hidden text-xs text-gray-600">
              <span className="text-brand-primary font-medium">Prepare</span>
              <span>Upload</span>
              <span>Map</span>
              <span>Train</span>
              <span>Go</span>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            onClick={handleContinue}
            className="flex-1 bg-brand-primary hover:bg-brand-primary-hover text-white font-semibold py-3"
          >
            <ArrowRight className="h-4 w-4 mr-2" />I Have My Files
          </Button>

          <Button
            onClick={handleHelp}
            variant="outline"
            className="flex-1 border-brand-primary text-brand-primary hover:bg-green-50 py-3"
          >
            <HelpCircle className="h-4 w-4 mr-2" />
            Need Help?
          </Button>
        </div>

        {/* Mobile Navigation - shows on small screens */}
        <div className="mt-8 md:hidden">
          <Card className="border border-gray-200 shadow-sm p-4">
            <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
              <FileSpreadsheet className="h-4 w-4" />
              <span>Sample files available in help section</span>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default WelcomePage;
