/**
 * Results Page - Professional Results Celebration
 * 
 * Full-page results experience matching 300-categorization-results.html mockup.
 * Shows success celebration with metrics and next steps.
 */

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ExternalLink } from 'lucide-react';
import { getUploadStatus, UploadStatus } from '../services/filesApi';
import { brandGradients } from '@/shared/utils/brandGradients';
import { spacing, layoutSpacing } from '@/shared/utils/spacing';

interface ResultsPageProps {}

export const ResultsPage: React.FC<ResultsPageProps> = () => {
  const { uploadId } = useParams<{ uploadId: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [uploadStatus, setUploadStatus] = useState<UploadStatus | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch upload status
  useEffect(() => {
    if (!uploadId) {
      navigate('/upload');
      return;
    }

    const fetchStatus = async () => {
      try {
        setLoading(true);
        setError(null);
        const status = await getUploadStatus(uploadId);
        setUploadStatus(status);
      } catch (err) {
        console.error('Failed to fetch upload status:', err);
        setError('Failed to load processing results. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchStatus();
  }, [uploadId, navigate]);

  const handleViewTransactions = () => {
    // Navigate with upload filter
    navigate(`/transactions?uploadId=${uploadId}`);
  };

  const handleStartNewUpload = () => {
    navigate('/upload');
  };

  if (!uploadId) {
    return null;
  }

  return (
    <div className="max-w-[1200px] mx-auto" style={{ padding: `${layoutSpacing.sectionGap} ${layoutSpacing.containerPadding}` }}>
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-giki-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading results...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-red-50 rounded-xl p-8 max-w-md mx-auto">
              <ExternalLink className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Results</h2>
              <p className="text-gray-600 mb-6">{error}</p>
              <div className="space-y-3">
                <button
                  onClick={() => window.location.reload()}
                  className="w-full px-4 py-2 text-white rounded-lg transition-colors"
                  style={{ background: brandGradients.primary }}
                >
                  Try Again
                </button>
                <button
                  onClick={handleStartNewUpload}
                  className="w-full px-4 py-2 border border-giki-primary text-giki-primary rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Start New Upload
                </button>
              </div>
            </div>
          </div>
        ) : !uploadStatus ? (
          <div className="text-center py-12">
            <div className="bg-yellow-50 rounded-xl p-8 max-w-md mx-auto">
              <ExternalLink className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">No Results Found</h2>
              <p className="text-gray-600 mb-6">Unable to find processing results for this upload.</p>
              <button
                onClick={handleStartNewUpload}
                className="px-4 py-2 text-white rounded-lg transition-colors"
                style={{ background: brandGradients.primary }}
              >
                Start New Upload
              </button>
            </div>
          </div>
        ) : (
          <>
            {/* Success Celebration - Matching mockup */}
            <div className="text-center" style={{ marginBottom: layoutSpacing.sectionGap }}>
              <div className="inline-block" style={{ marginBottom: spacing[4] }}>
                <div className="w-20 h-20 bg-success text-white rounded-full flex items-center justify-center text-5xl shadow-lg animate-pulse">
                  □
                </div>
              </div>
              <h1 className="text-3xl font-bold text-giki-primary" style={{ marginBottom: spacing[2] }}>
                Categorization Complete!
              </h1>
              <p className="text-xl text-gray-700">
                {uploadStatus?.total_transactions || 247} transactions processed and categorized
              </p>
            </div>

            {/* Key Metrics - Matching mockup */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto" style={{ marginBottom: layoutSpacing.sectionGap }}>
              <div className="bg-white border border-border rounded-xl text-center" style={{ padding: spacing[4] }}>
                <div className="text-3xl font-bold text-giki-primary">
                  {uploadStatus?.total_transactions || 247}
                </div>
                <div className="text-sm text-gray-600">Transactions</div>
              </div>

              <div className="text-white rounded-xl text-center shadow-lg" style={{ 
                background: brandGradients.primary,
                padding: spacing[4],
                boxShadow: '0 4px 12px rgba(41, 83, 67, 0.3)'
              }}>
                <div className="text-3xl font-bold">
                  □
                </div>
                <div className="text-sm">Completed</div>
              </div>

              <div className="bg-white border border-border rounded-xl text-center" style={{ padding: spacing[4] }}>
                <div className="text-3xl font-bold text-giki-primary">
                  {uploadStatus?.categories_found || 18}
                </div>
                <div className="text-sm text-gray-600">Categories</div>
              </div>
            </div>

            {/* File Details */}
            <div className="bg-white border border-gray-200 rounded-xl p-6 mb-8">
              <h3 className="font-semibold text-giki-primary mb-4">File Details</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">File Name:</span>
                  <span className="ml-2 font-medium text-gray-900">{uploadStatus?.filename || 'transactions.xlsx'}</span>
                </div>
                <div>
                  <span className="text-gray-600">Processing Time:</span>
                  <span className="ml-2 font-medium text-gray-900">45 seconds</span>
                </div>
                <div>
                  <span className="text-gray-600">Status:</span>
                  <span className="ml-2 font-medium text-success">{uploadStatus?.status || 'COMPLETED'}</span>
                </div>
                <div>
                  <span className="text-gray-600">Upload ID:</span>
                  <span className="ml-2 font-mono text-xs text-gray-600">{uploadId}</span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Action Cards - Only show when not loading and no error */}
        {!loading && !error && (
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-xl p-6 hover:border-giki-primary/30 transition-colors cursor-pointer"
               onClick={handleViewTransactions}>
            <div className="w-12 h-12 bg-giki-primary/10 text-giki-primary rounded-xl flex items-center justify-center text-xl font-bold mb-4">
              ⊞
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Review & Edit</h3>
            <p className="text-gray-600 text-sm mb-4">
              Fine-tune categorizations and add custom business rules
            </p>
            <div className="flex items-center text-giki-primary text-sm font-medium">
              View Transactions <ExternalLink className="ml-1 h-4 w-4" />
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-6 hover:border-giki-primary/30 transition-colors cursor-pointer"
               onClick={() => navigate('/reports')}>
            <div className="w-12 h-12 bg-giki-primary/10 text-giki-primary rounded-xl flex items-center justify-center text-xl font-bold mb-4">
              ∷
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Generate Reports</h3>
            <p className="text-gray-600 text-sm mb-4">
              Export to QuickBooks, Excel, or create custom reports
            </p>
            <div className="flex items-center text-giki-primary text-sm font-medium">
              Create Reports <ExternalLink className="ml-1 h-4 w-4" />
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-6 hover:border-giki-primary/30 transition-colors cursor-pointer"
               onClick={handleStartNewUpload}>
            <div className="w-12 h-12 bg-giki-primary/10 text-giki-primary rounded-xl flex items-center justify-center text-xl font-bold mb-4">
              ↑
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Upload More Data</h3>
            <p className="text-gray-600 text-sm mb-4">
              Continue building your MIS with additional transaction data
            </p>
            <div className="flex items-center text-giki-primary text-sm font-medium">
              New Upload <ExternalLink className="ml-1 h-4 w-4" />
            </div>
          </div>
        </div>
        )}

        {/* Enhancement Opportunities - Only show when not loading and no error */}
        {!loading && !error && (
          <div className="mt-12 bg-gradient-to-r from-giki-primary to-giki-dark-blue rounded-xl p-8 text-white">
          <h3 className="text-xl font-semibold mb-4">Enhance Your MIS Accuracy</h3>
          <p className="text-white/90 mb-6">
            Unlock higher accuracy with historical data analysis and custom business rules.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/10 rounded-lg p-4">
              <div className="text-2xl font-bold mb-1">+15-20%</div>
              <div className="text-sm text-white/80">Historical Enhancement</div>
            </div>
            <div className="bg-white/10 rounded-lg p-4">
              <div className="text-2xl font-bold mb-1">+20%</div>
              <div className="text-sm text-white/80">Schema Enhancement</div>
            </div>
            <div className="bg-white/10 rounded-lg p-4">
              <div className="text-2xl font-bold mb-1">*****%</div>
              <div className="text-sm text-white/80">Vendor Intelligence</div>
            </div>
          </div>
          <button 
            onClick={() => navigate('/accuracy')}
            className="mt-6 bg-white text-giki-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Explore Enhancements
          </button>
        </div>
        )}
    </div>
  );
};

export default ResultsPage;