/**
 * Processing Page - Unified Processing Center
 * 
 * Shows all active processing jobs when no uploadId is provided,
 * or focuses on a specific upload when uploadId is in the URL.
 */

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ProcessingExperience } from '../components/ProcessingExperience';
import { getAllProcessingStatus } from '../services/filesApi';
import useFileProcessing from '../hooks/useFileProcessing';
import { useWebSocketStatus, useWebSocket } from '@/shared/services/websocket/WebSocketService';

interface ProcessingPageProps {}

interface ProcessingJob {
  id: string;
  filename: string;
  status: string;
  transaction_count: number;
  created_at: string;
  progress?: number;
}

export const ProcessingPage: React.FC<ProcessingPageProps> = () => {
  const { uploadId } = useParams<{ uploadId: string }>();
  const navigate = useNavigate();
  const [processingComplete, setProcessingComplete] = useState(false);
  const [processingJobs, setProcessingJobs] = useState<ProcessingJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedJob, setSelectedJob] = useState<ProcessingJob | null>(null);
  
  // WebSocket connection status
  const { connected, reconnect } = useWebSocketStatus();
  const { subscribe, unsubscribe } = useWebSocket();
  
  // File processing with WebSocket integration
  const { state: processingState, actions } = useFileProcessing(uploadId);

  // Load all processing jobs when no specific uploadId
  useEffect(() => {
    if (!uploadId) {
      loadProcessingJobs();
      // Refresh every 5 seconds
      const interval = setInterval(loadProcessingJobs, 5000);
      return () => clearInterval(interval);
    } else {
      // If we have an uploadId, find and select that job
      const job = processingJobs.find(j => j.id === uploadId);
      if (job) {
        setSelectedJob(job);
      }
    }
  }, [uploadId, processingJobs]);
  
  // WebSocket listeners for real-time job updates
  useEffect(() => {
    const handleProcessingProgress = (data: any) => {
      setProcessingJobs(prev => prev.map(job => 
        job.id === data.uploadId 
          ? { ...job, progress: data.progress, status: data.status || job.status }
          : job
      ));
    };
    
    const handleProcessingComplete = (data: any) => {
      setProcessingJobs(prev => prev.map(job => 
        job.id === data.uploadId 
          ? { ...job, status: 'COMPLETED', progress: 100 }
          : job
      ));
    };
    
    const handleFileUploaded = (data: any) => {
      const newJob: ProcessingJob = {
        id: data.uploadId,
        filename: data.filename,
        status: 'PROCESSING',
        transaction_count: data.transactionCount || 0,
        created_at: new Date().toISOString(),
        progress: 0
      };
      setProcessingJobs(prev => [newJob, ...prev]);
    };
    
    // Subscribe to events
    subscribe('processing.progress', handleProcessingProgress);
    subscribe('processing.completed', handleProcessingComplete);
    subscribe('file.uploaded', handleFileUploaded);
    
    return () => {
      unsubscribe('processing.progress', handleProcessingProgress);
      unsubscribe('processing.completed', handleProcessingComplete);
      unsubscribe('file.uploaded', handleFileUploaded);
    };
  }, [subscribe, unsubscribe]);

  const loadProcessingJobs = async () => {
    try {
      const response = await getAllProcessingStatus();
      if ('uploads' in response) {
        setProcessingJobs(response.uploads);
        // Add mock jobs for demo
        if (response.uploads.length === 0) {
          setProcessingJobs([
            {
              id: 'demo1',
              filename: 'test_transactions_uncategorized.xlsx',
              status: 'PROCESSING',
              transaction_count: 247,
              created_at: new Date(Date.now() - 360000).toISOString(),
              progress: 45
            },
            {
              id: 'demo2',
              filename: 'hdfc_bank_mixed_1000tx.xlsx',
              status: 'PROCESSING',
              transaction_count: 1001,
              created_at: new Date(Date.now() - 720000).toISOString(),
              progress: 72
            },
            {
              id: 'demo3',
              filename: 'march_expenses.csv',
              status: 'COMPLETED',
              transaction_count: 156,
              created_at: new Date(Date.now() - 1800000).toISOString(),
              progress: 100
            }
          ]);
        }
      }
    } catch (error) {
      console.error('Failed to load processing jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle processing completion
  const handleProcessingComplete = (uploadIdParam: string, reportId: string | null) => {
    setProcessingComplete(true);
    // Navigate to results page after a brief delay
    setTimeout(() => {
      navigate(`/results/${uploadIdParam}`);
    }, 2000);
  };

  // Handle job selection
  const handleJobSelect = (job: ProcessingJob) => {
    setSelectedJob(job);
  };

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-brand-primary mb-2">Processing Center</h1>
            <p className="text-lg text-gray-600">Monitor all your file processing activities in one place</p>
          </div>
          {/* Connection Status Indicator */}
          <div className="flex items-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${connected ? 'bg-success' : 'bg-error'}`}></div>
            <span className={connected ? 'text-[#059669]' : 'text-[#DC2626]'}>
              {connected ? 'Live updates active' : 'Reconnecting...'}
            </span>
            {!connected && (
              <button
                onClick={reconnect}
                className="text-xs text-brand-primary underline hover:no-underline"
              >
                Retry
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Two-Column Layout: Jobs List + Details Panel */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column: Processing Jobs List */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-brand-primary">Active Processing Jobs</h2>
            <button 
              onClick={() => navigate('/files/upload')}
              className="bg-brand-primary text-white px-4 py-2 rounded-lg font-semibold flex items-center gap-2 hover:bg-brand-primary-hover transition-colors"
            >
              <span className="text-base">↑</span>
              Upload Files
            </button>
          </div>

          {/* Processing Jobs List */}
          <div className="space-y-4">
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"></div>
                <p className="text-gray-600">Loading processing jobs...</p>
              </div>
            ) : processingJobs.length === 0 ? (
              <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-lg flex items-center justify-center text-2xl font-bold text-gray-400 bg-gray-50">
                  □
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Processing</h3>
                <p className="text-gray-600">Upload files to start processing transactions</p>
              </div>
            ) : (
              processingJobs.map((job) => (
                <div 
                  key={job.id} 
                  className={`bg-white rounded-xl border-2 p-4 cursor-pointer transition-all ${
                    selectedJob?.id === job.id 
                      ? 'border-brand-primary shadow-lg' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleJobSelect(job)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-xl text-brand-primary">□</span>
                        <h3 className="font-semibold text-gray-900 text-sm">{job.filename}</h3>
                      </div>
                      <div className="flex items-center gap-4 text-xs text-gray-600">
                        <span>{job.transaction_count} transactions</span>
                        <span>Started {new Date(job.created_at).toLocaleTimeString()}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      {job.status === 'COMPLETED' ? (
                        <div className="flex items-center gap-2 text-success">
                          <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                          <span className="font-medium text-xs">Completed</span>
                        </div>
                      ) : job.status === 'FAILED' ? (
                        <div className="flex items-center gap-2 text-error">
                          <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                          <span className="font-medium text-xs">Failed</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 text-yellow-600">
                          <div className="w-2 h-2 bg-yellow-600 rounded-full animate-pulse"></div>
                          <span className="font-medium text-xs">Processing</span>
                        </div>
                      )}
                      {job.status === 'PROCESSING' && job.progress && (
                        <div className="mt-2">
                          <div className="w-16 bg-gray-200 rounded-full h-1">
                            <div 
                              className="bg-brand-primary h-full rounded-full transition-all"
                              style={{ width: `${job.progress}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Right Column: Processing Details Panel */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-brand-primary">Processing Details</h2>
            {selectedJob && (
              <button
                onClick={() => setSelectedJob(null)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <span className="text-xl">←</span>
              </button>
            )}
          </div>

          {selectedJob || uploadId ? (
            <ProcessingExperience
              uploadId={selectedJob?.id || uploadId || ''}
              onProcessingComplete={handleProcessingComplete}
              showFullPageLayout={false}
            />
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-lg flex items-center justify-center text-3xl font-bold text-gray-300 bg-gray-50">
                ↑
              </div>
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Select a Processing Job</h3>
              <p className="text-gray-500">Click on a job from the list to view processing details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProcessingPage;