/**
 * File Upload Hook
 *
 * Custom hook for managing file upload state and operations.
 */

import { useState, useCallback } from 'react';
import { UploadProgress, UploadResponse, UploadError } from '../types/upload';
import { UploadResponse as ApiUploadResponse } from '@/shared/types/categorization';
import { ApiError } from '@/shared/utils/errorHandling';
import {
  uploadFile as uploadFileService,
  uploadFileForOnboarding,
} from '../services/uploadService';

/**
 * Type guard to check if the response is an API error
 */
function isApiError(
  response: ApiUploadResponse | ApiError,
): response is ApiError {
  return 'error' in response;
}

/**
 * Type guard to check if the response is a successful upload response
 */
function isSuccessfulUploadResponse(
  response: ApiUploadResponse | ApiError,
): response is ApiUploadResponse {
  return !isApiError(response) && 'upload_id' in response;
}

export interface UseFileUploadReturn {
  isUploading: boolean;
  progress: UploadProgress | null;
  error: UploadError | null;
  uploadFile: (file: File, currency?: string) => Promise<UploadResponse>;
  reset: () => void;
}

export const useFileUpload = (isOnboarding = false): UseFileUploadReturn => {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState<UploadProgress | null>(null);
  const [error, setError] = useState<UploadError | null>(null);

  const uploadFile = useCallback(
    async (file: File, currency?: string): Promise<UploadResponse> => {
      setIsUploading(true);
      setError(null);
      setProgress({ loaded: 0, total: file.size, percentage: 0 });

      try {
        // Use the imported upload service functions

        // Use appropriate upload function based on context
        const response =
          isOnboarding && currency
            ? await uploadFileForOnboarding(file, currency, (percentage) => {
                setProgress({
                  loaded: Math.round((percentage / 100) * file.size),
                  total: file.size,
                  percentage,
                });
              })
            : await uploadFileService(
                file,
                (percentage) => {
                  setProgress({
                    loaded: Math.round((percentage / 100) * file.size),
                    total: file.size,
                    percentage,
                  });
                },
                currency,
              );

        // Handle API error response
        if (isApiError(response)) {
          throw new Error(response.message || 'Upload failed');
        }

        // Ensure we have a successful upload response
        if (!isSuccessfulUploadResponse(response)) {
          throw new Error('Invalid response format from upload API');
        }

        setProgress({ loaded: file.size, total: file.size, percentage: 100 });

        // Convert API response to expected format with proper type safety
        const uploadResponse: UploadResponse = {
          success: true,
          uploadId: response.upload_id,
          filename: response.filename,
          size: response.size,
          mimeType: response.content_type || file.type,
          data: response, // Include the full response data
        };

        return uploadResponse;
      } catch (err) {
        const uploadError: UploadError = {
          code: 'UPLOAD_FAILED',
          message: err instanceof Error ? err.message : 'Upload failed',
          details: err,
        };
        setError(uploadError);
        throw uploadError;
      } finally {
        setIsUploading(false);
      }
    },
    [isOnboarding],
  );

  const reset = useCallback(() => {
    setIsUploading(false);
    setProgress(null);
    setError(null);
  }, []);

  return {
    isUploading,
    progress,
    error,
    uploadFile,
    reset,
  };
};
