/**
 * Historical Data Upload Component
 *
 * Handles the upload of historical financial data for temporal validation.
 * Provides a user-friendly interface for selecting and uploading files.
 */

import React, { useState, useCallback } from 'react';
import { Upload, FileText, AlertCircle } from 'lucide-react';
import { uploadHistoricalData } from '../../services/temporalValidationService';
import { validateFile, formatFileSize } from '../../services/uploadService';
import { useToast } from '@/shared/components/ui/use-toast';
import { BatchUploadDialog } from './BatchUploadDialog';

interface HistoricalDataUploadProps {
  onUploadSuccess: (uploadId: string, filename: string) => void;
  onCancel?: () => void;
}

export const HistoricalDataUpload: React.FC<HistoricalDataUploadProps> = ({
  onUploadSuccess,
  onCancel,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  const [showBatchDialog, setShowBatchDialog] = useState(false);
  const { toast } = useToast();

  const handleFile = useCallback(
    (selectedFile: File) => {
      const validation = validateFile(selectedFile);

      if (!validation.isValid) {
        toast({
          variant: 'destructive',
          title: 'Invalid file',
          description: validation.error || 'Invalid file',
        });
        return;
      }

      setFile(selectedFile);
    },
    [toast],
  );

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (e?.dataTransfer?.files && e?.dataTransfer?.files[0]) {
        handleFile(e?.dataTransfer?.files[0]);
      }
    },
    [handleFile],
  );

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e?.target?.files && e?.target?.files[0]) {
        handleFile(e?.target?.files[0]);
      }
    },
    [handleFile],
  );

  const handleUpload = async () => {
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const result = await uploadHistoricalData(file, (progress) => {
        setUploadProgress(progress);
      });

      toast({
        title: 'Success',
        description: 'Historical data uploaded successfully',
      });

      onUploadSuccess(result.upload_id, result.filename);
    } catch {
      toast({
        variant: 'destructive',
        title: 'Upload failed',
        description: 'Failed to upload historical data',
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-3">
      <div className="mb-4">
        <h3 className="text-sm font-semibold text-gray-900 mb-1">
          Upload Historical Financial Data
        </h3>
        <p className="text-xs text-muted-foreground">
          Upload your historical transaction data to train the AI on your
          specific categorization patterns. We&apos;ll validate the accuracy
          month by month to ensure {'>'}85% accuracy.
        </p>
      </div>

      <div
        className={`
          border-2 border-dashed rounded-lg p-4 text-center transition-colors
          ${dragActive ? 'border-blue-500 bg-info/5' : 'border-border hover:border-gray-400'}
          ${file ? 'bg-gray-50' : ''}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        {!file ? (
          <>
            <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
            <p className="text-xs text-muted-foreground mb-1">
              Drag and drop your file here, or click to browse
            </p>
            <p className="text-xs text-muted-foreground mb-2">
              Supported formats: CSV, Excel (XLSX, XLS) • Max size: 50MB
            </p>
            <label htmlFor="file-upload" className="cursor-pointer">
              <span className="inline-flex items-center px-3 py-1 border border-border rounded-md shadow-sm text-xs text-gray-700 bg-white hover:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Select File
              </span>
              <input
                id="file-upload"
                type="file"
                className="sr-only"
                accept=".csv,.xlsx,.xls"
                onChange={handleFileSelect}
                disabled={isUploading}
              />
            </label>
          </>
        ) : (
          <div className="space-y-4">
            <div className="flex flex-wrap items-center justify-center space-x-3">
              <FileText className="h-8 w-8 text-info" />
              <div className="text-left">
                <p className="font-medium truncategray-900">{file.name}</p>
                <p className="text-sm text-muted-foreground">
                  {formatFileSize(file.size)}
                </p>
              </div>
            </div>

            {isUploading && (
              <div className="w-full max-w-xs mx-auto">
                <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
                  <div
                    className="bg-green-600 h-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {uploadProgress}% uploaded
                </p>
              </div>
            )}

            {!isUploading && (
              <div className="flex flex-wrap justify-center space-x-3">
                <button
                  onClick={() => setFile(null)}
                  className="px-3 py-1 border border-border rounded-md shadow-sm text-xs text-gray-700 bg-white hover:bg-muted/50"
                >
                  Change File
                </button>
                <button
                  onClick={() => void handleUpload()}
                  className="px-3 py-1 border border-transparent rounded-md shadow-sm text-xs text-white bg-green-600 hover:bg-blue-700"
                >
                  Start Validation
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="mt-3 bg-info/5 rounded-lg p-3">
        <div className="flex items-start space-x-2">
          <AlertCircle className="h-4 w-4 text-info mt-0.5" />
          <div className="text-xs text-blue-800">
            <p className="font-medium mb-1">
              Requirements for Historical Data:
            </p>
            <ul className="list-disc list-inside space-y-0.5">
              <li>Include at least 12 months of transaction data</li>
              <li>
                Data should have date, description, amount, and category columns
              </li>
              <li>
                Categories should be consistently labeled throughout the file
              </li>
              <li>More data improves AI accuracy and training quality</li>
            </ul>
          </div>
        </div>
      </div>

      {onCancel && (
        <div className="mt-3 flex justify-end">
          <button
            onClick={() => void onCancel()}
            className="px-3 py-1 border border-border rounded-md shadow-sm text-xs text-gray-700 bg-white hover:bg-muted/50"
          >
            Cancel
          </button>
        </div>
      )}

      {/* Batch Upload Dialog */}
      <BatchUploadDialog
        isOpen={showBatchDialog}
        onClose={() => setShowBatchDialog(false)}
        onSuccess={(uploadId, totalTransactions) => {
          setShowBatchDialog(false);
          onUploadSuccess(
            uploadId,
            `${totalTransactions} transactions from batch upload`,
          );
        }}
      />
    </div>
  );
};
