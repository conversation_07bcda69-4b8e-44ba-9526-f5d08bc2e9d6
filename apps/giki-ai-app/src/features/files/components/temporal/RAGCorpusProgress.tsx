/**
 * RAG Corpus Progress Component
 *
 * Displays the progress of building the RAG corpus from historical data.
 * Shows real-time updates as the AI processes and learns from transactions.
 */

import React, { useCallback, useEffect, useState } from 'react';
import { Brain, CheckCircle, Loader2 } from 'lucide-react';
import {
  getRAGCorpusStatus,
  RAGCorpusStatus,
} from '../../services/temporalValidationService';
import { useInterval } from '@/shared/hooks/useInterval';

interface RAGCorpusProgressProps {
  uploadId: string;
  onComplete: () => void;
}

export const RAGCorpusProgress: React.FC<RAGCorpusProgressProps> = ({
  uploadId,
  onComplete,
}) => {
  const [status, setStatus] = useState<RAGCorpusStatus | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    try {
      const corpusStatus = await getRAGCorpusStatus(uploadId);
      setStatus(corpusStatus);

      if (corpusStatus.status === 'ready') {
        onComplete();
      } else if (corpusStatus.status === 'failed') {
        setError('Failed to build AI knowledge base');
      }
    } catch {
      setError('Failed to fetch corpus status');
    }
  }, [uploadId, onComplete]);

  useEffect(() => {
    void fetchStatus();
  }, [uploadId, fetchStatus]);

  // Poll for updates every 2 seconds while building
  useInterval(
    () => {
      if (status?.status === 'building') {
        void fetchStatus();
      }
    },
    status?.status === 'building' ? 2000 : null,
  );

  const getProgressPercentage = () => {
    if (!status) return 0;
    if (status.status === 'ready') return 100;
    if (status.total_transactions === 0) return 0;

    return Math.round(
      (status.processed_transactions / status.total_transactions) * 100,
    );
  };

  const getStatusIcon = () => {
    if (!status) return null;

    switch (status.status) {
      case 'building':
        return <Loader2 className="h-6 w-6 text-info animate-spin" />;
      case 'ready':
        return <CheckCircle className="h-6 w-6 text-success" />;
      case 'failed':
        return (
          <div className="h-6 w-6 rounded-full bg-destructive/10 flex flex-wrap items-center justify-center">
            <span className="text-destructive text-sm font-bold">!</span>
          </div>
        );
      default:
        return null;
    }
  };

  const getStatusMessage = () => {
    if (!status) return 'Initializing...';

    switch (status.status) {
      case 'building':
        return 'Building AI knowledge base from your historical data...';
      case 'ready':
        return 'AI knowledge base ready!';
      case 'failed':
        return 'Failed to build knowledge base';
      default:
        return 'Unknown status';
    }
  };

  if (error) {
    return (
      <div className="bg-red-50 rounded-lg p-6 text-center">
        <div className="text-destructive mb-2">{error}</div>
        <button
          onClick={() => {
            setError(null);
            void fetchStatus();
          }}
          className="text-sm text-destructive underline hover:no-underline"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex flex-wrap items-center space-x-4 mb-6">
        <div className="flex flex-wrap-shrink-0">
          <Brain className="h-10 w-10 text-info" />
        </div>
        <div className="flex flex-wrap-1">
          <h3 className="text-card-foreground-title text-foreground">
            Building AI Knowledge Base
          </h3>
          <p className="text-sm text-[hsl(var(--giki-text-muted))]">
            Processing your historical transactions to learn categorization
            patterns
          </p>
        </div>
        <div className="flex flex-wrap-shrink-0">{getStatusIcon()}</div>
      </div>

      <div className="space-y-4">
        <div>
          <div className="flex flex-wrap justify-between text-sm text-[hsl(var(--giki-text-muted))] mb-1">
            <span>{getStatusMessage()}</span>
            <span>{getProgressPercentage()}%</span>
          </div>
          <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
            <div
              className="bg-green-600 h-full transition-all duration-500"
              style={{ width: `${getProgressPercentage()}%` }}
            />
          </div>
        </div>

        {status && (
          <div className="grid grid-cols-2 gap-4 pt-4">
            <div className="bg-muted/50 rounded p-3">
              <p className="text-sm text-[hsl(var(--giki-text-muted))]">
                Transactions Processed
              </p>
              <p className="text-card-foreground-title text-gray-900">
                {status?.processed_transactions?.toLocaleString()} /{' '}
                {status?.total_transactions?.toLocaleString()}
              </p>
            </div>
            <div className="bg-muted/50 rounded p-3">
              <p className="text-sm text-[hsl(var(--giki-text-muted))]">
                Categories Discovered
              </p>
              <p className="text-card-foreground-title text-gray-900">
                {status.unique_categories}
              </p>
            </div>
            <div className="bg-muted/50 rounded p-3">
              <p className="text-sm text-[hsl(var(--giki-text-muted))]">
                Corpus Quality Score
              </p>
              <p className="text-card-foreground-title text-gray-900">
                {Math.round(status.corpus_quality_score * 100)}%
              </p>
            </div>
            <div className="bg-muted/50 rounded p-3">
              <p className="text-sm text-[hsl(var(--giki-text-muted))]">
                Processing Time
              </p>
              <p className="text-card-foreground-title text-gray-900">
                {status.build_time_seconds
                  ? `${Math.round(status.build_time_seconds)}s`
                  : '--'}
              </p>
            </div>
          </div>
        )}
      </div>

      {status?.status === 'ready' && (
        <div className="mt-6 bg-green-50 rounded-lg p-4">
          <p className="text-success-foreground text-sm">
            <span className="font-medium">Success!</span> The AI has learned
            from {status?.processed_transactions?.toLocaleString()} transactions
            across {status.unique_categories} categories. Ready to start
            validation.
          </p>
        </div>
      )}
    </div>
  );
};
