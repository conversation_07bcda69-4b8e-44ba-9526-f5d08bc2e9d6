/**
 * Progressive RAG Progress Component
 *
 * Shows real-time progress of progressive RAG corpus building for temporal validation.
 * Each month builds its own RAG corpus from all previous months' data.
 */

import React from 'react';
import {
  Database,
  CheckCircle2,
  Clock,
  ArrowRight,
  BarChart3,
} from 'lucide-react';
import { ProgressBar } from '@/shared/components/ui/ProgressBar';

interface CorpusProgress {
  month: string;
  corpus_name: string;
  training_data_size: number;
  status: 'building' | 'completed' | 'failed';
  progress_percentage: number;
  started_at?: string;
  completed_at?: string;
}

interface ProgressiveRAGProgressProps {
  corpus_progress: CorpusProgress[];
  current_month?: string;
  overall_progress: number;
}

export const ProgressiveRAGProgress: React.FC<ProgressiveRAGProgressProps> = ({
  corpus_progress,
  current_month,
  overall_progress,
}) => {
  const formatMonth = (month: string) => {
    try {
      return new Date(month + '-01').toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric',
      });
    } catch {
      return month;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="h-5 w-5 text-success" />;
      case 'building':
        return <Clock className="h-5 w-5 text-info animate-spin" />;
      case 'failed':
        return <Database className="h-5 w-5 text-destructive" />;
      default:
        return <Database className="h-5 w-5 truncategray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-success';
      case 'building':
        return 'text-success';
      case 'failed':
        return 'text-error';
      default:
        return 'text-gray-600';
    }
  };

  const getProgressBarColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-600';
      case 'building':
        return 'bg-green-600';
      case 'failed':
        return 'bg-red-600';
      default:
        return 'bg-gray-300';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-border p-6">
      <div className="mb-6">
        <div className="flex flex-wrap items-center space-x-3 mb-2">
          <Database className="h-6 w-6 text-info" />
          <h3 className="text-card-foreground-title text-gray-900">
            Progressive RAG Corpus Building
          </h3>
        </div>
        <p className="text-sm text-muted-foreground mb-4">
          Each month builds its own RAG corpus from all previous months&apos;
          data for accurate temporal validation
        </p>

        {/* Overall Progress */}
        <div className="mb-4">
          <div className="flex flex-wrap justify-between text-sm text-muted-foreground mb-1">
            <span>Overall Corpus Building Progress</span>
            <span>{overall_progress.toFixed(1)}%</span>
          </div>
          <ProgressBar value={overall_progress} className="h-3" />
        </div>
      </div>

      {/* Individual Corpus Progress */}
      <div className="space-y-4">
        {corpus_progress.map((corpus, index) => {
          const isActive = corpus.month === current_month;
          const cumulativeData = corpus_progress
            .slice(0, index + 1)
            .reduce((sum, c) => sum + c.training_data_size, 0);

          return (
            <div
              key={corpus.month}
              className={`border rounded-lg p-4 transition-all ${
                isActive
                  ? 'border-blue-300 bg-info/5'
                  : corpus.status === 'completed'
                    ? 'border-green-200 bg-green-50'
                    : 'border-border bg-muted/50'
              }`}
            >
              <div className="flex flex-wrap items-center justify-between mb-3">
                <div className="flex flex-wrap items-center space-x-3">
                  {getStatusIcon(corpus.status)}
                  <div>
                    <h4 className="font-medium truncategray-900">
                      {formatMonth(corpus.month)}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {corpus.corpus_name}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p
                    className={`text-sm font-medium ${getStatusColor(corpus.status)}`}
                  >
                    {corpus.status === 'building' && 'Building...'}
                    {corpus.status === 'completed' && 'Complete'}
                    {corpus.status === 'failed' && 'Failed'}
                  </p>
                  <p className="truncate text-caption text-muted-foreground">
                    {corpus.status === 'building' &&
                      `${corpus.progress_percentage.toFixed(0)}%`}
                    {corpus.status === 'completed' &&
                      corpus.completed_at &&
                      `Completed ${new Date(corpus.completed_at).toLocaleTimeString()}`}
                  </p>
                </div>
              </div>

              {/* Progress Bar for Building Status */}
              {corpus.status === 'building' && (
                <div className="mb-3">
                  <ProgressBar
                    value={corpus.progress_percentage}
                    className="h-2"
                    barClassName={getProgressBarColor(corpus.status)}
                  />
                </div>
              )}

              {/* Data Statistics */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex flex-wrap items-center space-x-2">
                  <BarChart3 className="h-4 w-4 truncategray-400" />
                  <span className="text-muted-foreground">Training Data:</span>
                  <span className="font-medium truncategray-900">
                    {corpus.training_data_size.toLocaleString()} transactions
                  </span>
                </div>
                <div className="flex flex-wrap items-center space-x-2">
                  <ArrowRight className="h-4 w-4 truncategray-400" />
                  <span className="text-muted-foreground">Cumulative:</span>
                  <span className="font-medium truncategray-900">
                    {cumulativeData.toLocaleString()} total
                  </span>
                </div>
              </div>

              {/* Progressive Learning Explanation */}
              {index === 0 && corpus.status === 'building' && (
                <div className="mt-3 p-3 bg-info/10 rounded-lg">
                  <p className="truncate text-caption text-blue-800">
                    <strong>Progressive Learning:</strong> This corpus includes{' '}
                    {corpus.training_data_size.toLocaleString()} transactions
                    from all previous months (Jan-
                    {formatMonth(corpus.month).split(' ')[0]}) to train the AI
                    for this month&apos;s validation.
                  </p>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Progressive Learning Summary */}
      {corpus_progress.length > 0 && (
        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="font-medium truncategray-900 mb-2">
            How Progressive Learning Works
          </h4>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>
              • Each test month builds its own RAG corpus from all{' '}
              <strong>previous</strong> months
            </p>
            <p>• July uses Jan-June data, August uses Jan-July data, etc.</p>
            <p>
              • This simulates real-world deployment where the AI learns
              progressively
            </p>
            <p>
              • Achieves realistic accuracy measurements instead of 0% from
              empty corpora
            </p>
          </div>
        </div>
      )}
    </div>
  );
};
