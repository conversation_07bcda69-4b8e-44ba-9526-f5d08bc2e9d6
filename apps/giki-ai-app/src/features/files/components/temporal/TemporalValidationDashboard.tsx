/**
 * Temporal Validation Dashboard Component
 *
 * Visualizes the month-by-month accuracy validation progress.
 * Shows current accuracy, target threshold, and detailed results.
 */

import React, { useEffect, useState, useCallback } from 'react';
import {
  TrendingUp,
  Calendar,
  Target,
  AlertTriangle,
  CheckCircle2,
} from 'lucide-react';
import {
  getTemporalValidationStatus,
  TemporalValidationStatus,
  formatValidationStatus,
  meetsAccuracyThreshold,
  calculateValidationProgress,
} from '../../services/temporalValidationService';
import { useInterval } from '@/shared/hooks/useInterval';
import { ProgressBar } from '@/shared/components/ui/ProgressBar';
import { ProgressiveRAGProgress } from './ProgressiveRAGProgress';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts';

interface TemporalValidationDashboardProps {
  uploadId: string;
  onComplete: () => void;
  targetAccuracy?: number;
}

export const TemporalValidationDashboard: React.FC<
  TemporalValidationDashboardProps
> = ({ uploadId, onComplete, targetAccuracy = 85 }) => {
  const [status, setStatus] = useState<TemporalValidationStatus | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    try {
      const validationStatus = await getTemporalValidationStatus(uploadId);
      setStatus(validationStatus);

      if (
        validationStatus.status === 'completed' &&
        meetsAccuracyThreshold(validationStatus, targetAccuracy)
      ) {
        onComplete();
      }
    } catch {
      setError('Failed to fetch validation status');
    }
  }, [uploadId, targetAccuracy, onComplete]);

  useEffect(() => {
    void fetchStatus();
  }, [uploadId, fetchStatus]);

  // Poll for updates every 3 seconds while validating
  useInterval(
    () => {
      if (
        status?.status === 'validating' ||
        status?.status === 'building_corpus'
      ) {
        void fetchStatus();
      }
    },
    status?.status === 'validating' || status?.status === 'building_corpus'
      ? 3000
      : null,
  );

  const getChartData = () => {
    if (!status?.monthly_results) return [];

    return status?.monthly_results?.map((result) => ({
      month: new Date(result.month).toLocaleDateString('en-US', {
        month: 'short',
        year: '2-digit',
      }),
      accuracy: Math.round(result.accuracy * 100) / 100,
      target: targetAccuracy,
    }));
  };

  const getOverallProgress = () => {
    if (!status) return 0;
    return calculateValidationProgress(status);
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= targetAccuracy) return 'text-success';
    if (accuracy >= targetAccuracy - 10) return 'text-yellow-600';
    return 'text-error';
  };

  if (error) {
    return (
      <div className="bg-red-50 rounded-lg p-6 text-center">
        <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-3" />
        <div className="truncatered-800 font-medium mb-2">{error}</div>
        <button
          onClick={() => {
            setError(null);
            void fetchStatus();
          }}
          className="text-sm text-destructive underline hover:no-underline"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!status) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/2 mx-auto mb-4"></div>
          <div className="h-4 bg-muted rounded w-3/4 mx-auto"></div>
        </div>
      </div>
    );
  }

  const accuracyMet = meetsAccuracyThreshold(status, targetAccuracy);

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-wrap items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-foreground">
              Temporal Accuracy Validation
            </h2>
            <p className="text-muted-foreground mt-1">
              {formatValidationStatus(status.status)}
            </p>
          </div>
          <div className="text-right">
            <div className="truncate text-heading-1">
              <span className={getAccuracyColor(status.overall_accuracy)}>
                {status?.overall_accuracy?.toFixed(1)}%
              </span>
            </div>
            <p className="text-body-small">Overall Accuracy</p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex flex-wrap justify-between text-body-small mb-1">
            <span>Validation Progress</span>
            <span>{getOverallProgress()}%</span>
          </div>
          <ProgressBar value={getOverallProgress()} className="h-3" />
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-muted/50 rounded-lg p-3">
            <div className="flex flex-wrap items-center space-x-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <span className="text-body-small">Months Validated</span>
            </div>
            <p className="text-card-foreground-title mt-1">
              {status.months_validated} / {status.total_months}
            </p>
          </div>

          <div className="bg-muted/50 rounded-lg p-3">
            <div className="flex flex-wrap items-center space-x-2">
              <Target className="h-5 w-5 text-muted-foreground" />
              <span className="text-body-small">Target Accuracy</span>
            </div>
            <p className="text-card-foreground-title mt-1">{targetAccuracy}%</p>
          </div>

          <div className="bg-muted/50 rounded-lg p-3">
            <div className="flex flex-wrap items-center space-x-2">
              {accuracyMet ? (
                <CheckCircle2 className="h-5 w-5 text-success" />
              ) : (
                <TrendingUp className="h-5 w-5 text-muted-foreground" />
              )}
              <span className="text-body-small">Status</span>
            </div>
            <p
              className={`text-lg font-semibold mt-1 ${accuracyMet ? 'text-success' : 'text-foreground'}`}
            >
              {accuracyMet ? 'Target Met' : 'In Progress'}
            </p>
          </div>
        </div>
      </div>

      {/* Progressive RAG Corpus Building Progress */}
      {status?.status === 'building_corpus' ||
      status?.status === 'validating' ? (
        <div className="mt-6">
          <ProgressiveRAGProgress
            corpus_progress={[
              {
                month: '2024-07',
                corpus_name: `temporal_validation_1_2024-07`,
                training_data_size: 1200,
                status:
                  status?.status === 'building_corpus'
                    ? 'building'
                    : 'completed',
                progress_percentage:
                  status?.status === 'building_corpus' ? 60 : 100,
                started_at: new Date().toISOString(),
                completed_at:
                  (status?.status as string) === 'completed'
                    ? new Date().toISOString()
                    : undefined,
              },
              {
                month: '2024-08',
                corpus_name: `temporal_validation_1_2024-08`,
                training_data_size: 1450,
                status:
                  status?.months_validated >= 2
                    ? 'completed'
                    : status?.months_validated >= 1
                      ? 'building'
                      : 'building',
                progress_percentage:
                  status?.months_validated >= 2
                    ? 100
                    : status?.months_validated >= 1
                      ? 30
                      : 0,
              },
              {
                month: '2024-09',
                corpus_name: `temporal_validation_1_2024-09`,
                training_data_size: 1700,
                status:
                  status?.months_validated >= 3 ? 'completed' : 'building',
                progress_percentage: status?.months_validated >= 3 ? 100 : 0,
              },
            ]}
            current_month={
              status?.status === 'building_corpus' ? '2024-07' : undefined
            }
            overall_progress={getOverallProgress()}
          />
        </div>
      ) : null}

      {/* Accuracy Chart */}
      {status?.monthly_results?.length > 0 && (
        <div className="mt-6">
          <h3 className="text-card-foreground-title text-foreground mb-3">
            Monthly Accuracy Trend
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={getChartData()}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--giki-neutral-100))"
                />
                <XAxis dataKey="month" stroke="hsl(var(--giki-neutral-500))" />
                <YAxis
                  stroke="hsl(var(--giki-neutral-500))"
                  domain={[0, 100]}
                />
                <Tooltip
                  formatter={(value: number) => `${value}%`}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--giki-background))',
                    border: '1px solid #e5e7eb',
                  }}
                />
                <ReferenceLine
                  y={targetAccuracy}
                  stroke="#ef4444"
                  strokeDasharray="5 5"
                  label="Target"
                />
                <Line
                  type="monotone"
                  dataKey="accuracy"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ fill: '#3b82f6' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {/* Monthly Results Table */}
      {status?.monthly_results?.length > 0 && (
        <div className="mt-6">
          <h3 className="text-card-foreground-title text-foreground mb-3">
            Detailed Results
          </h3>
          <div className="overflow-x-auto">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-muted/50">
                  <tr>
                    <th className="px-4 py-3 text-left text-caption font-medium text-muted-foreground uppercase tracking-wider">
                      Month
                    </th>
                    <th className="px-4 py-3 text-left text-caption font-medium text-muted-foreground uppercase tracking-wider">
                      Accuracy
                    </th>
                    <th className="px-4 py-3 text-left text-caption font-medium text-muted-foreground uppercase tracking-wider">
                      Predictions
                    </th>
                    <th className="px-4 py-3 text-left text-caption font-medium text-muted-foreground uppercase tracking-wider">
                      Categories
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {status?.monthly_results?.map((result, index) => (
                    <tr key={index}>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 max-w-0 overflow-hidden">
                        <div className="truncate">
                          {new Date(result.month).toLocaleDateString('en-US', {
                            month: 'long',
                            year: 'numeric',
                          })}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap max-w-0 overflow-hidden">
                        <div className="truncate">
                          <span
                            className={`text-sm font-medium ${getAccuracyColor(result.accuracy)}`}
                          >
                            {result?.accuracy?.toFixed(1)}%
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground max-w-0 overflow-hidden">
                        <div className="truncate">
                          {result.correct_predictions} /{' '}
                          {result.total_predictions}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground max-w-0 overflow-hidden">
                        <div className="truncate">
                          {result?.categories_found?.length}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {status.status === 'completed' && (
        <div className="mt-6 flex flex-wrap justify-end">
          {accuracyMet ? (
            <button
              onClick={() => void onComplete()}
              className="inline-flex flex-wrap items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-label text-white bg-green-600 hover:bg-green-700"
            >
              <CheckCircle2 className="h-4 w-4 mr-2" />
              Continue to Dashboard
            </button>
          ) : (
            <div className="text-center">
              <p className="text-destructive text-sm mb-3">
                Accuracy below {targetAccuracy}% threshold. Consider uploading
                more historical data.
              </p>
              <button
                onClick={() => window?.location?.reload()}
                className="inline-flex flex-wrap items-center px-4 py-2 border border-border rounded-md shadow-sm text-label text-gray-700 bg-white hover:bg-muted/50"
              >
                Upload More Data
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
