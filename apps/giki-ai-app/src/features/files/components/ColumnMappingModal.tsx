import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/shared/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import { Label } from '@/shared/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { Badge } from '@/shared/components/ui/badge';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import { Separator } from '@/shared/components/ui/separator';
import { ScrollArea } from '@/shared/components/ui/scroll-area';
import {
  AlertCircle,
  Calendar,
  DollarSign,
  FileText,
  Tag,
  CheckCircle2,
  ArrowRight,
  Info,
  Brain,
  Zap,
  TrendingUp,
} from 'lucide-react';
import { Loading } from '@/shared/components/ui/loading';
import { getFileColumns } from '@/features/files/services/fileService'; // API function to fetch columns
import {
  getSchemaInterpretation,
  ColumnMapping as AIColumnMapping,
} from '@/features/files/services/schemaInterpretationService'; // AI interpretation service
import { isApiError } from '@/shared/utils/errorHandling';

interface ColumnMappingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (uploadId: string, mapping: Record<string, string | null>) => void;
  uploadId: string | null;
}

// Essential fields required for basic transaction functionality
const ESSENTIAL_FIELDS = ['date', 'description'];

// Amount fields - at least one must be mapped for transaction processing
const AMOUNT_FIELDS = [
  'amount',
  'credit',
  'debit',
  'credit_amount',
  'debit_amount',
  'transaction_amount',
];

// Field metadata for UI display - expandable as AI discovers new field types
const FIELD_METADATA: Record<
  string,
  {
    name: string;
    icon: React.ComponentType<{ className?: string }>;
    description: string;
    required?: boolean;
  }
> = {
  date: {
    name: 'Date',
    icon: Calendar,
    description: 'Transaction date (e.g., 2023-07-15)',
    required: true,
  },
  description: {
    name: 'Description',
    icon: FileText,
    description: 'Transaction description or memo',
    required: true,
  },
  amount: {
    name: 'Amount',
    icon: DollarSign,
    description:
      'Transaction amount (positive for income, negative for expenses)',
    required: false, // Not required since other amount fields can be used
  },
  transaction_type: {
    name: 'Transaction Type',
    icon: TrendingUp,
    description: 'Type of transaction (e.g., debit, credit, income, expense)',
    required: false, // Context-dependent
  },
  category: {
    name: 'Category',
    icon: Tag,
    description: 'Transaction category (basic level)',
    required: false,
  },
  category_l1: {
    name: 'Category L1',
    icon: Tag,
    description: 'Top-level category hierarchy',
    required: false,
  },
  category_l2: {
    name: 'Category L2',
    icon: Tag,
    description: 'Second-level category hierarchy',
    required: false,
  },
  category_l3: {
    name: 'Category L3',
    icon: Tag,
    description: 'Third-level category hierarchy',
    required: false,
  },
  reference: {
    name: 'Reference',
    icon: FileText,
    description: 'Transaction reference or ID',
    required: false,
  },
  merchant: {
    name: 'Merchant',
    icon: FileText,
    description: 'Merchant or vendor name',
    required: false,
  },
  credit: {
    name: 'Credit Amount',
    icon: DollarSign,
    description: 'Credit/deposit amounts (positive values)',
    required: false,
  },
  debit: {
    name: 'Debit Amount',
    icon: DollarSign,
    description: 'Debit/expense amounts (positive values)',
    required: false,
  },
  credit_amount: {
    name: 'Credit Amount',
    icon: DollarSign,
    description: 'Credit/income amounts',
    required: false,
  },
  debit_amount: {
    name: 'Debit Amount',
    icon: DollarSign,
    description: 'Debit/expense amounts',
    required: false,
  },
  transaction_amount: {
    name: 'Transaction Amount',
    icon: DollarSign,
    description: 'Single amount column (positive/negative values)',
    required: false,
  },
};

export const ColumnMappingModal: React.FC<ColumnMappingModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  uploadId,
}) => {
  const [fileColumns, setFileColumns] = useState<string[]>([]);
  const [mapping, setMapping] = useState<Record<string, string | null>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('mapping');
  const [sampleData, setSampleData] = useState<string[][]>([]);
  const [autoMappingApplied, setAutoMappingApplied] = useState(false);
  const [aiInterpretation, setAiInterpretation] = useState<
    AIColumnMapping[] | null
  >(null);
  const [aiOverallConfidence, setAiOverallConfidence] = useState(0);
  const [apiCallInProgress, setApiCallInProgress] = useState(false);

  // Track which uploadIds have been processed to prevent duplicate calls
  const processedUploadIdsRef = useRef<Set<string>>(new Set());

  const NONE_OPTION_VALUE = '__NONE_VALUE__'; // Unique constant for the "None" option

  useEffect(() => {
    if (
      isOpen &&
      uploadId &&
      !apiCallInProgress &&
      !processedUploadIdsRef.current.has(uploadId)
    ) {
      setIsLoading(true);
      setError(null);
      setApiCallInProgress(true);

      // Mark this uploadId as being processed
      processedUploadIdsRef.current.add(uploadId);

      // Call both columns service and AI interpretation in parallel with timeout
      // Starting parallel calls for column and AI interpretation

      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(
          () => reject(new Error('Request timeout after 30 seconds')),
          30000,
        ),
      );

      // Use Promise.race with proper typing
      Promise.race([
        Promise.all([
          getFileColumns(uploadId),
          getSchemaInterpretation(uploadId),
        ]),
        timeoutPromise,
      ])
        .then(([columnsResponse, aiResponse]) => {
          // Received responses from backend services

          // Handle columns response - ensure we always have a valid array
          let finalColumns: string[] = [];

          // Processing columns response from backend

          if (isApiError(columnsResponse)) {
            // Error fetching file columns - handled by error state
            setError(columnsResponse.message);
            setFileColumns([]);
            setIsLoading(false);
            return;
          }

          if (Array.isArray(columnsResponse)) {
            finalColumns = columnsResponse;
            // Columns extracted successfully
          } else {
            // Unexpected columns response format - handled by error state
            setError(
              'Failed to load file columns. The file may not have been processed correctly.',
            );
            setFileColumns([]);
            setIsLoading(false);
            return;
          }

          // Ensure finalColumns is valid before proceeding
          if (!Array.isArray(finalColumns) || finalColumns.length === 0) {
            // finalColumns is not a valid array - handled by error state
            setError('No columns found in uploaded file.');
            setFileColumns([]);
            setIsLoading(false);
            return;
          }

          // Setting file columns for mapping interface
          setFileColumns(finalColumns);

          // Debug logging
          // Setting file columns state for mapping interface

          // Force log to confirm this runs
          if (typeof window !== 'undefined') {
            // Column mapping debug information available in dev tools
          }

          // Handle AI interpretation response
          const initialMapping: Record<string, string | null> = {};
          let autoMappingCount = 0;

          if (isApiError(aiResponse)) {
            // AI interpretation failed, falling back to pattern matching

            // Fallback: Try to map only essential fields using basic pattern matching
            ESSENTIAL_FIELDS.forEach((fieldId) => {
              const metadata = FIELD_METADATA[fieldId];
              if (!metadata) return;

              const foundColumn = finalColumns.find((col: string) => {
                const normalizedCol = col.toLowerCase().replace(/[\s_-]/g, '');
                const normalizedFieldId = fieldId
                  .toLowerCase()
                  .replace(/[\s_-]/g, '');
                return (
                  normalizedCol.includes(normalizedFieldId) ||
                  normalizedCol === normalizedFieldId
                );
              });

              if (foundColumn) {
                autoMappingCount++;
              }
              initialMapping[fieldId] = foundColumn || null;
            });

            setAutoMappingApplied(autoMappingCount > 0);
          } else {
            // Use ALL AI interpretation results dynamically
            setAiInterpretation(aiResponse.column_mappings);
            setAiOverallConfidence(aiResponse.overall_confidence);

            // Convert ALL AI mappings to our mapping format (not just standard fields)
            if (aiResponse.column_mappings) {
              aiResponse?.column_mappings?.forEach((aiMapping) => {
                const fieldId = aiMapping.mapped_field;

                // Skip unmapped fields
                if (fieldId === 'unmapped') return;

                initialMapping[fieldId] = aiMapping.original_name;
                autoMappingCount++;
              });
            }

            setAutoMappingApplied(autoMappingCount > 0);
          }

          setMapping(initialMapping);

          // Use real sample data from API response
          if (aiResponse && !isApiError(aiResponse) && aiResponse.sample_data) {
            setSampleData(aiResponse.sample_data);
          } else {
            // If no sample data available, set empty array to show proper empty state
            setSampleData([]);
          }

          // Explicitly set loading to false after successful processing
          setIsLoading(false);
        })
        .catch((err) => {
          // Error fetching data - handled by error state

          // Handle timeout specifically
          if (err instanceof Error && err?.message?.includes('timeout')) {
            setError(
              'File analysis is taking longer than expected. Please try again.',
            );
          } else {
            setError(
              err instanceof Error ? err.message : 'Failed to load file data.',
            );
          }
        })
        .finally(() => {
          // Column mapping preparation completed
          setIsLoading(false);
          setApiCallInProgress(false);
        });
    } else if (!isOpen) {
      // Reset when closed
      setFileColumns([]);
      setMapping({});
      setSampleData([]);
      setAutoMappingApplied(false);
      setAiInterpretation(null);
      setAiOverallConfidence(0);
      setActiveTab('mapping');
      setApiCallInProgress(false);
      // Clear the processed uploadIds when modal closes
      processedUploadIdsRef.current.clear();
    }
  }, [isOpen, uploadId, apiCallInProgress]);

  const handleSelectChange = (
    standardFieldId: string,
    fileColumn: string | null,
  ) => {
    setMapping((prevMapping) => ({
      ...prevMapping,
      // Ensure that if fileColumn is the NONE_OPTION_VALUE, we store null.
      [standardFieldId]: fileColumn === NONE_OPTION_VALUE ? null : fileColumn,
    }));
  };

  const handleConfirmClick = () => {
    if (uploadId) {
      // Check essential fields (date, description)
      const unmappedEssentialFields = ESSENTIAL_FIELDS.filter(
        (fieldId) => !mapping[fieldId],
      );

      if (unmappedEssentialFields.length > 0) {
        const fieldNames = unmappedEssentialFields
          .map((fieldId) => FIELD_METADATA[fieldId]?.name || fieldId)
          .join(', ');
        setError(`Please map all essential fields: ${fieldNames}`);
        return;
      }

      // Check that at least one amount field is mapped
      const mappedAmountFields = AMOUNT_FIELDS.filter(
        (fieldId) => mapping[fieldId],
      );
      if (mappedAmountFields.length === 0) {
        setError(
          'Please map at least one amount field (amount, credit, debit, etc.)',
        );
        return;
      }

      setError(null);
      onConfirm(uploadId, mapping);
    } else {
      setError('Upload ID is missing. Cannot confirm mapping.');
    }
  };

  // Get the mapped column index for a field
  const getMappedColumnIndex = (fieldId: string): number => {
    const mappedColumn = mapping[fieldId];
    if (!mappedColumn) return -1;
    return fileColumns.indexOf(mappedColumn);
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open: boolean) => !open && onClose()}>
      <DialogContent className="sm:max-w-full sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl text-primary">
            Map Your File Columns
          </DialogTitle>
          <DialogDescription>
            Match your file&apos;s columns to our standard transaction fields
            for accurate data import.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex flex-wrap items-center justify-center py-12">
            <Loading size="lg" text="Analyzing your file..." />
          </div>
        ) : error ? (
          <Alert variant="destructive" className="my-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : fileColumns.length === 0 ? (
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="truncate text-heading-5">No columns found</h3>
            <p className="text-muted-foreground mt-1 max-w-md mx-auto">
              We couldn&apos;t detect any columns in your file. Please check the
              file format and try again.
            </p>
            {/* Debug info */}
            <div className="mt-4 truncate text-caption text-muted-foreground">
              [DEBUG] fileColumns.length = {fileColumns.length}, isLoading ={' '}
              {isLoading.toString()}, error = {error || 'null'}
              <br />
              [DEBUG] uploadId = {uploadId}, fileColumns ={' '}
              {JSON.stringify(fileColumns)}
              <br />
              [DEBUG] processedUploadIds ={' '}
              {Array.from(processedUploadIdsRef.current).join(', ')}
            </div>
          </div>
        ) : (
          <>
            {autoMappingApplied && (
              <Alert className="mb-4 bg-primary/10 border-primary/30">
                {aiInterpretation ? (
                  <Brain className="h-4 w-4 text-primary" />
                ) : (
                  <Info className="h-4 w-4 text-primary" />
                )}
                <AlertTitle>
                  {aiInterpretation
                    ? 'mapping applied'
                    : 'Auto-mapping applied'}
                </AlertTitle>
                <AlertDescription>
                  {aiInterpretation ? (
                    <>
                      Our AI has analyzed your file structure and automatically
                      mapped columns with{' '}
                      {(aiOverallConfidence * 100).toFixed(0)}% confidence.
                      Please review and adjust if needed.
                    </>
                  ) : (
                    <>
                      We&apos;ve automatically mapped some columns based on
                      their names. Please review and adjust if needed.
                    </>
                  )}
                </AlertDescription>
              </Alert>
            )}

            <Tabs
              defaultValue="mapping"
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-2 mb-4 p-1.5">
                <TabsTrigger value="mapping" className="font-medium">
                  <Tag className="mr-2 h-4 w-4" />
                  Column Mapping
                </TabsTrigger>
                <TabsTrigger value="preview" className="font-medium">
                  <FileText className="mr-2 h-4 w-4" />
                  Data Preview
                </TabsTrigger>
              </TabsList>

              <TabsContent value="mapping" className="mt-0 space-y-4">
                <ScrollArea className="h-[350px] overflow-y-auto pr-4">
                  <div className="space-y-6">
                    {/* Debug: Log when rendering mapping fields */}
                    {/* Show standard fields if mapping is empty */}
                    {Object.keys(mapping).length === 0 ? (
                      <>
                        {/* Show essential fields first */}
                        {ESSENTIAL_FIELDS.map((fieldId) => {
                          const field = FIELD_METADATA[fieldId];
                          if (!field) return null;

                          return (
                            <div className="space-y-2" key={fieldId}>
                              <div className="flex flex-wrap items-center justify-between">
                                <div className="flex flex-wrap items-center">
                                  <field.icon className="h-5 w-5 text-muted-foreground mr-2" />
                                  <Label
                                    htmlFor={fieldId}
                                    className="font-medium text-primary"
                                  >
                                    {field.name}
                                    <span className="text-destructive ml-1">
                                      *
                                    </span>
                                  </Label>
                                </div>
                              </div>

                              <p className="text-body-small mb-2">
                                {field.description}
                              </p>

                              <Select
                                value={mapping[fieldId] || NONE_OPTION_VALUE}
                                onValueChange={(value: string) =>
                                  handleSelectChange(fieldId, value)
                                }
                              >
                                <SelectTrigger
                                  className="w-full focus:ring-primary"
                                  data-testid={`mapping-select-${fieldId}`}
                                >
                                  <SelectValue placeholder="-- Select a column from your file --" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value={NONE_OPTION_VALUE}>
                                    -- None --
                                  </SelectItem>
                                  {fileColumns.map((column) => (
                                    <SelectItem key={column} value={column}>
                                      {column}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          );
                        })}

                        {/* Show amount fields */}
                        <Separator />
                        <div className="text-body-small">
                          Map at least one amount field:
                        </div>
                        {AMOUNT_FIELDS.slice(0, 3).map((fieldId) => {
                          const field = FIELD_METADATA[fieldId];
                          if (!field) return null;

                          return (
                            <div className="space-y-2" key={fieldId}>
                              <div className="flex flex-wrap items-center justify-between">
                                <div className="flex flex-wrap items-center">
                                  <field.icon className="h-5 w-5 text-muted-foreground mr-2" />
                                  <Label
                                    htmlFor={fieldId}
                                    className="font-medium text-primary"
                                  >
                                    {field.name}
                                  </Label>
                                </div>
                                <Badge
                                  variant="outline"
                                  className="text-caption bg-primary/10 text-primary border-primary/30 max-w-[150px] truncate"
                                >
                                  Optional
                                </Badge>
                              </div>

                              <p className="text-body-small mb-2">
                                {field.description}
                              </p>

                              <Select
                                value={mapping[fieldId] || NONE_OPTION_VALUE}
                                onValueChange={(value: string) =>
                                  handleSelectChange(fieldId, value)
                                }
                              >
                                <SelectTrigger
                                  className="w-full focus:ring-primary"
                                  data-testid={`mapping-select-${fieldId}`}
                                >
                                  <SelectValue placeholder="-- Select a column from your file --" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value={NONE_OPTION_VALUE}>
                                    -- None --
                                  </SelectItem>
                                  {fileColumns.map((column) => (
                                    <SelectItem key={column} value={column}>
                                      {column}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          );
                        })}
                      </>
                    ) : (
                      Object.keys(mapping).map((fieldId) => {
                        // Determine if field is required based on business logic
                        const isFieldRequired =
                          ESSENTIAL_FIELDS.includes(fieldId) ||
                          (AMOUNT_FIELDS.includes(fieldId) &&
                            !AMOUNT_FIELDS.some(
                              (f) => f !== fieldId && mapping[f],
                            ));

                        const field = FIELD_METADATA[fieldId] || {
                          name:
                            fieldId.charAt(0).toUpperCase() +
                            fieldId.slice(1).replace(/_/g, ' '),
                          icon: FileText,
                          description: `AI-discovered field: ${fieldId}`,
                          required: isFieldRequired,
                        };

                        // Override required status for dynamic logic
                        field.required = isFieldRequired;
                        return (
                          <div className="space-y-2" key={fieldId}>
                            <div className="flex flex-wrap items-center justify-between">
                              <div className="flex flex-wrap items-center">
                                <field.icon className="h-5 w-5 text-muted-foreground mr-2" />
                                <Label
                                  htmlFor={fieldId}
                                  className="font-medium text-primary"
                                >
                                  {field.name}
                                  {field.required && (
                                    <span className="text-destructive ml-1">
                                      *
                                    </span>
                                  )}
                                </Label>
                              </div>

                              {!field.required && (
                                <Badge
                                  variant="outline"
                                  className="text-caption bg-primary/10 text-primary border-primary/30 max-w-[150px] truncate"
                                >
                                  Optional
                                </Badge>
                              )}
                            </div>

                            <p className="text-body-small mb-2">
                              {field.description}
                            </p>

                            <Select
                              value={mapping[fieldId] || NONE_OPTION_VALUE}
                              onValueChange={(value: string) =>
                                handleSelectChange(
                                  fieldId,
                                  value, // Pass the raw value, handle NONE_OPTION_VALUE in handleSelectChange
                                )
                              }
                            >
                              <SelectTrigger
                                className="w-full focus:ring-primary"
                                data-testid={`mapping-select-${fieldId}`}
                              >
                                <SelectValue placeholder="-- Select a column from your file --" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem
                                  value={NONE_OPTION_VALUE}
                                  className="text-muted-foreground"
                                >
                                  -- None --
                                </SelectItem>
                                {fileColumns.map((col) => (
                                  <SelectItem
                                    key={col}
                                    value={col}
                                    className="focus:bg-primary/10 focus:text-primary"
                                  >
                                    {col}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>

                            {/* AI Interpretation Information */}
                            {aiInterpretation &&
                              Array.isArray(aiInterpretation) &&
                              (() => {
                                const aiMapping = aiInterpretation.find(
                                  (mapping) => mapping.mapped_field === fieldId,
                                );
                                return aiMapping ? (
                                  <div className="mt-2 p-2 bg-info/10 border border-info/20 rounded-md">
                                    <div className="flex flex-wrap items-center space-x-2">
                                      <Zap className="h-3 w-3 text-info" />
                                      <span className="truncate text-caption font-medium text-info">
                                        AI Confidence:{' '}
                                        {(aiMapping.confidence * 100).toFixed(
                                          0,
                                        )}
                                        %
                                      </span>
                                      <TrendingUp className="h-3 w-3 text-info" />
                                    </div>
                                    <p className="truncate text-caption text-info mt-1">
                                      {aiMapping.reasoning}
                                    </p>
                                  </div>
                                ) : null;
                              })()}

                            <Separator className="mt-4" />
                          </div>
                        );
                      })
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="preview" className="mt-0">
                <div className="space-y-4">
                  <div className="bg-muted p-4 rounded-md">
                    <h3 className="font-medium mb-2">
                      Data Preview with Mapping
                    </h3>
                    <p className="text-body-small mb-4">
                      This shows how your data will be imported based on your
                      current mapping.
                    </p>

                    <div className="border rounded-md overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="bg-muted">
                              {Object.keys(mapping).map((fieldId) => {
                                const field = FIELD_METADATA[fieldId] || {
                                  name:
                                    fieldId.charAt(0).toUpperCase() +
                                    fieldId.slice(1).replace(/_/g, ' '),
                                  icon: FileText,
                                };
                                return (
                                  <th
                                    key={fieldId}
                                    className="p-2 text-left font-medium border-r last:border-r-0"
                                  >
                                    <div className="flex flex-wrap items-center">
                                      <field.icon className="h-4 w-4 mr-1 text-primary" />
                                      <span className="text-primary">
                                        {field.name}
                                      </span>
                                    </div>
                                    {mapping[fieldId] && (
                                      <div className="truncate text-caption text-muted-foreground mt-1">
                                        Mapped to:{' '}
                                        <span className="font-medium text-primary">
                                          {mapping[fieldId]}
                                        </span>
                                      </div>
                                    )}
                                  </th>
                                );
                              })}
                            </tr>
                          </thead>
                          <tbody>
                            {sampleData.length > 0 ? (
                              sampleData.map((row, rowIndex) => (
                                <tr key={rowIndex} className="border-t">
                                  {Object.keys(mapping).map((fieldId) => {
                                    const colIndex =
                                      getMappedColumnIndex(fieldId);
                                    return (
                                      <td
                                        key={fieldId}
                                        className="p-2 border-r last:border-r-0 max-w-0 overflow-hidden"
                                      >
                                        <div className="truncate">
                                          {colIndex >= 0 &&
                                          colIndex < row.length ? (
                                            row[colIndex]
                                          ) : (
                                            <span className="text-muted-foreground italic">
                                              Not mapped
                                            </span>
                                          )}
                                        </div>
                                      </td>
                                    );
                                  })}
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td
                                  colSpan={Object.keys(mapping).length}
                                  className="p-4 text-center text-muted-foreground max-w-0 overflow-hidden"
                                >
                                  <div className="truncate">
                                    No preview data available
                                  </div>
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap justify-between items-center">
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab('mapping')}
                      className="space-x-1"
                    >
                      <ArrowRight className="h-4 w-4 rotate-180" />
                      <span>Back to Mapping</span>
                    </Button>

                    <Button
                      className="max-w-full"
                      onClick={() => void handleConfirmClick()}
                      disabled={isLoading}
                    >
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Confirm and Process
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}

        <DialogFooter className="pt-4 border-t mt-4">
          <div className="flex flex-wrap justify-between items-center w-full">
            <Button
              type="button"
              variant="outline"
              onClick={() => void onClose()}
              className="space-x-1 font-medium"
            >
              Cancel
            </Button>

            {activeTab === 'mapping' && (
              <div className="flex flex-wrap space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setActiveTab('preview')}
                  disabled={isLoading}
                  className="space-x-1 font-medium"
                >
                  <FileText className="mr-1 h-4 w-4" />
                  <span>Preview</span>
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Button>

                <Button
                  type="button"
                  onClick={() => void handleConfirmClick()}
                  disabled={
                    isLoading ||
                    ESSENTIAL_FIELDS.some((fieldId) => !mapping[fieldId]) ||
                    !AMOUNT_FIELDS.some((fieldId) => mapping[fieldId])
                  }
                  className="font-medium"
                  variant="default"
                >
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  Confirm Mapping
                </Button>
              </div>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ColumnMappingModal;
