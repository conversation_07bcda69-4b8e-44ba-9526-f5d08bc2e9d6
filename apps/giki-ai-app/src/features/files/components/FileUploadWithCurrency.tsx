import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Progress } from '@/shared/components/ui/progress';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  Upload,
  FileSpreadsheet,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Info,
  RefreshCw,
  XCircle,
} from 'lucide-react';
import { useToast } from '@/shared/components/ui/use-toast';
import { useId, announce, focusClasses } from '@/shared/utils/accessibility';
import { useErrorHandler } from '@/shared/components/ui/error-boundary';
import { logger } from '@/shared/lib/logger';
// File validation services temporarily disabled - using basic validation only

interface ValidationResult {
  is_valid: boolean;
  confidence: number;
  validation_errors?: string[];
  validation_warnings?: string[];
}

interface FileMetadata {
  row_count: number;
  column_count: number;
  file_type: string;
  file_size: number;
  headers: string[];
}

interface ValidationData {
  validation_result?: ValidationResult;
}

interface MetadataData {
  metadata?: FileMetadata;
}

interface FileUploadWithCurrencyProps {
  onFileUpload: (file: File, currency: string) => Promise<void>;
  isUploading: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

export const FileUploadWithCurrency: React.FC<FileUploadWithCurrencyProps> = ({
  onFileUpload,
  isUploading,
  maxRetries = 3,
  retryDelay = 1000,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedCurrency, setSelectedCurrency] = useState<string>('');
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState<number>(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadAbortController = useRef<AbortController | null>(null);
  const { toast, dismiss } = useToast();
  const { handleError } = useErrorHandler();
  const dropzoneId = useId('dropzone');
  const fileInputId = useId('file-input');
  const currencySelectId = useId('currency-select');

  // Advanced validation hooks disabled - using basic validation only

  // Cleanup abort controller on unmount
  useEffect(() => {
    return () => {
      if (uploadAbortController.current) {
        uploadAbortController.current.abort();
      }
    };
  }, []);

  const currencies = [
    { value: 'USD', label: 'USD ($)' },
    { value: 'EUR', label: 'EUR (€)' },
    { value: 'GBP', label: 'GBP (£)' },
    { value: 'CAD', label: 'CAD (C$)' },
    { value: 'AUD', label: 'AUD (A$)' },
    { value: 'JPY', label: 'JPY (¥)' },
    { value: 'INR', label: 'INR (₹)' },
  ];

  const handleFileSelect = useCallback(
    (file: File) => {
      // Clear any previous state
      dismiss();
      setUploadError(null);
      setUploadProgress(0);
      setRetryCount(0);

      // Basic client-side validation first
      const allowedTypes = [
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      ];

      const fileExtension = file?.name?.split('.').pop()?.toLowerCase();
      const isValidExtension = ['csv', 'xlsx', 'xls'].includes(
        fileExtension || '',
      );

      if (!allowedTypes.includes(file.type) && !isValidExtension) {
        const error = 'Invalid file type. Please select a CSV or Excel file';
        setUploadError(error);
        toast({
          title: 'Invalid File Type',
          description: 'Please select a CSV or Excel file (.csv, .xlsx, .xls)',
          variant: 'destructive',
        });
        announce(error, 'assertive');
        logger.warn('Invalid file type selected', {
          fileName: file.name,
          fileType: file.type,
        });
        return;
      }

      // Enhanced size validation with better messaging
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        const error = `File too large (${(file.size / 1024 / 1024).toFixed(2)}MB). Maximum size is 10MB`;
        setUploadError(error);
        toast({
          title: 'File Too Large',
          description: error,
          variant: 'destructive',
        });
        announce(error, 'assertive');
        logger.warn('File too large', {
          fileName: file.name,
          fileSize: file.size,
          maxSize,
        });
        return;
      }

      // Check for empty files
      if (file.size === 0) {
        const error = 'File is empty. Please select a file with data';
        setUploadError(error);
        toast({
          title: 'Empty File',
          description: error,
          variant: 'destructive',
        });
        announce(error, 'assertive');
        logger.warn('Empty file selected', { fileName: file.name });
        return;
      }

      setSelectedFile(file);
      announce(`File selected: ${file.name}`, 'polite');
      logger.info('File selected for upload', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
      });

      toast({
        title: 'File Selected',
        description: `${file.name} ready for upload`,
      });
    },
    [dismiss, toast],
  );

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e?.dataTransfer?.files && e?.dataTransfer?.files[0]) {
      handleFileSelect(e?.dataTransfer?.files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e?.target?.files && e?.target?.files[0]) {
      handleFileSelect(e?.target?.files[0]);
    }
  };

  const handleUpload = async (isRetry = false) => {
    if (!selectedFile || !selectedCurrency) return;

    if (!isRetry) {
      setRetryCount(0);
    }
    setUploadError(null);
    setUploadProgress(0);
    setIsRetrying(isRetry);

    // Create new abort controller for this upload
    uploadAbortController.current = new AbortController();

    // Declare progressInterval outside try block
    let progressInterval: NodeJS.Timeout | undefined;

    try {
      logger.info('Starting file upload', {
        fileName: selectedFile.name,
        currency: selectedCurrency,
        attempt: retryCount + 1,
        isRetry,
      });

      // Simulate progress for better UX
      progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 10;
        });
      }, 500);

      await onFileUpload(selectedFile, selectedCurrency);

      clearInterval(progressInterval);
      setUploadProgress(100);

      // Reset form after successful upload
      setSelectedFile(null);
      setSelectedCurrency('');
      setUploadProgress(0);
      setRetryCount(0);

      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      logger.info('File upload successful', { fileName: selectedFile.name });

      toast({
        title: 'Upload Successful',
        description: 'Your file has been uploaded and is being processed.',
      });
      announce('File uploaded successfully', 'polite');
    } catch (error) {
      if (progressInterval) clearInterval(progressInterval);
      setIsRetrying(false);

      logger.error('File upload failed', {
        fileName: selectedFile.name,
        attempt: retryCount + 1,
        error,
      });

      // Check if upload was cancelled
      if (uploadAbortController.current?.signal.aborted) {
        setUploadError('Upload cancelled');
        setUploadProgress(0);
        return;
      }

      // Handle different error types
      let errorMessage = 'Upload failed. Please try again.';

      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          errorMessage =
            'Upload timed out. Please check your connection and try again.';
        } else if (error.message.includes('network')) {
          errorMessage =
            'Network error. Please check your internet connection.';
        } else if (error.message.includes('413')) {
          errorMessage =
            'File too large for server. Please use a smaller file.';
        } else if (
          error.message.includes('401') ||
          error.message.includes('403')
        ) {
          errorMessage = 'Authentication failed. Please log in again.';
        } else {
          errorMessage = error.message;
        }
      }

      setUploadError(errorMessage);
      setUploadProgress(0);

      // Check if we should retry
      if (retryCount < maxRetries - 1 && shouldRetry(error)) {
        setRetryCount((prev) => prev + 1);
        const delay = retryDelay * Math.pow(2, retryCount); // Exponential backoff

        toast({
          title: 'Upload Failed',
          description: `${errorMessage} Retrying in ${delay / 1000}s...`,
          variant: 'destructive',
        });

        setTimeout(() => {
          handleUpload(true).catch(console.error);
        }, delay);
      } else {
        toast({
          title: 'Upload Failed',
          description: errorMessage,
          variant: 'destructive',
          action:
            retryCount >= maxRetries - 1 ? undefined : (
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  handleUpload(true).catch(console.error);
                }}
              >
                Retry
              </Button>
            ),
        });

        handleError(error instanceof Error ? error : new Error(errorMessage));
      }
    }
  };

  const shouldRetry = (error: unknown): boolean => {
    if (error instanceof Error) {
      // Retry on network errors, timeouts, and server errors
      return (
        error.message.includes('network') ||
        error.message.includes('timeout') ||
        error.message.includes('500') ||
        error.message.includes('502') ||
        error.message.includes('503') ||
        error.message.includes('504')
      );
    }
    return false;
  };

  const cancelUpload = () => {
    if (uploadAbortController.current) {
      uploadAbortController.current.abort();
      logger.info('Upload cancelled by user');
      toast({
        title: 'Upload Cancelled',
        description: 'File upload has been cancelled.',
      });
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setSelectedCurrency('');
    setUploadError(null);
    setUploadProgress(0);
    setRetryCount(0);
    setIsRetrying(false);

    if (uploadAbortController.current) {
      uploadAbortController.current.abort();
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    logger.debug('Form reset');
  };

  // Helper function to render validation status
  const renderValidationStatus = () => {
    if (!selectedFile) return null;

    // Advanced validation disabled - using fallback values
    const isValidating = false;
    const hasValidation: ValidationData | null = null;
    const hasMetadata: MetadataData | null = null;
    const hasErrors = false;

    return (
      <Card className="form-field-spacing">
        <CardContent className="card-padding-sm overflow-hidden">
          <div className="form-field-spacing">
            <h4 className="font-medium flex flex-wrap items-center gap-2">
              <Info className="w-4 h-4" />
              File Analysis
            </h4>

            {isValidating && (
              <div className="flex flex-wrap items-center gap-2 text-info">
                <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                <span className="text-sm">Analyzing file...</span>
              </div>
            )}

            {hasValidation && (
              <div className="component-gap-sm">
                <div className="flex flex-wrap items-center gap-2">
                  {hasValidation.validation_result?.is_valid ? (
                    <CheckCircle className="w-4 h-4 text-success" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-destructive" />
                  )}
                  <span className="text-sm font-medium">
                    Validation:{' '}
                    {hasValidation.validation_result?.is_valid
                      ? 'Passed'
                      : 'Failed'}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    ({(hasValidation.validation_result?.confidence || 0) * 100}%
                    confidence)
                  </span>
                </div>

                {hasValidation.validation_result?.validation_errors?.length &&
                  hasValidation?.validation_result?.validation_errors.length >
                    0 && (
                    <div className="text-sm text-destructive">
                      Errors:{' '}
                      {hasValidation?.validation_result?.validation_errors.join(
                        ', ',
                      )}
                    </div>
                  )}

                {hasValidation.validation_result?.validation_warnings?.length &&
                  hasValidation?.validation_result?.validation_warnings.length >
                    0 && (
                    <div className="text-sm text-warning">
                      Warnings:{' '}
                      {hasValidation?.validation_result?.validation_warnings.join(
                        ', ',
                      )}
                    </div>
                  )}
              </div>
            )}

            {hasMetadata && hasMetadata.metadata && (
              <div className="component-gap-sm">
                <div className="flex flex-wrap items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-success" />
                  <span className="text-sm font-medium">
                    Metadata Extracted
                  </span>
                </div>
                <div className="text-sm text-muted-foreground grid grid-cols-2 gap-2">
                  <div>Rows: {hasMetadata?.metadata?.row_count}</div>
                  <div>Columns: {hasMetadata?.metadata?.column_count}</div>
                  <div>Type: {hasMetadata?.metadata?.file_type}</div>
                  <div>
                    Size:{' '}
                    {(hasMetadata?.metadata?.file_size / 1024 / 1024).toFixed(
                      2,
                    )}{' '}
                    MB
                  </div>
                </div>
                {hasMetadata?.metadata?.headers.length > 0 && (
                  <div className="text-sm">
                    <span className="font-medium">Headers: </span>
                    <span className="text-muted-foreground">
                      {hasMetadata?.metadata?.headers.slice(0, 3).join(', ')}
                      {hasMetadata?.metadata?.headers.length > 3 &&
                        ` +${hasMetadata?.metadata?.headers.length - 3} more`}
                    </span>
                  </div>
                )}
              </div>
            )}

            {hasErrors && (
              <div className="flex flex-wrap items-center gap-2 text-destructive">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">
                  Analysis failed: {/* Validation error details disabled */}
                  &quot;Validation service unavailable&quot;
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div>
      {/* File Drop Zone */}
      <Card
        id={dropzoneId}
        className={`border-2 border-dashed transition-colors ${
          dragActive
            ? 'border-[hsl(var(--giki-primary))] bg-[hsl(var(--giki-success))]/10'
            : selectedFile
              ? 'border-success bg-success/10'
              : 'border-[hsl(var(--giki-border))] hover:border-[hsl(var(--giki-border-hover))]'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        role="region"
        aria-label="File upload drop zone"
        aria-describedby={`${dropzoneId}-description`}
      >
        <CardContent className="card-padding-lg overflow-hidden">
          <div className="text-center">
            {selectedFile ? (
              <div className="form-field-spacing">
                <FileSpreadsheet
                  className="w-12 h-12 text-success mx-auto"
                  aria-hidden="true"
                />
                <div>
                  <p className="truncate text-heading-5 text-success">
                    {selectedFile.name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB •{' '}
                    {selectedFile?.type?.includes('csv')
                      ? 'CSV File'
                      : 'Excel File'}
                  </p>
                </div>
                <Button
                  onClick={() => void resetForm()}
                  variant="outline"
                  size="sm"
                  className={`text-destructive hover:text-destructive/80 ${focusClasses}`}
                  aria-label="Remove selected file"
                >
                  Remove File
                </Button>
              </div>
            ) : (
              <div className="form-field-spacing">
                <Upload
                  className="w-12 h-12 text-muted-foreground mx-auto"
                  aria-hidden="true"
                />
                <div id={`${dropzoneId}-description`}>
                  <p className="truncate text-heading-5">
                    Drop your file here or click to browse
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Supports CSV and Excel files (.csv, .xlsx, .xls) up to 10MB
                  </p>
                </div>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                  className={`gap-2 ${focusClasses}`}
                  aria-label="Choose file to upload"
                >
                  <Upload className="w-4 h-4" aria-hidden="true" />
                  Choose File
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Hidden file input */}
      <label htmlFor={fileInputId} className="sr-only">
        Select file to upload
      </label>
      <input
        ref={fileInputRef}
        id={fileInputId}
        type="file"
        accept=".csv,.xlsx,.xls"
        onChange={handleFileInputChange}
        className="hidden"
        data-testid="file-input"
        aria-describedby={`${dropzoneId}-description`}
      />

      {/* Currency Selection */}
      {selectedFile && (
        <Card>
          <CardContent className="card-padding-sm overflow-hidden">
            <div className="flex flex-wrap items-center justify-between component-gap">
              <div className="flex flex-wrap items-center component-gap-sm">
                <DollarSign className="w-5 h-5 text-success" />
                <div>
                  <p className="font-medium">Select Currency</p>
                  <p className="text-sm text-muted-foreground">
                    Choose the currency used in this file
                  </p>
                </div>
              </div>
              <div className="flex flex-wrap items-center component-gap-sm">
                <label htmlFor={currencySelectId} className="sr-only">
                  Select currency for uploaded file
                </label>
                <Select
                  value={selectedCurrency}
                  onValueChange={(value) => {
                    setSelectedCurrency(value);
                    setUploadError(null);
                    announce(`Currency selected: ${value}`, 'polite');
                  }}
                  disabled={isUploading}
                >
                  <SelectTrigger
                    id={currencySelectId}
                    className={`w-40 ${focusClasses}`}
                  >
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {isUploading ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={cancelUpload}
                    className="text-destructive"
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                ) : (
                  <Button
                    className="max-w-full gap-2"
                    onClick={() => void handleUpload()}
                    disabled={!selectedFile || !selectedCurrency}
                  >
                    {isRetrying ? (
                      <>
                        <RefreshCw className="w-4 h-4 animate-spin" />
                        Retrying...
                      </>
                    ) : (
                      <>
                        <Upload className="w-4 h-4" />
                        Upload File
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Progress */}
      {isUploading && uploadProgress > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="component-gap-sm">
              <div className="flex justify-between text-sm">
                <span>Uploading {selectedFile?.name}...</span>
                <span>{Math.round(uploadProgress)}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
              {retryCount > 0 && (
                <p className="text-xs text-muted-foreground">
                  Retry attempt {retryCount} of {maxRetries}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {uploadError && !isUploading && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{uploadError}</span>
            {selectedFile && selectedCurrency && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  handleUpload(true).catch(console.error);
                }}
                className="ml-2"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Retry
              </Button>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Enhanced File Analysis Display */}
      {renderValidationStatus()}
    </div>
  );
};

export default FileUploadWithCurrency;
