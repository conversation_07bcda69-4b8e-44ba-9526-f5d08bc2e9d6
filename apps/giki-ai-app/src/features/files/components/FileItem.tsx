/**
 * Memoized File Item Component
 * Optimized for performance when rendering lists of uploading files
 */
import React from 'react';
import { X, RotateCcw } from 'lucide-react';

interface UploadFile {
  id: string;
  file: File;
  uploadId?: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  transactionCount?: number;
  categorizedCount?: number;
}

interface FileItemProps {
  file: UploadFile;
  onRemove: (fileId: string) => void;
  onRetry: (fileId: string) => void;
  getStatusIcon: (file: UploadFile) => React.ReactNode;
  getStatusColor: (status: UploadFile['status']) => string;
}

// Memoized file item component to prevent unnecessary re-renders
export const FileItem = React.memo<FileItemProps>(({
  file,
  onRemove,
  onRetry,
  getStatusIcon,
  getStatusColor,
}) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getStatusIcon(file)}
          <div>
            <p className="text-sm font-medium text-gray-900">{file.file.name}</p>
            <p className="text-xs text-gray-500">
              {(file.file.size / 1024 / 1024).toFixed(2)} MB
              {file.transactionCount && (
                <span className="ml-2">
                  • {file.transactionCount} transactions
                </span>
              )}
              {file.categorizedCount && (
                <span className="ml-2">
                  • {file.categorizedCount} categorized
                </span>
              )}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <span className={`text-xs font-medium ${getStatusColor(file.status)}`}>
            {file.status.toUpperCase()}
          </span>
          
          {file.status === 'error' && (
            <button
              onClick={() => onRetry(file.id)}
              className="text-blue-600 hover:text-blue-700 p-1"
              title="Retry upload"
            >
              <RotateCcw className="w-3 h-3" />
            </button>
          )}
          
          <button
            onClick={() => onRemove(file.id)}
            className="text-gray-400 hover:text-red-500 p-1"
            title="Remove file"
          >
            <X className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      {(file.status === 'uploading' || file.status === 'processing') && (
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-1">
            <div
              className="bg-blue-500 h-1 rounded-full transition-all duration-300"
              style={{ width: `${file.progress}%` }}
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {file.progress}% {file.status === 'uploading' ? 'uploaded' : 'processed'}
          </p>
        </div>
      )}

      {/* Error Message */}
      {file.error && (
        <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          {file.error}
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for optimal performance
  // Only re-render if the file's important properties change
  return (
    prevProps.file.id === nextProps.file.id &&
    prevProps.file.status === nextProps.file.status &&
    prevProps.file.progress === nextProps.file.progress &&
    prevProps.file.error === nextProps.file.error &&
    prevProps.file.transactionCount === nextProps.file.transactionCount &&
    prevProps.file.categorizedCount === nextProps.file.categorizedCount
  );
});

FileItem.displayName = 'FileItem';

export default FileItem;