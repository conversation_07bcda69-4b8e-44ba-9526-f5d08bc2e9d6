/**
 * Production Upload Flow Component
 *
 * Handles the upload of NEW transaction files WITHOUT categories for AI categorization.
 * This is used after onboarding is complete and the AI has been trained on historical data.
 */
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Upload,
  AlertCircle,
  CheckCircle,
  FileText,
  Brain,
} from 'lucide-react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '../../../../shared/components/ui/card';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '../../../../shared/components/ui/alert';
import { Button } from '../../../../shared/components/ui/button';
import { Progress } from '../../../../shared/components/ui/progress';
import * as uploadService from '../../services/uploadService';
import { useAuth } from '@/features/auth/hooks/useAuth';

interface ProductionUploadFlowProps {
  onComplete?: (uploadIds: string[]) => void;
  onError?: (error: Error) => void;
}

type UploadPhase = 'ready' | 'uploading' | 'processing' | 'complete' | 'error';

interface ProductionUploadResult {
  uploadId: string;
  filename: string;
  transactionCount: number;
  status: 'success' | 'error';
  error?: string;
}

export const ProductionUploadFlow: React.FC<ProductionUploadFlowProps> = ({
  onComplete,
  onError,
}) => {
  const navigate = useNavigate();
  const { user: _user } = useAuth();
  const [phase, setPhase] = useState<UploadPhase>('ready');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResults, setUploadResults] = useState<ProductionUploadResult[]>(
    [],
  );
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const handleFileSelection = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);

      // Validate file types
      const validFiles = files.filter(
        (file) =>
          file.name.endsWith('.csv') ||
          file.name.endsWith('.xlsx') ||
          file.name.endsWith('.xls'),
      );

      if (validFiles.length !== files.length) {
        setErrorMessage(
          'Some files were skipped. Only CSV and Excel files are supported.',
        );
      }

      setSelectedFiles(validFiles);
      setErrorMessage(null);
    },
    [],
  );

  const handleUpload = useCallback(async () => {
    if (selectedFiles.length === 0) {
      setErrorMessage('Please select files to upload');
      return;
    }

    setPhase('uploading');
    setUploadProgress(0);
    setErrorMessage(null);

    try {
      // Upload files to production endpoint
      const response = await uploadService.uploadFiles(selectedFiles, {
        onProgress: (progress: number) => setUploadProgress(progress),
      });

      if (
        !response ||
        typeof response !== 'object' ||
        !('uploads' in response)
      ) {
        throw new Error('Invalid response format from upload service');
      }

      const typedResponse = response as {
        uploads: {
          upload_id: string;
          filename: string;
          transaction_count: number;
        }[];
        failed: number;
      };

      if (typedResponse.failed > 0) {
        setErrorMessage(
          `${typedResponse.failed} files failed to upload. Please check the file format.`,
        );
      }

      // Map upload results
      const results: ProductionUploadResult[] = typedResponse.uploads.map(
        (upload: {
          upload_id: string;
          filename: string;
          transaction_count: number;
        }) => ({
          uploadId: upload.upload_id,
          filename: upload.filename,
          transactionCount: upload.transaction_count || 0,
          status: 'success',
        }),
      );

      setUploadResults(results);
      setPhase('processing');

      // Simulate processing time
      setTimeout(() => {
        setPhase('complete');
        if (onComplete) {
          onComplete(results.map((r) => r.uploadId));
        }
      }, 3000);
    } catch (error) {
      console.error('Upload failed:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to upload files';
      setErrorMessage(errorMessage);
      setPhase('error');
      if (onError && typeof onError === 'function') {
        onError(error instanceof Error ? error : new Error('Upload failed'));
      }
    }
  }, [selectedFiles, onComplete, onError]);

  const handleReset = useCallback(() => {
    setPhase('ready');
    setSelectedFiles([]);
    setUploadResults([]);
    setErrorMessage(null);
    setUploadProgress(0);
  }, []);

  const handleNavigateToReview = useCallback(() => {
    navigate('/review');
  }, [navigate]);

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex flex-wrap items-center gap-2">
          <Brain className="h-6 w-6 text-info" />
          Production Transaction Upload
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 overflow-hidden">
        {/* Important Notice */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Important</AlertTitle>
          <AlertDescription>
            Upload NEW transactions <strong>WITHOUT category labels</strong>.
            The AI will automatically categorize them based on patterns learned
            from your historical data.
          </AlertDescription>
        </Alert>

        {/* Phase: Ready */}
        {phase === 'ready' && (
          <div className="space-y-4">
            <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
              <Upload className="mx-auto h-12 w-12 truncategray-400 mb-4" />
              <p className="truncate text-heading-5 mb-2">
                Upload Transaction Files for AI Categorization
              </p>
              <p className="text-body-small mb-4">
                Select Excel (.xlsx) or CSV files containing uncategorized
                transactions
              </p>
              <input
                type="file"
                multiple
                accept=".csv,.xlsx,.xls"
                onChange={handleFileSelection}
                className="hidden"
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                className="cursor-pointer inline-flex flex-wrap items-center px-4 py-2 border border-transparent text-label rounded-md shadow-sm text-white bg-green-600 hover:bg-blue-700"
              >
                Select Files
              </label>
            </div>

            {selectedFiles.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-medium">Selected Files:</h3>
                {selectedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex flex-wrap items-center gap-2 text-sm"
                  >
                    <FileText className="h-4 w-4 truncategray-400" />
                    <span>{file.name}</span>
                    <span className="text-muted-foreground">
                      ({(file.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                ))}
                <Button
                  className="max-w-full mt-4"
                  onClick={() => void handleUpload()}
                >
                  Upload and Process
                </Button>
              </div>
            )}

            {errorMessage && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errorMessage}</AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Phase: Uploading */}
        {phase === 'uploading' && (
          <div className="space-y-4">
            <div className="text-center">
              <Upload className="mx-auto h-12 w-12 text-info animate-pulse mb-4" />
              <p className="truncate text-heading-5 mb-2">Uploading Files...</p>
              <Progress value={uploadProgress} className="w-full" />
              <p className="text-body-small mt-2">{uploadProgress}% complete</p>
            </div>
          </div>
        )}

        {/* Phase: Processing */}
        {phase === 'processing' && (
          <div className="space-y-4">
            <div className="text-center">
              <Brain className="mx-auto h-12 w-12 text-info animate-pulse mb-4" />
              <p className="truncate text-heading-5 mb-2">
                AI Categorization in Progress...
              </p>
              <p className="text-body-small">
                The AI is analyzing your transactions and applying categories
                based on your historical patterns.
              </p>
              <Progress value={75} className="w-full mt-4" />
            </div>
          </div>
        )}

        {/* Phase: Complete */}
        {phase === 'complete' && (
          <div className="space-y-4">
            <div className="text-center">
              <CheckCircle className="mx-auto h-12 w-12 text-success mb-4" />
              <p className="truncate text-heading-5 mb-2">Upload Complete</p>
              <p className="text-body-small mb-4">
                Your transactions have been categorized by AI
              </p>
            </div>

            {uploadResults.length > 0 && (
              <div className="bg-muted/50 rounded-lg p-4 space-y-2">
                <h3 className="font-medium">Processing Summary:</h3>
                {uploadResults.map((result, index) => (
                  <div
                    key={index}
                    className="flex flex-wrap justify-between text-sm"
                  >
                    <span>{result.filename}</span>
                    <span className="text-success">
                      {result.transactionCount} transactions processed
                    </span>
                  </div>
                ))}
              </div>
            )}

            <div className="flex flex-wrap gap-4 justify-center">
              <Button
                className="max-w-full"
                variant="outline"
                onClick={handleReset}
              >
                Upload More Files
              </Button>
              <Button className="max-w-full" onClick={handleNavigateToReview}>
                Review Categorizations
              </Button>
            </div>
          </div>
        )}

        {/* Phase: Error */}
        {phase === 'error' && (
          <div className="space-y-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Upload Failed</AlertTitle>
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
            <div className="flex flex-wrap justify-center">
              <Button
                className="max-w-full"
                variant="outline"
                onClick={handleReset}
              >
                Try Again
              </Button>
            </div>
          </div>
        )}

        {/* Help Text */}
        <div className="border-t pt-4 mt-6">
          <p className="text-body-small">
            <strong>Note:</strong> If your files contain category columns,
            please remove them first. For uploading historical data with
            categories during initial setup, use the{' '}
            <a href="/onboarding" className="text-info hover:underline">
              onboarding workflow
            </a>
            .
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductionUploadFlow;
