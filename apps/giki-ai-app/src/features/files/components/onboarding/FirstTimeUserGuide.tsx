/**
 * First-time User Guide Component
 *
 * Provides contextual tooltips and callouts for key UI elements to help
 * new users navigate and understand the interface.
 */
import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from 'react';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import {
  HelpCircle,
  ArrowRight,
  ArrowLeft,
  Lightbulb,
  Target,
  Zap,
  CheckCircle,
  BookOpen,
  Info,
} from 'lucide-react';

interface GuideStep {
  id: string;
  title: string;
  description: string;
  element?: string; // CSS selector for the target element
  position?: 'top' | 'bottom' | 'left' | 'right';
  action?: 'click' | 'hover' | 'none';
  isOptional?: boolean;
  nextStepDelay?: number; // Auto-advance delay in milliseconds
}

interface FirstTimeUserGuideProps {
  isActive?: boolean;
  onComplete?: () => void;
  onSkip?: () => void;
  guideName?: string;
  autoStart?: boolean;
}

export const FirstTimeUserGuide: React.FC<FirstTimeUserGuideProps> = ({
  isActive = false,
  onComplete,
  onSkip,
  guideName = 'Getting Started',
  autoStart = true,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isGuideActive, setIsGuideActive] = useState(isActive);
  const [showOverlay, setShowOverlay] = useState(false);
  const [highlightedElement, setHighlightedElement] =
    useState<HTMLElement | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const overlayRef = useRef<HTMLDivElement>(null);

  // Guide steps for the onboarding/upload interface
  const guideSteps: GuideStep[] = useMemo(
    () => [
      {
        id: 'welcome',
        title: 'Welcome to Giki AI',
        description:
          "Let's take a quick tour of the key features to help you get started with financial management.",
        position: 'bottom',
      },
      {
        id: 'upload-area',
        title: 'File Upload Area',
        description:
          'Drag and drop your CSV or Excel files here, or click to browse. We support multiple file formats and sizes up to 5MB.',
        element: '[data-testid="file-input"]',
        position: 'top',
        action: 'hover',
      },
      {
        id: 'upload-button',
        title: 'Upload Button',
        description:
          'Click here to start processing your files after selecting them. The AI will analyze and categorize your transactions automatically.',
        element: '[data-testid="upload-button"]',
        position: 'top',
        action: 'hover',
      },
      {
        id: 'navigation',
        title: 'Navigation Menu',
        description:
          'Use the navigation menu to access different features like transaction review, reports, and settings.',
        element: 'nav',
        position: 'right',
      },
      {
        id: 'dashboard-link',
        title: 'Dashboard',
        description:
          'Your main dashboard shows financial insights, spending trends, and AI-generated analytics.',
        element: '[href="/dashboard"]',
        position: 'bottom',
        action: 'hover',
      },
      {
        id: 'review-link',
        title: 'Transaction Review',
        description:
          'Review and adjust AI categorizations, make bulk edits, and ensure accuracy of your financial data.',
        element: '[href="/review"]',
        position: 'bottom',
        action: 'hover',
      },
      {
        id: 'reports-link',
        title: 'Reports & Analytics',
        description:
          'Generate custom reports, export data, and create financial summaries for accounting purposes.',
        element: '[href="/reports"]',
        position: 'bottom',
        action: 'hover',
      },
      {
        id: 'help-complete',
        title: "You're All Set!",
        description:
          'You now know the basics of Giki AI. Start by uploading your first file to experience the power of AI categorization.',
        position: 'bottom',
      },
    ],
    [],
  );

  const highlightElement = useCallback((selector: string) => {
    const element = document.querySelector(selector);
    if (element) {
      setHighlightedElement(element as HTMLElement);

      // Scroll element into view
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Calculate tooltip position
      const rect = element.getBoundingClientRect();
      setTooltipPosition({
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2,
      });
    }
  }, []);

  const startGuide = useCallback(() => {
    setIsGuideActive(true);
    setShowOverlay(true);
    setCurrentStep(0);
  }, []);

  const endGuide = useCallback(() => {
    setIsGuideActive(false);
    setShowOverlay(false);
    setHighlightedElement(null);
    onComplete?.();
  }, [onComplete]);

  const skipGuide = useCallback(() => {
    setIsGuideActive(false);
    setShowOverlay(false);
    setHighlightedElement(null);
    onSkip?.();
  }, [onSkip]);

  const nextStep = useCallback(() => {
    if (currentStep < guideSteps.length - 1) {
      setCurrentStep((prev) => prev + 1);
    } else {
      endGuide();
    }
  }, [currentStep, guideSteps.length, endGuide]);

  const previousStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  useEffect(() => {
    if (autoStart && isActive) {
      startGuide();
    }
  }, [isActive, autoStart, startGuide]);

  useEffect(() => {
    if (isGuideActive && currentStep < guideSteps.length) {
      const step = guideSteps[currentStep];
      if (step.element) {
        highlightElement(step.element);
      } else {
        setHighlightedElement(null);
      }
    }
  }, [currentStep, isGuideActive, guideSteps, highlightElement]);

  const currentStepData = guideSteps[currentStep];

  if (!isGuideActive) {
    return (
      <div className="fixed bottom-4 right-4 z-modal">
        <Button
          onClick={() => void startGuide()}
          variant="outline"
          size="sm"
          className="max-w-full flex flex-wrap items-center space-x-2 bg-white shadow-lg"
        >
          <HelpCircle className="h-4 w-4" />
          <span>Help</span>
        </Button>
      </div>
    );
  }

  return (
    <>
      {/* Overlay */}
      {showOverlay && (
        <div
          ref={overlayRef}
          className="fixed inset-0 bg-black bg-opacity-50 z-overlay"
          style={{
            background: highlightedElement
              ? `radial-gradient(circle at ${tooltipPosition.x}px ${tooltipPosition.y}px, transparent 60px, rgba(0,0,0,0.5) 80px)`
              : 'rgba(0,0,0,0.5)',
          }}
        />
      )}

      {/* Highlight Ring */}
      {highlightedElement && (
        <div
          className="fixed pointer-events-none z-modal"
          style={{
            left: highlightedElement.getBoundingClientRect().left - 4,
            top: highlightedElement.getBoundingClientRect().top - 4,
            width: highlightedElement.getBoundingClientRect().width + 8,
            height: highlightedElement.getBoundingClientRect().height + 8,
            border: '3px solid hsl(var(--giki-brand-blue))',
            borderRadius: '8px',
            boxShadow: '0 0 20px hsla(var(--giki-brand-blue), 0.5)',
          }}
        />
      )}

      {/* Guide Dialog */}
      <Dialog open={isGuideActive} onOpenChange={skipGuide}>
        <DialogContent className="max-w-md z-modal max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex flex-wrap items-center justify-between">
              <DialogTitle className="flex flex-wrap items-center space-x-2">
                <BookOpen className="h-5 w-5" />
                <span>{guideName}</span>
              </DialogTitle>
              <Badge variant="outline" className="max-w-[150px] truncate">
                {currentStep + 1} of {guideSteps.length}
              </Badge>
            </div>
            <DialogDescription>
              Interactive guide to help you get started
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Step Content */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex flex-wrap items-center text-lg">
                  {currentStepData.id === 'welcome' && (
                    <Lightbulb className="h-5 w-5 mr-2 truncateyellow-500" />
                  )}
                  {currentStepData?.id?.includes('upload') && (
                    <Target className="h-5 w-5 mr-2 truncateblue-500" />
                  )}
                  {currentStepData?.id?.includes('link') && (
                    <Zap className="h-5 w-5 mr-2 truncatepurple-500" />
                  )}
                  {currentStepData.id === 'help-complete' && (
                    <CheckCircle className="h-5 w-5 mr-2 truncategreen-500" />
                  )}
                  {currentStepData.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="overflow-hidden">
                <p className="text-sm text-[hsl(var(--giki-text-muted))]">
                  {currentStepData.description}
                </p>

                {/* Action Hint */}
                {currentStepData.action && (
                  <div className="mt-3 p-2 bg-info/5 rounded-lg">
                    <div className="flex flex-wrap items-center space-x-2">
                      <Info className="h-4 w-4 truncateblue-500" />
                      <span className="text-sm text-info">
                        {currentStepData.action === 'click' &&
                          'Try clicking on the highlighted element'}
                        {currentStepData.action === 'hover' &&
                          'Hover over the highlighted element to see more'}
                        {currentStepData.action === 'none' &&
                          'Look at the highlighted area'}
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex flex-wrap justify-between truncate text-caption text-[hsl(var(--giki-text-muted))]">
                <span>Progress</span>
                <span>
                  {Math.round(((currentStep + 1) / guideSteps.length) * 100)}%
                </span>
              </div>
              <div className="w-full bg-[hsl(var(--giki-neutral-200))] rounded-full h-2">
                <div
                  className="bg-info h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${((currentStep + 1) / guideSteps.length) * 100}%`,
                  }}
                />
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex flex-wrap justify-between items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => void skipGuide()}
                className="max-w-full truncate text-muted"
              >
                Skip Tour
              </Button>

              <div className="flex flex-wrap space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => void previousStep()}
                  disabled={currentStep === 0}
                  className="max-w-full"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back
                </Button>

                <Button
                  size="sm"
                  onClick={() => void nextStep()}
                  className="max-w-full"
                >
                  {currentStep === guideSteps.length - 1 ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Finish
                    </>
                  ) : (
                    <>
                      Next
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Step Indicators */}
            <div className="flex flex-wrap justify-center space-x-1">
              {guideSteps.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentStep(index)}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentStep
                      ? 'bg-info'
                      : index < currentStep
                        ? 'bg-success'
                        : 'bg-[hsl(var(--giki-neutral-300))]'
                  }`}
                  aria-label={`Go to step ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Quick Help Tooltip for specific elements */}
      {!showOverlay && <TooltipProvider />}
    </>
  );
};

// Helper component for providing quick help tooltips
const TooltipProvider: React.FC = () => {
  const quickTips = useMemo(
    () => [
      {
        selector: '[data-testid="file-input"]',
        title: 'File Upload',
        description: 'Drag & drop files or click to browse',
        icon: Target,
      },
      {
        selector: '[data-testid="upload-button"]',
        title: 'Process Files',
        description: 'Start AI categorization',
        icon: Zap,
      },
    ],
    [],
  );

  useEffect(() => {
    const addTooltips = () => {
      quickTips.forEach((tip) => {
        const element = document.querySelector(tip.selector);
        if (element && !(element as HTMLElement).dataset.tooltipAdded) {
          (element as HTMLElement).dataset.tooltipAdded = 'true';

          // Add help icon
          const helpIcon = document.createElement('div');
          helpIcon.className =
            'absolute -top-2 -right-2 w-5 h-5 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs cursor-help z-10';
          helpIcon.textContent = '?';
          helpIcon.style.position = 'absolute';

          // Position relatively
          const htmlElement = element as HTMLElement;
          if (
            htmlElement.style.position === '' ||
            htmlElement.style.position === 'static'
          ) {
            htmlElement.style.position = 'relative';
          }

          htmlElement.appendChild(helpIcon);
        }
      });
    };

    // Add tooltips after a delay to ensure DOM is ready
    const timer = setTimeout(addTooltips, 1000);
    return () => clearTimeout(timer);
  }, [quickTips]);

  return null;
};

export default FirstTimeUserGuide;
