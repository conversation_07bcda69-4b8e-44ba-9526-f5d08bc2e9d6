/**
 * Interactive Category Preview Component
 *
 * Shows AI categorization suggestions before final processing, allowing users to
 * preview and adjust categories before committing to the full categorization.
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import { ScrollArea } from '@/shared/components/ui/scroll-area';
import {
  Brain,
  ThumbsUp,
  ThumbsDown,
  Eye,
  BarChart3,
  PieChart,
  Info,
  Sparkles,
  Target,
  Zap,
} from 'lucide-react';

interface CategorySuggestion {
  id: string;
  name: string;
  path: string;
  confidence: number;
  transactionCount: number;
  totalAmount: number;
  examples: string[];
}

interface TransactionPreview {
  id: string;
  description: string;
  amount: number;
  date: string;
  suggestedCategory: string;
  confidence: number;
  alternativeCategories: string[];
}

interface InteractiveCategoryPreviewProps {
  uploadId: string;
  onApprove?: (approvedCategories: CategorySuggestion[]) => void;
  onReject?: (feedback: string) => void;
  onModify?: (modifications: Record<string, string>) => void;
}

export const InteractiveCategoryPreview: React.FC<
  InteractiveCategoryPreviewProps
> = ({ uploadId, onApprove, onReject, onModify: _onModify }) => {
  const [categorySuggestions, setCategorySuggestions] = useState<
    CategorySuggestion[]
  >([]);
  const [transactionPreviews, setTransactionPreviews] = useState<
    TransactionPreview[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [modifications, setModifications] = useState<Record<string, string>>(
    {},
  );
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);

  // Mock data based on active-tasks.md insights
  useEffect(() => {
    const loadPreviewData = async () => {
      setIsLoading(true);

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Mock category suggestions based on real data
      const mockCategories: CategorySuggestion[] = [
        {
          id: 'transportation',
          name: 'Transportation',
          path: 'Expenses > Transportation',
          confidence: 0.91,
          transactionCount: 156,
          totalAmount: 3450.75,
          examples: ['Uber rides', 'Gas station', 'Parking fees', 'Bus pass'],
        },
        {
          id: 'utilities',
          name: 'Utilities',
          path: 'Expenses > Utilities',
          confidence: 0.87,
          transactionCount: 89,
          totalAmount: 2180.5,
          examples: [
            'Electric bill',
            'Internet service',
            'Water utility',
            'Phone bill',
          ],
        },
        {
          id: 'food_dining',
          name: 'Food & Dining',
          path: 'Expenses > Food & Dining',
          confidence: 0.82,
          transactionCount: 234,
          totalAmount: 1890.25,
          examples: [
            'Restaurant meals',
            'Grocery stores',
            'Coffee shops',
            'Food delivery',
          ],
        },
        {
          id: 'office_supplies',
          name: 'Office Supplies',
          path: 'Expenses > Office Supplies',
          confidence: 0.78,
          transactionCount: 67,
          totalAmount: 890.0,
          examples: [
            'Stationery',
            'Software licenses',
            'Office equipment',
            'Printing',
          ],
        },
        {
          id: 'professional_services',
          name: 'Professional Services',
          path: 'Expenses > Professional Services',
          confidence: 0.74,
          transactionCount: 23,
          totalAmount: 4500.0,
          examples: [
            'Legal fees',
            'Consulting',
            'Accounting services',
            'Marketing',
          ],
        },
      ];

      const mockTransactions: TransactionPreview[] = [
        {
          id: '1',
          description: 'UBER TRIP 123456',
          amount: -25.5,
          date: '2024-12-01',
          suggestedCategory: 'Transportation',
          confidence: 0.95,
          alternativeCategories: ['Business Travel', 'Meals & Entertainment'],
        },
        {
          id: '2',
          description: 'PACIFIC GAS & ELECTRIC',
          amount: -180.75,
          date: '2024-12-01',
          suggestedCategory: 'Utilities',
          confidence: 0.92,
          alternativeCategories: ['Office Expenses', 'Maintenance'],
        },
        {
          id: '3',
          description: 'STARBUCKS COFFEE #1234',
          amount: -8.95,
          date: '2024-12-02',
          suggestedCategory: 'Food & Dining',
          confidence: 0.88,
          alternativeCategories: ['Meals & Entertainment', 'Office Supplies'],
        },
        {
          id: '4',
          description: 'MICROSOFT OFFICE 365',
          amount: -12.99,
          date: '2024-12-02',
          suggestedCategory: 'Office Supplies',
          confidence: 0.9,
          alternativeCategories: ['Software', 'Professional Services'],
        },
        {
          id: '5',
          description: 'LEGAL CONSULTING LLC',
          amount: -750.0,
          date: '2024-12-03',
          suggestedCategory: 'Professional Services',
          confidence: 0.85,
          alternativeCategories: ['Legal Fees', 'Business Services'],
        },
      ];

      setCategorySuggestions(mockCategories);
      setTransactionPreviews(mockTransactions);
      setIsLoading(false);
    };

    void loadPreviewData();
  }, [uploadId]);

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.85) return 'text-success';
    if (confidence >= 0.7) return 'text-yellow-600';
    return 'text-error';
  };

  const getConfidenceBadgeVariant = (confidence: number) => {
    if (confidence >= 0.85) return 'default';
    if (confidence >= 0.7) return 'secondary';
    return 'destructive';
  };

  const handleCategoryModification = (
    categoryId: string,
    newCategoryName: string,
  ) => {
    setModifications((prev) => ({
      ...prev,
      [categoryId]: newCategoryName,
    }));
  };

  const handleApproval = () => {
    const approvedCategories = categorySuggestions.map((category) => ({
      ...category,
      name: modifications[category.id] || category.name,
    }));
    onApprove?.(approvedCategories);
    setShowApprovalDialog(false);
  };

  const getOverallAccuracy = (): number => {
    if (categorySuggestions.length === 0) return 0;
    const totalTransactions = categorySuggestions.reduce(
      (sum, cat) => sum + cat.transactionCount,
      0,
    );
    const weightedConfidence = categorySuggestions.reduce(
      (sum, cat) => sum + cat.confidence * cat.transactionCount,
      0,
    );
    return weightedConfidence / totalTransactions;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-wrap items-center">
            <Brain className="h-5 w-5 mr-2 animate-pulse" />
            Generating Category Previews...
          </CardTitle>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="space-y-4">
            <Progress value={65} className="w-full" />
            <p className="text-body-small text-center">
              AI is analyzing transaction patterns and generating category
              suggestions...
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-wrap items-center justify-between">
            <div className="flex flex-wrap items-center">
              <Sparkles className="h-5 w-5 mr-2 truncatepurple-500" />
              AI Category Suggestions
            </div>
            <Badge
              variant="outline"
              className="flex flex-wrap items-center max-w-[150px] truncate"
            >
              <Target className="h-3 w-3 mr-1" />
              {Math.round(getOverallAccuracy() * 100)}% Avg Confidence
            </Badge>
          </CardTitle>
          <CardDescription>
            Preview of AI-generated categories for your{' '}
            {transactionPreviews.length} transactions
          </CardDescription>
        </CardHeader>
        <CardContent className="overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-info">
                {categorySuggestions.length}
              </div>
              <div className="text-body-small">Categories Detected</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success">
                {categorySuggestions.reduce(
                  (sum, cat) => sum + cat.transactionCount,
                  0,
                )}
              </div>
              <div className="text-body-small">Transactions Analyzed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success">
                $
                {categorySuggestions
                  .reduce((sum, cat) => sum + cat.totalAmount, 0)
                  .toFixed(2)}
              </div>
              <div className="text-body-small">Total Amount</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="categories" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="categories">Category Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transaction Preview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Categories Tab */}
        <TabsContent value="categories" className="space-y-4">
          <div className="grid gap-4">
            {categorySuggestions.map((category) => (
              <Card
                key={category.id}
                className="hover:shadow-md transition-shadow"
              >
                <CardContent className="p-4 overflow-hidden">
                  <div className="flex flex-wrap items-center justify-between mb-3">
                    <div className="flex flex-wrap items-center space-x-3">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <div>
                        <h3 className="font-semibold">{category.name}</h3>
                        <p className="text-body-small">{category.path}</p>
                      </div>
                    </div>
                    <div className="flex flex-wrap items-center space-x-2">
                      <Badge
                        variant={getConfidenceBadgeVariant(category.confidence)}
                        className="max-w-[150px] truncate"
                      >
                        {Math.round(category.confidence * 100)}% confidence
                      </Badge>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            className="max-w-full"
                            variant="ghost"
                            size="sm"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>{category.name} Details</DialogTitle>
                            <DialogDescription>
                              Category analysis and examples
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <div className="text-lg font-bold">
                                  {category.transactionCount}
                                </div>
                                <div className="text-body-small">
                                  Transactions
                                </div>
                              </div>
                              <div>
                                <div className="text-lg font-bold">
                                  ${category?.totalAmount?.toFixed(2)}
                                </div>
                                <div className="text-body-small">
                                  Total Amount
                                </div>
                              </div>
                            </div>
                            <div>
                              <h4 className="font-semibold mb-2">
                                Example Transactions:
                              </h4>
                              <ul className="space-y-1">
                                {category?.examples?.map((example, index) => (
                                  <li key={index} className="text-body-small">
                                    • {example}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center text-sm">
                    <div>
                      <div className="font-medium text-info">
                        {category.transactionCount}
                      </div>
                      <div className="text-muted-foreground">Transactions</div>
                    </div>
                    <div>
                      <div className="font-medium text-success">
                        ${category?.totalAmount?.toFixed(2)}
                      </div>
                      <div className="text-muted-foreground">Total Amount</div>
                    </div>
                    <div>
                      <div
                        className={`font-medium ${getConfidenceColor(category.confidence)}`}
                      >
                        {Math.round(category.confidence * 100)}%
                      </div>
                      <div className="text-muted-foreground">Confidence</div>
                    </div>
                  </div>

                  <div className="mt-3 flex flex-wrap items-center space-x-2">
                    <Select
                      value={modifications[category.id] || category.name}
                      onValueChange={(value) =>
                        handleCategoryModification(category.id, value)
                      }
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Modify category..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={category.name}>
                          {category.name} (Original)
                        </SelectItem>
                        <SelectItem value="Business Expenses">
                          Business Expenses
                        </SelectItem>
                        <SelectItem value="Personal Expenses">
                          Personal Expenses
                        </SelectItem>
                        <SelectItem value="Travel & Entertainment">
                          Travel & Entertainment
                        </SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Transactions Tab */}
        <TabsContent value="transactions" className="space-y-4">
          <ScrollArea className="h-[400px] overflow-y-auto">
            <div className="space-y-2">
              {transactionPreviews.map((transaction) => (
                <Card
                  key={transaction.id}
                  className="hover:bg-muted/50 transition-colors"
                >
                  <CardContent className="p-3 overflow-hidden">
                    <div className="flex flex-wrap items-center justify-between">
                      <div className="flex flex-wrap-1">
                        <div className="flex flex-wrap items-center justify-between mb-1">
                          <span className="font-medium truncate">
                            {transaction.description}
                          </span>
                          <span className="font-bold text-right">
                            ${Math.abs(transaction.amount).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex flex-wrap items-center space-x-2">
                          <Badge
                            variant="outline"
                            className="truncate text-caption max-w-[150px] truncate"
                          >
                            {transaction.suggestedCategory}
                          </Badge>
                          <Badge
                            variant={getConfidenceBadgeVariant(
                              transaction.confidence,
                            )}
                            className="truncate text-caption max-w-[150px] truncate"
                          >
                            {Math.round(transaction.confidence * 100)}%
                          </Badge>
                          <span className="truncate text-caption text-muted-foreground">
                            {transaction.date}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex flex-wrap items-center text-lg">
                  <PieChart className="h-5 w-5 mr-2" />
                  Category Distribution
                </CardTitle>
              </CardHeader>
              <CardContent className="overflow-hidden">
                <div className="space-y-3">
                  {categorySuggestions.map((category) => {
                    const percentage =
                      (category.transactionCount /
                        categorySuggestions.reduce(
                          (sum, cat) => sum + cat.transactionCount,
                          0,
                        )) *
                      100;
                    return (
                      <div key={category.id} className="space-y-1">
                        <div className="flex flex-wrap justify-between text-sm">
                          <span>{category.name}</span>
                          <span>{percentage.toFixed(1)}%</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex flex-wrap items-center text-lg">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Confidence Levels
                </CardTitle>
              </CardHeader>
              <CardContent className="overflow-hidden">
                <div className="space-y-3">
                  <div className="flex flex-wrap justify-between items-center">
                    <span className="text-sm">High Confidence (85%+)</span>
                    <Badge variant="default" className="max-w-[150px] truncate">
                      {
                        categorySuggestions.filter(
                          (cat) => cat.confidence >= 0.85,
                        ).length
                      }
                    </Badge>
                  </div>
                  <div className="flex flex-wrap justify-between items-center">
                    <span className="text-sm">Medium Confidence (70-84%)</span>
                    <Badge
                      variant="secondary"
                      className="max-w-[150px] truncate"
                    >
                      {
                        categorySuggestions.filter(
                          (cat) =>
                            cat.confidence >= 0.7 && cat.confidence < 0.85,
                        ).length
                      }
                    </Badge>
                  </div>
                  <div className="flex flex-wrap justify-between items-center">
                    <span className="text-sm">Low Confidence (&lt;70%)</span>
                    <Badge
                      variant="destructive"
                      className="max-w-[150px] truncate"
                    >
                      {
                        categorySuggestions.filter(
                          (cat) => cat.confidence < 0.7,
                        ).length
                      }
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <Card>
        <CardContent className="p-4 overflow-hidden">
          <div className="flex flex-wrap flex-col sm:flex-row gap-3 justify-between">
            <div className="flex flex-wrap items-center space-x-2">
              <Info className="h-4 w-4 truncateblue-500" />
              <span className="text-body-small">
                Review suggestions and make any adjustments before proceeding
              </span>
            </div>

            <div className="flex flex-wrap space-x-2">
              <Button
                className="max-w-full"
                variant="outline"
                onClick={() => onReject?.('Categories need improvement')}
              >
                <ThumbsDown className="h-4 w-4 mr-2" />
                Needs Work
              </Button>

              <Dialog
                open={showApprovalDialog}
                onOpenChange={setShowApprovalDialog}
              >
                <DialogTrigger asChild>
                  <Button className="max-w-full">
                    <ThumbsUp className="h-4 w-4 mr-2" />
                    Approve & Continue
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Confirm Category Approval</DialogTitle>
                    <DialogDescription>
                      Are you satisfied with these category suggestions? This
                      will apply categorization to all{' '}
                      {transactionPreviews.length} transactions.
                    </DialogDescription>
                  </DialogHeader>

                  <Alert>
                    <Zap className="h-4 w-4" />
                    <AlertDescription>
                      You can always adjust individual categories later in the
                      review interface.
                    </AlertDescription>
                  </Alert>

                  <div className="flex flex-wrap justify-end space-x-2">
                    <Button
                      className="max-w-full"
                      variant="outline"
                      onClick={() => setShowApprovalDialog(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="max-w-full"
                      onClick={() => void handleApproval()}
                    >
                      Apply Categories
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default InteractiveCategoryPreview;
