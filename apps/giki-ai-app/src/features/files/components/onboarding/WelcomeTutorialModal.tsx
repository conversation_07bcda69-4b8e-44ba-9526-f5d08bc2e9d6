/**
 * Welcome Tutorial Modal
 *
 * Interactive tutorial that explains the AI categorization process and key features
 * to new users. Provides educational content about how Giki AI works.
 */
import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import {
  Brain,
  Upload,
  Target,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Sparkles,
  Zap,
  Eye,
  Settings,
  BarChart3,
  Clock,
  Shield,
  BookOpen,
} from 'lucide-react';

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  content: React.ReactNode;
  icon: React.ComponentType<{ className?: string }>;
}

interface WelcomeTutorialModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStartOnboarding?: () => void;
}

export const WelcomeTutorialModal: React.FC<WelcomeTutorialModalProps> = ({
  isOpen,
  onClose,
  onStartOnboarding,
}) => {
  const [currentStep, setCurrentStep] = useState(0);

  const steps: TutorialStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Giki AI',
      icon: Sparkles,
      description: 'Your financial management tool',
      content: (
        <div className="space-y-6 text-center">
          <div className="mx-auto w-24 h-24 bg-gradient-to-br from-blue-500 to-green-600 rounded-full flex flex-wrap items-center justify-center">
            <Sparkles className="h-12 w-12 truncatewhite" />
          </div>
          <div>
            <h3 className="text-2xl font-bold mb-3">Welcome to Giki AI</h3>
            <p className="text-muted-foreground max-w-md mx-auto text-lg">
              improve your financial data management with intelligent
              categorization and insights.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4 mt-8">
            <div className="text-center">
              <div className="truncate text-heading-1 text-success">85%+</div>
              <div className="text-body-small">Accuracy Rate</div>
            </div>
            <div className="text-center">
              <div className="truncate text-heading-1 text-info">30s</div>
              <div className="text-body-small">Processing Time</div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'ai-categorization',
      title: 'categorization',
      icon: Brain,
      description: 'How our intelligent system works',
      content: (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <Brain className="h-16 w-16 sm:w-16 mx-auto mb-4 truncatepurple-500" />
            <h3 className="text-xl font-bold mb-2">
              Smart Transaction Analysis
            </h3>
            <p className="text-muted-foreground">
              Our AI analyzes transaction patterns, merchant names, and
              descriptions to automatically categorize your financial data.
            </p>
          </div>

          <div className="space-y-4">
            <Card className="border-l-4 border-l-blue-500">
              <CardContent className="p-4 overflow-hidden">
                <div className="flex flex-wrap items-start space-x-3">
                  <Eye className="h-5 w-5 truncateblue-500 mt-1" />
                  <div>
                    <h4 className="font-semibold">Pattern Recognition</h4>
                    <p className="text-body-small">
                      Identifies spending patterns and merchant types
                      automatically
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-green-500">
              <CardContent className="p-4 overflow-hidden">
                <div className="flex flex-wrap items-start space-x-3">
                  <Zap className="h-5 w-5 truncategreen-500 mt-1" />
                  <div>
                    <h4 className="font-semibold">Continuous Learning</h4>
                    <p className="text-body-small">
                      Improves accuracy based on your corrections and feedback
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-purple-500">
              <CardContent className="p-4 overflow-hidden">
                <div className="flex flex-wrap items-start space-x-3">
                  <Shield className="h-5 w-5 truncatepurple-500 mt-1" />
                  <div>
                    <h4 className="font-semibold">Confidence Scoring</h4>
                    <p className="text-body-small">
                      Each categorization includes a confidence score for
                      transparency
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ),
    },
    {
      id: 'upload-process',
      title: 'Upload Process',
      icon: Upload,
      description: 'Simple steps to get started',
      content: (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <Upload className="h-16 w-16 sm:w-16 mx-auto mb-4 truncateblue-500" />
            <h3 className="text-xl font-bold mb-2">Easy File Upload</h3>
            <p className="text-muted-foreground">
              Upload your transaction data in just a few clicks
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex flex-wrap items-center space-x-4">
              <div className="w-8 h-8 bg-info truncatewhite rounded-full flex flex-wrap items-center justify-center font-bold">
                1
              </div>
              <div>
                <h4 className="font-semibold">Upload Files</h4>
                <p className="text-body-small">
                  Drag & drop CSV, Excel files (.xlsx, .xls, .csv)
                </p>
              </div>
            </div>

            <div className="flex flex-wrap items-center space-x-4">
              <div className="w-8 h-8 bg-success truncatewhite rounded-full flex flex-wrap items-center justify-center font-bold">
                2
              </div>
              <div>
                <h4 className="font-semibold">AI Analysis</h4>
                <p className="text-body-small">
                  Our system automatically maps columns and processes data
                </p>
              </div>
            </div>

            <div className="flex flex-wrap items-center space-x-4">
              <div className="w-8 h-8 bg-purple-500 truncatewhite rounded-full flex flex-wrap items-center justify-center font-bold">
                3
              </div>
              <div>
                <h4 className="font-semibold">Review Results</h4>
                <p className="text-body-small">
                  Check categorizations and make any necessary adjustments
                </p>
              </div>
            </div>
          </div>

          <div className="bg-info/5 p-4 rounded-lg">
            <div className="flex flex-wrap items-start space-x-3">
              <BookOpen className="h-5 w-5 text-info mt-0.5" />
              <div>
                <h4 className="font-semibold text-info-foreground">
                  Supported Formats
                </h4>
                <div className="flex flex-wrap space-x-2 mt-2">
                  <Badge variant="outline" className="max-w-[150px] truncate">
                    .CSV
                  </Badge>
                  <Badge variant="outline" className="max-w-[150px] truncate">
                    .XLSX
                  </Badge>
                  <Badge variant="outline" className="max-w-[150px] truncate">
                    .XLS
                  </Badge>
                </div>
                <p className="text-sm text-info mt-2">
                  Maximum file size: 5MB per file
                </p>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'review-workflow',
      title: 'Review & Refine',
      icon: Target,
      description: 'Perfect your categorizations',
      content: (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <Target className="h-16 w-16 sm:w-16 mx-auto mb-4 truncategreen-500" />
            <h3 className="text-xl font-bold mb-2">Review & Improve</h3>
            <p className="text-muted-foreground">
              Fine-tune AI categorizations to match your specific needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex flex-wrap items-center">
                  <CheckCircle className="h-5 w-5 mr-2 truncategreen-500" />
                  AI Suggestions
                </CardTitle>
              </CardHeader>
              <CardContent className="overflow-hidden">
                <ul className="space-y-2 text-sm">
                  <li className="flex flex-wrap items-center space-x-2">
                    <div className="w-2 h-2 bg-success rounded-full"></div>
                    <span>High-confidence categorizations</span>
                  </li>
                  <li className="flex flex-wrap items-center space-x-2">
                    <div className="w-2 h-2 bg-success rounded-full"></div>
                    <span>Confidence scores displayed</span>
                  </li>
                  <li className="flex flex-wrap items-center space-x-2">
                    <div className="w-2 h-2 bg-success rounded-full"></div>
                    <span>One-click acceptance</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex flex-wrap items-center">
                  <Settings className="h-5 w-5 mr-2 truncateblue-500" />
                  Manual Adjustments
                </CardTitle>
              </CardHeader>
              <CardContent className="overflow-hidden">
                <ul className="space-y-2 text-sm">
                  <li className="flex flex-wrap items-center space-x-2">
                    <div className="w-2 h-2 bg-info rounded-full"></div>
                    <span>Easy category changes</span>
                  </li>
                  <li className="flex flex-wrap items-center space-x-2">
                    <div className="w-2 h-2 bg-info rounded-full"></div>
                    <span>Bulk editing options</span>
                  </li>
                  <li className="flex flex-wrap items-center space-x-2">
                    <div className="w-2 h-2 bg-info rounded-full"></div>
                    <span>Custom category creation</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="flex flex-wrap items-start space-x-3">
              <Zap className="h-5 w-5 text-warning mt-0.5" />
              <div>
                <h4 className="font-semibold truncateyellow-900">
                  Smart Learning
                </h4>
                <p className="text-sm text-warning mt-1">
                  Your corrections help train the AI to better understand your
                  preferences, improving accuracy for future uploads.
                </p>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'insights-analytics',
      title: 'Insights & Analytics',
      icon: BarChart3,
      description: 'Understand your financial patterns',
      content: (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <BarChart3 className="h-16 w-16 sm:w-16 mx-auto mb-4 truncateindigo-500" />
            <h3 className="text-xl font-bold mb-2">Analytics</h3>
            <p className="text-muted-foreground">
              Generate insights and reports from your categorized data
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="border-indigo-200">
              <CardContent className="p-4 overflow-hidden">
                <TrendingUp className="h-8 w-8 truncateindigo-500 mb-3" />
                <h4 className="font-semibold mb-2">Spending Trends</h4>
                <p className="text-body-small">
                  Track spending patterns across categories and time periods
                </p>
              </CardContent>
            </Card>

            <Card className="border-green-200">
              <CardContent className="p-4 overflow-hidden">
                <BarChart3 className="h-8 w-8 truncategreen-500 mb-3" />
                <h4 className="font-semibold mb-2">Category Breakdown</h4>
                <p className="text-body-small">
                  Visualize spending distribution across different categories
                </p>
              </CardContent>
            </Card>

            <Card className="border-purple-200">
              <CardContent className="p-4 overflow-hidden">
                <Clock className="h-8 w-8 truncatepurple-500 mb-3" />
                <h4 className="font-semibold mb-2">Time Analysis</h4>
                <p className="text-body-small">
                  Understand seasonal patterns and budget cycles
                </p>
              </CardContent>
            </Card>

            <Card className="border-blue-200">
              <CardContent className="p-4 overflow-hidden">
                <Target className="h-8 w-8 truncateblue-500 mb-3" />
                <h4 className="font-semibold mb-2">Custom Reports</h4>
                <p className="text-body-small">
                  Create tailored reports for specific business needs
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="text-center">
            <Badge
              variant="secondary"
              className="px-4 py-2 max-w-[150px] truncate"
            >
              Export to Excel, CSV, and PDF formats
            </Badge>
          </div>
        </div>
      ),
    },
  ];

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;
  const isFirstStep = currentStep === 0;

  const handleNext = () => {
    if (isLastStep) {
      onStartOnboarding?.();
      onClose();
    } else {
      setCurrentStep((prev) => Math.min(steps.length - 1, prev + 1));
    }
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(0, prev - 1));
  };

  const handleSkip = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex flex-wrap items-center space-x-2">
            <currentStepData.icon className="h-5 w-5" />
            <span>{currentStepData.title}</span>
          </DialogTitle>
          <DialogDescription>{currentStepData.description}</DialogDescription>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="mb-6">
          <div className="flex flex-wrap justify-between items-center mb-2">
            <span className="text-body-small">
              Step {currentStep + 1} of {steps.length}
            </span>
            <Button
              className="max-w-full"
              variant="ghost"
              size="sm"
              onClick={() => void handleSkip()}
            >
              Skip Tutorial
            </Button>
          </div>
          <Progress
            value={(currentStep / (steps.length - 1)) * 100}
            className="w-full"
          />
        </div>

        {/* Step Indicators */}
        <div className="flex flex-wrap justify-center space-x-2 mb-6">
          {steps.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentStep(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentStep
                  ? 'bg-info'
                  : index < currentStep
                    ? 'bg-success'
                    : 'bg-muted'
              }`}
              aria-label={`Go to step ${index + 1}`}
            />
          ))}
        </div>

        {/* Step Content */}
        <div className="min-h-[400px] overflow-y-auto mb-6">
          {currentStepData.content}
        </div>

        {/* Navigation */}
        <div className="flex flex-wrap justify-between items-center">
          <Button
            className="max-w-full"
            variant="outline"
            onClick={() => void handlePrevious()}
            disabled={isFirstStep}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>

          <div className="flex flex-wrap space-x-2">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center space-x-1 text-xs ${
                  index === currentStep
                    ? 'text-info font-medium'
                    : 'text-muted-foreground'
                }`}
              >
                <step.icon className="h-3 w-3" />
                <span className="hidden sm:inline">{step.title}</span>
              </div>
            ))}
          </div>

          <Button className="max-w-full" onClick={() => void handleNext()}>
            {isLastStep ? 'Start Using Giki AI' : 'Next'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default WelcomeTutorialModal;
