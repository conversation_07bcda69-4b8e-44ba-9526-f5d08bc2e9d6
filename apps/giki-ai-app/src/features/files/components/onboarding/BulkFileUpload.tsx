import React, { useState, useRef } from 'react';
import { Button } from '@/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import {
  Upload,
  FileSpreadsheet,
  DollarSign,
  CheckCircle,
  AlertCircle,
  X,
  Trash2,
  AlertTriangle,
} from 'lucide-react';
import { useToast } from '@/shared/components/ui/use-toast';
import { Progress } from '@/shared/components/ui/progress';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';

interface FileUploadItem {
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}

interface BulkFileUploadProps {
  onBulkUpload: (files: File[], currency: string) => Promise<void>;
  isUploading: boolean;
}

export const BulkFileUpload: React.FC<BulkFileUploadProps> = ({
  onBulkUpload,
  isUploading,
}) => {
  const [fileItems, setFileItems] = useState<FileUploadItem[]>([]);
  const [selectedCurrency, setSelectedCurrency] = useState<string>('');
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const currencies = [
    { value: 'USD', label: 'USD ($)' },
    { value: 'EUR', label: 'EUR (€)' },
    { value: 'GBP', label: 'GBP (£)' },
    { value: 'CAD', label: 'CAD (C$)' },
    { value: 'AUD', label: 'AUD (A$)' },
    { value: 'JPY', label: 'JPY (¥)' },
    { value: 'INR', label: 'INR (₹)' },
  ];

  const handleFilesSelect = (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const newFileItems: FileUploadItem[] = [];

    for (const file of fileArray) {
      // Basic validation
      const allowedTypes = [
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument?.spreadsheetml?.sheet',
      ];

      if (
        !allowedTypes.includes(file.type) &&
        !file?.name?.toLowerCase().endsWith('.csv') &&
        !file?.name?.toLowerCase().endsWith('.xlsx') &&
        !file?.name?.toLowerCase().endsWith('.xls')
      ) {
        toast({
          title: 'Invalid File Type',
          description: `${file.name} is not a valid CSV or Excel file`,
          variant: 'destructive',
        });
        continue;
      }

      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: 'File Too Large',
          description: `${file.name} exceeds the 10MB limit`,
          variant: 'destructive',
        });
        continue;
      }

      // Check for duplicates
      const isDuplicate = fileItems.some(
        (item) =>
          item?.file?.name === file.name && item?.file?.size === file.size,
      );

      if (!isDuplicate) {
        newFileItems.push({
          file,
          status: 'pending',
          progress: 0,
        });
      }
    }

    if (newFileItems.length > 0) {
      setFileItems((prev) => [...prev, ...newFileItems]);
      toast({
        title: 'Files Added',
        description: `Added ${newFileItems.length} file(s) to upload queue`,
      });
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e?.dataTransfer?.files && e?.dataTransfer?.files.length > 0) {
      void handleFilesSelect(e?.dataTransfer?.files);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e?.target?.files && e?.target?.files.length > 0) {
      void handleFilesSelect(e?.target?.files);
    }
  };

  const removeFile = (index: number) => {
    setFileItems((prev) => prev.filter((_, i) => i !== index));
  };

  const clearAllFiles = () => {
    setFileItems([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleBulkUpload = async () => {
    if (fileItems.length === 0 || !selectedCurrency) return;

    try {
      // Update all files to uploading status
      setFileItems((prev) =>
        prev.map((item) => ({
          ...item,
          status: 'uploading',
          progress: 0,
        })),
      );

      // Simulate progress for demo
      // const updateProgress = (index: number, progress: number) => {
      //   setFileItems((prev) =>
      //     prev.map((item, i) => (i === index ? { ...item, progress } : item)),
      //   );
      // };

      // Upload files
      const files = fileItems.map((item) => item.file);
      await onBulkUpload(files, selectedCurrency);

      // Mark all as success
      setFileItems((prev) =>
        prev.map((item) => ({
          ...item,
          status: 'success',
          progress: 100,
        })),
      );

      toast({
        title: 'Bulk Upload Complete',
        description: `Successfully uploaded ${files.length} file(s)`,
      });

      // Clear after delay
      setTimeout(() => {
        clearAllFiles();
        setSelectedCurrency('');
      }, 2000);
    } catch {
      // Mark failed files
      setFileItems((prev) =>
        prev.map((item) => ({
          ...item,
          status: item.status === 'success' ? 'success' : 'error',
          error: 'Upload failed',
        })),
      );

      toast({
        title: 'Upload Failed',
        description: 'Some files failed to upload. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const getFileIcon = (status: FileUploadItem['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-success" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-destructive" />;
      case 'uploading':
        return (
          <div className="w-5 h-5 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return <FileSpreadsheet className="w-5 h-5 text-muted-foreground/50" />;
    }
  };

  const pendingFiles = fileItems.filter(
    (item) => item.status === 'pending',
  ).length;
  const totalSize = fileItems.reduce((sum, item) => sum + item?.file?.size, 0);

  return (
    <div className="space-y-4">
      {/* File Drop Zone */}
      <Card
        className={`border-2 border-dashed transition-colors ${
          dragActive
            ? 'border-primary bg-emerald-50'
            : fileItems.length > 0
              ? 'border-green-500 bg-green-50'
              : 'border-border hover:border-border'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <CardContent className="p-8 overflow-hidden">
          <div className="text-center">
            <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <div>
              <p className="truncate text-heading-5">
                Drop multiple files here or click to browse
              </p>
              <p className="text-body-small">
                Supports CSV and Excel files (.csv, .xlsx, .xls) up to 10MB each
              </p>
            </div>
            <div className="flex flex-wrap gap-2 justify-center mt-4">
              <Button
                className="max-w-full gap-2"
                onClick={() => fileInputRef.current?.click()}
                variant="outline"
              >
                <Upload className="w-4 h-4" />
                Choose Files
              </Button>
              {fileItems.length > 0 && (
                <Button
                  className="max-w-full gap-2 text-destructive hover:text-destructive"
                  onClick={() => void clearAllFiles()}
                  variant="outline"
                >
                  <Trash2 className="w-4 h-4" />
                  Clear All
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv,.xlsx,.xls"
        onChange={handleFileInputChange}
        className="hidden"
        multiple
        data-testid="file-input"
      />

      {/* File List */}
      {fileItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-label">
              Files to Upload ({fileItems.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 overflow-hidden">
            <div className="max-h-60 overflow-y-auto space-y-2">
              {fileItems.map((item, index) => (
                <div
                  key={`${item?.file?.name}-${index}`}
                  className="flex flex-wrap items-center justify-between p-3 bg-muted/50 rounded-lg"
                >
                  <div className="flex flex-wrap items-center gap-3 flex-1">
                    {getFileIcon(item.status)}
                    <div className="flex flex-wrap-1 min-w-0">
                      <p className="text-label truncate">{item?.file?.name}</p>
                      <p className="truncate text-caption text-muted-foreground">
                        {(item?.file?.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      {item.status === 'uploading' && (
                        <Progress value={item.progress} className="h-1 mt-1" />
                      )}
                      {item.error && (
                        <p className="truncate text-caption text-destructive mt-1">
                          {item.error}
                        </p>
                      )}
                    </div>
                  </div>
                  {item.status === 'pending' && (
                    <Button
                      className="max-w-full text-destructive hover:text-destructive"
                      onClick={() => removeFile(index)}
                      variant="ghost"
                      size="sm"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>

            {/* Summary */}
            <div className="flex flex-wrap items-center justify-between pt-3 border-t">
              <div className="text-body-small">
                Total: {(totalSize / 1024 / 1024).toFixed(2)} MB
              </div>
              {pendingFiles > 0 && (
                <div className="text-sm text-info">
                  {pendingFiles} file(s) ready to upload
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Currency Selection and Upload */}
      {fileItems.length > 0 && (
        <Card>
          <CardContent className="p-4 overflow-hidden">
            <div className="flex flex-wrap items-center justify-between">
              <div className="flex flex-wrap items-center gap-3">
                <DollarSign className="w-5 h-5 text-success" />
                <div>
                  <p className="font-medium">Select Currency</p>
                  <p className="text-body-small">
                    All files will use the same currency
                  </p>
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-3">
                <Select
                  value={selectedCurrency}
                  onValueChange={setSelectedCurrency}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  className="max-w-full gap-2"
                  onClick={() => void handleBulkUpload()}
                  disabled={
                    !selectedCurrency || isUploading || pendingFiles === 0
                  }
                >
                  {isUploading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Uploading {fileItems.length} files...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4" />
                      Upload All Files
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Tip:</strong> You can upload multiple months of transaction
          data at once. Each file will be processed sequentially and
          transactions will be automatically categorized using our AI system.
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default BulkFileUpload;
