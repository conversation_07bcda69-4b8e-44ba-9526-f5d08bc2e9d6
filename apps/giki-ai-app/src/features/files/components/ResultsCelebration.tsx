/**
 * Results Celebration Component - Professional Results Display with Hierarchical Visualization
 * Matches the professional design from 300-categorization-results.html mockup
 * Enhanced with hierarchical categorization data visualization
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  CheckCircle, 
  ArrowRight, 
  Download, 
  BarChart3, 
  TrendingUp,
  FileText,
  Sparkles,
  ChevronRight,
  ChevronDown,
  Layers
} from 'lucide-react';
import { getHierarchicalResults, HierarchicalResults, CategoryVisualization } from '../services/fileService';

interface ResultsCelebrationProps {
  results: {
    uploadId: string;
    fileName: string;
    totalTransactions: number;
    categorizedTransactions: number;
    accuracy: number;
    processingTime: number;
    categories: string[];
    improvements?: {
      accuracyGain: number;
      timesSaved: string;
      categoriesAdded: number;
    };
  };
  onContinue: () => void;
  onDownload: (format: 'xlsx' | 'csv' | 'pdf') => void;
}

export const ResultsCelebration: React.FC<ResultsCelebrationProps> = ({
  results,
  onContinue,
  onDownload,
}) => {
  const navigate = useNavigate();
  const [showCon<PERSON><PERSON>, setShowConfetti] = useState(true);
  const [animatedAccuracy, setAnimatedAccuracy] = useState(0);
  const [hierarchicalData, setHierarchicalData] = useState<HierarchicalResults | null>(null);
  const [loadingHierarchy, setLoadingHierarchy] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set());

  // Animate accuracy percentage
  useEffect(() => {
    const duration = 2000; // 2 seconds
    const steps = 60;
    const increment = results.accuracy / steps;
    let currentStep = 0;

    const timer = setInterval(() => {
      currentStep++;
      setAnimatedAccuracy(Math.min(increment * currentStep, results.accuracy));
      
      if (currentStep >= steps) {
        clearInterval(timer);
      }
    }, duration / steps);

    return () => clearInterval(timer);
  }, [results.accuracy]);

  // Hide confetti after animation
  useEffect(() => {
    const timer = setTimeout(() => setShowConfetti(false), 3000);
    return () => clearTimeout(timer);
  }, []);

  // Fetch hierarchical results
  useEffect(() => {
    const fetchHierarchicalResults = async () => {
      if (!results.uploadId) return;
      
      setLoadingHierarchy(true);
      try {
        const hierarchyResult = await getHierarchicalResults(results.uploadId);
        if ('type' in hierarchyResult) {
          console.error('Failed to fetch hierarchical results:', hierarchyResult);
        } else {
          setHierarchicalData(hierarchyResult);
        }
      } catch (error) {
        console.error('Error fetching hierarchical results:', error);
      } finally {
        setLoadingHierarchy(false);
      }
    };

    fetchHierarchicalResults();
  }, [results.uploadId]);

  const formatAccuracy = (accuracy: number) => {
    return Math.round(accuracy * 10) / 10; // Round to 1 decimal place
  };

  const getAccuracyLevel = (accuracy: number) => {
    if (accuracy >= 95) return { label: 'Exceptional', color: 'text-success', bg: 'bg-green-50' };
    if (accuracy >= 90) return { label: 'Excellent', color: 'text-green-500', bg: 'bg-green-50' };
    if (accuracy >= 85) return { label: 'Very Good', color: 'text-blue-500', bg: 'bg-blue-50' };
    return { label: 'Good', color: 'text-orange-500', bg: 'bg-orange-50' };
  };

  const accuracyLevel = getAccuracyLevel(results.accuracy);

  const toggleCategory = (categoryId: number) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const renderCategoryTree = (category: CategoryVisualization, depth: number = 0) => {
    const hasChildren = category.children && category.children.length > 0;
    const isExpanded = expandedCategories.has(category.id);

    return (
      <div key={category.id} className={`${depth > 0 ? 'ml-6' : ''}`}>
        <div 
          className={`flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors ${hasChildren ? 'cursor-pointer' : ''}`}
          onClick={() => hasChildren && toggleCategory(category.id)}
        >
          <div className="flex items-center gap-2 flex-1">
            {hasChildren && (
              <div className="w-5 h-5 flex items-center justify-center">
                {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              </div>
            )}
            {!hasChildren && <div className="w-5" />}
            
            <div className="flex items-center gap-2 flex-1">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: category.color || '#6B7280' }}
              />
              <span className="text-sm font-medium text-gray-700">{category.name}</span>
              {category.gl_code && (
                <span className="text-xs text-gray-500">({category.gl_code})</span>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">{category.transaction_count} txns</span>
            <span className="text-sm font-medium text-gray-900">
              ${category.total_amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
            <span className="text-sm font-medium text-gray-600">
              {category.percentage.toFixed(1)}%
            </span>
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div className="mt-1">
            {category.children.map(child => renderCategoryTree(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-[#F8F9FA] min-h-screen relative overflow-hidden">
      {/* Confetti Animation */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none z-10">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-brand-primary opacity-70 animate-bounce"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${2 + Math.random() * 3}s`
              }}
            />
          ))}
        </div>
      )}

      {/* Hero Section */}
      <div className="bg-white border-b border-[#E1E5E9] relative">
        <div className="max-w-[1200px] mx-auto px-8 py-16 text-center">
          <div className="w-20 h-20 bg-gradient-to-br from-brand-primary to-[#059669] text-white rounded-full flex items-center justify-center text-4xl mx-auto mb-8 shadow-2xl">
            □
          </div>
          
          <h1 className="text-4xl font-bold text-brand-primary mb-4">
            Categorization Complete!
          </h1>
          <p className="text-xl text-[#6B7280] mb-8 max-w-2xl mx-auto">
            Your {results.totalTransactions} transactions have been successfully categorized with 
            our AI-powered system. Ready to review and export your results.
          </p>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-br from-[#E8F5E8] to-[#F0FDF4] border border-brand-primary/20 rounded-xl p-6">
              <div className="text-3xl font-bold text-brand-primary mb-2">
                {formatAccuracy(animatedAccuracy)}%
              </div>
              <div className="text-sm font-medium text-[#6B7280] mb-1">AI Accuracy</div>
              <div className={`text-xs px-2 py-1 rounded-full ${accuracyLevel.bg} ${accuracyLevel.color}`}>
                {accuracyLevel.label}
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#F0F9FF] to-[#EFF6FF] border border-[#2563EB]/20 rounded-xl p-6">
              <div className="text-3xl font-bold text-[#2563EB] mb-2">
                {results.categorizedTransactions}
              </div>
              <div className="text-sm font-medium text-[#6B7280] mb-1">Transactions</div>
              <div className="text-xs text-[#6B7280]">Successfully categorized</div>
            </div>

            <div className="bg-gradient-to-br from-[#FEF3C7] to-[#FEF7CD] border border-[#F59E0B]/20 rounded-xl p-6">
              <div className="text-3xl font-bold text-[#F59E0B] mb-2">
                {results.categories.length}
              </div>
              <div className="text-sm font-medium text-[#6B7280] mb-1">Categories</div>
              <div className="text-xs text-[#6B7280]">Business hierarchy applied</div>
            </div>

            <div className="bg-gradient-to-br from-[#F3E8FF] to-[#FAF5FF] border border-[#8B5CF6]/20 rounded-xl p-6">
              <div className="text-3xl font-bold text-[#8B5CF6] mb-2">
                {Math.round(results.processingTime)}s
              </div>
              <div className="text-sm font-medium text-[#6B7280] mb-1">Processing Time</div>
              <div className="text-xs text-[#6B7280]">Lightning fast AI</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={onContinue}
              className="bg-brand-primary hover:bg-brand-primary-hover text-white px-8 py-4 rounded-xl text-lg font-semibold transition-all hover:transform hover:-translate-y-0.5 shadow-lg flex items-center gap-2"
            >
              Review Transactions <ArrowRight size={20} />
            </button>
            <button
              onClick={() => onDownload('xlsx')}
              className="bg-white hover:bg-[#F8F9FA] text-brand-primary border-2 border-brand-primary px-8 py-4 rounded-xl text-lg font-semibold transition-all hover:transform hover:-translate-y-0.5 shadow-lg flex items-center gap-2"
            >
              <Download size={20} /> Export Results
            </button>
          </div>
        </div>
      </div>

      {/* Detailed Results */}
      <div className="max-w-[1200px] mx-auto px-8 py-12">
        {/* File Processing Summary */}
        <div className="bg-white border border-[#E1E5E9] rounded-2xl p-8 mb-8">
          <h2 className="text-2xl font-semibold text-brand-primary mb-6 flex items-center gap-3">
            <div className="w-8 h-8 bg-success/10 text-brand-primary rounded-lg flex items-center justify-center">
              <FileText size={20} />
            </div>
            Processing Summary
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-brand-primary mb-4">File Details</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-[#6B7280]">File Name:</span>
                  <span className="text-brand-primary font-medium">{results.fileName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#6B7280]">Total Transactions:</span>
                  <span className="text-brand-primary font-medium">{results.totalTransactions.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#6B7280]">Successfully Processed:</span>
                  <span className="text-[#059669] font-medium">{results.categorizedTransactions.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#6B7280]">Processing Time:</span>
                  <span className="text-brand-primary font-medium">{Math.round(results.processingTime)} seconds</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-brand-primary mb-4">AI Performance</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-[#6B7280]">Accuracy Score:</span>
                  <span className="text-[#059669] font-bold">{formatAccuracy(results.accuracy)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#6B7280]">Quality Level:</span>
                  <span className={`font-medium ${accuracyLevel.color}`}>{accuracyLevel.label}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#6B7280]">Categories Applied:</span>
                  <span className="text-brand-primary font-medium">{results.categories.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#6B7280]">Processing Speed:</span>
                  <span className="text-brand-primary font-medium">
                    {Math.round(results.totalTransactions / results.processingTime)} tx/sec
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Categories Preview */}
        <div className="bg-white border border-[#E1E5E9] rounded-2xl p-8 mb-8">
          <h2 className="text-2xl font-semibold text-brand-primary mb-6 flex items-center gap-3">
            <div className="w-8 h-8 bg-success/10 text-brand-primary rounded-lg flex items-center justify-center">
              <BarChart3 size={20} />
            </div>
            Categories Applied
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {results.categories.slice(0, 8).map((category, index) => (
              <div key={index} className="p-4 bg-[#F8F9FA] border border-[#E1E5E9] rounded-xl hover:border-brand-primary transition-colors">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-brand-primary rounded-full"></div>
                  <span className="text-brand-primary font-medium text-sm">{category}</span>
                </div>
              </div>
            ))}
            {results.categories.length > 8 && (
              <div className="p-4 bg-success/10 border border-brand-primary rounded-xl">
                <div className="text-brand-primary font-medium text-sm">
                  +{results.categories.length - 8} more categories
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Improvements Section */}
        {results.improvements && (
          <div className="bg-gradient-to-br from-[#E8F5E8] via-[#F0F9FF] to-[#FEF3C7] border border-brand-primary/20 rounded-2xl p-8 mb-8">
            <h2 className="text-2xl font-semibold text-brand-primary mb-6 flex items-center gap-3">
              <div className="w-8 h-8 bg-brand-primary text-white rounded-lg flex items-center justify-center">
                <TrendingUp size={20} />
              </div>
              Performance Improvements
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 bg-white/80 rounded-xl">
                <div className="text-3xl font-bold text-[#059669] mb-2">
                  +{results.improvements.accuracyGain}%
                </div>
                <div className="text-sm font-medium text-[#6B7280]">Accuracy Improvement</div>
                <div className="text-xs text-[#9CA3AF] mt-1">vs. manual categorization</div>
              </div>
              
              <div className="text-center p-6 bg-white/80 rounded-xl">
                <div className="text-3xl font-bold text-[#2563EB] mb-2">
                  {results.improvements.timesSaved}
                </div>
                <div className="text-sm font-medium text-[#6B7280]">Time Saved</div>
                <div className="text-xs text-[#9CA3AF] mt-1">vs. manual entry</div>
              </div>
              
              <div className="text-center p-6 bg-white/80 rounded-xl">
                <div className="text-3xl font-bold text-[#F59E0B] mb-2">
                  {results.improvements.categoriesAdded}
                </div>
                <div className="text-sm font-medium text-[#6B7280]">Categories Added</div>
                <div className="text-xs text-[#9CA3AF] mt-1">automated hierarchy</div>
              </div>
            </div>
          </div>
        )}

        {/* Hierarchical Categorization Breakdown */}
        {hierarchicalData && !loadingHierarchy && (
          <div className="bg-white border border-[#E1E5E9] rounded-2xl p-8 mb-8">
            <h2 className="text-2xl font-semibold text-brand-primary mb-6 flex items-center gap-3">
              <div className="w-8 h-8 bg-success/10 text-brand-primary rounded-lg flex items-center justify-center">
                <Layers size={20} />
              </div>
              Hierarchical Categorization Breakdown
            </h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Income Hierarchy */}
              <div>
                <h3 className="font-semibold text-brand-primary mb-4 flex items-center justify-between">
                  <span>Income Categories</span>
                  <span className="text-lg">${hierarchicalData.total_income.toLocaleString('en-US', { minimumFractionDigits: 2 })}</span>
                </h3>
                <div className="border border-[#E1E5E9] rounded-xl p-4 max-h-96 overflow-y-auto">
                  {hierarchicalData.income_hierarchy.length > 0 ? (
                    hierarchicalData.income_hierarchy.map(category => renderCategoryTree(category))
                  ) : (
                    <p className="text-[#6B7280] text-sm">No income transactions found</p>
                  )}
                </div>
              </div>

              {/* Expense Hierarchy */}
              <div>
                <h3 className="font-semibold text-brand-primary mb-4 flex items-center justify-between">
                  <span>Expense Categories</span>
                  <span className="text-lg">${hierarchicalData.total_expenses.toLocaleString('en-US', { minimumFractionDigits: 2 })}</span>
                </h3>
                <div className="border border-[#E1E5E9] rounded-xl p-4 max-h-96 overflow-y-auto">
                  {hierarchicalData.expense_hierarchy.length > 0 ? (
                    hierarchicalData.expense_hierarchy.map(category => renderCategoryTree(category))
                  ) : (
                    <p className="text-[#6B7280] text-sm">No expense transactions found</p>
                  )}
                </div>
              </div>
            </div>

            {/* Enhancement Suggestions */}
            {hierarchicalData.enhancement_suggestions.length > 0 && (
              <div className="mt-6 bg-gradient-to-r from-[#FEF3C7] to-[#FEF7CD] border border-[#F59E0B]/20 rounded-xl p-6">
                <h3 className="font-semibold text-[#92400E] mb-3 flex items-center gap-2">
                  <Sparkles size={20} />
                  Enhancement Opportunities
                </h3>
                <div className="space-y-2">
                  {hierarchicalData.enhancement_suggestions.map((suggestion, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className={`w-2 h-2 rounded-full mt-1.5 ${
                        suggestion.priority === 'high' ? 'bg-red-500' : 
                        suggestion.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      }`} />
                      <div className="flex-1">
                        <p className="text-sm text-[#92400E]">{suggestion.message}</p>
                        <p className="text-xs text-[#B45309] mt-1">
                          Potential accuracy gain: +{suggestion.potential_accuracy_gain.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Next Steps */}
        <div className="bg-white border border-[#E1E5E9] rounded-2xl p-8">
          <h2 className="text-2xl font-semibold text-brand-primary mb-6">What's Next?</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-6 border border-[#E1E5E9] rounded-xl hover:border-brand-primary transition-colors cursor-pointer"
                 onClick={onContinue}>
              <div className="w-12 h-12 bg-success/10 text-brand-primary rounded-xl flex items-center justify-center mb-4">
                <CheckCircle size={24} />
              </div>
              <h3 className="font-semibold text-brand-primary mb-2">Review & Adjust</h3>
              <p className="text-[#6B7280] text-sm mb-4">
                Review categorized transactions and make any necessary adjustments to improve accuracy.
              </p>
              <div className="text-brand-primary text-sm font-medium flex items-center gap-1">
                Start Review <ArrowRight size={14} />
              </div>
            </div>

            <div className="p-6 border border-[#E1E5E9] rounded-xl hover:border-brand-primary transition-colors cursor-pointer"
                 onClick={() => onDownload('xlsx')}>
              <div className="w-12 h-12 bg-success/10 text-brand-primary rounded-xl flex items-center justify-center mb-4">
                <Download size={24} />
              </div>
              <h3 className="font-semibold text-brand-primary mb-2">Export Results</h3>
              <p className="text-[#6B7280] text-sm mb-4">
                Download your categorized data in Excel, CSV, or PDF format for your accounting software.
              </p>
              <div className="text-brand-primary text-sm font-medium flex items-center gap-1">
                Export Now <ArrowRight size={14} />
              </div>
            </div>

            <div className="p-6 border border-[#E1E5E9] rounded-xl hover:border-brand-primary transition-colors cursor-pointer"
                 onClick={() => navigate('/reports')}>
              <div className="w-12 h-12 bg-success/10 text-brand-primary rounded-xl flex items-center justify-center mb-4">
                <BarChart3 size={24} />
              </div>
              <h3 className="font-semibold text-brand-primary mb-2">View Analytics</h3>
              <p className="text-[#6B7280] text-sm mb-4">
                Explore detailed analytics and insights about your financial data and spending patterns.
              </p>
              <div className="text-brand-primary text-sm font-medium flex items-center gap-1">
                View Reports <ArrowRight size={14} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultsCelebration;