/**
 * Memoized Activity Item Component for Live Categorization Display
 * Optimized for performance when rendering frequent activity updates
 */
import React from 'react';

interface Activity {
  id: string;
  description: string;
  category: string;
  status: 'processing' | 'complete';
}

interface ActivityItemProps {
  activity: Activity;
  isFirst: boolean;
}

// Memoized activity item component to prevent unnecessary re-renders
export const ActivityItem = React.memo<ActivityItemProps>(({
  activity,
  isFirst,
}) => {
  return (
    <div 
      className={`flex items-center justify-between py-2 px-2 rounded-lg border border-transparent transition-all duration-200 ${
        isFirst ? 'bg-success/10 border-brand-primary/20' : ''
      }`}
    >
      <div className="flex items-center gap-2">
        <div className={`w-1.5 h-1.5 ${activity.status === 'complete' ? 'bg-success' : 'bg-warning'} rounded-full ${
          activity.status === 'processing' ? 'animate-pulse' : ''
        }`}></div>
        <span className="text-xs truncate max-w-[200px]">{activity.description}</span>
      </div>
      <span className={`text-xs font-medium ${activity.status === 'complete' ? 'text-[#059669]' : 'text-[#D97706]'}`}>
        → {activity.category}
      </span>
    </div>
  );
}, (prevProps, nextProps) => {
  // Only re-render if the activity changes or if the isFirst status changes
  return (
    prevProps.activity.id === nextProps.activity.id &&
    prevProps.activity.status === nextProps.activity.status &&
    prevProps.isFirst === nextProps.isFirst
  );
});

ActivityItem.displayName = 'ActivityItem';

export default ActivityItem;