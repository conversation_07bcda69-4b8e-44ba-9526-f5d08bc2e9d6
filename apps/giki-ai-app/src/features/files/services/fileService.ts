import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError, type ApiError } from '@/shared/utils/errorHandling';

export interface UploadResponse {
  upload_id: string;
  filename: string;
  size: number;
  status: string;
  // Add any other fields that might be part of the response
}

export interface MultipleUploadResponse {
  uploads: UploadResponse[];
  total_files: number;
  successful: number;
  failed: number;
  message: string;
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// ============================================================================
// Enhanced File Validation Types (ADK Integration)
// ============================================================================

export interface FileValidationResult {
  is_valid: boolean;
  file_type: string;
  file_size: number;
  mime_type: string;
  validation_errors: string[];
  validation_warnings: string[];
  confidence: number;
}

export interface FileMetadata {
  filename: string;
  file_size: number;
  mime_type: string;
  file_type: string;
  encoding?: string;
  headers: string[];
  row_count: number;
  column_count: number;
  sample_data: string[][];
  extracted_at: string;
}

export interface FileSignatureCheck {
  signature_hash?: string;
  file_type: string;
  confidence: number;
  headers: string[];
  data_patterns: Record<string, unknown>;
  schema_match?: {
    matched: boolean;
    schema_id?: string;
    confidence: number;
    reason: string;
  };
  can_reuse_schema: boolean;
  checked_at: string;
  error?: string;
}

export interface FileValidationResponse {
  success: boolean;
  validation_result?: FileValidationResult;
  error?: string;
  message: string;
}

export interface FileMetadataResponse {
  success: boolean;
  metadata?: FileMetadata;
  error?: string;
  message: string;
}

export interface FileSignatureResponse {
  success: boolean;
  signature_check?: FileSignatureCheck;
  error?: string;
  message: string;
}

/**
 * Uploads a file to the server.
 * @param file - The file to upload.
 * @returns A promise that resolves with the upload response or an ApiError.
 */
export const uploadFile = async (
  file: File,
): Promise<UploadResponse | ApiError> => {
  const formData = new FormData();
  formData.append('files', file);

  try {
    // The API returns MultipleUploadResponse even for single file
    const response = await apiClient.postFormData<MultipleUploadResponse>(
      '/files/upload',
      formData,
    );

    // Extract the first upload from the response
    if (response.data.uploads && response.data.uploads.length > 0) {
      return response.data.uploads[0];
    }

    // If no uploads, return an error
    return handleApiError(new Error('No upload response received'), {
      context: 'uploadFile',
      defaultMessage: 'Failed to upload file - no response data.',
    });
  } catch (error) {
    return handleApiError(error, {
      context: 'uploadFile',
      defaultMessage: 'Failed to upload file.',
    });
  }
};

export const uploadMultipleFiles = async (
  files: File[],
): Promise<MultipleUploadResponse | ApiError> => {
  const formData = new FormData();

  // Append all files to the form data
  files.forEach((file) => {
    formData.append('files', file);
  });

  try {
    const response = await apiClient.postFormData<MultipleUploadResponse>(
      '/files/upload',
      formData,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'uploadMultipleFiles',
      defaultMessage: 'Failed to upload files.',
    });
  }
};

/**
 * Upload production files (files WITHOUT categories) for AI categorization.
 * This is for M1 Nuvie zero-onboarding and regular production usage.
 * @param files - Array of files to upload (should NOT contain category columns)
 * @returns A promise that resolves with the upload response or an ApiError.
 */
export const uploadProductionFiles = async (
  files: File[],
): Promise<MultipleUploadResponse | ApiError> => {
  const formData = new FormData();

  // Append all files to the form data
  files.forEach((file) => {
    formData.append('files', file);
  });

  try {
    const response = await apiClient.postFormData<MultipleUploadResponse>(
      '/files/upload-production-data',
      formData,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'uploadProductionFiles',
      defaultMessage:
        'Failed to upload production files for AI categorization.',
    });
  }
};

/**
 * Upload a single production file (file WITHOUT categories) for AI categorization.
 * @param file - The file to upload (should NOT contain category columns)
 * @returns A promise that resolves with the upload response or an ApiError.
 */
export const uploadProductionFile = async (
  file: File,
): Promise<UploadResponse | ApiError> => {
  try {
    const response = await uploadProductionFiles([file]);

    if (
      'uploads' in response &&
      response.uploads &&
      response.uploads.length > 0
    ) {
      return response.uploads[0];
    }

    // If no uploads, return an error
    return handleApiError(new Error('No upload response received'), {
      context: 'uploadProductionFile',
      defaultMessage: 'Failed to upload production file - no response data.',
    });
  } catch (error) {
    return handleApiError(error, {
      context: 'uploadProductionFile',
      defaultMessage: 'Failed to upload production file.',
    });
  }
};

/**
 * Response type for the columns API endpoint
 */
interface GetFileColumnsResponse {
  upload_id: string;
  columns: string[];
}

/**
 * Fetches the column names for a given upload ID.
 * @param uploadId - The ID of the upload.
 * @returns A promise that resolves with an array of column names or an ApiError.
 */
export const getFileColumns = async (
  uploadId: string,
): Promise<string[] | ApiError> => {
  try {
    const response = await apiClient.get<GetFileColumnsResponse>(
      `/uploads/${uploadId}/columns`,
    );

    // The backend returns {upload_id, columns} structure
    return response?.data?.columns || [];
  } catch (error) {
    return handleApiError(error, {
      context: 'getFileColumns',
      defaultMessage: `Failed to fetch columns for upload ID ${uploadId}.`,
    });
  }
};
// ============================================================================
// Enhanced File Validation Functions (ADK Integration)
// ============================================================================

/**
 * Validate a file using the FileValidationService ADK tool
 * @param file - The file to validate
 * @param maxFileSize - Maximum allowed file size in bytes (default: 10MB)
 * @returns Promise with validation results
 */
export const validateFile = async (
  file: File,
  maxFileSize: number = 10 * 1024 * 1024,
): Promise<FileValidationResponse | ApiError> => {
  try {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('max_file_size', maxFileSize.toString());

    const response = await apiClient.postFormData<FileValidationResponse>(
      '/files/validate',
      formData,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'validateFile',
      defaultMessage: 'Failed to validate file.',
    });
  }
};

/**
 * Extract comprehensive metadata from a file using the FileValidationService ADK tool
 * @param file - The file to analyze
 * @returns Promise with extracted metadata
 */
export const extractFileMetadata = async (
  file: File,
): Promise<FileMetadataResponse | ApiError> => {
  try {
    const formData = new FormData();
    formData.append('files', file);

    const response = await apiClient.postFormData<FileMetadataResponse>(
      '/files/metadata',
      formData,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'extractFileMetadata',
      defaultMessage: 'Failed to extract file metadata.',
    });
  }
};

/**
 * Check file signature for schema optimization using the FileValidationService ADK tool
 * @param file - The file to check
 * @param storedSchemas - Optional list of stored schemas to match against
 * @returns Promise with signature check results
 */
export const checkFileSignature = async (
  file: File,
  storedSchemas?: unknown[],
): Promise<FileSignatureResponse | ApiError> => {
  try {
    const formData = new FormData();
    formData.append('files', file);

    if (storedSchemas) {
      formData.append('stored_schemas', JSON.stringify(storedSchemas));
    }

    const response = await apiClient.postFormData<FileSignatureResponse>(
      '/files/signature',
      formData,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'checkFileSignature',
      defaultMessage: 'Failed to check file signature.',
    });
  }
};

/**
 * Enhanced file upload with validation and metadata extraction
 * @param file - The file to upload
 * @param options - Upload options
 * @returns Promise with comprehensive upload results
 */
export const uploadFileWithValidation = async (
  file: File,
  options: {
    validateFirst?: boolean;
    extractMetadata?: boolean;
    checkSignature?: boolean;
    maxFileSize?: number;
  } = {},
): Promise<{
  upload?: UploadResponse;
  validation?: FileValidationResponse;
  metadata?: FileMetadataResponse;
  signature?: FileSignatureResponse;
  errors: ApiError[];
}> => {
  const results = {
    upload: undefined as UploadResponse | undefined,
    validation: undefined as FileValidationResponse | undefined,
    metadata: undefined as FileMetadataResponse | undefined,
    signature: undefined as FileSignatureResponse | undefined,
    errors: [] as ApiError[],
  };

  // Step 1: Validate file if requested
  if (options.validateFirst) {
    const validationResult = await validateFile(file, options.maxFileSize);
    if ('type' in validationResult) {
      results?.errors?.push(validationResult);
      return results;
    }
    results.validation = validationResult;

    // Stop if validation failed
    if (
      !validationResult.success ||
      !validationResult.validation_result?.is_valid
    ) {
      return results;
    }
  }

  // Step 2: Extract metadata if requested
  if (options.extractMetadata) {
    const metadataResult = await extractFileMetadata(file);
    if ('type' in metadataResult) {
      results?.errors?.push(metadataResult);
    } else {
      results.metadata = metadataResult;
    }
  }

  // Step 3: Check signature if requested
  if (options.checkSignature) {
    const signatureResult = await checkFileSignature(file);
    if ('type' in signatureResult) {
      results?.errors?.push(signatureResult);
    } else {
      results.signature = signatureResult;
    }
  }

  // Step 4: Upload the file
  const uploadResult = await uploadFile(file);
  if ('type' in uploadResult) {
    results?.errors?.push(uploadResult);
  } else {
    results.upload = uploadResult;
  }

  return results;
};

// ============================================================================
// Onboarding Functions
// ============================================================================

export interface OnboardingResponse {
  success: boolean;
  job_id?: string;
  message: string;
  error?: string;
}

export interface OnboardingStatusResponse {
  job_id: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  progress?: number;
  current_step?: string;
  message?: string;
  error?: string;
  result?: Record<string, unknown>;
}

export interface ComprehensiveOnboardingRequest {
  files: Array<{
    uploadId: string;
    currency: string;
    fileName: string;
  }>;
}

export interface ComprehensiveOnboardingResponse extends OnboardingResponse {
  files_processed?: number;
}

export const isOnboardingError = (response: unknown): response is ApiError => {
  return (
    response !== null &&
    typeof response === 'object' &&
    'type' in response &&
    (response as { type: string }).type === 'error'
  );
};

export const startOnboarding = async (
  uploadId: string,
  currency: string,
): Promise<OnboardingResponse | ApiError> => {
  try {
    const response = await apiClient.post<OnboardingResponse>(
      '/files/onboarding/start',
      { upload_id: uploadId, currency },
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'startOnboarding',
      defaultMessage: 'Failed to start onboarding process.',
    });
  }
};

export const startComprehensiveOnboarding = async (
  request: ComprehensiveOnboardingRequest,
): Promise<ComprehensiveOnboardingResponse | ApiError> => {
  try {
    const response = await apiClient.post<ComprehensiveOnboardingResponse>(
      '/files/onboarding/comprehensive',
      request,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'startComprehensiveOnboarding',
      defaultMessage: 'Failed to start comprehensive onboarding.',
    });
  }
};

export const getOnboardingStatus = async (
  jobId: string,
): Promise<OnboardingStatusResponse | ApiError> => {
  try {
    const response = await apiClient.get<OnboardingStatusResponse>(
      `/files/onboarding/status/${jobId}`,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getOnboardingStatus',
      defaultMessage: 'Failed to get onboarding status.',
    });
  }
};

// ============================================================================
// Upload Status Functions
// ============================================================================

export interface UploadStatusResponse {
  upload_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  message: string;
  progress?: number;
  processed_rows?: number;
  total_rows?: number;
  error?: string;
  report_id?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Get the current status of a file upload/processing
 * @param uploadId - The upload ID to check status for
 * @returns Promise with upload status
 */
export const getUploadStatus = async (
  uploadId: string,
): Promise<UploadStatusResponse | ApiError> => {
  try {
    const response = await apiClient.get<UploadStatusResponse>(
      `/files/${uploadId}/status`,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getUploadStatus',
      defaultMessage: `Failed to get status for upload ${uploadId}.`,
    });
  }
};

/**
 * Poll upload status until it reaches a terminal state (completed/failed)
 * @param uploadId - The upload ID to poll
 * @param options - Polling options
 * @returns Promise with final upload status
 */
export const pollUploadStatus = async (
  uploadId: string,
  options: {
    maxAttempts?: number;
    intervalMs?: number;
    onProgress?: (status: UploadStatusResponse) => void;
  } = {},
): Promise<UploadStatusResponse | ApiError> => {
  const { maxAttempts = 60, intervalMs = 2000, onProgress } = options;
  let attempts = 0;

  while (attempts < maxAttempts) {
    const statusResult = await getUploadStatus(uploadId);

    if ('type' in statusResult && 'message' in statusResult) {
      // It's an ApiError
      return statusResult;
    }

    // Call progress callback if provided
    if (onProgress) {
      onProgress(statusResult);
    }

    // Check if we've reached a terminal state
    if (
      statusResult.status === 'completed' ||
      statusResult.status === 'failed'
    ) {
      return statusResult;
    }

    // Wait before next poll
    await new Promise((resolve) => setTimeout(resolve, intervalMs));
    attempts++;
  }

  // Timeout error
  return handleApiError(new Error('Upload status polling timeout'), {
    context: 'pollUploadStatus',
    defaultMessage: `Timeout waiting for upload ${uploadId} to complete.`,
  });
};

// ============================================================================
// Hierarchical Results Functions
// ============================================================================

export interface CategoryVisualization {
  id: number;
  name: string;
  path: string;
  level: number;
  transaction_count: number;
  total_amount: number;
  percentage: number;
  color: string | null;
  gl_code: string | null;
  children: CategoryVisualization[];
}

export interface HierarchicalResults {
  upload_id: string;
  total_transactions: number;
  categorized_transactions: number;
  uncategorized_transactions: number;
  accuracy_score: number;
  hierarchy_depth: number;
  total_categories_used: number;
  processing_time: number;
  
  // Hierarchical breakdown
  income_hierarchy: CategoryVisualization[];
  expense_hierarchy: CategoryVisualization[];
  
  // Summary statistics
  total_income: number;
  total_expenses: number;
  
  // Top categories by usage
  top_categories: Array<{
    name: string;
    path: string;
    usage_count: number;
    total_amount: number;
  }>;
  
  // Accuracy metrics
  high_confidence_count: number;
  medium_confidence_count: number;
  low_confidence_count: number;
  
  // Enhancement opportunities
  enhancement_suggestions: Array<{
    type: string;
    priority: string;
    message: string;
    potential_accuracy_gain: number;
  }>;
}

/**
 * Get hierarchical categorization results with visualization data
 * @param uploadId - The upload ID to get results for
 * @returns Promise with hierarchical results or API error
 */
export const getHierarchicalResults = async (
  uploadId: string,
): Promise<HierarchicalResults | ApiError> => {
  try {
    const response = await apiClient.get<HierarchicalResults>(
      `/categories/hierarchical-results/${uploadId}`,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getHierarchicalResults',
      defaultMessage: `Failed to get hierarchical results for upload ${uploadId}.`,
    });
  }
};

// ============================================================================
// Column Mapping Functions
// ============================================================================

export interface ProcessedFileResponse {
  message: string;
  records_processed: number;
  upload_id: string;
  categorization_job_id: string | null;
  errors: string[];
  report_id?: string | null; // Added for detailed processing reports
}

/**
 * Submit column mapping and process transactions
 * @param uploadId - The upload ID
 * @param mapping - Column mapping (file column -> transaction field)
 * @returns Promise with processing results
 */
export const submitColumnMapping = async (
  uploadId: string,
  mapping: Record<string, string | null>,
): Promise<ProcessedFileResponse | ApiError> => {
  try {
    // The API expects column names as keys and field names as values
    // but the frontend provides field names as keys and column names as values
    // So we need to invert the mapping
    const invertedMapping: Record<string, string> = {};

    Object.entries(mapping).forEach(([fieldName, columnName]) => {
      if (columnName) {
        invertedMapping[columnName] = fieldName;
      }
    });

    const response = await apiClient.post<ProcessedFileResponse>(
      `/uploads/${uploadId}/map`,
      { mapping: invertedMapping },
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'submitColumnMapping',
      defaultMessage: 'Failed to submit column mapping.',
    });
  }
};
