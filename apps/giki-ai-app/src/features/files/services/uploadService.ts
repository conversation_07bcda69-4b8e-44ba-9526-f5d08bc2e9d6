// apps/giki-ai-app/src/services/uploadService.ts
import { apiClient } from '@/shared/services/api/apiClient';
import type {
  UploadResponse,
  ProcessedFileResponse,
  JobStatus,
} from '@/shared/types/categorization';
import type { UploadRecord, RecentUpload } from '../types/upload';
import { handleApiError, ApiError } from '@/shared/utils/errorHandling';

// This was GetFileColumnsResponse in lib/api.ts, aliasing for clarity if needed or use directly
interface GetFileColumnsResponse {
  columns: string[];
}

// This was ColumnListResponse in lib/api.ts, often it's the same as GetFileColumnsResponse
// If it's truly different, define it, otherwise, GetFileColumnsResponse can be used.
// For now, assuming it's the same for simplicity based on typical usage.
// export interface ColumnListResponse extends GetFileColumnsResponse {}

// File upload for AI categorization (NEW transactions WITHOUT categories)
export const uploadFiles = async (
  files: File[],
  options?: {
    onProgress?: (progress: number) => void;
  },
): Promise<{
  uploads: {
    upload_id: string;
    filename: string;
    transaction_count?: number;
  }[];
  failed: number;
}> => {
  try {
    // Validate files
    for (const file of files) {
      const validation = validateFile(file);
      if (!validation.isValid) {
        throw new Error(`File ${file.name}: ${validation.error}`);
      }
    }

    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });

    // MultipleUploadResponse from backend
    interface MultipleUploadResponse {
      uploads: {
        upload_id: string;
        filename: string;
        content_type?: string;
        size?: number;
        status?: string;
        message?: string;
        headers?: string[];
        transaction_count?: number;
      }[];
      total_files: number;
      successful: number;
      failed: number;
      message: string;
    }

    const response = await apiClient.postFormData<MultipleUploadResponse>(
      '/files/upload',
      formData,
      {
        onUploadProgress: (() => {
          let lastProgressUpdate = 0;
          return (progressEvent: { loaded?: number; total?: number }) => {
            const now = Date.now();
            if (
              options?.onProgress &&
              progressEvent.total &&
              now - lastProgressUpdate > 100
            ) {
              const progress = Math.round(
                ((progressEvent.loaded || 0) * 100) /
                  (progressEvent.total || 1),
              );
              options.onProgress(progress);
              lastProgressUpdate = now;
            }
          };
        })(),
      },
    );

    // Return simplified format for compatibility
    return {
      uploads: response.data.uploads.map((upload) => ({
        upload_id: upload.upload_id,
        filename: upload.filename,
        transaction_count: upload.transaction_count || 0,
      })),
      failed: response.data.failed,
    };
  } catch (error) {
    console.error('Error in uploadFiles:', error);
    throw error;
  }
};

export const uploadFile = async (
  file: File,
  onProgress?: (progress: number) => void,
  _currency?: string,
): Promise<UploadResponse | ApiError> => {
  // Single file upload is just a case of multiple file upload with one file
  try {
    const result = await uploadFiles([file], {
      onProgress,
    });

    // Extract the first upload from the result
    if (result.uploads && result.uploads.length > 0) {
      // Convert the uploadFiles response format to UploadResponse format
      const upload = result.uploads[0];
      return {
        upload_id: upload.upload_id,
        filename: upload.filename,
        size: 0, // Size not provided in uploadFiles response
        content_type: '', // Content type not provided in uploadFiles response
        status: 'uploaded',
        message: 'File uploaded successfully',
        headers: [],
        transaction_count: upload.transaction_count,
      } as UploadResponse;
    }

    return handleApiError(new Error('No upload response received'), {
      context: 'uploadFile',
      defaultMessage: 'Failed to upload files - no response data.',
    });
  } catch (error) {
    return handleApiError(error as Error, {
      context: 'uploadFile',
      defaultMessage: 'Failed to upload files.',
    });
  }
};

export const getFileColumns = async (uploadId: string): Promise<string[]> => {
  try {
    const response = await apiClient.get<GetFileColumnsResponse>(
      `/uploads/${uploadId}/columns`,
    );
    return response?.data?.columns || [];
  } catch (error) {
    console.error('Error in getFileColumns:', error); // Keep console logs
    throw handleApiError(error, {
      showToast: false,
      context: 'getFileColumns',
      defaultMessage: 'Failed to get file columns',
    });
  }
};

// Schema interpretation types
export interface ColumnMapping {
  original_name: string;
  mapped_field: string;
  confidence: number;
  reasoning: string;
}

export interface SchemaInterpretationResponse {
  upload_id: string;
  filename: string;
  column_mappings: ColumnMapping[];
  overall_confidence: number;
  required_fields_mapped: Record<string, boolean>;
  interpretation_summary: string;
}

export const getSchemaInterpretation = async (
  uploadId: string,
): Promise<SchemaInterpretationResponse> => {
  try {
    const response = await apiClient.get<SchemaInterpretationResponse>(
      `/uploads/${uploadId}/schema-interpretation`,
    );
    return response.data;
  } catch (error) {
    console.error('Error in getSchemaInterpretation:', error);
    throw handleApiError(error, {
      showToast: false,
      context: 'getSchemaInterpretation',
      defaultMessage: 'Failed to get schema interpretation',
    });
  }
};

export const submitColumnMapping = async (
  uploadId: string,
  mapping: Record<string, string | null>,
): Promise<ProcessedFileResponse> => {
  try {
    // Use the working /map endpoint that creates transactions and updates onboarding status
    // This endpoint DOES persist data permanently and triggers category inference
    const response = await apiClient.post<ProcessedFileResponse>(
      `/uploads/${uploadId}/map`,
      { mapping },
    );
    return response.data;
  } catch (error) {
    console.error('Error in submitColumnMapping:', error); // Keep console logs
    throw handleApiError(error, {
      showToast: false,
      context: 'submitColumnMapping',
      defaultMessage: 'Failed to process file with column mapping',
    });
  }
};

export const getCategorizationJobStatus = async (
  jobId: string,
): Promise<JobStatus> => {
  try {
    const response = await apiClient.get<JobStatus>(
      `/categorization/jobs/${jobId}`,
    );
    return response.data;
  } catch (error) {
    console.error('Error in getCategorizationJobStatus:', error);
    throw handleApiError(error, {
      showToast: false,
      context: 'getCategorizationJobStatus',
      defaultMessage: 'Failed to get job status',
    });
  }
};

/**
 * Get upload status by ID
 */
export const getUploadStatus = async (
  uploadId: string,
): Promise<{ status: string; progress?: number } | ApiError> => {
  try {
    const response = await apiClient.get<{ status: string; progress?: number }>(
      `/files/${uploadId}/status`,
    );
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getUploadStatus',
      defaultMessage: 'Failed to get upload status.',
    });
  }
};

/**
 * Get file processing status with detailed progress
 */
export const getFileProcessingStatus = async (
  fileId: string,
): Promise<
  | {
      file_id: string;
      status: string;
      progress: number;
      current_step: string;
      transactions_processed: number;
      transactions_total: number;
      estimated_completion: string;
      categorization_results?: {
        categorized: number;
        high_confidence: number;
        needs_review: number;
      };
    }
  | ApiError
> => {
  try {
    const response = await apiClient.get<{
      file_id: string;
      status: string;
      progress: number;
      current_step: string;
      transactions_processed: number;
      transactions_total: number;
      estimated_completion: string;
      categorization_results?: {
        categorized: number;
        high_confidence: number;
        needs_review: number;
      };
    }>(`/files/${fileId}/processing-status`);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getFileProcessingStatus',
      defaultMessage: 'Failed to get file processing status.',
      showToast: false, // Let the UI handle the error display
    });
  }
};

/**
 * Get AI-interpreted schema for column mapping
 */
export const getFileSchema = async (
  fileId: string,
): Promise<
  | {
      file_id: string;
      headers: string[];
      detected_mapping: Record<string, string>;
      confidence_scores: Record<string, number>;
      sample_data: string[][];
      quality_score?: number;
    }
  | ApiError
> => {
  try {
    const response = await apiClient.get<{
      file_id: string;
      headers: string[];
      detected_mapping: Record<string, string>;
      confidence_scores: Record<string, number>;
      sample_data: string[][];
      quality_score?: number;
    }>(`/files/${fileId}/schema`);
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getFileSchema',
      defaultMessage: 'Failed to get file schema.',
      showToast: false, // Let the UI handle the error display
    });
  }
};

/**
 * Confirm column mapping and start processing
 */
export const confirmColumnMapping = async (
  fileId: string,
  mapping: Record<string, string>,
): Promise<
  | {
      processing_job_id: string;
      estimated_completion: string;
      webhook_url: string;
    }
  | ApiError
> => {
  try {
    const response = await apiClient.post<{
      processing_job_id: string;
      estimated_completion: string;
      webhook_url: string;
    }>(`/files/${fileId}/map`, {
      mapping,
    });
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'confirmColumnMapping',
      defaultMessage: 'Failed to confirm column mapping.',
      showToast: false, // Let the UI handle the error display
    });
  }
};

/**
 * Get all uploads for the current tenant
 */
export const getAllUploads = async (): Promise<UploadRecord[] | ApiError> => {
  try {
    const response = await apiClient.get<UploadRecord[]>('/uploads');
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getAllUploads',
      defaultMessage: 'Failed to get uploads.',
    });
  }
};

/**
 * Get upload history with pagination
 */
export const getUploadHistory = async (
  page: number = 1,
  perPage: number = 20,
): Promise<{ uploads: unknown[]; total: number; page: number } | ApiError> => {
  try {
    const response = await apiClient.get<{
      uploads: unknown[];
      total: number;
      page: number;
    }>('/uploads/history', {
      params: {
        page,
        per_page: perPage,
      },
    });
    return response.data;
  } catch (error) {
    return handleApiError(error, {
      context: 'getUploadHistory',
      defaultMessage: 'Failed to get upload history.',
    });
  }
};

/**
 * Validate file before upload
 */
export const validateFile = (
  file: File,
  supportedFormats: string[] = ['csv', 'xlsx', 'xls'],
  maxSizeBytes: number = 10 * 1024 * 1024, // 10MB default - align with backend limit
): { isValid: boolean; error?: string } => {
  // Check file size
  if (file.size > maxSizeBytes) {
    return {
      isValid: false,
      error: `File size exceeds ${Math.round(maxSizeBytes / (1024 * 1024))}MB limit`,
    };
  }

  // Check file extension
  const fileExtension = file?.name?.split('.').pop()?.toLowerCase();
  if (!fileExtension || !supportedFormats.includes(fileExtension)) {
    return {
      isValid: false,
      error: `Unsupported file format. Supported formats: ${supportedFormats.join(', ')}`,
    };
  }

  return { isValid: true };
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Convert UploadRecord from API to RecentUpload for frontend display
 */
export const convertUploadRecord = (record: UploadRecord): RecentUpload => {
  return {
    id: record.upload_id,
    filename: record.filename,
    uploadDate: record.created_at ? new Date(record.created_at) : new Date(),
    status:
      record.status === 'completed'
        ? 'completed'
        : record.status === 'processing'
          ? 'processing'
          : record.status === 'pending'
            ? 'pending'
            : 'error',
    recordCount: undefined, // Would need additional field from API
    errorMessage: record.status === 'error' ? record.message : undefined,
  };
};

/**
 * Upload file for onboarding flow with categorization labels
 */
export const uploadFileForOnboarding = async (
  file: File,
  currency: string,
  onProgress?: (progress: number) => void,
): Promise<UploadResponse | ApiError> => {
  try {
    // Validate file before upload
    const validation = validateFile(file);
    if (!validation.isValid) {
      return handleApiError(new Error(validation.error), {
        context: 'uploadFileForOnboarding',
        defaultMessage: validation.error || 'File validation failed',
      });
    }

    const formData = new FormData();
    formData.append('files', file); // Note: 'files' not 'file' for batch endpoint
    formData.append('year', new Date().getFullYear().toString());
    formData.append('has_category_labels', 'true'); // Onboarding always has labels
    if (currency) {
      formData.append('currency', currency);
    }

    const response = await apiClient.postFormData<{
      status: string;
      summary: {
        total_files_processed: number;
        successful_uploads: number;
        failed_uploads: number;
        total_transactions_imported: number;
        rag_corpus_built: boolean;
      };
      upload_results: Array<{
        filename: string;
        status: string;
        transactions_imported: number;
        report_id: string | null;
        column_mapping?: Record<string, unknown>;
        file_path?: string;
        error?: string;
      }>;
      message: string;
    }>('/onboarding/batch-upload-files', formData, {
      onUploadProgress: (() => {
        let lastProgressUpdate = 0;
        return (progressEvent: { loaded?: number; total?: number }) => {
          const now = Date.now();
          if (
            onProgress &&
            progressEvent.total &&
            now - lastProgressUpdate > 100
          ) {
            const progress = Math.round(
              ((progressEvent.loaded || 0) * 100) / (progressEvent.total || 1),
            );
            onProgress(progress);
            lastProgressUpdate = now;
          }
        };
      })(),
      // Removed timeout property as it's not supported by apiClient
    });

    // Extract the first upload result (since we're uploading single file)
    const uploadResult = response.data.upload_results[0];

    if (!uploadResult || uploadResult.status !== 'success') {
      const error = uploadResult?.error || 'Upload failed';
      return handleApiError(new Error(error), {
        context: 'uploadFileForOnboarding',
        defaultMessage: error,
      });
    }

    // Convert to UploadResponse format
    const uploadResponse: UploadResponse = {
      upload_id:
        typeof uploadResult.report_id === 'string'
          ? uploadResult.report_id
          : '',
      filename:
        typeof uploadResult.filename === 'string' ? uploadResult.filename : '',
      size: file.size,
      content_type: file.type,
      status: 'completed',
      transaction_count:
        typeof uploadResult.transactions_imported === 'number'
          ? uploadResult.transactions_imported
          : 0,
      column_mapping: uploadResult.column_mapping || {},
      report_id:
        typeof uploadResult.report_id === 'string'
          ? uploadResult.report_id
          : null,
    };

    return uploadResponse;
  } catch (error) {
    console.error('Error in uploadFileForOnboarding:', error);
    return handleApiError(error as Error, {
      context: 'uploadFileForOnboarding',
      defaultMessage: 'Failed to upload file for onboarding',
    });
  }
};
