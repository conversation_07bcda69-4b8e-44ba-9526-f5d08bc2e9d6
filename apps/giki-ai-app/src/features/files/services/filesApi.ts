/**
 * Files API Service
 * 
 * Handles all file-related API operations including upload, 
 * schema interpretation, processing status, and transaction retrieval.
 */

import { apiClient } from '@/shared/services/api/apiClient';

// Types
export interface FileUploadResponse {
  upload_id: string;
  filename: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  message?: string;
  transaction_count?: number;
  error?: string;
}

export interface MultipleUploadResponse {
  uploads: FileUploadResponse[];
  total_files: number;
  successful: number;
  failed: number;
  message?: string;
}

export interface SchemaInterpretation {
  upload_id: string;
  filename: string;
  column_mappings: ColumnMapping[];
  overall_confidence: number;
  required_fields_mapped: Record<string, boolean>;
  interpretation_summary: string;
  debit_credit_inference?: Record<string, any>;
  regional_variations?: any[];
}

export interface ColumnMapping {
  original_name: string;
  mapped_field: string;
  confidence: number;
  reasoning: string;
}

export interface UploadStatus {
  upload_id: string;
  filename: string;
  size: number;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  progress: number;
  current_step: 'pending' | 'schema_interpretation' | 'categorization' | 'finalizing' | 'completed' | 'failed';
  total_transactions: number;
  categorized_transactions: number;
  has_schema_interpretation: boolean;
  error_message?: string;
  created_at: string;
  updated_at: string;
  accuracy?: number;
  categories_found?: number;
}

export interface ProcessingResults {
  total_transactions: number;
  categorized: number;
  accuracy: number;
  processing_time: number;
  categories: string[];
}

export interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  category?: string;
  ai_category?: string;
  ai_confidence?: number;
  currency: string;
  type: 'debit' | 'credit';
}

export interface TransactionListResponse {
  transactions: Transaction[];
  total: number;
  page: number;
  limit: number;
}

/**
 * Upload one or more files for processing
 */
export async function uploadFiles(files: File[]): Promise<MultipleUploadResponse> {
  const formData = new FormData();
  
  // Add each file to the form data
  files.forEach(file => {
    formData.append('files', file);
  });

  const response = await apiClient.postFormData<MultipleUploadResponse>(
    '/files/upload',
    formData,
    {
      // Upload can take a while, especially for large files
      timeout: 300000, // 5 minutes
    }
  );

  return response.data;
}

/**
 * Get the schema interpretation for an uploaded file
 */
export async function getSchemaInterpretation(uploadId: string): Promise<SchemaInterpretation> {
  const response = await apiClient.get<SchemaInterpretation>(
    `/files/${uploadId}/schema`
  );
  return response.data;
}

/**
 * Get the processing status for a specific upload
 */
export async function getUploadStatus(uploadId: string): Promise<UploadStatus> {
  const response = await apiClient.get<UploadStatus>(
    `/files/${uploadId}/status`
  );
  return response.data;
}

/**
 * Poll upload status until completion or failure
 */
export async function pollUploadStatus(
  uploadId: string,
  onProgress?: (status: UploadStatus) => void,
  maxAttempts = 60,
  intervalMs = 2000
): Promise<UploadStatus> {
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      const status = await getUploadStatus(uploadId);
      
      if (onProgress) {
        onProgress(status);
      }
      
      // Check if processing is complete
      if (status.status === 'COMPLETED' || status.status === 'FAILED') {
        return status;
      }
      
      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, intervalMs));
      attempts++;
      
    } catch (error) {
      console.error('Error polling upload status:', error);
      
      // If we get a 404, the upload might not exist yet
      if ((error as any)?.statusCode === 404 && attempts < 5) {
        await new Promise(resolve => setTimeout(resolve, intervalMs));
        attempts++;
        continue;
      }
      
      throw error;
    }
  }
  
  throw new Error('Upload processing timeout - maximum attempts reached');
}

/**
 * Get transactions for an upload
 */
export async function getUploadTransactions(
  uploadId: string,
  page = 1,
  limit = 100
): Promise<TransactionListResponse> {
  const response = await apiClient.get<TransactionListResponse>(
    `/files/${uploadId}/transactions`,
    {
      params: { page, limit }
    }
  );
  return response.data;
}

/**
 * Confirm schema interpretation and start processing
 */
export async function confirmInterpretation(
  uploadId: string,
  columnMappings: ColumnMapping[]
): Promise<ProcessingResults> {
  const response = await apiClient.post<ProcessingResults>(
    `/files/${uploadId}/interpretation/confirm`,
    {
      column_mappings: columnMappings,
      interpretation_confirmed: true
    }
  );
  return response.data;
}

/**
 * Get all processing status for the current user
 */
export async function getAllProcessingStatus(): Promise<{
  total_uploads: number;
  uploads: Array<{
    id: string;
    filename: string;
    status: string;
    transaction_count: number;
    created_at: string;
  }>;
  summary: {
    processing: number;
    completed: number;
    failed: number;
  };
}> {
  const response = await apiClient.get('/files/processing-status');
  return response.data;
}

/**
 * Export transactions to CSV
 */
export async function exportTransactions(
  filters?: {
    start_date?: string;
    end_date?: string;
    category?: string;
    upload_id?: string;
  }
): Promise<Blob> {
  const response = await apiClient.get('/files/export-transactions', {
    params: filters,
    responseType: 'blob'
  });
  return response.data;
}