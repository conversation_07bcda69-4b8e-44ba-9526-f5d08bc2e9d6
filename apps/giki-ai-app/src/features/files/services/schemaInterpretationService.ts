import { ApiError, handleApiError } from '@/shared/utils/errorHandling';
import { apiClient } from '@/shared/services/api/apiClient';

export interface ColumnMapping {
  original_name: string;
  mapped_field: string;
  confidence: number;
  reasoning: string;
}

export interface SchemaInterpretationResponse {
  upload_id: string;
  filename: string;
  column_mappings: ColumnMapping[];
  overall_confidence: number;
  required_fields_mapped: Record<string, boolean>;
  interpretation_summary: string;
}

/**
 * Get AI-powered schema interpretation for an uploaded file
 */
export async function getSchemaInterpretation(
  uploadId: string,
): Promise<SchemaInterpretationResponse | ApiError> {
  try {
    const response = await apiClient.get<SchemaInterpretationResponse>(
      `/uploads/${uploadId}/schema-interpretation`,
    );

    return response.data;
  } catch (error) {
    console.error('Schema interpretation error:', error);

    // Use proper error handling instead of converting everything to NetworkError
    return handleApiError(error, {
      context: 'getSchemaInterpretation',
      defaultMessage: 'Failed to interpret file schema. Please try again.',
      showToast: false, // Let the caller handle error display
    });
  }
}
