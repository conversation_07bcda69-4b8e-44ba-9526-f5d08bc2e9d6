/**
 * System Settings & Administration Page
 *
 * Comprehensive system administration including:
 * - User management
 * - Tenant configuration
 * - System health monitoring
 * - Database operations
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Switch } from '@/shared/components/ui/switch';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/shared/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/shared/components/ui/alert';
import { Badge } from '@/shared/components/ui/badge';
import { Loading } from '@/shared/components/ui/loading';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog';
import {
  Settings,
  Users,
  Building,
  Database,
  Activity,
  Shield,
  AlertTriangle,
  Plus,
  RefreshCw,
  Monitor,
  Key,
  Palette,
  Tags,
} from 'lucide-react';
import { toast } from '@/shared/components/ui/use-toast';
import { apiClient } from '@/shared/services/api/apiClient';
import { handleApiError } from '@/shared/utils/errorHandling';

interface SystemHealth {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  services: {
    database: { status: string };
  };
  metrics: {
    total_tenants: number;
    active_tenants: number;
    total_users: number;
  };
}

interface TenantData {
  id: number;
  name: string;
  email: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  settings?: Record<string, unknown>;
}

interface UserData {
  id: number;
  email: string;
  tenant_id: number;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
}

interface UserSettings {
  theme: 'light' | 'dark' | 'system';
  notifications: boolean;
  language: string;
  timezone: string;
  currency: string;
}

const SettingsPage: React.FC = () => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [tenants, setTenants] = useState<TenantData[]>([]);
  const [users, setUsers] = useState<UserData[]>([]);
  const [userSettings, setUserSettings] = useState<UserSettings>({
    theme: 'system',
    notifications: true,
    language: 'en',
    timezone: 'UTC',
    currency: 'USD',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('preferences');
  const [newTenantDialog, setNewTenantDialog] = useState(false);
  const [newUserDialog, setNewUserDialog] = useState(false);

  // New tenant form state
  const [newTenant, setNewTenant] = useState({
    name: '',
    email: '',
    is_active: true,
  });

  // New user form state
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    tenant_id: 1,
    is_active: true,
    is_verified: false,
  });

  const loadSystemData = useCallback(async () => {
    setIsLoading(true);
    try {
      await Promise.all([loadSystemHealth(), loadTenants(), loadUsers()]);
    } catch (error) {
      console.error('Failed to load system data:', error);
      toast({
        title: 'Error loading data',
        description:
          'Failed to load system information. Some features may not be available.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  const loadUserSettings = useCallback(() => {
    // Load from localStorage or API
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
      setUserSettings(JSON.parse(savedSettings) as UserSettings);
    }
  }, []);

  // Load initial data
  useEffect(() => {
    void loadSystemData();
    void loadUserSettings();
  }, [loadSystemData, loadUserSettings]);

  const loadSystemHealth = async () => {
    try {
      const response = await apiClient.get<SystemHealth>(
        '/admin/system-health',
      );
      setSystemHealth(response.data);
    } catch (error) {
      console.error('Failed to load system health:', error);
    }
  };

  const loadTenants = async () => {
    try {
      const response = await apiClient.get<{ tenants: TenantData[] }>(
        '/admin/tenants',
      );
      setTenants(response?.data?.tenants || []);
    } catch (error) {
      console.error('Failed to load tenants:', error);
    }
  };

  const loadUsers = async () => {
    try {
      const response = await apiClient.get<{ users: UserData[] }>(
        '/admin/users',
      );
      setUsers(response?.data?.users || []);
    } catch (error) {
      console.error('Failed to load users:', error);
    }
  };

  const saveUserSettings = (settings: UserSettings) => {
    setIsSaving(true);
    try {
      // Save to localStorage and optionally to API
      localStorage.setItem('userSettings', JSON.stringify(settings));

      // Apply theme
      if (settings.theme !== 'system') {
        document?.documentElement?.classList.toggle(
          'dark',
          settings.theme === 'dark',
        );
      } else {
        // Use system preference
        const isDark = window.matchMedia(
          '(prefers-color-scheme: dark)',
        ).matches;
        document?.documentElement?.classList.toggle('dark', isDark);
      }

      setUserSettings(settings);

      toast({
        title: 'Settings saved',
        description: 'Your preferences have been updated successfully.',
      });
    } catch (error) {
      console.error('Failed to save user settings:', error);
      toast({
        title: 'Error saving settings',
        description: 'Failed to save your preferences. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const createTenant = async () => {
    if (!newTenant.name || !newTenant.email) {
      toast({
        title: 'Validation error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    setIsSaving(true);
    try {
      const response = await apiClient.post<TenantData>(
        '/admin/tenants',
        newTenant,
      );
      setTenants((prev) => [...prev, response.data]);
      setNewTenant({ name: '', email: '', is_active: true });
      setNewTenantDialog(false);

      toast({
        title: 'Tenant created',
        description: `Tenant "${response?.data?.name}" has been created successfully.`,
      });
    } catch (error) {
      const apiError = handleApiError(error, {
        context: 'createTenant',
        defaultMessage: 'Failed to create tenant.',
      });

      toast({
        title: 'Error creating tenant',
        description: apiError.message,
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const createUser = async () => {
    if (!newUser.email || !newUser.password) {
      toast({
        title: 'Validation error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    setIsSaving(true);
    try {
      const response = await apiClient.post<UserData>('/admin/users', newUser);
      setUsers((prev) => [...prev, response.data]);
      setNewUser({
        email: '',
        password: '',
        tenant_id: 1,
        is_active: true,
        is_verified: false,
      });
      setNewUserDialog(false);

      toast({
        title: 'User created',
        description: `User "${response?.data?.email}" has been created successfully.`,
      });
    } catch (error) {
      const apiError = handleApiError(error, {
        context: 'createUser',
        defaultMessage: 'Failed to create user.',
      });

      toast({
        title: 'Error creating user',
        description: apiError.message,
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const runDatabaseMigration = async () => {
    setIsSaving(true);
    try {
      await apiClient.post('/admin/run-migrations');

      toast({
        title: 'Migration started',
        description: 'Database migration has been started in the background.',
      });

      // Refresh system health after a short delay
      setTimeout(() => void loadSystemHealth(), 2000);
    } catch (error) {
      console.error('Database migration failed:', error);
      toast({
        title: 'Migration failed',
        description: 'Failed to run database migration. Please check logs.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getHealthStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return (
          <Badge
            variant="default"
            className="bg-success/10 text-success-foreground max-w-[150px] truncate"
          >
            Healthy
          </Badge>
        );
      case 'unhealthy':
        return (
          <Badge variant="destructive" className="max-w-[150px] truncate">
            Unhealthy
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary" className="max-w-[150px] truncate">
            Unknown
          </Badge>
        );
    }
  };

  return (
    <div className="page-container bg-white min-h-screen">
      {/* Professional Header - Enhanced Design */}
      <div className="section-spacing-md">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-semibold text-gray-700 mb-2">
              System Settings
            </h1>
            <p className="text-gray-500 text-base">
              Manage system configuration, users, and monitor health
            </p>
          </div>
          <Button
            onClick={() => void loadSystemData()}
            disabled={isLoading}
            variant="outline"
            className="border-brand-primary text-brand-primary hover:bg-green-50"
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Professional Settings Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="section-spacing-lg"
      >
        <TabsList className="grid w-full grid-cols-3 bg-white border border-gray-200">
          <TabsTrigger
            value="preferences"
            className="flex items-center component-gap-sm data-[state=active]:bg-brand-primary data-[state=active]:text-white"
          >
            <Settings className="h-4 w-4" />
            Preferences
          </TabsTrigger>
          <TabsTrigger
            value="management"
            className="flex items-center component-gap-sm data-[state=active]:bg-brand-primary data-[state=active]:text-white"
          >
            <Users className="h-4 w-4" />
            User Management
          </TabsTrigger>
          <TabsTrigger
            value="system"
            className="flex items-center component-gap-sm data-[state=active]:bg-brand-primary data-[state=active]:text-white"
          >
            <Monitor className="h-4 w-4" />
            System & Security
          </TabsTrigger>
        </TabsList>

        {/* Preferences */}
        <TabsContent value="preferences" className="section-spacing-lg">
          {/* Categories Management Card */}
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="bg-white border-b card-header">
              <CardTitle className="text-lg font-semibold flex items-center component-gap-sm text-gray-900">
                <Tags className="h-5 w-5 text-brand-primary" />
                Categories & GL Codes
              </CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Manage hierarchical categories and GL code mappings
              </CardDescription>
            </CardHeader>
            <CardContent className="card-content">
              <p className="text-sm text-muted-foreground section-spacing-sm">
                Configure parent-child category relationships and map them to GL
                codes for accounting integration.
              </p>
              <Button
                className="bg-brand-primary hover:bg-brand-primary-hover text-white"
                onClick={() => (window.location.href = '/categories')}
              >
                <Tags className="h-4 w-4 mr-2" />
                Manage Categories
              </Button>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="bg-white border-b card-header">
              <CardTitle className="text-lg font-semibold flex items-center component-gap-sm text-gray-900">
                <Palette className="h-5 w-5 text-brand-primary" />
                Appearance
              </CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Customize the appearance and behavior of the application
              </CardDescription>
            </CardHeader>
            <CardContent className="card-content section-spacing-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 grid-gap-md">
                <div className="space-y-2">
                  <Label htmlFor="theme">Theme</Label>
                  <Select
                    value={userSettings.theme}
                    onValueChange={(value) =>
                      saveUserSettings({
                        ...userSettings,
                        theme: value as UserSettings['theme'],
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select theme" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="system">System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <Select
                    value={userSettings.language}
                    onValueChange={(value) =>
                      saveUserSettings({
                        ...userSettings,
                        language: value,
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timezone">Timezone</Label>
                  <Input
                    id="timezone"
                    value={userSettings.timezone}
                    onChange={(e) =>
                      saveUserSettings({
                        ...userSettings,
                        timezone: e?.target?.value,
                      })
                    }
                    placeholder="UTC"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select
                    value={userSettings.currency}
                    onValueChange={(value) =>
                      saveUserSettings({
                        ...userSettings,
                        currency: value,
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                      <SelectItem value="CAD">CAD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex flex-wrap items-center component-gap-sm">
                <Switch
                  id="notifications"
                  checked={userSettings.notifications}
                  onCheckedChange={(checked) =>
                    saveUserSettings({
                      ...userSettings,
                      notifications: checked,
                    })
                  }
                />
                <Label htmlFor="notifications">Enable notifications</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Management - Combines Users and Tenants */}
        <TabsContent value="management" className="section-spacing-lg">
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="bg-white border-b card-header">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold flex items-center component-gap-sm text-gray-900">
                    <Users className="h-5 w-5 text-brand-primary" />
                    User Management
                  </CardTitle>
                  <CardDescription className="text-gray-600 text-sm">
                    Manage user accounts and permissions
                  </CardDescription>
                </div>
                <Dialog open={newUserDialog} onOpenChange={setNewUserDialog}>
                  <DialogTrigger asChild>
                    <Button className="bg-brand-primary text-white hover:bg-brand-primary-hover">
                      <Plus className="h-4 w-4 mr-2" />
                      Add User
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Create New User</DialogTitle>
                      <DialogDescription>
                        Add a new user to the system
                      </DialogDescription>
                    </DialogHeader>
                    <div className="section-spacing-md">
                      <div className="component-gap-sm">
                        <Label htmlFor="userEmail">Email</Label>
                        <Input
                          id="userEmail"
                          type="email"
                          value={newUser.email}
                          onChange={(e) =>
                            setNewUser({ ...newUser, email: e?.target?.value })
                          }
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div className="component-gap-sm">
                        <Label htmlFor="userPassword">Password</Label>
                        <Input
                          id="userPassword"
                          type="password"
                          value={newUser.password}
                          onChange={(e) =>
                            setNewUser({
                              ...newUser,
                              password: e?.target?.value,
                            })
                          }
                          placeholder="••••••••"
                        />
                      </div>
                      <div className="component-gap-sm">
                        <Label htmlFor="userTenant">Tenant ID</Label>
                        <Input
                          id="userTenant"
                          type="number"
                          value={newUser.tenant_id}
                          onChange={(e) =>
                            setNewUser({
                              ...newUser,
                              tenant_id: parseInt(e?.target?.value),
                            })
                          }
                        />
                      </div>
                      <div className="flex flex-wrap items-center component-gap-sm">
                        <Switch
                          id="userActive"
                          checked={newUser.is_active}
                          onCheckedChange={(checked) =>
                            setNewUser({ ...newUser, is_active: checked })
                          }
                        />
                        <Label htmlFor="userActive">Active</Label>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        className="max-w-full"
                        variant="outline"
                        onClick={() => setNewUserDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        className="max-w-full"
                        onClick={() => void createUser()}
                        disabled={isSaving}
                      >
                        {isSaving ? 'Creating...' : 'Create User'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent className="card-content">
              {isLoading ? (
                <Loading text="Loading users..." />
              ) : (
                <div className="section-spacing-md">
                  {users.map((user) => (
                    <div
                      key={user.id}
                      className="flex flex-wrap items-center justify-between card-content border rounded"
                    >
                      <div>
                        <p className="font-medium">{user.email}</p>
                        <p className="text-sm text-muted-foreground">
                          Tenant: {user.tenant_id} | Created:{' '}
                          {new Date(user.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex flex-wrap items-center component-gap-sm">
                        {user.is_active ? (
                          <Badge
                            variant="default"
                            className="max-w-[150px] truncate"
                          >
                            Active
                          </Badge>
                        ) : (
                          <Badge
                            variant="secondary"
                            className="max-w-[150px] truncate"
                          >
                            Inactive
                          </Badge>
                        )}
                        {user.is_verified && (
                          <Badge
                            variant="outline"
                            className="max-w-[150px] truncate"
                          >
                            Verified
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  {users.length === 0 && (
                    <p className="text-center text-muted-foreground page-container">
                      No users found. Create your first user to get started.
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tenants Management */}
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="bg-white border-b card-header">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold flex items-center component-gap-sm text-gray-900">
                    <Building className="h-5 w-5 text-brand-primary" />
                    Tenant Management
                  </CardTitle>
                  <CardDescription className="text-gray-600 text-sm">
                    Manage organizational tenants and their settings
                  </CardDescription>
                </div>
                <Dialog
                  open={newTenantDialog}
                  onOpenChange={setNewTenantDialog}
                >
                  <DialogTrigger asChild>
                    <Button className="bg-brand-primary text-white hover:bg-brand-primary-hover">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Tenant
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Create New Tenant</DialogTitle>
                      <DialogDescription>
                        Add a new organizational tenant
                      </DialogDescription>
                    </DialogHeader>
                    <div className="section-spacing-md">
                      <div className="component-gap-sm">
                        <Label htmlFor="tenantName">Name</Label>
                        <Input
                          id="tenantName"
                          value={newTenant.name}
                          onChange={(e) =>
                            setNewTenant({
                              ...newTenant,
                              name: e?.target?.value,
                            })
                          }
                          placeholder="Organization Name"
                        />
                      </div>
                      <div className="component-gap-sm">
                        <Label htmlFor="tenantEmail">Email</Label>
                        <Input
                          id="tenantEmail"
                          type="email"
                          value={newTenant.email}
                          onChange={(e) =>
                            setNewTenant({
                              ...newTenant,
                              email: e?.target?.value,
                            })
                          }
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div className="flex flex-wrap items-center component-gap-sm">
                        <Switch
                          id="tenantActive"
                          checked={newTenant.is_active}
                          onCheckedChange={(checked) =>
                            setNewTenant({ ...newTenant, is_active: checked })
                          }
                        />
                        <Label htmlFor="tenantActive">Active</Label>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        className="max-w-full"
                        variant="outline"
                        onClick={() => setNewTenantDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        className="max-w-full"
                        onClick={() => void createTenant()}
                        disabled={isSaving}
                      >
                        {isSaving ? 'Creating...' : 'Create Tenant'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent className="card-content">
              {isLoading ? (
                <Loading text="Loading tenants..." />
              ) : (
                <div className="section-spacing-md">
                  {tenants.map((tenant) => (
                    <div
                      key={tenant.id}
                      className="flex flex-wrap items-center justify-between card-content border rounded"
                    >
                      <div>
                        <p className="font-medium">{tenant.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {tenant.email} | Created:{' '}
                          {new Date(tenant.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex flex-wrap items-center component-gap-sm">
                        {tenant.is_active ? (
                          <Badge
                            variant="default"
                            className="max-w-[150px] truncate"
                          >
                            Active
                          </Badge>
                        ) : (
                          <Badge
                            variant="secondary"
                            className="max-w-[150px] truncate"
                          >
                            Inactive
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  {tenants.length === 0 && (
                    <p className="text-center text-muted-foreground page-container">
                      No tenants found. Create your first tenant to get started.
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* System & Security */}
        <TabsContent value="system" className="section-spacing-lg">
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="bg-white border-b card-header">
              <CardTitle className="text-lg font-semibold flex items-center component-gap-sm text-gray-900">
                <Activity className="h-5 w-5 text-brand-primary" />
                System Health
              </CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Monitor system status and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent className="card-content section-spacing-lg">
              {systemHealth ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 grid-gap-md">
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">
                        Overall Status
                      </p>
                      {getHealthStatusBadge(systemHealth.status)}
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">Database</p>
                      {getHealthStatusBadge(
                        systemHealth?.services?.database.status,
                      )}
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">
                        Total Tenants
                      </p>
                      <p className="text-2xl font-bold">
                        {systemHealth?.metrics?.total_tenants}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">
                        Total Users
                      </p>
                      <p className="text-2xl font-bold">
                        {systemHealth?.metrics?.total_users}
                      </p>
                    </div>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    Last updated:{' '}
                    {new Date(systemHealth.timestamp).toLocaleString()}
                  </div>
                </>
              ) : (
                <Loading text="Loading system health..." />
              )}
            </CardContent>
          </Card>

          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="bg-white border-b card-header">
              <CardTitle className="text-lg font-semibold flex items-center component-gap-sm text-gray-900">
                <Database className="h-5 w-5 text-brand-primary" />
                Database Operations
              </CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Manage database migrations and maintenance
              </CardDescription>
            </CardHeader>
            <CardContent className="card-content section-spacing-lg">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Warning</AlertTitle>
                <AlertDescription>
                  Database operations can affect system availability. Please
                  ensure you have proper backups before proceeding.
                </AlertDescription>
              </Alert>

              <Button
                className="bg-brand-primary hover:bg-brand-primary-hover text-white"
                onClick={() => void runDatabaseMigration()}
                disabled={isSaving}
              >
                <Database className="h-4 w-4 mr-2" />
                {isSaving ? 'Running...' : 'Run Database Migration'}
              </Button>
            </CardContent>
          </Card>

          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="bg-white border-b card-header">
              <CardTitle className="text-lg font-semibold flex items-center component-gap-sm text-gray-900">
                <Shield className="h-5 w-5 text-brand-primary" />
                Security Configuration
              </CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Manage security settings and access controls
              </CardDescription>
            </CardHeader>
            <CardContent className="card-content section-spacing-lg">
              <Alert>
                <Key className="h-4 w-4" />
                <AlertTitle>API Keys</AlertTitle>
                <AlertDescription>
                  API key management and authentication settings are configured
                  via environment variables for security.
                </AlertDescription>
              </Alert>

              <div className="section-spacing-md">
                <div className="flex flex-wrap items-center justify-between">
                  <div>
                    <p className="font-medium">Two-Factor Authentication</p>
                    <p className="text-sm text-muted-foreground">
                      Enable 2FA for enhanced security
                    </p>
                  </div>
                  <Switch disabled />
                </div>

                <div className="flex flex-wrap items-center justify-between">
                  <div>
                    <p className="font-medium">Session Timeout</p>
                    <p className="text-sm text-muted-foreground">
                      Automatic logout after inactivity
                    </p>
                  </div>
                  <Switch disabled />
                </div>

                <div className="flex flex-wrap items-center justify-between">
                  <div>
                    <p className="font-medium">Audit Logging</p>
                    <p className="text-sm text-muted-foreground">
                      Log administrative actions
                    </p>
                  </div>
                  <Switch disabled />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SettingsPage;
