/**
 * Help Hub Page
 * Documentation and support resources
 */
import React from 'react';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';

export const HelpHubPage: React.FC = () => {
  return (
    <div className="page-container section-spacing-lg bg-white min-h-screen">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Help Hub</h1>
          <p className="text-gray-600">Documentation and support resources</p>
        </div>
        <Button className="bg-brand-primary hover:bg-brand-primary-hover text-white">
          View Details
        </Button>
      </div>
      <Card className="card-content text-center">
        <CardContent>
          <div className="text-xl text-gray-600">Help Hub Dashboard</div>
          <div className="text-sm text-gray-500 section-spacing-sm">
            Professional help hub interface coming soon
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HelpHubPage;
