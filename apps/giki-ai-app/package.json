{"name": "giki-ai-app", "version": "1.0.0", "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:visual": "vitest run src/test/visual/", "test:visual:update": "vitest run src/test/visual/ --reporter=verbose && node -e \"console.log('Visual baselines updated')\"", "test:integration": "vitest run src/test/integration/", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "lint": "eslint . --fix", "lint:check": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "lucide-react": "^0.510.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "react-router-dom": "^6.28.0", "recharts": "^2.15.3", "serve": "^14.2.4", "tailwind-merge": "^3.3.0", "tslib": "^2.8.1", "uuid": "^11.1.0", "zod": "^3.25.1", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.27.0", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.5.0", "@vitest/ui": "3.1.4", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "jsdom": "^26.1.0", "msw": "^2.0.8", "playwright": "^1.40.0", "prettier": "^3.5.3", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.33.0", "vite": "^5.4.0", "vite-bundle-analyzer": "^0.23.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.1.4", "workbox-window": "^7.3.0"}}