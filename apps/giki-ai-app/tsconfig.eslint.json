{
  "extends": "./tsconfig.json",
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "tests/**/*.ts",
    "tests/**/*.tsx",
    "vite.config.ts",
    "vitest.config.ts",
    "tailwind.config.ts",
    "playwright.config.ts"
  ],
  "exclude": [
    "**/*.lock",
    "**/node_modules/**",
    "**/dist/**",
    "**/coverage/**",
    "**/.vite-cache/**",
    "**/test-results/**",
    "**/playwright-report/**",
    "**/vitest-report.json",
    "**/.DS_Store",
    "**/apps/**",
    "./index.d.ts",
    "./src/__mocks__/*.d.ts",
    "**/*.d.ts"
  ],
  "compilerOptions": {
    "noEmit": true // Important for ESLint-specific tsconfigs
  }
}
