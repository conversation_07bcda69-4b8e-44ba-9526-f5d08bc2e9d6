{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "allowJs": false, "types": ["vitest/globals", "vite/client"], "composite": true}, "files": ["../../node_modules/@nx/react/typings/cssmodule.d.ts", "../../node_modules/@nx/react/typings/image.d.ts"], "exclude": ["src/__mocks__/**/*", "**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "**/*.spec.js", "**/*.test.js", "**/*.spec.jsx", "**/*.test.jsx", "vitest.config.ts", "src/test-setup.ts"], "include": ["src/**/*.ts", "src/**/*.tsx"]}