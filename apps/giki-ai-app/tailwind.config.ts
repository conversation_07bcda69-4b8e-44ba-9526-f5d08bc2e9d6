import type { Config } from 'tailwindcss';

export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // Primary Brand Colors
        primary: {
          DEFAULT: 'hsl(var(--giki-primary))',
          foreground: 'hsl(var(--giki-primary-foreground))',
          hover: 'hsl(var(--giki-primary-hover))',
          active: 'hsl(var(--giki-primary-active))',
        },

        // Direct giki-primary colors for new components
        'giki-primary': {
          DEFAULT: '#295343',
          dark: '#295343',
          light: '#E8F5E8',
          foreground: '#FFFFFF',
        },

        // Brand Colors from Logo
        brand: {
          green: {
            DEFAULT: 'hsl(var(--giki-brand-green))',
            dark: 'hsl(var(--giki-brand-green-dark))',
            foreground: 'hsl(var(--giki-brand-green-foreground))',
          },
          blue: {
            DEFAULT: 'hsl(var(--giki-brand-blue))',
            dark: 'hsl(var(--giki-brand-blue-dark))',
            foreground: 'hsl(var(--giki-brand-blue-foreground))',
          },
          pink: {
            DEFAULT: 'hsl(var(--giki-brand-pink))',
            dark: 'hsl(var(--giki-brand-pink-dark))',
            foreground: 'hsl(var(--giki-brand-pink-foreground))',
          },
          purple: {
            DEFAULT: 'hsl(var(--giki-brand-purple))',
            dark: 'hsl(var(--giki-brand-purple-dark))',
            foreground: 'hsl(var(--giki-brand-purple-foreground))',
          },
        },

        // Semantic Colors
        success: {
          DEFAULT: 'hsl(var(--giki-success))',
          foreground: 'hsl(var(--giki-success-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--giki-destructive))',
          foreground: 'hsl(var(--giki-destructive-foreground))',
        },
        warning: {
          DEFAULT: 'hsl(var(--giki-warning))',
          foreground: 'hsl(var(--giki-warning-foreground))',
        },
        info: {
          DEFAULT: 'hsl(var(--giki-info))',
          foreground: 'hsl(var(--giki-info-foreground))',
        },

        // Layout Colors
        background: 'hsl(var(--giki-bg-primary))',
        foreground: 'hsl(var(--giki-text-primary))',
        card: {
          DEFAULT: 'hsl(var(--giki-card-bg))',
          foreground: 'hsl(var(--giki-text-primary))',
        },
        popover: {
          DEFAULT: 'hsl(var(--giki-card-bg))',
          foreground: 'hsl(var(--giki-text-primary))',
        },
        muted: {
          DEFAULT: 'hsl(var(--giki-bg-muted))',
          foreground: 'hsl(var(--giki-text-muted))',
        },
        accent: {
          DEFAULT: 'hsl(var(--giki-bg-accent))',
          foreground: 'hsl(var(--giki-text-primary))',
        },

        // Excel-Inspired Colors
        'excel-card': 'hsl(var(--giki-bg-secondary))',

        // Borders and Inputs
        border: 'hsl(var(--giki-border-primary))',
        input: 'hsl(var(--giki-input-border))',
        ring: 'hsl(var(--giki-border-focus))',

        // Legacy compatibility (mapped to new tokens)
        secondary: {
          DEFAULT: 'hsl(var(--giki-bg-secondary))',
          foreground: 'hsl(var(--giki-text-secondary))',
        },
      },

      // Typography Scale
      fontSize: {
        xs: 'var(--giki-text-xs)',
        sm: 'var(--giki-text-sm)',
        base: 'var(--giki-text-base)',
        lg: 'var(--giki-text-lg)',
        xl: 'var(--giki-text-xl)',
        '2xl': 'var(--giki-text-2xl)',
        '3xl': 'var(--giki-text-3xl)',
        '4xl': 'var(--giki-text-4xl)',
        '5xl': 'var(--giki-text-5xl)',
      },

      fontFamily: {
        sans: 'var(--giki-font-family-sans)',
        mono: 'var(--giki-font-family-mono)',
        display: 'var(--giki-font-family-display)',
        inter: [
          'Inter',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'sans-serif',
        ],
      },

      fontWeight: {
        light: 'var(--giki-font-weight-light)',
        normal: 'var(--giki-font-weight-normal)',
        medium: 'var(--giki-font-weight-medium)',
        semibold: 'var(--giki-font-weight-semibold)',
        bold: 'var(--giki-font-weight-bold)',
        extrabold: 'var(--giki-font-weight-extrabold)',
      },

      lineHeight: {
        tight: 'var(--giki-leading-tight)',
        normal: 'var(--giki-leading-normal)',
        relaxed: 'var(--giki-leading-relaxed)',
      },

      // Spacing Scale (Design System)
      spacing: {
        0: 'var(--giki-space-0)',
        1: 'var(--giki-space-1)',
        2: 'var(--giki-space-2)',
        3: 'var(--giki-space-3)',
        4: 'var(--giki-space-4)',
        5: 'var(--giki-space-5)',
        6: 'var(--giki-space-6)',
        8: 'var(--giki-space-8)',
        10: 'var(--giki-space-10)',
        12: 'var(--giki-space-12)',
        16: 'var(--giki-space-16)',
        20: 'var(--giki-space-20)',
        24: 'var(--giki-space-24)',
        // Excel-specific spacing
        'excel-cell': 'var(--giki-space-8)', // 32px
        'excel-row': 'var(--giki-space-6)', // 24px
        'excel-header': 'var(--giki-space-10)', // 40px
      },

      // Border Radius Scale (Design System)
      borderRadius: {
        none: 'var(--giki-radius-none)',
        sm: 'var(--giki-radius-sm)',
        md: 'var(--giki-radius-md)',
        lg: 'var(--giki-radius-lg)',
        xl: 'var(--giki-radius-xl)',
        '2xl': 'var(--giki-radius-2xl)',
        full: 'var(--giki-radius-full)',
        // Semantic aliases
        excel: 'var(--giki-radius-sm)',
        professional: 'var(--giki-radius-md)',
        modern: 'var(--giki-radius-lg)',
      },

      // Professional Shadows
      boxShadow: {
        'excel-card': 'var(--giki-card-shadow)',
        'excel-dropdown': 'var(--giki-shadow-lg)',
        sm: 'var(--giki-shadow-sm)',
        md: 'var(--giki-shadow-md)',
        lg: 'var(--giki-shadow-lg)',
        xl: 'var(--giki-shadow-xl)',
        '2xl': 'var(--giki-shadow-2xl)',
      },

      // Animations
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
} satisfies Config;
