/**
 * E2E Transaction Management Tests
 * End-to-end testing of transaction review, categorization, and bulk operations
 */

import { test, expect } from '@playwright/test';

// Helper function to authenticate
async function authenticateUser(page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'GikiTest2025Secure');
  await page.click('[data-testid="login-submit"]');
  await expect(page).toHaveURL('/dashboard');
}

test.describe('Transaction Management E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await authenticateUser(page);
    
    // Navigate to transactions page
    await page.click('a[href="/transactions"]');
    await expect(page).toHaveURL('/transactions');
  });

  test('should display transaction table with data', async ({ page }) => {
    // Verify transaction table loads
    await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();
    await expect(page.locator('text=Transaction Management')).toBeVisible();
    
    // Check table headers
    await expect(page.locator('th:has-text("Date")')).toBeVisible();
    await expect(page.locator('th:has-text("Description")')).toBeVisible();
    await expect(page.locator('th:has-text("Amount")')).toBeVisible();
    await expect(page.locator('th:has-text("Category")')).toBeVisible();
    await expect(page.locator('th:has-text("Status")')).toBeVisible();
    
    // Should have transaction rows
    await expect(page.locator('[data-testid="transaction-row"]')).toHaveCount(3, { timeout: 10000 });
  });

  test('should filter transactions by status', async ({ page }) => {
    // Use status filter
    await page.selectOption('[data-testid="status-filter"]', 'needs_review');
    
    // Should show only transactions needing review
    await expect(page.locator('[data-testid="transaction-row"]')).toHaveCount(1);
    await expect(page.locator('text=Unknown Vendor')).toBeVisible();
    
    // Clear filter
    await page.selectOption('[data-testid="status-filter"]', 'all');
    await expect(page.locator('[data-testid="transaction-row"]')).toHaveCount(3);
  });

  test('should search transactions', async ({ page }) => {
    // Search for specific transaction
    await page.fill('[data-testid="search-input"]', 'Coffee');
    
    // Should filter to matching transactions
    await expect(page.locator('text=Coffee Shop Payment')).toBeVisible();
    await expect(page.locator('text=Unknown Vendor')).not.toBeVisible();
    
    // Clear search
    await page.fill('[data-testid="search-input"]', '');
    await expect(page.locator('[data-testid="transaction-row"]')).toHaveCount(3);
  });

  test('should sort transactions by column', async ({ page }) => {
    // Click on Amount header to sort
    await page.click('th:has-text("Amount")');
    
    // Should sort by amount (ascending)
    const firstAmount = await page.locator('[data-testid="transaction-row"]:first-child [data-testid="amount-cell"]').textContent();
    expect(firstAmount).toContain('$45.50'); // Smallest amount
    
    // Click again to sort descending
    await page.click('th:has-text("Amount")');
    
    // Should sort by amount (descending)
    const firstAmountDesc = await page.locator('[data-testid="transaction-row"]:first-child [data-testid="amount-cell"]').textContent();
    expect(firstAmountDesc).toContain('$2,500.00'); // Largest amount
  });

  test('should edit individual transaction', async ({ page }) => {
    // Find and click edit button for first transaction
    await page.locator('[data-testid="transaction-row"]:first-child [data-testid="edit-button"]').click();
    
    // Should open edit modal
    await expect(page.locator('[data-testid="edit-transaction-modal"]')).toBeVisible();
    await expect(page.locator('text=Edit Transaction')).toBeVisible();
    
    // Edit description
    const descriptionInput = page.locator('[data-testid="description-input"]');
    await descriptionInput.clear();
    await descriptionInput.fill('Local Coffee Shop - Team Meeting');
    
    // Change category
    await page.selectOption('[data-testid="category-select"]', 'Professional Services/Consulting');
    
    // Add notes
    await page.fill('[data-testid="notes-input"]', 'Weekly team meeting expenses');
    
    // Save changes
    await page.click('[data-testid="save-button"]');
    
    // Modal should close and changes should be reflected
    await expect(page.locator('[data-testid="edit-transaction-modal"]')).not.toBeVisible();
    await expect(page.locator('text=Local Coffee Shop - Team Meeting')).toBeVisible();
    await expect(page.locator('text=Professional Services → Consulting')).toBeVisible();
  });

  test('should handle edit validation errors', async ({ page }) => {
    await page.locator('[data-testid="transaction-row"]:first-child [data-testid="edit-button"]').click();
    
    // Clear required field
    const descriptionInput = page.locator('[data-testid="description-input"]');
    await descriptionInput.clear();
    
    // Try to save
    await page.click('[data-testid="save-button"]');
    
    // Should show validation error
    await expect(page.locator('[data-testid="description-error"]')).toContainText('Description is required');
    
    // Modal should remain open
    await expect(page.locator('[data-testid="edit-transaction-modal"]')).toBeVisible();
  });

  test('should get AI suggestions for categorization', async ({ page }) => {
    // Edit a transaction that needs review
    await page.selectOption('[data-testid="status-filter"]', 'needs_review');
    await page.locator('[data-testid="edit-button"]').click();
    
    // Click AI suggestions button
    await page.click('[data-testid="ai-suggestions-button"]');
    
    // Should show AI suggestions
    await expect(page.locator('[data-testid="ai-suggestions"]')).toBeVisible();
    await expect(page.locator('text=AI Suggestions')).toBeVisible();
    
    // Should show suggested categories with confidence scores
    await expect(page.locator('[data-testid="suggestion-1"]')).toBeVisible();
    await expect(page.locator('text=89% confidence')).toBeVisible();
    
    // Accept first suggestion
    await page.click('[data-testid="accept-suggestion-1"]');
    
    // Category should be updated
    const categorySelect = page.locator('[data-testid="category-select"]');
    await expect(categorySelect).toHaveValue('Professional Services/Consulting');
    
    // Save changes
    await page.click('[data-testid="save-button"]');
    
    // Should reflect changes in table
    await expect(page.locator('text=Professional Services → Consulting')).toBeVisible();
  });

  test('should perform bulk operations', async ({ page }) => {
    // Select multiple transactions
    await page.check('[data-testid="transaction-row"]:nth-child(1) [data-testid="select-checkbox"]');
    await page.check('[data-testid="transaction-row"]:nth-child(2) [data-testid="select-checkbox"]');
    
    // Should show bulk actions
    await expect(page.locator('[data-testid="bulk-actions"]')).toBeVisible();
    await expect(page.locator('text=2 selected')).toBeVisible();
    
    // Open bulk actions menu
    await page.click('[data-testid="bulk-actions-button"]');
    
    // Should show bulk action options
    await expect(page.locator('[data-testid="bulk-approve"]')).toBeVisible();
    await expect(page.locator('[data-testid="bulk-categorize"]')).toBeVisible();
    await expect(page.locator('[data-testid="bulk-export"]')).toBeVisible();
    
    // Perform bulk approve
    await page.click('[data-testid="bulk-approve"]');
    
    // Should show confirmation
    await expect(page.locator('[data-testid="bulk-success"]')).toContainText('2 transactions approved');
    
    // Checkboxes should be cleared
    await expect(page.locator('[data-testid="bulk-actions"]')).not.toBeVisible();
  });

  test('should handle bulk categorization', async ({ page }) => {
    // Select transactions
    await page.check('[data-testid="select-all-checkbox"]');
    
    // Open bulk actions and select categorize
    await page.click('[data-testid="bulk-actions-button"]');
    await page.click('[data-testid="bulk-categorize"]');
    
    // Should open bulk categorization modal
    await expect(page.locator('[data-testid="bulk-categorize-modal"]')).toBeVisible();
    
    // Select category for all
    await page.selectOption('[data-testid="bulk-category-select"]', 'Business Expenses/General');
    
    // Apply to all selected
    await page.click('[data-testid="apply-bulk-category"]');
    
    // Should update all selected transactions
    await expect(page.locator('[data-testid="bulk-categorize-modal"]')).not.toBeVisible();
    await expect(page.locator('text=Business Expenses → General')).toHaveCount(3);
  });

  test('should export transactions', async ({ page }) => {
    // Click export button
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-button"]');
    
    // Should show export options
    await expect(page.locator('[data-testid="export-menu"]')).toBeVisible();
    
    // Select CSV export
    await page.click('[data-testid="export-csv"]');
    
    // Should trigger download
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toMatch(/transactions.*\.csv$/);
  });

  test('should handle pagination', async ({ page }) => {
    // Should show pagination controls
    await expect(page.locator('[data-testid="pagination"]')).toBeVisible();
    await expect(page.locator('text=Page 1 of')).toBeVisible();
    
    // Test page size change
    await page.selectOption('[data-testid="page-size-select"]', '10');
    
    // Should update display
    await expect(page.locator('text=Showing 1-3 of 3')).toBeVisible();
  });

  test('should show transaction details in sidebar', async ({ page }) => {
    // Click on a transaction row to show details
    await page.locator('[data-testid="transaction-row"]:first-child').click();
    
    // Should open transaction details sidebar
    await expect(page.locator('[data-testid="transaction-details-sidebar"]')).toBeVisible();
    
    // Should show transaction information
    await expect(page.locator('[data-testid="transaction-details"]')).toBeVisible();
    await expect(page.locator('[data-testid="transaction-amount"]')).toBeVisible();
    await expect(page.locator('[data-testid="transaction-date"]')).toBeVisible();
    
    // Should show categorization history if available
    await expect(page.locator('[data-testid="categorization-history"]')).toBeVisible();
    
    // Close sidebar
    await page.click('[data-testid="close-sidebar"]');
    await expect(page.locator('[data-testid="transaction-details-sidebar"]')).not.toBeVisible();
  });

  test('should handle real-time updates', async ({ page }) => {
    // Simulate real-time update by refreshing data
    await page.click('[data-testid="refresh-button"]');
    
    // Should show loading state briefly
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
    
    // Should reload data
    await expect(page.locator('[data-testid="transaction-table"]')).toBeVisible();
    await expect(page.locator('[data-testid="loading-spinner"]')).not.toBeVisible();
  });

  test('should filter by confidence level', async ({ page }) => {
    // Use confidence filter
    await page.selectOption('[data-testid="confidence-filter"]', 'low');
    
    // Should show only low confidence transactions
    await expect(page.locator('[data-testid="confidence-indicator"]:has-text("45%")')).toBeVisible();
    
    // Change to high confidence
    await page.selectOption('[data-testid="confidence-filter"]', 'high');
    
    // Should show only high confidence transactions
    await expect(page.locator('[data-testid="confidence-indicator"]:has-text("95%")')).toBeVisible();
  });

  test('should filter by date range', async ({ page }) => {
    // Open date range picker
    await page.click('[data-testid="date-range-button"]');
    
    // Should show date picker
    await expect(page.locator('[data-testid="date-range-picker"]')).toBeVisible();
    
    // Select date range
    await page.click('[data-testid="start-date"]');
    await page.click('text=15'); // Select day 15
    
    await page.click('[data-testid="end-date"]');
    await page.click('text=17'); // Select day 17
    
    // Apply filter
    await page.click('[data-testid="apply-date-filter"]');
    
    // Should filter transactions within date range
    await expect(page.locator('[data-testid="transaction-row"]')).toHaveCount(3);
  });

  test('should show transaction metrics summary', async ({ page }) => {
    // Should display transaction summary
    await expect(page.locator('[data-testid="transaction-summary"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-transactions"]')).toContainText('3 transactions');
    await expect(page.locator('[data-testid="categorized-count"]')).toContainText('2 categorized');
    await expect(page.locator('[data-testid="needs-review-count"]')).toContainText('1 needs review');
    
    // Should show accuracy metrics
    await expect(page.locator('[data-testid="average-confidence"]')).toBeVisible();
  });

  test('should handle keyboard navigation', async ({ page }) => {
    // Focus on first transaction
    await page.keyboard.press('Tab');
    await page.keyboard.press('ArrowDown');
    
    // Should highlight first transaction row
    await expect(page.locator('[data-testid="transaction-row"]:first-child')).toHaveClass(/focused|selected/);
    
    // Navigate down
    await page.keyboard.press('ArrowDown');
    await expect(page.locator('[data-testid="transaction-row"]:nth-child(2)')).toHaveClass(/focused|selected/);
    
    // Press Enter to edit
    await page.keyboard.press('Enter');
    await expect(page.locator('[data-testid="edit-transaction-modal"]')).toBeVisible();
    
    // Press Escape to close
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="edit-transaction-modal"]')).not.toBeVisible();
  });

  test('should support undo/redo for bulk operations', async ({ page }) => {
    // Perform bulk operation
    await page.check('[data-testid="select-all-checkbox"]');
    await page.click('[data-testid="bulk-actions-button"]');
    await page.click('[data-testid="bulk-approve"]');
    
    // Should show undo option
    await expect(page.locator('[data-testid="undo-notification"]')).toBeVisible();
    await expect(page.locator('[data-testid="undo-button"]')).toBeVisible();
    
    // Click undo
    await page.click('[data-testid="undo-button"]');
    
    // Should revert changes
    await expect(page.locator('[data-testid="undo-success"]')).toContainText('Changes undone');
  });

  test('should validate transaction edits before saving', async ({ page }) => {
    await page.locator('[data-testid="edit-button"]').first().click();
    
    // Enter invalid amount
    const amountInput = page.locator('[data-testid="amount-input"]');
    await amountInput.clear();
    await amountInput.fill('invalid-amount');
    
    // Try to save
    await page.click('[data-testid="save-button"]');
    
    // Should show validation error
    await expect(page.locator('[data-testid="amount-error"]')).toContainText('Please enter a valid amount');
    
    // Fix the amount
    await amountInput.clear();
    await amountInput.fill('123.45');
    
    // Should be able to save now
    await page.click('[data-testid="save-button"]');
    await expect(page.locator('[data-testid="edit-transaction-modal"]')).not.toBeVisible();
  });
});