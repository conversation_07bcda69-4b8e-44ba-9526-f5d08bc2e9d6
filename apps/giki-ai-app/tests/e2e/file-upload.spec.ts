/**
 * E2E File Upload Tests
 * End-to-end testing of complete file upload and processing workflows
 */

import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test data files
const TEST_FILES_DIR = path.join(__dirname, 'test-files');

// Sample CSV content for testing
const SAMPLE_CSV = `Date,Description,Amount,Category
2024-01-15,Coffee Shop,-45.50,
2024-01-16,Client Payment,2500.00,
2024-01-17,Office Supplies,-89.99,
2024-01-18,Software Subscription,-99.00,
2024-01-19,Consulting Revenue,3500.00,`;

const INVALID_CSV = `Invalid,Data,Format
This,is,not,a,valid,transaction,file`;

// Helper function to create test files
async function createTestFile(filename: string, content: string) {
  if (!fs.existsSync(TEST_FILES_DIR)) {
    fs.mkdirSync(TEST_FILES_DIR, { recursive: true });
  }
  
  const filePath = path.join(TEST_FILES_DIR, filename);
  fs.writeFileSync(filePath, content);
  return filePath;
}

// Helper function to authenticate
async function authenticateUser(page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'GikiTest2025Secure');
  await page.click('[data-testid="login-submit"]');
  await expect(page).toHaveURL('/dashboard');
}

test.describe('File Upload E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await authenticateUser(page);
  });

  test.afterAll(async () => {
    // Clean up test files
    if (fs.existsSync(TEST_FILES_DIR)) {
      fs.rmSync(TEST_FILES_DIR, { recursive: true, force: true });
    }
  });

  test('should complete full file upload and processing flow', async ({ page }) => {
    // Create test file
    const testFile = await createTestFile('transactions.csv', SAMPLE_CSV);
    
    // Navigate to upload page
    await page.click('a[href="/upload"]');
    await expect(page).toHaveURL('/upload');
    
    // Verify upload interface loads
    await expect(page.locator('[data-testid="upload-zone"]')).toBeVisible();
    await expect(page.locator('text=Upload your financial data')).toBeVisible();
    
    // Upload file
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(testFile);
    
    // Verify file is selected and preview shows
    await expect(page.locator('[data-testid="file-preview"]')).toBeVisible();
    await expect(page.locator('text=transactions.csv')).toBeVisible();
    await expect(page.locator('text=5 transactions detected')).toBeVisible();
    
    // Continue to column mapping
    await page.click('[data-testid="continue-button"]');
    
    // Verify column mapping page
    await expect(page.locator('[data-testid="column-mapping"]')).toBeVisible();
    await expect(page.locator('text=Map your columns')).toBeVisible();
    
    // Verify auto-detected mappings
    await expect(page.locator('[data-testid="date-mapping"]')).toHaveValue('Date');
    await expect(page.locator('[data-testid="description-mapping"]')).toHaveValue('Description');
    await expect(page.locator('[data-testid="amount-mapping"]')).toHaveValue('Amount');
    
    // Proceed to processing
    await page.click('[data-testid="process-button"]');
    
    // Verify processing page
    await expect(page.locator('[data-testid="processing-status"]')).toBeVisible();
    await expect(page.locator('text=Processing your file')).toBeVisible();
    
    // Wait for processing to complete
    await expect(page.locator('[data-testid="processing-complete"]')).toBeVisible({ timeout: 30000 });
    
    // Verify results
    await expect(page.locator('text=5 transactions processed')).toBeVisible();
    await expect(page.locator('[data-testid="categorization-results"]')).toBeVisible();
    
    // Check individual transactions are displayed
    await expect(page.locator('text=Coffee Shop')).toBeVisible();
    await expect(page.locator('text=Client Payment')).toBeVisible();
    await expect(page.locator('text=Office Supplies')).toBeVisible();
    
    // Verify action buttons are available
    await expect(page.locator('[data-testid="review-transactions"]')).toBeVisible();
    await expect(page.locator('[data-testid="export-results"]')).toBeVisible();
  });

  test('should handle drag and drop file upload', async ({ page }) => {
    const testFile = await createTestFile('drag-drop.csv', SAMPLE_CSV);
    
    await page.goto('/upload');
    
    // Get the upload zone
    const uploadZone = page.locator('[data-testid="upload-zone"]');
    
    // Simulate drag and drop
    const fileBuffer = fs.readFileSync(testFile);
    const dataTransfer = await page.evaluateHandle((data) => {
      const dt = new DataTransfer();
      const file = new File([new Uint8Array(data)], 'drag-drop.csv', { type: 'text/csv' });
      dt.items.add(file);
      return dt;
    }, Array.from(fileBuffer));
    
    // Trigger drop event
    await uploadZone.dispatchEvent('drop', { dataTransfer });
    
    // Verify file was uploaded
    await expect(page.locator('text=drag-drop.csv')).toBeVisible();
    await expect(page.locator('[data-testid="file-preview"]')).toBeVisible();
  });

  test('should validate file format and show errors', async ({ page }) => {
    // Create an invalid file (wrong format)
    const invalidFile = await createTestFile('invalid.txt', 'This is not a financial file');
    
    await page.goto('/upload');
    
    // Try to upload invalid file
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(invalidFile);
    
    // Should show error message
    await expect(page.locator('[data-testid="file-error"]')).toContainText('Invalid file format');
    await expect(page.locator('text=Please upload a CSV, Excel, or PDF file')).toBeVisible();
    
    // Continue button should be disabled
    await expect(page.locator('[data-testid="continue-button"]')).toBeDisabled();
  });

  test('should handle large file uploads with progress', async ({ page }) => {
    // Create a larger CSV file
    let largeCsvContent = 'Date,Description,Amount,Category\n';
    for (let i = 0; i < 1000; i++) {
      largeCsvContent += `2024-01-${(i % 28) + 1},Transaction ${i},-${(Math.random() * 1000).toFixed(2)},\n`;
    }
    
    const largeFile = await createTestFile('large-transactions.csv', largeCsvContent);
    
    await page.goto('/upload');
    
    // Upload large file
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(largeFile);
    
    // Should show upload progress
    await expect(page.locator('[data-testid="upload-progress"]')).toBeVisible();
    
    // Wait for upload to complete
    await expect(page.locator('[data-testid="file-preview"]')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=1000 transactions detected')).toBeVisible();
  });

  test('should support custom column mapping', async ({ page }) => {
    const testFile = await createTestFile('custom-columns.csv', 
      `Transaction Date,Vendor,Debit,Credit\n2024-01-15,Coffee Shop,45.50,\n2024-01-16,Client,0,2500.00`);
    
    await page.goto('/upload');
    
    // Upload file
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(testFile);
    
    await page.click('[data-testid="continue-button"]');
    
    // Customize column mappings
    await page.selectOption('[data-testid="date-mapping"]', 'Transaction Date');
    await page.selectOption('[data-testid="description-mapping"]', 'Vendor');
    await page.selectOption('[data-testid="amount-mapping"]', 'Debit');
    
    // Should show mapping preview
    await expect(page.locator('[data-testid="mapping-preview"]')).toBeVisible();
    
    // Proceed with custom mapping
    await page.click('[data-testid="process-button"]');
    
    // Should process successfully
    await expect(page.locator('[data-testid="processing-complete"]')).toBeVisible({ timeout: 30000 });
  });

  test('should handle processing errors gracefully', async ({ page }) => {
    const testFile = await createTestFile('error-test.csv', SAMPLE_CSV);
    
    // Intercept processing API to simulate error
    await page.route('/api/v1/uploads/*/process', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Processing failed: AI service unavailable' }),
      });
    });
    
    await page.goto('/upload');
    
    // Upload and process file
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(testFile);
    await page.click('[data-testid="continue-button"]');
    await page.click('[data-testid="process-button"]');
    
    // Should show error message
    await expect(page.locator('[data-testid="processing-error"]')).toContainText('Processing failed');
    await expect(page.locator('text=AI service unavailable')).toBeVisible();
    
    // Should show retry option
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
  });

  test('should validate file content and show warnings', async ({ page }) => {
    const problemFile = await createTestFile('problem.csv', INVALID_CSV);
    
    await page.goto('/upload');
    
    // Upload problematic file
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(problemFile);
    
    // Should show validation warnings
    await expect(page.locator('[data-testid="validation-warnings"]')).toBeVisible();
    await expect(page.locator('text=File format issues detected')).toBeVisible();
    
    // Should still allow proceeding with warnings
    await expect(page.locator('[data-testid="continue-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="continue-button"]')).not.toBeDisabled();
  });

  test('should preserve upload state during navigation', async ({ page }) => {
    const testFile = await createTestFile('state-test.csv', SAMPLE_CSV);
    
    await page.goto('/upload');
    
    // Upload file
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(testFile);
    
    // Navigate away and back
    await page.click('a[href="/dashboard"]');
    await page.click('a[href="/upload"]');
    
    // File should still be selected (if implemented with state persistence)
    // Note: This depends on implementation - some apps might clear state
    await expect(page.locator('[data-testid="upload-zone"]')).toBeVisible();
  });

  test('should support multiple file formats', async ({ page }) => {
    // Test different file extensions
    const csvFile = await createTestFile('test.csv', SAMPLE_CSV);
    
    await page.goto('/upload');
    
    // Upload CSV file
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(csvFile);
    
    await expect(page.locator('text=test.csv')).toBeVisible();
    await expect(page.locator('[data-testid="file-preview"]')).toBeVisible();
  });

  test('should show file size and transaction count', async ({ page }) => {
    const testFile = await createTestFile('info-test.csv', SAMPLE_CSV);
    const stats = fs.statSync(testFile);
    
    await page.goto('/upload');
    
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(testFile);
    
    // Should show file information
    await expect(page.locator('[data-testid="file-size"]')).toContainText(`${Math.round(stats.size / 1024)} KB`);
    await expect(page.locator('[data-testid="transaction-count"]')).toContainText('5 transactions');
  });

  test('should handle upload cancellation', async ({ page }) => {
    const testFile = await createTestFile('cancel-test.csv', SAMPLE_CSV);
    
    await page.goto('/upload');
    
    // Start upload
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(testFile);
    
    // Cancel upload if cancel button is available
    const cancelButton = page.locator('[data-testid="cancel-upload"]');
    if (await cancelButton.isVisible()) {
      await cancelButton.click();
      
      // Should return to initial state
      await expect(page.locator('[data-testid="upload-zone"]')).toBeVisible();
      await expect(page.locator('[data-testid="file-preview"]')).not.toBeVisible();
    }
  });

  test('should export processing results', async ({ page }) => {
    const testFile = await createTestFile('export-test.csv', SAMPLE_CSV);
    
    await page.goto('/upload');
    
    // Complete upload flow
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(testFile);
    await page.click('[data-testid="continue-button"]');
    await page.click('[data-testid="process-button"]');
    
    await expect(page.locator('[data-testid="processing-complete"]')).toBeVisible({ timeout: 30000 });
    
    // Test export functionality
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-results"]');
    
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toMatch(/results.*\.(csv|xlsx|pdf)$/);
  });

  test('should integrate with transaction review', async ({ page }) => {
    const testFile = await createTestFile('review-test.csv', SAMPLE_CSV);
    
    await page.goto('/upload');
    
    // Complete upload flow
    const fileInput = page.locator('[data-testid="file-input"]');
    await fileInput.setInputFiles(testFile);
    await page.click('[data-testid="continue-button"]');
    await page.click('[data-testid="process-button"]');
    
    await expect(page.locator('[data-testid="processing-complete"]')).toBeVisible({ timeout: 30000 });
    
    // Navigate to transaction review
    await page.click('[data-testid="review-transactions"]');
    
    // Should navigate to transactions page with uploaded data
    await expect(page).toHaveURL('/transactions');
    await expect(page.locator('text=Coffee Shop')).toBeVisible();
  });
});