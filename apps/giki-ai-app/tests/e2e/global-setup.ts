/**
 * Playwright Global Setup
 * Runs once before all tests to prepare testing environment
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test environment setup...');
  
  // Start development server is handled by webServer config
  // This setup handles test data and authentication preparation
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Wait for development server to be ready
    let retries = 60; // 60 seconds maximum wait
    let serverReady = false;
    
    while (retries > 0 && !serverReady) {
      try {
        await page.goto('http://localhost:4200/health', { waitUntil: 'networkidle' });
        serverReady = true;
        console.log('✅ Development server is ready');
      } catch (error) {
        retries--;
        if (retries === 0) {
          console.error('❌ Development server failed to start');
          throw error;
        }
        await page.waitForTimeout(1000);
      }
    }
    
    // Test API connectivity
    try {
      const response = await page.evaluate(async () => {
        return fetch('http://localhost:8000/api/v1/health');
      });
      
      if (response) {
        console.log('✅ API server is ready');
      }
    } catch (error) {
      console.warn('⚠️ API server may not be ready, tests might fail');
    }
    
    // Verify test user exists by attempting authentication
    try {
      await page.goto('http://localhost:4200/login');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'GikiTest2025Secure');
      await page.click('[data-testid="login-submit"]');
      
      // Check if login was successful
      const currentUrl = page.url();
      if (currentUrl.includes('/dashboard')) {
        console.log('✅ Test user authentication verified');
      } else {
        console.warn('⚠️ Test user authentication may be failing');
      }
    } catch (error) {
      console.warn('⚠️ Could not verify test user authentication');
    }
    
    console.log('✅ Global setup completed successfully');
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await page.close();
    await context.close();
    await browser.close();
  }
}

export default globalSetup;