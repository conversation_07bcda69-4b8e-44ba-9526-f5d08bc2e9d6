/**
 * E2E Smoke Tests
 * Basic connectivity and functionality tests to verify the application is working
 */

import { test, expect } from '@playwright/test';

test.describe('Smoke Tests', () => {
  test('should load the application home page', async ({ page }) => {
    await page.goto('/');
    
    // Should redirect to login or show some content
    await expect(page).toHaveURL(/\/(login|dashboard|$)/);
    
    // Page should have loaded content
    await expect(page.locator('body')).toBeVisible();
  });
  
  test('should load login page', async ({ page }) => {
    await page.goto('/login');
    
    // Should be on login page
    await expect(page).toHaveURL('/login');
    
    // Should have basic login elements
    await expect(page.locator('input[type="email"], [data-testid*="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"], [data-testid*="password"]')).toBeVisible();
  });
  
  test('should have working API health endpoint', async ({ page }) => {
    // Check API health
    const response = await page.request.get('http://localhost:8000/api/v1/health');
    expect(response.status()).toBe(200);
  });
  
  test('should have responsive layout', async ({ page }) => {
    await page.goto('/login');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 812 });
    await expect(page.locator('body')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('body')).toBeVisible();
  });
});