/**
 * Playwright Global Teardown
 * Runs once after all tests to clean up testing environment
 */

import { FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test environment cleanup...');
  
  try {
    // Clean up test artifacts
    const testResultsDir = path.join(process.cwd(), 'test-results');
    const downloadsDir = path.join(process.cwd(), 'test-downloads');
    
    // Clean up any temporary test files
    const tempDirs = [
      path.join(__dirname, 'test-files'),
      testResultsDir,
      downloadsDir,
    ];
    
    for (const dir of tempDirs) {
      if (fs.existsSync(dir)) {
        try {
          fs.rmSync(dir, { recursive: true, force: true });
          console.log(`✅ Cleaned up ${dir}`);
        } catch (error) {
          console.warn(`⚠️ Could not clean up ${dir}:`, error);
        }
      }
    }
    
    // Generate test summary if available
    const summaryFile = path.join(testResultsDir, 'e2e-results.json');
    if (fs.existsSync(summaryFile)) {
      try {
        const results = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
        console.log('\n📊 E2E Test Summary:');
        console.log(`   Total Tests: ${results.stats?.total || 'Unknown'}`);
        console.log(`   Passed: ${results.stats?.passed || 0}`);
        console.log(`   Failed: ${results.stats?.failed || 0}`);
        console.log(`   Skipped: ${results.stats?.skipped || 0}`);
        console.log(`   Duration: ${results.stats?.duration || 'Unknown'}ms`);
      } catch (error) {
        console.warn('⚠️ Could not parse test results');
      }
    }
    
    console.log('✅ Global teardown completed successfully');
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw - teardown failures shouldn't fail the test run
  }
}

export default globalTeardown;