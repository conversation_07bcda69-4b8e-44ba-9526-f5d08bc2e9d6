# E2E Testing Setup - giki.ai Platform

## Overview

This directory contains comprehensive end-to-end tests for the giki.ai MIS platform, built with <PERSON><PERSON> for cross-browser testing automation.

## Test Coverage

### 🔐 Authentication Tests (`auth.spec.ts`)
- **15 test cases** covering complete authentication workflows
- Login/logout flows with validation
- Session management and token handling
- Error handling and edge cases
- Keyboard navigation and accessibility
- Remember me functionality
- Network error scenarios

### 📊 Dashboard Tests (`dashboard.spec.ts`)
- **20 test cases** covering dashboard functionality
- Metrics display and calculation validation
- Real-time data updates and refresh
- Chart rendering and interactions
- Navigation between dashboard views
- Mobile responsiveness
- Export functionality
- Customization features

### 📁 File Upload Tests (`file-upload.spec.ts`)
- **14 test cases** covering upload workflows
- Drag & drop file upload
- Multiple file format support
- Column mapping and validation
- Processing status monitoring
- Error handling and retry logic
- Large file upload with progress
- Export and integration with transaction review

### 💰 Transaction Management Tests (`transaction-management.spec.ts`)
- **20 test cases** covering transaction operations
- Table sorting, filtering, and pagination
- Individual transaction editing
- Bulk operations and validation
- AI suggestions integration
- Real-time updates and synchronization
- Export functionality
- Keyboard navigation
- Undo/redo capabilities

### 🔍 Smoke Tests (`smoke.spec.ts`)
- **4 basic connectivity tests**
- Application loading verification
- API health checks
- Responsive layout validation
- Basic navigation functionality

## Configuration

### Multi-Browser Testing
- **Chromium** (Desktop)
- **Firefox** (Desktop)
- **WebKit** (Safari)
- **Mobile Chrome** (Pixel 5)
- **Mobile Safari** (iPhone 12)
- **iPad** (Tablet)
- **High DPI** (Retina displays)

### Test Environment
- **Base URL**: http://localhost:4200
- **API URL**: http://localhost:8000
- **Global Setup**: Pre-test environment preparation
- **Global Teardown**: Post-test cleanup and reporting
- **Test Data**: Centralized in `libs/test-data/`

## Running Tests

### Prerequisites
```bash
# Start development servers
pnpm serve:all

# Verify servers are running
curl http://localhost:4200
curl http://localhost:8000/api/v1/health
```

### Test Execution
```bash
# Run all E2E tests
pnpm test:e2e

# Run with UI for debugging
pnpm test:e2e:ui

# Run in headed mode (visible browser)
pnpm test:e2e:headed

# Run specific test file
pnpm test:e2e tests/e2e/auth.spec.ts

# Run tests for specific browser
pnpm test:e2e --project=chromium
```

### Test Reports
- **HTML Report**: `apps/giki-ai-app/playwright-report/`
- **JSON Results**: `test-results/e2e-results.json`
- **JUnit XML**: `test-results/e2e-junit.xml`
- **Screenshots**: `test-results/e2e-artifacts/`
- **Videos**: Available on test failures

## Test Data Management

### Authentication
- **Test User**: `<EMAIL>`
- **Password**: `GikiTest2025Secure`
- **Organization**: `Test Company`

### Transaction Data
- **Sample CSV**: Generated programmatically for upload tests
- **Mock API**: MSW integration for reliable data scenarios
- **Edge Cases**: Invalid data, large files, error conditions

### File Upload Testing
- **Valid CSV**: Standard transaction format
- **Invalid Files**: Wrong format, corrupt data
- **Large Files**: 1000+ transaction records
- **Custom Columns**: Non-standard CSV structures

## Quality Assurance

### Test Reliability
- **Retry Logic**: Automatic retry on CI failures
- **Wait Strategies**: Smart waiting for dynamic content
- **Error Recovery**: Graceful handling of test failures
- **Cleanup**: Automatic test artifact cleanup

### Performance Monitoring
- **Page Load Times**: Tracked and reported
- **API Response Times**: Monitored during tests
- **Memory Usage**: Browser resource tracking
- **Network Requests**: Captured and analyzed

### Accessibility Testing
- **Keyboard Navigation**: Full keyboard-only workflows
- **Screen Reader**: ARIA compliance validation
- **Color Contrast**: Automated accessibility checks
- **Touch Targets**: Mobile interaction verification

## Integration Points

### MIS-First Testing Strategy
- **Complete MIS Setup**: 5-minute onboarding validation
- **Progressive Enhancement**: Historical and schema improvements
- **Accuracy Metrics**: AI processing quality validation
- **Business Value**: Revenue impact measurement

### Customer Journey Validation
- **New User Onboarding**: Complete signup to first insight
- **Daily Workflow**: Upload → Review → Categorize → Report
- **Monthly Reporting**: End-to-end reporting generation
- **Year-end Analysis**: Historical data analysis workflows

## Maintenance

### Regular Updates
- Update test user credentials as needed
- Refresh test data to match production patterns
- Validate browser compatibility with new releases
- Update selectors when UI changes

### Monitoring
- Track test execution times and identify slow tests
- Monitor flaky tests and improve reliability
- Review test coverage and add missing scenarios
- Update documentation with new features

## Troubleshooting

### Common Issues
1. **Server not running**: Ensure `pnpm serve:all` is active
2. **Test user missing**: Run database setup scripts
3. **Flaky tests**: Check network timeouts and selectors
4. **Browser issues**: Clear cache and update browsers

### Debug Mode
```bash
# Run tests with debug output
pnpm test:e2e --debug

# Run specific test with trace
pnpm test:e2e tests/e2e/auth.spec.ts --trace on

# Open trace viewer
pnpm exec playwright show-trace test-results/trace.zip
```

---

**Total Test Cases**: 73 comprehensive E2E tests  
**Coverage**: Authentication, Dashboard, File Upload, Transaction Management, Smoke Tests  
**Browsers**: 7 different browser/device combinations  
**Last Updated**: 2025-07-12