<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Luckysheet Loader</title>
    <!-- Import Luckysheet CSS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/luckysheet/dist/plugins/css/pluginsCss.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/luckysheet/dist/plugins/plugins.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/luckysheet/dist/css/luckysheet.css"
    />
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
        height: 100%;
        width: 100%;
        overflow: hidden; /* Prevent scrollbars on body */
      }
      #luckysheet-container {
        margin: 0;
        padding: 0;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
      }
      /* --- CSS Override for Toolbar Text & Icons --- */
      /* Target text elements */
      #luckysheet-toolbar-container
        .luckysheet-toolbar-button
        .luckysheet-toolbar-button-text,
      #luckysheet-toolbar-container
        .luckysheet-toolbar-select
        .luckysheet-toolbar-button-text,
      #luckysheet-toolbar-container
        .luckysheet-toolbar-select
        .luckysheet-toolbar-select-text,
      #luckysheet-toolbar-container
        .luckysheet-toolbar-zoom-combobox
        .luckysheet-toolbar-select-text,
      #luckysheet-toolbar-container
        .luckysheet-toolbar-more-btn
        .luckysheet-toolbar-button-text {
        color: #333 !important;
      }

      /* Target the ::before pseudo-element of the icon container directly */
      #luckysheet_info_detail_save .luckysheet-icon-img-container.iconfont::before, /* Save icon */
        #luckysheet-toolbar-container .luckysheet-toolbar-button .luckysheet-icon-img-container.iconfont::before, /* General toolbar icons */
        .luckysheet-cols-menu .luckysheet-cols-menuitem .luckysheet-icon-img-container.iconfont::before /* Dropdown icons */ {
        color: #333 !important; /* Set the icon font color */
        opacity: 1 !important; /* Ensure it's not transparent */
        /* background-image: none !important; /* Remove potential background images hiding the font */
      }

      /* Ensure dropdown text is also visible */
      .luckysheet-cols-menu
        .luckysheet-cols-menuitem
        .luckysheet-cols-menuitem-content {
        color: #333 !important;
      }
      /* --- End CSS Override --- */
    </style>
  </head>
  <body>
    <div id="luckysheet-container"></div>

    <!-- Import Luckysheet JS -->
    <script src="https://cdn.jsdelivr.net/npm/luckysheet/dist/plugins/js/plugin.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luckysheet/dist/luckysheet.umd.js"></script>

    <script>
      // Listen for messages from the parent window (React component)
      window.addEventListener('message', function (event) {
        // Optional: Add origin check for security
        // if (event.origin !== 'YOUR_REACT_APP_ORIGIN') return;

        if (event.data && event.data.type === 'loadData') {
         ('iframe received loadData:', event.data.payload);
          const data = event.data.payload;

          // Ensure data is in the expected format (array of sheet objects)
          if (!Array.isArray(data) || data.length === 0) {
            console.error('Invalid data format received in iframe:', data);
            const container = document.getElementById('luckysheet-container');
            if (container) {
              container.innerHTML =
                '<p style="color: orange; padding: 10px;">Received invalid data format.</p>';
            }
            return;
          }

          // Destroy existing instance if it exists
          if (window.luckysheet) {
            try {
              // Check if destroy method exists before calling
              if (typeof luckysheet.destroy === 'function') {
                luckysheet.destroy();
               ('Previous Luckysheet instance destroyed.');
              } else {
                console.warn(
                  'luckysheet.destroy is not a function. Cannot destroy previous instance.'
                );
                // Attempt to clear the container manually as a fallback
                const container = document.getElementById(
                  'luckysheet-container'
                );
                if (container) container.innerHTML = '';
              }
            } catch (destroyError) {
              console.error(
                'Error destroying previous Luckysheet instance:',
                destroyError
              );
              // Attempt to clear the container manually if destroy fails
              const container = document.getElementById('luckysheet-container');
              if (container) container.innerHTML = '';
            }
          } else {
           ('No existing luckysheet instance found to destroy.');
          }

          // Create new instance with received data
          const options = {
            container: 'luckysheet-container',
            lang: 'en',
            data: data, // Use the received data
            showtoolbar: true,
            showinfobar: true,
            showsheetbar: true,
            showstatisticbar: true,
            sheetFormulaBar: true,
            allowEdit: true, // Or false based on requirements
            enableAddRow: true,
            enableAddCol: true,
            // Add any other necessary options
          };
          try {
            luckysheet.create(options);
           ('New Luckysheet instance created with received data.');
          } catch (error) {
            console.error(
              'Failed to create Luckysheet instance with received data:',
              error
            );
            const container = document.getElementById('luckysheet-container');
            if (container) {
              container.innerHTML =
                '<p style="color: red; padding: 10px;">Error loading data into Luckysheet. Check console.</p>';
            }
          }
        }
      });

      document.addEventListener('DOMContentLoaded', function () {
       (
          'DOM Loaded, initializing placeholder Luckysheet (will be replaced on data load)'
        );
        // Initial placeholder options - this will be replaced when data is received
        const options = {
          container: 'luckysheet-container',
          lang: 'en',
          data: [
            {
              name: 'Sheet1',
              celldata: [],
              order: 0,
            },
          ],
          showtoolbar: true,
          showinfobar: true,
          showsheetbar: true,
          showstatisticbar: true,
          sheetFormulaBar: true,
          allowEdit: true,
          enableAddRow: true,
          enableAddCol: true,
        };
        try {
          luckysheet.create(options);
        } catch (error) {
          console.error(
            'Failed to create Luckysheet instance in iframe:',
            error
          );
          const container = document.getElementById('luckysheet-container');
          if (container) {
            container.innerHTML =
              '<p style="color: red; padding: 10px;">Error loading Luckysheet. Check console.</p>';
          }
        }
      });
    </script>
  </body>
</html>
