import pandas as pd
import random
import os
from datetime import datetime

# Configuration
OUTPUT_DIR = "apps/giki-ai-app/test-data/transactions"
START_YEAR = 2024
START_MONTH = 7  # July
END_MONTH = 12   # December
MONTHS = [(START_YEAR, month) for month in range(START_MONTH, END_MONTH + 1)]

CATEGORIES = [
    "Software & Subscriptions", "Travel & Commute", "Groceries & Household",
    "Utilities & Bills", "Dining & Entertainment", "Healthcare & Wellness",
    "Shopping & Retail", "Education & Learning", "Financial Services", "Miscellaneous"
]

DESCRIPTION_TEMPLATES = {
    "Software & Subscriptions": [
        "Adobe Creative Cloud Subscription", "Microsoft Office 365 Renewal", "Zoom Pro Monthly Fee",
        "AWS Services Bill {} Plan", "Netflix Premium Plan", "Spotify Subscription"
    ],
    "Travel & Commute": [
        "Flight to {} Indigo {}", "Uber ride to {}", "Ola Cab fare - {}", "Monthly Metro Pass {}",
        "Train Ticket - {} Express", "Bus Fare - {} Route"
    ],
    "Groceries & Household": [
        "BigBasket Online Groceries Order #{}", "Reliance Fresh Purchase {}", "DMart Weekly Shopping Bill {}",
        "Local Kirana Store Purchase", "Swiggy Instamart Order {}"
    ],
    "Utilities & Bills": [
        "Electricity Bill Payment - {}", "BSNL Broadband Bill {}", "Water Bill - {} Municipal Corp",
        "Gas Cylinder Booking {}", "Mobile Recharge - {}"
    ],
    "Dining & Entertainment": [
        "Zomato Order - {}", "Swiggy - {} Restaurant", "Dinner at {} Restaurant", "Movie Tickets - PVR {}",
        "Coffee Shop - {} Cafe", "Lunch with colleagues at {}"
    ],
    "Healthcare & Wellness": [
        "Apollo Pharmacy Bill {}", "Doctor Consultation Fee - Dr. {}", "MedPlus Medicines Order {}",
        "Gym Membership - {} Fitness", "Yoga Class Fee"
    ],
    "Shopping & Retail": [
        "Amazon Order #{} - {}", "Flipkart Order #{} - {}", "Myntra Purchase - {}",
        "Lifestyle Store Shopping", "Croma Electronics Purchase {}"
    ],
    "Education & Learning": [
        "Coursera Course Fee - {}", "Udemy - {} Bootcamp", "Byju's Subscription Renewal",
        "Book Purchase - {} Store", "Skillshare Membership"
    ],
    "Financial Services": [
        "HDFC Credit Card Payment", "ICICI Bank Service Charge", "Mutual Fund SIP - {}",
        "Stock Purchase - {}", "Loan EMI Payment - {}"
    ],
    "Miscellaneous": [ # For E2E-AGENT-002
        "Online Payment Ref: {}", "UPI Transfer to {}", "Merchant Purchase ID: {}",
        "Service Fee {}", "Miscellaneous Expense {}", "Refund Received {}"
    ]
}

RANDOM_WORDS_FOR_DESCRIPTIONS = [
    "Mumbai", "Delhi", "Bangalore", "Airport", "Office", "Home", "Monthly", "Annual", "Weekly",
    "INV001", "TRN789", "BILL0042", "REF990", "Essentials", "Electronics", "Clothing", "Food",
    "Consultation", "Services", "Payment", "Purchase", "Order", "Booking", "Renewal", "Fee"
]

def generate_random_date(year, month):
    """Generates a random date within the given year and month."""
    if month == 2 and year % 4 == 0 and (year % 100 != 0 or year % 400 == 0): # Leap year
        last_day = 29
    elif month == 2:
        last_day = 28
    elif month in [4, 6, 9, 11]:
        last_day = 30
    else:
        last_day = 31
    
    day = random.randint(1, last_day)
    return datetime(year, month, day)

def generate_description(category):
    """Generates a realistic description for a given category."""
    template_list = DESCRIPTION_TEMPLATES.get(category, DESCRIPTION_TEMPLATES["Miscellaneous"])
    template = random.choice(template_list)
    
    # Count placeholders and fill them
    num_placeholders = template.count("{}")
    fillers = random.sample(RANDOM_WORDS_FOR_DESCRIPTIONS, k=min(num_placeholders, len(RANDOM_WORDS_FOR_DESCRIPTIONS)))
    
    # Ensure enough fillers, duplicate if necessary
    while len(fillers) < num_placeholders:
        fillers.extend(random.sample(RANDOM_WORDS_FOR_DESCRIPTIONS, k=min(num_placeholders - len(fillers), len(RANDOM_WORDS_FOR_DESCRIPTIONS))))

    try:
        return template.format(*fillers[:num_placeholders])
    except IndexError: # Fallback if formatting fails
        return f"{category} - {random.choice(RANDOM_WORDS_FOR_DESCRIPTIONS)}"


def generate_transactions(year, month):
    """Generates a list of transactions for a given month."""
    transactions = []
    num_transactions = random.randint(75, 125)

    for _ in range(num_transactions):
        date = generate_random_date(year, month)
        category = random.choice(CATEGORIES)
        description = generate_description(category)
        amount = round(random.uniform(100.00, 50000.00), 2)
        transaction_type = random.choice(["Debit", "Credit"])

        transactions.append({
            "Date": date.strftime("%Y-%m-%d"),
            "Description": description,
            "Category": category,
            "Amount (INR)": amount,
            "Transaction Type": transaction_type
        })
    return transactions

def main():
    # Create output directory if it doesn't exist
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"Created directory: {OUTPUT_DIR}")

    for year, month_num in MONTHS:
        month_name = datetime(year, month_num, 1).strftime("%B")
        print(f"Generating data for {month_name} {year}...")

        transactions_data = generate_transactions(year, month_num)
        df = pd.DataFrame(transactions_data)

        base_filename = f"{year}-{month_num:02d}_{month_name}_Transactions"

        # Save to CSV
        csv_path = os.path.join(OUTPUT_DIR, f"{base_filename}.csv")
        df.to_csv(csv_path, index=False)
        print(f"Saved: {csv_path}")

        # Save to XLSX
        xlsx_path = os.path.join(OUTPUT_DIR, f"{base_filename}.xlsx")
        df.to_excel(xlsx_path, index=False, engine='openpyxl')
        print(f"Saved: {xlsx_path}")
        
        # Save to XLS
        # For .xls, pandas might require xlwt. If not installed, this will fail.
        # Also, sheet name length is limited for .xls
        xls_sheet_name = f"{month_name[:25]}{year}" # Max 31 chars, keeping it safe
        xls_path = os.path.join(OUTPUT_DIR, f"{base_filename}.xls")
        try:
            # Attempt to import xlwt before trying to use it
            import xlwt  # type: ignore
            df.to_excel(xls_path, index=False, sheet_name=xls_sheet_name, engine='xlwt')
            print(f"Saved: {xls_path}")
        except ImportError:
            print(f"Skipping .xls generation for {month_name} {year}: 'xlwt' library not found or not usable in the current environment.")
        except Exception as e:
            print(f"Could not save {xls_path} (other issue): {e}")


    print("\nData generation complete.")
    print(f"Files are located in: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()