#!/bin/bash

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

# Kill any existing test processes
pkill -f "pytest" 2>/dev/null || true

# Parse arguments for test selection
TEST_MARKER=""
if [ "$1" = "unit" ]; then
    TEST_MARKER="-m unit"
    echo "🧪 Starting API unit tests in background..."
elif [ "$1" = "integration" ]; then
    TEST_MARKER="-m integration"
    echo "🧪 Starting API integration tests in background..."
elif [ "$1" = "slow" ]; then
    TEST_MARKER="-m slow"
    echo "🧪 Starting API slow tests in background..."
elif [ "$1" = "all" ] || [ -z "$1" ]; then
    TEST_MARKER=""
    echo "🧪 Starting all API tests in background..."
else
    echo "❌ Invalid test type: $1"
    echo "Usage: $0 [unit|integration|slow|all]"
    exit 1
fi

# Start API testing in background with logging (overwrite each time)
cd "$WORKSPACE_ROOT/apps/giki-ai-api"
(uv run pytest $TEST_MARKER -v > "$WORKSPACE_ROOT/logs/test-api.log" 2>&1) &
TEST_PID=$!

echo "✅ API testing started (PID: $TEST_PID)"
echo "📝 Logs: logs/test-api.log"
echo $TEST_PID > "$WORKSPACE_ROOT/logs/test-api.pid"

# Show initial status
sleep 2
if kill -0 $TEST_PID 2>/dev/null; then
    echo "⏳ Testing in progress... Use 'tail -f logs/test-api.log' to follow"
else
    echo "✅ Testing completed quickly - check logs/test-api.log for results"
fi