#!/bin/bash
# Test Database Setup Script
# Sets up a clean test database for giki.ai testing

set -e  # Exit on error

# Load environment variables
if [ -f ".env.test" ]; then
    echo "Loading test environment from .env.test..."
    export $(grep -v '^#' .env.test | xargs)
else
    echo "Warning: .env.test not found. Using environment variables."
fi

# Validate required environment variables
if [ -z "$TEST_DATABASE_URL" ]; then
    echo "Error: TEST_DATABASE_URL environment variable is required"
    echo "Example: export TEST_DATABASE_URL='postgresql://test_user:password@localhost:5432/giki_ai_test'"
    exit 1
fi

# Parse database URL
DB_URL=$TEST_DATABASE_URL
DB_HOST=$(echo $DB_URL | sed -n 's/.*@\([^:\/]*\).*/\1/p')
DB_PORT=$(echo $DB_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
DB_NAME=$(echo $DB_URL | sed -n 's/.*\/\([^?]*\).*/\1/p')
DB_USER=$(echo $DB_URL | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')
DB_PASS=$(echo $DB_URL | sed -n 's/.*\/\/[^:]*:\([^@]*\)@.*/\1/p')

echo "🚀 Setting up test database..."
echo "   Host: $DB_HOST:$DB_PORT"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"

# Function to run psql commands
run_psql() {
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "$1"
}

# Function to run psql commands on test database
run_psql_test() {
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "$1"
}

# Check if database exists
DB_EXISTS=$(PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$DB_NAME'" 2>/dev/null || echo "0")

if [ "$DB_EXISTS" = "1" ]; then
    echo "⚠️  Test database already exists. Dropping and recreating..."
    
    # Terminate existing connections
    run_psql "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname='$DB_NAME' AND pid <> pg_backend_pid();" || true
    
    # Drop database
    run_psql "DROP DATABASE IF EXISTS $DB_NAME;"
fi

# Create test database
echo "📦 Creating test database..."
run_psql "CREATE DATABASE $DB_NAME;"

# Apply migrations
echo "🔧 Applying database migrations..."
cd apps/giki-ai-api

# Check if alembic is available
if command -v alembic &> /dev/null; then
    # Use alembic directly
    DATABASE_URL=$TEST_DATABASE_URL alembic upgrade head
elif [ -f "src/.venv/bin/alembic" ]; then
    # Use virtual environment alembic
    DATABASE_URL=$TEST_DATABASE_URL src/.venv/bin/alembic upgrade head
else
    # Use uv run
    DATABASE_URL=$TEST_DATABASE_URL uv run alembic upgrade head
fi

cd ../..

# Create test data directories
echo "📂 Creating test data directories..."
mkdir -p testing/data
mkdir -p testing/results
mkdir -p testing/artifacts

# Set permissions
chmod -R 755 testing/

# Create initial test data (optional)
if [ -f "scripts/create-test-data.sql" ]; then
    echo "💾 Loading initial test data..."
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f scripts/create-test-data.sql
fi

# Verify setup
echo "✅ Verifying test database setup..."
TABLE_COUNT=$(PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='public';" 2>/dev/null || echo "0")

if [ "$TABLE_COUNT" -gt "0" ]; then
    echo "✅ Test database setup complete!"
    echo "   Tables created: $TABLE_COUNT"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Copy .env.test.example to .env.test"
    echo "   2. Update TEST_DATABASE_URL in .env.test"
    echo "   3. Run tests with: pnpm nx test:api"
else
    echo "❌ Test database setup failed - no tables created"
    exit 1
fi