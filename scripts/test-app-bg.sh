#!/bin/bash

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

# Kill any existing test processes
pkill -f "vitest\|playwright" 2>/dev/null || true

# Parse arguments for test selection
TEST_TYPE=""
if [ "$1" = "unit" ] || [ "$1" = "test" ] || [ -z "$1" ]; then
    TEST_TYPE="test:run"
    echo "🧪 Starting frontend unit tests in background..."
elif [ "$1" = "coverage" ]; then
    TEST_TYPE="test:coverage"
    echo "🧪 Starting frontend tests with coverage in background..."
elif [ "$1" = "visual" ]; then
    TEST_TYPE="test:visual"
    echo "🧪 Starting frontend visual tests in background..."
elif [ "$1" = "integration" ]; then
    TEST_TYPE="test:integration"
    echo "🧪 Starting frontend integration tests in background..."
elif [ "$1" = "e2e" ]; then
    TEST_TYPE="test:e2e"
    echo "🧪 Starting frontend E2E tests in background..."
else
    echo "❌ Invalid test type: $1"
    echo "Usage: $0 [unit|coverage|visual|integration|e2e]"
    exit 1
fi

# Start frontend testing in background with logging (overwrite each time)
cd "$WORKSPACE_ROOT/apps/giki-ai-app"
(pnpm run $TEST_TYPE > "$WORKSPACE_ROOT/logs/test-app.log" 2>&1) &
TEST_PID=$!

echo "✅ Frontend testing started (PID: $TEST_PID)"
echo "📝 Logs: logs/test-app.log"
echo $TEST_PID > "$WORKSPACE_ROOT/logs/test-app.pid"

# Show initial status
sleep 2
if kill -0 $TEST_PID 2>/dev/null; then
    echo "⏳ Testing in progress... Use 'tail -f logs/test-app.log' to follow"
else
    echo "✅ Testing completed quickly - check logs/test-app.log for results"
fi