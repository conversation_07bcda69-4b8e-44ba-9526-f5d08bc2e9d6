#!/bin/bash
# Test Data Validation Script - Enforces mandatory test data policy

echo "🔍 Validating test data compliance..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

VIOLATIONS=0

# Check for unauthorized CSV files
echo -e "\n${YELLOW}Checking for unauthorized CSV files...${NC}"
CSV_FILES=$(find . -name "*.csv" -not -path "./node_modules/*" -not -path "./.nx/*" -not -path "./venv/*" -not -path "./.venv/*" -not -path "*/site-packages/*" | head -20)
if [ ! -z "$CSV_FILES" ]; then
    echo -e "${RED}❌ Found unauthorized CSV files:${NC}"
    echo "$CSV_FILES"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ No unauthorized CSV files found${NC}"
fi

# Check for test files in workspace root
echo -e "\n${YELLOW}Checking for test files in workspace root...${NC}"
ROOT_TEST_FILES=$(ls test_*.py *_test.py test_*.json check_*.py *_test_results.json 2>/dev/null)
if [ ! -z "$ROOT_TEST_FILES" ]; then
    echo -e "${RED}❌ Found test files in workspace root:${NC}"
    echo "$ROOT_TEST_FILES"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ No test files in workspace root${NC}"
fi

# Check for unauthorized test data in testing directory
echo -e "\n${YELLOW}Checking testing directory for unauthorized data...${NC}"
UNAUTHORIZED_TEST_DATA=$(find testing/data -type f \( -name "*.csv" -o -name "*.txt" -o -name "*.json" -o -name "*.qif" -o -name "*.ofx" \) 2>/dev/null)
if [ ! -z "$UNAUTHORIZED_TEST_DATA" ]; then
    echo -e "${RED}❌ Found unauthorized test data in testing/data:${NC}"
    echo "$UNAUTHORIZED_TEST_DATA"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ Testing directory contains only authorized data${NC}"
fi

# Verify authorized test files exist
echo -e "\n${YELLOW}Verifying authorized test files exist...${NC}"
AUTHORIZED_FILES=(
    "data/milestones/M1-nuvie/nuvie_expense_ledger.xlsx"
    "data/milestones/M2-rezolve/Capital One.xlsx"
    "data/milestones/M2-rezolve/Credit Card.xlsx"
    "data/milestones/M2-rezolve/ICICI.xlsx"
    "data/milestones/M2-rezolve/SVB.xlsx"
)

MISSING_FILES=0
for FILE in "${AUTHORIZED_FILES[@]}"; do
    if [ ! -f "$FILE" ]; then
        echo -e "${RED}❌ Missing authorized file: $FILE${NC}"
        MISSING_FILES=$((MISSING_FILES + 1))
    fi
done

if [ $MISSING_FILES -eq 0 ]; then
    echo -e "${GREEN}✅ All authorized test files present${NC}"
else
    VIOLATIONS=$((VIOLATIONS + 1))
fi

# Check for dummy/sample/mock files
echo -e "\n${YELLOW}Checking for dummy/sample/mock files...${NC}"
DUMMY_FILES=$(find . -name "sample_*" -o -name "dummy_*" -o -name "mock_*" -o -name "test-transactions.*" | grep -v node_modules | grep -v ".nx" | grep -v "site-packages" | grep -v ".venv" | grep -v ".mypy_cache" | grep -v "__pycache__" | head -20)
if [ ! -z "$DUMMY_FILES" ]; then
    echo -e "${RED}❌ Found dummy/sample/mock files:${NC}"
    echo "$DUMMY_FILES"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ No dummy/sample/mock files found${NC}"
fi

# Final report
echo -e "\n${YELLOW}===== Test Data Validation Summary =====${NC}"
if [ $VIOLATIONS -eq 0 ]; then
    echo -e "${GREEN}✅ All test data policies are being followed!${NC}"
    echo -e "${GREEN}✅ Only authorized Nuvie and Rezolve Excel files are used${NC}"
    exit 0
else
    echo -e "${RED}❌ Found $VIOLATIONS policy violations${NC}"
    echo -e "${RED}❌ Please fix all violations before proceeding${NC}"
    echo -e "\n${YELLOW}Reminder: Only use these authorized files for testing:${NC}"
    for FILE in "${AUTHORIZED_FILES[@]}"; do
        echo "  - $FILE"
    done
    exit 1
fi