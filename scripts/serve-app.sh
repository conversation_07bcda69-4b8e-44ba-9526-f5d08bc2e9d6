#!/bin/bash

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Kill any existing process on port 4200
lsof -ti:4200 | xargs kill -9 2>/dev/null || true

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

# Load development environment variables
echo "🔧 Loading development environment..."
if [ -f "$WORKSPACE_ROOT/infrastructure/environments/development/.env.development" ]; then
    set -a  # automatically export all variables
    source "$WORKSPACE_ROOT/infrastructure/environments/development/.env.development"
    set +a  # stop automatically exporting
    echo "✅ Development environment loaded"
else
    echo "⚠️  Development environment file not found at infrastructure/environments/development/.env.development"
fi

# Load frontend environment variables
if [ -f "$WORKSPACE_ROOT/apps/giki-ai-app/.env.development" ]; then
    set -a  # automatically export all variables
    source "$WORKSPACE_ROOT/apps/giki-ai-app/.env.development"
    set +a  # stop automatically exporting
    echo "✅ Frontend environment loaded"
fi

# Start frontend server in background with logging
cd "$WORKSPACE_ROOT/apps/giki-ai-app"
(pnpm run dev > "$WORKSPACE_ROOT/logs/serve-app.log" 2>&1) &
FRONTEND_PID=$!

echo "✅ Frontend server started on port 4200 (PID: $FRONTEND_PID)"
echo "📝 Logs: logs/serve-app.log"
echo $FRONTEND_PID > "$WORKSPACE_ROOT/logs/serve-app.pid"