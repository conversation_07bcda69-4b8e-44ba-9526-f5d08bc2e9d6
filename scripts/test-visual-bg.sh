#!/bin/bash

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

echo "🎨 Starting visual regression tests..."

# Function to check if port is in use
check_port() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to wait for server to be ready
wait_for_server() {
    local port=$1
    local service=$2
    local max_attempts=30
    local attempt=0
    
    echo "⏳ Waiting for $service (port $port) to be ready..."
    while [ $attempt -lt $max_attempts ]; do
        if check_port $port; then
            echo "✅ $service is ready"
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service failed to start within 60 seconds"
    return 1
}

# Function to ensure database services are running
ensure_database_services() {
    # Ensure PostgreSQL is running
    echo "🔍 Checking PostgreSQL..."
    if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
        echo "🚀 Starting PostgreSQL..."
        if command -v brew >/dev/null 2>&1; then
            brew services start postgresql@15 >/dev/null 2>&1 || brew services start postgresql >/dev/null 2>&1
            sleep 3
        fi
        
        if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
            echo "❌ Could not start PostgreSQL automatically"
            echo "💡 Please start PostgreSQL manually: brew services start postgresql@15"
            return 1
        fi
    fi
    echo "✅ PostgreSQL is running"

    # Ensure Redis is running (if needed)
    echo "🔍 Checking Redis..."
    if ! redis-cli ping >/dev/null 2>&1; then
        echo "🚀 Starting Redis..."
        if command -v brew >/dev/null 2>&1; then
            brew services start redis >/dev/null 2>&1
            sleep 2
        fi
        
        if ! redis-cli ping >/dev/null 2>&1; then
            echo "⚠️  Redis not available - continuing without Redis (some features may be limited)"
        else
            echo "✅ Redis is running"
        fi
    else
        echo "✅ Redis is running"
    fi
}

# Ensure database services are running first
ensure_database_services || exit 1

# Check and start API server if needed
if ! check_port 8000; then
    echo "🚀 Starting API server..."
    pnpm serve:api >/dev/null 2>&1 &
    wait_for_server 8000 "API server" || exit 1
else
    echo "✅ API server already running"
fi

# Check and start frontend server if needed  
if ! check_port 4200; then
    echo "🚀 Starting frontend server..."
    pnpm serve:app >/dev/null 2>&1 &
    wait_for_server 4200 "Frontend server" || exit 1
else
    echo "✅ Frontend server already running"
fi

# Kill any existing visual test processes
pkill -f "playwright.*visual-regression" 2>/dev/null || true

# Start visual regression tests in background
echo "🎨 Running visual regression tests in background..."
(playwright test --config=tests/visual-regression/playwright.config.ts > "$WORKSPACE_ROOT/logs/test-visual.log" 2>&1) &
TEST_PID=$!

echo "✅ Visual tests started (PID: $TEST_PID)"
echo "📝 Logs: logs/test-visual.log"
echo $TEST_PID > "$WORKSPACE_ROOT/logs/test-visual.pid"

# Show initial status
sleep 2
if kill -0 $TEST_PID 2>/dev/null; then
    echo "⏳ Testing in progress... Use 'tail -f logs/test-visual.log' to follow"
else
    echo "✅ Testing completed quickly - check logs/test-visual.log for results"
fi