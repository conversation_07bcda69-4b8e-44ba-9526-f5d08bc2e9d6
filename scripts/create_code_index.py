import os
import re
try:
    import pathspec
except ImportError:
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pathspec"])
    import pathspec

# Files/patterns to completely exclude from the index
EXCLUDE_PATTERNS = [
    # Test and temporary files
    '*.test.*', '*.spec.*', '__tests__/', 'test/', 'tests/', '*.tmp', '*.temp',
    'test-*.sh', '*-test.sh', 'test*.py', '*_test.py',
    
    # Build artifacts and caches
    'dist/', 'build/', '.next/', '__pycache__/', '*.pyc', 'node_modules/',
    '.cache/', 'coverage/', '.pytest_cache/', '.mypy_cache/', 'tfplan',
    
    # Lock and dependency files
    '*.lock', 'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml',
    
    # Documentation and reports (unless essential)
    '*_REPORT.md', '*_RESULTS.md', '*_ANALYSIS.md', '*_STATUS.md',
    'WORKFLOW_*', 'PERFORMANCE_*', 'SECURITY_*', 'BACKEND_*',
    '*-SUMMARY.md', '*-GUIDE.md', '*-audit.md', '*-analysis.md',
    
    # Config files (keep only essential ones)
    '.prettierrc', '.prettierignore', '.dockerignore', '.gcloudignore',
    '.firebaserc', '.augmentignore', '.cursorignore', '.roomodes',
    '.augment-guidelines', 'firebase.json', 'cloudbuild.yaml',
    '*.tfvars', '*.hcl',
    
    # Database migrations (too granular)
    'migrations/*.sql', 'initdb/', '*.sql',
    
    # Generated files
    '*.json', '*.yaml', '*.yml',  # Keep only if they define structure
    'openapi.*', 'api_endpoints.json', 'endpoints.json',
    '*-results.json', '*-report.json',
    
    # Binary and media files
    '*.png', '*.jpg', '*.jpeg', '*.gif', '*.ico', '*.svg',
    '*.pdf', '*.docx', '*.xlsx', '*.zip', '*.tar', '*.gz',
    
    # IDE and system files
    '.idea/', '.vscode/', '.DS_Store', 'Thumbs.db', '.cursor/',
    
    # Logs and debug files
    '*.log', 'nohup.out', 'debug/', '*.pid', 'logs/',
    
    # Data files
    '*.csv', '*.iif', '*.xml',
    
    # Cloud SQL proxy and similar binaries
    'cloud_sql_proxy', 'cloud-sql-proxy', '--build',
    
    # Shell scripts for deployment/setup
    'deploy*.sh', 'setup*.sh', 'start*.sh',
    
    # HTML files (mockups, etc)
    '*.html',
    
    # Temporary verification/analysis files
    'color-verification.html', 'heading-analysis-results.md',
    'luckysheet-loader.html', '.gitkeep',
    
    # Environment and secret files  
    '.env*', 'docker-entrypoint.sh'
]

# Important directories to include (override exclusions)
IMPORTANT_DIRS = [
    'src/',
    'components/',
    'features/',
    'services/',
    'hooks/',
    'utils/',
    'shared/',
    'core/',
    'domains/',
    'pages/',
    'providers/',
    'router/'
]

# Files that should always be included if present
IMPORTANT_FILES = [
    'CLAUDE.md',
    'README.md',
    'package.json',
    'pyproject.toml',
    'requirements.txt',
    'tsconfig.json',
    'vite.config.ts',
    'main.py',
    'main.tsx',
    'App.tsx',
    'index.ts',
    'index.tsx'
]

def should_include_file(relative_path):
    """Determines if a file should be included in the index based on importance and exclusion rules."""
    basename = os.path.basename(relative_path)
    
    # Always include important files
    if basename in IMPORTANT_FILES:
        return True
    
    # Check if file is in an important directory
    for important_dir in IMPORTANT_DIRS:
        if important_dir in relative_path:
            # Still apply some exclusions even in important dirs
            if any(pattern in relative_path for pattern in ['__tests__/', '.test.', '.spec.', '__pycache__']):
                return False
            return True
    
    # Exclude based on patterns
    for pattern in EXCLUDE_PATTERNS:
        if pattern.endswith('/'):
            if pattern in relative_path:
                return False
        elif '*' in pattern:
            # Simple glob matching
            pattern_regex = pattern.replace('.', '\\.').replace('*', '.*')
            if re.match(pattern_regex, basename):
                return False
        elif pattern == basename:
            return False
    
    return True

def get_file_type(path):
    """Categorizes files by type - removed as not used in concise output."""
    return None

def get_summary(file_path):
    """No longer used - focusing on key definitions only."""
    return ""

def get_key_definitions(file_path):
    """
    Extracts key definitions (classes, functions, components) from a file
    using regular expressions. Enhanced to be more selective and informative.
    """
    definitions = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Skip files that are too small to have meaningful definitions
        if len(content) < 50:
            return []

        # Pattern for Python classes and functions
        if file_path.endswith('.py'):
            # Classes
            class_pattern = r'^\s*class\s+([A-Z][a-zA-Z0-9_]+)'
            definitions.extend(re.findall(class_pattern, content, re.MULTILINE))
            
            # Functions (but not private ones)
            func_pattern = r'^\s*(?:async\s+)?def\s+([a-z][a-zA-Z0-9_]+)\s*\('
            funcs = re.findall(func_pattern, content, re.MULTILINE)
            # Filter out common test functions and private functions
            definitions.extend([f for f in funcs if not f.startswith('_') and f not in ['setUp', 'tearDown', 'test_']])

        # Pattern for TypeScript/JavaScript
        elif file_path.endswith(('.ts', '.tsx', '.js', '.jsx')):
            # React components (typically PascalCase)
            component_pattern = r'(?:export\s+)?(?:const|function|class)\s+([A-Z][a-zA-Z0-9]+)'
            definitions.extend(re.findall(component_pattern, content, re.MULTILINE))
            
            # Exported functions/hooks
            export_pattern = r'export\s+(?:const|function)\s+([a-z][a-zA-Z0-9]+)'
            definitions.extend(re.findall(export_pattern, content, re.MULTILINE))
            
            # Custom hooks (use*)
            hook_pattern = r'(?:export\s+)?(?:const|function)\s+(use[A-Z][a-zA-Z0-9]+)'
            definitions.extend(re.findall(hook_pattern, content, re.MULTILINE))

    except Exception:
        # Ignore files that can't be read
        pass

    # Filter out common React imports and utilities
    common_excludes = {
        'React', 'useState', 'useEffect', 'useContext', 'useReducer', 
        'useCallback', 'useMemo', 'useRef', 'forwardRef', 'Fragment',
        'Component', 'PureComponent', 'memo', 'lazy', 'Suspense',
        'render', 'mount', 'shallow', 'describe', 'it', 'test', 'expect',
        'beforeEach', 'afterEach', 'beforeAll', 'afterAll'
    }
    
    # Clean up and limit definitions
    unique_definitions = []
    seen = set()
    for d in definitions:
        if d not in common_excludes and d not in seen:
            seen.add(d)
            unique_definitions.append(d)
    
    # Limit to 5 most important (first ones found are usually most important)
    return unique_definitions[:5]

def create_code_index():
    output_path = '.claude/memory/code_index.md'
    workspace_dir = os.getcwd()
    gitignore_path = os.path.join(workspace_dir, '.gitignore')

    # Hardcoded essential ignores
    hardcoded_ignore_patterns = ['.git/', '.claude/', 'node_modules/', '__pycache__/']

    # Load patterns from .gitignore
    ignore_patterns = list(hardcoded_ignore_patterns)
    if os.path.exists(gitignore_path):
        with open(gitignore_path, 'r', encoding='utf-8') as f:
            ignore_patterns.extend(line.strip() for line in f if line.strip() and not line.startswith('#'))
    
    spec = pathspec.PathSpec.from_lines(pathspec.patterns.GitWildMatchPattern, ignore_patterns)

    with open(output_path, 'w', encoding='utf-8') as index_file:
        index_file.write("# Codebase Index\n\n")
        index_file.write("This index shows the key source files and their main exports/definitions.\n\n")

        all_files = []
        for root, dirs, files in os.walk(workspace_dir, topdown=True):
            # Exclude directories based on ignore spec
            dirs[:] = [d for d in dirs if not spec.match_file(os.path.relpath(os.path.join(root, d), workspace_dir) + '/')]
            
            for file in files:
                full_path = os.path.join(root, file)
                relative_path = os.path.relpath(full_path, workspace_dir)
                
                # Apply our custom filtering
                if not spec.match_file(relative_path) and should_include_file(relative_path):
                    all_files.append((relative_path, full_path))

        # Group files by major sections for better organization
        sections = {
            'root': [],
            'api': [],
            'app': [],
            'libs': [],
            'scripts': [],
            'docs': [],
            'infrastructure': []
        }
        
        for relative_path, full_path in all_files:
            if relative_path.startswith('apps/giki-ai-api/'):
                sections['api'].append((relative_path, full_path))
            elif relative_path.startswith('apps/giki-ai-app/'):
                sections['app'].append((relative_path, full_path))
            elif relative_path.startswith('libs/'):
                sections['libs'].append((relative_path, full_path))
            elif relative_path.startswith('scripts/'):
                sections['scripts'].append((relative_path, full_path))
            elif relative_path.startswith('docs/'):
                sections['docs'].append((relative_path, full_path))
            elif relative_path.startswith('infrastructure/'):
                sections['infrastructure'].append((relative_path, full_path))
            elif '/' not in relative_path:
                sections['root'].append((relative_path, full_path))

        # Write root files first
        if sections['root']:
            index_file.write("## Root Files\n")
            for relative_path, full_path in sorted(sections['root']):
                definitions = get_key_definitions(full_path)
                if definitions:
                    index_file.write(f"- `{relative_path}`: {', '.join(definitions)}\n")
                else:
                    index_file.write(f"- `{relative_path}`\n")
            index_file.write("\n")

        # Write API section with hierarchical structure
        if sections['api']:
            index_file.write("## API (FastAPI Backend)\n")
            write_hierarchical_section(index_file, sections['api'], 'apps/giki-ai-api/')
            index_file.write("\n")

        # Write App section with hierarchical structure  
        if sections['app']:
            index_file.write("## App (React Frontend)\n")
            write_hierarchical_section(index_file, sections['app'], 'apps/giki-ai-app/')
            index_file.write("\n")

        # Write other sections with smart grouping
        for section_name, section_files in sections.items():
            if section_name not in ['root', 'api', 'app'] and section_files:
                index_file.write(f"## {section_name.capitalize()}\n")
                
                # For scripts and docs, use hierarchical view
                if section_name in ['scripts', 'docs']:
                    write_hierarchical_section(index_file, section_files, f"{section_name}/")
                else:
                    # For other sections, list files directly
                    for relative_path, full_path in sorted(section_files):
                        definitions = get_key_definitions(full_path)
                        if definitions:
                            index_file.write(f"- `{relative_path}`: {', '.join(definitions)}\n")
                        else:
                            # Skip files with no definitions in less important sections
                            if section_name != 'infrastructure':
                                index_file.write(f"- `{relative_path}`\n")
                index_file.write("\n")

    print(f"Code index generated at {output_path}")
    print(f"Total files indexed: {len(all_files)}")

def write_hierarchical_section(index_file, files, base_path):
    """Write files in a hierarchical structure for better readability."""
    # Build tree structure
    tree = {}
    for relative_path, full_path in sorted(files):
        path_without_base = relative_path[len(base_path):]
        parts = path_without_base.split(os.sep)
        
        current = tree
        for i, part in enumerate(parts[:-1]):
            if part not in current:
                current[part] = {}
            current = current[part]
        
        # Add file with definitions
        filename = parts[-1]
        definitions = get_key_definitions(full_path)
        current[filename] = definitions

    # Write tree with collapsing of single-file directories
    def write_subtree(subtree, indent="", path=""):
        for key, value in sorted(subtree.items()):
            if isinstance(value, dict):
                # Check if this directory only contains one item
                if len(value) == 1:
                    single_key = list(value.keys())[0]
                    single_value = value[single_key]
                    if isinstance(single_value, dict):
                        # Collapse: dir/subdir/
                        index_file.write(f"{indent}- `{key}/{single_key}/`\n")
                        write_subtree(single_value, indent + "  ", f"{path}{key}/{single_key}/")
                    else:
                        # Show directory with single file
                        index_file.write(f"{indent}- `{key}/`\n")
                        if single_value:  # Has definitions
                            index_file.write(f"{indent}  - `{single_key}`: {', '.join(single_value)}\n")
                        else:
                            index_file.write(f"{indent}  - `{single_key}`\n")
                else:
                    # Multiple items, show normally
                    index_file.write(f"{indent}- `{key}/`\n")
                    write_subtree(value, indent + "  ", f"{path}{key}/")
            else:
                if value:  # Has definitions
                    index_file.write(f"{indent}- `{key}`: {', '.join(value)}\n")
                else:
                    # For files without definitions, be more selective
                    basename = key.lower()
                    if (basename in IMPORTANT_FILES or 
                        basename.endswith(('.tsx', '.ts', '.py')) or
                        'main' in basename or 'index' in basename or 'app' in basename):
                        index_file.write(f"{indent}- `{key}`\n")
    
    write_subtree(tree)

if __name__ == "__main__":
    # Ensure the output directory exists
    os.makedirs('.claude/memory', exist_ok=True)
    create_code_index()