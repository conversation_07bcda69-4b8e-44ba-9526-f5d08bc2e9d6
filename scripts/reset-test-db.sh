#!/bin/bash
# Test Database Reset Script
# Cleans and resets the test database for giki.ai

set -e  # Exit on error

# Load environment variables
if [ -f ".env.test" ]; then
    echo "Loading test environment from .env.test..."
    export $(grep -v '^#' .env.test | xargs)
else
    echo "Warning: .env.test not found. Using environment variables."
fi

# Validate required environment variables
if [ -z "$TEST_DATABASE_URL" ]; then
    echo "Error: TEST_DATABASE_URL environment variable is required"
    echo "Example: export TEST_DATABASE_URL='postgresql://test_user:password@localhost:5432/giki_ai_test'"
    exit 1
fi

# Parse database URL
DB_URL=$TEST_DATABASE_URL
DB_HOST=$(echo $DB_URL | sed -n 's/.*@\([^:\/]*\).*/\1/p')
DB_PORT=$(echo $DB_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
DB_NAME=$(echo $DB_URL | sed -n 's/.*\/\([^?]*\).*/\1/p')
DB_USER=$(echo $DB_URL | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')
DB_PASS=$(echo $DB_URL | sed -n 's/.*\/\/[^:]*:\([^@]*\)@.*/\1/p')

echo "🧹 Resetting test database..."
echo "   Host: $DB_HOST:$DB_PORT"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"

# Function to run psql commands on test database
run_psql_test() {
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "$1"
}

# Safety check - ensure this is a test database
if [[ ! "$DB_NAME" =~ "test" ]]; then
    echo "❌ Safety check failed: Database name must contain 'test'"
    echo "   Current database: $DB_NAME"
    exit 1
fi

# Get list of all tables
echo "📋 Getting list of tables..."
TABLES=$(PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -tAc "SELECT tablename FROM pg_tables WHERE schemaname='public' AND tablename NOT LIKE 'alembic%';" 2>/dev/null)

if [ -z "$TABLES" ]; then
    echo "⚠️  No tables found to reset"
else
    # Truncate all tables
    echo "🗑️  Truncating tables..."
    for TABLE in $TABLES; do
        echo "   - Truncating $TABLE..."
        run_psql_test "TRUNCATE TABLE $TABLE CASCADE;" || echo "   ⚠️  Failed to truncate $TABLE"
    done
fi

# Reset sequences
echo "🔄 Resetting sequences..."
SEQUENCES=$(PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -tAc "SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema='public';" 2>/dev/null)

if [ ! -z "$SEQUENCES" ]; then
    for SEQ in $SEQUENCES; do
        echo "   - Resetting $SEQ..."
        run_psql_test "ALTER SEQUENCE $SEQ RESTART WITH 1;" || echo "   ⚠️  Failed to reset $SEQ"
    done
fi

# Clean test artifacts
echo "🧹 Cleaning test artifacts..."
if [ -d "testing/results" ]; then
    find testing/results -type f -name "*.log" -o -name "*.json" -o -name "*.xml" | xargs rm -f 2>/dev/null || true
    echo "   - Cleaned test results"
fi

if [ -d "testing/artifacts" ]; then
    find testing/artifacts -type f | xargs rm -f 2>/dev/null || true
    echo "   - Cleaned test artifacts"
fi

# Clean Playwright test results
if [ -d "apps/giki-ai-e2e/test-results" ]; then
    rm -rf apps/giki-ai-e2e/test-results/*
    echo "   - Cleaned Playwright test results"
fi

# Load initial test data if available
if [ -f "scripts/create-test-data.sql" ]; then
    echo "💾 Loading initial test data..."
    PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f scripts/create-test-data.sql
fi

# Verify reset
echo "✅ Verifying database reset..."
for TABLE in $TABLES; do
    COUNT=$(PGPASSWORD=$DB_PASS psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -tAc "SELECT COUNT(*) FROM $TABLE;" 2>/dev/null || echo "error")
    if [ "$COUNT" = "error" ]; then
        echo "   ❌ $TABLE: Error checking count"
    elif [ "$COUNT" = "0" ]; then
        echo "   ✅ $TABLE: Empty"
    else
        echo "   ⚠️  $TABLE: Contains $COUNT rows"
    fi
done

echo ""
echo "✅ Test database reset complete!"
echo ""
echo "📋 Next steps:"
echo "   - Run tests with: pnpm nx test:api"
echo "   - Run E2E tests with: pnpm nx test:e2e"