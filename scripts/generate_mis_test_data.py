#!/usr/bin/env python3
"""
Generate MIS-Specific Test Data
===============================

Creates synthetic test data specifically designed for MIS categorization testing:
- Quick setup test data (5-minute MIS setup)
- Historical enhancement test data (with matching patterns)
- Schema enhancement test data (with GL codes)
- Vendor mapping test data
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import random
import json

# MIS Categories based on our hierarchy
MIS_CATEGORIES = {
    'Income': {
        'Sales': ['Product Sales', 'Service Revenue', 'Subscription Income'],
        'Other Income': ['Interest Income', 'Investment Income', 'Rental Income']
    },
    'Expense': {
        'Cost of Goods Sold': {
            'Direct Materials': ['Raw Materials', 'Components', 'Packaging'],
            'Direct Labor': ['Manufacturing Wages', 'Production Bonuses'],
            'Manufacturing Overhead': ['Factory Rent', 'Equipment Depreciation']
        },
        'Operating Expenses': {
            'Sales & Marketing': ['Advertising', 'Sales Commissions', 'Marketing Campaigns'],
            'General & Administrative': ['Office Rent', 'Admin Salaries', 'Insurance'],
            'Research & Development': ['R&D Salaries', 'Lab Equipment', 'Patents']
        },
        'Financial Expenses': {
            'Interest Expense': ['Loan Interest', 'Credit Card Interest'],
            'Bank Charges': ['Transaction Fees', 'Wire Fees', 'Service Charges']
        }
    }
}

# GL Code mapping for enhanced testing
GL_CODES = {
    'Product Sales': '4000',
    'Service Revenue': '4100',
    'Subscription Income': '4200',
    'Interest Income': '4500',
    'Raw Materials': '5000',
    'Manufacturing Wages': '5100',
    'Factory Rent': '5200',
    'Advertising': '6100',
    'Office Rent': '6200',
    'Admin Salaries': '6300',
    'R&D Salaries': '7000',
    'Loan Interest': '8000',
    'Transaction Fees': '8100'
}

class MISTestDataGenerator:
    """Generate MIS-specific test data"""
    
    def __init__(self, output_dir: str = "libs/test-data/mis-testing"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_quick_setup_data(self):
        """Generate data for 5-minute MIS setup testing"""
        print("\nGenerating Quick Setup Test Data...")
        
        # Create 3 months of mixed transactions
        transactions = []
        start_date = datetime(2024, 1, 1)
        
        # Common vendors for consistency
        vendors = {
            'income': [
                'ABC Corporation', 'XYZ Industries', 'Global Tech Solutions',
                'Premier Services Ltd', 'Digital Innovations Inc'
            ],
            'expense': [
                'Office Supplies Plus', 'Tech Hardware Depot', 'Cloud Services Pro',
                'Marketing Agency X', 'Facilities Management Co', 'Payroll Services Inc',
                'Utility Company', 'Insurance Provider', 'Legal Services LLP'
            ]
        }
        
        # Generate 150 transactions over 3 months
        for i in range(150):
            date = start_date + timedelta(days=random.randint(0, 90))
            
            # 30% income, 70% expense
            if random.random() < 0.3:
                # Income transaction
                vendor = random.choice(vendors['income'])
                amount = round(random.uniform(5000, 50000), 2)
                description = f"Invoice #{random.randint(1000, 9999)} - {vendor}"
                tx_type = 'Credit'
            else:
                # Expense transaction
                vendor = random.choice(vendors['expense'])
                
                # Vary amounts by vendor type
                if 'Payroll' in vendor:
                    amount = round(random.choice([25000, 30000, 35000, 40000]), 2)
                elif 'Office' in vendor or 'Facilities' in vendor:
                    amount = round(random.uniform(1000, 10000), 2)
                else:
                    amount = round(random.uniform(100, 5000), 2)
                
                description = f"{vendor} - {date.strftime('%B %Y')}"
                tx_type = 'Debit'
            
            transactions.append({
                'Date': date.strftime('%Y-%m-%d'),
                'Description': description,
                'Vendor': vendor,
                'Amount': amount,
                'Type': tx_type,
                'Category': '',  # Empty for AI to categorize
                'GL Code': ''    # Empty for AI to assign
            })
        
        # Sort by date
        df = pd.DataFrame(transactions).sort_values('Date')
        
        # Save multiple formats
        output_files = []
        
        # Excel format
        excel_path = self.output_dir / 'quick-setup' / 'test_transactions_uncategorized.xlsx'
        excel_path.parent.mkdir(exist_ok=True)
        df.to_excel(excel_path, index=False)
        output_files.append(str(excel_path))
        
        # CSV format
        csv_path = self.output_dir / 'quick-setup' / 'test_transactions_uncategorized.csv'
        df.to_csv(csv_path, index=False)
        output_files.append(str(csv_path))
        
        print(f"  ✓ Created quick setup test files:")
        for f in output_files:
            print(f"    - {f}")
        
        return df
    
    def generate_historical_enhancement_data(self):
        """Generate data for historical enhancement testing"""
        print("\nGenerating Historical Enhancement Test Data...")
        
        # Create 12 months of historical data with patterns
        transactions = []
        start_date = datetime(2023, 1, 1)
        
        # Seasonal vendors
        seasonal_patterns = {
            'Q1': ['Tax Preparation Services', 'Audit Firm LLP', 'Compliance Consultants'],
            'Q2': ['Summer Marketing Campaign', 'Trade Show Expenses', 'Travel Agency'],
            'Q3': ['Back to School Promotions', 'Inventory Buildup', 'Seasonal Staff'],
            'Q4': ['Holiday Marketing', 'Year End Bonuses', 'Gift Suppliers']
        }
        
        # Regular monthly expenses
        monthly_expenses = [
            ('Rent - Main Office', 15000),
            ('Utilities - Electricity', 2500),
            ('Internet Services', 500),
            ('Insurance Premium', 5000),
            ('Accounting Software', 299),
            ('Cloud Storage', 99)
        ]
        
        # Generate 12 months of data
        for month in range(12):
            current_month = start_date + timedelta(days=30 * month)
            quarter = f'Q{(month // 3) + 1}'
            
            # Add monthly recurring expenses
            for expense, amount in monthly_expenses:
                transactions.append({
                    'Date': current_month.strftime('%Y-%m-%d'),
                    'Description': f"{expense} - {current_month.strftime('%B %Y')}",
                    'Vendor': expense.split(' - ')[0],
                    'Amount': amount,
                    'Type': 'Debit',
                    'Category': '',
                    'Pattern': 'Monthly Recurring'
                })
            
            # Add seasonal expenses
            seasonal_vendors = seasonal_patterns.get(quarter, [])
            for vendor in seasonal_vendors:
                if random.random() < 0.7:  # 70% chance of seasonal expense
                    amount = round(random.uniform(1000, 10000), 2)
                    transactions.append({
                        'Date': (current_month + timedelta(days=random.randint(0, 29))).strftime('%Y-%m-%d'),
                        'Description': f"{vendor} - {quarter} Activity",
                        'Vendor': vendor,
                        'Amount': amount,
                        'Type': 'Debit',
                        'Category': '',
                        'Pattern': f'{quarter} Seasonal'
                    })
            
            # Add random transactions
            for _ in range(random.randint(5, 15)):
                date = current_month + timedelta(days=random.randint(0, 29))
                if random.random() < 0.3:
                    # Income
                    amount = round(random.uniform(10000, 100000), 2)
                    vendor = f"Customer {random.randint(100, 999)}"
                    description = f"Payment from {vendor}"
                    tx_type = 'Credit'
                    pattern = 'Customer Payment'
                else:
                    # Expense
                    amount = round(random.uniform(100, 5000), 2)
                    vendor = f"Vendor {random.randint(100, 999)}"
                    description = f"Purchase from {vendor}"
                    tx_type = 'Debit'
                    pattern = 'Ad-hoc Purchase'
                
                transactions.append({
                    'Date': date.strftime('%Y-%m-%d'),
                    'Description': description,
                    'Vendor': vendor,
                    'Amount': amount,
                    'Type': tx_type,
                    'Category': '',
                    'Pattern': pattern
                })
        
        df = pd.DataFrame(transactions).sort_values('Date')
        
        # Save with pattern analysis
        output_path = self.output_dir / 'historical-enhancement' / 'historical_transactions_with_patterns.xlsx'
        output_path.parent.mkdir(exist_ok=True)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Transactions', index=False)
            
            # Add pattern summary sheet
            pattern_summary = df.groupby('Pattern').agg({
                'Amount': ['count', 'sum', 'mean'],
                'Date': ['min', 'max']
            }).round(2)
            pattern_summary.to_excel(writer, sheet_name='Pattern_Summary')
        
        print(f"  ✓ Created historical enhancement test file:")
        print(f"    - {output_path}")
        print(f"    - Total transactions: {len(df)}")
        print(f"    - Date range: {df['Date'].min()} to {df['Date'].max()}")
        
        return df
    
    def generate_schema_enhancement_data(self):
        """Generate data for schema/GL code enhancement testing"""
        print("\nGenerating Schema Enhancement Test Data...")
        
        # Create transactions with partial GL codes
        transactions = []
        start_date = datetime(2024, 1, 1)
        
        # Some transactions with GL codes, some without
        gl_mapped_vendors = {
            'Sales Team Commission': ('6100', 'Sales & Marketing'),
            'Google Ads': ('6100', 'Sales & Marketing'),
            'Office Lease Payment': ('6200', 'General & Administrative'),
            'Employee Salaries': ('6300', 'General & Administrative'),
            'Research Equipment': ('7000', 'Research & Development'),
            'Bank Loan Interest': ('8000', 'Interest Expense'),
            'Wire Transfer Fee': ('8100', 'Bank Charges')
        }
        
        # Generate 200 transactions
        for i in range(200):
            date = start_date + timedelta(days=random.randint(0, 60))
            
            if random.random() < 0.4 and gl_mapped_vendors:
                # Use known vendor with GL code (40% of transactions)
                vendor, (gl_code, category) = random.choice(list(gl_mapped_vendors.items()))
                amount = round(random.uniform(1000, 20000), 2)
                has_gl = random.random() < 0.7  # 70% already have GL codes
                
                transactions.append({
                    'Date': date.strftime('%Y-%m-%d'),
                    'Description': f"{vendor} - {date.strftime('%B %Y')}",
                    'Vendor': vendor,
                    'Amount': amount,
                    'Type': 'Debit',
                    'Category': category if has_gl else '',
                    'GL Code': gl_code if has_gl else '',
                    'Expected GL': gl_code  # For validation
                })
            else:
                # Random vendor without GL code
                if random.random() < 0.3:
                    # Income
                    vendor = f"Client {random.randint(100, 999)}"
                    amount = round(random.uniform(5000, 50000), 2)
                    tx_type = 'Credit'
                    category = 'Sales'
                    gl_code = '4000'
                else:
                    # Expense
                    vendor = f"Supplier {random.randint(100, 999)}"
                    amount = round(random.uniform(100, 10000), 2)
                    tx_type = 'Debit'
                    category = random.choice(['Operating Expenses', 'Cost of Goods Sold'])
                    gl_code = ''
                
                transactions.append({
                    'Date': date.strftime('%Y-%m-%d'),
                    'Description': f"Transaction with {vendor}",
                    'Vendor': vendor,
                    'Amount': amount,
                    'Type': tx_type,
                    'Category': '',
                    'GL Code': '',
                    'Expected GL': gl_code
                })
        
        df = pd.DataFrame(transactions).sort_values('Date')
        
        # Save test file
        output_path = self.output_dir / 'schema-enhancement' / 'transactions_partial_gl_codes.xlsx'
        output_path.parent.mkdir(exist_ok=True)
        df.to_excel(output_path, index=False)
        
        # Also create a GL code reference file
        gl_reference = pd.DataFrame([
            {'GL Code': code, 'Category': cat, 'Description': desc}
            for desc, code in GL_CODES.items()
            for cat in ['Sales', 'COGS', 'Operating Expenses', 'Financial Expenses']
            if desc in str(MIS_CATEGORIES)
        ])
        
        ref_path = self.output_dir / 'schema-enhancement' / 'gl_code_reference.xlsx'
        gl_reference.to_excel(ref_path, index=False)
        
        print(f"  ✓ Created schema enhancement test files:")
        print(f"    - {output_path}")
        print(f"    - {ref_path}")
        print(f"    - Transactions with GL codes: {(df['GL Code'] != '').sum()}/{len(df)}")
        
        return df
    
    def generate_vendor_mapping_data(self):
        """Generate vendor categorization mapping test data"""
        print("\nGenerating Vendor Mapping Test Data...")
        
        # Create vendor patterns
        vendor_patterns = {
            'Technology': ['*SOFT*', '*TECH*', '*CLOUD*', '*DIGITAL*', 'AWS', 'GOOGLE', 'MICROSOFT'],
            'Marketing': ['*MARKETING*', '*ADVERTIS*', '*CAMPAIGN*', 'FACEBOOK', 'LINKEDIN'],
            'Utilities': ['*ELECTRIC*', '*WATER*', '*GAS*', '*INTERNET*', '*TELECOM*'],
            'Professional Services': ['*CONSULT*', '*LEGAL*', '*ACCOUNT*', '*AUDIT*', 'LLP', 'CPA'],
            'Travel': ['*AIRLINE*', '*HOTEL*', '*TRAVEL*', 'UBER', 'LYFT'],
            'Office Supplies': ['*SUPPLIES*', '*STATIONERY*', 'STAPLES', 'OFFICE DEPOT']
        }
        
        # Generate transactions with vendor variations
        transactions = []
        vendors_list = []
        
        for category, patterns in vendor_patterns.items():
            for pattern in patterns:
                # Create variations
                if '*' in pattern:
                    base = pattern.replace('*', '')
                    variations = [
                        f"{base} Inc",
                        f"{base} Services",
                        f"{base} Solutions",
                        f"Advanced {base}",
                        f"{base} Pro"
                    ]
                else:
                    variations = [
                        pattern,
                        f"{pattern} INC",
                        f"{pattern} SERVICES",
                        f"{pattern} - Monthly",
                        f"{pattern} Subscription"
                    ]
                
                for vendor in variations[:3]:  # Take 3 variations
                    vendors_list.append({
                        'Vendor Name': vendor,
                        'Category': category,
                        'Pattern': pattern,
                        'Confidence': round(random.uniform(0.8, 1.0), 2)
                    })
        
        # Create vendor mapping file
        vendor_df = pd.DataFrame(vendors_list)
        vendor_path = self.output_dir / 'vendor-mapping' / 'vendor_category_mappings.xlsx'
        vendor_path.parent.mkdir(exist_ok=True)
        vendor_df.to_excel(vendor_path, index=False)
        
        # Generate test transactions using these vendors
        start_date = datetime(2024, 1, 1)
        for i in range(100):
            date = start_date + timedelta(days=random.randint(0, 30))
            vendor_info = random.choice(vendors_list)
            
            amount = round(random.uniform(100, 10000), 2)
            
            transactions.append({
                'Date': date.strftime('%Y-%m-%d'),
                'Description': f"Payment to {vendor_info['Vendor Name']}",
                'Vendor': vendor_info['Vendor Name'],
                'Amount': amount,
                'Type': 'Debit',
                'Expected Category': vendor_info['Category']
            })
        
        tx_df = pd.DataFrame(transactions).sort_values('Date')
        tx_path = self.output_dir / 'vendor-mapping' / 'vendor_test_transactions.xlsx'
        tx_df.to_excel(tx_path, index=False)
        
        print(f"  ✓ Created vendor mapping test files:")
        print(f"    - {vendor_path} ({len(vendor_df)} vendor mappings)")
        print(f"    - {tx_path} ({len(tx_df)} test transactions)")
        
        return vendor_df, tx_df
    
    def create_test_summary(self):
        """Create summary of all test data generated"""
        summary = {
            "generation_date": datetime.now().isoformat(),
            "test_scenarios": {
                "quick_setup": {
                    "description": "5-minute MIS setup validation",
                    "files": ["test_transactions_uncategorized.xlsx", "test_transactions_uncategorized.csv"],
                    "expected_accuracy": "87% baseline"
                },
                "historical_enhancement": {
                    "description": "Pattern recognition from historical data",
                    "files": ["historical_transactions_with_patterns.xlsx"],
                    "expected_improvement": "+15-20% accuracy"
                },
                "schema_enhancement": {
                    "description": "GL code mapping and compliance",
                    "files": ["transactions_partial_gl_codes.xlsx", "gl_code_reference.xlsx"],
                    "expected_improvement": "+20% accuracy"
                },
                "vendor_mapping": {
                    "description": "Vendor categorization patterns",
                    "files": ["vendor_category_mappings.xlsx", "vendor_test_transactions.xlsx"],
                    "expected_improvement": "+5-10% accuracy"
                }
            },
            "total_accuracy_target": "95%+ with all enhancements"
        }
        
        summary_path = self.output_dir / 'mis_test_data_summary.json'
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n✓ Created test data summary: {summary_path}")
    
    def generate_all(self):
        """Generate all MIS test datasets"""
        print("\n" + "="*60)
        print("MIS Test Data Generation")
        print("="*60)
        
        # Generate all test data types
        self.generate_quick_setup_data()
        self.generate_historical_enhancement_data()
        self.generate_schema_enhancement_data()
        self.generate_vendor_mapping_data()
        self.create_test_summary()
        
        print("\n" + "="*60)
        print("✅ MIS test data generation complete!")
        print(f"📁 Output directory: {self.output_dir}")
        print("="*60)


def main():
    generator = MISTestDataGenerator()
    generator.generate_all()


if __name__ == "__main__":
    main()