#!/bin/bash
# Comprehensive log viewer for giki.ai

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Function to display usage
usage() {
    echo "Usage: $0 [serve|deploy|lint|test|build|all|status]"
    echo "  serve  - Show server logs (API and Frontend)"
    echo "  deploy - Show deployment logs"
    echo "  lint   - Show linting logs (API and Frontend)"
    echo "  test   - Show testing logs (API and Frontend)"
    echo "  build  - Show build logs"
    echo "  all    - Show all logs"
    echo "  status - Show server status and latest logs"
    echo ""
    echo "Available log files:"
    echo "  logs/serve-api.log         - API server output"
    echo "  logs/serve-app.log         - Frontend server output"
    echo "  logs/lint-api.log          - API linting output"
    echo "  logs/lint-app.log          - Frontend linting output"
    echo "  logs/test-api.log          - API testing output"
    echo "  logs/test-app.log          - Frontend testing output"
    echo "  logs/test-visual.log       - Visual regression testing output"
    echo "  logs/test-e2e.log          - E2E testing output"
    echo "  logs/build-app.log         - Frontend build output"
    echo "  logs/deploy-api-latest.log - Latest API deployment"
    echo "  logs/deploy-app-latest.log - Latest frontend deployment"
    exit 1
}

# Default to status if no argument
if [ $# -eq 0 ]; then
    MODE="status"
else
    MODE=$1
fi

# Show logs based on mode
case $MODE in
    serve)
        echo "📝 Following server logs (API and Frontend)..."
        echo "Press Ctrl+C to exit"
        echo ""
        
        # Create empty log files if they don't exist
        touch "$WORKSPACE_ROOT/logs/serve-api.log" "$WORKSPACE_ROOT/logs/serve-app.log"
        
        tail -f "$WORKSPACE_ROOT/logs/serve-api.log" "$WORKSPACE_ROOT/logs/serve-app.log"
        ;;
        
    deploy)
        echo "📝 Following deployment logs..."
        echo "Press Ctrl+C to exit"
        echo ""
        
        # Check if deployment logs exist
        if [ -f "$WORKSPACE_ROOT/logs/deploy-api-latest.log" ] || [ -f "$WORKSPACE_ROOT/logs/deploy-app-latest.log" ]; then
            tail -f "$WORKSPACE_ROOT/logs/deploy-api-latest.log" "$WORKSPACE_ROOT/logs/deploy-app-latest.log" 2>/dev/null
        else
            echo "No deployment logs found yet."
            echo "Run 'pnpm deploy:api' or 'pnpm deploy:app' to create deployment logs."
        fi
        ;;
        
    lint)
        echo "🔍 Following linting logs (API and Frontend)..."
        echo "Press Ctrl+C to exit"
        echo ""
        
        # Create empty log files if they don't exist
        touch "$WORKSPACE_ROOT/logs/lint-api.log" "$WORKSPACE_ROOT/logs/lint-app.log"
        
        tail -f "$WORKSPACE_ROOT/logs/lint-api.log" "$WORKSPACE_ROOT/logs/lint-app.log"
        ;;
        
    test)
        echo "🧪 Following testing logs (API, Frontend, Visual, E2E)..."
        echo "Press Ctrl+C to exit"
        echo ""
        
        # Create empty log files if they don't exist
        touch "$WORKSPACE_ROOT/logs/test-api.log" "$WORKSPACE_ROOT/logs/test-app.log"
        touch "$WORKSPACE_ROOT/logs/test-visual.log" "$WORKSPACE_ROOT/logs/test-e2e.log"
        
        tail -f "$WORKSPACE_ROOT/logs/test-api.log" "$WORKSPACE_ROOT/logs/test-app.log" "$WORKSPACE_ROOT/logs/test-visual.log" "$WORKSPACE_ROOT/logs/test-e2e.log"
        ;;
        
    build)
        echo "🏗️  Following build logs..."
        echo "Press Ctrl+C to exit"
        echo ""
        
        # Create empty log file if it doesn't exist
        touch "$WORKSPACE_ROOT/logs/build-app.log"
        
        tail -f "$WORKSPACE_ROOT/logs/build-app.log"
        ;;
        
    all)
        echo "📝 Following all logs..."
        echo "Press Ctrl+C to exit"
        echo ""
        
        # Create empty log files if they don't exist
        touch "$WORKSPACE_ROOT/logs/serve-api.log" "$WORKSPACE_ROOT/logs/serve-app.log"
        touch "$WORKSPACE_ROOT/logs/lint-api.log" "$WORKSPACE_ROOT/logs/lint-app.log"
        touch "$WORKSPACE_ROOT/logs/test-api.log" "$WORKSPACE_ROOT/logs/test-app.log"
        touch "$WORKSPACE_ROOT/logs/build-app.log"
        
        tail -f "$WORKSPACE_ROOT/logs/"*.log 2>/dev/null
        ;;
        
    status)
        echo "🔍 giki.ai Status Report"
        echo "========================"
        echo ""
        
        echo "=== Server Status ==="
        echo "API (port 8000):"
        if lsof -i :8000 >/dev/null 2>&1; then
            echo "  ✅ Running"
            echo "  PID: $(cat "$WORKSPACE_ROOT/logs/serve-api.pid" 2>/dev/null || echo "unknown")"
        else
            echo "  ❌ Not running"
        fi
        
        echo "Frontend (port 4200):"
        if lsof -i :4200 >/dev/null 2>&1; then
            echo "  ✅ Running"
            echo "  PID: $(cat "$WORKSPACE_ROOT/logs/serve-app.pid" 2>/dev/null || echo "unknown")"
        else
            echo "  ❌ Not running"
        fi
        
        echo ""
        echo "=== Latest Server Logs ==="
        
        # Show API logs
        echo "📝 API Server (last 10 lines):"
        if [ -f "$WORKSPACE_ROOT/logs/serve-api.log" ]; then
            tail -10 "$WORKSPACE_ROOT/logs/serve-api.log" | sed 's/^/  /'
        else
            echo "  No logs found"
        fi
        
        echo ""
        echo "📝 Frontend Server (last 10 lines):"
        if [ -f "$WORKSPACE_ROOT/logs/serve-app.log" ]; then
            tail -10 "$WORKSPACE_ROOT/logs/serve-app.log" | sed 's/^/  /'
        else
            echo "  No logs found"
        fi
        
        echo ""
        echo "=== Latest Deployment Status ==="
        
        # Check API deployment
        if [ -f "$WORKSPACE_ROOT/logs/deploy-api-latest.log" ]; then
            echo "🚀 API Deployment:"
            grep -E "(=== API Deployment|✅|❌)" "$WORKSPACE_ROOT/logs/deploy-api-latest.log" | tail -3 | sed 's/^/  /'
        else
            echo "🚀 API Deployment: No deployments yet"
        fi
        
        echo ""
        
        # Check Frontend deployment
        if [ -f "$WORKSPACE_ROOT/logs/deploy-app-latest.log" ]; then
            echo "🎨 Frontend Deployment:"
            grep -E "(=== Frontend Deployment|✅|❌)" "$WORKSPACE_ROOT/logs/deploy-app-latest.log" | tail -3 | sed 's/^/  /'
        else
            echo "🎨 Frontend Deployment: No deployments yet"
        fi
        
        echo ""
        echo "💡 Tips:"
        echo "  - Use 'pnpm logs serve' to follow server logs"
        echo "  - Use 'pnpm logs lint' to follow linting logs"
        echo "  - Use 'pnpm logs test' to follow testing logs"
        echo "  - Use 'pnpm logs build' to follow build logs"
        echo "  - Use 'pnpm logs deploy' to follow deployment logs"
        echo "  - Use 'pnpm serve' to start both servers"
        echo "  - Use background scripts: lint-api-bg.sh, test-app-bg.sh, etc."
        ;;
        
    *)
        usage
        ;;
esac