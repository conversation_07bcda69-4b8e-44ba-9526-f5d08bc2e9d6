#!/bin/bash

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

# Kill any existing lint processes
pkill -f "ruff check" 2>/dev/null || true

echo "🔍 Starting API linting in background..."

# Start API linting in background with logging (overwrite each time)
cd "$WORKSPACE_ROOT/apps/giki-ai-api"
(uv run ruff check src/ > "$WORKSPACE_ROOT/logs/lint-api.log" 2>&1) &
LINT_PID=$!

echo "✅ API linting started (PID: $LINT_PID)"
echo "📝 Logs: logs/lint-api.log"
echo $LINT_PID > "$WORKSPACE_ROOT/logs/lint-api.pid"

# Show initial status
sleep 1
if kill -0 $LINT_PID 2>/dev/null; then
    echo "⏳ Linting in progress... Use 'tail -f logs/lint-api.log' to follow"
else
    echo "✅ Linting completed quickly - check logs/lint-api.log for results"
fi