#!/bin/bash

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

# Kill any existing build processes
pkill -f "vite build" 2>/dev/null || true

echo "🏗️  Starting frontend build in background..."

# Start frontend build in background with logging (overwrite each time)
cd "$WORKSPACE_ROOT"
(pnpm design-check && (cd apps/giki-ai-app && pnpm run build) > "$WORKSPACE_ROOT/logs/build-app.log" 2>&1) &
BUILD_PID=$!

echo "✅ Frontend build started (PID: $BUILD_PID)"
echo "📝 Logs: logs/build-app.log"
echo $BUILD_PID > "$WORKSPACE_ROOT/logs/build-app.pid"

# Show initial status
sleep 2
if kill -0 $BUILD_PID 2>/dev/null; then
    echo "⏳ Build in progress... Use 'tail -f logs/build-app.log' to follow"
else
    echo "✅ Build completed quickly - check logs/build-app.log for results"
fi