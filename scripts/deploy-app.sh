#!/bin/bash
# Deploy frontend to Firebase with proper logging
set -e

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Check for dry-run mode
DRY_RUN=false
if [ "$1" = "--dry-run" ]; then
    DRY_RUN=true
fi

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

# Timestamp for this deployment
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
LOG_FILE="$WORKSPACE_ROOT/logs/deploy-app-$TIMESTAMP.log"
LATEST_LOG="$WORKSPACE_ROOT/logs/deploy-app-latest.log"

echo "🎨 Deploying frontend to Firebase Hosting..."
echo "📝 Deployment logs: logs/deploy-app-latest.log"
echo "📝 Timestamped logs: logs/deploy-app-$TIMESTAMP.log"

# Function to log output
log_output() {
    tee -a "$LOG_FILE" | tee "$LATEST_LOG"
}

# Start deployment with logging
{
    echo "=== Frontend Deployment Started at $(date) ===" 
    # Load production environment variables for deployment
    if [ -f "$WORKSPACE_ROOT/infrastructure/environments/production/.env.production" ]; then
        set -a  # automatically export all variables
        source "$WORKSPACE_ROOT/infrastructure/environments/production/.env.production"
        set +a  # stop automatically exporting
    fi
    
    echo "Project: rezolve-poc"
    echo "Site: app-giki-ai"
    echo "Environment: production"
    echo ""
    
    # Build frontend
    echo "📦 Building frontend..."
    cd "$WORKSPACE_ROOT/apps/giki-ai-app"
    pnpm run build
    
    echo ""
    echo "🌐 Deploying to Firebase Hosting..."
    cd "$WORKSPACE_ROOT"
    
    if [ "$DRY_RUN" = true ]; then
        echo "🧪 DRY RUN MODE - Simulating deployment..."
        echo "Would run: firebase deploy --only hosting --project rezolve-poc"
        echo "Checking firebase service account exists..."
        if [ -f "$WORKSPACE_ROOT/infrastructure/service-accounts/development/firebase-admin-key.json" ]; then
            echo "✅ Firebase service account found"
        else
            echo "❌ Firebase service account not found at infrastructure/service-accounts/development/firebase-admin-key.json!"
            exit 1
        fi
        echo "Checking build output exists..."
        if [ -d "$WORKSPACE_ROOT/apps/giki-ai-app/dist" ]; then
            echo "✅ Build output found in apps/giki-ai-app/dist"
        else
            echo "❌ Build output not found!"
            exit 1
        fi
        echo "🧪 DRY RUN - Deployment simulation complete"
    else
        # Deploy using service account
        export GOOGLE_APPLICATION_CREDENTIALS="$WORKSPACE_ROOT/infrastructure/service-accounts/development/firebase-admin-key.json"
        firebase deploy --only hosting --project rezolve-poc
    fi
    
    echo ""
    echo "=== Frontend Deployment Completed at $(date) ==="
    
} 2>&1 | log_output

# Check if deployment succeeded
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo "✅ Frontend deployment successful!"
    echo "🌐 Live at: https://app-giki-ai.web.app"
else
    echo "❌ Frontend deployment failed! Check logs/deploy-app-latest.log for details"
    exit 1
fi