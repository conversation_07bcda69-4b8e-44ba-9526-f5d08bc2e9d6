#!/bin/bash

# Debug script for categorization workflow
set -e

API_BASE="http://localhost:8000"
TEST_USER="<EMAIL>"
TEST_PASSWORD="GikiTest2025Secure"
TEST_FILE="/Users/<USER>/giki-ai-workspace/libs/test-data/mis-testing/test_transactions_small.csv"

echo "=== Debug Categorization Workflow ==="

# Step 1: Get token
echo "1. Getting authentication token..."
TOKEN_RESPONSE=$(curl -s -X POST "$API_BASE/api/v1/auth/token" -d "username=$TEST_USER&password=$TEST_PASSWORD")
echo "Token response: $TOKEN_RESPONSE"

TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')
echo "Token: $TOKEN"

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ Authentication failed"
    exit 1
fi

echo "✅ Authentication successful"

# Step 2: Upload file
echo "2. Uploading test file..."
UPLOAD_RESPONSE=$(curl -s -X POST "$API_BASE/api/v1/files/upload" \
    -H "Authorization: Bearer $TOKEN" \
    -F "files=@$TEST_FILE")

echo "Upload response: $UPLOAD_RESPONSE"

UPLOAD_ID=$(echo "$UPLOAD_RESPONSE" | jq -r '.uploads[0].upload_id')
echo "Upload ID: $UPLOAD_ID"

if [ "$UPLOAD_ID" = "null" ] || [ -z "$UPLOAD_ID" ]; then
    echo "❌ File upload failed"
    exit 1
fi

echo "✅ File uploaded successfully - ID: $UPLOAD_ID"

# Step 3: Get interpretation
echo "3. Getting schema interpretation..."
sleep 3

INTERPRETATION_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
    "$API_BASE/api/v1/files/$UPLOAD_ID/interpretation")

echo "Interpretation response: $INTERPRETATION_RESPONSE"

CONFIDENCE=$(echo "$INTERPRETATION_RESPONSE" | jq -r '.overall_confidence')
echo "Confidence: $CONFIDENCE"

if [ "$CONFIDENCE" = "null" ] || [ -z "$CONFIDENCE" ]; then
    echo "❌ Schema interpretation failed"
    exit 1
fi

echo "✅ Schema interpretation successful - Confidence: $CONFIDENCE"

# Step 4: Test column mapping
echo "4. Testing column mapping..."
MAPPING_RESPONSE=$(curl -s -X POST "$API_BASE/api/v1/files/$UPLOAD_ID/map" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"column_mappings": {"Date": "date", "Description": "description", "Amount": "amount", "Vendor": "vendor"}}')

echo "Mapping response: $MAPPING_RESPONSE"

PROCESSED_COUNT=$(echo "$MAPPING_RESPONSE" | jq -r '.records_processed // 0')
echo "Processed: $PROCESSED_COUNT"

if [ "$PROCESSED_COUNT" = "0" ]; then
    echo "❌ Column mapping failed"
    exit 1
fi

echo "✅ Column mapping successful - Processed: $PROCESSED_COUNT transactions"

# Step 5: Check transactions
echo "5. Checking transactions..."
TRANSACTIONS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
    "$API_BASE/api/v1/transactions/fast?limit=10")

echo "Transactions response: $TRANSACTIONS_RESPONSE"

TX_COUNT=$(echo "$TRANSACTIONS_RESPONSE" | jq '.items | length // 0')
echo "Transaction count: $TX_COUNT"

echo "✅ Found $TX_COUNT transactions"

echo ""
echo "=== ALL TESTS PASSED ==="
echo "✅ Authentication - WORKING"
echo "✅ File upload - WORKING"
echo "✅ Schema interpretation - WORKING"
echo "✅ Column mapping - WORKING"
echo "✅ Transaction creation - WORKING"
echo ""
echo "🎉 Categorization workflow is working correctly!"