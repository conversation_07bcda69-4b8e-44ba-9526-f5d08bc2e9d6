#!/bin/bash

# Update OpenAPI Specification Script
# Usage: ./scripts/update-openapi-spec.sh
# Purpose: Fetch latest OpenAPI spec from running FastAPI backend

set -e

echo "🔄 Updating OpenAPI specification..."

# Check if backend is running
if ! curl -s http://localhost:8000/health > /dev/null; then
    echo "❌ Backend not running at http://localhost:8000"
    echo "💡 Run: pnpm nx serve:api"
    exit 1
fi

# Fetch OpenAPI spec
echo "📥 Fetching OpenAPI spec from http://localhost:8000/openapi.json"
curl -s http://localhost:8000/openapi.json | jq . > docs/api/openapi.json

# Verify the spec was created
if [ -f "docs/api/openapi.json" ]; then
    SPEC_SIZE=$(wc -c < docs/api/openapi.json)
    ENDPOINT_COUNT=$(jq '.paths | keys | length' docs/api/openapi.json)
    echo "✅ OpenAPI spec updated successfully"
    echo "📊 File size: ${SPEC_SIZE} bytes"
    echo "🔗 Endpoints documented: ${ENDPOINT_COUNT}"
    echo "📝 Spec version: $(jq -r '.info.version' docs/api/openapi.json)"
else
    echo "❌ Failed to create OpenAPI specification"
    exit 1
fi

echo ""
echo "🎯 Next steps:"
echo "   • Import docs/api/openapi.json into Postman/Insomnia"
echo "   • Use for API client generation"
echo "   • Share with frontend developers for type generation"
echo ""
echo "💡 Tip: Add this script to your git pre-commit hook for auto-sync"