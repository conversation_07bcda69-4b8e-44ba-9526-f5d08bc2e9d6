#!/bin/bash

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

# Kill any existing lint processes
pkill -f "eslint" 2>/dev/null || true

echo "🔍 Starting frontend linting in background..."

# Start frontend linting in background with logging (overwrite each time)
cd "$WORKSPACE_ROOT/apps/giki-ai-app"
(pnpm run lint > "$WORKSPACE_ROOT/logs/lint-app.log" 2>&1) &
LINT_PID=$!

echo "✅ Frontend linting started (PID: $LINT_PID)"
echo "📝 Logs: logs/lint-app.log"
echo $LINT_PID > "$WORKSPACE_ROOT/logs/lint-app.pid"

# Show initial status
sleep 1
if kill -0 $LINT_PID 2>/dev/null; then
    echo "⏳ Linting in progress... Use 'tail -f logs/lint-app.log' to follow"
else
    echo "✅ <PERSON><PERSON> completed quickly - check logs/lint-app.log for results"
fi