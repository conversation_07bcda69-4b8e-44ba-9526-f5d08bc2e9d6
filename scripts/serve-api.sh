#!/bin/bash

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Kill any existing process on port 8000
lsof -ti:8000 | xargs kill -9 2>/dev/null || true

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

# Load development environment variables
echo "🔧 Loading development environment..."
if [ -f "$WORKSPACE_ROOT/infrastructure/environments/development/.env.development" ]; then
    set -a  # automatically export all variables
    source "$WORKSPACE_ROOT/infrastructure/environments/development/.env.development"
    set +a  # stop automatically exporting
    echo "✅ Development environment loaded"
else
    echo "⚠️  Development environment file not found at infrastructure/environments/development/.env.development"
fi

# Ensure PostgreSQL is running
echo "🔍 Checking PostgreSQL..."
if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
    echo "🚀 Starting PostgreSQL..."
    if command -v brew >/dev/null 2>&1; then
        brew services start postgresql@15 >/dev/null 2>&1 || brew services start postgresql >/dev/null 2>&1
        sleep 3
    fi
    
    if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
        echo "❌ Could not start PostgreSQL automatically"
        echo "💡 Please start PostgreSQL manually: brew services start postgresql@15"
        exit 1
    fi
fi
echo "✅ PostgreSQL is running"

# Ensure Redis is running (if needed)
echo "🔍 Checking Redis..."
if ! redis-cli ping >/dev/null 2>&1; then
    echo "🚀 Starting Redis..."
    if command -v brew >/dev/null 2>&1; then
        brew services start redis >/dev/null 2>&1
        sleep 2
    fi
    
    if ! redis-cli ping >/dev/null 2>&1; then
        echo "⚠️  Redis not available - continuing without Redis (some features may be limited)"
    else
        echo "✅ Redis is running"
    fi
else
    echo "✅ Redis is running"
fi

# Start API server in background with logging
cd "$WORKSPACE_ROOT/apps/giki-ai-api"
(uv run python -m uvicorn giki_ai_api.main:app --reload --reload-dir src --host 0.0.0.0 --port 8000 > "$WORKSPACE_ROOT/logs/serve-api.log" 2>&1) &
API_PID=$!

echo "✅ API server started on port 8000 (PID: $API_PID)"
echo "📝 Logs: logs/serve-api.log"
echo $API_PID > "$WORKSPACE_ROOT/logs/serve-api.pid"