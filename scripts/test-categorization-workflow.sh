#!/bin/bash

# Categorization Workflow Testing Script
# Tests the complete transaction categorization pipeline from upload to final categories

set -euo pipefail

# Configuration
API_BASE="http://localhost:8000"
TEST_USER="<EMAIL>"
TEST_PASSWORD="GikiTest2025Secure"
TEST_FILE="/Users/<USER>/giki-ai-workspace/libs/test-data/mis-testing/test_transactions_small.csv"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get authentication token
get_auth_token() {
    log_info "Getting authentication token..."
    local token_response
    token_response=$(curl -s -X POST "$API_BASE/api/v1/auth/token" \
        -d "username=$TEST_USER&password=$TEST_PASSWORD")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to get authentication token"
        return 1
    fi
    
    local token
    token=$(echo "$token_response" | jq -r '.access_token')
    if [ "$token" = "null" ] || [ "$token" = "" ]; then
        log_error "Authentication failed: $token_response"
        return 1
    fi
    
    echo "$token"
}

# Test complete categorization workflow
test_categorization_workflow() {
    local token="$1"
    
    log_info "=== Testing Complete Categorization Workflow ==="
    
    # Step 1: Upload file
    log_info "Step 1: Uploading test file..."
    local upload_response
    upload_response=$(curl -s -X POST "$API_BASE/api/v1/files/upload" \
        -H "Authorization: Bearer $token" \
        -F "files=@$TEST_FILE")
    
    if [ $? -ne 0 ]; then
        log_error "File upload failed - curl error"
        return 1
    fi
    
    # Extract upload ID
    local upload_id
    upload_id=$(echo "$upload_response" | jq -r '.uploads[0].upload_id')
    
    if [ -z "$upload_id" ] || [ "$upload_id" = "null" ]; then
        log_error "No upload_id in response: $upload_response"
        return 1
    fi
    
    log_success "File uploaded - ID: $upload_id"
    
    # Step 2: Wait for processing and get schema interpretation
    log_info "Step 2: Getting schema interpretation..."
    sleep 3
    
    local interpretation_response
    interpretation_response=$(curl -s -H "Authorization: Bearer $token" \
        "$API_BASE/api/v1/files/$upload_id/interpretation")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to get interpretation - curl error"
        return 1
    fi
    
    # Check interpretation success
    local interp_error
    interp_error=$(echo "$interpretation_response" | jq -r '.detail // empty')
    if [ -n "$interp_error" ]; then
        log_error "Interpretation failed: $interp_error"
        return 1
    fi
    
    local confidence
    confidence=$(echo "$interpretation_response" | jq -r '.overall_confidence')
    log_success "Schema interpretation successful - Confidence: $confidence"
    
    # Step 3: Confirm column mapping and process transactions
    log_info "Step 3: Confirming column mapping and processing transactions..."
    
    # Extract column mappings for confirmation
    local column_mappings
    column_mappings=$(echo "$interpretation_response" | jq '.column_mappings')
    
    # Build mapping confirmation payload
    local mapping_payload
    mapping_payload=$(echo "$column_mappings" | jq '{
        column_mappings: (
            reduce .[] as $item ({}; 
                .[$item.source_column] = $item.target_field
            )
        )
    }')
    
    log_info "Mapping payload: $mapping_payload"
    
    # Confirm mapping and process
    local mapping_response
    mapping_response=$(curl -s -X POST "$API_BASE/api/v1/files/$upload_id/map" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -d "$mapping_payload")
    
    if [ $? -ne 0 ]; then
        log_error "Column mapping confirmation failed - curl error"
        return 1
    fi
    
    # Check mapping success
    local mapping_error
    mapping_error=$(echo "$mapping_response" | jq -r '.detail // empty')
    if [ -n "$mapping_error" ]; then
        log_error "Column mapping failed: $mapping_error"
        return 1
    fi
    
    local processed_count
    processed_count=$(echo "$mapping_response" | jq -r '.processed_transactions // 0')
    local categorized_count
    categorized_count=$(echo "$mapping_response" | jq -r '.categorized_transactions // 0')
    
    log_success "Transactions processed: $processed_count, Categorized: $categorized_count"
    
    # Step 4: Verify transactions were created and categorized
    log_info "Step 4: Verifying transaction categorization..."
    
    local transactions_response
    transactions_response=$(curl -s -H "Authorization: Bearer $token" \
        "$API_BASE/api/v1/transactions?upload_id=$upload_id&limit=10")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to get transactions - curl error"
        return 1
    fi
    
    # Check if we got transactions
    local tx_count
    tx_count=$(echo "$transactions_response" | jq '.transactions | length // 0')
    
    if [ "$tx_count" -eq 0 ]; then
        log_error "No transactions found for upload ID: $upload_id"
        return 1
    fi
    
    log_success "Found $tx_count transactions"
    
    # Step 5: Analyze categorization results
    log_info "Step 5: Analyzing categorization results..."
    
    local categorized_tx
    categorized_tx=$(echo "$transactions_response" | jq '[.transactions[] | select(.ai_category != null and .ai_category != "Uncategorized")] | length')
    
    local uncategorized_tx
    uncategorized_tx=$(echo "$transactions_response" | jq '[.transactions[] | select(.ai_category == null or .ai_category == "Uncategorized")] | length')
    
    local categorization_rate
    if [ "$tx_count" -gt 0 ]; then
        categorization_rate=$(echo "scale=2; $categorized_tx * 100 / $tx_count" | bc)
    else
        categorization_rate=0
    fi
    
    log_info "Categorization Analysis:"
    log_info "  Total transactions: $tx_count"
    log_info "  Categorized: $categorized_tx"
    log_info "  Uncategorized: $uncategorized_tx"
    log_info "  Categorization rate: ${categorization_rate}%"
    
    # Step 6: Show sample categorized transactions
    log_info "Step 6: Sample categorized transactions:"
    echo "$transactions_response" | jq -r '.transactions[] | select(.ai_category != null and .ai_category != "Uncategorized") | "  - \(.description) → \(.ai_category) (conf: \(.ai_confidence // "N/A"))"' | head -5
    
    # Step 7: Test category breakdown
    log_info "Step 7: Testing category breakdown..."
    
    local category_response
    category_response=$(curl -s -H "Authorization: Bearer $token" \
        "$API_BASE/api/v1/reports/spending-by-category")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to get category breakdown - curl error"
        return 1
    fi
    
    local category_count
    category_count=$(echo "$category_response" | jq '.categories | length // 0')
    
    log_success "Category breakdown shows $category_count categories"
    
    # Success criteria
    if [ "$categorization_rate" = "0.00" ]; then
        log_error "FAILED: No transactions were categorized (0% rate)"
        return 1
    elif (( $(echo "$categorization_rate < 50" | bc -l) )); then
        log_warning "LOW: Categorization rate below 50% ($categorization_rate%)"
        return 1
    else
        log_success "PASSED: Categorization rate acceptable ($categorization_rate%)"
        return 0
    fi
}

# Main function
main() {
    echo "=================================="
    echo "Categorization Workflow Testing"
    echo "=================================="
    
    # Check test file exists
    if [ ! -f "$TEST_FILE" ]; then
        log_error "Test file not found: $TEST_FILE"
        exit 1
    fi
    
    # Get authentication token
    local token
    token=$(get_auth_token)
    if [ $? -ne 0 ]; then
        exit 1
    fi
    
    log_success "Authentication successful"
    
    # Test categorization workflow
    if test_categorization_workflow "$token"; then
        echo ""
        echo "=================================="
        log_success "Categorization Workflow Test PASSED"
        echo "=================================="
        return 0
    else
        echo ""
        echo "=================================="
        log_error "Categorization Workflow Test FAILED"
        echo "=================================="
        return 1
    fi
}

# Check dependencies
if ! command -v jq &> /dev/null; then
    log_error "jq is required but not installed. Please install jq first."
    exit 1
fi

if ! command -v curl &> /dev/null; then
    log_error "curl is required but not installed. Please install curl first."
    exit 1
fi

if ! command -v bc &> /dev/null; then
    log_error "bc is required but not installed. Please install bc first."
    exit 1
fi

# Run main function
main "$@"