#!/bin/bash

# Schema Interpretation Testing Script
# Tests the AI-powered schema interpretation system for file uploads

set -euo pipefail

# Configuration
API_BASE="http://localhost:8000"
TEST_USER="<EMAIL>"
TEST_PASSWORD="GikiTest2025Secure"
TEST_FILES_DIR="/Users/<USER>/giki-ai-workspace/libs/test-data/synthetic"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get authentication token
get_auth_token() {
    log_info "Getting authentication token..."
    local token_response
    token_response=$(curl -s -X POST "$API_BASE/api/v1/auth/token" \
        -d "username=$TEST_USER&password=$TEST_PASSWORD")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to get authentication token"
        return 1
    fi
    
    local token
    token=$(echo "$token_response" | jq -r '.access_token')
    if [ "$token" = "null" ] || [ "$token" = "" ]; then
        log_error "Authentication failed: $token_response"
        return 1
    fi
    
    echo "$token"
}

# Test schema interpretation for a specific file format
test_schema_interpretation() {
    local file_path="$1"
    local file_description="$2"
    local token="$3"
    
    log_info "Testing schema interpretation for: $file_description"
    log_info "File: $(basename "$file_path")"
    
    if [ ! -f "$file_path" ]; then
        log_error "Test file not found: $file_path"
        return 1
    fi
    
    # Upload the file
    log_info "Uploading file for interpretation..."
    local upload_response
    upload_response=$(curl -s -X POST "$API_BASE/api/v1/files/upload" \
        -H "Authorization: Bearer $token" \
        -F "files=@$file_path")
    
    if [ $? -ne 0 ]; then
        log_error "File upload failed - curl error"
        return 1
    fi
    
    # Check for upload errors
    local error_message
    error_message=$(echo "$upload_response" | jq -r '.detail // empty')
    if [ -n "$error_message" ]; then
        log_error "Upload failed: $error_message"
        return 1
    fi
    
    # Extract upload information
    local upload_id
    upload_id=$(echo "$upload_response" | jq -r '.uploads[0].upload_id // .upload_id // empty')
    
    if [ -z "$upload_id" ]; then
        log_error "No upload_id in response"
        return 1
    fi
    
    log_success "File uploaded - ID: $upload_id"
    
    # Wait for processing
    log_info "Waiting for schema interpretation..."
    sleep 3
    
    # Test interpretation endpoint
    log_info "Getting schema interpretation..."
    local interpretation_response
    interpretation_response=$(curl -s -H "Authorization: Bearer $token" \
        "$API_BASE/api/v1/files/$upload_id/interpretation")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to get interpretation - curl error"
        return 1
    fi
    
    # Check for interpretation errors
    local interp_error
    interp_error=$(echo "$interpretation_response" | jq -r '.detail // empty')
    if [ -n "$interp_error" ]; then
        log_error "Interpretation failed: $interp_error"
        return 1
    fi
    
    # Validate interpretation structure
    local headers
    headers=$(echo "$interpretation_response" | jq -r '[.column_mappings[].source_column] | join(",") // empty')
    local column_mappings
    column_mappings=$(echo "$interpretation_response" | jq -r '.column_mappings // empty')
    local confidence
    confidence=$(echo "$interpretation_response" | jq -r '.overall_confidence // empty')
    local schema_type
    schema_type="bank_statement"  # Default value since API doesn't return this
    
    # Check required fields
    if [ -z "$headers" ] || [ "$headers" = "null" ]; then
        log_error "No headers found in interpretation"
        return 1
    fi
    
    if [ -z "$column_mappings" ] || [ "$column_mappings" = "null" ]; then
        log_error "No column mappings found in interpretation"
        return 1
    fi
    
    # Parse and display results
    local header_count
    header_count=$(echo "$interpretation_response" | jq '.column_mappings | length // 0')
    local mapping_count  
    mapping_count=$(echo "$interpretation_response" | jq '.column_mappings | length // 0')
    
    log_success "Schema interpretation successful!"
    log_info "  Headers detected: $header_count"
    log_info "  Column mappings: $mapping_count" 
    log_info "  Schema type: $schema_type"
    log_info "  Confidence: $confidence"
    
    # Show sample headers
    if [ "$header_count" -gt 0 ]; then
        log_info "  Sample headers:"
        echo "$interpretation_response" | jq -r '.column_mappings[0:3][].source_column' | sed 's/^/    - /'
    fi
    
    # Show sample mappings
    if [ "$mapping_count" -gt 0 ]; then
        log_info "  Sample mappings:"
        echo "$interpretation_response" | jq -r '.column_mappings[0:3][] | "    - \(.source_column) → \(.target_field)"'
    fi
    
    echo "$upload_id" # Return upload_id for further testing
}

# Test column mapping confirmation
test_column_mapping() {
    local upload_id="$1"
    local token="$2"
    
    log_info "Testing column mapping confirmation for upload: $upload_id"
    
    # Get the interpretation first
    local interpretation_response
    interpretation_response=$(curl -s -H "Authorization: Bearer $token" \
        "$API_BASE/api/v1/files/$upload_id/interpretation")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to get interpretation for mapping test"
        return 1
    fi
    
    # Extract column mappings
    local mappings
    mappings=$(echo "$interpretation_response" | jq '.column_mappings // {}')
    
    if [ "$mappings" = "{}" ] || [ "$mappings" = "null" ]; then
        log_warning "No column mappings available for confirmation test"
        return 0
    fi
    
    # Test column mapping confirmation endpoint
    log_info "Testing column mapping confirmation..."
    local mapping_response
    mapping_response=$(curl -s -X POST "$API_BASE/api/v1/files/$upload_id/map" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -d "{\"column_mappings\": $mappings}")
    
    if [ $? -ne 0 ]; then
        log_error "Column mapping confirmation failed - curl error"
        return 1
    fi
    
    # Check for mapping errors
    local mapping_error
    mapping_error=$(echo "$mapping_response" | jq -r '.detail // empty')
    if [ -n "$mapping_error" ]; then
        log_error "Column mapping failed: $mapping_error"
        return 1
    fi
    
    # Check success
    local success
    success=$(echo "$mapping_response" | jq -r '.success // false')
    if [ "$success" = "true" ]; then
        log_success "Column mapping confirmation successful"
        
        # Show processing results
        local processed_count
        processed_count=$(echo "$mapping_response" | jq -r '.processed_transactions // 0')
        local categorized_count
        categorized_count=$(echo "$mapping_response" | jq -r '.categorized_transactions // 0')
        
        log_info "  Processed transactions: $processed_count"
        log_info "  Categorized transactions: $categorized_count"
        
        return 0
    else
        log_warning "Column mapping returned success=false"
        return 1
    fi
}

# Main testing function
main() {
    echo "=================================="
    echo "Schema Interpretation Testing"
    echo "=================================="
    
    # Get authentication token
    local token
    token=$(get_auth_token)
    if [ $? -ne 0 ]; then
        exit 1
    fi
    
    log_success "Authentication successful"
    
    # Test different file formats
    declare -a test_files=(
        "$TEST_FILES_DIR/indian-banks/hdfc_bank_mixed_1000tx.xlsx|HDFC Bank Statement (Excel)"
        "$TEST_FILES_DIR/us-banks/chase_mixed_500tx.xlsx|Chase Bank Excel"
        "$TEST_FILES_DIR/credit-cards/visa_credit_card_300tx.xlsx|Visa Credit Card Statement"
        "/Users/<USER>/giki-ai-workspace/libs/test-data/mis-testing/quick-setup/test_transactions_uncategorized.csv|MIS Quick Setup CSV"
    )
    
    local total_tests=0
    local passed_tests=0
    local upload_ids=()
    
    # Test each file format
    for test_case in "${test_files[@]}"; do
        IFS='|' read -r file_path description <<< "$test_case"
        
        if [ -f "$file_path" ]; then
            total_tests=$((total_tests + 1))
            echo ""
            log_info "=== Test $total_tests: $description ==="
            
            local upload_id
            if upload_id=$(test_schema_interpretation "$file_path" "$description" "$token"); then
                passed_tests=$((passed_tests + 1))
                upload_ids+=("$upload_id")
                
                # Test column mapping for this upload
                if test_column_mapping "$upload_id" "$token"; then
                    log_success "Full workflow test passed for: $description"
                else
                    log_warning "Column mapping test failed for: $description"
                fi
            else
                log_error "Schema interpretation test failed for: $description"
            fi
        else
            log_warning "Test file not found: $file_path"
        fi
    done
    
    # Summary
    echo ""
    echo "=================================="
    log_info "Schema Interpretation Test Summary"
    echo "=================================="
    log_info "Total tests: $total_tests"
    log_info "Passed: $passed_tests"
    log_info "Failed: $((total_tests - passed_tests))"
    
    if [ "$passed_tests" -eq "$total_tests" ] && [ "$total_tests" -gt 0 ]; then
        log_success "All schema interpretation tests passed!"
        return 0
    else
        log_error "Some schema interpretation tests failed"
        return 1
    fi
}

# Check dependencies
if ! command -v jq &> /dev/null; then
    log_error "jq is required but not installed. Please install jq first."
    exit 1
fi

if ! command -v curl &> /dev/null; then
    log_error "curl is required but not installed. Please install curl first."
    exit 1
fi

# Run main function
main "$@"