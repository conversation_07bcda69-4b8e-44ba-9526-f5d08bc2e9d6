#!/bin/bash
# Deploy script for giki.ai with comprehensive logging
set -e

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

echo "🚀 giki.ai Deployment Script"
echo "=============================="

# Function to display usage
usage() {
    echo "Usage: $0 [api|app|all|logs] [--dry-run]"
    echo "  api  - Deploy API to Cloud Run"
    echo "  app  - Deploy frontend to Firebase Hosting"
    echo "  all  - Deploy both API and frontend"
    echo "  logs - Show latest deployment logs"
    echo ""
    echo "Options:"
    echo "  --dry-run - Simulate deployment without actually deploying"
    echo ""
    echo "Logs are saved to:"
    echo "  logs/deploy-api-latest.log    - Latest API deployment"
    echo "  logs/deploy-app-latest.log    - Latest frontend deployment"
    echo "  logs/deploy-api-*.log         - Timestamped API deployments"
    echo "  logs/deploy-app-*.log         - Timestamped frontend deployments"
    exit 1
}

# Check if argument provided
if [ $# -eq 0 ]; then
    usage
fi

# Function to show deployment logs
show_logs() {
    echo "📋 Latest Deployment Logs"
    echo "========================="
    
    if [ -f "$WORKSPACE_ROOT/logs/deploy-api-latest.log" ]; then
        echo ""
        echo "🔧 Latest API Deployment:"
        echo "-------------------------"
        tail -20 "$WORKSPACE_ROOT/logs/deploy-api-latest.log"
    fi
    
    if [ -f "$WORKSPACE_ROOT/logs/deploy-app-latest.log" ]; then
        echo ""
        echo "🎨 Latest Frontend Deployment:"
        echo "------------------------------"
        tail -20 "$WORKSPACE_ROOT/logs/deploy-app-latest.log"
    fi
    
    echo ""
    echo "💡 Tip: Use 'tail -f logs/deploy-*-latest.log' to follow deployment progress"
}

# Check for dry-run option
DRY_RUN_ARG=""
if [ "$2" = "--dry-run" ]; then
    DRY_RUN_ARG="--dry-run"
fi

# Process command
case $1 in
    api)
        "$WORKSPACE_ROOT/scripts/deploy-api.sh" $DRY_RUN_ARG
        ;;
    app)
        "$WORKSPACE_ROOT/scripts/deploy-app.sh" $DRY_RUN_ARG
        ;;
    all)
        echo "🚀 Starting full deployment..."
        if [ -n "$DRY_RUN_ARG" ]; then
            echo "🧪 Running in DRY RUN mode"
        fi
        echo ""
        "$WORKSPACE_ROOT/scripts/deploy-api.sh" $DRY_RUN_ARG
        echo ""
        "$WORKSPACE_ROOT/scripts/deploy-app.sh" $DRY_RUN_ARG
        echo ""
        echo "✅ Full deployment complete!"
        if [ -z "$DRY_RUN_ARG" ]; then
            echo "📊 API: https://console.cloud.google.com/run?project=rezolve-poc"
            echo "🌐 App: https://app-giki-ai.web.app"
        fi
        ;;
    logs)
        show_logs
        ;;
    *)
        usage
        ;;
esac