#!/usr/bin/env python3
"""
Authentication Helper Service - Automated Token Management
=========================================================

Eliminates manual curl authentication overhead by providing:
- Automatic login and token refresh
- Easy token access for API testing
- Integration with existing test credentials
- Efficient development workflow

Usage:
    python scripts/auth-helper.py --get-token <EMAIL>
    python scripts/auth-helper.py --test-auth
    python scripts/auth-helper.py --refresh-token <token>
"""

import argparse
import json
import requests
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional, Tuple


class AuthHelper:
    """Automated authentication helper for development testing."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.token_cache_file = Path("/tmp/giki-auth-tokens.json")
        
        # Test credentials from docs/01-CURRENT-STATUS.md
        self.test_users = {
            "<EMAIL>": "GikiTest2025Secure",
            "<EMAIL>": "GikiTest2025Secure", 
            "<EMAIL>": "GikiTest2025Secure",
            "<EMAIL>": "GikiTest2025Secure",
            "<EMAIL>": "GikiTest2025Secure",
        }
    
    def get_cached_token(self, email: str) -> Optional[str]:
        """Get valid cached token if available."""
        if not self.token_cache_file.exists():
            return None
            
        try:
            with open(self.token_cache_file, 'r') as f:
                cache = json.load(f)
                
            user_data = cache.get(email, {})
            token = user_data.get('token')
            expires_at = user_data.get('expires_at')
            
            if token and expires_at:
                # Check if token expires in next 5 minutes
                expiry_time = datetime.fromisoformat(expires_at)
                if expiry_time > datetime.now() + timedelta(minutes=5):
                    return token
                    
        except (json.JSONDecodeError, KeyError, ValueError):
            pass
            
        return None
    
    def cache_token(self, email: str, token: str, expires_in: int = 3600):
        """Cache token with expiration time."""
        try:
            cache = {}
            if self.token_cache_file.exists():
                with open(self.token_cache_file, 'r') as f:
                    cache = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            cache = {}
            
        expires_at = datetime.now() + timedelta(seconds=expires_in - 300)  # 5 min buffer
        cache[email] = {
            'token': token,
            'expires_at': expires_at.isoformat(),
            'cached_at': datetime.now().isoformat()
        }
        
        with open(self.token_cache_file, 'w') as f:
            json.dump(cache, f, indent=2)
    
    def login(self, email: str, password: str = None) -> Tuple[bool, str]:
        """Login and get access token."""
        if password is None:
            password = self.test_users.get(email)
            if not password:
                return False, f"No test password found for {email}"
        
        # Check cache first
        cached_token = self.get_cached_token(email)
        if cached_token:
            # Verify token is still valid
            if self.verify_token(cached_token):
                return True, cached_token
        
        # Login to get new token
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/auth/token",
                data={
                    "username": email,
                    "password": password
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                token = data.get('access_token')
                expires_in = data.get('expires_in', 3600)
                
                if token:
                    self.cache_token(email, token, expires_in)
                    return True, token
                else:
                    return False, "No access token in response"
            else:
                return False, f"Login failed: {response.status_code} - {response.text}"
                
        except requests.RequestException as e:
            return False, f"Network error: {str(e)}"
    
    def verify_token(self, token: str) -> bool:
        """Verify if token is still valid."""
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/auth/me",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )
            return response.status_code == 200
        except requests.RequestException:
            return False
    
    def refresh_token(self, current_token: str) -> Tuple[bool, str]:
        """Refresh access token."""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/auth/refresh",
                headers={"Authorization": f"Bearer {current_token}"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                new_token = data.get('access_token')
                if new_token:
                    return True, new_token
                else:
                    return False, "No access token in refresh response"
            else:
                return False, f"Refresh failed: {response.status_code} - {response.text}"
                
        except requests.RequestException as e:
            return False, f"Network error: {str(e)}"
    
    def get_auth_header(self, email: str) -> Dict[str, str]:
        """Get Authorization header for API requests."""
        success, token = self.login(email)
        if success:
            return {"Authorization": f"Bearer {token}"}
        else:
            raise Exception(f"Failed to get token: {token}")
    
    def test_api_call(self, email: str, endpoint: str = "/api/v1/auth/me") -> Tuple[bool, str]:
        """Test API call with authentication."""
        try:
            headers = self.get_auth_header(email)
            response = requests.get(
                f"{self.base_url}{endpoint}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return True, f"✅ API call successful: {response.json()}"
            else:
                return False, f"❌ API call failed: {response.status_code} - {response.text}"
                
        except Exception as e:
            return False, f"❌ API call error: {str(e)}"


def main():
    parser = argparse.ArgumentParser(description="Authentication Helper for giki.ai API testing")
    parser.add_argument("--get-token", help="Get access token for user email")
    parser.add_argument("--test-auth", action="store_true", help="Test authentication for all users")
    parser.add_argument("--refresh-token", help="Refresh given access token")
    parser.add_argument("--test-api", help="Test API call with authentication for user email")
    parser.add_argument("--clear-cache", action="store_true", help="Clear token cache")
    parser.add_argument("--base-url", default="http://localhost:8000", help="API base URL")
    
    args = parser.parse_args()
    
    auth_helper = AuthHelper(args.base_url)
    
    if args.clear_cache:
        if auth_helper.token_cache_file.exists():
            auth_helper.token_cache_file.unlink()
            print("✅ Token cache cleared")
        else:
            print("ℹ️  No token cache to clear")
        return
    
    if args.get_token:
        email = args.get_token
        success, token = auth_helper.login(email)
        if success:
            print(f"✅ Token for {email}:")
            print(token)
            print(f"\n📋 Copy this for curl:")
            print(f'export TOKEN="{token}"')
            print(f'curl -H "Authorization: Bearer $TOKEN" "{args.base_url}/api/v1/auth/me"')
        else:
            print(f"❌ Failed to get token: {token}")
            sys.exit(1)
    
    elif args.test_auth:
        print("🔐 Testing authentication for all test users...\n")
        all_success = True
        
        for email in auth_helper.test_users.keys():
            success, result = auth_helper.test_api_call(email)
            print(f"{email}: {result}")
            if not success:
                all_success = False
        
        if all_success:
            print("\n✅ All authentication tests passed!")
        else:
            print("\n❌ Some authentication tests failed!")
            sys.exit(1)
    
    elif args.refresh_token:
        token = args.refresh_token
        success, new_token = auth_helper.refresh_token(token)
        if success:
            print(f"✅ Refreshed token:")
            print(new_token)
        else:
            print(f"❌ Failed to refresh token: {new_token}")
            sys.exit(1)
    
    elif args.test_api:
        email = args.test_api
        success, result = auth_helper.test_api_call(email)
        print(result)
        if not success:
            sys.exit(1)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()