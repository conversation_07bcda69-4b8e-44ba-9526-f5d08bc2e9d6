#!/bin/bash
# Deploy API to Cloud Run with proper logging
set -e

# Get workspace root
WORKSPACE_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Check for dry-run mode
DRY_RUN=false
if [ "$1" = "--dry-run" ]; then
    DRY_RUN=true
fi

# Create logs directory
mkdir -p "$WORKSPACE_ROOT/logs"

# Timestamp for this deployment
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
LOG_FILE="$WORKSPACE_ROOT/logs/deploy-api-$TIMESTAMP.log"
LATEST_LOG="$WORKSPACE_ROOT/logs/deploy-api-latest.log"

echo "🚀 Deploying API to Cloud Run..."
echo "📝 Deployment logs: logs/deploy-api-latest.log"
echo "📝 Timestamped logs: logs/deploy-api-$TIMESTAMP.log"

# Function to log output
log_output() {
    tee -a "$LOG_FILE" | tee "$LATEST_LOG"
}

# Start deployment with logging
{
    echo "=== API Deployment Started at $(date) ===" 
    
    # Load production environment variables for deployment
    if [ -f "$WORKSPACE_ROOT/infrastructure/environments/production/.env.production" ]; then
        set -a  # automatically export all variables
        source "$WORKSPACE_ROOT/infrastructure/environments/production/.env.production"
        set +a  # stop automatically exporting
    fi
    
    # Set up service account for deployment
    export GOOGLE_APPLICATION_CREDENTIALS="$WORKSPACE_ROOT/infrastructure/service-accounts/development/dev-service-account.json"
    
    echo "Project: rezolve-poc"
    echo "Service: giki-ai-api"
    echo "Region: us-central1"
    echo "Service Account: Using dev-service-account.json"
    echo ""
    
    # Run the actual deployment
    if [ "$DRY_RUN" = true ]; then
        echo "🧪 DRY RUN MODE - Simulating deployment..."
        echo "Would run: gcloud builds submit --config=cloudbuild.yaml --substitutions=_SERVICE_NAME=giki-ai-api --project=rezolve-poc"
        echo "Checking cloudbuild.yaml exists..."
        if [ -f "$WORKSPACE_ROOT/cloudbuild.yaml" ]; then
            echo "✅ cloudbuild.yaml found"
        else
            echo "❌ cloudbuild.yaml not found!"
            exit 1
        fi
        echo "🧪 DRY RUN - Deployment simulation complete"
    else
        # Submit build asynchronously to avoid timeout
        echo "Submitting build asynchronously..."
        BUILD_ID=$(gcloud builds submit \
            --async \
            --timeout=30m \
            --config=cloudbuild.yaml \
            --substitutions=_SERVICE_NAME=giki-ai-api \
            --project=rezolve-poc \
            --format="value(name)" 2>&1)
        
        if [ $? -eq 0 ]; then
            # Extract build ID from the response
            BUILD_ID=$(echo "$BUILD_ID" | grep -oE '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}' | head -1)
            echo "Build submitted successfully. Build ID: $BUILD_ID"
            echo "Console URL: https://console.cloud.google.com/cloud-build/builds/$BUILD_ID?project=rezolve-poc"
            echo ""
            
            # Start background monitoring (like serve script pattern)
            echo "🔄 Starting background build monitoring..."
            (
                while true; do
                    STATUS=$(gcloud builds describe "$BUILD_ID" \
                        --project=rezolve-poc \
                        --format="value(status)" 2>/dev/null)
                    
                    case "$STATUS" in
                        "SUCCESS")
                            echo "✅ Build completed successfully! ($(date +%H:%M:%S))" >> "$LOG_FILE"
                            break
                            ;;
                        "FAILURE"|"TIMEOUT"|"CANCELLED")
                            echo "❌ Build failed with status: $STATUS ($(date +%H:%M:%S))" >> "$LOG_FILE"
                            echo "Check logs at: https://console.cloud.google.com/cloud-build/builds/$BUILD_ID?project=rezolve-poc" >> "$LOG_FILE"
                            break
                            ;;
                        "QUEUED"|"WORKING")
                            echo "⏳ Build status: $STATUS ($(date +%H:%M:%S))" >> "$LOG_FILE"
                            sleep 10
                            ;;
                        *)
                            echo "⚠️ Unknown status: $STATUS ($(date +%H:%M:%S))" >> "$LOG_FILE"
                            sleep 10
                            ;;
                    esac
                done
            ) &
            
            MONITOR_PID=$!
            echo "📊 Build monitoring started in background (PID: $MONITOR_PID)"
            echo "🔗 Console: https://console.cloud.google.com/cloud-build/builds/$BUILD_ID?project=rezolve-poc"
            echo "📝 Monitor logs: tail -f logs/deploy-api-latest.log"
        else
            echo "❌ Failed to submit build"
            echo "$BUILD_ID"
            exit 1
        fi
    fi
    
    echo ""
    echo "=== API Deployment Completed at $(date) ==="
    
} 2>&1 | log_output

echo "🚀 API deployment initiated!"
echo "📊 Monitor progress: pnpm logs:deploy"
echo "🌐 Cloud Console: https://console.cloud.google.com/run/detail/us-central1/giki-ai-api/metrics?project=rezolve-poc"