#!/bin/bash

# Test script for confidence-based categorization system
# Tests the complete 5-step mockup workflow:
# 1. Onboarding (simplified-onboarding)
# 2. Processing (ai-processing) 
# 3. Results (categorization-results)
# 4. Review & Improve (review-and-improve)
# 5. Export (export-options)

set -e  # Exit on any error

# Setup cleanup trap
trap cleanup_server EXIT

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE="http://localhost:8000"
TEST_USER="<EMAIL>"
TEST_PASSWORD="GikiTest2025Secure"
DB_NAME="giki_ai_dev"
DB_USER="nikhilsingh"
DB_HOST="localhost"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get authentication token
get_auth_token() {
    log_info "Getting authentication token..."
    
    TOKEN_RESPONSE=$(curl -s -X POST "$API_BASE/api/v1/auth/token" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=$TEST_USER&password=$TEST_PASSWORD")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to get authentication token"
        exit 1
    fi
    
    # log_info "Token response: $TOKEN_RESPONSE"
    
    TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')
    
    if [ "$TOKEN" = "null" ] || [ "$TOKEN" = "" ]; then
        log_error "Authentication failed: $TOKEN_RESPONSE"
        exit 1
    fi
    
    log_success "Authentication token obtained: ${TOKEN:0:20}..."
    echo "$TOKEN"
}

# Test API endpoint
test_api_endpoint() {
    local endpoint="$1"
    local description="$2"
    local token="$3"
    
    log_info "Testing $description: $endpoint"
    
    response=$(curl -s -w "\n%{http_code}" -X GET "$API_BASE$endpoint" \
        -H "Authorization: Bearer $token" \
        -H "accept: application/json")
    
    http_code=$(echo "$response" | tail -n 1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "200" ]; then
        log_success "$description - OK"
        return 0
    else
        log_error "$description - HTTP $http_code"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Test database function
test_database_function() {
    local tenant_id="$1"
    local upload_id="$2"
    
    log_info "Testing database function get_review_queue_summary..."
    
    result=$(psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c \
        "SELECT get_review_queue_summary($tenant_id, '$upload_id');")
    
    if [ $? -eq 0 ]; then
        log_success "Database function works"
        echo "$result" | head -5
    else
        log_error "Database function failed"
        return 1
    fi
}

# Find uploads with transactions
find_test_upload() {
    log_info "Finding uploads with transactions..."
    
    upload_info=$(psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c \
        "SELECT upload_id, COUNT(*) as transaction_count 
         FROM transactions 
         WHERE tenant_id = 3 
         GROUP BY upload_id 
         ORDER BY transaction_count DESC 
         LIMIT 1;")
    
    if [ $? -eq 0 ] && [ -n "$upload_info" ]; then
        upload_id=$(echo "$upload_info" | awk '{print $1}')
        transaction_count=$(echo "$upload_info" | awk '{print $3}')
        
        log_success "Found upload: $upload_id with $transaction_count transactions"
        echo "$upload_id"
    else
        log_error "No uploads with transactions found"
        return 1
    fi
}

# Test review queue endpoint
test_review_queue() {
    local token="$1"
    local upload_id="$2"
    
    log_info "Testing review queue endpoint..."
    
    response=$(curl -s -w "\n%{http_code}" -X GET \
        "$API_BASE/api/v1/categories/review/queue/$upload_id" \
        -H "Authorization: Bearer $token" \
        -H "accept: application/json")
    
    http_code=$(echo "$response" | tail -n 1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "200" ]; then
        log_success "Review queue endpoint - OK"
        
        # Parse and display summary
        summary=$(echo "$body" | jq '.summary')
        if [ "$summary" != "null" ]; then
            log_info "Review Queue Summary:"
            echo "$summary" | jq '.'
        fi
        
        # Check for suggestions
        suggestions_count=$(echo "$body" | jq '.suggestions | length')
        groups_count=$(echo "$body" | jq '.groups | length')
        uncategorized_count=$(echo "$body" | jq '.uncategorized | length')
        
        log_info "Suggestions: $suggestions_count, Groups: $groups_count, Uncategorized: $uncategorized_count"
        
        return 0
    else
        log_error "Review queue endpoint - HTTP $http_code"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Test bulk action endpoint
test_bulk_action() {
    local token="$1"
    
    log_info "Testing bulk action endpoint..."
    
    # Test with empty transaction list (should succeed but do nothing)
    response=$(curl -s -w "\n%{http_code}" -X POST \
        "$API_BASE/api/v1/categories/review/bulk-action" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -d '{
            "action": "accept",
            "transaction_ids": [],
            "notes": "Test bulk action"
        }')
    
    http_code=$(echo "$response" | tail -n 1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "200" ]; then
        log_success "Bulk action endpoint - OK"
        echo "$body" | jq '.'
        return 0
    else
        log_error "Bulk action endpoint - HTTP $http_code"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Test accept all suggestions endpoint
test_accept_all_suggestions() {
    local token="$1"
    local upload_id="$2"
    
    log_info "Testing accept all suggestions endpoint..."
    
    response=$(curl -s -w "\n%{http_code}" -X POST \
        "$API_BASE/api/v1/categories/review/accept-all-suggestions?upload_id=$upload_id&min_confidence=0.95" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json")
    
    http_code=$(echo "$response" | tail -n 1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "200" ]; then
        log_success "Accept all suggestions endpoint - OK"
        echo "$body" | jq '.'
        return 0
    else
        log_error "Accept all suggestions endpoint - HTTP $http_code"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Restart API server for clean testing environment
restart_api_server() {
    log_info "Restarting API server for clean testing environment..."
    
    # Kill existing API server processes
    pkill -f "uvicorn.*giki_ai_api.main:app" || true
    sleep 2
    
    # Start the API server
    pnpm serve:api &
    API_PID=$!
    
    # Wait for server to start (up to 30 seconds)
    for i in {1..30}; do
        if curl -s "$API_BASE/health" > /dev/null; then
            log_success "API server restarted successfully (PID: $API_PID)"
            return 0
        fi
        sleep 1
    done
    
    log_error "API server failed to restart within 30 seconds"
    return 1
}

# Restart frontend server for testing (optional)
restart_frontend_server() {
    log_info "Restarting frontend server for testing..."
    
    # Kill existing frontend server processes
    pkill -f "vite.*--port 4200" || true
    sleep 2
    
    # Start the frontend server
    pnpm serve:app &
    FRONTEND_PID=$!
    
    # Wait for server to start (up to 30 seconds)
    for i in {1..30}; do
        if curl -s "http://localhost:4200" > /dev/null; then
            log_success "Frontend server restarted successfully (PID: $FRONTEND_PID)"
            return 0
        fi
        sleep 1
    done
    
    log_warning "Frontend server failed to restart within 30 seconds (continuing without it)"
    return 0  # Don't fail the test if frontend doesn't start
}

# Stop servers if we started them
cleanup_server() {
    if [ -n "$API_PID" ]; then
        log_info "Stopping API server (PID: $API_PID)..."
        kill $API_PID 2>/dev/null
        log_success "API server stopped"
    fi
    
    if [ -n "$FRONTEND_PID" ]; then
        log_info "Stopping frontend server (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID 2>/dev/null
        log_success "Frontend server stopped"
    fi
}

# Main test execution
main() {
    echo "=================================="
    echo "Confidence-Based Categorization Test"
    echo "=================================="
    
    # Check prerequisites
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed"
        exit 1
    fi
    
    if ! command -v psql &> /dev/null; then
        log_error "psql is required but not installed"
        exit 1
    fi
    
    # Apply database migrations
    log_info "Applying database migrations..."
    psql -h localhost -U nikhilsingh -d giki_ai_dev -f apps/giki-ai-api/migrations/add_confidence_review_fields_v2.sql > /dev/null 2>&1
    log_success "Database migrations applied"
    
    # Restart API server for clean testing environment
    if ! restart_api_server; then
        exit 1
    fi
    
    # Restart frontend server for testing (optional)
    restart_frontend_server
    
    # Get authentication token
    TOKEN=$(get_auth_token)
    
    # Find test upload
    UPLOAD_ID=$(find_test_upload)
    if [ $? -ne 0 ]; then
        log_warning "No test uploads found, skipping upload-specific tests"
        UPLOAD_ID=""
    fi
    
    # Test database function first (this should work)
    if [ -n "$UPLOAD_ID" ]; then
        test_database_function 3 "$UPLOAD_ID"
    fi
    
    # Test review queue endpoint (main test)
    if [ -n "$UPLOAD_ID" ]; then
        test_review_queue "$TOKEN" "$UPLOAD_ID"
    fi
    
    # Test bulk action endpoint
    test_bulk_action "$TOKEN"
    
    # Test accept all suggestions endpoint
    if [ -n "$UPLOAD_ID" ]; then
        test_accept_all_suggestions "$TOKEN" "$UPLOAD_ID"
    fi
    
    # Test categories endpoint to verify auth is working
    test_api_endpoint "/api/v1/categories" "Categories endpoint" "$TOKEN"
    
    echo "=================================="
    log_success "All tests completed!"
    echo "=================================="
    
    # Test 5-step mockup workflow
    test_mockup_workflow "$TOKEN" "$UPLOAD_ID"
    
    # Cleanup
    cleanup_server
}

# Test complete 5-step mockup workflow
test_mockup_workflow() {
    local token="$1"
    local upload_id="$2"
    
    if [ -z "$upload_id" ]; then
        log_warning "No upload ID available, skipping mockup workflow test"
        return 0
    fi
    
    log_info "Testing 5-step mockup workflow..."
    
    # Step 1: Onboarding (simplified-onboarding)
    log_info "Step 1: Testing onboarding/upload capabilities..."
    test_api_endpoint "/api/v1/files" "File upload endpoint" "$token"
    
    # Step 2: Processing (ai-processing)
    log_info "Step 2: Testing AI processing status..."
    test_processing_status "$token" "$upload_id"
    
    # Step 3: Results (categorization-results)
    log_info "Step 3: Testing categorization results..."
    test_categorization_results "$token" "$upload_id"
    
    # Step 4: Review & Improve (review-and-improve)
    log_info "Step 4: Testing review queue and confidence-based improvements..."
    test_review_queue "$token" "$upload_id"
    
    # Step 5: Export (export-options)
    log_info "Step 5: Testing export capabilities..."
    test_export_options "$token" "$upload_id"
    
    log_success "5-step mockup workflow test completed!"
}

# Test processing status (Step 2)
test_processing_status() {
    local token="$1"
    local upload_id="$2"
    
    log_info "Testing processing status for upload $upload_id..."
    
    # Test upload status endpoint
    response=$(curl -s -w "\n%{http_code}" -X GET \
        "$API_BASE/api/v1/files/upload/$upload_id/status" \
        -H "Authorization: Bearer $token" \
        -H "accept: application/json")
    
    http_code=$(echo "$response" | tail -n 1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "200" ]; then
        log_success "Processing status endpoint - OK"
        
        # Parse processing details
        status=$(echo "$body" | jq -r '.status // "unknown"')
        transaction_count=$(echo "$body" | jq -r '.transaction_count // 0')
        
        log_info "Upload Status: $status, Transactions: $transaction_count"
        return 0
    else
        log_error "Processing status endpoint - HTTP $http_code"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Test categorization results (Step 3)
test_categorization_results() {
    local token="$1"
    local upload_id="$2"
    
    log_info "Testing categorization results for upload $upload_id..."
    
    # Test transactions endpoint
    response=$(curl -s -w "\n%{http_code}" -X GET \
        "$API_BASE/api/v1/transactions?upload_id=$upload_id&limit=5" \
        -H "Authorization: Bearer $token" \
        -H "accept: application/json")
    
    http_code=$(echo "$response" | tail -n 1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "200" ]; then
        log_success "Categorization results endpoint - OK"
        
        # Parse results
        transaction_count=$(echo "$body" | jq '.transactions | length')
        categorized_count=$(echo "$body" | jq '.transactions | map(select(.category_id != null)) | length')
        
        log_info "Transactions: $transaction_count, Categorized: $categorized_count"
        return 0
    else
        log_error "Categorization results endpoint - HTTP $http_code"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Test export options (Step 5)
test_export_options() {
    local token="$1"
    local upload_id="$2"
    
    log_info "Testing export options for upload $upload_id..."
    
    # Test export endpoint (if it exists)
    response=$(curl -s -w "\n%{http_code}" -X GET \
        "$API_BASE/api/v1/files/export?upload_id=$upload_id&format=csv" \
        -H "Authorization: Bearer $token" \
        -H "accept: application/json")
    
    http_code=$(echo "$response" | tail -n 1)
    
    if [ "$http_code" = "200" ]; then
        log_success "Export options endpoint - OK"
        return 0
    else
        log_warning "Export options endpoint - HTTP $http_code (may not be implemented yet)"
        return 0  # Don't fail the test if export isn't implemented
    fi
}

# Run main function
main "$@"