#!/usr/bin/env python3
"""
Run Categorization Tests
========================

This script runs the categorization accuracy tests using the real test data
and provides detailed reports on the performance of the categorization system.
"""

import sys
import asyncio
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "apps" / "giki-ai-api" / "src"))

from giki_ai_api.tests.integration.test_categorization_accuracy import CategorizationAccuracyTester


async def run_comprehensive_categorization_tests():
    """Run comprehensive categorization tests with real data."""
    
    print("🚀 Starting Comprehensive Categorization Tests")
    print("=" * 60)
    
    # Initialize the tester
    tester = CategorizationAccuracyTester()
    
    # Load test cases from our curated data
    test_data_file = Path("/Users/<USER>/giki-ai-workspace/libs/test-data/categorization-validation/categorization_test_cases.xlsx")
    
    if not test_data_file.exists():
        print(f"❌ Test data file not found: {test_data_file}")
        print("Please run generate_categorization_test_data.py first")
        return
    
    try:
        # Load test cases from Excel file
        print(f"📊 Loading test cases from: {test_data_file}")
        real_test_cases = tester.load_test_cases_from_excel(test_data_file)
        print(f"✅ Loaded {len(real_test_cases)} test cases")
        
        # Also create some standard test cases
        standard_test_cases = tester.create_standard_test_cases()
        print(f"✅ Created {len(standard_test_cases)} standard test cases")
        
        # Run tests on real data
        print("\n🧪 Running tests on curated test data...")
        real_metrics = await tester.run_accuracy_test_suite(real_test_cases)
        
        # Display results
        print("\n" + "="*60)
        print("📊 CURATED TEST DATA RESULTS")
        print("="*60)
        print(f"Total Test Cases: {real_metrics.total_tests}")
        print(f"Category Accuracy: {real_metrics.category_accuracy:.1%}")
        print(f"Parent Accuracy: {real_metrics.parent_accuracy:.1%}")
        print(f"Confidence Reliability: {real_metrics.confidence_reliability:.1%}")
        print(f"Average Confidence: {real_metrics.average_confidence:.2f}")
        print(f"Average Execution Time: {real_metrics.average_execution_time_ms:.1f}ms")
        print(f"Vendor Accuracy: {real_metrics.vendor_accuracy:.1%}")
        print(f"Pattern Accuracy: {real_metrics.pattern_accuracy:.1%}")
        print(f"Edge Case Accuracy: {real_metrics.edge_case_accuracy:.1%}")
        print(f"Standard Accuracy: {real_metrics.standard_accuracy:.1%}")
        
        # Generate detailed report
        report_path = Path("/Users/<USER>/giki-ai-workspace/testing/results/curated_categorization_report.json")
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        print(f"\n📝 Generating detailed report...")
        report = tester.generate_test_report(real_metrics, report_path)
        
        # Print failure analysis
        failed_cases = [r for r in tester.results if not r.correct_category]
        if failed_cases:
            print(f"\n❌ FAILED CASES ANALYSIS ({len(failed_cases)} failures):")
            print("-" * 60)
            
            for i, case in enumerate(failed_cases[:10]):  # Show first 10 failures
                print(f"{i+1}. {case.test_case.description[:50]}...")
                print(f"   Expected: {case.test_case.expected_category}")
                print(f"   Got: {case.predicted_category} (conf: {case.predicted_confidence:.2f})")
                print(f"   Type: {case.test_case.test_type}")
                print(f"   Reasoning: {case.reasoning[:80]}...")
                print()
            
            if len(failed_cases) > 10:
                print(f"   ... and {len(failed_cases) - 10} more failures")
        
        # Run comparison with standard test cases
        print("\n🔄 Running comparison with standard test cases...")
        standard_metrics = await tester.run_accuracy_test_suite(standard_test_cases)
        
        print("\n" + "="*60)
        print("📊 STANDARD TEST DATA RESULTS")
        print("="*60)
        print(f"Total Test Cases: {standard_metrics.total_tests}")
        print(f"Category Accuracy: {standard_metrics.category_accuracy:.1%}")
        print(f"Parent Accuracy: {standard_metrics.parent_accuracy:.1%}")
        print(f"Confidence Reliability: {standard_metrics.confidence_reliability:.1%}")
        print(f"Average Confidence: {standard_metrics.average_confidence:.2f}")
        print(f"Average Execution Time: {standard_metrics.average_execution_time_ms:.1f}ms")
        
        # Comparison summary
        print("\n" + "="*60)
        print("📈 COMPARISON SUMMARY")
        print("="*60)
        print(f"Curated Data Accuracy: {real_metrics.category_accuracy:.1%}")
        print(f"Standard Data Accuracy: {standard_metrics.category_accuracy:.1%}")
        print(f"Difference: {(real_metrics.category_accuracy - standard_metrics.category_accuracy)*100:+.1f} percentage points")
        
        if real_metrics.category_accuracy >= 0.70:
            print("✅ CATEGORIZATION SYSTEM PASSES ACCURACY REQUIREMENTS")
        else:
            print("❌ CATEGORIZATION SYSTEM NEEDS IMPROVEMENT")
            
        if real_metrics.confidence_reliability >= 0.60:
            print("✅ CONFIDENCE SCORING SYSTEM IS RELIABLE")
        else:
            print("❌ CONFIDENCE SCORING SYSTEM NEEDS IMPROVEMENT")
            
        print(f"\n📋 Detailed reports saved to: {report_path.parent}")
        
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(run_comprehensive_categorization_tests())