#!/usr/bin/env python3
"""Create realistic synthetic test data based on Nuvie expense ledger format"""

import pandas as pd
import random
from datetime import datetime, timedelta
from pathlib import Path
import numpy as np

# Real Indian business vendors from analysis
REAL_VENDORS = {
    'expenses': {
        'staff_welfare': [
            '<PERSON><PERSON><PERSON>GY PAYMENT TEAM', '<PERSON><PERSON><PERSON><PERSON> ORDER', 'DOMINOS PIZZA', 'MCDONALDS INDIA',
            'CAFE COFFEE DAY', 'STARBUCKS INDIA', 'HALDIRAMS', 'BARBEQUE NATION',
            'PVR CINEMAS TEAM EVENT', 'BOOKMYSHOW TEAM OUTING'
        ],
        'travel': [
            'UBER INDIA SYSTEMS PVT', 'O<PERSON> CABS BANGALORE', 'RAPIDO BIKE TAXI',
            'MAKEMYTRIP INDIA PVT', 'CLEARTRIP PVT LTD', 'YATRA ONLINE PRI',
            'IRCTC E TICKETING', 'INDIGO AIRLINES', 'SPICEJET LIMITED',
            'GO<PERSON><PERSON><PERSON> HOTELS'
        ],
        'utilities': [
            'BESCOM ELECTRICITY BILL', '<PERSON><PERSON><PERSON> WATER CHARGES', 'AIRTEL BROADBAND',
            'JIO FIBER CONNECTION', 'ACT FIBERNET', 'VODAFONE IDEA LIMITED',
            'BSNL LANDLINE', 'TATA POWER DELHI', 'MAHANAGAR GAS LIMITED'
        ],
        'office_supplies': [
            'AMAZON PAY INDIA', 'FLIPKART INTERNET PVT', 'RELIANCE DIGITAL',
            'CROMA RETAIL', 'STAPLES FUTURE OFFICE', 'OFFICEWORKS INDIA',
            'WS RETAIL SERVICES', 'CLOUDTAIL INDIA PVT'
        ],
        'software': [
            'GOOGLE WORKSPAC', 'MICROSOFT INDIA', 'ADOBE SYSTEMS INDIA',
            'ZOHO CORPORATION', 'FRESHWORKS INC', 'SLACK TECHNOLOGIES',
            'OPENAI *CHATGPT SUBSCR', 'GITHUB INC', 'ATLASSIAN JIRA',
            'CANVA PTY LTD', 'FIGMA INC', 'NOTION LABS INC'
        ],
        'marketing': [
            'FACEBOOK INDIA ONLINE', 'GOOGLE ADS INDIA', 'LINKEDIN INDIA',
            'TWITTER INDIA', 'INSTAGRAM BUSINESS', 'YOUTUBE ADVERTISING',
            'TIMES OF INDIA ADS', 'ECONOMIC TIMES', 'PRINT VISION INDIA'
        ],
        'professional_services': [
            'DELOITTE HASKINS SELLS', 'KPMG INDIA PVT LTD', 'EY GDS INDIA',
            'PWC INDIA', 'GRANT THORNTON LLP', 'BDO INDIA LLP',
            'LEGAL SERVICES INDIA', 'COMPANY SECRETARY FIRM', 'TAX CONSULTANT'
        ],
        'bank_charges': [
            'HDFC BANK CHARGES', 'ICICI BANK FEES', 'AXIS BANK CHARGES',
            'SBI TRANSACTION CHARGES', 'KOTAK BANK FEES', 'YES BANK CHARGES',
            'RAZORPAY PROCESSING FEE', 'PAYTM GATEWAY CHARGES', 'PHONEPE CHARGES'
        ],
        'rent': [
            'MONTHLY OFFICE RENT', 'COWORKING SPACE RENT', 'WAREHOUSE RENT PAYMENT',
            'SECURITY DEPOSIT OFFICE', 'MAINTENANCE CHARGES', 'PROPERTY TAX PAYMENT'
        ],
        'salary': [
            'SALARY PAYMENT', 'EMPLOYEE REIMBURSEMENT', 'CONSULTANT PAYMENT',
            'FREELANCER PAYMENT', 'INTERN STIPEND', 'BONUS PAYMENT',
            'PF CONTRIBUTION', 'ESI CONTRIBUTION', 'GRATUITY PAYMENT'
        ]
    },
    'income': {
        'sales': [
            'CUSTOMER PAYMENT', 'CLIENT INVOICE PAYMENT', 'PRODUCT SALE',
            'SERVICE REVENUE', 'SUBSCRIPTION PAYMENT', 'CONSULTING FEE',
            'PROJECT MILESTONE', 'ADVANCE PAYMENT', 'FINAL SETTLEMENT'
        ],
        'other': [
            'INTEREST CREDIT', 'GST REFUND', 'TDS REFUND', 'VENDOR REFUND',
            'INSURANCE CLAIM', 'GOVT SUBSIDY', 'GRANT RECEIVED'
        ]
    }
}

# Transaction type prefixes
TRANSACTION_PREFIXES = {
    'neft': 'NEFT',
    'imps': 'IMPS',
    'upi': 'UPI',
    'pos': 'POS',
    'ach': 'ACH',
    'tpt': 'TPT',
    'chq': 'CHQ DEP'
}

def generate_realistic_narration(vendor, tx_type='neft'):
    """Generate realistic transaction narration"""
    prefix = TRANSACTION_PREFIXES.get(tx_type, 'NEFT')
    ref_num = ''.join([str(random.randint(0, 9)) for _ in range(12)])
    
    if tx_type == 'pos':
        # POS transactions
        masked_card = f"403875{'X' * 6}{random.randint(1000, 9999)}"
        return f"{prefix} {masked_card} {vendor}"
    elif tx_type == 'imps':
        # IMPS transactions
        return f"{prefix}-{ref_num}-{vendor}-{random.choice(['HDFC', 'ICICI', 'AXIS', 'SBI'])}-XXXXXXXXX{random.randint(1000, 9999)}"
    elif tx_type == 'upi':
        # UPI transactions
        upi_id = f"{vendor.split()[0].lower()}@{random.choice(['paytm', 'phonepe', 'gpay', 'ybl'])}"
        return f"{prefix}/{ref_num}/{vendor}/{upi_id}"
    elif tx_type == 'tpt':
        # TPT transactions
        return f"{ref_num[:17]}-{prefix}-{vendor}"
    else:
        # NEFT/Default
        bank_codes = ['HDFC0001234', 'ICIC0001234', 'UTIB0001234', 'SBIN0001234']
        return f"{prefix} DR-{random.choice(bank_codes)}-{vendor}-NETBANK, MUM-N{ref_num[:15]}"

def generate_synthetic_data(num_transactions=200):
    """Generate synthetic transaction data"""
    
    # Start date and opening balance
    start_date = datetime(2024, 4, 1)
    opening_balance = 100000.00
    
    transactions = []
    current_balance = opening_balance
    current_date = start_date
    
    # Add opening balance row
    transactions.append({
        'Month': start_date.replace(day=1),
        'Date': start_date.strftime('%d/%m/%y'),
        'Narration': 'Opening Balance',
        'Chq./Ref.No.': '',
        'Value Dt': start_date.strftime('%d/%m/%y'),
        'Withdrawal Amt.': np.nan,
        'Deposit Amt.': np.nan,
        'Closing Balance': opening_balance
    })
    
    # Generate transactions
    for i in range(num_transactions):
        # Random date progression (1-3 days)
        current_date += timedelta(days=random.randint(1, 3))
        
        # 70% expenses, 30% income
        is_expense = random.random() < 0.7
        
        if is_expense:
            # Select expense category
            category = random.choice(list(REAL_VENDORS['expenses'].keys()))
            vendor = random.choice(REAL_VENDORS['expenses'][category])
            
            # Amount based on category
            if category == 'salary':
                amount = random.choice([25000, 30000, 35000, 40000, 50000, 60000, 75000])
            elif category == 'rent':
                amount = random.choice([15000, 20000, 25000, 30000, 40000, 50000])
            elif category == 'professional_services':
                amount = random.uniform(5000, 50000)
            elif category == 'software':
                amount = random.choice([99, 199, 299, 499, 999, 1499, 2999, 4999])
            elif category == 'staff_welfare':
                amount = random.uniform(500, 5000)
            elif category == 'travel':
                amount = random.uniform(200, 10000)
            else:
                amount = random.uniform(100, 20000)
            
            amount = round(amount, 2)
            current_balance -= amount
            
            # Transaction type based on amount
            if amount < 2000:
                tx_type = random.choice(['pos', 'upi'])
            elif amount < 50000:
                tx_type = random.choice(['imps', 'neft', 'upi'])
            else:
                tx_type = random.choice(['neft', 'tpt'])
            
            transactions.append({
                'Month': current_date.replace(day=1),
                'Date': current_date.strftime('%d/%m/%y'),
                'Narration': generate_realistic_narration(vendor, tx_type),
                'Chq./Ref.No.': ''.join([str(random.randint(0, 9)) for _ in range(16)]),
                'Value Dt': (current_date + timedelta(days=random.randint(0, 1))).strftime('%d/%m/%y'),
                'Withdrawal Amt.': amount,
                'Deposit Amt.': np.nan,
                'Closing Balance': round(current_balance, 2)
            })
        else:
            # Income transaction
            category = random.choice(list(REAL_VENDORS['income'].keys()))
            vendor = random.choice(REAL_VENDORS['income'][category])
            
            # Amount based on category
            if category == 'sales':
                amount = random.choice([10000, 25000, 50000, 75000, 100000, 150000, 200000])
            else:
                amount = random.uniform(1000, 50000)
            
            amount = round(amount, 2)
            current_balance += amount
            
            # Income usually through NEFT/IMPS
            tx_type = random.choice(['neft', 'imps', 'tpt'])
            
            transactions.append({
                'Month': current_date.replace(day=1),
                'Date': current_date.strftime('%d/%m/%y'),
                'Narration': generate_realistic_narration(vendor, tx_type),
                'Chq./Ref.No.': ''.join([str(random.randint(0, 9)) for _ in range(16)]),
                'Value Dt': (current_date + timedelta(days=random.randint(0, 1))).strftime('%d/%m/%y'),
                'Withdrawal Amt.': np.nan,
                'Deposit Amt.': amount,
                'Closing Balance': round(current_balance, 2)
            })
    
    return pd.DataFrame(transactions)

# Generate the data
print("Generating realistic synthetic test data...")
df = generate_synthetic_data(200)

# Save to Excel (only columns up to Closing Balance as requested)
output_path = Path("/Users/<USER>/giki-ai-workspace/data/test_data/realistic_bank_statement.xlsx")
output_path.parent.mkdir(parents=True, exist_ok=True)

# Write to Excel with proper formatting
with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
    df.to_excel(writer, sheet_name='Transactions', index=False)
    
    # Get the workbook and worksheet
    workbook = writer.book
    worksheet = writer.sheets['Transactions']
    
    # Format columns
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        
        adjusted_width = min(max_length + 2, 50)
        worksheet.column_dimensions[column_letter].width = adjusted_width

print(f"✅ Created realistic test data at: {output_path}")
print(f"   - Total transactions: {len(df)}")
print(f"   - Date range: {df['Date'].iloc[1]} to {df['Date'].iloc[-1]}")
print(f"   - Opening balance: ₹{df['Closing Balance'].iloc[0]:,.2f}")
print(f"   - Closing balance: ₹{df['Closing Balance'].iloc[-1]:,.2f}")
print(f"   - Columns: {', '.join(df.columns)}")
print("\n⚠️  Note: No columns after 'Closing Balance' as requested!")

# Show sample transactions
print("\n=== SAMPLE TRANSACTIONS ===")
sample_indices = [1, 50, 100, 150, -1]  # Skip row 0 (opening balance)
for idx in sample_indices:
    if idx < len(df):
        row = df.iloc[idx]
        print(f"\nTransaction {idx}:")
        print(f"  Date: {row['Date']}")
        print(f"  Narration: {row['Narration']}")
        if pd.notna(row['Withdrawal Amt.']):
            print(f"  Withdrawal: ₹{row['Withdrawal Amt.']:,.2f}")
        if pd.notna(row['Deposit Amt.']):
            print(f"  Deposit: ₹{row['Deposit Amt.']:,.2f}")
        print(f"  Balance: ₹{row['Closing Balance']:,.2f}")