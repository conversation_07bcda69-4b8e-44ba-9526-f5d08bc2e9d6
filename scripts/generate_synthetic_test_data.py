#!/usr/bin/env python3
"""
Generate Comprehensive Synthetic Test Data
=========================================

Creates realistic synthetic financial data based on analysis of:
- Nuvie expense ledger (Indian business transactions)
- ICICI Bank statements (Indian bank format)
- Credit Card statements (US credit card format)
- Capital One statements (US bank format)
- SVB statements (Silicon Valley Bank format)

This script generates multiple datasets covering various scenarios:
1. Indian bank statements (HDFC, ICICI, Axis, SBI)
2. US bank statements (Chase, Wells Fargo, Bank of America)
3. Credit card statements
4. Mixed business expenses
5. Different business types (SaaS, E-commerce, Consulting, Manufacturing)
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import random
import json
from typing import Dict, List, Tuple, Optional
import argparse

# Load analysis results to understand patterns
def load_analysis_results():
    """Load all analysis JSON files"""
    analyses = {}
    analysis_files = [
        'analysis_nuvie.json',
        'analysis_icici.json', 
        'analysis_credit_card.json',
        'analysis_capital_one.json',
        'analysis_svb.json'
    ]
    
    for file in analysis_files:
        try:
            with open(file, 'r') as f:
                analyses[file.replace('analysis_', '').replace('.json', '')] = json.load(f)
        except:
            pass
    
    return analyses

# Real vendor data extracted from analysis
INDIAN_VENDORS = {
    'Software & Technology': [
        'GOOGLE WORKSPAC', 'MICROSOFT INDIA', 'ADOBE SYSTEMS INDIA',
        'ZOHO CORPORATION', 'FRESHWORKS INC', 'SLACK TECHNOLOGIES',
        'OPENAI *CHATGPT SUBSCR', 'GITHUB INC', 'ATLASSIAN JIRA',
        'CANVA PTY LTD', 'FIGMA INC', 'NOTION LABS INC',
        'AWS INDIA', 'DIGITALOCEAN INDIA', 'CLOUDFLARE INDIA'
    ],
    'Staff Welfare': [
        'SWIGGY PAYMENT TEAM', 'ZOMATO ORDER', 'DOMINOS PIZZA',
        'MCDONALDS INDIA', 'CAFE COFFEE DAY', 'STARBUCKS INDIA',
        'HALDIRAMS', 'BARBEQUE NATION', 'PVR CINEMAS TEAM EVENT',
        'BOOKMYSHOW TEAM OUTING', 'PIZZA HUT', 'KFC INDIA'
    ],
    'Travel & Transportation': [
        'UBER INDIA SYSTEMS PVT', 'OLA CABS BANGALORE', 'RAPIDO BIKE TAXI',
        'MAKEMYTRIP INDIA PVT', 'CLEARTRIP PVT LTD', 'YATRA ONLINE PRI',
        'IRCTC E TICKETING', 'INDIGO AIRLINES', 'SPICEJET LIMITED',
        'GOIBIBO HOTELS', 'AIR INDIA', 'VISTARA AIRLINES'
    ],
    'Utilities': [
        'BESCOM ELECTRICITY BILL', 'BWSSB WATER CHARGES', 'AIRTEL BROADBAND',
        'JIO FIBER CONNECTION', 'ACT FIBERNET', 'VODAFONE IDEA LIMITED',
        'BSNL LANDLINE', 'TATA POWER DELHI', 'MAHANAGAR GAS LIMITED',
        'INDANE GAS BOOKING', 'HP GAS CYLINDER'
    ],
    'Office Supplies': [
        'AMAZON PAY INDIA', 'FLIPKART INTERNET PVT', 'RELIANCE DIGITAL',
        'CROMA RETAIL', 'STAPLES FUTURE OFFICE', 'OFFICEWORKS INDIA',
        'WS RETAIL SERVICES', 'CLOUDTAIL INDIA PVT', 'VIJAY SALES'
    ],
    'Marketing & Advertising': [
        'FACEBOOK INDIA ONLINE', 'GOOGLE ADS INDIA', 'LINKEDIN INDIA',
        'TWITTER INDIA', 'INSTAGRAM BUSINESS', 'YOUTUBE ADVERTISING',
        'TIMES OF INDIA ADS', 'ECONOMIC TIMES', 'PRINT VISION INDIA'
    ],
    'Professional Services': [
        'DELOITTE HASKINS SELLS', 'KPMG INDIA PVT LTD', 'EY GDS INDIA',
        'PWC INDIA', 'GRANT THORNTON LLP', 'BDO INDIA LLP',
        'LEGAL SERVICES INDIA', 'COMPANY SECRETARY FIRM', 'TAX CONSULTANT'
    ],
    'Banking & Finance': [
        'HDFC BANK CHARGES', 'ICICI BANK FEES', 'AXIS BANK CHARGES',
        'SBI TRANSACTION CHARGES', 'KOTAK BANK FEES', 'YES BANK CHARGES',
        'RAZORPAY PROCESSING FEE', 'PAYTM GATEWAY CHARGES', 'PHONEPE CHARGES'
    ],
    'Rent & Facilities': [
        'MONTHLY OFFICE RENT', 'COWORKING SPACE RENT', 'WAREHOUSE RENT PAYMENT',
        'SECURITY DEPOSIT OFFICE', 'MAINTENANCE CHARGES', 'PROPERTY TAX PAYMENT'
    ],
    'Payroll': [
        'SALARY PAYMENT', 'EMPLOYEE REIMBURSEMENT', 'CONSULTANT PAYMENT',
        'FREELANCER PAYMENT', 'INTERN STIPEND', 'BONUS PAYMENT',
        'PF CONTRIBUTION', 'ESI CONTRIBUTION', 'GRATUITY PAYMENT'
    ]
}

US_VENDORS = {
    'Software & Technology': [
        'ADOBE Adobe Systems SAN JOSE CA', 'MICROSOFT', 'COGGLE AWESOME CAMBRIDGE CA',
        'ATLASSIAN SAN FRANCISCO CA', 'OPENAI *CHATGPT SUBSSAN FRANCISCO CA',
        'GITHUB', 'CHATGPT SUBSCRIPTIONSAN FRANCISCO CA', 'OPENAI SAN FRANCISCO CA',
        'CALENDLY AVONDALE ESTATES GA', 'ZOOM.US ************ CA',
        'GOOGLE *WORKSPACE', 'DROPBOX', 'SLACK', 'NOTION LABS'
    ],
    'Payroll & Benefits': [
        'GUSTO NET', 'GUSTO TAX', 'GUSTO REM', 'GUSTO CND',
        'ADP PAYROLL', 'PAYCHEX', 'RIPPLING', 'JUSTWORKS',
        'ANTHEM BLUE CORP PYMT', 'KAISER PERMANENTE', 'UNITED HEALTHCARE'
    ],
    'International Transfers': [
        'Wise Inc WISE', 'WISE US INC WISE', 'OYSTER HR',
        'DEEL PAYMENTS', 'REMOTE.COM', 'PAPAYA GLOBAL'
    ],
    'Insurance': [
        'NEXT INSUR (AP I NEXT INSUR', 'NEXT Insur (AP I NEXT Insur',
        'STATE FARM INSURANCE', 'GEICO', 'PROGRESSIVE'
    ],
    'Office & Facilities': [
        '580 EXECUTIVE CE SALE', 'WEWORK', 'REGUS OFFICES',
        'OFFICE DEPOT', 'STAPLES', 'BEST BUY BUSINESS'
    ],
    'Travel & Transport': [
        'UBER', 'LYFT', 'UNITED AIRLINES', 'AMERICAN AIRLINES',
        'DELTA AIR LINES', 'SOUTHWEST', 'MARRIOTT', 'HILTON'
    ],
    'Food & Entertainment': [
        'DOORDASH', 'UBER EATS', 'GRUBHUB', 'STARBUCKS',
        'CHIPOTLE', 'PANERA BREAD', 'WHOLE FOODS'
    ],
    'E-commerce': [
        'AMAZON.COM', 'AMAZON PRIME', 'WALMART.COM', 'TARGET.COM',
        'COSTCO WHOLESALE', 'SAMS CLUB', 'HOME DEPOT'
    ],
    'Utilities': [
        'PG&E PAYMENT', 'COMCAST BUSINESS', 'AT&T BUSINESS',
        'VERIZON BUSINESS', 'T-MOBILE BUSINESS', 'SPECTRUM BUSINESS'
    ],
    'Banking': [
        'WIRE Convenience Charge', 'Wire Withdrawal', 'ACH FEE',
        'OVERDRAFT FEE', 'MONTHLY SERVICE FEE', 'ATM FEE'
    ]
}

# Transaction patterns by region
INDIAN_TX_PATTERNS = {
    'prefixes': {
        'neft': 'NEFT', 'imps': 'IMPS', 'upi': 'UPI', 'rtgs': 'RTGS',
        'pos': 'POS', 'tpt': 'TPT', 'chq': 'CHQ DEP', 'cash': 'CASH'
    },
    'formats': {
        'neft': lambda v, ref: f"NEFT DR-{random.choice(['HDFC0001234', 'ICIC0001234', 'UTIB0001234', 'SBIN0001234'])}-{v}-NETBANK, MUM-{ref[:15]}",
        'imps': lambda v, ref: f"IMPS-{ref}-{v}-{random.choice(['HDFC', 'ICICI', 'AXIS', 'SBI'])}-XXXXXXXXX{random.randint(1000, 9999)}",
        'upi': lambda v, ref: f"UPI/{ref}/{v}/{v.split()[0].lower()}@{random.choice(['paytm', 'phonepe', 'gpay', 'ybl'])}",
        'rtgs': lambda v, ref: f"RTGS-{ref[:10]}-{v}-{random.choice(['MUM', 'DEL', 'BLR', 'CHE'])}",
        'pos': lambda v, ref: f"POS 403875XXXXXX{random.randint(1000, 9999)} {v}",
        'tpt': lambda v, ref: f"{ref[:17]}-TPT-{v}",
        'chq': lambda v, ref: f"CHQ DEP/{ref[:6]} {v}",
        'cash': lambda v, ref: f"CASH WITHDRAWAL {v}"
    }
}

US_TX_PATTERNS = {
    'prefixes': {
        'ach': 'ACH', 'wire': 'WIRE', 'check': 'CHECK', 'debit': 'DEBIT',
        'withdrawal': 'Withdrawal from', 'deposit': 'Deposit from'
    },
    'formats': {
        'ach': lambda v, ref: f"ACH Debit {v} REF#{ref[:10]}",
        'wire': lambda v, ref: f"Wire Transfer to {v} Conf#{ref[:8]}",
        'check': lambda v, ref: f"Check #{ref[:4]} {v}",
        'debit': lambda v, ref: f"Debit Card Purchase {v}",
        'withdrawal': lambda v, ref: f"Withdrawal from {v}",
        'deposit': lambda v, ref: f"Deposit from {v}"
    }
}

class SyntheticDataGenerator:
    """Generate synthetic financial data based on real patterns"""
    
    def __init__(self, output_dir: str = "libs/test-data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def generate_reference_number(self, length: int = 16) -> str:
        """Generate random reference number"""
        return ''.join([str(random.randint(0, 9)) for _ in range(length)])
    
    def generate_date_sequence(self, start_date: datetime, num_transactions: int, 
                             frequency: str = 'daily') -> List[datetime]:
        """Generate realistic date sequence"""
        dates = []
        current_date = start_date
        
        for _ in range(num_transactions):
            dates.append(current_date)
            
            if frequency == 'daily':
                # 1-3 days between transactions
                current_date += timedelta(days=random.randint(1, 3))
            elif frequency == 'weekly':
                # 5-9 days between transactions
                current_date += timedelta(days=random.randint(5, 9))
            elif frequency == 'monthly':
                # 25-35 days between transactions
                current_date += timedelta(days=random.randint(25, 35))
            else:
                # Mixed frequency
                current_date += timedelta(days=random.randint(1, 7))
        
        return dates
    
    def generate_amount_by_category(self, category: str, region: str = 'india') -> float:
        """Generate realistic amount based on category and region"""
        if region == 'india':
            amounts = {
                'Payroll': lambda: random.choice([25000, 30000, 35000, 40000, 50000, 60000, 75000, 100000]),
                'Rent & Facilities': lambda: random.choice([15000, 20000, 25000, 30000, 40000, 50000, 75000]),
                'Professional Services': lambda: random.uniform(5000, 50000),
                'Software & Technology': lambda: random.choice([99, 199, 299, 499, 999, 1499, 2999, 4999, 9999]),
                'Staff Welfare': lambda: random.uniform(500, 5000),
                'Travel & Transportation': lambda: random.uniform(200, 15000),
                'Utilities': lambda: random.uniform(500, 10000),
                'Office Supplies': lambda: random.uniform(100, 20000),
                'Marketing & Advertising': lambda: random.uniform(1000, 100000),
                'Banking & Finance': lambda: random.uniform(50, 5000)
            }
        else:  # US
            amounts = {
                'Payroll & Benefits': lambda: random.choice([3000, 4000, 5000, 6000, 8000, 10000, 12000]),
                'Office & Facilities': lambda: random.choice([2000, 3000, 4000, 5000, 7500, 10000]),
                'Software & Technology': lambda: random.choice([9.99, 19.99, 29.99, 49.99, 99.99, 199.99, 499.99]),
                'International Transfers': lambda: random.uniform(1000, 50000),
                'Insurance': lambda: random.uniform(500, 5000),
                'Travel & Transport': lambda: random.uniform(50, 2000),
                'Food & Entertainment': lambda: random.uniform(10, 500),
                'E-commerce': lambda: random.uniform(20, 5000),
                'Utilities': lambda: random.uniform(100, 1000),
                'Banking': lambda: random.uniform(10, 100)
            }
        
        category_key = next((k for k in amounts.keys() if category.startswith(k)), None)
        if category_key:
            return round(amounts[category_key](), 2)
        else:
            return round(random.uniform(100, 10000), 2)
    
    def generate_indian_bank_statement(self, bank_name: str, num_transactions: int = 500,
                                     business_type: str = 'mixed') -> pd.DataFrame:
        """Generate Indian bank statement format"""
        transactions = []
        
        # Starting values
        start_date = datetime(2024, 1, 1)
        opening_balance = random.uniform(100000, 500000)
        current_balance = opening_balance
        
        # Add opening balance row
        transactions.append({
            'Month': start_date.replace(day=1),
            'Date': start_date.strftime('%d/%m/%y'),
            'Narration': 'Opening Balance',
            'Chq./Ref.No.': '',
            'Value Dt': start_date.strftime('%d/%m/%y'),
            'Withdrawal Amt.': np.nan,
            'Deposit Amt.': np.nan,
            'Closing Balance': opening_balance
        })
        
        # Generate transaction dates
        dates = self.generate_date_sequence(start_date, num_transactions)
        
        # Transaction mix based on business type
        expense_ratio = 0.7 if business_type == 'mixed' else 0.8
        
        for date in dates:
            is_expense = random.random() < expense_ratio
            
            if is_expense:
                # Select expense category and vendor
                category = random.choice(list(INDIAN_VENDORS.keys()))
                vendor = random.choice(INDIAN_VENDORS[category])
                amount = self.generate_amount_by_category(category, 'india')
                
                # Transaction type based on amount
                if amount < 2000:
                    tx_type = random.choice(['pos', 'upi'])
                elif amount < 50000:
                    tx_type = random.choice(['imps', 'neft', 'upi'])
                else:
                    tx_type = random.choice(['neft', 'rtgs', 'tpt'])
                
                # Generate narration
                ref_num = self.generate_reference_number()
                narration = INDIAN_TX_PATTERNS['formats'][tx_type](vendor, ref_num)
                
                current_balance -= amount
                
                transactions.append({
                    'Month': date.replace(day=1),
                    'Date': date.strftime('%d/%m/%y'),
                    'Narration': narration,
                    'Chq./Ref.No.': ref_num,
                    'Value Dt': (date + timedelta(days=random.randint(0, 1))).strftime('%d/%m/%y'),
                    'Withdrawal Amt.': amount,
                    'Deposit Amt.': np.nan,
                    'Closing Balance': round(current_balance, 2)
                })
            else:
                # Income transaction
                income_sources = [
                    'CUSTOMER PAYMENT', 'CLIENT INVOICE PAYMENT', 'PRODUCT SALE',
                    'SERVICE REVENUE', 'SUBSCRIPTION PAYMENT', 'CONSULTING FEE',
                    'PROJECT MILESTONE', 'ADVANCE PAYMENT', 'FINAL SETTLEMENT',
                    'INTEREST CREDIT', 'GST REFUND', 'TDS REFUND'
                ]
                vendor = random.choice(income_sources)
                
                # Income amounts
                if business_type == 'saas':
                    amount = random.choice([999, 1999, 4999, 9999, 19999, 49999, 99999])
                elif business_type == 'consulting':
                    amount = random.choice([50000, 100000, 150000, 200000, 300000, 500000])
                else:
                    amount = random.uniform(10000, 200000)
                
                amount = round(amount, 2)
                current_balance += amount
                
                tx_type = random.choice(['neft', 'imps', 'rtgs', 'tpt'])
                ref_num = self.generate_reference_number()
                narration = f"{INDIAN_TX_PATTERNS['prefixes'][tx_type]} CR-{vendor}-{ref_num[:10]}"
                
                transactions.append({
                    'Month': date.replace(day=1),
                    'Date': date.strftime('%d/%m/%y'),
                    'Narration': narration,
                    'Chq./Ref.No.': ref_num,
                    'Value Dt': (date + timedelta(days=random.randint(0, 1))).strftime('%d/%m/%y'),
                    'Withdrawal Amt.': np.nan,
                    'Deposit Amt.': amount,
                    'Closing Balance': round(current_balance, 2)
                })
        
        return pd.DataFrame(transactions)
    
    def generate_us_bank_statement(self, bank_name: str, num_transactions: int = 300,
                                 business_type: str = 'mixed') -> pd.DataFrame:
        """Generate US bank statement format"""
        transactions = []
        
        # Starting values
        start_date = datetime(2024, 1, 1)
        opening_balance = random.uniform(10000, 100000)
        current_balance = opening_balance
        
        # Generate transaction dates
        dates = self.generate_date_sequence(start_date, num_transactions, 'weekly')
        
        for date in dates:
            is_expense = random.random() < 0.75
            
            if is_expense:
                # Select expense category and vendor
                category = random.choice(list(US_VENDORS.keys()))
                vendor = random.choice(US_VENDORS[category])
                amount = self.generate_amount_by_category(category, 'us')
                
                # Transaction type
                if category == 'International Transfers':
                    tx_type = 'wire'
                elif amount > 10000:
                    tx_type = random.choice(['wire', 'ach'])
                else:
                    tx_type = random.choice(['ach', 'withdrawal', 'debit'])
                
                # Generate description
                ref_num = self.generate_reference_number(10)
                description = US_TX_PATTERNS['formats'][tx_type](vendor, ref_num)
                
                current_balance -= amount
                
                transactions.append({
                    'Posted Date': date.strftime('%m/%d/%Y'),
                    'Description': description,
                    'Debit': amount,
                    'Credit': np.nan,
                    'Balance': round(current_balance, 2)
                })
            else:
                # Income transaction
                income_sources = [
                    'Customer Payment', 'Wire Transfer', 'ACH Credit',
                    'Merchant Services Deposit', 'PayPal Transfer',
                    'Stripe Payout', 'Square Deposit'
                ]
                source = random.choice(income_sources)
                
                # Income amounts
                if business_type == 'saas':
                    amount = random.choice([99, 199, 499, 999, 1999, 4999, 9999])
                elif business_type == 'ecommerce':
                    amount = random.uniform(100, 10000)
                else:
                    amount = random.uniform(1000, 50000)
                
                amount = round(amount, 2)
                current_balance += amount
                
                ref_num = self.generate_reference_number(8)
                description = f"{source} Ref#{ref_num}"
                
                transactions.append({
                    'Posted Date': date.strftime('%m/%d/%Y'),
                    'Description': description,
                    'Debit': np.nan,
                    'Credit': amount,
                    'Balance': round(current_balance, 2)
                })
        
        return pd.DataFrame(transactions)
    
    def generate_credit_card_statement(self, num_transactions: int = 200) -> pd.DataFrame:
        """Generate credit card statement"""
        transactions = []
        
        start_date = datetime(2024, 1, 1)
        dates = self.generate_date_sequence(start_date, num_transactions)
        
        # Mix of US vendors for credit card
        for date in dates:
            category = random.choice(list(US_VENDORS.keys()))
            vendor = random.choice(US_VENDORS[category])
            
            # Credit card amounts tend to be smaller
            if category == 'Software & Technology':
                amount = random.choice([9.99, 19.99, 29.99, 49.99, 99.99, 199.99])
            elif category == 'Food & Entertainment':
                amount = random.uniform(10, 200)
            elif category == 'Travel & Transport':
                amount = random.uniform(20, 1000)
            else:
                amount = random.uniform(50, 2000)
            
            amount = round(amount, 2)
            
            transactions.append({
                'Date': date.strftime('%m/%d/%Y'),
                'Description': vendor,
                'Category': category,
                'Amount': amount
            })
        
        return pd.DataFrame(transactions)
    
    def generate_complete_test_suite(self):
        """Generate comprehensive test data suite"""
        print("Generating comprehensive synthetic test data...")
        
        # Create subdirectories
        subdirs = ['indian-banks', 'us-banks', 'credit-cards', 'mixed-formats']
        for subdir in subdirs:
            (self.output_dir / subdir).mkdir(exist_ok=True)
        
        # 1. Indian Bank Statements
        indian_banks = [
            ('HDFC', 'mixed', 1000),
            ('ICICI', 'saas', 800),
            ('Axis', 'consulting', 600),
            ('SBI', 'ecommerce', 1200)
        ]
        
        for bank, business_type, num_tx in indian_banks:
            print(f"Generating {bank} bank statement ({business_type} business)...")
            df = self.generate_indian_bank_statement(bank, num_tx, business_type)
            filename = f"{bank.lower()}_bank_{business_type}_{num_tx}tx.xlsx"
            filepath = self.output_dir / 'indian-banks' / filename
            
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Transactions', index=False)
            
            print(f"  ✓ Created: {filepath}")
            print(f"    - Transactions: {len(df)}")
            print(f"    - Date range: {df['Date'].iloc[1]} to {df['Date'].iloc[-1]}")
            print(f"    - Opening balance: ₹{df['Closing Balance'].iloc[0]:,.2f}")
            print(f"    - Closing balance: ₹{df['Closing Balance'].iloc[-1]:,.2f}")
        
        # 2. US Bank Statements
        us_banks = [
            ('Chase', 'mixed', 500),
            ('Wells_Fargo', 'saas', 400),
            ('Bank_of_America', 'ecommerce', 600),
            ('Capital_One', 'consulting', 300)
        ]
        
        for bank, business_type, num_tx in us_banks:
            print(f"\nGenerating {bank} bank statement ({business_type} business)...")
            df = self.generate_us_bank_statement(bank, num_tx, business_type)
            filename = f"{bank.lower()}_{business_type}_{num_tx}tx.xlsx"
            filepath = self.output_dir / 'us-banks' / filename
            
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Transactions', index=False)
            
            print(f"  ✓ Created: {filepath}")
            print(f"    - Transactions: {len(df)}")
            if len(df) > 0:
                print(f"    - Date range: {df['Posted Date'].iloc[0]} to {df['Posted Date'].iloc[-1]}")
                print(f"    - Final balance: ${df['Balance'].iloc[-1]:,.2f}")
        
        # 3. Credit Card Statements
        credit_cards = [
            ('Visa', 300),
            ('Mastercard', 250),
            ('Amex', 200)
        ]
        
        for card, num_tx in credit_cards:
            print(f"\nGenerating {card} credit card statement...")
            df = self.generate_credit_card_statement(num_tx)
            filename = f"{card.lower()}_credit_card_{num_tx}tx.xlsx"
            filepath = self.output_dir / 'credit-cards' / filename
            
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Transactions', index=False)
            
            print(f"  ✓ Created: {filepath}")
            print(f"    - Transactions: {len(df)}")
            print(f"    - Total spend: ${df['Amount'].sum():,.2f}")
        
        # 4. Mixed Format Files (for schema interpretation testing)
        print("\nGenerating mixed format files for schema testing...")
        
        # Create a file with multiple sheets
        mixed_filepath = self.output_dir / 'mixed-formats' / 'multi_sheet_financial_data.xlsx'
        with pd.ExcelWriter(mixed_filepath, engine='openpyxl') as writer:
            # Indian format sheet
            indian_df = self.generate_indian_bank_statement('HDFC', 100)
            indian_df.to_excel(writer, sheet_name='Indian_Transactions', index=False)
            
            # US format sheet
            us_df = self.generate_us_bank_statement('Chase', 100)
            us_df.to_excel(writer, sheet_name='US_Transactions', index=False)
            
            # Credit card sheet
            cc_df = self.generate_credit_card_statement(50)
            cc_df.to_excel(writer, sheet_name='Credit_Card', index=False)
        
        print(f"  ✓ Created multi-sheet file: {mixed_filepath}")
        
        # 5. Create summary JSON with all generated files
        summary = {
            "generation_date": datetime.now().isoformat(),
            "total_files_created": len(indian_banks) + len(us_banks) + len(credit_cards) + 1,
            "categories": {
                "indian_banks": [f"{b[0].lower()}_bank_{b[1]}_{b[2]}tx.xlsx" for b in indian_banks],
                "us_banks": [f"{b[0].lower()}_{b[1]}_{b[2]}tx.xlsx" for b in us_banks],
                "credit_cards": [f"{c[0].lower()}_credit_card_{c[1]}tx.xlsx" for c in credit_cards],
                "mixed_formats": ["multi_sheet_financial_data.xlsx"]
            },
            "patterns_included": {
                "indian": list(INDIAN_TX_PATTERNS['prefixes'].keys()),
                "us": list(US_TX_PATTERNS['prefixes'].keys())
            },
            "vendor_categories": {
                "indian": list(INDIAN_VENDORS.keys()),
                "us": list(US_VENDORS.keys())
            }
        }
        
        summary_path = self.output_dir / 'test_data_summary.json'
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n✓ Created summary file: {summary_path}")
        print(f"\n🎉 Synthetic test data generation complete!")
        print(f"   Total files created: {summary['total_files_created']}")
        print(f"   Output directory: {self.output_dir}")


def main():
    parser = argparse.ArgumentParser(description="Generate comprehensive synthetic financial test data")
    parser.add_argument('-o', '--output', default='libs/test-data/synthetic',
                      help='Output directory for generated files')
    parser.add_argument('--quick', action='store_true',
                      help='Generate smaller dataset for quick testing')
    
    args = parser.parse_args()
    
    generator = SyntheticDataGenerator(args.output)
    
    if args.quick:
        # Generate smaller dataset
        print("Generating quick test dataset...")
        df = generator.generate_indian_bank_statement('HDFC', 50)
        filepath = Path(args.output) / 'quick_test_hdfc.xlsx'
        df.to_excel(filepath, index=False)
        print(f"Created: {filepath}")
    else:
        # Generate complete test suite
        generator.generate_complete_test_suite()


if __name__ == "__main__":
    main()