#!/bin/bash
# Setup production secrets and service accounts
set -e

PROJECT_ID="rezolve-poc"
SERVICE_ACCOUNT_EMAIL="dev-giki-ai-service-account@${PROJECT_ID}.iam.gserviceaccount.com"
SECRET_NAME="giki-ai-service-account"

echo "🔐 Setting up production secrets for giki.ai..."

# Create service account key if it doesn't exist
echo "Creating service account key..."
gcloud iam service-accounts keys create service-account-key.json \
    --iam-account="$SERVICE_ACCOUNT_EMAIL" \
    --project="$PROJECT_ID"

# Create secret in Secret Manager
echo "Creating secret in Secret Manager..."
gcloud secrets create "$SECRET_NAME" \
    --data-file="service-account-key.json" \
    --project="$PROJECT_ID" \
    --replication-policy="automatic" || true

# Update secret if it already exists
echo "Updating secret with new key..."
gcloud secrets versions add "$SECRET_NAME" \
    --data-file="service-account-key.json" \
    --project="$PROJECT_ID"

# Grant Cloud Run access to the secret
echo "Granting Cloud Run access to secret..."
gcloud secrets add-iam-policy-binding "$SECRET_NAME" \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/secretmanager.secretAccessor" \
    --project="$PROJECT_ID"

# Clean up the temporary key file
rm -f service-account-key.json

echo "✅ Production secrets setup complete!"
echo "🔗 Secret: projects/$PROJECT_ID/secrets/$SECRET_NAME"
echo "🔑 Service account: $SERVICE_ACCOUNT_EMAIL"