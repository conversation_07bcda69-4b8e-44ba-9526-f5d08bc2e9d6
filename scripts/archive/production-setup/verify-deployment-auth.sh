#!/bin/bash

# Verify deployment authentication and permissions
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PROJECT_ID="rezolve-poc"
FIREBASE_SA="firebase-deploy@${PROJECT_ID}.iam.gserviceaccount.com"
API_SA="dev-giki-ai-service-account@${PROJECT_ID}.iam.gserviceaccount.com"

print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_status "=== Deployment Authentication Verification ==="

# Check Firebase service account key
print_status "Checking Firebase service account key..."
if [ -f "firebase-service-account-key.json" ]; then
    SA_EMAIL=$(cat firebase-service-account-key.json | jq -r '.client_email' 2>/dev/null)
    if [ "$SA_EMAIL" = "$FIREBASE_SA" ]; then
        print_info "✅ Firebase service account key found for: $SA_EMAIL"
    else
        print_error "❌ Firebase key is for wrong account: $SA_EMAIL (expected: $FIREBASE_SA)"
    fi
else
    print_error "❌ firebase-service-account-key.json not found in workspace root"
fi

# Check API service account permissions
print_status "Checking API service account permissions..."
API_ROLES=$(gcloud projects get-iam-policy $PROJECT_ID \
    --flatten="bindings[].members" \
    --filter="bindings.members:serviceAccount:$API_SA" \
    --format="value(bindings.role)" 2>/dev/null | sort | uniq)

REQUIRED_API_ROLES=(
    "roles/aiplatform.admin"
    "roles/cloudsql.client"
    "roles/run.invoker"
    "roles/storage.admin"
)

for role in "${REQUIRED_API_ROLES[@]}"; do
    if echo "$API_ROLES" | grep -q "$role"; then
        print_info "✅ $role"
    else
        print_warning "⚠️  Missing: $role"
    fi
done

# Check Firebase service account permissions
print_status "Checking Firebase service account permissions..."
FIREBASE_ROLES=$(gcloud projects get-iam-policy $PROJECT_ID \
    --flatten="bindings[].members" \
    --filter="bindings.members:serviceAccount:$FIREBASE_SA" \
    --format="value(bindings.role)" 2>/dev/null | sort | uniq)

REQUIRED_FIREBASE_ROLES=(
    "roles/firebase.hosting.admin"
    "roles/firebase.admin"
)

for role in "${REQUIRED_FIREBASE_ROLES[@]}"; do
    if echo "$FIREBASE_ROLES" | grep -q "$role"; then
        print_info "✅ $role"
    else
        print_warning "⚠️  Missing: $role"
    fi
done

# Test Firebase authentication
print_status "Testing Firebase authentication with service account..."
export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/firebase-service-account-key.json"

if [ -f "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
    # Test Firebase access
    cd apps/giki-ai-app
    firebase projects:list --non-interactive 2>&1 | grep -q "$PROJECT_ID" && {
        print_info "✅ Firebase authentication working with service account"
    } || {
        print_warning "⚠️  Firebase authentication may have issues"
    }
    cd ../..
else
    print_error "❌ Cannot test Firebase auth - service account key missing"
fi

# Summary
echo ""
print_status "=== Summary ==="
echo "Project: $PROJECT_ID"
echo "API Service Account: $API_SA"
echo "Firebase Service Account: $FIREBASE_SA"
echo ""
echo "To deploy:"
echo "  API: pnpm nx deploy giki-ai-api"
echo "  Frontend: pnpm nx deploy giki-ai-app"
echo ""
echo "For issues, check:"
echo "  1. Service account keys exist and are valid"
echo "  2. Required IAM roles are granted"
echo "  3. GOOGLE_APPLICATION_CREDENTIALS is set correctly"