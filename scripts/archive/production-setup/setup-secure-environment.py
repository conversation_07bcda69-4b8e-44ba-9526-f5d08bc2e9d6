#!/usr/bin/env python3
"""
Secure Environment Setup Script for GIKI.AI
Generates secure credentials and creates proper environment files.
"""

import os
import secrets
import shutil
import subprocess
import sys
from pathlib import Path


def generate_secure_secret(length: int = 64) -> str:
    """Generate a cryptographically secure secret."""
    return secrets.token_urlsafe(length)


def generate_secure_admin_key(length: int = 32) -> str:
    """Generate a secure admin API key."""
    return secrets.token_urlsafe(length)


def create_env_file(template_path: Path, output_path: Path, secrets_map: dict) -> None:
    """Create environment file from template with secure secrets."""
    with open(template_path, 'r') as f:
        content = f.read()
    
    # Replace placeholders with actual secrets
    for placeholder, secret in secrets_map.items():
        content = content.replace(placeholder, secret)
    
    with open(output_path, 'w') as f:
        f.write(content)
    
    # Set restrictive permissions
    os.chmod(output_path, 0o600)
    print(f"✅ Created secure environment file: {output_path}")


def main():
    """Main setup function."""
    print("🔐 GIKI.AI Secure Environment Setup")
    print("=" * 50)
    
    # Get workspace root
    workspace_root = Path(__file__).parent.parent
    print(f"📁 Workspace: {workspace_root}")
    
    # Generate secure secrets
    print("\n🔑 Generating secure secrets...")
    dev_jwt_secret = generate_secure_secret(64)
    prod_jwt_secret = generate_secure_secret(64)
    dev_admin_key = generate_secure_admin_key(32)
    prod_admin_key = generate_secure_admin_key(32)
    
    print("✅ Secrets generated successfully")
    
    # Environment specific configurations
    environments = {
        'development': {
            'template': workspace_root / '.env.example',
            'output': workspace_root / '.env',
            'secrets': {
                'CHANGE_THIS_SECRET_KEY_USE_PYTHON_SECRETS_MODULE': dev_jwt_secret,
                'CHANGE_THIS_ADMIN_KEY_USE_PYTHON_SECRETS_MODULE': dev_admin_key,
                'your-gemini-api-key': 'REPLACE_WITH_YOUR_GEMINI_API_KEY',
                'your-project-id': 'your-gcp-project-id',
                'username:password@localhost:5432/database_name': 'giki_ai_user:YOUR_DB_PASSWORD@localhost:5432/giki_ai_db',
            }
        },
        'api_production': {
            'template': workspace_root / 'apps/giki-ai-api/.env.example',
            'output': workspace_root / 'apps/giki-ai-api/.env.production',
            'secrets': {
                'CHANGE_THIS_SECRET_KEY_USE_PYTHON_SECRETS_MODULE': prod_jwt_secret,
                'CHANGE_THIS_ADMIN_KEY_USE_PYTHON_SECRETS_MODULE': prod_admin_key,
                'your-gemini-api-key': 'REPLACE_WITH_YOUR_GEMINI_API_KEY',
                'your-project-id': 'your-gcp-project-id',
                'username:password@localhost:5432/database_name': 'giki_ai_user:YOUR_DB_PASSWORD@/giki_ai_db?host=/cloudsql/PROJECT:REGION:INSTANCE',
            }
        }
    }
    
    # Create environment files
    print("\n📄 Creating environment files...")
    for env_name, config in environments.items():
        if config['template'].exists():
            create_env_file(config['template'], config['output'], config['secrets'])
        else:
            print(f"⚠️  Template not found: {config['template']}")
    
    # Display security reminders
    print("\n🛡️  SECURITY REMINDERS:")
    print("=" * 50)
    print("1. Replace placeholder API keys with real values:")
    print("   - GEMINI_API_KEY: Get from Google Cloud Console")
    print("   - VERTEX_PROJECT_ID: Your GCP project ID")
    print("   - DATABASE_URL: Update with real database credentials")
    print()
    print("2. Never commit .env files to version control")
    print("3. Use different secrets for development and production")
    print("4. Rotate secrets regularly")
    print("5. Store production secrets in secure secret management")
    print()
    print("🔒 Secrets generated:")
    print(f"   Dev JWT Secret: {dev_jwt_secret[:20]}...")
    print(f"   Prod JWT Secret: {prod_jwt_secret[:20]}...")
    print(f"   Dev Admin Key: {dev_admin_key[:20]}...")
    print(f"   Prod Admin Key: {prod_admin_key[:20]}...")
    print()
    print("✅ Secure environment setup complete!")


if __name__ == "__main__":
    main()