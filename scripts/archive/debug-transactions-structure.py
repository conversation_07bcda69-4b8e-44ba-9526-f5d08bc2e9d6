#!/usr/bin/env python3
"""
Debug transactions table structure and data
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def debug_transactions_structure():
    """Debug transactions table structure and sample data"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check complete transactions table structure
        print("📊 Complete transactions table structure:")
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'transactions' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        for col in columns:
            default = col['column_default'] or 'None'
            nullable = 'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'
            print(f"  - {col['column_name']}: {col['data_type']} ({nullable}) default={default}")
        
        # Check how many transactions exist
        count = await conn.fetchval("SELECT COUNT(*) FROM transactions")
        print(f"\n📋 Total transactions: {count}")
        
        if count > 0:
            print("\n🔍 Sample transaction data (first 2 rows):")
            sample_transactions = await conn.fetch("SELECT * FROM transactions LIMIT 2")
            for i, tx in enumerate(sample_transactions, 1):
                print(f"\nTransaction {i}:")
                tx_dict = dict(tx)
                for key, value in tx_dict.items():
                    print(f"  {key}: {value} ({type(value).__name__})")
        
        # Check for specific fields that might be causing issues
        print("\n🔍 Checking ID field types:")
        id_info = await conn.fetch("""
            SELECT id, category_id, tenant_id
            FROM transactions 
            LIMIT 3
        """)
        
        for tx in id_info:
            print(f"  ID: {tx['id']} (type: {type(tx['id']).__name__})")
            print(f"  Category ID: {tx['category_id']} (type: {type(tx['category_id']).__name__})")
            print(f"  Tenant ID: {tx['tenant_id']} (type: {type(tx['tenant_id']).__name__})")
            print("  ---")
            
        print("\n✅ Debugging completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error debugging transactions: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(debug_transactions_structure())