#!/usr/bin/env python3
"""
Add a string_id column to transactions table as a workaround for Pydantic UUID issues
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def add_string_id_column():
    """Add string_id column and populate it with UUID as string"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check if string_id column already exists
        print("🔍 Checking if string_id column exists...")
        existing = await conn.fetchrow("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'transactions' 
            AND column_name = 'string_id'
            AND table_schema = 'public'
        """)
        
        if not existing:
            # Add string_id column
            print("📝 Adding string_id column...")
            await conn.execute("""
                ALTER TABLE transactions 
                ADD COLUMN string_id VARCHAR(36)
            """)
            print("✅ Added string_id column")
        else:
            print("⏭️ string_id column already exists")
        
        # Populate string_id with UUID as string
        print("📝 Populating string_id with UUID values...")
        await conn.execute("""
            UPDATE transactions 
            SET string_id = id::text 
            WHERE string_id IS NULL
        """)
        print("✅ Populated string_id column")
        
        # Create index on string_id for performance
        print("📝 Creating index on string_id...")
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_transactions_string_id 
            ON transactions(string_id)
        """)
        print("✅ Created index on string_id")
        
        # Create trigger to auto-populate string_id for new records
        print("📝 Creating trigger for auto-populating string_id...")
        await conn.execute("""
            CREATE OR REPLACE FUNCTION update_string_id()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.string_id = NEW.id::text;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        await conn.execute("""
            DROP TRIGGER IF EXISTS tr_update_string_id ON transactions;
        """)
        
        await conn.execute("""
            CREATE TRIGGER tr_update_string_id
                BEFORE INSERT OR UPDATE ON transactions
                FOR EACH ROW
                EXECUTE FUNCTION update_string_id();
        """)
        print("✅ Created trigger for string_id auto-population")
        
        # Verify the setup
        print("\n🔍 Verifying string_id setup...")
        sample = await conn.fetchrow("""
            SELECT id, string_id, description 
            FROM transactions 
            LIMIT 1
        """)
        
        if sample:
            print(f"Sample verification:")
            print(f"  UUID id: {sample['id']} ({type(sample['id']).__name__})")
            print(f"  String id: {sample['string_id']} ({type(sample['string_id']).__name__})")
            print(f"  Match: {str(sample['id']) == sample['string_id']}")
        
        print("\n✅ String ID column setup completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up string_id column: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(add_string_id_column())