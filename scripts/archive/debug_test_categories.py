#!/usr/bin/env python3
"""
Debug Test Categories
====================

Debug the categorization test discrepancy by testing specific cases
and seeing what our system actually returns vs what's expected.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "apps" / "giki-ai-api" / "src"))

from giki_ai_api.domains.categories.business_category_mapper import business_category_mapper


def debug_comprehensive_test_cases():
    """Debug the comprehensive test cases to see exact outputs."""
    
    # Test cases from the comprehensive test framework
    test_cases = [
        {
            "description": "NEXT Insurance Premium Payment",
            "amount": -1406.11,
            "vendor": None,
            "expected": "Insurance Expense"
        },
        {
            "description": "Auto Insurance - State Farm",
            "amount": -450.00,
            "vendor": "State Farm",
            "expected": "Insurance Expense"
        },
        {
            "description": "Microsoft Office 365 Subscription",
            "amount": -19.99,
            "vendor": "Microsoft",
            "expected": "Software & Technology"
        },
        {
            "description": "AWS Cloud Services",
            "amount": -156.78,
            "vendor": "Amazon",
            "expected": "Software & Technology"
        },
        {
            "description": "Uber ride to airport",
            "amount": -35.50,
            "vendor": "Uber",
            "expected": "Travel & Transportation"
        },
        {
            "description": "Hotel booking for business trip",
            "amount": -289.00,
            "vendor": None,
            "expected": "Travel & Transportation"
        },
        {
            "description": "ACH Credit Consulting Payment",
            "amount": 45980.68,
            "vendor": None,
            "expected": "Consulting Income"
        },
        {
            "description": "Client payment for web development",
            "amount": 5000.00,
            "vendor": None,
            "expected": "Service Revenue"
        },
        {
            "description": "Stripe payment from customer",
            "amount": 299.99,
            "vendor": "Stripe",
            "expected": "Sales Revenue"
        },
        {
            "description": "Office supplies from Staples",
            "amount": -67.43,
            "vendor": "Staples",
            "expected": "Office Supplies"
        },
        {
            "description": "Client lunch at restaurant",
            "amount": -85.60,
            "vendor": None,
            "expected": "Meals & Entertainment"
        },
        {
            "description": "Starbucks coffee meeting",
            "amount": -12.45,
            "vendor": "Starbucks",
            "expected": "Meals & Entertainment"
        },
        {
            "description": "Bank service charge",
            "amount": -25.00,
            "vendor": None,
            "expected": "Banking & Finance"
        },
        {
            "description": "Refund from cancelled order",
            "amount": 150.00,
            "vendor": None,
            "expected": "Refunds & Credits"
        }
    ]
    
    print("🔍 DEBUGGING COMPREHENSIVE TEST CASES")
    print("=" * 60)
    
    correct = 0
    total = len(test_cases)
    mismatches = []
    
    for i, case in enumerate(test_cases, 1):
        try:
            result = business_category_mapper.categorize_transaction(
                description=case["description"],
                amount=case["amount"],
                vendor=case["vendor"]
            )
            
            # Check for exact match or partial match
            exact_match = case["expected"] == result.category_name
            partial_match = (case["expected"].lower() in result.category_name.lower() or 
                           result.category_name.lower() in case["expected"].lower())
            
            if exact_match:
                correct += 1
                status = "✅ EXACT"
            elif partial_match:
                correct += 1
                status = "✅ PARTIAL"
            else:
                status = "❌ MISMATCH"
                mismatches.append({
                    'description': case['description'][:50],
                    'expected': case['expected'],
                    'got': result.category_name,
                    'confidence': result.confidence
                })
            
            print(f"{status} Test {i}: {case['description'][:50]}...")
            print(f"   Expected: {case['expected']}")
            print(f"   Got: {result.category_name}")
            print(f"   Parent: {result.parent_category}")
            print(f"   Confidence: {result.confidence:.2f}")
            print()
            
        except Exception as e:
            print(f"❌ Test {i}: ERROR - {e}")
            print(f"   Description: {case['description']}")
            print()
    
    accuracy = correct / total
    print("=" * 60)
    print(f"📊 RESULTS: {correct}/{total} correct ({accuracy:.1%})")
    
    if mismatches:
        print(f"\n❌ CATEGORY MISMATCHES ({len(mismatches)}):")
        print("-" * 40)
        for mismatch in mismatches:
            print(f"• {mismatch['description']}...")
            print(f"  Expected: {mismatch['expected']}")
            print(f"  Got: {mismatch['got']} (conf: {mismatch['confidence']:.2f})")
            print()
    
    # Check our available categories
    print("\n🏷️ AVAILABLE EXPENSE CATEGORIES:")
    expense_patterns = business_category_mapper._build_expense_patterns()
    for category in expense_patterns.keys():
        print(f"   • {category}")
    
    print("\n🏷️ AVAILABLE INCOME CATEGORIES:")
    income_patterns = business_category_mapper._build_income_patterns()
    for category in income_patterns.keys():
        print(f"   • {category}")
    
    return accuracy, mismatches


if __name__ == "__main__":
    accuracy, mismatches = debug_comprehensive_test_cases()
    
    print("\n" + "="*60)
    print("DEBUGGING SUMMARY")
    print("="*60)
    print(f"Accuracy: {accuracy:.1%}")
    print(f"Mismatches: {len(mismatches)}")
    
    if accuracy < 0.7:
        print("\n🔧 RECOMMENDED FIXES:")
        for mismatch in mismatches:
            expected = mismatch['expected']
            got = mismatch['got']
            print(f"   • Add '{expected}' pattern or map '{got}' → '{expected}'")
    print("="*60)