#!/usr/bin/env python3
"""
Universal Excel Analyzer
=======================

A general-purpose script to read and analyze ANY Excel file:
- Reads all sheets in the file
- Detects column types and patterns
- Identifies transaction/financial data
- Extracts vendor patterns, date formats, amount structures
- Provides comprehensive analysis for synthetic data generation

This replaces specific scripts with a universal analyzer.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
from datetime import datetime
import re
from typing import Dict, List, Any, Optional, Tuple
import argparse
from collections import Counter, defaultdict


class UniversalExcelAnalyzer:
    """Analyzes any Excel file to understand its structure and extract patterns."""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.analysis_results = {}
        
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Main method to analyze any Excel file."""
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
            
        print(f"\n{'='*60}")
        print(f"Analyzing: {path.name}")
        print(f"Size: {path.stat().st_size / 1024:.1f} KB")
        print(f"{'='*60}\n")
        
        # Read all sheets
        try:
            excel_file = pd.ExcelFile(file_path)
            sheets = excel_file.sheet_names
            print(f"Found {len(sheets)} sheet(s): {sheets}\n")
        except Exception as e:
            print(f"Error reading Excel file: {e}")
            return {}
        
        file_analysis = {
            "file_name": path.name,
            "file_path": str(path),
            "file_size_kb": path.stat().st_size / 1024,
            "sheets": {}
        }
        
        # Analyze each sheet
        for sheet_name in sheets:
            print(f"\n{'-'*40}")
            print(f"Analyzing sheet: '{sheet_name}'")
            print(f"{'-'*40}")
            
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                sheet_analysis = self._analyze_sheet(df, sheet_name)
                file_analysis["sheets"][sheet_name] = sheet_analysis
                
                # Print summary
                self._print_sheet_summary(sheet_analysis)
                
            except Exception as e:
                print(f"Error analyzing sheet '{sheet_name}': {e}")
                file_analysis["sheets"][sheet_name] = {"error": str(e)}
        
        # Detect if this is financial/transaction data
        file_analysis["is_financial_data"] = self._detect_financial_data(file_analysis)
        file_analysis["detected_patterns"] = self._extract_global_patterns(file_analysis)
        
        return file_analysis
    
    def _analyze_sheet(self, df: pd.DataFrame, sheet_name: str) -> Dict[str, Any]:
        """Analyze a single sheet comprehensively."""
        analysis = {
            "sheet_name": sheet_name,
            "shape": {"rows": len(df), "columns": len(df.columns)},
            "columns": {},
            "data_quality": {},
            "patterns": {}
        }
        
        # Skip empty sheets
        if df.empty:
            analysis["is_empty"] = True
            return analysis
        
        # Analyze each column
        for col in df.columns:
            col_analysis = self._analyze_column(df[col])
            analysis["columns"][str(col)] = col_analysis
        
        # Detect special patterns
        analysis["patterns"]["date_columns"] = self._detect_date_columns(df)
        analysis["patterns"]["amount_columns"] = self._detect_amount_columns(df)
        analysis["patterns"]["description_columns"] = self._detect_description_columns(df)
        analysis["patterns"]["balance_columns"] = self._detect_balance_columns(df)
        analysis["patterns"]["transaction_type_columns"] = self._detect_transaction_type_columns(df)
        
        # Extract sample data
        analysis["sample_data"] = self._get_sample_data(df)
        
        # Detect vendors/merchants if applicable
        if analysis["patterns"]["description_columns"]:
            desc_col = analysis["patterns"]["description_columns"][0]
            analysis["patterns"]["vendors"] = self._extract_vendors(df[desc_col])
            analysis["patterns"]["transaction_patterns"] = self._extract_transaction_patterns(df[desc_col])
        
        return analysis
    
    def _analyze_column(self, series: pd.Series) -> Dict[str, Any]:
        """Analyze a single column."""
        col_info = {
            "dtype": str(series.dtype),
            "null_count": int(series.isnull().sum()),
            "null_percentage": float(series.isnull().sum() / len(series) * 100),
            "unique_count": int(series.nunique()),
            "unique_percentage": float(series.nunique() / len(series) * 100)
        }
        
        # Get sample values
        non_null = series.dropna()
        if len(non_null) > 0:
            col_info["sample_values"] = [str(v) for v in non_null.head(5).tolist()]
            
            # Numeric analysis
            if pd.api.types.is_numeric_dtype(series):
                col_info["numeric_stats"] = {
                    "min": float(non_null.min()),
                    "max": float(non_null.max()),
                    "mean": float(non_null.mean()),
                    "std": float(non_null.std()) if len(non_null) > 1 else 0,
                    "all_positive": bool((non_null >= 0).all()),
                    "all_negative": bool((non_null <= 0).all()),
                    "has_decimals": bool(any(v != int(v) for v in non_null if pd.notna(v)))
                }
            
            # String analysis
            elif pd.api.types.is_string_dtype(series) or pd.api.types.is_object_dtype(series):
                str_series = series.astype(str)
                col_info["string_stats"] = {
                    "avg_length": float(str_series.str.len().mean()),
                    "max_length": int(str_series.str.len().max()),
                    "has_numbers": bool(str_series.str.contains(r'\d', na=False).any()),
                    "has_special_chars": bool(str_series.str.contains(r'[^a-zA-Z0-9\s]', na=False).any()),
                    "common_patterns": self._detect_string_patterns(str_series)
                }
        
        return col_info
    
    def _detect_string_patterns(self, series: pd.Series) -> List[str]:
        """Detect common patterns in string data."""
        patterns = []
        sample = series.dropna().head(100)
        
        # Date patterns
        date_patterns = [
            r'\d{1,2}[-/]\d{1,2}[-/]\d{2,4}',  # DD/MM/YYYY or MM/DD/YYYY
            r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',     # YYYY-MM-DD
            r'\d{1,2}-\w{3}-\d{2,4}'            # DD-MMM-YYYY
        ]
        
        for pattern in date_patterns:
            if sample.str.contains(pattern, na=False).any():
                patterns.append(f"date_pattern: {pattern}")
        
        # Currency patterns
        if sample.str.contains(r'[₹$€£]', na=False).any():
            patterns.append("currency_symbols")
        
        # Transaction type patterns
        transaction_keywords = ['NEFT', 'IMPS', 'UPI', 'RTGS', 'ACH', 'WIRE', 'POS', 'ATM']
        for keyword in transaction_keywords:
            if sample.str.contains(keyword, case=False, na=False).any():
                patterns.append(f"transaction_type: {keyword}")
        
        return patterns
    
    def _detect_date_columns(self, df: pd.DataFrame) -> List[str]:
        """Detect columns that contain dates."""
        date_columns = []
        
        for col in df.columns:
            # Check column name
            if any(term in str(col).lower() for term in ['date', 'dt', 'time', 'period', 'month', 'year']):
                date_columns.append(col)
                continue
            
            # Try to parse as date
            try:
                pd.to_datetime(df[col], errors='coerce')
                non_null_dates = pd.to_datetime(df[col], errors='coerce').notna().sum()
                if non_null_dates > len(df) * 0.5:  # More than 50% valid dates
                    date_columns.append(col)
            except:
                pass
        
        return date_columns
    
    def _detect_amount_columns(self, df: pd.DataFrame) -> List[str]:
        """Detect columns that contain monetary amounts."""
        amount_columns = []
        
        for col in df.columns:
            # Check column name
            col_lower = str(col).lower()
            if any(term in col_lower for term in ['amount', 'amt', 'value', 'debit', 'credit', 'withdrawal', 'deposit', 'payment', 'price', 'cost', 'total']):
                if pd.api.types.is_numeric_dtype(df[col]) or self._is_numeric_string(df[col]):
                    amount_columns.append(col)
        
        return amount_columns
    
    def _detect_description_columns(self, df: pd.DataFrame) -> List[str]:
        """Detect columns that contain transaction descriptions."""
        desc_columns = []
        
        for col in df.columns:
            col_lower = str(col).lower()
            if any(term in col_lower for term in ['description', 'desc', 'narration', 'particular', 'detail', 'memo', 'remark', 'comment']):
                desc_columns.append(col)
            elif pd.api.types.is_string_dtype(df[col]) or pd.api.types.is_object_dtype(df[col]):
                # Check if it has high text variety (likely descriptions)
                if df[col].nunique() > len(df) * 0.3:  # High uniqueness
                    desc_columns.append(col)
        
        return desc_columns
    
    def _detect_balance_columns(self, df: pd.DataFrame) -> List[str]:
        """Detect columns that contain balance information."""
        balance_columns = []
        
        for col in df.columns:
            col_lower = str(col).lower()
            if any(term in col_lower for term in ['balance', 'bal', 'closing', 'opening', 'running']):
                if pd.api.types.is_numeric_dtype(df[col]) or self._is_numeric_string(df[col]):
                    balance_columns.append(col)
        
        return balance_columns
    
    def _detect_transaction_type_columns(self, df: pd.DataFrame) -> List[str]:
        """Detect columns that indicate transaction type (debit/credit)."""
        type_columns = []
        
        for col in df.columns:
            col_lower = str(col).lower()
            if any(term in col_lower for term in ['type', 'dr/cr', 'drcr', 'debit/credit', 'transaction_type']):
                type_columns.append(col)
            elif pd.api.types.is_string_dtype(df[col]) or pd.api.types.is_object_dtype(df[col]):
                # Check for DR/CR patterns
                sample = df[col].dropna().astype(str).head(100)
                if sample.isin(['DR', 'CR', 'D', 'C', 'Debit', 'Credit', 'Dr', 'Cr']).any():
                    type_columns.append(col)
        
        return type_columns
    
    def _is_numeric_string(self, series: pd.Series) -> bool:
        """Check if a string column contains numeric values."""
        try:
            # Try converting to numeric
            pd.to_numeric(series.astype(str).str.replace(',', '').str.replace('₹', '').str.replace('$', ''), errors='coerce')
            return True
        except:
            return False
    
    def _extract_vendors(self, series: pd.Series, top_n: int = 20) -> Dict[str, int]:
        """Extract vendor/merchant patterns from description column."""
        if series.empty:
            return {}
        
        vendors = []
        for desc in series.dropna().astype(str):
            # Simple vendor extraction - take first part before common delimiters
            vendor = desc.split('/')[0].split('-')[0].split(',')[0].strip()
            if len(vendor) > 3:  # Minimum vendor name length
                vendors.append(vendor)
        
        # Count frequencies
        vendor_counts = Counter(vendors)
        return dict(vendor_counts.most_common(top_n))
    
    def _extract_transaction_patterns(self, series: pd.Series) -> Dict[str, int]:
        """Extract transaction patterns like NEFT, IMPS, etc."""
        patterns = defaultdict(int)
        keywords = ['NEFT', 'IMPS', 'UPI', 'RTGS', 'POS', 'ATM', 'ACH', 'WIRE', 'CHQ', 'CASH', 'TPT', 'REVERSAL', 'REFUND']
        
        for desc in series.dropna().astype(str):
            desc_upper = desc.upper()
            for keyword in keywords:
                if keyword in desc_upper:
                    patterns[keyword] += 1
        
        return dict(patterns)
    
    def _get_sample_data(self, df: pd.DataFrame, n_rows: int = 5) -> List[Dict[str, Any]]:
        """Get sample rows from the dataframe."""
        sample_rows = []
        for _, row in df.head(n_rows).iterrows():
            sample_rows.append({str(k): str(v) if pd.notna(v) else None for k, v in row.items()})
        return sample_rows
    
    def _detect_financial_data(self, file_analysis: Dict[str, Any]) -> bool:
        """Detect if the file contains financial/transaction data."""
        financial_indicators = 0
        total_indicators = 0
        
        for sheet_name, sheet_data in file_analysis["sheets"].items():
            if isinstance(sheet_data, dict) and "patterns" in sheet_data:
                patterns = sheet_data["patterns"]
                
                # Check for financial indicators
                if patterns.get("date_columns"):
                    financial_indicators += 1
                total_indicators += 1
                
                if patterns.get("amount_columns"):
                    financial_indicators += 1
                total_indicators += 1
                
                if patterns.get("description_columns"):
                    financial_indicators += 1
                total_indicators += 1
                
                if patterns.get("balance_columns"):
                    financial_indicators += 1
                total_indicators += 1
                
                # Check for transaction patterns
                if patterns.get("transaction_patterns"):
                    financial_indicators += 1
                total_indicators += 1
        
        # Consider it financial data if we have at least 3 out of 5 indicators
        return financial_indicators >= 3 if total_indicators > 0 else False
    
    def _extract_global_patterns(self, file_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Extract patterns across all sheets."""
        global_patterns = {
            "all_vendors": {},
            "all_transaction_types": {},
            "date_formats": set(),
            "amount_ranges": {"min": float('inf'), "max": float('-inf')},
            "detected_bank": None,
            "detected_region": None
        }
        
        for sheet_name, sheet_data in file_analysis["sheets"].items():
            if isinstance(sheet_data, dict) and "patterns" in sheet_data:
                patterns = sheet_data["patterns"]
                
                # Aggregate vendors
                if patterns.get("vendors"):
                    for vendor, count in patterns["vendors"].items():
                        global_patterns["all_vendors"][vendor] = global_patterns["all_vendors"].get(vendor, 0) + count
                
                # Aggregate transaction types
                if patterns.get("transaction_patterns"):
                    for trans_type, count in patterns["transaction_patterns"].items():
                        global_patterns["all_transaction_types"][trans_type] = global_patterns["all_transaction_types"].get(trans_type, 0) + count
                
                # Check columns for amount ranges
                for col_name, col_data in sheet_data.get("columns", {}).items():
                    if "numeric_stats" in col_data:
                        stats = col_data["numeric_stats"]
                        global_patterns["amount_ranges"]["min"] = min(global_patterns["amount_ranges"]["min"], stats["min"])
                        global_patterns["amount_ranges"]["max"] = max(global_patterns["amount_ranges"]["max"], stats["max"])
        
        # Detect bank from filename or patterns
        file_name = file_analysis["file_name"].lower()
        bank_keywords = {
            "hdfc": "HDFC Bank",
            "icici": "ICICI Bank",
            "sbi": "State Bank of India",
            "axis": "Axis Bank",
            "kotak": "Kotak Mahindra Bank",
            "chase": "Chase Bank",
            "capital one": "Capital One",
            "svb": "Silicon Valley Bank",
            "wells fargo": "Wells Fargo",
            "bank of america": "Bank of America",
            "boa": "Bank of America"
        }
        
        for keyword, bank_name in bank_keywords.items():
            if keyword in file_name:
                global_patterns["detected_bank"] = bank_name
                break
        
        # Detect region from transaction patterns
        indian_patterns = ["NEFT", "IMPS", "UPI", "RTGS"]
        us_patterns = ["ACH", "WIRE"]
        
        indian_count = sum(global_patterns["all_transaction_types"].get(p, 0) for p in indian_patterns)
        us_count = sum(global_patterns["all_transaction_types"].get(p, 0) for p in us_patterns)
        
        if indian_count > us_count:
            global_patterns["detected_region"] = "India"
        elif us_count > indian_count:
            global_patterns["detected_region"] = "US"
        
        return global_patterns
    
    def _print_sheet_summary(self, sheet_analysis: Dict[str, Any]) -> None:
        """Print a summary of sheet analysis."""
        if sheet_analysis.get("is_empty"):
            print("Sheet is empty.")
            return
        
        print(f"Shape: {sheet_analysis['shape']['rows']} rows × {sheet_analysis['shape']['columns']} columns")
        
        # Print detected patterns
        patterns = sheet_analysis["patterns"]
        if patterns.get("date_columns"):
            print(f"Date columns: {patterns['date_columns']}")
        if patterns.get("amount_columns"):
            print(f"Amount columns: {patterns['amount_columns']}")
        if patterns.get("description_columns"):
            print(f"Description columns: {patterns['description_columns']}")
        if patterns.get("balance_columns"):
            print(f"Balance columns: {patterns['balance_columns']}")
        
        # Print top vendors if found
        if patterns.get("vendors"):
            print(f"\nTop vendors/merchants:")
            for vendor, count in list(patterns["vendors"].items())[:10]:
                print(f"  - {vendor}: {count} transactions")
        
        # Print transaction patterns
        if patterns.get("transaction_patterns"):
            print(f"\nTransaction types:")
            for trans_type, count in patterns["transaction_patterns"].items():
                print(f"  - {trans_type}: {count}")


def main():
    """Main function to run the analyzer."""
    parser = argparse.ArgumentParser(description="Analyze any Excel file to understand its structure and patterns")
    parser.add_argument("file_path", help="Path to the Excel file to analyze")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    parser.add_argument("-o", "--output", help="Save analysis results to JSON file")
    parser.add_argument("-a", "--analyze-all", action="store_true", help="Analyze all Excel files in directory")
    
    args = parser.parse_args()
    
    analyzer = UniversalExcelAnalyzer(verbose=args.verbose)
    
    if args.analyze_all:
        # Analyze all Excel files in directory
        path = Path(args.file_path)
        if path.is_dir():
            excel_files = list(path.glob("*.xlsx")) + list(path.glob("*.xls"))
            print(f"Found {len(excel_files)} Excel files in {path}\n")
            
            all_results = {}
            for excel_file in excel_files:
                try:
                    results = analyzer.analyze_file(str(excel_file))
                    all_results[str(excel_file)] = results
                except Exception as e:
                    print(f"Error analyzing {excel_file}: {e}")
            
            if args.output:
                with open(args.output, 'w') as f:
                    json.dump(all_results, f, indent=2, default=str)
                print(f"\nAnalysis results saved to: {args.output}")
        else:
            print(f"Error: {path} is not a directory")
    else:
        # Analyze single file
        try:
            results = analyzer.analyze_file(args.file_path)
            
            # Print summary
            print(f"\n{'='*60}")
            print("SUMMARY")
            print(f"{'='*60}")
            print(f"Is financial data: {results.get('is_financial_data', False)}")
            
            if results.get("detected_patterns"):
                patterns = results["detected_patterns"]
                if patterns.get("detected_bank"):
                    print(f"Detected bank: {patterns['detected_bank']}")
                if patterns.get("detected_region"):
                    print(f"Detected region: {patterns['detected_region']}")
                if patterns.get("all_vendors"):
                    print(f"Total unique vendors: {len(patterns['all_vendors'])}")
                if patterns.get("all_transaction_types"):
                    print(f"Transaction types found: {list(patterns['all_transaction_types'].keys())}")
            
            # Save to file if requested
            if args.output:
                with open(args.output, 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                print(f"\nAnalysis results saved to: {args.output}")
                
        except Exception as e:
            print(f"Error: {e}")
            sys.exit(1)


if __name__ == "__main__":
    main()