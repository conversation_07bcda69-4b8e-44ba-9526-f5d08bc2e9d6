#!/usr/bin/env python3
"""
<PERSON>ript to fix async mock patterns in Reports API tests.
"""

import re
from pathlib import Path

def fix_async_mocks():
    """Fix async mock patterns in test file."""
    test_file = Path("/Users/<USER>/giki-ai-workspace/apps/giki-ai-api/tests/unit/test_reports_api.py")
    content = test_file.read_text()
    
    # Pattern replacements for CustomReportService mocks
    replacements = [
        # Fix save_custom_report
        (r'mock_instance\.save_custom_report\.return_value = ({[^}]+})',
         r'mock_instance.save_custom_report = AsyncMock(return_value=\1)'),
        
        # Fix list_custom_reports
        (r'mock_instance\.list_custom_reports\.return_value = (\[[^\]]+\])',
         r'mock_instance.list_custom_reports = AsyncMock(return_value=\1)'),
        
        # Fix delete_custom_report
        (r'mock_instance\.delete_custom_report\.return_value = True',
         r'mock_instance.delete_custom_report = AsyncMock(return_value=True)'),
         
        # Fix export services
        (r'mock_instance\.export_transactions\.return_value = ',
         r'mock_instance.export_transactions = AsyncMock(return_value='),
         
        (r'mock_instance\.generate_financial_report_pdf\.return_value = ',
         r'mock_instance.generate_financial_report_pdf = AsyncMock(return_value='),
         
        (r'mock_instance\.generate_transactions_csv\.return_value = ',
         r'mock_instance.generate_transactions_csv = AsyncMock(return_value='),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # Write back the fixed content
    test_file.write_text(content)
    print("Fixed async mock patterns in Reports API tests!")

if __name__ == "__main__":
    fix_async_mocks()