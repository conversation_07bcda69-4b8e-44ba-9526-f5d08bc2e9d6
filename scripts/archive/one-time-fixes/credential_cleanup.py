#!/usr/bin/env python3
"""
EMERGENCY Credential Cleanup Script

This script systematically removes ALL hardcoded credentials from the codebase:
- Replaces hardcoded passwords with environment variable references
- Updates test files to use secure credential generation
- Removes database credentials from configuration files
- Creates secure environment variable templates
"""

import os
import re
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple


class CredentialCleanup:
    """Comprehensive credential cleanup with backup and validation."""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / f"credential_cleanup_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.cleanup_log = []
        
        # Critical credentials to remove
        self.dangerous_credentials = {
            "iV6Jl5JhM63KRXB0H6dh3rLdm": "REDACTED_DB_PASSWORD",
            "GikiTest2025Secure": "REDACTED_TEST_PASSWORD", 
            "<EMAIL>": "REDACTED_TEST_EMAIL",
            "<EMAIL>": "REDACTED_TEST_EMAIL",
            "<EMAIL>": "REDACTED_TEST_EMAIL",
            "giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm": "REDACTED_DB_CREDENTIAL",
        }
        
        # Files that need complete replacement vs cleanup
        self.files_to_replace = {
            "apps/giki-ai-api/tests/conftest.py": "apps/giki-ai-api/tests/secure_conftest.py",
        }
        
        # Files to skip (documentation, this script, etc.)
        self.skip_files = {
            "SECURITY-REMEDIATION-PLAN.md",
            "credential_cleanup.py",
            "security_performance_assessment.py",
            ".git",
            "__pycache__",
            ".venv",
            "node_modules",
        }
    
    def run_cleanup(self) -> None:
        """Run complete credential cleanup process."""
        print("🚨 EMERGENCY CREDENTIAL CLEANUP - Starting...")
        print(f"📁 Project root: {self.project_root}")
        print(f"💾 Backup directory: {self.backup_dir}")
        
        # Create backup directory
        self.backup_dir.mkdir(exist_ok=True)
        
        # Step 1: Scan and identify all credential occurrences
        credential_files = self._scan_credential_files()
        print(f"🔍 Found {len(credential_files)} files with credentials")
        
        # Step 2: Create backups before modification
        self._create_backups(credential_files)
        
        # Step 3: Clean credentials from files
        self._clean_credential_files(credential_files)
        
        # Step 4: Replace problematic files
        self._replace_problematic_files()
        
        # Step 5: Create secure environment templates
        self._create_secure_env_templates()
        
        # Step 6: Validate cleanup
        remaining_credentials = self._validate_cleanup()
        
        # Step 7: Generate cleanup report
        self._generate_cleanup_report(remaining_credentials)
        
        print("✅ CREDENTIAL CLEANUP COMPLETED")
        print(f"📋 See cleanup report: {self.backup_dir / 'cleanup_report.txt'}")
    
    def _scan_credential_files(self) -> List[Path]:
        """Scan for files containing dangerous credentials."""
        files_with_credentials = []
        
        for file_path in self.project_root.rglob("*"):
            if not file_path.is_file():
                continue
                
            # Skip excluded files and directories
            if any(skip in str(file_path) for skip in self.skip_files):
                continue
            
            # Only scan text files
            if not self._is_text_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    # Check for any dangerous credential
                    for credential in self.dangerous_credentials:
                        if credential in content:
                            files_with_credentials.append(file_path)
                            self.cleanup_log.append(f"FOUND: {credential} in {file_path}")
                            break
                            
            except Exception as e:
                print(f"⚠️ Could not scan {file_path}: {e}")
        
        return files_with_credentials
    
    def _is_text_file(self, file_path: Path) -> bool:
        """Check if file is a text file that should be cleaned."""
        text_extensions = {
            '.py', '.js', '.ts', '.tsx', '.jsx',
            '.json', '.yaml', '.yml', '.env', '.env.example',
            '.md', '.txt', '.sh', '.sql', '.toml',
            '.cfg', '.conf', '.ini'
        }
        return file_path.suffix.lower() in text_extensions
    
    def _create_backups(self, files: List[Path]) -> None:
        """Create backups of files before modification."""
        print("💾 Creating backups...")
        
        for file_path in files:
            try:
                # Create backup directory structure
                relative_path = file_path.relative_to(self.project_root)
                backup_path = self.backup_dir / relative_path
                backup_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Copy original file
                shutil.copy2(file_path, backup_path)
                self.cleanup_log.append(f"BACKUP: {file_path} -> {backup_path}")
                
            except Exception as e:
                print(f"❌ Could not backup {file_path}: {e}")
    
    def _clean_credential_files(self, files: List[Path]) -> None:
        """Clean credentials from files."""
        print("🧹 Cleaning credentials from files...")
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # Replace each dangerous credential
                for credential, replacement in self.dangerous_credentials.items():
                    if credential in content:
                        content = content.replace(credential, replacement)
                        self.cleanup_log.append(f"REPLACED: {credential} -> {replacement} in {file_path}")
                
                # Special replacements for specific patterns
                content = self._apply_special_replacements(content, file_path)
                
                # Write cleaned content back
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    self.cleanup_log.append(f"CLEANED: {file_path}")
                
            except Exception as e:
                print(f"❌ Could not clean {file_path}: {e}")
    
    def _apply_special_replacements(self, content: str, file_path: Path) -> str:
        """Apply special replacements for specific file patterns."""
        
        # Environment files - replace entire database URLs
        if file_path.name.startswith('.env'):
            # Replace entire DATABASE_URL lines
            content = re.sub(
                r'DATABASE_URL=postgresql\+asyncpg://.*',
                'DATABASE_URL=${DATABASE_URL}',
                content
            )
            content = re.sub(
                r'LOCAL_DATABASE_URL=postgresql\+asyncpg://.*',
                'LOCAL_DATABASE_URL=${LOCAL_DATABASE_URL}',
                content
            )
        
        # Python config files
        if file_path.suffix == '.py' and 'config' in str(file_path):
            # Replace hardcoded database URL in config files
            content = re.sub(
                r'"postgresql\+asyncpg://giki_ai_user:.*?"',
                'os.getenv("DATABASE_URL", "")',
                content
            )
        
        # Test files - replace with environment variable references
        if 'test' in str(file_path) and file_path.suffix == '.py':
            # Replace test database URLs
            content = re.sub(
                r'"postgresql://giki_ai_user:.*?"',
                'os.getenv("TEST_DATABASE_URL")',
                content
            )
        
        return content
    
    def _replace_problematic_files(self) -> None:
        """Replace files that need complete replacement."""
        print("🔄 Replacing problematic files...")
        
        for old_file, new_file in self.files_to_replace.items():
            old_path = self.project_root / old_file
            new_path = self.project_root / new_file
            
            try:
                if old_path.exists() and new_path.exists():
                    # Backup original
                    backup_path = self.backup_dir / old_file
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(old_path, backup_path)
                    
                    # Replace with secure version
                    shutil.copy2(new_path, old_path)
                    self.cleanup_log.append(f"REPLACED: {old_file} with {new_file}")
                    
            except Exception as e:
                print(f"❌ Could not replace {old_file}: {e}")
    
    def _create_secure_env_templates(self) -> None:
        """Create secure environment variable templates."""
        print("📝 Creating secure environment templates...")
        
        # Development environment template
        dev_env_template = """# Development Environment Configuration - SECURE VERSION
# 
# SECURITY: NO hardcoded credentials in this file
# All sensitive values MUST be set via environment variables
#

# Environment
ENVIRONMENT=development
DEBUG=true

# Database Configuration
# REQUIRED: Set via environment variable
# Example: export DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/db"
DATABASE_URL=${DATABASE_URL}

# Google Cloud Configuration
VERTEX_PROJECT_ID=rezolve-poc
VERTEX_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS}

# Development settings  
API_HOST=0.0.0.0
API_PORT=8000
VITE_API_BASE_URL=http://localhost:8000
VITE_ENVIRONMENT=development
"""
        
        # Production environment template
        prod_env_template = """# Production Environment Configuration - SECURE VERSION
#
# SECURITY: ALL values MUST come from Google Secret Manager
# NO hardcoded credentials allowed
#

# Environment
ENVIRONMENT=production
DEBUG=false

# Database Configuration - FROM SECRET MANAGER
DATABASE_URL=${DATABASE_URL}

# Google Cloud Configuration  
VERTEX_PROJECT_ID=rezolve-poc
VERTEX_LOCATION=us-central1

# Performance settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
"""
        
        # Test environment template
        test_env_template = """# Test Environment Configuration - SECURE VERSION
#
# SECURITY: Test credentials MUST be generated dynamically
# NO hardcoded test credentials allowed
#

# Environment
ENVIRONMENT=test
DEBUG=true

# Test Database - MUST be set via environment
# Example: export TEST_DATABASE_URL="postgresql://user:pass@localhost:5432/test_db"
TEST_DATABASE_URL=${TEST_DATABASE_URL}

# Test Redis
TEST_REDIS_URL=redis://localhost:6379/1

# Test Service Account
TEST_GOOGLE_APPLICATION_CREDENTIALS=${TEST_GOOGLE_APPLICATION_CREDENTIALS}
"""
        
        # Write templates
        templates = [
            ("apps/giki-ai-api/.env.development.template", dev_env_template),
            ("apps/giki-ai-api/.env.production.template", prod_env_template),
            ("apps/giki-ai-api/.env.test.template", test_env_template),
        ]
        
        for template_path, content in templates:
            try:
                full_path = self.project_root / template_path
                with open(full_path, 'w') as f:
                    f.write(content)
                self.cleanup_log.append(f"CREATED: {template_path}")
            except Exception as e:
                print(f"❌ Could not create template {template_path}: {e}")
    
    def _validate_cleanup(self) -> List[Tuple[str, Path]]:
        """Validate that cleanup was successful."""
        print("✅ Validating cleanup...")
        
        remaining_credentials = []
        
        for file_path in self.project_root.rglob("*"):
            if not file_path.is_file():
                continue
                
            # Skip excluded files and backup directory
            if any(skip in str(file_path) for skip in self.skip_files):
                continue
            if str(self.backup_dir) in str(file_path):
                continue
            
            if not self._is_text_file(file_path):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    for credential in self.dangerous_credentials:
                        if credential in content:
                            remaining_credentials.append((credential, file_path))
                            
            except Exception:
                pass
        
        return remaining_credentials
    
    def _generate_cleanup_report(self, remaining_credentials: List[Tuple[str, Path]]) -> None:
        """Generate comprehensive cleanup report."""
        report = f"""
CREDENTIAL CLEANUP REPORT
========================

Generated: {datetime.now().isoformat()}
Project: {self.project_root}
Backup Location: {self.backup_dir}

CLEANUP SUMMARY
---------------
Actions Performed: {len(self.cleanup_log)}
Files Processed: {len(set(log.split()[1] for log in self.cleanup_log if 'in' in log))}
Remaining Issues: {len(remaining_credentials)}

CLEANUP ACTIONS
---------------
"""
        
        for log_entry in self.cleanup_log:
            report += f"{log_entry}\n"
        
        if remaining_credentials:
            report += f"""

❌ REMAINING CREDENTIALS ({len(remaining_credentials)})
====================================================
"""
            for credential, file_path in remaining_credentials:
                relative_path = file_path.relative_to(self.project_root)
                report += f"STILL FOUND: {credential} in {relative_path}\n"
            
            report += """

🚨 CRITICAL: Manual cleanup required for remaining credentials
"""
        else:
            report += """

✅ SUCCESS: All dangerous credentials have been removed!
"""
        
        report += f"""

NEXT STEPS
----------
1. Review backup files in: {self.backup_dir}
2. Set required environment variables:
   - DATABASE_URL
   - TEST_DATABASE_URL  
   - GOOGLE_APPLICATION_CREDENTIALS
3. Test application with new secure configuration
4. Update deployment scripts to use environment variables
5. Rotate any credentials that were exposed

ENVIRONMENT VARIABLE SETUP
---------------------------
# Development
export DATABASE_URL="postgresql+asyncpg://user:newpassword@localhost:5432/giki_ai_db"
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"

# Testing  
export TEST_DATABASE_URL="postgresql://user:password@localhost:5432/test_db"
export TEST_GOOGLE_APPLICATION_CREDENTIALS="/path/to/test-service-account.json"

SECURITY REMINDERS
------------------
1. NEVER commit credentials to version control
2. Use Google Secret Manager for production
3. Rotate any previously exposed credentials
4. Monitor for credential leaks in future commits
5. Keep this backup for reference only - do not commit
"""
        
        # Write report
        report_path = self.backup_dir / "cleanup_report.txt"
        with open(report_path, 'w') as f:
            f.write(report)
        
        print(f"📋 Cleanup report written to: {report_path}")


def main():
    """Run credential cleanup."""
    project_root = Path(__file__).parent.parent
    
    print("🚨 EMERGENCY CREDENTIAL CLEANUP")
    print("=" * 50)
    print("This script will remove ALL hardcoded credentials from the codebase.")
    print("Backups will be created before any modifications.")
    print()
    
    # Auto-confirm for automated execution
    print("🤖 Automated execution - proceeding with cleanup...")
    
    # Run cleanup
    cleanup = CredentialCleanup(str(project_root))
    cleanup.run_cleanup()
    
    print("\n🔒 SECURITY REMINDER:")
    print("1. Rotate any exposed database credentials immediately")
    print("2. Set up environment variables before running application")
    print("3. Update CI/CD pipelines to use secure environment variables")
    print("4. Review and test all changes before deployment")


if __name__ == "__main__":
    main()