#!/usr/bin/env node

/**
 * Card Component Migration Script
 * 
 * Automatically migrates imports from separate card files (excel-card.tsx, giki-card.tsx)
 * to the unified card system in card.tsx.
 */

const fs = require('fs');
const path = require('path');

// Migration mappings
const MIGRATION_RULES = [
  {
    // ExcelCard migration
    from: /import\s+(?:{\s*)?ExcelCard(?:\s*})?\s+from\s+['"].*excel-card['"]/g,
    to: "import { ExcelCard } from '@/shared/components/ui/card'",
    description: "Migrating ExcelCard imports to unified card system"
  },
  {
    // GikiCard migration  
    from: /import\s+(?:{\s*)?GikiCard(?:\s*})?\s+from\s+['"].*giki-card['"]/g,
    to: "import { GikiCard } from '@/shared/components/ui/card'",
    description: "Migrating GikiCard imports to unified card system"
  },
  {
    // Multiple card imports migration
    from: /import\s+{\s*([^}]*(?:ExcelCard|GikiCard)[^}]*)\s*}\s+from\s+['"].*(?:excel-card|giki-card)['"]/g,
    to: (match, imports) => {
      // Extract individual imports and redirect to unified card
      const cleanImports = imports.split(',').map(imp => imp.trim()).join(', ');
      return `import { ${cleanImports} } from '@/shared/components/ui/card'`;
    },
    description: "Migrating multiple card imports to unified system"
  }
];

// Directory to search
const SEARCH_DIR = 'apps/giki-ai-app/src';

// Files to ignore
const IGNORE_FILES = [
  'apps/giki-ai-app/src/shared/components/ui/card.tsx', // Skip the main card file
  'apps/giki-ai-app/src/shared/components/ui/excel-card.tsx', // Skip deprecated files
  'apps/giki-ai-app/src/shared/components/ui/giki-card.tsx', // Skip deprecated files
];

/**
 * Recursively find all TypeScript/React files to migrate
 */
function findFilesToMigrate() {
  const files = [];
  
  function walkDirectory(dirPath) {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        // Skip node_modules, dist, build directories
        if (!['node_modules', 'dist', 'build'].includes(entry.name)) {
          walkDirectory(fullPath);
        }
      } else if (entry.isFile() && (entry.name.endsWith('.tsx') || entry.name.endsWith('.ts'))) {
        // Skip ignored files
        if (!IGNORE_FILES.includes(fullPath)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  walkDirectory(SEARCH_DIR);
  return files;
}

/**
 * Check if file contains card imports that need migration
 */
function needsMigration(content) {
  return MIGRATION_RULES.some(rule => {
    if (rule.from instanceof RegExp) {
      return rule.from.test(content);
    }
    return content.includes(rule.from);
  });
}

/**
 * Apply migration rules to file content
 */
function migrateContent(content) {
  let migratedContent = content;
  const appliedRules = [];
  
  MIGRATION_RULES.forEach(rule => {
    const beforeMigration = migratedContent;
    
    if (typeof rule.to === 'function') {
      migratedContent = migratedContent.replace(rule.from, rule.to);
    } else {
      migratedContent = migratedContent.replace(rule.from, rule.to);
    }
    
    if (beforeMigration !== migratedContent) {
      appliedRules.push(rule.description);
    }
  });
  
  return { content: migratedContent, appliedRules };
}

/**
 * Main migration function
 */
function runMigration() {
  console.log('🔄 Starting Card Component Migration...\n');
  
  const filesToCheck = findFilesToMigrate();
  console.log(`📁 Found ${filesToCheck.length} files to check\n`);
  
  let migratedFiles = 0;
  let totalRulesApplied = 0;
  
  filesToCheck.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      if (needsMigration(content)) {
        const { content: migratedContent, appliedRules } = migrateContent(content);
        
        if (appliedRules.length > 0) {
          // Write migrated content back to file
          fs.writeFileSync(filePath, migratedContent, 'utf8');
          
          migratedFiles++;
          totalRulesApplied += appliedRules.length;
          
          console.log(`✅ Migrated: ${filePath}`);
          appliedRules.forEach(rule => {
            console.log(`   - ${rule}`);
          });
          console.log('');
        }
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  });
  
  console.log('📊 Migration Summary:');
  console.log(`   - Files migrated: ${migratedFiles}`);
  console.log(`   - Total rules applied: ${totalRulesApplied}`);
  console.log(`   - Files checked: ${filesToCheck.length}`);
  
  if (migratedFiles > 0) {
    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Review the migrated files to ensure correctness');
    console.log('   2. Run tests to verify functionality');
    console.log('   3. Remove deprecated card files:');
    console.log('      - apps/giki-ai-app/src/shared/components/ui/excel-card.tsx');
    console.log('      - apps/giki-ai-app/src/shared/components/ui/giki-card.tsx');
    console.log('   4. Update any remaining manual imports');
  } else {
    console.log('\n✨ No migration needed - all imports already use unified card system!');
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration, findFilesToMigrate, migrateContent };