#!/usr/bin/env python3
"""Get transaction IDs for testing"""

import asyncio
import asyncpg

async def get_transaction_ids():
    """Get some transaction IDs from the database"""
    try:
        conn = await asyncpg.connect('postgresql://nikhilsingh@localhost:5432/giki_ai_dev')
        print('✅ Database connected successfully')
        
        # Get some transaction IDs
        result = await conn.fetch('SELECT id, description, amount FROM transactions LIMIT 5')
        
        if result:
            print("\n📋 Sample transaction IDs:")
            for row in result:
                print(f"  ID: {row['id']}")
                print(f"  Description: {row['description']}")
                print(f"  Amount: {row['amount']}")
                print("  ---")
        else:
            print("❌ No transactions found")
        
        await conn.close()
        return [str(row['id']) for row in result] if result else []
        
    except Exception as e:
        print(f'❌ Database query failed: {e}')
        return []

if __name__ == "__main__":
    transaction_ids = asyncio.run(get_transaction_ids())
    if transaction_ids:
        print(f"\n✅ Found {len(transaction_ids)} transaction IDs")
        print(f"First ID: {transaction_ids[0]}")
    else:
        print("❌ No transaction IDs found")