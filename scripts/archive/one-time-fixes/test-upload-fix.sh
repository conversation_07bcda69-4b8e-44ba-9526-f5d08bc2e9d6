#!/bin/bash

# Quick test script to verify upload workflow categorization fix
# This tests the specific fix for category path to ID conversion error

set -euo pipefail

# Configuration
API_BASE="http://localhost:8000"
TEST_USER="<EMAIL>"
TEST_PASSWORD="GikiTest2025Secure"
TEST_FILE="/Users/<USER>/giki-ai-workspace/libs/test-data/mis-testing/test_transactions_small.csv"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get authentication token
log_info "Getting authentication token..."
TOKEN_RESPONSE=$(curl -s -X POST "$API_BASE/api/v1/auth/token" \
    -d "username=$TEST_USER&password=$TEST_PASSWORD")

if [ $? -ne 0 ]; then
    log_error "Failed to get authentication token"
    exit 1
fi

TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')
if [ "$TOKEN" = "null" ] || [ "$TOKEN" = "" ]; then
    log_error "Authentication failed: $TOKEN_RESPONSE"
    exit 1
fi

log_success "Authentication token obtained"

# Create test CSV file if it doesn't exist
if [ ! -f "$TEST_FILE" ]; then
    log_info "Creating test CSV file..."
    mkdir -p "$(dirname "$TEST_FILE")"
    cat > "$TEST_FILE" << 'EOF'
Date,Description,Amount,Vendor
2025-01-01,"NEXT Insurance Premium Payment",-1406.11,"NEXT Insurance"
2025-01-02,"Microsoft Office 365 Subscription",-19.99,"Microsoft"
2025-01-03,"ACH Credit Consulting Payment",45980.68,"Freelance Client"
2025-01-04,"Uber ride to airport",-35.50,"Uber"
2025-01-05,"Stripe payment from customer",299.99,"Stripe"
EOF
    log_success "Test CSV file created: $TEST_FILE"
fi

# Test file upload
log_info "Testing file upload with categorization..."
upload_response=$(curl -s -X POST "$API_BASE/api/v1/files/upload" \
    -H "Authorization: Bearer $TOKEN" \
    -F "files=@$TEST_FILE")

if [ $? -ne 0 ]; then
    log_error "File upload failed - curl error"
    exit 1
fi

# Check for upload errors
error_message=$(echo "$upload_response" | jq -r '.detail // empty')
if [ -n "$error_message" ]; then
    log_error "Upload failed: $error_message"
    echo "Full response: $upload_response"
    exit 1
fi

# Extract upload information
upload_id=$(echo "$upload_response" | jq -r '.uploads[0].upload_id // .upload_id // empty')
filename=$(echo "$upload_response" | jq -r '.uploads[0].filename // .filename // empty')

if [ -z "$upload_id" ]; then
    log_error "No upload_id in response"
    echo "Full response: $upload_response"
    exit 1
fi

log_success "File uploaded successfully - ID: $upload_id, File: $filename"

# Wait a moment for processing
log_info "Waiting for processing to complete..."
sleep 5

# Check for transactions with categorization
log_info "Checking transactions for categorization..."
transactions_response=$(curl -s -H "Authorization: Bearer $TOKEN" \
    "$API_BASE/api/v1/transactions?upload_id=$upload_id&limit=10")

if [ $? -ne 0 ]; then
    log_error "Failed to get transactions"
    exit 1
fi

# Parse transaction data
transaction_count=$(echo "$transactions_response" | jq '.data | length // 0')
categorized_count=$(echo "$transactions_response" | jq '[.data[] | select(.ai_suggested_category != null and .ai_suggested_category != "")] | length')

log_info "Transactions found: $transaction_count"
log_info "Categorized transactions: $categorized_count"

# Check if any transactions were categorized
if [ "$categorized_count" -gt 0 ]; then
    log_success "Categorization is working! $categorized_count out of $transaction_count transactions categorized"
    
    # Show a sample categorized transaction
    sample_transaction=$(echo "$transactions_response" | jq '.data[] | select(.ai_suggested_category != null) | {description, ai_category, ai_suggested_category, ai_confidence} | select(.ai_suggested_category != null)' | head -1)
    if [ -n "$sample_transaction" ]; then
        log_info "Sample categorized transaction:"
        echo "$sample_transaction" | jq '.'
    fi
else
    log_error "No transactions were categorized"
fi

# Check logs for any errors
log_info "Checking logs for category conversion errors..."
if grep -q "str.*cannot be interpreted as an integer" /Users/<USER>/giki-ai-workspace/logs/serve-api.log; then
    log_error "Category conversion error still found in logs!"
    grep "str.*cannot be interpreted as an integer" /Users/<USER>/giki-ai-workspace/logs/serve-api.log | tail -3
    exit 1
else
    log_success "No category conversion errors found in logs"
fi

log_success "Upload workflow test completed successfully!"
echo ""
echo "Summary:"
echo "- Upload ID: $upload_id"
echo "- Transactions: $transaction_count"
echo "- Categorized: $categorized_count"
echo "- Category conversion errors: None"