#!/bin/bash
# Quick deployment fix for production issues
set -e

echo "🔧 Quick deployment fix for production..."

# Deploy the current image directly using gcloud run deploy
gcloud run deploy giki-ai-api \
    --image=us-central1-docker.pkg.dev/rezolve-poc/giki-ai/api:latest \
    --region=us-central1 \
    --service-account=<EMAIL> \
    --add-cloudsql-instances=rezolve-poc:us-central1:giki-ai-postgres-prod \
    --set-env-vars="ENVIRONMENT=production,DEBUG=true,API_HOST=0.0.0.0,SECRET_KEY=prod_secret_key_replace_with_secure_key,AUTH_SECRET_KEY=auth_secret_key_replace_with_secure_key,ALGORITHM=HS256,ACCESS_TOKEN_EXPIRE_MINUTES=30,CORS_ALLOWED_ORIGINS=https://app-giki-ai.web.app;https://giki-ai.web.app,DATABASE_URL=postgresql+asyncpg://giki_ai_user:GikiAI2025SecureProdPwd@************:5432/giki_ai_db?sslmode=require,VERTEX_PROJECT_ID=rezolve-poc,VERTEX_LOCATION=us-central1" \
    --allow-unauthenticated \
    --timeout=300 \
    --project=rezolve-poc \
    --quiet

echo "✅ Quick deployment completed!"