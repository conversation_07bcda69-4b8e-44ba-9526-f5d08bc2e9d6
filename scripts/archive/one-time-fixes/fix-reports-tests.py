#!/usr/bin/env python3
"""
<PERSON>ript to fix Reports API tests by converting async tests to sync and updating dependency injection.
"""

import re
from pathlib import Path

def fix_reports_tests():
    """Fix the reports API test file."""
    test_file = Path("/Users/<USER>/giki-ai-workspace/apps/giki-ai-api/tests/unit/test_reports_api.py")
    content = test_file.read_text()
    
    # Pattern replacements
    replacements = [
        # Convert async test methods to sync
        (r'@pytest\.mark\.asyncio\s*\n\s*async def test_', 'def test_'),
        
        # Replace AsyncClient with TestClient in params
        (r'client: AsyncClient', 'client: TestClient'),
        
        # Replace Connection with AsyncMock in params
        (r'mock_db_connection: Connection', 'mock_db_connection: AsyncMock'),
        
        # Remove mock_current_user parameter where it appears
        (r',\s*mock_current_user: User', ''),
        
        # Replace await client. with client.
        (r'await client\.', 'client.'),
        
        # Remove mock_db_connection.fetch.return_value = and use direct assignment
        (r'mock_db_connection\.fetch\.return_value = ', 'mock_db_connection.fetch = AsyncMock(return_value='),
        (r'mock_db_connection\.fetchrow\.return_value = ', 'mock_db_connection.fetchrow = AsyncMock(return_value='),
        
        # Add closing parenthesis after AsyncMock assignments
        (r'(mock_db_connection\.fetch = AsyncMock\(return_value=.*?)(\n)', r'\1)\2'),
        (r'(mock_db_connection\.fetchrow = AsyncMock\(return_value=.*?)(\n)', r'\1)\2'),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    # Write back the fixed content
    test_file.write_text(content)
    print("Fixed Reports API tests!")

if __name__ == "__main__":
    fix_reports_tests()