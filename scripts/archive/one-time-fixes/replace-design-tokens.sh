#!/bin/bash

# Design Token Replacement Script
# Systematically replaces hard-coded colors with design tokens

BASE_DIR="/Users/<USER>/giki-ai-workspace/apps/giki-ai-app/src"

echo "🎨 Starting design token replacement..."

# Function to replace patterns in all TSX files
replace_pattern() {
    local old_pattern="$1"
    local new_pattern="$2"
    local description="$3"
    
    echo "  🔄 Replacing: $description"
    find "$BASE_DIR" -name "*.tsx" -type f -exec sed -i '' "s/$old_pattern/$new_pattern/g" {} \;
}

# Brand Primary Color Replacements
echo "📝 Replacing brand primary colors (#295343)..."

# Background colors
replace_pattern "bg-\[#295343\]" "bg-brand-primary" "primary background colors"
replace_pattern "bg-\[#1D372E\]" "bg-brand-primary-hover" "primary hover background colors" 
replace_pattern "hover:bg-\[#295343\]" "hover:bg-brand-primary" "hover primary backgrounds"
replace_pattern "hover:bg-\[#1D372E\]" "hover:bg-brand-primary-hover" "hover darker primary backgrounds"

# Text colors
replace_pattern "text-\[#295343\]" "text-brand-primary" "primary text colors"
replace_pattern "hover:text-\[#295343\]" "hover:text-brand-primary" "hover primary text colors"

# Border colors
replace_pattern "border-\[#295343\]" "border-brand-primary" "primary border colors"
replace_pattern "border-\[#295343\]\/20" "border-brand-primary/20" "primary border with opacity"
replace_pattern "hover:border-\[#295343\]" "hover:border-brand-primary" "hover primary borders"
replace_pattern "focus:border-\[#295343\]" "focus:border-brand-primary" "focus primary borders"

# Ring/outline colors  
replace_pattern "ring-\[#295343\]" "ring-brand-primary" "primary ring colors"
replace_pattern "focus:ring-\[#295343\]" "focus:ring-brand-primary" "focus primary rings"
replace_pattern "focus:ring-\[#295343\]\/20" "focus:ring-brand-primary/20" "focus primary rings with opacity"

# AI Dark Colors
echo "📝 Replacing AI system colors..."
replace_pattern "bg-\[#1a3f5f\]" "bg-ai-dark-blue" "AI dark blue backgrounds"
replace_pattern "text-\[#1a3f5f\]" "text-ai-dark-blue" "AI dark blue text"
replace_pattern "bg-\[#3f1a5f\]" "bg-ai-dark-purple" "AI dark purple backgrounds"
replace_pattern "text-\[#3f1a5f\]" "text-ai-dark-purple" "AI dark purple text"

# Common Error/Success Colors
echo "📝 Replacing semantic colors..."
replace_pattern "text-red-600" "text-error" "error text colors"
replace_pattern "text-green-600" "text-success" "success text colors"
replace_pattern "bg-red-50" "bg-error/10" "light error backgrounds"
replace_pattern "bg-green-50" "bg-success/10" "light success backgrounds"

# Loading Spinner Colors
echo "📝 Replacing spinner colors..."
replace_pattern "border-b-2 border-\[#295343\]" "border-b-2 border-brand-primary" "loading spinner borders"

# Inline Styles (more complex)
echo "📝 Replacing inline styles..."
replace_pattern "backgroundColor: '#295343'" "backgroundColor: 'var(--giki-primary)'" "inline background styles"
replace_pattern "color: '#295343'" "color: 'var(--giki-primary)'" "inline color styles"
replace_pattern "borderColor: '#295343'" "borderColor: 'var(--giki-primary)'" "inline border styles"

# Gradient replacements
echo "📝 Replacing gradient colors..."
replace_pattern "from-\[#295343\]" "from-brand-primary" "gradient start colors"
replace_pattern "to-\[#295343\]" "to-brand-primary" "gradient end colors"
replace_pattern "to-\[#1A3F5F\]" "to-ai-dark-blue" "gradient to AI blue"

echo "✅ Design token replacement complete!"
echo "📊 Summary of replacements made:"
echo "   - Brand primary colors (#295343)"
echo "   - AI system colors (#1a3f5f, #3f1a5f)"
echo "   - Semantic colors (success, error)"
echo "   - Hover and focus states"
echo "   - Inline styles"
echo "   - Gradient definitions"
echo ""
echo "🔍 Files modified:"
find "$BASE_DIR" -name "*.tsx" -type f -exec grep -l "bg-brand-primary\|text-brand-primary\|border-brand-primary" {} \; | wc -l | xargs echo "   TSX files with design tokens:"