#!/usr/bin/env python3
"""
Fix production endpoint issues
"""
import asyncio
import httpx

BASE_URL = "https://giki-ai-api-273348121056.us-central1.run.app"

async def test_and_fix_endpoints():
    """Test and diagnose endpoint issues"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # Get fresh authentication token
        print("🔐 Getting fresh authentication token...")
        try:
            auth_data = {
                "username": "<EMAIL>",
                "password": "GikiTest2025Secure"
            }
            response = await client.post(
                f"{BASE_URL}/api/v1/auth/token",
                data=auth_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code != 200:
                print(f"❌ Authentication failed: {response.status_code}")
                return
                
            token = response.json()["access_token"]
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            print("✅ Authentication successful")
            
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return
            
        # Test all endpoint variations
        endpoints_to_test = [
            "/api/v1/transactions",
            "/api/v1/transactions/",
            "/api/v1/uploads", 
            "/api/v1/uploads/",
            "/api/v1/files",
            "/api/v1/files/",
            "/api/v1/file",
            "/api/v1/file/",
            "/api/v1/monitoring/dashboard",
            "/api/v1/dashboard",
            "/api/v1/dashboard/",
        ]
        
        print("\n🔍 Testing endpoint variations...")
        for endpoint in endpoints_to_test:
            try:
                response = await client.get(f"{BASE_URL}{endpoint}", headers=headers)
                status = response.status_code
                
                if status == 200:
                    try:
                        data = response.json()
                        if isinstance(data, list):
                            print(f"✅ {endpoint} -> {status} (list with {len(data)} items)")
                        else:
                            print(f"✅ {endpoint} -> {status} (object with keys: {list(data.keys())[:5]})")
                    except:
                        print(f"✅ {endpoint} -> {status} (text response)")
                elif status == 307:
                    location = response.headers.get('location', 'No location header')
                    print(f"🔄 {endpoint} -> {status} (redirect to {location})")
                elif status == 404:
                    print(f"❌ {endpoint} -> {status} (not found)")
                elif status == 401:
                    print(f"🔑 {endpoint} -> {status} (unauthorized)")
                elif status == 403:
                    print(f"🚫 {endpoint} -> {status} (forbidden)")
                elif status == 500:
                    error_text = response.text[:100]
                    print(f"💥 {endpoint} -> {status} (server error: {error_text})")
                else:
                    print(f"⚠️  {endpoint} -> {status}")
                    
            except Exception as e:
                print(f"💥 {endpoint} -> Error: {str(e)[:50]}")
                
        # Test specific transactions endpoint issues
        print("\n💰 Deep testing transactions endpoint...")
        try:
            # Test with proper URL following redirect
            response = await client.get(f"{BASE_URL}/api/v1/transactions/", headers=headers, follow_redirects=True)
            print(f"Transactions with redirect: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Transactions data retrieved: {len(data)} transactions")
                if data:
                    print(f"Sample transaction keys: {list(data[0].keys())}")
                else:
                    print("No transactions found - this might be expected")
            else:
                print(f"❌ Transactions error: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Transactions deep test failed: {e}")
            
        print("\n🎯 Endpoint testing completed!")

if __name__ == "__main__":
    asyncio.run(test_and_fix_endpoints())