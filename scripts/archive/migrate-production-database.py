#!/usr/bin/env python3
"""
Migrate production database to include all missing tables
"""
import asyncio
import asyncpg
from pathlib import Path

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def migrate_production_database():
    """Apply all missing migrations to production database"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        print("📊 Checking current tables...")
        existing_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        existing_table_names = [table['table_name'] for table in existing_tables]
        print(f"Existing tables: {existing_table_names}")
        
        # Read and apply base schema migration
        migrations_dir = Path(__file__).parent.parent / "apps/giki-ai-api/migrations"
        base_schema_file = migrations_dir / "001_base_schema.sql"
        
        if base_schema_file.exists():
            print("📝 Applying base schema migration...")
            base_schema_sql = base_schema_file.read_text()
            
            # Execute the migration
            await conn.execute(base_schema_sql)
            print("✅ Base schema migration applied")
        else:
            print("❌ Base schema file not found")
            return False
            
        # Apply additional migrations
        additional_migrations = [
            "010_create_onboarding_status_table.sql",
            "011_create_vendor_category_mappings_table.sql", 
            "012_create_mis_enhancements_table.sql"
        ]
        
        for migration_file in additional_migrations:
            migration_path = migrations_dir / migration_file
            if migration_path.exists():
                print(f"📝 Applying {migration_file}...")
                migration_sql = migration_path.read_text()
                await conn.execute(migration_sql)
                print(f"✅ {migration_file} applied")
            else:
                print(f"⚠️ {migration_file} not found, skipping")
        
        # Check if we need entities tables
        print("🔍 Checking for entities table in development...")
        try:
            # Try to get structure of entities from local dev
            dev_conn = await asyncpg.connect("postgresql://giki_ai_user:giki_ai_password@localhost:5432/giki_ai_dev")
            
            entities_exists = await dev_conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'entities'
                )
            """)
            
            if entities_exists:
                print("📋 Getting entities table structure from development...")
                entities_structure = await dev_conn.fetch("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_name = 'entities' AND table_schema = 'public'
                    ORDER BY ordinal_position
                """)
                
                # Create entities table in production
                print("📝 Creating entities table in production...")
                
                create_entities_sql = """
                CREATE TABLE IF NOT EXISTS entities (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER NOT NULL,
                    entity_type VARCHAR(50) DEFAULT 'business',
                    name VARCHAR(255) NOT NULL,
                    legal_name VARCHAR(255),
                    business_type VARCHAR(100),
                    industry VARCHAR(100),
                    address JSONB DEFAULT '{}',
                    contact_info JSONB DEFAULT '{}',
                    tax_info JSONB DEFAULT '{}',
                    settings JSONB DEFAULT '{}',
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    -- Constraints
                    CONSTRAINT fk_entities_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
                    CONSTRAINT uq_entities_tenant_name UNIQUE (tenant_id, name)
                );
                
                -- Create indexes
                CREATE INDEX IF NOT EXISTS idx_entities_tenant_id ON entities(tenant_id);
                CREATE INDEX IF NOT EXISTS idx_entities_type ON entities(entity_type);
                CREATE INDEX IF NOT EXISTS idx_entities_active ON entities(is_active);
                
                -- Add update trigger
                CREATE TRIGGER trigger_entities_updated_at
                    BEFORE UPDATE ON entities
                    FOR EACH ROW EXECUTE FUNCTION trigger_update_timestamp();
                """
                
                await conn.execute(create_entities_sql)
                print("✅ Entities table created")
                
            await dev_conn.close()
            
        except Exception as e:
            print(f"⚠️ Could not connect to development database: {e}")
            print("Creating basic entities table...")
            
            # Create a basic entities table
            create_basic_entities = """
            CREATE TABLE IF NOT EXISTS entities (
                id SERIAL PRIMARY KEY,
                tenant_id INTEGER NOT NULL,
                name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                
                CONSTRAINT fk_entities_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
            );
            
            CREATE INDEX IF NOT EXISTS idx_entities_tenant_id ON entities(tenant_id);
            """
            
            await conn.execute(create_basic_entities)
            print("✅ Basic entities table created")
        
        # Check final table status
        print("\n📊 Final table status:")
        final_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        for table in final_tables:
            table_name = table['table_name']
            count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
            print(f"  ✅ {table_name}: {count} records")
        
        print("\n🎉 Production database migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error migrating database: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(migrate_production_database())