-- Fix Tenant Configuration Script V2
-- Purpose: Setup MIS-focused categorization for giki.ai
-- Date: 2025-01-02

-- Step 1: Clean up existing data (fresh start as requested)
DELETE FROM transactions WHERE tenant_id IN (1, 2, 3);
DELETE FROM categories WHERE tenant_id IN (1, 2, 3);

-- Step 2: Fix user tenant assignments  
UPDATE users 
SET tenant_id = 3 -- Nuvie tenant
WHERE email = '<EMAIL>';

UPDATE users
SET tenant_id = 2 -- Rezolve tenant  
WHERE email = '<EMAIL>';

-- Create <EMAIL> user if not exists
INSERT INTO users (email, username, hashed_password, tenant_id, is_active, is_verified, created_at, updated_at)
VALUES ('<EMAIL>', '<EMAIL>', '$2b$12$GR7ROytL2bXn3uaMOX1CEOdVbtOAe1DLB8CEaCg50INRGHdhDrZPG', 1, true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (email) DO UPDATE
SET tenant_id = 1, updated_at = CURRENT_TIMESTAMP;

-- Step 3: Update tenant names and settings for MIS focus
UPDATE tenant 
SET name = 'Giki M3 GL Schema'
WHERE id = 1;

UPDATE tenant 
SET name = 'Rezolve M2 Temporal' 
WHERE id = 2;

UPDATE tenant
SET name = 'Nuvie M1 Zero-Onboarding'
WHERE id = 3;

-- Step 4: Create Business-Focused MIS Categories for all tenants
-- Clear existing categories first
DELETE FROM categories WHERE tenant_id IN (1, 2, 3);

-- Create Income categories
WITH income_main AS (
  INSERT INTO categories (name, tenant_id, parent_id, description, level, created_at, updated_at)
  SELECT 
    'Income',
    t.id,
    NULL,
    'All revenue and income sources',
    1,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  FROM (SELECT id FROM tenant WHERE id IN (1, 2, 3)) t
  RETURNING id, tenant_id
)
INSERT INTO categories (name, tenant_id, parent_id, description, level, created_at, updated_at)
SELECT 
  sub.name,
  im.tenant_id,
  im.id,
  sub.description,
  2,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
FROM income_main im
CROSS JOIN (VALUES
  ('Sales Income', 'Revenue from product sales'),
  ('Service Income', 'Revenue from services rendered'),
  ('Other Income', 'Interest, dividends, and other income')
) sub(name, description);

-- Create Expense categories  
WITH expense_main AS (
  INSERT INTO categories (name, tenant_id, parent_id, description, level, created_at, updated_at)
  SELECT 
    'Expenses',
    t.id,
    NULL,
    'All business expenses',
    1,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  FROM (SELECT id FROM tenant WHERE id IN (1, 2, 3)) t
  RETURNING id, tenant_id
)
INSERT INTO categories (name, tenant_id, parent_id, description, level, created_at, updated_at)
SELECT 
  sub.name,
  em.tenant_id,
  em.id,
  sub.description,
  2,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
FROM expense_main em
CROSS JOIN (VALUES
  ('Employee Costs', 'Salaries, wages, benefits, PF, ESI'),
  ('Office & Admin', 'Rent, utilities, office supplies, maintenance'),
  ('Marketing & Sales', 'Advertising, promotions, sales commissions'),
  ('Technology & Software', 'Software subscriptions, IT services, equipment'),
  ('Professional Services', 'Legal, accounting, consulting fees'),
  ('Travel & Transportation', 'Business travel, fuel, vehicle expenses'),
  ('Finance Charges', 'Interest, bank fees, processing charges'),
  ('Taxes & Compliance', 'GST, income tax, other statutory payments'),
  ('Other Operating Expenses', 'Miscellaneous business expenses')
) sub(name, description);

-- Step 5: Add GL codes for tenant 1 (Giki M3)
UPDATE categories 
SET gl_code = CASE 
    -- Income GL Codes (4000 series)
    WHEN name = 'Sales Income' THEN '4000'
    WHEN name = 'Service Income' THEN '4100'
    WHEN name = 'Other Income' THEN '4900'
    -- Expense GL Codes  
    WHEN name = 'Employee Costs' THEN '6000'
    WHEN name = 'Office & Admin' THEN '6100'
    WHEN name = 'Marketing & Sales' THEN '6200'
    WHEN name = 'Technology & Software' THEN '6300'
    WHEN name = 'Professional Services' THEN '6400'
    WHEN name = 'Travel & Transportation' THEN '6500'
    WHEN name = 'Finance Charges' THEN '7000'
    WHEN name = 'Taxes & Compliance' THEN '8000'
    WHEN name = 'Other Operating Expenses' THEN '6900'
  END
WHERE tenant_id = 1;

-- Step 6: Update uploads to correct tenant associations and mark for reprocessing
UPDATE uploads 
SET tenant_id = 3, processing_status = 'pending'
WHERE filename LIKE '%nuvie%';

UPDATE uploads 
SET tenant_id = 2, processing_status = 'pending'  
WHERE filename IN ('Capital One.xlsx', 'Credit Card.xlsx', 'ICICI.xlsx', 'SVB.xlsx');

-- Display summary
SELECT 
  'Configuration Complete' as status,
  (SELECT COUNT(*) FROM tenant WHERE id IN (1,2,3)) as tenants_configured,
  (SELECT COUNT(*) FROM users WHERE tenant_id IN (1,2,3)) as users_assigned,
  (SELECT COUNT(*) FROM categories WHERE tenant_id IN (1,2,3)) as categories_created,
  (SELECT COUNT(*) FROM uploads WHERE tenant_id IN (1,2,3)) as uploads_ready;