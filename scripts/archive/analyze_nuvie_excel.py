#!/usr/bin/env python3
"""Analyze Nuvie expense ledger structure to create realistic synthetic test data"""

import pandas as pd
import json
from pathlib import Path

# Find and read the Nuvie expense ledger
file_path = Path("/Users/<USER>/giki-ai-workspace/data/milestones/M1-nuvie/nuvie_expense_ledger.xlsx")

if file_path.exists():
    # Read the Excel file - skip the first row which seems to have values as headers
    df = pd.read_excel(file_path, skiprows=1)
    
    print("=== NUVIE EXPENSE LEDGER STRUCTURE ===")
    print(f"Total rows: {len(df)}")
    print(f"Total columns: {len(df.columns)}")
    print("\nColumn names:")
    for i, col in enumerate(df.columns):
        print(f"{i+1}. {col}")
    
    # Check for closing balance column and any columns after it
    closing_balance_idx = None
    for i, col in enumerate(df.columns):
        if 'closing' in str(col).lower() and 'balance' in str(col).lower():
            closing_balance_idx = i
            print(f"\n⚠️ Found 'Closing Balance' at column {i+1}: {col}")
            if i < len(df.columns) - 1:
                print("Columns AFTER Closing Balance (to be removed):")
                for j in range(i+1, len(df.columns)):
                    print(f"  - {df.columns[j]}")
            break
    
    # Show sample data (first 5 rows)
    print("\n=== SAMPLE DATA (First 5 rows) ===")
    # Only show columns up to closing balance
    if closing_balance_idx is not None:
        display_cols = df.columns[:closing_balance_idx+1]
    else:
        display_cols = df.columns
    
    for idx, row in df.head().iterrows():
        print(f"\nRow {idx+1}:")
        for col in display_cols:
            if pd.notna(row[col]):
                print(f"  {col}: {row[col]}")
    
    # Analyze transaction patterns
    print("\n=== TRANSACTION PATTERNS ===")
    if 'Narration' in df.columns:
        # Get unique transaction descriptions
        descriptions = df['Narration'].dropna().unique()
        print(f"Unique transaction descriptions: {len(descriptions)}")
        print("\nSample descriptions:")
        for desc in descriptions[:15]:
            print(f"  - {desc}")
    
    # Analyze amounts
    debit_col = 'Withdrawal Amt.'
    credit_col = 'Deposit Amt.'
    
    if debit_col and credit_col:
        print(f"\n=== AMOUNT ANALYSIS ===")
        print(f"Debit column: {debit_col}")
        print(f"Credit column: {credit_col}")
        
        # Get non-zero debits
        debits = df[df[debit_col] > 0][debit_col]
        if len(debits) > 0:
            print(f"\nDebit transactions: {len(debits)}")
            print(f"  Min: ₹{debits.min():,.2f}")
            print(f"  Max: ₹{debits.max():,.2f}")
            print(f"  Avg: ₹{debits.mean():,.2f}")
        
        # Get non-zero credits
        credits = df[df[credit_col] > 0][credit_col]
        if len(credits) > 0:
            print(f"\nCredit transactions: {len(credits)}")
            print(f"  Min: ₹{credits.min():,.2f}")
            print(f"  Max: ₹{credits.max():,.2f}")
            print(f"  Avg: ₹{credits.mean():,.2f}")
    
    # Extract realistic vendor patterns
    print("\n=== VENDOR PATTERNS ===")
    vendors = []
    if 'Narration' in df.columns:
        for desc in df['Narration'].dropna():
            desc_str = str(desc).strip()
            if desc_str and desc_str not in ['Opening Balance', 'Closing Balance']:
                vendors.append(desc_str)
    
    print(f"Found {len(set(vendors))} unique vendors/descriptions")
    print("\nCommon patterns:")
    from collections import Counter
    vendor_counts = Counter(vendors)
    for vendor, count in vendor_counts.most_common(10):
        print(f"  - {vendor}: {count} times")
    
else:
    print(f"File not found: {file_path}")