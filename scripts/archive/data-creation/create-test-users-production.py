#!/usr/bin/env python3
"""
Create test users in production database
"""
import asyncio
import asyncpg
from passlib.context import CryptContext

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def create_test_users():
    """Create test users in production"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Hash the test password
        test_password = "GikiTest2025Secure"
        hashed_password = pwd_context.hash(test_password)
        
        # Create a test company tenant if it doesn't exist
        print("🏢 Creating test company tenant...")
        tenant_id = await conn.fetchval("""
            INSERT INTO tenants (name, is_active, created_at, updated_at)
            VALUES ('Test Company', true, NOW(), NOW())
            ON CONFLICT DO NOTHING
            RETURNING id
        """)
        
        if not tenant_id:
            # Tenant already exists, get its ID
            tenant_id = await conn.fetchval("""
                SELECT id FROM tenants WHERE name = 'Test Company'
            """)
            
        if not tenant_id:
            # Use default tenant
            tenant_id = 2  # Default tenant from earlier
            
        print(f"📊 Using tenant_id: {tenant_id}")
        
        # Test users to create
        test_users = [
            {
                'email': '<EMAIL>',
                'username': '<EMAIL>',
                'is_superuser': False
            },
            {
                'email': '<EMAIL>', 
                'username': '<EMAIL>',
                'is_superuser': False
            },
            {
                'email': '<EMAIL>',
                'username': '<EMAIL>', 
                'is_superuser': False
            },
            {
                'email': '<EMAIL>',
                'username': '<EMAIL>',
                'is_superuser': False
            }
        ]
        
        print("👤 Creating test users...")
        for user_data in test_users:
            # Check if user already exists
            existing = await conn.fetchval("""
                SELECT id FROM users WHERE email = $1
            """, user_data['email'])
            
            if existing:
                print(f"  ✅ User {user_data['email']} already exists (ID: {existing})")
                continue
                
            # Create user
            user_id = await conn.fetchval("""
                INSERT INTO users (
                    email, username, hashed_password, is_active, is_verified, 
                    is_superuser, tenant_id, created_at, updated_at, login_count
                ) VALUES ($1, $2, $3, true, true, $4, $5, NOW(), NOW(), 0)
                RETURNING id
            """, 
                user_data['email'], user_data['username'], hashed_password,
                user_data['is_superuser'], tenant_id
            )
            
            print(f"  ✅ Created user {user_data['email']} (ID: {user_id})")
            
        print("✅ Test users setup complete!")
        
        # Test authentication
        print("\n🔐 Testing authentication...")
        user = await conn.fetchrow("""
            SELECT id, email, hashed_password, tenant_id 
            FROM users 
            WHERE email = '<EMAIL>'
        """)
        
        if user:
            print(f"  ✅ Found owner user: ID {user['id']}, Tenant {user['tenant_id']}")
            # Verify password
            if pwd_context.verify(test_password, user['hashed_password']):
                print(f"  ✅ Password verification successful")
            else:
                print(f"  ❌ Password verification failed")
        else:
            print(f"  ❌ Owner user not found")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(create_test_users())