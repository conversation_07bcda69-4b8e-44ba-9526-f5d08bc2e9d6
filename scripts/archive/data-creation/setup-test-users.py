#!/usr/bin/env python3
"""Create test users using asyncpg"""

import asyncio
import asyncpg
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def setup_test_users():
    """Create test users for the platform"""
    try:
        conn = await asyncpg.connect('postgresql://nikhilsingh@localhost:5432/giki_ai_dev')
        print('✅ Database connected successfully')
        
        # First, ensure we have a tenant
        tenant_result = await conn.fetchrow("SELECT id FROM tenant WHERE name = 'Test Company' LIMIT 1")
        if not tenant_result:
            tenant_id = await conn.fetchval("""
                INSERT INTO tenant (name, is_active, created_at, updated_at) 
                VALUES ('Test Company', true, NOW(), NOW()) 
                RETURNING id
            """)
            print(f"✅ Created tenant: Test Company (ID: {tenant_id})")
        else:
            tenant_id = tenant_result['id']
            print(f"✅ Using existing tenant: Test Company (ID: {tenant_id})")
        
        # Define test users
        test_users = [
            {
                'email': '<EMAIL>',
                'password': 'GikiTest2025Secure',
                'is_superuser': False,
                'role': 'owner'
            },
            {
                'email': '<EMAIL>', 
                'password': 'GikiTest2025Secure',
                'is_superuser': False,
                'role': 'accountant'
            },
            {
                'email': '<EMAIL>',
                'password': 'GikiTest2025Secure', 
                'is_superuser': False,
                'role': 'bookkeeper'
            },
            {
                'email': '<EMAIL>',
                'password': 'GikiTest2025Secure',
                'is_superuser': False, 
                'role': 'viewer'
            },
            {
                'email': '<EMAIL>',
                'password': 'GikiTest2025Secure',
                'is_superuser': True,
                'role': 'admin'
            }
        ]
        
        created_count = 0
        for user_data in test_users:
            # Check if user exists
            existing = await conn.fetchrow("SELECT id FROM users WHERE email = $1", user_data['email'])
            
            if existing:
                print(f"⚠️  User {user_data['email']} already exists")
                continue
                
            # Hash password
            hashed_password = pwd_context.hash(user_data['password'])
            
            # Create user
            user_id = await conn.fetchval("""
                INSERT INTO users (
                    email, hashed_password, is_active, is_superuser, 
                    tenant_id, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW()) 
                RETURNING id
            """, 
                user_data['email'],
                hashed_password, 
                True,  # is_active
                user_data['is_superuser'],
                tenant_id
            )
            
            print(f"✅ Created user: {user_data['email']} (ID: {user_id}, Role: {user_data['role']})")
            created_count += 1
        
        print(f"\n🎉 Setup complete! Created {created_count} new users")
        print(f"🔐 All users use password: GikiTest2025Secure")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f'❌ Setup failed: {e}')
        return False

if __name__ == "__main__":
    success = asyncio.run(setup_test_users())
    exit(0 if success else 1)