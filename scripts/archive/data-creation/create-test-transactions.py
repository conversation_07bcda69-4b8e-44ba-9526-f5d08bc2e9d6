#!/usr/bin/env python3
"""Create test transactions for testing endpoints"""

import asyncio
import asyncpg
from uuid import uuid4
from datetime import datetime, date

async def create_test_transactions():
    """Create some test transactions"""
    try:
        conn = await asyncpg.connect('postgresql://nikhilsingh@localhost:5432/giki_ai_dev')
        print('✅ Database connected successfully')
        
        # Get a tenant ID from the singular tenant table (which is what the FK references)
        tenant = await conn.fetchrow("SELECT id FROM tenant LIMIT 1")
        if not tenant:
            print("❌ No tenant found in tenant table")
            return False
            
        tenant_id = tenant['id']
        print(f"✅ Using tenant ID: {tenant_id}")
        
        # Create some test transactions
        test_transactions = [
            {
                'description': 'Amazon Web Services - Cloud Storage',
                'amount': -125.50,
                'date': date(2025, 1, 5),
            },
            {
                'description': 'Client Payment - Invoice #1001',
                'amount': 2500.00,
                'date': date(2025, 1, 6),
            },
            {
                'description': 'Office Supplies - Staples',
                'amount': -75.25,
                'date': date(2025, 1, 7),
            },
            {
                'description': 'Google Workspace Subscription',
                'amount': -12.99,
                'date': date(2025, 1, 8),
            },
            {
                'description': 'Marketing Campaign - Facebook Ads',
                'amount': -200.00,
                'date': date(2025, 1, 9),
            }
        ]
        
        created_ids = []
        for tx_data in test_transactions:
            
            tx_id = await conn.fetchval("""
                INSERT INTO transactions (
                    tenant_id, description, amount, date, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, NOW(), NOW())
                RETURNING id
            """, 
                tenant_id,
                tx_data['description'],
                tx_data['amount'],
                tx_data['date']
            )
            
            created_ids.append(tx_id)
            print(f"✅ Created transaction: {tx_data['description']} (ID: {tx_id})")
        
        print(f"\n🎉 Created {len(created_ids)} test transactions")
        print(f"📋 Transaction IDs for testing:")
        for i, tx_id in enumerate(created_ids):
            print(f"  {i+1}. {tx_id}")
        
        await conn.close()
        return created_ids
        
    except Exception as e:
        print(f'❌ Transaction creation failed: {e}')
        return []

if __name__ == "__main__":
    result = asyncio.run(create_test_transactions())
    if result:
        print(f"\n✅ Success! Created {len(result)} transactions")
    else:
        print("❌ Failed to create transactions")