#!/usr/bin/env python3
"""
Create test transactions for tenant_id=3 to fix empty dashboard issue.
This script addresses the tenant data mismatch where test users on tenant_id=3
have no transactions to display.
"""

import asyncio
import csv
import logging
import os
import sys
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from pathlib import Path
from typing import List, Dict, Any
import uuid

# Add the API source to Python path
api_path = Path(__file__).parent.parent / "apps" / "giki-ai-api" / "src"
sys.path.insert(0, str(api_path))

try:
    import asyncpg
except ImportError as e:
    print(f"Error importing dependencies: {e}")
    print("Make sure you have asyncpg installed: pip install asyncpg")
    sys.exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_database_connection():
    """Create database connection using proper configuration"""
    try:
        # Try to get database URL from environment first (matches API config)
        database_url = os.getenv('DATABASE_URL')
        
        if not database_url:
            # Use the same database connection as MCP PostgreSQL tools
            database_url = 'postgresql://giki_ai_user@127.0.0.1:5432/giki_ai_db'
            logger.info("Using MCP-compatible database URL (giki_ai_db)")
        else:
            logger.info("Using DATABASE_URL from environment")
        
        conn = await asyncpg.connect(database_url)
        logger.info("✅ Database connection established")
        
        # Test the connection with a simple query
        version = await conn.fetchval('SELECT version()')
        logger.info(f"Connected to: {version[:50]}...")
        
        return conn
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        raise

async def get_categories_for_tenant(conn, tenant_id: int) -> List[Dict[str, Any]]:
    """Get available categories for the tenant"""
    
    # First, let's check if the categories table exists
    table_check = """
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'categories'
    )
    """
    table_exists = await conn.fetchval(table_check)
    logger.info(f"Categories table exists: {table_exists}")
    
    if not table_exists:
        logger.error("Categories table does not exist!")
        return []
    
    # Check total categories
    total_count = await conn.fetchval("SELECT COUNT(*) FROM categories")
    logger.info(f"Total categories in database: {total_count}")
    
    # First, let's check what tenants exist
    tenant_check = "SELECT DISTINCT tenant_id FROM categories ORDER BY tenant_id"
    tenant_rows = await conn.fetch(tenant_check)
    logger.info(f"Available tenants in categories: {[row['tenant_id'] for row in tenant_rows]}")
    
    # Check total categories for this tenant
    count_query = "SELECT COUNT(*) FROM categories WHERE tenant_id = $1"
    count = await conn.fetchval(count_query, tenant_id)
    logger.info(f"Total categories for tenant {tenant_id}: {count}")
    
    query = """
    SELECT id, name, parent_id, gl_code, path, level
    FROM categories 
    WHERE tenant_id = $1
    ORDER BY level, name
    LIMIT 20
    """
    rows = await conn.fetch(query, tenant_id)
    categories = [dict(row) for row in rows]
    logger.info(f"Found {len(categories)} categories for tenant {tenant_id}")
    
    if categories:
        logger.info(f"Sample categories: {[c['name'] for c in categories[:5]]}")
    
    return categories

async def create_sample_transactions(conn, tenant_id: int, categories: List[Dict[str, Any]]) -> int:
    """Create sample transactions for the tenant"""
    
    # Sample transaction data
    sample_transactions = [
        {"description": "Office Rent - January", "amount": -25000.00, "vendor": "Property Management Co", "type": "expense"},
        {"description": "Consulting Revenue - ABC Corp", "amount": 150000.00, "vendor": "ABC Corp", "type": "income"},
        {"description": "Software Subscription - Microsoft 365", "amount": -2500.00, "vendor": "Microsoft", "type": "expense"},
        {"description": "Marketing Campaign - Google Ads", "amount": -15000.00, "vendor": "Google", "type": "expense"},
        {"description": "Professional Services - XYZ Ltd", "amount": 75000.00, "vendor": "XYZ Ltd", "type": "income"},
        {"description": "Office Supplies - Staples", "amount": -3500.00, "vendor": "Staples", "type": "expense"},
        {"description": "Internet & Phone - Airtel", "amount": -1800.00, "vendor": "Airtel", "type": "expense"},
        {"description": "Project Revenue - DEF Corp", "amount": 200000.00, "vendor": "DEF Corp", "type": "income"},
        {"description": "Travel Expenses - Flight Booking", "amount": -12000.00, "vendor": "IndiGo", "type": "expense"},
        {"description": "Training & Development", "amount": -8000.00, "vendor": "Training Institute", "type": "expense"},
        {"description": "Client Payment - GHI Ltd", "amount": 125000.00, "vendor": "GHI Ltd", "type": "income"},
        {"description": "Electricity Bill", "amount": -4500.00, "vendor": "BESCOM", "type": "expense"},
        {"description": "Professional Consultation", "amount": 95000.00, "vendor": "Consultant ABC", "type": "income"},
        {"description": "Equipment Purchase - Laptop", "amount": -65000.00, "vendor": "Dell", "type": "expense"},
        {"description": "Maintenance & Repairs", "amount": -7500.00, "vendor": "Service Provider", "type": "expense"},
    ]
    
    # Get suitable categories
    expense_categories = [c for c in categories if c['parent_id'] is not None and 'expense' in (c['name'] or '').lower()]
    income_categories = [c for c in categories if c['parent_id'] is not None and 'income' in (c['name'] or '').lower()]
    
    # Fallback to any available categories
    if not expense_categories:
        expense_categories = [c for c in categories if c['parent_id'] is not None][:5]
    if not income_categories:
        income_categories = [c for c in categories if c['parent_id'] is not None][-5:]
    
    logger.info(f"Using {len(expense_categories)} expense categories and {len(income_categories)} income categories")
    
    created_count = 0
    base_date = datetime.now() - timedelta(days=90)  # Start 3 months ago
    
    for i, transaction_data in enumerate(sample_transactions):
        transaction_id = str(uuid.uuid4())
        transaction_date = base_date + timedelta(days=i * 6)  # Spread over time
        
        # Choose appropriate category
        if transaction_data['type'] == 'expense' and expense_categories:
            category = expense_categories[i % len(expense_categories)]
        elif transaction_data['type'] == 'income' and income_categories:
            category = income_categories[i % len(income_categories)]
        else:
            category = categories[i % len(categories)] if categories else None
        
        # Create transaction
        insert_query = """
        INSERT INTO transactions (
            id, tenant_id, date, description, amount, vendor, account,
            transaction_type, category_id, ai_confidence, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        """
        
        try:
            await conn.execute(
                insert_query,
                transaction_id,
                tenant_id,
                transaction_date.date(),
                transaction_data['description'],
                Decimal(str(transaction_data['amount'])),
                transaction_data['vendor'],
                "Business Account",
                transaction_data['type'],
                category['id'] if category else None,
                0.95,  # High AI confidence for test data
                datetime.now(),
                datetime.now()
            )
            created_count += 1
            logger.info(f"Created transaction {created_count}: {transaction_data['description']}")
            
        except Exception as e:
            logger.error(f"Failed to create transaction {transaction_data['description']}: {e}")
    
    return created_count

async def update_dashboard_metrics(conn, tenant_id: int):
    """Update any cached metrics for the tenant"""
    # This could include updating any cached dashboard data
    # For now, just log that transactions are ready
    
    # Check final transaction count
    count_query = "SELECT COUNT(*) FROM transactions WHERE tenant_id = $1"
    count = await conn.fetchval(count_query, tenant_id)
    
    logger.info(f"✅ Tenant {tenant_id} now has {count} transactions")
    return count

async def main():
    """Main execution function"""
    logger.info("🚀 Starting test transaction creation for tenant_id=3")
    
    conn = None
    try:
        # Connect to database
        conn = await create_database_connection()
        
        # Target tenant (where test users are)
        tenant_id = 3
        
        # Get categories for the tenant
        categories = await get_categories_for_tenant(conn, tenant_id)
        
        if not categories:
            logger.error(f"❌ No categories found for tenant {tenant_id}")
            return
        
        # Create sample transactions
        created_count = await create_sample_transactions(conn, tenant_id, categories)
        
        # Update metrics
        total_count = await update_dashboard_metrics(conn, tenant_id)
        
        logger.info(f"✅ Successfully created {created_count} test transactions")
        logger.info(f"✅ Tenant {tenant_id} dashboard should now show data")
        
    except Exception as e:
        logger.error(f"❌ Script execution failed: {e}")
        raise
    finally:
        if conn:
            await conn.close()
            logger.info("🔒 Database connection closed")

if __name__ == "__main__":
    asyncio.run(main())