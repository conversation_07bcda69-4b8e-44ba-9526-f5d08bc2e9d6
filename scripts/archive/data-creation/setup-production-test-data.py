#!/usr/bin/env python3
"""
Setup test data in production database
"""
import asyncio
import asyncpg
import json
from datetime import datetime, date

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def setup_production_test_data():
    """Create basic test data in production database"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check if test users exist
        print("👤 Checking test users...")
        users = await conn.fetch("SELECT id, email, tenant_id FROM users ORDER BY id")
        print(f"Found {len(users)} users:")
        for user in users:
            print(f"  - {user['email']} (ID: {user['id']}, Tenant: {user['tenant_id']})")
        
        # Find owner test user
        owner_user = None
        for user in users:
            if user['email'] == '<EMAIL>':
                owner_user = user
                break
        
        if not owner_user:
            print("❌ <NAME_EMAIL> not found!")
            return False
            
        tenant_id = owner_user['tenant_id']
        print(f"✅ Using tenant_id: {tenant_id}")
        
        # Check existing transactions
        print("📊 Checking existing transactions...")
        existing_tx = await conn.fetchval(
            "SELECT COUNT(*) FROM transactions WHERE tenant_id = $1", 
            tenant_id
        )
        print(f"Found {existing_tx} existing transactions for tenant {tenant_id}")
        
        # Create some test transactions if none exist
        if existing_tx == 0:
            print("📝 Creating test transactions...")
            test_transactions = [
                {
                    'description': 'Test Office Supplies Purchase',
                    'amount': -150.00,
                    'date': date(2025, 1, 15),
                    'account': 'Business Checking',
                    'transaction_type': 'debit'
                },
                {
                    'description': 'Test Client Payment Received',
                    'amount': 2500.00,
                    'date': date(2025, 1, 16),
                    'account': 'Business Checking', 
                    'transaction_type': 'credit'
                },
                {
                    'description': 'Test Software Subscription',
                    'amount': -99.00,
                    'date': date(2025, 1, 17),
                    'account': 'Business Checking',
                    'transaction_type': 'debit'
                }
            ]
            
            for tx in test_transactions:
                tx_id = await conn.fetchval("""
                    INSERT INTO transactions (
                        tenant_id, description, amount, date, account, transaction_type, 
                        created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                    RETURNING id
                """, 
                    tenant_id, tx['description'], tx['amount'], tx['date'], 
                    tx['account'], tx['transaction_type']
                )
                print(f"  ✅ Created transaction {tx_id}: {tx['description']}")
                
        # Check categories
        print("📂 Checking categories...")
        categories = await conn.fetch(
            "SELECT id, name, parent_id FROM categories WHERE tenant_id = $1 ORDER BY id", 
            tenant_id
        )
        print(f"Found {len(categories)} categories for tenant {tenant_id}")
        for cat in categories[:5]:  # Show first 5
            print(f"  - {cat['name']} (ID: {cat['id']}, Parent: {cat['parent_id']})")
        if len(categories) > 5:
            print(f"  ... and {len(categories) - 5} more")
            
        # Create basic categories if none exist
        if len(categories) == 0:
            print("📝 Creating basic categories...")
            basic_categories = [
                {'name': 'Income', 'parent_id': None},
                {'name': 'Expenses', 'parent_id': None},
                {'name': 'Office Supplies', 'parent_id': None},
                {'name': 'Software', 'parent_id': None},
                {'name': 'Revenue', 'parent_id': None}
            ]
            
            for cat in basic_categories:
                cat_id = await conn.fetchval("""
                    INSERT INTO categories (tenant_id, name, parent_id, created_at, updated_at)
                    VALUES ($1, $2, $3, NOW(), NOW())
                    RETURNING id
                """, tenant_id, cat['name'], cat['parent_id'])
                print(f"  ✅ Created category {cat_id}: {cat['name']}")
        
        print("✅ Production test data setup complete!")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up test data: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(setup_production_test_data())