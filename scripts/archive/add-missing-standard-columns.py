#!/usr/bin/env python3
"""
Add commonly missing columns that might be expected by the API
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def add_missing_standard_columns():
    """Add commonly missing columns that FastAPI might expect"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check current columns
        print("🔍 Checking current transactions columns...")
        columns = await conn.fetch("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'transactions' AND table_schema = 'public'
        """)
        
        existing_columns = [col['column_name'] for col in columns]
        print(f"Found {len(existing_columns)} existing columns")
        
        # Common columns that might be missing from a typical transactions table
        potential_missing_columns = [
            ('external_id', 'VARCHAR(255)'),
            ('bank_transaction_id', 'VARCHAR(255)'),
            ('bank_reference', 'VARCHAR(255)'),
            ('check_number', 'VARCHAR(50)'),
            ('memo', 'TEXT'),
            ('balance', 'DECIMAL(15,2)'),
            ('running_balance', 'DECIMAL(15,2)'),
            ('cleared', 'BOOLEAN DEFAULT FALSE'),
            ('reconciled', 'BOOLEAN DEFAULT FALSE'),
            ('currency', 'VARCHAR(3) DEFAULT \'USD\''),
            ('exchange_rate', 'DECIMAL(10,6)'),
            ('split_transaction_id', 'UUID'),
            ('parent_transaction_id', 'UUID'),
            ('transaction_class', 'VARCHAR(50)'),
            ('transaction_source', 'VARCHAR(50)'),
            ('fee_amount', 'DECIMAL(15,2)'),
            ('tax_amount', 'DECIMAL(15,2)'),
            ('total_amount', 'DECIMAL(15,2)'),
            ('location', 'VARCHAR(255)'),
            ('merchant_category_code', 'VARCHAR(10)'),
            ('card_last_four', 'VARCHAR(4)'),
            ('authorization_code', 'VARCHAR(50)'),
            ('batch_id', 'VARCHAR(100)'),
            ('import_source', 'VARCHAR(100)'),
            ('import_date', 'TIMESTAMP WITH TIME ZONE'),
            ('version', 'INTEGER DEFAULT 1'),
            ('full_category_path', 'VARCHAR(500)'),
            ('gl_code', 'VARCHAR(50)'),
            ('notes', 'TEXT'),
            ('attachments', 'TEXT[]'),
        ]
        
        added_columns = []
        for column_name, column_type in potential_missing_columns:
            if column_name not in existing_columns:
                try:
                    print(f"📝 Adding column {column_name} ({column_type})...")
                    await conn.execute(f"""
                        ALTER TABLE transactions 
                        ADD COLUMN IF NOT EXISTS {column_name} {column_type}
                    """)
                    print(f"✅ Added column {column_name}")
                    added_columns.append(column_name)
                except Exception as e:
                    print(f"⚠️ Failed to add column {column_name}: {e}")
            else:
                print(f"⏭️ Column {column_name} already exists")
        
        print(f"\n📊 Added {len(added_columns)} new columns: {added_columns}")
        
        # Check final count
        final_columns = await conn.fetch("""
            SELECT COUNT(*) as count
            FROM information_schema.columns 
            WHERE table_name = 'transactions' AND table_schema = 'public'
        """)
        
        print(f"📋 Total columns now: {final_columns[0]['count']}")
        
        print("\n✅ Standard columns added!")
        return True
        
    except Exception as e:
        print(f"❌ Error adding standard columns: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(add_missing_standard_columns())