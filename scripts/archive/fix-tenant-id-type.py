#!/usr/bin/env python3
"""
Check and potentially fix tenant_id type issues
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def check_id_types():
    """Check ID field types that might be causing Pydantic issues"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check ID field types across key tables
        print("🔍 Checking ID field types across tables...")
        
        tables_to_check = ['transactions', 'categories', 'uploads', 'users']
        
        for table in tables_to_check:
            print(f"\n📊 {table.upper()} table ID fields:")
            try:
                id_columns = await conn.fetch(f"""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns 
                    WHERE table_name = '{table}' 
                    AND column_name LIKE '%id%'
                    AND table_schema = 'public'
                    ORDER BY column_name
                """)
                
                for col in id_columns:
                    nullable = 'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'
                    print(f"  - {col['column_name']}: {col['data_type']} ({nullable})")
                    
                # Check sample data types for this table
                if table == 'transactions':
                    sample = await conn.fetchrow(f"SELECT id, tenant_id, category_id FROM {table} LIMIT 1")
                    if sample:
                        print(f"  Sample data types:")
                        print(f"    id: {sample['id']} ({type(sample['id']).__name__})")
                        print(f"    tenant_id: {sample['tenant_id']} ({type(sample['tenant_id']).__name__})")
                        print(f"    category_id: {sample['category_id']} ({type(sample['category_id']).__name__})")
                        
            except Exception as e:
                print(f"  ❌ Error checking {table}: {e}")
        
        # Check if there are any string ID fields we might need
        print(f"\n🔍 Checking for alternative ID column patterns...")
        
        # Look for columns that might be used as string IDs
        string_id_candidates = await conn.fetch("""
            SELECT table_name, column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = 'public'
            AND (column_name ILIKE '%uuid%' OR column_name ILIKE '%external_id%' OR column_name ILIKE '%public_id%')
            ORDER BY table_name, column_name
        """)
        
        for col in string_id_candidates:
            print(f"  - {col['table_name']}.{col['column_name']}: {col['data_type']}")
        
        print("\n✅ ID type analysis completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error checking ID types: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(check_id_types())