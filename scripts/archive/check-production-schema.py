#!/usr/bin/env python3
"""
Check production database schema
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def check_production_schema():
    """Check what tables and schemas exist in production"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check available schemas
        print("📋 Available schemas:")
        schemas = await conn.fetch("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
            ORDER BY schema_name
        """)
        for schema in schemas:
            print(f"  - {schema['schema_name']}")
        
        # Check all tables across schemas
        print("\n📊 Available tables:")
        tables = await conn.fetch("""
            SELECT table_schema, table_name 
            FROM information_schema.tables 
            WHERE table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
            ORDER BY table_schema, table_name
        """)
        
        current_schema = None
        for table in tables:
            if table['table_schema'] != current_schema:
                current_schema = table['table_schema']
                print(f"\n  {current_schema}:")
            print(f"    - {table['table_name']}")
        
        # Try to find user-like tables
        print("\n👤 Looking for user tables...")
        user_tables = await conn.fetch("""
            SELECT table_schema, table_name 
            FROM information_schema.tables 
            WHERE table_name ILIKE '%user%' 
               OR table_name ILIKE '%tenant%'
               OR table_name ILIKE '%auth%'
        """)
        
        for table in user_tables:
            print(f"  - {table['table_schema']}.{table['table_name']}")
            
            # Check columns of this table
            columns = await conn.fetch("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_schema = $1 AND table_name = $2
                ORDER BY ordinal_position
            """, table['table_schema'], table['table_name'])
            
            for col in columns[:5]:  # Show first 5 columns
                print(f"      {col['column_name']} ({col['data_type']})")
            if len(columns) > 5:
                print(f"      ... and {len(columns) - 5} more columns")
                
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(check_production_schema())