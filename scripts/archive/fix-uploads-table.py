#!/usr/bin/env python3
"""
Fix uploads table to match development schema
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def fix_uploads_table():
    """Fix uploads table structure to match development"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check current uploads table structure
        print("📊 Checking current uploads table structure...")
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'uploads' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        existing_columns = [col['column_name'] for col in columns]
        print(f"Existing columns: {existing_columns}")
        
        # Add missing columns that exist in development
        missing_columns = [
            ('file_name', 'VARCHAR(255)'),
            ('content_type', 'VARCHAR(255)'),
            ('size', 'INTEGER'),
            ('file_type', 'VARCHAR(255)'),
            ('processing_status', 'VARCHAR(255) DEFAULT \'pending\''),
            ('status', 'VARCHAR(255) DEFAULT \'uploaded\''),
            ('row_count', 'INTEGER DEFAULT 0'),
            ('processed_count', 'INTEGER DEFAULT 0'),
            ('error_count', 'INTEGER DEFAULT 0'),
            ('error_message', 'TEXT'),
            ('headers', 'JSON'),
            ('upload_type', 'VARCHAR(255)'),
            ('has_category_labels', 'BOOLEAN'),
            ('baseline_accuracy', 'DECIMAL(5,2)'),
            ('transaction_count', 'INTEGER'),
        ]
        
        added_columns = []
        for column_name, column_type in missing_columns:
            if column_name not in existing_columns:
                print(f"📝 Adding column {column_name} ({column_type})...")
                try:
                    await conn.execute(f"""
                        ALTER TABLE uploads 
                        ADD COLUMN IF NOT EXISTS {column_name} {column_type}
                    """)
                    print(f"✅ Added column {column_name}")
                    added_columns.append(column_name)
                except Exception as e:
                    print(f"⚠️ Failed to add column {column_name}: {e}")
            else:
                print(f"⏭️ Column {column_name} already exists")
                
        # Update the upload_status to use development default if needed
        print("📝 Setting default values for new columns...")
        if 'file_name' in added_columns:
            await conn.execute("UPDATE uploads SET file_name = filename WHERE file_name IS NULL")
        if 'content_type' in added_columns:
            await conn.execute("UPDATE uploads SET content_type = mime_type WHERE content_type IS NULL")
        if 'size' in added_columns:
            await conn.execute("UPDATE uploads SET size = file_size WHERE size IS NULL")
                
        # Check final structure
        print("\n📊 Final uploads table structure:")
        final_columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'uploads' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        print(f"Total columns: {len(final_columns)}")
        for col in final_columns:
            nullable = 'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'
            print(f"  - {col['column_name']}: {col['data_type']} ({nullable})")
            
        # Check how many uploads exist
        count = await conn.fetchval("SELECT COUNT(*) FROM uploads")
        print(f"\n📋 Total uploads: {count}")
        
        print("\n✅ Uploads table structure fixed!")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing uploads table: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(fix_uploads_table())