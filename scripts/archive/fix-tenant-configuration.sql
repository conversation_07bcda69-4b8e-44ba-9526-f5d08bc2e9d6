-- Fix Tenant Configuration Script
-- Purpose: Properly configure tenants for M1, M2, and M3 scenarios
-- Date: 2025-01-02

-- Step 1: Ensure tenants exist in tenant table (the one with foreign key)
INSERT INTO tenant (id, name, created_at, updated_at)
SELECT id, name, created_at, updated_at FROM tenants WHERE id IN (4, 5, 6)
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, updated_at = CURRENT_TIMESTAMP;

-- Step 2: Update tenant settings for proper milestone association
UPDATE tenants 
SET 
  name = 'Nuvie M1 Zero-Onboarding',
  settings = jsonb_build_object(
    'milestone', 'M1',
    'onboarding_type', 'zero_onboarding',
    'use_remarks_column', true,
    'target_accuracy', 85,
    'batch_size', 100,
    'description', 'Zero onboarding with remarks column for category hints'
  ),
  updated_at = CURRENT_TIMESTAMP
WHERE id = 4;

UPDATE tenants 
SET 
  name = 'Rezolve M2 Temporal Learning',
  settings = jsonb_build_object(
    'milestone', 'M2',
    'onboarding_type', 'historical_learning',
    'use_temporal_accuracy', true,
    'training_period', 'Jan-Jun 2024',
    'validation_period', 'Jul-Dec 2024',
    'target_accuracy', 90,
    'description', 'Historical data learning with temporal accuracy validation'
  ),
  updated_at = CURRENT_TIMESTAMP
WHERE id = 5;

UPDATE tenants 
SET 
  name = 'Giki M3 GL Schema',
  settings = jsonb_build_object(
    'milestone', 'M3',
    'onboarding_type', 'schema_guided',
    'use_gl_codes', true,
    'gl_schema_version', '1.0',
    'compliance_required', true,
    'description', 'Predefined GL code schema for accounting compliance'
  ),
  updated_at = CURRENT_TIMESTAMP
WHERE id = 6;

-- Step 2: Fix user tenant assignments
-- Move <EMAIL> from tenant 3 (default) to tenant 4 (nuvie)
UPDATE users 
SET tenant_id = 4, updated_at = CURRENT_TIMESTAMP
WHERE email = '<EMAIL>';

-- Create test users for other tenants if they don't exist
INSERT INTO users (email, username, hashed_password, tenant_id, is_active, is_verified, created_at, updated_at)
VALUES 
  ('<EMAIL>', '<EMAIL>', '$2b$12$GR7ROytL2bXn3uaMOX1CEOdVbtOAe1DLB8CEaCg50INRGHdhDrZPG', 5, true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('<EMAIL>', '<EMAIL>', '$2b$12$GR7ROytL2bXn3uaMOX1CEOdVbtOAe1DLB8CEaCg50INRGHdhDrZPG', 6, true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (email) DO UPDATE
SET tenant_id = EXCLUDED.tenant_id, updated_at = CURRENT_TIMESTAMP;

-- Step 3: Clear all existing transaction data for fresh start
DELETE FROM transactions WHERE tenant_id IN (1, 2, 3, 4, 5, 6);
DELETE FROM categorizations WHERE tenant_id IN (1, 2, 3, 4, 5, 6);
DELETE FROM accuracy_scores WHERE tenant_id IN (1, 2, 3, 4, 5, 6);

-- Step 4: Create Business-Focused MIS Categories
-- First, delete existing categories
DELETE FROM categories WHERE tenant_id IN (4, 5, 6);

-- Insert business-focused MIS categories for all three tenants
-- INCOME Categories (Simple and Business-Friendly)
WITH income_categories AS (
  INSERT INTO categories (name, parent_id, tenant_id, is_income, hierarchy_level, created_at, updated_at)
  SELECT 
    'Income' as name,
    NULL as parent_id,
    t.id as tenant_id,
    true as is_income,
    1 as hierarchy_level,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  FROM (SELECT id FROM tenants WHERE id IN (4, 5, 6)) AS t
  RETURNING id, tenant_id
)
INSERT INTO categories (name, parent_id, tenant_id, is_income, hierarchy_level, created_at, updated_at)
SELECT 
  subcat.name,
  ic.id as parent_id,
  ic.tenant_id,
  true as is_income,
  2 as hierarchy_level,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
FROM income_categories ic
CROSS JOIN (VALUES 
  ('Sales Income'),
  ('Service Income'),
  ('Other Income')
) AS subcat(name);

-- EXPENSE Categories (Business Owner Friendly)
WITH expense_main AS (
  INSERT INTO categories (name, parent_id, tenant_id, is_income, hierarchy_level, created_at, updated_at)
  SELECT 
    'Expenses' as name,
    NULL as parent_id,
    t.id as tenant_id,
    false as is_income,
    1 as hierarchy_level,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  FROM (SELECT id FROM tenants WHERE id IN (4, 5, 6)) AS t
  RETURNING id, tenant_id
)
-- Operating Expenses (What businesses actually track)
INSERT INTO categories (name, parent_id, tenant_id, is_income, hierarchy_level, mis_description, created_at, updated_at)
SELECT 
  subcat.name,
  em.id as parent_id,
  em.tenant_id,
  false as is_income,
  2 as hierarchy_level,
  subcat.description as mis_description,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
FROM expense_main em
CROSS JOIN (VALUES 
  ('Employee Costs', 'Salaries, wages, benefits, PF, ESI'),
  ('Office & Admin', 'Rent, utilities, office supplies, maintenance'),
  ('Marketing & Sales', 'Advertising, promotions, sales commissions'),
  ('Technology & Software', 'Software subscriptions, IT services, equipment'),
  ('Professional Services', 'Legal, accounting, consulting fees'),
  ('Travel & Transportation', 'Business travel, fuel, vehicle expenses'),
  ('Finance Charges', 'Interest, bank fees, processing charges'),
  ('Taxes & Compliance', 'GST, income tax, other statutory payments'),
  ('Other Operating Expenses', 'Miscellaneous business expenses')
) AS subcat(name, description);

-- Step 5: Add GL codes for M3 tenant (Giki) - Business Focused
UPDATE categories 
SET 
  gl_code = CASE 
    -- Income GL Codes (4000 series)
    WHEN name = 'Sales Income' THEN '4000'
    WHEN name = 'Service Income' THEN '4100'
    WHEN name = 'Other Income' THEN '4900'
    -- Expense GL Codes (6000 series for Operating Expenses)
    WHEN name = 'Employee Costs' THEN '6000'
    WHEN name = 'Office & Admin' THEN '6100'
    WHEN name = 'Marketing & Sales' THEN '6200'
    WHEN name = 'Technology & Software' THEN '6300'
    WHEN name = 'Professional Services' THEN '6400'
    WHEN name = 'Travel & Transportation' THEN '6500'
    WHEN name = 'Finance Charges' THEN '7000'
    WHEN name = 'Taxes & Compliance' THEN '8000'
    WHEN name = 'Other Operating Expenses' THEN '6900'
  END
WHERE tenant_id = 6 AND gl_code IS NULL;

-- Step 6: Update uploads to correct tenant associations
UPDATE uploads 
SET tenant_id = 4, processing_status = 'pending'
WHERE filename LIKE '%nuvie%';

UPDATE uploads 
SET tenant_id = 5, processing_status = 'pending'  
WHERE filename IN ('Capital One.xlsx', 'Credit Card.xlsx', 'ICICI.xlsx', 'SVB.xlsx');

-- Step 7: Add onboarding context to tenants for business-aware categorization
UPDATE tenants
SET settings = settings || jsonb_build_object(
  'business_context', jsonb_build_object(
    'industry', 'Technology Services',
    'business_type', 'B2B SaaS',
    'size', 'Small Business',
    'location', 'India',
    'categorization_hints', jsonb_build_object(
      'Facebook', 'Marketing & Sales',
      'Google', 'Marketing & Sales', 
      'AWS', 'Technology & Software',
      'Uber', 'Travel & Transportation',
      'Swiggy', 'Office & Admin',
      'Zomato', 'Office & Admin'
    )
  )
)
WHERE id = 4; -- Nuvie

UPDATE tenants
SET settings = settings || jsonb_build_object(
  'business_context', jsonb_build_object(
    'industry', 'Financial Services',
    'business_type', 'B2B Fintech',
    'size', 'Medium Business',
    'location', 'India',
    'categorization_hints', jsonb_build_object(
      'Salesforce', 'Technology & Software',
      'LinkedIn', 'Marketing & Sales',
      'Delta Airlines', 'Travel & Transportation',
      'Hilton', 'Travel & Transportation'
    )
  )
)  
WHERE id = 5; -- Rezolve

UPDATE tenants
SET settings = settings || jsonb_build_object(
  'business_context', jsonb_build_object(
    'industry', 'Professional Services',
    'business_type', 'Consulting',
    'size', 'Small Business',
    'location', 'India',
    'gl_schema_enabled', true
  )
)
WHERE id = 6; -- Giki with GL codes

-- Display summary
SELECT 
  'Tenant Configuration Updated' as status,
  COUNT(DISTINCT t.id) as total_tenants,
  COUNT(DISTINCT u.id) as total_users,
  COUNT(DISTINCT c.id) as total_categories
FROM tenants t
LEFT JOIN users u ON u.tenant_id = t.id
LEFT JOIN categories c ON c.tenant_id = t.id
WHERE t.id IN (4, 5, 6);