#!/usr/bin/env python3
"""
Check users table structure in production
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def check_users_table():
    """Check users table structure"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check users table columns
        print("👤 Users table structure:")
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'users'
            ORDER BY ordinal_position
        """)
        
        for col in columns:
            print(f"  - {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
        
        # Check if there are any users
        print("\n📊 Existing users:")
        users = await conn.fetch("SELECT * FROM users LIMIT 5")
        for user in users:
            print(f"  - {dict(user)}")
            
        # Check tenants table too
        print("\n🏢 Tenants table structure:")
        tenant_columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'tenants'
            ORDER BY ordinal_position
        """)
        
        for col in tenant_columns:
            print(f"  - {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
        
        # Check existing tenants
        print("\n📊 Existing tenants:")
        tenants = await conn.fetch("SELECT * FROM tenants LIMIT 5")
        for tenant in tenants:
            print(f"  - {dict(tenant)}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(check_users_table())