#!/usr/bin/env python3
"""
Fix transactions table missing columns
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def fix_transactions_table():
    """Add missing columns to transactions table"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check current transactions table structure
        print("📊 Checking current transactions table structure...")
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'transactions' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        existing_columns = [col['column_name'] for col in columns]
        print(f"Existing columns ({len(existing_columns)}): {existing_columns}")
        
        # Check development schema for comparison
        print("\n🔍 Checking what columns should exist...")
        
        # List of columns that should exist based on the error and typical transaction structure
        required_columns = [
            ('original_category', 'VARCHAR(255)'),
            ('original_amount', 'DECIMAL(15,2)'),
            ('original_description', 'TEXT'),
            ('ai_suggested_category', 'INTEGER'),
            ('ai_suggested_confidence', 'DECIMAL(5,4)'),
            ('processing_status', 'VARCHAR(50) DEFAULT \'pending\''),
            ('source_file_name', 'VARCHAR(255)'),
            ('row_number', 'INTEGER'),
            ('duplicate_group_id', 'UUID'),
            ('is_duplicate', 'BOOLEAN DEFAULT FALSE'),
            ('file_upload_session_id', 'VARCHAR(255)'),
            ('reconciliation_status', 'VARCHAR(50)'),
            ('tags', 'TEXT[]'),
        ]
        
        # Add missing columns
        missing_columns = []
        for column_name, column_type in required_columns:
            if column_name not in existing_columns:
                missing_columns.append((column_name, column_type))
                
        print(f"\nMissing columns to add: {[col[0] for col in missing_columns]}")
        
        for column_name, column_type in missing_columns:
            print(f"📝 Adding column {column_name} ({column_type})...")
            try:
                await conn.execute(f"""
                    ALTER TABLE transactions 
                    ADD COLUMN IF NOT EXISTS {column_name} {column_type}
                """)
                print(f"✅ Added column {column_name}")
            except Exception as e:
                print(f"⚠️ Failed to add column {column_name}: {e}")
                
        # Add foreign key constraint for ai_suggested_category if it doesn't exist
        if 'ai_suggested_category' in [col[0] for col in missing_columns]:
            print("📝 Adding foreign key constraint for ai_suggested_category...")
            try:
                await conn.execute("""
                    ALTER TABLE transactions 
                    ADD CONSTRAINT IF NOT EXISTS transactions_ai_suggested_category_fkey 
                    FOREIGN KEY (ai_suggested_category) REFERENCES categories(id)
                """)
                print("✅ Added foreign key constraint")
            except Exception as e:
                print(f"⚠️ Failed to add foreign key: {e}")
                
        # Check final structure
        print("\n📊 Final transactions table structure:")
        final_columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'transactions' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        print(f"Total columns: {len(final_columns)}")
        for col in final_columns:
            nullable = 'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'
            print(f"  - {col['column_name']}: {col['data_type']} ({nullable})")
            
        # Check how many transactions exist
        count = await conn.fetchval("SELECT COUNT(*) FROM transactions")
        print(f"\n📋 Total transactions: {count}")
        
        if count > 0:
            sample_transactions = await conn.fetch("SELECT * FROM transactions LIMIT 2")
            print("Sample transactions:")
            for tx in sample_transactions:
                print(f"  - ID: {tx['id']}, Description: {tx['description']}, Amount: {tx['amount']}")
        
        print("\n✅ Transactions table structure fixed!")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing transactions table: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(fix_transactions_table())