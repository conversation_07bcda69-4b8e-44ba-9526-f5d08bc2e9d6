#!/usr/bin/env python3
"""
Fix categories table path column type to match development
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def fix_categories_path_column():
    """Fix the path column type from text[] to varchar"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check current path column type
        print("📊 Checking current path column type...")
        path_column = await conn.fetch("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'categories' AND column_name = 'path'
        """)
        
        if path_column:
            current_type = path_column[0]['data_type']
            print(f"Current path column type: {current_type}")
            
            if current_type == 'ARRAY':
                print("📝 Converting path column from text[] to varchar...")
                
                # First, drop the GIN index on path
                print("🗑️ Dropping GIN index on path column...")
                await conn.execute("DROP INDEX IF EXISTS idx_categories_path")
                
                # Convert the column type
                await conn.execute("""
                    ALTER TABLE categories 
                    ALTER COLUMN path TYPE character varying 
                    USING array_to_string(path, '/')
                """)
                
                # Create a new btree index for varchar
                print("📝 Creating new btree index on path column...")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_categories_path ON categories(path)")
                
                print("✅ Path column converted to varchar with new index")
            else:
                print(f"Path column is already {current_type}")
                
        # Also add missing columns that we see in development
        print("📝 Adding missing columns from development schema...")
        
        existing_columns_result = await conn.fetch("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'categories' AND table_schema = 'public'
        """)
        existing_columns = [col['column_name'] for col in existing_columns_result]
        
        missing_columns = []
        
        if 'original_labels' not in existing_columns:
            missing_columns.append(('original_labels', 'text[]'))
            
        if 'is_unified_category' not in existing_columns:
            missing_columns.append(('is_unified_category', 'boolean DEFAULT false'))
            
        if 'unified_category_id' not in existing_columns:
            missing_columns.append(('unified_category_id', 'integer'))
            
        if 'schema_discovery_session_id' not in existing_columns:
            missing_columns.append(('schema_discovery_session_id', 'character varying(255)'))
            
        if 'source_files' not in existing_columns:
            missing_columns.append(('source_files', 'text[]'))
            
        for column_name, column_type in missing_columns:
            print(f"📝 Adding column {column_name} ({column_type})...")
            await conn.execute(f"""
                ALTER TABLE categories 
                ADD COLUMN IF NOT EXISTS {column_name} {column_type}
            """)
            print(f"✅ Added column {column_name}")
            
        # Check final structure
        print("\n📊 Final categories table structure (key columns):")
        final_columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'categories' AND table_schema = 'public'
            AND column_name IN ('path', 'original_labels', 'is_unified_category', 'unified_category_id', 'schema_discovery_session_id', 'source_files')
            ORDER BY ordinal_position
        """)
        
        for col in final_columns:
            print(f"  - {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
            
        print("\n✅ Categories table path column and missing columns fixed!")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing categories table: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(fix_categories_path_column())