#!/usr/bin/env python3
"""
Safe production database migration - only add missing tables
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def safe_production_migration():
    """Safely add missing tables to production database"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check existing tables
        print("📊 Checking current tables...")
        existing_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        existing_table_names = [table['table_name'] for table in existing_tables]
        print(f"Existing tables: {existing_table_names}")
        
        # Create update timestamp function if it doesn't exist
        print("📝 Creating update timestamp function...")
        await conn.execute("""
            CREATE OR REPLACE FUNCTION trigger_update_timestamp()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        # Create categories table if missing
        if 'categories' not in existing_table_names:
            print("📝 Creating categories table...")
            await conn.execute("""
                CREATE TABLE categories (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    parent_id INTEGER,
                    level INTEGER DEFAULT 0,
                    path TEXT[],
                    is_income BOOLEAN DEFAULT FALSE,
                    is_system BOOLEAN DEFAULT FALSE,
                    gl_code VARCHAR(20),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    CONSTRAINT fk_categories_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
                    CONSTRAINT fk_categories_parent FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
                    CONSTRAINT uq_categories_tenant_name_parent UNIQUE (tenant_id, name, parent_id),
                    CONSTRAINT chk_categories_level CHECK (level >= 0 AND level <= 10)
                );
                
                CREATE INDEX idx_categories_tenant_id ON categories(tenant_id);
                CREATE INDEX idx_categories_parent_id ON categories(parent_id);
                CREATE INDEX idx_categories_path ON categories USING GIN(path);
                CREATE INDEX idx_categories_income ON categories(is_income);
                CREATE INDEX idx_categories_system ON categories(is_system);
                
                CREATE TRIGGER trigger_categories_updated_at
                    BEFORE UPDATE ON categories
                    FOR EACH ROW EXECUTE FUNCTION trigger_update_timestamp();
            """)
            print("✅ Categories table created")
        
        # Create uploads table if missing
        if 'uploads' not in existing_table_names:
            print("📝 Creating uploads table...")
            await conn.execute("""
                CREATE TABLE uploads (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    filename VARCHAR(255) NOT NULL,
                    original_filename VARCHAR(255) NOT NULL,
                    file_size BIGINT NOT NULL,
                    mime_type VARCHAR(255),
                    file_path VARCHAR(500),
                    upload_status VARCHAR(50) DEFAULT 'pending',
                    processed_at TIMESTAMP WITH TIME ZONE,
                    metadata JSONB DEFAULT '{}',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    CONSTRAINT fk_uploads_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
                    CONSTRAINT fk_uploads_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    CONSTRAINT chk_uploads_status CHECK (upload_status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
                    CONSTRAINT chk_uploads_file_size CHECK (file_size > 0)
                );
                
                CREATE INDEX idx_uploads_tenant_id ON uploads(tenant_id);
                CREATE INDEX idx_uploads_user_id ON uploads(user_id);
                CREATE INDEX idx_uploads_status ON uploads(upload_status);
                CREATE INDEX idx_uploads_created_at ON uploads(created_at);
                
                CREATE TRIGGER trigger_uploads_updated_at
                    BEFORE UPDATE ON uploads
                    FOR EACH ROW EXECUTE FUNCTION trigger_update_timestamp();
            """)
            print("✅ Uploads table created")
        
        # Create transactions table if missing
        if 'transactions' not in existing_table_names:
            print("📝 Creating transactions table...")
            await conn.execute("""
                CREATE TABLE transactions (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    tenant_id INTEGER NOT NULL,
                    upload_id INTEGER,
                    category_id INTEGER,
                    date DATE NOT NULL,
                    description TEXT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    is_debit BOOLEAN DEFAULT TRUE,
                    vendor VARCHAR(255),
                    reference_number VARCHAR(255),
                    account VARCHAR(255),
                    transaction_type VARCHAR(50),
                    ai_confidence DECIMAL(5,4),
                    user_confirmed_category_id INTEGER,
                    entity_id INTEGER,
                    metadata JSONB DEFAULT '{}',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    CONSTRAINT fk_transactions_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
                    CONSTRAINT fk_transactions_upload FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE SET NULL,
                    CONSTRAINT fk_transactions_category FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
                    CONSTRAINT chk_transactions_amount CHECK (amount != 0)
                );
                
                CREATE INDEX idx_transactions_tenant_id ON transactions(tenant_id);
                CREATE INDEX idx_transactions_upload_id ON transactions(upload_id);
                CREATE INDEX idx_transactions_category_id ON transactions(category_id);
                CREATE INDEX idx_transactions_date ON transactions(date);
                CREATE INDEX idx_transactions_tenant_date ON transactions(tenant_id, date);
                CREATE INDEX idx_transactions_amount ON transactions(amount);
                CREATE INDEX idx_transactions_vendor ON transactions(vendor);
                
                CREATE TRIGGER trigger_transactions_updated_at
                    BEFORE UPDATE ON transactions
                    FOR EACH ROW EXECUTE FUNCTION trigger_update_timestamp();
            """)
            print("✅ Transactions table created")
        
        # Create entities table if missing
        if 'entities' not in existing_table_names:
            print("📝 Creating entities table...")
            await conn.execute("""
                CREATE TABLE entities (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER NOT NULL,
                    entity_type VARCHAR(50) DEFAULT 'business',
                    name VARCHAR(255) NOT NULL,
                    legal_name VARCHAR(255),
                    business_type VARCHAR(100),
                    industry VARCHAR(100),
                    address JSONB DEFAULT '{}',
                    contact_info JSONB DEFAULT '{}',
                    tax_info JSONB DEFAULT '{}',
                    settings JSONB DEFAULT '{}',
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    CONSTRAINT fk_entities_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
                    CONSTRAINT uq_entities_tenant_name UNIQUE (tenant_id, name)
                );
                
                CREATE INDEX idx_entities_tenant_id ON entities(tenant_id);
                CREATE INDEX idx_entities_type ON entities(entity_type);
                CREATE INDEX idx_entities_active ON entities(is_active);
                
                CREATE TRIGGER trigger_entities_updated_at
                    BEFORE UPDATE ON entities
                    FOR EACH ROW EXECUTE FUNCTION trigger_update_timestamp();
            """)
            print("✅ Entities table created")
        
        # Create onboarding_status table if missing
        if 'onboarding_status' not in existing_table_names:
            print("📝 Creating onboarding_status table...")
            await conn.execute("""
                CREATE TABLE onboarding_status (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER NOT NULL,
                    onboarding_type VARCHAR(50) DEFAULT 'historical_data',
                    stage VARCHAR(50) DEFAULT 'not_started',
                    approved_for_production BOOLEAN DEFAULT FALSE,
                    approved_at TIMESTAMP WITH TIME ZONE,
                    approved_by_user_id INTEGER,
                    approval_notes TEXT,
                    last_validation_id VARCHAR(255),
                    last_validation_accuracy DECIMAL(5,4),
                    total_transactions INTEGER DEFAULT 0,
                    transactions_with_labels INTEGER DEFAULT 0,
                    date_range_start TIMESTAMP WITH TIME ZONE,
                    date_range_end TIMESTAMP WITH TIME ZONE,
                    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    CONSTRAINT fk_onboarding_status_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
                    CONSTRAINT fk_onboarding_status_approved_by FOREIGN KEY (approved_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
                    CONSTRAINT uq_onboarding_status_tenant UNIQUE (tenant_id),
                    CONSTRAINT chk_onboarding_status_accuracy CHECK (last_validation_accuracy IS NULL OR (last_validation_accuracy >= 0 AND last_validation_accuracy <= 1)),
                    CONSTRAINT chk_onboarding_status_transactions CHECK (total_transactions >= 0 AND transactions_with_labels >= 0 AND transactions_with_labels <= total_transactions)
                );
                
                CREATE INDEX idx_onboarding_status_tenant ON onboarding_status(tenant_id);
                CREATE INDEX idx_onboarding_status_stage ON onboarding_status(stage);
                CREATE INDEX idx_onboarding_status_approved ON onboarding_status(approved_for_production);
                CREATE INDEX idx_onboarding_status_type ON onboarding_status(onboarding_type);
                CREATE INDEX idx_onboarding_status_activity ON onboarding_status(last_activity DESC);
            """)
            print("✅ Onboarding status table created")
        
        # Create vendor_category_mappings table if missing
        if 'vendor_category_mappings' not in existing_table_names:
            print("📝 Creating vendor_category_mappings table...")
            await conn.execute("""
                CREATE TABLE vendor_category_mappings (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER NOT NULL,
                    vendor_name VARCHAR(255) NOT NULL,
                    category_name VARCHAR(255) NOT NULL,
                    gl_code VARCHAR(20),
                    confidence DECIMAL(5,4) DEFAULT 0.9,
                    source VARCHAR(50) DEFAULT 'vendor_enhancement',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    CONSTRAINT fk_vendor_mappings_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
                    CONSTRAINT uq_vendor_mappings_tenant_vendor UNIQUE (tenant_id, vendor_name),
                    CONSTRAINT chk_vendor_mappings_confidence CHECK (confidence >= 0 AND confidence <= 1),
                    CONSTRAINT chk_vendor_mappings_source CHECK (source IN ('vendor_enhancement', 'historical_enhancement', 'manual_mapping', 'ai_generated'))
                );
                
                CREATE INDEX idx_vendor_mappings_tenant ON vendor_category_mappings(tenant_id);
                CREATE INDEX idx_vendor_mappings_vendor ON vendor_category_mappings(vendor_name);
                CREATE INDEX idx_vendor_mappings_category ON vendor_category_mappings(category_name);
                CREATE INDEX idx_vendor_mappings_confidence ON vendor_category_mappings(confidence DESC);
                CREATE INDEX idx_vendor_mappings_source ON vendor_category_mappings(source);
            """)
            print("✅ Vendor category mappings table created")
        
        # Create mis_enhancements table if missing
        if 'mis_enhancements' not in existing_table_names:
            print("📝 Creating mis_enhancements table...")
            await conn.execute("""
                CREATE TABLE mis_enhancements (
                    id SERIAL PRIMARY KEY,
                    tenant_id INTEGER NOT NULL,
                    setup_id INTEGER NOT NULL,
                    enhancement_type VARCHAR(50) NOT NULL,
                    accuracy_gain DECIMAL(5,4) DEFAULT 0.0,
                    status VARCHAR(20) DEFAULT 'completed',
                    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    CONSTRAINT fk_mis_enhancements_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
                    CONSTRAINT chk_mis_enhancements_accuracy_gain CHECK (accuracy_gain >= 0 AND accuracy_gain <= 1),
                    CONSTRAINT chk_mis_enhancements_type CHECK (enhancement_type IN ('historical', 'schema', 'vendor')),
                    CONSTRAINT chk_mis_enhancements_status CHECK (status IN ('pending', 'in_progress', 'completed', 'failed'))
                );
                
                CREATE INDEX idx_mis_enhancements_tenant ON mis_enhancements(tenant_id);
                CREATE INDEX idx_mis_enhancements_setup ON mis_enhancements(setup_id);
                CREATE INDEX idx_mis_enhancements_type ON mis_enhancements(enhancement_type);
                CREATE INDEX idx_mis_enhancements_applied ON mis_enhancements(applied_at DESC);
            """)
            print("✅ MIS enhancements table created")
        
        # Check final table status
        print("\n📊 Final table status:")
        final_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        for table in final_tables:
            table_name = table['table_name']
            try:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                print(f"  ✅ {table_name}: {count} records")
            except Exception as e:
                print(f"  ⚠️ {table_name}: Error counting - {e}")
        
        print("\n🎉 Production database migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error migrating database: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(safe_production_migration())