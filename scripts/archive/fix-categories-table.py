#!/usr/bin/env python3
"""
Fix categories table to add missing columns
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def fix_categories_table():
    """Add missing columns to categories table"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check current categories table structure
        print("📊 Checking current categories table structure...")
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'categories' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        existing_columns = [col['column_name'] for col in columns]
        print(f"Existing columns: {existing_columns}")
        
        # Add missing columns
        missing_columns = []
        
        if 'color' not in existing_columns:
            missing_columns.append(('color', 'VARCHAR(7)'))
            
        if 'icon' not in existing_columns:
            missing_columns.append(('icon', 'VARCHAR(50)'))
            
        if 'sort_order' not in existing_columns:
            missing_columns.append(('sort_order', 'INTEGER'))
            
        if 'is_archived' not in existing_columns:
            missing_columns.append(('is_archived', 'BOOLEAN DEFAULT FALSE'))
            
        if 'gl_account_name' not in existing_columns:
            missing_columns.append(('gl_account_name', 'VARCHAR(255)'))
            
        if 'gl_account_number' not in existing_columns:
            missing_columns.append(('gl_account_number', 'VARCHAR(50)'))
            
        if 'gl_account_type' not in existing_columns:
            missing_columns.append(('gl_account_type', 'VARCHAR(50)'))
            
        for column_name, column_type in missing_columns:
            print(f"📝 Adding column {column_name} ({column_type})...")
            await conn.execute(f"""
                ALTER TABLE categories 
                ADD COLUMN IF NOT EXISTS {column_name} {column_type}
            """)
            print(f"✅ Added column {column_name}")
            
        # Update some default values
        print("📝 Setting default values...")
        await conn.execute("""
            UPDATE categories 
            SET color = '#295343' 
            WHERE color IS NULL
        """)
        
        await conn.execute("""
            UPDATE categories 
            SET icon = 'square' 
            WHERE icon IS NULL
        """)
        
        await conn.execute("""
            UPDATE categories 
            SET sort_order = id 
            WHERE sort_order IS NULL
        """)
        
        # Check final structure
        print("\n📊 Final categories table structure:")
        final_columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'categories' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        for col in final_columns:
            print(f"  - {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
            
        # Check how many categories exist
        count = await conn.fetchval("SELECT COUNT(*) FROM categories")
        print(f"\n📋 Total categories: {count}")
        
        if count > 0:
            sample_categories = await conn.fetch("SELECT * FROM categories LIMIT 3")
            print("Sample categories:")
            for cat in sample_categories:
                print(f"  - {dict(cat)}")
        
        print("\n✅ Categories table structure fixed!")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing categories table: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(fix_categories_table())