#!/usr/bin/env python3
"""
Comprehensive production API testing
"""
import asyncio
import httpx
import json

BASE_URL = "https://giki-ai-api-273348121056.us-central1.run.app"

async def test_production_apis():
    """Test all production API endpoints"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # 1. Health check
        print("🔍 Testing health endpoint...")
        try:
            response = await client.get(f"{BASE_URL}/health")
            print(f"Health: {response.status_code} - {response.json()}")
        except Exception as e:
            print(f"Health check failed: {e}")
            
        # 2. Authentication
        print("\n🔐 Testing authentication...")
        try:
            auth_data = {
                "username": "<EMAIL>",
                "password": "GikiTest2025Secure"
            }
            response = await client.post(
                f"{BASE_URL}/api/v1/auth/token",
                data=auth_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                token_data = response.json()
                token = token_data["access_token"]
                print(f"✅ Authentication successful - Token length: {len(token)}")
                
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                
                # 3. Categories endpoint
                print("\n📂 Testing categories endpoint...")
                try:
                    response = await client.get(f"{BASE_URL}/api/v1/categories", headers=headers)
                    print(f"Categories: {response.status_code}")
                    if response.status_code == 200:
                        categories = response.json()
                        print(f"✅ Categories retrieved - Count: {len(categories)}")
                        if categories:
                            print(f"Sample category: {categories[0]}")
                    else:
                        error_text = response.text[:200]
                        print(f"❌ Categories error: {error_text}")
                except Exception as e:
                    print(f"❌ Categories test failed: {e}")
                
                # 4. Transactions endpoint  
                print("\n💰 Testing transactions endpoint...")
                try:
                    response = await client.get(f"{BASE_URL}/api/v1/transactions", headers=headers)
                    print(f"Transactions: {response.status_code}")
                    if response.status_code == 200:
                        transactions = response.json()
                        print(f"✅ Transactions retrieved - Count: {len(transactions)}")
                        if transactions:
                            print(f"Sample transaction: {transactions[0]}")
                    else:
                        error_text = response.text[:200]
                        print(f"❌ Transactions error: {error_text}")
                except Exception as e:
                    print(f"❌ Transactions test failed: {e}")
                
                # 5. Upload status endpoint
                print("\n📤 Testing uploads endpoint...")
                try:
                    response = await client.get(f"{BASE_URL}/api/v1/uploads", headers=headers)
                    print(f"Uploads: {response.status_code}")
                    if response.status_code == 200:
                        uploads = response.json()
                        print(f"✅ Uploads retrieved - Count: {len(uploads)}")
                    else:
                        error_text = response.text[:200]
                        print(f"❌ Uploads error: {error_text}")
                except Exception as e:
                    print(f"❌ Uploads test failed: {e}")
                
                # 6. Health check with auth
                print("\n🏥 Testing authenticated health endpoint...")
                try:
                    response = await client.get(f"{BASE_URL}/api/v1/monitoring/dashboard", headers=headers)
                    print(f"Monitoring: {response.status_code}")
                    if response.status_code == 200:
                        monitoring = response.json()
                        print(f"✅ Monitoring data retrieved")
                        print(f"Monitoring keys: {list(monitoring.keys())}")
                    else:
                        error_text = response.text[:200]
                        print(f"❌ Monitoring error: {error_text}")
                except Exception as e:
                    print(f"❌ Monitoring test failed: {e}")
                    
            else:
                print(f"❌ Authentication failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Authentication test failed: {e}")
            
        print("\n🎯 Production API testing completed!")

if __name__ == "__main__":
    asyncio.run(test_production_apis())