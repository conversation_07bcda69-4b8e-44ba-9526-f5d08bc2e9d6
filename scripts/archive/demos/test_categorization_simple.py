#!/usr/bin/env python3
"""
Simple Categorization Test
=========================

Test the categorization system directly with a few examples.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "apps" / "giki-ai-api" / "src"))

from giki_ai_api.domains.categories.business_category_mapper import business_category_mapper


def test_categorization_examples():
    """Test categorization with specific examples."""
    
    test_cases = [
        {
            "description": "Microsoft Office 365 Subscription",
            "amount": -19.99,
            "vendor": "Microsoft",
            "expected": "Software & Technology"
        },
        {
            "description": "Uber ride to airport", 
            "amount": -35.50,
            "vendor": "Uber",
            "expected": "Travel & Transportation"
        },
        {
            "description": "ACH Credit Consulting Payment",
            "amount": 45980.68,
            "vendor": None,
            "expected": "Consulting Income"
        },
        {
            "description": "Office supplies from Staples",
            "amount": -67.43,
            "vendor": "Staples",
            "expected": "Office Supplies"
        },
        {
            "description": "Business lunch at restaurant",
            "amount": -85.60,
            "vendor": None,
            "expected": "Meals & Entertainment"
        }
    ]
    
    print("🧪 Testing Business Category Mapper")
    print("=" * 50)
    
    correct = 0
    total = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        try:
            result = business_category_mapper.categorize_transaction(
                description=case["description"],
                amount=case["amount"],
                vendor=case["vendor"]
            )
            
            is_correct = case["expected"] in result.category_name or result.category_name in case["expected"]
            if is_correct:
                correct += 1
                status = "✅"
            else:
                status = "❌"
            
            print(f"{status} Test {i}: {case['description'][:40]}...")
            print(f"   Expected: {case['expected']}")
            print(f"   Got: {result.category_name}")
            print(f"   Parent: {result.parent_category}")
            print(f"   Confidence: {result.confidence:.2f}")
            print(f"   Reasoning: {result.reasoning[:60]}...")
            print()
            
        except Exception as e:
            print(f"❌ Test {i}: ERROR - {e}")
            print(f"   Description: {case['description']}")
            print()
    
    accuracy = correct / total
    print("=" * 50)
    print(f"📊 RESULTS: {correct}/{total} correct ({accuracy:.1%})")
    
    if accuracy >= 0.7:
        print("✅ CATEGORIZATION SYSTEM PASSES")
    else:
        print("❌ CATEGORIZATION SYSTEM NEEDS IMPROVEMENT")
        print("   Patterns and logic need refinement")
    
    return accuracy


if __name__ == "__main__":
    accuracy = test_categorization_examples()