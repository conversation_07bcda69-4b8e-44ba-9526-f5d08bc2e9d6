#!/usr/bin/env python3
"""
Demo script to showcase the context-aware vendor lookup system.

This demonstrates how the system intelligently determines what to search for
and validates results based on business context.
"""

import asyncio
import asyncpg
from apps.giki_ai_api.src.giki_ai_api.domains.categories.vendor_lookup_service import (
    VendorLookupService, VendorSearchContext
)
from apps.giki_ai_api.src.giki_ai_api.domains.categories.vendor_detection_service import VendorPattern


async def demo_vendor_lookup():
    """Demonstrate the vendor lookup system."""
    
    print("=== Context-Aware Vendor Lookup Demo ===\n")
    
    # Initialize service
    service = VendorLookupService()
    
    # Demo vendors with different characteristics
    demo_vendors = [
        {
            "name": "STARBUCKS #1234",
            "avg_amount": 8.50,
            "count": 45,
            "context": "Coffee purchases, small amounts, frequent"
        },
        {
            "name": "AWS SERVICES",
            "avg_amount": 2500.00,
            "count": 12,
            "context": "Monthly charges, large amounts, cloud services"
        },
        {
            "name": "OFFICE DEPOT #567",
            "avg_amount": 125.00,
            "count": 8,
            "context": "Office supplies, medium amounts, periodic"
        },
        {
            "name": "UBER *TRIP HELP.UBER.COM",
            "avg_amount": 25.00,
            "count": 30,
            "context": "Transportation, variable amounts, frequent"
        },
        {
            "name": "ACME CONSULTING LLC",
            "avg_amount": 5000.00,
            "count": 6,
            "context": "Professional services, large amounts, monthly"
        }
    ]
    
    for vendor_info in demo_vendors:
        print(f"\nVendor: {vendor_info['name']}")
        print(f"Context: {vendor_info['context']}")
        print("-" * 50)
        
        # Create vendor pattern
        pattern = VendorPattern(
            pattern=vendor_info['name'],
            normalized_name=vendor_info['name'].split('#')[0].split('*')[0].strip(),
            transaction_count=vendor_info['count'],
            total_amount=vendor_info['avg_amount'] * vendor_info['count'],
            avg_amount=vendor_info['avg_amount'],
            category_variations=2,
            confidence_score=0.6,  # Low confidence to trigger lookup
            sample_descriptions=[vendor_info['name']]
        )
        
        # Build search context
        context = VendorSearchContext(
            vendor_name=pattern.normalized_name,
            transaction_patterns=[vendor_info['name']],
            avg_amount=vendor_info['avg_amount'],
            transaction_count=vendor_info['count'],
            existing_categories=[],
            business_industry="General Business",
            location=None
        )
        
        # Build search query
        search_query = service._build_contextual_search_query(context)
        print(f"Search Query: {search_query}")
        
        # Try pattern matching first
        pattern_result = service._identify_by_pattern(pattern, context)
        if pattern_result:
            print(f"Pattern Match: ✓ ({pattern_result.confidence_score:.2f} confidence)")
            print(f"Business Type: {pattern_result.business_type}")
            print(f"Category: {pattern_result.suggested_category_path}")
        else:
            print("Pattern Match: ✗ (Would use Google search)")
            
            # Simulate what would happen with Google search
            if vendor_info['avg_amount'] < 20:
                print("Amount Analysis: Small amounts → Likely retail/restaurant")
                print("Category: Expenses/Operating Expenses/General Supplies")
            elif vendor_info['avg_amount'] < 100:
                print("Amount Analysis: Medium amounts → Likely services/dining")
                print("Category: Expenses/Meals & Entertainment")
            elif vendor_info['avg_amount'] > 1000:
                print("Amount Analysis: Large amounts → Likely subscription/services")
                print("Category: Expenses/Operating Expenses/Software & Subscriptions")
        
        print("\nValidation Factors:")
        print(f"- Transaction frequency: {vendor_info['count']} (", end="")
        if vendor_info['count'] > 20:
            print("High - boosts confidence)")
        else:
            print("Low - reduces confidence)")
        
        print(f"- Average amount: ${vendor_info['avg_amount']:.2f} (", end="")
        if vendor_info['avg_amount'] > 500 and 'restaurant' in vendor_info['name'].lower():
            print("Unusual for type - reduces confidence)")
        else:
            print("Matches expected range)")


if __name__ == "__main__":
    print("\nThis demo shows how the vendor lookup system:")
    print("1. Builds intelligent search queries with business context")
    print("2. Uses pattern matching for known vendors")
    print("3. Validates results based on transaction patterns")
    print("4. Ensures searches have proper context\n")
    
    asyncio.run(demo_vendor_lookup())
    
    print("\n\n=== Key Insights ===")
    print("✓ Known vendors (Starbucks, Uber) are identified by patterns")
    print("✓ Search queries include business context and transaction hints")
    print("✓ Amount patterns help determine business type")
    print("✓ High transaction frequency increases confidence")
    print("✓ Validation catches mismatches (e.g., $500 restaurant bill)")