#!/usr/bin/env python3
"""
Comprehensive test of all production endpoints
"""
import asyncio
import httpx

BASE_URL = "https://giki-ai-api-273348121056.us-central1.run.app"

async def test_all_endpoints():
    """Test all available endpoints comprehensively"""
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # Get fresh authentication token
        print("🔐 Getting fresh authentication token...")
        try:
            auth_data = {
                "username": "<EMAIL>",
                "password": "GikiTest2025Secure"
            }
            response = await client.post(
                f"{BASE_URL}/api/v1/auth/token",
                data=auth_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code != 200:
                print(f"❌ Authentication failed: {response.status_code}")
                return
                
            token = response.json()["access_token"]
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            print("✅ Authentication successful")
            
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return
            
        # Test all major endpoint groups
        endpoint_groups = {
            "Categories": [
                "/api/v1/categories",
                "/api/v1/categories/",
            ],
            "Transactions": [
                "/api/v1/transactions",
                "/api/v1/transactions/",
            ],
            "Files": [
                "/api/v1/files",
                "/api/v1/files/",
            ],
            "Reports": [
                "/api/v1/reports",
                "/api/v1/reports/",
                "/api/v1/reports/export",
                "/api/v1/reports/dashboard",
            ],
            "Analytics": [
                "/api/v1/analytics",
                "/api/v1/analytics/",
                "/api/v1/analytics/accuracy",
                "/api/v1/analytics/trends",
            ],
            "AI/Agent": [
                "/api/v1/agent",
                "/api/v1/agent/",
                "/api/v1/agent/categorize",
                "/api/v1/agent/suggest",
            ],
            "User/Profile": [
                "/api/v1/users/me",
                "/api/v1/users/profile",
                "/api/v1/profile",
            ],
            "Admin/Monitoring": [
                "/api/v1/monitoring/dashboard",
                "/api/v1/monitoring/health",
                "/api/v1/admin/dashboard",
                "/api/v1/health",
            ],
            "Onboarding": [
                "/api/v1/onboarding",
                "/api/v1/onboarding/",
                "/api/v1/onboarding/status",
            ],
        }
        
        working_endpoints = []
        failing_endpoints = []
        
        print("\\n🔍 Testing all endpoint groups...")
        for group_name, endpoints in endpoint_groups.items():
            print(f"\\n📂 {group_name} endpoints:")
            
            for endpoint in endpoints:
                try:
                    response = await client.get(f"{BASE_URL}{endpoint}", headers=headers)
                    status = response.status_code
                    
                    if status == 200:
                        try:
                            data = response.json()
                            if isinstance(data, list):
                                print(f"  ✅ {endpoint} -> {status} (list with {len(data)} items)")
                                working_endpoints.append(endpoint)
                            else:
                                keys = list(data.keys())[:3] if isinstance(data, dict) else "non-dict"
                                print(f"  ✅ {endpoint} -> {status} (object: {keys})")
                                working_endpoints.append(endpoint)
                        except:
                            print(f"  ✅ {endpoint} -> {status} (text response)")
                            working_endpoints.append(endpoint)
                    elif status == 307:
                        location = response.headers.get('location', 'No location header')
                        print(f"  🔄 {endpoint} -> {status} (redirect to {location})")
                    elif status == 404:
                        print(f"  ❌ {endpoint} -> {status} (not found)")
                        failing_endpoints.append((endpoint, status, "not found"))
                    elif status == 401:
                        print(f"  🔑 {endpoint} -> {status} (unauthorized)")
                        failing_endpoints.append((endpoint, status, "unauthorized"))
                    elif status == 403:
                        print(f"  🚫 {endpoint} -> {status} (forbidden)")
                        failing_endpoints.append((endpoint, status, "forbidden"))
                    elif status == 500:
                        error_text = response.text[:100]
                        print(f"  💥 {endpoint} -> {status} (server error)")
                        failing_endpoints.append((endpoint, status, "server error"))
                    else:
                        print(f"  ⚠️  {endpoint} -> {status}")
                        failing_endpoints.append((endpoint, status, "other"))
                        
                except Exception as e:
                    print(f"  💥 {endpoint} -> Error: {str(e)[:50]}")
                    failing_endpoints.append((endpoint, "ERROR", str(e)[:50]))
        
        # Summary
        print(f"\\n📊 ENDPOINT TESTING SUMMARY:")
        print(f"✅ Working endpoints: {len(working_endpoints)}")
        for endpoint in working_endpoints:
            print(f"  - {endpoint}")
        
        print(f"\\n❌ Failing endpoints: {len(failing_endpoints)}")
        for endpoint, status, reason in failing_endpoints:
            print(f"  - {endpoint} ({status}: {reason})")
            
        print(f"\\n🎯 Testing completed!")

if __name__ == "__main__":
    asyncio.run(test_all_endpoints())