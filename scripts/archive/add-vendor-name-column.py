#!/usr/bin/env python3
"""
Add vendor_name column to transactions table
"""
import asyncio
import asyncpg

# Production database connection
DATABASE_URL = "*******************************************************************/giki_ai_db"

async def add_vendor_name_column():
    """Add vendor_name column to transactions table"""
    
    print("🔗 Connecting to production database...")
    conn = await asyncpg.connect(DATABASE_URL)
    
    try:
        # Check if vendor_name column exists
        print("🔍 Checking if vendor_name column exists...")
        columns = await conn.fetch("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'transactions' 
            AND column_name = 'vendor_name'
            AND table_schema = 'public'
        """)
        
        if columns:
            print("✅ vendor_name column already exists")
            return True
            
        # Add vendor_name column
        print("📝 Adding vendor_name column...")
        await conn.execute("""
            ALTER TABLE transactions 
            ADD COLUMN vendor_name VARCHAR(255)
        """)
        print("✅ Added vendor_name column")
        
        # Check final structure
        print("\n📊 Verifying vendor_name column was added...")
        verification = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'transactions' 
            AND column_name = 'vendor_name'
            AND table_schema = 'public'
        """)
        
        if verification:
            col = verification[0]
            print(f"✅ vendor_name: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
        else:
            print("❌ vendor_name column not found after adding!")
            
        print("\n✅ vendor_name column added successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error adding vendor_name column: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(add_vendor_name_column())