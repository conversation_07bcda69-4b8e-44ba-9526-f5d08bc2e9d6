#!/usr/bin/env python3
"""
COMPREHENSIVE Security & Performance Assessment Script

This script validates all security improvements and measures performance:
- Scans for hardcoded credentials across codebase
- Tests authentication performance (<200ms target)
- Validates Google Cloud Secret Manager access
- Checks file upload timeout configurations
- Assesses overall security posture
"""

import os
import sys
import time
import json
import asyncio
import subprocess
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "apps" / "giki-ai-api" / "src"))

# Performance and security imports
import aiofiles
import asyncpg
import redis.asyncio as redis


class SecurityAssessment:
    """Comprehensive security vulnerability assessment."""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "security_issues": [],
            "performance_metrics": {},
            "recommendations": [],
            "risk_score": 0,
        }
    
    async def run_full_assessment(self) -> Dict[str, Any]:
        """Run complete security and performance assessment."""
        print("🔒 Starting comprehensive security & performance assessment...")
        
        # Security assessments
        await self._scan_hardcoded_credentials()
        await self._validate_environment_security()
        await self._check_file_permissions()
        await self._analyze_dependency_vulnerabilities()
        
        # Performance assessments
        await self._measure_authentication_performance()
        await self._test_secret_manager_performance()
        await self._analyze_file_upload_timeouts()
        await self._validate_database_performance()
        
        # Generate recommendations
        self._generate_security_recommendations()
        self._calculate_risk_score()
        
        return self.results
    
    async def _scan_hardcoded_credentials(self):
        """Scan entire codebase for hardcoded credentials."""
        print("🔍 Scanning for hardcoded credentials...")
        
        dangerous_patterns = [
            # Database credentials
            ("iV6Jl5JhM63KRXB0H6dh3rLdm", "Database password"),
            ("giki_ai_user:iV6Jl5JhM63KRXB0H6dh3rLdm", "Full database credential"),
            
            # Test credentials
            ("GikiTest2025Secure", "Test password"),
            ("<EMAIL>", "Test email"),
            ("<EMAIL>", "Test email"),
            ("<EMAIL>", "Test email"),
            
            # API keys and secrets
            ("AIza", "Google API key prefix"),
            ("sk-", "OpenAI API key prefix"),
            ("aws_access_key", "AWS credentials"),
            ("PRIVATE KEY", "Private key"),
            
            # Common weak patterns
            ("password123", "Weak password"),
            ("admin:admin", "Default admin"),
            ("localhost:password", "Default localhost password"),
        ]
        
        excluded_dirs = {".git", "__pycache__", ".venv", "node_modules", ".pytest_cache"}
        excluded_files = {"SECURITY-REMEDIATION-PLAN.md", "security_performance_assessment.py"}
        
        issues_found = []
        
        for root, dirs, files in os.walk(self.project_root):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in excluded_dirs]
            
            for file in files:
                if file in excluded_files:
                    continue
                    
                file_path = Path(root) / file
                
                # Only scan text files
                if not self._is_text_file(file_path):
                    continue
                
                try:
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                        content = await f.read()
                        
                        for pattern, description in dangerous_patterns:
                            if pattern.lower() in content.lower():
                                issues_found.append({
                                    "type": "hardcoded_credential",
                                    "severity": "CRITICAL",
                                    "file": str(file_path.relative_to(self.project_root)),
                                    "pattern": description,
                                    "line_number": self._find_line_number(content, pattern),
                                })
                                
                except Exception as e:
                    print(f"⚠️ Could not scan {file_path}: {e}")
        
        self.results["security_issues"].extend(issues_found)
        print(f"{'✅' if not issues_found else '❌'} Credential scan: {len(issues_found)} issues found")
    
    def _is_text_file(self, file_path: Path) -> bool:
        """Check if file is a text file that should be scanned."""
        text_extensions = {'.py', '.js', '.ts', '.json', '.yaml', '.yml', '.env', '.md', '.txt', '.sh'}
        return file_path.suffix.lower() in text_extensions
    
    def _find_line_number(self, content: str, pattern: str) -> int:
        """Find line number of pattern in content."""
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if pattern.lower() in line.lower():
                return i
        return 0
    
    async def _validate_environment_security(self):
        """Validate environment configuration security."""
        print("🔧 Validating environment security...")
        
        issues = []
        
        # Check for .env files with credentials
        env_files = list(self.project_root.rglob(".env*"))
        for env_file in env_files:
            if env_file.name in {".env.example", ".env.template"}:
                continue
                
            try:
                async with aiofiles.open(env_file, 'r') as f:
                    content = await f.read()
                    
                    if "password" in content.lower() or "secret" in content.lower():
                        issues.append({
                            "type": "environment_credential",
                            "severity": "HIGH",
                            "file": str(env_file.relative_to(self.project_root)),
                            "description": "Environment file contains credentials",
                        })
            except Exception:
                pass
        
        # Check for missing critical environment variables
        critical_env_vars = [
            "GOOGLE_APPLICATION_CREDENTIALS",
            "VERTEX_PROJECT_ID",
        ]
        
        for var in critical_env_vars:
            if not os.getenv(var):
                issues.append({
                    "type": "missing_environment_variable",
                    "severity": "MEDIUM",
                    "variable": var,
                    "description": f"Critical environment variable {var} not set",
                })
        
        self.results["security_issues"].extend(issues)
        print(f"{'✅' if not issues else '❌'} Environment security: {len(issues)} issues found")
    
    async def _check_file_permissions(self):
        """Check file permissions for security issues."""
        print("📁 Checking file permissions...")
        
        issues = []
        sensitive_files = [
            "dev-service-account.json",
            ".env*",
            "private_key*",
            "*secret*",
        ]
        
        for pattern in sensitive_files:
            files = list(self.project_root.rglob(pattern))
            for file_path in files:
                if file_path.is_file():
                    try:
                        # Check if file is world-readable
                        stat = file_path.stat()
                        mode = stat.st_mode
                        
                        # Check for overly permissive permissions
                        if mode & 0o044:  # World or group readable
                            issues.append({
                                "type": "file_permissions",
                                "severity": "MEDIUM",
                                "file": str(file_path.relative_to(self.project_root)),
                                "permissions": oct(mode)[-3:],
                                "description": "Sensitive file has overly permissive permissions",
                            })
                    except Exception:
                        pass
        
        self.results["security_issues"].extend(issues)
        print(f"{'✅' if not issues else '❌'} File permissions: {len(issues)} issues found")
    
    async def _analyze_dependency_vulnerabilities(self):
        """Analyze dependencies for known vulnerabilities."""
        print("📦 Analyzing dependency vulnerabilities...")
        
        issues = []
        
        # Check Python dependencies
        try:
            result = subprocess.run(
                ["pip", "audit", "--format", "json"],
                capture_output=True,
                text=True,
                cwd=self.project_root / "apps" / "giki-ai-api"
            )
            
            if result.returncode == 0:
                vulnerabilities = json.loads(result.stdout)
                for vuln in vulnerabilities:
                    issues.append({
                        "type": "dependency_vulnerability",
                        "severity": "HIGH" if vuln.get("is_critical") else "MEDIUM",
                        "package": vuln.get("package"),
                        "vulnerability": vuln.get("description"),
                        "version": vuln.get("version"),
                    })
        except Exception as e:
            print(f"⚠️ Could not run pip audit: {e}")
        
        # Check Node.js dependencies
        try:
            result = subprocess.run(
                ["npm", "audit", "--json"],
                capture_output=True,
                text=True,
                cwd=self.project_root / "apps" / "giki-ai-app"
            )
            
            if result.returncode != 0:  # npm audit returns non-zero for vulnerabilities
                audit_data = json.loads(result.stdout)
                for vuln_id, vuln in audit_data.get("vulnerabilities", {}).items():
                    severity = vuln.get("severity", "MEDIUM").upper()
                    issues.append({
                        "type": "dependency_vulnerability",
                        "severity": severity,
                        "package": vuln.get("name"),
                        "vulnerability": vuln.get("title"),
                        "version": vuln.get("version"),
                    })
        except Exception as e:
            print(f"⚠️ Could not run npm audit: {e}")
        
        self.results["security_issues"].extend(issues)
        print(f"{'✅' if not issues else '❌'} Dependencies: {len(issues)} vulnerabilities found")
    
    async def _measure_authentication_performance(self):
        """Measure authentication performance against <200ms target."""
        print("⚡ Measuring authentication performance...")
        
        try:
            # Import authentication modules
            from giki_ai_api.domains.auth.performance_auth import authenticate_user_fast
            from giki_ai_api.core.database import get_db_connection
            
            # Test database connection
            conn = await get_db_connection()
            
            # Performance test iterations
            iterations = 10
            times = []
            
            for i in range(iterations):
                start_time = time.time()
                
                # Test authentication with non-existent user (should be fast failure)
                test_email = f"perf_test_{i}@example.com"
                test_password = "test_password_123"
                
                result = await authenticate_user_fast(conn, test_email, test_password)
                
                end_time = time.time()
                auth_time = (end_time - start_time) * 1000  # Convert to milliseconds
                times.append(auth_time)
            
            await conn.close()
            
            # Calculate performance metrics
            avg_time = sum(times) / len(times)
            max_time = max(times)
            min_time = min(times)
            
            performance_data = {
                "average_ms": round(avg_time, 2),
                "max_ms": round(max_time, 2),
                "min_ms": round(min_time, 2),
                "target_ms": 200,
                "meets_target": avg_time < 200,
                "iterations": iterations,
            }
            
            self.results["performance_metrics"]["authentication"] = performance_data
            
            status = "✅" if avg_time < 200 else "❌"
            print(f"{status} Authentication: {avg_time:.1f}ms avg (target: <200ms)")
            
            if avg_time >= 200:
                self.results["security_issues"].append({
                    "type": "performance_issue",
                    "severity": "MEDIUM",
                    "component": "authentication",
                    "metric": f"{avg_time:.1f}ms",
                    "target": "200ms",
                    "description": "Authentication performance exceeds target",
                })
        
        except Exception as e:
            print(f"❌ Authentication performance test failed: {e}")
            self.results["performance_metrics"]["authentication"] = {"error": str(e)}
    
    async def _test_secret_manager_performance(self):
        """Test Google Cloud Secret Manager performance and availability."""
        print("🔑 Testing Secret Manager performance...")
        
        try:
            from giki_ai_api.shared.services.google_cloud_secrets import get_secrets_manager
            
            secrets_manager = get_secrets_manager()
            
            # Test secret retrieval performance
            start_time = time.time()
            secret_value = secrets_manager.get_secret("auth-secret-key")
            end_time = time.time()
            
            retrieval_time = (end_time - start_time) * 1000  # Convert to ms
            
            performance_data = {
                "retrieval_time_ms": round(retrieval_time, 2),
                "target_ms": 5000,  # 5 second timeout
                "secret_retrieved": secret_value is not None,
                "meets_target": retrieval_time < 5000,
            }
            
            self.results["performance_metrics"]["secret_manager"] = performance_data
            
            status = "✅" if retrieval_time < 5000 and secret_value else "❌"
            print(f"{status} Secret Manager: {retrieval_time:.1f}ms")
            
            if retrieval_time >= 5000 or not secret_value:
                self.results["security_issues"].append({
                    "type": "secret_manager_issue",
                    "severity": "HIGH",
                    "retrieval_time_ms": retrieval_time,
                    "description": "Secret Manager performance or availability issue",
                })
        
        except Exception as e:
            print(f"❌ Secret Manager test failed: {e}")
            self.results["performance_metrics"]["secret_manager"] = {"error": str(e)}
            self.results["security_issues"].append({
                "type": "secret_manager_unavailable",
                "severity": "CRITICAL",
                "error": str(e),
                "description": "Secret Manager completely unavailable",
            })
    
    async def _analyze_file_upload_timeouts(self):
        """Analyze file upload timeout configurations."""
        print("📁 Analyzing file upload timeout configurations...")
        
        timeout_configs = []
        
        # Check FastAPI timeout middleware
        middleware_files = list(self.project_root.rglob("*middleware*.py"))
        for file_path in middleware_files:
            try:
                async with aiofiles.open(file_path, 'r') as f:
                    content = await f.read()
                    
                    if "timeout" in content.lower():
                        timeout_configs.append({
                            "file": str(file_path.relative_to(self.project_root)),
                            "type": "middleware_timeout",
                            "configured": True,
                        })
            except Exception:
                pass
        
        # Check for upload size limits
        config_files = list(self.project_root.rglob("*config*.py"))
        max_upload_size = None
        
        for file_path in config_files:
            try:
                async with aiofiles.open(file_path, 'r') as f:
                    content = await f.read()
                    
                    if "MAX_UPLOAD" in content:
                        # Extract the value
                        lines = content.split('\n')
                        for line in lines:
                            if "MAX_UPLOAD" in line and "=" in line:
                                try:
                                    value = line.split('=')[1].strip()
                                    max_upload_size = value
                                except:
                                    pass
            except Exception:
                pass
        
        upload_config = {
            "timeout_middleware_found": len(timeout_configs) > 0,
            "max_upload_size": max_upload_size,
            "timeout_configs": timeout_configs,
        }
        
        self.results["performance_metrics"]["file_upload"] = upload_config
        
        status = "✅" if timeout_configs else "⚠️"
        print(f"{status} File upload timeouts: {'configured' if timeout_configs else 'not found'}")
    
    async def _validate_database_performance(self):
        """Validate database connection performance."""
        print("🗄️ Validating database performance...")
        
        try:
            # Test database connection performance
            start_time = time.time()
            
            # Use environment database URL for testing
            db_url = os.getenv("DATABASE_URL")
            if not db_url:
                raise ValueError("DATABASE_URL not configured")
            
            conn = await asyncpg.connect(db_url)
            
            # Test query performance
            query_start = time.time()
            result = await conn.fetchval("SELECT 1")
            query_end = time.time()
            
            await conn.close()
            end_time = time.time()
            
            connection_time = (query_start - start_time) * 1000
            query_time = (query_end - query_start) * 1000
            total_time = (end_time - start_time) * 1000
            
            db_performance = {
                "connection_time_ms": round(connection_time, 2),
                "query_time_ms": round(query_time, 2),
                "total_time_ms": round(total_time, 2),
                "target_connection_ms": 100,
                "target_query_ms": 50,
                "meets_target": connection_time < 100 and query_time < 50,
            }
            
            self.results["performance_metrics"]["database"] = db_performance
            
            status = "✅" if db_performance["meets_target"] else "❌"
            print(f"{status} Database: {total_time:.1f}ms total")
        
        except Exception as e:
            print(f"❌ Database performance test failed: {e}")
            self.results["performance_metrics"]["database"] = {"error": str(e)}
    
    def _generate_security_recommendations(self):
        """Generate security recommendations based on findings."""
        recommendations = []
        
        # Count issues by severity
        critical_count = len([i for i in self.results["security_issues"] if i.get("severity") == "CRITICAL"])
        high_count = len([i for i in self.results["security_issues"] if i.get("severity") == "HIGH"])
        medium_count = len([i for i in self.results["security_issues"] if i.get("severity") == "MEDIUM"])
        
        if critical_count > 0:
            recommendations.append({
                "priority": "IMMEDIATE",
                "action": f"Address {critical_count} critical security issues immediately",
                "timeline": "0-4 hours",
                "details": "Critical issues pose immediate security risk",
            })
        
        if high_count > 0:
            recommendations.append({
                "priority": "HIGH",
                "action": f"Resolve {high_count} high-severity security issues",
                "timeline": "4-24 hours",
                "details": "High-severity issues should be addressed within 24 hours",
            })
        
        # Performance recommendations
        auth_perf = self.results["performance_metrics"].get("authentication", {})
        if not auth_perf.get("meets_target", True):
            recommendations.append({
                "priority": "MEDIUM",
                "action": "Optimize authentication performance",
                "timeline": "1-3 days",
                "details": f"Current: {auth_perf.get('average_ms', 0)}ms, Target: <200ms",
            })
        
        # Secret Manager recommendations
        sm_perf = self.results["performance_metrics"].get("secret_manager", {})
        if "error" in sm_perf:
            recommendations.append({
                "priority": "HIGH",
                "action": "Fix Google Cloud Secret Manager access",
                "timeline": "4-8 hours",
                "details": "Secret Manager is required for production security",
            })
        
        self.results["recommendations"] = recommendations
    
    def _calculate_risk_score(self):
        """Calculate overall security risk score (0-100)."""
        score = 0
        
        # Add points for security issues
        for issue in self.results["security_issues"]:
            severity = issue.get("severity", "LOW")
            if severity == "CRITICAL":
                score += 25
            elif severity == "HIGH":
                score += 15
            elif severity == "MEDIUM":
                score += 5
            else:  # LOW
                score += 1
        
        # Add points for performance issues
        auth_perf = self.results["performance_metrics"].get("authentication", {})
        if not auth_perf.get("meets_target", True):
            score += 10
        
        sm_perf = self.results["performance_metrics"].get("secret_manager", {})
        if "error" in sm_perf:
            score += 20
        
        # Cap at 100
        self.results["risk_score"] = min(score, 100)
    
    def generate_report(self) -> str:
        """Generate comprehensive security assessment report."""
        report = f"""
🔒 SECURITY & PERFORMANCE ASSESSMENT REPORT
===========================================

Generated: {self.results['timestamp']}
Risk Score: {self.results['risk_score']}/100

SECURITY ISSUES SUMMARY
-----------------------
"""
        
        # Group issues by severity
        by_severity = {}
        for issue in self.results["security_issues"]:
            severity = issue.get("severity", "LOW")
            if severity not in by_severity:
                by_severity[severity] = []
            by_severity[severity].append(issue)
        
        for severity in ["CRITICAL", "HIGH", "MEDIUM", "LOW"]:
            issues = by_severity.get(severity, [])
            if issues:
                report += f"\n{severity}: {len(issues)} issues\n"
                for issue in issues[:5]:  # Show first 5
                    report += f"  - {issue.get('type', 'Unknown')}: {issue.get('description', 'No description')}\n"
                if len(issues) > 5:
                    report += f"  ... and {len(issues) - 5} more\n"
        
        # Performance metrics
        report += f"""

PERFORMANCE METRICS
-------------------
"""
        
        for component, metrics in self.results["performance_metrics"].items():
            if "error" in metrics:
                report += f"{component.title()}: ERROR - {metrics['error']}\n"
            else:
                report += f"{component.title()}: "
                if "average_ms" in metrics:
                    target = metrics.get("target_ms", 0)
                    avg = metrics.get("average_ms", 0)
                    status = "✅" if avg < target else "❌"
                    report += f"{status} {avg}ms (target: <{target}ms)\n"
                elif "retrieval_time_ms" in metrics:
                    time_ms = metrics.get("retrieval_time_ms", 0)
                    target = metrics.get("target_ms", 0)
                    status = "✅" if time_ms < target else "❌"
                    report += f"{status} {time_ms}ms\n"
                else:
                    report += f"Configured: {metrics}\n"
        
        # Recommendations
        if self.results["recommendations"]:
            report += f"""

RECOMMENDATIONS
---------------
"""
            for rec in self.results["recommendations"]:
                report += f"[{rec['priority']}] {rec['action']}\n"
                report += f"  Timeline: {rec['timeline']}\n"
                report += f"  Details: {rec['details']}\n\n"
        
        return report


async def main():
    """Run comprehensive security and performance assessment."""
    project_root = Path(__file__).parent.parent
    
    print("🚀 Starting giki.ai Security & Performance Assessment")
    print(f"📁 Project root: {project_root}")
    
    # Run assessment
    assessment = SecurityAssessment(str(project_root))
    results = await assessment.run_full_assessment()
    
    # Generate and save report
    report = assessment.generate_report()
    
    # Save results
    results_file = project_root / "security_assessment_results.json"
    report_file = project_root / "security_assessment_report.txt"
    
    async with aiofiles.open(results_file, 'w') as f:
        await f.write(json.dumps(results, indent=2))
    
    async with aiofiles.open(report_file, 'w') as f:
        await f.write(report)
    
    print(f"\n📊 Assessment complete!")
    print(f"📄 Report saved to: {report_file}")
    print(f"📋 Results saved to: {results_file}")
    print(f"🎯 Risk Score: {results['risk_score']}/100")
    
    # Print summary
    critical_issues = len([i for i in results["security_issues"] if i.get("severity") == "CRITICAL"])
    high_issues = len([i for i in results["security_issues"] if i.get("severity") == "HIGH"])
    
    if critical_issues > 0:
        print(f"🚨 CRITICAL: {critical_issues} critical security issues require immediate attention!")
    elif high_issues > 0:
        print(f"⚠️ HIGH: {high_issues} high-severity issues need resolution within 24 hours")
    else:
        print("✅ No critical or high-severity security issues found")
    
    # Print report summary
    print("\n" + report)


if __name__ == "__main__":
    asyncio.run(main())