#!/usr/bin/env node

/**
 * Design System Compliance Checker
 * 
 * Automatically scans codebase for design system violations and provides
 * actionable feedback to maintain consistent professional UI standards.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  // Prohibited icons (emojis that should be replaced with geometric icons)
  PROHIBITED_ICONS: [
    '🎯', '🚀', '💡', '📊', '✨', '🔄', '⚡', '✓', '❌', '⏰',
    '📈', '📉', '📋', '💼', '🏢', '💰', '📄', '🧑‍🦱', '👤', '🔍',
    '⭐', '🌟', '💫', '🔥', '💯', '🎉', '🎊', '👍', '👎'
  ],
  
  // Allowed geometric icons
  ALLOWED_ICONS: ['□', '⊞', '∷', '⚬', '↑', '↓', '←', '→', '⋯', '⋮'],
  
  // Brand colors that should use design tokens
  HARD_CODED_COLORS: [
    '#295343', '#1D372E', '#1A3F5F', '#15324D', '#2D4739',
    'rgb(41, 83, 67)', 'rgb(29, 55, 46)', 'hsl(151, 33%, 24%)'
  ],
  
  // Required design token prefixes
  DESIGN_TOKEN_CLASSES: [
    'bg-brand-', 'text-brand-', 'border-brand-',
    'bg-success', 'text-success', 'bg-error', 'text-error',
    'card-elevated', 'card-professional', 'btn-professional',
    'input-elevated', 'nav-item-professional', 'btn-touch-friendly'
  ],
  
  // Files to scan
  SCAN_PATTERNS: [
    'apps/giki-ai-app/src/**/*.tsx',
    'apps/giki-ai-app/src/**/*.ts',
    'apps/giki-ai-app/src/**/*.jsx',
    'apps/giki-ai-app/src/**/*.js'
  ],
  
  // Files to ignore
  IGNORE_PATTERNS: [
    'node_modules',
    '.git',
    'dist',
    'build',
    '*.test.*',
    '*.spec.*'
  ]
};

class DesignSystemChecker {
  constructor() {
    this.violations = [];
    this.stats = {
      filesScanned: 0,
      violationsFound: 0,
      iconViolations: 0,
      colorViolations: 0,
      classViolations: 0
    };
  }

  async run() {
    console.log('🔍 Design System Compliance Check Starting...\n');
    
    const files = this.getFilesToScan();
    
    for (const file of files) {
      await this.scanFile(file);
    }
    
    this.generateReport();
    return this.violations.length === 0;
  }

  getFilesToScan() {
    const files = [];
    
    try {
      // Use find command to get all TypeScript/React files
      const findCommand = `find apps/giki-ai-app/src -type f \\( -name "*.tsx" -o -name "*.ts" -o -name "*.jsx" -o -name "*.js" \\) | grep -v node_modules | grep -v ".test." | grep -v ".spec."`;
      const result = execSync(findCommand, { encoding: 'utf8' });
      return result.trim().split('\n').filter(f => f);
    } catch (error) {
      console.warn('Warning: Could not scan files automatically, using fallback');
      return [];
    }
  }

  async scanFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      this.stats.filesScanned++;
      
      // Check for prohibited icons
      this.checkProhibitedIcons(filePath, content);
      
      // Check for hard-coded colors
      this.checkHardCodedColors(filePath, content);
      
      // Check for missing design token usage
      this.checkDesignTokenUsage(filePath, content);
      
      // Check for accessibility violations
      this.checkAccessibilityViolations(filePath, content);
      
    } catch (error) {
      console.warn(`Warning: Could not read file ${filePath}:`, error.message);
    }
  }

  checkProhibitedIcons(filePath, content) {
    CONFIG.PROHIBITED_ICONS.forEach(icon => {
      const regex = new RegExp(icon.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      const matches = content.match(regex);
      
      if (matches) {
        this.addViolation({
          type: 'PROHIBITED_ICON',
          file: filePath,
          icon: icon,
          count: matches.length,
          message: `Prohibited emoji icon "${icon}" found ${matches.length} time(s). Replace with geometric icon.`,
          suggestion: this.getIconSuggestion(icon),
          severity: 'error'
        });
        this.stats.iconViolations += matches.length;
      }
    });
  }

  checkHardCodedColors(filePath, content) {
    CONFIG.HARD_CODED_COLORS.forEach(color => {
      const regex = new RegExp(color.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
      const matches = content.match(regex);
      
      if (matches) {
        this.addViolation({
          type: 'HARD_CODED_COLOR',
          file: filePath,
          color: color,
          count: matches.length,
          message: `Hard-coded color "${color}" found ${matches.length} time(s). Use design token instead.`,
          suggestion: 'Replace with: bg-brand-primary, text-brand-primary, or border-brand-primary',
          severity: 'error'
        });
        this.stats.colorViolations += matches.length;
      }
    });
    
    // Check for other suspicious hard-coded colors
    const colorPattern = /(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\([^)]+\)|hsl\([^)]+\))/g;
    const colorMatches = content.match(colorPattern);
    
    if (colorMatches) {
      colorMatches.forEach(color => {
        // Skip if it's already a known brand color (handled above)
        if (!CONFIG.HARD_CODED_COLORS.some(known => 
          color.toLowerCase().includes(known.toLowerCase())
        )) {
          this.addViolation({
            type: 'UNKNOWN_COLOR',
            file: filePath,
            color: color,
            message: `Potentially hard-coded color "${color}" found. Consider using design tokens.`,
            suggestion: 'Verify if this should use a design token',
            severity: 'warning'
          });
        }
      });
    }
  }

  checkDesignTokenUsage(filePath, content) {
    // Check for missing professional classes on common elements
    const buttonPattern = /<Button[^>]*className="([^"]*)"[^>]*>/g;
    let match;
    
    while ((match = buttonPattern.exec(content)) !== null) {
      const className = match[1];
      if (!className.includes('btn-professional') && !className.includes('btn-touch-friendly')) {
        this.addViolation({
          type: 'MISSING_DESIGN_TOKEN',
          file: filePath,
          element: 'Button',
          className: className,
          message: 'Button missing professional design classes',
          suggestion: 'Add "btn-professional" or "btn-touch-friendly" class',
          severity: 'warning'
        });
        this.stats.classViolations++;
      }
    }
    
    // Check for missing card classes
    const cardPattern = /<Card[^>]*className="([^"]*)"[^>]*>/g;
    while ((match = cardPattern.exec(content)) !== null) {
      const className = match[1];
      if (!className.includes('card-elevated') && !className.includes('card-professional')) {
        this.addViolation({
          type: 'MISSING_DESIGN_TOKEN',
          file: filePath,
          element: 'Card',
          className: className,
          message: 'Card missing professional design classes',
          suggestion: 'Add "card-elevated" or "card-professional" class',
          severity: 'warning'
        });
        this.stats.classViolations++;
      }
    }
  }

  checkAccessibilityViolations(filePath, content) {
    // Check for missing touch targets (44px minimum)
    const buttonPattern = /<button[^>]*>/gi;
    const matches = content.match(buttonPattern);
    
    if (matches) {
      matches.forEach(button => {
        if (!button.includes('btn-touch-friendly') && 
            !button.includes('min-h-[44px]') && 
            !button.includes('h-11') &&
            !button.includes('py-3')) {
          this.addViolation({
            type: 'ACCESSIBILITY_VIOLATION',
            file: filePath,
            element: button,
            message: 'Button may not meet 44px touch target requirement',
            suggestion: 'Add "btn-touch-friendly" class or ensure 44px minimum height',
            severity: 'warning'
          });
        }
      });
    }
    
    // Check for missing alt text on images
    const imgPattern = /<img[^>]*>/gi;
    const imgMatches = content.match(imgPattern);
    
    if (imgMatches) {
      imgMatches.forEach(img => {
        if (!img.includes('alt=')) {
          this.addViolation({
            type: 'ACCESSIBILITY_VIOLATION',
            file: filePath,
            element: img,
            message: 'Image missing alt attribute',
            suggestion: 'Add alt="" for decorative images or descriptive alt text',
            severity: 'error'
          });
        }
      });
    }
  }

  getIconSuggestion(prohibitedIcon) {
    const suggestions = {
      '🎯': '⚬',
      '🚀': '↑',
      '💡': '∷',
      '📊': '⊞',
      '✨': '⋯',
      '🔄': '↑',
      '⚡': '↑',
      '✓': '□',
      '❌': '□',
      '⏰': '⚬',
      '📈': '↑',
      '📉': '↓',
      '📋': '∷',
      '💼': '□',
      '🏢': '⊞',
      '💰': '⊞',
      '📄': '∷'
    };
    
    return suggestions[prohibitedIcon] || '□';
  }

  addViolation(violation) {
    this.violations.push(violation);
    this.stats.violationsFound++;
  }

  generateReport() {
    console.log('📋 Design System Compliance Report\n');
    console.log('═'.repeat(60));
    
    // Summary
    console.log(`Files Scanned: ${this.stats.filesScanned}`);
    console.log(`Total Violations: ${this.stats.violationsFound}`);
    console.log(`  - Icon Violations: ${this.stats.iconViolations}`);
    console.log(`  - Color Violations: ${this.stats.colorViolations}`);
    console.log(`  - Class Violations: ${this.stats.classViolations}`);
    console.log('═'.repeat(60));
    
    if (this.violations.length === 0) {
      console.log('✅ No design system violations found! Great job!');
      return;
    }
    
    // Group violations by type
    const grouped = this.violations.reduce((acc, violation) => {
      if (!acc[violation.type]) acc[violation.type] = [];
      acc[violation.type].push(violation);
      return acc;
    }, {});
    
    // Report each group
    Object.entries(grouped).forEach(([type, violations]) => {
      console.log(`\n🚨 ${type} (${violations.length} violations)`);
      console.log('─'.repeat(40));
      
      violations.slice(0, 10).forEach(v => { // Limit to first 10 per type
        console.log(`File: ${v.file}`);
        console.log(`Issue: ${v.message}`);
        console.log(`Fix: ${v.suggestion}`);
        console.log(`Severity: ${v.severity}`);
        console.log('');
      });
      
      if (violations.length > 10) {
        console.log(`... and ${violations.length - 10} more violations of this type`);
      }
    });
    
    // Quick fix suggestions
    console.log('\n🔧 Quick Fix Commands:');
    console.log('─'.repeat(40));
    
    if (this.stats.iconViolations > 0) {
      console.log('# Replace prohibited icons:');
      console.log('find apps/giki-ai-app/src -name "*.tsx" -exec sed -i "" "s/🎯/⚬/g" {} \\;');
      console.log('find apps/giki-ai-app/src -name "*.tsx" -exec sed -i "" "s/✓/□/g" {} \\;');
    }
    
    if (this.stats.colorViolations > 0) {
      console.log('\n# Replace hard-coded colors:');
      console.log('find apps/giki-ai-app/src -name "*.tsx" -exec sed -i "" "s/#295343/var(--giki-primary)/g" {} \\;');
    }
    
    console.log('\n💡 Design System Resources:');
    console.log('─'.repeat(40));
    console.log('- Design Tokens: apps/giki-ai-app/src/styles/design-tokens.css');
    console.log('- Utility Classes: apps/giki-ai-app/src/styles/design-token-utilities.css');
    console.log('- Brand Guidelines: docs/05-DESIGN-SYSTEM.md');
    
    // Exit with error code if violations found
    if (this.stats.violationsFound > 0) {
      console.log('\n❌ Design system violations found. Please fix before proceeding.');
      process.exit(1);
    }
  }
}

// Run the checker
if (require.main === module) {
  const checker = new DesignSystemChecker();
  checker.run().catch(error => {
    console.error('Error running design system check:', error);
    process.exit(1);
  });
}

module.exports = DesignSystemChecker;