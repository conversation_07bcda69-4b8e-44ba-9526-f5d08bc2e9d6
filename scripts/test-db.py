#!/usr/bin/env python3
"""Database connectivity test using asyncpg"""

import asyncio
import asyncpg

async def test_database():
    """Test database connection using asyncpg"""
    try:
        conn = await asyncpg.connect('postgresql://nikhilsingh@localhost:5432/giki_ai_dev')
        print('✅ Database connected successfully with asyncpg')
        
        # Test a simple query
        result = await conn.fetchval('SELECT version()')
        print(f'✅ PostgreSQL version: {result}')
        
        await conn.close()
        return True
    except Exception as e:
        print(f'❌ Database connection failed: {e}')
        return False

if __name__ == "__main__":
    success = asyncio.run(test_database())
    exit(0 if success else 1)