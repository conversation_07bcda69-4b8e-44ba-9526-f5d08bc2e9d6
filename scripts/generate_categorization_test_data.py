#!/usr/bin/env python3
"""
Generate Categorization Test Data
=================================

This script creates comprehensive test data for categorization accuracy validation.
It generates test cases with known correct categories for different types of transactions.
"""

import pandas as pd
from pathlib import Path
import os


def create_categorization_test_data():
    """Create comprehensive test data with known correct categories."""
    
    # Define comprehensive test cases
    test_cases = [
        # === VENDOR-BASED TESTS (High Confidence Expected) ===
        {
            "description": "NEXT Insurance Premium Payment",
            "amount": -1406.11,
            "expected_category": "Insurance Expense",
            "expected_parent": "Expenses", 
            "vendor": "NEXT Insurance",
            "confidence_threshold": 0.8,
            "test_type": "vendor",
            "notes": "Insurance vendor should be easily identified"
        },
        {
            "description": "Auto Insurance - State Farm",
            "amount": -450.00,
            "expected_category": "Insurance Expense",
            "expected_parent": "Expenses",
            "vendor": "State Farm", 
            "confidence_threshold": 0.8,
            "test_type": "vendor",
            "notes": "Well-known insurance provider"
        },
        {
            "description": "Microsoft Office 365 Subscription",
            "amount": -19.99,
            "expected_category": "Software & Technology",
            "expected_parent": "Expenses",
            "vendor": "Microsoft",
            "confidence_threshold": 0.85,
            "test_type": "vendor",
            "notes": "Common software subscription"
        },
        {
            "description": "AWS Cloud Services",
            "amount": -156.78,
            "expected_category": "Software & Technology",
            "expected_parent": "Expenses",
            "vendor": "Amazon Web Services",
            "confidence_threshold": 0.85,
            "test_type": "vendor",
            "notes": "Cloud hosting service"
        },
        {
            "description": "Google Workspace Business",
            "amount": -12.00,
            "expected_category": "Software & Technology",
            "expected_parent": "Expenses",
            "vendor": "Google",
            "confidence_threshold": 0.85,
            "test_type": "vendor",
            "notes": "Business productivity suite"
        },
        {
            "description": "Uber ride to airport",
            "amount": -35.50,
            "expected_category": "Travel & Transportation",
            "expected_parent": "Expenses",
            "vendor": "Uber",
            "confidence_threshold": 0.8,
            "test_type": "vendor",
            "notes": "Transportation service"
        },
        {
            "description": "Lyft ride to client meeting",
            "amount": -25.50,
            "expected_category": "Travel & Transportation",
            "expected_parent": "Expenses",
            "vendor": "Lyft",
            "confidence_threshold": 0.8,
            "test_type": "vendor",
            "notes": "Transportation service"
        },
        {
            "description": "Office supplies from Staples",
            "amount": -67.43,
            "expected_category": "Office Supplies",
            "expected_parent": "Expenses",
            "vendor": "Staples",
            "confidence_threshold": 0.8,
            "test_type": "vendor",
            "notes": "Office supply store"
        },
        {
            "description": "Starbucks coffee meeting",
            "amount": -12.45,
            "expected_category": "Meals & Entertainment",
            "expected_parent": "Expenses",
            "vendor": "Starbucks",
            "confidence_threshold": 0.75,
            "test_type": "vendor",
            "notes": "Coffee shop - business meeting"
        },
        {
            "description": "Stripe payment from customer",
            "amount": 299.99,
            "expected_category": "Sales Revenue",
            "expected_parent": "Income",
            "vendor": "Stripe",
            "confidence_threshold": 0.85,
            "test_type": "vendor",
            "notes": "Payment processor - customer payment"
        },
        {
            "description": "PayPal payment received",
            "amount": 1250.00,
            "expected_category": "Sales Revenue",
            "expected_parent": "Income",
            "vendor": "PayPal",
            "confidence_threshold": 0.85,
            "test_type": "vendor",
            "notes": "Payment processor - customer payment"
        },
        
        # === PATTERN-BASED TESTS (Medium-High Confidence Expected) ===
        {
            "description": "ACH Credit Consulting Payment",
            "amount": 45980.68,
            "expected_category": "Consulting Income",
            "expected_parent": "Income",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "pattern",
            "notes": "Consulting payment pattern"
        },
        {
            "description": "Client payment for web development",
            "amount": 5000.00,
            "expected_category": "Service Revenue",
            "expected_parent": "Income",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "pattern",
            "notes": "Professional services payment"
        },
        {
            "description": "Hotel booking for business trip",
            "amount": -289.00,
            "expected_category": "Travel & Transportation",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "pattern",
            "notes": "Business travel expense"
        },
        {
            "description": "Conference registration fee",
            "amount": -399.00,
            "expected_category": "Professional Services",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.65,
            "test_type": "pattern",
            "notes": "Professional development expense"
        },
        {
            "description": "Client lunch at restaurant",
            "amount": -85.60,
            "expected_category": "Meals & Entertainment",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.65,
            "test_type": "pattern",
            "notes": "Business meal expense"
        },
        {
            "description": "Business dinner with partners",
            "amount": -125.80,
            "expected_category": "Meals & Entertainment",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.65,
            "test_type": "pattern",
            "notes": "Business meal expense"
        },
        {
            "description": "Legal consultation fees",
            "amount": -750.00,
            "expected_category": "Professional Services",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "pattern",
            "notes": "Legal services expense"
        },
        {
            "description": "Accounting services monthly",
            "amount": -500.00,
            "expected_category": "Professional Services",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "pattern",
            "notes": "Accounting services expense"
        },
        {
            "description": "Office rent monthly payment",
            "amount": -2500.00,
            "expected_category": "Rent & Facilities",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.75,
            "test_type": "pattern",
            "notes": "Office space rental"
        },
        {
            "description": "Electricity bill - office",
            "amount": -156.78,
            "expected_category": "Utilities",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "pattern",
            "notes": "Utility expense"
        },
        {
            "description": "Internet service monthly",
            "amount": -89.99,
            "expected_category": "Utilities",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "pattern",
            "notes": "Internet utility expense"
        },
        {
            "description": "Phone service business line",
            "amount": -65.00,
            "expected_category": "Utilities",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "pattern",
            "notes": "Phone utility expense"
        },
        
        # === EDGE CASE TESTS (Variable Confidence Expected) ===
        {
            "description": "Bank service charge",
            "amount": -25.00,
            "expected_category": "Banking & Finance",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.6,
            "test_type": "edge_case",
            "notes": "Bank fee - should be categorized correctly"
        },
        {
            "description": "Wire transfer fee",
            "amount": -15.00,
            "expected_category": "Banking & Finance",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.6,
            "test_type": "edge_case",
            "notes": "Banking fee"
        },
        {
            "description": "Refund from cancelled order",
            "amount": 150.00,
            "expected_category": "Refunds & Credits",
            "expected_parent": "Income",
            "vendor": None,
            "confidence_threshold": 0.5,
            "test_type": "edge_case",
            "notes": "Refund - can be tricky to categorize"
        },
        {
            "description": "Tax refund from IRS",
            "amount": 2500.00,
            "expected_category": "Refunds & Credits",
            "expected_parent": "Income",
            "vendor": None,
            "confidence_threshold": 0.6,
            "test_type": "edge_case",
            "notes": "Tax refund"
        },
        {
            "description": "Interest earned on savings",
            "amount": 23.45,
            "expected_category": "Interest Income",
            "expected_parent": "Income",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "edge_case",
            "notes": "Interest income"
        },
        {
            "description": "Dividend payment received",
            "amount": 456.78,
            "expected_category": "Interest Income",
            "expected_parent": "Income",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "edge_case",
            "notes": "Investment income"
        },
        
        # === CHALLENGING CASES (Lower Confidence Expected) ===
        {
            "description": "Payment to John Smith",
            "amount": -1000.00,
            "expected_category": "Professional Services",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.3,
            "test_type": "challenging",
            "notes": "Ambiguous payment to individual - assume contractor"
        },
        {
            "description": "Cash withdrawal ATM",
            "amount": -200.00,
            "expected_category": "Banking & Finance",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.4,
            "test_type": "challenging",
            "notes": "ATM withdrawal - technically not an expense but fee is"
        },
        {
            "description": "Miscellaneous expense",
            "amount": -50.00,
            "expected_category": "Miscellaneous Expenses",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.2,
            "test_type": "challenging",
            "notes": "Intentionally vague description"
        },
        {
            "description": "Transfer to business account",
            "amount": -5000.00,
            "expected_category": "Banking & Finance",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.3,
            "test_type": "challenging",
            "notes": "Internal transfer - not really an expense"
        },
        
        # === AMOUNT-BASED TESTS (Test Amount Heuristics) ===
        {
            "description": "Large equipment purchase",
            "amount": -15000.00,
            "expected_category": "Equipment & Assets",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.4,
            "test_type": "amount_based",
            "notes": "Large amount should suggest equipment"
        },
        {
            "description": "Major contract payment",
            "amount": 75000.00,
            "expected_category": "Sales Revenue",
            "expected_parent": "Income",
            "vendor": None,
            "confidence_threshold": 0.4,
            "test_type": "amount_based",
            "notes": "Large income amount"
        },
        {
            "description": "Small office expense",
            "amount": -12.50,
            "expected_category": "Office Supplies",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.3,
            "test_type": "amount_based",
            "notes": "Small amount - office supplies guess"
        },
        
        # === RECURRING PATTERN TESTS ===
        {
            "description": "Monthly subscription Adobe Creative",
            "amount": -52.99,
            "expected_category": "Software & Technology",
            "expected_parent": "Expenses",
            "vendor": "Adobe",
            "confidence_threshold": 0.8,
            "test_type": "recurring",
            "notes": "Monthly subscription pattern"
        },
        {
            "description": "Weekly gas station fill-up",
            "amount": -45.00,
            "expected_category": "Travel & Transportation",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.6,
            "test_type": "recurring",
            "notes": "Recurring fuel expense"
        },
        {
            "description": "Quarterly insurance premium",
            "amount": -1200.00,
            "expected_category": "Insurance Expense",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.7,
            "test_type": "recurring",
            "notes": "Quarterly insurance payment"
        },
        
        # === INDUSTRY-SPECIFIC TESTS ===
        {
            "description": "Software license renewal",
            "amount": -2400.00,
            "expected_category": "Software & Technology",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.75,
            "test_type": "industry_specific",
            "notes": "Software business expense"
        },
        {
            "description": "Marketing campaign Facebook Ads",
            "amount": -850.00,
            "expected_category": "Marketing & Advertising",
            "expected_parent": "Expenses",
            "vendor": "Facebook",
            "confidence_threshold": 0.8,
            "test_type": "industry_specific",
            "notes": "Digital marketing expense"
        },
        {
            "description": "Google Ads campaign",
            "amount": -1200.00,
            "expected_category": "Marketing & Advertising",
            "expected_parent": "Expenses",
            "vendor": "Google",
            "confidence_threshold": 0.8,
            "test_type": "industry_specific",
            "notes": "Digital marketing expense"
        },
        {
            "description": "Trade show booth rental",
            "amount": -3500.00,
            "expected_category": "Marketing & Advertising",
            "expected_parent": "Expenses",
            "vendor": None,
            "confidence_threshold": 0.65,
            "test_type": "industry_specific",
            "notes": "Marketing event expense"
        },
    ]
    
    # Create DataFrame
    df = pd.DataFrame(test_cases)
    
    # Create directory if it doesn't exist
    output_dir = Path("/Users/<USER>/giki-ai-workspace/libs/test-data/categorization-validation")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save as Excel file
    output_file = output_dir / "categorization_test_cases.xlsx"
    df.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"✅ Created categorization test data: {output_file}")
    print(f"📊 Total test cases: {len(test_cases)}")
    print(f"🔍 Test types: {df['test_type'].value_counts().to_dict()}")
    
    # Create summary statistics
    summary = {
        "total_cases": len(test_cases),
        "test_types": df['test_type'].value_counts().to_dict(),
        "confidence_distribution": {
            "high_confidence (>0.7)": len(df[df['confidence_threshold'] > 0.7]),
            "medium_confidence (0.5-0.7)": len(df[(df['confidence_threshold'] >= 0.5) & (df['confidence_threshold'] <= 0.7)]),
            "low_confidence (<0.5)": len(df[df['confidence_threshold'] < 0.5])
        },
        "amount_distribution": {
            "expenses": len(df[df['amount'] < 0]),
            "income": len(df[df['amount'] > 0])
        },
        "vendor_tests": len(df[df['vendor'].notna()]),
        "pattern_tests": len(df[df['vendor'].isna()])
    }
    
    # Save summary
    summary_file = output_dir / "test_data_summary.json"
    import json
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"📋 Summary saved: {summary_file}")
    
    return output_file, summary


if __name__ == "__main__":
    output_file, summary = create_categorization_test_data()
    
    print("\n" + "="*60)
    print("CATEGORIZATION TEST DATA CREATED")
    print("="*60)
    print(f"File: {output_file}")
    print(f"Test Cases: {summary['total_cases']}")
    print(f"Test Types: {summary['test_types']}")
    print(f"Confidence Distribution: {summary['confidence_distribution']}")
    print(f"Vendor Tests: {summary['vendor_tests']}")
    print(f"Pattern Tests: {summary['pattern_tests']}")
    print("="*60)