#!/bin/bash

# Core Workflow Testing Framework
# ==============================
# Systematic testing of upload, schema interpretation, categorization, and review workflows
# Usage: ./scripts/test-core-workflows.sh [workflow_name]
# Workflows: auth, upload, schema, categorization, review, all

set -euo pipefail

# Configuration
API_BASE="http://localhost:8000"
TEST_USER_EMAIL="<EMAIL>"
TEST_USER_PASSWORD="GikiTest2025Secure"
RESULTS_DIR="/Users/<USER>/giki-ai-workspace/testing/results"
TEST_DATA_DIR="/Users/<USER>/giki-ai-workspace/libs/test-data"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Global variables
ACCESS_TOKEN=""
TENANT_ID=""
UPLOAD_ID=""
TEST_RESULTS=()

# Utility functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

add_test_result() {
    local test_name="$1"
    local status="$2"
    local details="$3"
    TEST_RESULTS+=("$test_name|$status|$details")
}

# API Helper functions
make_api_call() {
    local method="$1"
    local endpoint="$2"
    local data="${3:-}"
    local auth_header=""
    
    if [[ -n "$ACCESS_TOKEN" ]]; then
        auth_header="-H \"Authorization: Bearer $ACCESS_TOKEN\""
    fi
    
    local response
    if [[ "$method" == "POST" && -n "$data" ]]; then
        response=$(eval curl -s -X "$method" "$API_BASE$endpoint" \
            -H "Content-Type: application/json" \
            $auth_header \
            -d "'$data'" || echo "CURL_ERROR")
    else
        response=$(eval curl -s -X "$method" "$API_BASE$endpoint" \
            $auth_header || echo "CURL_ERROR")
    fi
    
    echo "$response"
}

# Authentication Testing
test_authentication() {
    log_info "Testing authentication workflow..."
    
    # Test 1: Get access token
    log_info "1. Getting access token..."
    local auth_response
    auth_response=$(curl -s -X POST "$API_BASE/api/v1/auth/token" \
        -d "username=$TEST_USER_EMAIL&password=$TEST_USER_PASSWORD" || echo "CURL_ERROR")
    
    if [[ "$auth_response" == "CURL_ERROR" ]]; then
        add_test_result "auth_token" "FAIL" "Curl command failed"
        log_error "Failed to get access token - curl error"
        return 1
    fi
    
    ACCESS_TOKEN=$(echo "$auth_response" | jq -r '.access_token // empty')
    if [[ -z "$ACCESS_TOKEN" || "$ACCESS_TOKEN" == "null" ]]; then
        add_test_result "auth_token" "FAIL" "No access token in response: $auth_response"
        log_error "Failed to get access token from response"
        return 1
    fi
    
    add_test_result "auth_token" "PASS" "Token obtained successfully"
    log_success "Access token obtained: ${ACCESS_TOKEN:0:20}..."
    
    # Test 2: Verify token with /auth/me
    log_info "2. Verifying token with /auth/me..."
    local me_response
    me_response=$(make_api_call "GET" "/api/v1/auth/me")
    
    if [[ "$me_response" == "CURL_ERROR" ]]; then
        add_test_result "auth_verify" "FAIL" "Curl command failed"
        log_error "Failed to verify token - curl error"
        return 1
    fi
    
    TENANT_ID=$(echo "$me_response" | jq -r '.tenant_id // empty')
    local user_email
    user_email=$(echo "$me_response" | jq -r '.email // empty')
    
    if [[ -z "$TENANT_ID" || "$TENANT_ID" == "null" ]]; then
        add_test_result "auth_verify" "FAIL" "No tenant_id in response: $me_response"
        log_error "Failed to get tenant_id from /auth/me"
        return 1
    fi
    
    if [[ "$user_email" != "$TEST_USER_EMAIL" ]]; then
        add_test_result "auth_verify" "FAIL" "Email mismatch: expected $TEST_USER_EMAIL, got $user_email"
        log_error "Email mismatch in /auth/me response"
        return 1
    fi
    
    add_test_result "auth_verify" "PASS" "Token verified, tenant_id: $TENANT_ID"
    log_success "Authentication verified - tenant_id: $TENANT_ID, email: $user_email"
    
    return 0
}

# Upload Workflow Testing
test_upload_workflow() {
    log_info "Testing upload workflow..."
    
    if [[ -z "$ACCESS_TOKEN" ]]; then
        log_error "No access token - run authentication first"
        return 1
    fi
    
    # Test 1: Upload test file
    log_info "1. Uploading test file..."
    local test_file="$TEST_DATA_DIR/synthetic/indian-banks/hdfc_bank_statement_1000tx.xlsx"
    
    if [[ ! -f "$test_file" ]]; then
        add_test_result "upload_file" "FAIL" "Test file not found: $test_file"
        log_error "Test file not found: $test_file"
        return 1
    fi
    
    local upload_response
    upload_response=$(curl -s -X POST "$API_BASE/api/v1/files/upload" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -F "files=@$test_file" || echo "CURL_ERROR")
    
    if [[ "$upload_response" == "CURL_ERROR" ]]; then
        add_test_result "upload_file" "FAIL" "Curl command failed"
        log_error "Failed to upload file - curl error"
        return 1
    fi
    
    # Check for errors in response
    local error_message
    error_message=$(echo "$upload_response" | jq -r '.detail // empty')
    if [[ -n "$error_message" && "$error_message" != "null" ]]; then
        add_test_result "upload_file" "FAIL" "API error: $error_message"
        log_error "Upload failed with API error: $error_message"
        return 1
    fi
    
    # Extract upload ID
    UPLOAD_ID=$(echo "$upload_response" | jq -r '.upload_id // .uploads[0].upload_id // empty')
    if [[ -z "$UPLOAD_ID" || "$UPLOAD_ID" == "null" ]]; then
        add_test_result "upload_file" "FAIL" "No upload_id in response: $upload_response"
        log_error "Failed to get upload_id from response"
        return 1
    fi
    
    add_test_result "upload_file" "PASS" "Upload successful, ID: $UPLOAD_ID"
    log_success "File uploaded successfully - upload_id: $UPLOAD_ID"
    
    return 0
}

# Schema Interpretation Testing
test_schema_interpretation() {
    log_info "Testing schema interpretation workflow..."
    
    if [[ -z "$UPLOAD_ID" ]]; then
        log_error "No upload ID - run upload workflow first"
        return 1
    fi
    
    # Test 1: Get schema interpretation
    log_info "1. Getting schema interpretation for upload $UPLOAD_ID..."
    local interpretation_response
    interpretation_response=$(make_api_call "GET" "/api/v1/files/$UPLOAD_ID/interpretation")
    
    if [[ "$interpretation_response" == "CURL_ERROR" ]]; then
        add_test_result "schema_interpretation" "FAIL" "Curl command failed"
        log_error "Failed to get schema interpretation - curl error"
        return 1
    fi
    
    # Check for errors
    local error_message
    error_message=$(echo "$interpretation_response" | jq -r '.detail // empty')
    if [[ -n "$error_message" && "$error_message" != "null" ]]; then
        add_test_result "schema_interpretation" "FAIL" "API error: $error_message"
        log_error "Schema interpretation failed: $error_message"
        return 1
    fi
    
    # Validate interpretation structure
    local headers
    headers=$(echo "$interpretation_response" | jq -r '.headers // empty')
    local column_mappings
    column_mappings=$(echo "$interpretation_response" | jq -r '.column_mappings // empty')
    
    if [[ -z "$headers" || "$headers" == "null" ]]; then
        add_test_result "schema_interpretation" "FAIL" "No headers in interpretation response"
        log_error "No headers found in schema interpretation"
        return 1
    fi
    
    if [[ -z "$column_mappings" || "$column_mappings" == "null" ]]; then
        add_test_result "schema_interpretation" "FAIL" "No column_mappings in interpretation response"
        log_error "No column mappings found in schema interpretation"
        return 1
    fi
    
    add_test_result "schema_interpretation" "PASS" "Schema interpreted successfully"
    log_success "Schema interpretation successful - headers and mappings found"
    
    return 0
}

# Categorization Testing
test_categorization_workflow() {
    log_info "Testing categorization workflow..."
    
    if [[ -z "$UPLOAD_ID" ]]; then
        log_error "No upload ID - run upload workflow first"
        return 1
    fi
    
    # Test 1: Get transactions for categorization testing
    log_info "1. Getting transactions to test categorization..."
    local transactions_response
    transactions_response=$(make_api_call "GET" "/api/v1/transactions/fast?limit=5")
    
    if [[ "$transactions_response" == "CURL_ERROR" ]]; then
        add_test_result "categorization_get_transactions" "FAIL" "Curl command failed"
        log_error "Failed to get transactions - curl error"
        return 1
    fi
    
    # Check if we have transactions
    local transaction_count
    transaction_count=$(echo "$transactions_response" | jq '.data | length // 0')
    
    if [[ "$transaction_count" -eq 0 ]]; then
        add_test_result "categorization_get_transactions" "FAIL" "No transactions found"
        log_error "No transactions found for categorization testing"
        return 1
    fi
    
    add_test_result "categorization_get_transactions" "PASS" "Found $transaction_count transactions"
    log_success "Found $transaction_count transactions for categorization testing"
    
    # Test 2: Check categorization accuracy
    log_info "2. Checking categorization accuracy..."
    local categorized_count
    categorized_count=$(echo "$transactions_response" | jq '[.data[] | select(.ai_suggested_category != null and .ai_suggested_category != "")] | length')
    
    local accuracy_percentage=0
    if [[ "$transaction_count" -gt 0 ]]; then
        accuracy_percentage=$((categorized_count * 100 / transaction_count))
    fi
    
    if [[ "$accuracy_percentage" -lt 70 ]]; then
        add_test_result "categorization_accuracy" "FAIL" "Accuracy too low: $accuracy_percentage% ($categorized_count/$transaction_count)"
        log_error "Categorization accuracy too low: $accuracy_percentage% ($categorized_count/$transaction_count categorized)"
        return 1
    fi
    
    add_test_result "categorization_accuracy" "PASS" "Accuracy: $accuracy_percentage% ($categorized_count/$transaction_count)"
    log_success "Categorization accuracy: $accuracy_percentage% ($categorized_count/$transaction_count categorized)"
    
    return 0
}

# Review and Improvement Testing
test_review_improvement_workflow() {
    log_info "Testing review and improvement workflow..."
    
    # Test 1: Get review queue
    log_info "1. Getting review queue..."
    local review_response
    review_response=$(make_api_call "GET" "/api/v1/transactions/review-queue?confidence_threshold=0.85&limit=10")
    
    if [[ "$review_response" == "CURL_ERROR" ]]; then
        add_test_result "review_queue" "FAIL" "Curl command failed"
        log_error "Failed to get review queue - curl error"
        return 1
    fi
    
    local review_count
    review_count=$(echo "$review_response" | jq '.data | length // 0')
    
    add_test_result "review_queue" "PASS" "Review queue has $review_count items"
    log_success "Review queue retrieved - $review_count items for review"
    
    # Test 2: Get categorization metrics
    log_info "2. Getting categorization metrics..."
    local metrics_response
    metrics_response=$(make_api_call "GET" "/api/v1/categories/metrics/categorization")
    
    if [[ "$metrics_response" == "CURL_ERROR" ]]; then
        add_test_result "categorization_metrics" "FAIL" "Curl command failed"
        log_error "Failed to get categorization metrics - curl error"
        return 1
    fi
    
    local total_transactions
    total_transactions=$(echo "$metrics_response" | jq '.total_transactions // 0')
    local categorized_transactions
    categorized_transactions=$(echo "$metrics_response" | jq '.categorized_transactions // 0')
    
    add_test_result "categorization_metrics" "PASS" "Total: $total_transactions, Categorized: $categorized_transactions"
    log_success "Categorization metrics - Total: $total_transactions, Categorized: $categorized_transactions"
    
    return 0
}

# Generate test report
generate_test_report() {
    local workflow_name="$1"
    local report_file="$RESULTS_DIR/workflow_test_${workflow_name}_$(date +%Y%m%d_%H%M%S).json"
    
    mkdir -p "$RESULTS_DIR"
    
    local total_tests=${#TEST_RESULTS[@]}
    local passed_tests=0
    local failed_tests=0
    
    echo "{" > "$report_file"
    echo "  \"test_run\": {" >> "$report_file"
    echo "    \"workflow\": \"$workflow_name\"," >> "$report_file"
    echo "    \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"," >> "$report_file"
    echo "    \"total_tests\": $total_tests" >> "$report_file"
    echo "  }," >> "$report_file"
    echo "  \"results\": [" >> "$report_file"
    
    local first=true
    for result in "${TEST_RESULTS[@]}"; do
        IFS='|' read -r test_name status details <<< "$result"
        
        if [[ "$status" == "PASS" ]]; then
            ((passed_tests++))
        else
            ((failed_tests++))
        fi
        
        if [[ "$first" == true ]]; then
            first=false
        else
            echo "    ," >> "$report_file"
        fi
        
        echo "    {" >> "$report_file"
        echo "      \"test_name\": \"$test_name\"," >> "$report_file"
        echo "      \"status\": \"$status\"," >> "$report_file"
        echo "      \"details\": \"$details\"" >> "$report_file"
        echo -n "    }" >> "$report_file"
    done
    
    echo "" >> "$report_file"
    echo "  ]," >> "$report_file"
    echo "  \"summary\": {" >> "$report_file"
    echo "    \"passed\": $passed_tests," >> "$report_file"
    echo "    \"failed\": $failed_tests," >> "$report_file"
    echo "    \"success_rate\": \"$(( passed_tests * 100 / total_tests ))%\"" >> "$report_file"
    echo "  }" >> "$report_file"
    echo "}" >> "$report_file"
    
    log_info "Test report saved to: $report_file"
    log_info "Summary: $passed_tests passed, $failed_tests failed ($(( passed_tests * 100 / total_tests ))% success rate)"
}

# Main execution
main() {
    local workflow="${1:-all}"
    
    log_info "Starting core workflow testing - workflow: $workflow"
    log_info "API Base: $API_BASE"
    log_info "Test User: $TEST_USER_EMAIL"
    log_info "Results Dir: $RESULTS_DIR"
    
    case "$workflow" in
        "auth")
            test_authentication
            ;;
        "upload")
            test_authentication && test_upload_workflow
            ;;
        "schema")
            test_authentication && test_upload_workflow && test_schema_interpretation
            ;;
        "categorization")
            test_authentication && test_categorization_workflow
            ;;
        "review")
            test_authentication && test_review_improvement_workflow
            ;;
        "all")
            test_authentication && \
            test_upload_workflow && \
            test_schema_interpretation && \
            test_categorization_workflow && \
            test_review_improvement_workflow
            ;;
        *)
            log_error "Unknown workflow: $workflow"
            log_info "Available workflows: auth, upload, schema, categorization, review, all"
            exit 1
            ;;
    esac
    
    generate_test_report "$workflow"
    
    log_info "Core workflow testing completed"
}

# Check dependencies
if ! command -v jq &> /dev/null; then
    log_error "jq is required but not installed. Please install jq first."
    exit 1
fi

if ! command -v curl &> /dev/null; then
    log_error "curl is required but not installed. Please install curl first."
    exit 1
fi

# Run main function
main "$@"