# Scripts Directory - NX-Essential Only (Anti-Fragmentation Enforced)

## CORE ANTI-FRAGMENTATION PRINCIPLE APPLIED

This directory contains ONLY scripts directly referenced by NX targets. All ad-hoc scripts removed following the "modify existing files instead of creating multiple versions" principle.

**Script Creation Prohibited For:**
- ❌ Database fixes/changes → Use Alembic migrations
- ❌ Management utilities → Use CLI commands in `apps/giki-ai-api/src/giki_ai_api/cli/`
- ❌ Test scripts → Use proper test directories
- ❌ Quick fixes → Fix in application source code
- ❌ Alternative/simplified/debug versions → Enhance existing files

## Current Directory Structure (Post-Anti-Fragmentation Cleanup)

```
scripts/
├── README.md                    # This documentation
├── start-servers.sh            # NX workspace serve target
└── nx/                         # Scripts required by NX project targets
    ├── build-and-deploy.sh     # API deploy target
    ├── check-development-logs.sh # API/App check-logs:development
    ├── check-production-logs.sh  # API/App check-logs:production
    ├── deploy-all.sh           # App deploy target
    ├── dev-local.sh            # API dev target
    ├── ensure-postgres.sh      # API db target
    ├── log-with-limit.sh       # API/App test targets (log management)
    ├── monitor-deployment.sh   # API deploy:monitor target
    ├── serve-api.sh            # API serve target
    └── serve-frontend.sh       # App serve target
```

## Anti-Fragmentation Cleanup Results (2025-06-30)

**Removed 57 fragmented scripts following consolidation principle:**
- ✅ auth/* (7 scripts) → One-time utilities, not ongoing workflow
- ✅ infrastructure/* (8 scripts) → Not referenced by NX targets
- ✅ migrations/* (2 scripts) → Use proper Alembic migrations
- ✅ monitoring/* (1 script) → Not NX workflow
- ✅ security/* (2 scripts) → Not NX workflow  
- ✅ validation/* (2 scripts) → Not NX workflow
- ✅ 35+ root-level utilities → Not referenced by NX targets

**From 68 scattered scripts → 11 NX-essential scripts**

## Development Workflow (No Scripts Required)

```bash
# Database changes (Proper way)
cd apps/giki-ai-api
uv run alembic revision --autogenerate -m "add new column"
uv run alembic upgrade head

# Management tasks (Proper way)
cd apps/giki-ai-api
uv run python -m giki_ai_api.cli.main user-create --email <EMAIL>

# Development (Using package.json scripts)
pnpm serve                      # Start all servers
pnpm serve:status              # Check server health
pnpm check-logs:dev:api        # View API logs
pnpm test:e2e                  # Run E2E tests
pnpm lint                      # Lint all code

# Deployment (Using nx)
pnpm deploy:dev                # Deploy to development
pnpm deploy:prod               # Deploy to production
```

## Adding Scripts (Rare Cases Only)

**Before adding ANY script, verify:**
1. ✅ Required by package.json npm script?
2. ✅ Required by nx project.json target?
3. ✅ Essential infrastructure that can't be nx command?

**If not essential infrastructure → DO NOT ADD**

## Enforcement

Any PR adding ad hoc scripts will be rejected. Fix problems at the source:
- Database issues → Fix in Alembic migrations
- Management needs → Add CLI command
- Testing needs → Add proper test
- Build/deploy needs → Extend nx targets

## Deployment URLs

After successful deployment:
- **Backend**: https://giki-ai-api-6uyufgxcxa-uc.a.run.app
- **Frontend**: https://app-giki-ai.web.app