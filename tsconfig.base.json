{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": true, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom", "esnext"], "types": ["node"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "noEmit": true, "paths": {}}, "exclude": ["node_modules", "tmp"]}