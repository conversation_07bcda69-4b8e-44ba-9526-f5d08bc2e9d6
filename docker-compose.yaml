version: '3.8'

services:
  # PostgreSQL database for local development
  postgres:
    image: postgres:16-alpine
    container_name: giki-ai-postgres
    environment:
      POSTGRES_DB: giki_ai_db
      POSTGRES_USER: giki_ai_user
      POSTGRES_PASSWORD: local_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/sql:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U giki_ai_user -d giki_ai_db"]
      interval: 5s
      timeout: 5s
      retries: 5

  # API service
  api:
    build:
      context: ./apps/giki-ai-api
      dockerfile: Dockerfile
    container_name: giki-ai-api
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - ENVIRONMENT=development
      - DEBUG=true
      - API_HOST=0.0.0.0
      - DATABASE_URL=postgresql+asyncpg://giki_ai_user:local_dev_password@postgres:5432/giki_ai_db
      - SECRET_KEY=dev_secret_key_for_local_only
      - AUTH_SECRET_KEY=dev_auth_secret_key_for_local_only
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - CORS_ALLOWED_ORIGINS=http://localhost:4200,http://127.0.0.1:4200,https://app-giki-ai.web.app
      - VERTEX_PROJECT_ID=rezolve-poc
      - VERTEX_LOCATION=us-central1
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      # Mount source code for hot reload in development
      - ./apps/giki-ai-api/src:/app/src:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend service (optional for full local testing)
  frontend:
    build:
      context: ./apps/giki-ai-app
      dockerfile: Dockerfile
    container_name: giki-ai-frontend
    ports:
      - "4200:80"
    environment:
      - VITE_API_URL=http://localhost:8080
    depends_on:
      - api
    profiles:
      - full  # Use with: docker compose --profile full up

volumes:
  postgres_data:

networks:
  default:
    name: giki-ai-network