# Production Environment
# Terraform configuration for giki.ai production environment

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.0"
    }
  }
  
  # Backend configuration for state storage
  backend "gcs" {
    bucket = "giki-ai-terraform-state"
    prefix = "environments/production"
  }
}

# Configure Google Provider
provider "google" {
  project = var.project_id
  region  = var.region
}

# Local variables
locals {
  environment = "production"
  
  # Production-specific configuration
  database_tier     = "db-n1-standard-2"
  disk_size        = 100
  max_disk_size    = 500
  max_connections  = 200
  
  # Service account roles for production
  api_roles = [
    "roles/cloudsql.client",
    "roles/aiplatform.user",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectUser",
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/cloudtrace.agent"
  ]
  
  frontend_roles = [
    "roles/firebase.sdkAdminServiceAccount"
  ]
  
  # Restricted network access for production
  authorized_networks = [
    {
      name  = "cloud-run-access"
      value = "0.0.0.0/0"  # Cloud Run needs public access to Cloud SQL
    }
  ]
}

# Service Accounts Module
module "service_accounts" {
  source = "../../modules/service-accounts"
  
  project_id  = var.project_id
  region      = var.region
  environment = local.environment
  
  api_roles      = local.api_roles
  frontend_roles = local.frontend_roles
}

# Cloud SQL Module
module "cloud_sql" {
  source = "../../modules/cloud-sql"
  
  project_id  = var.project_id
  region      = var.region
  environment = local.environment
  
  database_tier       = local.database_tier
  disk_size          = local.disk_size
  max_disk_size      = local.max_disk_size
  max_connections    = local.max_connections
  authorized_networks = local.authorized_networks
}

# Secrets Module
module "secrets" {
  source = "../../modules/secrets"
  
  project_id  = var.project_id
  region      = var.region
  environment = local.environment
  
  # Production should use Vertex AI, not API key
  gemini_api_key = ""
}

# Cloud Storage for file uploads
resource "google_storage_bucket" "uploads" {
  name     = "giki-ai-${local.environment}-uploads"
  location = var.region
  project  = var.project_id
  
  # Production settings - secure
  uniform_bucket_level_access = true
  force_destroy              = false  # Prevent accidental deletion
  
  cors {
    origin          = ["https://app-giki-ai.web.app"]
    method          = ["GET", "POST", "PUT", "DELETE"]
    response_header = ["*"]
    max_age_seconds = 3600
  }
  
  # Lifecycle management for production
  lifecycle_rule {
    condition {
      age = 365  # Keep files for 1 year
    }
    action {
      type = "Delete"
    }
  }
  
  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type          = "SetStorageClass"
      storage_class = "NEARLINE"
    }
  }
  
  # Versioning for production
  versioning {
    enabled = true
  }
  
  labels = {
    environment = local.environment
    project     = "giki-ai"
    managed_by  = "terraform"
  }
}

# Cloud Storage IAM for API service account
resource "google_storage_bucket_iam_member" "api_storage_access" {
  bucket = google_storage_bucket.uploads.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${module.service_accounts.api_service_account_email}"
}

# Cloud Run service (placeholder - actual deployment handled by nx)
resource "google_cloud_run_service" "api" {
  name     = "giki-ai-api"
  location = var.region
  project  = var.project_id
  
  template {
    spec {
      service_account_name = module.service_accounts.api_service_account_email
      
      containers {
        image = "gcr.io/${var.project_id}/giki-ai-api:latest"
        
        env {
          name  = "ENVIRONMENT"
          value = local.environment
        }
        
        env {
          name  = "VERTEX_PROJECT_ID"
          value = var.project_id
        }
        
        env {
          name  = "VERTEX_LOCATION"
          value = var.region
        }
        
        resources {
          limits = {
            cpu    = "2000m"
            memory = "4Gi"
          }
          requests = {
            cpu    = "1000m"
            memory = "2Gi"
          }
        }
      }
    }
    
    metadata {
      annotations = {
        "autoscaling.knative.dev/maxScale"      = "100"
        "run.googleapis.com/cloudsql-instances" = module.cloud_sql.connection_name
        "run.googleapis.com/execution-environment" = "gen2"
      }
    }
  }
  
  traffic {
    percent         = 100
    latest_revision = true
  }
  
  lifecycle {
    ignore_changes = [
      template[0].spec[0].containers[0].image,
      template[0].metadata[0].annotations
    ]
  }
}

# Make Cloud Run service publicly accessible
resource "google_cloud_run_service_iam_member" "public_access" {
  service  = google_cloud_run_service.api.name
  location = google_cloud_run_service.api.location
  project  = var.project_id
  role     = "roles/run.invoker"
  member   = "allUsers"
}