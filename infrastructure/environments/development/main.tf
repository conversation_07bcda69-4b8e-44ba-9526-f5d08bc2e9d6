# Development Environment
# Terraform configuration for giki.ai development environment

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.0"
    }
  }
  
  # Backend configuration for state storage
  backend "gcs" {
    bucket = "giki-ai-terraform-state"
    prefix = "environments/development"
  }
}

# Configure Google Provider
provider "google" {
  project = var.project_id
  region  = var.region
}

# Local variables
locals {
  environment = "development"
  
  # Development-specific configuration (Cloud-based development environment)
  database_tier     = "db-f1-micro"  # Small instance for development
  disk_size        = 20
  max_disk_size    = 50
  max_connections  = 50
  
  # Service account roles for development
  api_roles = [
    "roles/cloudsql.client",
    "roles/aiplatform.user",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectUser"
  ]
  
  frontend_roles = [
    "roles/firebase.sdkAdminServiceAccount"
  ]
  
  # Network configuration for development
  authorized_networks = [
    {
      name  = "development-access"
      value = "0.0.0.0/0"  # Open for development - restrict in production
    }
  ]
}

# Service Accounts Module
module "service_accounts" {
  source = "../../modules/service-accounts"
  
  project_id  = var.project_id
  region      = var.region
  environment = local.environment
  
  api_roles      = local.api_roles
  frontend_roles = local.frontend_roles
}

# Cloud SQL Module
module "cloud_sql" {
  source = "../../modules/cloud-sql"
  
  project_id  = var.project_id
  region      = var.region
  environment = local.environment
  
  database_tier       = local.database_tier
  disk_size          = local.disk_size
  max_disk_size      = local.max_disk_size
  max_connections    = local.max_connections
  authorized_networks = local.authorized_networks
}

# Secrets Module
module "secrets" {
  source = "../../modules/secrets"
  
  project_id  = var.project_id
  region      = var.region
  environment = local.environment
  
  # Development can use API key for testing
  gemini_api_key = var.gemini_api_key
}

# Cloud Storage for file uploads
resource "google_storage_bucket" "uploads" {
  name     = "giki-ai-${local.environment}-uploads"
  location = var.region
  project  = var.project_id
  
  # Development settings - less restrictive
  uniform_bucket_level_access = true
  force_destroy              = true  # Allow destroy for development
  
  cors {
    origin          = ["http://localhost:4200", "http://127.0.0.1:4200"]
    method          = ["GET", "POST", "PUT", "DELETE"]
    response_header = ["*"]
    max_age_seconds = 3600
  }
  
  lifecycle_rule {
    condition {
      age = 30  # Delete files older than 30 days in development
    }
    action {
      type = "Delete"
    }
  }
  
  labels = {
    environment = local.environment
    project     = "giki-ai"
    managed_by  = "terraform"
  }
}

# Cloud Storage IAM for API service account
resource "google_storage_bucket_iam_member" "api_storage_access" {
  bucket = google_storage_bucket.uploads.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${module.service_accounts.api_service_account_email}"
}