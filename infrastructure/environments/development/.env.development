# giki.ai Development Environment Configuration
# This file contains development-specific settings

# AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.5-pro
GOOGLE_CLOUD_PROJECT=rezolve-poc
GOOGLE_GENAI_USE_VERTEXAI=true
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/giki-ai-workspace/infrastructure/service-accounts/development/dev-service-account.json

# Database Configuration - PostgreSQL (Development)
DATABASE_URL=postgresql+asyncpg://giki_ai_user@localhost:5432/giki_ai_db
TEST_DATABASE_URL=postgresql+asyncpg://giki_ai_user@localhost:5432/giki_ai_test_db

# Environment Settings
ENVIRONMENT=development
DEBUG=true
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_ENVIRONMENT=development

# Google Cloud Configuration
VERTEX_SERVICE_ACCOUNT_KEY_PATH=/Users/<USER>/giki-ai-workspace/infrastructure/service-accounts/development/dev-service-account.json
VERTEX_LOCATION=us-central1
VERTEX_PROJECT_ID=rezolve-poc

# Security Settings - Development Keys (NOT for production!)
SECRET_KEY=dev_secret_key_12345_not_for_production
AUTH_SECRET_KEY=dev_auth_secret_key_12345_not_for_production_persistent
ALGORITHM=RS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
ADMIN_API_KEY=dev_admin_key_12345_not_for_production

# Test User Credentials (Development only)
TEST_USER_PASSWORD=GikiTest2025Secure

# AI Model Configuration
GCS_BUCKET_NAME_RAG=rezolve-poc-rag-bucket
FILE_INGESTION_AI_MODEL_NAME=gemini-2.0-flash-001
CATEGORIZATION_MODEL_ID=gemini-2.0-flash-001
VERTEX_CHAT_MODEL_NAME=gemini-2.0-flash-001
VERTEX_AI_GEMINI_MODEL_ID=gemini-2.0-flash-001

# App Configuration
APP_HOST=0.0.0.0
APP_PORT=4200

# Redis Configuration
REDIS_ENABLED=true
REDIS_URL=redis://localhost:6379
REDIS_DB=0

# JWT Configuration
JWT_KEY_DIR=/Users/<USER>/giki-ai-workspace/infrastructure/jwt-keys