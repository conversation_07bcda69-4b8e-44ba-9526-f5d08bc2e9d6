# Infrastructure Organization

## Overview
This directory contains all infrastructure configuration for giki.ai including Terraform, deployment configs, and service accounts.

## Structure
```
infrastructure/
├── environments/        # Terraform environment configs
│   ├── development/     # Dev environment Terraform
│   └── production/      # Prod environment Terraform
├── modules/             # Reusable Terraform modules
│   ├── service-accounts/
│   ├── cloud-sql/
│   └── secrets/
├── deployment/          # Cloud Build and deployment configs
│   ├── cloudbuild.yaml      # Standard Docker build
│   ├── cloudbuild.kaniko.yaml  # Kaniko fast build
│   └── cloud-run-env.yaml  # Environment variables
├── service-accounts/    # GCP service account keys (environment-specific)
│   ├── development/     # Development environment keys
│   │   ├── dev-service-account.json
│   │   ├── firebase-admin-key.json
│   │   ├── github-actions-key.json
│   │   └── service-account.json
│   └── production/      # Production environment keys
│       ├── cloud-run-app-key.json
│       └── service-account.json
└── scripts/             # Infrastructure scripts
```

## Usage

### Terraform Infrastructure
```bash
# Development
cd infrastructure/environments/development
terraform init && terraform plan && terraform apply

# Production  
cd infrastructure/environments/production
terraform init && terraform plan && terraform apply
```

### Cloud Build Deployment
```bash
# Standard deployment
gcloud builds submit --config infrastructure/deployment/cloudbuild.yaml

# Fast Kaniko deployment (recommended)
gcloud builds submit --config infrastructure/deployment/cloudbuild.kaniko.yaml
```

### Service Account Management
- Development keys: `infrastructure/service-accounts/development/`
- Production keys: `infrastructure/service-accounts/production/`
- Service accounts configured via Terraform in `modules/service-accounts/`

## Environment Variables Required
- `TF_VAR_project_id` - GCP Project ID
- `TF_VAR_region` - GCP Region (us-central1)
- `TF_VAR_environment` - Environment name (development/production)

## Security
- All sensitive values stored in Google Secret Manager
- Service accounts follow principle of least privilege
- Separate resources per environment for isolation