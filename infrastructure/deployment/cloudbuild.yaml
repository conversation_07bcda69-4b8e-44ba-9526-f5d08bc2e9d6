# Ultra-fast Cloud Build configuration with layer caching
# This can reduce build times from 5-10 minutes to 30 seconds - 2 minutes

substitutions:
  _REGION: us-central1
  _SERVICE_NAME: giki-ai-api
  _REGISTRY: us-central1-docker.pkg.dev
  _REPO: giki-ai

steps:
  # Step 1: Pull previous image for cache (ignore if doesn't exist)
  - name: 'gcr.io/cloud-builders/docker'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        docker pull ${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:latest || exit 0
    id: 'pull-cache'

  # Step 2: Build final image with layer caching
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-f'
      - 'Dockerfile'
      - '-t'
      - '${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:${BUILD_ID}'
      - '-t'
      - '${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:latest'
      - '--cache-from'
      - '${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:latest'
      - '.'
    id: 'build-app'
    waitFor: ['pull-cache']

  # Step 4: Deploy to Cloud Run (fastest step - just updates service)
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image=${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:${BUILD_ID}'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--add-cloudsql-instances=${PROJECT_ID}:${_REGION}:giki-ai-postgres-prod'
      - '--service-account=dev-giki-ai-service-account@${PROJECT_ID}.iam.gserviceaccount.com'
      - '--allow-unauthenticated'
      - '--memory=1Gi'
      - '--cpu=1'
      - '--concurrency=100'
      - '--timeout=300'
      - '--max-instances=10'
      - '--no-traffic'  # Deploy without traffic for zero-downtime
    id: 'deploy'
    waitFor: ['build-app']

  # Step 5: Update environment variables separately
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'services'
      - 'update'
      - '${_SERVICE_NAME}'
      - '--region=${_REGION}'
      - '--set-env-vars=DATABASE_URL=postgresql+asyncpg://giki_ai_user:REDACTED_DB_PASSWORD@/giki_ai_db?host=/cloudsql/${PROJECT_ID}:${_REGION}:giki-ai-postgres-prod,ENVIRONMENT=production'
    id: 'update-env'
    waitFor: ['deploy']

  # Step 6: Gradual traffic migration for zero-downtime deployment
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'services'
      - 'update-traffic'
      - '${_SERVICE_NAME}'
      - '--to-latest'
      - '--region=${_REGION}'
    id: 'update-traffic'
    waitFor: ['update-env']

# Push images for next build
images:
  - '${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:latest'
  - '${_REGISTRY}/${PROJECT_ID}/${_REPO}/${_SERVICE_NAME}:${BUILD_ID}'

# Use high-performance build machine for speed
options:
  machineType: 'E2_HIGHCPU_8'  # 8 vCPU for fast builds
  diskSizeGb: 100
  logging: CLOUD_LOGGING_ONLY

# Build timeout
timeout: '600s'  # 10 minutes max (should complete in 1-3 minutes)