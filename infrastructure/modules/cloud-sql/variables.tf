variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP Region"
  type        = string
  default     = "us-central1"
}

variable "environment" {
  description = "Environment name (development, production)"
  type        = string
  validation {
    condition     = contains(["development", "production"], var.environment)
    error_message = "Environment must be either 'development' or 'production'."
  }
}

variable "database_tier" {
  description = "Cloud SQL instance tier"
  type        = string
  default     = "db-f1-micro"  # Will be overridden per environment
}

variable "disk_size" {
  description = "Initial disk size in GB"
  type        = number
  default     = 20
}

variable "max_disk_size" {
  description = "Maximum disk size for autoresize in GB"
  type        = number
  default     = 100
}

variable "max_connections" {
  description = "Maximum number of database connections"
  type        = number
  default     = 100
}

variable "backup_location" {
  description = "Backup location"
  type        = string
  default     = "us"
}

variable "vpc_network" {
  description = "VPC network for private IP"
  type        = string
  default     = null
}

variable "authorized_networks" {
  description = "List of authorized networks"
  type = list(object({
    name  = string
    value = string
  }))
  default = [
    {
      name  = "all"
      value = "0.0.0.0/0"  # Will be restricted per environment
    }
  ]
}