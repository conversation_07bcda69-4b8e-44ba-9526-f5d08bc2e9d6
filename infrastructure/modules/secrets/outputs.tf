output "auth_secret_key_name" {
  description = "Secret Manager secret name for auth secret key"
  value       = google_secret_manager_secret.auth_secret_key.secret_id
}

output "secret_key_name" {
  description = "Secret Manager secret name for application secret key"
  value       = google_secret_manager_secret.secret_key.secret_id
}

output "admin_api_key_name" {
  description = "Secret Manager secret name for admin API key"
  value       = google_secret_manager_secret.admin_api_key.secret_id
}

output "gemini_api_key_name" {
  description = "Secret Manager secret name for Gemini API key"
  value       = var.gemini_api_key != "" ? google_secret_manager_secret.gemini_api_key[0].secret_id : null
}

output "cors_origins_name" {
  description = "Secret Manager secret name for CORS origins"
  value       = google_secret_manager_secret.cors_origins.secret_id
}