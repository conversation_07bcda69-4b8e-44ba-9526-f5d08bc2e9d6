# Secrets Module
# Manages application secrets in Google Secret Manager

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.0"
    }
  }
}

locals {
  labels = {
    environment = var.environment
    project     = "giki-ai"
    managed_by  = "terraform"
  }
}

# Generate secure authentication secrets
resource "random_password" "auth_secret_key" {
  length  = 64
  special = true
}

resource "random_password" "secret_key" {
  length  = 64
  special = true
}

resource "random_password" "admin_api_key" {
  length  = 32
  special = false
  upper   = true
  lower   = true
  numeric = true
}

# Auth Secret Key
resource "google_secret_manager_secret" "auth_secret_key" {
  secret_id = "giki-ai-${var.environment}-auth-secret-key"
  
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
  
  labels = local.labels
}

resource "google_secret_manager_secret_version" "auth_secret_key" {
  secret      = google_secret_manager_secret.auth_secret_key.id
  secret_data = random_password.auth_secret_key.result
}

# Application Secret Key
resource "google_secret_manager_secret" "secret_key" {
  secret_id = "giki-ai-${var.environment}-secret-key"
  
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
  
  labels = local.labels
}

resource "google_secret_manager_secret_version" "secret_key" {
  secret      = google_secret_manager_secret.secret_key.id
  secret_data = random_password.secret_key.result
}

# Admin API Key
resource "google_secret_manager_secret" "admin_api_key" {
  secret_id = "giki-ai-${var.environment}-admin-api-key"
  
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
  
  labels = local.labels
}

resource "google_secret_manager_secret_version" "admin_api_key" {
  secret      = google_secret_manager_secret.admin_api_key.id
  secret_data = "giki-admin-${var.environment}-${random_password.admin_api_key.result}"
}

# API Keys (from variables)
resource "google_secret_manager_secret" "gemini_api_key" {
  count     = var.gemini_api_key != "" ? 1 : 0
  secret_id = "giki-ai-${var.environment}-gemini-api-key"
  
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
  
  labels = local.labels
}

resource "google_secret_manager_secret_version" "gemini_api_key" {
  count       = var.gemini_api_key != "" ? 1 : 0
  secret      = google_secret_manager_secret.gemini_api_key[0].id
  secret_data = var.gemini_api_key
}

# CORS Origins (environment-specific)
locals {
  cors_origins = var.environment == "production" ? "https://app-giki-ai.web.app" : "http://localhost:4200,http://127.0.0.1:4200,https://app-giki-ai.web.app"
}

resource "google_secret_manager_secret" "cors_origins" {
  secret_id = "giki-ai-${var.environment}-cors-origins"
  
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
  
  labels = local.labels
}

resource "google_secret_manager_secret_version" "cors_origins" {
  secret      = google_secret_manager_secret.cors_origins.id
  secret_data = local.cors_origins
}