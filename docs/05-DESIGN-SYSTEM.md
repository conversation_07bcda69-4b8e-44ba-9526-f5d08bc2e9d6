# Design System - giki.ai Platform

**Version:** 2.0 | **Updated:** 2025-01-11  
**Purpose:** Professional B2B design standards and component specifications  
**Scope:** Brand identity, layout systems, component library, and visual consistency

## DOCUMENTATION INTEGRATION

**See Also:**
- **docs/06-PAGE-SPECIFICATIONS.md** - Functional page behavior, states, and API integrations
- **docs/04-CUSTOMER-JOURNEYS.md** - Business context and user flow requirements
- **docs/02-SYSTEM-ARCHITECTURE.md** - Technical implementation patterns

## DESIGN SYSTEM IMPORTS

### Visual References
docs/design-system/mockups/components/component-preview.html
docs/design-system/mockups/authentication/000-landing-login.html
docs/design-system/mockups/dashboard/300-dashboard-overview.html
docs/design-system/mockups/upload/100-upload-interface.html
docs/design-system/mockups/processing/200-ai-processing.html
docs/design-system/mockups/transactions/400-transaction-review.html
docs/design-system/mockups/reports/500-export-options.html
docs/design-system/mockups/components/agent/agent-panel.html
docs/design-system/mockups/mobile/mobile-responsive-design.html

### Functional Specifications
docs/06-PAGE-SPECIFICATIONS.md - Complete page states, API integrations, and user flows
docs/04-CUSTOMER-JOURNEYS.md - User journey flows and business requirements
docs/02-SYSTEM-ARCHITECTURE.md - Technical implementation architecture
docs/08-TESTING-STRATEGY.md - Testing approach and validation methods

### Cross-Reference Integration
- **Page Implementation**: Use this design system with docs/06-PAGE-SPECIFICATIONS.md for complete page development
- **Component Standards**: Reference component specifications when implementing page states
- **Visual Validation**: Use design mockups to validate page specification implementations
- **Brand Compliance**: Ensure page behaviors maintain design system standards

---

## 🎨 BRAND IDENTITY & STANDARDS

### Color System (Exact Specifications)
```css
/* Primary Brand Colors */
:root {
  --brand-primary: #295343;           /* Professional Excel-like green */
  --brand-primary-hover: #1D372E;     /* Darker hover state */
  --brand-primary-light: #E8F5E8;     /* Light brand accent */
  --brand-primary-alpha: rgba(41, 83, 67, 0.1); /* Transparent overlay */
}

/* Professional UI Colors */
:root {
  --ui-background: #FFFFFF;           /* Clean white background */
  --ui-surface: #F8F9FA;              /* Light surface color */
  --ui-border: #E1E5E9;               /* Subtle border color */
  --ui-text-primary: #1A1D29;         /* Primary text color */
  --ui-text-secondary: #6B7280;       /* Secondary text color */
  --ui-text-muted: #9CA3AF;           /* Muted text color */
}

/* Status & Feedback Colors */
:root {
  --status-success: #059669;          /* Success green */
  --status-warning: #D97706;          /* Warning orange */
  --status-error: #DC2626;            /* Error red */
  --status-info: #2563EB;             /* Info blue */
  --status-neutral: #6B7280;          /* Neutral gray */
}
```

### Typography System
```css
/* Font Stack (Professional B2B) */
:root {
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

/* Typography Scale */
:root {
  --text-xs: 0.75rem;     /* 12px - Fine print */
  --text-sm: 0.875rem;    /* 14px - Secondary text */
  --text-base: 1rem;      /* 16px - Body text */
  --text-lg: 1.125rem;    /* 18px - Emphasized text */
  --text-xl: 1.25rem;     /* 20px - Small headings */
  --text-2xl: 1.5rem;     /* 24px - Section headings */
  --text-3xl: 1.875rem;   /* 30px - Page headings */
  --text-4xl: 2.25rem;    /* 36px - Display headings */
}

/* Font Weights */
:root {
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

### Icon System (Geometric Only)
```typescript
// ALLOWED ICONS (Geometric shapes only)
export const ALLOWED_ICONS = {
  square: '□',           // General purpose, containers, completion states
  squares: '⊞',          // Multiple items, collections, transactions  
  menu: '∷',             // Menu, options, settings, reports
  circle: '⚬',           // Status indicators, bullets, processing
  arrow: '↑',            // Direction, sorting, movement, upload
} as const;

// PROHIBITED ICONS (Emojis - NEVER USE)
export const PROHIBITED_ICONS = {
  // ❌ NEVER USE THESE IN PRODUCTION
  target: '🎯',          // Use ⚬ instead
  rocket: '🚀',          // Use ↑ instead  
  lightbulb: '💡',       // Use □ instead
  chart: '📊',           // Use ∷ instead
  sparkles: '✨',        // Use ⚬ instead
  refresh: '🔄',         // Use ↑ instead
  lightning: '⚡',       // Use ↑ instead
  checkmark: '✓',        // Use □ instead - Brand compliance critical
} as const;
```

---

## 🏗️ LAYOUT SYSTEM ARCHITECTURE

### Three-Panel Professional Layout
```typescript
// Master Layout Component Structure
interface EnhancedAppLayoutProps {
  leftPanel?: React.ReactNode;      // Work-focused navigation
  agentPanel?: React.ReactNode;     // AI assistance panel
  children: React.ReactNode;        // Main content area
}

// Panel State Management (Mutual Exclusion)
interface PanelState {
  leftPanel: 'expanded' | 'collapsed';   // 240px | 64px
  agentPanel: 'hidden' | 'open';         // 0px | 400px
  // RULE: Never both expanded - Work-first OR Agent-mode
}

// CSS Grid Configuration
const LAYOUT_GRID_STATES = {
  workFirst: "240px 1fr 0px",       // Left expanded, agent hidden
  agentMode: "64px 1fr 400px",      // Left collapsed, agent open
  mobileOnly: "1fr",                // Single column on mobile
} as const;
```

### Responsive Breakpoints
```css
/* Professional Responsive Design */
:root {
  --breakpoint-sm: 640px;   /* Mobile landscape */
  --breakpoint-md: 768px;   /* Tablet portrait */
  --breakpoint-lg: 1024px;  /* Tablet landscape */
  --breakpoint-xl: 1280px;  /* Desktop */
  --breakpoint-2xl: 1536px; /* Large desktop */
}

/* Layout Variables */
:root {
  --nav-expanded: 240px;     /* Work-focused navigation width */
  --nav-collapsed: 64px;     /* Minimal navigation width */
  --agent-panel: 400px;      /* AI assistance panel width */
  --agent-min: 320px;        /* Minimum resizable width */
  --agent-max: 480px;        /* Maximum resizable width */
  --content-max: 1200px;     /* Maximum content width */
}
```

### Spacing System
```css
/* Consistent Spacing Scale */
:root {
  --space-0: 0px;
  --space-1: 0.25rem;    /* 4px */
  --space-2: 0.5rem;     /* 8px */
  --space-3: 0.75rem;    /* 12px */
  --space-4: 1rem;       /* 16px */
  --space-5: 1.25rem;    /* 20px */
  --space-6: 1.5rem;     /* 24px */
  --space-8: 2rem;       /* 32px */
  --space-10: 2.5rem;    /* 40px */
  --space-12: 3rem;      /* 48px */
  --space-16: 4rem;      /* 64px */
  --space-20: 5rem;      /* 80px */
  --space-24: 6rem;      /* 96px */
}
```

### Mobile Responsiveness System
```css
/* Responsive Breakpoints */
:root {
  --breakpoint-sm: 640px;    /* Small devices (landscape phones) */
  --breakpoint-md: 768px;    /* Medium devices (tablets) */
  --breakpoint-lg: 1024px;   /* Large devices (laptops) */
  --breakpoint-xl: 1280px;   /* Extra large devices (desktops) */
  --breakpoint-2xl: 1536px;  /* 2X large devices (large desktops) */
}

/* Touch Target Standards */
:root {
  --touch-target-min: 44px;  /* Minimum touch target size */
  --touch-target-ideal: 48px; /* Ideal touch target size */
}

/* Mobile-Optimized Spacing */
@media (max-width: 768px) {
  :root {
    --page-padding: 16px;     /* Reduced page padding for mobile */
    --section-padding: 16px;  /* Reduced section padding */
    --card-padding: 16px;     /* Reduced card padding */
    --section-gap: 16px;      /* Reduced section gaps */
    --element-gap: 12px;      /* Reduced element gaps */
  }
}
```

### Mobile Navigation System
```typescript
// Mobile Navigation States
interface MobileNavigation {
  isExpanded: boolean;
  hasBackdrop: boolean;
  position: 'overlay' | 'push';
  animation: 'slide' | 'fade';
}

// Mobile Navigation Controls
const MOBILE_NAV_CONFIG = {
  toggleButton: {
    position: 'fixed',
    top: '1rem',
    left: '1rem',
    size: 'touch-target',
    zIndex: 60,
    icons: { open: '☰', close: '✕' }
  },
  panel: {
    width: '280px',
    position: 'overlay',
    backdrop: true,
    zIndex: 50
  }
} as const;
```

### Responsive Table Design
```css
/* Mobile Table Optimization */
.responsive-table {
  /* Mobile: Horizontal scroll */
  @media (max-width: 768px) {
    overflow-x: auto;
    min-width: 100%;
    
    /* Hide non-essential columns */
    .column-secondary {
      display: none;
    }
    
    /* Show hidden data inline */
    .mobile-inline-data {
      display: block;
      font-size: var(--text-xs);
      color: var(--ui-text-secondary);
      margin-top: 0.25rem;
    }
  }
  
  /* Tablet and up: Full table */
  @media (min-width: 768px) {
    .mobile-inline-data {
      display: none;
    }
  }
}
```

### Touch-Friendly Component Standards
```css
/* Touch Target Utilities */
.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile Button Sizing */
@media (max-width: 768px) {
  .btn-mobile {
    padding: 0.75rem 1rem;
    font-size: var(--text-sm);
    min-height: var(--touch-target-min);
  }
  
  .btn-compact-mobile {
    padding: 0.5rem 0.75rem;
    font-size: var(--text-xs);
    min-height: 36px;
  }
}
```

---

## 🎯 COMPONENT SPECIFICATIONS

### Enhanced Navigation System
```typescript
// Left Navigation Panel Component
interface LeftNavigationProps {
  state: 'expanded' | 'collapsed';
  onStateChange: (state: 'expanded' | 'collapsed') => void;
  currentPath: string;
}

// Navigation Items Configuration
const NAVIGATION_ITEMS = [
  { 
    path: '/dashboard', 
    label: 'Dashboard', 
    icon: '⊞',
    description: 'Business metrics and overview'
  },
  { 
    path: '/upload', 
    label: 'Upload', 
    icon: '↑',
    description: 'File upload and processing'
  },
  { 
    path: '/transactions', 
    label: 'Review', 
    icon: '□',
    description: 'Transaction review and approval'
  },
  { 
    path: '/reports', 
    label: 'Reports', 
    icon: '∷',
    description: 'Analytics and export'
  },
  { 
    path: '/categories', 
    label: 'Categories', 
    icon: '⚬',
    description: 'Category management'
  },
] as const;
```

### Professional Form System
```css
/* Form Component Standards */
.form-input {
  @apply w-full px-4 py-3 border border-ui-border rounded-lg;
  @apply focus:ring-2 focus:ring-brand-primary focus:border-brand-primary;
  @apply transition-colors duration-200;
  font-family: var(--font-primary);
  font-size: var(--text-base);
}

.form-label {
  @apply block text-sm font-medium text-ui-text-primary mb-2;
  font-family: var(--font-primary);
}

.form-error {
  @apply text-sm text-status-error mt-1;
  font-family: var(--font-primary);
}

.form-button-primary {
  @apply px-6 py-3 bg-brand-primary text-white rounded-lg;
  @apply hover:bg-brand-primary-hover focus:ring-2 focus:ring-brand-primary;
  @apply transition-colors duration-200 font-medium;
  font-family: var(--font-primary);
}
```

### Business Intelligence Cards
```css
/* Professional Card System */
.metric-card {
  @apply bg-white border border-ui-border rounded-lg p-6;
  @apply shadow-sm hover:shadow-md transition-shadow duration-200;
}

.metric-card-header {
  @apply flex items-center justify-between mb-4;
}

.metric-card-title {
  @apply text-lg font-semibold text-ui-text-primary;
  font-family: var(--font-primary);
}

.metric-card-value {
  @apply text-3xl font-bold text-brand-primary;
  font-family: var(--font-primary);
}

.metric-card-description {
  @apply text-sm text-ui-text-secondary mt-2;
  font-family: var(--font-primary);
}
```

### Status & Progress Indicators
```css
/* Inline Progress System (NO floating notifications) */
.progress-inline {
  @apply w-full bg-ui-surface rounded-full h-2;
}

.progress-inline-bar {
  @apply h-2 bg-brand-primary rounded-full transition-all duration-300;
}

.status-badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
}

.status-badge-success {
  @apply bg-green-100 text-green-800;
}

.status-badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.status-badge-error {
  @apply bg-red-100 text-red-800;
}

.status-badge-info {
  @apply bg-blue-100 text-blue-800;
}
```

---

## 📱 MOBILE & RESPONSIVE DESIGN

### Mobile-First Approach
```css
/* Mobile Navigation Pattern */
@media (max-width: 768px) {
  .three-panel-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .mobile-header {
    @apply flex items-center justify-between p-4 border-b border-ui-border;
    background: var(--ui-background);
  }
  
  .mobile-nav-toggle {
    @apply p-2 rounded-lg border border-ui-border;
    background: var(--ui-surface);
  }
  
  .mobile-nav-drawer {
    @apply fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-ui-border;
    @apply transform transition-transform duration-300 ease-in-out;
    transform: translateX(-100%);
  }
  
  .mobile-nav-drawer.open {
    transform: translateX(0);
  }
}
```

### Touch-Optimized Interactions
```css
/* Mobile Touch Targets */
.touch-target {
  @apply min-h-[44px] min-w-[44px];
  @apply flex items-center justify-center;
}

.mobile-form-input {
  @apply text-base;  /* Prevent zoom on iOS */
  @apply min-h-[44px];
}

.mobile-button {
  @apply min-h-[44px] px-6;
  @apply text-base font-medium;
}
```

---

## 🎭 VISUAL REFERENCE SYSTEM

### HTML Mockups (Implementation Targets)
```
Visual Implementation Standards:
├── docs/design-system/page-mockups/01-authentication/
│   └── landing-login.html                    # Professional login interface
├── docs/design-system/page-mockups/03-dashboard/
│   ├── empty-dashboard.html                  # First-time user experience
│   └── populated-dashboard.html              # Business metrics interface
├── docs/design-system/page-mockups/04-upload/
│   └── upload-interface.html                 # Enhanced file upload
├── docs/design-system/page-mockups/05-transactions/
│   └── review-interface.html                 # Transaction review workflow
└── docs/design-system/page-mockups/06-reports/
    └── report-templates.html                 # Professional reporting
```

### Brand Compliance Validation
```typescript
// Design System Validation Rules
export const DESIGN_COMPLIANCE = {
  colors: {
    primary: "#295343",              // EXACT brand color required
    prohibited: ["#FF0000", "#00FF00"], // No bright colors
  },
  icons: {
    allowed: ["□", "⊞", "∷", "⚬", "↑"],
    prohibited: ["🎯", "🚀", "💡", "📊", "✨", "🔄", "⚡"],
  },
  layout: {
    panelMutualExclusion: true,      // Never both panels expanded
    workFirstPriority: true,         // Work content prioritized
    noFloatingNotifications: true,   // Inline status only
  },
  typography: {
    professional: true,              // Business-appropriate
    noComicSans: true,              // Professional fonts only
    consistentHierarchy: true,       // Clear information hierarchy
  },
} as const;
```

---

## 🔧 COMPONENT IMPLEMENTATION PATTERNS

### Enhanced Component Architecture
```typescript
// Component File Organization
interface ComponentStructure {
  location: "apps/giki-ai-app/src/shared/components/";
  organization: {
    layout: "EnhancedAppLayout, PanelStateProvider";
    forms: "FormInput, FormButton, FormCard";
    navigation: "LeftNavigation, BreadcrumbNav";
    data: "MetricCard, StatusBadge, ProgressBar";
    feedback: "InlineAlert, StatusIndicator";
  };
}

// Component Naming Convention
export const ComponentNaming = {
  prefix: "Enhanced",                // UploadPage
  suffix: "Component",               // MetricCardComponent
  hook: "use",                       // useTransactionData
  context: "Context",                // PanelStateContext
  provider: "Provider",              // ThemeProvider
} as const;
```

### Styling Architecture
```css
/* CSS Custom Properties Pattern */
.component {
  /* Use CSS variables for consistency */
  color: var(--ui-text-primary);
  background: var(--ui-background);
  border: 1px solid var(--ui-border);
  
  /* Professional spacing */
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  
  /* Consistent typography */
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: 1.5;
}

/* Responsive Design Pattern */
@media (min-width: 768px) {
  .component {
    padding: var(--space-6);
    margin-bottom: var(--space-8);
  }
}
```

---

## 🎨 ACCESSIBILITY STANDARDS

### WCAG 2.1 Compliance
```css
/* Accessibility Requirements */
.accessible-component {
  /* Color contrast ratios */
  --contrast-normal: 4.5:1;    /* Normal text */
  --contrast-large: 3:1;       /* Large text */
  
  /* Focus indicators */
  focus-visible: 2px solid var(--brand-primary);
  outline-offset: 2px;
  
  /* Touch targets */
  min-height: 44px;
  min-width: 44px;
}

/* Screen Reader Support */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
```

### Keyboard Navigation
```typescript
// Keyboard Accessibility Patterns
export const KeyboardNavigation = {
  tabOrder: "logical",              // Sequential tab navigation
  escapeKey: "closeModals",         // Escape closes dialogs
  enterKey: "activateButtons",      // Enter activates buttons
  spaceKey: "selectCheckboxes",     // Space selects checkboxes
  arrowKeys: "navigateMenus",       // Arrows navigate menus
} as const;
```

---

## 📏 DESIGN TOKENS

### Complete Token System
```typescript
// Design Token Export
export const DesignTokens = {
  colors: {
    brand: {
      primary: "#295343",
      primaryHover: "#1D372E", 
      primaryLight: "#E8F5E8",
    },
    ui: {
      background: "#FFFFFF",
      surface: "#F8F9FA",
      border: "#E1E5E9",
    },
    text: {
      primary: "#1A1D29",
      secondary: "#6B7280",
      muted: "#9CA3AF",
    },
    status: {
      success: "#059669",
      warning: "#D97706",
      error: "#DC2626",
      info: "#2563EB",
    },
  },
  
  typography: {
    fontFamily: {
      primary: "Inter, system-ui, sans-serif",
      mono: "JetBrains Mono, monospace",
    },
    fontSize: {
      xs: "0.75rem",
      sm: "0.875rem", 
      base: "1rem",
      lg: "1.125rem",
      xl: "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
      "4xl": "2.25rem",
    },
  },
  
  spacing: {
    0: "0px",
    1: "0.25rem",
    2: "0.5rem", 
    3: "0.75rem",
    4: "1rem",
    6: "1.5rem",
    8: "2rem",
    12: "3rem",
    16: "4rem",
  },
  
  layout: {
    navExpanded: "240px",
    navCollapsed: "64px", 
    agentPanel: "400px",
    contentMax: "1200px",
  },
} as const;
```

---

**DESIGN SYSTEM SUMMARY:** Professional B2B design system with exact brand compliance, geometric icon system, three-panel layout architecture, and comprehensive component specifications. All standards validated through HTML mockups and customer testing.

**NEXT UPDATE:** After component library completion and design token implementation across all features.