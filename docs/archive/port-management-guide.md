# Port Management Guide - giki.ai Development

**Updated:** 2025-07-01  
**Purpose:** Comprehensive guide to port management in NX development workflow  
**Scope:** Port clearing, status checking, and conflict resolution

---

## 🎯 OVERVIEW

The NX command targets now include automatic port clearing functionality to ensure clean port allocation for all server commands. This eliminates common "port already in use" errors and provides predictable development environments.

### Key Benefits
- **Automatic Port Clearing**: Server commands automatically clear their required ports
- **Known Port Management**: Centralized management of all development ports
- **Graceful Shutdown**: Attempts graceful process termination before force killing
- **Status Monitoring**: Easy checking of which ports are in use
- **NX Integration**: Full integration with existing NX workflow

---

## 🔌 KNOWN DEVELOPMENT PORTS

```bash
Port 3000  - Storybook
Port 4200  - Frontend (React/Vite)
Port 5432  - PostgreSQL Database
Port 6379  - Redis Cache
Port 8000  - API (FastAPI)
Port 8001  - API Stable (FastAPI without reload)
Port 9229  - Node.js Debug
```

---

## 📋 AVAILABLE COMMANDS

### Port Status Checking

```bash
# Show status of all development ports
pnpm nx port-status

# Direct script usage
./scripts/nx/clear-ports.sh status
```

**Output Example:**
```
Development ports status:

  🔴 Port 4200 - Frontend (React/Vite) (PID: 32072)
  🔴 Port 8000 - API (FastAPI) (PID: 30520)
  🟢 Port 8001 - API Stable (FastAPI without reload)
  🟢 Port 6379 - Redis Cache
```

### Port Clearing Commands

```bash
# Clear all development ports (except PostgreSQL)
pnpm nx clear-ports

# Clear all development ports including PostgreSQL
pnpm nx clear-ports --configuration=all

# Force clear all ports (no graceful shutdown)
pnpm nx clear-ports --configuration=force

# Clear only frontend port (4200)
pnpm nx clear-ports --configuration=frontend

# Clear only API ports (8000, 8001)
pnpm nx clear-ports --configuration=api

# Clear specific port
./scripts/nx/clear-ports.sh clear 8000

# Force clear specific port
./scripts/nx/clear-ports.sh clear 8000 --force
```

### Server Commands with Auto Port Clearing

```bash
# Frontend serving (auto-clears port 4200)
pnpm nx serve giki-ai-app

# API serving (auto-clears ports 8000, 8001)
pnpm nx serve giki-ai-api

# Serve both (auto-clears all required ports)
pnpm nx serve
```

---

## 🛠️ CONFIGURATION DETAILS

### NX Target Configurations

**Port Status Target:**
```json
"port-status": {
  "executor": "nx:run-commands",
  "options": {
    "command": "./scripts/nx/clear-ports.sh status"
  }
}
```

**Port Clearing Target:**
```json
"clear-ports": {
  "executor": "nx:run-commands",
  "options": {
    "command": "./scripts/nx/clear-ports.sh clear-dev-ports"
  },
  "configurations": {
    "force": {
      "command": "./scripts/nx/clear-ports.sh clear-dev-ports --force"
    },
    "all": {
      "command": "./scripts/nx/clear-ports.sh clear-dev-ports --include-postgres"
    },
    "frontend": {
      "command": "./scripts/nx/clear-ports.sh clear-frontend"
    },
    "api": {
      "command": "./scripts/nx/clear-ports.sh clear-api"
    }
  }
}
```

### Modified Serve Commands

**Frontend Serve (apps/giki-ai-app/project.json):**
```json
"serve": {
  "executor": "nx:run-commands",
  "options": {
    "command": "./scripts/nx/clear-ports.sh clear-frontend && ./scripts/nx/nx-log.sh serve app 'cd apps/giki-ai-app && pnpm vite dev --port 4200 --host 0.0.0.0'"
  }
}
```

**API Serve (apps/giki-ai-api/project.json):**
```json
"serve": {
  "executor": "nx:run-commands",
  "options": {
    "command": "./scripts/nx/clear-ports.sh clear-api && ./scripts/nx/nx-log.sh serve api 'cd apps/giki-ai-api && uv run uvicorn giki_ai_api.core.main:app --host 0.0.0.0 --port 8000 --reload'"
  }
}
```

---

## 🔧 SCRIPT IMPLEMENTATION

### Port Clearing Script: `scripts/nx/clear-ports.sh`

**Key Functions:**
- **`is_port_in_use()`**: Check if a port is currently in use
- **`get_port_pid()`**: Get the process ID using a specific port
- **`kill_port_process()`**: Gracefully terminate or force kill processes
- **`clear_port()`**: Clear a specific port with validation
- **`clear_all_dev_ports()`**: Clear all known development ports
- **`show_port_status()`**: Display status of all known ports

**Graceful Shutdown Process:**
1. Send SIGTERM signal to process
2. Wait up to 5 seconds for graceful shutdown
3. Force kill with SIGKILL if process doesn't respond
4. Verify port is actually freed

**Safety Features:**
- Port number validation (1-65535)
- PostgreSQL protection (excluded by default)
- Process verification before killing
- Detailed error reporting

---

## 🚀 USAGE EXAMPLES

### Daily Development Workflow

```bash
# 1. Check current port status
pnpm nx port-status

# 2. Clear any conflicting ports
pnpm nx clear-ports

# 3. Start development servers (ports auto-cleared)
pnpm nx serve

# 4. If you need to restart just the API
pnpm nx serve giki-ai-api
```

### Troubleshooting Port Conflicts

```bash
# Problem: "Port 8000 already in use"
# Solution 1: Auto-clear via serve command
pnpm nx serve giki-ai-api

# Solution 2: Manual port clearing
pnpm nx clear-ports --configuration=api

# Solution 3: Force clear if graceful fails
./scripts/nx/clear-ports.sh clear 8000 --force

# Solution 4: Check what's using the port
pnpm nx port-status
```

### CI/CD Integration

```bash
# In CI scripts, ensure clean environment
pnpm nx clear-ports --configuration=force
pnpm nx serve giki-ai-api --configuration=production &
pnpm nx serve giki-ai-app --configuration=production &
```

---

## 🔍 DEBUGGING AND MONITORING

### Process Manager Integration

The port clearing script is integrated with the process manager (`scripts/nx/process-manager.sh`):

```bash
# Process manager also supports port clearing
./scripts/nx/process-manager.sh kill-port 8000
./scripts/nx/process-manager.sh status
./scripts/nx/process-manager.sh cleanup
```

### Logging and Diagnostics

**Port clearing actions are logged:**
- Graceful shutdown attempts
- Force kill operations
- Port validation results
- Process information before termination

**View process manager help:**
```bash
./scripts/nx/process-manager.sh help
```

---

## ⚠️ IMPORTANT NOTES

### PostgreSQL Handling
- PostgreSQL (port 5432) is **excluded** from automatic clearing by default
- Use `--include-postgres` flag to include it in bulk operations
- PostgreSQL is typically a persistent service that shouldn't be killed

### Redis Handling
- Redis (port 6379) is only cleared if actually running locally
- Skipped if not in use to avoid unnecessary operations

### Production Considerations
- Port clearing is designed for development environments
- In production, use proper process managers (systemd, PM2, etc.)
- CI/CD pipelines should handle process lifecycle differently

### Safety Considerations
- Always attempts graceful shutdown first
- Force kill only when necessary or explicitly requested
- Validates port numbers to prevent invalid operations
- Provides detailed process information before termination

---

## 🔄 INTEGRATION WITH EXISTING WORKFLOW

The port management system is fully integrated with existing NX patterns:

1. **NX Logging**: All port operations logged through `nx-log.sh`
2. **Process Manager**: Coordinated with existing process management
3. **Configuration**: Follows NX configuration patterns
4. **Error Handling**: Consistent error reporting and exit codes
5. **Documentation**: Integrated with existing development guides

**No breaking changes to existing commands** - all previous functionality remains intact with added port clearing capabilities.

---

**NEXT UPDATE:** After testing in production CI/CD environments and gathering developer feedback.