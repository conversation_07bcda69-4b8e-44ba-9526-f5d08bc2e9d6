# AI Package Architecture - giki.ai Platform

**Version:** 1.0 | **Updated:** 2025-07-08  
**Purpose:** Define clear boundaries and usage patterns for AI packages  
**Status:** Current package versions and migration guidelines

---

## 🎯 Package Overview

### Current AI Package Stack

```toml
# From pyproject.toml - These are our CURRENT versions
"google-cloud-aiplatform>=1.100.0"  # Google Cloud AI Platform SDK
"vertexai>=1.43.0"                  # Vertex AI Generative Models SDK
"google-adk>=1.5.0"                 # Google Agent Development Kit
"a2a-sdk>=0.2.8"                    # Agent-to-Agent Communication SDK
```

### ⚠️ IMPORTANT: Package Deprecation Notice

As of June 24, 2025, the following modules in Vertex AI SDK are **DEPRECATED**:
- `vertexai.generative_models` ❌
- `vertexai.language_models` ❌
- `vertexai.vision_models` ❌
- `vertexai.tuning` ❌
- `vertexai.caching` ❌

**Migration Required**: Use Google Gen AI SDK for these features by June 24, 2026.

---

## 📦 Package Usage Guidelines

### 1. For Services (Vertex AI / Google Cloud AI Platform)

**Primary Package**: `google-cloud-aiplatform` + `vertexai`

**Used For**:
- Service-level AI operations
- Batch processing
- Model predictions
- Feature stores
- RAG (Retrieval Augmented Generation)
- Direct model interactions

**Current Implementation Pattern**:
```python
# In services like vertex_client.py
from google.cloud import aiplatform
from vertexai.generative_models import GenerativeModel, GenerationConfig

# For RAG operations
from google.cloud.aiplatform_v1beta1 import (
    VertexRagServiceAsyncClient,
    VertexRagDataServiceAsyncClient
)
```

**Key Service Files**:
- `shared/ai/vertex_client.py` - Main Vertex AI client
- `domains/categories/vertex_rag.py` - RAG implementation
- `shared/ai/unified_ai.py` - Unified AI interface

### 2. For Agents (Google ADK + A2A SDK)

**Primary Package**: `google-adk` (Agent Development Kit)

**Used For**:
- Building AI agents with tool capabilities
- Multi-agent orchestration
- Tool integration (search, memory, etc.)
- Agent-to-agent communication

**Current Implementation**:
```python
# In agents like categorization_agent.py
from google.adk.agents import Agent
from google.adk.tools import FunctionTool
from a2a import transfer_to_agent  # For multi-agent coordination
```

**Available but Underutilized ADK Tools**:
- `google_search` - Real-time web search
- `vertex_ai_search_tool` - Enterprise knowledge search
- `load_memory` / `preload_memory` - Persistent memory management
- `transfer_to_agent` - Multi-agent coordination
- `LongRunningFunctionTool` - Async operations
- `APIHubToolset` - API integration
- `openapi_tool` - OpenAPI spec integration

**Key Agent Files**:
- `shared/ai/adk_integration.py` - ADK integration patterns
- `shared/ai/standard_giki_agent.py` - Base agent class
- `domains/*/agent.py` - Domain-specific agents

---

## 🏗️ Architecture Patterns

### Service Layer (Vertex AI)
```
Service Request → Vertex AI Client → Model Prediction → Response
     ↓                    ↓                   ↓
 Batch Processing    RAG Integration    Streaming Support
```

### Agent Layer (ADK)
```
User Intent → ADK Agent → Tool Selection → Tool Execution → Response
                ↓              ↓                ↓
          Memory Context   Function Tools   External APIs
```

### Real-Time Processing Considerations

**For Batch Processing Visibility**:
1. Use Vertex AI streaming capabilities for real-time updates
2. Implement WebSocket connections for progress updates
3. Use async processing with status polling

**Current Gap**: The frontend shows progress bars but backend doesn't stream real-time categorization progress.

---

## 🚀 Migration Strategy

### Phase 1: Immediate Actions
1. **Update Import Warnings**: Add deprecation notices to all files using deprecated Vertex AI modules
2. **Identify Migration Targets**: List all files using deprecated modules
3. **Test Google Gen AI SDK**: Validate compatibility with our use cases

### Phase 2: Gradual Migration
1. **Create Adapter Layer**: Build abstraction to switch between old/new SDKs
2. **Update Non-Critical Paths**: Start with less critical features
3. **Parallel Testing**: Run both implementations side-by-side

### Phase 3: Complete Migration
1. **Update All Agents**: Migrate to new SDK patterns
2. **Update Documentation**: Ensure all docs reflect new patterns
3. **Remove Deprecated Code**: Clean up old implementations

---

## 📊 Current Usage Analysis

### Well-Utilized Features
- ✅ Basic Vertex AI model predictions
- ✅ Simple function tools in ADK
- ✅ RAG corpus management
- ✅ Batch categorization

### Underutilized Features
- ❌ ADK advanced tools (search, memory, APIs)
- ❌ Real-time streaming for progress updates
- ❌ Multi-agent coordination patterns
- ❌ Long-running async operations
- ❌ Vertex AI Feature Store for ML features

---

## 🔧 Implementation Guidelines

### For New Services
```python
# Use google-cloud-aiplatform for service operations
from google.cloud import aiplatform

# Initialize with project settings
aiplatform.init(
    project=settings.gcp_project_id,
    location=settings.gcp_location
)

# Use for batch operations, predictions, etc.
```

### For New Agents
```python
# Use google-adk for agent development
from google.adk.agents import Agent
from google.adk.tools import FunctionTool, google_search

# Create agents with rich tool capabilities
agent = Agent(
    name="financial_analyst",
    model="gemini-2.0-flash",
    tools=[custom_tool, google_search]
)
```

### For Real-Time Updates
```python
# Implement streaming for batch processing visibility
async def stream_categorization_progress(upload_id: str):
    """Stream real-time categorization progress to frontend."""
    # Use WebSocket or SSE for real-time updates
    # Update progress as each transaction is categorized
    pass
```

---

## 📋 Package Version Checks

To ensure we have the latest versions:

```bash
# Check current installed versions
pip list | grep -E "google-cloud-aiplatform|vertexai|google-adk|a2a-sdk"

# Update to latest versions
pip install --upgrade google-cloud-aiplatform vertexai google-adk a2a-sdk

# Verify compatibility
python -c "import vertexai; print(vertexai.__version__)"
```

---

## 🎯 Action Items

1. **Immediate**: Add deprecation warnings to code using deprecated Vertex AI modules
2. **Short-term**: Implement real-time progress streaming for batch operations
3. **Medium-term**: Utilize more ADK tools for enhanced agent capabilities
4. **Long-term**: Complete migration to Google Gen AI SDK before June 2026

---

**Last Updated**: 2025-07-08 | **Next Review**: When Google Gen AI SDK is stable