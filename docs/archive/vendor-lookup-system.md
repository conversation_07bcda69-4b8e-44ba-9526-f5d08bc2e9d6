# Context-Aware Vendor Lookup System

## Overview

The vendor lookup system intelligently identifies business types and suggests appropriate categories for recurring vendors/merchants in financial transactions. It addresses the key concerns:

1. **What to Google?** - Only high-value, low-confidence vendors
2. **Correct Context?** - Uses business context, transaction patterns, and validation

## How It Works

### 1. Vendor Detection
The system first identifies vendors that would benefit from lookup:
- **High transaction volume**: 10+ transactions
- **Low confidence**: <75% categorization confidence
- **No existing mapping**: Not already in the system
- **Multiple categories**: Inconsistent categorization

### 2. Context Building
For each vendor, the system builds comprehensive context:
```python
context = {
    "vendor_name": "Normalized name",
    "transaction_patterns": ["Original descriptions"],
    "avg_amount": 125.50,
    "transaction_count": 25,
    "business_industry": "User's industry",
    "existing_categories": ["Previous categorizations"]
}
```

### 3. Intelligent Search Query
The system builds contextual search queries:
- **Basic**: "what is [vendor] business type"
- **With context**: "what is [vendor] business type for [industry]"
- **With hints**: Add "enterprise" for large amounts, "retail" for small

Example queries:
- "what is AWS Services business type enterprise"
- "what is Starbucks business type retail"
- "what is Acme Consulting business type for manufacturing"

### 4. Multi-Layer Identification

#### Pattern Matching (First Layer)
Known vendors are identified immediately:
- Amazon → E-commerce/Retail
- Starbucks → Restaurant/Coffee
- Uber → Transportation
- Microsoft → Software/Subscription

#### Context Validation (Second Layer)
Search results are validated against transaction data:
- Restaurant with $500 average? → Reduce confidence
- Gas station with $1000 charge? → Flag as unusual
- Software with monthly pattern? → Boost confidence

#### Amount-Based Fallback (Third Layer)
If uncertain, use transaction amounts as hints:
- <$20 → Likely retail/supplies
- $20-100 → Likely restaurant/services
- $100-1000 → Likely professional services
- >$1000 → Likely software/subscriptions

### 5. Confidence Scoring
Each result gets a confidence score based on:
- Pattern match quality (0.9-0.95 for known vendors)
- Search result relevance (0.7-0.85 for good matches)
- Context validation (±20% based on validation)
- Transaction frequency (high frequency boosts confidence)

## API Endpoints

### Detect Vendors
```
GET /api/v1/categories/vendors/detect/{upload_id}
```
Returns vendors that would benefit from mapping.

### Apply Intelligent Mappings
```
POST /api/v1/categories/vendors/apply-intelligent-mappings
```
Automatically creates vendor mappings using context-aware lookup.

### Preview Lookup
```
GET /api/v1/categories/vendors/lookup-preview?vendor_name=VENDOR_NAME
```
Shows what the system would determine for a specific vendor.

## Benefits

1. **Accuracy**: Only searches for vendors that truly need identification
2. **Context**: Uses business context to improve search relevance
3. **Validation**: Ensures results make sense for the business
4. **Efficiency**: Caches results to avoid repeated lookups
5. **Transparency**: Shows reasoning and confidence scores

## Example Results

### High Confidence (Pattern Match)
```json
{
  "vendor_name": "Starbucks",
  "business_type": "restaurant",
  "confidence_score": 0.95,
  "suggested_category": "Expenses/Meals & Entertainment/Restaurants",
  "data_source": "pattern",
  "validation_reasons": [
    "Matched known vendor pattern: STARBUCKS",
    "Transaction volume: 45",
    "Average amount: $8.50"
  ]
}
```

### Medium Confidence (Validated Search)
```json
{
  "vendor_name": "Acme Consulting",
  "business_type": "professional_services",
  "confidence_score": 0.78,
  "suggested_category": "Expenses/Professional Services/Consulting",
  "data_source": "google",
  "search_query_used": "what is Acme Consulting business type enterprise",
  "validation_reasons": [
    "High average amount for professional services",
    "Monthly transaction pattern",
    "Search confidence: 0.70"
  ]
}
```

### Low Confidence (Fallback)
```json
{
  "vendor_name": "Unknown Vendor 123",
  "business_type": "unknown",
  "confidence_score": 0.50,
  "suggested_category": "Expenses/Other",
  "data_source": "pattern",
  "validation_reasons": [
    "Based on average amount: $75.00",
    "Transaction count: 5",
    "No confident match found"
  ]
}
```

## Security & Privacy

- Vendor names are normalized to remove sensitive data
- No actual Google API calls in the demo (simulated)
- Results are cached to minimize external lookups
- All lookups are tenant-specific and isolated