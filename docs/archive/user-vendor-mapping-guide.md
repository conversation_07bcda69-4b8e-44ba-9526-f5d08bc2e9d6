# User Vendor Mapping Guide

## Overview

The user vendor mapping system allows users to create permanent vendor-to-category mappings after initial categorization. This ensures consistent categorization for recurring vendors with awareness of transaction direction (credits vs debits).

## Key Features

### 1. Credit/Debit Awareness
The same vendor can be mapped differently based on transaction direction:
- **Amazon (Debit)** → Expenses/Office Supplies
- **Amazon (Credit)** → Income/Refunds
- **Stripe (Credit)** → Income/Payment Processing
- **Stripe (Debit)** → Expenses/Transaction Fees

### 2. Amount-Based Rules
Create different mappings based on transaction amounts:
```json
{
  "vendor_name": "AMAZON",
  "category_id": 123,  // Office Supplies
  "applies_to_debits": true,
  "max_amount": 100.00
}

{
  "vendor_name": "AMAZON",
  "category_id": 456,  // Equipment
  "applies_to_debits": true,
  "min_amount": 100.01
}
```

### 3. Integration with Bulk UI
The vendor groupings API integrates with your existing transaction grouping UI:

```
GET /api/v1/categories/vendors/groupings?upload_id=xxx
```

Returns grouped transactions showing:
- Vendor name and transaction count
- Debit vs credit breakdown
- Current categorization patterns
- Existing mappings if any

## API Workflow

### 1. Get Vendor Groupings
```bash
GET /api/v1/categories/vendors/groupings
```

Response:
```json
[
  {
    "vendor_name": "Starbucks",
    "normalized_name": "starbucks",
    "transaction_count": 45,
    "total_amount": -382.50,
    "debit_count": 45,
    "debit_total": -382.50,
    "credit_count": 0,
    "credit_total": 0.00,
    "categories_used": ["Expenses/Meals/Coffee", "Expenses/Other"],
    "most_common_category": "Expenses/Meals/Coffee",
    "has_mapping": false,
    "sample_transactions": [...]
  },
  {
    "vendor_name": "Amazon Web Services",
    "normalized_name": "amazon web services",
    "transaction_count": 12,
    "total_amount": -15000.00,
    "debit_count": 12,
    "debit_total": -15000.00,
    "credit_count": 0,
    "credit_total": 0.00,
    "categories_used": ["Expenses/Technology"],
    "has_mapping": true,
    "current_mapping": {
      "vendor_name": "amazon web services",
      "category_id": 789,
      "applies_to_debits": true,
      "applies_to_credits": false
    }
  }
]
```

### 2. Create Single Vendor Mapping
```bash
POST /api/v1/categories/vendors/user-mapping
```

Request:
```json
{
  "vendor_name": "STARBUCKS",
  "category_id": 234,
  "applies_to_debits": true,
  "applies_to_credits": false,
  "notes": "Coffee and meetings"
}
```

### 3. Create Bulk Mappings
```bash
POST /api/v1/categories/vendors/bulk-user-mappings
```

Request:
```json
{
  "mappings": [
    {
      "vendor_name": "UBER",
      "category_id": 345,
      "applies_to_debits": true,
      "applies_to_credits": false
    },
    {
      "vendor_name": "STRIPE",
      "category_id": 567,
      "applies_to_debits": false,
      "applies_to_credits": true
    }
  ],
  "apply_to_existing": true,
  "apply_to_future": true
}
```

## UI Integration Example

### Transaction Grouping View
```jsx
// Existing transaction grouping component
function TransactionGroupingView() {
  const [vendorGroups, setVendorGroups] = useState([]);
  const [selectedVendors, setSelectedVendors] = useState([]);
  
  // Fetch vendor groupings
  useEffect(() => {
    fetchVendorGroupings(uploadId).then(setVendorGroups);
  }, [uploadId]);
  
  // Show grouped transactions
  return (
    <div>
      {vendorGroups.map(group => (
        <VendorGroup key={group.vendor_name}>
          <VendorHeader>
            <h3>{group.vendor_name}</h3>
            <span>{group.transaction_count} transactions</span>
            <span>${Math.abs(group.total_amount)}</span>
          </VendorHeader>
          
          {/* Transaction direction info */}
          <DirectionInfo>
            {group.debit_count > 0 && (
              <span>📤 {group.debit_count} expenses</span>
            )}
            {group.credit_count > 0 && (
              <span>📥 {group.credit_count} income</span>
            )}
          </DirectionInfo>
          
          {/* Category selection with direction awareness */}
          <CategorySelector
            vendor={group}
            onSelect={(categoryId, direction) => {
              addVendorMapping({
                vendor_name: group.vendor_name,
                category_id: categoryId,
                applies_to_debits: direction === 'debit',
                applies_to_credits: direction === 'credit'
              });
            }}
          />
          
          {/* Show existing mapping if any */}
          {group.has_mapping && (
            <ExistingMapping mapping={group.current_mapping} />
          )}
        </VendorGroup>
      ))}
      
      {/* Bulk action button */}
      <BulkActionButton
        onClick={() => submitBulkMappings(selectedVendors)}
      >
        Save Vendor Mappings
      </BulkActionButton>
    </div>
  );
}
```

## Best Practices

### 1. Handle Mixed Vendors
Some vendors have both income and expenses:
- Payment processors (fees vs revenues)
- Marketplaces (purchases vs refunds)
- Service providers (charges vs credits)

Always check both debit_count and credit_count before creating mappings.

### 2. Use Descriptive Notes
Add notes to explain mapping logic:
```json
{
  "vendor_name": "AMAZON",
  "category_id": 123,
  "notes": "Small office supplies under $100"
}
```

### 3. Review Existing Patterns
Look at `categories_used` and `most_common_category` to understand current categorization before creating new mappings.

### 4. Amount Ranges for Complex Vendors
Use amount ranges for vendors with multiple expense types:
- Small amounts → Supplies
- Medium amounts → Services
- Large amounts → Equipment

## Database Schema

```sql
-- Enhanced vendor_category_mappings table
CREATE TABLE vendor_category_mappings (
    id SERIAL PRIMARY KEY,
    vendor_name VARCHAR(255) NOT NULL,
    normalized_name VARCHAR(255) NOT NULL,
    category_id INTEGER REFERENCES categories(id),
    tenant_id INTEGER REFERENCES tenants(id),
    
    -- Direction awareness
    applies_to_debits BOOLEAN DEFAULT true,
    applies_to_credits BOOLEAN DEFAULT false,
    
    -- Amount filtering
    min_amount DECIMAL(12, 2),
    max_amount DECIMAL(12, 2),
    
    -- Pattern matching
    description_pattern TEXT,
    
    -- User control
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    
    -- Usage tracking
    times_applied INTEGER DEFAULT 0,
    last_applied TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(vendor_name, tenant_id)
);
```

## Future Enhancements

1. **Conflict Resolution**: When same vendor has different categories for similar amounts
2. **Seasonal Rules**: Different mappings based on time of year
3. **Department-Based**: Different mappings per department/cost center
4. **Approval Workflow**: Require approval for certain mapping changes
5. **Smart Suggestions**: AI-powered mapping suggestions based on business type