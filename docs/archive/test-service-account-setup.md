# Test Service Account Setup Guide

## Overview
This guide explains how to set up a Google Cloud service account specifically for testing purposes in the giki.ai platform.

## Security Principles
- **NEVER** use production service accounts for testing
- **NEVER** commit service account keys to version control
- **ALWAYS** use dedicated test service accounts with limited permissions
- **ALWAYS** store service account paths in environment variables

## Creating a Test Service Account

### 1. Prerequisites
- Google Cloud Project access (e.g., `rezolve-poc`)
- `gcloud` CLI installed and configured
- Appropriate IAM permissions to create service accounts

### 2. Create the Service Account

```bash
# Set your project ID
export PROJECT_ID="rezolve-poc"

# Create test service account
gcloud iam service-accounts create giki-ai-test \
    --display-name="giki.ai Test Service Account" \
    --description="Service account for automated testing only" \
    --project=$PROJECT_ID
```

### 3. Grant Required Permissions

```bash
# Grant minimal required roles for testing
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:giki-ai-test@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:giki-ai-test@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectViewer"
```

### 4. Create and Download Service Account Key

```bash
# Create key file
gcloud iam service-accounts keys create test-service-account.json \
    --iam-account=giki-ai-test@$PROJECT_ID.iam.gserviceaccount.com

# Set appropriate permissions
chmod 600 test-service-account.json
```

## Environment Configuration

### 1. Copy Environment Template
```bash
cp .env.test.example .env.test
```

### 2. Update Test Service Account Path
Edit `.env.test` and set:
```bash
TEST_SERVICE_ACCOUNT_PATH=./test-service-account.json
```

### 3. Verify Configuration
```bash
# Source test environment
source .env.test

# Verify path exists
if [ -f "$TEST_SERVICE_ACCOUNT_PATH" ]; then
    echo "✅ Test service account found at: $TEST_SERVICE_ACCOUNT_PATH"
else
    echo "❌ Test service account not found!"
fi
```

## Using Test Service Account in Tests

### Python/FastAPI Tests
The test configuration automatically loads the service account from the environment:

```python
# In conftest.py
service_account_path = os.getenv("TEST_SERVICE_ACCOUNT_PATH")
if service_account_path and os.path.exists(service_account_path):
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = service_account_path
```

### E2E/Playwright Tests
Set the environment variable before running tests:

```bash
export TEST_SERVICE_ACCOUNT_PATH=./test-service-account.json
pnpm nx test:e2e
```

## CI/CD Configuration

### GitHub Actions Example
```yaml
- name: Set up test service account
  env:
    TEST_SERVICE_ACCOUNT_KEY: ${{ secrets.TEST_SERVICE_ACCOUNT_KEY }}
  run: |
    echo "$TEST_SERVICE_ACCOUNT_KEY" | base64 -d > test-service-account.json
    echo "TEST_SERVICE_ACCOUNT_PATH=./test-service-account.json" >> $GITHUB_ENV
```

### GitLab CI Example
```yaml
before_script:
  - echo "$TEST_SERVICE_ACCOUNT_KEY" | base64 -d > test-service-account.json
  - export TEST_SERVICE_ACCOUNT_PATH=./test-service-account.json
```

## Security Best Practices

### 1. Git Ignore
Ensure service account files are in `.gitignore`:
```gitignore
# Service account keys
*-service-account.json
test-service-account.json
dev-service-account.json
```

### 2. Regular Key Rotation
```bash
# List existing keys
gcloud iam service-accounts keys list \
    --iam-account=giki-ai-test@$PROJECT_ID.iam.gserviceaccount.com

# Delete old keys
gcloud iam service-accounts keys delete KEY_ID \
    --iam-account=giki-ai-test@$PROJECT_ID.iam.gserviceaccount.com
```

### 3. Least Privilege
Only grant the minimum roles required for testing:
- `roles/aiplatform.user` - For AI model inference
- `roles/storage.objectViewer` - For reading test data
- Additional roles only as needed

## Troubleshooting

### Service Account Not Found
```bash
# Check if file exists
ls -la test-service-account.json

# Verify permissions
stat -c "%a" test-service-account.json  # Should be 600
```

### Authentication Errors
```bash
# Test authentication
export GOOGLE_APPLICATION_CREDENTIALS=./test-service-account.json
gcloud auth application-default print-access-token
```

### Permission Denied
```bash
# Check service account roles
gcloud projects get-iam-policy $PROJECT_ID \
    --flatten="bindings[].members" \
    --filter="bindings.members:serviceAccount:giki-ai-test@$PROJECT_ID.iam.gserviceaccount.com"
```

## Clean Up

When no longer needed:
```bash
# Delete service account
gcloud iam service-accounts delete giki-ai-test@$PROJECT_ID.iam.gserviceaccount.com

# Remove local key file
rm -f test-service-account.json
```