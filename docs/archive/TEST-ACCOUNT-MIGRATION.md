# Test Account Migration Guide

**Date:** 2025-01-05  
**Purpose:** Migration from milestone-based to role-based test accounts

## Overview

As part of the MIS-first transformation, we're migrating from milestone-based test accounts (M1/M2/M3) to role-based accounts that better reflect real-world usage patterns.

## Account Migration Map

### Old Accounts → New Accounts

| Old Account | New Account | Role | Access Level |
|------------|-------------|------|--------------|
| <EMAIL> | <EMAIL> | Business Owner | Full MIS management and configuration |
| <EMAIL> | <EMAIL> | Accountant | Category management and review |
| <EMAIL> | <EMAIL> | Bookkeeper | Transaction entry and categorization |
| <EMAIL> | <EMAIL> | Administrator | Full system administration |
| - | <EMAIL> | Viewer | Read-only access to reports and dashboards |

## Migration Steps

### 1. Database Migration

Run the role-based account setup script:

```bash
cd apps/giki-ai-api
python scripts/setup_role_based_accounts.py
```

This script will:
- Create the new role-based test accounts
- Maintain the same password (GikiTest2025Secure)
- Ensure proper tenant association
- Preserve any existing data

### 2. Code Updates (Completed)

The following files have been updated to use the new accounts:
- ✅ `apps/giki-ai-api/src/giki_ai_api/core/main.py`
- ✅ `apps/giki-ai-api/tests/integration/test_complete_ui_workflow_integration.py`
- ✅ `apps/giki-ai-api/tests/integration/test_business_performance.py`
- ✅ `apps/giki-ai-app/src/test-utils.tsx`
- ✅ `docs/01-CURRENT-STATUS.md`
- ✅ `docs/07-TESTING-STRATEGY.md`

### 3. Documentation Updates (Completed)

All documentation has been updated to reflect the new role-based accounts:
- ✅ Current Status documentation
- ✅ Testing Strategy documentation
- ✅ README documentation
- ✅ Command documentation (.claude/commands/)

## Testing the New Accounts

### Local Development Testing

```bash
# Test authentication with new accounts
curl -X POST "http://localhost:8000/api/v1/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=GikiTest2025Secure"
```

### Integration Testing

```bash
# Run integration tests with new accounts
cd apps/giki-ai-api
uv run pytest tests/integration/test_customer_journeys.py -v
```

## Benefits of Role-Based Accounts

1. **Clear Role Definition**: Each account represents a specific business role
2. **Realistic Testing**: Test permissions and workflows as different user types
3. **Better Documentation**: Self-documenting account names
4. **Scalability**: Easy to add new roles as needed
5. **Security**: Role-based access control implementation ready

## Rollback Plan

If issues arise, the old accounts can coexist with the new ones temporarily. The database schema supports multiple users per tenant, so both sets of accounts can be active during transition.

## Next Steps

1. Run the account setup script in development environment
2. Validate all test suites pass with new accounts
3. Update any CI/CD configurations that reference old accounts
4. Deploy to production with new accounts
5. Monitor for any authentication issues
6. Deprecate old accounts after successful transition

---

**Note**: All test accounts use the same secure password: `GikiTest2025Secure`