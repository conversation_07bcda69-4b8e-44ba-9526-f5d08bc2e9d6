# Test Configuration Guide

## Overview
This guide explains the comprehensive test configuration system for giki.ai, which supports environment-based testing across all components.

## Quick Start

### 1. Copy Environment Template
```bash
cp .env.test.example .env.test
```

### 2. Configure Test Database
```bash
# Set your test database URL
export TEST_DATABASE_URL="postgresql://test_user:password@localhost:5432/giki_ai_test"

# Setup test database
./scripts/setup-test-db.sh
```

### 3. Configure Service Account
```bash
# Copy your test service account
cp /path/to/test-service-account.json ./test-service-account.json

# Update .env.test
echo "TEST_SERVICE_ACCOUNT_PATH=./test-service-account.json" >> .env.test
```

### 4. Run Tests
```bash
# Backend tests
pnpm nx test:api

# Frontend tests  
pnpm nx test:app

# E2E tests
pnpm nx test:e2e
```

## Environment Variables

### Core Configuration
All test configuration is managed through environment variables defined in `.env.test`:

| Variable | Description | Example |
|----------|-------------|---------|
| `TEST_DATABASE_URL` | PostgreSQL test database connection | `postgresql://user:pass@localhost:5432/test_db` |
| `TEST_SERVICE_ACCOUNT_PATH` | Path to Google Cloud service account | `./test-service-account.json` |
| `TEST_API_BASE_URL` | API base URL for tests | `http://localhost:8000` |
| `VITE_API_BASE_URL` | Frontend API URL | `http://localhost:8000` |

### Test User Configuration
| Variable | Description | Default |
|----------|-------------|---------|
| `TEST_USER_EMAIL_PREFIX` | Prefix for test emails | `test` |
| `TEST_USER_DOMAIN` | Domain for test emails | `giki.ai` |
| `TEST_USER_PASSWORD` | Password for test users | (Generated if not set) |

### Performance Thresholds
| Variable | Description | Default |
|----------|-------------|---------|
| `TEST_PAGE_LOAD_THRESHOLD_MS` | Max page load time | `2000` |
| `TEST_API_RESPONSE_THRESHOLD_MS` | Max API response time | `500` |
| `TEST_MIS_BASELINE_ACCURACY` | MIS baseline accuracy target | `0.87` |
| `TEST_MIS_ENHANCED_ACCURACY` | MIS enhanced accuracy target | `0.95` |
| `TEST_MIS_OPTIMAL_ACCURACY` | MIS optimal accuracy target | `0.98` |

## Test Database Management

### Setup Test Database
```bash
# Create and migrate test database
./scripts/setup-test-db.sh

# This will:
# 1. Create test database if it doesn't exist
# 2. Run all migrations
# 3. Create test data directories
# 4. Load initial test data (if available)
```

### Reset Test Database
```bash
# Clean all test data
./scripts/reset-test-db.sh

# This will:
# 1. Truncate all tables (except migrations)
# 2. Reset sequences
# 3. Clean test artifacts
# 4. Reload initial test data
```

### Safety Features
- Database name must contain "test" for safety
- Only test data (created within 1 hour) is cleaned
- Production database URLs are rejected

## Service Account Setup

### Create Test Service Account
See [Test Service Account Setup Guide](./test-service-account-setup.md) for detailed instructions.

### Key Security Rules
1. **NEVER** use production service accounts for testing
2. **NEVER** commit service account keys to git
3. **ALWAYS** use environment variables for paths
4. **ALWAYS** set file permissions to 600

## Test Organization

### Directory Structure
```
testing/
├── data/                 # Test data files
│   └── milestones/      # Milestone-specific test data
├── results/             # Test run outputs
│   ├── coverage/        # Coverage reports
│   └── *.log           # Test logs
└── artifacts/           # Screenshots, videos, traces
```

### Test File Locations
```
apps/
├── giki-ai-api/
│   └── tests/
│       ├── unit/        # Unit tests
│       ├── integration/ # Integration tests
│       └── conftest.py  # Pytest configuration
├── giki-ai-app/
│   └── src/**/__tests__/ # Component tests
└── giki-ai-e2e/
    └── src/             # E2E tests
```

## Running Tests

### Backend Tests (pytest)
```bash
# All tests
pnpm nx test:api

# Specific test file
cd apps/giki-ai-api
uv run pytest tests/unit/test_auth.py -v

# With coverage
uv run pytest --cov=src/giki_ai_api --cov-report=html

# Specific markers
uv run pytest -m "unit" -v
uv run pytest -m "integration" -v
```

### Frontend Tests (vitest)
```bash
# All tests
pnpm nx test:app

# Watch mode
pnpm nx test:app -- --watch

# With coverage
pnpm nx test:app -- --coverage
```

### E2E Tests (Playwright)
```bash
# All E2E tests
pnpm nx test:e2e

# Interactive mode
pnpm exec playwright test --ui

# Specific browser
pnpm exec playwright test --project=chromium

# Debug mode
pnpm exec playwright test --debug
```

## CI/CD Integration

### GitHub Actions
```yaml
jobs:
  test:
    steps:
      - name: Setup test environment
        run: |
          cp .env.test.example .env.test
          echo "TEST_DATABASE_URL=${{ secrets.TEST_DATABASE_URL }}" >> .env.test
          echo "${{ secrets.TEST_SERVICE_ACCOUNT }}" | base64 -d > test-service-account.json
          echo "TEST_SERVICE_ACCOUNT_PATH=./test-service-account.json" >> .env.test
      
      - name: Setup test database
        run: ./scripts/setup-test-db.sh
      
      - name: Run tests
        run: |
          pnpm nx test:api
          pnpm nx test:app
          pnpm nx test:e2e
```

### Local Development
```bash
# Load test environment
source .env.test

# Verify configuration
pnpm nx test:api -- tests/unit/test_environment_config.py -v
```

## Troubleshooting

### Database Connection Issues
```bash
# Test connection
psql $TEST_DATABASE_URL -c "SELECT 1"

# Check if database exists
psql -h localhost -U postgres -lqt | cut -d \| -f 1 | grep -qw giki_ai_test
```

### Service Account Issues
```bash
# Verify file exists and has correct permissions
ls -la test-service-account.json

# Test authentication
export GOOGLE_APPLICATION_CREDENTIALS=./test-service-account.json
gcloud auth application-default print-access-token
```

### Test Data Issues
```bash
# Verify test data directories
ls -la testing/data/
ls -la testing/results/
ls -la testing/artifacts/

# Check test data files
find testing/data -name "*.xlsx" -type f
```

## Best Practices

1. **Environment Isolation**
   - Always use `.env.test` for test configuration
   - Never mix test and production configurations
   - Use TEST_ prefix for all test environment variables

2. **Data Management**
   - Reset database before integration test runs
   - Use transaction rollback for unit tests
   - Clean up test artifacts regularly

3. **Security**
   - Rotate test service account keys regularly
   - Use minimal permissions for test accounts
   - Never expose test credentials in logs

4. **Performance**
   - Run unit tests in parallel
   - Use test database on same machine
   - Cache dependencies in CI/CD

5. **Debugging**
   - Use descriptive test names
   - Capture screenshots/videos on failure
   - Log test configuration at startup