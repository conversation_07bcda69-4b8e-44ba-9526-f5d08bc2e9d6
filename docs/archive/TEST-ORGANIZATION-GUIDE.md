# Test Organization Guide - giki.ai MIS Platform

**Updated:** 2025-07-05  
**Purpose:** Comprehensive guide for the new centralized test organization  
**Scope:** MIS-focused testing with consolidated test data management

---

## 📋 OVERVIEW

This guide documents the updated test organization for the giki.ai MIS platform, reflecting the migration from M1/M2/M3 terminology to MIS-focused structure with centralized test data management.

### Key Changes (2025-07-05)
1. **Terminology Update**: M1/M2/M3 → MIS Quick Setup/Historical Enhancement/Schema Enhancement
2. **Centralized Test Data**: All test data moved to `libs/test-data/` with organized structure
3. **Environment Variables**: Test credentials now use secure environment configuration
4. **Role-Based Testing**: Updated test accounts for business roles
5. **Consolidated Documentation**: Single source of truth for test organization

---

## 🏗️ TEST STRUCTURE

### Centralized Test Data Organization
```
libs/test-data/
├── mis-quick-setup/              # 5-minute MIS setup validation
│   ├── nuvie_expense_ledger.xlsx    # Primary test file
│   ├── M1_Nuvie_Retail_Demo.xlsx   # Demo file
│   ├── accuracy_criteria.yaml      # Validation rules
│   └── exports/                     # Export validation files
├── mis-historical-enhancement/   # Historical learning validation (30-45 min)
│   ├── Capital One.xlsx            # Bank statements
│   ├── Credit Card.xlsx            # Credit card data
│   ├── ICICI.xlsx                  # International banking
│   ├── SVB.xlsx                    # Business accounts
│   ├── M2_Rezolve_Consultancy_Demo.xlsx
│   └── improvement_criteria.yaml   # Enhancement rules
├── mis-schema-enhancement/       # GL compliance validation (30 min)
│   ├── M3_Giki_Manufacturing_Demo.xlsx
│   └── compliance_rules.yaml      # GL compliance rules
└── common/                       # Shared test data
    ├── DEMO_*.xlsx                # Demo files
    ├── comprehensive_test_data.xlsx
    ├── company_setup_template.json
    ├── test_credentials_template.json
    └── sample_transactions.csv
```

### Test File Placement Rules
```typescript
interface TestFilePlacement {
  // Backend Tests (Python/FastAPI)
  backendTests: {
    unit: "apps/giki-ai-api/tests/unit/*.py",
    integration: "apps/giki-ai-api/tests/integration/*.py",
    misSetup: "apps/giki-ai-api/tests/integration/test_mis_setup.py",
    conftest: "apps/giki-ai-api/tests/conftest.py"
  },
  
  // Frontend Tests (React/TypeScript)
  frontendTests: {
    components: "apps/giki-ai-app/src/**/__tests__/*.{ts,tsx}",
    e2e: "apps/giki-ai-e2e/src/*.spec.ts",
    misWorkflows: "apps/giki-ai-e2e/src/mis-workflows/*.spec.ts"
  },
  
  // Test Data (Centralized)
  testData: {
    misData: "libs/test-data/mis-*/",
    commonData: "libs/test-data/common/",
    testResults: "testing/results/",
    testArtifacts: "testing/artifacts/"
  },
  
  // PROHIBITED Locations
  prohibited: [
    "./test_*.py",           // NO test files in workspace root
    "./*_test.py",           // NO test files in workspace root
    "./test_*.json",         // NO test results in workspace root
    "./check_*.py"           // NO check scripts in workspace root
  ]
}
```

---

## ⚙️ ENVIRONMENT CONFIGURATION

### Test Environment Setup
```bash
# File: apps/giki-ai-api/.env.test.example

# Test Database Configuration
DATABASE_URL="postgresql://test_user:test_pass@localhost:5432/giki_ai_test"

# Test User Credentials (Environment Variables)
TEST_USER_PASSWORD="${TEST_USER_PASSWORD:-SecureTestPass2025!}"

# Test Service Account
GOOGLE_APPLICATION_CREDENTIALS="./dev-service-account.json"
VERTEX_AI_PROJECT_ID="rezolve-poc"
VERTEX_AI_LOCATION="us-central1"

# Test Data Paths
TEST_DATA_ROOT="libs/test-data"
MIS_QUICK_SETUP_DATA="libs/test-data/mis-quick-setup"
MIS_HISTORICAL_DATA="libs/test-data/mis-historical-enhancement"
MIS_SCHEMA_DATA="libs/test-data/mis-schema-enhancement"
```

### Role-Based Test Accounts
```typescript
interface TestCredentials {
  // Business Owner Role
  businessOwner: {
    email: "<EMAIL>",
    role: "owner",
    purpose: "Full MIS access and management"
  },
  
  // Accountant Role  
  accountant: {
    email: "<EMAIL>",
    role: "accountant",
    purpose: "Financial reporting and analysis"
  },
  
  // Bookkeeper Role
  bookkeeper: {
    email: "<EMAIL>",
    role: "bookkeeper",
    purpose: "Transaction processing and categorization"
  },
  
  // Viewer Role
  viewer: {
    email: "<EMAIL>",
    role: "viewer",
    purpose: "Read-only access to reports"
  }
}
```

---

## 🧪 TEST EXECUTION

### MIS Testing Commands
```bash
# MIS Quick Setup Testing (5-minute validation)
cd apps/giki-ai-api
uv run pytest tests/integration/test_mis_setup.py::test_owner_complete_setup -v
uv run pytest tests/integration/test_mis_setup.py -k "quick_setup" -v

# MIS Enhancement Testing (Progressive accuracy improvements)
uv run pytest tests/integration/test_mis_setup.py -k "enhancement" -v
uv run pytest tests/integration/test_mis_setup.py -k "historical" -v
uv run pytest tests/integration/test_mis_setup.py -k "schema" -v

# Role-Based Access Testing
uv run pytest tests/integration/test_auth.py::test_owner_authentication -v
uv run pytest tests/integration/test_auth.py::test_accountant_access -v
uv run pytest tests/integration/test_auth.py::test_bookkeeper_access -v
uv run pytest tests/integration/test_auth.py::test_viewer_access -v

# MIS Accuracy Validation Testing
uv run pytest tests/integration/test_mis_setup.py -k "accuracy" -v
```

### Frontend MIS Testing
```bash
# Component Tests
pnpm nx test giki-ai-app --testNamePattern="MIS"

# E2E Workflows
pnpm nx test:e2e --grep="MIS workflow"

# Specific MIS Components
pnpm nx test giki-ai-app --testNamePattern="Upload|Dashboard|Categories"
```

---

## 📊 TEST DATA USAGE

### Python Test Data Access
```python
from pathlib import Path

# Centralized test data paths
TEST_DATA_ROOT = Path("libs/test-data")
MIS_QUICK_SETUP = TEST_DATA_ROOT / "mis-quick-setup/nuvie_expense_ledger.xlsx"
MIS_HISTORICAL = TEST_DATA_ROOT / "mis-historical-enhancement/Capital One.xlsx"
MIS_SCHEMA = TEST_DATA_ROOT / "mis-schema-enhancement/M3_Giki_Manufacturing_Demo.xlsx"

# Test execution example
def test_mis_quick_setup():
    """Test MIS Quick Setup with centralized test data"""
    with open(MIS_QUICK_SETUP, 'rb') as f:
        result = process_mis_setup(f)
        assert result.accuracy >= 0.87
        assert result.setup_time <= 300  # 5 minutes
```

### TypeScript Test Data Access
```typescript
import { join } from 'path';

// Centralized test data paths
const TEST_DATA_ROOT = join(__dirname, '../../../libs/test-data');
const MIS_QUICK_SETUP = join(TEST_DATA_ROOT, 'mis-quick-setup/nuvie_expense_ledger.xlsx');

// Component testing example
describe('MIS Upload Component', () => {
  it('should handle MIS quick setup file upload', async () => {
    const file = new File([await readFile(MIS_QUICK_SETUP)], 'test.xlsx');
    const result = await uploadFile(file);
    expect(result.misSetupCompleted).toBe(true);
  });
});
```

---

## 🔧 TROUBLESHOOTING

### Common Issues and Solutions

#### Test Data Files Not Found
```bash
# Verify centralized test data structure
ls -la libs/test-data/mis-quick-setup/
ls -la libs/test-data/mis-historical-enhancement/
ls -la libs/test-data/mis-schema-enhancement/
```

#### Environment Variables Not Set
```bash
# Copy and configure test environment
cp apps/giki-ai-api/.env.test.example apps/giki-ai-api/.env.test
# Edit .env.test with appropriate values
```

#### Test Authentication Failures
```bash
# Verify role-based test accounts
cd apps/giki-ai-api
uv run pytest tests/integration/test_auth.py::test_owner_authentication -v
uv run pytest tests/integration/test_auth.py::test_accountant_authentication -v
```

#### MIS Accuracy Below Threshold
```bash
# Check test data quality and AI service configuration
python -c "from libs.test_data.validator import MISTestDataValidator; MISTestDataValidator().validate_mis_test_files()"
```

#### Database Connection Errors
```bash
# Ensure test database is properly configured
psql -h localhost -U test_user -d giki_ai_test -c "SELECT version();"
```

#### Service Account Authentication
```bash
# Verify development service account
ls -la dev-service-account.json
export GOOGLE_APPLICATION_CREDENTIALS="./dev-service-account.json"
```

---

## 📈 PERFORMANCE BENCHMARKS

### MIS Testing Performance Targets
```yaml
QuickSetup:
  setup_time: "< 5 minutes"
  accuracy_threshold: "87%"
  file_processing: "< 30 seconds"
  
HistoricalEnhancement:
  processing_time: "< 45 minutes"
  accuracy_improvement: "+15-20%"
  files_processed: "4+ historical files"
  
SchemaEnhancement:
  processing_time: "< 30 minutes"
  accuracy_improvement: "+20%"
  gl_compliance: "100%"
  
RoleBasedAccess:
  login_time: "< 2 seconds"
  role_validation: "100% accurate"
  data_isolation: "100% enforced"
```

---

## 🔄 MIGRATION SUMMARY

### Completed Migrations (2025-07-05)
- ✅ Test data centralized in `libs/test-data/`
- ✅ Terminology updated from M1/M2/M3 to MIS structure
- ✅ Environment variable configuration implemented
- ✅ Role-based test accounts established
- ✅ Documentation consolidated and updated
- ✅ Test commands updated for MIS workflows
- ✅ Troubleshooting guide created

### Anti-Fragmentation Compliance
- ✅ Single source of truth for test data
- ✅ No duplicate test files in workspace root
- ✅ Consolidated test organization
- ✅ Enhanced existing files rather than creating new versions

---

## 📚 RELATED DOCUMENTATION

- **Primary Testing Strategy**: `docs/07-TESTING-STRATEGY.md`
- **Development Patterns**: `.claude/memory/consolidated-development-patterns.md`
- **Current Status**: `docs/01-CURRENT-STATUS.md`
- **System Architecture**: `docs/02-SYSTEM-ARCHITECTURE.md`
- **Test Data Structure**: `libs/test-data/README.md`

---

**PRINCIPLE**: The giki.ai MIS platform uses centralized test data management with MIS-focused terminology to ensure comprehensive validation of the complete Management Information System with progressive enhancement capabilities.