# Session Monitoring Guide for CEO Workflow

## Overview
This guide helps monitor the automatic session refresh system that prevents unexpected logouts during extended workflows.

## Key Session Timings
- **Access Token Lifetime**: 30 minutes
- **Refresh Token Lifetime**: 7 days  
- **Automatic Refresh**: Triggers 10 minutes before token expiry (at the 20-minute mark)
- **Manual Refresh**: Triggered on any 401 authentication error

## How to Monitor Session Health

### 1. Open Browser Developer Console
- **Chrome/Edge**: Press `F12` or right-click → "Inspect" → "Console" tab
- **Firefox**: Press `F12` or right-click → "Inspect Element" → "Console" tab
- **Safari**: Enable Developer menu in Preferences → Advanced, then press `Cmd+Opt+C`

### 2. Key Log Messages to Watch

#### ✅ Healthy Session Indicators
- `🚀 Session recovery initialized - scheduling token refresh` - System started successfully
- `📅 Scheduling token refresh` - Next refresh scheduled (shows exact time)
- `⏰ Executing scheduled token refresh` - Refresh is happening now
- `🔄 Starting automatic token refresh` - Refresh process begun
- `✅ Token refresh successful` - New token received, session extended

#### ⚠️ Warning Signs
- `⚠️ Token expires soon, refreshing immediately` - Token about to expire
- `🚨 401 Authentication error detected` - Session expired, attempting recovery
- `⚠️ Token already expired` - Session lost, manual login needed

#### ❌ Error Indicators
- `❌ Scheduled token refresh failed` - Automatic refresh failed
- `❌ Auth error token refresh failed` - Recovery attempt failed
- `Session Expired` toast notification - Must log in again

### 3. Session Timeline Example

For a typical 30-minute workflow:
```
0 min    - Login successful
0 min    - 🚀 Session recovery initialized
0 min    - 📅 Scheduling token refresh (for 20 min mark)
20 min   - ⏰ Executing scheduled token refresh
20 min   - 🔄 Starting automatic token refresh
20 min   - ✅ Token refresh successful
20 min   - 📅 Scheduling token refresh (for 40 min mark)
40 min   - ⏰ Executing scheduled token refresh
...continues every 20 minutes...
```

## Troubleshooting

### Session Expires Unexpectedly
1. Check console for error messages
2. Look for network connectivity issues
3. Verify backend API is responsive
4. Check if multiple tabs are open (can interfere with refresh)

### Frequent Re-authentication Required
1. Clear browser cache and cookies
2. Check for browser extensions blocking requests
3. Ensure stable internet connection
4. Contact technical support if issue persists

### Debug Information
To see detailed session information in console:
1. Type `localStorage.getItem('accessToken')` - Shows current token
2. Type `localStorage.getItem('refreshToken')` - Shows refresh token
3. Look for `minutesUntilRefresh` in logs - Shows time until next refresh

## Best Practices for Long Workflows

1. **Keep One Tab Open**: Multiple tabs can interfere with token refresh
2. **Stable Connection**: Ensure reliable internet for refresh requests
3. **Regular Saves**: Save work periodically in case of unexpected issues
4. **Monitor Console**: Keep developer console open during critical workflows
5. **Browser Choice**: Use modern browsers (Chrome, Firefox, Safari latest versions)

## Technical Details

The session management system:
- Automatically refreshes tokens 10 minutes before expiry
- Handles 401 errors by attempting immediate refresh
- Maintains session across page refreshes
- Logs all operations with clear emoji indicators
- Falls back to login page only when refresh fails

## Contact Support

If you experience persistent session issues:
1. Save console logs (right-click in console → "Save as...")
2. Note the time when issues occurred
3. Contact technical team with logs and description

---

**Last Updated**: 2025-07-05
**System Version**: 1.0.0