# Specification: Claude Codebase Index

## 1. Overview

This document specifies the format and generation process for a codebase index file. The purpose of this index is to provide the Claude AI with a persistent, high-level overview of the entire repository, including source code, tests, and documentation. This helps the AI maintain context, understand the project structure, and locate relevant files more efficiently.

## 2. File Format and Location

- **File Name**: `code_index.md`
- **Location**: `.claude/memory/`
- **Format**: Markdown

Markdown is chosen for its human-readability and because it is a format that Large Language Models (LLMs) like <PERSON> can easily parse and understand as part of a prompt.

## 3. Index Structure

The `code_index.md` file will contain a markdown representation of the file tree. Each entry will represent a file or directory. For files, a brief summary or list of key definitions will be included on the same line.

### File Tree Format

The index will be structured as a nested Markdown list to represent the directory structure.

- Directories will be represented as list items ending with a `/`.
- Files will be represented as list items containing the filename, followed by a colon and a brief, one-line summary or a list of key definitions.

### Example:

```markdown
- `src/`
  - `components/`
    - `Button.tsx`: `Button` component for user interactions.
    - `Modal.tsx`: `Modal` component for displaying dialogs.
  - `services/`
    - `api.ts`: `fetchData()`, `postData()`
    - `authentication.ts`: `AuthenticationService`, `registerUser()`, `signIn()`
  - `main.ts`: Application entry point.
- `docs/`
  - `README.md`: Project overview and setup instructions.
- `.gitignore`: Specifies intentionally untracked files to ignore.
```

This format provides a compact, scannable overview of the codebase structure and key contents.

## 4. Generation Process & Filtering

The index will be generated and updated by an automated script. This script must be designed for precision, ensuring the index remains concise and relevant.

### 4.1. File Scanning and Filtering

1.  **Scanning**: The script will recursively scan the entire workspace directory.
2.  **Filtering with `.codeignore`**: To ensure the index is not cluttered with irrelevant files, the script will use a dedicated `.codeignore` file located in the root of the repository. This file will explicitly list files, directories, and patterns to exclude from the index.
    - The `.codeignore` file will follow the same syntax as `.gitignore`.
    - It should be seeded with sensible defaults, ignoring common artifacts like `node_modules`, `dist`, `build`, lockfiles (`pnpm-lock.yaml`, `uv.lock`), logs, and local configuration files.
3.  **Default Ignore List**: The script should have a default, hardcoded list of patterns to ignore, such as `.git/`, `.DS_Store`, and the `.claude/memory/` directory itself to prevent self-referencing.

### 4.2. Content Processing

1.  **Type Classification**: For each included file, determine its `Type` (`code`, `test`, `doc`, `config`) based on its path and extension. For example:
    - `tests/`, `*.test.ts`, `*.spec.ts` -> `test`
    - `docs/`, `*.md` -> `doc`
    - `*.json`, `*.yaml`, `*.toml` -> `config`
    - `src/`, `apps/` -> `code`
2.  **Content Summarization**: Generate a `Summary` for each file. This can be derived from existing comments (e.g., JSDoc, Python docstrings) or by using an LLM to summarize the file's content if no comments are available.
3.  **Key Definition Extraction**: Extract `Key Definitions` using Abstract Syntax Trees (ASTs) for supported languages. This is more reliable than regular expressions. For unsupported file types, this step can be skipped.

### 4.3. Index Generation

1.  **Writing the Index**: The script will write the collected information into `.claude/memory/code_index.md`, overwriting the previous version to ensure it is always up-to-date.

## 5. Integration with CLAUDE.md

The `CLAUDE.md` file, which likely serves as a root prompt or context file for the AI, must be updated to import the index. This is done by adding the following line at an appropriate place in the file, usually near the beginning:

```markdown
Here is a comprehensive index of the codebase. Use it to understand the project structure and find relevant files.

@{./.claude/memory/code_index.md}
```

This ensures that the AI always has the latest version of the codebase index as part of its context.