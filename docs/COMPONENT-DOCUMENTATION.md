# Component Documentation - giki.ai Design System

## Overview

This document provides comprehensive documentation for the giki.ai design system components, including usage examples, design tokens, and best practices for maintaining visual consistency.

## Design System Principles

### Professional B2B Design
- **Clean & Minimal**: Focus on content and functionality
- **Consistent Branding**: #295343 primary green throughout
- **Geometric Icons**: Only use □⊞∷⚬↑ (no emojis)
- **Touch-Friendly**: 44px minimum touch targets
- **Responsive-First**: Mobile-first design approach with comprehensive mobile navigation
- **Cross-Device**: Optimized for mobile (375px), tablet (768px), desktop (1024px+)

### Brand Colors
- **Primary**: `#295343` (`--giki-primary`)
- **Success**: `#10B981` (`--giki-success`)
- **Error**: `hsl(var(--giki-destructive))`
- **Warning**: `hsl(var(--giki-warning))`

---

## Core Components

### Button Components

#### Basic Button
```tsx
import { Button } from '@/shared/components/ui/button';

// Professional button with design tokens
<Button className="btn-professional">
  Standard Action
</Button>

// Touch-friendly button for mobile
<Button className="btn-professional btn-touch-friendly">
  Mobile Action
</Button>

// Brand primary button
<Button className="btn-professional bg-brand-primary text-white">
  Primary Action
</Button>
```

#### Button Classes Reference
- `btn-professional` - Standard professional styling
- `btn-touch-friendly` - 44px minimum touch target
- `bg-brand-primary` - Brand primary background
- `hover:bg-brand-primary-hover` - Hover state

### Card Components

#### Basic Card
```tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';

// Elevated card with professional styling
<Card className="card-elevated">
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
  </CardHeader>
  <CardContent>
    Card content goes here
  </CardContent>
</Card>

// Interactive card with hover effects
<Card className="card-elevated card-interactive">
  <CardContent>
    Clickable card content
  </CardContent>
</Card>

// Mobile-optimized card
<Card className="card-mobile-optimized">
  <CardContent>
    Responsive padding and margins
  </CardContent>
</Card>
```

#### Card Classes Reference
- `card-elevated` - Professional shadows and borders
- `card-interactive` - Hover effects for clickable cards
- `card-professional` - Standard professional card styling
- `card-mobile-optimized` - Responsive padding/margins

### Input Components

#### Form Inputs
```tsx
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';

// Professional input with design tokens
<div className="space-y-2">
  <Label htmlFor="email">Email Address</Label>
  <Input
    id="email"
    type="email"
    className="input-elevated"
    placeholder="Enter email"
  />
</div>

// Touch-friendly input for mobile
<Input
  className="input-elevated btn-touch-friendly"
  placeholder="Mobile-optimized input"
/>
```

#### Input Classes Reference
- `input-elevated` - Professional styling with focus states
- `input-professional` - Alternative professional styling
- `btn-touch-friendly` - 44px minimum height for mobile

### Navigation Components

#### Navigation Items
```tsx
import { Link } from 'react-router-dom';

// Professional navigation item
<Link
  to="/dashboard"
  className="nav-item-professional"
>
  <span className="nav-item-icon">⊞</span>
  Dashboard
</Link>

// Active navigation state
<Link
  to="/transactions"
  className="nav-item-professional active"
>
  <span className="nav-item-icon">□</span>
  Transactions
</Link>
```

#### Navigation Classes Reference
- `nav-item-professional` - Standard navigation styling
- `nav-item-professional.active` - Active state styling
- `btn-touch-friendly` - Touch-friendly navigation

---

## Layout Components

### Responsive Grid System

#### Grid Classes
```tsx
// Mobile-first responsive grids
<div className="responsive-grid-1">
  {/* Always 1 column */}
</div>

<div className="responsive-grid-2">
  {/* 1 col mobile, 2 cols tablet+ */}
</div>

<div className="responsive-grid-3">
  {/* 1 col mobile, 2 cols tablet, 3 cols desktop */}
</div>

<div className="responsive-grid-4">
  {/* 1 col mobile, 2 cols tablet, 3 cols desktop, 4 cols large */}
</div>
```

#### Layout Utilities
```tsx
// Mobile-first layout stacking
<div className="layout-mobile-stack">
  <div>Stacks vertically on mobile</div>
  <div>Side-by-side on tablet+</div>
</div>

// Responsive spacing
<div className="p-responsive">Standard responsive padding</div>
<div className="p-responsive-lg">Large responsive padding</div>
<div className="p-responsive-xl">Extra large responsive padding</div>
```

### Three-Panel Layout
```tsx
import { EnhancedAppLayout } from '@/shared/components/layout/EnhancedAppLayout';

// Main application layout with navigation and agent panel
<EnhancedAppLayout>
  <YourPageContent />
</EnhancedAppLayout>
```

---

## Professional Components

### Metric Cards
```tsx
// Professional metric display
<div className="metric-card-professional">
  <div className="metric-number-professional">
    87.3%
  </div>
  <div className="metric-label-professional">
    Accuracy Rate
  </div>
</div>
```

### Status Indicators
```tsx
// Professional status badges
<div className="status-professional status-success-professional">
  Operation Successful
</div>

<div className="status-professional status-warning-professional">
  Needs Attention
</div>

<div className="status-professional status-error-professional">
  Error Occurred
</div>
```

### Tables
```tsx
// Professional table styling
<div className="table-responsive">
  <table className="table-professional">
    <thead>
      <tr>
        <th>Column 1</th>
        <th>Column 2</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>Data 1</td>
        <td>Data 2</td>
      </tr>
    </tbody>
  </table>
</div>
```

---

## Animation & Interaction

### Loading States
```tsx
// Professional loading shimmer
<div className="loading-shimmer card-elevated">
  <div className="h-32 rounded"></div>
</div>

// Fade in animation
<div className="animate-fade-in">
  Content that fades in
</div>

// Slide up animation
<div className="animate-slide-up">
  Content that slides up
</div>
```

### Hover Effects
```tsx
// Cards with hover transforms
<Card className="card-elevated hover:transform hover:scale-[1.01]">
  Interactive card
</Card>

// Buttons with professional hover
<Button className="btn-professional hover:transform hover:translateY(-1px)">
  Elevated button
</Button>
```

---

## Typography System

### Responsive Typography
```tsx
// Mobile-first responsive text sizes
<h1 className="text-responsive-3xl">Main Heading</h1>
<h2 className="text-responsive-2xl">Section Heading</h2>
<h3 className="text-responsive-xl">Subsection</h3>
<p className="text-responsive-base">Body text</p>
<span className="text-responsive-sm">Small text</span>
```

### Professional Text Classes
- `text-responsive-3xl` - Main headings (1.875rem → 2.5rem)
- `text-responsive-2xl` - Section headings (1.5rem → 2rem)
- `text-responsive-xl` - Subsections (1.25rem → 1.5rem)
- `text-responsive-lg` - Large text (1.125rem → 1.25rem)
- `text-responsive-base` - Body text (1rem)
- `text-responsive-sm` - Small text (0.875rem)

---

## Design Token Usage

### Color Tokens
```css
/* Brand Colors */
.bg-brand-primary { background-color: var(--giki-primary); }
.text-brand-primary { color: var(--giki-primary); }
.border-brand-primary { border-color: var(--giki-primary); }

/* Semantic Colors */
.bg-success { background-color: var(--giki-success); }
.text-success { color: var(--giki-success); }
.bg-error { background-color: hsl(var(--giki-destructive)); }
.text-error { color: hsl(var(--giki-destructive)); }

/* AI System Colors */
.bg-ai-dark-green { background-color: var(--giki-dark-green); }
.bg-gradient-ai-primary { background: var(--giki-ai-gradient-primary); }
```

### Shadow & Spacing Tokens
```css
/* Professional Shadows */
.shadow-sm { box-shadow: var(--giki-shadow-sm); }
.shadow-md { box-shadow: var(--giki-shadow-md); }
.shadow-lg { box-shadow: var(--giki-shadow-lg); }

/* Card Shadows */
.card-shadow { box-shadow: var(--giki-card-shadow); }
.card-shadow-premium { box-shadow: var(--giki-card-shadow-premium); }

/* Border Radius */
.radius-sm { border-radius: var(--giki-radius-sm); }
.radius-md { border-radius: var(--giki-radius-md); }
.radius-lg { border-radius: var(--giki-radius-lg); }
```

---

## Accessibility Guidelines

### Touch Targets
- All interactive elements must be minimum 44px
- Use `btn-touch-friendly` class for compliance
- Test with mobile devices and touch interfaces

### Color Contrast
- Text on brand primary: Use white text
- Text on light backgrounds: Use dark gray
- Error states: Use high contrast red
- Success states: Use high contrast green

### Keyboard Navigation
- All interactive elements must be keyboard accessible
- Use proper ARIA labels and roles
- Focus states must be clearly visible

---

## Mobile Responsiveness

### Breakpoints
- Mobile: < 768px (stack layouts, larger touch targets)
- Tablet: 768px - 1024px (2-column grids)
- Desktop: 1024px - 1280px (3-column grids)
- Large: 1280px+ (4-column grids)

### Mobile-Specific Classes
```tsx
// Hide on mobile, show on desktop
<div className="mobile-hidden">
  Desktop only content
</div>

// Show on mobile, hide on desktop
<div className="desktop-hidden">
  Mobile only content
</div>

// Mobile navigation overlay
<div className="mobile-nav-hidden">
  <div className="mobile-nav-panel">
    Mobile navigation content
  </div>
</div>
```

---

## Mobile Responsiveness System

### Touch Target Guidelines
```css
/* All interactive elements must meet minimum touch target size */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Apply to buttons, links, and interactive elements */
.btn-mobile {
  min-height: 44px;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
}
```

### Mobile Navigation Pattern
```tsx
// Mobile navigation toggle implementation
{screenSize === 'mobile' && (
  <button
    onClick={() => setIsNavigationExpanded(!isNavigationExpanded)}
    className="fixed top-4 left-4 z-60 bg-brand-primary text-white rounded-lg p-2 shadow-lg touch-target"
    aria-label="Toggle navigation menu"
  >
    <span className="w-5 h-5 text-lg font-bold flex items-center justify-center">
      {isNavigationExpanded ? '✕' : '☰'}
    </span>
  </button>
)}
```

### Responsive Table Design
```tsx
// Tables with horizontal scroll and column hiding
<div className="overflow-x-auto">
  <Table className="min-w-full">
    <TableHead className="hidden sm:table-cell">Desktop Only Column</TableHead>
    <TableCell className="sm:hidden text-xs text-gray-500">
      Mobile inline data
    </TableCell>
  </Table>
</div>
```

### Responsive Grid Layouts
```tsx
// Statistics cards that adapt based on screen size
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
  {items.map(item => (
    <Card key={item.id} className="p-3 sm:p-4">
      <CardContent className="space-y-2 sm:space-y-3">
        {item.content}
      </CardContent>
    </Card>
  ))}
</div>
```

### Mobile Form Optimization
```tsx
// Form layouts optimized for mobile input
<div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
  <Input 
    className="text-sm touch-target" 
    placeholder="Mobile-optimized input"
  />
  <Button className="w-full sm:w-auto touch-target">
    Submit
  </Button>
</div>
```

### Responsive Component Classes
- `responsive-grid-2` - 1 column mobile, 2 columns desktop
- `responsive-grid-3` - 1 column mobile, 3 columns desktop  
- `responsive-grid-4` - 1→2→4 columns responsive
- `touch-target` - 44px minimum touch target
- `text-sm sm:text-base` - Smaller text on mobile
- `p-3 sm:p-4` - Reduced padding on mobile
- `gap-2 sm:gap-4` - Smaller gaps on mobile

---

## Best Practices

### Component Creation
1. **Always use design tokens** instead of hard-coded values
2. **Include responsive classes** for mobile optimization
3. **Add touch-friendly classes** for interactive elements
4. **Use semantic color classes** (success, error, warning)
5. **Include hover and focus states** for better UX

### Performance
1. **Use CSS custom properties** for theme values
2. **Leverage CSS Grid** for layouts instead of flexbox when appropriate
3. **Minimize DOM nesting** for better rendering performance
4. **Use loading states** for better perceived performance

### Maintenance
1. **Run design system compliance checker** before commits
2. **Update documentation** when adding new components
3. **Test across device sizes** before shipping
4. **Validate accessibility** with screen readers and keyboard navigation

---

## Common Patterns

### Dashboard Metrics
```tsx
<div className="responsive-grid-4">
  {metrics.map(metric => (
    <Card key={metric.id} className="card-elevated card-interactive">
      <CardContent className="p-responsive">
        <div className="metric-number-professional">
          {metric.value}
        </div>
        <div className="metric-label-professional">
          {metric.label}
        </div>
      </CardContent>
    </Card>
  ))}
</div>
```

### Form Layouts
```tsx
<form className="space-y-6">
  <div className="responsive-grid-2">
    <div className="space-y-2">
      <Label htmlFor="firstName">First Name</Label>
      <Input
        id="firstName"
        className="input-elevated"
        placeholder="Enter first name"
      />
    </div>
    <div className="space-y-2">
      <Label htmlFor="lastName">Last Name</Label>
      <Input
        id="lastName"
        className="input-elevated"
        placeholder="Enter last name"
      />
    </div>
  </div>
  <div className="layout-mobile-stack">
    <Button variant="outline" className="btn-professional btn-touch-friendly">
      Cancel
    </Button>
    <Button className="btn-professional btn-touch-friendly bg-brand-primary text-white">
      Submit
    </Button>
  </div>
</form>
```

### Data Tables
```tsx
<Card className="card-elevated">
  <CardHeader className="bg-gradient-ai-primary text-white">
    <CardTitle>Data Table</CardTitle>
  </CardHeader>
  <CardContent className="p-0">
    <div className="table-responsive">
      <table className="table-professional">
        <thead>
          <tr>
            <th>Name</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {data.map(row => (
            <tr key={row.id}>
              <td>{row.name}</td>
              <td>
                <span className="status-professional status-success-professional">
                  {row.status}
                </span>
              </td>
              <td>
                <Button
                  size="sm"
                  className="btn-professional btn-touch-friendly"
                >
                  Edit
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </CardContent>
</Card>
```

---

This documentation ensures consistent implementation of the giki.ai design system across all components and maintains the professional B2B aesthetic while providing excellent user experience on all devices.