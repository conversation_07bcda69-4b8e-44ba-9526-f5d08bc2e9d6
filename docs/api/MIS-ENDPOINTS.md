# MIS API Endpoints Documentation

## Overview
The giki.ai Management Information System (MIS) provides a comprehensive set of API endpoints for financial data management, categorization, and reporting. All endpoints require authentication and operate within a tenant-isolated environment.

## Base URL
- Development: `http://localhost:8000/api/v1`
- Production: `https://giki-ai-api-273348121056.us-central1.run.app/api/v1`

## Authentication
All endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <your-access-token>
```

## MIS Setup Endpoints

### Initialize MIS Setup
`POST /onboarding/mis/setup`

Initialize a new MIS setup for a company.

**Request Body:**
```json
{
  "company_info": {
    "name": "Acme Corporation",
    "industry": "technology",
    "size": "11-50",
    "fiscal_year_end": "december",
    "default_currency": "USD"
  },
  "uploaded_files": ["file_id_1", "file_id_2"]
}
```

**Response:**
```json
{
  "setup_id": "mis_setup_123",
  "status": "initialized",
  "enhancement_opportunities": [
    {
      "type": "historical_data",
      "description": "Upload 12 months of bank statements",
      "potential_accuracy_gain": "+15-20%"
    },
    {
      "type": "schema_upload", 
      "description": "Upload your Chart of Accounts",
      "potential_accuracy_gain": "+20%"
    }
  ],
  "baseline_accuracy": 87.5
}
```

### Get MIS Setup Status
`GET /onboarding/status`

Get the current MIS setup status and enhancement opportunities.

**Response:**
```json
{
  "stage": "production",
  "mis_completeness": 95.2,
  "enhancements_applied": ["historical_data", "schema_upload"],
  "total_transactions": 5432,
  "categorized_transactions": 5168,
  "accuracy_metrics": {
    "baseline": 87.5,
    "current": 95.2,
    "improvement": 7.7
  }
}
```

## Transaction Management

### Upload Transactions
`POST /files/upload`

Upload financial transaction files (Excel, CSV).

**Request:**
- Method: `multipart/form-data`
- Fields:
  - `file`: The file to upload
  - `type`: File type (optional, auto-detected)

**Response:**
```json
{
  "upload_id": "upload_123",
  "filename": "bank_transactions_2025.xlsx",
  "sheets": [
    {
      "name": "Transactions",
      "columns": ["Date", "Description", "Amount", "Balance"],
      "row_count": 1523
    }
  ],
  "status": "processing"
}
```

### Get Transactions
`GET /transactions/fast`

Retrieve categorized transactions with MIS hierarchy.

**Query Parameters:**
- `limit`: Number of transactions (default: 50, max: 1000)
- `offset`: Pagination offset
- `start_date`: Filter by date range (ISO format)
- `end_date`: Filter by date range (ISO format)
- `category`: Filter by category name
- `search`: Search in description/vendor

**Response:**
```json
{
  "transactions": [
    {
      "id": "txn_123",
      "date": "2025-07-05",
      "description": "Office Supplies - Staples",
      "amount": -156.32,
      "category": {
        "name": "Office Supplies",
        "parent": "Operating Expenses",
        "gl_code": "6210",
        "full_path": "Expenses > Operating Expenses > Office Supplies"
      },
      "vendor": {
        "name": "Staples",
        "category_history": ["Office Supplies", "Office Equipment"]
      },
      "confidence": 0.95
    }
  ],
  "total": 5432,
  "mis_accuracy": 95.2
}
```

### Update Transaction Category
`PUT /transactions/{transaction_id}/category`

Manually update a transaction's category (system learns from corrections).

**Request Body:**
```json
{
  "category_name": "Software Subscriptions",
  "reason": "Monthly SaaS subscription"
}
```

**Response:**
```json
{
  "transaction_id": "txn_123",
  "old_category": "Office Supplies",
  "new_category": "Software Subscriptions",
  "gl_code": "6420",
  "learning_applied": true
}
```

## Category Management

### Get Category Hierarchy
`GET /categories`

Retrieve the complete MIS category hierarchy.

**Query Parameters:**
- `type`: Filter by type (`income`, `expense`, `all`)
- `include_gl_codes`: Include GL code mappings (default: true)

**Response:**
```json
{
  "categories": [
    {
      "id": "cat_001",
      "name": "Revenue",
      "type": "income",
      "gl_code": "4000",
      "children": [
        {
          "id": "cat_002",
          "name": "Product Sales",
          "gl_code": "4100",
          "children": []
        }
      ]
    }
  ],
  "total_categories": 156,
  "custom_categories": 12
}
```

### Create Custom Category
`POST /categories`

Add a custom category to the MIS hierarchy.

**Request Body:**
```json
{
  "name": "AI Services Revenue",
  "parent_id": "cat_001",
  "type": "income",
  "gl_code": "4150",
  "description": "Revenue from AI consulting services"
}
```

## Reporting Endpoints

### Income vs Expense Report
`GET /reports/income-vs-expense`

Get summarized income and expense data.

**Query Parameters:**
- `period`: `monthly`, `quarterly`, `yearly`
- `start_date`: Start of period
- `end_date`: End of period

**Response:**
```json
{
  "total_income": 523000,
  "total_expense": 387000,
  "net_amount": 136000,
  "period": "2025-Q1",
  "breakdown": {
    "income_categories": [
      {"category": "Product Sales", "amount": 423000},
      {"category": "Service Revenue", "amount": 100000}
    ],
    "expense_categories": [
      {"category": "Salaries", "amount": 250000},
      {"category": "Operating Expenses", "amount": 137000}
    ]
  }
}
```

### Monthly Trends
`GET /reports/monthly-trends`

Get monthly financial trends.

**Response:**
```json
{
  "trends": [
    {
      "month": "2025-06",
      "income": 175000,
      "expenses": 129000,
      "net": 46000,
      "transaction_count": 432
    }
  ],
  "growth_metrics": {
    "income_growth": 12.3,
    "expense_growth": 8.7,
    "net_growth": 23.5
  }
}
```

### Export Financial Reports
`POST /reports/export`

Export financial reports in various formats.

**Request Body:**
```json
{
  "report_type": "profit_loss",
  "format": "excel",
  "period": "2025-Q1",
  "include_details": true
}
```

**Response:**
```json
{
  "export_id": "export_123",
  "download_url": "/reports/download/export_123",
  "expires_at": "2025-07-06T12:00:00Z"
}
```

## Performance Monitoring

### Get Performance Metrics
`GET /monitoring/performance/metrics`

Monitor API performance for critical operations.

**Query Parameters:**
- `operation_filter`: Filter by operation type

**Response:**
```json
{
  "total_operations": 15432,
  "operations": {
    "file_upload": {
      "count": 234,
      "avg_ms": 3421,
      "min_ms": 1200,
      "max_ms": 8900,
      "success_rate": 0.98
    },
    "categorization": {
      "count": 5432,
      "avg_ms": 245,
      "min_ms": 89,
      "max_ms": 1203,
      "success_rate": 0.995
    }
  },
  "summary": {
    "filter": "all",
    "thresholds": {
      "file_upload": 5000,
      "categorization": 500,
      "mis_setup": 300000
    }
  }
}
```

## AI Agent Endpoints

### Chat with MIS Agent
`POST /intelligence/adk/completions`

Interact with the AI agent for business insights.

**Request Body:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "What are my top expense categories this month?"
    }
  ],
  "context": {
    "time_period": "current_month"
  }
}
```

**Response:**
```json
{
  "response": "Based on your MIS data for July 2025, your top expense categories are:\n\n1. **Salaries & Wages** - $85,000 (65.4%)\n2. **Rent & Utilities** - $15,000 (11.5%)\n3. **Marketing** - $12,000 (9.2%)\n\nYour total expenses this month are $130,000, which is 8% higher than June.",
  "data_sources": ["transactions", "categories", "monthly_trends"],
  "confidence": 0.98
}
```

## Webhook Events

### Available Webhooks
- `transaction.categorized` - Fired when transactions are categorized
- `mis.accuracy.improved` - Fired when accuracy improves
- `report.generated` - Fired when reports are ready
- `enhancement.completed` - Fired when enhancement processing completes

### Webhook Payload Example
```json
{
  "event": "transaction.categorized",
  "timestamp": "2025-07-05T10:30:00Z",
  "data": {
    "transaction_count": 156,
    "accuracy": 95.3,
    "processing_time_ms": 4521
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "CATEGORY_NOT_FOUND",
    "message": "The specified category does not exist",
    "details": {
      "category_name": "Invalid Category",
      "suggestion": "Office Supplies"
    }
  },
  "request_id": "req_abc123"
}
```

Common error codes:
- `UNAUTHORIZED` - Invalid or missing authentication
- `FORBIDDEN` - Insufficient permissions
- `NOT_FOUND` - Resource not found
- `VALIDATION_ERROR` - Invalid request data
- `PROCESSING_ERROR` - Server processing error
- `RATE_LIMITED` - Too many requests

## Rate Limits

- Standard: 1000 requests per minute
- File uploads: 100 per hour
- Report generation: 50 per hour
- AI agent queries: 500 per hour

## Best Practices

1. **Batch Operations**: Use batch endpoints when processing multiple items
2. **Pagination**: Always paginate large result sets
3. **Caching**: Cache category hierarchies and reports when possible
4. **Webhooks**: Use webhooks instead of polling for status updates
5. **Error Handling**: Implement exponential backoff for retries

## SDK Examples

### Python
```python
from giki_ai import MISClient

client = MISClient(api_key="your-api-key")

# Upload transactions
result = client.upload_file("transactions.xlsx")

# Get categorized transactions
transactions = client.get_transactions(
    start_date="2025-07-01",
    limit=100
)

# Generate report
report = client.generate_report(
    type="profit_loss",
    period="2025-Q2"
)
```

### TypeScript
```typescript
import { MISClient } from '@giki-ai/sdk';

const client = new MISClient({ apiKey: 'your-api-key' });

// Setup MIS
const setup = await client.setupMIS({
  companyInfo: {
    name: 'Acme Corp',
    industry: 'technology',
    size: '11-50'
  }
});

// Get transactions with typing
const transactions = await client.getTransactions({
  startDate: '2025-07-01',
  limit: 100
});

// Chat with AI agent
const response = await client.chat({
  message: 'Analyze my expense trends'
});
```