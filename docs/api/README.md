# API Specification - giki.ai Platform

**Version:** Auto-generated from FastAPI backend  
**Updated:** 2025-07-06  
**Purpose:** Machine-readable API specification for development tools and integration

---

## 📋 OpenAPI Specification

### Machine-Readable Specification
- **File:** `openapi.json` (OpenAPI 3.1.0 format)
- **Usage:** Import into development tools for automated client generation
- **Endpoints:** 190 documented endpoints
- **Version:** 1.0.0

### Human-Readable Documentation
- **Live Docs:** http://localhost:8000/docs (local development)
- **Production Docs:** https://giki-ai-api-6uyufgxcxa-uc.a.run.app/docs
- **Interactive:** Full Swagger UI with "Try it out" functionality

---

## 🛠️ Development Tools Integration

### API Client Generation
```bash
# Generate TypeScript client
openapi-generator-cli generate -i docs/api/openapi.json -g typescript-axios -o src/generated/api

# Generate Python client  
openapi-generator-cli generate -i docs/api/openapi.json -g python -o clients/python

# Generate cURL commands
openapi-generator-cli generate -i docs/api/openapi.json -g bash -o scripts/api
```

### Tool Integration
- **Postman:** Import `openapi.json` for instant collection creation
- **Insomnia:** Direct OpenAPI import for REST client testing
- **VSCode:** Use OpenAPI extensions for autocompletion
- **IntelliJ:** API specification support for development

---

## 🔄 Auto-Sync Instructions

### Keeping Specification Updated
```bash
# Manual update (run when API changes)
curl -s http://localhost:8000/openapi.json | jq . > docs/api/openapi.json

# Add to pre-commit hook for automatic updates
echo "curl -s http://localhost:8000/openapi.json | jq . > docs/api/openapi.json" >> .git/hooks/pre-commit
```

### CI/CD Integration
```yaml
# Add to GitHub Actions workflow
- name: Update OpenAPI Spec
  run: |
    pnpm nx serve:api &
    sleep 10
    curl -s http://localhost:8000/openapi.json | jq . > docs/api/openapi.json
    git add docs/api/openapi.json
```

---

## 📚 Documentation Structure

### Complete API Documentation
- **Comprehensive:** `../03-API-REFERENCE.md` - Full endpoint documentation
- **Architecture:** `../02-SYSTEM-ARCHITECTURE.md` - Technical implementation
- **Authentication:** JWT token-based with bearer authentication
- **Testing:** `../07-TESTING-STRATEGY.md` - API testing strategies

### Quick Reference
```
/api/v1/auth/          # Authentication endpoints
/api/v1/files/         # File upload and processing  
/api/v1/categories/    # AI categorization
/api/v1/transactions/  # Transaction management
/api/v1/reports/       # Report generation
/api/v1/accuracy/      # Temporal accuracy validation
```

---

## 🔑 Authentication

### Bearer Token Usage
```bash
# Get authentication token
curl -X POST "http://localhost:8000/api/v1/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=GikiTest2025Secure"

# Use token in requests
curl -H "Authorization: Bearer <your-token>" \
  "http://localhost:8000/api/v1/transactions"
```

### Test Accounts (Role-Based)
- **Business Owner:** <EMAIL> (full MIS access)
- **Accountant:** <EMAIL> (reporting focus)
- **Bookkeeper:** <EMAIL> (transaction processing)
- **Viewer:** <EMAIL> (read-only access)
- **Admin:** <EMAIL> (system administration)
- **Password:** GikiTest2025Secure

---

**SYNC REMINDER:** Always update this specification when making API changes to maintain development tool compatibility.