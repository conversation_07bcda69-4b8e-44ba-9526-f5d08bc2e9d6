<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>giki.ai - Complete MIS in 5 Minutes</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        /* Design System Variables */
        :root {
            /* Primary Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            --brand-primary-alpha: rgba(41, 83, 67, 0.1);
            
            /* Professional UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Status & Feedback Colors */
            --status-success: #059669;
            --status-warning: #D97706;
            --status-error: #DC2626;
            --status-info: #2563EB;
            --status-neutral: #6B7280;
            
            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            line-height: 1.5;
        }

        /* Header */
        .header {
            background: var(--ui-background);
            border-bottom: 1px solid var(--ui-border);
            padding: var(--space-4) var(--space-6);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
        }

        .logo-text .ai {
            color: var(--ui-text-secondary);
        }

        /* Navigation */
        .nav-links {
            display: flex;
            gap: var(--space-8);
            align-items: center;
        }

        .nav-link {
            color: var(--ui-text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .nav-link:hover {
            color: var(--brand-primary);
        }

        /* Main Container */
        .main-container {
            margin-top: 72px;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
            padding: var(--space-8) var(--space-6);
            display: grid;
            grid-template-columns: 1fr 460px;
            gap: var(--space-16);
            align-items: center;
            min-height: calc(100vh - 72px);
        }

        /* Hero Section */
        .hero-section {
            padding-right: var(--space-8);
        }


        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: var(--space-6);
            color: var(--ui-text-primary);
        }

        .hero-title .highlight {
            color: var(--brand-primary);
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: var(--ui-text-secondary);
            line-height: 1.6;
            margin-bottom: var(--space-10);
        }


        /* CTA Buttons */
        .cta-buttons {
            display: flex;
            gap: var(--space-4);
            margin-bottom: var(--space-8);
        }

        .btn {
            padding: var(--space-4) var(--space-8);
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.125rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(41, 83, 67, 0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--brand-primary-hover) 0%, #163651 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(41, 83, 67, 0.3);
        }

        .btn-secondary {
            background: white;
            color: var(--brand-primary);
            border: 2px solid var(--brand-primary);
        }

        .btn-secondary:hover {
            background: var(--brand-primary-light);
        }

        /* Social Proof */
        .social-proof {
            display: flex;
            align-items: center;
            gap: var(--space-6);
            color: var(--ui-text-secondary);
            font-size: 0.875rem;
        }

        .proof-item {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .proof-icon {
            color: var(--brand-primary);
        }

        /* Login Panel */
        .login-panel {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 20px;
            padding: var(--space-10);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
        }

        .login-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .login-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
        }

        .login-subtitle {
            color: var(--ui-text-secondary);
        }

        .form-group {
            margin-bottom: var(--space-4);
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
            font-size: 0.875rem;
        }

        .form-input {
            width: 100%;
            padding: var(--space-4) var(--space-5);
            border: 1px solid var(--ui-border);
            border-radius: 10px;
            font-size: 1.0625rem;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 3px var(--brand-primary-alpha);
        }

        .form-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            font-size: 0.875rem;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .forgot-link {
            color: var(--brand-primary);
            text-decoration: none;
            font-weight: 500;
        }

        .forgot-link:hover {
            text-decoration: underline;
        }

        .btn-full {
            width: 100%;
        }

        .divider {
            position: relative;
            text-align: center;
            margin: var(--space-6) 0;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--ui-border);
        }

        .divider-text {
            background: var(--ui-background);
            padding: 0 var(--space-3);
            position: relative;
            color: var(--ui-text-muted);
            font-size: 0.875rem;
        }

        .try-now-section {
            text-align: center;
        }

        .try-now-title {
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
        }

        .try-now-subtitle {
            color: var(--ui-text-secondary);
            font-size: 0.875rem;
            margin-bottom: var(--space-4);
        }


        /* Responsive */
        @media (max-width: 1024px) {
            .main-container {
                grid-template-columns: 1fr;
                gap: var(--space-12);
            }

            .hero-section {
                text-align: center;
                padding-right: 0;
            }

            .value-props {
                max-width: 600px;
                margin: 0 auto var(--space-8);
            }

            .cta-buttons {
                justify-content: center;
            }

            .social-proof {
                justify-content: center;
            }

            .login-panel {
                max-width: 400px;
                margin: 0 auto;
            }

            .process-steps {
                grid-template-columns: repeat(2, 1fr);
            }

            .features-grid {
                grid-template-columns: 1fr;
                max-width: 600px;
                margin: 0 auto;
            }
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero-title {
                font-size: 2rem;
            }

            .value-props {
                grid-template-columns: 1fr;
            }

            .cta-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span style="font-size: 1.5rem; font-weight: 600; color: var(--brand-primary);">giki.ai</span>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Hero Section -->
        <section class="hero-section">

            <h1 class="hero-title">
                Turn Messy Statements<br/>
                Into <span class="highlight">Organized Books</span>
            </h1>

            <p class="hero-subtitle">
                AI categorizes your transactions. Export to any accounting software.
            </p>

            <!-- Process Preview - Visual, not text -->
            <div style="margin: var(--space-10) 0; padding: var(--space-8); background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%); border-radius: 16px; color: white; box-shadow: 0 8px 32px rgba(41, 83, 67, 0.2);">
                <div style="display: flex; align-items: center; justify-content: space-around;">
                    <div style="text-align: center;">
                        <div style="width: 64px; height: 64px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 28px; margin: 0 auto var(--space-3); border: 2px solid rgba(255,255,255,0.3);">↑</div>
                        <div style="font-size: 1rem; font-weight: 600;">Bank Statements</div>
                        <div style="font-size: 0.875rem; opacity: 0.8; margin-top: var(--space-1);">Upload any format</div>
                    </div>
                    <div style="font-size: 24px; opacity: 0.6;">→</div>
                    <div style="text-align: center;">
                        <div style="width: 64px; height: 64px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 28px; margin: 0 auto var(--space-3); border: 2px solid rgba(255,255,255,0.3);">⚬</div>
                        <div style="font-size: 1rem; font-weight: 600;">AI Categorized</div>
                        <div style="font-size: 0.875rem; opacity: 0.8; margin-top: var(--space-1);">87% accuracy</div>
                    </div>
                    <div style="font-size: 24px; opacity: 0.6;">→</div>
                    <div style="text-align: center;">
                        <div style="width: 64px; height: 64px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 28px; margin: 0 auto var(--space-3); border: 2px solid rgba(255,255,255,0.3);">□</div>
                        <div style="font-size: 1rem; font-weight: 600;">Export Ready</div>
                        <div style="font-size: 0.875rem; opacity: 0.8; margin-top: var(--space-1);">QuickBooks, Xero, CSV</div>
                    </div>
                </div>
            </div>

            <!-- Get Expert Setup Help Section -->
            <div style="text-align: center; margin-top: var(--space-10);">
                <div style="text-align: center; margin-bottom: var(--space-3);">
                    <p style="color: var(--ui-text-secondary); font-size: 0.875rem;">
                        Need help getting started? We'll set everything up for you.
                    </p>
                </div>
                <button 
                    style="display: inline-flex; align-items: center; padding: var(--space-3) var(--space-8); border: 2px solid var(--brand-primary); color: var(--brand-primary); background: white; border-radius: 12px; font-weight: 600; font-size: 1.125rem; cursor: pointer; transition: all 0.2s ease; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); text-decoration: none;"
                    onmouseover="this.style.background='var(--brand-primary)'; this.style.color='white'; this.style.boxShadow='0 6px 12px rgba(0, 0, 0, 0.15)';"
                    onmouseout="this.style.background='white'; this.style.color='var(--brand-primary)'; this.style.boxShadow='0 4px 8px rgba(0, 0, 0, 0.1)';"
                    onclick="window.open('mailto:<EMAIL>?subject=Business MIS Setup - Let\'s Get Started&body=Hi! I\'d like to learn more about having the giki.ai team set up my MIS. Here are some details about my business:%0D%0A%0D%0ABusiness Name: %0D%0AIndustry: %0D%0AMonthly Transaction Volume: %0D%0AAccounting Software: %0D%0A%0D%0APlease let me know the next steps!', '_blank')"
                >
                    Get Expert Setup Help
                </button>
            </div>
        </section>

        <!-- Login Panel -->
        <section class="login-panel">
            <div class="login-header">
                <h2 class="login-title">Welcome Back</h2>
                <p class="login-subtitle">Access your MIS dashboard</p>
            </div>

            <form>
                <div class="form-group">
                    <label class="form-label" for="email">Email Address</label>
                    <input type="email" id="email" class="form-input" placeholder="<EMAIL>" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">Password</label>
                    <input type="password" id="password" class="form-input" placeholder="••••••••" required>
                </div>

                <div class="form-footer">
                    <div class="checkbox-group">
                        <input type="checkbox" id="remember">
                        <label for="remember">Remember me</label>
                    </div>
                    <a href="/forgot-password" class="forgot-link">Forgot password?</a>
                </div>

                <button type="submit" class="btn btn-primary btn-full">
                    Sign In
                </button>
            </form>

            <div class="divider">
                <span class="divider-text">or</span>
            </div>

            <div class="try-now-section">
                <p style="text-align: center; color: var(--ui-text-secondary); font-size: 0.875rem;">
                    New to giki.ai? <a href="/signup" style="color: var(--brand-primary); text-decoration: none; font-weight: 500;">Create account</a>
                </p>
            </div>
        </section>
    </div>
</body>
</html>