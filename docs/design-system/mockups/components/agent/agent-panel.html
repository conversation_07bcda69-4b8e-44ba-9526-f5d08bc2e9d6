<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Panel with Expand/Collapse Handle - giki.ai</title>
    <style>
        :root {
            /* Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            
            /* UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Layout Variables */
            --nav-expanded: 240px;
            --nav-collapsed: 64px;
            --agent-panel: 400px;
            --agent-handle: 24px;
            
            /* Spacing */
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            
            /* Typography */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-primary);
            background-color: var(--ui-background);
            color: var(--ui-text-primary);
            line-height: 1.5;
        }
        
        /* Three-Panel Layout */
        .app-layout {
            display: grid;
            grid-template-columns: var(--nav-expanded) 1fr var(--agent-panel);
            grid-template-rows: 1fr;
            height: 100vh;
            overflow: hidden;
        }
        
        .app-layout.agent-hidden {
            grid-template-columns: var(--nav-expanded) 1fr 0px;
        }
        
        .app-layout.work-first {
            grid-template-columns: var(--nav-expanded) 1fr 0px;
        }
        
        .app-layout.agent-mode {
            grid-template-columns: var(--nav-collapsed) 1fr var(--agent-panel);
        }
        
        /* Left Navigation */
        .left-nav {
            background: var(--ui-surface);
            border-right: 1px solid var(--ui-border);
            display: flex;
            flex-direction: column;
            padding: var(--space-4);
            overflow-y: auto;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            padding: var(--space-3);
            margin-bottom: var(--space-2);
            border-radius: 6px;
            text-decoration: none;
            color: var(--ui-text-secondary);
            transition: all 0.2s;
        }
        
        .nav-item:hover {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
        }
        
        .nav-item.active {
            background: var(--brand-primary);
            color: white;
        }
        
        .nav-icon {
            margin-right: var(--space-3);
            font-size: var(--text-lg);
        }
        
        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            position: relative;
        }
        
        .content-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--ui-border);
            background: var(--ui-background);
        }
        
        .content-body {
            flex: 1;
            padding: var(--space-6);
        }
        
        /* Agent Panel */
        .agent-panel {
            background: var(--ui-background);
            border-left: 1px solid var(--ui-border);
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease-in-out;
        }
        
        .agent-panel.hidden {
            width: 0;
            border: none;
        }
        
        /* Agent Panel Handle - This is the key feature! */
        .agent-handle {
            position: absolute;
            left: calc(-1 * var(--agent-handle));
            top: 50%;
            transform: translateY(-50%);
            width: var(--agent-handle);
            height: 80px;
            background: var(--brand-primary);
            border: 1px solid var(--ui-border);
            border-right: none;
            border-radius: 6px 0 0 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            z-index: 10;
        }
        
        .agent-handle:hover {
            background: var(--brand-primary-hover);
            transform: translateY(-50%) translateX(-2px);
        }
        
        .agent-handle.collapsed {
            left: -2px;
            border-radius: 0 6px 6px 0;
            border-left: none;
            border-right: 1px solid var(--ui-border);
        }
        
        .agent-handle.collapsed:hover {
            transform: translateY(-50%) translateX(2px);
        }
        
        .handle-icon {
            color: white;
            font-size: var(--text-sm);
            font-weight: bold;
            writing-mode: vertical-lr;
            text-orientation: mixed;
        }
        
        .handle-chevron {
            color: white;
            font-size: var(--text-lg);
            transition: transform 0.2s;
        }
        
        .agent-handle.collapsed .handle-chevron {
            transform: rotate(180deg);
        }
        
        /* Agent Panel Content */
        .agent-header {
            padding: var(--space-4);
            border-bottom: 1px solid var(--ui-border);
            background: var(--ui-surface);
        }
        
        .agent-title {
            font-size: var(--text-lg);
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
        }
        
        .agent-subtitle {
            font-size: var(--text-sm);
            color: var(--ui-text-secondary);
        }
        
        .agent-content {
            flex: 1;
            padding: var(--space-4);
            overflow-y: auto;
        }
        
        .agent-input {
            width: 100%;
            padding: var(--space-3);
            border: 1px solid var(--ui-border);
            border-radius: 6px;
            font-size: var(--text-base);
            resize: vertical;
            min-height: 60px;
            margin-bottom: var(--space-4);
        }
        
        .agent-input:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 2px rgba(41, 83, 67, 0.1);
        }
        
        .agent-send-btn {
            background: var(--brand-primary);
            color: white;
            border: none;
            padding: var(--space-3) var(--space-4);
            border-radius: 6px;
            font-size: var(--text-base);
            cursor: pointer;
            transition: background 0.2s;
            width: 100%;
        }
        
        .agent-send-btn:hover {
            background: var(--brand-primary-hover);
        }
        
        .agent-conversation {
            margin-top: var(--space-6);
        }
        
        .agent-message {
            margin-bottom: var(--space-4);
            padding: var(--space-3);
            border-radius: 6px;
            background: var(--ui-surface);
            border: 1px solid var(--ui-border);
        }
        
        .agent-message.user {
            background: var(--brand-primary-light);
            border-color: var(--brand-primary);
            margin-left: var(--space-4);
        }
        
        .agent-message.assistant {
            background: var(--ui-surface);
            margin-right: var(--space-4);
        }
        
        .message-content {
            font-size: var(--text-base);
            line-height: 1.5;
        }
        
        .message-timestamp {
            font-size: var(--text-sm);
            color: var(--ui-text-muted);
            margin-top: var(--space-2);
        }
        
        /* Sample Content */
        .sample-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }
        
        .metric-card {
            background: white;
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            padding: var(--space-6);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .metric-title {
            font-size: var(--text-base);
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-2);
        }
        
        .metric-value {
            font-size: var(--text-xl);
            font-weight: 600;
            color: var(--brand-primary);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .app-layout {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }
            
            .agent-panel {
                position: fixed;
                top: 0;
                right: 0;
                height: 100vh;
                width: var(--agent-panel);
                transform: translateX(100%);
                z-index: 1000;
            }
            
            .agent-panel.open {
                transform: translateX(0);
            }
            
            .agent-handle {
                display: none;
            }
        }
        
        /* States and Animations */
        .state-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--brand-primary);
            color: white;
            padding: var(--space-2) var(--space-3);
            border-radius: 6px;
            font-size: var(--text-sm);
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="app-layout" id="appLayout">
        <!-- Left Navigation Panel -->
        <nav class="left-nav">
            <div style="margin-bottom: var(--space-6);">
                <h1 style="color: var(--brand-primary); font-size: var(--text-xl); font-weight: 600;">giki.ai</h1>
                <p style="color: var(--ui-text-secondary); font-size: var(--text-sm);">MIS Platform</p>
            </div>
            
            <a href="#" class="nav-item active">
                <span class="nav-icon">⊞</span>
                Dashboard
            </a>
            <a href="#" class="nav-item">
                <span class="nav-icon">↑</span>
                Upload
            </a>
            <a href="#" class="nav-item">
                <span class="nav-icon">□</span>
                Transactions
            </a>
            <a href="#" class="nav-item">
                <span class="nav-icon">∷</span>
                Reports
            </a>
            <a href="#" class="nav-item">
                <span class="nav-icon">⚬</span>
                Categories
            </a>
        </nav>
        
        <!-- Main Content Area -->
        <main class="main-content">
            <div class="content-header">
                <h2 style="font-size: var(--text-xl); font-weight: 600; margin-bottom: var(--space-2);">Dashboard</h2>
                <p style="color: var(--ui-text-secondary);">Overview of your business metrics and AI categorization performance</p>
            </div>
            
            <div class="content-body">
                <div class="sample-dashboard">
                    <div class="metric-card">
                        <div class="metric-title">Total Transactions</div>
                        <div class="metric-value">2,847</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Categorization Accuracy</div>
                        <div class="metric-value">87.3%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Monthly Revenue</div>
                        <div class="metric-value">$24,500</div>
                    </div>
                </div>
                
                <div style="background: var(--ui-surface); border: 1px solid var(--ui-border); border-radius: 8px; padding: var(--space-6);">
                    <h3 style="font-size: var(--text-lg); font-weight: 600; margin-bottom: var(--space-4);">Recent Activity</h3>
                    <p style="color: var(--ui-text-secondary);">Your recent transactions and categorizations will appear here.</p>
                </div>
            </div>
        </main>
        
        <!-- Agent Panel with Handle -->
        <aside class="agent-panel" id="agentPanel">
            <!-- This is the key feature - the handle that shows/hides the panel -->
            <div class="agent-handle" id="agentHandle" onclick="toggleAgentPanel()">
                <div class="handle-chevron">‹</div>
            </div>
            
            <div class="agent-header">
                <h3 class="agent-title">AI Assistant</h3>
                <p class="agent-subtitle">Ask questions about your data or get help with categorization</p>
            </div>
            
            <div class="agent-content">
                <textarea class="agent-input" placeholder="Ask me anything about your transactions, categories, or financial data..."></textarea>
                <button class="agent-send-btn">Send Message</button>
                
                <div class="agent-conversation">
                    <div class="agent-message assistant">
                        <div class="message-content">
                            Hello! I'm your AI assistant. I can help you with:
                            <ul style="margin-top: var(--space-2); padding-left: var(--space-4);">
                                <li>Categorizing transactions</li>
                                <li>Analyzing spending patterns</li>
                                <li>Generating reports</li>
                                <li>Answering questions about your data</li>
                            </ul>
                        </div>
                        <div class="message-timestamp">Just now</div>
                    </div>
                    
                    <div class="agent-message user">
                        <div class="message-content">
                            What are my top expense categories this month?
                        </div>
                        <div class="message-timestamp">2 minutes ago</div>
                    </div>
                    
                    <div class="agent-message assistant">
                        <div class="message-content">
                            Based on your transaction data, your top 3 expense categories this month are:
                            <ol style="margin-top: var(--space-2); padding-left: var(--space-4);">
                                <li>Office Supplies: $1,250 (26.3%)</li>
                                <li>Software Subscriptions: $890 (18.8%)</li>
                                <li>Travel & Entertainment: $723 (15.3%)</li>
                            </ol>
                        </div>
                        <div class="message-timestamp">2 minutes ago</div>
                    </div>
                </div>
            </div>
        </aside>
    </div>
    
    <!-- State Indicator for Demo -->
    <div class="state-indicator" id="stateIndicator">
        Agent Panel: Open
    </div>
    
    <script>
        let agentPanelOpen = true;
        
        function toggleAgentPanel() {
            const layout = document.getElementById('appLayout');
            const panel = document.getElementById('agentPanel');
            const handle = document.getElementById('agentHandle');
            const indicator = document.getElementById('stateIndicator');
            
            if (agentPanelOpen) {
                // Hide agent panel - switch to work-first mode
                layout.className = 'app-layout work-first';
                handle.className = 'agent-handle collapsed';
                handle.querySelector('.handle-chevron').textContent = '›';
                indicator.textContent = 'Agent Panel: Closed (Work-First Mode)';
                agentPanelOpen = false;
            } else {
                // Show agent panel - switch to agent mode
                layout.className = 'app-layout agent-mode';
                handle.className = 'agent-handle';
                handle.querySelector('.handle-chevron').textContent = '‹';
                indicator.textContent = 'Agent Panel: Open (Agent Mode)';
                agentPanelOpen = true;
            }
        }
        
        // Demo: Show different states
        function showWorkFirstMode() {
            document.getElementById('appLayout').className = 'app-layout work-first';
            document.getElementById('agentHandle').className = 'agent-handle collapsed';
            document.getElementById('agentHandle').querySelector('.handle-chevron').textContent = '›';
            document.getElementById('stateIndicator').textContent = 'Work-First Mode: Left nav expanded, agent hidden';
            agentPanelOpen = false;
        }
        
        function showAgentMode() {
            document.getElementById('appLayout').className = 'app-layout agent-mode';
            document.getElementById('agentHandle').className = 'agent-handle';
            document.getElementById('agentHandle').querySelector('.handle-chevron').textContent = '‹';
            document.getElementById('stateIndicator').textContent = 'Agent Mode: Left nav collapsed, agent open';
            agentPanelOpen = true;
        }
        
        // Initialize in agent mode
        showAgentMode();
    </script>
</body>
</html>