<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Panel Handle Mechanism - giki.ai</title>
    <style>
        :root {
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --ui-background: #FFFFFF;
            --ui-border: #E1E5E9;
            --ui-surface: #F8F9FA;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --agent-panel: 400px;
            --agent-handle: 24px;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --text-base: 1rem;
            --text-lg: 1.125rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-primary);
            background: var(--ui-background);
            color: var(--ui-text-primary);
            padding: var(--space-6);
            line-height: 1.5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--brand-primary);
        }
        
        .demo-description {
            font-size: var(--text-lg);
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-6);
            line-height: 1.6;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-6);
            margin-bottom: var(--space-6);
        }
        
        .demo-panel {
            background: var(--ui-surface);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            padding: var(--space-6);
            position: relative;
            overflow: hidden;
        }
        
        .demo-panel h3 {
            font-size: var(--text-lg);
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--brand-primary);
        }
        
        .demo-panel p {
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-4);
        }
        
        /* Agent Panel Simulation */
        .agent-panel-sim {
            width: 300px;
            height: 400px;
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            position: relative;
            margin: 0 auto;
            transition: all 0.3s ease-in-out;
        }
        
        .agent-panel-sim.hidden {
            width: 0;
            border: none;
            opacity: 0;
        }
        
        /* Handle Styles */
        .agent-handle {
            position: absolute;
            left: calc(-1 * var(--agent-handle));
            top: 50%;
            transform: translateY(-50%);
            width: var(--agent-handle);
            height: 80px;
            background: var(--brand-primary);
            border: 1px solid var(--ui-border);
            border-right: none;
            border-radius: 6px 0 0 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            z-index: 10;
        }
        
        .agent-handle:hover {
            background: var(--brand-primary-hover);
            transform: translateY(-50%) translateX(-2px);
        }
        
        .agent-handle.collapsed {
            left: -2px;
            border-radius: 0 6px 6px 0;
            border-left: none;
            border-right: 1px solid var(--ui-border);
        }
        
        .agent-handle.collapsed:hover {
            transform: translateY(-50%) translateX(2px);
        }
        
        .handle-chevron {
            color: white;
            font-size: var(--text-lg);
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .agent-handle.collapsed .handle-chevron {
            transform: rotate(180deg);
        }
        
        /* Panel Content */
        .agent-panel-content {
            padding: var(--space-4);
            height: 100%;
        }
        
        .agent-panel-title {
            font-size: var(--text-lg);
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--brand-primary);
        }
        
        .agent-panel-text {
            color: var(--ui-text-secondary);
            font-size: var(--text-base);
        }
        
        /* Controls */
        .demo-controls {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
            margin-top: var(--space-6);
        }
        
        .demo-button {
            background: var(--brand-primary);
            color: white;
            border: none;
            padding: var(--space-4) var(--space-6);
            border-radius: 6px;
            font-size: var(--text-base);
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .demo-button:hover {
            background: var(--brand-primary-hover);
        }
        
        .demo-button.secondary {
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            border: 1px solid var(--ui-border);
        }
        
        .demo-button.secondary:hover {
            background: var(--ui-border);
        }
        
        /* State Indicator */
        .state-indicator {
            text-align: center;
            margin-top: var(--space-4);
            padding: var(--space-4);
            background: var(--brand-primary);
            color: white;
            border-radius: 6px;
            font-weight: 600;
        }
        
        /* Handle Details */
        .handle-details {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            padding: var(--space-6);
            margin-top: var(--space-6);
        }
        
        .handle-details h3 {
            font-size: var(--text-lg);
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--brand-primary);
        }
        
        .handle-specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            margin-top: var(--space-4);
        }
        
        .spec-item {
            padding: var(--space-4);
            background: var(--ui-surface);
            border-radius: 6px;
            border: 1px solid var(--ui-border);
        }
        
        .spec-label {
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
        }
        
        .spec-value {
            color: var(--ui-text-secondary);
            font-size: 0.9rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .agent-panel-sim {
                width: 250px;
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Agent Panel Handle Mechanism</h1>
        <p class="demo-description">
            Interactive demonstration of the agent panel expand/collapse handle functionality. 
            The handle provides a smooth, professional way to toggle between work-first mode and agent-assisted mode.
        </p>
        
        <div class="demo-grid">
            <div class="demo-panel">
                <h3>Open State (Agent Mode)</h3>
                <p>When the agent panel is open, the handle appears on the left edge with a left-pointing chevron (‹). 
                   The left navigation collapses to make room for the agent panel.</p>
                
                <div class="agent-panel-sim" id="panelOpen">
                    <div class="agent-handle" onclick="togglePanel('panelOpen')">
                        <div class="handle-chevron">‹</div>
                    </div>
                    <div class="agent-panel-content">
                        <div class="agent-panel-title">AI Assistant</div>
                        <div class="agent-panel-text">Panel is open and ready for interaction...</div>
                    </div>
                </div>
            </div>
            
            <div class="demo-panel">
                <h3>Closed State (Work-First Mode)</h3>
                <p>When the agent panel is closed, the handle appears on the right edge with a right-pointing chevron (›). 
                   The left navigation expands to provide more workspace.</p>
                
                <div class="agent-panel-sim hidden" id="panelClosed">
                    <div class="agent-handle collapsed" onclick="togglePanel('panelClosed')">
                        <div class="handle-chevron">›</div>
                    </div>
                    <div class="agent-panel-content">
                        <div class="agent-panel-title">AI Assistant</div>
                        <div class="agent-panel-text">Panel is hidden to focus on work...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-controls">
            <button class="demo-button" onclick="showBoth()">Show Both States</button>
            <button class="demo-button secondary" onclick="resetDemo()">Reset Demo</button>
        </div>
        
        <div class="state-indicator" id="stateDisplay">
            Click the handles above to see the toggle animation
        </div>
        
        <div class="handle-details">
            <h3>Handle Specifications</h3>
            <p>Technical details and design specifications for the agent panel handle mechanism:</p>
            
            <div class="handle-specs">
                <div class="spec-item">
                    <div class="spec-label">Dimensions</div>
                    <div class="spec-value">24px wide × 80px tall</div>
                </div>
                
                <div class="spec-item">
                    <div class="spec-label">Position</div>
                    <div class="spec-value">Absolute, vertically centered</div>
                </div>
                
                <div class="spec-item">
                    <div class="spec-label">Colors</div>
                    <div class="spec-value">Background: #295343<br>Hover: #1D372E</div>
                </div>
                
                <div class="spec-item">
                    <div class="spec-label">Animation</div>
                    <div class="spec-value">0.3s ease-in-out transition</div>
                </div>
                
                <div class="spec-item">
                    <div class="spec-label">Hover Effect</div>
                    <div class="spec-value">2px slide + color change</div>
                </div>
                
                <div class="spec-item">
                    <div class="spec-label">Icon</div>
                    <div class="spec-value">Chevron (‹/›) with rotation</div>
                </div>
                
                <div class="spec-item">
                    <div class="spec-label">Cursor</div>
                    <div class="spec-value">Pointer on hover</div>
                </div>
                
                <div class="spec-item">
                    <div class="spec-label">Z-Index</div>
                    <div class="spec-value">10 (always on top)</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function togglePanel(panelId) {
            const panel = document.getElementById(panelId);
            const handle = panel.querySelector('.agent-handle');
            const chevron = handle.querySelector('.handle-chevron');
            const indicator = document.getElementById('stateDisplay');
            
            if (panel.classList.contains('hidden')) {
                // Show panel
                panel.classList.remove('hidden');
                handle.classList.remove('collapsed');
                chevron.textContent = '‹';
                indicator.textContent = 'Agent Panel: Open (Agent Mode)';
            } else {
                // Hide panel
                panel.classList.add('hidden');
                handle.classList.add('collapsed');
                chevron.textContent = '›';
                indicator.textContent = 'Agent Panel: Closed (Work-First Mode)';
            }
        }
        
        function showBoth() {
            const panelOpen = document.getElementById('panelOpen');
            const panelClosed = document.getElementById('panelClosed');
            
            panelOpen.classList.remove('hidden');
            panelClosed.classList.add('hidden');
            
            document.getElementById('stateDisplay').textContent = 'Showing both states for comparison';
        }
        
        function resetDemo() {
            const panelOpen = document.getElementById('panelOpen');
            const panelClosed = document.getElementById('panelClosed');
            
            panelOpen.classList.remove('hidden');
            panelOpen.querySelector('.agent-handle').classList.remove('collapsed');
            panelOpen.querySelector('.handle-chevron').textContent = '‹';
            
            panelClosed.classList.add('hidden');
            panelClosed.querySelector('.agent-handle').classList.add('collapsed');
            panelClosed.querySelector('.handle-chevron').textContent = '›';
            
            document.getElementById('stateDisplay').textContent = 'Demo reset - Click handles to interact';
        }
        
        // Initialize
        resetDemo();
    </script>
</body>
</html>