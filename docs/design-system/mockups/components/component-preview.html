<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Giki.AI Component Preview - Visual Design System</title>
    <style>
        * { box-sizing: border-box; }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 0;
            background: #F9FAFB;
            color: #374151;
            line-height: 1.5;
        }
        
        .header {
            text-align: center;
            padding: 40px 20px;
            background: white;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .header h1 {
            color: #295343;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #6B7280;
            font-size: 1.1rem;
        }
        
        .component-section {
            margin: 40px auto;
            max-width: 1200px;
            padding: 0 20px;
        }
        
        .component-section h2 {
            color: #295343;
            font-size: 1.8rem;
            margin-bottom: 30px;
            border-bottom: 3px solid #295343;
            padding-bottom: 10px;
        }
        
        .layout-demo {
            display: grid;
            height: 600px;
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .layout-normal {
            grid-template-columns: 240px 1fr 0px;
        }
        
        .layout-agent-open {
            grid-template-columns: 64px 1fr 400px;
        }
        
        .nav-panel.collapsed {
            width: 64px;
        }
        
        .nav-panel.collapsed .nav-item span:not(.nav-item-icon) {
            display: none;
        }
        
        .nav-panel.collapsed .user-info span:not([style]) {
            display: none;
        }
        
        .agent-panel.closed {
            display: none;
        }
        
        /* Navigation Panel */
        .nav-panel {
            background: #FAFAFA;
            border-right: 1px solid #E5E7EB;
            padding: 0;
            display: flex;
            flex-direction: column;
        }
        
        .nav-header {
            padding: 20px;
            border-bottom: 1px solid #E5E7EB;
            background: white;
            text-align: center;
        }
        
        .nav-header .logo {
            color: #295343;
            font-weight: 700;
            font-size: 18px;
        }
        
        .nav-items {
            flex: 1;
            padding: 20px 0;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s;
            margin: 2px 12px;
            border-radius: 6px;
        }
        
        .nav-item:hover {
            background: #E8F5E8;
            color: #295343;
            transform: translateX(2px);
        }
        
        .nav-item.active {
            background: #E8F5E8;
            color: #295343;
            border-right: 3px solid #295343;
            font-weight: 500;
        }
        
        .nav-item-icon {
            margin-right: 12px;
            font-size: 16px;
            width: 16px;
            height: 16px;
            color: #6B7280;
        }
        
        .nav-item.active .nav-item-icon {
            color: #295343;
        }
        
        .nav-footer {
            padding: 20px;
            border-top: 1px solid #E5E7EB;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            padding: 12px;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
        }
        
        .user-avatar {
            background: #295343;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-right: 12px;
        }
        
        /* Main Content */
        .main-content {
            padding: 30px;
            overflow-y: auto;
            background: white;
        }
        
        .main-header {
            margin-bottom: 30px;
        }
        
        .main-header h1 {
            color: #295343;
            font-size: 1.8rem;
            margin-bottom: 8px;
        }
        
        .main-header p {
            color: #6B7280;
            margin: 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #E8F5E8 0%, #B4F0C5 100%);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #D1F7DD;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #295343;
            font-family: 'Monaco', monospace;
        }
        
        .stat-label {
            color: #295343;
            font-size: 14px;
            margin-top: 4px;
        }
        
        /* Agent Panel */
        .agent-panel {
            background: white;
            border-left: 1px solid #E5E7EB;
            display: flex;
            flex-direction: column;
        }
        
        .agent-header {
            background: linear-gradient(to right, #295343 0%, #295343 65%, #1A3F5F 80%, #3F1A5F 90%, #5F1A1A 100%);
            color: #FEFEFE;
            padding: 20px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .agent-close {
            background: none;
            border: none;
            color: #FEFEFE;
            cursor: pointer;
            font-size: 18px;
        }
        
        .agent-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .agent-message {
            padding: 12px 16px;
            border-radius: 8px;
            max-width: 90%;
        }
        
        .agent-message.ai {
            background: #F3F4F6;
            color: #374151;
            align-self: flex-start;
            border-left: 3px solid #295343;
        }
        
        .agent-message.user {
            background: #295343;
            color: #FEFEFE;
            align-self: flex-end;
        }
        
        .agent-input {
            padding: 20px;
            border-top: 1px solid #E5E7EB;
        }
        
        .agent-input-field {
            width: 100%;
            padding: 12px;
            border: 1px solid #E5E7EB;
            border-radius: 6px;
            font-size: 14px;
            resize: none;
        }
        
        /* Buttons */
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            font-size: 14px;
            display: inline-block;
        }
        
        .btn-primary {
            background: #295343;
            color: #FEFEFE;
        }
        
        .btn-primary:hover {
            background: #1D372E;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: white;
            color: #295343;
            border: 1px solid #295343;
        }
        
        .btn-secondary:hover {
            background: #E8F5E8;
        }
        
        .btn-success {
            background: #B4F0C5;
            color: #295343;
        }
        
        .btn-warning {
            background: #E67E22;
            color: white;
        }
        
        .btn-error {
            background: #DC2626;
            color: white;
        }
        
        /* Inline Status Areas */
        .status-bar {
            margin: 20px 0;
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }
        
        .status-item {
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            border-left: 4px solid transparent;
        }
        
        .status-success {
            background: #F0F9FF;
            color: #295343;
            border-left-color: #295343;
        }
        
        .status-warning {
            background: #FFF7ED;
            color: #C2410C;
            border-left-color: #EA580C;
        }
        
        .status-error {
            background: #FEF2F2;
            color: #DC2626;
            border-left-color: #DC2626;
        }
        
        .status-info {
            background: #F0F9FF;
            color: #1A3F5F;
            border-left-color: #1A3F5F;
        }
        
        /* Data Table */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #E5E7EB;
        }
        
        .data-table th {
            background: #FAFAFA;
            color: #374151;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .data-table td {
            padding: 12px 16px;
            color: #374151;
            font-size: 14px;
            border-bottom: 1px solid #F3F4F6;
        }
        
        .data-table tr:hover {
            background: #F9FAFB;
        }
        
        .data-table tr.selected {
            background: #E8F5E8;
            color: #295343;
        }
        
        /* Forms */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 6px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #E5E7EB;
            border-radius: 6px;
            font-size: 14px;
            color: #374151;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #295343;
            box-shadow: 0 0 0 3px rgba(41, 83, 67, 0.1);
        }
        
        /* Agent Panel Border Handle */
        .agent-handle {
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: #295343;
            border: 2px solid white;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .agent-handle:hover {
            background: #1D372E;
            transform: translateY(-50%) scale(1.05);
        }
        
        .component-demo {
            background: white;
            padding: 30px;
            border-radius: 12px;
            border: 1px solid #E5E7EB;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Giki.AI Component Preview</h1>
        <p>Visual design system implementation using planned color combinations</p>
        <p><strong>Three-Panel Layout</strong> | Professional B2B Financial Platform</p>
    </div>

    <div class="component-section">
        <h2>🖥️ Layout State 1: Work-Focused (Normal)</h2>
        <p style="color: #6B7280; margin-bottom: 20px;">Main panel gets maximum space for work visibility. Left navigation expanded for easy access.</p>
        <div class="layout-demo layout-normal">
            <!-- Left Navigation - Expanded -->
            <div class="nav-panel">
                <div class="nav-header">
                    <div class="logo" style="display: flex; align-items: center; gap: 8px; cursor: pointer; transition: all 0.3s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                        <svg width="28" height="28" viewBox="0 0 32 32" style="fill: #295343;">
                            <path d="M16 2L2 8v16l14 6 14-6V8L16 2zm0 3.2L26.4 9.6 16 14 5.6 9.6 16 5.2zM6 11.4L15 16v12.6L6 24V11.4zm20 0v12.6L17 28.6V16l9-4.6z"/>
                        </svg>
                        <span style="font-weight: 700; font-size: 1.1rem;">giki.ai</span>
                    </div>
                </div>
                <div class="nav-items">
                    <a href="#" class="nav-item active">
                        <span class="nav-item-icon">□</span>
                        <span>Dashboard</span>
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-item-icon">⊞</span>
                        <span>Transactions</span>
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-item-icon">∷</span>
                        <span>Reports</span>
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-item-icon">⚬</span>
                        <span>Categories</span>
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-item-icon">↑</span>
                        <span>Upload</span>
                    </a>
                </div>
                <div class="nav-footer">
                    <div class="user-info">
                        <span class="user-avatar">NS</span>
                        <span>Nikhil Singh</span>
                    </div>
                </div>
            </div>
            
            <!-- Main Content - Maximum Width -->
            <div class="main-content">
                <div class="main-header">
                    <h1>Financial Processing Overview</h1>
                    <p>Maximum space for work - transactions, reports, data analysis</p>
                    
                    <!-- Inline Status Bar - No floating notifications -->
                    <div class="status-bar">
                        <div class="status-item status-success">
                            Processing Complete: 1,247 transactions categorized
                        </div>
                        <div class="status-item status-warning">
                            Review Required: 23 transactions need attention
                        </div>
                    </div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">Transactions Categorized</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">$458.2K</div>
                        <div class="stat-label">Processed Automatically</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">96.4%</div>
                        <div class="stat-label">Accuracy · No work needed</div>
                    </div>
                </div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Amount</th>
                            <th>Category</th>
                            <th>Confidence</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Jan 15, 2025</td>
                            <td>STARBUCKS #1234 SF</td>
                            <td>$12.50</td>
                            <td>Food & Dining</td>
                            <td>98%</td>
                        </tr>
                        <tr class="selected">
                            <td>Jan 14, 2025</td>
                            <td>AWS Services - EC2</td>
                            <td>$89.23</td>
                            <td>Software & Technology</td>
                            <td>99%</td>
                        </tr>
                        <tr>
                            <td>Jan 13, 2025</td>
                            <td>Office Depot Supplies</td>
                            <td>$45.67</td>
                            <td>Office Expenses</td>
                            <td>95%</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="margin-top: 20px; padding: 16px; background: #F0F9FF; border-radius: 8px; border-left: 4px solid #0EA5E9;">
                    <p style="margin: 0; color: #0369A1; font-size: 14px;">
                        ⚬ <strong>Work-focused layout:</strong> Agent available via FAB when needed, main panel maximized for productivity
                    </p>
                </div>
            </div>
            
            <!-- Agent Panel - Hidden -->
            <div class="agent-panel closed"></div>
        </div>
    </div>

    <div class="component-section">
        <h2>🤖 Layout State 2: Agent Assistance Mode</h2>
        <p style="color: #6B7280; margin-bottom: 20px;">Left panel collapses to icons-only, agent panel opens. Still work-focused but with AI assistance.</p>
        <div class="layout-demo layout-agent-open">
            <!-- Left Navigation - Collapsed -->
            <div class="nav-panel collapsed">
                <div class="nav-header">
                    <div class="logo" style="display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s;" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                        <svg width="24" height="24" viewBox="0 0 32 32" style="fill: #295343;">
                            <path d="M16 2L2 8v16l14 6 14-6V8L16 2zm0 3.2L26.4 9.6 16 14 5.6 9.6 16 5.2zM6 11.4L15 16v12.6L6 24V11.4zm20 0v12.6L17 28.6V16l9-4.6z"/>
                        </svg>
                    </div>
                </div>
                <div class="nav-items">
                    <a href="#" class="nav-item active">
                        <span class="nav-item-icon">□</span>
                        <span>Dashboard</span>
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-item-icon">⊞</span>
                        <span>Transactions</span>
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-item-icon">∷</span>
                        <span>Reports</span>
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-item-icon">⚬</span>
                        <span>Categories</span>
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-item-icon">↑</span>
                        <span>Upload</span>
                    </a>
                </div>
                <div class="nav-footer">
                    <div class="user-info">
                        <span class="user-avatar">NS</span>
                        <span>Nikhil Singh</span>
                    </div>
                </div>
            </div>
            
            <!-- Main Content - Still Prominent -->
            <div class="main-content">
                <div class="main-header">
                    <h1>Financial Processing Overview</h1>
                    <p>Work remains visible and interactive while getting AI assistance</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">Transactions Categorized</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">$458.2K</div>
                        <div class="stat-label">Processed Automatically</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">96.4%</div>
                        <div class="stat-label">Accuracy · No work needed</div>
                    </div>
                </div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Amount</th>
                            <th>Category</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Jan 15, 2025</td>
                            <td>STARBUCKS #1234</td>
                            <td>$12.50</td>
                            <td>Food & Dining</td>
                        </tr>
                        <tr class="selected">
                            <td>Jan 14, 2025</td>
                            <td>AWS Services</td>
                            <td>$89.23</td>
                            <td>Software & Technology</td>
                        </tr>
                        <tr>
                            <td>Jan 13, 2025</td>
                            <td>Office Depot</td>
                            <td>$45.67</td>
                            <td>Office Expenses</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Agent Panel - Open and Resizable -->
            <div class="agent-panel">
                <div class="agent-header">
                    <span>giki Assistant</span>
                    <button class="agent-close">×</button>
                </div>
                <div class="agent-messages">
                    <div class="agent-message ai">
                        I can see you're reviewing transactions. Need help with categorization or analysis?
                    </div>
                    <div class="agent-message user">
                        Generate P&L for Q1 with these transactions
                    </div>
                    <div class="agent-message ai">
                        Q1 P&L Summary:<br><br>
                        <strong>Revenue:</strong> $458.2K<br>
                        <strong>Expenses:</strong> $312.1K<br>
                        <strong>Net Profit:</strong> $146.1K<br><br>
                        ∷ Exporting detailed report to Excel...<br>
                        □ Download ready: <u>Q1-2025-PL.xlsx</u>
                    </div>
                </div>
                <div class="agent-input">
                    <textarea class="agent-input-field" placeholder="Ask about your data..." rows="2"></textarea>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 16px; background: #F5F3FF; border-radius: 8px; border-left: 4px solid #8B5CF6;">
            <p style="margin: 0; color: #6D28D9; font-size: 14px;">
                ↑ <strong>Smart panel management:</strong> Agent panel width: 400px (resizable 320-480px). Main work stays prominent and interactive.
            </p>
        </div>
    </div>

    <!-- CUSTOMER JOURNEY PHASE 1: PROSPECT/LANDING -->
    <div class="component-section">
        <h2>↑ Phase 1: Landing Page & Login Components</h2>
        
        <!-- Landing Page Hero -->
        <h3>Landing Page Hero</h3>
        <div style="display: grid; grid-template-columns: 1fr 350px; gap: 40px; padding: 40px; background: white; border: 1px solid #E5E7EB; border-radius: 12px; margin-bottom: 30px;">
            <div style="display: flex; flex-direction: column; justify-content: center;">
                <h1 style="font-size: 2.5rem; font-weight: bold; color: #295343; margin-bottom: 20px;">Complete Automated MIS Preparation</h1>
                <p style="font-size: 1.1rem; color: #6B7280; margin-bottom: 20px;">Upload financial data and receive complete Management Information Systems reports automatically.</p>
                
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                        <span style="color: #295343;">∷</span>
                        <span>Transaction processing</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                        <span style="color: #295343;">📈</span>
                        <span>Financial reporting</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                        <span style="color: #295343;">⚬</span>
                        <span>Spend analysis</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                        <span style="color: #295343;">✅</span>
                        <span>Accuracy validation</span>
                    </div>
                </div>
                
                <p style="font-size: 1.1rem; margin-bottom: 30px;"><strong>Just upload your files. We do the rest.</strong></p>
                
                <button style="background: #295343; color: white; padding: 16px 32px; border: none; border-radius: 8px; font-size: 1.1rem; font-weight: 600; cursor: pointer;">Get Started Free</button>
            </div>
            
            <!-- Login Panel -->
            <div style="background: #F9FAFB; padding: 30px; border-radius: 12px; border: 1px solid #E5E7EB;">
                <h3 style="text-align: center; margin-bottom: 20px; color: #374151;">Welcome Back</h3>
                
                <div style="margin-bottom: 16px;">
                    <input type="email" placeholder="Email Address" style="width: 100%; padding: 12px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px;">
                </div>
                
                <div style="margin-bottom: 16px;">
                    <input type="password" placeholder="Password" style="width: 100%; padding: 12px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px;">
                </div>
                
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 20px;">
                    <input type="checkbox" id="remember">
                    <label for="remember" style="font-size: 14px; color: #6B7280;">Remember me</label>
                </div>
                
                <button style="width: 100%; background: #295343; color: white; padding: 12px; border: none; border-radius: 6px; font-weight: 600; margin-bottom: 16px; cursor: pointer;">Sign In</button>
                
                <div style="text-align: center;">
                    <a href="#" style="color: #295343; text-decoration: none; font-size: 14px;">Forgot password?</a>
                </div>
                
                <hr style="margin: 20px 0; border: none; border-top: 1px solid #E5E7EB;">
                
                <div style="text-align: center;">
                    <span style="font-size: 14px; color: #6B7280;">New to giki.ai?</span><br>
                    <a href="#" style="color: #295343; text-decoration: none; font-weight: 600;">Create account →</a>
                </div>
            </div>
        </div>
        
        <!-- Trust Element -->
        <div style="background: #F9FAFB; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 40px; border-left: 4px solid #295343;">
            <p style="font-style: italic; color: #374151; margin-bottom: 8px;">"Finally, I can focus on strategy instead of spreadsheets"</p>
            <p style="font-size: 14px; color: #6B7280;">— Finance Director</p>
        </div>
    </div>
    
    <!-- CUSTOMER JOURNEY PHASE 2: FIRST LOGIN & ONBOARDING -->
    <div class="component-section">
        <h2>⚬ Phase 2: First Login & Three Onboarding Paths</h2>
        
        <!-- Onboarding Path Selection -->
        <h3>Onboarding Path Selection (Three Customer Approaches)</h3>
        <div style="background: white; padding: 30px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="color: #374151; margin-bottom: 10px;">Choose Your Getting Started Approach</h2>
                <p style="color: #6B7280;">Three ways to get the most accurate AI categorization for your business</p>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px; margin-bottom: 30px;">
                
                <!-- Path 1: Historical Data -->
                <div style="background: white; border: 2px solid #295343; border-radius: 12px; padding: 24px; cursor: pointer; transition: all 0.3s;" onmouseover="this.style.boxShadow='0 8px 25px rgba(41, 83, 67, 0.15)'" onmouseout="this.style.boxShadow='none'">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                        <span style="color: #295343; font-size: 24px;">∷</span>
                        <h3 style="color: #295343; margin: 0;">Historical Data</h3>
                        <span style="background: #DCFCE7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 11px; margin-left: auto;">Recommended</span>
                    </div>
                    <p style="color: #6B7280; font-size: 14px; margin-bottom: 16px;">I have transaction files WITH existing categories</p>
                    
                    <div style="background: #F0F9F4; border: 1px solid #A7F3D0; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
                        <div style="font-weight: 600; color: #065F46; font-size: 14px; margin-bottom: 8px;">Best For:</div>
                        <div style="color: #065F46; font-size: 13px;">
                            <div>• Companies with existing categorization</div>
                            <div>• Want 95%+ accuracy from day one</div>
                            <div>• Have historical files with categories</div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 16px;">
                        <div style="color: #295343; font-size: 14px; font-weight: 600; margin-bottom: 8px;">AI Mode: Historical Data + RAG</div>
                        <div style="color: #6B7280; font-size: 13px;">Uses your existing patterns for maximum accuracy</div>
                    </div>
                    
                    <button style="width: 100%; background: #295343; color: white; padding: 12px; border: none; border-radius: 6px; font-weight: 600; cursor: pointer;">Select This Path →</button>
                </div>
                
                <!-- Path 2: Schema Only -->
                <div style="background: white; border: 2px solid #E5E7EB; border-radius: 12px; padding: 24px; cursor: pointer; transition: all 0.3s;" onmouseover="this.style.borderColor='#295343'; this.style.boxShadow='0 8px 25px rgba(41, 83, 67, 0.15)'" onmouseout="this.style.borderColor='#E5E7EB'; this.style.boxShadow='none'">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                        <span style="color: #7C3AED; font-size: 24px;">🗂️</span>
                        <h3 style="color: #374151; margin: 0;">Schema Only</h3>
                        <span style="background: #F3E8FF; color: #7C3AED; padding: 2px 8px; border-radius: 12px; font-size: 11px; margin-left: auto;">Schema Guided</span>
                    </div>
                    <p style="color: #6B7280; font-size: 14px; margin-bottom: 16px;">I have a chart of accounts or categorization schema</p>
                    
                    <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
                        <div style="font-weight: 600; color: #374151; font-size: 14px; margin-bottom: 8px;">Best For:</div>
                        <div style="color: #6B7280; font-size: 13px;">
                            <div>• Have GL codes or category hierarchy</div>
                            <div>• Want AI to follow specific schema</div>
                            <div>• No historical categorized files</div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 16px;">
                        <div style="color: #7C3AED; font-size: 14px; font-weight: 600; margin-bottom: 8px;">AI Mode: Schema-Guided Zero-Onboarding</div>
                        <div style="color: #6B7280; font-size: 13px;">AI learns your intended structure</div>
                    </div>
                    
                    <button style="width: 100%; background: white; border: 1px solid #D1D5DB; color: #374151; padding: 12px; border-radius: 6px; font-weight: 600; cursor: pointer;">Select This Path →</button>
                </div>
                
                <!-- Path 3: Pure Zero-Onboarding -->
                <div style="background: white; border: 2px solid #E5E7EB; border-radius: 12px; padding: 24px; cursor: pointer; transition: all 0.3s;" onmouseover="this.style.borderColor='#295343'; this.style.boxShadow='0 8px 25px rgba(41, 83, 67, 0.15)'" onmouseout="this.style.borderColor='#E5E7EB'; this.style.boxShadow='none'">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                        <span style="color: #059669; font-size: 24px;">↑</span>
                        <h3 style="color: #374151; margin: 0;">Zero-Onboarding</h3>
                        <span style="background: #ECFDF5; color: #059669; padding: 2px 8px; border-radius: 12px; font-size: 11px; margin-left: auto;">AI Bootstrap</span>
                    </div>
                    <p style="color: #6B7280; font-size: 14px; margin-bottom: 16px;">Just start using it - AI figures everything out</p>
                    
                    <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
                        <div style="font-weight: 600; color: #374151; font-size: 14px; margin-bottom: 8px;">Best For:</div>
                        <div style="color: #6B7280; font-size: 13px;">
                            <div>• Want to start immediately</div>
                            <div>• No existing categorization</div>
                            <div>• Trust AI to create smart categories</div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 16px;">
                        <div style="color: #059669; font-size: 14px; font-weight: 600; margin-bottom: 8px;">AI Mode: Pure Zero-Onboarding</div>
                        <div style="color: #6B7280; font-size: 13px;">AI creates intelligent business hierarchies</div>
                    </div>
                    
                    <button style="width: 100%; background: white; border: 1px solid #D1D5DB; color: #374151; padding: 12px; border-radius: 6px; font-weight: 600; cursor: pointer;">Select This Path →</button>
                </div>
            </div>
            
            <div style="background: #F8FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 16px;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span style="color: #475569;">⚬</span>
                    <span style="font-weight: 600; color: #475569;">Not Sure Which Path?</span>
                </div>
                <p style="color: #475569; font-size: 14px; margin: 0;">Most customers choose Historical Data if they have any existing categorization. Schema Only if they have GL codes but no categorized files. Zero-Onboarding to start immediately without preparation.</p>
            </div>
        </div>
        
        <!-- Schema Upload Interface (Path 2) -->
        <h3>Schema Upload Interface (Chart of Accounts / GL Codes)</h3>
        <div style="background: white; padding: 30px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                <span style="color: #7C3AED;">🗂️</span>
                <h2 style="color: #374151; margin: 0;">Upload Your Category Schema</h2>
                <span style="background: #F3E8FF; color: #7C3AED; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: auto;">Schema-Guided Mode</span>
            </div>
            <p style="color: #6B7280; margin-bottom: 30px;">Provide your chart of accounts or category hierarchy so AI can categorize according to your intended structure</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                
                <!-- Schema File Upload -->
                <div>
                    <h3 style="color: #374151; margin-bottom: 15px;">Option 1: Upload Schema File</h3>
                    <div style="border: 2px dashed #7C3AED; border-radius: 12px; padding: 40px 30px; text-align: center; background: #FDFAFF; margin-bottom: 20px;">
                        <div style="font-size: 40px; margin-bottom: 16px;">□</div>
                        <p style="font-size: 16px; font-weight: 500; margin-bottom: 8px; color: #374151;">Drop schema file here</p>
                        <p style="color: #6B7280; margin-bottom: 4px;">Excel or CSV with your category structure</p>
                        <p style="color: #6B7280; font-size: 14px;">Example: GL codes, category names, hierarchy</p>
                    </div>
                    
                    <div style="background: #F3E8FF; border: 1px solid #C4B5FD; border-radius: 8px; padding: 16px;">
                        <div style="color: #5B21B6; font-size: 14px; margin-bottom: 8px;"><strong>Schema File Requirements:</strong></div>
                        <div style="color: #5B21B6; font-size: 13px;">
                            <div>• Category names (required)</div>
                            <div>• GL codes (optional)</div>
                            <div>• Parent-child relationships (optional)</div>
                            <div>• Use ">" for hierarchy: "Business > Travel"</div>
                        </div>
                    </div>
                </div>
                
                <!-- Manual Schema Entry -->
                <div>
                    <h3 style="color: #374151; margin-bottom: 15px;">Option 2: Build Schema Manually</h3>
                    <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; margin-bottom: 8px; color: #374151;">Add Categories</label>
                            <input type="text" placeholder="Business Expenses > Travel > Flights" style="width: 100%; padding: 12px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px; margin-bottom: 8px;">
                            <input type="text" placeholder="Business Expenses > Office > Supplies" style="width: 100%; padding: 12px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px; margin-bottom: 8px;">
                            <input type="text" placeholder="Business Expenses > Technology > Software" style="width: 100%; padding: 12px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px;">
                        </div>
                        
                        <button style="background: white; border: 1px solid #D1D5DB; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer;">+ Add More Categories</button>
                    </div>
                    
                    <div style="background: #EFF6FF; border: 1px solid #93C5FD; border-radius: 8px; padding: 16px;">
                        <div style="color: #1E40AF; font-size: 14px; margin-bottom: 8px;"><strong>How This Works:</strong></div>
                        <div style="color: #1E40AF; font-size: 13px;">
                            <div>• AI learns your category structure</div>
                            <div>• Future transactions follow your schema</div>
                            <div>• Hierarchical structure enables better reporting</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Preview of Schema Structure -->
            <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h4 style="color: #374151; margin-bottom: 12px;">Schema Preview</h4>
                <div style="font-family: monospace; font-size: 13px; color: #374151;">
                    <div style="margin-bottom: 4px;">Business Expenses</div>
                    <div style="margin-bottom: 4px; margin-left: 16px;">├─ Travel & Transportation</div>
                    <div style="margin-bottom: 4px; margin-left: 32px;">│  ├─ Flights</div>
                    <div style="margin-bottom: 4px; margin-left: 32px;">│  └─ Hotels</div>
                    <div style="margin-bottom: 4px; margin-left: 16px;">├─ Office Supplies</div>
                    <div style="margin-left: 16px;">└─ Technology & Software</div>
                </div>
            </div>
            
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <button style="background: white; border: 1px solid #D1D5DB; padding: 12px 24px; border-radius: 6px; font-weight: 600; cursor: pointer;">← Back to Path Selection</button>
                <button style="background: #7C3AED; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-weight: 600; cursor: pointer;">Continue with Schema →</button>
            </div>
        </div>
        
        <!-- Pure Zero-Onboarding Flow -->
        <h3>Pure Zero-Onboarding Flow (No Setup Required)</h3>
        <div style="background: white; padding: 30px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                <span style="color: #059669;">↑</span>
                <h2 style="color: #374151; margin: 0;">Start Using Immediately</h2>
                <span style="background: #ECFDF5; color: #059669; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: auto;">Zero Setup</span>
            </div>
            <p style="color: #6B7280; margin-bottom: 30px;">Just upload your transaction files - our AI will figure out the best categorization structure for your business</p>
            
            <div style="border: 2px dashed #059669; border-radius: 12px; padding: 60px 40px; text-align: center; background: #F0FDF4; margin-bottom: 30px;">
                <div style="font-size: 48px; margin-bottom: 20px;">↑</div>
                <p style="font-size: 18px; font-weight: 500; margin-bottom: 8px; color: #374151;">Drop any transaction files here</p>
                <p style="color: #6B7280; margin-bottom: 4px;">No categories needed - AI creates them automatically</p>
                <p style="color: #6B7280; font-size: 14px;">Excel, CSV, bank exports - we handle it all</p>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                <div style="background: #F0FDF4; border: 1px solid #A7F3D0; border-radius: 8px; padding: 16px;">
                    <div style="color: #065F46; font-size: 14px; margin-bottom: 8px;"><strong>What AI Will Do:</strong></div>
                    <div style="color: #065F46; font-size: 13px;">
                        <div>• Analyze transaction patterns</div>
                        <div>• Create intelligent business categories</div>
                        <div>• Build hierarchical structure automatically</div>
                        <div>• Learn and improve over time</div>
                    </div>
                </div>
                
                <div style="background: #EFF6FF; border: 1px solid #93C5FD; border-radius: 8px; padding: 16px;">
                    <div style="color: #1E40AF; font-size: 14px; margin-bottom: 8px;"><strong>Expected Results:</strong></div>
                    <div style="color: #1E40AF; font-size: 13px;">
                        <div>• 85%+ accuracy from first upload</div>
                        <div>• Improves to 95%+ with more data</div>
                        <div>• Smart business category hierarchy</div>
                        <div>• Ready for production use immediately</div>
                    </div>
                </div>
            </div>
            
            <div style="background: #FEF3C7; border: 1px solid #FDE68A; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span style="color: #D97706;">↑</span>
                    <span style="font-weight: 600; color: #92400E;">AI Business Intelligence Mode</span>
                </div>
                <p style="color: #92400E; font-size: 14px; margin: 0;">Our AI has business intelligence built-in. It understands common business expense patterns and will create professional categories like "Business > Travel > Flights" automatically.</p>
            </div>
            
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <button style="background: white; border: 1px solid #D1D5DB; padding: 12px 24px; border-radius: 6px; font-weight: 600; cursor: pointer;">← Back to Path Selection</button>
                <button style="background: #059669; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-weight: 600; cursor: pointer;">Start Zero-Onboarding →</button>
            </div>
        </div>
        
        <!-- Welcome Hero Card -->
        <h3>Welcome Hero Card</h3>
        <div style="background: white; padding: 40px; border-radius: 12px; text-align: center; max-width: 600px; margin: 0 auto 30px; border: 1px solid #E5E7EB; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
            <h1 style="font-size: 1.8rem; font-weight: 600; margin-bottom: 10px; color: #374151;">Welcome to giki.ai, Sarah! 👋</h1>
            
            <div style="margin-top: 30px;">
                <div style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 20px;">
                    <span style="color: #295343; font-size: 1.2rem;">⚬</span>
                    <h2 style="font-size: 1.3rem; font-weight: 600; color: #374151;">Let's Get You Started</h2>
                </div>
                
                <p style="color: #6B7280; margin-bottom: 30px;">We'll have you up and running in just 10 minutes.<br>Here's what we'll do together:</p>
                
                <div style="text-align: left; max-width: 400px; margin: 0 auto 30px;">
                    <div style="display: flex; gap: 12px; margin-bottom: 12px; align-items: start;">
                        <span style="font-size: 1.1rem;">1️⃣</span>
                        <span style="font-size: 14px; color: #374151;">Upload your historical data (with categories)</span>
                    </div>
                    <div style="display: flex; gap: 12px; margin-bottom: 12px; align-items: start;">
                        <span style="font-size: 1.1rem;">2️⃣</span>
                        <span style="font-size: 14px; color: #374151;">We'll learn your categorization patterns</span>
                    </div>
                    <div style="display: flex; gap: 12px; margin-bottom: 12px; align-items: start;">
                        <span style="font-size: 1.1rem;">3️⃣</span>
                        <span style="font-size: 14px; color: #374151;">Test our accuracy together</span>
                    </div>
                    <div style="display: flex; gap: 12px; margin-bottom: 12px; align-items: start;">
                        <span style="font-size: 1.1rem;">4️⃣</span>
                        <span style="font-size: 14px; color: #374151;">Go live with automatic categorization!</span>
                    </div>
                </div>
                
                <button style="background: #295343; color: white; padding: 16px 32px; border: none; border-radius: 8px; font-size: 1rem; font-weight: 600; cursor: pointer;">Start Setup →</button>
            </div>
        </div>
        
        <!-- Benefits & Time Grid -->
        <h3>Benefits & Time Estimation Grid</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 40px; max-width: 800px; margin-left: auto; margin-right: auto;">
            <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 20px;">
                    <span style="color: #295343;">∷</span>
                    <h3 style="font-weight: 600; color: #374151;">What You'll Get</h3>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span style="color: #16A34A;">□</span>
                    <span style="font-size: 14px;">95%+ accuracy on categorization</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span style="color: #16A34A;">□</span>
                    <span style="font-size: 14px;">Automatic processing going forward</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span style="color: #16A34A;">□</span>
                    <span style="font-size: 14px;">Export-ready reports</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="color: #16A34A;">□</span>
                    <span style="font-size: 14px;">Real-time insights</span>
                </div>
            </div>
            
            <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 20px;">
                    <span style="color: #295343;">⏱️</span>
                    <h3 style="font-weight: 600; color: #374151;">Time Estimate</h3>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span style="font-size: 14px;">• Upload files:</span>
                    <span style="font-size: 14px; color: #6B7280;">2 min</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span style="font-size: 14px;">• AI training:</span>
                    <span style="font-size: 14px; color: #6B7280;">5 min</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                    <span style="font-size: 14px;">• Validation:</span>
                    <span style="font-size: 14px; color: #6B7280;">3 min</span>
                </div>
                <hr style="margin: 12px 0; border: none; border-top: 1px solid #E5E7EB;">
                <div style="display: flex; justify-content: space-between; font-weight: 600;">
                    <span style="font-size: 14px;">Total:</span>
                    <span style="font-size: 14px;">~10 minutes</span>
                </div>
            </div>
        </div>
        
        <!-- Historical Data Upload (WITH Categories Required) -->
        <h3>Historical Data Upload (Onboarding)</h3>
        <div style="background: white; padding: 30px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <h2 style="text-align: center; margin-bottom: 20px; color: #374151;">Upload Historical Data</h2>
            <p style="text-align: center; color: #6B7280; margin-bottom: 30px;">We need files WITH existing categories to train the AI</p>
            
            <div style="border: 2px dashed #295343; border-radius: 12px; padding: 60px 40px; text-align: center; background: #F0F9F4; margin-bottom: 20px;">
                <div style="font-size: 48px; margin-bottom: 20px;">□</div>
                <p style="font-size: 18px; font-weight: 500; margin-bottom: 8px; color: #374151;">Drop your historical files here</p>
                <p style="color: #6B7280; margin-bottom: 4px;">Must include existing categories for training</p>
                <p style="color: #6B7280; font-size: 14px;">Excel or CSV with category column</p>
            </div>
            
            <div style="background: #FEF3C7; border: 1px solid #F59E0B; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="color: #D97706;">⭐</span>
                    <span style="font-weight: 600; color: #92400E;">Category column required for AI training</span>
                </div>
                <p style="font-size: 14px; color: #92400E; margin-top: 8px;">Your file must have a column with existing transaction categories so we can learn your patterns.</p>
            </div>
        </div>
        
        <!-- AI-Powered Schema Interpretation with Confidence Scoring -->
        <h3>AI Schema Interpretation (Gemini 2.0 Flash)</h3>
        <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                <span style="color: #295343;">🧠</span>
                <h3 style="font-weight: 600; color: #374151;">AI Column Interpretation Results</h3>
                <span style="background: #DCFCE7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: auto;">Gemini 2.0 Flash</span>
            </div>
            <p style="color: #6B7280; margin-bottom: 20px;">Our AI analyzed your file structure using advanced pattern recognition:</p>
            
            <table style="width: 100%; border-collapse: collapse; border: 1px solid #E5E7EB;">
                <thead>
                    <tr style="background: #F9FAFB;">
                        <th style="padding: 12px; text-align: left; border: 1px solid #E5E7EB; font-weight: 600;">Your Column</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #E5E7EB; font-weight: 600;">AI Interpretation</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #E5E7EB; font-weight: 600;">Confidence</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #E5E7EB; font-weight: 600;">Sample Data</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #E5E7EB; font-weight: 600;">Override</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #E5E7EB; font-weight: 500;">Transaction Date</td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span style="color: #295343;">→</span>
                                <span style="font-weight: 500;">date</span>
                                <span style="background: #DCFCE7; color: #166534; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Required</span>
                            </div>
                        </td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                                <span style="font-family: monospace; font-size: 12px; font-weight: 600;">98%</span>
                            </div>
                        </td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB; color: #6B7280; font-size: 14px;">03/15/2025<br>03/14/2025</td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Fix</button>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #E5E7EB; font-weight: 500;">Description</td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span style="color: #295343;">→</span>
                                <span style="font-weight: 500;">description</span>
                                <span style="background: #DCFCE7; color: #166534; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Required</span>
                            </div>
                        </td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                                <span style="font-family: monospace; font-size: 12px; font-weight: 600;">96%</span>
                            </div>
                        </td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB; color: #6B7280; font-size: 14px;">AMZN Marketplace<br>Uber Trip</td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Fix</button>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #E5E7EB; font-weight: 500;">Amount</td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span style="color: #295343;">→</span>
                                <span style="font-weight: 500;">amount</span>
                                <span style="background: #DCFCE7; color: #166534; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Required</span>
                                <span style="background: #EFF6FF; color: #1E40AF; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Debit/Credit Detected</span>
                            </div>
                        </td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                                <span style="font-family: monospace; font-size: 12px; font-weight: 600;">94%</span>
                            </div>
                        </td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB; color: #6B7280; font-size: 14px;">-$45.67<br>-$123.45</td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Fix</button>
                        </td>
                    </tr>
                    <tr style="background: #FEF3C7;">
                        <td style="padding: 12px; border: 1px solid #E5E7EB; font-weight: 600;">Category ⭐</td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span style="color: #295343;">→</span>
                                <span style="font-weight: 500;">category</span>
                                <span style="background: #FEF3C7; color: #92400E; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Training Data</span>
                                <span style="background: #F3E8FF; color: #7C3AED; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Hierarchy Detected</span>
                            </div>
                        </td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                                <span style="font-family: monospace; font-size: 12px; font-weight: 600;">92%</span>
                            </div>
                        </td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB; color: #6B7280; font-size: 14px;">Business > Travel<br>Business > Office</td>
                        <td style="padding: 12px; border: 1px solid #E5E7EB;">
                            <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Fix</button>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <div style="background: #F0F9F4; border: 1px solid #A7F3D0; border-radius: 8px; padding: 16px; margin: 20px 0;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span style="color: #059669;">🧠</span>
                    <span style="font-weight: 600; color: #065F46;">AI Analysis Complete</span>
                </div>
                <div style="color: #065F46; font-size: 14px;">
                    <p style="margin: 0 0 8px;">□ Hierarchical category structure detected using ">" separator</p>
                    <p style="margin: 0 0 8px;">□ Debit/Credit pattern recognized in amount column</p>
                    <p style="margin: 0;">□ 847 sample transactions analyzed for pattern validation</p>
                </div>
            </div>
            
            <div style="display: flex; justify-content: space-between; margin-top: 20px;">
                <div style="display: flex; gap: 12px;">
                    <button style="background: white; border: 1px solid #D1D5DB; padding: 10px 20px; border-radius: 6px; cursor: pointer;">↑ Re-analyze</button>
                    <button style="background: white; border: 1px solid #D1D5DB; padding: 10px 20px; border-radius: 6px; cursor: pointer;">∷ View Stats</button>
                </div>
                <button style="background: #295343; color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer;">□ Confirm AI Mapping →</button>
            </div>
        </div>
        
        <!-- Category Hierarchy Detection Results -->
        <h3>Category Hierarchy Analysis</h3>
        <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                <span style="color: #7C3AED;">🌳</span>
                <h3 style="font-weight: 600; color: #374151;">Detected Category Hierarchy Structure</h3>
            </div>
            <p style="color: #6B7280; margin-bottom: 20px;">Our AI found hierarchical patterns in your categories. This is powerful for intelligent categorization!</p>
            
            <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4 style="color: #374151; margin-bottom: 12px;">Pattern Detected</h4>
                        <div style="color: #6B7280; font-size: 14px; margin-bottom: 8px;">Separator: <span style="font-family: monospace; background: #F3F4F6; padding: 2px 4px; border-radius: 4px;">" > "</span></div>
                        <div style="color: #6B7280; font-size: 14px; margin-bottom: 8px;">Type: <span style="font-weight: 500;">Hierarchical</span></div>
                        <div style="color: #6B7280; font-size: 14px;">Confidence: <span style="color: #16A34A; font-weight: 600;">95%</span></div>
                    </div>
                    <div>
                        <h4 style="color: #374151; margin-bottom: 12px;">Sample Hierarchy</h4>
                        <div style="font-family: monospace; font-size: 13px; color: #374151;">
                            <div style="margin-bottom: 4px;">Business Expenses</div>
                            <div style="margin-bottom: 4px; margin-left: 16px;">├─ Travel & Transportation</div>
                            <div style="margin-bottom: 4px; margin-left: 16px;">├─ Office Supplies</div>
                            <div style="margin-left: 16px;">└─ Technology & Software</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="background: #F3E8FF; border: 1px solid #C4B5FD; border-radius: 8px; padding: 16px;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span style="color: #7C3AED;">↑</span>
                    <span style="font-weight: 600; color: #5B21B6;">Hierarchy Advantage</span>
                </div>
                <p style="color: #5B21B6; font-size: 14px; margin: 0;">With hierarchical categories, our AI can create intelligent parent-child relationships and provide more accurate categorization. This enables sophisticated reporting and drill-down analysis.</p>
            </div>
        </div>
        
        <!-- AI Training Progress -->
        <h3>AI Training Progress Visualization</h3>
        <div style="background: white; padding: 30px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="color: #374151; margin-bottom: 10px;">Training AI on Your Patterns</h2>
                <p style="color: #6B7280;">Learning from 2,847 historical transactions...</p>
            </div>
            
            <div style="background: #F9FAFB; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <span style="font-weight: 600;">Overall Progress</span>
                    <span style="color: #6B7280;">85%</span>
                </div>
                <div style="background: #E5E7EB; height: 8px; border-radius: 4px; overflow: hidden;">
                    <div style="background: linear-gradient(to right, #295343, #16A34A); width: 85%; height: 100%;"></div>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div style="background: #F0F9F4; padding: 16px; border-radius: 8px; border-left: 4px solid #16A34A;">
                    <h4 style="color: #166534; margin-bottom: 8px;">Patterns Learned</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #166534;">847</div>
                    <p style="font-size: 14px; color: #166534;">Transaction patterns identified</p>
                </div>
                
                <div style="background: #EFF6FF; padding: 16px; border-radius: 8px; border-left: 4px solid #2563EB;">
                    <h4 style="color: #1E40AF; margin-bottom: 8px;">Categories Mapped</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #1E40AF;">23</div>
                    <p style="font-size: 14px; color: #1E40AF;">Unique categories understood</p>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <p style="color: #6B7280; font-size: 14px;">Estimated completion: 2 minutes</p>
            </div>
        </div>
        
        <!-- Schema Interpretation Analytics Dashboard -->
        <h3>Schema Interpretation Analytics Dashboard (Advanced AI Analysis)</h3>
        <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                <span style="color: #059669;">∷</span>
                <h3 style="font-weight: 600; color: #374151; margin: 0;">AI Schema Analysis Results</h3>
                <span style="background: #DCFCE7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px;">Gemini 2.0 Flash</span>
            </div>
            <p style="color: #6B7280; margin-bottom: 20px;">Deep analysis of file structure, patterns, and categorization intelligence by our AI</p>
            
            <!-- Analysis Performance Metrics -->
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px; margin-bottom: 20px;">
                <div style="background: #F0F9F4; padding: 16px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #166534;">247ms</div>
                    <div style="font-size: 13px; color: #166534;">Analysis Latency</div>
                </div>
                <div style="background: #EFF6FF; padding: 16px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #1E40AF;">95.2%</div>
                    <div style="font-size: 13px; color: #1E40AF;">Avg Confidence</div>
                </div>
                <div style="background: #F3E8FF; padding: 16px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #7C3AED;">7</div>
                    <div style="font-size: 13px; color: #7C3AED;">Columns Mapped</div>
                </div>
                <div style="background: #FEF3C7; padding: 16px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #92400E;">2,847</div>
                    <div style="font-size: 13px; color: #92400E;">Rows Analyzed</div>
                </div>
            </div>
            
            <!-- Pattern Recognition Results -->
            <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                <h4 style="color: #374151; margin-bottom: 12px;">AI Pattern Recognition Results</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <div style="color: #6B7280; font-size: 14px; margin-bottom: 8px;"><strong>Debit/Credit Pattern:</strong></div>
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                            <span style="font-size: 13px;">Negative values = Expenses (94% confidence)</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                            <span style="font-size: 13px;">Positive values = Income (91% confidence)</span>
                        </div>
                        
                        <div style="color: #6B7280; font-size: 14px; margin: 16px 0 8px;"><strong>Date Format Detection:</strong></div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                            <span style="font-size: 13px;">MM/DD/YYYY format (98% confidence)</span>
                        </div>
                    </div>
                    <div>
                        <div style="color: #6B7280; font-size: 14px; margin-bottom: 8px;"><strong>Category Structure:</strong></div>
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #7C3AED;"></div>
                            <span style="font-size: 13px;">Hierarchical with ">" separator (95% confidence)</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #7C3AED;"></div>
                            <span style="font-size: 13px;">Max depth: 3 levels (Business > Travel > Flights)</span>
                        </div>
                        
                        <div style="color: #6B7280; font-size: 14px; margin: 16px 0 8px;"><strong>Statistical Analysis:</strong></div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #2563EB;"></div>
                            <span style="font-size: 13px;">23 unique categories, 47 subcategories detected</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sample Data Analysis -->
            <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; overflow: hidden; margin-bottom: 20px;">
                <div style="background: #F9FAFB; padding: 12px; border-bottom: 1px solid #E5E7EB; font-weight: 600;">
                    <span>Sample Data Analysis (First 5 Rows)</span>
                </div>
                
                <div style="padding: 12px; border-bottom: 1px solid #F3F4F6;">
                    <div style="display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 12px; align-items: center;">
                        <div>
                            <div style="font-size: 13px; font-weight: 500;">Row 1</div>
                            <div style="font-size: 11px; color: #6B7280;">03/15/2025</div>
                        </div>
                        <div>
                            <div style="font-size: 13px;">"AMAZON WEB SERVICES" → Business > Technology > Cloud Services</div>
                            <div style="font-size: 11px; color: #6B7280;">Pattern: Technology vendor → Hierarchical categorization</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="display: flex; align-items: center; gap: 4px; justify-content: flex-end;">
                                <div style="width: 6px; height: 6px; border-radius: 50%; background: #16A34A;"></div>
                                <span style="font-family: monospace; font-size: 11px;">96%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="padding: 12px; border-bottom: 1px solid #F3F4F6;">
                    <div style="display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 12px; align-items: center;">
                        <div>
                            <div style="font-size: 13px; font-weight: 500;">Row 2</div>
                            <div style="font-size: 11px; color: #6B7280;">03/14/2025</div>
                        </div>
                        <div>
                            <div style="font-size: 13px;">"STARBUCKS #1234" → Business > Food & Dining</div>
                            <div style="font-size: 11px; color: #6B7280;">Pattern: Restaurant merchant → Food category</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="display: flex; align-items: center; gap: 4px; justify-content: flex-end;">
                                <div style="width: 6px; height: 6px; border-radius: 50%; background: #16A34A;"></div>
                                <span style="font-family: monospace; font-size: 11px;">94%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="padding: 12px;">
                    <div style="display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 12px; align-items: center;">
                        <div>
                            <div style="font-size: 13px; font-weight: 500;">Row 3</div>
                            <div style="font-size: 11px; color: #6B7280;">03/13/2025</div>
                        </div>
                        <div>
                            <div style="font-size: 13px;">"UNKNOWN VENDOR 5678" → Business > Miscellaneous</div>
                            <div style="font-size: 11px; color: #6B7280;">Pattern: Unrecognized → Default categorization</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="display: flex; align-items: center; gap: 4px; justify-content: flex-end;">
                                <div style="width: 6px; height: 6px; border-radius: 50%; background: #F59E0B;"></div>
                                <span style="font-family: monospace; font-size: 11px;">67%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- AI Insights and Recommendations -->
            <div style="background: #F0F9F4; border: 1px solid #A7F3D0; border-radius: 8px; padding: 16px;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                    <span style="color: #059669;">⚬</span>
                    <span style="font-weight: 600; color: #065F46;">AI Insights & Recommendations</span>
                </div>
                <div style="color: #065F46; font-size: 14px;">
                    <p style="margin: 0 0 8px;">□ <strong>File Quality:</strong> Excellent structure with consistent formatting and complete data</p>
                    <p style="margin: 0 0 8px;">□ <strong>Category Training:</strong> Rich hierarchical data will enable 95%+ accuracy for future categorization</p>
                    <p style="margin: 0 0 8px;">□ <strong>Pattern Recognition:</strong> Clear debit/credit patterns and vendor naming conventions detected</p>
                    <p style="margin: 0;">□ <strong>Recommendation:</strong> Ready for production use - no additional data cleaning required</p>
                </div>
            </div>
        </div>
        
        <!-- Accuracy Validation Results -->
        <h3>Temporal Accuracy Validation</h3>
        <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <h3 style="text-align: center; margin-bottom: 20px; color: #374151;">Accuracy Validation Results</h3>
            <p style="text-align: center; color: #6B7280; margin-bottom: 30px;">Testing AI performance against your historical data month by month</p>
            
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; margin-bottom: 20px;">
                <div style="background: #F0F9F4; padding: 16px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #166534;">96.2%</div>
                    <div style="font-size: 14px; color: #166534;">January 2025</div>
                </div>
                <div style="background: #F0F9F4; padding: 16px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #166534;">94.8%</div>
                    <div style="font-size: 14px; color: #166534;">February 2025</div>
                </div>
                <div style="background: #F0F9F4; padding: 16px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #166534;">97.1%</div>
                    <div style="font-size: 14px; color: #166534;">March 2025</div>
                </div>
            </div>
            
            <div style="background: #F0F9F4; padding: 20px; border-radius: 8px; text-align: center;">
                <div style="font-size: 28px; font-weight: bold; color: #166534; margin-bottom: 8px;">96.0% Average</div>
                <p style="color: #166534; margin-bottom: 20px;">Exceeds 85% accuracy target □</p>
                <button style="background: #16A34A; color: white; padding: 16px 32px; border: none; border-radius: 8px; font-weight: 600; cursor: pointer;">Approve for Production →</button>
            </div>
        </div>
    </div>
    
    <!-- CUSTOMER JOURNEY PHASE 3: DAILY USE -->
    <div class="component-section">
        <h2>⊞ Phase 3: Daily Use Components</h2>
        
        <!-- Production Upload (NO Categories Required) -->
        <h3>Production Upload (No Categories Required)</h3>
        <div style="background: white; padding: 30px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <h2 style="text-align: center; margin-bottom: 20px; color: #374151;">Upload New Transactions</h2>
            
            <div style="border: 2px dashed #D1D5DB; border-radius: 12px; padding: 60px 40px; text-align: center; background: #FAFAFA; margin-bottom: 20px; transition: all 0.3s; cursor: pointer;" onmouseover="this.style.borderColor='#295343'; this.style.background='#F0F9F4'" onmouseout="this.style.borderColor='#D1D5DB'; this.style.background='#FAFAFA'">
                <div style="font-size: 48px; margin-bottom: 20px;">□</div>
                <p style="font-size: 18px; font-weight: 500; margin-bottom: 8px; color: #374151;">Drop your files here</p>
                <p style="color: #6B7280; margin-bottom: 4px;">We'll categorize them for you</p>
                <p style="color: #6B7280; font-size: 14px;">Excel or CSV accepted</p>
            </div>
            
            <div style="background: #EFF6FF; border: 1px solid #3B82F6; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="color: #1D4ED8;">⚬</span>
                    <span style="font-weight: 600; color: #1E40AF;">No categories needed</span>
                </div>
                <p style="font-size: 14px; color: #1E40AF; margin-top: 8px;">Just upload your files - our trained AI will categorize everything automatically.</p>
            </div>
        </div>
        
        <!-- Transaction Review with Confidence Indicators -->
        <h3>Transaction Review Table (Production)</h3>
        <div style="background: white; border: 1px solid #E5E7EB; border-radius: 12px; overflow: hidden; margin-bottom: 30px;">
            <div style="background: #295343; color: white; padding: 16px;">
                <h3 style="margin: 0;">Review AI Categorizations</h3>
                <p style="margin: 8px 0 0; color: #E8F5E8; font-size: 14px;">847 transactions processed • 23 need review</p>
            </div>
            
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #F9FAFB;">
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #E5E7EB; font-weight: 600;">□</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #E5E7EB; font-weight: 600;">Date</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #E5E7EB; font-weight: 600;">Description</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #E5E7EB; font-weight: 600;">Amount</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #E5E7EB; font-weight: 600;">AI Category</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #E5E7EB; font-weight: 600;">Confidence</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="border-bottom: 1px solid #F3F4F6;" onmouseover="this.style.background='#F9FAFB'" onmouseout="this.style.background='white'">
                        <td style="padding: 12px;"><input type="checkbox"></td>
                        <td style="padding: 12px; font-family: monospace;">Mar 15</td>
                        <td style="padding: 12px;">AMAZON WEB SERVICES</td>
                        <td style="padding: 12px; font-family: monospace;">$1,250</td>
                        <td style="padding: 12px;">
                            <span>Technology</span>
                            <span style="background: #DCFCE7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 8px;">High conf</span>
                        </td>
                        <td style="padding: 12px;">
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                                <span style="font-family: monospace; font-size: 12px;">98%</span>
                            </div>
                        </td>
                    </tr>
                    <tr style="border-bottom: 1px solid #F3F4F6; background: #FEF3C7;" onmouseover="this.style.background='#FDE68A'" onmouseout="this.style.background='#FEF3C7'">
                        <td style="padding: 12px;"><input type="checkbox"></td>
                        <td style="padding: 12px; font-family: monospace;">Mar 14</td>
                        <td style="padding: 12px;">UNUSUAL VENDOR NAME</td>
                        <td style="padding: 12px; font-family: monospace;">$45.32</td>
                        <td style="padding: 12px;">
                            <select style="padding: 4px 8px; border: 1px solid #D1D5DB; border-radius: 4px; background: white;">
                                <option>Office Expenses</option>
                                <option>Travel</option>
                                <option>Supplies</option>
                            </select>
                            <span style="background: #FEF3C7; color: #92400E; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 8px;">?</span>
                        </td>
                        <td style="padding: 12px;">
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #F59E0B;"></div>
                                <span style="font-family: monospace; font-size: 12px;">67%</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <div style="padding: 16px; background: #F9FAFB; display: flex; justify-content: space-between; align-items: center;">
                <div style="display: flex; gap: 12px;">
                    <button style="background: #295343; color: white; padding: 8px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">Approve Selected</button>
                    <button style="background: white; border: 1px solid #D1D5DB; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer;">Bulk Edit</button>
                </div>
                <span style="font-size: 14px; color: #6B7280;">Showing 1-25 of 847 transactions</span>
            </div>
        </div>
        
        <!-- Advanced Hierarchical Category Management -->
        <h3>Hierarchical Category Management (Backend Database Structure)</h3>
        <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div>
                    <h3 style="color: #374151; margin-bottom: 4px;">Category Tree Structure & Parent-Child Relationships</h3>
                    <p style="color: #6B7280; font-size: 14px; margin: 0;">Database stores parent_id, depth, and full hierarchy paths for intelligent categorization</p>
                </div>
                <div style="display: flex; gap: 8px;">
                    <button style="background: white; border: 1px solid #D1D5DB; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer;">🌳 Expand All</button>
                    <button style="background: #295343; color: white; padding: 8px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">+ Add Category</button>
                </div>
            </div>
            
            <div style="border: 1px solid #E5E7EB; border-radius: 8px; overflow: hidden;">
                <div style="background: #F9FAFB; padding: 12px; border-bottom: 1px solid #E5E7EB; font-weight: 600; display: grid; grid-template-columns: 3fr 1fr 1fr 1fr 100px; gap: 16px;">
                    <span>Hierarchy Path (parent_id → child)</span>
                    <span>GL Code</span>
                    <span>Depth</span>
                    <span>Usage</span>
                    <span>Actions</span>
                </div>
                
                <!-- ROOT LEVEL: Business Expenses (parent_id: null) -->
                <div style="padding: 12px; border-bottom: 1px solid #F3F4F6; display: grid; grid-template-columns: 3fr 1fr 1fr 1fr 100px; gap: 16px; align-items: center; background: #F8FAFC;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <button style="background: transparent; border: none; color: #295343; font-size: 14px; cursor: pointer; padding: 2px;">▼</button>
                        <span style="font-weight: 600; color: #295343;">Business Expenses</span>
                        <span style="background: #DCFCE7; color: #166534; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Root</span>
                        <span style="color: #6B7280; font-size: 12px;">(parent_id: null)</span>
                    </div>
                    <span style="font-family: monospace; color: #6B7280;">5000-5999</span>
                    <span style="color: #6B7280; font-family: monospace;">0</span>
                    <span style="color: #6B7280;">847 total</span>
                    <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Edit</button>
                </div>
                
                <!-- LEVEL 1: Travel & Transportation (parent_id: 1) -->
                <div style="padding: 12px; border-bottom: 1px solid #F3F4F6; display: grid; grid-template-columns: 3fr 1fr 1fr 1fr 100px; gap: 16px; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-left: 20px;">
                        <button style="background: transparent; border: none; color: #295343; font-size: 14px; cursor: pointer; padding: 2px;">▼</button>
                        <span style="color: #6B7280;">├─</span>
                        <span style="font-weight: 500;">Travel & Transportation</span>
                        <span style="color: #6B7280; font-size: 12px;">(parent_id: 1)</span>
                    </div>
                    <span style="font-family: monospace; color: #6B7280;">5100</span>
                    <span style="color: #6B7280; font-family: monospace;">1</span>
                    <span style="color: #6B7280;">234 transactions</span>
                    <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Edit</button>
                </div>
                
                <!-- LEVEL 2: Flights (parent_id: 2) -->
                <div style="padding: 12px; border-bottom: 1px solid #F3F4F6; display: grid; grid-template-columns: 3fr 1fr 1fr 1fr 100px; gap: 16px; align-items: center; background: #FAFAFA;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-left: 40px;">
                        <span style="color: #6B7280;">│ ├─</span>
                        <span style="color: #374151;">Flights</span>
                        <span style="color: #6B7280; font-size: 12px;">(parent_id: 2)</span>
                        <span style="background: #EFF6FF; color: #1E40AF; padding: 2px 6px; border-radius: 8px; font-size: 11px;">AI Created</span>
                    </div>
                    <span style="font-family: monospace; color: #6B7280;">5110</span>
                    <span style="color: #6B7280; font-family: monospace;">2</span>
                    <span style="color: #6B7280;">89 transactions</span>
                    <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Edit</button>
                </div>
                
                <!-- LEVEL 2: Hotels (parent_id: 2) -->
                <div style="padding: 12px; border-bottom: 1px solid #F3F4F6; display: grid; grid-template-columns: 3fr 1fr 1fr 1fr 100px; gap: 16px; align-items: center; background: #FAFAFA;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-left: 40px;">
                        <span style="color: #6B7280;">│ └─</span>
                        <span style="color: #374151;">Hotels & Lodging</span>
                        <span style="color: #6B7280; font-size: 12px;">(parent_id: 2)</span>
                        <span style="background: #EFF6FF; color: #1E40AF; padding: 2px 6px; border-radius: 8px; font-size: 11px;">AI Created</span>
                    </div>
                    <span style="font-family: monospace; color: #6B7280;">5120</span>
                    <span style="color: #6B7280; font-family: monospace;">2</span>
                    <span style="color: #6B7280;">67 transactions</span>
                    <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Edit</button>
                </div>
                
                <!-- LEVEL 1: Office Supplies (parent_id: 1) -->
                <div style="padding: 12px; border-bottom: 1px solid #F3F4F6; display: grid; grid-template-columns: 3fr 1fr 1fr 1fr 100px; gap: 16px; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-left: 20px;">
                        <button style="background: transparent; border: none; color: #6B7280; font-size: 14px; cursor: pointer; padding: 2px;">▶</button>
                        <span style="color: #6B7280;">├─</span>
                        <span style="font-weight: 500;">Office Supplies</span>
                        <span style="color: #6B7280; font-size: 12px;">(parent_id: 1)</span>
                    </div>
                    <span style="font-family: monospace; color: #6B7280;">5200</span>
                    <span style="color: #6B7280; font-family: monospace;">1</span>
                    <span style="color: #6B7280;">156 transactions</span>
                    <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Edit</button>
                </div>
                
                <!-- LEVEL 1: Technology & Software (parent_id: 1) -->
                <div style="padding: 12px; display: grid; grid-template-columns: 3fr 1fr 1fr 1fr 100px; gap: 16px; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-left: 20px;">
                        <button style="background: transparent; border: none; color: #6B7280; font-size: 14px; cursor: pointer; padding: 2px;">▶</button>
                        <span style="color: #6B7280;">└─</span>
                        <span style="font-weight: 500;">Technology & Software</span>
                        <span style="color: #6B7280; font-size: 12px;">(parent_id: 1)</span>
                    </div>
                    <span style="font-family: monospace; color: #6B7280;">5300</span>
                    <span style="color: #6B7280; font-family: monospace;">1</span>
                    <span style="color: #6B7280;">357 transactions</span>
                    <button style="background: transparent; border: 1px solid #D1D5DB; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">Edit</button>
                </div>
            </div>
            
            <div style="background: #F8FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 16px; margin-top: 20px;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                    <span style="color: #475569;">🔧</span>
                    <span style="font-weight: 600; color: #475569;">Backend Database Structure</span>
                </div>
                <div style="color: #475569; font-size: 14px;">
                    <p style="margin: 0 0 8px;">□ Parent-child relationships stored via parent_id foreign key</p>
                    <p style="margin: 0 0 8px;">□ Recursive queries build full hierarchy paths like "Business > Travel > Flights"</p>
                    <p style="margin: 0 0 8px;">□ AI can create new hierarchical categories in zero-onboarding mode</p>
                    <p style="margin: 0;">□ Depth tracking enables sophisticated reporting and drill-down analysis</p>
                </div>
            </div>
        </div>
        
        <!-- Zero-Onboarding vs Historical Data Mode Comparison -->
        <h3>AI Categorization Modes: Zero-Onboarding vs Historical Data</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 30px;">
            
            <!-- Zero-Onboarding Mode -->
            <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                    <span style="color: #7C3AED;">↑</span>
                    <h4 style="color: #374151; margin: 0;">Zero-Onboarding Mode</h4>
                    <span style="background: #F3E8FF; color: #7C3AED; padding: 2px 8px; border-radius: 12px; font-size: 11px;">AI Bootstrap</span>
                </div>
                <p style="color: #6B7280; font-size: 14px; margin-bottom: 16px;">When tenant has no historical categories, AI creates intelligent business hierarchies</p>
                
                <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
                    <div style="font-size: 13px; color: #374151; margin-bottom: 8px;"><strong>Transaction:</strong> "AWS EC2 Instance Services"</div>
                    <div style="font-size: 13px; color: #374151; margin-bottom: 8px;"><strong>Amount:</strong> -$125.67</div>
                    <div style="font-size: 13px; color: #374151;"><strong>AI Analysis:</strong> Business technology expense</div>
                </div>
                
                <div style="background: #F3E8FF; border: 1px solid #C4B5FD; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
                    <div style="color: #5B21B6; font-size: 13px; margin-bottom: 8px;"><strong>AI Creates Hierarchy:</strong></div>
                    <div style="font-family: monospace; font-size: 12px; color: #5B21B6;">
                        <div>Business Expenses</div>
                        <div style="margin-left: 12px;">└─ Technology & Software</div>
                        <div style="margin-left: 24px;">└─ Cloud Services</div>
                    </div>
                </div>
                
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span style="color: #7C3AED; font-size: 12px;">Result:</span>
                    <span style="background: #F3E8FF; color: #7C3AED; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Business > Technology > Cloud Services</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="color: #7C3AED; font-size: 12px;">Confidence:</span>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                        <span style="font-family: monospace; font-size: 12px; font-weight: 600;">87%</span>
                    </div>
                </div>
            </div>
            
            <!-- Historical Data Mode -->
            <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB;">
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                    <span style="color: #1E40AF;">∷</span>
                    <h4 style="color: #374151; margin: 0;">Historical Data Mode</h4>
                    <span style="background: #EFF6FF; color: #1E40AF; padding: 2px 8px; border-radius: 12px; font-size: 11px;">RAG Enhanced</span>
                </div>
                <p style="color: #6B7280; font-size: 14px; margin-bottom: 16px;">When tenant has existing categories, AI uses RAG lookup + existing taxonomy</p>
                
                <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
                    <div style="font-size: 13px; color: #374151; margin-bottom: 8px;"><strong>Transaction:</strong> "AWS EC2 Instance Services"</div>
                    <div style="font-size: 13px; color: #374151; margin-bottom: 8px;"><strong>Amount:</strong> -$125.67</div>
                    <div style="font-size: 13px; color: #374151;"><strong>Historical Match:</strong> 23 similar AWS transactions found</div>
                </div>
                
                <div style="background: #EFF6FF; border: 1px solid #93C5FD; border-radius: 8px; padding: 12px; margin-bottom: 16px;">
                    <div style="color: #1E40AF; font-size: 13px; margin-bottom: 8px;"><strong>Existing Categories:</strong></div>
                    <div style="font-family: monospace; font-size: 12px; color: #1E40AF;">
                        <div>□ Technology Infrastructure > Cloud Services</div>
                        <div>□ Technology Infrastructure > Hosting</div>
                        <div>□ Technology Infrastructure > Development Tools</div>
                    </div>
                </div>
                
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                    <span style="color: #1E40AF; font-size: 12px;">Result:</span>
                    <span style="background: #EFF6FF; color: #1E40AF; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Technology Infrastructure > Cloud Services</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="color: #1E40AF; font-size: 12px;">Confidence:</span>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                        <span style="font-family: monospace; font-size: 12px; font-weight: 600;">96%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="background: #F0F9F4; border: 1px solid #A7F3D0; border-radius: 8px; padding: 16px; margin-bottom: 30px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                <span style="color: #059669;">↑</span>
                <span style="font-weight: 600; color: #065F46;">Automatic Mode Detection</span>
            </div>
            <p style="color: #065F46; font-size: 14px; margin: 0;">The categorization agent automatically detects which mode to use based on tenant data. Zero-onboarding for new customers, historical data mode for existing customers with category taxonomy.</p>
        </div>
        
        <!-- Live Categorization Engine Dashboard -->
        <h3>Live Categorization Engine (Real-time Processing)</h3>
        <div style="background: white; padding: 24px; border-radius: 12px; border: 1px solid #E5E7EB; margin-bottom: 30px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                <span style="color: #059669;">↑</span>
                <h3 style="font-weight: 600; color: #374151; margin: 0;">Real-time Transaction Processing</h3>
                <span style="background: #DCFCE7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px;">Live</span>
            </div>
            <p style="color: #6B7280; margin-bottom: 20px;">Watch the AI categorization engine process transactions in real-time with confidence scoring</p>
            
            <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #059669;">847</div>
                        <div style="font-size: 14px; color: #6B7280;">Processed</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #DC2626;">23</div>
                        <div style="font-size: 14px; color: #6B7280;">Low Confidence</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #2563EB;">96.2%</div>
                        <div style="font-size: 14px; color: #6B7280;">Avg Confidence</div>
                    </div>
                </div>
                
                <div style="background: #E5E7EB; height: 8px; border-radius: 4px; overflow: hidden; margin-bottom: 8px;">
                    <div style="background: linear-gradient(to right, #059669, #16A34A); width: 97%; height: 100%; animation: pulse 2s infinite;"></div>
                </div>
                <div style="text-align: center; color: #6B7280; font-size: 14px;">Processing: 97% complete</div>
            </div>
            
            <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; overflow: hidden;">
                <div style="background: #F9FAFB; padding: 12px; border-bottom: 1px solid #E5E7EB; font-weight: 600; display: grid; grid-template-columns: 3fr 2fr 1fr 1fr; gap: 16px;">
                    <span>Transaction</span>
                    <span>AI Category</span>
                    <span>Mode</span>
                    <span>Confidence</span>
                </div>
                
                <div style="padding: 12px; border-bottom: 1px solid #F3F4F6; display: grid; grid-template-columns: 3fr 2fr 1fr 1fr; gap: 16px; align-items: center;">
                    <div>
                        <div style="font-weight: 500; font-size: 14px;">STRIPE PAYMENT PROCESSING</div>
                        <div style="color: #6B7280; font-size: 12px;">-$29.95</div>
                    </div>
                    <div>
                        <div style="font-size: 14px;">Business > Technology > Payment Services</div>
                        <span style="background: #EFF6FF; color: #1E40AF; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Hierarchy Created</span>
                    </div>
                    <span style="background: #F3E8FF; color: #7C3AED; padding: 2px 8px; border-radius: 12px; font-size: 11px;">Zero-Onb</span>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                        <span style="font-family: monospace; font-size: 12px; font-weight: 600;">89%</span>
                    </div>
                </div>
                
                <div style="padding: 12px; border-bottom: 1px solid #F3F4F6; display: grid; grid-template-columns: 3fr 2fr 1fr 1fr; gap: 16px; align-items: center;">
                    <div>
                        <div style="font-weight: 500; font-size: 14px;">HERTZ CAR RENTAL DFW</div>
                        <div style="color: #6B7280; font-size: 12px;">-$147.83</div>
                    </div>
                    <div>
                        <div style="font-size: 14px;">Travel & Transportation > Car Rental</div>
                        <span style="background: #DCFCE7; color: #166534; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Existing Match</span>
                    </div>
                    <span style="background: #EFF6FF; color: #1E40AF; padding: 2px 8px; border-radius: 12px; font-size: 11px;">Historical</span>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <div style="width: 8px; height: 8px; border-radius: 50%; background: #16A34A;"></div>
                        <span style="font-family: monospace; font-size: 12px; font-weight: 600;">97%</span>
                    </div>
                </div>
                
                <div style="padding: 12px; display: grid; grid-template-columns: 3fr 2fr 1fr 1fr; gap: 16px; align-items: center; background: #FEF3C7;">
                    <div>
                        <div style="font-weight: 500; font-size: 14px;">UNKNOWN VENDOR 1234</div>
                        <div style="color: #6B7280; font-size: 12px;">-$67.45</div>
                    </div>
                    <div>
                        <div style="font-size: 14px;">Business > Miscellaneous</div>
                        <span style="background: #FEF3C7; color: #92400E; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Low Confidence</span>
                    </div>
                    <span style="background: #F3E8FF; color: #7C3AED; padding: 2px 8px; border-radius: 12px; font-size: 11px;">Zero-Onb</span>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <div style="width: 8px; height: 8px; border-radius: 50%; background: #F59E0B;"></div>
                        <span style="font-family: monospace; font-size: 12px; font-weight: 600;">67%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- FOUNDATIONAL UI COMPONENTS -->
    <div class="component-section">
        <h2>🎛️ Foundational UI Components</h2>
        <div class="component-demo">
            <div class="button-grid">
                <button class="btn btn-primary">Upload Transaction Data</button>
                <button class="btn btn-secondary">Download Report</button>
                <button class="btn btn-success">Process Complete</button>
                <button class="btn btn-warning">Review Required</button>
                <button class="btn btn-error">Delete Category</button>
            </div>
        </div>
    </div>

    <div class="component-section">
        <h2>📝 Form Components</h2>
        <div class="component-demo">
            <div style="max-width: 400px;">
                <div class="form-group">
                    <label class="form-label">Company Name</label>
                    <input type="text" class="form-input" placeholder="Enter your company name">
                </div>
                <div class="form-group">
                    <label class="form-label">Email Address</label>
                    <input type="email" class="form-input" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label class="form-label">Upload File</label>
                    <input type="file" class="form-input">
                </div>
                <button class="btn btn-primary">Submit for Processing</button>
            </div>
        </div>
    </div>

    <!-- Upload & Processing Components -->
    <div class="component-section">
        <h2>📁 Upload & Processing Components</h2>
        
        <!-- Drag & Drop Upload Area -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Drag & Drop Upload Area</h3>
            <div style="border: 2px dashed #D1D5DB; border-radius: 8px; padding: 60px 40px; text-align: center; background: #FAFAFA;">
                <div style="font-size: 48px; margin-bottom: 20px;">□</div>
                <p style="font-size: 18px; font-weight: 500; margin-bottom: 8px; color: #374151;">Drop your files here</p>
                <p style="color: #6B7280; margin-bottom: 4px;">We'll categorize them for you</p>
                <p style="color: #6B7280; font-size: 14px;">Excel or CSV accepted</p>
            </div>
        </div>

        <!-- Progress Bars -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Progress Bars</h3>
            <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; padding: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <span style="font-weight: 500;">march_transactions.xlsx</span>
                    <span style="color: #6B7280; font-size: 14px;">75%</span>
                </div>
                <div style="background: #F3F4F6; border-radius: 4px; height: 8px; margin-bottom: 12px;">
                    <div style="background: #295343; border-radius: 4px; height: 8px; width: 75%;"></div>
                </div>
                <div style="color: #6B7280; font-size: 14px;">
                    Status: Reading file → Analyzing → Categorizing → Done
                </div>
            </div>
        </div>

        <!-- File Status Cards -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">File Status Cards</h3>
            <div style="space-y: 12px;">
                <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; padding: 16px; display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <div>
                        <span style="color: #16A34A; margin-right: 8px;">□</span>
                        <span style="font-weight: 500;">february_data.csv</span>
                        <span style="color: #6B7280; margin-left: 16px;">147 transactions</span>
                    </div>
                    <button class="btn btn-secondary">View →</button>
                </div>
                <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; padding: 16px; display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <span style="color: #16A34A; margin-right: 8px;">□</span>
                        <span style="font-weight: 500;">january_2025.xlsx</span>
                        <span style="color: #6B7280; margin-left: 16px;">523 transactions</span>
                    </div>
                    <button class="btn btn-secondary">View →</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard & Metrics Components -->
    <div class="component-section">
        <h2>∷ Dashboard & Metrics Components</h2>
        
        <!-- Metric Cards -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Metric Cards</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; padding: 30px; text-align: center;">
                    <div style="font-size: 36px; font-weight: bold; color: #295343; margin-bottom: 8px;">2,847</div>
                    <div style="font-weight: 500; margin-bottom: 4px;">Transactions</div>
                    <div style="font-size: 12px; color: #6B7280;">Categorized</div>
                </div>
                <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; padding: 30px; text-align: center;">
                    <div style="font-size: 36px; font-weight: bold; color: #295343; margin-bottom: 8px;">$458.2K</div>
                    <div style="font-weight: 500; margin-bottom: 4px;">Processed</div>
                    <div style="font-size: 12px; color: #6B7280;">Automatically</div>
                </div>
                <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; padding: 30px; text-align: center;">
                    <div style="font-size: 36px; font-weight: bold; color: #295343; margin-bottom: 8px;">96.4%</div>
                    <div style="font-weight: 500; margin-bottom: 4px;">Accuracy</div>
                    <div style="font-size: 12px; color: #6B7280;">No work</div>
                </div>
            </div>
        </div>

        <!-- Action Cards -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Action Cards</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; padding: 24px;">
                    <h4 style="color: #295343; margin-bottom: 16px;">23 Low Confidence Items</h4>
                    <p style="color: #6B7280; margin-bottom: 20px; font-size: 14px;">
                        We've categorized these but you might want to double-check
                    </p>
                    <div style="display: flex; gap: 12px;">
                        <button class="btn btn-primary">Review Now</button>
                        <button class="btn btn-secondary">Skip</button>
                    </div>
                </div>
                <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; padding: 24px;">
                    <h4 style="color: #295343; margin-bottom: 16px;">Upload More Files</h4>
                    <p style="color: #6B7280; margin-bottom: 20px; font-size: 14px;">
                        Drop new transactions for instant processing
                    </p>
                    <button class="btn btn-primary">📁 Upload Files</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports & Export Components -->
    <div class="component-section">
        <h2>📈 Reports & Export Components</h2>
        
        <!-- Report Cards -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Report Cards</h3>
            <div style="background: white; border: 1px solid #E5E7EB; border-radius: 8px; padding: 24px; margin-bottom: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 16px;">
                    <h4 style="color: #295343; margin: 0;">March 2025 Financial Summary</h4>
                    <span style="background: #D1FAE5; color: #065F46; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500;">Ready ✅</span>
                </div>
                <p style="color: #6B7280; font-size: 14px; margin-bottom: 16px;">
                    Generated: 2 hours ago | Period: Mar 1-31
                </p>
                <div style="margin-bottom: 20px;">
                    <div style="color: #374151; font-size: 14px; margin-bottom: 4px;">• Total Expenses: $45,678</div>
                    <div style="color: #374151; font-size: 14px; margin-bottom: 4px;">• Categories: 23 | Transactions: 1,247</div>
                    <div style="color: #374151; font-size: 14px;">• Top Category: Transportation ($8,234)</div>
                </div>
                <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                    <button class="btn btn-secondary">View</button>
                    <button class="btn btn-secondary">Download Excel</button>
                    <button class="btn btn-secondary">Download PDF</button>
                    <button class="btn btn-secondary">Email</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Feedback & Status Components -->
    <div class="component-section">
        <h2>✅ Feedback & Status Components</h2>
        
        <!-- Status Badges -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Status Badges</h3>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <span style="background: #D1FAE5; color: #065F46; padding: 4px 12px; border-radius: 4px; font-size: 12px; font-weight: 500;">High conf</span>
                <span style="background: #FEF3C7; color: #92400E; padding: 4px 12px; border-radius: 4px; font-size: 12px; font-weight: 500;">?</span>
                <span style="background: #D1FAE5; color: #065F46; padding: 4px 12px; border-radius: 4px; font-size: 12px; font-weight: 500;">Ready ✅</span>
                <span style="background: #E5E7EB; color: #374151; padding: 4px 12px; border-radius: 4px; font-size: 12px; font-weight: 500;">Processing</span>
            </div>
        </div>

        <!-- Success Banners -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Success Banners</h3>
            <div style="background: #D1FAE5; border: 1px solid #A7F3D0; border-radius: 8px; padding: 16px; display: flex; align-items: center; gap: 12px;">
                <span style="color: #059669; font-size: 18px;">✅</span>
                <span style="color: #065F46; font-weight: 500;">File Uploaded Successfully</span>
            </div>
        </div>

        <!-- Info Cards -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Info Cards</h3>
            <div style="background: #FEF3C7; border: 1px solid #FDE68A; border-radius: 8px; padding: 16px; display: flex; align-items: start; gap: 12px;">
                <span style="color: #D97706; font-size: 18px;">⚬</span>
                <span style="color: #92400E;">Tip: Upload daily, weekly, or monthly - we handle it all</span>
            </div>
        </div>
    </div>

    <!-- Navigation & Flow Components -->
    <div class="component-section">
        <h2>🧭 Navigation & Flow Components</h2>
        
        <!-- Filter Bars -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Filter Bars</h3>
            <div style="background: #F9FAFB; border: 1px solid #E5E7EB; border-radius: 8px; padding: 16px; display: flex; gap: 16px; align-items: center;">
                <select class="form-input" style="width: auto; min-width: 120px;">
                    <option>All ▼</option>
                    <option>Needs Review</option>
                </select>
                <select class="form-input" style="width: auto; min-width: 140px;">
                    <option>This Month ▼</option>
                    <option>Last Month</option>
                </select>
                <input type="text" class="form-input" placeholder="🔍 Search..." style="flex: 1; max-width: 300px;">
            </div>
        </div>

        <!-- Pagination Controls -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Pagination Controls</h3>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="display: flex; gap: 8px;">
                    <button class="btn btn-secondary">◄</button>
                    <button class="btn btn-secondary">1</button>
                    <button class="btn btn-primary">2</button>
                    <button class="btn btn-secondary">3</button>
                    <button class="btn btn-secondary">...</button>
                    <button class="btn btn-secondary">11</button>
                    <button class="btn btn-secondary">►</button>
                </div>
                <span style="color: #6B7280; font-size: 14px;">
                    Showing 1-25 of 524 transactions
                </span>
            </div>
        </div>

        <!-- Step Indicators -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Step Indicators</h3>
            <div style="text-align: right;">
                <span style="color: #6B7280; font-size: 14px;">Step 2 of 4: Column Mapping</span>
            </div>
        </div>

        <!-- Breadcrumb Navigation -->
        <div style="margin-bottom: 40px;">
            <h3 style="color: #374151; margin-bottom: 15px;">Breadcrumb Navigation</h3>
            <div>
                <a href="#" style="color: #295343; text-decoration: none; font-size: 14px;">← Back to Upload</a>
            </div>
        </div>
    </div>

    <div class="component-section" style="text-align: center; border-top: 2px solid #E5E7EB; padding-top: 40px; margin-top: 60px;">
        <p style="color: #6B7280; font-size: 14px;">
            <strong>Complete Component System Preview</strong><br>
            All colors, contrasts, and interactions planned for WCAG compliance<br>
            <em>Professional B2B financial platform design using systematic color mapping</em>
        </p>
    </div>

    <script>
        // Add some interactive behavior
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
            });
        });

        document.querySelectorAll('.data-table tr').forEach(row => {
            row.addEventListener('click', () => {
                document.querySelectorAll('.data-table tr').forEach(r => r.classList.remove('selected'));
                row.classList.add('selected');
            });
        });

        document.querySelector('.agent-fab').addEventListener('click', () => {
            alert('Agent panel would open/close here');
        });
    </script>
</body>
</html>