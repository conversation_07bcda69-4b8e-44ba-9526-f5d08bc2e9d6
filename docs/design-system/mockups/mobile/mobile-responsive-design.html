<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile-First Responsive Design - giki.ai</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        /* Design System Variables */
        :root {
            /* Primary Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            --brand-primary-alpha: rgba(41, 83, 67, 0.1);
            
            /* Professional UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Status & Feedback Colors */
            --status-success: #059669;
            --status-warning: #D97706;
            --status-error: #DC2626;
            --status-info: #2563EB;
            --status-neutral: #6B7280;
            
            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            line-height: 1.5;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Mobile-First Layout */
        .mobile-app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Mobile Header */
        .mobile-header {
            background: var(--ui-background);
            border-bottom: 1px solid var(--ui-border);
            padding: var(--space-3) var(--space-4);
            position: sticky;
            top: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 60px;
        }

        .mobile-nav-toggle {
            width: 44px;
            height: 44px;
            border: none;
            background: var(--ui-surface);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--ui-text-secondary);
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mobile-nav-toggle:hover {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
        }

        .mobile-logo {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--brand-primary);
        }

        .mobile-actions {
            display: flex;
            gap: var(--space-2);
        }

        .mobile-action-btn {
            width: 44px;
            height: 44px;
            border: none;
            background: var(--brand-primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mobile-action-btn:hover {
            background: var(--brand-primary-hover);
        }

        /* Mobile Navigation Drawer */
        .mobile-nav-drawer {
            position: fixed;
            inset: 0;
            z-index: 2000;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .mobile-nav-drawer.open {
            transform: translateX(0);
        }

        .mobile-nav-overlay {
            position: absolute;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .mobile-nav-content {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 280px;
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1D372E 100%);
            padding: var(--space-6);
            overflow-y: auto;
        }

        .mobile-nav-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .mobile-nav-logo {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .mobile-nav-close {
            width: 32px;
            height: 32px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mobile-nav-items {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .mobile-nav-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-4);
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
            min-height: 52px;
        }

        .mobile-nav-item:hover,
        .mobile-nav-item.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .mobile-nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .mobile-nav-label {
            flex: 1;
            font-weight: 500;
        }

        /* Mobile Content Area */
        .mobile-content {
            flex: 1;
            padding: var(--space-4);
            overflow-y: auto;
        }

        /* Mobile Cards */
        .mobile-card {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 12px;
            padding: var(--space-4);
            margin-bottom: var(--space-4);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .mobile-card-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: var(--space-3);
        }

        .mobile-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--ui-text-primary);
        }

        .mobile-card-subtitle {
            color: var(--ui-text-secondary);
            font-size: 0.875rem;
            margin-top: var(--space-1);
        }

        /* Mobile Metrics */
        .mobile-metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-3);
            margin-bottom: var(--space-6);
        }

        .mobile-metric {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            padding: var(--space-3);
            text-align: center;
        }

        .mobile-metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: var(--space-1);
        }

        .mobile-metric-label {
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
            font-weight: 500;
        }

        /* Mobile Upload Zone */
        .mobile-upload-zone {
            border: 2px dashed var(--ui-border);
            border-radius: 12px;
            padding: var(--space-8);
            text-align: center;
            background: var(--ui-surface);
            margin-bottom: var(--space-6);
            transition: all 0.3s ease;
        }

        .mobile-upload-zone:active {
            border-color: var(--brand-primary);
            background: var(--brand-primary-light);
        }

        .mobile-upload-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto var(--space-3);
            background: var(--brand-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .mobile-upload-text {
            font-size: 1rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-1);
        }

        .mobile-upload-subtext {
            font-size: 0.875rem;
            color: var(--ui-text-secondary);
        }

        /* Mobile Buttons */
        .mobile-btn {
            width: 100%;
            min-height: 52px;
            padding: var(--space-4);
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
        }

        .mobile-btn-primary {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(41, 83, 67, 0.2);
        }

        .mobile-btn-primary:active {
            background: linear-gradient(135deg, var(--brand-primary-hover) 0%, #163651 100%);
            transform: translateY(1px);
        }

        .mobile-btn-secondary {
            background: var(--ui-background);
            color: var(--ui-text-secondary);
            border: 1px solid var(--ui-border);
        }

        .mobile-btn-secondary:active {
            background: var(--ui-surface);
            color: var(--ui-text-primary);
        }

        /* Mobile Transaction List */
        .mobile-transaction-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .mobile-transaction-item {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            padding: var(--space-3);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .mobile-transaction-icon {
            width: 32px;
            height: 32px;
            background: var(--brand-primary-light);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--brand-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .mobile-transaction-details {
            flex: 1;
            min-width: 0;
        }

        .mobile-transaction-description {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .mobile-transaction-category {
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
        }

        .mobile-transaction-amount {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            text-align: right;
        }

        /* Mobile Bottom Bar */
        .mobile-bottom-bar {
            background: var(--ui-background);
            border-top: 1px solid var(--ui-border);
            padding: var(--space-3) var(--space-4);
            display: flex;
            justify-content: space-around;
            position: sticky;
            bottom: 0;
            z-index: 100;
        }

        .mobile-bottom-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--space-1);
            padding: var(--space-2);
            border-radius: 8px;
            color: var(--ui-text-secondary);
            text-decoration: none;
            transition: all 0.2s ease;
            min-width: 52px;
        }

        .mobile-bottom-item.active {
            color: var(--brand-primary);
            background: var(--brand-primary-light);
        }

        .mobile-bottom-icon {
            font-size: 20px;
        }

        .mobile-bottom-label {
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Progressive Web App Features */
        .mobile-pwa-prompt {
            background: var(--brand-primary);
            color: white;
            padding: var(--space-3) var(--space-4);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 0.875rem;
        }

        .mobile-pwa-close {
            width: 24px;
            height: 24px;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: white;
            cursor: pointer;
        }

        /* Touch Optimization */
        @media (pointer: coarse) {
            .mobile-nav-toggle,
            .mobile-action-btn,
            .mobile-btn {
                min-height: 48px;
                min-width: 48px;
            }
            
            .mobile-nav-item {
                min-height: 56px;
            }
            
            .mobile-transaction-item {
                min-height: 64px;
            }
        }

        /* Responsive Breakpoints */
        @media (min-width: 768px) {
            .mobile-app {
                display: none;
            }
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            :root {
                --ui-background: #1F2937;
                --ui-surface: #111827;
                --ui-border: #374151;
                --ui-text-primary: #F9FAFB;
                --ui-text-secondary: #D1D5DB;
                --ui-text-muted: #9CA3AF;
            }
        }

        /* Accessibility Enhancements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* High Contrast Mode */
        @media (prefers-contrast: high) {
            .mobile-card,
            .mobile-metric,
            .mobile-transaction-item {
                border-width: 2px;
            }
        }

        /* Focus Visible */
        *:focus-visible {
            outline: 2px solid var(--brand-primary);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <div class="mobile-app">
        <!-- PWA Install Prompt -->
        <div class="mobile-pwa-prompt" id="pwaPrompt" style="display: none;">
            <span>Install giki.ai for faster access</span>
            <button class="mobile-pwa-close" onclick="dismissPWAPrompt()">×</button>
        </div>

        <!-- Mobile Header -->
        <header class="mobile-header">
            <button class="mobile-nav-toggle" onclick="toggleMobileNav()">≡</button>
            <div class="mobile-logo">giki.ai</div>
            <div class="mobile-actions">
                <button class="mobile-action-btn">AI</button>
            </div>
        </header>

        <!-- Mobile Navigation Drawer -->
        <div class="mobile-nav-drawer" id="mobileNav">
            <div class="mobile-nav-overlay" onclick="closeMobileNav()"></div>
            <div class="mobile-nav-content">
                <div class="mobile-nav-header">
                    <div class="mobile-nav-logo">giki.ai</div>
                    <button class="mobile-nav-close" onclick="closeMobileNav()">×</button>
                </div>
                <nav class="mobile-nav-items">
                    <a href="#dashboard" class="mobile-nav-item active">
                        <div class="mobile-nav-icon">□</div>
                        <div class="mobile-nav-label">Dashboard</div>
                    </a>
                    <a href="#upload" class="mobile-nav-item">
                        <div class="mobile-nav-icon">↑</div>
                        <div class="mobile-nav-label">Upload</div>
                    </a>
                    <a href="#transactions" class="mobile-nav-item">
                        <div class="mobile-nav-icon">⊞</div>
                        <div class="mobile-nav-label">Transactions</div>
                    </a>
                    <a href="#reports" class="mobile-nav-item">
                        <div class="mobile-nav-icon">∷</div>
                        <div class="mobile-nav-label">Reports</div>
                    </a>
                    <a href="#categories" class="mobile-nav-item">
                        <div class="mobile-nav-icon">⚬</div>
                        <div class="mobile-nav-label">Categories</div>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Mobile Content -->
        <main class="mobile-content">
            <!-- Dashboard View -->
            <div id="dashboardView">
                <!-- Metrics Grid -->
                <div class="mobile-metrics-grid">
                    <div class="mobile-metric">
                        <div class="mobile-metric-value">1,247</div>
                        <div class="mobile-metric-label">Transactions</div>
                    </div>
                    <div class="mobile-metric">
                        <div class="mobile-metric-value">87.3%</div>
                        <div class="mobile-metric-label">Accuracy</div>
                    </div>
                    <div class="mobile-metric">
                        <div class="mobile-metric-value">15h</div>
                        <div class="mobile-metric-label">Time Saved</div>
                    </div>
                    <div class="mobile-metric">
                        <div class="mobile-metric-value">34</div>
                        <div class="mobile-metric-label">Categories</div>
                    </div>
                </div>

                <!-- Quick Upload -->
                <div class="mobile-card">
                    <div class="mobile-card-header">
                        <div>
                            <div class="mobile-card-title">Quick Upload</div>
                            <div class="mobile-card-subtitle">Add new transactions</div>
                        </div>
                    </div>
                    <div class="mobile-upload-zone">
                        <div class="mobile-upload-icon">↑</div>
                        <div class="mobile-upload-text">Tap to upload files</div>
                        <div class="mobile-upload-subtext">Excel, CSV, PDF supported</div>
                    </div>
                    <button class="mobile-btn mobile-btn-primary">
                        Choose Files
                    </button>
                </div>

                <!-- Recent Transactions -->
                <div class="mobile-card">
                    <div class="mobile-card-header">
                        <div>
                            <div class="mobile-card-title">Recent Transactions</div>
                            <div class="mobile-card-subtitle">Latest activity</div>
                        </div>
                    </div>
                    <div class="mobile-transaction-list">
                        <div class="mobile-transaction-item">
                            <div class="mobile-transaction-icon">□</div>
                            <div class="mobile-transaction-details">
                                <div class="mobile-transaction-description">AMAZON.COM*2M8NK2JK3</div>
                                <div class="mobile-transaction-category">Business Supplies</div>
                            </div>
                            <div class="mobile-transaction-amount">-$127.45</div>
                        </div>
                        <div class="mobile-transaction-item">
                            <div class="mobile-transaction-icon">□</div>
                            <div class="mobile-transaction-details">
                                <div class="mobile-transaction-description">PAYPAL *SPOTIFY</div>
                                <div class="mobile-transaction-category">Entertainment</div>
                            </div>
                            <div class="mobile-transaction-amount">-$15.99</div>
                        </div>
                        <div class="mobile-transaction-item">
                            <div class="mobile-transaction-icon">□</div>
                            <div class="mobile-transaction-details">
                                <div class="mobile-transaction-description">TRANSFER TO SAVINGS</div>
                                <div class="mobile-transaction-category">Transfers</div>
                            </div>
                            <div class="mobile-transaction-amount">-$500.00</div>
                        </div>
                    </div>
                    <button class="mobile-btn mobile-btn-secondary" style="margin-top: var(--space-4);">
                        View All Transactions
                    </button>
                </div>
            </div>
        </main>

        <!-- Mobile Bottom Navigation -->
        <nav class="mobile-bottom-bar">
            <a href="#dashboard" class="mobile-bottom-item active">
                <div class="mobile-bottom-icon">□</div>
                <div class="mobile-bottom-label">Home</div>
            </a>
            <a href="#upload" class="mobile-bottom-item">
                <div class="mobile-bottom-icon">↑</div>
                <div class="mobile-bottom-label">Upload</div>
            </a>
            <a href="#transactions" class="mobile-bottom-item">
                <div class="mobile-bottom-icon">⊞</div>
                <div class="mobile-bottom-label">Review</div>
            </a>
            <a href="#reports" class="mobile-bottom-item">
                <div class="mobile-bottom-icon">∷</div>
                <div class="mobile-bottom-label">Reports</div>
            </a>
        </nav>
    </div>

    <script>
        // Mobile Navigation
        function toggleMobileNav() {
            const nav = document.getElementById('mobileNav');
            nav.classList.toggle('open');
        }

        function closeMobileNav() {
            const nav = document.getElementById('mobileNav');
            nav.classList.remove('open');
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            document.getElementById('pwaPrompt').style.display = 'flex';
        });

        function dismissPWAPrompt() {
            document.getElementById('pwaPrompt').style.display = 'none';
        }

        // Touch Feedback
        document.addEventListener('touchstart', function(e) {
            if (e.target.closest('.mobile-btn, .mobile-nav-item, .mobile-bottom-item, .mobile-transaction-item')) {
                e.target.style.transform = 'scale(0.98)';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (e.target.closest('.mobile-btn, .mobile-nav-item, .mobile-bottom-item, .mobile-transaction-item')) {
                e.target.style.transform = 'scale(1)';
            }
        });

        // Prevent zoom on input focus (iOS)
        document.addEventListener('touchstart', function(event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        });

        // Accessibility: Focus management
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileNav();
            }
        });
    </script>
</body>
</html>