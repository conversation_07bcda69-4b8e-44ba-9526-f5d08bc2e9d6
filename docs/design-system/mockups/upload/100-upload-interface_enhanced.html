<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload - Enhanced File Upload Interface</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        /* Design System Variables */
        :root {
            /* Primary Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            --brand-primary-alpha: rgba(41, 83, 67, 0.1);
            
            /* Professional UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Status & Feedback Colors */
            --status-success: #059669;
            --status-warning: #D97706;
            --status-error: #DC2626;
            --status-info: #2563EB;
            --status-neutral: #6B7280;
            
            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            line-height: 1.5;
            margin: 0;
            padding: 0;
        }

        /* Three-Panel Layout */
        .app-layout {
            display: grid;
            grid-template-columns: 64px 1fr 0px;
            grid-template-rows: 1fr;
            height: 100vh;
            overflow: hidden;
        }

        /* Left Navigation Panel */
        .nav-panel {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1D372E 100%);
            border-right: 1px solid var(--ui-border);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: var(--space-4);
        }

        .nav-logo {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--brand-primary);
            font-weight: 700;
            font-size: 14px;
            margin-bottom: var(--space-6);
        }

        .nav-items {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .nav-item {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 20px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* Main Content Area */
        .main-content {
            background: var(--ui-background);
            padding: var(--space-8);
            overflow-y: auto;
            position: relative;
        }

        /* Upload Container */
        .upload-container {
            max-width: 900px;
            margin: 0 auto;
        }

        /* Header Section */
        .upload-header {
            margin-bottom: var(--space-8);
        }

        .upload-title {
            font-size: 2rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
        }

        .upload-subtitle {
            color: var(--ui-text-secondary);
            font-size: 1.125rem;
            margin-bottom: var(--space-4);
        }

        /* Progress Steps */
        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--space-10);
            position: relative;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 16px;
            left: 16px;
            right: 16px;
            height: 2px;
            background: var(--ui-border);
            z-index: 1;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .step-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--ui-background);
            border: 2px solid var(--ui-border);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: var(--space-2);
        }

        .step-circle.active {
            background: var(--brand-primary);
            border-color: var(--brand-primary);
            color: white;
        }

        .step-circle.completed {
            background: var(--status-success);
            border-color: var(--status-success);
            color: white;
        }

        .step-label {
            font-size: 0.875rem;
            color: var(--ui-text-secondary);
            text-align: center;
            max-width: 120px;
        }

        .step-label.active {
            color: var(--brand-primary);
            font-weight: 600;
        }

        /* Upload Zone */
        .upload-zone {
            border: 2px dashed var(--ui-border);
            border-radius: 16px;
            padding: var(--space-16);
            text-align: center;
            margin-bottom: var(--space-8);
            background: var(--ui-surface);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .upload-zone:hover {
            border-color: var(--brand-primary);
            background: var(--brand-primary-light);
        }

        .upload-zone.drag-over {
            border-color: var(--brand-primary);
            background: var(--brand-primary-light);
            transform: scale(1.02);
        }

        .upload-zone.error {
            border-color: var(--status-error);
            background: rgba(220, 38, 38, 0.05);
        }

        .upload-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto var(--space-4);
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
        }

        .upload-zone.error .upload-icon {
            background: linear-gradient(135deg, var(--status-error) 0%, #B91C1C 100%);
        }

        .upload-text {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
        }

        .upload-subtext {
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-4);
        }

        .upload-button {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: var(--space-3) var(--space-6);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(41, 83, 67, 0.2);
        }

        .upload-button:hover {
            background: linear-gradient(135deg, var(--brand-primary-hover) 0%, #163651 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(41, 83, 67, 0.3);
        }

        /* Supported Formats */
        .supported-formats {
            display: flex;
            justify-content: center;
            gap: var(--space-4);
            margin-top: var(--space-4);
        }

        .format-tag {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 6px;
            padding: var(--space-1) var(--space-3);
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
            font-weight: 500;
        }

        /* File Queue */
        .file-queue {
            background: var(--ui-surface);
            border: 1px solid var(--ui-border);
            border-radius: 12px;
            padding: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .queue-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: var(--space-4);
        }

        .queue-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--ui-text-primary);
        }

        .queue-count {
            color: var(--ui-text-secondary);
            font-size: 0.875rem;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3);
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            margin-bottom: var(--space-2);
        }

        .file-item:last-child {
            margin-bottom: 0;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            background: var(--brand-primary-light);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--brand-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: 2px;
        }

        .file-size {
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
        }

        .file-progress {
            width: 100px;
            height: 6px;
            background: var(--ui-border);
            border-radius: 3px;
            overflow: hidden;
            margin-right: var(--space-2);
        }

        .file-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--brand-primary) 0%, var(--status-success) 100%);
            transition: width 0.3s ease;
        }

        .file-status {
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
            min-width: 60px;
            text-align: right;
        }

        .file-status.success {
            color: var(--status-success);
        }

        .file-status.error {
            color: var(--status-error);
        }

        /* Error Message */
        .error-message {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.2);
            border-radius: 8px;
            padding: var(--space-4);
            margin-bottom: var(--space-4);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .error-icon {
            color: var(--status-error);
            font-size: 18px;
        }

        .error-text {
            color: var(--status-error);
            font-size: 0.875rem;
        }

        /* Success Message */
        .success-message {
            background: rgba(5, 150, 105, 0.1);
            border: 1px solid rgba(5, 150, 105, 0.2);
            border-radius: 8px;
            padding: var(--space-4);
            margin-bottom: var(--space-4);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .success-icon {
            color: var(--status-success);
            font-size: 18px;
        }

        .success-text {
            color: var(--status-success);
            font-size: 0.875rem;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
        }

        .btn {
            padding: var(--space-3) var(--space-6);
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(41, 83, 67, 0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--brand-primary-hover) 0%, #163651 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(41, 83, 67, 0.3);
        }

        .btn-secondary {
            background: var(--ui-background);
            color: var(--ui-text-secondary);
            border: 1px solid var(--ui-border);
        }

        .btn-secondary:hover {
            background: var(--ui-surface);
            color: var(--ui-text-primary);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .app-layout {
                grid-template-columns: 1fr;
            }
            
            .nav-panel {
                display: none;
            }
            
            .main-content {
                padding: var(--space-4);
            }
            
            .upload-container {
                max-width: 100%;
            }
            
            .progress-steps {
                flex-direction: column;
                gap: var(--space-4);
            }
            
            .progress-steps::before {
                display: none;
            }
            
            .supported-formats {
                flex-wrap: wrap;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Left Navigation Panel -->
        <div class="nav-panel">
            <div class="nav-logo">G</div>
            <div class="nav-items">
                <div class="nav-item">□</div>
                <div class="nav-item active">↑</div>
                <div class="nav-item">⊞</div>
                <div class="nav-item">∷</div>
                <div class="nav-item">⚬</div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <div class="upload-container">
                <!-- Header -->
                <div class="upload-header">
                    <h1 class="upload-title">Upload Financial Data</h1>
                    <p class="upload-subtitle">Upload your bank statements, credit card statements, or transaction files for AI-powered categorization</p>
                </div>

                <!-- Progress Steps -->
                <div class="progress-steps">
                    <div class="progress-step">
                        <div class="step-circle active">1</div>
                        <div class="step-label active">Upload Files</div>
                    </div>
                    <div class="progress-step">
                        <div class="step-circle">2</div>
                        <div class="step-label">Schema Detection</div>
                    </div>
                    <div class="progress-step">
                        <div class="step-circle">3</div>
                        <div class="step-label">Column Mapping</div>
                    </div>
                    <div class="progress-step">
                        <div class="step-circle">4</div>
                        <div class="step-label">AI Processing</div>
                    </div>
                </div>

                <!-- Upload Zone -->
                <div class="upload-zone" id="uploadZone">
                    <div class="upload-icon">↑</div>
                    <div class="upload-text">Drop your files here or click to browse</div>
                    <div class="upload-subtext">Support for Excel, CSV, OFX, QBO, and PDF files</div>
                    <button class="upload-button">Choose Files</button>
                    
                    <div class="supported-formats">
                        <div class="format-tag">Excel (.xlsx, .xls)</div>
                        <div class="format-tag">CSV</div>
                        <div class="format-tag">OFX</div>
                        <div class="format-tag">QBO</div>
                        <div class="format-tag">PDF</div>
                    </div>
                </div>

                <!-- Success Message (hidden by default) -->
                <div class="success-message" style="display: none;">
                    <div class="success-icon">⚬</div>
                    <div class="success-text">Files uploaded successfully! Schema detection in progress...</div>
                </div>

                <!-- Error Message (hidden by default) -->
                <div class="error-message" style="display: none;">
                    <div class="error-icon">×</div>
                    <div class="error-text">Upload failed: File size too large (max 50MB) or unsupported format.</div>
                </div>

                <!-- File Queue -->
                <div class="file-queue">
                    <div class="queue-header">
                        <div class="queue-title">Upload Queue</div>
                        <div class="queue-count">3 files</div>
                    </div>
                    
                    <div class="file-item">
                        <div class="file-icon">XLS</div>
                        <div class="file-details">
                            <div class="file-name">March_Bank_Statement.xlsx</div>
                            <div class="file-size">2.4 MB • 1,247 transactions</div>
                        </div>
                        <div class="file-progress">
                            <div class="file-progress-bar" style="width: 100%;"></div>
                        </div>
                        <div class="file-status success">Complete</div>
                    </div>
                    
                    <div class="file-item">
                        <div class="file-icon">CSV</div>
                        <div class="file-details">
                            <div class="file-name">Credit_Card_Feb.csv</div>
                            <div class="file-size">845 KB • 523 transactions</div>
                        </div>
                        <div class="file-progress">
                            <div class="file-progress-bar" style="width: 73%;"></div>
                        </div>
                        <div class="file-status">Uploading...</div>
                    </div>
                    
                    <div class="file-item">
                        <div class="file-icon">PDF</div>
                        <div class="file-details">
                            <div class="file-name">January_Statement.pdf</div>
                            <div class="file-size">3.2 MB • Processing OCR</div>
                        </div>
                        <div class="file-progress">
                            <div class="file-progress-bar" style="width: 0%;"></div>
                        </div>
                        <div class="file-status">Queued</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="btn btn-primary">
                        Continue to Schema Detection
                    </button>
                    <button class="btn btn-secondary">
                        Clear Queue
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced drag and drop functionality
        const uploadZone = document.getElementById('uploadZone');
        
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('drag-over');
        });
        
        uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');
        });
        
        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');
            // Handle file drop logic here
        });
    </script>
</body>
</html>