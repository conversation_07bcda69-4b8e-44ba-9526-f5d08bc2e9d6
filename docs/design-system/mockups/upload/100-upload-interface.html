<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Statements - giki.ai</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        /* Design System Variables - Matching Login Page */
        :root {
            /* Primary Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            --brand-primary-alpha: rgba(41, 83, 67, 0.1);
            
            /* Professional UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Status & Feedback Colors */
            --status-success: #059669;
            --status-warning: #D97706;
            --status-error: #DC2626;
            --status-info: #2563EB;
            --status-neutral: #6B7280;
            
            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            line-height: 1.5;
        }

        /* Header - Matching Login Page */
        .header {
            background: var(--ui-background);
            border-bottom: 1px solid var(--ui-border);
            padding: var(--space-4) var(--space-6);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--brand-primary);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            color: var(--ui-text-secondary);
        }

        /* Main Container with Navigation Preview and Agent Panel */
        .main-container {
            margin-top: 72px;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
            padding: var(--space-8) var(--space-6);
            display: grid;
            grid-template-columns: 64px 1fr 460px 400px;
            gap: var(--space-8);
            align-items: start;
            min-height: calc(100vh - 72px);
        }

        /* Navigation Preview Panel */
        .nav-preview {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            border: 1px solid var(--brand-primary);
            border-radius: 12px;
            padding: 0;
            display: flex;
            flex-direction: column;
            position: sticky;
            top: var(--space-8);
            height: fit-content;
            color: white;
        }

        .nav-preview-header {
            padding: var(--space-4);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .nav-preview-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            transition: all 0.15s ease;
            font-size: 1.25rem;
            border-radius: 6px;
            margin: 0 var(--space-2);
            opacity: 0.6;
        }

        .nav-preview-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            opacity: 1;
            font-weight: 600;
        }

        .nav-icon {
            font-size: 1.5rem;
            font-weight: 700;
        }

        /* Process Section with reduced width */
        .process-section {
            padding-right: var(--space-4);
        }

        /* Left Column - Process Info */
        .process-section {
            padding-right: var(--space-8);
        }

        .page-title {
            font-size: 3.5rem;
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: var(--space-6);
            color: var(--ui-text-primary);
        }

        .page-title .highlight {
            color: var(--brand-primary);
        }

        .page-subtitle {
            font-size: 1.5rem;
            color: var(--ui-text-secondary);
            line-height: 1.6;
            margin-bottom: var(--space-10);
        }

        /* Process Preview - Matching Login Page Style */
        .process-preview {
            margin: var(--space-10) 0;
            padding: var(--space-8);
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            border-radius: 16px;
            color: white;
            box-shadow: 0 8px 32px rgba(41, 83, 67, 0.2);
        }

        .process-steps {
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .process-step {
            text-align: center;
        }

        .step-icon {
            width: 64px;
            height: 64px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin: 0 auto var(--space-3);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .step-title {
            font-size: 1rem;
            font-weight: 600;
        }

        .step-subtitle {
            font-size: 0.875rem;
            opacity: 0.8;
            margin-top: var(--space-1);
        }

        .process-arrow {
            font-size: 24px;
            opacity: 0.6;
        }

        /* Upload Panel - Compact */
        .upload-panel {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 20px;
            padding: var(--space-8);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
            margin-top: 88px;
        }

        .upload-header {
            text-align: center;
            margin-bottom: var(--space-6);
        }

        .upload-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--ui-text-primary);
            margin-bottom: 0.25rem;
        }

        .upload-subtitle {
            color: var(--ui-text-secondary);
            font-size: 0.875rem;
        }

        /* Compact Upload Zone */
        .upload-zone {
            border: 2px dashed var(--brand-primary);
            border-radius: 12px;
            padding: var(--space-6);
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: var(--ui-surface);
            margin-bottom: var(--space-4);
        }

        .upload-zone:hover {
            border-color: var(--brand-primary-hover);
            background: rgba(41, 83, 67, 0.05);
        }

        .upload-icon {
            width: 40px;
            height: 40px;
            background: var(--brand-primary);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin: 0 auto var(--space-3);
        }

        .upload-text {
            font-weight: 600;
            color: var(--brand-primary);
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }

        .upload-formats {
            font-size: 0.75rem;
            color: var(--ui-text-muted);
        }

        .btn {
            padding: var(--space-4) var(--space-8);
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.125rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            border: none;
            width: 100%;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(41, 83, 67, 0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--brand-primary-hover) 0%, #163651 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(41, 83, 67, 0.3);
        }

        /* What Happens Next - Compact */
        .info-section {
            background: var(--ui-surface);
            border: 1px solid var(--ui-border);
            border-radius: 12px;
            padding: var(--space-4);
            margin-bottom: var(--space-4);
        }

        .info-title {
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
            font-size: 0.875rem;
        }

        .info-list {
            list-style: none;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--ui-text-secondary);
            font-size: 0.75rem;
            margin-bottom: 0.25rem;
        }

        .info-icon {
            color: var(--brand-primary);
            flex-shrink: 0;
        }

        /* Security Note */
        .security-note {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--ui-text-muted);
            font-size: 0.75rem;
            margin-top: var(--space-3);
            justify-content: center;
        }

        /* Agent Panel */
        .agent-panel {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 16px;
            padding: 0;
            display: flex;
            flex-direction: column;
            position: sticky;
            top: var(--space-8);
            height: calc(100vh - 120px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .agent-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--ui-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .agent-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .agent-content {
            flex: 1;
            padding: var(--space-6);
            overflow-y: auto;
        }

        .agent-message {
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            background: var(--ui-surface);
            border-radius: 12px;
            border-left: 3px solid var(--brand-primary);
        }

        .agent-input {
            padding: var(--space-4);
            border-top: 1px solid var(--ui-border);
            background: var(--ui-surface);
        }

        .agent-input-field {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            font-size: 0.875rem;
            resize: none;
            height: 60px;
        }

        .agent-input-field:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 2px rgba(41, 83, 67, 0.1);
        }

        .agent-quick-actions {
            display: flex;
            gap: var(--space-2);
            margin-top: var(--space-3);
        }

        .quick-action {
            padding: var(--space-2) var(--space-3);
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 6px;
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .quick-action:hover {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .main-container {
                grid-template-columns: 1fr;
                gap: var(--space-12);
            }

            .process-section {
                text-align: center;
                padding-right: 0;
            }

            .upload-panel {
                max-width: 460px;
                margin: 0 auto;
            }

            .page-title {
                font-size: 2.5rem;
            }

            .agent-panel {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">giki.ai</div>
            <div class="user-info">
                <span><EMAIL></span>
                <div style="width: 40px; height: 40px; background: var(--brand-primary); color: white; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: 600;">O</div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Navigation Preview Panel -->
        <aside class="nav-preview">
            <div class="nav-preview-header">
                <div style="height: 24px; width: 24px; background: rgba(255, 255, 255, 0.9); border-radius: 4px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 12px; color: var(--brand-primary); margin: 0 auto;">G</div>
            </div>
            
            <div style="padding: var(--space-4);">
                <div style="font-size: 0.625rem; font-weight: 600; color: rgba(255, 255, 255, 0.8); text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: var(--space-3); padding: 0 var(--space-4);">WORK</div>
                <a href="#" class="nav-preview-item">
                    <span class="nav-icon">□</span>
                </a>
                <a href="#" class="nav-preview-item active">
                    <span class="nav-icon">↑</span>
                </a>
                <a href="#" class="nav-preview-item">
                    <span class="nav-icon">⊞</span>
                </a>
                <a href="#" class="nav-preview-item">
                    <span class="nav-icon">∷</span>
                </a>
                <a href="#" class="nav-preview-item">
                    <span class="nav-icon">⚬</span>
                </a>
            </div>
        </aside>

        <!-- Left Column - Process Info -->
        <section class="process-section">
            <h1 class="page-title">
                Your Statements<br/>
                Become <span class="highlight">Organized Books</span>
            </h1>

            <p class="page-subtitle">
                Upload your bank or credit card statements. We'll categorize every transaction.
            </p>

            <!-- Process Preview -->
            <div class="process-preview">
                <div class="process-steps">
                    <div class="process-step">
                        <div class="step-icon">1</div>
                        <div class="step-title">Upload</div>
                        <div class="step-subtitle">Your statements</div>
                    </div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">
                        <div class="step-icon">2</div>
                        <div class="step-title">AI Categorizes</div>
                        <div class="step-subtitle">Every transaction</div>
                    </div>
                    <div class="process-arrow">→</div>
                    <div class="process-step">
                        <div class="step-icon">3</div>
                        <div class="step-title">Export</div>
                        <div class="step-subtitle">To accounting software</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Right Column - Upload Panel -->
        <section class="upload-panel">
            <div class="upload-header">
                <h2 class="upload-title">Start Your Upload</h2>
                <p class="upload-subtitle">Get categorized transactions in minutes</p>
            </div>

            <!-- What Happens Next - Compact -->
            <div class="info-section">
                <h3 class="info-title">What happens next:</h3>
                <ul class="info-list">
                    <li class="info-item">
                        <span class="info-icon">□</span>
                        <span>AI extracts all transactions</span>
                    </li>
                    <li class="info-item">
                        <span class="info-icon">□</span>
                        <span>87% accurate categorization</span>
                    </li>
                    <li class="info-item">
                        <span class="info-icon">□</span>
                        <span>Review & adjust as needed</span>
                    </li>
                    <li class="info-item">
                        <span class="info-icon">□</span>
                        <span>Export to any software</span>
                    </li>
                </ul>
            </div>

            <!-- Upload Zone -->
            <div class="upload-zone" onclick="document.getElementById('fileInput').click();">
                <input type="file" id="fileInput" style="display: none;" accept=".xlsx,.xls,.csv,.pdf,.qbo,.ofx,.txt" multiple>
                <div class="upload-icon">↑</div>
                <div class="upload-text">Choose files to upload</div>
                <div class="upload-formats">Excel, CSV, PDF, OFX, QBO</div>
            </div>

            <!-- Upload Button -->
            <button class="btn btn-primary">
                Select Files
            </button>

            <!-- Security Note -->
            <div class="security-note">
                <span>🔒</span>
                <span>Bank-level encryption • Files deleted after processing</span>
            </div>
        </section>

        <!-- Agent Panel -->
        <aside class="agent-panel">
            <div class="agent-header">
                <div class="agent-title">
                    <div class="agent-avatar">AI</div>
                    <span>giki Assistant</span>
                </div>
                <button style="background: none; border: none; color: var(--ui-text-muted); cursor: pointer; font-size: 1.25rem;">×</button>
            </div>
            
            <div class="agent-content">
                <div class="agent-message">
                    <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Hi! I'm your AI assistant.</strong></p>
                    <p style="font-size: 0.875rem; color: var(--ui-text-secondary);">I can help you with uploading your bank statements, understanding file formats, and getting the most accurate categorization results.</p>
                </div>
                
                <div class="agent-message">
                    <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Quick Tips:</strong></p>
                    <ul style="font-size: 0.875rem; color: var(--ui-text-secondary); margin-left: var(--space-4);">
                        <li>Excel and CSV files work best</li>
                        <li>Include date, amount, and description columns</li>
                        <li>One file per bank account for better accuracy</li>
                    </ul>
                </div>
            </div>
            
            <div class="agent-input">
                <textarea class="agent-input-field" placeholder="Ask me anything about uploading your files..."></textarea>
                <div class="agent-quick-actions">
                    <button class="quick-action">File formats</button>
                    <button class="quick-action">Best practices</button>
                    <button class="quick-action">Troubleshooting</button>
                </div>
            </div>
        </aside>
    </div>

    <script>
        // File upload handling
        const uploadZone = document.querySelector('.upload-zone');
        const fileInput = document.getElementById('fileInput');
        const uploadButton = document.querySelector('.btn-primary');

        uploadButton.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadZone.addEventListener(eventName, () => {
                uploadZone.style.borderColor = '#1D372E';
                uploadZone.style.background = 'rgba(41, 83, 67, 0.05)';
            });
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadZone.addEventListener(eventName, () => {
                uploadZone.style.borderColor = '';
                uploadZone.style.background = '';
            });
        });

        uploadZone.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            handleFiles(files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            if (files.length > 0) {
                // Show uploading state
                uploadZone.innerHTML = `
                    <div class="upload-icon" style="background: var(--status-success);">□</div>
                    <div class="upload-text">${files.length} file(s) selected</div>
                    <div class="upload-formats">Processing will begin after upload</div>
                `;
                
                // Simulate upload
                setTimeout(() => {
                    window.location.href = '/processing';
                }, 1500);
            }
        }
    </script>
</body>
</html>