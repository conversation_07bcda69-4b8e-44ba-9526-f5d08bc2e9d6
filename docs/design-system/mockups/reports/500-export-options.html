<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Your Data - giki.ai</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        /* Design System Variables */
        :root {
            /* Primary Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            --brand-primary-alpha: rgba(41, 83, 67, 0.1);
            
            /* Professional UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Status & Feedback Colors */
            --status-success: #059669;
            --status-warning: #D97706;
            --status-error: #DC2626;
            --status-info: #2563EB;
            --status-neutral: #6B7280;
            
            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            /* Layout Variables */
            --nav-collapsed: 64px;
            --nav-expanded: 240px;
            --agent-panel: 400px;
            --content-max: 1200px;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            line-height: 1.5;
        }

        /* Three-Panel Layout with Agent Panel */
        .app-layout {
            display: grid;
            grid-template-columns: var(--nav-collapsed) 1fr var(--agent-panel);
            min-height: 100vh;
            position: relative;
        }

        /* Agent Activation Badge */
        .agent-activation-badge {
            position: fixed;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 48px;
            height: 48px;
            background: var(--brand-primary);
            border-radius: 12px 0 0 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .agent-activation-badge:hover {
            background: var(--brand-primary-hover);
            transform: translateY(-50%) translateX(-4px);
        }

        .agent-activation-badge img {
            width: 24px;
            height: 24px;
            filter: brightness(0) invert(1);
        }

        /* Agent activation pulse animation */
        .agent-activation-badge::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: #fff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pulse-dot 2s infinite;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.5; transform: translate(-50%, -50%) scale(1.2); }
        }

        /* Header */
        .header {
            background: var(--ui-background);
            border-bottom: 1px solid var(--ui-border);
            padding: var(--space-4) var(--space-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
            grid-column: 1 / -1;
            z-index: 10;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
        }

        .logo-text .ai {
            color: var(--ui-text-secondary);
        }

        .header-subtitle {
            color: var(--ui-text-secondary);
            font-size: 0.875rem;
            margin-left: var(--space-3);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            color: var(--ui-text-primary);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: var(--brand-primary);
            color: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        /* Left Navigation Panel */
        .left-panel {
            background: var(--ui-background);
            border-right: 1px solid var(--ui-border);
            padding: 0;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .nav-header {
            padding: var(--space-5);
            border-bottom: 1px solid var(--ui-border);
            text-align: center;
        }

        .nav-section {
            margin-bottom: var(--space-8);
        }

        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--ui-text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--space-3);
            padding: 0 var(--space-4);
        }

        .nav-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--ui-text-secondary);
            text-decoration: none;
            transition: all 0.15s ease;
            font-size: 1.25rem;
            border-radius: 6px;
            margin: 0 var(--space-2);
        }

        .nav-item:hover {
            background: var(--brand-primary-alpha);
            color: var(--brand-primary);
        }

        .nav-item.active {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
            font-weight: 600;
        }

        .nav-icon {
            font-size: 1.5rem;
            font-weight: 700;
        }

        /* Main Content */
        .main-content {
            padding: var(--space-8) var(--space-6);
            overflow-y: auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: var(--space-2);
        }

        .page-subtitle {
            font-size: 1.125rem;
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-6);
        }

        /* Export Options */
        .export-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 48px;
        }

        .export-card {
            background: white;
            border: 2px solid #E5E7EB;
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .export-card:hover {
            border-color: #295343;
            box-shadow: 0 8px 25px rgba(41, 83, 67, 0.15);
            transform: translateY(-4px);
        }

        .export-card.recommended::before {
            content: 'MOST POPULAR';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(90deg, #295343, #1D372E);
            color: white;
            padding: 8px;
            font-size: 12px;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .export-card.recommended {
            border-color: #295343;
            margin-top: 32px;
        }

        .export-icon {
            width: 80px;
            height: 80px;
            background: #E8F5E8;
            color: #295343;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: 700;
            margin: 0 auto 24px;
            transition: all 0.3s ease;
        }

        .export-card:hover .export-icon {
            background: #295343;
            color: white;
            transform: scale(1.1);
        }

        .export-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #295343;
            margin-bottom: 12px;
        }

        .export-description {
            color: #6B7280;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .export-features {
            text-align: left;
            margin-bottom: 24px;
        }

        .export-feature {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .export-feature-icon {
            color: #295343;
            font-weight: 700;
        }

        .export-button {
            background: #295343;
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .export-button:hover {
            background: #1D372E;
            transform: translateY(-1px);
        }

        .export-button.secondary {
            background: white;
            color: #295343;
            border: 2px solid #295343;
        }

        .export-button.secondary:hover {
            background: #295343;
            color: white;
        }

        /* Quick Export Section */
        .quick-export {
            background: linear-gradient(135deg, rgba(41, 83, 67, 0.05), rgba(41, 83, 67, 0.1));
            border: 1px solid rgba(41, 83, 67, 0.2);
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            margin-bottom: 32px;
        }

        .quick-export-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #295343;
            margin-bottom: 16px;
        }

        .quick-export-description {
            color: #6B7280;
            margin-bottom: 24px;
        }

        .quick-export-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .quick-button {
            background: #295343;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quick-button:hover {
            background: #1D372E;
            transform: translateY(-1px);
        }

        /* File Preview */
        .file-preview {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 32px;
        }

        .preview-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #295343;
            margin-bottom: 16px;
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .preview-table th {
            background: #F8F9FA;
            color: #374151;
            font-weight: 600;
            padding: 12px;
            text-align: left;
            border-bottom: 2px solid #E5E7EB;
        }

        .preview-table td {
            padding: 12px;
            border-bottom: 1px solid #F3F4F6;
        }

        .preview-table tr:hover {
            background: #F9FAFB;
        }

        .category-tag {
            background: #E8F5E8;
            color: #295343;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        /* Enhancement Options */
        .enhancement-section {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
        }

        .enhancement-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #295343;
            margin-bottom: 16px;
            text-align: center;
        }

        .enhancement-description {
            color: #6B7280;
            text-align: center;
            margin-bottom: 24px;
        }

        .enhancement-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .enhancement-card {
            background: #F8F9FA;
            border: 1px solid #E1E5E9;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .enhancement-card:hover {
            border-color: #295343;
            background: rgba(41, 83, 67, 0.02);
        }

        .enhancement-icon {
            font-size: 24px;
            margin-bottom: 12px;
        }

        .enhancement-name {
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
        }

        .enhancement-benefit {
            color: #6B7280;
            font-size: 12px;
            margin-bottom: 12px;
        }

        .enhancement-button {
            background: white;
            color: #295343;
            border: 1px solid #295343;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }

        .enhancement-button:hover {
            background: #295343;
            color: white;
        }

        /* Navigation */
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 32px;
        }

        .nav-button {
            background: white;
            color: #295343;
            border: 2px solid #295343;
            padding: 14px 28px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-button:hover {
            background: #295343;
            color: white;
        }

        .nav-button.primary {
            background: #295343;
            color: white;
        }

        .nav-button.primary:hover {
            background: #1D372E;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .export-options {
                grid-template-columns: 1fr;
            }
            
            .quick-export-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .enhancement-options {
                grid-template-columns: 1fr;
            }
            
            .navigation {
                flex-direction: column;
                gap: 16px;
            }
            
            .preview-table {
                font-size: 12px;
            }
            
            .preview-table th,
            .preview-table td {
                padding: 8px;
            }
        }

        /* Agent Panel */
        .agent-panel {
            background: var(--ui-background);
            border-left: 1px solid var(--ui-border);
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
        }

        .agent-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--ui-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .agent-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .agent-content {
            flex: 1;
            padding: var(--space-6);
            overflow-y: auto;
        }

        .agent-message {
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            background: var(--ui-surface);
            border-radius: 12px;
            border-left: 3px solid var(--brand-primary);
        }

        .agent-input {
            padding: var(--space-4);
            border-top: 1px solid var(--ui-border);
            background: var(--ui-surface);
        }

        .agent-input-field {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            font-size: 0.875rem;
            resize: none;
            height: 60px;
        }

        .agent-input-field:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 2px rgba(41, 83, 67, 0.1);
        }

        .agent-quick-actions {
            display: flex;
            gap: var(--space-2);
            margin-top: var(--space-3);
        }

        .quick-action {
            padding: var(--space-2) var(--space-3);
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 6px;
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .quick-action:hover {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <img src="/images/giki-logo.svg" alt="giki.ai" style="height: 40px;">
                <div class="header-subtitle">Export Ready</div>
            </div>
            <div class="user-info">
                <div>
                    <div style="font-weight: 600;">Business Owner</div>
                    <div style="font-size: 12px; color: var(--ui-text-muted);">Choose Format</div>
                </div>
                <div class="user-avatar">BO</div>
            </div>
        </header>

        <!-- Left Navigation Panel -->
        <aside class="left-panel">
            <div class="nav-header">
                <img src="/images/giki-logo.svg" alt="giki.ai" style="height: 32px; opacity: 0.8;">
            </div>
            
            <div style="padding: var(--space-4);">
                <div class="nav-section">
                    <div class="nav-section-title">WORK</div>
                    <a href="/dashboard" class="nav-item" title="Dashboard">
                        <span class="nav-icon">□</span>
                    </a>
                    <a href="/upload" class="nav-item" title="Upload">
                        <span class="nav-icon">↑</span>
                    </a>
                    <a href="/transactions" class="nav-item active" title="Transactions">
                        <span class="nav-icon">⊞</span>
                    </a>
                    <a href="/reports" class="nav-item" title="Reports">
                        <span class="nav-icon">∷</span>
                    </a>
                    <a href="/categories" class="nav-item" title="Categories">
                        <span class="nav-icon">⚬</span>
                    </a>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
        <!-- Streamlined Export Header -->
        <div class="export-header" style="text-align: center; margin-bottom: var(--space-8);">
            <h1 style="font-size: 2rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--space-2);">Export Your Data</h1>
            <p style="font-size: 1.125rem; color: var(--ui-text-secondary);">Choose your format and download</p>
        </div>

        <!-- Combined Export Interface with Format Selection -->
        <div class="format-selector" style="display: flex; justify-content: center; gap: var(--space-4); margin-bottom: var(--space-8);">
            <div class="format-option selected" onclick="selectFormat('excel')" style="background: var(--ui-background); border: 2px solid var(--brand-primary); border-radius: 12px; padding: var(--space-4) var(--space-6); cursor: pointer; transition: all 0.2s;">
                <div style="display: flex; align-items: center; gap: var(--space-3);">
                    <span style="font-size: 24px;">∷</span>
                    <div>
                        <div style="font-weight: 600; color: var(--brand-primary);">Excel</div>
                        <div style="font-size: 0.875rem; color: var(--ui-text-secondary);">Complete workbook</div>
                    </div>
                </div>
            </div>
            
            <div class="format-option" onclick="selectFormat('quickbooks')" style="background: var(--ui-background); border: 2px solid var(--ui-border); border-radius: 12px; padding: var(--space-4) var(--space-6); cursor: pointer; transition: all 0.2s;">
                <div style="display: flex; align-items: center; gap: var(--space-3);">
                    <span style="font-size: 24px;">⊞</span>
                    <div>
                        <div style="font-weight: 600; color: var(--ui-text-primary);">QuickBooks</div>
                        <div style="font-size: 0.875rem; color: var(--ui-text-secondary);">QBO format</div>
                    </div>
                </div>
            </div>
            
            <div class="format-option" onclick="selectFormat('csv')" style="background: var(--ui-background); border: 2px solid var(--ui-border); border-radius: 12px; padding: var(--space-4) var(--space-6); cursor: pointer; transition: all 0.2s;">
                <div style="display: flex; align-items: center; gap: var(--space-3);">
                    <span style="font-size: 24px;">□</span>
                    <div>
                        <div style="font-weight: 600; color: var(--ui-text-primary);">CSV</div>
                        <div style="font-size: 0.875rem; color: var(--ui-text-secondary);">Simple format</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Format Preview -->
        <div class="format-preview" style="background: var(--ui-background); border: 1px solid var(--ui-border); border-radius: 12px; padding: var(--space-6); margin-bottom: var(--space-8);">
            <h3 style="font-weight: 600; color: var(--brand-primary); margin-bottom: var(--space-4);">Preview: <span id="formatName">Excel</span> Format</h3>
            
            <!-- Excel Preview (default) -->
            <div id="excelPreview" class="preview-content">
                <div style="display: flex; gap: var(--space-2); margin-bottom: var(--space-3);">
                    <span style="background: var(--ui-surface); padding: var(--space-2) var(--space-3); border-radius: 4px; font-size: 0.875rem;">Sheet 1: Transactions</span>
                    <span style="background: var(--brand-primary-light); padding: var(--space-2) var(--space-3); border-radius: 4px; font-size: 0.875rem; color: var(--brand-primary);">Sheet 2: Summary</span>
                    <span style="background: var(--ui-surface); padding: var(--space-2) var(--space-3); border-radius: 4px; font-size: 0.875rem;">Sheet 3: Pivot</span>
                </div>
                <div style="overflow-x: auto;">
                    <table class="preview-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Vendor</th>
                                <th>Amount</th>
                                <th>Category</th>
                                <th>Sub-Category</th>
                                <th>GL Code</th>
                                <th>Confidence</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-03-15</td>
                                <td>AMAZON.COM</td>
                                <td>$127.49</td>
                                <td>Operating Expenses</td>
                                <td>Office Supplies</td>
                                <td>5100</td>
                                <td>94%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- QuickBooks Preview (hidden) -->
            <div id="quickbooksPreview" class="preview-content" style="display: none;">
                <div style="background: var(--brand-primary-light); border: 1px solid var(--brand-primary); border-radius: 8px; padding: var(--space-4); margin-bottom: var(--space-3);">
                    <div style="font-weight: 600; color: var(--brand-primary); margin-bottom: var(--space-2);">QBO Import Format</div>
                    <div style="font-family: monospace; font-size: 0.875rem; color: var(--ui-text-secondary);">
                        !TRNS TRNSID TRNSTYPE DATE ACCNT NAME CLASS AMOUNT DOCNUM MEMO<br>
                        !SPL SPLID TRNSTYPE DATE ACCNT NAME CLASS AMOUNT DOCNUM MEMO<br>
                        !ENDTRNS
                    </div>
                </div>
            </div>
            
            <!-- CSV Preview (hidden) -->
            <div id="csvPreview" class="preview-content" style="display: none;">
                <div style="background: var(--ui-surface); border: 1px solid var(--ui-border); border-radius: 8px; padding: var(--space-4);">
                    <pre style="font-family: monospace; font-size: 0.875rem; margin: 0;">Date,Description,Amount,Category,Confidence
2024-03-15,"AMAZON.COM AMZN.COM/BILL",127.49,"Office Supplies",94
2024-03-14,"UBER TRIP SAN FRANCISCO",23.45,"Travel & Entertainment",98</pre>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="export-options" style="background: var(--ui-background); border: 1px solid var(--ui-border); border-radius: 12px; padding: var(--space-4); margin-bottom: var(--space-8);">
            <h4 style="font-weight: 600; margin-bottom: var(--space-3);">Export Options</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--space-3);">
                <label style="display: flex; align-items: center; gap: var(--space-2); cursor: pointer;">
                    <input type="checkbox" checked>
                    <span>Include category hierarchies</span>
                </label>
                <label style="display: flex; align-items: center; gap: var(--space-2); cursor: pointer;">
                    <input type="checkbox" checked>
                    <span>Add confidence scores</span>
                </label>
                <label style="display: flex; align-items: center; gap: var(--space-2); cursor: pointer;">
                    <input type="checkbox">
                    <span>Include original descriptions</span>
                </label>
                <label style="display: flex; align-items: center; gap: var(--space-2); cursor: pointer;">
                    <input type="checkbox">
                    <span>Add transaction notes</span>
                </label>
            </div>
        </div>

        <!-- Download Button -->
        <div style="text-align: center; margin-bottom: var(--space-8);">
            <button id="downloadBtn" class="export-button" style="background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%); color: white; padding: var(--space-4) var(--space-8); font-size: 1.125rem;">
                <span>↑</span>
                <span id="downloadText">Download Excel File</span>
            </button>
            <div style="margin-top: var(--space-2); color: var(--ui-text-secondary); font-size: 0.875rem;">
                247 transactions • 18 categories • 89% accuracy
            </div>
        </div>

        <!-- Download Success State (hidden) -->
        <div id="downloadSuccess" style="display: none; text-align: center; background: var(--status-success); color: white; padding: var(--space-6); border-radius: 12px; margin-bottom: var(--space-8);">
            <div style="font-size: 48px; margin-bottom: var(--space-3);">□</div>
            <h3 style="font-size: 1.5rem; margin-bottom: var(--space-2);">Download Complete!</h3>
            <p style="margin-bottom: var(--space-4);">Your file has been downloaded successfully.</p>
            <button onclick="window.location.href='/dashboard'" style="background: white; color: var(--status-success); border: none; padding: var(--space-3) var(--space-6); border-radius: 8px; font-weight: 600; cursor: pointer;">
                Go to Dashboard →
            </button>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <button class="nav-button">
                <span>←</span>
                <span>Back to Results</span>
            </button>
            <button class="nav-button primary">
                <span>Continue to Dashboard</span>
                <span>→</span>
            </button>
        </div>
        </main>
        
    <!-- Agent Panel -->
    <aside class="agent-panel">
        <div class="agent-header">
            <div class="agent-title">
                <div class="agent-avatar">AI</div>
                <span>giki Assistant</span>
            </div>
            <button style="background: none; border: none; color: var(--ui-text-muted); cursor: pointer; font-size: 1.25rem;">×</button>
        </div>
        
        <div class="agent-content">
            <div class="agent-message">
                <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Export Ready!</strong></p>
                <p style="font-size: 0.875rem; color: var(--ui-text-secondary);">Your data is ready to export. I can help you choose the best format for your needs, set up automatic exports, or integrate with your accounting software.</p>
            </div>
            
            <div class="agent-message">
                <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Format Recommendations:</strong></p>
                <ul style="font-size: 0.875rem; color: var(--ui-text-secondary); margin-left: var(--space-4);">
                    <li>Excel: Best for detailed analysis and custom reports</li>
                    <li>QuickBooks: Direct import to QuickBooks Online</li>
                    <li>CSV: Universal format for other accounting software</li>
                    <li>Include confidence scores for validation</li>
                </ul>
            </div>
            
            <div class="agent-message">
                <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Export Summary:</strong></p>
                <p style="font-size: 0.875rem; color: var(--ui-text-secondary);">247 transactions categorized into 18 business categories with 89% average confidence. Ready for immediate accounting use.</p>
            </div>
        </div>
        
        <div class="agent-input">
            <textarea class="agent-input-field" placeholder="Ask me about export formats, accounting integration, or setup help..."></textarea>
            <div class="agent-quick-actions">
                <button class="quick-action">Best format</button>
                <button class="quick-action">Integration help</button>
                <button class="quick-action">Schedule exports</button>
            </div>
        </div>
    </aside>
        
    <!-- Agent Activation Badge -->
    <div class="agent-activation-badge" title="Open AI Assistant">
        <img src="/images/giki-logo.svg" alt="AI Assistant" />
    </div>
    
    </div> <!-- End app-layout -->

    <script>
        // Format selection functionality
        function selectFormat(format) {
            // Update selected state
            document.querySelectorAll('.format-option').forEach(option => {
                option.style.border = '2px solid var(--ui-border)';
                option.querySelectorAll('div').forEach(div => {
                    if (div.style.fontWeight === '600') {
                        div.style.color = 'var(--ui-text-primary)';
                    }
                });
            });
            
            // Highlight selected
            event.currentTarget.style.border = '2px solid var(--brand-primary)';
            event.currentTarget.querySelector('div[style*="font-weight: 600"]').style.color = 'var(--brand-primary)';
            
            // Update preview
            document.querySelectorAll('.preview-content').forEach(preview => {
                preview.style.display = 'none';
            });
            
            // Update format name and show preview
            const formatName = document.getElementById('formatName');
            const downloadText = document.getElementById('downloadText');
            
            switch(format) {
                case 'excel':
                    document.getElementById('excelPreview').style.display = 'block';
                    formatName.textContent = 'Excel';
                    downloadText.textContent = 'Download Excel File';
                    break;
                case 'quickbooks':
                    document.getElementById('quickbooksPreview').style.display = 'block';
                    formatName.textContent = 'QuickBooks';
                    downloadText.textContent = 'Download QBO File';
                    break;
                case 'csv':
                    document.getElementById('csvPreview').style.display = 'block';
                    formatName.textContent = 'CSV';
                    downloadText.textContent = 'Download CSV File';
                    break;
            }
        }
        
        // Download functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Download button
            const downloadBtn = document.getElementById('downloadBtn');
            downloadBtn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Show downloading state
                const originalContent = this.innerHTML;
                this.innerHTML = '<span>⏳</span><span>Preparing download...</span>';
                this.disabled = true;
                
                // Progress simulation
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += 20;
                    if (progress <= 100) {
                        this.innerHTML = `<span>⏳</span><span>Preparing... ${progress}%</span>`;
                    }
                }, 300);
                
                // Complete after 1.5 seconds
                setTimeout(() => {
                    clearInterval(progressInterval);
                    
                    // Hide button container
                    this.parentElement.style.display = 'none';
                    
                    // Show success state
                    document.getElementById('downloadSuccess').style.display = 'block';
                    
                    // Simulate actual download
                    console.log('Download initiated for selected format');
                }, 1500);
            });
            
            // Format option hover effects
            document.querySelectorAll('.format-option').forEach(option => {
                option.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('selected')) {
                        this.style.borderColor = 'var(--brand-primary)';
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 4px 12px rgba(41, 83, 67, 0.1)';
                    }
                });
                
                option.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('selected')) {
                        this.style.borderColor = 'var(--ui-border)';
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = 'none';
                    }
                });
            });
            
            // Checkbox interactions
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    console.log('Export option changed:', this.nextElementSibling.textContent, this.checked);
                });
            });
            
            // Navigation buttons
            const navButtons = document.querySelectorAll('.nav-button');
            navButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (this.textContent.includes('Results')) {
                        window.location.href = '300-categorization-results.html';
                    }
                });
            });
        });
    </script>
</body>
</html>