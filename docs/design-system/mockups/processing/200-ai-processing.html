<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Processing Your Transactions - giki.ai</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        /* Design System Variables */
        :root {
            /* Primary Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            --brand-primary-alpha: rgba(41, 83, 67, 0.1);
            
            /* Professional UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Status & Feedback Colors */
            --status-success: #059669;
            --status-warning: #D97706;
            --status-error: #DC2626;
            --status-info: #2563EB;
            --status-neutral: #6B7280;
            
            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            /* Layout Variables */
            --nav-collapsed: 64px;
            --nav-expanded: 240px;
            --agent-panel: 400px;
            --content-max: 1200px;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            line-height: 1.5;
        }

        /* Two-Panel Layout with Agent Activation */
        .app-layout {
            display: grid;
            grid-template-columns: var(--nav-collapsed) 1fr var(--agent-panel);
            min-height: 100vh;
            position: relative;
        }

        /* Agent Activation Badge */
        .agent-activation-badge {
            position: fixed;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 48px;
            height: 48px;
            background: var(--brand-primary);
            border-radius: 12px 0 0 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .agent-activation-badge:hover {
            background: var(--brand-primary-hover);
            transform: translateY(-50%) translateX(-4px);
        }

        .agent-activation-badge img {
            width: 24px;
            height: 24px;
            filter: brightness(0) invert(1);
        }

        /* Agent activation pulse animation */
        .agent-activation-badge::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: #fff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pulse-dot 2s infinite;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.5; transform: translate(-50%, -50%) scale(1.2); }
        }

        /* Header */
        .header {
            background: var(--ui-background);
            border-bottom: 1px solid var(--ui-border);
            padding: var(--space-4) var(--space-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
            grid-column: 1 / -1;
            z-index: 10;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
        }

        .logo-text .ai {
            color: var(--ui-text-secondary);
        }

        .header-subtitle {
            color: var(--ui-text-secondary);
            font-size: 0.875rem;
            margin-left: var(--space-3);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            color: var(--ui-text-primary);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: var(--brand-primary);
            color: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        /* Left Navigation Panel */
        .left-panel {
            background: var(--ui-background);
            border-right: 1px solid var(--ui-border);
            padding: 0;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .nav-header {
            padding: var(--space-5);
            border-bottom: 1px solid var(--ui-border);
            text-align: center;
        }

        .nav-section {
            margin-bottom: var(--space-8);
        }

        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--ui-text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--space-3);
            padding: 0 var(--space-4);
        }

        .nav-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--ui-text-secondary);
            text-decoration: none;
            transition: all 0.15s ease;
            font-size: 1.25rem;
            border-radius: 6px;
            margin: 0 var(--space-2);
        }

        .nav-item:hover {
            background: var(--brand-primary-alpha);
            color: var(--brand-primary);
        }

        .nav-item.active {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
            font-weight: 600;
        }

        .nav-icon {
            font-size: 1.5rem;
            font-weight: 700;
        }

        /* Main Content */
        .main-content {
            padding: var(--space-8) var(--space-6);
            overflow-y: auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: var(--space-2);
        }

        .page-subtitle {
            font-size: 1.125rem;
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-6);
        }

        /* Processing Status */
        .processing-container {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 16px;
            padding: var(--space-12);
            text-align: center;
            margin-bottom: var(--space-12);
        }

        .processing-animation {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 50%, #3F1A5F 100%);
            border-radius: 50%;
            margin: 0 auto var(--space-6);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(41, 83, 67, 0.3);
        }

        .processing-animation::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: spin 2s linear infinite;
        }

        .processing-icon {
            font-size: 48px;
            color: white;
            font-weight: 700;
            z-index: 1;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .processing-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--brand-primary);
            margin-bottom: var(--space-3);
        }

        .processing-description {
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-8);
            font-size: 1.1rem;
        }

        /* Progress Steps */
        .progress-steps {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
            margin-bottom: 32px;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .step-circle {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }

        .step.completed .step-circle {
            background: var(--status-success);
            color: white;
            box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
        }

        .step.active .step-circle {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            color: white;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 12px rgba(41, 83, 67, 0.3);
        }

        .step.pending .step-circle {
            background: var(--ui-surface);
            color: var(--ui-text-muted);
            border: 2px solid var(--ui-border);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .step-title {
            font-weight: 600;
            color: var(--brand-primary);
            margin-bottom: 4px;
            font-size: 14px;
        }

        .step.pending .step-title {
            color: var(--ui-text-muted);
        }

        .step-description {
            color: var(--ui-text-secondary);
            font-size: 12px;
        }

        .step.pending .step-description {
            color: var(--ui-border);
        }

        /* File Info */
        .file-info {
            background: var(--brand-primary-alpha);
            border: 1px solid var(--brand-primary);
            border-radius: 12px;
            padding: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .file-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            text-align: left;
        }

        .file-detail {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-detail-label {
            color: #6B7280;
            font-size: 14px;
        }

        .file-detail-value {
            color: #295343;
            font-weight: 600;
            font-size: 14px;
        }

        /* Current Activity */
        .current-activity {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 50%, #3F1A5F 100%);
            border: none;
            border-radius: 12px;
            padding: var(--space-5);
            margin-bottom: var(--space-6);
            box-shadow: 0 4px 12px rgba(41, 83, 67, 0.2);
        }

        .activity-text {
            color: white;
            font-weight: 600;
            text-align: center;
        }

        /* Estimated Time */
        .estimated-time {
            color: #6B7280;
            font-size: 14px;
            text-align: center;
        }

        /* Trust Indicators */
        .trust-indicators {
            display: flex;
            justify-content: center;
            gap: 48px;
            margin-top: 48px;
            padding-top: 32px;
            border-top: 1px solid #E5E7EB;
        }

        .trust-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6B7280;
            font-size: 14px;
        }

        .trust-icon {
            font-size: 16px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .progress-steps {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }
            
            .file-details {
                grid-template-columns: 1fr;
            }
            
            .trust-indicators {
                flex-direction: column;
                gap: 16px;
                align-items: center;
            }
        }

        /* Agent Panel */
        .agent-panel {
            background: var(--ui-background);
            border-left: 1px solid var(--ui-border);
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
        }

        .agent-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--ui-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .agent-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .agent-content {
            flex: 1;
            padding: var(--space-6);
            overflow-y: auto;
        }

        .agent-message {
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            background: var(--ui-surface);
            border-radius: 12px;
            border-left: 3px solid var(--brand-primary);
        }

        .agent-input {
            padding: var(--space-4);
            border-top: 1px solid var(--ui-border);
            background: var(--ui-surface);
        }

        .agent-input-field {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            font-size: 0.875rem;
            resize: none;
            height: 60px;
        }

        .agent-input-field:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 2px rgba(41, 83, 67, 0.1);
        }

        .agent-quick-actions {
            display: flex;
            gap: var(--space-2);
            margin-top: var(--space-3);
        }

        .quick-action {
            padding: var(--space-2) var(--space-3);
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 6px;
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .quick-action:hover {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <img src="/images/giki-logo.svg" alt="giki.ai" style="height: 40px;">
                <div class="header-subtitle">Processing Your Data</div>
            </div>
            <div class="user-info">
                <div>
                    <div style="font-weight: 600;">Business Owner</div>
                    <div style="font-size: 12px; color: var(--ui-text-muted);">Processing...</div>
                </div>
                <div class="user-avatar">BO</div>
            </div>
        </header>

        <!-- Left Navigation Panel -->
        <aside class="left-panel">
            <div class="nav-header">
                <img src="/images/giki-logo.svg" alt="giki.ai" style="height: 32px; opacity: 0.8;">
            </div>
            
            <div style="padding: var(--space-4);">
                <div class="nav-section">
                    <div class="nav-section-title">WORK</div>
                    <a href="/dashboard" class="nav-item" title="Dashboard">
                        <span class="nav-icon">□</span>
                    </a>
                    <a href="/files/upload" class="nav-item active" title="Upload">
                        <span class="nav-icon">↑</span>
                    </a>
                    <a href="/transactions" class="nav-item" title="Transactions">
                        <span class="nav-icon">⊞</span>
                    </a>
                    <a href="/reports" class="nav-item" title="Reports">
                        <span class="nav-icon">∷</span>
                    </a>
                    <a href="/categories" class="nav-item" title="Categories">
                        <span class="nav-icon">⚬</span>
                    </a>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
        <!-- Header -->
        <div style="margin-bottom: var(--space-8);">
            <h1 style="font-size: 2rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--space-2);">Processing Center</h1>
            <p style="font-size: 1.125rem; color: var(--ui-text-secondary);">
                Monitor all your file processing activities in one place
            </p>
        </div>

        <!-- Two-Column Layout: Jobs List + Details Panel -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-8);">
            <!-- Left Column: Processing Jobs List -->
            <div style="space-y: var(--space-6);">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-6);">
                    <h2 style="font-size: 1.25rem; font-weight: 600; color: var(--brand-primary);">Active Processing Jobs</h2>
                    <button style="background: var(--brand-primary); color: white; padding: var(--space-2) var(--space-4); border-radius: 8px; border: none; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: var(--space-2);">
                        <span style="font-size: 1rem;">↑</span>
                        Upload Files
                    </button>
                </div>

                <!-- Processing Jobs List -->
                <div style="display: flex; flex-direction: column; gap: var(--space-4);">
                    <!-- Job 1 - Selected -->
                    <div style="background: var(--ui-background); border: 2px solid var(--brand-primary); border-radius: 12px; padding: var(--space-4); cursor: pointer; box-shadow: 0 2px 8px rgba(41, 83, 67, 0.1);">
                        <div style="display: flex; align-items: start; justify-content: space-between;">
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: var(--space-3); margin-bottom: var(--space-2);">
                                    <div style="font-size: 1.25rem; color: var(--brand-primary);">□</div>
                                    <h3 style="font-weight: 600; color: var(--brand-primary); font-size: 0.875rem;">test_transactions_uncategorized.xlsx</h3>
                                </div>
                                <div style="display: flex; align-items: center; gap: var(--space-4); font-size: 0.75rem; color: var(--ui-text-secondary);">
                                    <span>247 transactions</span>
                                    <span>Started 2:34 PM</span>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <div style="display: flex; align-items: center; gap: var(--space-2); color: var(--status-warning);">
                                    <div style="width: 8px; height: 8px; background: var(--status-warning); border-radius: 50%; animation: pulse 1s infinite;"></div>
                                    <span style="font-weight: 500; font-size: 0.75rem;">Processing</span>
                                </div>
                                <div style="margin-top: var(--space-2);">
                                    <div style="width: 60px; background: var(--ui-border); border-radius: 999px; height: 4px;">
                                        <div style="background: var(--brand-primary); height: 100%; width: 45%; border-radius: 999px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Job 2 -->
                    <div style="background: var(--ui-background); border: 1px solid var(--ui-border); border-radius: 12px; padding: var(--space-4); cursor: pointer; transition: all 0.2s ease;">
                        <div style="display: flex; align-items: start; justify-content: space-between;">
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: var(--space-3); margin-bottom: var(--space-2);">
                                    <div style="font-size: 1.25rem; color: var(--brand-primary);">□</div>
                                    <h3 style="font-weight: 600; color: var(--ui-text-primary); font-size: 0.875rem;">hdfc_bank_mixed_1000tx.xlsx</h3>
                                </div>
                                <div style="display: flex; align-items: center; gap: var(--space-4); font-size: 0.75rem; color: var(--ui-text-secondary);">
                                    <span>1001 transactions</span>
                                    <span>Started 2:28 PM</span>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <div style="display: flex; align-items: center; gap: var(--space-2); color: var(--status-warning);">
                                    <div style="width: 8px; height: 8px; background: var(--status-warning); border-radius: 50%; animation: pulse 1s infinite;"></div>
                                    <span style="font-weight: 500; font-size: 0.75rem;">Processing</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Job 3 - Completed -->
                    <div style="background: var(--ui-background); border: 1px solid var(--ui-border); border-radius: 12px; padding: var(--space-4); cursor: pointer; transition: all 0.2s ease;">
                        <div style="display: flex; align-items: start; justify-content: space-between;">
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: var(--space-3); margin-bottom: var(--space-2);">
                                    <div style="font-size: 1.25rem; color: var(--brand-primary);">□</div>
                                    <h3 style="font-weight: 600; color: var(--ui-text-primary); font-size: 0.875rem;">march_expenses.csv</h3>
                                </div>
                                <div style="display: flex; align-items: center; gap: var(--space-4); font-size: 0.75rem; color: var(--ui-text-secondary);">
                                    <span>156 transactions</span>
                                    <span>Completed 2:15 PM</span>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <div style="display: flex; align-items: center; gap: var(--space-2); color: var(--status-success);">
                                    <div style="width: 8px; height: 8px; background: var(--status-success); border-radius: 50%;"></div>
                                    <span style="font-weight: 500; font-size: 0.75rem;">Completed</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column: Processing Details Panel -->
            <div style="background: var(--ui-background); border: 1px solid var(--ui-border); border-radius: 16px; padding: var(--space-6);">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-6);">
                    <h2 style="font-size: 1.25rem; font-weight: 600; color: var(--brand-primary);">Processing Details</h2>
                    <button style="background: none; border: none; color: var(--ui-text-secondary); cursor: pointer; display: flex; align-items: center; gap: var(--space-2);">
                        <span style="font-size: 1rem;">←</span>
                    </button>
                </div>

                <!-- Schema Interpretation Results - Compact for inline view -->
                <div style="margin-bottom: var(--space-6);">
                    <h3 style="font-size: 1rem; font-weight: 600; color: var(--brand-primary); margin-bottom: var(--space-4);">Schema Detection Results</h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--space-4); margin-bottom: var(--space-4);">
                        <div style="background: var(--ui-surface); padding: var(--space-3); border-radius: 8px; border: 1px solid var(--ui-border);">
                            <div style="font-size: 0.75rem; color: var(--ui-text-secondary); margin-bottom: var(--space-1);">Date Column</div>
                            <div style="font-weight: 600; color: var(--brand-primary); font-size: 0.875rem;">Transaction Date</div>
                            <div style="font-size: 0.75rem; color: var(--status-success); margin-top: var(--space-1); font-weight: 500;">98% confidence</div>
                        </div>
                        <div style="background: var(--ui-surface); padding: var(--space-3); border-radius: 8px; border: 1px solid var(--ui-border);">
                            <div style="font-size: 0.75rem; color: var(--ui-text-secondary); margin-bottom: var(--space-1);">Description</div>
                            <div style="font-weight: 600; color: var(--brand-primary); font-size: 0.875rem;">Merchant/Vendor</div>
                            <div style="font-size: 0.75rem; color: var(--status-success); margin-top: var(--space-1); font-weight: 500;">95% confidence</div>
                        </div>
                        <div style="background: var(--ui-surface); padding: var(--space-3); border-radius: 8px; border: 1px solid var(--ui-border);">
                            <div style="font-size: 0.75rem; color: var(--ui-text-secondary); margin-bottom: var(--space-1);">Amount</div>
                            <div style="font-weight: 600; color: var(--brand-primary); font-size: 0.875rem;">Debit Amount</div>
                            <div style="font-size: 0.75rem; color: var(--status-success); margin-top: var(--space-1); font-weight: 500;">100% confidence</div>
                        </div>
                        <div style="background: var(--ui-surface); padding: var(--space-3); border-radius: 8px; border: 1px solid var(--ui-border);">
                            <div style="font-size: 0.75rem; color: var(--ui-text-secondary); margin-bottom: var(--space-1);">Category</div>
                            <div style="font-weight: 600; color: var(--brand-primary); font-size: 0.875rem;">AI-Categorizing</div>
                            <div style="font-size: 0.75rem; color: var(--status-warning); margin-top: var(--space-1); font-weight: 500; animation: pulse 1s infinite;">Processing</div>
                        </div>
                    </div>
                    
                    <div style="background: var(--brand-primary-light); border: 1px solid var(--brand-primary); border-radius: 8px; padding: var(--space-3); text-align: center; margin-bottom: var(--space-4);">
                        <div style="font-size: 0.875rem; color: var(--brand-primary); font-weight: 600;">
                            □ Schema validated - High confidence mapping detected
                        </div>
                    </div>
                </div>

                <!-- Categorization Progress - Compact View -->
                <div style="margin-bottom: var(--space-6);">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-4);">
                        <h3 style="font-weight: 600; color: var(--brand-primary); font-size: 1rem;">Categorization Progress</h3>
                        <span style="font-size: 0.875rem; color: var(--brand-primary); font-weight: 500;">45%</span>
                    </div>
                    
                    <div style="width: 100%; background: var(--ui-border); border-radius: 999px; height: 8px; margin-bottom: var(--space-4);">
                        <div style="background: linear-gradient(90deg, var(--brand-primary) 0%, #1A3F5F 100%); height: 100%; width: 45%; border-radius: 999px; transition: width 0.5s ease;"></div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--space-4); font-size: 0.75rem;">
                        <div>
                            <div style="color: var(--ui-text-secondary);">Processed</div>
                            <div style="font-weight: 600; color: var(--brand-primary);">111 of 247</div>
                        </div>
                        <div>
                            <div style="color: var(--ui-text-secondary);">Accuracy</div>
                            <div style="font-weight: 600; color: var(--brand-primary);">87%</div>
                        </div>
                        <div>
                            <div style="color: var(--ui-text-secondary);">Categories Found</div>
                            <div style="font-weight: 600; color: var(--brand-primary);">12</div>
                        </div>
                        <div>
                            <div style="color: var(--ui-text-secondary);">Time Remaining</div>
                            <div style="font-weight: 600; color: var(--brand-primary);">25s</div>
                        </div>
                    </div>
                </div>

                <!-- Live Categorization Feed -->
                <div>
                    <h3 style="font-weight: 600; color: var(--brand-primary); margin-bottom: var(--space-4); font-size: 1rem;">Live Categorization Activity</h3>
                    <div style="max-height: 200px; overflow-y: auto;">
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: var(--space-2) 0; border-bottom: 1px solid var(--ui-surface);">
                            <div style="display: flex; align-items: center; gap: var(--space-2);">
                                <div style="width: 6px; height: 6px; background: var(--status-success); border-radius: 50%; animation: pulse 1s infinite;"></div>
                                <span style="font-size: 0.75rem;">Utility Company - January 2024</span>
                            </div>
                            <span style="font-size: 0.75rem; color: var(--status-success); font-weight: 500;">→ Utilities</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: var(--space-2) 0; border-bottom: 1px solid var(--ui-surface);">
                            <div style="display: flex; align-items: center; gap: var(--space-2);">
                                <div style="width: 6px; height: 6px; background: var(--status-success); border-radius: 50%; animation: pulse 1s infinite;"></div>
                                <span style="font-size: 0.75rem;">Payroll Services Inc - January 2024</span>
                            </div>
                            <span style="font-size: 0.75rem; color: var(--status-success); font-weight: 500;">→ Payroll & Benefits</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: var(--space-2) 0; border-bottom: 1px solid var(--ui-surface);">
                            <div style="display: flex; align-items: center; gap: var(--space-2);">
                                <div style="width: 6px; height: 6px; background: var(--status-success); border-radius: 50%; animation: pulse 1s infinite;"></div>
                                <span style="font-size: 0.75rem;">Tech Hardware Depot - January 2024</span>
                            </div>
                            <span style="font-size: 0.75rem; color: var(--status-success); font-weight: 500;">→ Technology & Equipment</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: var(--space-2) 0;">
                            <div style="display: flex; align-items: center; gap: var(--space-2);">
                                <div style="width: 6px; height: 6px; background: var(--status-warning); border-radius: 50%; animation: pulse 1s infinite;"></div>
                                <span style="font-size: 0.75rem;">Cloud Services Pro - January 2024</span>
                            </div>
                            <span style="font-size: 0.75rem; color: var(--status-warning); font-weight: 500;">→ Analyzing...</span>
                        </div>
                    </div>
                    
                    <!-- Status -->
                    <div style="margin-top: var(--space-4); padding-top: var(--space-4); border-top: 1px solid var(--ui-border);">
                        <div style="display: flex; align-items: center; justify-content: space-between; font-size: 0.75rem;">
                            <div style="display: flex; align-items: center; gap: var(--space-2);">
                                <div style="width: 6px; height: 6px; background: var(--status-success); border-radius: 50%;"></div>
                                <span style="color: var(--ui-text-secondary);">Live updates active</span>
                            </div>
                            <button style="background: var(--brand-primary); color: white; padding: var(--space-2) var(--space-3); border-radius: 6px; border: none; font-weight: 600; cursor: pointer; opacity: 0.5; font-size: 0.75rem;" disabled>
                                Processing...
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </main>
        
        <!-- Agent Panel -->
        <aside class="agent-panel">
            <div class="agent-header">
                <div class="agent-title">
                    <div class="agent-avatar">AI</div>
                    <span>giki Assistant</span>
                </div>
                <button style="background: none; border: none; color: var(--ui-text-muted); cursor: pointer; font-size: 1.25rem;">×</button>
            </div>
            
            <div class="agent-content">
                <div class="agent-message">
                    <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Processing Monitor Active</strong></p>
                    <p style="font-size: 0.875rem; color: var(--ui-text-secondary);">I'm watching your file processing in real-time. I can help explain what's happening, troubleshoot issues, or answer questions about your categorization results.</p>
                </div>
                
                <div class="agent-message">
                    <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Current Status:</strong></p>
                    <ul style="font-size: 0.875rem; color: var(--ui-text-secondary); margin-left: var(--space-4);">
                        <li>Schema detection: 98% confidence</li>
                        <li>Processing speed: 4.2 transactions/second</li>
                        <li>Current accuracy: 87%</li>
                        <li>Estimated completion: 25 seconds</li>
                    </ul>
                </div>
                
                <div class="agent-message">
                    <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Accuracy Insights:</strong></p>
                    <p style="font-size: 0.875rem; color: var(--ui-text-secondary);">Your file has clear merchant names and consistent formatting. This is helping achieve high accuracy. Would you like me to explain any specific categorizations?</p>
                </div>
            </div>
            
            <div class="agent-input">
                <textarea class="agent-input-field" placeholder="Ask me about the processing status, accuracy, or any issues..."></textarea>
                <div class="agent-quick-actions">
                    <button class="quick-action">Processing speed</button>
                    <button class="quick-action">Accuracy details</button>
                    <button class="quick-action">Stop processing</button>
                </div>
            </div>
        </aside>
        
        <!-- Agent Activation Badge -->
        <div class="agent-activation-badge" title="Open AI Assistant">
            <img src="/images/giki-logo.svg" alt="AI Assistant" />
        </div>
        
    </div> <!-- End app-layout -->

    <style>
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
    <script>
        // Simulate real API polling like in the UploadPage component
        let currentStep = 2; // AI Processing step
        let progress = 0;
        let uploadId = 'upload_' + Date.now(); // Simulate upload ID
        
        // Enhanced progress update with real-time UI updates
        function updateProgress() {
            progress += Math.random() * 10;
            if (progress > 100) progress = 100;
            
            // Update progress bar
            const progressBar = document.getElementById('progressBar');
            if (progressBar) {
                progressBar.style.width = progress + '%';
            }
            
            // Update processed count
            const processedCount = Math.floor((progress / 100) * 247);
            document.getElementById('processedCount').textContent = processedCount;
            
            // Simulate API call: pollUploadStatus(uploadId)
            console.log('API Call: GET /api/v1/files/upload/status/' + uploadId);
            
            // Add activity items periodically
            if (Math.random() > 0.7 && progress < 90) {
                addActivityItem();
            }
            
            if (progress >= 100) {
                // Simulate completion - redirect to results page
                document.querySelector('.processing-title').textContent = 'Processing Complete!';
                document.getElementById('currentActivity').textContent = '□ All 247 transactions categorized successfully';
                document.querySelector('.estimated-time').innerHTML = '<button style="background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%); color: white; border: none; padding: var(--space-3) var(--space-6); border-radius: 8px; font-weight: 600; cursor: pointer;" onclick="window.location.href=\'/results\'">View Results →</button>';
                
                // Update final step to completed
                const activeStep = document.querySelector('.step.active');
                activeStep.classList.remove('active');
                activeStep.classList.add('completed');
                activeStep.querySelector('.step-circle').textContent = '□';
                
                const pendingStep = document.querySelector('.step.pending');
                pendingStep.classList.remove('pending');
                pendingStep.classList.add('active');
                
                // Final activity item
                addActivityItem('Processing complete! 89% accuracy achieved', true);
                
                clearInterval(progressInterval);
            }
        }
        
        // Add activity items to the feed
        function addActivityItem(text, isComplete) {
            const vendors = [
                'AMAZON.COM AMZN.COM/BILL',
                'UBER TRIP SAN FRANCISCO',
                'MICROSOFT*OFFICE 365',
                'GOOGLE ADS LLC',
                'PAYPAL *VENDORNAME',
                'STRIPE PAYMENT'
            ];
            
            const categories = [
                'Office Supplies',
                'Travel & Entertainment',
                'Software Subscriptions',
                'Marketing & Advertising',
                'Professional Services'
            ];
            
            const activityList = document.getElementById('activityList');
            
            // Mark current as done
            const current = activityList.querySelector('.current');
            if (current) {
                current.classList.remove('current');
                current.querySelector('span:first-child').textContent = '□';
                current.querySelector('span:first-child').style.color = 'var(--status-success)';
                current.querySelector('span:first-child').style.animation = 'none';
            }
            
            // Add new activity
            const newItem = document.createElement('div');
            newItem.className = 'activity-item' + (isComplete ? '' : ' current');
            newItem.style.display = 'flex';
            newItem.style.alignItems = 'center';
            newItem.style.gap = 'var(--space-2)';
            
            if (!text) {
                const vendor = vendors[Math.floor(Math.random() * vendors.length)];
                const category = categories[Math.floor(Math.random() * categories.length)];
                text = `Categorized "${vendor}" as ${category}`;
            }
            
            newItem.innerHTML = `
                <span style="color: ${isComplete ? 'var(--status-success)' : 'var(--brand-primary)'}; ${isComplete ? '' : 'animation: pulse 1s infinite;'}">${isComplete ? '□' : '⚬'}</span>
                <span style="font-size: 0.875rem; color: ${isComplete ? 'var(--ui-text-primary)' : 'var(--ui-text-secondary)'};">${text}</span>
            `;
            
            activityList.appendChild(newItem);
            activityList.scrollTop = activityList.scrollHeight;
        }
        
        const progressInterval = setInterval(updateProgress, 800);
        
        // Simulate real-time activity updates
        const activities = [
            'Analyzing merchant patterns...',
            'Applying business categories...',
            'Calculating confidence scores...',
            'Validating categorizations...',
            'Optimizing accuracy...'
        ];
        
        let activityIndex = 0;
        setInterval(() => {
            if (progress < 100) {
                document.querySelector('.activity-text').textContent = activities[activityIndex % activities.length];
                activityIndex++;
            }
        }, 2000);
        
        // Update estimated time
        let timeRemaining = 45;
        setInterval(() => {
            if (timeRemaining > 0 && progress < 100) {
                timeRemaining--;
                document.getElementById('timeRemaining').textContent = timeRemaining;
            }
        }, 1000);
        
        // Update current activity text
        const activities = [
            'Analyzing merchant patterns...',
            'Applying business categories...',
            'Calculating confidence scores...',
            'Validating categorizations...',
            'Optimizing accuracy...'
        ];
        
        let activityIndex = 0;
        setInterval(() => {
            if (progress < 100) {
                document.getElementById('currentActivity').textContent = activities[activityIndex % activities.length];
                activityIndex++;
            }
        }, 3000);
    </script>
</body>
</html>