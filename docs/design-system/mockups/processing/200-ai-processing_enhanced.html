<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Processing - AI-Powered Transaction Categorization</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        /* Design System Variables */
        :root {
            /* Primary Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            --brand-primary-alpha: rgba(41, 83, 67, 0.1);
            
            /* Professional UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Status & Feedback Colors */
            --status-success: #059669;
            --status-warning: #D97706;
            --status-error: #DC2626;
            --status-info: #2563EB;
            --status-neutral: #6B7280;
            
            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            line-height: 1.5;
            margin: 0;
            padding: 0;
        }

        /* Three-Panel Layout */
        .app-layout {
            display: grid;
            grid-template-columns: 64px 1fr 0px;
            grid-template-rows: 1fr;
            height: 100vh;
            overflow: hidden;
        }

        /* Left Navigation Panel */
        .nav-panel {
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1D372E 100%);
            border-right: 1px solid var(--ui-border);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: var(--space-4);
        }

        .nav-logo {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--brand-primary);
            font-weight: 700;
            font-size: 14px;
            margin-bottom: var(--space-6);
        }

        .nav-items {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .nav-item {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 20px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* Main Content Area */
        .main-content {
            background: var(--ui-background);
            padding: var(--space-8);
            overflow-y: auto;
            position: relative;
        }

        /* Processing Container */
        .processing-container {
            max-width: 800px;
            margin: 0 auto;
        }

        /* Header Section */
        .processing-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .processing-title {
            font-size: 2rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
        }

        .processing-subtitle {
            color: var(--ui-text-secondary);
            font-size: 1.125rem;
        }

        /* Status Card */
        .status-card {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 16px;
            padding: var(--space-8);
            margin-bottom: var(--space-8);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }

        /* Progress Section */
        .progress-section {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .progress-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto var(--space-4);
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            position: relative;
            overflow: hidden;
        }

        .progress-icon::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .progress-text {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
        }

        .progress-subtext {
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-6);
        }

        /* Progress Bar */
        .progress-bar-container {
            width: 100%;
            height: 8px;
            background: var(--ui-border);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--space-2);
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--brand-primary) 0%, var(--status-success) 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            animation: progress-pulse 2s infinite;
        }

        @keyframes progress-pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .progress-stats {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
            color: var(--ui-text-secondary);
        }

        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: var(--space-4);
            margin-bottom: var(--space-8);
        }

        .metric-card {
            background: var(--ui-surface);
            border: 1px solid var(--ui-border);
            border-radius: 12px;
            padding: var(--space-4);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--brand-primary) 0%, var(--status-success) 100%);
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: var(--space-1);
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-2);
        }

        .metric-change {
            font-size: 0.75rem;
            color: var(--status-success);
            font-weight: 500;
        }

        /* Live Activity */
        .live-activity {
            background: var(--ui-surface);
            border: 1px solid var(--ui-border);
            border-radius: 12px;
            padding: var(--space-6);
        }

        .activity-header {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            margin-bottom: var(--space-4);
        }

        .activity-icon {
            width: 8px;
            height: 8px;
            background: var(--status-success);
            border-radius: 50%;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }

        .activity-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--ui-text-primary);
        }

        .activity-log {
            max-height: 200px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-2) 0;
            border-bottom: 1px solid var(--ui-border);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-step {
            width: 6px;
            height: 6px;
            background: var(--status-success);
            border-radius: 50%;
            flex-shrink: 0;
        }

        .activity-step.processing {
            background: var(--status-warning);
            animation: pulse 1s infinite;
        }

        .activity-text {
            flex: 1;
            font-size: 0.875rem;
            color: var(--ui-text-secondary);
        }

        .activity-count {
            font-size: 0.75rem;
            color: var(--ui-text-muted);
            font-weight: 500;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .app-layout {
                grid-template-columns: 1fr;
            }
            
            .nav-panel {
                display: none;
            }
            
            .main-content {
                padding: var(--space-4);
            }
            
            .processing-container {
                max-width: 100%;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Left Navigation Panel -->
        <div class="nav-panel">
            <div class="nav-logo">G</div>
            <div class="nav-items">
                <div class="nav-item">□</div>
                <div class="nav-item active">↑</div>
                <div class="nav-item">⊞</div>
                <div class="nav-item">∷</div>
                <div class="nav-item">⚬</div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <div class="processing-container">
                <!-- Header -->
                <div class="processing-header">
                    <h1 class="processing-title">Processing Your Transactions</h1>
                    <p class="processing-subtitle">AI is categorizing your financial data with 87% accuracy</p>
                </div>

                <!-- Status Card -->
                <div class="status-card">
                    <!-- Progress Section -->
                    <div class="progress-section">
                        <div class="progress-icon">
                            ⚬
                        </div>
                        <div class="progress-text">Analyzing Transactions</div>
                        <div class="progress-subtext">Processing 1,247 transactions from March_Bank_Statement.csv</div>
                        
                        <div class="progress-bar-container">
                            <div class="progress-bar" style="width: 73%;"></div>
                        </div>
                        <div class="progress-stats">
                            <span>910 of 1,247 transactions</span>
                            <span>73% complete</span>
                        </div>
                    </div>

                    <!-- Metrics Grid -->
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value">910</div>
                            <div class="metric-label">Categorized</div>
                            <div class="metric-change">+45/min</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">87.3%</div>
                            <div class="metric-label">Accuracy</div>
                            <div class="metric-change">High confidence</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">2m 15s</div>
                            <div class="metric-label">Time Remaining</div>
                            <div class="metric-change">Estimated</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">34</div>
                            <div class="metric-label">Categories</div>
                            <div class="metric-change">Auto-created</div>
                        </div>
                    </div>

                    <!-- Live Activity -->
                    <div class="live-activity">
                        <div class="activity-header">
                            <div class="activity-icon"></div>
                            <div class="activity-title">Live Processing Activity</div>
                        </div>
                        <div class="activity-log">
                            <div class="activity-item">
                                <div class="activity-step processing"></div>
                                <div class="activity-text">Processing transaction: "AMAZON.COM*2M8NK2JK3 AMZN.COM/BILL"</div>
                                <div class="activity-count">Now</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-step"></div>
                                <div class="activity-text">Categorized "PAYPAL *SPOTIFY" as Entertainment → Music Streaming</div>
                                <div class="activity-count">5s ago</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-step"></div>
                                <div class="activity-text">Categorized "SHELL SERVICE STN" as Transportation → Fuel</div>
                                <div class="activity-count">8s ago</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-step"></div>
                                <div class="activity-text">Categorized "TRADER JOES #123" as Food & Dining → Groceries</div>
                                <div class="activity-count">12s ago</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-step"></div>
                                <div class="activity-text">Categorized "VERIZON WIRELESS" as Utilities → Phone</div>
                                <div class="activity-count">15s ago</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-step"></div>
                                <div class="activity-text">Categorized "STARBUCKS STORE" as Food & Dining → Coffee Shops</div>
                                <div class="activity-count">18s ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>