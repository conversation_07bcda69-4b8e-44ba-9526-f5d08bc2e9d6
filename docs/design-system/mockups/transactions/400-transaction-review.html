<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review & Improve - giki.ai</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        /* Design System Variables */
        :root {
            /* Primary Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            --brand-primary-alpha: rgba(41, 83, 67, 0.1);
            
            /* Professional UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Status & Feedback Colors */
            --status-success: #059669;
            --status-warning: #D97706;
            --status-error: #DC2626;
            --status-info: #2563EB;
            --status-neutral: #6B7280;
            
            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            /* Layout Variables */
            --nav-collapsed: 64px;
            --nav-expanded: 240px;
            --agent-panel: 400px;
            --content-max: 1200px;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            line-height: 1.5;
        }

        /* Three-Panel Layout with Agent Panel */
        .app-layout {
            display: grid;
            grid-template-columns: var(--nav-collapsed) 1fr var(--agent-panel);
            min-height: 100vh;
            position: relative;
        }

        /* Agent Activation Badge */
        .agent-activation-badge {
            position: fixed;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 48px;
            height: 48px;
            background: var(--brand-primary);
            border-radius: 12px 0 0 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .agent-activation-badge:hover {
            background: var(--brand-primary-hover);
            transform: translateY(-50%) translateX(-4px);
        }

        .agent-activation-badge img {
            width: 24px;
            height: 24px;
            filter: brightness(0) invert(1);
        }

        /* Agent activation pulse animation */
        .agent-activation-badge::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: #fff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pulse-dot 2s infinite;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.5; transform: translate(-50%, -50%) scale(1.2); }
        }

        /* Header */
        .header {
            background: var(--ui-background);
            border-bottom: 1px solid var(--ui-border);
            padding: var(--space-4) var(--space-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
            grid-column: 1 / -1;
            z-index: 10;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--brand-primary);
        }

        .logo-text .ai {
            color: var(--ui-text-secondary);
        }

        .header-subtitle {
            color: var(--ui-text-secondary);
            font-size: 0.875rem;
            margin-left: var(--space-3);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            color: var(--ui-text-primary);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: var(--brand-primary);
            color: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        /* Left Navigation Panel */
        .left-panel {
            background: var(--ui-background);
            border-right: 1px solid var(--ui-border);
            padding: 0;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .nav-header {
            padding: var(--space-5);
            border-bottom: 1px solid var(--ui-border);
            text-align: center;
        }

        .nav-section {
            margin-bottom: var(--space-8);
        }

        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--ui-text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--space-3);
            padding: 0 var(--space-4);
        }

        .nav-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--ui-text-secondary);
            text-decoration: none;
            transition: all 0.15s ease;
            font-size: 1.25rem;
            border-radius: 6px;
            margin: 0 var(--space-2);
        }

        .nav-item:hover {
            background: var(--brand-primary-alpha);
            color: var(--brand-primary);
        }

        .nav-item.active {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
            font-weight: 600;
        }

        .nav-icon {
            font-size: 1.5rem;
            font-weight: 700;
        }

        /* Main Content */
        .main-content {
            padding: var(--space-8) var(--space-6);
            overflow-y: auto;
        }

        .page-header {
            margin-bottom: var(--space-8);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: var(--space-2);
        }

        .page-subtitle {
            font-size: 1.125rem;
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-6);
        }

        .progress-indicator {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 12px;
            padding: var(--space-6);
            margin-bottom: var(--space-6);
        }

        .accuracy-bar {
            width: 100%;
            height: 8px;
            background: var(--ui-surface);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--space-4);
            position: relative;
        }

        .accuracy-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--status-warning), var(--status-success));
            width: 89%;
            transition: width 0.5s ease;
        }

        .accuracy-target {
            position: absolute;
            right: 0;
            top: 0;
            width: 5%;
            height: 100%;
            background: var(--brand-primary);
            border-radius: 0 4px 4px 0;
        }

        .accuracy-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
        }

        .accuracy-current {
            color: var(--status-warning);
            font-weight: 600;
        }

        .accuracy-goal {
            color: var(--brand-primary);
            font-weight: 600;
        }

        /* Improvement Options */
        .improvement-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }

        .improvement-card {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 12px;
            padding: var(--space-6);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .improvement-card:hover {
            border-color: var(--brand-primary);
            box-shadow: 0 4px 12px var(--brand-primary-alpha);
            transform: translateY(-2px);
        }

        .improvement-card.recommended {
            border-color: var(--brand-primary);
            background: var(--brand-primary-light);
        }

        .improvement-header {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-4);
        }

        .improvement-icon {
            width: 48px;
            height: 48px;
            background: var(--brand-primary);
            color: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            font-weight: 700;
        }

        .improvement-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--brand-primary);
        }

        .improvement-boost {
            background: var(--status-success);
            color: white;
            padding: var(--space-1) var(--space-2);
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: auto;
        }

        .improvement-description {
            color: var(--ui-text-secondary);
            margin-bottom: var(--space-4);
            line-height: 1.6;
        }

        .improvement-steps {
            margin-bottom: var(--space-4);
        }

        .improvement-step {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            margin-bottom: var(--space-2);
            font-size: 0.875rem;
            color: var(--ui-text-secondary);
        }

        .step-icon {
            color: var(--brand-primary);
            font-weight: 700;
        }

        .improvement-action {
            background: var(--brand-primary);
            color: white;
            border: none;
            padding: var(--space-3) var(--space-4);
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.2s ease;
        }

        .improvement-action:hover {
            background: var(--brand-primary-hover);
            transform: translateY(-1px);
        }

        .improvement-action.secondary {
            background: white;
            color: var(--brand-primary);
            border: 1px solid var(--brand-primary);
        }

        .improvement-action.secondary:hover {
            background: var(--brand-primary);
            color: white;
        }

        /* Review Section */
        .review-section {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 12px;
            padding: var(--space-6);
            margin-bottom: var(--space-6);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--brand-primary);
            margin-bottom: var(--space-4);
        }

        .flagged-transactions {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .transaction-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--space-4);
            background: var(--ui-surface);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .transaction-item:hover {
            border-color: var(--brand-primary);
            background: var(--brand-primary-light);
        }

        .transaction-info {
            flex: 1;
        }

        .transaction-description {
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-1);
        }

        .transaction-details {
            font-size: 0.875rem;
            color: var(--ui-text-secondary);
        }

        .transaction-confidence {
            padding: var(--space-1) var(--space-2);
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: var(--space-3);
        }

        .confidence-low {
            background: var(--status-warning);
            color: white;
        }

        .confidence-medium {
            background: var(--status-info);
            color: white;
        }

        .transaction-actions {
            display: flex;
            gap: var(--space-2);
            margin-left: var(--space-3);
        }

        .action-btn {
            padding: var(--space-1) var(--space-3);
            border: 1px solid var(--ui-border);
            border-radius: 4px;
            background: white;
            color: var(--ui-text-primary);
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            border-color: var(--brand-primary);
            color: var(--brand-primary);
        }

        /* Review Section Enhancements */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            flex-wrap: wrap;
            gap: var(--space-4);
        }

        .review-filters {
            display: flex;
            gap: var(--space-2);
        }

        .filter-btn {
            background: var(--ui-surface);
            border: 1px solid var(--ui-border);
            color: var(--ui-text-secondary);
            padding: var(--space-2) var(--space-4);
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn.active {
            background: var(--brand-primary);
            color: white;
            border-color: var(--brand-primary);
        }

        .filter-btn:hover {
            background: var(--brand-primary-alpha);
            border-color: var(--brand-primary);
        }

        /* Transaction Groups */
        .transaction-groups {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
        }

        .transaction-group {
            background: var(--ui-background);
            border: 2px solid var(--ui-border);
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .transaction-group:hover {
            border-color: var(--brand-primary);
            box-shadow: 0 4px 16px var(--brand-primary-alpha);
        }

        .transaction-group.urgent {
            border-color: var(--status-error);
        }

        .group-header {
            padding: var(--space-5);
            border-bottom: 1px solid var(--ui-border);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--space-4);
        }

        .group-info {
            flex: 1;
        }

        .group-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            margin-bottom: var(--space-2);
        }

        .group-hierarchy {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            flex-wrap: wrap;
        }

        .hierarchy-level {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
            padding: 2px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .hierarchy-level.final {
            background: var(--brand-primary);
            color: white;
            font-weight: 600;
        }

        .hierarchy-level.uncertain {
            background: #FEF3C7;
            color: var(--status-warning);
        }

        .hierarchy-level.error {
            background: #FEF2F2;
            color: var(--status-error);
        }

        .hierarchy-separator {
            color: var(--ui-text-muted);
            font-weight: 600;
        }

        .gl-code {
            color: var(--ui-text-muted);
            font-size: 11px;
            font-weight: 500;
        }

        .action-required {
            background: var(--status-error);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
        }

        .urgent-flag {
            background: var(--status-error);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }

        .group-actions {
            display: flex;
            gap: var(--space-2);
            align-items: flex-start;
        }

        .bulk-action-btn {
            background: var(--brand-primary);
            color: white;
            border: none;
            padding: var(--space-3) var(--space-4);
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .bulk-action-btn:hover {
            background: var(--brand-primary-hover);
            transform: translateY(-1px);
        }

        .bulk-action-btn.secondary {
            background: var(--ui-surface);
            color: var(--brand-primary);
            border: 1px solid var(--brand-primary);
        }

        .bulk-action-btn.secondary:hover {
            background: var(--brand-primary);
            color: white;
        }

        .bulk-action-btn.urgent {
            background: var(--status-error);
            animation: pulse 2s infinite;
        }

        .group-summary {
            padding: var(--space-4) var(--space-5);
            background: var(--ui-surface);
            border-bottom: 1px solid var(--ui-border);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
        }

        .group-summary.mixed {
            background: #FEF3C7;
        }

        .group-summary.urgent {
            background: #FEF2F2;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .summary-label {
            color: var(--ui-text-secondary);
            font-size: 14px;
            font-weight: 500;
        }

        .summary-value {
            color: var(--ui-text-primary);
            font-weight: 600;
            font-size: 14px;
        }

        .summary-value.high-amount {
            color: var(--status-error);
            font-size: 16px;
        }

        .summary-value.requires-review {
            color: var(--status-warning);
        }

        .group-transactions {
            padding: var(--space-4) var(--space-5);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .group-transactions.expanded {
            max-height: 400px;
        }

        .mini-transaction {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-2) 0;
            border-bottom: 1px solid var(--ui-border);
            font-size: 14px;
        }

        .mini-transaction:last-child {
            border-bottom: none;
        }

        .vendor {
            flex: 1;
            font-weight: 500;
        }

        .amount {
            font-weight: 600;
            color: var(--ui-text-primary);
            margin: 0 var(--space-4);
        }

        .confidence {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            min-width: 40px;
            text-align: center;
        }

        /* Agent Panel */
        .agent-panel {
            background: var(--ui-background);
            border-left: 1px solid var(--ui-border);
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
        }

        .agent-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--ui-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .agent-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .agent-content {
            flex: 1;
            padding: var(--space-6);
            overflow-y: auto;
        }

        .agent-message {
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            background: var(--ui-surface);
            border-radius: 12px;
            border-left: 3px solid var(--brand-primary);
        }

        .agent-input {
            padding: var(--space-4);
            border-top: 1px solid var(--ui-border);
            background: var(--ui-surface);
        }

        .agent-input-field {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            font-size: 0.875rem;
            resize: none;
            height: 60px;
        }

        .agent-input-field:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 2px rgba(41, 83, 67, 0.1);
        }

        .agent-quick-actions {
            display: flex;
            gap: var(--space-2);
            margin-top: var(--space-3);
        }

        .quick-action {
            padding: var(--space-2) var(--space-3);
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 6px;
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .quick-action:hover {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <img src="/images/giki-logo.svg" alt="giki.ai" style="height: 40px;">
                <div class="header-subtitle">Review & Improve</div>
            </div>
            <div class="user-info">
                <div>
                    <div style="font-weight: 600;">Business Owner</div>
                    <div style="font-size: 12px; color: var(--ui-text-muted);">Improving Accuracy</div>
                </div>
                <div class="user-avatar">BO</div>
            </div>
        </header>

        <!-- Left Navigation Panel -->
        <aside class="left-panel">
            <div class="nav-header">
                <img src="/images/giki-logo.svg" alt="giki.ai" style="height: 32px; opacity: 0.8;">
            </div>
            
            <div style="padding: var(--space-4);">
                <div class="nav-section">
                    <div class="nav-section-title">WORK</div>
                    <a href="/dashboard" class="nav-item" title="Dashboard">
                        <span class="nav-icon">□</span>
                    </a>
                    <a href="/upload" class="nav-item" title="Upload">
                        <span class="nav-icon">↑</span>
                    </a>
                    <a href="/transactions" class="nav-item active" title="Transactions">
                        <span class="nav-icon">⊞</span>
                    </a>
                    <a href="/reports" class="nav-item" title="Reports">
                        <span class="nav-icon">∷</span>
                    </a>
                    <a href="/categories" class="nav-item" title="Categories">
                        <span class="nav-icon">⚬</span>
                    </a>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Review & Improve Accuracy</h1>
                <p class="page-subtitle">
                    Boost your categorization accuracy from 89% to 95%+ by reviewing flagged transactions 
                    and adding business context.
                </p>
                
                <!-- Progress Indicator -->
                <div class="progress-indicator">
                    <div class="accuracy-bar">
                        <div class="accuracy-fill"></div>
                        <div class="accuracy-target"></div>
                    </div>
                    <div class="accuracy-labels">
                        <div class="accuracy-current">Current: 89% Accuracy</div>
                        <div class="accuracy-goal">Goal: 95% Accuracy</div>
                    </div>
                </div>
            </div>

            <!-- Simplified Review Queue -->
            <div class="review-queue" style="background: var(--ui-background); border: 1px solid var(--ui-border); border-radius: 16px; padding: var(--space-6); margin-bottom: var(--space-8);">
                <div class="queue-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-6);">
                    <h2 style="font-size: 1.5rem; font-weight: 600; color: var(--brand-primary);">23 Transactions Need Review</h2>
                    <div class="queue-filters" style="display: flex; gap: var(--space-2);">
                        <button class="filter-btn active">All</button>
                        <button class="filter-btn">Low Confidence</button>
                        <button class="filter-btn">Uncategorized</button>
                    </div>
                </div>
                
                <!-- Single Transaction Card -->
                <div class="review-card" style="background: var(--ui-surface); border: 1px solid var(--ui-border); border-radius: 12px; padding: var(--space-6); margin-bottom: var(--space-4);">
                    <div class="transaction-info" style="display: flex; justify-content: space-between; margin-bottom: var(--space-4);">
                        <div class="merchant" style="font-size: 1.125rem; font-weight: 600; color: var(--ui-text-primary);">AMAZON.COM AMZN.COM/BILL</div>
                        <div class="amount" style="font-size: 1.25rem; font-weight: 600; color: var(--ui-text-primary);">$127.49</div>
                    </div>
                    
                    <div class="category-suggestion" style="margin-bottom: var(--space-4);">
                        <label style="display: block; font-weight: 600; color: var(--ui-text-secondary); margin-bottom: var(--space-2);">AI Suggestion:</label>
                        <select class="category-select" style="width: 100%; padding: var(--space-3); border: 1px solid var(--ui-border); border-radius: 8px; font-size: 1rem; background: white;">
                            <option selected>Office Supplies (76% confidence)</option>
                            <option>Equipment - Computer Hardware</option>
                            <option>Software - Cloud Services</option>
                            <option>Marketing - Online Advertising</option>
                            <option>Other...</option>
                        </select>
                    </div>
                    
                    <div class="review-actions" style="display: flex; gap: var(--space-3);">
                        <button class="btn-approve" style="flex: 1; background: var(--status-success); color: white; border: none; padding: var(--space-3); border-radius: 8px; font-weight: 600; cursor: pointer;">□ Approve</button>
                        <button class="btn-skip" style="flex: 1; background: white; color: var(--ui-text-secondary); border: 1px solid var(--ui-border); padding: var(--space-3); border-radius: 8px; font-weight: 600; cursor: pointer;">Skip</button>
                    </div>
                </div>
                
                <!-- Bulk Actions -->
                <div class="bulk-actions" style="display: flex; gap: var(--space-3); padding-top: var(--space-4); border-top: 1px solid var(--ui-border);">
                    <button class="btn-primary" style="background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%); color: white; border: none; padding: var(--space-3) var(--space-6); border-radius: 8px; font-weight: 600; cursor: pointer;">Approve All Similar (5)</button>
                    <button class="btn-secondary" style="background: white; color: var(--brand-primary); border: 2px solid var(--brand-primary); padding: var(--space-3) var(--space-6); border-radius: 8px; font-weight: 600; cursor: pointer;">Create Rule</button>
                </div>
            </div>

            <!-- Navigation -->
            <div class="navigation" style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space-8);">
                <button class="nav-button" style="background: white; color: var(--brand-primary); border: 2px solid var(--brand-primary); padding: var(--space-3) var(--space-6); border-radius: 12px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: var(--space-2);" onclick="window.location.href='/results'">
                    <span>←</span>
                    <span>Back to Results</span>
                </button>
                <button class="nav-button primary" style="background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%); color: white; border: none; padding: var(--space-3) var(--space-6); border-radius: 12px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: var(--space-2);" onclick="window.location.href='/export'">
                    <span>Continue to Export</span>
                    <span>→</span>
                </button>
            </div>
                                <span class="summary-label">AI Status:</span>
                                <span class="summary-value">Could not categorize</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Reason:</span>
                                <span class="summary-value">Unknown vendors, unusual patterns</span>
                            </div>
                        </div>
                    </div>

                    <!-- Group 4: Travel & Transportation -->
                    <div class="transaction-group">
                        <div class="group-header">
                            <div class="group-info">
                                <div class="group-title">Travel & Transportation (6 transactions)</div>
                                <div class="group-hierarchy">
                                    <span class="hierarchy-level">Expenses</span>
                                    <span class="hierarchy-separator">></span>
                                    <span class="hierarchy-level">Sales & Marketing</span>
                                    <span class="hierarchy-separator">></span>
                                    <span class="hierarchy-level final">Travel & Transportation</span>
                                    <span class="gl-code">[GL: 5210]</span>
                                </div>
                            </div>
                            <div class="group-actions">
                                <button class="bulk-action-btn" onclick="approveGroup(this)">□ Approve All (6)</button>
                                <button class="bulk-action-btn secondary" onclick="createRule(this)">∷ Create Rule</button>
                            </div>
                        </div>
                        <div class="group-summary">
                            <div class="summary-item">
                                <span class="summary-label">Total Amount:</span>
                                <span class="summary-value">$456.78</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Avg Confidence:</span>
                                <span class="summary-value confidence-high">91%</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Pattern:</span>
                                <span class="summary-value">Uber, Lyft, Parking, Flights</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
    <!-- Agent Panel -->
    <aside class="agent-panel">
        <div class="agent-header">
            <div class="agent-title">
                <div class="agent-avatar">AI</div>
                <span>giki Assistant</span>
            </div>
            <button style="background: none; border: none; color: var(--ui-text-muted); cursor: pointer; font-size: 1.25rem;">×</button>
        </div>
        
        <div class="agent-content">
            <div class="agent-message">
                <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Review Assistant Active</strong></p>
                <p style="font-size: 0.875rem; color: var(--ui-text-secondary);">I'm here to help you efficiently review and improve your categorization accuracy. I can prioritize which transactions to review first and suggest bulk actions.</p>
            </div>
            
            <div class="agent-message">
                <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Smart Suggestions:</strong></p>
                <ul style="font-size: 0.875rem; color: var(--ui-text-secondary); margin-left: var(--space-4);">
                    <li>Start with Amazon transactions - highest impact</li>
                    <li>Office supplies group has 76% confidence</li>
                    <li>Travel expenses show consistent patterns</li>
                    <li>Consider creating rules for recurring vendors</li>
                </ul>
            </div>
            
            <div class="agent-message">
                <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Progress Tracking:</strong></p>
                <p style="font-size: 0.875rem; color: var(--ui-text-secondary);">23 transactions remaining. Reviewing these could boost your accuracy from 89% to 95%+. Each approval adds ~0.26% accuracy.</p>
            </div>
        </div>
        
        <div class="agent-input">
            <textarea class="agent-input-field" placeholder="Ask me which transactions to prioritize, or request bulk actions..."></textarea>
            <div class="agent-quick-actions">
                <button class="quick-action">Highest impact</button>
                <button class="quick-action">Bulk approve</button>
                <button class="quick-action">Create rules</button>
            </div>
        </div>
    </aside>
        
    <!-- Agent Activation Badge -->
    <div class="agent-activation-badge" title="Open AI Assistant">
        <img src="/images/giki-logo.svg" alt="AI Assistant" />
    </div>
    
    </div> <!-- End app-layout -->

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Improvement card interactions
            const improvementCards = document.querySelectorAll('.improvement-card');
            improvementCards.forEach(card => {
                card.addEventListener('click', function() {
                    const button = this.querySelector('.improvement-action');
                    if (button) {
                        button.click();
                    }
                });
            });

            // Transaction item interactions
            const transactionItems = document.querySelectorAll('.transaction-item');
            transactionItems.forEach(item => {
                const actionBtns = item.querySelectorAll('.action-btn');
                actionBtns.forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const action = this.textContent.trim();
                        
                        if (action.includes('Correct')) {
                            item.style.background = 'var(--status-success)';
                            item.style.color = 'white';
                            item.style.opacity = '0.8';
                            setTimeout(() => {
                                item.style.display = 'none';
                            }, 500);
                        } else if (action.includes('Different')) {
                            // Show category selection (would open modal in real app)
                            alert('Category selection modal would open here');
                        } else if (action.includes('Skip')) {
                            item.style.opacity = '0.5';
                        }
                    });
                });
            });

            // Agent action buttons
            const agentActionBtns = document.querySelectorAll('.agent-action-btn');
            agentActionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    console.log('Agent action:', action);
                    
                    // Simulate agent responses
                    if (action.includes('highest impact')) {
                        // Highlight the first transaction
                        const firstTransaction = document.querySelector('.transaction-item');
                        if (firstTransaction) {
                            firstTransaction.style.border = '2px solid var(--brand-primary)';
                            firstTransaction.scrollIntoView({ behavior: 'smooth' });
                        }
                    }
                });
            });
        });

        // Enhanced Group Operations for MIS Hierarchical Review
        function approveGroup(button) {
            const group = button.closest('.transaction-group');
            const groupTitle = group.querySelector('.group-title').textContent;
            const transactionCount = groupTitle.match(/\((\d+) transactions\)/)[1];
            
            // Simulate bulk approval API call
            console.log('API Call: POST /api/v1/transactions/batch/approve');
            
            // Visual feedback
            group.style.background = 'var(--status-success-light, #F0FDF4)';
            group.style.borderColor = 'var(--status-success)';
            button.textContent = `□ Approved (${transactionCount})`;
            button.disabled = true;
            
            // Visual confirmation of approval
        }

        function expandGroup(button) {
            const group = button.closest('.transaction-group');
            const transactions = group.querySelector('.group-transactions');
            
            if (transactions.classList.contains('expanded')) {
                transactions.classList.remove('expanded');
                button.textContent = button.textContent.replace('👁 Collapse', '👁 Review Each');
            } else {
                transactions.classList.add('expanded');
                button.textContent = button.textContent.replace('👁 Review Each', '👁 Collapse');
                button.textContent = button.textContent.replace('📋 Review Each', '📋 Collapse');
                button.textContent = button.textContent.replace('⚡ Review Now', '⚡ Collapse');
            }
        }

        function bulkCategorize(button) {
            const group = button.closest('.transaction-group');
            const groupTitle = group.querySelector('.group-title').textContent;
            
            // Simulate showing hierarchical category selector
            const categories = [
                'Expenses > Sales & Marketing > Digital Marketing',
                'Expenses > Technology > Software Subscriptions', 
                'Expenses > Operations > Office Supplies',
                'Expenses > Operations > Equipment',
                'Income > Software Revenue > Subscription Revenue'
            ];
            
            const selectedCategory = prompt(`Select category hierarchy for ${groupTitle}:\n\n` + 
                categories.map((cat, i) => `${i+1}. ${cat}`).join('\n') + 
                '\n\nEnter number (1-5):');
            
            if (selectedCategory && selectedCategory >= 1 && selectedCategory <= 5) {
                const category = categories[selectedCategory - 1];
                
                // Update hierarchy display
                const hierarchy = group.querySelector('.group-hierarchy');
                const parts = category.split(' > ');
                hierarchy.innerHTML = parts.map((part, i) => 
                    `<span class="hierarchy-level ${i === parts.length - 1 ? 'final' : ''}">${part}</span>`
                ).join('<span class="hierarchy-separator">></span>') + 
                '<span class="gl-code">[GL: 5XXX]</span>';
                
                // Simulate API call
                console.log('API Call: PUT /api/v1/transactions/batch/category');
                // Visual confirmation of categorization
            }
        }

        function createRule(button) {
            const group = button.closest('.transaction-group');
            const groupTitle = group.querySelector('.group-title').textContent;
            const vendors = group.querySelector('.summary-value').textContent;
            
            // Simulate rule creation
            console.log('API Call: POST /api/v1/transactions/rules');
            
            // Visual confirmation of rule creation
            
            // Visual feedback
            button.textContent = '□ Rule Created';
            button.disabled = true;
        }

        // No agent messaging on review page

        // Filter functionality
        function handleFilterClick(button) {
            // Remove active class from all filters
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            const filterType = button.textContent.toLowerCase();
            const groups = document.querySelectorAll('.transaction-group');
            
            groups.forEach(group => {
                if (filterType.includes('all')) {
                    group.style.display = 'block';
                } else if (filterType.includes('uncategorized') && group.classList.contains('urgent')) {
                    group.style.display = 'block';
                } else if (filterType.includes('low confidence') && !group.classList.contains('urgent')) {
                    group.style.display = 'block';
                } else if (filterType.includes('high amount') && group.querySelector('.high-amount')) {
                    group.style.display = 'block';
                } else if (!filterType.includes('all')) {
                    group.style.display = 'none';
                }
            });
        }

        // Add filter event listeners
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', () => handleFilterClick(btn));
            });
        });

        // Simulate intelligent grouping suggestions based on MIS system
        function generateGroupSuggestions() {
            const suggestions = [
                "I've grouped your transactions by vendor similarity. The software subscriptions group has high confidence - you can approve all 4 at once.",
                "Your Amazon purchases need individual review since they span multiple MIS categories. I recommend using the bulk categorize feature.",
                "I found 3 high-value uncategorized transactions. These need immediate attention as they significantly impact your financial reports.",
                "The travel expenses show consistent patterns. Would you like me to create an automatic rule for Uber and Lyft transactions?"
            ];
            
            setTimeout(() => {
                // No agent suggestions on review page
            }, 3000);
        }

        // Start smart suggestions after page load
        generateGroupSuggestions();
    </script>
    
    <!-- Simplified Review Page Scripts -->
    <script>
        // Filter functionality for the simplified review queue
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                console.log('Filtering by:', this.textContent);
            });
        });
        
        // Handle approve/skip buttons
        document.addEventListener('click', function(e) {
            if (e.target.matches('.btn-approve')) {
                const card = e.target.closest('.review-card');
                card.style.background = '#F0FDF4';
                card.style.borderColor = 'var(--status-success)';
                e.target.textContent = '□ Approved';
                e.target.disabled = true;
                
                // Update progress
                const progressBar = document.querySelector('.accuracy-fill');
                if (progressBar) {
                    const current = parseInt(progressBar.style.width) || 89;
                    progressBar.style.width = Math.min(current + 0.5, 95) + '%';
                }
            }
            
            if (e.target.matches('.btn-skip')) {
                const card = e.target.closest('.review-card');
                card.style.opacity = '0.5';
                setTimeout(() => {
                    card.style.opacity = '1';
                }, 300);
            }
        });
    </script>
</body>
</html>