<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categorization Results - giki.ai</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        /* Design System Variables */
        :root {
            /* Primary Brand Colors */
            --brand-primary: #295343;
            --brand-primary-hover: #1D372E;
            --brand-primary-light: #E8F5E8;
            --brand-primary-alpha: rgba(41, 83, 67, 0.1);
            
            /* Professional UI Colors */
            --ui-background: #FFFFFF;
            --ui-surface: #F8F9FA;
            --ui-border: #E1E5E9;
            --ui-text-primary: #1A1D29;
            --ui-text-secondary: #6B7280;
            --ui-text-muted: #9CA3AF;
            
            /* Status & Feedback Colors */
            --status-success: #059669;
            --status-warning: #D97706;
            --status-error: #DC2626;
            --status-info: #2563EB;
            --status-neutral: #6B7280;
            
            /* Spacing System */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            /* Layout Variables */
            --nav-collapsed: 64px;
            --nav-expanded: 240px;
            --agent-panel: 400px;
            --content-max: 1200px;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--ui-surface);
            color: var(--ui-text-primary);
            line-height: 1.5;
        }

        /* Three-Panel Layout with Agent Panel */
        .app-layout {
            display: grid;
            grid-template-columns: var(--nav-collapsed) 1fr var(--agent-panel);
            min-height: 100vh;
            position: relative;
        }

        /* Agent Activation Badge */
        .agent-activation-badge {
            position: fixed;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 48px;
            height: 48px;
            background: var(--brand-primary);
            border-radius: 12px 0 0 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .agent-activation-badge:hover {
            background: var(--brand-primary-hover);
            transform: translateY(-50%) translateX(-4px);
        }

        .agent-activation-badge img {
            width: 24px;
            height: 24px;
            filter: brightness(0) invert(1);
        }

        /* Agent activation pulse animation */
        .agent-activation-badge::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: #fff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pulse-dot 2s infinite;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.5; transform: translate(-50%, -50%) scale(1.2); }
        }

        /* Header */
        .header {
            background: var(--ui-background);
            border-bottom: 1px solid var(--ui-border);
            padding: var(--space-4) var(--space-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
            grid-column: 1 / -1;
            z-index: 10;
        }

        /* Left Navigation Panel */
        .left-panel {
            background: var(--ui-background);
            border-right: 1px solid var(--ui-border);
            padding: 0;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .nav-header {
            padding: var(--space-5);
            border-bottom: 1px solid var(--ui-border);
            text-align: center;
        }

        .nav-section {
            margin-bottom: var(--space-8);
        }

        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--ui-text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--space-3);
            padding: 0 var(--space-4);
        }

        .nav-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--ui-text-secondary);
            text-decoration: none;
            transition: all 0.15s ease;
            font-size: 1.25rem;
            border-radius: 6px;
            margin: 0 var(--space-2);
        }

        .nav-item:hover {
            background: var(--brand-primary-alpha);
            color: var(--brand-primary);
        }

        .nav-item.active {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
            font-weight: 600;
        }

        .nav-icon {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #295343;
        }

        .logo-text .ai {
            color: #6B7280;
        }

        .header-subtitle {
            color: #6B7280;
            font-size: 14px;
            margin-left: 12px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
            color: #374151;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: #295343;
            color: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        /* Agent Panel */
        .agent-panel {
            background: var(--ui-background);
            border-left: 1px solid var(--ui-border);
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .agent-header {
            padding: var(--space-4);
            border-bottom: 1px solid var(--ui-border);
            background: var(--brand-primary);
            color: white;
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            background: white;
            color: var(--brand-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
        }

        .agent-title {
            font-weight: 600;
            font-size: 1rem;
        }

        .agent-subtitle {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .agent-content {
            flex: 1;
            padding: var(--space-4);
            overflow-y: auto;
        }

        .agent-message {
            background: var(--brand-primary-light);
            border: 1px solid var(--brand-primary);
            border-left: 4px solid var(--brand-primary);
            border-radius: 8px;
            padding: var(--space-4);
            margin-bottom: var(--space-4);
        }

        .agent-message-content {
            color: var(--ui-text-primary);
            margin-bottom: var(--space-3);
        }

        .agent-actions {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .agent-action-btn {
            background: var(--brand-primary);
            color: white;
            border: none;
            padding: var(--space-2) var(--space-4);
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: left;
        }

        .agent-action-btn:hover {
            background: var(--brand-primary-hover);
            transform: translateY(-1px);
        }

        .agent-action-btn.secondary {
            background: white;
            color: var(--brand-primary);
            border: 1px solid var(--brand-primary);
        }

        .agent-action-btn.secondary:hover {
            background: var(--brand-primary);
            color: white;
        }

        .agent-input-area {
            padding: var(--space-4);
            border-top: 1px solid var(--ui-border);
            background: var(--ui-surface);
        }

        .agent-input {
            width: 100%;
            padding: var(--space-3);
            border: 1px solid var(--ui-border);
            border-radius: 6px;
            font-family: inherit;
            font-size: 0.875rem;
            resize: none;
            min-height: 60px;
        }

        .agent-input:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 2px var(--brand-primary-alpha);
        }

        .agent-send-btn {
            background: var(--brand-primary);
            color: white;
            border: none;
            padding: var(--space-2) var(--space-4);
            border-radius: 6px;
            margin-top: var(--space-2);
            cursor: pointer;
            font-weight: 500;
            width: 100%;
        }

        /* Main Content */
        .main-content {
            padding: var(--space-8) var(--space-6);
            overflow-y: auto;
        }

        /* Success Header */
        .success-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--status-success), #10B981);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            font-weight: 700;
            margin: 0 auto var(--space-6);
            box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3), 0 0 0 8px rgba(5, 150, 105, 0.1);
        }

        .success-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: var(--space-4);
        }

        .success-subtitle {
            font-size: 1.25rem;
            color: var(--ui-text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Results Summary */
        .results-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
            margin-bottom: 48px;
        }

        .result-card {
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 12px;
            padding: var(--space-6);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .result-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--brand-primary), var(--brand-primary-hover));
        }

        .result-card:hover {
            border-color: var(--brand-primary);
            box-shadow: 0 8px 25px var(--brand-primary-alpha);
            transform: translateY(-2px);
        }

        .result-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--brand-primary);
            margin-bottom: var(--space-2);
            font-family: 'JetBrains Mono', monospace;
        }

        .result-label {
            color: var(--ui-text-secondary);
            font-weight: 600;
        }

        .result-detail {
            font-size: 0.75rem;
            color: var(--ui-text-muted);
            margin-top: var(--space-1);
        }

        /* Categories Preview */
        .categories-section {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #295343;
            margin-bottom: 24px;
            text-align: center;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .category-item {
            background: #F8F9FA;
            border: 1px solid #E1E5E9;
            border-radius: 12px;
            padding: 16px;
            transition: all 0.2s ease;
        }

        .category-item:hover {
            border-color: #295343;
            background: rgba(41, 83, 67, 0.02);
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .category-icon {
            width: 32px;
            height: 32px;
            background: #295343;
            color: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 700;
        }

        .category-name {
            font-weight: 600;
            color: #1F2937;
        }

        .category-count {
            color: #6B7280;
            font-size: 14px;
        }

        .category-examples {
            font-size: 12px;
            color: #9CA3AF;
            margin-top: 8px;
        }

        /* Action Buttons */
        .action-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .action-card {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .action-card:hover {
            border-color: #295343;
            box-shadow: 0 8px 25px rgba(41, 83, 67, 0.12);
            transform: translateY(-2px);
        }

        .action-icon {
            width: 64px;
            height: 64px;
            background: #E8F5E8;
            color: #295343;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
            margin: 0 auto 16px;
        }

        .action-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #295343;
            margin-bottom: 8px;
        }

        .action-description {
            color: #6B7280;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .action-button {
            background: #295343;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }

        .action-button:hover {
            background: #1D372E;
            transform: translateY(-1px);
        }

        .action-button.secondary {
            background: white;
            color: #295343;
            border: 2px solid #295343;
        }

        .action-button.secondary:hover {
            background: #295343;
            color: white;
        }

        /* Next Steps */
        .next-steps {
            background: linear-gradient(135deg, rgba(41, 83, 67, 0.05), rgba(41, 83, 67, 0.1));
            border: 1px solid rgba(41, 83, 67, 0.2);
            border-radius: 16px;
            padding: 32px;
            text-align: center;
        }

        .next-steps-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #295343;
            margin-bottom: 16px;
        }

        .next-steps-description {
            color: #6B7280;
            margin-bottom: 24px;
        }

        .next-steps-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .primary-button {
            background: #295343;
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .primary-button:hover {
            background: #1D372E;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(41, 83, 67, 0.3);
        }

        .secondary-button {
            background: white;
            color: #295343;
            border: 2px solid #295343;
            padding: 14px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .secondary-button:hover {
            background: #295343;
            color: white;
        }

        /* Trust Indicators */
        .trust-indicators {
            display: flex;
            justify-content: center;
            gap: 48px;
            margin-top: 48px;
            padding-top: 32px;
            border-top: 1px solid #E5E7EB;
            flex-wrap: wrap;
        }

        .trust-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6B7280;
            font-size: 14px;
        }

        .trust-icon {
            font-size: 16px;
            color: #295343;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .results-summary {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }
            
            .categories-grid {
                grid-template-columns: 1fr;
            }
            
            .action-section {
                grid-template-columns: 1fr;
            }
            
            .next-steps-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .trust-indicators {
                flex-direction: column;
                gap: 16px;
                align-items: center;
            }
        }

        /* Agent Panel */
        .agent-panel {
            background: var(--ui-background);
            border-left: 1px solid var(--ui-border);
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
        }

        .agent-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--ui-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .agent-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--ui-text-primary);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .agent-content {
            flex: 1;
            padding: var(--space-6);
            overflow-y: auto;
        }

        .agent-message {
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            background: var(--ui-surface);
            border-radius: 12px;
            border-left: 3px solid var(--brand-primary);
        }

        .agent-input {
            padding: var(--space-4);
            border-top: 1px solid var(--ui-border);
            background: var(--ui-surface);
        }

        .agent-input-field {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--ui-border);
            border-radius: 8px;
            font-size: 0.875rem;
            resize: none;
            height: 60px;
        }

        .agent-input-field:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 2px rgba(41, 83, 67, 0.1);
        }

        .agent-quick-actions {
            display: flex;
            gap: var(--space-2);
            margin-top: var(--space-3);
        }

        .quick-action {
            padding: var(--space-2) var(--space-3);
            background: var(--ui-background);
            border: 1px solid var(--ui-border);
            border-radius: 6px;
            font-size: 0.75rem;
            color: var(--ui-text-secondary);
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .quick-action:hover {
            background: var(--brand-primary-light);
            color: var(--brand-primary);
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <img src="/images/giki-logo.svg" alt="giki.ai" style="height: 40px;">
                <div class="header-subtitle">Results Ready</div>
            </div>
            <div class="user-info">
                <div>
                    <div style="font-weight: 600;">Business Owner</div>
                    <div style="font-size: 12px; color: var(--ui-text-muted);">Processing Complete</div>
                </div>
                <div class="user-avatar">BO</div>
            </div>
        </header>

        <!-- Left Navigation Panel -->
        <aside class="left-panel">
            <div class="nav-header">
                <img src="/images/giki-logo.svg" alt="giki.ai" style="height: 32px; opacity: 0.8;">
            </div>
            
            <div style="padding: var(--space-4);">
                <div class="nav-section">
                    <div class="nav-section-title">WORK</div>
                    <a href="/dashboard" class="nav-item" title="Dashboard">
                        <span class="nav-icon">□</span>
                    </a>
                    <a href="/upload" class="nav-item" title="Upload">
                        <span class="nav-icon">↑</span>
                    </a>
                    <a href="/transactions" class="nav-item active" title="Transactions">
                        <span class="nav-icon">⊞</span>
                    </a>
                    <a href="/reports" class="nav-item" title="Reports">
                        <span class="nav-icon">∷</span>
                    </a>
                    <a href="/categories" class="nav-item" title="Categories">
                        <span class="nav-icon">⚬</span>
                    </a>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
        <!-- Success Celebration - Simplified -->
        <div class="results-header" style="text-align: center; margin-bottom: var(--space-8);">
            <div class="success-animation" style="display: inline-block; margin-bottom: var(--space-4);">
                <div class="completion-icon" style="width: 80px; height: 80px; background: var(--status-success); color: white; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 48px; box-shadow: 0 4px 16px rgba(5, 150, 105, 0.3); animation: scaleIn 0.5s ease;">□</div>
            </div>
            <h1 style="font-size: 2rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--space-2);">Categorization Complete!</h1>
            <p class="results-summary" style="font-size: 1.25rem; color: var(--ui-text-primary);">247 transactions categorized with <span style="font-weight: 600; color: var(--status-success);">89% confidence</span></p>
        </div>

        <!-- Key Metrics - Visual -->
        <div class="metrics-cards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--space-4); margin-bottom: var(--space-8); max-width: 600px; margin-left: auto; margin-right: auto;">
            <div class="metric-card" style="background: var(--ui-background); border: 1px solid var(--ui-border); border-radius: 12px; padding: var(--space-4); text-align: center;">
                <div class="metric-value" style="font-size: 2rem; font-weight: 700; color: var(--brand-primary);">247</div>
                <div class="metric-label" style="color: var(--ui-text-secondary); font-size: 0.875rem;">Transactions</div>
            </div>
            <div class="metric-card highlight" style="background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%); color: white; border-radius: 12px; padding: var(--space-4); text-align: center; box-shadow: 0 4px 12px rgba(41, 83, 67, 0.3);">
                <div class="metric-value" style="font-size: 2rem; font-weight: 700;">89%</div>
                <div class="metric-label" style="font-size: 0.875rem;">Accuracy</div>
            </div>
            <div class="metric-card" style="background: var(--ui-background); border: 1px solid var(--ui-border); border-radius: 12px; padding: var(--space-4); text-align: center;">
                <div class="metric-value" style="font-size: 2rem; font-weight: 700; color: var(--brand-primary);">18</div>
                <div class="metric-label" style="color: var(--ui-text-secondary); font-size: 0.875rem;">Categories</div>
            </div>
        </div>

        <!-- Primary Action -->
        <div class="action-section" style="text-align: center; margin-bottom: var(--space-8);">
            <button class="btn-primary btn-large" style="background: linear-gradient(135deg, var(--brand-primary) 0%, #1A3F5F 100%); color: white; border: none; padding: var(--space-4) var(--space-8); border-radius: 12px; font-size: 1.125rem; font-weight: 600; cursor: pointer; box-shadow: 0 4px 12px rgba(41, 83, 67, 0.3); transition: all 0.2s ease;" onclick="window.location.href='/review'">
                Review & Boost Accuracy
                <span class="boost-indicator" style="background: var(--status-success); padding: 2px 8px; border-radius: 20px; margin-left: var(--space-2); font-size: 0.875rem;">+6%</span>
            </button>
            <p class="action-description" style="color: var(--ui-text-secondary); margin-top: var(--space-2); font-size: 0.875rem;">Quick review can boost accuracy to 95%+</p>
        </div>

        <!-- Secondary Actions -->
        <div class="secondary-actions" style="display: flex; gap: var(--space-4); justify-content: center; margin-bottom: var(--space-8);">
            <button class="btn-secondary" style="background: white; color: var(--brand-primary); border: 2px solid var(--brand-primary); padding: var(--space-3) var(--space-6); border-radius: 8px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: var(--space-2);" onclick="window.location.href='/export'">
                <span class="icon" style="font-size: 1.25rem;">↓</span>
                Export Now
            </button>
            <button class="btn-secondary" style="background: white; color: var(--brand-primary); border: 2px solid var(--brand-primary); padding: var(--space-3) var(--space-6); border-radius: 8px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: var(--space-2);" onclick="window.location.href='/upload'">
                <span class="icon" style="font-size: 1.25rem;">↑</span>
                Upload More
            </button>
        </div>


        <!-- Categories Preview -->
        <div class="categories-section">
            <h2 class="section-title">AI-Generated Business Categories</h2>
            <div class="categories-grid">
                <div class="category-item">
                    <div class="category-header">
                        <div class="category-icon">□</div>
                        <div>
                            <div class="category-name">Office Supplies</div>
                            <div class="category-count">24 transactions</div>
                        </div>
                    </div>
                    <div class="category-examples">Staples, Office Depot, Amazon Business...</div>
                </div>
                <div class="category-item">
                    <div class="category-header">
                        <div class="category-icon">↑</div>
                        <div>
                            <div class="category-name">Travel & Entertainment</div>
                            <div class="category-count">18 transactions</div>
                        </div>
                    </div>
                    <div class="category-examples">Uber, Hotels, Restaurants...</div>
                </div>
                <div class="category-item">
                    <div class="category-header">
                        <div class="category-icon">⊞</div>
                        <div>
                            <div class="category-name">Software Subscriptions</div>
                            <div class="category-count">12 transactions</div>
                        </div>
                    </div>
                    <div class="category-examples">Microsoft, Adobe, Slack...</div>
                </div>
                <div class="category-item">
                    <div class="category-header">
                        <div class="category-icon">∷</div>
                        <div>
                            <div class="category-name">Marketing & Advertising</div>
                            <div class="category-count">31 transactions</div>
                        </div>
                    </div>
                    <div class="category-examples">Google Ads, Facebook, LinkedIn...</div>
                </div>
                <div class="category-item">
                    <div class="category-header">
                        <div class="category-icon">⚬</div>
                        <div>
                            <div class="category-name">Professional Services</div>
                            <div class="category-count">15 transactions</div>
                        </div>
                    </div>
                    <div class="category-examples">Legal, Consulting, Accounting...</div>
                </div>
                <div class="category-item">
                    <div class="category-header">
                        <div class="category-icon">□</div>
                        <div>
                            <div class="category-name">Employee Benefits</div>
                            <div class="category-count">22 transactions</div>
                        </div>
                    </div>
                    <div class="category-examples">Health insurance, 401k, Benefits...</div>
                </div>
            </div>
        </div>

        <!-- Quick Preview - Collapsible -->
        <details class="transaction-preview" style="background: var(--ui-background); border: 1px solid var(--ui-border); border-radius: 12px; padding: 0; margin-bottom: var(--space-8); overflow: hidden;">
            <summary style="padding: var(--space-6); cursor: pointer; font-size: 1.25rem; font-weight: 600; color: var(--brand-primary); list-style: none; display: flex; justify-content: space-between; align-items: center;">
                Preview Categorized Transactions
                <span style="font-size: 0.875rem; color: var(--ui-text-secondary);">▼</span>
            </summary>
            <div style="padding: 0 var(--space-6) var(--space-6) var(--space-6);">
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <thead>
                        <tr style="background: var(--ui-surface);">
                            <th style="padding: var(--space-3); text-align: left; font-weight: 600; color: var(--ui-text-primary); border-bottom: 2px solid var(--ui-border);">Date</th>
                            <th style="padding: var(--space-3); text-align: left; font-weight: 600; color: var(--ui-text-primary); border-bottom: 2px solid var(--ui-border);">Description</th>
                            <th style="padding: var(--space-3); text-align: right; font-weight: 600; color: var(--ui-text-primary); border-bottom: 2px solid var(--ui-border);">Amount</th>
                            <th style="padding: var(--space-3); text-align: left; font-weight: 600; color: var(--ui-text-primary); border-bottom: 2px solid var(--ui-border);">Category</th>
                            <th style="padding: var(--space-3); text-align: center; font-weight: 600; color: var(--ui-text-primary); border-bottom: 2px solid var(--ui-border);">Confidence</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid var(--ui-border);">
                            <td style="padding: var(--space-3);">2024-03-15</td>
                            <td style="padding: var(--space-3);">AMAZON.COM AMZN.COM/BILL WA</td>
                            <td style="padding: var(--space-3); text-align: right; font-weight: 600;">$127.49</td>
                            <td style="padding: var(--space-3);">
                                <div style="display: flex; align-items: center; gap: 4px; font-size: 11px;">
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Expenses</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Operations</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary); color: white; padding: 2px 6px; border-radius: 4px; font-weight: 600;">Office Supplies</span>
                                    <span style="color: var(--ui-text-muted); font-size: 10px;">[GL: 5130]</span>
                                </div>
                            </td>
                            <td style="padding: var(--space-3); text-align: center;"><span style="background: var(--status-success); color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">94%</span></td>
                        </tr>
                        <tr style="border-bottom: 1px solid var(--ui-border);">
                            <td style="padding: var(--space-3);">2024-03-14</td>
                            <td style="padding: var(--space-3);">SQ *COFFEE SHOP DOWNTOWN</td>
                            <td style="padding: var(--space-3); text-align: right; font-weight: 600;">$45.80</td>
                            <td style="padding: var(--space-3);">
                                <div style="display: flex; align-items: center; gap: 4px; font-size: 11px;">
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Expenses</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Sales & Marketing</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary); color: white; padding: 2px 6px; border-radius: 4px; font-weight: 600;">Meals & Entertainment</span>
                                    <span style="color: var(--ui-text-muted); font-size: 10px;">[GL: 5220]</span>
                                </div>
                            </td>
                            <td style="padding: var(--space-3); text-align: center;"><span style="background: var(--status-warning); color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">82%</span></td>
                        </tr>
                        <tr style="border-bottom: 1px solid var(--ui-border);">
                            <td style="padding: var(--space-3);">2024-03-13</td>
                            <td style="padding: var(--space-3);">MICROSOFT*OFFICE 365</td>
                            <td style="padding: var(--space-3); text-align: right; font-weight: 600;">$12.99</td>
                            <td style="padding: var(--space-3);">
                                <div style="display: flex; align-items: center; gap: 4px; font-size: 11px;">
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Expenses</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Technology</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary); color: white; padding: 2px 6px; border-radius: 4px; font-weight: 600;">Software Subscriptions</span>
                                    <span style="color: var(--ui-text-muted); font-size: 10px;">[GL: 5120]</span>
                                </div>
                            </td>
                            <td style="padding: var(--space-3); text-align: center;"><span style="background: var(--status-success); color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">99%</span></td>
                        </tr>
                        <tr style="border-bottom: 1px solid var(--ui-border);">
                            <td style="padding: var(--space-3);">2024-03-12</td>
                            <td style="padding: var(--space-3);">GOOGLE ADS LLC</td>
                            <td style="padding: var(--space-3); text-align: right; font-weight: 600;">$350.00</td>
                            <td style="padding: var(--space-3);">
                                <div style="display: flex; align-items: center; gap: 4px; font-size: 11px;">
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Expenses</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Sales & Marketing</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary); color: white; padding: 2px 6px; border-radius: 4px; font-weight: 600;">Digital Marketing</span>
                                    <span style="color: var(--ui-text-muted); font-size: 10px;">[GL: 5200]</span>
                                </div>
                            </td>
                            <td style="padding: var(--space-3); text-align: center;"><span style="background: var(--status-success); color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">97%</span></td>
                        </tr>
                        <tr style="border-bottom: 1px solid var(--ui-border);">
                            <td style="padding: var(--space-3);">2024-03-11</td>
                            <td style="padding: var(--space-3);">UBER TRIP SAN FRANCISCO</td>
                            <td style="padding: var(--space-3); text-align: right; font-weight: 600;">$23.45</td>
                            <td style="padding: var(--space-3);">
                                <div style="display: flex; align-items: center; gap: 4px; font-size: 11px;">
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Expenses</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary-light); color: var(--brand-primary); padding: 2px 6px; border-radius: 4px;">Sales & Marketing</span>
                                    <span style="color: var(--ui-text-muted);">></span>
                                    <span style="background: var(--brand-primary); color: white; padding: 2px 6px; border-radius: 4px; font-weight: 600;">Travel & Transportation</span>
                                    <span style="color: var(--ui-text-muted); font-size: 10px;">[GL: 5210]</span>
                                </div>
                            </td>
                            <td style="padding: var(--space-3); text-align: center;"><span style="background: var(--status-success); color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">98%</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div style="margin-top: var(--space-4); text-align: center; color: var(--ui-text-secondary); font-size: 14px;">Showing 5 of 247 transactions • <a href="/transactions" style="color: var(--brand-primary); text-decoration: none;">View all transactions</a></div>
            </div>
        </details>


        <!-- Trust Indicators -->
        <div class="trust-indicators">
            <div class="trust-item">
                <span class="trust-icon">⚬</span>
                <span>Data processed securely</span>
            </div>
            <div class="trust-item">
                <span class="trust-icon">□</span>
                <span>Export ready for accounting</span>
            </div>
            <div class="trust-item">
                <span class="trust-icon">∷</span>
                <span>Categories improve over time</span>
            </div>
        </div>
    </main>

    <!-- Agent Panel -->
    <aside class="agent-panel">
        <div class="agent-header">
            <div class="agent-title">
                <div class="agent-avatar">AI</div>
                <span>giki Assistant</span>
            </div>
            <button style="background: none; border: none; color: var(--ui-text-muted); cursor: pointer; font-size: 1.25rem;">×</button>
        </div>
        
        <div class="agent-content">
            <div class="agent-message">
                <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Congratulations! Processing Complete</strong></p>
                <p style="font-size: 0.875rem; color: var(--ui-text-secondary);">Your 247 transactions have been categorized with 89% accuracy. I can help you review specific categorizations, explain the AI's decisions, or suggest improvements.</p>
            </div>
            
            <div class="agent-message">
                <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Top Insights:</strong></p>
                <ul style="font-size: 0.875rem; color: var(--ui-text-secondary); margin-left: var(--space-4);">
                    <li>Office Supplies: 24 transactions (high confidence)</li>
                    <li>Marketing: 31 transactions (review recommended)</li>
                    <li>Travel: 18 transactions (excellent accuracy)</li>
                    <li>5 categories needed manual review</li>
                </ul>
            </div>
            
            <div class="agent-message">
                <p style="font-size: 0.875rem; color: var(--ui-text-primary); margin-bottom: var(--space-2);"><strong>Next Steps:</strong></p>
                <p style="font-size: 0.875rem; color: var(--ui-text-secondary);">I recommend reviewing the Marketing & Advertising category first - it has the most transactions and could benefit from your business expertise.</p>
            </div>
        </div>
        
        <div class="agent-input">
            <textarea class="agent-input-field" placeholder="Ask me about specific categories, accuracy improvements, or export options..."></textarea>
            <div class="agent-quick-actions">
                <button class="quick-action">Review categories</button>
                <button class="quick-action">Export options</button>
                <button class="quick-action">Boost accuracy</button>
            </div>
        </div>
    </aside>

    <!-- Agent Activation Badge -->
    <div class="agent-activation-badge" title="Open AI Assistant">
        <img src="/images/giki-logo.svg" alt="AI Assistant" />
    </div>
    
    </div> <!-- End app-layout -->

    <style>
        @keyframes scaleIn {
            0% { transform: scale(0); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(41, 83, 67, 0.4);
        }
        
        summary::-webkit-details-marker {
            display: none;
        }
        
        details[open] summary span {
            transform: rotate(180deg);
        }
        
        summary span {
            transition: transform 0.3s ease;
        }
    </style>
    <script>
        // Add smooth interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Result cards animation on scroll
            const resultCards = document.querySelectorAll('.result-card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.transform = 'translateY(0)';
                            entry.target.style.opacity = '1';
                        }, index * 100);
                    }
                });
            });

            resultCards.forEach(card => {
                card.style.transform = 'translateY(20px)';
                card.style.opacity = '0';
                card.style.transition = 'all 0.5s ease';
                observer.observe(card);
            });

            // Button click animations
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.pointerEvents = 'none';
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Category hover effects
            const categoryItems = document.querySelectorAll('.category-item');
            categoryItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 12px var(--brand-primary-alpha)';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });
            
            // Agent panel interactions
            const agentActionBtns = document.querySelectorAll('.agent-action-btn');
            agentActionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    console.log('Agent action:', action);
                    
                    // Add loading state
                    const originalText = this.textContent;
                    this.textContent = '⏳ Processing...';
                    this.disabled = true;
                    
                    // Simulate agent response
                    setTimeout(() => {
                        this.textContent = '□ ' + originalText;
                        this.style.background = 'var(--status-success)';
                        
                        // Reset after 2 seconds
                        setTimeout(() => {
                            this.textContent = originalText;
                            this.disabled = false;
                            this.style.background = '';
                        }, 2000);
                    }, 1000);
                });
            });
            
            // Agent input interactions
            const agentInput = document.querySelector('.agent-input');
            const agentSendBtn = document.querySelector('.agent-send-btn');
            
            if (agentInput && agentSendBtn) {
                agentSendBtn.addEventListener('click', function() {
                    const message = agentInput.value.trim();
                    if (message) {
                        console.log('User message:', message);
                        agentInput.value = '';
                        
                        // Add a simple response simulation
                        const agentContent = document.querySelector('.agent-content');
                        const newMessage = document.createElement('div');
                        newMessage.className = 'agent-message';
                        newMessage.innerHTML = `
                            <div class="agent-message-content">
                                <strong>You asked:</strong> "${message}"<br><br>
                                I'm processing your request and will provide insights based on your transaction data. 
                                This is a demonstration of how I'll help you analyze your financial information.
                            </div>
                        `;
                        agentContent.appendChild(newMessage);
                        agentContent.scrollTop = agentContent.scrollHeight;
                    }
                });
                
                // Send on Enter key
                agentInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        agentSendBtn.click();
                    }
                });
            }
        });

        // Add ripple animation to CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>