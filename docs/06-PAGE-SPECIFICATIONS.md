# Page Specifications - giki.ai MIS Platform

**Version:** 1.0 | **Updated:** 2025-01-11  
**Purpose:** Comprehensive page state specifications and backend integrations  
**Scope:** All pages, states, API mappings, and user flows for the giki.ai MIS platform

## DOCUMENTATION INTEGRATION

**See Also:**
- **docs/05-DESIGN-SYSTEM.md** - Visual standards, components, and brand guidelines
- **docs/04-CUSTOMER-JOURNEYS.md** - Business requirements and user journey flows  
- **docs/02-SYSTEM-ARCHITECTURE.md** - Technical implementation architecture
- **docs/08-TESTING-STRATEGY.md** - Testing approach and validation methods
- **docs/design-system/mockups/** - Visual mockups for each page state

### Implementation Integration
- **Design Standards**: Apply docs/05-DESIGN-SYSTEM.md brand colors, typography, and layout rules to all page states
- **Visual Reference**: Use mockups in docs/design-system/mockups/ for accurate visual implementation
- **Testing Validation**: Validate page behaviors against docs/08-TESTING-STRATEGY.md requirements
- **Architecture Compliance**: Ensure API integrations follow docs/02-SYSTEM-ARCHITECTURE.md patterns

---

## 🎯 DOCUMENT PURPOSE

This document serves as the definitive reference for:
- **What pages exist** in the giki.ai platform
- **What each page looks like in different states** (loading, populated, empty, error)
- **How pages integrate with backend APIs** and services
- **User flows and state transitions** between pages
- **Role-based page variations** for different user types

---

## 📋 COMPLETE PAGE INVENTORY

### **Core Business Pages** (Main User Journey)
| Page | Purpose | Primary APIs | User Roles | Status |
|------|---------|-------------|------------|---------|
| `DashboardPage` | Financial overview & metrics | `/dashboard/metrics`, `/transactions/fast` | All | ✅ |
| `UploadPage` | File upload interface | `/files/upload`, `/files/schema` | Owner, Accountant, Bookkeeper | ✅ |
| `ProcessingPage` | AI categorization status | `/files/{id}/status`, WebSocket updates | All | ✅ |
| `TransactionReviewPage` | Transaction review & approval | `/transactions/`, `/transactions/review-queue` | All | ✅ |
| `ReportsPage` | Financial reporting & export | `/reports/*`, `/exports/*` | All | ✅ |
| `CategoriesPage` | Category management | `/categories/`, `/categories/with-counts` | Owner, Accountant | ✅ |

### **Authentication Pages**
| Page | Purpose | Primary APIs | User Roles | Status |
|------|---------|-------------|------------|---------|
| `LoginPage` | User authentication | `/auth/login`, `/auth/token` | Unauthenticated | ✅ |
| `RegisterPage` | User registration | `/auth/register`, `/auth/public-register` | Unauthenticated | ✅ |
| `GetStartedPage` | Landing page | None | Unauthenticated | ✅ |
| `FirstLoginPage` | First-time user setup | `/auth/me`, `/onboarding/start` | New Users | ✅ |

### **Onboarding & MIS Setup Pages**
| Page | Purpose | Primary APIs | User Roles | Status |
|------|---------|-------------|------------|---------|
| `OnboardingWizardPage` | Multi-step MIS setup | `/onboarding/*`, `/onboarding/mis/*` | New Users | ✅ |
| `MISSetupIntroduction` | MIS system introduction | `/onboarding/status` | New Users | ✅ |
| `MISCompanySetup` | Company information | `/onboarding/business-context` | Owner | ✅ |
| `MISDataUpload` | Enhancement data upload | `/onboarding/upload-historical-data` | All | ✅ |
| `MISActivation` | MIS system activation | `/onboarding/approve-for-production` | Owner | ✅ |

### **Intelligence & Analytics Pages**
| Page | Purpose | Primary APIs | User Roles | Status |
|------|---------|-------------|------------|---------|
| `CashflowDashboardPage` | Cash flow analysis | `/reports/monthly-trends` | All | ✅ |
| `ProfitLossPage` | P&L statements | `/reports/income-expense-summary` | All | ✅ |
| `BudgetVariancePage` | Budget analysis | `/reports/budget-variance` | Owner, Accountant | ✅ |
| `VendorSpendPage` | Vendor spending analysis | `/reports/spending-by-entity` | All | ✅ |
| `FinancialForecastingPage` | Financial forecasting | `/intelligence/forecasting/generate` | Owner, Accountant | ✅ |
| `TrendsAnalysisPage` | Trend analysis | `/reports/monthly-trends` | All | ✅ |
| `PatternRecognitionPage` | Pattern insights | `/intelligence/insights/patterns` | All | ✅ |

### **Administrative Pages**
| Page | Purpose | Primary APIs | User Roles | Status |
|------|---------|-------------|------------|---------|
| `SettingsPage` | System settings | `/auth/me`, user preferences | All | ✅ |
| `UserManagementPage` | User management | `/admin/users`, `/admin/roles` | Owner | ✅ |
| `AccountingSyncPage` | Accounting integration | `/exports/formats`, sync APIs | Owner, Accountant | ✅ |
| `AuditTrailPage` | Audit trail & compliance | `/admin/audit-trail` | Owner, Accountant | ✅ |

---

## 🎨 LAYOUT SYSTEM ARCHITECTURE

### **EnhancedAppLayout** (Three-Panel System)
All authenticated pages use the `EnhancedAppLayout` component with:

```typescript
interface LayoutStates {
  workFirst: "240px 1fr 0px";     // Left nav expanded, agent hidden
  agentMode: "64px 1fr 400px";    // Left nav collapsed, agent open  
  mobileOnly: "1fr";              // Single column on mobile
}
```

**Key Rules:**
- **Mutual Exclusion**: Never both panels expanded simultaneously
- **Work-First Priority**: Main content prioritized over AI assistance
- **Professional Design**: Brand color `#295343`, geometric icons only
- **Responsive**: Mobile collapses to single column

---

## 📊 DETAILED PAGE SPECIFICATIONS

## **DashboardPage.tsx** - Financial Overview

### **Purpose & Role**
Primary landing page showing business financial overview and MIS performance metrics.

### **API Integrations**
```typescript
// Primary data fetching
useDashboard() hook aggregates:
- GET /dashboard/metrics          // Overall business metrics
- GET /transactions/fast          // Recent transaction summary  
- GET /categories/metrics/categorization  // AI accuracy metrics
- GET /accuracy/temporal-data     // Historical performance
- GET /reports/monthly-trends     // Trend data
- GET /files/processing-status    // Current processing status
```

### **Page States**

#### **Loading State**
- Skeleton components for metrics cards
- Loading spinner for charts
- Progressive loading of different sections
- Estimated load time: 2-3 seconds

#### **Empty State** (New User)
- Welcome message: "Welcome to your MIS Dashboard"
- Setup prompts: "Upload your first file to see insights"
- Quick action buttons: "Upload File", "Setup Categories"
- Demo data toggle option

#### **Populated State** (Active User)
- **Metrics Grid**: Revenue, Expenses, Profit Margin, Accuracy Score
- **Quick Actions**: Upload File, Review Transactions, Generate Report
- **Recent Activity**: Last 10 transactions with categories
- **Enhancement Cards**: Available MIS improvements
- **Charts**: Monthly trends, category breakdown

#### **Error States**
- **Network Error**: "Unable to load dashboard data" with retry button
- **Authentication Error**: Redirect to login
- **Data Error**: "Some metrics unavailable" with partial display

### **User Role Variations**
- **Owner**: Full dashboard with all metrics and enhancement cards
- **Accountant**: Financial focus with reporting shortcuts
- **Bookkeeper**: Transaction-focused with categorization metrics
- **Viewer**: Read-only view with limited actions

### **State Management**
```typescript
const [timeRange, setTimeRange] = useState('30d');
const [exportReadiness, setExportReadiness] = useState<ExportReadiness>({});
const [refreshTrigger, setRefreshTrigger] = useState(0);
```

### **Key Interactions**
- Time range selector (7d, 30d, 90d, 1y)
- Enhancement card actions
- Quick navigation to other pages
- Real-time updates when new data processed

---

## **UploadPage.tsx** - File Upload Interface

### **Purpose & Role**
File upload interface with drag-and-drop, multi-format support, and real-time processing.

### **API Integrations**
```typescript
// File upload workflow
1. POST /files/upload (FormData)          // Initial file upload
2. GET /files/{id}/schema                 // Schema interpretation
3. POST /files/{id}/map                   // Column mapping confirmation
4. WebSocket connection                   // Real-time status updates
```

### **Page States**

#### **Empty State** (No Files)
- Large drag-and-drop zone
- "Drop your financial files here or click to browse"
- Supported formats: Excel (.xlsx, .xls), CSV
- File size limits and validation rules
- Example file templates download

#### **Uploading State**
- Progress bar for file upload
- File name and size display
- Upload speed indicator
- Cancel upload option
- Multiple file queue management

#### **Schema Interpretation State**
- Column mapping interface with drag-and-drop functionality
- AI-detected column suggestions with confidence indicators
- Manual column assignment options with validation
- Preview of data mapping with sample data
- Confidence scores for AI detection with color-coded indicators
- Step-by-step progress visualization
- Schema validation with error highlighting

#### **Processing State**
- Real-time processing status with live activity feed
- Transaction count and progress with animated progress bars
- AI categorization progress with confidence scoring
- Estimated completion time with dynamic updates
- Live accuracy metrics with trend indicators
- Real-time WebSocket updates for processing status
- Live transaction categorization feed showing current processing
- Performance metrics dashboard (speed, accuracy, categories created)

#### **Completion State**
- Success summary with statistics
- "View Transactions" button
- Processing performance metrics
- Enhancement suggestions based on uploaded data

#### **Error States**
- **File Format Error**: "Unsupported file format" with format guidelines
- **File Size Error**: "File too large" with size limits
- **Processing Error**: "Error processing file" with retry option
- **Network Error**: Upload interrupted with resume capability

### **State Management**
```typescript
const [files, setFiles] = useState<UploadedFile[]>([]);
const [isDragActive, setIsDragActive] = useState(false);
const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({});
```

### **Key Features**
- Drag-and-drop file upload
- Multi-file support with queue management
- Real-time progress tracking
- Schema detection with AI
- Column mapping confirmation
- Duplicate detection and prevention

---

## **TransactionReviewPage.tsx** - Transaction Review & Approval

### **Purpose & Role**
Transaction review interface for categorization validation and bulk approval.

### **API Integrations**
```typescript
// Transaction management
- GET /transactions/                           // Paginated transaction list
- GET /transactions/review-queue               // Transactions needing review
- PUT /transactions/{id}/category              // Update single transaction
- POST /transactions/bulk-approve              // Bulk approve transactions
- POST /transactions/{id}/approve-ai-suggestion // Approve AI suggestion
- DELETE /transactions/{id}                    // Delete transaction
```

### **Page States**

#### **Loading State**
- Transaction list skeleton
- Filter panel placeholder
- Loading spinner with transaction count

#### **Empty State**
- **No Transactions**: "No transactions found" with upload prompt
- **All Reviewed**: "All transactions reviewed! Great work." with summary
- **Filtered Empty**: "No transactions match your filters" with clear filters option

#### **Populated State**
- **Transaction List**: Paginated table with sorting/filtering
- **Review Queue**: Transactions requiring attention
- **Batch Actions**: Select all, bulk approve, bulk categorize
- **Search & Filters**: Date range, amount, category, confidence score
- **Sidebar Actions**: Category quick-assign, AI suggestions

#### **Bulk Operations State**
- Selected transaction counter
- Bulk action toolbar
- Progress indicator for bulk operations
- Undo capability for recent actions

#### **Error States**
- **Update Error**: "Failed to update transaction" with retry
- **Network Error**: "Connection lost" with offline capability
- **Validation Error**: "Invalid category selection" with correction guidance

### **State Management**
```typescript
const [transactions, setTransactions] = useState<Transaction[]>([]);
const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());
const [filters, setFilters] = useState<TransactionFilters>({});
const [pagination, setPagination] = useState<PaginationInfo>({});
const [sortConfig, setSortConfig] = useState<SortConfig>({});
```

### **Key Features**
- Pagination with configurable page sizes
- Advanced filtering and search
- Bulk selection and operations
- Inline editing capabilities
- AI confidence indicators
- Category suggestions and quick-assign

---

## **ReportsPage.tsx** - Financial Reporting & Export

### **Purpose & Role**
Comprehensive financial reporting with multiple export formats and custom report building.

### **API Integrations**
```typescript
// Report generation and export
- GET /reports/spending-by-category         // Category spending analysis
- GET /reports/income-expense-summary       // P&L summary
- GET /reports/monthly-trends               // Trend analysis
- POST /reports/custom/generate             // Custom report generation
- GET /exports/formats                      // Available export formats
- POST /exports/download                    // Download export file
- GET /exports/readiness/{format_id}        // Export readiness check
```

### **Page States**

#### **Loading State**
- Report preview skeleton
- Chart placeholders
- Loading indicators for data aggregation

#### **Empty State** (No Data)
- "No transactions to report" message
- Upload prompt with file examples
- Sample report preview

#### **Populated State**
- **Report Selector**: Pre-built reports (P&L, Category Analysis, Trends)
- **Report Preview**: Charts, tables, and key metrics
- **Export Options**: 10+ accounting software formats
- **Custom Report Builder**: Drag-and-drop report creation
- **Time Range Selector**: Date filtering options
- **Performance Metrics**: Data accuracy and completeness indicators

#### **Export Ready State**
- Export format validation complete
- Download buttons for each format
- Export history and re-download options
- Format-specific configuration options

#### **Export Processing State**
- Generation progress indicator
- Estimated completion time
- Queue position for large exports
- Background processing notification

#### **Error States**
- **Data Error**: "Insufficient data for report" with requirements
- **Export Error**: "Export failed" with format-specific troubleshooting
- **Format Error**: "Unsupported export format" with alternatives

### **State Management**
```typescript
const [selectedReport, setSelectedReport] = useState<ReportType>('overview');
const [timeRange, setTimeRange] = useState<TimeRange>('monthly');
const [exportFormat, setExportFormat] = useState<ExportFormat>('excel');
const [customReportConfig, setCustomReportConfig] = useState<ReportConfig>({});
```

### **Key Features**
- Multiple pre-built report templates
- Custom report builder with drag-and-drop
- Real-time export readiness validation
- Multiple export formats with format-specific options
- Report scheduling and automation
- Export history and re-download capability

---

## **ProfessionalAgentPanel** - AI Assistant Integration

### **Purpose & Role**
Context-aware AI assistant panel that provides business insights and answers questions.

### **API Integrations**
```typescript
// AI agent communication
- WebSocket /intelligence/ws/agent          // Real-time conversation
- POST /intelligence/agent/command          // Execute agent commands
- GET /intelligence/insights/spending       // Business insights
- GET /intelligence/insights/patterns       // Pattern recognition
```

### **Panel States**

#### **Collapsed State** (Hidden)
- Panel width: 0px
- No API calls active
- Toggle button in header to open

#### **Opening State**
- Panel animates to 400px width
- Loading spinner while connecting
- WebSocket connection establishment

#### **Connected State**
- Welcome message with business context
- Input field for questions
- Conversation history
- Quick action suggestions
- Real-time typing indicators

#### **Conversation State**
- Message bubbles (user/agent)
- Rich content support (charts, tables, links)
- Action buttons for suggested next steps
- Context awareness of current page

#### **Error States**
- **Connection Error**: "Agent unavailable" with retry
- **Processing Error**: "Unable to process request" with clarification
- **Context Error**: "Insufficient data" with requirements

### **State Management**
```typescript
const [isConnected, setIsConnected] = useState(false);
const [messages, setMessages] = useState<Message[]>([]);
const [isTyping, setIsTyping] = useState(false);
const [currentContext, setCurrentContext] = useState<PageContext>({});
```

### **Key Features**
- Real-time WebSocket communication
- Context awareness of current page and data
- Rich message content with interactive elements
- Business intelligence integration
- Quick actions and suggestions
- Conversation history and memory

---

## 🔄 USER FLOW DOCUMENTATION

### **Core Customer Journey**
```
1. Authentication → 2. Dashboard → 3. Upload → 4. Processing → 5. Review → 6. Reports → 7. Export
```

#### **1. Authentication Flow**
- `GetStartedPage` → `LoginPage` → `DashboardPage`
- New users: `GetStartedPage` → `RegisterPage` → `OnboardingWizardPage` → `DashboardPage`
- Password reset: `LoginPage` → Reset email → Password reset form → `LoginPage`

#### **2. File Upload & Processing Flow**
- `UploadPage` (drag/drop) → Schema interpretation → Column mapping → `ProcessingPage` → `TransactionReviewPage`
- Error recovery: Processing errors → Retry options → Support contact

#### **3. Transaction Management Flow**
- `DashboardPage` → `TransactionReviewPage` → Bulk actions → Review completion → `ReportsPage`
- Individual review: Transaction detail → Category assignment → AI approval → Next transaction

#### **4. Reporting & Export Flow**
- `ReportsPage` → Report selection → Time range → Export format → Readiness check → Download
- Custom reports: Report builder → Configuration → Preview → Save/Export

#### **5. MIS Enhancement Flow**
- `DashboardPage` enhancement cards → `OnboardingWizardPage` → Data upload → Processing → Accuracy validation → Production approval

### **Error Recovery Flows**
- Network errors: Automatic retry with exponential backoff
- Authentication errors: Redirect to login with return URL
- Processing errors: Error details → Retry options → Support escalation
- Data errors: Error explanation → Correction guidance → Validation

### **Role-Based Navigation**
- **Owner**: Full access to all pages and administrative functions
- **Accountant**: Focus on categorization, reporting, and compliance features
- **Bookkeeper**: Transaction processing and basic reporting access
- **Viewer**: Read-only access to dashboards and reports

---

## 🎨 VISUAL STATE SPECIFICATIONS

### **Brand Compliance Rules**
- **Primary Color**: `#295343` (exact brand color required)
- **Icons**: Geometric shapes only (□ ⊞ ∷ ⚬ ↑) - NO emojis
- **Typography**: Inter font family, professional hierarchy
- **Layout**: Three-panel system with mutual exclusion
- **Status Indicators**: Inline progress bars, no floating notifications

### **Loading State Standards**
- Skeleton components matching final layout
- Progressive loading with priority content first
- Loading spinners with estimated time when available
- Graceful degradation for slow connections

### **Error State Standards**
- Clear error messages with actionable next steps
- Retry buttons where appropriate
- Error codes for technical debugging
- Fallback content when possible

### **Success State Standards**
- Confirmation messages with key metrics
- Clear next step guidance
- Success indicators without interrupting workflow
- Undo options for reversible actions

---

## 🔒 AUTHENTICATION & AUTHORIZATION

### **Authentication Requirements**
- All pages except `GetStartedPage`, `LoginPage`, `RegisterPage` require authentication
- JWT token validation on every API request
- Automatic token refresh before expiration
- Session timeout with warning and extension option

### **Role-Based Access Control**
```typescript
interface UserRoles {
  owner: {
    pages: "All pages";
    actions: "All actions including user management";
    restrictions: "None";
  };
  accountant: {
    pages: "All except user management";
    actions: "Financial operations, reporting, categorization";
    restrictions: "Cannot manage users or delete data";
  };
  bookkeeper: {
    pages: "Transaction processing, basic reporting";
    actions: "Transaction categorization, file upload, basic reports";
    restrictions: "Cannot access admin functions or modify categories";
  };
  viewer: {
    pages: "Dashboards and reports only";
    actions: "View data, export reports";
    restrictions: "Read-only access, cannot modify data";
  };
}
```

### **Page-Level Permissions**
- Route guards check user role before rendering
- API endpoints validate permissions for each request
- UI elements conditionally render based on user capabilities
- Audit trail logging for all user actions

---

## 📱 RESPONSIVE DESIGN SPECIFICATIONS

### **Breakpoint Behavior**
```css
/* Mobile (< 768px) */
- Single column layout
- Collapsed navigation with hamburger menu
- Stacked components with full-width
- Touch-optimized interactions (44px minimum touch targets)

/* Tablet (768px - 1024px) */
- Two-panel layout (navigation + content)
- Agent panel as overlay when activated
- Optimized for portrait and landscape orientations
- Touch and mouse interaction support

/* Desktop (> 1024px) */
- Full three-panel layout with mutual exclusion
- Optimal spacing and typography scaling
- Keyboard navigation support
- Mouse hover states and interactions
```

### **Mobile-Specific States**
- Mobile header with navigation toggle
- Swipe gestures for list navigation
- Pull-to-refresh functionality
- Offline capability indicators
- Optimized file upload for mobile cameras

---

## 🔧 TECHNICAL IMPLEMENTATION NOTES

### **State Management Patterns**
- React hooks for local component state
- Zustand stores for global state (auth, panels)
- Custom hooks for API integration (`useDashboard`, `useAuth`, etc.)
- WebSocket state management for real-time features

### **Performance Optimizations**
- React.memo for expensive components
- useMemo and useCallback for optimization
- Virtual scrolling for large transaction lists
- Image optimization and lazy loading
- API response caching with TTL

### **Error Handling Patterns**
- Error boundaries for component error recovery
- Global error handling with user-friendly messages
- Retry logic with exponential backoff
- Circuit breaker pattern for API resilience
- Logging and monitoring integration

### **Accessibility Compliance**
- WCAG 2.1 AA compliance target
- Keyboard navigation support
- Screen reader compatibility
- Color contrast validation (4.5:1 minimum)
- Focus indicators and logical tab order

---

## 📊 SUCCESS METRICS

### **Page Performance Targets**
- Initial page load: < 2 seconds
- Data fetching: < 1 second for cached data, < 3 seconds for fresh data
- File upload: Progress indication within 500ms
- Search results: < 1 second response time
- Export generation: Progress indication for operations > 5 seconds

### **User Experience Metrics**
- Error rate: < 1% for critical user flows
- User task completion rate: > 95% for core workflows
- Customer satisfaction: > 90% positive feedback
- Support ticket reduction: 50% decrease with improved UX

### **Technical Quality Metrics**
- Accessibility score: 100% WCAG 2.1 AA compliance
- Performance score: > 90 Lighthouse score
- Error handling: 100% coverage for user-facing errors
- Mobile responsiveness: 100% feature parity across devices

---

**COMPREHENSIVE PAGE SPECIFICATIONS SUMMARY:** This document provides the complete reference for all pages, states, API integrations, and user flows in the giki.ai MIS platform. It serves as the single source of truth for frontend-backend integration and ensures consistent user experience across all features and user roles.

**NEXT UPDATE:** After new pages are added or existing page workflows are modified based on user feedback and usage analytics.