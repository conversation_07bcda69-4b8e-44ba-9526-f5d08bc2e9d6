# API Reference - giki.ai Platform

**Version:** 2.0 | **Updated:** 2025-01-02  
**Base URL:** `https://giki-ai-api-273348121056.us-central1.run.app`  
**Local Dev:** `http://localhost:8000`

## API DOCUMENTATION IMPORTS
docs/api/openapi.json
docs/api/README.md
<!-- SYNC REMINDER: Always update OpenAPI spec when making API changes -->

## 📋 API SPECIFICATIONS

### Machine-Readable Formats
- **OpenAPI 3.1.0 Specification**: [`docs/api/openapi.json`](./api/openapi.json)
- **Interactive Documentation**: `http://localhost:8000/docs` (local) | `https://giki-ai-api-273348121056.us-central1.run.app/docs` (production)
- **ReDoc Format**: `http://localhost:8000/redoc` (local) | `https://giki-ai-api-273348121056.us-central1.run.app/redoc` (production)

### Developer Tools Integration
Import the OpenAPI specification into your preferred API client:
```bash
# Postman: Import → Link → http://localhost:8000/openapi.json
# Insomnia: Create → Import from URL → http://localhost:8000/openapi.json
# VS Code REST Client: Use @baseUrl = http://localhost:8000
```

**For complete developer setup instructions, see**: [`docs/api/README.md`](./api/README.md)

---

## 🔐 AUTHENTICATION

### JWT Token Authentication
```http
# All API requests require authentication header
Authorization: Bearer <jwt_token>

# Content type for JSON requests
Content-Type: application/json
```

### Auth Endpoints

#### POST /auth/login
**Description:** Authenticate user and receive JWT tokens  
**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "GikiTest2025Secure"
}
```
**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 86400,
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "tenant_id": "uuid",
    "tenant_name": "Nuvie"
  }
}
```

#### POST /auth/refresh
**Description:** Refresh access token using refresh token  
**Body:**
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
}
```

#### POST /auth/logout
**Description:** Invalidate current session tokens  
**Response:** `204 No Content`

---

## 📁 FILE MANAGEMENT

### Upload Endpoints

#### POST /files/upload
**Description:** Upload financial data file for processing  
**Content-Type:** `multipart/form-data`  
**Body:**
```form
file: <binary_file_data>           # Excel, CSV, QIF, OFX files
tenant_id: "uuid"                  # Tenant isolation
```
**Response:**
```json
{
  "upload_id": "uuid",
  "filename": "transactions.xlsx",
  "file_size": 15420,
  "mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "status": "uploaded",
  "columns_detected": [
    "Date", "Description", "Amount", "Type"
  ],
  "rows_detected": 150,
  "processing_required": true
}
```

#### GET /files/{upload_id}/schema
**Description:** Get detected file schema for column mapping  
**Response:**
```json
{
  "upload_id": "uuid",
  "detected_columns": [
    {
      "name": "Date",
      "type": "date",
      "sample_values": ["2025-01-15", "2025-01-16"],
      "confidence": 0.95
    },
    {
      "name": "Description", 
      "type": "text",
      "sample_values": ["AMAZON.COM", "STARBUCKS"],
      "confidence": 0.98
    },
    {
      "name": "Amount",
      "type": "decimal", 
      "sample_values": ["-89.50", "-4.95"],
      "confidence": 0.99
    }
  ],
  "suggested_mapping": {
    "date": "Date",
    "description": "Description", 
    "amount": "Amount",
    "transaction_type": "Type"
  }
}
```

#### POST /files/{upload_id}/mapping
**Description:** Submit column mapping for file processing  
**Body:**
```json
{
  "column_mapping": {
    "date": "Date",
    "description": "Description",
    "amount": "Amount", 
    "transaction_type": "Type",
    "category": null
  },
  "processing_options": {
    "auto_categorize": true,
    "confidence_threshold": 0.85,
    "business_context": "small_business_expenses"
  }
}
```
**Response:**
```json
{
  "processing_id": "uuid",
  "status": "processing",
  "estimated_completion": "2025-06-30T12:05:00Z",
  "transactions_to_process": 150
}
```

### File Status & Results

#### GET /files/{upload_id}/status
**Description:** Check file processing status  
**Response:**
```json
{
  "upload_id": "uuid",
  "status": "completed",
  "progress": 100,
  "processed_transactions": 150,
  "successful_categorizations": 131,
  "manual_review_required": 19,
  "processing_time_seconds": 45,
  "overall_accuracy": 87.3
}
```

#### GET /files/{upload_id}/results
**Description:** Get processing results and transaction data  
**Response:**
```json
{
  "upload_id": "uuid", 
  "processing_summary": {
    "total_transactions": 150,
    "successful_categorizations": 131,
    "high_confidence": 98,
    "medium_confidence": 33,
    "low_confidence": 19,
    "overall_accuracy": 87.3
  },
  "transactions": [
    {
      "id": "uuid",
      "date": "2025-01-15",
      "description": "AMAZON.COM PURCHASE",
      "amount": -89.50,
      "suggested_category": "Office Supplies",
      "confidence_score": 0.92,
      "ai_reasoning": "Online purchase pattern consistent with office supplies",
      "review_status": "auto_approved"
    }
  ]
}
```

---

## 💳 TRANSACTION MANAGEMENT

### Transaction Queries

#### GET /transactions
**Description:** Get paginated list of transactions  
**Query Parameters:**
```
page: int = 1
limit: int = 50
status: str = "all" | "pending" | "approved" | "rejected"
category: str = optional category filter
date_from: date = optional start date
date_to: date = optional end date
confidence_min: float = minimum confidence score
```
**Response:**
```json
{
  "transactions": [
    {
      "id": "uuid",
      "date": "2025-01-15", 
      "description": "STARBUCKS COFFEE",
      "amount": -4.95,
      "category": {
        "id": "uuid",
        "name": "Meals & Entertainment", 
        "confidence_score": 0.89
      },
      "review_status": "pending",
      "created_at": "2025-06-30T10:30:00Z"
    }
  ],
  "pagination": {
    "total": 150,
    "page": 1,
    "limit": 50,
    "pages": 3
  }
}
```

#### GET /transactions/{transaction_id}
**Description:** Get detailed transaction information  
**Response:**
```json
{
  "id": "uuid",
  "date": "2025-01-15",
  "description": "AMAZON.COM PURCHASE #123456789",
  "amount": -89.50,
  "category": {
    "id": "uuid", 
    "name": "Office Supplies",
    "confidence_score": 0.92,
    "ai_reasoning": "Large online retailer purchase pattern, amount consistent with office supplies, business context suggests professional use"
  },
  "review_status": "auto_approved",
  "manual_overrides": [],
  "ai_suggestions": [
    {
      "category": "Office Supplies",
      "confidence": 0.92
    },
    {
      "category": "Equipment", 
      "confidence": 0.76
    }
  ],
  "file_source": {
    "upload_id": "uuid",
    "filename": "transactions.xlsx",
    "row_number": 15
  }
}
```

### Transaction Updates

#### PUT /transactions/{transaction_id}/category
**Description:** Update transaction category (manual review)  
**Body:**
```json
{
  "category_id": "uuid",
  "confidence_score": 1.0,
  "review_notes": "Manual correction - software subscription",
  "training_feedback": true
}
```

#### POST /transactions/bulk-approve
**Description:** Bulk approve high-confidence transactions  
**Body:**
```json
{
  "transaction_ids": ["uuid1", "uuid2", "uuid3"],
  "confidence_threshold": 0.85,
  "approval_reason": "batch_high_confidence"
}
```

---

## 🏷️ CATEGORY MANAGEMENT

### Category Queries

#### GET /categories
**Description:** Get hierarchical category structure  
**Response:**
```json
{
  "categories": [
    {
      "id": "uuid",
      "name": "Operating Expenses",
      "parent_id": null,
      "gl_code": "6000",
      "business_context": "Day-to-day business operational costs",
      "children": [
        {
          "id": "uuid",
          "name": "Office Supplies",
          "parent_id": "uuid", 
          "gl_code": "6100",
          "business_context": "Stationery, paper, office equipment under $500"
        }
      ]
    }
  ],
  "tenant_customizations": {
    "m1_zero_onboarding": true,
    "gl_code_required": false,
    "hierarchy_depth": 3
  }
}
```

#### POST /categories
**Description:** Create new category (M3 GL code compliance)  
**Body:**
```json
{
  "name": "Software Subscriptions",
  "parent_id": "uuid",
  "gl_code": "6200", 
  "business_context": "Monthly and annual software service subscriptions",
  "confidence_threshold": 0.80
}
```

### Category Intelligence

#### POST /categories/ai-suggest
**Description:** Get AI suggestions for transaction categorization  
**Body:**
```json
{
  "transaction": {
    "description": "ADOBE CREATIVE CLOUD", 
    "amount": -29.99,
    "date": "2025-01-15"
  },
  "business_context": "small_business_creative_agency"
}
```
**Response:**
```json
{
  "suggestions": [
    {
      "category": "Software Subscriptions",
      "confidence": 0.94,
      "reasoning": "Adobe Creative Cloud is professional software, monthly amount typical of subscription model"
    },
    {
      "category": "Professional Services", 
      "confidence": 0.78,
      "reasoning": "Could be professional service expense for creative work"
    }
  ]
}
```

---

## 📊 REPORTS & ANALYTICS

### Report Generation

#### GET /reports/templates
**Description:** Available report templates  
**Response:**
```json
{
  "templates": [
    {
      "id": "monthly_pl",
      "name": "Monthly P&L Statement", 
      "description": "Profit & Loss breakdown by category",
      "export_formats": ["excel", "pdf", "csv"],
      "parameters": ["date_range", "category_filter"]
    },
    {
      "id": "expense_analysis",
      "name": "Expense Analysis by Category",
      "description": "Detailed spending breakdown and trends", 
      "export_formats": ["pdf", "excel"],
      "parameters": ["date_range", "comparison_period"]
    }
  ]
}
```

#### POST /reports/generate
**Description:** Generate custom report  
**Body:**
```json
{
  "template_id": "monthly_pl",
  "parameters": {
    "date_range": {
      "start": "2025-01-01",
      "end": "2025-01-31"
    },
    "category_filter": ["Operating Expenses"],
    "export_format": "excel"
  },
  "include_charts": true,
  "include_summaries": true
}
```
**Response:**
```json
{
  "report_id": "uuid",
  "status": "generating",
  "estimated_completion": "2025-06-30T12:02:00Z",
  "template_used": "monthly_pl", 
  "parameters": {...}
}
```

#### GET /reports/{report_id}
**Description:** Get generated report data or download link  
**Response:**
```json
{
  "report_id": "uuid",
  "status": "completed",
  "generated_at": "2025-06-30T12:02:15Z",
  "data": {
    "summary": {
      "total_income": 15000.00,
      "total_expenses": -8750.50,
      "net_profit": 6249.50
    },
    "categories": [
      {
        "name": "Office Supplies",
        "amount": -1250.00,
        "percentage": 14.3,
        "transaction_count": 23
      }
    ]
  },
  "download_urls": {
    "excel": "https://storage.googleapis.com/reports/report_uuid.xlsx",
    "pdf": "https://storage.googleapis.com/reports/report_uuid.pdf"
  }
}
```

### Analytics Endpoints

#### GET /analytics/accuracy
**Description:** Get categorization accuracy metrics  
**Query Parameters:**
```
period: str = "last_30_days" | "last_90_days" | "all_time"
breakdown: str = "daily" | "weekly" | "monthly"
```
**Response:**
```json
{
  "overall_accuracy": 87.3,
  "period": "last_30_days", 
  "accuracy_by_category": [
    {
      "category": "Office Supplies",
      "accuracy": 92.1,
      "transaction_count": 45
    }
  ],
  "accuracy_trends": [
    {
      "date": "2025-06-01",
      "accuracy": 85.2
    },
    {
      "date": "2025-06-30", 
      "accuracy": 87.3
    }
  ]
}
```

#### GET /analytics/spending-patterns
**Description:** Spending pattern analysis  
**Response:**
```json
{
  "monthly_spending": [
    {
      "month": "2025-01",
      "total": -8750.50,
      "categories": [
        {
          "name": "Office Supplies", 
          "amount": -1250.00,
          "percentage": 14.3
        }
      ]
    }
  ],
  "top_vendors": [
    {
      "vendor": "AMAZON.COM",
      "total_spent": -450.75,
      "transaction_count": 8,
      "avg_amount": -56.34
    }
  ]
}
```

---

## 🎯 ACCURACY VALIDATION (M2)

### Temporal Accuracy Endpoints

#### POST /accuracy/temporal-validation
**Description:** Submit historical data for M2 temporal accuracy testing  
**Body:**
```json
{
  "historical_data": [
    {
      "month": "2024-01",
      "transactions": [
        {
          "description": "OFFICE DEPOT SUPPLIES",
          "amount": -89.50,
          "actual_category": "Office Supplies"
        }
      ]
    }
  ],
  "validation_config": {
    "baseline_month": "2024-01",
    "improvement_tracking": true,
    "confidence_calibration": true
  }
}
```

#### GET /accuracy/temporal-results
**Description:** Get temporal accuracy improvement results  
**Response:**
```json
{
  "validation_summary": {
    "baseline_accuracy": 78.5,
    "current_accuracy": 87.3,
    "improvement_delta": 8.8,
    "months_tracked": 6
  },
  "monthly_progression": [
    {
      "month": "2024-01",
      "accuracy": 78.5,
      "transaction_count": 120
    },
    {
      "month": "2024-06", 
      "accuracy": 87.3,
      "transaction_count": 135
    }
  ],
  "improvement_factors": [
    "Historical pattern learning",
    "Business context refinement",
    "Category definition improvements"
  ]
}
```

---

## 🏗️ ONBOARDING PATHS (M1/M2/M3)

### Path Selection

#### GET /onboarding/paths
**Description:** Available onboarding paths for tenant  
**Response:**
```json
{
  "available_paths": [
    {
      "id": "zero_onboarding",
      "name": "Zero-Onboarding (M1)",
      "description": "Pure AI business intelligence without training data",
      "target_accuracy": 87,
      "setup_time": "5 minutes",
      "requirements": []
    },
    {
      "id": "schema_guided", 
      "name": "Schema-Guided (M3)",
      "description": "AI follows provided GL code structure",
      "target_accuracy": 95,
      "setup_time": "30 minutes",
      "requirements": ["gl_codes_upload", "chart_of_accounts"]
    },
    {
      "id": "historical_pattern",
      "name": "Historical Pattern (M2)", 
      "description": "Learn from categorized transaction history",
      "target_accuracy": 95,
      "setup_time": "45 minutes", 
      "requirements": ["historical_data", "category_training"]
    }
  ]
}
```

#### POST /onboarding/select-path
**Description:** Select onboarding path for tenant  
**Body:**
```json
{
  "path_id": "zero_onboarding",
  "business_context": {
    "company_size": "small_business",
    "industry": "professional_services",
    "transaction_volume": "100-500_monthly"
  }
}
```

### Schema Upload (M3)

#### POST /onboarding/schema-upload
**Description:** Upload GL codes and chart of accounts  
**Content-Type:** `multipart/form-data`  
**Body:**
```form
schema_file: <binary_file_data>     # Excel or CSV with GL codes
mapping_config: <json_config>       # Category mapping rules
```

#### GET /onboarding/schema-validation
**Description:** Validate uploaded schema structure  
**Response:**
```json
{
  "validation_status": "valid",
  "gl_codes_detected": 45,
  "hierarchy_levels": 3,
  "category_mappings": [
    {
      "gl_code": "6100",
      "category": "Office Supplies", 
      "confidence": 0.95
    }
  ],
  "compliance_check": {
    "accounting_standards": "valid",
    "hierarchy_structure": "valid",
    "code_uniqueness": "valid"
  }
}
```

---

## 🤖 AI AGENT INTERACTION

### Conversational AI

#### POST /agent/chat
**Description:** Interact with AI assistant  
**Body:**
```json
{
  "message": "What are my top expense categories this month?",
  "context": {
    "current_page": "reports",
    "user_intent": "analytics",
    "business_context": "expense_analysis"
  }
}
```
**Response:**
```json
{
  "response": "Based on your transaction data, your top 3 expense categories this month are: 1) Office Supplies ($1,250 - 26.3%), 2) Software Subscriptions ($890 - 18.8%), 3) Travel & Entertainment ($723 - 15.3%). Would you like me to generate a detailed expense report?",
  "confidence": 0.95,
  "suggested_actions": [
    {
      "action": "generate_expense_report",
      "label": "Generate Detailed Report"
    },
    {
      "action": "view_category_breakdown", 
      "label": "View Category Details"
    }
  ],
  "business_context": "expense_analysis_complete"
}
```

#### POST /agent/quick-categorize
**Description:** Quick categorization assistance  
**Body:**
```json
{
  "transactions": [
    {
      "description": "ZOOM VIDEO COMMUNICATIONS",
      "amount": -14.99
    }
  ]
}
```
**Response:**
```json
{
  "categorizations": [
    {
      "transaction": "ZOOM VIDEO COMMUNICATIONS",
      "suggested_category": "Software Subscriptions",
      "confidence": 0.94,
      "reasoning": "Video conferencing software subscription service"
    }
  ]
}
```

---

## 🔧 SYSTEM ADMINISTRATION

### Health Check

#### GET /health
**Description:** System health status  
**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-30T12:00:00Z",
  "services": {
    "database": "healthy",
    "ai_service": "healthy", 
    "file_storage": "healthy"
  },
  "performance": {
    "avg_response_time_ms": 145,
    "active_connections": 23,
    "memory_usage_percent": 45
  }
}
```

### API Documentation

#### GET /docs
**Description:** Interactive API documentation (Swagger UI)  
**Response:** HTML Swagger interface

#### GET /openapi.json
**Description:** OpenAPI specification  
**Response:** JSON schema

---

## 📚 ERROR RESPONSES

### Standard Error Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "File format not supported",
    "details": {
      "supported_formats": ["xlsx", "csv", "qif", "ofx"],
      "received_format": "txt"
    },
    "timestamp": "2025-06-30T12:00:00Z",
    "request_id": "uuid"
  }
}
```

### Common Error Codes
```
400 - VALIDATION_ERROR        # Invalid request data
401 - AUTHENTICATION_FAILED   # Invalid or missing token
403 - AUTHORIZATION_DENIED    # Insufficient permissions
404 - RESOURCE_NOT_FOUND     # Resource doesn't exist
409 - CONFLICT               # Resource conflict
422 - PROCESSING_ERROR       # File processing failed
429 - RATE_LIMIT_EXCEEDED    # Too many requests
500 - INTERNAL_ERROR         # Server error
503 - SERVICE_UNAVAILABLE    # AI service down
```

---

## 🚀 RATE LIMITS

### Default Limits
```
Authentication: 5 requests/minute
File Upload: 10 requests/hour
AI Categorization: 100 requests/minute  
Report Generation: 20 requests/hour
General API: 1000 requests/hour
```

### Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 995
X-RateLimit-Reset: 1672531200
```

---

**API STATUS:** Production-ready with comprehensive endpoint coverage for M1/M2/M3 milestone achievement. All endpoints validated through UAT testing and ready for customer integration.