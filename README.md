# Giki AI Workspace

Advanced financial data processing system with intelligent categorization and analysis capabilities.

## Technical Architecture

> **📋 Complete Architecture & Tech Stack**: docs/technical/system-architecture.md
> **📋 Performance Requirements**: docs/operations/performance-monitoring.md
> **📋 Quality Standards**: docs/execution/active-work.md

## Design System & User Experience

> **📋 Design System**: docs/design-system/MASTER-DESIGN-SPEC.md (comprehensive UI guidelines)
> **📋 Customer Journey**: docs/customer-journeys/COMPLETE-JOURNEY-MAP.md (complete user flows)
> **📋 Brand Colors**: docs/design-system/brand/colors.md (AI-themed color palette)
> **📋 Component Library**: docs/design-system/components/README.md (reusable UI patterns)
> **📋 Implementation Guide**: docs/design-system/implementation-guide.md (developer handbook)

## Development Setup

### Prerequisites
```bash
# Node.js Environment
pnpm install

# Python Environment
uv venv
source .venv/bin/activate
uv sync

# Database
# Local PostgreSQL required for development
# Production uses Google Cloud SQL
```

### Essential Commands
```bash
# Development
pnpm dev                 # Start development environment
pnpm test:watch         # Watch mode testing
pnpm env:dev            # Set development environment

# Production
pnpm build:prod         # Production build
pnpm deploy             # Deploy to production
pnpm env:prod           # Set production environment
pnpm deploy:dry-run     # Test deployment
```

## Project Structure

> **📋 Complete Directory Structure**: docs/technical/system-architecture.md#codebase-structure
> **📋 Documentation Structure**: docs/README.md

## Service Architecture

> **📋 Domain-Driven Architecture**: docs/technical/system-architecture.md#backend-architecture
> **📋 Agent Implementation**: docs/technical/agents-and-tools.md

## Technical Capabilities

### Data Processing
- Multi-sheet Excel support
- Automatic column interpretation
- Different schemas per sheet
- Hierarchical category detection
- Credit/debit transaction mapping
- Real-time processing

### AI Features (✅ REAL AI IMPLEMENTATION)
- **Intelligent Transaction Categorization**: Vertex AI Gemini 2.0 Flash with >85% accuracy
- **AI Reasoning Engine**: Detailed explanations for categorization decisions
- **Confidence Scoring**: Real-time confidence assessment (0.0-1.0) for all AI decisions
- **Natural Language Processing**: Intelligent query parsing and conversation context
- **Entity Extraction**: Hybrid approach with LLM fallback for complex patterns
- **RAG-Enhanced Processing**: Vector-based similarity search for context retrieval
- **Real-time Learning**: Adaptive categorization based on transaction patterns

### Performance Metrics
- File Processing: <60s for 1MB files
- Bulk Processing: <30s for 10,000 transactions
- Schema Operations: <5s
- RAG Operations: <100ms similarity search
- File Validation: <2s
- Customer Onboarding: <5min complete process

## Core Technical Rules

### Development Environment
1. NEVER use containers for local development
2. ALWAYS use Cloud Run for production
3. NEVER create intermediate environments
4. ALWAYS use PostgreSQL (local for dev, Cloud SQL for prod)
5. NEVER manually verify deployments

### Technical Documentation
- Specs/ directory uses standard markdown
- All other docs/ subdirectories use YAML-like format
- All metrics must be concrete and measurable
- All changes require validation evidence
- No theoretical estimates or subjective assessments

### Testing Requirements
1. Screenshot validation with Playwright
2. Automated test coverage verification
3. Performance metrics validation
4. Specific test commands for each feature
5. E2E test coverage must be 100%

### Testing Organization
- **Test Data**: `testing/data/` (consolidated test files)
- **Test Results**: `testing/results/` (accuracy comparisons)
- **Test Artifacts**: `testing/artifacts/` (screenshots, logs)
- **Test Code**: `tests/` (E2E and integration specs)

## Environment URLs

### Production
- Frontend: https://giki-ai-frontend-273348121056.us-central1.run.app
- API: https://dev-giki-ai-api-273348121056.us-central1.run.app
- API Docs: https://dev-giki-ai-api-273348121056.us-central1.run.app/docs

### Development
- Frontend: http://localhost:4200
- API: http://localhost:8000
- API Docs: http://localhost:8000/docs
- Database: PostgreSQL on localhost:5433

## UI Architecture
- Excel-inspired interface
- Three-column layout
  - Navigation
  - Content
  - Agent panel
- Branding color: #0D4F12 (systematic color mapping from logo to dark variants)
- Frontend Status: Optimized codebase with 15+ unused components removed
- Key sections:
  - Upload Data
  - Reports
  - Knowledge Hub
  - Customer Onboarding
  - Settings

## Multi-Tenant Setup ✅ VERIFIED
**Three Required Tenants Configured:**
- **Giki AI** (ID: 1) - Primary development tenant
- **Rezolve AI** (ID: 2) - Enterprise client tenant
- **Nuvie** (ID: 3) - Enterprise client tenant

**Test Accounts (All use password: `GikiTest2025Secure`):**
- `<EMAIL>` (Giki AI Tenant)
- `<EMAIL>` (Rezolve AI)
- `<EMAIL>` (Nuvie)
- `<EMAIL>` (Admin)

**Database Status:** Complete tenant isolation with proper foreign key relationships

## Contributing

### Development Workflow
1. **Check active tasks first**: `cat docs/execution/active-work.md`
2. **Search before creating**: Use existing patterns and components
3. **Edit production code**: Work in `apps/` directories only
4. **Test everything**: `pnpm lint && pnpm test:e2e` before commit
5. **Commit frequently**: Every 15-30 minutes
6. **Follow wireframes**: Implementation must match `docs/customer-journeys/COMPLETE-JOURNEY-MAP.md`
7. **Use design system**: All UI from `docs/design-system/MASTER-DESIGN-SPEC.md` components

### Quality Standards
- Zero warnings: `pnpm lint` must pass
- File size: Max 500 lines (1000 absolute max)
- Follow architecture patterns: `domains/*/models.py|service.py|agent.py`
- Use MCP postgres for database queries
- Clean up unused code immediately

### Package Management
```bash
# ALWAYS from workspace root
pnpm add <package> -w     # JavaScript/TypeScript
uv add <package>          # Python
```

### Troubleshooting
```bash
# Process overload (>20 processes)
ps aux | grep -E "node|python" | wc -l
pnpm serve:stop && ./scripts/kill-all-processes.sh

# Emergency recovery
rm -rf node_modules && pnpm install && pnpm serve
```

## Architecture Rules
- **Backend**: `domains/*/models.py|service.py|agent.py` pattern
- **Frontend**: `features/*/components/|hooks/|pages/|services/` pattern
- **No Versioning**: Never create v2, new, improved versions
- **Design System**: All UI follows `docs/design-system/MASTER-DESIGN-SPEC.md`
- **Batch Operations**: Parallel execution, never sequential

## Support
- **Development Guide**: `CLAUDE.md`
- **API Health**: https://giki-ai-api-6uyufgxcxa-uc.a.run.app/health
- **Local API**: http://localhost:8000/health
