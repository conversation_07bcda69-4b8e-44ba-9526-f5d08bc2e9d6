/**
 * Playwright Configuration for Visual Regression Testing
 * 
 * Specialized configuration for capturing and comparing screenshots
 * to detect visual regressions and design system violations.
 */

import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/visual-regression',
  
  // Parallel execution settings
  fullyParallel: true,
  workers: process.env.CI ? 1 : undefined,
  
  // Test timeouts
  timeout: 30 * 1000,
  expect: {
    // Visual comparison timeout
    timeout: 10 * 1000,
    // Visual comparison threshold (0-1, lower = more strict)
    toHaveScreenshot: {
      threshold: 0.1,
      mode: 'actual'
    },
    toMatchSnapshot: {
      threshold: 0.1
    }
  },

  // Fail fast on CI
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,

  // Reporter configuration
  reporter: [
    ['html', { outputFolder: 'tests/visual-regression/reports' }],
    ['json', { outputFile: 'tests/visual-regression/results.json' }],
    ['list']
  ],

  // Global test settings
  use: {
    // Base URL for tests
    baseURL: 'http://localhost:4200',
    
    // Browser settings for consistent screenshots
    headless: true,
    
    // Default viewport (overridden in tests)
    viewport: { width: 1440, height: 900 },
    
    // Disable animations for consistent captures
    reducedMotion: 'reduce',
    
    // Screenshot settings
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // Network settings
    extraHTTPHeaders: {
      'Accept-Language': 'en-US'
    }
  },

  // Test projects for different browsers and viewports
  projects: [
    {
      name: 'chromium-desktop',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1440, height: 900 }
      },
    },
    {
      name: 'chromium-tablet',
      use: { 
        ...devices['iPad Pro'],
        viewport: { width: 1024, height: 768 }
      },
    },
    {
      name: 'chromium-mobile',
      use: { 
        ...devices['iPhone 12'],
        viewport: { width: 390, height: 844 }
      },
    },
    
    // Optional: Test in other browsers for broader coverage
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },
    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },
  ],

  // Web server configuration
  webServer: {
    command: 'pnpm serve:app',
    url: 'http://localhost:4200',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },

  // Output directories
  outputDir: 'tests/visual-regression/test-results',
});