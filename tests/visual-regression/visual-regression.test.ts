/**
 * Visual Regression Testing Suite
 * 
 * Automatically captures screenshots of key pages and components
 * to detect unintended visual changes and design system violations.
 */

import { test, expect, type Page } from '@playwright/test';
import path from 'path';

// Test configuration
const SCREENSHOT_DIR = 'tests/visual-regression/screenshots';
const VIEWPORTS = [
  { name: 'mobile', width: 375, height: 667 },
  { name: 'tablet', width: 768, height: 1024 },
  { name: 'desktop', width: 1440, height: 900 }
];

// Test pages and components
const TEST_SCENARIOS = [
  {
    name: 'login-page',
    url: '/login',
    description: 'Login page with professional branding'
  },
  {
    name: 'dashboard-page',
    url: '/dashboard',
    description: 'Main dashboard with metrics grid',
    requiresAuth: true
  },
  {
    name: 'upload-page',
    url: '/upload',
    description: 'File upload page with onboarding',
    requiresAuth: true
  },
  {
    name: 'transactions-page',
    url: '/transactions',
    description: 'Transaction review interface',
    requiresAuth: true
  },
  {
    name: 'reports-page',
    url: '/reports',
    description: 'Reports and export page',
    requiresAuth: true
  },
  {
    name: 'categories-page',
    url: '/categories',
    description: 'Category management page',
    requiresAuth: true
  }
];

// Design system validation helpers
async function validateDesignSystem(page: Page, scenarioName: string) {
  // Check for prohibited emoji icons
  const prohibitedIcons = ['🎯', '🚀', '💡', '📊', '✨', '🔄', '⚡', '✓', '❌'];
  for (const icon of prohibitedIcons) {
    const iconElements = await page.locator(`text="${icon}"`).count();
    expect(iconElements, `Found prohibited icon "${icon}" in ${scenarioName}`).toBe(0);
  }
  
  // Check for hard-coded brand colors
  const brandColorRegex = /#295343|#1D372E/i;
  const pageContent = await page.content();
  const hardCodedColors = pageContent.match(brandColorRegex);
  expect(hardCodedColors, `Found hard-coded brand colors in ${scenarioName}`).toBeNull();
  
  // Validate geometric icon usage
  const allowedIcons = ['□', '⊞', '∷', '⚬', '↑'];
  const iconCount = await page.locator('.nav-item-icon').count();
  if (iconCount > 0) {
    for (let i = 0; i < iconCount; i++) {
      const iconText = await page.locator('.nav-item-icon').nth(i).textContent();
      expect(allowedIcons.includes(iconText || ''), 
        `Invalid icon "${iconText}" found in navigation`).toBeTruthy();
    }
  }
}

// Authentication helper
async function authenticateUser(page: Page) {
  await page.goto('/login');
  
  // Use test credentials
  await page.fill('input[type="email"]', '<EMAIL>');
  await page.fill('input[type="password"]', 'testpassword123');
  await page.click('button[type="submit"]');
  
  // Wait for navigation to dashboard
  await page.waitForURL('/dashboard');
}

// Main test suite
test.describe('Visual Regression Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up page with consistent viewport
    await page.setViewportSize({ width: 1440, height: 900 });
    
    // Disable animations for consistent screenshots
    await page.addStyleTag({
      content: `
        *, *::before, *::after {
          animation-duration: 0s !important;
          animation-delay: 0s !important;
          transition-duration: 0s !important;
          transition-delay: 0s !important;
        }
      `
    });
  });

  // Test each scenario across different viewports
  VIEWPORTS.forEach(viewport => {
    TEST_SCENARIOS.forEach(scenario => {
      test(`${scenario.name} - ${viewport.name}`, async ({ page }) => {
        // Set viewport
        await page.setViewportSize(viewport);
        
        // Authenticate if required
        if (scenario.requiresAuth) {
          await authenticateUser(page);
        }
        
        // Navigate to test page
        await page.goto(scenario.url);
        
        // Wait for page to be fully loaded
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1000); // Additional wait for any remaining async operations
        
        // Validate design system compliance
        await validateDesignSystem(page, `${scenario.name}-${viewport.name}`);
        
        // Take screenshot
        const screenshotPath = path.join(SCREENSHOT_DIR, `${scenario.name}-${viewport.name}.png`);
        await page.screenshot({
          path: screenshotPath,
          fullPage: true
        });
        
        // Compare with baseline (Playwright automatically handles this)
        await expect(page).toHaveScreenshot(`${scenario.name}-${viewport.name}.png`);
      });
    });
  });

  // Component-specific visual tests
  test.describe('Component Visual Tests', () => {
    
    test('Professional Metrics Grid', async ({ page }) => {
      await authenticateUser(page);
      await page.goto('/dashboard');
      
      // Focus on metrics grid component
      const metricsGrid = page.locator('.responsive-grid-4').first();
      await expect(metricsGrid).toBeVisible();
      
      // Validate metric cards use professional classes
      const metricCards = page.locator('.card-elevated, .card-professional');
      const cardCount = await metricCards.count();
      expect(cardCount).toBeGreaterThan(0);
      
      // Screenshot specific component
      await metricsGrid.screenshot({
        path: path.join(SCREENSHOT_DIR, 'metrics-grid-component.png')
      });
    });
    
    test('Navigation Panel', async ({ page }) => {
      await authenticateUser(page);
      await page.goto('/dashboard');
      
      // Focus on navigation panel
      const navPanel = page.locator('nav').first();
      await expect(navPanel).toBeVisible();
      
      // Validate navigation items use professional classes
      const navItems = page.locator('.nav-item-professional');
      const navItemCount = await navItems.count();
      expect(navItemCount).toBeGreaterThan(0);
      
      // Check for active state styling
      const activeItem = page.locator('.nav-item-professional.active');
      if ((await activeItem.count()) > 0) {
        await expect(activeItem).toHaveClass(/active/);
      }
      
      await navPanel.screenshot({
        path: path.join(SCREENSHOT_DIR, 'navigation-panel-component.png')
      });
    });
    
    test('Login Form Component', async ({ page }) => {
      await page.goto('/login');
      
      // Focus on login form
      const loginCard = page.locator('.card-elevated').first();
      await expect(loginCard).toBeVisible();
      
      // Validate form uses professional input classes
      const inputs = page.locator('.input-elevated');
      const inputCount = await inputs.count();
      expect(inputCount).toBeGreaterThan(0);
      
      // Validate button uses professional classes
      const submitButton = page.locator('button[type="submit"]');
      await expect(submitButton).toHaveClass(/btn-professional/);
      
      await loginCard.screenshot({
        path: path.join(SCREENSHOT_DIR, 'login-form-component.png')
      });
    });
    
    test('Transaction Review Interface', async ({ page }) => {
      await authenticateUser(page);
      await page.goto('/transactions');
      
      // Wait for transaction data to load
      await page.waitForTimeout(2000);
      
      // Focus on transaction review card
      const reviewCard = page.locator('.card-elevated').first();
      if ((await reviewCard.count()) > 0) {
        await expect(reviewCard).toBeVisible();
        
        // Validate buttons use touch-friendly classes
        const buttons = page.locator('button.btn-touch-friendly');
        const buttonCount = await buttons.count();
        expect(buttonCount).toBeGreaterThan(0);
        
        await reviewCard.screenshot({
          path: path.join(SCREENSHOT_DIR, 'transaction-review-component.png')
        });
      }
    });
  });

  // Responsive design validation tests
  test.describe('Responsive Design Tests', () => {
    
    test('Mobile Navigation Behavior', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await authenticateUser(page);
      await page.goto('/dashboard');
      
      // On mobile, navigation should be collapsible
      const mobileNavTrigger = page.locator('[data-testid="mobile-nav-trigger"]');
      if ((await mobileNavTrigger.count()) > 0) {
        await mobileNavTrigger.click();
        await page.waitForTimeout(500); // Wait for animation
        
        await page.screenshot({
          path: path.join(SCREENSHOT_DIR, 'mobile-navigation-open.png')
        });
      }
    });
    
    test('Responsive Grid Behavior', async ({ page }) => {
      // Test different viewport sizes
      const viewports = [
        { width: 375, height: 667, name: 'mobile' },
        { width: 768, height: 1024, name: 'tablet' },
        { width: 1440, height: 900, name: 'desktop' }
      ];
      
      await authenticateUser(page);
      
      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await page.goto('/dashboard');
        await page.waitForTimeout(1000);
        
        // Validate responsive grid classes
        const responsiveGrids = page.locator('.responsive-grid-1, .responsive-grid-2, .responsive-grid-3, .responsive-grid-4');
        const gridCount = await responsiveGrids.count();
        expect(gridCount).toBeGreaterThan(0);
        
        await page.screenshot({
          path: path.join(SCREENSHOT_DIR, `responsive-grid-${viewport.name}.png`)
        });
      }
    });
  });

  // Accessibility visual tests
  test.describe('Accessibility Visual Tests', () => {
    
    test('Touch Target Sizes', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await authenticateUser(page);
      await page.goto('/transactions');
      
      // Check for touch-friendly buttons
      const touchButtons = page.locator('.btn-touch-friendly');
      const touchButtonCount = await touchButtons.count();
      
      if (touchButtonCount > 0) {
        // Validate minimum touch target size (44px)
        for (let i = 0; i < Math.min(touchButtonCount, 5); i++) {
          const button = touchButtons.nth(i);
          const boundingBox = await button.boundingBox();
          
          if (boundingBox) {
            expect(boundingBox.height, 'Touch target height must be at least 44px').toBeGreaterThanOrEqual(44);
            expect(boundingBox.width, 'Touch target width must be at least 44px').toBeGreaterThanOrEqual(44);
          }
        }
      }
    });
    
    test('Focus States Visibility', async ({ page }) => {
      await page.goto('/login');
      
      // Tab through interactive elements and capture focus states
      const interactiveElements = await page.locator('button, input, select, a[href]').all();
      
      for (let i = 0; i < Math.min(interactiveElements.length, 3); i++) {
        await interactiveElements[i].focus();
        await page.waitForTimeout(100);
        
        // Capture focus state
        await page.screenshot({
          path: path.join(SCREENSHOT_DIR, `focus-state-${i}.png`)
        });
      }
    });
  });

  // Brand compliance tests
  test.describe('Brand Compliance Tests', () => {
    
    test('Color Consistency', async ({ page }) => {
      await authenticateUser(page);
      await page.goto('/dashboard');
      
      // Check that brand primary color is used consistently
      const brandElements = page.locator('.bg-brand-primary, .text-brand-primary, .border-brand-primary');
      const brandElementCount = await brandElements.count();
      expect(brandElementCount).toBeGreaterThan(0);
      
      // Validate computed styles use correct brand color
      if (brandElementCount > 0) {
        const firstBrandElement = brandElements.first();
        const computedColor = await firstBrandElement.evaluate(el => 
          window.getComputedStyle(el).backgroundColor
        );
        
        // Check if it matches our brand color (rgb(41, 83, 67) = #295343)
        expect(computedColor).toMatch(/rgb\(41,\s*83,\s*67\)|#295343/i);
      }
    });
    
    test('Typography Consistency', async ({ page }) => {
      await authenticateUser(page);
      await page.goto('/dashboard');
      
      // Check for responsive typography classes
      const responsiveText = page.locator('[class*="text-responsive-"]');
      const responsiveTextCount = await responsiveText.count();
      expect(responsiveTextCount).toBeGreaterThan(0);
      
      // Validate heading hierarchy
      const headings = page.locator('h1, h2, h3');
      const headingCount = await headings.count();
      
      if (headingCount > 0) {
        for (let i = 0; i < headingCount; i++) {
          const heading = headings.nth(i);
          const className = await heading.getAttribute('class');
          expect(className).toMatch(/text-responsive-/);
        }
      }
    });
  });
});

// Global setup for visual regression testing
test.beforeAll(async () => {
  // Ensure screenshot directory exists
  const fs = require('fs');
  const screenshotDir = path.resolve(SCREENSHOT_DIR);
  
  if (!fs.existsSync(screenshotDir)) {
    fs.mkdirSync(screenshotDir, { recursive: true });
  }
});